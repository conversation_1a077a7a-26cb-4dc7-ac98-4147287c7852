#!/bin/bash

# Start script for Labwise Web standalone service

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if service is already running
if [ -f "$SCRIPT_DIR/run/nginx.pid" ]; then
    PID=$(cat "$SCRIPT_DIR/run/nginx.pid")
    if kill -0 "$PID" 2>/dev/null; then
        log_warning "Labwise Web service is already running (PID: $PID)"
        
        # Try to get the port from the config
        if [ -f "$SCRIPT_DIR/run/nginx.conf" ]; then
            PORT=$(grep "listen" "$SCRIPT_DIR/run/nginx.conf" | head -1 | awk '{print $2}' | tr -d ';')
            log_info "Service is running on port: $PORT"
            log_info "URL: http://localhost:$PORT"
        fi
        exit 0
    else
        log_warning "Stale PID file found, removing..."
        rm -f "$SCRIPT_DIR/run/nginx.pid"
    fi
fi

# Check if configuration exists
if [ ! -f "$SCRIPT_DIR/run/nginx.conf" ]; then
    log_error "Runtime configuration not found. Please run ./deploy.sh first."
    exit 1
fi

# Set library path for nginx dependencies (glibc-based)
export LD_LIBRARY_PATH="$SCRIPT_DIR/nginx/lib:$LD_LIBRARY_PATH"

log_info "Starting Labwise Web service..."
log_info "Using library path: $LD_LIBRARY_PATH"

# Start nginx
"$SCRIPT_DIR/nginx/nginx" -c "$SCRIPT_DIR/run/nginx.conf" -p "$SCRIPT_DIR"

# Wait a moment and check if it started successfully
sleep 2

if [ -f "$SCRIPT_DIR/run/nginx.pid" ] && kill -0 "$(cat "$SCRIPT_DIR/run/nginx.pid")" 2>/dev/null; then
    PID=$(cat "$SCRIPT_DIR/run/nginx.pid")
    PORT=$(grep "listen" "$SCRIPT_DIR/run/nginx.conf" | head -1 | awk '{print $2}' | tr -d ';')
    
    log_success "Labwise Web service started successfully!"
    log_info "PID: $PID"
    log_info "Port: $PORT"
    log_info "URL: http://localhost:$PORT"
    log_info ""
    log_info "To stop the service: ./stop.sh"
    log_info "To check status: ./status.sh"
else
    log_error "Failed to start Labwise Web service"
    log_info "Check error log: $SCRIPT_DIR/logs/error.log"
    exit 1
fi
