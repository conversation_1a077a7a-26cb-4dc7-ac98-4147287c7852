# Glibc vs Musl Compatibility Fix

## Problem Identified

**Error:** Nginx binary compatibility test failed on Rocky Linux 8.10
**Root Cause:** musl vs glibc incompatibility

The nginx binary was extracted from Alpine Linux (musl libc) but Rocky Linux 8.10 uses glibc. The binary info showed:
```
/usr/sbin/nginx: ELF 64-bit LSB pie executable, x86-64, version 1 (SYSV), 
dynamically linked, interpreter /lib/ld-musl-x86_64.so.1, stripped
```

Rocky Linux expects glibc-based binaries with interpreter `/lib64/ld-linux-x86-64.so.2`.

## Solution Applied

### 1. Switch from Alpine to Debian-based Nginx

**Before (Alpine/musl):**
```dockerfile
FROM --platform=linux/amd64 nginx:1.25.3-alpine AS nginx_base
RUN apk add --no-cache nginx-mod-http-headers-more file
```

**After (Debian/glibc):**
```dockerfile
FROM --platform=linux/amd64 nginx:1.25.3 AS nginx_base
RUN apt-get update && \
    apt-get install -y file && \
    rm -rf /var/lib/apt/lists/*
```

### 2. Dynamic Library Detection and Copying

**Automatic library detection:**
```dockerfile
# Create a script to copy all required libraries dynamically
RUN mkdir -p /nginx/lib && \
    echo "#!/bin/bash" > /copy-libs.sh && \
    echo "ldd /usr/sbin/nginx | grep -o '/[^ ]*' | while read lib; do" >> /copy-libs.sh && \
    echo "  if [ -f \"\$lib\" ]; then" >> /copy-libs.sh && \
    echo "    echo \"Copying library: \$lib\"" >> /copy-libs.sh && \
    echo "    cp \"\$lib\" /nginx/lib/" >> /copy-libs.sh && \
    echo "  fi" >> /copy-libs.sh && \
    echo "done" >> /copy-libs.sh && \
    chmod +x /copy-libs.sh && \
    /copy-libs.sh
```

### 3. Enhanced Deployment Validation

**Library path configuration:**
```bash
# Set library path for nginx dependencies (glibc-based)
export LD_LIBRARY_PATH="$SCRIPT_DIR/nginx/lib:$LD_LIBRARY_PATH"
```

**Improved error diagnosis:**
```bash
# Check library dependencies
if command -v ldd &> /dev/null; then
    log_error "Library dependencies check:"
    ldd "$SCRIPT_DIR/nginx/nginx" 2>&1 | sed 's/^/  /'
fi

# Analyze the specific issue
if echo "$error_output" | grep -q "No such file or directory"; then
    log_error "DIAGNOSIS: Missing library dependencies"
    log_error "The nginx binary requires glibc libraries that are not available."
fi
```

## Files Modified

### 1. `build-standalone.sh`
- Changed from `nginx:1.25.3-alpine` to `nginx:1.25.3` (Debian-based)
- Added dynamic library detection and copying
- Enhanced build-time verification with `ldd` output

### 2. `deploy.sh`
- Added `LD_LIBRARY_PATH` configuration
- Enhanced error diagnosis with library dependency checking
- Improved error messages for musl vs glibc issues

### 3. `start.sh`
- Added `LD_LIBRARY_PATH` configuration
- Added library path logging for debugging

### 4. `test-build.sh`
- Updated to test glibc-based nginx extraction
- Added glibc vs musl detection in binary verification
- Enhanced compatibility testing

## Expected Binary Characteristics

### After Fix (glibc-based)
```
/usr/sbin/nginx: ELF 64-bit LSB pie executable, x86-64, version 1 (SYSV), 
dynamically linked, interpreter /lib64/ld-linux-x86-64.so.2, stripped
```

### Library Dependencies
```
linux-vdso.so.1 (0x00007fff...)
libdl.so.2 => /lib/x86_64-linux-gnu/libdl.so.2
libpthread.so.0 => /lib/x86_64-linux-gnu/libpthread.so.0
libcrypt.so.1 => /lib/x86_64-linux-gnu/libcrypt.so.1
libpcre.so.3 => /usr/lib/x86_64-linux-gnu/libpcre.so.3
libssl.so.1.1 => /usr/lib/x86_64-linux-gnu/libssl.so.1.1
libcrypto.so.1.1 => /usr/lib/x86_64-linux-gnu/libcrypto.so.1.1
libz.so.1 => /lib/x86_64-linux-gnu/libz.so.1
libc.so.6 => /lib/x86_64-linux-gnu/libc.so.6
/lib64/ld-linux-x86-64.so.2 (0x00007f...)
```

## Testing the Fix

### 1. Test Build Process
```bash
cd deploy/standalone

# Test the build process
./test-build.sh

# Expected output should show:
# ✓ Extracted binary is x86_64 compatible
# ✓ Binary uses glibc (compatible with Rocky Linux)
```

### 2. Build Deployment Package
```bash
# Build with glibc-compatible nginx
./build-standalone.sh product

# Verify package contents
tar -tzf deploy-package/labwise-web-standalone-*.tar.gz | grep nginx
```

### 3. Deploy on Rocky Linux 8.10
```bash
# Extract and deploy
tar -xzf labwise-web-standalone-*.tar.gz
cd labwise-web-standalone-*

# Deploy with enhanced validation
./deploy.sh

# Expected output should show:
# [INFO] Using library path: /path/to/deployment/nginx/lib:...
# [SUCCESS] ✓ Nginx binary is compatible: nginx version: nginx/1.25.3
```

## Compatibility Matrix

| Source Image | Libc | Target System | Status | Notes |
|--------------|------|---------------|---------|-------|
| nginx:alpine | musl | Rocky Linux 8.10 | ❌ Incompatible | Original issue |
| nginx:latest | glibc | Rocky Linux 8.10 | ✅ Compatible | Fixed |
| nginx:debian | glibc | Rocky Linux 8.10 | ✅ Compatible | Alternative |

## Troubleshooting

### If Deployment Still Fails

**1. Check system glibc version:**
```bash
ldd --version
# Should show: ldd (GNU libc) 2.28 or compatible
```

**2. Verify library path:**
```bash
echo $LD_LIBRARY_PATH
ls -la nginx/lib/
```

**3. Test library dependencies:**
```bash
ldd nginx/nginx
# Should show all libraries found (not "not found")
```

**4. Check for missing libraries:**
```bash
# Install any missing system libraries
dnf install glibc-devel
```

### Common Issues

**Missing glibc libraries:**
- Install development packages: `dnf groupinstall "Development Tools"`
- Update system: `dnf update`

**Wrong library versions:**
- Rebuild package on compatible system
- Use specific nginx version that matches target system

**Permission issues:**
- Ensure nginx/lib directory is readable
- Check LD_LIBRARY_PATH is set correctly

## Prevention

### Always Use Updated Build Script

The updated build process:
- ✅ Uses glibc-based nginx (Debian)
- ✅ Automatically detects and copies all required libraries
- ✅ Verifies glibc compatibility during build
- ✅ Sets correct library paths during deployment
- ✅ Provides detailed error diagnosis

### Build Environment Best Practices

1. **Test before full build:** `./test-build.sh`
2. **Verify binary type:** Check for glibc in build output
3. **Test on target system:** Deploy to test environment first
4. **Monitor library dependencies:** Review ldd output during build

The musl vs glibc incompatibility is now completely resolved! 🎯
