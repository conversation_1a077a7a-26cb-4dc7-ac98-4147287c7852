#!/bin/bash

# Test script for Labwise Web standalone deployment
# This script validates that the deployment is working correctly

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to test HTTP endpoint
test_http_endpoint() {
    local url="$1"
    local expected_status="$2"
    local description="$3"
    
    log_info "Testing: $description"
    log_info "URL: $url"
    
    if command -v curl &> /dev/null; then
        local response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null || echo "000")
        if [ "$response" = "$expected_status" ]; then
            log_success "✓ $description - HTTP $response"
            return 0
        else
            log_error "✗ $description - Expected HTTP $expected_status, got HTTP $response"
            return 1
        fi
    else
        log_warning "curl not available, skipping HTTP test for: $description"
        return 0
    fi
}

# Function to get service port
get_service_port() {
    if [ -f "$SCRIPT_DIR/run/nginx.conf" ]; then
        grep "listen" "$SCRIPT_DIR/run/nginx.conf" | head -1 | awk '{print $2}' | tr -d ';'
    else
        echo "8080"
    fi
}

# Main test function
main() {
    echo "========================================"
    echo "  Labwise Web Deployment Test Suite"
    echo "========================================"
    echo
    
    local tests_passed=0
    local tests_failed=0
    local port
    port=$(get_service_port)
    local base_url="http://localhost:$port"
    
    log_info "Testing deployment on port: $port"
    echo
    
    # Test 1: Architecture Compatibility
    log_info "Test 1: Architecture Compatibility"
    local system_arch=$(uname -m)
    if [ "$system_arch" = "x86_64" ]; then
        log_success "✓ System architecture is compatible: $system_arch"
        ((tests_passed++))
    else
        log_error "✗ Architecture mismatch: $system_arch (expected x86_64)"
        ((tests_failed++))
    fi

    # Test nginx binary architecture
    if command -v file &> /dev/null && [ -f "$SCRIPT_DIR/nginx/nginx" ]; then
        local binary_info=$(file "$SCRIPT_DIR/nginx/nginx")
        if echo "$binary_info" | grep -q "x86-64\|x86_64"; then
            log_success "✓ Nginx binary is x86_64 compatible"
            ((tests_passed++))
        else
            log_error "✗ Nginx binary architecture mismatch: $binary_info"
            ((tests_failed++))
        fi
    else
        log_warning "Cannot verify nginx binary architecture (file command not available)"
    fi
    echo

    # Test 2: Check if service is running
    log_info "Test 2: Service Status"
    if [ -f "$SCRIPT_DIR/run/nginx.pid" ] && kill -0 "$(cat "$SCRIPT_DIR/run/nginx.pid")" 2>/dev/null; then
        log_success "✓ Service is running"
        ((tests_passed++))
    else
        log_error "✗ Service is not running"
        ((tests_failed++))
        log_info "Try running: ./start.sh"
        echo
        log_info "Test Results: $tests_passed passed, $tests_failed failed"
        exit 1
    fi
    echo
    
    # Test 3: Health check endpoint
    log_info "Test 3: Health Check"
    if test_http_endpoint "$base_url/health" "200" "Health check endpoint"; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    echo

    # Test 4: Main application
    log_info "Test 4: Main Application"
    if test_http_endpoint "$base_url/" "200" "Main application page"; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    echo

    # Test 5: Static assets
    log_info "Test 5: Static Assets"
    if test_http_endpoint "$base_url/favicon.ico" "200" "Favicon"; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    echo

    # Test 6: API endpoints (should return 503 with default config)
    log_info "Test 6: API Endpoints"
    if test_http_endpoint "$base_url/api/test" "503" "API endpoint (expected 503 - not configured)"; then
        log_info "  Note: API endpoints return 503 until backend is configured"
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    echo
    
    # Test 7: Check log files
    log_info "Test 7: Log Files"
    if [ -f "$SCRIPT_DIR/logs/access.log" ] && [ -f "$SCRIPT_DIR/logs/error.log" ]; then
        log_success "✓ Log files exist"
        ((tests_passed++))

        # Check for recent errors
        if [ -s "$SCRIPT_DIR/logs/error.log" ]; then
            local error_count=$(grep -c "error" "$SCRIPT_DIR/logs/error.log" 2>/dev/null || echo "0")
            if [ "$error_count" -gt 0 ]; then
                log_warning "Found $error_count error(s) in error log"
                log_info "Recent errors:"
                tail -n 5 "$SCRIPT_DIR/logs/error.log" | sed 's/^/    /'
            fi
        fi
    else
        log_error "✗ Log files missing"
        ((tests_failed++))
    fi
    echo

    # Test 8: Configuration files
    log_info "Test 8: Configuration"
    if [ -f "$SCRIPT_DIR/run/nginx.conf" ]; then
        log_success "✓ Runtime configuration exists"
        ((tests_passed++))

        # Test nginx configuration
        if "$SCRIPT_DIR/nginx/nginx" -t -c "$SCRIPT_DIR/run/nginx.conf" -p "$SCRIPT_DIR" 2>/dev/null; then
            log_success "✓ Nginx configuration is valid"
            ((tests_passed++))
        else
            log_error "✗ Nginx configuration is invalid"
            ((tests_failed++))
        fi
    else
        log_error "✗ Runtime configuration missing"
        ((tests_failed++))
    fi
    echo

    # Test 9: File permissions
    log_info "Test 9: File Permissions"
    if [ -x "$SCRIPT_DIR/nginx/nginx" ]; then
        log_success "✓ Nginx binary is executable"
        ((tests_passed++))
    else
        log_error "✗ Nginx binary is not executable"
        ((tests_failed++))
    fi

    if [ -w "$SCRIPT_DIR/logs" ] && [ -w "$SCRIPT_DIR/run" ]; then
        log_success "✓ Required directories are writable"
        ((tests_passed++))
    else
        log_error "✗ Required directories are not writable"
        ((tests_failed++))
    fi
    echo
    
    # Summary
    echo "========================================"
    echo "  Test Results Summary"
    echo "========================================"
    echo
    log_info "Tests passed: $tests_passed"
    if [ "$tests_failed" -gt 0 ]; then
        log_error "Tests failed: $tests_failed"
    else
        log_success "Tests failed: $tests_failed"
    fi
    echo
    
    if [ "$tests_failed" -eq 0 ]; then
        log_success "🎉 All tests passed! Deployment is working correctly."
        echo
        log_info "Service Information:"
        log_info "  URL: $base_url"
        log_info "  Health Check: $base_url/health"
        log_info "  Status: ./status.sh"
        echo
        log_info "Next Steps:"
        log_info "1. Configure backend services in conf/nginx.conf"
        log_info "2. Restart service: ./stop.sh && ./start.sh"
        log_info "3. Test backend connectivity"
    else
        log_error "❌ Some tests failed. Please check the issues above."
        echo
        log_info "Common Solutions:"
        log_info "1. Ensure service is running: ./start.sh"
        log_info "2. Check logs: cat logs/error.log"
        log_info "3. Verify port availability: netstat -tuln | grep :$port"
        log_info "4. Check file permissions: ls -la"
        exit 1
    fi
}

# Show usage
show_usage() {
    echo "Usage: $0"
    echo ""
    echo "This script tests the Labwise Web standalone deployment to ensure"
    echo "it's working correctly. Run it after deploying the service."
    echo ""
    echo "The script will test:"
    echo "  - Service status"
    echo "  - HTTP endpoints"
    echo "  - Configuration validity"
    echo "  - File permissions"
    echo "  - Log files"
}

# Parse command line arguments
case "${1:-}" in
    -h|--help)
        show_usage
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
