# Architecture Mismatch Fix - Quick Reference

## Problem Fixed

**Error:** `cannot execute binary file: Exec format error`

**Root Cause:** Building on Apple Silicon Mac (ARM64) but deploying on Rocky Linux 8.10 x86_64

## Solution Applied

### 1. Build Script Updates (`build-standalone.sh`)

**Added platform targeting:**
```bash
# Frontend build
docker buildx build --platform linux/amd64 ...

# Nginx extraction  
docker buildx build --platform linux/amd64 ...
```

**Added architecture detection:**
```bash
detect_host_architecture() {
    local arch=$(uname -m)
    case $arch in
        x86_64) echo "amd64" ;;
        aarch64|arm64) echo "arm64" ;;
        *) echo "unknown" ;;
    esac
}
```

**Added binary verification:**
```bash
# Verify extracted nginx binary
file "$BUILD_OUTPUT_DIR/nginx/nginx"
# Expected: ELF 64-bit LSB executable, x86-64
```

### 2. Deployment Script Updates (`deploy.sh`)

**Added architecture validation:**
```bash
system_arch=$(uname -m)
if [ "$system_arch" != "x86_64" ]; then
    log_error "Architecture mismatch detected!"
    exit 1
fi
```

**Added binary compatibility test:**
```bash
if ! "$SCRIPT_DIR/nginx/nginx" -v &> /dev/null; then
    log_error "Nginx binary compatibility test failed!"
    exit 1
fi
```

### 3. Enhanced Documentation

- Updated README.md with architecture compatibility section
- Added troubleshooting for "Exec format error"
- Created ARCHITECTURE_GUIDE.md with detailed explanations
- Updated test script to verify architecture compatibility

## Quick Fix Commands

### If You Get "Exec format error"

**1. Verify target system:**
```bash
uname -m  # Must show x86_64
```

**2. Rebuild package:**
```bash
cd deploy/standalone
./build-standalone.sh product
```

**3. Verify build output:**
```bash
# Check build logs for platform confirmation
grep -i "platform\|x86_64\|amd64" build.log

# Verify package contents
tar -xzf deploy-package/labwise-web-standalone-*.tar.gz
file labwise-web-standalone-*/nginx/nginx
# Should show: x86-64
```

**4. Deploy with validation:**
```bash
./deploy.sh  # Now includes architecture checks
```

## Prevention

### Always Use Updated Build Script

The updated `build-standalone.sh` automatically:
- ✅ Targets linux/amd64 regardless of host architecture
- ✅ Uses Docker buildx for cross-platform builds
- ✅ Verifies binary architecture after extraction
- ✅ Provides clear warnings for cross-platform builds
- ✅ Includes architecture info in package metadata

### Build Environment Requirements

**For Apple Silicon Macs:**
- Update Docker Desktop to latest version
- Enable "Use Rosetta for x86/amd64 emulation"
- Verify buildx support: `docker buildx version`

**For all systems:**
- Ensure Docker buildx is available
- Use the updated build script (don't use old manual commands)

## Verification Checklist

### Build Phase ✅
- [ ] Build script shows "Platform: linux/amd64"
- [ ] Build logs confirm cross-platform build (if on ARM64)
- [ ] Binary verification passes: "✓ Nginx binary is x86_64 compatible"
- [ ] Package info shows "Target Platform: linux/amd64"

### Deployment Phase ✅
- [ ] Target system shows `uname -m` = `x86_64`
- [ ] Architecture validation passes
- [ ] Binary compatibility test passes
- [ ] Service starts successfully
- [ ] Test script passes all architecture tests

## Compatibility Matrix

| Build Host | Target System | Status | Notes |
|------------|---------------|---------|-------|
| macOS ARM64 | Rocky Linux x86_64 | ✅ Fixed | Cross-platform build |
| macOS Intel | Rocky Linux x86_64 | ✅ Works | Native build |
| Ubuntu x86_64 | Rocky Linux x86_64 | ✅ Works | Native build |
| Any | ARM64 system | ❌ Not supported | Package always targets x86_64 |

## Files Modified

1. `build-standalone.sh` - Added platform targeting and verification
2. `deploy.sh` - Added architecture validation
3. `test-deployment.sh` - Added architecture compatibility tests
4. `README.md` - Added architecture compatibility section
5. `ARCHITECTURE_GUIDE.md` - Comprehensive architecture documentation

## Testing the Fix

```bash
# Build on any system
./build-standalone.sh product

# Deploy on Rocky Linux 8.10 x86_64
./deploy.sh

# Run comprehensive tests
./test-deployment.sh
```

The fix ensures that builds work from any Docker-capable system but always produce x86_64-compatible deployment packages for Rocky Linux 8.10.
