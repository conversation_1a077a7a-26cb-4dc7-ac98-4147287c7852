# Labwise Web Standalone Deployment Guide

This comprehensive guide walks you through deploying the Labwise Web application using the standalone deployment solution that doesn't require Docker on the target system.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Architecture Compatibility](#architecture-compatibility)
3. [Build Process](#build-process)
4. [Deployment Process](#deployment-process)
5. [Troubleshooting Architecture Issues](#troubleshooting-architecture-issues)

## Prerequisites

### Development Environment (Build Phase)

**Any system with Docker support:**
- macOS (Intel or Apple Silicon)
- Ubuntu x86_64
- Windows with WSL2
- Any Linux distribution with Docker

**Required software:**
- Docker installed and running
- Docker buildx support (for cross-platform builds)
- Git access to the repository

**Verify prerequisites:**
```bash
docker --version
docker info
docker buildx version  # Should show buildx support
```

**Important for Apple Silicon Macs:**
- Ensure Docker Desktop is updated to latest version
- Enable "Use Rosetta for x86/amd64 emulation" in Docker Desktop settings
- The build process will automatically target linux/amd64 regardless of host architecture

### Target Environment (Deployment Phase)

**Rocky Linux 8.10 x86_64 system with:**
- No Docker required ✅
- No root/sudo access required ✅
- Architecture: x86_64 (verify with `uname -m`)
- Basic system utilities (ps, netstat/ss, tar)
- Network access (for backend API calls)
- Available port (default: 8080, configurable)

## Architecture Compatibility

### Supported Build Platforms

The build process works on any Docker-capable system:

| Build Platform | Host Architecture | Status | Notes |
|----------------|-------------------|---------|-------|
| macOS Apple Silicon | arm64 | ✅ Supported | Cross-platform build to x86_64 |
| macOS Intel | x86_64 | ✅ Supported | Native build |
| Ubuntu x86_64 | x86_64 | ✅ Supported | Native build |
| Windows WSL2 | x86_64 | ✅ Supported | Native build |

### Target Platform Requirements

**Deployment packages are ALWAYS built for:**
- Architecture: linux/amd64 (x86_64)
- Target OS: Rocky Linux 8.10, CentOS 8, RHEL 8
- Compatible with: Most x86_64 Linux distributions

**NOT compatible with:**
- ARM64/aarch64 systems
- 32-bit systems
- Non-Linux systems

### Architecture Verification

**On build system:**
```bash
# Check your build system
uname -m
docker version --format '{{.Server.Arch}}'
```

**On target system:**
```bash
# Must show x86_64 for compatibility
uname -m
```

## Build Process

### Step 1: Clone and Navigate

```bash
git clone <repository-url>
cd labwise-web-2/deploy/standalone
```

### Step 2: Build Deployment Package

The build script automatically targets linux/amd64 regardless of host architecture:

```bash
# For production (recommended)
./build-standalone.sh product

# For other environments
./build-standalone.sh test
./build-standalone.sh develop
./build-standalone.sh demo
```

### Step 3: Verify Build Output

```bash
ls -la deploy-package/
# Should show: labwise-web-standalone-{env}-{timestamp}.tar.gz

# Check package info
tar -tzf deploy-package/labwise-web-standalone-*.tar.gz | head -10
```

**Build process includes:**
1. Cross-platform frontend compilation (linux/amd64)
2. Nginx binary extraction with x86_64 dependencies
3. Architecture verification
4. Self-contained package creation

## Deployment Process

### Step 1: Transfer Package

Transfer the built package to your Rocky Linux 8.10 target system:

```bash
# Using SCP
scp deploy-package/labwise-web-standalone-*.tar.gz user@target-server:/path/to/deployment/

# Using rsync
rsync -av deploy-package/labwise-web-standalone-*.tar.gz user@target-server:/path/to/deployment/
```

### Step 2: Extract and Verify

On the target system:

```bash
cd /path/to/deployment/
tar -xzf labwise-web-standalone-*.tar.gz
cd labwise-web-standalone-*

# Verify architecture compatibility
uname -m  # Should show x86_64
cat package-info.txt  # Check package details
```

### Step 3: Deploy Service

```bash
# Deploy with automatic architecture validation
./deploy.sh

# Deploy on specific port
./deploy.sh --port 9000
```

The deployment script will:
1. Validate system architecture (must be x86_64)
2. Test nginx binary compatibility
3. Create runtime configuration
4. Start the service

### Step 4: Verify Deployment

```bash
# Check service status
./status.sh

# Run comprehensive tests
./test-deployment.sh

# Test HTTP endpoints
curl http://localhost:8080/health
```

## Troubleshooting Architecture Issues

### Common Error: "Exec format error"

**Symptoms:**
```
[ERROR] Nginx binary compatibility test failed!
cannot execute binary file: Exec format error
```

**Cause:**
Architecture mismatch - trying to run ARM64 binary on x86_64 system or vice versa.

**Solutions:**

1. **Verify target system architecture:**
   ```bash
   uname -m  # Must show x86_64
   ```

2. **Rebuild with correct platform targeting:**
   ```bash
   # On build system
   cd deploy/standalone
   ./build-standalone.sh product
   
   # Check build logs for platform confirmation
   ```

3. **Verify Docker platform support:**
   ```bash
   # On build system
   docker buildx version
   docker buildx ls
   ```

### Build Issues on Apple Silicon

**Problem:** Docker not building for correct architecture

**Solutions:**

1. **Update Docker Desktop:**
   - Install latest Docker Desktop for Mac
   - Enable "Use Rosetta for x86/amd64 emulation"

2. **Verify buildx setup:**
   ```bash
   docker buildx create --use --name multiarch
   docker buildx inspect --bootstrap
   ```

3. **Manual platform specification:**
   ```bash
   # If automatic detection fails
   docker buildx build --platform linux/amd64 ...
   ```

### Deployment Validation Failures

**Problem:** Architecture validation fails during deployment

**Check system compatibility:**
```bash
# System info
uname -a
cat /etc/os-release

# Architecture
uname -m

# Available libraries
ldd --version
```

**Verify package contents:**
```bash
# Check nginx binary
file nginx/nginx
# Should show: ELF 64-bit LSB executable, x86-64

# Test binary
./nginx/nginx -v
```

### Cross-Platform Build Verification

**Verify build process worked correctly:**

1. **Check build logs:**
   ```bash
   # Look for platform confirmation in build output
   grep -i "platform\|architecture\|x86_64\|amd64" build.log
   ```

2. **Inspect package contents:**
   ```bash
   tar -tzf labwise-web-standalone-*.tar.gz | grep nginx
   tar -xzf labwise-web-standalone-*.tar.gz
   file labwise-web-standalone-*/nginx/nginx
   ```

3. **Test on target system:**
   ```bash
   # Extract and test without deploying
   ./nginx/nginx -v
   ```

### Getting Help

When reporting architecture issues, include:

1. **Build system info:**
   ```bash
   uname -a
   docker version
   docker buildx version
   ```

2. **Target system info:**
   ```bash
   uname -a
   cat /etc/os-release
   ```

3. **Package verification:**
   ```bash
   file nginx/nginx
   cat package-info.txt
   ```

4. **Error messages:**
   - Complete error output
   - Build logs
   - Deployment logs

This information helps diagnose architecture compatibility issues quickly.
