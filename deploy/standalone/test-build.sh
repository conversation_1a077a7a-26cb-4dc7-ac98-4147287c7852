#!/bin/bash

# Test script for the build process
# This script tests the Docker build process to ensure it works correctly

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to test Docker prerequisites
test_docker_prerequisites() {
    log_info "Testing Docker prerequisites..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        return 1
    fi
    
    if docker buildx version &> /dev/null; then
        log_success "✓ Docker buildx is available"
    else
        log_warning "Docker buildx is not available - may affect cross-platform builds"
    fi
    
    log_success "✓ Docker prerequisites check passed"
    return 0
}

# Function to test nginx extraction with glibc compatibility
test_nginx_extraction() {
    log_info "Testing nginx extraction with glibc compatibility..."

    local test_dir="$SCRIPT_DIR/test-build-output"
    rm -rf "$test_dir"
    mkdir -p "$test_dir"

    # Create test Dockerfile using Debian-based nginx (glibc)
    cat > "$test_dir/Dockerfile.test" << 'EOF'
FROM --platform=linux/amd64 nginx:1.25.3 AS nginx_base

# Install file command and test it
RUN apt-get update && \
    apt-get install -y file && \
    rm -rf /var/lib/apt/lists/*

# Test file command and library dependencies
RUN echo "Testing nginx binary:" && \
    file /usr/sbin/nginx && \
    echo "Architecture check:" && \
    uname -m && \
    echo "Library dependencies:" && \
    ldd /usr/sbin/nginx

FROM scratch AS export
COPY --from=nginx_base /usr/sbin/nginx /nginx-binary
EOF

    log_info "Building test Docker image..."
    
    if docker buildx version &> /dev/null; then
        docker buildx build \
            --platform linux/amd64 \
            -f "$test_dir/Dockerfile.test" \
            --target export \
            --output "$test_dir" \
            "$SCRIPT_DIR" || return 1
    else
        docker build \
            --platform linux/amd64 \
            -f "$test_dir/Dockerfile.test" \
            --target export \
            --output "$test_dir" \
            "$SCRIPT_DIR" || return 1
    fi
    
    if [ -f "$test_dir/nginx-binary" ]; then
        log_success "✓ Nginx binary extracted successfully"
        
        # Test file command on extracted binary
        if command -v file &> /dev/null; then
            local binary_info=$(file "$test_dir/nginx-binary")
            log_info "Extracted binary info: $binary_info"

            if echo "$binary_info" | grep -q "x86-64\|x86_64"; then
                log_success "✓ Extracted binary is x86_64 compatible"

                # Check if it's glibc-based (not musl)
                if echo "$binary_info" | grep -q "dynamically linked"; then
                    if echo "$binary_info" | grep -q "ld-linux-x86-64"; then
                        log_success "✓ Binary uses glibc (compatible with Rocky Linux)"
                    elif echo "$binary_info" | grep -q "ld-musl"; then
                        log_warning "Binary uses musl libc (may have compatibility issues)"
                    else
                        log_info "Binary linking type: dynamically linked"
                    fi
                else
                    log_info "Binary appears to be statically linked"
                fi
            else
                log_warning "Binary architecture could not be verified: $binary_info"
            fi
        else
            log_warning "file command not available on host for verification"
        fi
    else
        log_error "✗ Nginx binary extraction failed"
        return 1
    fi
    
    # Cleanup
    rm -rf "$test_dir"
    log_success "✓ Nginx extraction test completed"
    return 0
}

# Function to test platform targeting
test_platform_targeting() {
    log_info "Testing platform targeting..."
    
    local test_dir="$SCRIPT_DIR/test-platform-output"
    rm -rf "$test_dir"
    mkdir -p "$test_dir"
    
    # Create test Dockerfile that reports architecture
    cat > "$test_dir/Dockerfile.platform" << 'EOF'
FROM --platform=linux/amd64 alpine:latest

RUN echo "Container architecture: $(uname -m)" && \
    echo "Expected: x86_64" && \
    if [ "$(uname -m)" = "x86_64" ]; then \
        echo "SUCCESS: Platform targeting worked"; \
    else \
        echo "ERROR: Platform targeting failed"; \
        exit 1; \
    fi

FROM scratch AS export
RUN echo "Platform test completed" > /platform-test.txt
EOF

    log_info "Testing platform targeting with Docker build..."
    
    if docker buildx version &> /dev/null; then
        if docker buildx build \
            --platform linux/amd64 \
            -f "$test_dir/Dockerfile.platform" \
            "$SCRIPT_DIR" &> "$test_dir/build.log"; then
            log_success "✓ Platform targeting test passed"
        else
            log_error "✗ Platform targeting test failed"
            log_info "Build log:"
            cat "$test_dir/build.log" | sed 's/^/  /'
            rm -rf "$test_dir"
            return 1
        fi
    else
        log_warning "Skipping platform targeting test (buildx not available)"
    fi
    
    # Cleanup
    rm -rf "$test_dir"
    return 0
}

# Function to test the actual build script
test_build_script() {
    log_info "Testing build script dry run..."
    
    # Check if build script exists and is executable
    if [ ! -x "$SCRIPT_DIR/build-standalone.sh" ]; then
        log_error "build-standalone.sh is not executable"
        return 1
    fi
    
    # Test help option
    if "$SCRIPT_DIR/build-standalone.sh" --help &> /dev/null; then
        log_success "✓ Build script help option works"
    else
        log_warning "Build script help option may not work correctly"
    fi
    
    log_success "✓ Build script basic tests passed"
    return 0
}

# Main test function
main() {
    echo "========================================"
    echo "  Build Process Test Suite"
    echo "========================================"
    echo
    
    local tests_passed=0
    local tests_failed=0
    
    # Test 1: Docker prerequisites
    if test_docker_prerequisites; then
        ((tests_passed++))
    else
        ((tests_failed++))
        log_error "Docker prerequisites test failed - cannot continue"
        exit 1
    fi
    echo
    
    # Test 2: Platform targeting
    if test_platform_targeting; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    echo
    
    # Test 3: Nginx extraction
    if test_nginx_extraction; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    echo
    
    # Test 4: Build script
    if test_build_script; then
        ((tests_passed++))
    else
        ((tests_failed++))
    fi
    echo
    
    # Summary
    echo "========================================"
    echo "  Test Results Summary"
    echo "========================================"
    echo
    log_info "Tests passed: $tests_passed"
    if [ "$tests_failed" -gt 0 ]; then
        log_error "Tests failed: $tests_failed"
        echo
        log_error "❌ Some tests failed. Please check the issues above."
        exit 1
    else
        log_success "Tests failed: $tests_failed"
        echo
        log_success "🎉 All tests passed! Build process should work correctly."
        echo
        log_info "You can now run the full build:"
        log_info "  ./build-standalone.sh develop"
        log_info "  ./build-standalone.sh product"
    fi
}

# Show usage
show_usage() {
    echo "Usage: $0"
    echo ""
    echo "This script tests the Docker build process to ensure it works correctly"
    echo "before running the full build. It verifies:"
    echo "  - Docker prerequisites"
    echo "  - Platform targeting (linux/amd64)"
    echo "  - Nginx extraction with file command"
    echo "  - Build script functionality"
    echo ""
    echo "Run this test before building deployment packages to catch issues early."
}

# Parse command line arguments
case "${1:-}" in
    -h|--help)
        show_usage
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
