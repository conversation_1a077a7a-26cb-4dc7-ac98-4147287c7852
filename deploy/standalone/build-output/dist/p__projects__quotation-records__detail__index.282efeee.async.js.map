{"version": 3, "file": "p__projects__quotation-records__detail__index.282efeee.async.js", "mappings": "6KACIA,EAAmB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,qrBAAsrB,CAAE,CAAC,CAAE,EAAG,KAAQ,aAAc,MAAS,QAAS,EACp4B,EAAeA,E,YCIX,EAAmB,SAA0BC,EAAOC,EAAK,CAC3D,OAAoB,gBAAoBC,GAAA,KAAU,QAAc,KAAc,CAAC,EAAGF,CAAK,EAAG,CAAC,EAAG,CAC5F,IAAKC,EACL,KAAM,CACR,CAAC,CAAC,CACJ,EACIE,EAAuB,aAAiB,CAAgB,EAI5D,EAAeA,C,wECVXC,GAAc,SAAqBJ,EAAOC,EAAK,CACjD,OAAoB,gBAAoB,OAAU,QAAc,KAAc,CAAC,EAAGD,CAAK,EAAG,CAAC,EAAG,CAC5F,IAAKC,EACL,KAAM,GACR,CAAC,CAAC,CACJ,EACIE,EAAuB,aAAiBC,EAAW,EAIvD,KAAeD,C,oFCfXE,EAAoB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAC,EAAG,SAAY,CAAC,CAAE,IAAO,QAAS,MAAS,CAAC,CAAE,CAAC,CAAE,EAAG,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,qWAAsW,CAAE,EAAG,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,8kBAA+kB,CAAE,CAAC,CAAE,EAAG,KAAQ,aAAc,MAAS,UAAW,EAC3vC,EAAeA,E,YCIX,EAAoB,SAA2BL,EAAOC,EAAK,CAC7D,OAAoB,gBAAoBC,GAAA,KAAU,QAAc,KAAc,CAAC,EAAGF,CAAK,EAAG,CAAC,EAAG,CAC5F,IAAKC,EACL,KAAM,CACR,CAAC,CAAC,CACJ,EACIE,EAAuB,aAAiB,CAAiB,EAI7D,EAAeA,C,oFCfXG,EAAkB,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,44BAA64B,CAAE,CAAC,CAAE,EAAG,KAAQ,UAAW,MAAS,UAAW,EACzlC,EAAeA,E,YCIX,EAAkB,SAAyBN,EAAOC,EAAK,CACzD,OAAoB,gBAAoBC,GAAA,KAAU,QAAc,KAAc,CAAC,EAAGF,CAAK,EAAG,CAAC,EAAG,CAC5F,IAAKC,EACL,KAAM,CACR,CAAC,CAAC,CACJ,EACIE,EAAuB,aAAiB,CAAe,EAI3D,EAAeA,C,oFCfXI,EAAe,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,6dAA8d,CAAE,CAAC,CAAE,EAAG,KAAQ,OAAQ,MAAS,UAAW,EACpqB,EAAeA,E,YCIX,EAAe,SAAsBP,EAAOC,EAAK,CACnD,OAAoB,gBAAoBC,GAAA,KAAU,QAAc,KAAc,CAAC,EAAGF,CAAK,EAAG,CAAC,EAAG,CAC5F,IAAKC,EACL,KAAM,CACR,CAAC,CAAC,CACJ,EACIE,EAAuB,aAAiB,CAAY,EAIxD,EAAeA,C,6KCTXK,EAAe,SAAsBR,GAAOC,GAAK,CACnD,OAAoB,gBAAoBC,EAAA,KAAU,KAAS,CAAC,EAAGF,GAAO,CACpE,IAAKC,GACL,KAAM,GACR,CAAC,CAAC,CACJ,EAGIE,EAAuB,aAAiBK,CAAY,EAIxD,EAAeL,E,mKCbXM,GAAY,CAAC,gBAAiB,YAAa,gBAAiB,qBAAsB,SAAU,aAAc,eAAgB,WAAY,iBAAiB,EACzJC,GAAa,CAAC,SAAU,WAAY,oBAAqB,gBAAiB,YAAa,OAAO,EAc5FC,GAA0C,gBAAoB,MAAS,EAG3E,SAASC,GAAcZ,EAAO,CAC5B,IAAIa,GAAWb,EAAM,SACnBc,GAASd,EAAM,OACfe,EAAWf,EAAM,SACjBgB,GAAgBhB,EAAM,cACtBiB,GAAYjB,EAAM,UAChBkB,KAAY,cAAWP,EAA0B,EACrD,OAAoB,eAAmBE,MAAU,QAAc,KAAc,CAAC,EAAGA,GAAS,KAAK,EAAG,CAAC,EAAG,CACpG,QAAS,UAAY,CACnB,IAAIM,MAAW,SAAgC,KAAoB,EAAE,KAAK,SAASC,GAAQC,GAAG,CAC5F,IAAIC,GAAuBC,GAAiBC,GACxCC,GACJ,SAAO,KAAoB,EAAE,KAAK,SAAkBC,GAAU,CAC5D,OAAU,OAAQA,GAAS,KAAOA,GAAS,KAAM,CAC/C,IAAK,GACH,OAAAA,GAAS,KAAO,GACRJ,IAAyBC,GAAkBV,GAAS,OAAO,WAAa,MAAQS,KAA0B,OAAS,OAASA,GAAsB,KAAKC,GAAiBF,EAAC,EACnL,IAAK,GAEH,GADAI,GAAOC,GAAS,KACVD,KAAS,GAAQ,CACrBC,GAAS,KAAO,EAChB,KACF,CACA,OAAOA,GAAS,OAAO,QAAQ,EACjC,IAAK,GACHR,GAAc,OAAiCM,GAAqBN,EAAU,WAAa,MAAQM,KAAuB,QAAUA,GAAmB,cAAcV,GAAQ,CAC3K,SAAUC,EACV,cAAeC,GACf,UAAWC,EACb,CAAC,EACH,IAAK,GACL,IAAK,MACH,OAAOS,GAAS,KAAK,CACzB,CACF,EAAGN,EAAO,CACZ,CAAC,CAAC,EACF,SAASO,GAAQC,GAAI,CACnB,OAAOT,GAAS,MAAM,KAAM,SAAS,CACvC,CACA,OAAOQ,EACT,EAAE,CACJ,CAAC,CAAC,CACJ,CAOA,SAASE,EAAc7B,EAAO,CAC5B,IAAI8B,GAAkBC,GAClBC,KAAO,OAAQ,EACfC,GAAgBjC,EAAM,cACxBkC,GAAYlC,EAAM,UAClBmC,EAAgBnC,EAAM,cACtBoC,GAAqBpC,EAAM,mBAC3BqC,GAASrC,EAAM,OACfsC,GAAatC,EAAM,WACnBuC,GAAevC,EAAM,aACrBwC,GAAWxC,EAAM,SACjByC,GAAkBzC,EAAM,gBACxB0C,MAAO,KAAyB1C,EAAOS,EAAS,EAC9CkC,MAAU,UAAO,MAAS,EAC1BzB,MAAY,UAAO,EACnB0B,MAAU,UAAO,KAGrB,uBAAoBF,GAAK,UAAW,UAAY,CAC9C,OAAOxB,GAAU,OACnB,EAAG,CAACA,GAAU,OAAO,CAAC,EACtB,IAAI2B,MAAkBC,GAAA,GAAe,UAAY,CAC7C,OAAO9C,EAAM,OAASuC,IAAgB,CAAC,CACzC,EAAG,CACD,MAAOvC,EAAM,MACb,SAAUA,EAAM,QAClB,CAAC,EACD+C,MAAmB,KAAeF,GAAiB,CAAC,EACpDG,GAAQD,GAAiB,CAAC,EAC1BE,GAAWF,GAAiB,CAAC,EAC3BG,GAAY,UAAc,UAAY,CACxC,OAAI,OAAOb,IAAW,WACbA,GAEF,SAAUvB,EAAQqC,EAAO,CAC9B,OAAOrC,EAAOuB,EAAM,GAAKc,CAC3B,CACF,EAAG,CAACd,EAAM,CAAC,EAOPe,MAAcC,GAAA,GAAe,SAAUC,EAAc,CAIvD,GAAI,OAAOA,GAAiB,UAAY,CAACtD,EAAM,KAAM,CACnD,GAAIsD,GAAgBN,GAAM,OAAQ,OAAOM,EACzC,IAAIC,EAAUP,IAASA,GAAMM,CAAY,EACzC,OAAOJ,IAAc,KAA+B,OAASA,GAAUK,EAASD,CAAY,CAC9F,CAKA,IAAK,OAAOA,GAAiB,UAAYA,GAAgBN,GAAM,SAAWhD,EAAM,KAAM,CACpF,IAAIwD,EAAYR,GAAM,UAAU,SAAUS,EAAMN,EAAO,CACrD,IAAIO,EACJ,OAAQR,IAAc,OAAiCQ,EAAaR,GAAUO,EAAMN,CAAK,KAAO,MAAQO,IAAe,OAAS,OAASA,EAAW,SAAS,MAAQJ,GAAiB,KAAkC,OAASA,EAAa,SAAS,EACzP,CAAC,EACD,GAAIE,IAAc,GAAI,OAAOA,CAC/B,CACA,OAAOF,CACT,CAAC,KAGD,uBAAoBb,GAAiB,UAAY,CAM/C,IAAIkB,EAAa,SAAoBC,EAAU,CAC7C,IAAIC,EAAuBC,EAC3B,GAAIF,GAAY,KACd,MAAM,IAAI,MAAM,sBAAsB,EAExC,IAAIN,EAAeF,GAAYQ,CAAQ,EACnCG,EAAa,CAAC/D,EAAM,MAAO6D,EAAwBP,GAAiB,KAAkC,OAASA,EAAa,SAAS,KAAO,MAAQO,IAA0B,OAASA,EAAwB,EAAE,EAAE,KAAK,CAAC,EAAE,OAAO,OAAO,EAC7O,OAAQC,EAAmBlB,GAAQ,WAAa,MAAQkB,IAAqB,OAAS,OAASA,EAAiB,cAAcC,CAAU,CAC1I,EAMIC,EAAc,UAAuB,CACvC,IAAIC,EACAF,EAAa,CAAC/D,EAAM,IAAI,EAAE,KAAK,CAAC,EAAE,OAAO,OAAO,EACpD,GAAI,MAAM,QAAQ+D,CAAU,GAAKA,EAAW,SAAW,EAAG,CACxD,IAAIG,EACAX,GAAWW,EAAoBtB,GAAQ,WAAa,MAAQsB,IAAsB,OAAS,OAASA,EAAkB,eAAe,EACzI,OAAI,MAAM,QAAQX,CAAO,EAAUA,EAC5B,OAAO,KAAKA,CAAO,EAAE,IAAI,SAAUY,EAAK,CAC7C,OAAOZ,EAAQY,CAAG,CACpB,CAAC,CACH,CACA,OAAQF,EAAoBrB,GAAQ,WAAa,MAAQqB,IAAsB,OAAS,OAASA,EAAkB,cAAcF,CAAU,CAC7I,EACA,SAAO,QAAc,KAAc,CAAC,EAAGnB,GAAQ,OAAO,EAAG,CAAC,EAAG,CAC3D,WAAYe,EACZ,YAAaK,EAOb,WAAY,SAAoBJ,EAAUQ,EAAM,CAC9C,IAAIC,EAAwBC,EAC5B,GAAIV,GAAY,KACd,MAAM,IAAI,MAAM,sBAAsB,EAExC,IAAIN,EAAeF,GAAYQ,CAAQ,EACnCG,EAAa,CAAC/D,EAAM,MAAOqE,EAAyBf,GAAiB,KAAkC,OAASA,EAAa,SAAS,KAAO,MAAQe,IAA2B,OAASA,EAAyB,EAAE,EAAE,KAAK,CAAC,EAAE,OAAO,OAAO,EAC5OE,EAAa,OAAO,OAAO,CAAC,KAAG,QAAc,KAAc,CAAC,EAAGZ,EAAWC,CAAQ,CAAC,EAAGQ,GAAQ,CAAC,CAAC,CAAC,EACjGI,MAAeC,GAAA,GAAI,CAAC,EAAGV,EAAYQ,CAAU,EACjD,OAACD,EAAoB1B,GAAQ,WAAa,MAAQ0B,IAAsB,QAAUA,EAAkB,eAAeE,EAAY,EACxH,EACT,CACF,CAAC,CACH,EAAG,CAACpB,GAAapD,EAAM,KAAM4C,GAAQ,OAAO,CAAC,KAC7C,aAAU,UAAY,CACf5C,EAAM,aACVgD,IAAS,CAAC,GAAG,QAAQ,SAAU0B,EAASvB,EAAO,CAC9C,IAAIwB,GACHA,EAAoB/B,GAAQ,WAAa,MAAQ+B,IAAsB,QAAUA,EAAkB,kBAAe,KAAgB,CAAC,EAAG,GAAG,OAAOzB,GAAUwB,EAASvB,CAAK,CAAC,EAAGuB,CAAO,CAAC,CACvL,EAAG,CAAC,CAAC,CAEP,EAAG,IAACE,EAAA,IAAU5B,EAAK,EAAGhD,EAAM,UAAU,CAAC,KACvC,aAAU,UAAY,CACpB,GAAIA,EAAM,KAAM,CACd,IAAI6E,EACJjC,GAAQ,QAAU5C,GAAU,OAA6B6E,EAAkB7E,EAAM,YAAc,MAAQ6E,IAAoB,OAAS,OAASA,EAAgB,IAC/J,CACF,EAAG,EAAE/C,GAAmB9B,EAAM,YAAc,MAAQ8B,KAAqB,OAAS,OAASA,GAAiB,KAAM9B,EAAM,IAAI,CAAC,EAC7H,IAAI8E,GAAO1C,IAAsB,CAAC,EAChCtB,GAASgE,GAAK,OACd/D,GAAW+D,GAAK,SAChBC,GAAoBD,GAAK,kBACzB9D,GAAgB8D,GAAK,cACrB7D,GAAY6D,GAAK,UACjBE,GAAQF,GAAK,MACbG,MAAkB,KAAyBH,GAAMpE,EAAU,EACzDwE,GAAQnE,KAAa,MACrBoE,MAAmB,WAAQ,UAAY,CACzC,OAAI,OAAOjD,IAAc,UAAYA,KAAcc,IAAU,KAA2B,OAASA,GAAM,QAC9F,GAEFZ,KAAuB,OAAsB,QAAKxB,GAAe,CACtE,UAAQwE,GAAA,GAAYtE,GAAQkC,IAAU,KAA2B,OAASA,GAAM,OAAQA,EAAK,GAAK,CAAC,EACnG,SAAUjC,GACV,aAAWqE,GAAA,GAAYnE,GAAW+B,IAAU,KAA2B,OAASA,GAAM,OAAQA,EAAK,EACnG,cAAehC,GACf,YAAuB,QAAK,SAAQ,QAAc,KAAc,CAC9D,KAAM,SACN,SAAO,KAAc,CACnB,QAAS,QACT,OAAQ,SACR,MAAO,MACT,EAAGgE,EAAK,EACR,QAAmB,QAAK,EAAc,CAAC,CAAC,CAC1C,EAAGC,EAAe,EAAG,CAAC,EAAG,CACvB,SAAUF,IAAqB/C,EAAK,WAAW,2BAA4B,sCAAQ,CACrF,CAAC,CAAC,CACJ,CAAC,CAEH,EAAG,CAACI,GAAoBF,GAAWc,IAAU,KAA2B,OAASA,GAAM,MAAM,CAAC,EAC1FqC,MAAoB,WAAQ,UAAY,CAC1C,OAAKF,GAGDD,GACK,CACL,WAAY,CACV,OAAQ,CACN,QAAS,SAAiBI,EAAO,CAC/B,IAAIC,EACAC,EAAYF,EAAM,UACpBzE,EAAWyE,EAAM,SACnB,SAAoB,SAAM,QAAS,CACjC,UAAWE,EACX,SAAU,CAAC3E,KAAuB,SAAM,KAAM,CAC5C,MAAO,CACL,SAAU,UACZ,EACA,SAAU,IAAc,QAAK,KAAM,CACjC,QAAS,EACT,MAAO,CACL,WAAY,QACd,EACA,SAAUsE,EACZ,CAAC,KAAgB,QAAK,KAAM,CAC1B,MAAO,CACL,SAAU,WACV,KAAM,EACN,MAAO,MACT,EACA,SAAUI,EAAgB7C,GAAK,WAAa,MAAQ6C,IAAkB,OAAS,OAASA,EAAc,OACtG,SAAUJ,EACZ,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,CACH,CACF,CACF,CACF,EAEK,CACL,gBAAiB,SAAyBM,EAAGC,EAAK,CAChD,IAAIC,EAAuBC,EAC3B,SAAoB,SAAM,YAAW,CACnC,SAAU,EAAED,GAAyBC,EAAyB5F,EAAM,mBAAqB,MAAQ4F,IAA2B,OAAS,OAASA,EAAuB,KAAK5F,EAAOyF,EAAGC,CAAG,KAAO,MAAQC,IAA0B,OAASA,EAAwBD,EAAKP,EAAgB,CACxR,CAAC,CACH,CACF,EA7CS,CAAC,CA+CZ,EAAG,CAACD,GAAOC,EAAgB,CAAC,EACxBU,KAAgB,KAAc,CAAC,EAAG7F,EAAM,QAAQ,EAOhD8F,KAAmBzC,GAAA,GAAe,SAAU0C,EAAGC,EAAY,CAC7D,IAAIC,EAAkBC,EAAuBC,EAG7C,IAFCF,EAAmBjG,EAAM,YAAc,MAAQiG,IAAqB,SAAWC,EAAwBD,EAAiB,kBAAoB,MAAQC,IAA0B,QAAUA,EAAsB,KAAKD,EAAkBF,EAAGC,CAAU,GAClPG,EAAwBnG,EAAM,kBAAoB,MAAQmG,IAA0B,QAAUA,EAAsB,KAAKnG,EAAOgG,EAAYD,CAAC,EAC1I/F,EAAM,WAAY,CACpB,IAAIoG,EACJpG,GAAU,OAA6BoG,EAAkBpG,EAAM,YAAc,MAAQoG,IAAoB,QAAUA,EAAgB,KAAKpG,EAAOgG,CAAU,CAC3J,CACF,CAAC,EACD,OAAIhG,GAAU,MAA4BA,EAAM,iBAAmB+B,GAAmB/B,EAAM,YAAc,MAAQ+B,KAAqB,QAAUA,GAAiB,gBAElK/B,EAAM,YAAcA,IAAU,MAAQA,IAAU,QAAUA,EAAM,YAC9D6F,EAAc,eAAiBC,MAEb,SAAM,YAAW,CACnC,SAAU,IAAc,QAAKnF,GAA2B,SAAU,CAChE,MAAOO,GACP,YAAuB,QAAK,QAAU,QAAc,QAAc,KAAc,CAC9E,OAAQ,GACR,QAAS,GACT,WAAY,GACZ,OAAQmB,GACR,kBAAmB,EACrB,EAAGK,EAAI,EAAG2C,EAAiB,EAAG,CAAC,EAAG,CAChC,YAAa,QACb,UAAWnE,GACX,SAAUe,GACV,YAAU,QAAc,KAAc,CAAC,EAAG4D,CAAa,EAAG,CAAC,EAAG,CAC5D,aAAW,KAAc,CACvB,QAASjD,EACX,EAAGiD,EAAc,SAAS,CAC5B,CAAC,EACD,WAAY7C,GACZ,mBAAoB,SAA4BgD,EAAY,CAK1D,GAJA/C,GAAS+C,CAAU,EAIfhG,EAAM,MAAQe,KAAa,MAAO,CACpC,IAAIsF,EACAC,KAAW7B,GAAA,GAAI,CAAC,EAAG,CAACzE,EAAM,IAAI,EAAE,KAAK,CAAC,EAAE,OAAO,OAAO,EAAGgG,CAAU,GACtEK,EAAoBzD,GAAQ,WAAa,MAAQyD,IAAsB,QAAUA,EAAkB,eAAeC,CAAQ,CAC7H,CACF,CACF,CAAC,CAAC,CACJ,CAAC,EAAGtG,EAAM,QAAoB,QAAK,IAAmB,CACpD,KAAM,CAACA,EAAM,IAAI,EACjB,SAAU,SAAkBuG,EAAa,CACvC,IAAIC,EAAkBC,EACtB,GAAI,CAAC9D,GAAQ,QACX,OAAAA,GAAQ,QAAUK,GACX,KAET,IAAI0D,KAAOC,GAAA,GAAIJ,EAAa,CAACvG,EAAM,IAAI,EAAE,KAAK,CAAC,CAAC,EAC5C4G,EAAaF,GAAS,KAA0B,OAASA,EAAK,KAAK,SAAUjD,EAAMN,EAAO,CAC5F,IAAI0D,EACJ,MAAO,IAACC,GAAA,GAAiBrD,GAAOoD,EAAmBlE,GAAQ,WAAa,MAAQkE,IAAqB,OAAS,OAASA,EAAiB1D,CAAK,CAAC,CAChJ,CAAC,EAED,OADAR,GAAQ,QAAUK,GACb4D,IAEL5G,GAAU,OAA6BwG,EAAmBxG,EAAM,YAAc,MAAQwG,IAAqB,SAAWC,EAAwBD,EAAiB,kBAAoB,MAAQC,IAA0B,QAAUA,EAAsB,KAAKD,EAAkBI,EAAYF,CAAI,GACrR,IACT,CACF,CAAC,EAAI,IAAI,CACX,CAAC,CACH,CAOA,SAASK,GAAmB/G,EAAO,CACjC,IAAIgH,GAAO,MAAQ,gBAAgB,EACnC,OAAKhH,EAAM,QAMS,QAAK,KAAK,QAAM,QAAc,KAAc,CAC9D,MAAO,CACL,SAAU,MACZ,CACF,EAAGA,GAAU,KAA2B,OAASA,EAAM,aAAa,EAAG,CAAC,EAAG,CACzE,KAAMA,EAAM,KACZ,aAAc,SAAsBiH,EAAMC,GAAM,CAC9C,IAAIC,GAAO,CAACnH,EAAM,IAAI,EAAE,KAAK,CAAC,EAC9B,GAAI,CACF,OAAO,KAAK,aAAU2G,GAAA,GAAIM,EAAME,EAAI,CAAC,IAAM,KAAK,aAAUR,GAAA,GAAIO,GAAMC,EAAI,CAAC,CAC3E,OAASC,EAAO,CACd,MAAO,EACT,CACF,EACA,YAAuB,QAAKvF,KAAe,QAAc,KAAc,CACrE,YAAa,QACb,OAAQ,CACN,EAAG,aACL,CACF,EAAG7B,CAAK,EAAG,CAAC,EAAG,CACb,YAAU,QAAc,KAAc,CAAC,EAAGA,EAAM,QAAQ,EAAG,CAAC,EAAG,CAC7D,KAAMgH,EACR,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CAAC,KA9BmC,QAAKnF,KAAe,KAAc,CACrE,YAAa,QACb,OAAQ,CACN,EAAG,aACL,CACF,EAAG7B,CAAK,CAAC,CA0BX,CACA+G,GAAmB,cAAgBnG,GACnC,OAAemG,E,mDClZTM,EAA8B,SAAHvC,GAA2B,KAAAwC,EAAAxC,GAArByC,MAAAA,EAAKD,IAAA,OAAG,OAAMA,EACnD,SACEE,EAAAA,MAAA,OAAKC,QAAQ,aAAaC,MAAM,KAAKC,OAAO,KAAKC,UAAU,cAAa/G,SAAA,IACtEgH,EAAAA,KAAA,QAAAhH,YACEgH,EAAAA,KAAA,UACEC,GAAG,qBACHL,QAAQ,UACRM,YAAY,iBACZC,YAAY,KACZC,aAAa,KACbC,KAAK,IACLC,KAAK,IACLC,OAAO,OACPC,KAAMd,EAAM1G,YAEZgH,EAAAA,KAAA,QAAMS,EAAE,gGAAgG,CAAO,CAAC,CAC1G,CAAC,CACL,KACNT,EAAAA,KAAA,QACEU,GAAG,IACHC,GAAG,IACHC,GAAG,MACHC,GAAG,IACHC,YAAY,IACZC,OAAQrB,EACRsB,UAAU,0BAA0B,CAC/B,CAAC,EACL,CAET,EAEA,KAAexB,C,kLClCTyB,GAA2C,SAAHhE,GAA8B,KAAxBnD,EAAOmD,GAAPnD,QAAY3B,GAAK+I,EAAAA,EAAAjE,GAAArE,CAAA,EACnEuI,MAA8BC,EAAAA,UAAkB,EAAK,EAACC,GAAAC,EAAAA,EAAAH,GAAA,GAA/CI,GAAOF,GAAA,GAAEG,GAAUH,GAAA,GACpBI,GAAsC,eAAAhE,GAAAiE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAArI,GAAOsI,GAAO,CAAF,IAAAC,GAAA,OAAAH,EAAAA,EAAA,EAAAI,KAAA,SAAAlI,GAAE,CAAF,cAAAA,GAAAuF,KAAAvF,GAAAwF,KAAE,CAAF,OACzDmC,OAAAA,GAAW,EAAI,EAAC3H,GAAAuF,KAAA,EAAAvF,GAAAwF,KAAA,EAEOvF,GAAO,YAAPA,EACnB+H,EACF,EAAC,OAFKC,OAAAA,GAAMjI,GAAAmI,KAGZR,GAAW,EAAK,EAAC3H,GAAAoI,OAAA,SACVH,EAAM,SAAAjI,OAAAA,GAAAuF,KAAA,EAAAvF,GAAAqI,GAAArI,GAAA,SAEb2H,GAAW,EAAK,EAAC3H,GAAAoI,OAAA,SACV,EAAE,2BAAApI,GAAAsI,KAAA,IAAA5I,GAAA,cAEZ,mBAZ2CQ,GAAA,QAAA0D,GAAA2E,MAAA,KAAAC,SAAA,MAc5C,SAAOrC,EAAAA,KAACsC,EAAAA,GAAMC,EAAAA,EAAAA,EAAAA,EAAA,CAAChB,QAASA,EAAQ,EAAKpJ,EAAK,MAAE2B,QAAS2H,EAAe,EAAE,CACxE,EAEA,KAAeR,E,oFCTTuB,EAA0C,CAC9CC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,UAAW,UACXC,SAAU,UACVC,UAAW,UACXC,SAAU,UAEVC,QAAS,UACTC,KAAM,UACNC,UAAW,UACXC,QAAS,UACTC,OAAQ,UAERC,KAAM,UACNC,SAAU,SACZ,EAEMC,EAEoC,SAAHvG,EAMjC,KALJwG,EAAMxG,EAANwG,OACAC,EAAQzG,EAARyG,SACAC,EAAW1G,EAAX0G,YACOC,EAAS3G,EAAhB4G,MACAlG,EAASV,EAATU,UAEM+B,GAAQ6C,EAAAA,EAAAA,EAAAA,EAAA,GAAKC,CAAe,EAAKkB,CAAQ,EAAGD,CAAM,EAClDI,GAAQD,MAAaE,EAAAA,IAAQ,GAADC,OAAIJ,EAAW,KAAAI,OAAIN,CAAM,CAAE,EAC7D,SACEzD,GAAAA,KAACgE,EAAAA,EAAG,CAACrG,UAAWA,EAAW+B,MAAOA,GAAM1G,SACrC6K,EAAK,CACH,CAET,EAEA,KAAeL,C,6DC9CTS,EAAa,UAAM,CACvB,IAAMC,EAAwB,CAC5B,CAAE/I,MAAO,UAAW0I,SAAOC,EAAAA,IAAQ,0BAA0B,CAAE,EAC/D,CAAE3I,MAAO,YAAa0I,SAAOC,EAAAA,IAAQ,4BAA4B,CAAE,EACnE,CAAE3I,MAAO,eAAgB0I,SAAOC,EAAAA,IAAQ,+BAA+B,CAAE,EACzE,CACE3I,MAAO,WACP0I,SAAOC,EAAAA,IAAQ,4CAA4C,CAC7D,EACA,CACE3I,MAAO,WACP0I,SAAOC,EAAAA,IAAQ,0CAA0C,CAC3D,CAAC,EAGGK,EAGA,CACJ,CAAEhJ,MAAO,UAAW0I,SAAOC,EAAAA,IAAQ,SAAS,CAAE,EAC9C,CAAE3I,MAAO,gBAAiB0I,SAAOC,EAAAA,IAAQ,eAAe,CAAE,EAC1D,CAAE3I,MAAO,WAAY0I,SAAOC,EAAAA,IAAQ,UAAU,CAAE,EAChD,CAAE3I,MAAO,gBAAiB0I,SAAOC,EAAAA,IAAQ,eAAe,CAAE,CAAC,EAGvDM,EAA0B,CAC9B,CAAEP,SAAOC,EAAAA,IAAQ,mBAAmB,EAAG3I,MAAO,gBAAiB,EAC/D,CAAE0I,SAAOC,EAAAA,IAAQ,mBAAmB,EAAG3I,MAAO,SAAU,EACxD,CAAE0I,SAAOC,EAAAA,IAAQ,aAAa,EAAG3I,MAAO,WAAY,CAAC,EAGjDkJ,EAA+B,CACnC,CAAER,SAAOC,EAAAA,IAAQ,mBAAmB,EAAG3I,MAAO,OAAQ,EACtD,CACE0I,SAAOC,EAAAA,IAAQ,2BAA2B,EAC1C3I,MAAO,qBACT,EACA,CAAE0I,SAAOC,EAAAA,IAAQ,iBAAiB,EAAG3I,MAAO,iBAAkB,EAC9D,CAAE0I,SAAOC,EAAAA,IAAQ,cAAc,EAAG3I,MAAO,uBAAwB,CAAC,EAG9DmJ,EAAe,CACnBC,aAAWT,EAAAA,IAAQ,eAAe,EAClCU,aAAWV,EAAAA,IAAQ,kBAAkB,EACrCW,MAAIX,EAAAA,IAAQ,MAAM,CACpB,EAEMY,EAAU,CACdC,UAAQb,EAAAA,IAAQ,kBAAkB,EAClCc,kBAAgBd,EAAAA,IAAQ,kBAAkB,EAC1Ce,cAAYf,EAAAA,IAAQ,gBAAgB,CACtC,EAEMgB,KAAmBC,EAAAA,MAAKL,EAAS,YAAY,EAE7CM,EAAiB,CACrBC,iCAA+BnB,EAAAA,IAAQ,eAAe,EACtDoB,8BAA4BpB,EAAAA,IAAQ,gBAAgB,CACtD,EAEMqB,EAAY,CAChBC,cAAYtB,EAAAA,IAAQ,OAAO,EAC3BuB,iBAAevB,EAAAA,IAAQ,eAAe,EACtCwB,cAAYxB,EAAAA,IAAQ,YAAY,CAClC,EAEMyB,GAAuB,CAC3BC,SAAO1B,EAAAA,IAAQ,OAAO,EACtB2B,aAAW3B,EAAAA,IAAQ,QAAQ,EAC3B4B,WAAS5B,EAAAA,IAAQ,SAAS,CAC5B,EAEM6B,GAAsB,CAC1BvC,WAASU,EAAAA,IAAQ,sCAAsC,EACvD8B,QAAM9B,EAAAA,IAAQ,qCAAqC,EACnD+B,cAAY/B,EAAAA,IAAQ,sCAAsC,CAC5D,EAEMgC,GAAmB,CACvBC,WAASjC,EAAAA,IAAQ,4CAA4C,EAC7DX,aAAWW,EAAAA,IAAQ,4CAA4C,EAC/Db,WAASa,EAAAA,IAAQ,4CAA4C,EAC7DkC,WAASlC,EAAAA,IAAQ,4CAA4C,EAC7DT,UAAQS,EAAAA,IAAQ,2CAA2C,CAC7D,EAEMmC,EAAc,CAClBC,WAASpC,EAAAA,IAAQ,SAAS,EAC1BlB,WAASkB,EAAAA,IAAQ,MAAM,EACvBvE,SAAOuE,EAAAA,IAAQ,aAAa,EAC5BqC,QAAMrC,EAAAA,IAAQ,MAAM,CACtB,EAEA,MAAO,CACLI,sBAAAA,EACAC,oBAAAA,EACAC,aAAAA,EACAC,kBAAAA,EACAK,QAAAA,EACAJ,aAAAA,EACAQ,iBAAAA,EACAE,eAAAA,EACAG,UAAAA,EACAI,qBAAAA,GACAO,iBAAAA,GACAH,oBAAAA,GACAM,YAAAA,CACF,CACF,EACA,KAAehC,C,0ECxGTmC,GAAe,SAAHnJ,EAKuB,KAJvCoJ,EAAYpJ,EAAZoJ,aACAC,EAAcrJ,EAAdqJ,eACAC,EAAQtJ,EAARsJ,SAAQC,EAAAvJ,EACRwJ,YAAAA,EAAWD,IAAA,OAAG,SAACE,EAAM,CAAF,SAAK1G,EAAAA,KAAA2G,EAAAA,SAAA,CAAA3N,SAAG0N,CAAC,CAAG,CAAC,EAAAF,EAEhC,GAAI,CAACF,EAAeM,OAClB,OAAOH,EAAYJ,CAAY,EAGjC,IAAMQ,EAAYP,EAAeQ,IAAI,SAACC,EAAG,CAAF,MAAM,CAC3ClD,MAAO4C,EAAYM,CAAC,EACpBzK,IAAKyK,CACP,CAAC,CAAC,EACF,SACE/G,EAAAA,KAACgH,EAAAA,EAAQ,CACPC,KAAM,CAAEC,MAAOL,EAAW/M,QAAS,SAACqN,GAAM,CAAF,OAAKZ,EAASY,GAAK7K,GAAQ,CAAC,CAAC,EACrE8K,QAAS,CAAC,OAAO,EAAEpO,YAEnB2G,EAAAA,MAAC0H,EAAAA,EAAK,CAAArO,SAAA,CACHyN,EAAYJ,CAAY,KACzBrG,EAAAA,KAACsH,EAAAA,EAAY,EAAE,CAAC,EACX,CAAC,CACA,CAEd,EAEA,KAAelB,E,wfC1BA,SAASmB,GAAgBpP,EAA6B,KAAAqP,EAC3DrI,EAAmBhH,EAAnBgH,KAAMsI,EAAatP,EAAbsP,YAEdC,EAAAA,WAAU,UAAM,CACdvI,EAAKwI,eAAexP,GAAK,YAALA,EAAOyP,YAAY,CACzC,EAAG,CAACzP,GAAK,YAALA,EAAOyP,YAAY,CAAC,EACxB,IAAAzG,KAAkCC,EAAAA,UAAqB,GAAG,EAACC,EAAAC,EAAAA,EAAAH,EAAA,GAApD0G,EAASxG,EAAA,GAAEyG,EAAYzG,EAAA,GACxB0G,KAASC,GAAAA,WAAU,EACzB,SACErI,EAAAA,MAACsI,EAAAA,EAAS,CACRC,OAAO,aACPrI,MAAO,IACPsI,SAAOrE,EAAAA,IAAQ,sBAAsB,EACrCsD,QACEW,GAAM,OAAAP,EAANO,EAAQK,gBAAY,MAAAZ,IAAA,QAApBA,EAAsBa,SACpB,6CACF,KACErI,EAAAA,KAACsC,GAAAA,GAAM,CAAAtJ,YACJ8K,EAAAA,IAAQ,sBAAsB,CAAC,CAC1B,KAER9D,EAAAA,KAAA2G,EAAAA,SAAA,EAAI,EAGRxH,KAAMA,EACNmJ,oBAAmB,GACnBC,WAAY,CACVC,eAAgB,GAChBC,SAAU,kBAAMC,QAAQC,IAAI,KAAK,CAAC,CACpC,EACAC,cAAe,IACfnB,SAAQ,eAAAxK,EAAAyE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAArI,EAAOsP,EAAQ,CAAF,OAAAlH,EAAAA,EAAA,EAAAI,KAAA,SAAAlI,EAAE,CAAF,cAAAA,EAAAuF,KAAAvF,EAAAwF,KAAE,CAAF,OACrBoI,OAAAA,EAAS,CACPqB,cAAeD,GAAM,YAANA,EAAQC,cACvBC,YAAalB,CACf,CAAoB,EAAChO,EAAAoI,OAAA,SACd,EAAI,0BAAApI,EAAAsI,KAAA,IAAA5I,CAAA,EACZ,mBAAAQ,EAAA,QAAAkD,EAAAmF,MAAA,KAAAC,SAAA,MAACrJ,SAAA,IAEF2G,EAAAA,MAACqJ,GAAAA,EAAG,CAAAhQ,SAAA,IACFgH,EAAAA,KAACiJ,GAAAA,EAAG,CAACC,KAAM,GAAGlQ,YACZgH,EAAAA,KAACmJ,GAAAA,EAAW,CACV7J,KAAK,cACLuE,SAAOC,EAAAA,IAAQ,cAAc,EAC7BsF,MAAO,CAAC,CAAEC,SAAU,EAAK,CAAC,EAC1BC,SAAU,CAAEJ,KAAM,CAAE,EACpBK,WAAY,CAAEL,KAAM,EAAG,EACvBM,SAAQ,GACT,CAAC,CACC,KACLxJ,EAAAA,KAACiJ,GAAAA,EAAG,CAACC,KAAM,GAAGlQ,YACZgH,EAAAA,KAACyJ,GAAAA,EAAY,CACXnK,KAAK,gBACLuE,SAAOC,EAAAA,IAAQ,QAAQ,EACvBwF,SAAU,CAAEJ,KAAM,CAAE,EACpBK,WAAY,CAAEL,KAAM,EAAG,EACvBQ,cACE/J,EAAAA,MAACgK,GAAAA,EAAM,CACLxO,MAAO0M,EACP1K,MAAO,CAAE0C,MAAO,MAAO,EACvBlF,SAAU,SAACnB,EAAG,CAAF,OAAKsO,EAAatO,CAAC,CAAC,EAACR,SAAA,IAEjCgH,EAAAA,KAAC2J,GAAAA,EAAOC,OAAM,CAACzO,MAAM,IAAGnC,SAAC,GAAC,CAAe,KACzCgH,EAAAA,KAAC2J,GAAAA,EAAOC,OAAM,CAACzO,MAAM,KAAInC,SAAC,IAAE,CAAe,CAAC,EACtC,EAEVoQ,MAAO,CAAC,CAAEC,SAAU,EAAK,CAAC,EAC1BQ,IAAK,EAAI,CACV,CAAC,CACC,CAAC,EACH,KACLlK,EAAAA,MAACqJ,GAAAA,EAAG,CAAAhQ,SAAA,IACFgH,EAAAA,KAACiJ,GAAAA,EAAG,CAACC,KAAM,GAAGlQ,YACZgH,EAAAA,KAACyJ,GAAAA,EAAY,CACXH,SAAU,CAAEJ,KAAM,CAAE,EACpBK,WAAY,CAAEL,KAAM,EAAG,EACvB5J,KAAK,SACL8J,MAAO,CAAC,CAAEC,SAAU,EAAK,CAAC,EAC1BxF,MAAK,GAAAE,UAAKD,EAAAA,IAAQ,QAAQ,EAAC,OAC3B+F,IAAK,GACLC,IAAK,IACLN,SAAQ,GACT,CAAC,CACC,KACLxJ,EAAAA,KAACiJ,GAAAA,EAAG,CAACC,KAAM,GAAGlQ,YACZgH,EAAAA,KAACyJ,GAAAA,EAAY,CACXnK,KAAK,QACLuE,SAAOC,EAAAA,IAAQ,aAAa,EAC5BwF,SAAU,CAAEJ,KAAM,CAAE,EACpBK,WAAY,CAAEL,KAAM,EAAG,EACvBE,MAAO,CAAC,CAAEC,SAAU,EAAK,CAAC,EAC1BQ,IAAK,GACLC,IAAK,IACLN,SAAQ,GACT,CAAC,CACC,CAAC,EACH,CAAC,EACG,CAEf,C,gBC7GA,EAAe,CAAC,YAAc,sBAAsB,UAAY,oBAAoB,eAAiB,yBAAyB,aAAe,uBAAuB,UAAY,oBAAoB,WAAa,qBAAqB,OAAS,iBAAiB,SAAW,mBAAmB,YAAc,sBAAsB,QAAU,kBAAkB,MAAQ,gBAAgB,UAAY,oBAAoB,gBAAkB,0BAA0B,eAAiB,yBAAyB,mBAAqB,6BAA6B,uBAAyB,iCAAiC,UAAY,oBAAoB,UAAY,oBAAoB,iBAAmB,2BAA2B,mBAAqB,6BAA6B,YAAc,sBAAsB,cAAgB,wBAAwB,aAAe,sBAAsB,ECY11B,SAASO,GAAa5R,EAA0B,CAC7D,IAAQ6R,EAAc7R,EAAd6R,UACRC,KAAiCC,EAAAA,GAAa,EAAtCC,EAAWF,EAAXE,YAAaC,EAAOH,EAAPG,QACbC,EAA8BlS,EAA9BkS,aAAcC,EAAgBnS,EAAhBmS,YAChBC,EAAU,CACd,CACEpC,SAAOrE,EAAAA,IAAQ,OAAO,EACtB0G,UAAW,SACb,EACA,CACErC,SAAOsC,EAAAA,IAAK,EAAC,GAAA1G,UACND,EAAAA,IAAQ,cAAc,EAAC,UAAAC,UAAID,EAAAA,IAAQ,MAAM,EAAC,aAAAC,UAC1CD,EAAAA,IAAQ,cAAc,EAAC,UAAAC,UAAID,EAAAA,IAAQ,MAAM,EAAC,UACjD0G,aAAWC,EAAAA,IAAK,EAAI,UAAY,UAChCC,SAAU,EACZ,EACA,CACEvC,MAAO,MACPqC,UAAW,QACb,EACA,CACErC,SAAOrE,EAAAA,IAAQ,UAAU,EACzB0G,UAAW,SACb,EACA,CACErC,SAAOrE,EAAAA,IAAQ,MAAM,EACrB0G,UAAW,MACb,CAAC,EAEGG,EAAc,CAClBxM,WAAYkM,EACZO,SAAU,GACVC,WAAY,IACd,EAEMC,EAAe,eAAA7N,EAAAyE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAArI,GAAA,QAAAoI,EAAAA,EAAA,EAAAI,KAAA,SAAAlI,EAAA,eAAAA,EAAAuF,KAAAvF,EAAAwF,KAAA,QACtBiL,EAAY,EACZF,EAAQ,EAAC,wBAAAvQ,EAAAsI,KAAA,IAAA5I,CAAA,EACV,oBAHoB,QAAA0D,EAAAmF,MAAA,KAAAC,SAAA,MAKrB,SACE1C,EAAAA,MAACoL,EAAAA,EAASxI,EAAAA,EAAAA,EAAAA,EAAA,GACJ4H,CAAW,MACfhC,SAAOrE,EAAAA,IAAQ,eAAe,EAC9BnG,UAAWqN,EAAOC,aAClBpL,MAAO,IACPmK,UAAWA,EACXkB,UAAWJ,EAAgB9R,SAAA,IAE3BgH,EAAAA,KAAA,OAAKrC,UAAWqN,EAAO7C,MAAMnP,YAC1B8K,EAAAA,IAAQ,kCAAkC,CAAC,CACzC,KACL9D,EAAAA,KAACmL,GAAAA,EAAW5I,EAAAA,EAAAA,EAAAA,EAAA,GAAKoI,CAAW,MAAEJ,QAASA,EAAS/P,OAAO,QAAQ,EAAE,CAAC,GACzD,CAEf,C,uICnEA,GAAe,CAAC,YAAc,sBAAsB,IAAM,aAAa,ECSxD,SAAS4Q,GAASjT,EAAsB,CACrD,IAAQkT,EAAwClT,EAAxCkT,cAAeC,EAAyBnT,EAAzBmT,qBACvBC,KAA2BtH,GAAAA,GAAW,EAA9Be,EAAcuG,EAAdvG,eACRwG,EAAeC,GAAAA,EAAKC,QAAwB,EAACC,EAAArK,EAAAA,EAAAkK,EAAA,GAAtCrM,EAAIwM,EAAA,GACXxK,KAAoCC,EAAAA,UAA2B,CAAC,CAAC,EAACC,EAAAC,EAAAA,EAAAH,EAAA,GAA3DhD,EAAUkD,EAAA,GAAEuK,EAAavK,EAAA,GAChCwK,MAA2CzK,EAAAA,UAAsB,EAAC0K,EAAAxK,EAAAA,EAAAuK,GAAA,GAA3DE,EAAYD,EAAA,GAAEE,GAAkBF,EAAA,GACvCG,KAAoC7K,EAAAA,UAAkB,EAAK,EAAC8K,GAAA5K,EAAAA,EAAA2K,EAAA,GAArDE,GAAUD,GAAA,GAAEE,GAAaF,GAAA,MAChCxE,EAAAA,WAAU,UAAM,CACdkE,EAAcP,CAAa,CAC7B,EAAG,CAACA,CAAa,CAAC,EAElB,IAAMgB,EAAmB,SAACC,EAA2B,CAC/CA,MAAiBC,EAAAA,IAAapO,CAAU,GAC1CiO,GAAc,EAAI,EAClBJ,GAAmB,kBAAM7N,EAAW2I,IAAI,SAAClL,EAAM,CAAF,OAAKA,EAAKqE,EAAE,EAAC,KAE1DmM,GAAc,EAAK,EACnBJ,GAAmB,CAAC,CAAC,EAEzB,EACMQ,EAAiB,SAACvT,EAAQ,CAAF,OAC5BA,GAAM,MAANA,EAAQwT,UAAYxT,IAAM,MAANA,IAAM,QAANA,EAAQyT,KAAI,GAAA3I,OACzB9K,GAAM,YAANA,EAAQwT,QAAQ,EAAA1I,OAAG9K,GAAM,YAANA,EAAQyT,IAAI,EAClC,GAAG,EAEHnC,EAAwC,CAC5C,CACEpC,MAAO,MACPqC,UAAW,SACX3K,MAAO,GACP8M,SAAU,GACVC,UAAW,MACb,EACA,CACEzE,SAAOrE,EAAAA,IAAQ,UAAU,EACzB0G,UAAW,SACX3K,MAAO,GACP8M,SAAU,GACVC,UAAW,OACXC,WAAY,SAACC,EAAc7T,EAAW,KAAA8T,GAAAC,GACpC,OAAO/T,GAAM,MAANA,EAAQgU,aAAe,CAACd,MAC7BxM,EAAAA,MAAA,KAAG7F,QAAS,kBAAMoT,OAAOC,KAAKlU,GAAM,YAANA,EAAQgU,WAAW,CAAC,EAACjU,SAAA,CAChD8T,EACA7T,GAAM,OAAA8T,GAAN9T,EAAQmU,gBAAY,MAAAL,KAAA,QAApBA,GAAsBM,WACrB1N,EAAAA,MAAAgH,EAAAA,SAAA,CAAA3N,SAAA,CAAE,UAEA2G,EAAAA,MAAA,QAAMhC,UAAU,MAAK3E,SAAA,CAAC,IAAEC,GAAM,OAAA+T,GAAN/T,EAAQmU,gBAAY,MAAAJ,KAAA,cAApBA,GAAsBK,QAAQ,GAAC,EAAM,CAAC,EAC9D,EAEF,EACD,EACA,EAEHP,CAEJ,CACF,EACA,CACE3E,SAAOrE,EAAAA,IAAQ,QAAQ,EACvB0G,UAAW,WACX3K,MAAO,GACP8M,SAAU,GACVW,eAAgB,SAACnG,EAAc,CAAF,OAAKqF,EAAerF,GAAI,YAAJA,EAAMoG,MAAM,CAAC,EAC9DC,OAAQ,SAAC5P,EAAW3E,EAAQ,CAAF,OAAKuT,EAAevT,CAAM,CAAC,CACvD,EACA,CACEkP,SAAOrE,EAAAA,IAAQ,QAAQ,EACvB0G,UAAW,SACX3K,MAAO,GACP8M,SAAU,EACZ,EACA,CACExE,SAAOrE,EAAAA,IAAQ,iBAAiB,EAChC6I,SAAU,GACVnC,UAAW,WACX3K,MAAO,IACP4N,aAAc,CAAC,OAAQ,OAAO,EAC9BC,UAAW,CACT,KAAM,CAAEZ,QAAMhJ,EAAAA,IAAQ,MAAM,CAAE,EAC9B,MAAO,CAAEgJ,QAAMhJ,EAAAA,IAAQ,SAAS,CAAE,CACpC,CACF,EACA,CACEqE,MAAO,GAAFpE,UAAKD,EAAAA,IAAQ,OAAO,EAAC,WAC1B6I,SAAU,GACVnC,UAAW,QACX3K,MAAO,IACP2N,OAAQ,SAAC5P,EAAGhC,EAAM,CAAF,OACdA,GAAI,MAAJA,EAAM+R,OAAS,CAACC,MAAMhS,GAAI,YAAJA,EAAM+R,KAAK,EAAI/R,GAAI,YAAJA,EAAM+R,MAAME,QAAQ,CAAC,EAAI,EAAE,CACpE,EACA,CACE1F,MAAO,GAAFpE,UAAKD,EAAAA,IAAQ,YAAY,EAAC,YAC/B6I,SAAU,GACV9M,MAAO,IACP2K,UAAW,aACXgD,OAAQ,SAAC5P,EAAGhC,EAAM,CAAF,OACdA,GAAI,MAAJA,EAAMkS,YAAc,CAACF,MAAMhS,GAAI,YAAJA,EAAMkS,UAAU,EACvClS,GAAI,YAAJA,EAAMkS,WAAWD,QAAQ,CAAC,EAC1B,EAAE,CACV,EACA,CACE1F,SAAOrE,EAAAA,IAAQ,UAAU,EACzBjE,MAAO,GACP+M,UAAW,QACXD,SAAU,GACVnC,UAAW,QACXlQ,cAAe,CACb8O,MAAO,CACL,CACE2E,WAASjK,EAAAA,IAAQ,4BAA4B,EAC7CkK,QAAS,YACX,CAAC,CAEL,CACF,EACA,CACE7F,MAAO,GAAFpE,UAAKD,EAAAA,IAAQ,MAAM,EAAC,WACzB6I,SAAU,GACV9M,MAAO,IACP2K,UAAW,OACXqC,WAAY,SAACjP,EAAW3E,EAAW,CACjC,IAAIgV,GACFhV,GAAM,MAANA,EAAQiV,OAASjV,IAAM,MAANA,IAAM,QAANA,EAAQ0U,OAAQ1U,GAAM,YAANA,EAAQiV,QAAQjV,GAAM,YAANA,EAAQ0U,OAAQ,IACnE,SAAOQ,EAAAA,IAAwBF,EAAG,CACpC,CACF,CAAC,EAEH,SACEtO,EAAAA,MAACsI,EAAAA,EAAS,CACRE,SAAOrE,EAAAA,IAAQ,UAAU,EACzBjE,MAAO,KACPuH,WAASpH,EAAAA,KAAA,KAAAhH,YAAmB8K,EAAAA,IAAQ,UAAU,CAAC,EAA/B,UAAmC,EACnD3E,KAAMA,EACNmJ,oBAAmB,GACnB8F,aAAc,SAACjB,EAAkB,CAC1BA,GAAMvB,EAAcP,CAAa,CACxC,EACA9C,WAAY,CACVC,eAAgB,EAClB,EACA6F,UAAW,CACTC,iBAAkB,CAAEnR,MAAO,CAAEoR,QAAS,MAAO,CAAE,EAC/CC,kBAAmB,CACjBrR,MAAO,CAAEoR,QAAUpC,GAAyB,OAAZsC,MAAmB,CACrD,EACAC,aAAc,CACZC,cAAY7K,EAAAA,IAAQ,6BAA6B,CACnD,CACF,EACA2D,SAAQ/F,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAArI,GAAA,QAAAoI,EAAAA,EAAA,EAAAI,KAAA,SAAAlI,EAAA,eAAAA,EAAAuF,KAAAvF,EAAAwF,KAAA,QAAAxF,OAAAA,EAAAwF,KAAA,EACFiM,EAAqBnN,CAAU,EAAC,cAAAtE,EAAAoI,OAAA,SAC/B,EAAI,0BAAApI,EAAAsI,KAAA,IAAA5I,CAAA,EACZ,GAACP,SAAA,IAEF2G,EAAAA,MAAA,OAAKhC,UAAWqN,GAAO4D,YAAY5V,SAAA,IACjCgH,EAAAA,KAAA,QAAAhH,YAAO8K,EAAAA,IAAQ,mBAAmB,CAAC,CAAO,KAC1C9D,EAAAA,KAAA,KACElG,QAAS,kBAAMuS,EAAiB,CAACF,EAAU,CAAC,EAC5ChP,MAAO,CAAE0C,SAAO4K,EAAAA,IAAK,EAAI,QAAU,MAAO,EAAEzR,SAE3CmT,MAAarI,EAAAA,IAAQ,wBAAwB,KAAIA,EAAAA,IAAQ,MAAM,CAAC,CAChE,CAAC,EACD,KACL9D,EAAAA,KAAC6O,GAAAA,EAAgB,CACftE,QAASA,EACT/P,OAAO,KACPW,MAAOgD,EACPxD,SAAUiR,EACVrR,mBAAoB,GACpBuU,OAAQ,CAAEC,EAAG,GAAI,EACjBC,SAAQzM,EAAAA,EAAAA,EAAAA,EAAA,GACHyC,CAAc,MACjBiK,KAAM,WACNlD,aAAAA,EACAmD,eAAgB,SAACC,EAASC,EAAe,CACvCxD,EAAcwD,CAAU,CAC1B,EACAzU,SAAUqR,EAAkB,EAC5B,CACH,CAAC,EACO,CAEf,CCxKe,SAASqD,GAAmBlX,EAA2B,CACpE,IAAAoT,KAA2BtH,GAAAA,GAAW,EAA9Be,EAAcuG,EAAdvG,eACAsK,EAAuCnX,EAAvCmX,gBAAiBC,EAAsBpX,EAAtBoX,kBACnBC,EAAuBC,SAASC,SAASrH,SAAS,mBAAmB,EAC3ElH,KAA2CC,EAAAA,UAAsB,CAAC,CAAC,EAACC,EAAAC,EAAAA,EAAAH,EAAA,GAA7D4K,EAAY1K,EAAA,GAAE2K,EAAkB3K,EAAA,GACvCwK,KAAoCzK,EAAAA,UAA8B,CAAC,CAAC,EAAC0K,EAAAxK,EAAAA,EAAAuK,EAAA,GAA9D1N,GAAU2N,EAAA,GAAEF,EAAaE,EAAA,GAChC6D,KAAyBC,GAAAA,UAAS,WAAW,EAArCC,GAAYF,EAAZE,aACF9H,KAASC,GAAAA,WAAU,KAEzBN,EAAAA,WAAU,UAAM,CACdkE,EAAczT,GAAK,YAALA,EAAO2X,YAAY,CACnC,EAAG,CAAC3X,GAAK,YAALA,EAAO2X,YAAY,CAAC,EAExB,IAAMC,GAAmB,eAAA9S,EAAAyE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAArI,EAAOsP,EAAamH,EAAwB,CAAF,IAAAC,EAAA,OAAAtO,EAAAA,EAAA,EAAAI,KAAA,SAAAlI,EAAE,CAAF,cAAAA,EAAAuF,KAAAvF,EAAAwF,KAAE,CAAF,OAAAxF,OAAAA,EAAAwF,KAAA,EAClDwQ,GAAaP,EAAe/M,EAAAA,EAAAA,EAAAA,EAAA,GACzCsG,CAAM,MACTqH,oBAAqBX,CAAiB,EACvC,EAAC,OAHIU,EAAGpW,EAAAmI,KAILiO,GAAOD,GAAWA,EAAU,EAAC,wBAAAnW,EAAAsI,KAAA,IAAA5I,CAAA,EAClC,mBANwBQ,EAAAoW,EAAA,QAAAlT,EAAAmF,MAAA,KAAAC,SAAA,MAQnB+N,GAAY,eAAA3S,EAAAiE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAyO,EACnB9T,EACA+T,EACAC,EAA0B,KAAAC,EAAA,OAAA7O,EAAAA,EAAA,EAAAI,KAAA,SAAA0O,GAAA,eAAAA,GAAArR,KAAAqR,GAAApR,KAAA,QAEtBmR,OAAAA,EAAS,CACXN,oBAAqBX,EACrBmB,KAAMnU,GAAI,YAAJA,EAAMmU,KACZC,OAAQpU,GAAI,YAAJA,EAAMoU,OACdC,OAAQrU,GAAI,YAAJA,EAAMqU,OACdC,IAAKtU,GAAI,YAAJA,EAAMsU,IACXC,QAASvU,GAAI,YAAJA,EAAMuU,QACfC,QAASxU,GAAI,YAAJA,EAAMwU,QACfC,WAAYzU,GAAI,YAAJA,EAAMyU,WAClBtE,KAAMnQ,GAAI,YAAJA,EAAMmQ,KACZuE,kBAAmB1U,GAAI,YAAJA,EAAM0U,kBACzBC,SAAU3U,GAAI,YAAJA,EAAM2U,SAChBC,YAAa5U,GAAI,YAAJA,EAAMkI,GACnB2M,QAAS7U,GAAI,YAAJA,EAAM6U,QACfC,OAAQf,EAAQ,MAAQ7B,MAC1B,EAACgC,GAAApR,KAAA,EACK0Q,GAAoBS,EAAQ,UAAM,CAClCD,GAAe3E,EAAc2E,CAAa,CAChD,CAAC,EAAC,wBAAAE,GAAAtO,KAAA,IAAAkO,CAAA,EACH,mBAxBiBiB,EAAAC,EAAAC,EAAA,QAAA/T,EAAA2E,MAAA,KAAAC,SAAA,MA0BZoP,GAAY,eAAAC,EAAAhQ,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAA+P,EACnBC,EACA3Y,EACAqX,EAAe,KAAAC,EAAAsB,EAAAC,GAAAC,GAAAhB,GAAAD,GAAAH,GAAAqB,GAAA,OAAArQ,EAAAA,EAAA,EAAAI,KAAA,SAAAkQ,GAAA,eAAAA,GAAA7S,KAAA6S,GAAA5S,KAAA,QAEXkR,OAAAA,KAAgB2B,GAAAA,WAAU/Z,GAAK,YAALA,EAAO2X,YAAY,EAACmC,GAAA5S,KAAA,KACvB8S,EAAAA,IAAeP,CAAU,EAAC,OAMpD,GANKC,EAAYI,GAAAjQ,KACZ8P,GAAiB,SACrBxV,GACG,CACH,IAAI8V,GAAaP,EAAaQ,KAAK,SAAC7Y,GAAG,CAAF,OAAKA,GAAE8C,EAAG,GAAK9C,GAAE8C,EAAG,IAAM,IAAI,GACnE,OAAO8V,GAAaA,GAAW9V,EAAG,EAAImS,MACxC,EAAC,CACGoD,EAAc,CAAFI,GAAA5S,KAAA,SACV0S,OAAAA,GAAWD,GAAe,SAAS,EACrCf,GAAUe,GAAe,SAAS,EAClChB,GAAUgB,GAAe,SAAS,EAClCnB,GAASmB,GAAe,QAAQ,EAC9BE,GAAUzP,EAAAA,EAAAA,EAAAA,EAAA,GACTtJ,CAAM,MACT4X,IAAKe,EACLhB,OAAQiB,EACRT,QAASW,GACTrB,MAAMzX,GAAM,YAANA,EAAQyX,OAAQ,WACtBK,QAAAA,GACAJ,OAAAA,GACAG,QAAAA,EAAO,GAETP,EAAc+B,KAAKN,EAAU,EAACC,GAAA5S,KAAA,GACxB+Q,GAAa4B,GAAwB1B,EAAOC,CAAa,EAAC,yBAAA0B,GAAA9P,KAAA,IAAAwP,CAAA,EAEnE,mBA/BiBY,EAAAC,EAAAC,EAAA,QAAAf,EAAAtP,MAAA,KAAAC,SAAA,MAiClB4J,KAA8B7K,EAAAA,UAAiC,CAC7D,CACE+G,SAAOrE,EAAAA,IAAQ,YAAY,EAC3B0G,UAAW,MACXmC,SAAU,GACV9M,MAAO,IACP+M,UAAW,OACXU,eAAgB,SAACnG,EAAYuL,EAAqB,KAAAC,EAAAC,EAAjBC,EAAUH,EAAVG,WACzB5Z,EAASkO,GAAI,YAAJA,EAAMoG,OACfuF,GAAS3L,GAAI,OAAAwL,EAAJxL,EAAMoG,UAAM,MAAAoF,IAAA,cAAZA,EAAc9B,IAC7B,OAAO1J,GAAI,OAAAyL,EAAJzL,EAAMoG,UAAM,MAAAqF,IAAA,cAAZA,EAAcG,cAAYjP,EAAAA,IAAQ,OAAO,GAC9C+O,GACAC,KAAW,IACXA,MAEA9S,EAAAA,KAAA,OAAKrC,UAAWqN,GAAO6F,IAAI7X,YAEvBgH,EAAAA,KAACgT,GAAAA,EAAW,CACVC,SAAU,GACVtY,SAAU,SAACuY,GAAmB,CAAF,OAC1BzB,GAAayB,GAAWja,CAAM,CAAC,CAChC,CACF,CAAC,CAED,CAET,EACAuU,OAAQ,SAACsF,EAAQ7Z,EAAQ,CAAF,SACrB+G,EAAAA,KAAA,OAAKrC,UAAWqN,GAAO6F,IAAI7X,UACxBC,GAAM,YAANA,EAAQ8Z,cAAYjP,EAAAA,IAAQ,OAAO,EAClC,OAEA9D,EAAAA,KAACgT,GAAAA,EAAW,CACV7X,MAAO2X,IAAW,IAAM,CAAC,EAAIA,EAC7BtJ,YAAU2J,GAAAA,IAAuBla,GAAM,YAANA,EAAQyX,IAAc,EACvDuC,SAAU,GACVtY,SAAU,SAACuY,EAAmB,CAAF,OAAKzB,GAAayB,EAAWja,CAAM,CAAC,CAAC,CAClE,CACF,CACE,CAAC,CAEV,EACA,CACEkP,SAAOrE,EAAAA,IAAQ,MAAM,EACrB0G,UAAW,OACXoC,UAAW,SACX/M,MAAO,IACP6N,UAAW,SAAC0F,EAAK,CAAF,OACbA,GAAG,YAAHA,EAAK1C,QAAS,gBACV,CACE2C,iBAAevP,EAAAA,IAAQ,eAAe,CACxC,EACA,CACEwP,YAAUxP,EAAAA,IAAQ,UAAU,EAC5ByP,iBAAezP,EAAAA,IAAQ,eAAe,EACtC0P,WAAS1P,EAAAA,IAAQ,SAAS,CAC5B,CAAC,CACT,EACA,CACEqE,SAAOrE,EAAAA,IAAQ,cAAc,EAC7B0G,UAAW,UACX3K,MAAO,IACP+M,UAAW,OACX6G,eAAahJ,EAAAA,IAAK,CACpB,EACA,CACEtC,SAAOrE,EAAAA,IAAQ,cAAc,EAC7B0G,UAAW,UACX3K,MAAO,IACP+M,UAAW,MACb,EACA,CACEzE,MAAO,MACPqC,UAAW,SACX3K,MAAO,IACP+M,UAAW,MACb,EACA,CACEzE,SAAOrE,EAAAA,IAAQ,gBAAgB,EAC/B0G,UAAW,UACX3K,MAAO,IACP4T,YAAa,CAACjE,EACd5C,UAAW,QACXC,WAAY,SAACjP,EAAGhC,EAAM,CAAF,SAAKuS,EAAAA,IAAwBvS,GAAI,YAAJA,EAAMwV,OAAiB,CAAC,CAC3E,EACA,CACEjJ,SAAOrE,EAAAA,IAAQ,KAAK,EACpB0G,UAAW,aACXoC,UAAW,QACX/M,MAAO,IACPvF,cAAe,CACb8O,MAAO,CACL,CACE2E,WAASjK,EAAAA,IAAQ,yBAAyB,EAC1CkK,QAAS,sCACX,CAAC,CAEL,EACAnB,WAAY,SAACjP,EAAGhC,EAAM,CAAF,SAClBuS,EAAAA,IAAwBvS,GAAI,YAAJA,EAAMoV,UAAoB,CAAC,CACvD,EACA,CACE7I,SAAOrE,EAAAA,IAAQ,oBAAoB,EACnC0G,UAAW,OACX3K,MAAO,IACP6N,UAAWgG,GAAAA,EACb,EACA,CACEvL,MAAO,GAAFpE,UAAKD,EAAAA,IAAQ,iBAAiB,EAAC,MACpC0G,UAAW,oBACXoC,UAAW,QACX/M,MAAO,IACPvF,cAAe,CACb8O,MAAO,CACL,CACE2E,WAASjK,EAAAA,IAAQ,yBAAyB,EAC1CkK,QAAS,sCACX,CAAC,CAEL,EACAnB,WAAY,SAACjP,EAAGhC,EAAM,CAAF,SAClBuS,EAAAA,IAAwBvS,GAAI,YAAJA,EAAMqV,iBAA2B,CAAC,CAC9D,EACA,CACE9I,MAAO,oBACLnI,EAAAA,KAAA,OAAKrC,UAAU,0BAAyB3E,YACtC2G,EAAAA,MAAA,OAAA3G,SAAA,IAAM8K,EAAAA,IAAQ,UAAU,EAAE,SAAE,EAAK,CAAC,CAC/B,CAAC,EAER0G,UAAW,OACXoC,UAAW,OACX/M,MAAO,IACP8M,SAAU,GACVE,WAAY,SAACjP,EAAGhC,EAAS,CACvB,IAAIA,GAAI,YAAJA,EAAMmX,cAAYjP,EAAAA,IAAQ,OAAO,EAAG,MAAO,GAC/C,IAAI6P,KAAWxF,EAAAA,IAAwBvS,GAAI,YAAJA,EAAMgY,IAAc,EAC3D,MAAO,CAAChG,MAAM+F,CAAQ,GAAK,IAACE,GAAAA,SAAQF,CAAQ,EACxCA,KACA7P,EAAAA,IAAQ,cAAc,CAC5B,CACF,EACA,CACEqE,MAAO,oBACLxI,EAAAA,MAAA,OAAKhC,UAAU,0BAAyB3E,SAAA,IACtC2G,EAAAA,MAAA,OAAA3G,SAAA,IAAM8K,EAAAA,IAAQ,MAAM,EAAE,SAAE,EAAK,KAC7B9D,EAAAA,KAAC8T,GAAAA,EAAO,CAACC,WAASjQ,EAAAA,IAAQ,gBAAgB,EAAE9K,YAC1CgH,EAAAA,KAACgU,GAAAA,EAAQ,CAACnU,MAAO,GAAI1C,MAAO,CAAE8W,OAAQ,SAAU,CAAE,CAAE,CAAC,CAC9C,CAAC,EACP,CAAC,EAERzJ,UAAW,WACXoC,UAAW,OACX/M,MAAO,IACPvF,cAAe,CACb8O,MAAO,CACL,CACE2E,WAASjK,EAAAA,IAAQ,yBAAyB,EAC1CkK,QAAS,sCACX,CAAC,CAEL,EACAnB,WAAY,SAACjP,EAAGhC,EAAS,CACvB,IAAIA,GAAI,YAAJA,EAAMmX,cAAYjP,EAAAA,IAAQ,OAAO,EAAG,MAAO,GAC/C,IAAI6P,KAAWxF,EAAAA,KACbvS,GAAI,YAAJA,EAAMsV,YAAatV,GAAI,YAAJA,EAAMgY,KAC3B,EACA,MAAO,CAAChG,MAAM+F,CAAQ,GAAK,IAACE,GAAAA,SAAQF,CAAQ,EAAIA,EAAW,GAC7D,CACF,CAAC,CACF,EAACzH,EAAA5K,EAAAA,EAAA2K,EAAA,GAzKK1B,EAAO2B,EAAA,GAAEgI,EAAUhI,EAAA,MA2K1BxE,EAAAA,WAAU,UAAM,CACd,GAAI,EAACvP,GAAK,MAALA,EAAOgc,WAAW,CACrB,IAAIC,KAAWlC,GAAAA,WAAU3H,CAAO,EAChC6J,EAASC,QAAQ,CACflM,SAAOrE,EAAAA,IAAQ,OAAO,EACtBjE,MAAO,GACPvD,IAAK,UACLkO,UAAW,UACX8J,MAAO,OACP3H,SAAU,GACVa,OAAQ,SAAC+G,EAAOtb,EAAkB,CAAF,OAC9BA,GAAM,YAANA,EAAQ8Z,cAAYjP,EAAAA,IAAQ,OAAO,EACjC7K,GAAM,YAANA,EAAQ8Z,WAER/S,EAAAA,KAAA,KAAGwU,KAAI,IAAAzQ,OAAM9K,GAAM,YAANA,EAAQ8Z,OAAO,EAAG/Z,SAAEC,GAAM,YAANA,EAAQ8Z,OAAO,CAAI,CACrD,CACL,CAAC,EACDmB,EAAWE,CAAQ,CACrB,CACF,EAAG,CAACjc,GAAK,YAALA,EAAOgc,SAAS,CAAC,EAErB,IAAMM,EAAU,UAAM,CACpB,MAAO,CACL,CACEtM,SAAOrE,EAAAA,IAAQ,kCAAkC,EACjD8I,UAAW,SACX/M,MAAO,IACPyU,MAAO,QACP9G,OAAQ,SACN+G,EACAtb,EACA2E,EACAyT,EAA+C,KAAA7J,EAAA,MAC5C,IACH7H,EAAAA,MAAAgH,EAAAA,SAAA,CAAA3N,SAAA,EACGC,GAAM,YAANA,EAAQ8Z,cAAYjP,EAAAA,IAAQ,OAAO,EAClC,MAEA9D,EAAAA,KAAA,KAEElG,QAAS,UAAM,KAAA4a,GACb1I,EAAmB,CAAC,EAAFjI,OAAA4Q,GAAAA,EAAK5I,CAAY,GAAE9S,EAAOwL,EAAE,CAAC,GAC/C4M,GAAM,OAAAqD,GAANrD,EAAQuD,iBAAa,MAAAF,KAAA,QAArBA,GAAAG,KAAAxD,EAAwBpY,EAAOwL,EAAE,CACnC,EAAEzL,YAED8K,EAAAA,IAAQ,MAAM,CAAC,EANZ,UAOH,EAEJiE,GAAM,OAAAP,EAANO,EAAQK,gBAAY,MAAAZ,IAAA,QAApBA,EAAsBa,SACrB,mCACF,MAAKkE,EAAAA,IAAatT,GAAM,YAANA,EAAQ2X,MAAM,KAC9B5Q,EAAAA,KAACoL,GAAQ,CACPC,cAAepS,GAAM,YAANA,EAAQ2X,OACvBtF,qBAAoB,eAAAwJ,GAAApT,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAmT,GAAOlM,GAAQ,CAAF,OAAAlH,EAAAA,EAAA,EAAAI,KAAA,SAAAiT,GAAE,CAAF,cAAAA,GAAA5V,KAAA4V,GAAA3V,KAAE,CAAF,OAAA2V,OAAAA,GAAA3V,KAAA,EAC3BwQ,GAAaP,EAA2B,CAC5C6B,YAAalY,GAAM,YAANA,EAAQwL,GACrBmM,OAAQ/H,EACV,CAAC,EAAC,cAAAmM,GAAA/S,OAAA,SAAA+S,GAAAhT,IAAA,0BAAAgT,GAAA7S,KAAA,IAAA4S,EAAA,qBAAAE,GAAA,QAAAH,GAAA1S,MAAA,KAAAC,SAAA,KACH,CACF,EAED,EACD,EACD,CAAC,CACJ,CACH,CAAC,CAEL,EAEA6S,KAAwB9T,EAAAA,UAAkB,EAAK,EAAC+T,GAAA7T,EAAAA,EAAA4T,EAAA,GAAzC/H,GAAIgI,GAAA,GAAEC,EAAOD,GAAA,GAEpB,SACExV,EAAAA,MAAAgH,EAAAA,SAAA,CAAA3N,SAAA,IACEgH,EAAAA,KAAC6O,GAAAA,EAAgB,CACfrU,OAAO,KACPH,UAAW,EACXyU,OAAQ,CACNuG,EAAG,GACL,EACA9T,QAAS,GACThH,mBACEpC,GAAK,MAALA,EAAOmd,YAAcnd,IAAK,MAALA,IAAK,QAALA,EAAOod,kBACxB,CACErc,SAAU,SACVC,cAAe,aACf+D,qBAAmB4G,EAAAA,IAAQ,mBAAmB,EAC9ChK,QAAS,kBAAMsb,EAAQ,EAAI,CAAC,EAC5Bnc,OAAQ,iBAAO,CACbwL,GAAI,KAAFV,UAAOyR,GAAAA,GAAO,EAAEC,MAAM,EAAG,CAAC,CAAC,EAC7B5E,IAAK,GACP,CAAC,CACH,EACA,GAENtG,QACEpS,GAAK,MAALA,EAAOmd,WAAU,GAAAvR,OAAA4Q,GAAAA,EACRpK,CAAO,EAAAoK,GAAAA,EAAKF,EAAQ,CAAC,GAC1BlK,EAENpP,MAAOgD,GACP6Q,SAAQzM,EAAAA,EAAAA,EAAAA,EAAA,GACHyC,CAAc,MACjBiK,KAAM,WACNlD,aAAAA,EACA2J,aAAc,SAACC,EAAMC,EAASC,EAAY,CAAF,OACtCF,GAAI,MAAJA,EAAMjF,OAAQiF,GAAI,YAAJA,EAAMjF,QAAS,gBACzB,CAACmF,GAAU,YAAVA,EAAYC,KAAMD,GAAU,YAAVA,EAAYE,MAAM,EACrC,CAACF,GAAU,YAAVA,EAAYC,KAAMD,GAAU,YAAVA,EAAYE,OAAQF,GAAU,YAAVA,EAAU,MAAQ,CAAC,EAChEG,OAAQ,UAAF,KAAAC,EAAAvU,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAsU,EAAOC,EAAS5Z,EAAM,CAAF,OAAAoF,EAAAA,EAAA,EAAAI,KAAA,SAAAqU,EAAE,CAAF,cAAAA,EAAAhX,KAAAgX,EAAA/W,KAAE,CAAF,UACrB9C,EAAKmU,KAAM,CAAF0F,EAAA/W,KAAA,eAAA+W,EAAAnU,OAAA,SAAS,EAAK,SAAAmU,OAAAA,EAAA/W,KAAA,EACtB+Q,GAAa7T,CAAI,EAAC,wBAAA6Z,EAAAjU,KAAA,IAAA+T,CAAA,EACzB,YAAAF,EAAAK,EAAAC,EAAA,QAAAL,EAAA7T,MAAA,KAAAC,SAAA,SAAA2T,CAAA,IACDO,SAAU,UAAF,KAAAC,EAAA9U,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAA6U,EAAON,EAAS5Z,EAAM,CAAF,OAAAoF,EAAAA,EAAA,EAAAI,KAAA,SAAA2U,EAAE,CAAF,cAAAA,EAAAtX,KAAAsX,EAAArX,KAAE,CAAF,OAAAqX,OAAAA,EAAArX,KAAA,EACtB0Q,GAAoB,CACxBG,oBAAqBX,EACrB4B,YAAa5U,GAAI,YAAJA,EAAMkI,GACnB4M,OAAQ,QACV,CAAC,EAAC,wBAAAqF,EAAAvU,KAAA,IAAAsU,CAAA,EACH,YAAAF,EAAAI,EAAAC,EAAA,QAAAJ,EAAApU,MAAA,KAAAC,SAAA,SAAAkU,CAAA,IACD5b,SAAUqR,CAAkB,EAC5B,CACH,KACDhM,EAAAA,KAAC6W,GAAAA,EAAY,CACX1J,KAAMA,GACN2J,KAAK,GACLC,QAAO,eAAAC,EAAAtV,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAqV,EAAOvQ,EAAW,CAAF,OAAA/E,EAAAA,EAAA,EAAAI,KAAA,SAAAmV,EAAE,CAAF,cAAAA,EAAA9X,KAAA8X,EAAA7X,KAAE,CAAF,WACnBqH,EAAG,CAAFwQ,EAAA7X,KAAA,QAAA6X,OAAAA,EAAA7X,KAAA,EACGoS,GACJ/K,EACA,CACEjC,GAAI,KAAFV,UAAOyR,GAAAA,GAAO,EAAEC,MAAM,EAAG,CAAC,CAAC,EAC7B5E,IAAKnK,CACP,EACA,EACF,EAAC,OAEH0O,EAAQ,EAAK,EAAC,wBAAA8B,EAAA/U,KAAA,IAAA8U,CAAA,EACf,mBAAAE,EAAA,QAAAH,EAAA5U,MAAA,KAAAC,SAAA,KAAC,CACH,CAAC,EACF,CAEN,CCvZe,SAAS+U,GAAajf,EAA0B,KAAAkf,EACrDC,EAAuBnf,EAAvBmf,mBACR3H,KAAyBC,GAAAA,UAAS,WAAW,EAArCC,EAAYF,EAAZE,aACR1O,KAAwCC,EAAAA,UAAqB,CAAC,CAAC,EAACC,EAAAC,EAAAA,EAAAH,EAAA,GAAzDoW,EAAYlW,EAAA,GAAEmW,EAAenW,EAAA,GAC9BoW,EAAU,eAAAxa,GAAAyE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAArI,EAAOsP,EAAQ,CAAF,OAAAlH,EAAAA,EAAA,EAAAI,KAAA,SAAAlI,EAAE,CAAF,cAAAA,EAAAuF,KAAAvF,EAAAwF,KAAE,CAAF,OAAAxF,OAAAA,EAAAwF,KAAA,EACxBwQ,EAAayH,EAA8BzO,CAAM,EAAC,cAAAhP,EAAAoI,OAAA,SAAApI,EAAAmI,IAAA,0BAAAnI,EAAAsI,KAAA,IAAA5I,CAAA,qBAD1CQ,EAAA,QAAAkD,GAAAmF,MAAA,KAAAC,SAAA,MAIVqV,EAAwB,SAACle,EAAW,CACxC,IAAIme,KAAUzF,GAAAA,WAAU1Y,CAAC,EACzBme,OAAAA,EAAQC,UAAUC,QAAQ,SAACjc,GAAS,KAAAkc,EAAAC,GAClCnc,GAAKmX,QAAU4E,GAAO,OAAAG,EAAPH,EAASK,aAAS,MAAAF,IAAA,cAAlBA,EAAoB/E,QACnCnX,GAAKqc,QAAUN,GAAO,OAAAI,GAAPJ,EAASK,aAAS,MAAAD,KAAA,cAAlBA,GAAoBE,OAErC,CAAC,EACMN,CACT,EAEMO,EAAkB,UAAM,KAAAC,EACxBC,EAAyB,CAAC,EAC1BC,MAAcnG,GAAAA,WAAU/Z,GAAK,OAAAggB,EAALhgB,EAAOmgB,gBAAY,MAAAH,IAAA,cAAnBA,EAAqBI,WAAW,EAC5DF,GAAYR,QAAQ,SAACre,EAAM,CACzB,IAAIme,GAAUD,EAAsBle,CAAC,EACrC4e,EAAaA,EAAWrU,OAAO4T,IAAO,YAAPA,GAASC,SAAS,CACnD,CAAC,EAQDJ,EAAgBY,CAAU,CAC5B,EAEA1Q,SAAAA,EAAAA,WAAU,UAAM,KAAA8Q,GACVrgB,GAAK,OAAAqgB,GAALrgB,EAAOmgB,gBAAY,MAAAE,KAAA,QAAnBA,GAAqBD,aAAaL,EAAgB,CACxD,EAAG,CAAC/f,GAAK,OAAAkf,EAALlf,EAAOmgB,gBAAY,MAAAjB,IAAA,cAAnBA,EAAqBkB,WAAW,CAAC,KAGnC5Y,EAAAA,MAAAgH,EAAAA,SAAA,CAAA3N,SAAA,IACEgH,EAAAA,KAACyY,EAAAA,EAAY,CAACC,SAAS,gBAAgBC,QAAM7U,EAAAA,IAAQ,eAAe,CAAE,CAAE,KACxE9D,EAAAA,KAAA,OAAKrC,UAAWqN,EAAO+I,QAAQ/a,YAC7BgH,EAAAA,KAACqP,GAAkB,CACjBiG,WAAY,EAACnd,GAAK,MAALA,EAAOqR,UACpB+L,kBAAmB,GACnBpB,UAAW,GACXrE,aAAcyH,EACdE,WAAYA,EACZnI,gBAAiBgI,CAA6B,CAC/C,CAAC,CACC,CAAC,EACN,CAEN,C,gEC1De,SAASsB,GAAWzgB,EAAwB,CACzD,IAAQ0gB,EAAuD1gB,EAAvD0gB,YAAaC,EAA0C3gB,EAA1C2gB,UAAWC,EAA+B5gB,EAA/B4gB,iBAAkBvP,EAAarR,EAAbqR,SAClD,SACE7J,EAAAA,MAAC0H,GAAAA,EAAK,CAAC1J,UAAWqb,GAAAA,EAAG,CAAEC,KAAMH,GAAa,GAAKtP,CAAS,CAAC,EAAExQ,SAAA,CACxD6f,IAAgB,KACf7Y,EAAAA,KAAA,KAAGrC,UAAU,cAAa3E,YACvB8K,EAAAA,IAAQ,UAAU,CAAC,CACnB,KAEH9D,EAAAA,KAAA,KAAGlG,QAAS,kBAAMif,EAAiB,MAAM,CAAC,EAAC/f,YACxC8K,EAAAA,IAAQ,UAAU,CAAC,CACnB,EAEJ+U,EAAcC,EAAY,KACzB9Y,EAAAA,KAAA,KAAGlG,QAAS,kBAAMif,EAAiB,MAAM,CAAC,EAAC/f,YACxC8K,EAAAA,IAAQ,MAAM,CAAC,CACf,KAEH9D,EAAAA,KAAA,KAAGrC,UAAU,cAAa3E,YACvB8K,EAAAA,IAAQ,MAAM,CAAC,CACf,CACJ,EACI,CAEX,CCde,SAASoV,GAAW/gB,EAAwB,KAAAghB,EAAAC,EAAA5R,EAAA6R,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACjDpC,EAAmDnf,EAAnDmf,mBAAoBqC,EAA+BxhB,EAA/BwhB,kBAAmBC,GAAYzhB,EAAZyhB,QAE/CC,KAA0BC,GAAAA,WAEvB,EAFSC,EAASF,EAAb5Z,GAGR+Z,GAAwC7hB,GAAK,YAALA,EAAO8hB,WAAvCjC,EAASgC,GAAThC,UAAWJ,GAASoC,GAATpC,UAAWsC,GAAKF,GAALE,MAC9BC,GAAeC,GAAAA,EAAQ1O,QAAQ,EAAC2O,EAAA/Y,EAAAA,EAAA6Y,GAAA,GAAzBhb,EAAIkb,EAAA,GACXC,EAAoBC,GAAAA,EAAIC,OAAO,EAAvBzM,EAAOuM,EAAPvM,QACFhG,KAASC,GAAAA,WAAU,KAEzBN,EAAAA,WAAU,UAAM,KAAA+S,GAAAC,GACdvb,EAAKwI,eAAe,CAClBuS,MAAOA,GACPS,OAAQ3C,GAAS,OAAAyC,GAATzC,EAAW4C,aAAS,MAAAH,KAAA,QAApBA,GAAsBE,OAAS3C,GAAS,OAAA0C,GAAT1C,EAAW4C,aAAS,MAAAF,KAAA,cAApBA,GAAsBC,OAAS,EACxE,CAAC,CACH,EAAG,CAACT,GAAOlC,GAAS,OAAAmB,EAATnB,EAAW4C,aAAS,MAAAzB,IAAA,cAApBA,EAAsBwB,MAAM,CAAC,EAExC,IAAAhL,KAAyBC,GAAAA,UAAS,WAAW,EAArCC,GAAYF,EAAZE,aACR1O,MAA4CC,EAAAA,UAAkB,EAAK,EAACC,EAAAC,EAAAA,EAAAH,GAAA,GAA7D0Z,EAAcxZ,EAAA,GAAEyZ,EAAiBzZ,EAAA,GACxCwK,KAAwCzK,EAAAA,UAAkB,EAAK,EAAC0K,EAAAxK,EAAAA,EAAAuK,EAAA,GAAzDkP,EAAYjP,EAAA,GAAEkP,EAAelP,EAAA,GACpCG,KAAwC7K,EAAAA,UAAkB,EAAK,EAAC8K,GAAA5K,EAAAA,EAAA2K,EAAA,GAAzDgP,GAAY/O,GAAA,GAAEgP,GAAehP,GAAA,GACpCgJ,MAAgC9T,EAAAA,UAAiB,EAAC+T,GAAA7T,EAAAA,EAAA4T,GAAA,GAA3CiG,GAAQhG,GAAA,GAAEiG,GAAWjG,GAAA,GAC5BkG,MAAwCja,EAAAA,UAAkB,EAAK,EAACka,GAAAha,EAAAA,EAAA+Z,GAAA,GAAzDE,GAAYD,GAAA,GAAEE,GAAeF,GAAA,GACpCG,MAAkDra,EAAAA,UAAkB,EAAK,EAACsa,GAAApa,EAAAA,EAAAma,GAAA,GAAnEE,GAAiBD,GAAA,GAAEE,GAAoBF,GAAA,GAC9CG,MAAsCza,EAAAA,UAAiB,EAAC0a,GAAAxa,EAAAA,EAAAua,GAAA,GAAjDE,GAAWD,GAAA,GAAEE,GAAcF,GAAA,GAC5BG,GAAsBjE,GAAS,OAAAoB,EAATpB,EAAWkE,gBAAY,MAAA9C,IAAA,cAAvBA,EAAyBxS,OAK/CmS,GAAgB,eAAA9b,GAAAyE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAArI,GAAO4iB,GAAuB,CAAF,OAAAxa,EAAAA,EAAA,EAAAI,KAAA,SAAAlI,GAAE,CAAF,cAAAA,GAAAuF,KAAAvF,GAAAwF,KAAE,CAAF,OAAAxF,OAAAA,GAAAwF,KAAA,EAC7CwQ,GACJyH,EACA,CACE8E,oBACED,KAAS,QAASnE,GAAS,YAATA,EAAW1c,OAAQ,GAAI0c,GAAS,YAATA,EAAW1c,OAAQ,EAC9D4U,oBAAqB8H,GAAS,YAATA,EAAWC,OAClC,EACA,0BACF,EAAC,cAAApe,GAAAoI,OAAA,SAAApI,GAAAmI,IAAA,0BAAAnI,GAAAsI,KAAA,IAAA5I,EAAA,qBATmBQ,GAAA,QAAAkD,GAAAmF,MAAA,KAAAC,SAAA,MAWhB+H,EAAO,eAAA3M,GAAAiE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAyO,IAAA,KAAAgM,GAAA,OAAA1a,EAAAA,EAAA,EAAAI,KAAA,SAAA0O,GAAA,eAAAA,GAAArR,KAAAqR,GAAApR,KAAA,QACVgd,OAAAA,GAAYld,EAAKmd,cAAc,QAAQ,EAAC7L,GAAApR,KAAA,EACtCwQ,GAAayH,EAA8B,CAC/CW,QAASD,GAAS,YAATA,EAAWC,QACpB0C,UAAQ4B,GAAAA,IAAMF,EAAS,CACzB,CAAC,EAAC,OACFnB,GAAgB,EAAK,EAAC,wBAAAzK,GAAAtO,KAAA,IAAAkO,EAAA,EACvB,oBAPY,QAAA5S,GAAA2E,MAAA,KAAAC,SAAA,MASP0T,GAAS,UAAM,CACnBmF,GAAgB,EAAK,EACrB/b,EAAKqd,cAAc,SAAUT,EAAW,CAC1C,EAEA,SACE/b,EAAAA,KAAA2G,EAAAA,SAAA,CAAA3N,YACE2G,EAAAA,MAAA,OAAKhC,UAAWqN,EAAO+I,QAAQ/a,SAAA,IAC7BgH,EAAAA,KAAA,QAAMrC,UAAWqN,EAAOyR,UAAWxc,GAAI+X,GAAS,YAATA,EAAWjF,QAAQ/Z,SACvDgf,GAAS,YAATA,EAAWjF,OAAO,CACf,EACLiF,GAAS,MAATA,EAAW0E,OACV1c,EAAAA,KAAC2c,GAAAA,QAAiB,CAChBC,UAAW5E,GAAS,YAATA,EAAW0E,IACtB/e,UAAWqb,GAAAA,EAAGhO,EAAO4R,UAAW,eAAe,CAAE,CAClD,EAED,MAEFjd,EAAAA,MAAC0H,GAAAA,EAAK,CAAC1J,UAAWqN,EAAO6R,gBAAgB7jB,SAAA,EACtC+O,GAAM,OAAAP,EAANO,EAAQK,gBAAY,MAAAZ,IAAA,cAApBA,EAAsBa,SACrB,gDACF,OACErI,EAAAA,KAAC8T,GAAAA,EAAO,CAACC,WAASjQ,EAAAA,IAAQ,2BAA2B,EAAE9K,YACrDgH,EAAAA,KAAA,KACErC,UAAWqN,EAAO8R,aAClBhjB,QAAS,UAAM,CACbiU,EAAQgP,WAAQjZ,EAAAA,IAAQ,qBAAqB,CAAC,EAC9CoJ,OAAOC,KAAK,aAADpJ,OACIgW,EAAS,cAAAhW,OAAaiU,GAAS,YAATA,EAAWC,OAAO,EACrD,QACF,CACF,EAAEjf,YAED8K,EAAAA,IAAQ,uBAAuB,CAAC,CAChC,CAAC,CACG,GAEViE,GAAM,OAAAsR,EAANtR,EAAQK,gBAAY,MAAAiR,IAAA,cAApBA,EAAsBhR,SACrB,oCACF,OACErI,EAAAA,KAAC8T,GAAAA,EAAO,CAACC,WAASjQ,EAAAA,IAAQ,2BAA2B,EAAE9K,YACrDgH,EAAAA,KAAA,KACErC,UAAWqN,EAAO8R,aAClBhjB,QAAS,UAAM,CACbiU,EAAQgP,WAAQjZ,EAAAA,IAAQ,qBAAqB,CAAC,EAC9C,IAAMkZ,GAAK,IAAIC,gBACfD,GAAGpgB,IAAI,OAAQob,GAAS,YAATA,EAAW0E,GAAG,EACzB3C,GAAaJ,GAAqBC,IACpC1M,OAAOC,KAAK,aAADpJ,OACIgW,EAAS,cAAAhW,OAAa4V,EAAiB,UAAA5V,OAAS6V,GAAO,KAAA7V,OAAIiZ,GAAGE,SAAS,CAAC,EACrF,QACF,CAEJ,EAAElkB,YAED8K,EAAAA,IAAQ,qBAAqB,CAAC,CAC9B,CAAC,CACG,CACV,EACI,KACPnE,EAAAA,MAAA,OAAKhC,UAAU,6BAA4B3E,SAAA,IACzC2G,EAAAA,MAAA,OAAKhC,UAAWqN,EAAOyR,UAAWtf,MAAO,CAAEggB,WAAY,QAAS,EAAEnkB,SAAA,IAC/D8K,EAAAA,IAAQ,gCAAgC,EACxCmY,GAAc,IAAHlY,OAAOkY,GAAW,KAAM,EAAE,EACnC,EACJA,MACCjc,EAAAA,KAAC4Y,GAAU,CACTE,UAAWmD,GACXlD,iBAAkBA,GAClBF,YAAab,GAAS,YAATA,EAAW1c,KAAM,CAC/B,EAED,EACD,EACE,KACLqE,EAAAA,MAACqJ,GAAAA,EAAG,CAAAhQ,SAAA,IACF2G,EAAAA,MAACsJ,GAAAA,EAAG,CAACC,KAAM,GAAIvL,UAAWqN,EAAOoS,iBAAiBpkB,SAAA,CAC/Cgf,GAAS,OAAAsB,EAATtB,EAAW4C,aAAS,MAAAtB,IAAA,QAApBA,EAAsBoD,OACrB1c,EAAAA,KAAC2c,GAAAA,QAAiB,CAChBC,UAAW5E,GAAS,OAAAuB,EAATvB,EAAW4C,aAAS,MAAArB,IAAA,cAApBA,EAAsBmD,IACjC/e,UAAWqb,GAAAA,EAAGhO,EAAOqS,mBAAoB,eAAe,CAAE,CAC3D,EAED,MAEF1d,EAAAA,MAACya,GAAAA,EAAO,CACNkD,KAAI,GACJpV,OAAO,aACPmG,UAAW,GACXlP,KAAMA,EACNoe,SAAU,CAAEC,GAAI,EAAGC,GAAI,EAAGC,GAAI,EAAGC,GAAI,GAAIC,OAAQ,CAAE,EACnDtU,SAAU,CAAEJ,KAAM,EAAG,EACrBK,WAAY,CAAEL,KAAM,CAAE,EAAElQ,SAAA,IAExBgH,EAAAA,KAACyJ,GAAAA,EAAY,CACXnK,KAAK,SACLuE,MAAK,GAAAE,UAAKD,EAAAA,IAAQ,OAAO,EAAC,KAC1B0F,SAAU,CAACmS,GACX7R,IAAK,IACL+T,MACG1lB,EAAMqR,SAmDL,MAlDAxJ,EAAAA,KAAC8d,GAAAA,EAAU,CACT3V,SAAOrE,EAAAA,IAAQ,oBAAoB,EACnCia,YAAa5C,GACbhO,KAAM8N,GACN/P,UAAWd,EACX3B,SAAUsN,GACViI,aAAc,CACZne,MAAO,OACT,EAAE7G,YAEFgH,EAAAA,KAACsC,GAAAA,GAAM,CACL2M,KAAK,OACL1N,QAASwZ,EACTjhB,QAAO4H,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAA+P,IAAA,KAAA0K,GAAA4B,GAAA1hB,GAAAgD,GAAA,OAAAoC,EAAAA,EAAA,EAAAI,KAAA,SAAAkQ,GAAA,eAAAA,GAAA7S,KAAA6S,GAAA5S,KAAA,QACqC,GAAxCgd,GAAYld,EAAKmd,cAAc,QAAQ,EAAC,CACxCX,GAAmB,CAAF1J,GAAA5S,KAAA,SACnB2b,OAAAA,EAAgB,EAAI,EAAC/I,GAAA5S,KAAA,KACS6e,GAAAA,SAAQ,gBAADna,OACnBuT,EAAkB,KAAAvT,OAAIgW,CAAS,EAC/C,CACEoE,OAAQ,MACV,CACF,EAAEC,OAAO,CACPnG,QAASD,GAAS,YAATA,EAAWC,QACpB0C,UAAQ4B,GAAAA,IAAMF,EAAS,CACzB,CAAC,EAAC,OAAA4B,GAAAhM,GAAAjQ,KARMzF,GAAI0hB,GAAJ1hB,KAAMgD,GAAK0e,GAAL1e,MASVA,IAAK,MAALA,GAAOwO,UACT5O,EAAKqd,cAAc,SAAUT,EAAW,EACxChO,EAAQxO,MAAMA,IAAK,YAALA,GAAOwO,OAAO,EAC5B6N,GAAqB,CAACD,EAAiB,GAErCpf,IAAI,MAAJA,GAAMwR,UACRmN,GAAgB,EAAI,EACpBE,GAAY7e,IAAI,YAAJA,GAAMwR,OAAO,GAE3BiN,EAAgB,EAAK,EAAC/I,GAAA5S,KAAA,iBAEtB2c,GAAeK,EAAS,EAAC,QAE3BT,GAAqB,CAACD,EAAiB,EAAC,yBAAA1J,GAAA9P,KAAA,IAAAwP,EAAA,EACzC,GACDhU,UAAWqN,EAAOqT,YAClBlhB,MAAO,CAAEmhB,SAAO7T,EAAAA,IAAK,EAAI,QAAU,OAAQ,EAAEzR,SAE3C2iB,MAEE7X,EAAAA,IAAQ,gCAAgC,KADxCA,EAAAA,IAAQ,MAAM,CAC2B,CACvC,CAAC,CACC,CAIf,CACF,KACD9D,EAAAA,KAACyJ,GAAAA,EAAY,CACXnK,KAAK,QACLuE,SAAOC,EAAAA,IAAQ,UAAU,EACzB0F,SAAU,CAAC+R,GACXnS,MAAO,CACL,CACE4E,QAAS,uCACTD,WAASjK,EAAAA,IAAQ,8BAA8B,CACjD,CAAC,EAEH+Z,MACG1lB,EAAMqR,SA4BL,MA3BAxJ,EAAAA,KAACsC,GAAAA,GAAM,CACL2M,KAAK,OACL1N,QAASsZ,EACT/gB,QAAO4H,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAmT,IAAA,KAAAwJ,GAAA,OAAA5c,EAAAA,EAAA,EAAAI,KAAA,SAAAiT,GAAA,eAAAA,GAAA5V,KAAA4V,GAAA3V,KAAA,QACmC,GAAtCkf,GAAWpf,EAAKmd,cAAc,OAAO,EAAC,EACtC,CAACiC,IAAYA,IAAY,GAAC,CAAAvJ,GAAA3V,KAAA,eAAA2V,GAAA/S,OAAA,SACrB8L,EAAQxO,SACbuE,EAAAA,IAAQ,8BAA8B,CACxC,CAAC,aACCyX,GAAc,CAAFvG,GAAA3V,KAAA,QACdyb,OAAAA,EAAkB,EAAI,EAAC9F,GAAA3V,KAAA,EACjBwQ,GAAayH,EAA8B,CAC/CW,QAASD,GAAS,YAATA,EAAWC,QACpBiC,MAAOsE,OAAOC,WAAWF,EAAQ,CACnC,CAAC,EAAC,OACFzD,EAAkB,EAAK,EAAC,OAE1BU,GAAgB,CAACD,EAAY,EAAC,wBAAAvG,GAAA7S,KAAA,IAAA4S,EAAA,EAC/B,GACDpX,UAAWqN,EAAOqT,YAClBlhB,MAAO,CAAEmhB,SAAO7T,EAAAA,IAAK,EAAI,QAAU,OAAQ,EAAEzR,SAE3CuiB,MAEEzX,EAAAA,IAAQ,gCAAgC,KADxCA,EAAAA,IAAQ,MAAM,CAC2B,CACvC,CAIX,CACF,CAAC,EACK,CAAC,EACP,KACLnE,EAAAA,MAACsJ,GAAAA,EAAG,CAACC,KAAM,GAAGlQ,SAAA,IACZgH,EAAAA,KAAC0e,GAAAA,EAAa,CACZC,KAAM,EACN/D,UAAW,CAAE9N,KAAMkL,GAAS,OAAAwB,EAATxB,EAAW4C,aAAS,MAAApB,IAAA,cAApBA,EAAsBoB,SAAU,CAAE,CACtD,GACA5C,GAAS,OAAAyB,EAATzB,EAAW4C,aAAS,MAAAnB,IAAA,cAApBA,EAAsBmF,oBACrBjf,EAAAA,MAAA,OAAKhC,UAAWqN,EAAO6T,mBAAmB7lB,SAAA,IACvC8K,EAAAA,IAAQ,QAAQ,EAAE,SAAEkU,GAAS,OAAA0B,EAAT1B,EAAW4C,aAAS,MAAAlB,IAAA,cAApBA,EAAsBkF,cAAc,EACtD,CACN,EACE,CAAC,EACH,KACL5e,EAAAA,KAACqP,GAAkB,CACjBiG,WAAY,CAACnd,EAAMqR,SACnB+L,kBAAmB,GACnBpB,UAAW,GACX5E,kBAAmByI,GAAS,YAATA,EAAWC,QAC9BnI,aAAc8H,GACdtI,gBAAiBgI,CAA6B,CAC/C,CAAC,EACC,CAAC,CACN,CAEN,CCzRe,SAASwH,GAAS3mB,EAAsB,KAAAkf,EAC7CC,EAAuBnf,EAAvBmf,mBACR/L,KAA2BtH,GAAAA,GAAW,EAA9Be,EAAcuG,EAAdvG,eAER7D,KAA2CC,EAAAA,UAAsB,CAAC,CAAC,EAACC,EAAAC,EAAAA,EAAAH,EAAA,GAA7D4K,EAAY1K,EAAA,GAAE2K,EAAkB3K,EAAA,GACvCwK,KAAoCzK,EAAAA,UAAyB,CAAC,CAAC,EAAC0K,EAAAxK,EAAAA,EAAAuK,EAAA,GAAzD1N,EAAU2N,EAAA,GAAEF,GAAaE,EAAA,GAChC6D,KAAyBC,GAAAA,UAAS,WAAW,EAArCC,EAAYF,EAAZE,aACFkP,GAAiB,UAAM,KAAA5G,GACvBlK,EAAM,EACNoK,KAAcnG,GAAAA,WAAU/Z,GAAK,OAAAggB,GAALhgB,EAAOmgB,gBAAY,MAAAH,KAAA,cAAnBA,GAAqBI,WAAW,KACxDhM,EAAAA,IAAa8L,CAAW,GAAKA,EAAYzR,OAAS,IACpDyR,EAAYR,QAAQ,SAACre,EAAM,CACzByU,GAAOuQ,OAAOhlB,GAAC,YAADA,EAAG0gB,KAAK,CACxB,CAAC,EACGjM,EAAM,GACRoK,EAAY/F,KAAK,CACf2F,WAASnU,EAAAA,IAAQ,OAAO,EACxBoW,MAAO,CAACtM,MAAMK,CAAG,GAAKA,KAAO+Q,GAAAA,UAAS/Q,CAAG,EAAEJ,QAAQ,CAAC,EAAe,EACrE,CAAC,GAGLjC,GAAcyM,CAAW,CAC3B,KAEA3Q,EAAAA,WAAU,UAAM,KAAA8Q,GACVrgB,GAAK,OAAAqgB,GAALrgB,EAAOmgB,gBAAY,MAAAE,KAAA,QAAnBA,GAAqBD,aAAawG,GAAe,CACvD,EAAG,CAAC5mB,GAAK,OAAAkf,EAALlf,EAAOmgB,gBAAY,MAAAjB,IAAA,cAAnBA,EAAqBkB,WAAW,CAAC,EAErC,IAAM9D,EAAU,UAA6B,CAC3C,MAAO,CACL,CACEtM,SAAOrE,EAAAA,IAAQ,kCAAkC,EACjD8I,UAAW,SACX/M,MAAO,IACP2N,OAAQ,SACN+G,EACAtb,EACA2E,EACAyT,EACG,CACH,MAAO,EACLpY,GAAM,YAANA,EAAQgf,cAAYnU,EAAAA,IAAQ,OAAO,EACjC,MAEA9D,EAAAA,KAAA,KAEElG,QAAS,UAAM,KAAA4a,EACbrD,GAAM,OAAAqD,EAANrD,EAAQuD,iBAAa,MAAAF,IAAA,QAArBA,EAAAG,KAAAxD,EAAwBpY,GAAM,YAANA,EAAQgf,OAAO,CACzC,EAAEjf,YAED8K,EAAAA,IAAQ,MAAM,CAAC,EALZ,UAMH,CACJ,CAEL,CACF,CAAC,CAEL,EACMyG,GAAiC,CACrC,CACEpC,SAAOrE,EAAAA,IAAQ,OAAO,EACtB0G,UAAW,CAAC,YAAa,SAAS,EAClCmC,SAAU,GACVa,OAAQ,SAAC+G,GAAOtb,EAAiB,CAAF,IAAAgmB,EAAAC,EAAA,OAC7BjmB,GAAM,YAANA,EAAQgf,cAAYnU,EAAAA,IAAQ,OAAO,EACjC7K,GAAM,YAANA,EAAQgf,WAERjY,EAAAA,KAAA,KAAGwU,KAAI,IAAAzQ,OAAM9K,GAAM,OAAAgmB,EAANhmB,EAAQ+e,aAAS,MAAAiH,IAAA,cAAjBA,EAAmBlM,OAAO,EAAG/Z,SACvCC,GAAM,OAAAimB,EAANjmB,EAAQ+e,aAAS,MAAAkH,IAAA,cAAjBA,EAAmBnM,OAAO,CAC1B,CACJ,CACL,EACA,CACE5K,SAAOrE,EAAAA,IAAQ,mBAAmB,EAClC0G,UAAW,OACb,CAAC,EAEH,SACE7K,EAAAA,MAAAgH,EAAAA,SAAA,CAAA3N,SAAA,IACEgH,EAAAA,KAACyY,EAAAA,EAAY,CAACC,SAAS,aAAaC,QAAM7U,EAAAA,IAAQ,kBAAkB,CAAE,CAAE,KACxE9D,EAAAA,KAAA,OAAKrC,UAAWqN,EAAO+I,QAAQ/a,YAC7BgH,EAAAA,KAAC6O,GAAAA,EAAgB,CACfrU,OAAO,UACPH,UAAW,EACXyU,OAAQ,CACNuG,EAAG,GACL,EACA9a,mBAAoB,GACpBgH,QAAS,GACTgJ,QAASpS,GAAK,MAALA,EAAOqR,SAAWe,GAAU,CAAC,EAAJxG,OAAOwG,GAAOoK,GAAAA,EAAKF,EAAQ,CAAC,GAC9DtZ,MAAOgD,EACP6Q,SAAQzM,EAAAA,EAAAA,EAAAA,EAAA,GACHyC,CAAc,MACjBiK,KAAM,WACNlD,aAAAA,EACA2J,aAAc,SAACC,GAAMC,EAASC,EAAY,CAAF,MAAK,CAC3CA,EAAWC,KACXD,EAAWE,MAAM,CAClB,EACDC,OAAQ,UAAF,KAAAC,GAAAvU,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAArI,EAAO4c,EAAS5Z,EAAM,CAAF,OAAAoF,EAAAA,EAAA,EAAAI,KAAA,SAAAlI,EAAE,CAAF,cAAAA,EAAAuF,KAAAvF,EAAAwF,KAAE,CAAF,OAAAxF,OAAAA,EAAAwF,KAAA,EACpBwQ,EAAayH,EAA8B,CAC/CW,QAAS1b,GAAI,YAAJA,EAAM0b,QACfiC,MAAOsE,OAAOjiB,GAAI,YAAJA,EAAM2d,KAAK,CAC3B,CAAC,EAAC,wBAAArgB,EAAAsI,KAAA,IAAA5I,CAAA,EACH,YAAAyc,GAAAjc,EAAAoW,EAAA,QAAA8F,GAAA7T,MAAA,KAAAC,SAAA,SAAA2T,EAAA,IACDrb,SAAUqR,CAAkB,EAC5B,CACH,CAAC,CACC,CAAC,EACN,CAEN,CC1Ge,SAASmT,GAAQhnB,EAAqB,KAAAkf,EAAA+H,EAAAC,EAC3C/H,EAAuBnf,EAAvBmf,mBACR/L,KAAsCtH,GAAAA,GAAW,EAAzCkB,EAASoG,EAATpG,UAAWH,EAAcuG,EAAdvG,eACnB2K,KAAyBC,GAAAA,UAAS,WAAW,EAArCC,EAAYF,EAAZE,aACR1O,KAAgDC,EAAAA,UAE9C,CAAC,CAAC,EAACC,EAAAC,EAAAA,EAAAH,EAAA,GAFEme,GAAgBje,EAAA,GAAEke,EAAmBle,EAAA,GAG5CwK,KAA0CzK,EAAAA,UAAoB,CAAC,CAAC,EAAC0K,GAAAxK,EAAAA,EAAAuK,EAAA,GAA1D2T,EAAa1T,GAAA,GAAE2T,GAAgB3T,GAAA,GACtCG,MAAsC7K,EAAAA,aACpC0C,EAAAA,IAAQ,eAAe,CACzB,EAACoI,GAAA5K,EAAAA,EAAA2K,GAAA,GAFMyT,EAAWxT,GAAA,GAAEyT,EAAczT,GAAA,GAI5B0T,EAAuB,SAC3BC,EACAC,EACAC,EACG,CACH,GAAIlM,IAAAA,GAAAA,SAAQgM,CAAc,EAC1B,KAAIG,KAAmB9N,GAAAA,WAAU2N,CAAc,EAC/C,GAAI,IAAChM,GAAAA,SAAQmM,CAAgB,EAC3B,GAAID,EAAQ,CACV,IAAIE,EAAeD,EAAiB3N,KAClC,SAAC7Y,EAAG,CAAF,OAAKA,GAAC,YAADA,EAAG2B,SAAU2kB,IAAetmB,GAAC,YAADA,EAAGqK,SAAUic,CAAW,CAC7D,EACApX,QAAQC,IAAI,kBAAmBmX,CAAW,EAC1CH,EAAeG,CAAW,EACtBG,IAAcA,EAAazW,SAAW,GAC5C,MACEwW,EAAiBlZ,IAAI,SAACtN,EAAM,CAC1BA,OAAAA,EAAEgQ,UAAWhQ,GAAC,YAADA,EAAG2B,SAAU2kB,IAAetmB,GAAC,YAADA,EAAGqK,SAAUic,EAC/C,IACT,CAAC,EAGLL,GAAiBO,CAAgB,EACnC,EAEME,EAAiB,SACrBC,EACAN,EACG,CACH,IAAIO,EAAUV,EACVW,EACFF,EAAoBG,UAClB,SAAC9mB,EAAG,CAAF,OAAKA,GAAC,YAADA,EAAG8F,WAASwE,EAAAA,IAAQ,eAAe,IAAKtK,GAAC,YAADA,EAAG8F,QAAS,OAAO,CACpE,IAAM,GACJihB,EACFJ,EAAoBG,UAClB,SAAC9mB,EAAG,CAAF,OACAA,GAAC,YAADA,EAAG8F,WAASwE,EAAAA,IAAQ,mBAAmB,IAAKtK,GAAC,YAADA,EAAG8F,QAAS,YAAY,CACxE,IAAM,GACJ+gB,GAAwBE,GAC1BX,EAAqBC,KAAgB/b,EAAAA,IAAQ,eAAe,CAAC,EAC7D8b,EAAqBC,KAAgB/b,EAAAA,IAAQ,mBAAmB,CAAC,GACxDuc,GACTD,KAAUtc,EAAAA,IAAQ,mBAAmB,EACrC8b,EAAqBC,KAAgB/b,EAAAA,IAAQ,eAAe,CAAC,GACpDyc,IACTX,EAAqBC,KAAgB/b,EAAAA,IAAQ,mBAAmB,CAAC,EACjEsc,KAAUtc,EAAAA,IAAQ,eAAe,GAEnC6b,EAAeS,CAAO,CACxB,EAEMI,EAAgB,eAAAvjB,EAAAyE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAArI,EAAOknB,EAAmC,CAAF,IAAAxQ,EAAA1T,EAAAgD,EAAAmhB,EAAAC,EAAA,OAAAhf,EAAAA,EAAA,EAAAI,KAAA,SAAAlI,GAAE,CAAF,cAAAA,GAAAuF,KAAAvF,GAAAwF,KAAE,CAAF,OAAAxF,OAAAA,GAAAwF,KAAA,KAC7CuhB,GAAAA,OAAM,cAAc,EACnCC,WAAW,WAAY,KAAM,MAAM,EACnC/hB,IAAI,EAAC,OAeP,GAjBKmR,EAAGpW,GAAAmI,KAGLzF,EAAO0T,GAAG,YAAHA,EAAK1T,KACdgD,EAAQ0Q,GAAG,YAAHA,EAAK1Q,MACXmhB,EAAuB,CAAC,EACxB,IAAC7M,GAAAA,SAAQtX,CAAI,MAAKukB,GAAAA,SAAQvkB,CAAI,IAC1BokB,EAAShM,GAAAA,EAAOpY,CAAI,EAC1BokB,EAAU7Z,IAAI,SAACtN,GAAM,CACnBknB,OAAAA,EAAUpO,KAAK,CACbnX,MAAO3B,IAAC,YAADA,GAAGunB,KACVld,MAAOrK,IAAC,YAADA,GAAG8F,KACVkK,SACEiX,EAAkBH,UAAU,SAACU,GAAK,CAAF,OAAKA,GAAI1hB,QAAS9F,IAAC,YAADA,GAAGunB,KAAI,KAAM,EACnE,CAAC,EACM,IACT,CAAC,GACF,EACGxhB,GAAK,MAALA,EAAOwO,SAAO,CAAAlU,GAAAwF,KAAA,eAAAxF,GAAAoI,OAAA,SAAS8L,GAAAA,GAAQxO,MAAMA,GAAK,YAALA,EAAOwO,OAAO,CAAC,SACpDxR,GAAMkjB,GAAiBiB,CAAS,EAChC,IAAC7M,GAAAA,SAAQ4M,CAAiB,GAAK,IAAC5M,GAAAA,SAAQ6M,CAAS,GACnDR,EAAeO,EAAmBC,CAAS,EAAC,yBAAA7mB,GAAAsI,KAAA,IAAA5I,CAAA,EAC/C,mBAvBqBQ,EAAA,QAAAkD,EAAAmF,MAAA,KAAAC,SAAA,SAyBtBqF,EAAAA,WAAU,UAAM,CACV,IAACmM,GAAAA,SAAQyL,EAAgB,GAAK,IAACzL,GAAAA,SAAQ2L,CAAa,GACtDU,EAAeZ,GAAoCE,CAAa,CACpE,EAAG,CAACF,EAAgB,CAAC,EAErB,IAAM2B,EAAe,SAACC,EAA+B,CACnD,OAAOA,EAAWpa,IAAI,SAAClL,EAAS,CAC9B,OAAA2G,EAAAA,EAAAA,EAAAA,EAAA,GACK3G,CAAI,MACPqE,GAAIrE,EAAK0D,IAAI,EAEjB,CAAC,CACH,KAEAoI,EAAAA,WAAU,UAAM,KAAAyQ,EAAAK,EACd,MAAKjM,EAAAA,IAAapU,GAAK,OAAAggB,EAALhgB,EAAOmgB,gBAAY,MAAAH,IAAA,cAAnBA,EAAqBgJ,YAAY,EACnD,KAAMC,EAAUH,EAAa9oB,GAAK,OAAAqgB,EAALrgB,EAAOmgB,gBAAY,MAAAE,IAAA,cAAnBA,EAAqB2I,YAAY,EAC9D5B,EAAoB6B,CAAO,EAC3BZ,EAAiBY,CAAO,EAC1B,EAAG,CAACjpB,GAAK,OAAAkf,EAALlf,EAAOmgB,gBAAY,MAAAjB,IAAA,cAAnBA,EAAqB8J,YAAY,CAAC,EAEtC,IAAM5W,GAAsC,CAC1C,CACEpC,SAAOrE,EAAAA,IAAQ,cAAc,EAC7B0G,UAAW,OACXoC,UAAW,SACXyU,WAAY,SAACC,EAAK7jB,EAAmB,KAAA8jB,EAAfxlB,EAAQ0B,EAAR1B,SACpB,OAAIA,EAAW,GAAI5D,GAAK,OAAAopB,EAALppB,EAAOmgB,gBAAY,MAAAiJ,IAAA,SAAAA,EAAnBA,EAAqBJ,gBAAY,MAAAI,IAAA,cAAjCA,EAAmC3a,QAC7C,CACL4a,QAAShC,CACX,EAEO,CACLgC,QAAShC,EACThW,SAAU,EACZ,CAEJ,EACAgE,OAAQ,SAACV,EAAM,CAAF,OAAM3H,EAAU2H,CAAI,EAAI3H,EAAU2H,CAAI,EAAIA,CAAI,CAC7D,EACA,CACE3E,MAAO,MACPqC,UAAW,MACXoC,UAAW,OACb,EACA,CACEzE,MAAO,MACPqC,UAAW,MACXmC,SAAU,EACZ,CAAC,EAGG8H,GAAU,UAAkC,CAChD,MAAO,CACL,CACEtM,SAAOrE,EAAAA,IAAQ,kCAAkC,EACjD8I,UAAW,SACX/M,MAAO,IACP2N,OAAQ,SAAC+G,EAAOtb,EAAQ2E,EAAGyT,EAAQ,CAAF,MAC9B,CAAC,aAAc,gBAAiB,YAAY,EAAEhJ,SAASpP,GAAM,YAANA,EAAQqG,IAAI,EA+BhE,IA9BA,IACEU,EAAAA,KAAA,KAEElG,QAAS,UAAM,KAAA4a,EACbrD,GAAM,OAAAqD,EAANrD,EAAQuD,iBAAa,MAAAF,IAAA,QAArBA,EAAAG,KAAAxD,EAAwBpY,EAAOqG,IAAI,CACrC,EAAEtG,YAED8K,EAAAA,IAAQ,MAAM,CAAC,EALZ,UAMH,KACH9D,EAAAA,KAAA,KAEElG,QAAO4H,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAyO,GAAA,QAAA1O,EAAAA,EAAA,EAAAI,KAAA,SAAA0O,GAAA,eAAAA,GAAArR,KAAAqR,GAAApR,KAAA,QACPkgB,OAAAA,EACED,GAAiBmC,OACf,SAAC7lB,GAAM,CAAF,OAAKA,GAAK0D,OAASrG,EAAOqG,IAAI,CACrC,CACF,EACAsgB,EAAqBJ,EAAevmB,EAAOqG,KAAM,EAAI,EAACmR,GAAApR,KAAA,EAChDwQ,EAAayH,EAAoB,CACrCoK,YAAa,CACXpiB,KAAMrG,GAAM,YAANA,EAAQqG,KACdqiB,IAAKnD,OAAOvlB,GAAM,YAANA,EAAQ0oB,GAAG,EACvB,OAAQ,EACV,CACF,CAAC,EAAC,wBAAAlR,GAAAtO,KAAA,IAAAkO,CAAA,EACH,GAACrX,YAED8K,EAAAA,IAAQ,KAAK,CAAC,EAjBX,QAkBH,CAAC,CAEH,CACX,CAAC,CAEL,EAEA,SACE9D,EAAAA,KAAA2G,EAAAA,SAAA,CAAA3N,YACE2G,EAAAA,MAAA,OAAKhC,UAAWqN,EAAO+I,QAAQ/a,SAAA,IAC7BgH,EAAAA,KAAA,OAAKrC,UAAWqN,EAAO7C,MAAMnP,SAAA,GAAA+K,UAAKD,EAAAA,IAAQ,WAAW,EAAC,YAAgB,KACtEnE,EAAAA,MAAA,OAAKhC,UAAWqN,EAAO4W,uBAAuB5oB,SAAA,IAC3C8K,EAAAA,IAAQ,mBAAmB,EAAE,QAC7B3L,GAAK,OAAAinB,EAALjnB,EAAOmgB,gBAAY,MAAA8G,IAAA,cAAnBA,EAAqByC,aAAa,EAChC,KACLliB,EAAAA,MAAA,OACEhC,UAAWqN,EAAO4W,uBAClBzkB,MAAO,CAAE2kB,aAAc,MAAO,EAAE9oB,SAAA,IAAA+K,UAE5BD,EAAAA,IAAQ,WAAW,EAAC,2BAAgB,QAEvC3L,GAAK,OAAAknB,EAALlnB,EAAOmgB,gBAAY,MAAA+G,IAAA,cAAnBA,EAAqB0C,iBAAiB,EACpC,KACL/hB,EAAAA,KAAA,OAAKrC,UAAWqN,EAAO7C,MAAOhL,MAAO,CAAE2kB,aAAc,MAAO,EAAE9oB,YAC3D8K,EAAAA,IAAQ,cAAc,CAAC,CACrB,KACL9D,EAAAA,KAAC6O,GAAAA,EAAgB,CACfrU,OAAO,KACPH,UAAW,EACXyU,OAAQ,CACNuG,EAAG,GACL,EACA9T,QAAS,GACThH,mBACEpC,GAAK,MAALA,EAAOqR,SACH,GACA,CACEtQ,SAAU,SACVgE,qBAAmB4G,EAAAA,IAAQ,gBAAgB,EAC3C7K,OAAQ,iBAAO,CAAEgH,IAAK+hB,KAAKC,OAAO,EAAI,KAASpU,QAAQ,CAAC,CAAE,CAAC,CAC7D,EAENtD,QAASpS,GAAK,MAALA,EAAOqR,SAAWe,GAAU,CAAC,EAAJxG,OAAOwG,GAAOoK,GAAAA,EAAKF,GAAQ,CAAC,GAC9DtZ,MACE,IAAC0Y,GAAAA,SAAQ2L,CAAa,GAAK,IAAC3L,GAAAA,SAAQyL,EAAgB,EAC/CA,GACD,CAAC,EAEPtQ,SAAQzM,EAAAA,EAAAA,EAAAA,EAAA,GACHyC,CAAc,MACjBiK,KAAM,WACN+G,OAAQ,UAAF,KAAAC,EAAAvU,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAA+P,EAAO/T,EAAGrB,EAAM,CAAF,OAAAoF,EAAAA,EAAA,EAAAI,KAAA,SAAAkQ,EAAE,CAAF,cAAAA,EAAA7S,KAAA6S,EAAA5S,KAAE,CAAF,OAAA4S,OAAAA,EAAA5S,KAAA,EACdwQ,EAAayH,EAA8B,CAC/CoK,YAAa,CACXpiB,KAAM/C,GAAI,YAAJA,EAAM+C,KACZqiB,IAAKnD,OAAOjiB,GAAI,YAAJA,EAAMolB,GAAG,CACvB,CACF,CAAC,EAAC,wBAAA1P,EAAA9P,KAAA,IAAAwP,CAAA,EACH,YAAAqE,EAAA7F,EAAAmB,EAAA,QAAA2E,EAAA7T,MAAA,KAAAC,SAAA,SAAA2T,CAAA,KACD,CACH,CAAC,EACC,CAAC,CACN,CAEN,CC3PO,IAAMkM,GAAgB,SAACjI,EAA8B,CAC1D,IAAIkI,EAAwB,CAAC,EAC7B,MAAItO,GAAAA,SAAQoG,CAAU,GAAK,CAACA,EAAY,MAAO,CAAC,EAChD,QAASmI,EAAI,EAAGA,EAAInI,EAAWrT,OAAQwb,IACrC,QAASC,EAAI,EAAGA,IAACC,EAAGrI,EAAWmI,CAAC,KAAC,MAAAE,IAAA,cAAbA,EAAe1K,UAAUhR,QAAQyb,IAAK,KAAAC,EAAAC,EACpDC,GAAOD,EAAGtI,EAAWmI,CAAC,KAAC,MAAAG,IAAA,cAAbA,EAAe3K,UAAUyK,CAAC,EACxC,GAAIG,GAAO,MAAPA,EAASC,cAAe,KAAAC,EAC1BP,EAAU7P,KAAK,CACbS,SAAO2P,EAAEzI,EAAWmI,CAAC,KAAC,MAAAM,IAAA,SAAAA,EAAbA,EAAe1K,aAAS,MAAA0K,IAAA,cAAxBA,EAA0B3P,QACnCjC,QAAS0R,GAAO,YAAPA,EAAS1R,QAClBC,QAASyR,GAAO,YAAPA,EAASzR,QAClBJ,OAAQ6R,GAAO,YAAPA,EAAS7R,OACjBgS,QAASH,GAAO,YAAPA,EAAS5O,KAClBA,KAAM4O,GAAO,YAAPA,EAAStR,QACjB,CAAC,CACH,CACF,CAEF,OAAOiR,CACT,ECWe,SAASS,IAAc,KAAApb,EAAA6R,EAAAwJ,EAAAC,EACpCnT,KAAyBC,GAAAA,UAAS,gBAAgB,EAA1CmT,EAAYpT,EAAZoT,aACRC,KAAuBC,GAAAA,iBAAgB,EAACC,EAAA5hB,EAAAA,EAAA0hB,EAAA,GAAjCG,EAAYD,EAAA,GACnBE,KAOIxT,GAAAA,UAAS,WAAW,EANtB0I,EAAY8K,EAAZ9K,aACA+K,EAAkBD,EAAlBC,mBACAC,GAAWF,EAAXE,YACAzT,EAAYuT,EAAZvT,aACA0T,EAAgBH,EAAhBG,iBACApB,GAASiB,EAATjB,UAEFqB,KAA2B5T,GAAAA,UAAS,OAAO,EAAnC6T,GAAcD,EAAdC,eACRtiB,MAAkCC,EAAAA,UAAqB,GAAG,EAACC,GAAAC,EAAAA,EAAAH,GAAA,GAApD0G,EAASxG,GAAA,GAAEyG,EAAYzG,GAAA,GAC9BwK,KAAkDzK,EAAAA,UAAkB,EAAK,EAAC0K,EAAAxK,EAAAA,EAAAuK,EAAA,GAAnE6X,EAAiB5X,EAAA,GAAE6X,EAAoB7X,EAAA,GAC1C8X,GAAuBT,EAAarkB,IAAI,MAAM,EAC9C+kB,GAAsBV,EAAarkB,IAAI,aAAa,EAElDglB,EAAwBF,KAAc,SAC5C/J,KAA2CC,GAAAA,WAGxC,EAHSC,EAASF,EAAb5Z,GAAeqP,EAAeuK,EAAfvK,gBAIjBvH,KAASC,GAAAA,WAAU,EAEzBiE,KAAoD7K,EAAAA,UAASkO,CAAe,EAACpD,EAAA5K,EAAAA,EAAA2K,EAAA,GAAtEqL,EAAkBpL,EAAA,GAAE6X,GAAqB7X,EAAA,GAChDgJ,MAAkC9T,EAAAA,UAAiB,EAAC+T,GAAA7T,EAAAA,EAAA4T,GAAA,GAA7C4D,GAAS3D,GAAA,GAAE6O,GAAY7O,GAAA,GAC9BkG,MAA4Cja,EAAAA,UAA0B,CAAC,CAAC,EAACka,GAAAha,EAAAA,EAAA+Z,GAAA,GAAlE4I,GAAc3I,GAAA,GAAE4I,GAAiB5I,GAAA,GACxCG,MAAwCra,EAAAA,UAAS,EAAK,EAACsa,GAAApa,EAAAA,EAAAma,GAAA,GAAhD0I,GAAYzI,GAAA,GAAE0I,GAAe1I,GAAA,GAC9B2I,GACJ,CAACF,KAAgB7L,GAAY,YAAZA,EAAc7U,UAAW,YAC5C+H,GAAeC,GAAAA,EAAKC,QAAoB,EAACC,GAAArK,EAAAA,EAAAkK,GAAA,GAAlCrM,GAAIwM,GAAA,GAEL2Y,GAAmB,eAAArnB,EAAAyE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAArI,GAAA,KAAAgrB,EAAAhoB,EAAAioB,EAAA,OAAA7iB,EAAAA,EAAA,EAAAI,KAAA,SAAAlI,GAAA,eAAAA,GAAAuF,KAAAvF,GAAAwF,KAAA,QAAAxF,OAAAA,GAAAwF,KAAA,KACGuhB,GAAAA,OAAM,2BAAD7c,OACLgW,EAAS,iBAAAhW,OAAgBuT,CAAkB,CACxE,EAAExY,IAAI,EAAC,OAAAylB,EAAA1qB,GAAAmI,KAFCzF,EAAIgoB,EAAJhoB,KAAMioB,EAAID,EAAJC,KAGdN,GAAkB3nB,CAAI,EACtBynB,GAAaQ,GAAI,YAAJA,EAAMC,KAAK,EAAC,wBAAA5qB,GAAAsI,KAAA,IAAA5I,CAAA,EAC1B,oBANwB,QAAA0D,EAAAmF,MAAA,KAAAC,SAAA,MAQnBqiB,GAAe,SAACnoB,EAAiB,CACrC,GAAKA,EACL,KAAIooB,EAAyBzC,GAAc3lB,GAAI,YAAJA,EAAMgc,WAAW,EAC5DgL,EAAiBoB,CAAU,EAC7B,KAEAjd,EAAAA,WAAU,UAAM,IACTkd,GAAAA,OAAMtM,CAAY,IACrBoM,GAAapM,CAAY,EACzB4L,GAAkB,CAAC5L,CAAY,CAAC,EAEpC,EAAG,CAACA,CAAY,CAAC,EAEjB,IAAMuM,GAAc,SAACC,EAAgC,CACnDC,GAAAA,QAAQC,QAAQ,aAADjhB,OACAgW,EAAS,uBAAAhW,OAAsB+gB,EAAmB,wCAAA/gB,OAAuC8f,EAAW,CACnH,CACF,EAEMoB,GAAc,eAAAxnB,EAAAiE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAyO,GAAA,KAAA6U,EAAAC,EAAA5oB,EAAA,OAAAoF,EAAAA,EAAA,EAAAI,KAAA,SAAA0O,GAAA,eAAAA,GAAArR,KAAAqR,GAAApR,KAAA,QAAAoR,OAAAA,GAAApR,KAAA,KACEuhB,GAAAA,OAAM,UAAD7c,OAAWuT,CAAkB,CAAE,EAAExY,IAAI,EAAC,OAAAqmB,EAAA1U,GAAAzO,KAA1DzF,EAAI4oB,EAAJ5oB,KACR2nB,GAAkB,CAAC3nB,CAAI,CAAC,EACxBynB,GAAaznB,GAAI,OAAA2oB,EAAJ3oB,EAAM6oB,eAAW,MAAAF,IAAA,cAAjBA,EAAmBte,MAAM,EACtC8d,GAAanoB,CAAI,EACjB8mB,EAAmB9mB,CAAI,EAAC,wBAAAkU,GAAAtO,KAAA,IAAAkO,CAAA,EACzB,oBANmB,QAAA5S,EAAA2E,MAAA,KAAAC,SAAA,MAQdtH,KAAUsqB,EAAAA,QAAwB,KACxC3d,EAAAA,WAAU,UAAM,CACd,GAAI3M,GAAO,MAAPA,EAAS8B,QAAS,KAAAZ,EACpBlB,GAAO,OAAAkB,EAAPlB,EAAS8B,WAAO,MAAAZ,IAAA,QAAhBA,EAAkB0L,eAAe,CAC/BmB,cAAewP,GAAY,YAAZA,EAAcxP,cAC7Bwc,OAAQhN,GAAY,YAAZA,EAAcgN,OACtBC,eAAgBjN,GAAY,YAAZA,EAAciN,eAC9BC,MAAOlN,GAAY,YAAZA,EAAckN,KACvB,CAAC,EACGlN,GAAY,MAAZA,EAAcvP,aAAajB,EAAawQ,GAAY,YAAZA,EAAcvP,WAAW,CACvE,CACF,EAAG,CAACuP,CAAY,CAAC,KAEjB5Q,EAAAA,WAAU,UAAM,CACd,GAAI3M,GAAO,MAAPA,EAAS8B,SAAWyb,IAAY,MAAZA,IAAY,QAAZA,EAAcmN,oBAAqB,KAAAppB,EACzDtB,GAAO,OAAAsB,EAAPtB,EAAS8B,WAAO,MAAAR,IAAA,QAAhBA,EAAkBsL,eAAe,CAC/B8d,oBAAqBnN,GAAY,YAAZA,EAAcmN,mBACrC,CAAC,CACH,CACF,EAAG,CAACnN,GAAY,YAAZA,EAAcmN,mBAAmB,CAAC,EAEtC,IAAA5J,MAAoCza,EAAAA,UAAiB,EAAC0a,GAAAxa,EAAAA,EAAAua,GAAA,GAA/C6J,GAAU5J,GAAA,GAAE6J,GAAa7J,GAAA,GAChC8J,MAAgDxkB,EAAAA,UAAiB,CAAC,EAACykB,GAAAvkB,EAAAA,EAAAskB,GAAA,GAA5DE,GAAgBD,GAAA,GAAEE,GAAmBF,GAAA,MAC5Cne,EAAAA,WAAU,UAAM,CACd,GAAI3M,GAAO,MAAPA,EAAS8B,SAAWgnB,GAAa,KAAAznB,EACnCrB,GAAO,OAAAqB,EAAPrB,EAAS8B,WAAO,MAAAT,IAAA,QAAhBA,EAAkBuL,eAAe,CAC/Bkc,YAAaA,EACf,CAAC,CACH,CACF,EAAG,CAACA,EAAW,CAAC,EAEhB,IAAAmC,MAAkC5kB,EAAAA,UAA4B,EAAC6kB,GAAA3kB,EAAAA,EAAA0kB,GAAA,GAAxDhc,GAASic,GAAA,GAAEC,GAAYD,GAAA,GACxBE,IAAmB7N,GAAY,YAAZA,EAAc7U,UAAW,YAC9C2iB,IACD,CAAC,QAAS,SAAS,EAAE/d,SAASiQ,GAAY,YAAZA,EAAc7U,MAAM,GACjD,CAAC4gB,MACHtc,GAAM,OAAAP,EAANO,EAAQK,gBAAY,MAAAZ,IAAA,cAApBA,EAAsBa,SAAS,oCAAoC,GACjEge,IACD,CAAC,QAAS,SAAS,EAAEhe,SAASiQ,GAAY,YAAZA,EAAc7U,MAAM,GACjD,CAAC4gB,MACHtc,GAAM,OAAAsR,EAANtR,EAAQK,gBAAY,MAAAiR,IAAA,cAApBA,EAAsBhR,SAAS,uCAAuC,GAClEie,GAAiB,eAAA5U,EAAAhQ,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAA+P,EAAO4U,EAAsC,CAAF,IAAA9pB,EAAA+pB,EAAAC,GAAAlnB,GAAA,OAAAoC,EAAAA,EAAA,EAAAI,KAAA,SAAAkQ,GAAE,CAAF,cAAAA,GAAA7S,KAAA6S,GAAA5S,KAAE,CAAF,OAAA4S,OAAAA,GAAA5S,KAAA,EAC5CtE,GAAO,OAAA0B,EAAP1B,EAAS8B,WAAO,MAAAJ,IAAA,cAAhBA,EAAkBiqB,eAAe,EAAC,OAArDF,OAAAA,EAAUvU,GAAAjQ,KAAAiQ,GAAA5S,KAAG,EACOwQ,EACtByH,EAAkB/U,EAAAA,EAAAA,EAAAA,EAAA,GAEbikB,CAAU,MACb/iB,OAAQ8iB,EACRxd,YAAalB,EACb8e,WAAY5M,CAAS,GAEvBwM,IAAgB,eACZziB,EAAAA,IAAQ,yBAAyB,KACjCA,EAAAA,IAAQ,oBAAoB,CAClC,EAAC,OAAA2iB,GAAAxU,GAAAjQ,KAXOzC,GAAKknB,GAALlnB,MAYJ,CAACA,IAASgnB,IAAgB,aAAanC,GAAgB,EAAK,EAAC,wBAAAnS,GAAA9P,KAAA,IAAAwP,CAAA,EAClE,mBAfsB5X,EAAA,QAAA2X,EAAAtP,MAAA,KAAAC,SAAA,MAiBjBukB,GAAS,eAAAlU,EAAAhR,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAmT,GAAA,QAAApT,EAAAA,EAAA,EAAAI,KAAA,SAAAiT,EAAA,eAAAA,EAAA5V,KAAA4V,EAAA3V,KAAA,YACZqkB,EAAmB,CAAF1O,EAAA3V,KAAA,eAAA2V,EAAA/S,OAAA,iBACK,GAA1B0hB,EAAqB,EAAI,EACpBG,EAAc,CAAF9O,EAAA3V,KAAA,QAAA2V,OAAAA,EAAA3V,KAAA,EACT4lB,GAAe,EAAC,OAAAjQ,EAAA3V,KAAA,gBAAA2V,OAAAA,EAAA3V,KAAA,GAEhBilB,GAAoB,EAAC,QAE7BX,EAAqB,EAAK,EAAC,yBAAA3O,EAAA7S,KAAA,IAAA4S,CAAA,EAC5B,oBATc,QAAArC,EAAAtQ,MAAA,KAAAC,SAAA,MAWTwkB,GAAiB,eAAA/R,EAAApT,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAsU,GAAA,KAAA3Z,EAAAuqB,EAAAhqB,EAAAiqB,GAAAC,GAAA,OAAArlB,EAAAA,EAAA,EAAAI,KAAA,SAAAqU,GAAA,eAAAA,GAAAhX,KAAAgX,GAAA/W,KAAA,QAAA+W,OAAAA,GAAA/W,KAAA,EACLokB,GAAe,EAAC,OAA7BlnB,EAAI6Z,GAAApU,KACN8kB,EAAuBvqB,GAAI,YAAJA,EAAM8V,KAC/B,SAAC7Y,GAAG,CAAF,OAAKA,GAAEytB,gBAAkB,WAAW,CACxC,EACIlsB,GAAO,MAAPA,EAAS8B,SAAWiqB,IACtB/rB,GAAO,OAAA+B,EAAP/B,EAAS8B,WAAO,MAAAC,IAAA,QAAhBA,EAAkB6K,eAAe,CAC/B4d,gBACEjN,GAAY,YAAZA,EAAciN,kBACduB,GAAoB,OAAAC,GAApBD,EAAsBI,iBAAa,MAAAH,KAAA,cAAnCA,GAAqCI,UACvC3B,OAAOlN,GAAY,YAAZA,EAAckN,SAASsB,GAAoB,OAAAE,GAApBF,EAAsBI,iBAAa,MAAAF,KAAA,cAAnCA,GAAqCxB,MACrE,CAAC,GACF,wBAAApP,GAAAjU,KAAA,IAAA+T,CAAA,EACF,oBAbsB,QAAApB,EAAA1S,MAAA,KAAAC,SAAA,SAgBvBqF,EAAAA,WAAU,UAAM,CACTkc,KACLgD,GAAU,EACVC,GAAkB,EACpB,EAAG,CAAC/C,CAAY,CAAC,KAEjBpc,EAAAA,WAAU,UAAM,CACd,OAAO,UAAM,CACX2b,EAAmB,IAAI,CACzB,CACF,EAAG,CAAC,CAAC,EAEL,IAAM+D,MACJznB,EAAAA,MAACgK,GAAAA,EAAM,CACLhM,UAAWqN,EAAOqc,WAClBlsB,MAAO0M,EACP1K,MAAO,CAAE0C,MAAO,MAAO,EACvBlF,SAAU,SAACnB,EAAG,CAAF,OAAKsO,EAAatO,CAAC,CAAC,EAChCgQ,SAAU,CAACsa,EAAa9qB,SAAA,IAExBgH,EAAAA,KAAC2J,GAAAA,EAAOC,OAAM,CAACzO,MAAM,IAAGnC,SAAC,GAAC,CAAe,KACzCgH,EAAAA,KAAC2J,GAAAA,EAAOC,OAAM,CAACzO,MAAM,KAAInC,SAAC,IAAE,CAAe,CAAC,EACtC,EAGJ+f,GAAgB,eAAA/B,EAAAtV,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAA6U,EAAO0F,EAAuB,CAAF,IAAAmL,EAAAC,EAAAC,GAAAC,GAAAlrB,GAAAgD,GAAA,OAAAoC,EAAAA,EAAA,EAAAI,KAAA,SAAA2U,GAAE,CAAF,cAAAA,GAAAtX,KAAAsX,GAAArX,KAAE,CAAF,OAME,GALjDkoB,EACFpL,IAAS,QACL7D,GAAY,YAAZA,EAAcO,aAAc,GAC5BP,GAAY,YAAZA,EAAcO,aAAc,EAC9B2O,GACFlP,GAAY,OAAAgP,EAAZhP,EAAc8M,YAAYmC,CAAc,KAAC,MAAAD,IAAA,cAAzCA,EAA2CI,SACxCF,GAAgB,CAAF9Q,GAAArX,KAAA,eAAAqX,GAAAzU,OAAA,iBAAAyU,OAAAA,GAAArX,KAAA,KACWuhB,GAAAA,OAAK,UAAA7c,OAAgByjB,EAAc,CAAE,EAAE1oB,IAAI,EAAC,OAAvD,GAAuD2oB,GAAA/Q,GAAA1U,KAAlEzF,GAAIkrB,GAAJlrB,KAAMgD,GAAKkoB,GAALloB,MAAK,EACfA,IAAK,MAALA,GAAOwO,SAAO,CAAA2I,GAAArX,KAAA,gBAAAqX,GAAAzU,OAAA,SAAS8L,GAAAA,GAAQxO,MAAMA,IAAK,YAALA,GAAOwO,OAAO,CAAC,UAC/CxR,KACP8mB,EAAmB9mB,EAAI,EACvBwnB,GAAsByD,EAAc,EACpCta,OAAO6X,QAAQ4C,UACb,KACA,GAAE,GAAA5jB,OACCmJ,OAAOuC,SAASmY,OAAM,cAAA7jB,OAAagW,EAAS,uBAAAhW,OAAsByjB,GAAc,wCAAAzjB,OAAuC8f,EAAW,CACvI,GACD,yBAAAnN,GAAAvU,KAAA,IAAAsU,CAAA,EACF,mBAnBqBtG,EAAA,QAAA6G,EAAA5U,MAAA,KAAAC,SAAA,MAqBhBwlB,GAAsB,eAAAC,EAAApmB,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAqV,EAAOkF,EAAuB,CAAF,IAAAoL,EAAA,OAAA5lB,EAAAA,EAAA,EAAAI,KAAA,SAAAmV,GAAE,CAAF,cAAAA,GAAA9X,KAAA8X,GAAA7X,KAAE,CAAF,OACrDkoB,EACFpL,IAAS,OAAS2J,GAAmB,EAAIA,GAAmB,EAC9DC,GAAoBwB,CAAc,EAAC,wBAAArQ,GAAA/U,KAAA,IAAA8U,CAAA,EACpC,mBAJ2B3F,EAAA,QAAAwW,EAAA1lB,MAAA,KAAAC,SAAA,MAMtB0lB,GAAe,CACnB,CACEzrB,IAAK,qBACLkY,KAAM,sBACNrM,SAAOrE,EAAAA,IAAQ,cAAc,CAC/B,EACA,CACExH,IAAK,iBACLkY,KAAM,kBACNrM,SAAOrE,EAAAA,IAAQ,iBAAiB,CAClC,CAAC,EAGGkkB,GAAY,SAAHC,EAQT,KAPJC,EAAUD,EAAVC,WACAC,EAAKF,EAALE,MACAC,EAAUH,EAAVG,WAMA,GAAI,CAACD,EAAO,MAAO,GACnB,IAAMvO,IAAUuO,GAAK,YAALA,EAAOE,oBAAoBF,GAAK,YAALA,EAAOloB,IAClD0lB,OAAAA,GAAc/L,EAAO,KAEnBja,EAAAA,MAAAgH,EAAAA,SAAA,CAAA3N,SAAA,IACE2G,EAAAA,MAAA,OAAKhC,UAAU,6BAA4B3E,SAAA,IACzC2G,EAAAA,MAAA,OAAA3G,SAAA,IACG8K,EAAAA,IAAQ,UAAU,EAAE,SACpB8V,OACC5Z,EAAAA,KAACsoB,GAAAA,EAAWC,KAAI,CACd/T,KAAI,aAAAzQ,OAAegW,EAAS,cAAAhW,OAAaokB,GAAK,YAALA,EAAO1C,oBAAmB,UAAA1hB,OAAS6V,EAAO,EACnFjV,OAAO,SAAQ3L,SAEd4gB,EAAO,CACO,CAClB,EACE,KACL5Z,EAAAA,KAAC4Y,GAAU,CACTpP,SAAU6a,GACVvL,UAAWA,GACXC,iBAAkBmP,EAClBrP,YAAauP,CAAW,CACzB,CAAC,EACC,KACLpoB,EAAAA,KAACwoB,GAAAA,EAAkB,CAAeL,MAAOA,EAAOM,YAAa,EAAK,EAAzC7O,EAA2C,CAAC,EACrE,CAEN,EAEM8O,GAAe,UAAiB,CACpC,SAAI7U,GAAAA,SAAQoQ,EAAc,GAAK,IAACnD,GAAAA,SAAQmD,EAAc,EAAU,GACxDH,KAcN9jB,EAAAA,KAACgoB,GAAS,CAERG,MAAOlE,GAAe6B,EAAgB,EACtCoC,WAAYL,GACZO,WAAYtC,EAA2B,EAHnC,gBAIL,EAlBD7B,IAAc,YAAdA,GAAgBnd,IAAI,SAACqhB,EAAuB7sB,EAAkB,CAC5D,SACE0E,EAAAA,KAAA2G,EAAAA,SAAA,CAAA3N,YACEgH,EAAAA,KAACgoB,GAAS,CAERG,MAAOA,EACPD,WAAYnP,GACZqP,WAAY9P,GAAY,YAAZA,EAAcO,WAAsB,eAAA9U,OAH9BzI,CAAK,CAIxB,CAAC,CACF,CAEN,CAAC,CASL,EAEAqtB,MAA4CvnB,EAAAA,UAAiB,EAACwnB,GAAAtnB,EAAAA,EAAAqnB,GAAA,GAAvDE,GAAcD,GAAA,GAAEE,GAAiBF,GAAA,GACxCG,MAAsC3nB,EAAAA,UAAiB,EAAC4nB,GAAA1nB,EAAAA,EAAAynB,GAAA,GAAjDE,GAAWD,GAAA,GAAEE,GAAcF,GAAA,GAClC/e,MAAiCC,EAAAA,GAAa,EAAtCC,GAAWF,GAAXE,YAAaC,GAAOH,GAAPG,QAEf+e,GAAgB,SAACtgB,EAAsBugB,EAA0B,CACrE,IAAQtE,EAAuDjc,EAAvDic,oBAAqBuE,EAAkCxgB,EAAlCwgB,gBAAiBC,GAAiBzgB,EAAjBygB,aAC1CD,GACFjf,GAAQ,EACR0e,GAAkBO,CAAe,EACjCH,GAAeI,EAAY,IAE3BvF,GAAsBe,CAA6B,EAC9CsE,GAGHrF,GAAsBe,CAA6B,EACnDD,GAAYC,CAA6B,EAG/C,EAEAyE,MAA0CnoB,EAAAA,UAAkB,EAAK,EAACooB,GAAAloB,EAAAA,EAAAioB,GAAA,GAA3DE,GAAaD,GAAA,GAAEE,GAAgBF,GAAA,GAEhCG,GAAiB,UAAM,KAAAnrB,EAC3BzD,GAAO,OAAAyD,EAAPzD,EAAS8B,WAAO,MAAA2B,IAAA,QAAhBA,EAAkBkoB,eAAe,EAAEkD,KAAI,eAAAC,EAAAnoB,EAAAA,EAAAC,EAAAA,EAAC,EAADC,KAAC,SAAAkoB,EAAOtD,EAAY,CAAF,OAAA7kB,EAAAA,EAAA,EAAAI,KAAA,SAAAgoB,GAAE,CAAF,cAAAA,GAAA3qB,KAAA2qB,GAAA1qB,KAAE,CAAF,OACvDqqB,OAAAA,GAAiB,EAAI,EAACK,GAAA1qB,KAAA,EAChBikB,GAAW/gB,EAAAA,EAAAA,EAAAA,EAAC,CAAD,EAEVikB,CAAU,MACbzd,YAAalB,EACb8e,WAAY5M,EACZiQ,SAAUtE,EAAU,GAEtB,SAAC7c,GAAsB,CAAF,OAAKsgB,GAActgB,GAAQ,EAAK,CAAC,CACxD,EAAC,OACD6gB,GAAiB,EAAK,EAAC,wBAAAK,GAAA5nB,KAAA,IAAA2nB,CAAA,EACxB,CAAC,EAAD,gBAAAvY,EAAA,QAAAsY,EAAAznB,MAAA,KAAAC,SAAA,MACH,EAGM4nB,GAAqB,eAAAC,EAAAxoB,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAuoB,GAAA,KAAAla,EAAA,OAAAtO,EAAAA,EAAA,EAAAI,KAAA,SAAAqoB,EAAA,eAAAA,EAAAhrB,KAAAgrB,EAAA/qB,KAAA,QAAA+qB,OAAAA,EAAA/qB,KAAA,KACVgrB,GAAAA,IAAgB,CAChCC,YAAa,GAAFvmB,OAAKklB,GAAW,KAAAllB,OAAIgW,CAAS,CAC1C,CAAC,EAAC,OAFI9J,EAAGma,EAAApoB,QAGLuoB,GAAAA,IAAoBta,CAAG,EAAEua,IAAIb,GAAe,EAAC,wBAAAS,EAAAjoB,KAAA,IAAAgoB,CAAA,EAClD,oBAL0B,QAAAD,EAAA9nB,MAAA,KAAAC,SAAA,MAOrB8lB,GAAQlE,IAAc,YAAdA,GAAiB6B,EAAgB,EAE/C,SACEnmB,EAAAA,MAAA,OAAKhC,UAAWqN,EAAOyf,UAAUzxB,SAAA,IAC/BgH,EAAAA,KAAC+J,GAAY,CACXM,aAAc8X,GACdnY,UAAWA,GACXM,YAAW5I,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAA8oB,GAAA,QAAA/oB,EAAAA,EAAA,EAAAI,KAAA,SAAA4oB,EAAA,eAAAA,EAAAvrB,KAAAurB,EAAAtrB,KAAA,QACXkkB,OAAAA,EAAiB,CAAC,CAAC,EAACoH,EAAAtrB,KAAA,EACdinB,GAAkB,WAAW,EAAC,wBAAAqE,EAAAxoB,KAAA,IAAAuoB,CAAA,EACrC,EAAC,CACH,KACD/qB,EAAAA,MAACirB,GAAAA,GAAa,CACZjtB,UAAWqb,GAAAA,EAAGhO,EAAO6f,YAAWC,EAAAA,EAAAA,EAAAA,EAAAA,EAAAA,EAAAA,EAAAA,EAAA,GAC7B9f,EAAO,YAAiB,EAAC+X,GAAY,MAAZA,EAAcgI,gBAAe,EACtD/f,EAAO,UAAe+X,GAAY,YAAZA,EAAcgI,eAAe,EACnD/f,EAAO,eAAoB,EAAC+X,GAAY,MAAZA,EAAcgI,qBAAmBtgB,EAAAA,IAAK,CAAC,EACnEO,EAAO,cAAkB+X,GAAY,YAAZA,EAAcgI,qBAAmBtgB,EAAAA,IAAK,CAAC,CAClE,EAAEzR,SAAA,IAEHgH,EAAAA,KAACyY,EAAAA,EAAY,CACXC,SAAS,qBACTC,QAAM7U,EAAAA,IAAQ,cAAc,CAAE,CAC/B,KACDnE,EAAAA,MAACya,GAAAA,EAAO,CACNkD,KAAI,GACJpV,OAAO,aACPmG,UAAW,GACXtT,QAASA,EACTwiB,SAAU,CAAEC,GAAI,GAAIC,GAAI,GAAIC,GAAI,EAAGC,GAAI,CAAE,EACzCrU,SAAU,CAAEJ,QAAMuB,EAAAA,IAAK,EAAI,GAAK,EAAG,EACnClB,WAAY,CAAEL,QAAMuB,EAAAA,IAAK,EAAI,GAAK,EAAG,EACrCugB,cAAe,CACbvF,oBAAqBnO,CACvB,EAAEte,SAAA,IAEFgH,EAAAA,KAACmJ,GAAAA,EAAW,CACV7J,KAAK,cACLuE,SAAOC,EAAAA,IAAQ,cAAc,EAC7BwF,SAAU,CAAEJ,SAAMuB,EAAAA,IAAK,EAAI,GAAQ,EACnClB,WAAY,CAAEL,SAAMuB,EAAAA,IAAK,EAAI,GAAQ,EACrCjB,SAAQ,GACT,KACDxJ,EAAAA,KAACmJ,GAAAA,EAAW,CACV7J,KAAK,sBACLuE,SAAOC,EAAAA,IAAQ,qBAAqB,EACpCwF,SAAU,CAAEJ,QAAMuB,EAAAA,IAAK,EAAI,GAAK,EAAG,EACnClB,WAAY,CAAEL,QAAMuB,EAAAA,IAAK,EAAI,GAAK,EAAG,EACrCjB,SAAQ,GACT,KACDxJ,EAAAA,KAACyJ,GAAAA,EAAY,CACXnK,KAAK,gBACLkK,SAAU,CAACsa,EACXjgB,SAAOC,EAAAA,IAAQ,QAAQ,EACvB+Z,MAAOuJ,GACPhe,MAAO,CAAC,CAAEC,SAAU,EAAK,CAAC,EAC1BC,SAAU,CAAEJ,KAAM,CAAE,EACpBK,WAAY,CAAEL,KAAM,EAAG,EACvBW,IAAK,EAAI,CACV,KACD7J,EAAAA,KAACyJ,GAAAA,EAAY,CACXnK,KAAK,SACLkK,SAAU,CAACsa,EACX1a,MAAO,CAAC,CAAEC,SAAU,EAAK,CAAC,EAC1BxF,SAAOC,EAAAA,IAAQ,QAAQ,EACvB+F,IAAK,GACLC,IAAK,IACLR,SAAU,CAAEJ,QAAMuB,EAAAA,IAAK,EAAI,GAAK,EAAG,EACnClB,WAAY,CAAEL,KAAM,CAAE,EACtBQ,cACE1J,EAAAA,KAAA,OACErC,UAAWqN,EAAOqc,WAClBlqB,MAAO,CAAEmhB,MAAO,QAAS2M,IAAK,KAAM,EAAEjyB,SACvC,GAED,CAAK,CACN,CACF,KACDgH,EAAAA,KAACyJ,GAAAA,EAAY,CACXnK,KAAK,iBACLuE,SAAOC,EAAAA,IAAQ,aAAa,EAC5BwF,SAAU,CAAEJ,SAAMuB,EAAAA,IAAK,EAAI,GAAQ,EACnClB,WAAY,CAAEL,SAAMuB,EAAAA,IAAK,EAAI,GAAQ,EACrCrB,MAAO,CAAC,CAAEC,SAAU,EAAK,CAAC,EAC1BG,SAAU6a,GACVxG,SACE7d,EAAAA,KAAA,OACErC,UAAWqN,EAAOqc,WAClBlqB,MAAO,CAAE8tB,IAAK,MAAO3M,SAAO7T,EAAAA,IAAK,EAAI,QAAU,OAAQ,EAAEzR,YAExD8K,EAAAA,IAAQ,kBAAkB,CAAC,CACzB,CACN,CACF,KACD9D,EAAAA,KAACyJ,GAAAA,EAAY,CACXnK,KAAK,QACLuE,SAAOC,EAAAA,IAAQ,aAAa,EAC5BsF,MAAO,CAAC,CAAEC,SAAU,EAAK,CAAC,EAC1BC,SAAU,CAAEJ,QAAMuB,EAAAA,IAAK,EAAI,GAAK,EAAG,EACnClB,WAAY,CAAEL,QAAMuB,EAAAA,IAAK,EAAI,EAAI,EAAG,EACpCjB,SAAU6a,GACVxG,MACEwG,IAAiCP,EAC/B,MAEA9jB,EAAAA,KAAA,KACErC,UAAWqN,EAAOqc,WAClBlqB,MAAO,CAAE8tB,IAAK,MAAO3M,SAAO7T,EAAAA,IAAK,EAAI,SAAW,OAAQ,EACxD3Q,QAAO4H,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAspB,GAAA,KAAAC,EAAAC,EAAA,OAAAzpB,EAAAA,EAAA,EAAAI,KAAA,SAAAspB,EAAA,eAAAA,EAAAjsB,KAAAisB,EAAAhsB,KAAA,QAAAgsB,OAAAA,EAAAhsB,KAAA,EACDwQ,EAAayH,EAA8B,CAC/CiO,eACExqB,GAAO,OAAAowB,EAAPpwB,EAAS8B,WAAO,MAAAsuB,IAAA,cAAhBA,EAAkB7O,cAAc,gBAAgB,EAClDkJ,MAAOzqB,GAAO,OAAAqwB,EAAPrwB,EAAS8B,WAAO,MAAAuuB,IAAA,cAAhBA,EAAkB9O,cAAc,OAAO,CAChD,CAAC,EAAC,OACF2I,GAAe,EAAC,wBAAAoG,EAAAlpB,KAAA,IAAA+oB,CAAA,EACjB,GAAClyB,YAED8K,EAAAA,IAAQ,qBAAqB,CAAC,CAC9B,EAGP+F,IAAK,GACLC,IAAK,GAAI,CACV,KACD9J,EAAAA,KAAA,OAAKrC,UAAWqN,EAAOsgB,cAActyB,SAClC,CAAC8qB,GAAgBK,MAChBxkB,EAAAA,MAAC0H,GAAAA,EAAK,CAAArO,SAAA,CACHotB,MACCpmB,EAAAA,KAACiB,EAAAA,EAAiB,CAChBnH,QAAS,kBAAMwsB,GAAkB,SAAS,CAAC,EAACttB,YAE3C8K,EAAAA,IAAQ,YAAY,CAAC,CACL,EAEnB,GAEDuiB,MACCrmB,EAAAA,KAACiB,EAAAA,EAAiB,CAChBnH,QAAS,UAAM,CACb,GAAI,IAAC+Z,GAAAA,SAAQsO,EAAS,EACpB,OAAO+D,GAAa,CAAE/Y,KAAM,EAAK,CAAC,EACpCmZ,GAAkB,WAAW,CAC/B,EAAEttB,YAED8K,EAAAA,IAAQ,eAAe,CAAC,CACR,EAEnB,GAEDqiB,MACCnmB,EAAAA,KAAA2G,EAAAA,SAAA,CAAA3N,SACG,CAACmrB,OACAxkB,EAAAA,MAAAgH,EAAAA,SAAA,CAAA3N,SAAA,EACG+O,GAAM,OAAA8a,EAAN9a,EAAQK,gBAAY,MAAAya,IAAA,cAApBA,EAAsBxa,SACrB,oCACF,OACErI,EAAAA,KAACsC,GAAAA,GAAM,CAACxI,QAAS,kBAAMsqB,GAAgB,EAAI,CAAC,EAACprB,YAC1C8K,EAAAA,IAAQ,MAAM,CAAC,CACV,KAEV9D,EAAAA,KAACuH,GAAe,CACdpI,KAAMA,GACNyI,aAAc,CACZic,YAAaA,GACbyB,OAAQhN,GAAY,YAAZA,EAAcgN,OACtBE,MAAOlN,GAAY,YAAZA,EAAckN,KACvB,EACA/d,SAAU,SAACoB,EAA4B,CACrCya,GAAW/gB,EAAAA,EAAAA,EAAAA,EAAC,CAAD,EAEJsG,CAAM,MACT6e,SAAUpQ,CAA4B,GAExC,SAACzO,EAAsB,CAAF,OACnBsgB,GAActgB,EAAQ,EAAI,CAAC,CAC/B,CACF,CAAE,CACH,CAAC,EACF,CACH,CACD,EAEF,EACD,EACI,KAEP7I,EAAAA,KAACiB,EAAAA,EAAiB,CAChBnH,QAAS6vB,GACTpoB,QAASkoB,GAAczwB,YAEtB8K,EAAAA,IAAQ,gCAAgC,CAAC,CACzB,CACpB,CACE,CAAC,EACC,KACT9D,EAAAA,KAACyY,EAAAA,EAAY,CACXC,SAAS,iBACTC,KACE,CAACmL,MAAgBvX,EAAAA,IAAa0X,EAAc,KACxCngB,EAAAA,IAAQ,eAAe,EAAC,GAAAC,UACrBD,EAAAA,IAAQ,iBAAiB,CAAC,EAAAC,OAC3B+U,GAAY,SAAH/U,OAAO+U,GAAS,UAAM,EAAE,CAExC,CACF,KAEDnZ,EAAAA,MAAA,OAAKhC,UAAWqN,EAAOugB,UAAUvyB,SAAA,IAE/BgH,EAAAA,KAAC0oB,GAAY,EAAE,EACb5E,EAOA,MANA9jB,EAAAA,KAACmf,GAAO,CACN7G,aAAcA,EACd9O,SAAU6a,GACV/M,mBAAoBA,CAA6B,CAClD,CAGF,EACE,EACHwM,EAiCA,MAhCAnkB,EAAAA,MAAAgH,EAAAA,SAAA,CAAA3N,SAAA,IACEgH,EAAAA,KAACoX,GAAY,CACXkB,aAAcA,EACd9O,SAAU6a,GACV/M,mBAAoBA,CAA6B,CAClD,KACDtX,EAAAA,KAAC8e,GAAQ,CACPxG,aAAcA,EACd9O,SAAU6a,GACV/M,mBAAoBA,CAA6B,CAClD,KACDtX,EAAAA,KAACyY,EAAAA,EAAY,CACXC,SAAS,iBACTC,QAAM7U,EAAAA,IAAQ,kBAAkB,CAAE,CACnC,EACA,IAAC+P,GAAAA,SAAQyE,GAAY,YAAZA,EAAcC,WAAW,MACnCuI,GAAAA,SAAQxI,GAAY,YAAZA,EAAcC,WAAW,EAC7BD,GAAY,OAAAwK,EAAZxK,EAAcC,eAAW,MAAAuK,IAAA,cAAzBA,EAA2Bhc,IACzB,SAAClL,EAAmBN,EAAe,CAAF,SAC/B0E,EAAAA,KAACkZ,GAAU,CACT1P,SAAU6a,GACVpK,WAAYre,EACZ0b,mBAAoBA,EAEpBqC,kBAAmBwO,IAAK,YAALA,GAAO1C,oBAC1B7L,SAASuO,IAAK,YAALA,GAAOE,oBAAoBF,IAAK,YAALA,GAAOloB,GAAG,cAAA8D,OAF7BzI,CAAK,CAGvB,CAAC,CAEN,EACA,EAAE,EACN,CAGH,EACY,KAGfqE,EAAAA,MAAA,OAAKxC,MAAO,CAAEquB,QAAM/gB,EAAAA,IAAK,EAAI,YAAc,WAAY,EAAEzR,SAAA,IACvDgH,EAAAA,KAACyrB,GAAAA,EAAY,CACXC,iBAAejhB,EAAAA,IAAK,EAAIO,EAAO2gB,SAAW3gB,EAAO4gB,OACjD1kB,MACG4c,EAkBGiE,GAjBAA,GAAahkB,OAAO,CAClB,CACEzH,IAAK,gBACLkY,KAAM,iBACNrM,SAAOrE,EAAAA,IAAQ,eAAe,CAChC,EACA,CACExH,IAAK,aACLkY,KAAM,cACNrM,SAAOrE,EAAAA,IAAQ,kBAAkB,CACnC,EACA,CACExH,IAAK,iBACLkY,KAAM,kBACNrM,SAAOrE,EAAAA,IAAQ,kBAAkB,CACnC,CAAC,CACF,CAEN,CACF,KACD9D,EAAAA,KAAC+K,EAAAA,EAASxI,EAAAA,EAAAA,EAAAA,EAAA,GACJ4H,EAAW,MACfhC,SAAOrE,EAAAA,IAAQ,gBAAgB,EAC/BjE,MAAO,IACPqL,UAAW+e,GAAsBjxB,YAEjCgH,EAAAA,KAAA,QAAM7C,MAAO,CAAE0uB,SAAU,MAAO,EAAE7yB,YAC/ByR,EAAAA,IAAK,EAAI,+CAAH1G,OAC0C8kB,GAAc,4EAE7DlpB,EAAAA,MAAAgH,EAAAA,SAAA,CAAA3N,SAAA,CAAE,0DAEAgH,EAAAA,KAAA,QAAM7C,MAAO,CAAEuC,MAAO,KAAM,EAAE1G,SAAE6vB,EAAc,CAAO,EAAC,sIAExD,EAAE,CACH,CACG,CAAC,EACE,CAAC,EACT,CAAC,EACH,CAET,C,yIC5oBMiD,EAAgB,SAAhBA,EAAa7uB,EAAiD,KAAA8uB,EAAA9uB,EAA3CjE,SAAAA,EAAQ+yB,IAAA,OAAG,CAAC,EAACA,EACpC,OAAK/yB,EAAS4N,OAEZ5N,EACG8N,IAAI,SAACklB,EAAO,CAAF,OAAKF,EAAcE,CAAK,CAAC,GACnCC,OAAO,SAACC,EAAKlL,GAAK,CAAF,OAAKgB,KAAKlY,IAAIoiB,EAAKlL,EAAG,CAAC,GAAI,EAJnB,CAM/B,EAEMmL,EAAe,SACnBnzB,EACAozB,EAC4C,CAC5C,IAAAC,KAAmCC,GAAAA,IAAkCtzB,CAAQ,EAACuzB,EAAAjrB,EAAAA,EAAA+qB,EAAA,GAAvEG,GAAaD,EAAA,GAAE3U,GAAS2U,EAAA,GAC/B,GAAIC,GAAc5lB,SAAW,EAAG,CAC9B,GAAIgR,GAAUhR,SAAW,EAAG,MAAO,CAAC,KAAM,CAAC,CAAC,EAE5C,IAAM6lB,GAAU7U,GAAUqU,OAAO,SAACC,GAAKlL,GAAK,CAAF,OACxCkL,GAAI/wB,MAAMyL,OAASoa,GAAI7lB,MAAMyL,OAASslB,GAAMlL,EAAG,CACjD,EACM0L,EAAmB9U,GAAUvF,KAAK,SAACsa,GAAG,CAAF,OAAKA,GAAExxB,QAAUixB,CAAY,GACjEQ,GAAOF,GAAoBD,GAC3BI,GAASjV,GAAU6J,OAAO,SAACuK,GAAO,CAAF,OAAKA,KAAUY,EAAI,GACzD,MAAO,CAACA,GAAMC,EAAM,CACtB,CAEA,IAAMC,GAA8BN,GAAcP,OAAO,SAACC,GAAKlL,GAAK,CAAF,OAChE8K,EAAcI,EAAG,GAAKJ,EAAc9K,EAAG,EAAIkL,GAAMlL,EAAG,CACtD,EACM6L,GAASL,GAAc/K,OAC3B,SAAAhkB,GAAA,KAAGtC,GAAKsC,GAALtC,MAAK,OAAOA,KAAU2xB,GAA4B3xB,KAAK,CAC5D,EACA,MAAO,CAAC2xB,GAA6B,CAAC,EAAH/oB,OAAA4Q,EAAAA,EAAMkY,EAAM,EAAAlY,EAAAA,EAAKiD,EAAS,GAC/D,EAEamV,EAAsB,SAAtBA,EAAmBrb,EAGZ,KAFhBvW,EAAKuW,EAALvW,MAAOixB,EAAY1a,EAAZ0a,aAAYY,EAAAtb,EAAE1Y,SAAAA,GAAQg0B,IAAA,OAAG,CAAC,EAACA,EACpCC,GAAc5qB,UAAAuE,OAAA,GAAAvE,UAAA,KAAAoM,OAAApM,UAAA,GAAG,CAAC,EAElB6qB,GAAoBf,EAAanzB,GAAUozB,CAAY,EAACe,EAAA7rB,EAAAA,EAAA4rB,GAAA,GAAjDE,GAASD,EAAA,GACVE,GAAc,CAAC,EAAJtpB,OAAA4Q,EAAAA,EAAOsY,EAAI,GAAE9xB,CAAK,CAAC,EACpC,MAAO,CACLA,MAAAA,EACA6wB,MAAOoB,GAAYL,EAAoBK,GAAWC,EAAW,EAAI5e,OACjEiO,IAAK,GAAF3Y,OAAK/K,GAAS8N,IAAI,SAAC6lB,GAAG,CAAF,OAAKA,GAAExxB,KAAK,GAAEmyB,KAAK,GAAG,EAAC,KAAAvpB,OAAI5I,CAAK,EACvD8xB,KAAMI,EACR,CACF,EAEaE,EAAiB,SAACC,EAAsC,CACnE,OAAOA,EAASC,YACd,SAACvB,EAAKlL,EAAK1lB,EAAU,CACnB,IAAM+xB,GAAcG,EAAS/X,MAAM,EAAGna,EAAQ,CAAC,EAC/C,OAAI4wB,EAAI/wB,MACC,CAAEA,MAAO6lB,EAAKgL,MAAOE,EAAKe,KAAMI,EAAY,EAE5C,CAAElyB,MAAO6lB,EAAKiM,KAAMI,EAAY,CAE3C,EACA,CAAElyB,MAAO,GAAI8xB,KAAM,CAAC,CAAE,CACxB,CACF,C,qIC/DaS,EAA2B,SAA3BA,EAAwBzwB,EAEnC0wB,EACkB,KAFhBxyB,EAAK8B,EAAL9B,MAAOnC,EAAQiE,EAARjE,SAGT,OAAKA,GAAQ,MAARA,EAAU4N,OACR,CACLzL,MAAAA,EACAnC,SAAUA,EAAS8N,IAAI,SAAC6lB,EAAG,CAAF,OAAKe,EAAyBf,EAAGxxB,CAAK,CAAC,GAChEwyB,OAAAA,CACF,EAL8B,CAAExyB,MAAAA,EAAOwyB,OAAAA,CAAO,CAMhD,EAEaC,EAAwB,SAAxBA,EAAqBnwB,EAEhCkwB,EACqC,KAAAE,EAFnClpB,EAAMlH,EAANkH,OAAQ+X,EAAGjf,EAAHif,IAAK1jB,EAAQyE,EAARzE,SAGT80B,GAAoBpR,GAAG,OAAAmR,EAAHnR,EAAKqR,MAAM,IAAI,EAAE,CAAC,KAAC,MAAAF,IAAA,cAAnBA,EAAqBE,MAAM,GAAG,EACxD,GAAI,CAACD,GAAmB,MAAO,CAAE3yB,MAAOwJ,EAAQgpB,OAAAA,CAAO,EAEvD,IAAMK,GAA4BC,QAAQj1B,EAAU,SAAA0Y,GAAA,KAAG/M,EAAM+M,GAAN/M,OAAM,OAAOA,CAAM,GAC1E,MAAO,CACLxJ,MAAOwJ,EACP3L,SAAU80B,GAAkBhnB,IAAI,SAACgM,GAAW,KAAAob,EAC1C,OAAAA,EAAIF,GAA0Blb,EAAM,KAAC,MAAAob,IAAA,QAAjCA,EAAmCtnB,OAC9BgnB,EACLI,GAA0Blb,EAAM,EAAE,CAAC,EACnCnO,CACF,EAEK,CAAExJ,MAAO2X,GAAQ6a,OAAQhpB,CAAO,CACzC,CAAC,EACDgpB,OAAAA,EACAjR,IAAAA,CACF,CACF,EAEa4P,GAAoC,UAAH,KAC5CtzB,EAAyBqJ,UAAAuE,OAAA,GAAAvE,UAAA,KAAAoM,OAAApM,UAAA,GAAG,CAAC,EAAC,OAE9BrJ,EAASizB,OACP,SAACC,EAAKlL,EAAQ,KAAAmN,EACZ,OAAAA,EAAInN,EAAIhoB,YAAQ,MAAAm1B,IAAA,QAAZA,EAAcvnB,OAChBslB,EAAI,CAAC,EAAE5Z,KAAK0O,CAAG,EAEfkL,EAAI,CAAC,EAAE5Z,KAAK0O,CAAG,EAEVkL,CACT,EACA,CAAC,CAAC,EAAG,CAAC,CAAC,CACT,CAAC,EAEUkC,EAAmB,SAAnBA,EAAoBC,EAAgC,KAAAC,EAC/D,OAAIA,EAACD,EAAKr1B,YAAQ,MAAAs1B,IAAA,QAAbA,EAAe1nB,OACbynB,EAAKr1B,SAASizB,OAAO,SAACC,EAAKlL,EAAK,CAAF,OAAKkL,EAAMkC,EAAiBpN,CAAG,CAAC,EAAE,CAAC,EAAI,EADzC,CAErC,EAEauN,EAAe,SAAfA,EAAY7b,EAA6C,KAAvC1Z,EAAQ0Z,EAAR1Z,SAC7B,GAAI,EAACA,GAAQ,MAARA,EAAU4N,QAAQ,MAAO,GAC9B,IAAM4nB,EAA0Bx1B,EAASizB,OACvC,SAACC,EAAKlL,EAAK,CAAF,IAAAyN,EAAA,OAAKvC,IAAOuC,EAAAzN,EAAIhoB,YAAQ,MAAAy1B,IAAA,QAAZA,EAAc7nB,OAAS,EAAI,EAAE,EAClD,CACF,EACA,OAAI4nB,GAA2B,EAAU,GAClCx1B,EAASizB,OAAO,SAACC,EAAKlL,EAAK,CAAF,OAAKkL,GAAOqC,EAAavN,CAAG,CAAC,EAAE,EAAK,CACtE,C", "sources": ["webpack://labwise-web/./node_modules/@ant-design/icons-svg/es/asn/ExperimentFilled.js", "webpack://labwise-web/./node_modules/@ant-design/icons/es/icons/ExperimentFilled.js", "webpack://labwise-web/./node_modules/@ant-design/icons/es/icons/EyeOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/icons-svg/es/asn/FieldTimeOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/icons/es/icons/FieldTimeOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/icons-svg/es/asn/MessageOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/icons/es/icons/MessageOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/icons-svg/es/asn/StarOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/icons/es/icons/StarOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/pro-table/node_modules/@ant-design/icons/es/icons/PlusOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/pro-table/es/components/EditableTable/index.js", "webpack://labwise-web/./src/components/Arrow.tsx", "webpack://labwise-web/./src/components/ButtonWithLoading/index.tsx", "webpack://labwise-web/./src/components/StatusRender/index.tsx", "webpack://labwise-web/./src/hooks/useOptions.ts", "webpack://labwise-web/./src/pages/projects/components/EnumSwitcher.tsx", "webpack://labwise-web/./src/pages/projects/components/OtherQuoteModal/index.tsx", "webpack://labwise-web/./src/pages/projects/quotation-records/detail/index.less?0df5", "webpack://labwise-web/./src/pages/projects/quotation-records/detail/ConfirmQuoteModal.tsx", "webpack://labwise-web/./src/components/QuoteMaterialTable/index.less?06cd", "webpack://labwise-web/./src/components/QuoteMaterialTable/Supplier.tsx", "webpack://labwise-web/./src/components/QuoteMaterialTable/index.tsx", "webpack://labwise-web/./src/pages/projects/quotation-records/detail/MaterialList/index.tsx", "webpack://labwise-web/./src/pages/projects/quotation-records/detail/StepButton/index.tsx", "webpack://labwise-web/./src/pages/projects/quotation-records/detail/MyReaction/index.tsx", "webpack://labwise-web/./src/pages/projects/quotation-records/detail/Schedule/index.tsx", "webpack://labwise-web/./src/pages/projects/quotation-records/detail/Summary/index.tsx", "webpack://labwise-web/./src/pages/projects/quotation-records/detail/utils.ts", "webpack://labwise-web/./src/pages/projects/quotation-records/detail/index.tsx", "webpack://labwise-web/./src/types/SyntheticRoute/SyntheticLink.ts", "webpack://labwise-web/./src/types/SyntheticRoute/SyntheticTree.ts"], "sourcesContent": ["// This icon file is generated automatically.\nvar ExperimentFilled = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M218.9 636.3l42.6 26.6c.1.1.3.2.4.3l12.7 8 .3.3a186.9 186.9 0 0094.1 25.1c44.9 0 87.2-15.7 121-43.8a256.27 256.27 0 01164.9-59.9c52.3 0 102.2 15.7 144.6 44.5l7.9 5-111.6-289V179.8h63.5c4.4 0 8-3.6 8-8V120c0-4.4-3.6-8-8-8H264.7c-4.4 0-8 3.6-8 8v51.9c0 4.4 3.6 8 8 8h63.5v173.6L218.9 636.3zm333-203.1c22 0 39.9 17.9 39.9 39.9S573.9 513 551.9 513 512 495.1 512 473.1s17.9-39.9 39.9-39.9zM878 825.1l-29.9-77.4-85.7-53.5-.1.1c-.7-.5-1.5-1-2.2-1.5l-8.1-5-.3-.3c-29-17.5-62.3-26.8-97-26.8-44.9 0-87.2 15.7-121 43.8a256.27 256.27 0 01-164.9 59.9c-53 0-103.5-16.1-146.2-45.6l-28.9-18.1L146 825.1c-2.8 7.4-4.3 15.2-4.3 23 0 35.2 28.6 63.8 63.8 63.8h612.9c7.9 0 15.7-1.5 23-4.3a63.6 63.6 0 0036.6-82.5z\" } }] }, \"name\": \"experiment\", \"theme\": \"filled\" };\nexport default ExperimentFilled;\n", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport ExperimentFilledSvg from \"@ant-design/icons-svg/es/asn/ExperimentFilled\";\nimport AntdIcon from '../components/AntdIcon';\nvar ExperimentFilled = function ExperimentFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: ExperimentFilledSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(ExperimentFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'ExperimentFilled';\n}\nexport default RefIcon;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport EyeOutlinedSvg from \"@ant-design/icons-svg/es/asn/EyeOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar EyeOutlined = function EyeOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: EyeOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(EyeOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'EyeOutlined';\n}\nexport default RefIcon;", "// This icon file is generated automatically.\nvar FieldTimeOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"defs\", \"attrs\": {}, \"children\": [{ \"tag\": \"style\", \"attrs\": {} }] }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M945 412H689c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h256c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM811 548H689c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h122c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM477.3 322.5H434c-6.2 0-11.2 5-11.2 11.2v248c0 3.6 1.7 6.9 4.6 9l148.9 108.6c5 3.6 12 2.6 15.6-2.4l25.7-35.1v-.1c3.6-5 2.5-12-2.5-15.6l-126.7-91.6V333.7c.1-6.2-5-11.2-11.1-11.2z\" } }, { \"tag\": \"path\", \"attrs\": { \"d\": \"M804.8 673.9H747c-5.6 0-10.9 2.9-13.9 7.7a321 321 0 01-44.5 55.7 317.17 317.17 0 01-101.3 68.3c-39.3 16.6-81 25-124 25-43.1 0-84.8-8.4-124-25-37.9-16-72-39-101.3-68.3s-52.3-63.4-68.3-101.3c-16.6-39.2-25-80.9-25-124 0-43.1 8.4-84.7 25-124 16-37.9 39-72 68.3-101.3 29.3-29.3 63.4-52.3 101.3-68.3 39.2-16.6 81-25 124-25 43.1 0 84.8 8.4 124 25 37.9 16 72 39 101.3 68.3a321 321 0 0144.5 55.7c3 4.8 8.3 7.7 13.9 7.7h57.8c6.9 0 11.3-7.2 8.2-13.3-65.2-129.7-197.4-214-345-215.7-216.1-2.7-395.6 174.2-396 390.1C71.6 727.5 246.9 903 463.2 903c149.5 0 283.9-84.6 349.8-215.8a9.18 9.18 0 00-8.2-13.3z\" } }] }, \"name\": \"field-time\", \"theme\": \"outlined\" };\nexport default FieldTimeOutlined;\n", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport FieldTimeOutlinedSvg from \"@ant-design/icons-svg/es/asn/FieldTimeOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar FieldTimeOutlined = function FieldTimeOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: FieldTimeOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(FieldTimeOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FieldTimeOutlined';\n}\nexport default RefIcon;", "// This icon file is generated automatically.\nvar MessageOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z\" } }] }, \"name\": \"message\", \"theme\": \"outlined\" };\nexport default MessageOutlined;\n", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport MessageOutlinedSvg from \"@ant-design/icons-svg/es/asn/MessageOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar MessageOutlined = function MessageOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: MessageOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(MessageOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'MessageOutlined';\n}\nexport default RefIcon;", "// This icon file is generated automatically.\nvar StarOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z\" } }] }, \"name\": \"star\", \"theme\": \"outlined\" };\nexport default StarOutlined;\n", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport StarOutlinedSvg from \"@ant-design/icons-svg/es/asn/StarOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar StarOutlined = function StarOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: StarOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(StarOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'StarOutlined';\n}\nexport default RefIcon;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport PlusOutlinedSvg from \"@ant-design/icons-svg/es/asn/PlusOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar PlusOutlined = function PlusOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: PlusOutlinedSvg\n  }));\n};\n\n/**![plus](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4MiAxNTJoNjBxOCAwIDggOHY3MDRxMCA4LTggOGgtNjBxLTggMC04LThWMTYwcTAtOCA4LTh6IiAvPjxwYXRoIGQ9Ik0xOTIgNDc0aDY3MnE4IDAgOCA4djYwcTAgOC04IDhIMTYwcS04IDAtOC04di02MHEwLTggOC04eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(PlusOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'PlusOutlined';\n}\nexport default RefIcon;", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar _excluded = [\"onTableChange\", \"maxLength\", \"formItemProps\", \"recordCreatorProps\", \"rowKey\", \"controlled\", \"defaultValue\", \"onChange\", \"editableFormRef\"],\n  _excluded2 = [\"record\", \"position\", \"creatorButtonText\", \"newRecordType\", \"parentKey\", \"style\"];\nimport { PlusOutlined } from '@ant-design/icons';\nimport ProForm, { ProFormDependency } from '@ant-design/pro-form';\nimport { useIntl } from '@ant-design/pro-provider';\nimport { isDeepEqualReact, runFunction, stringify, useRefFunction } from '@ant-design/pro-utils';\nimport { Button, Form } from 'antd';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport get from \"rc-util/es/utils/get\";\nimport set from \"rc-util/es/utils/set\";\nimport React, { useContext, useEffect, useImperativeHandle, useMemo, useRef } from 'react';\nimport ProTable from \"../../Table\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nvar EditableTableActionContext = /*#__PURE__*/React.createContext(undefined);\n\n/** 可编辑表格的按钮 */\nfunction RecordCreator(props) {\n  var children = props.children,\n    record = props.record,\n    position = props.position,\n    newRecordType = props.newRecordType,\n    parentKey = props.parentKey;\n  var actionRef = useContext(EditableTableActionContext);\n  return /*#__PURE__*/React.cloneElement(children, _objectSpread(_objectSpread({}, children.props), {}, {\n    onClick: function () {\n      var _onClick = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(e) {\n        var _children$props$onCli, _children$props, _actionRef$current;\n        var isOk;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return (_children$props$onCli = (_children$props = children.props).onClick) === null || _children$props$onCli === void 0 ? void 0 : _children$props$onCli.call(_children$props, e);\n            case 2:\n              isOk = _context.sent;\n              if (!(isOk === false)) {\n                _context.next = 5;\n                break;\n              }\n              return _context.abrupt(\"return\");\n            case 5:\n              actionRef === null || actionRef === void 0 || (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.addEditRecord(record, {\n                position: position,\n                newRecordType: newRecordType,\n                parentKey: parentKey\n              });\n            case 6:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      function onClick(_x) {\n        return _onClick.apply(this, arguments);\n      }\n      return onClick;\n    }()\n  }));\n}\n\n/**\n * 可以直接放到 Form 中的可编辑表格\n * A React component that is used to create a table.\n * @param props\n */\nfunction EditableTable(props) {\n  var _props$editable2, _props$editable4;\n  var intl = useIntl();\n  var onTableChange = props.onTableChange,\n    maxLength = props.maxLength,\n    formItemProps = props.formItemProps,\n    recordCreatorProps = props.recordCreatorProps,\n    rowKey = props.rowKey,\n    controlled = props.controlled,\n    defaultValue = props.defaultValue,\n    onChange = props.onChange,\n    editableFormRef = props.editableFormRef,\n    rest = _objectWithoutProperties(props, _excluded);\n  var preData = useRef(undefined);\n  var actionRef = useRef();\n  var formRef = useRef();\n\n  // 设置 ref\n  useImperativeHandle(rest.actionRef, function () {\n    return actionRef.current;\n  }, [actionRef.current]);\n  var _useMergedState = useMergedState(function () {\n      return props.value || defaultValue || [];\n    }, {\n      value: props.value,\n      onChange: props.onChange\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    value = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var getRowKey = React.useMemo(function () {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n    return function (record, index) {\n      return record[rowKey] || index;\n    };\n  }, [rowKey]);\n\n  /**\n   * 根据不同的情况返回不同的 rowKey\n   * @param finlayRowKey\n   * @returns string | number\n   */\n  var coverRowKey = useRefFunction(function (finlayRowKey) {\n    /**\n     * 如果是 prop.name 的模式，就需要把行号转化成具体的rowKey。\n     */\n    if (typeof finlayRowKey === 'number' && !props.name) {\n      if (finlayRowKey >= value.length) return finlayRowKey;\n      var rowData = value && value[finlayRowKey];\n      return getRowKey === null || getRowKey === void 0 ? void 0 : getRowKey(rowData, finlayRowKey);\n    }\n\n    /**\n     * 如果是 prop.name 的模式，就直接返回行号\n     */\n    if ((typeof finlayRowKey === 'string' || finlayRowKey >= value.length) && props.name) {\n      var _rowIndex = value.findIndex(function (item, index) {\n        var _getRowKey;\n        return (getRowKey === null || getRowKey === void 0 || (_getRowKey = getRowKey(item, index)) === null || _getRowKey === void 0 ? void 0 : _getRowKey.toString()) === (finlayRowKey === null || finlayRowKey === void 0 ? void 0 : finlayRowKey.toString());\n      });\n      if (_rowIndex !== -1) return _rowIndex;\n    }\n    return finlayRowKey;\n  });\n\n  // 设置 editableFormRef\n  useImperativeHandle(editableFormRef, function () {\n    /**\n     * 获取一行数据的\n     * @param rowIndex\n     * @returns T | undefined\n     */\n    var getRowData = function getRowData(rowIndex) {\n      var _finlayRowKey$toStrin, _formRef$current;\n      if (rowIndex == undefined) {\n        throw new Error('rowIndex is required');\n      }\n      var finlayRowKey = coverRowKey(rowIndex);\n      var rowKeyName = [props.name, (_finlayRowKey$toStrin = finlayRowKey === null || finlayRowKey === void 0 ? void 0 : finlayRowKey.toString()) !== null && _finlayRowKey$toStrin !== void 0 ? _finlayRowKey$toStrin : ''].flat(1).filter(Boolean);\n      return (_formRef$current = formRef.current) === null || _formRef$current === void 0 ? void 0 : _formRef$current.getFieldValue(rowKeyName);\n    };\n\n    /**\n     * 获取整个 table 的数据\n     * @returns T[] | undefined\n     */\n    var getRowsData = function getRowsData() {\n      var _formRef$current3;\n      var rowKeyName = [props.name].flat(1).filter(Boolean);\n      if (Array.isArray(rowKeyName) && rowKeyName.length === 0) {\n        var _formRef$current2;\n        var rowData = (_formRef$current2 = formRef.current) === null || _formRef$current2 === void 0 ? void 0 : _formRef$current2.getFieldsValue();\n        if (Array.isArray(rowData)) return rowData;\n        return Object.keys(rowData).map(function (key) {\n          return rowData[key];\n        });\n      }\n      return (_formRef$current3 = formRef.current) === null || _formRef$current3 === void 0 ? void 0 : _formRef$current3.getFieldValue(rowKeyName);\n    };\n    return _objectSpread(_objectSpread({}, formRef.current), {}, {\n      getRowData: getRowData,\n      getRowsData: getRowsData,\n      /**\n       * 设置一行的数据，会将数据进行简单的 merge\n       * @param rowIndex\n       * @param data\n       * @returns void\n       */\n      setRowData: function setRowData(rowIndex, data) {\n        var _finlayRowKey$toStrin2, _formRef$current4;\n        if (rowIndex == undefined) {\n          throw new Error('rowIndex is required');\n        }\n        var finlayRowKey = coverRowKey(rowIndex);\n        var rowKeyName = [props.name, (_finlayRowKey$toStrin2 = finlayRowKey === null || finlayRowKey === void 0 ? void 0 : finlayRowKey.toString()) !== null && _finlayRowKey$toStrin2 !== void 0 ? _finlayRowKey$toStrin2 : ''].flat(1).filter(Boolean);\n        var newRowData = Object.assign({}, _objectSpread(_objectSpread({}, getRowData(rowIndex)), data || {}));\n        var updateValues = set({}, rowKeyName, newRowData);\n        (_formRef$current4 = formRef.current) === null || _formRef$current4 === void 0 || _formRef$current4.setFieldsValue(updateValues);\n        return true;\n      }\n    });\n  }, [coverRowKey, props.name, formRef.current]);\n  useEffect(function () {\n    if (!props.controlled) return;\n    (value || []).forEach(function (current, index) {\n      var _formRef$current5;\n      (_formRef$current5 = formRef.current) === null || _formRef$current5 === void 0 || _formRef$current5.setFieldsValue(_defineProperty({}, \"\".concat(getRowKey(current, index)), current));\n    }, {});\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [stringify(value), props.controlled]);\n  useEffect(function () {\n    if (props.name) {\n      var _props$editable;\n      formRef.current = props === null || props === void 0 || (_props$editable = props.editable) === null || _props$editable === void 0 ? void 0 : _props$editable.form;\n    }\n  }, [(_props$editable2 = props.editable) === null || _props$editable2 === void 0 ? void 0 : _props$editable2.form, props.name]);\n  var _ref = recordCreatorProps || {},\n    record = _ref.record,\n    position = _ref.position,\n    creatorButtonText = _ref.creatorButtonText,\n    newRecordType = _ref.newRecordType,\n    parentKey = _ref.parentKey,\n    style = _ref.style,\n    restButtonProps = _objectWithoutProperties(_ref, _excluded2);\n  var isTop = position === 'top';\n  var creatorButtonDom = useMemo(function () {\n    if (typeof maxLength === 'number' && maxLength <= (value === null || value === void 0 ? void 0 : value.length)) {\n      return false;\n    }\n    return recordCreatorProps !== false && /*#__PURE__*/_jsx(RecordCreator, {\n      record: runFunction(record, value === null || value === void 0 ? void 0 : value.length, value) || {},\n      position: position,\n      parentKey: runFunction(parentKey, value === null || value === void 0 ? void 0 : value.length, value),\n      newRecordType: newRecordType,\n      children: /*#__PURE__*/_jsx(Button, _objectSpread(_objectSpread({\n        type: \"dashed\",\n        style: _objectSpread({\n          display: 'block',\n          margin: '10px 0',\n          width: '100%'\n        }, style),\n        icon: /*#__PURE__*/_jsx(PlusOutlined, {})\n      }, restButtonProps), {}, {\n        children: creatorButtonText || intl.getMessage('editableTable.action.add', '添加一行数据')\n      }))\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [recordCreatorProps, maxLength, value === null || value === void 0 ? void 0 : value.length]);\n  var buttonRenderProps = useMemo(function () {\n    if (!creatorButtonDom) {\n      return {};\n    }\n    if (isTop) {\n      return {\n        components: {\n          header: {\n            wrapper: function wrapper(_ref2) {\n              var _rest$columns;\n              var className = _ref2.className,\n                children = _ref2.children;\n              return /*#__PURE__*/_jsxs(\"thead\", {\n                className: className,\n                children: [children, /*#__PURE__*/_jsxs(\"tr\", {\n                  style: {\n                    position: 'relative'\n                  },\n                  children: [/*#__PURE__*/_jsx(\"td\", {\n                    colSpan: 0,\n                    style: {\n                      visibility: 'hidden'\n                    },\n                    children: creatorButtonDom\n                  }), /*#__PURE__*/_jsx(\"td\", {\n                    style: {\n                      position: 'absolute',\n                      left: 0,\n                      width: '100%'\n                    },\n                    colSpan: (_rest$columns = rest.columns) === null || _rest$columns === void 0 ? void 0 : _rest$columns.length,\n                    children: creatorButtonDom\n                  })]\n                })]\n              });\n            }\n          }\n        }\n      };\n    }\n    return {\n      tableViewRender: function tableViewRender(_, dom) {\n        var _props$tableViewRende, _props$tableViewRende2;\n        return /*#__PURE__*/_jsxs(_Fragment, {\n          children: [(_props$tableViewRende = (_props$tableViewRende2 = props.tableViewRender) === null || _props$tableViewRende2 === void 0 ? void 0 : _props$tableViewRende2.call(props, _, dom)) !== null && _props$tableViewRende !== void 0 ? _props$tableViewRende : dom, creatorButtonDom]\n        });\n      }\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isTop, creatorButtonDom]);\n  var editableProps = _objectSpread({}, props.editable);\n\n  /**\n   * 防止闭包的onchange\n   *\n   * >>>>>>为了性能好辛苦\n   */\n  var newOnValueChange = useRefFunction(function (r, dataSource) {\n    var _props$editable3, _props$editable3$onVa, _props$onValuesChange;\n    (_props$editable3 = props.editable) === null || _props$editable3 === void 0 || (_props$editable3$onVa = _props$editable3.onValuesChange) === null || _props$editable3$onVa === void 0 || _props$editable3$onVa.call(_props$editable3, r, dataSource);\n    (_props$onValuesChange = props.onValuesChange) === null || _props$onValuesChange === void 0 || _props$onValuesChange.call(props, dataSource, r);\n    if (props.controlled) {\n      var _props$onChange;\n      props === null || props === void 0 || (_props$onChange = props.onChange) === null || _props$onChange === void 0 || _props$onChange.call(props, dataSource);\n    }\n  });\n  if (props !== null && props !== void 0 && props.onValuesChange || (_props$editable4 = props.editable) !== null && _props$editable4 !== void 0 && _props$editable4.onValuesChange ||\n  // 受控模式需要触发 onchange\n  props.controlled && props !== null && props !== void 0 && props.onChange) {\n    editableProps.onValuesChange = newOnValueChange;\n  }\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [/*#__PURE__*/_jsx(EditableTableActionContext.Provider, {\n      value: actionRef,\n      children: /*#__PURE__*/_jsx(ProTable, _objectSpread(_objectSpread(_objectSpread({\n        search: false,\n        options: false,\n        pagination: false,\n        rowKey: rowKey,\n        revalidateOnFocus: false\n      }, rest), buttonRenderProps), {}, {\n        tableLayout: \"fixed\",\n        actionRef: actionRef,\n        onChange: onTableChange,\n        editable: _objectSpread(_objectSpread({}, editableProps), {}, {\n          formProps: _objectSpread({\n            formRef: formRef\n          }, editableProps.formProps)\n        }),\n        dataSource: value,\n        onDataSourceChange: function onDataSourceChange(dataSource) {\n          setValue(dataSource);\n          /**\n           * 如果是top，需要重新设置一下 form，不然会导致 id 相同数据混淆\n           */\n          if (props.name && position === 'top') {\n            var _formRef$current6;\n            var newValue = set({}, [props.name].flat(1).filter(Boolean), dataSource);\n            (_formRef$current6 = formRef.current) === null || _formRef$current6 === void 0 || _formRef$current6.setFieldsValue(newValue);\n          }\n        }\n      }))\n    }), props.name ? /*#__PURE__*/_jsx(ProFormDependency, {\n      name: [props.name],\n      children: function children(changeValue) {\n        var _props$editable5, _props$editable5$onVa;\n        if (!preData.current) {\n          preData.current = value;\n          return null;\n        }\n        var list = get(changeValue, [props.name].flat(1));\n        var changeItem = list === null || list === void 0 ? void 0 : list.find(function (item, index) {\n          var _preData$current;\n          return !isDeepEqualReact(item, (_preData$current = preData.current) === null || _preData$current === void 0 ? void 0 : _preData$current[index]);\n        });\n        preData.current = value;\n        if (!changeItem) return null;\n        // 如果不存在 preData 说明是初始化，此时不需要触发 onValuesChange\n        props === null || props === void 0 || (_props$editable5 = props.editable) === null || _props$editable5 === void 0 || (_props$editable5$onVa = _props$editable5.onValuesChange) === null || _props$editable5$onVa === void 0 || _props$editable5$onVa.call(_props$editable5, changeItem, list);\n        return null;\n      }\n    }) : null]\n  });\n}\n\n/**\n * 可以直接放到 Form 中的可编辑表格\n * A React component that is used to create a table.\n * @param props\n */\nfunction FieldEditableTable(props) {\n  var form = ProForm.useFormInstance();\n  if (!props.name) return /*#__PURE__*/_jsx(EditableTable, _objectSpread({\n    tableLayout: \"fixed\",\n    scroll: {\n      x: 'max-content'\n    }\n  }, props));\n  return /*#__PURE__*/_jsx(Form.Item, _objectSpread(_objectSpread({\n    style: {\n      maxWidth: '100%'\n    }\n  }, props === null || props === void 0 ? void 0 : props.formItemProps), {}, {\n    name: props.name,\n    shouldUpdate: function shouldUpdate(prev, next) {\n      var name = [props.name].flat(1);\n      try {\n        return JSON.stringify(get(prev, name)) !== JSON.stringify(get(next, name));\n      } catch (error) {\n        return true;\n      }\n    },\n    children: /*#__PURE__*/_jsx(EditableTable, _objectSpread(_objectSpread({\n      tableLayout: \"fixed\",\n      scroll: {\n        x: 'max-content'\n      }\n    }, props), {}, {\n      editable: _objectSpread(_objectSpread({}, props.editable), {}, {\n        form: form\n      })\n    }))\n  }));\n}\nFieldEditableTable.RecordCreator = RecordCreator;\nexport default FieldEditableTable;", "import React from 'react'\n\nexport interface ArrowProps {\n  color?: string\n}\n\nconst Arrow: React.FC<ArrowProps> = ({ color = '#222' }) => {\n  return (\n    <svg viewBox=\"0 -3 128 6\" width=\"60\" height=\"12\" transform=\"rotate(180)\">\n      <defs>\n        <marker\n          id=\"reaction-arrowhead\"\n          viewBox=\"0 0 8 6\"\n          markerUnits=\"userSpaceOnUse\"\n          markerWidth=\"18\"\n          markerHeight=\"12\"\n          refX=\"2\"\n          refY=\"2\"\n          orient=\"auto\"\n          fill={color}\n        >\n          <path d=\"m 0 0 l 7 2.25 l -7 2.25 c 0 0 0.735 -1.084 0.735 -2.28 c 0 -1.196 -0.735 -2.22 -0.735 -2.22 z\"></path>\n        </marker>\n      </defs>\n      <line\n        x1=\"0\"\n        y1=\"0\"\n        x2=\"120\"\n        y2=\"0\"\n        strokeWidth=\"1\"\n        stroke={color}\n        markerEnd=\"url(#reaction-arrowhead)\"\n      ></line>\n    </svg>\n  )\n}\n\nexport default Arrow\n", "import { Button, ButtonProps } from 'antd'\nimport React, { useState } from 'react'\n\nconst ButtonWithLoading: React.FC<ButtonProps> = ({ onClick, ...props }) => {\n  const [loading, setLoading] = useState<boolean>(false)\n  const onClickHandler: ButtonProps['onClick'] = async (event) => {\n    setLoading(true)\n    try {\n      const result = await onClick?.(\n        event as React.MouseEvent<HTMLButtonElement, MouseEvent>\n      )\n      setLoading(false)\n      return result\n    } catch {\n      setLoading(false)\n      return ''\n    }\n  }\n\n  return <Button loading={loading} {...props} onClick={onClickHandler} />\n}\n\nexport default ButtonWithLoading\n", "import { getWord } from '@/utils'\n\nimport { Tag } from 'antd'\nimport { ReactElement } from 'react'\n\nexport interface StatusRenderProps<T extends string> {\n  status: T\n  label?: string\n  labelPrefix?: string\n  colorMap?: Record<string, string>\n  className?: string\n}\n\nconst commonStatusMap: Record<string, string> = {\n  created: '#F5B544',\n  editing: '#F5B544',\n  started: '#4B9F47',\n  holding: '#E6521F',\n  confirmed: '#4B9F47',\n  finished: '#1890FF',\n  cancelled: '#979797',\n  canceled: '#979797',\n\n  running: '#2AD259',\n  hold: '#E6521F',\n  completed: '#1890FF',\n  success: '#F51D2C',\n  failed: '#9747FF',\n\n  todo: '#F5B544',\n  checking: '#4B9F47'\n}\n\nconst StatusRender: <T extends string>(\n  props: StatusRenderProps<T>\n) => ReactElement<StatusRenderProps<T>> = ({\n  status,\n  colorMap,\n  labelPrefix,\n  label: propLabel,\n  className\n}) => {\n  const color = { ...commonStatusMap, ...colorMap }[status]\n  const label = propLabel || getWord(`${labelPrefix}.${status}`)\n  return (\n    <Tag className={className} color={color}>\n      {label}\n    </Tag>\n  )\n}\n\nexport default StatusRender\n", "import { ReactionRole } from '@/services/brain'\nimport { IOption } from '@/types/common'\nimport { omit } from 'lodash'\nimport { getWord } from '../utils/lang'\n\nconst useOptions = () => {\n  const moleculeStatusOptions = [\n    { value: 'created', label: getWord('molecules-status.created') },\n    { value: 'designing', label: getWord('molecules-status.designing') },\n    { value: 'synthesizing', label: getWord('molecules-status.synthesizing') },\n    {\n      value: 'finished',\n      label: getWord('component.notification.statusValue.success')\n    },\n    {\n      value: 'canceled',\n      label: getWord('pages.projectTable.statusLabel.cancelled')\n    }\n  ]\n\n  const reactionRoleOptions: {\n    value: ReactionRole | string\n    label: string\n  }[] = [\n    { value: 'product', label: getWord('product') },\n    { value: 'main_reactant', label: getWord('main-reactant') },\n    { value: 'reactant', label: getWord('reactant') },\n    { value: 'other_reagent', label: getWord('other-reagent') }\n  ]\n\n  const groupOptions: IOption[] = [\n    { label: getWord('same-key-material'), value: 'start_material' },\n    { label: getWord('algorithm-cluster'), value: 'cluster' },\n    { label: getWord('not-grouped'), value: 'ungrouped' }\n  ]\n\n  const proportionOptions: IOption[] = [\n    { label: getWord('algorithmic-score'), value: 'score' },\n    {\n      label: getWord('known-reaction-proportion'),\n      value: 'known_reaction_rate'\n    },\n    { label: getWord('longest-chain-l'), value: 'backbone_length' },\n    { label: getWord('route-length'), value: 'min_n_main_tree_steps' }\n  ]\n\n  const sortStandard = {\n    createdAt: getWord('creation-time'),\n    updatedAt: getWord('last-update-time'),\n    no: getWord('name')\n  }\n\n  const typeMap = {\n    target: getWord('target-molecules'),\n    building_block: getWord('key-intermediate'),\n    temp_block: getWord('show-materials')\n  }\n\n  const typeMapForSelect = omit(typeMap, 'temp_block')\n\n  const editableConfig = {\n    onlyOneLineEditorAlertMessage: getWord('only-one-edit'),\n    onlyAddOneLineAlertMessage: getWord('only-one-added')\n  }\n\n  const chargeDes = {\n    total_cost: getWord('total'),\n    material_cost: getWord('material-cost'),\n    labor_cost: getWord('labor-cost')\n  }\n\n  const materialManageStauts = {\n    draft: getWord('draft'),\n    published: getWord('in-use'),\n    deleted: getWord('deleted')\n  }\n\n  const aiAIInferenceStauts = {\n    success: getWord('pages.experiment.statusLabel.success'),\n    fail: getWord('pages.experiment.statusLabel.failed'),\n    processing: getWord('pages.experiment.statusLabel.running')\n  }\n\n  const aiGenerateStauts = {\n    limited: getWord('component.notification.statusValue.limited'),\n    completed: getWord('component.notification.statusValue.success'),\n    running: getWord('component.notification.statusValue.running'),\n    pending: getWord('component.notification.statusValue.pending'),\n    failed: getWord('component.notification.statusValue.failed')\n  }\n\n  const robotStatus = {\n    working: getWord('working'),\n    holding: getWord('hold'),\n    error: getWord('unavailable'),\n    idle: getWord('idle')\n  }\n\n  return {\n    moleculeStatusOptions,\n    reactionRoleOptions,\n    groupOptions,\n    proportionOptions,\n    typeMap,\n    sortStandard,\n    typeMapForSelect,\n    editableConfig,\n    chargeDes,\n    materialManageStauts,\n    aiGenerateStauts,\n    aiAIInferenceStauts,\n    robotStatus\n  }\n}\nexport default useOptions\n", "import { DownOutlined } from '@ant-design/icons'\nimport { Dropdown, Space } from 'antd'\n\nexport interface EnumSwitcherProps<T extends string> {\n  currentValue: T\n  avalibleValues: T[]\n  onSelect: (s: T) => void\n  valueRender?: (value: T) => JSX.Element\n}\n\nconst EnumSwitcher = <T extends string>({\n  currentValue,\n  avalibleValues,\n  onSelect,\n  valueRender = (s: T) => <>{s}</>\n}: EnumSwitcherProps<T>): JSX.Element => {\n  if (!avalibleValues.length) {\n    return valueRender(currentValue)\n  }\n\n  const menuItems = avalibleValues.map((v) => ({\n    label: valueRender(v),\n    key: v\n  }))\n  return (\n    <Dropdown\n      menu={{ items: menuItems, onClick: (info) => onSelect(info.key as T) }}\n      trigger={['click']}\n    >\n      <Space>\n        {valueRender(currentValue)}\n        <DownOutlined />\n      </Space>\n    </Dropdown>\n  )\n}\n\nexport default EnumSwitcher\n", "import { AmendOtherQuote, OtherQuote } from '@/models/quotation'\nimport { getWord } from '@/utils'\nimport {\n  ModalForm,\n  ProFormDigit,\n  ProFormText\n} from '@ant-design/pro-components'\nimport { Button, Col, Row, Select } from 'antd'\nimport { useEffect, useState } from 'react'\nimport {  useAccess } from 'umi'\nimport type { OtherQuoteModalProps } from './index.d'\nexport default function OtherQuoteModal(props: OtherQuoteModalProps) {\n  const { form, onFinish } = props\n\n  useEffect(() => {\n    form.setFieldsValue(props?.initalValues)\n  }, [props?.initalValues])\n  const [weighUnit, setWeighUnit] = useState<'mg' | 'g'>('g')\n  const access = useAccess()\n  return (\n    <ModalForm<OtherQuote>\n      layout=\"horizontal\"\n      width={680}\n      title={getWord('other-weight-quotate')}\n      trigger={\n        access?.authCodeList?.includes(\n          'quotation-records.button.otherWeightQuotate'\n        ) ? (\n          <Button>\n            {getWord('other-weight-quotate')}\n          </Button>\n        ) : (\n          <></>\n        )\n      }\n      form={form}\n      autoFocusFirstInput\n      modalProps={{\n        destroyOnClose: true,\n        onCancel: () => console.log('run')\n      }}\n      submitTimeout={2000}\n      onFinish={async (values) => {\n        onFinish({\n          target_weight: values?.target_weight,\n          target_unit: weighUnit\n        } as AmendOtherQuote)\n        return true\n      }}\n    >\n      <Row>\n        <Col span={12}>\n          <ProFormText\n            name=\"compound_no\"\n            label={getWord('molecules-no')}\n            rules={[{ required: true }]}\n            labelCol={{ span: 8 }}\n            wrapperCol={{ span: 14 }}\n            disabled\n          />\n        </Col>\n        <Col span={12}>\n          <ProFormDigit\n            name=\"target_weight\"\n            label={getWord('weight')}\n            labelCol={{ span: 6 }}\n            wrapperCol={{ span: 18 }}\n            addonAfter={\n              <Select\n                value={weighUnit}\n                style={{ width: '60px' }}\n                onChange={(e) => setWeighUnit(e)}\n              >\n                <Select.Option value=\"g\">g</Select.Option>\n                <Select.Option value=\"mg\">mg</Select.Option>\n              </Select>\n            }\n            rules={[{ required: true }]}\n            min={0.1}\n          />\n        </Col>\n      </Row>\n      <Row>\n        <Col span={12}>\n          <ProFormDigit\n            labelCol={{ span: 8 }}\n            wrapperCol={{ span: 14 }}\n            name=\"purity\"\n            rules={[{ required: true }]}\n            label={`${getWord('purity')}(%)`}\n            min={0.1}\n            max={100}\n            disabled\n          />\n        </Col>\n        <Col span={12}>\n          <ProFormDigit\n            name=\"ratio\"\n            label={getWord('coefficient')}\n            labelCol={{ span: 6 }}\n            wrapperCol={{ span: 13 }}\n            rules={[{ required: true }]}\n            min={0.1}\n            max={100}\n            disabled\n          />\n        </Col>\n      </Row>\n    </ModalForm>\n  )\n}\n", "// extracted by mini-css-extract-plugin\nexport default {\"unfoldWidth\":\"unfoldWidth___OVBf0\",\"foldWidth\":\"foldWidth___j89Q3\",\"unfoldWidth_EN\":\"unfoldWidth_EN___ic7X2\",\"foldWidth_EN\":\"foldWidth_EN___RCpY4\",\"quoteInfo\":\"quoteInfo___ocFoY\",\"unitSelect\":\"unitSelect___W8hh1\",\"anchor\":\"anchor___VpYXB\",\"anchorEN\":\"anchorEN___yzHrz\",\"quoteDetail\":\"quoteDetail___CPuC2\",\"content\":\"content___Nss6m\",\"title\":\"title___zYn2a\",\"stepTitle\":\"stepTitle___OKg7c\",\"openLinkWrapper\":\"openLinkWrapper___hxFyb\",\"procedureTitle\":\"procedureTitle___OPIr7\",\"procedureReference\":\"procedureReference___oQZE9\",\"quotationSummaryDetail\":\"quotationSummaryDetail___v_H_s\",\"routeInfo\":\"routeInfo___njAIE\",\"structure\":\"structure___UvTj7\",\"referenceContent\":\"referenceContent___OC0WQ\",\"referenceStructure\":\"referenceStructure___fNzn0\",\"laborEditor\":\"laborEditor___OnT9W\",\"operateButton\":\"operateButton___PQYhX\",\"confirmModal\":\"confirmModal___M5OOP\"};", "import CustomTable from '@/components/CustomTable'\nimport ModalBase from '@/components/ModalBase'\nimport { useModalBase } from '@/components/ModalBase/useModalBase'\nimport { getWord, isEN } from '@/utils'\nimport type { DiffCost } from './index.d'\nimport styles from './index.less'\ninterface ConfirmModalProps {\n  diffCostData: DiffCost[]\n  // dialogProps: ModalBaseProps\n  // confirm: () => Promise<string>\n  confirmDiff: () => void\n  openEvent?: { open?: boolean }\n}\nexport default function ConfirmModal(props: ConfirmModalProps) {\n  const { openEvent } = props\n  const { dialogProps, confirm } = useModalBase()\n  const { diffCostData, confirmDiff } = props\n  const columns = [\n    {\n      title: getWord('steps'),\n      dataIndex: 'step_no'\n    },\n    {\n      title: isEN()\n        ? `${getWord('english-name')}（${getWord('yuan')}）`\n        : `${getWord('chinese-name')}（${getWord('yuan')}）`,\n      dataIndex: isEN() ? 'name_en' : 'name_zh',\n      ellipsis: true\n    },\n    {\n      title: 'CAS',\n      dataIndex: 'cas_no'\n    },\n    {\n      title: getWord('min-cost'),\n      dataIndex: 'minCost'\n    },\n    {\n      title: getWord('cost'),\n      dataIndex: 'cost'\n    }\n  ]\n  const tableConfig = {\n    dataSource: diffCostData,\n    bordered: true,\n    pagination: null\n  }\n\n  const repeatedConfirm = async () => {\n    confirmDiff()\n    confirm()\n  }\n\n  return (\n    <ModalBase\n      {...dialogProps}\n      title={getWord('confirm-quote')}\n      className={styles.confirmModal}\n      width={800}\n      openEvent={openEvent}\n      onConfirm={repeatedConfirm}\n    >\n      <div className={styles.title}>\n        {getWord('cost-conflict-quote-double-check')}\n      </div>\n      <CustomTable {...tableConfig} columns={columns} rowKey=\"cas_no\" />\n    </ModalBase>\n  )\n}\n", "// extracted by mini-css-extract-plugin\nexport default {\"editContent\":\"editContent___Qsb6z\",\"smi\":\"smi___SGQAC\"};", "import useOptions from '@/hooks/useOptions'\nimport { SupplierDetail } from '@/services/brain'\nimport { getWord, isEN, isValidArray, roundToTwoDecimalPlaces } from '@/utils'\nimport type { ProColumns } from '@ant-design/pro-components'\nimport { EditableProTable, ModalForm } from '@ant-design/pro-components'\nimport { Form } from 'antd'\nimport { useEffect, useState } from 'react'\n\nimport type { SupplierProps } from './index.d'\nimport styles from './index.less'\nexport default function Supplier(props: SupplierProps) {\n  const { supplierDatas, confirmSupplierDatas } = props\n  const { editableConfig } = useOptions()\n  const [form] = Form.useForm<SupplierDetail>()\n  const [dataSource, setDataSource] = useState<SupplierDetail[]>([])\n  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>()\n  const [isEditType, setIsEditType] = useState<boolean>(false)\n  useEffect(() => {\n    setDataSource(supplierDatas)\n  }, [supplierDatas])\n\n  const changeEditorType = (_isEditorType: boolean) => {\n    if (_isEditorType && isValidArray(dataSource)) {\n      setIsEditType(true)\n      setEditableRowKeys(() => dataSource.map((item) => item.id))\n    } else {\n      setIsEditType(false)\n      setEditableRowKeys([])\n    }\n  }\n  const renderQuantity = (record) =>\n    record?.quantity && record?.unit\n      ? `${record?.quantity}${record?.unit}`\n      : '-'\n\n  const columns: ProColumns<SupplierDetail>[] = [\n    {\n      title: 'CAS',\n      dataIndex: 'cas_no',\n      width: 80,\n      readonly: true,\n      valueType: 'text'\n    },\n    {\n      title: getWord('supplier'),\n      dataIndex: 'source',\n      width: 80,\n      readonly: true,\n      valueType: 'text',\n      renderText: (text: string, record) => {\n        return record?.source_link && !isEditType ? (\n          <a onClick={() => window.open(record?.source_link)}>\n            {text}\n            {record?.material_lib?.version ? (\n              <>\n                &nbsp;\n                <span className=\"red\">({record?.material_lib?.version})</span>\n              </>\n            ) : (\n              ''\n            )}\n          </a>\n        ) : (\n          text\n        )\n      }\n    },\n    {\n      title: getWord('amount'),\n      dataIndex: 'quantity',\n      width: 80,\n      readonly: true,\n      renderFormItem: (info: string) => renderQuantity(info?.entity),\n      render: (_: string, record) => renderQuantity(record)\n    },\n    {\n      title: getWord('purity'),\n      dataIndex: 'purity',\n      width: 80,\n      readonly: true\n    },\n    {\n      title: getWord('spot-or-futures'),\n      readonly: true,\n      dataIndex: 'in_stock',\n      width: 120,\n      initialValue: ['true', 'false'],\n      valueEnum: {\n        true: { text: getWord('spot') },\n        false: { text: getWord('futures') }\n      }\n    },\n    {\n      title: `${getWord('price')}/￥`,\n      readonly: true,\n      dataIndex: 'price',\n      width: 120,\n      render: (_, item) =>\n        item?.price && !isNaN(item?.price) ? item?.price.toFixed(2) : ''\n    },\n    {\n      title: `${getWord('unit-price')}￥/g`,\n      readonly: true,\n      width: 130,\n      dataIndex: 'unit_price',\n      render: (_, item) =>\n        item?.unit_price && !isNaN(item?.unit_price)\n          ? item?.unit_price.toFixed(2)\n          : ''\n    },\n    {\n      title: getWord('quantity'),\n      width: 90,\n      valueType: 'digit',\n      readonly: false,\n      dataIndex: 'count',\n      formItemProps: {\n        rules: [\n          {\n            message: getWord('cant-input-negative-number'),\n            pattern: /^[0-9]\\d*$/\n          }\n        ]\n      }\n    },\n    {\n      title: `${getWord('cost')}/￥`,\n      readonly: true,\n      width: 120,\n      dataIndex: 'decs',\n      renderText: (_: string, record) => {\n        let sum =\n          record?.count && record?.price ? record?.count * record?.price : '-'\n        return roundToTwoDecimalPlaces(sum)\n      }\n    }\n  ]\n  return (\n    <ModalForm<SupplierDetail>\n      title={getWord('supplier')}\n      width={1180}\n      trigger={<a key=\"supplier\">{getWord('supplier')}</a>}\n      form={form}\n      autoFocusFirstInput\n      onOpenChange={(open: boolean) => {\n        if (!open) setDataSource(supplierDatas)\n      }}\n      modalProps={{\n        destroyOnClose: true\n      }}\n      submitter={{\n        resetButtonProps: { style: { display: 'none' } },\n        submitButtonProps: {\n          style: { display: !isEditType ? undefined : 'none' }\n        },\n        searchConfig: {\n          submitText: getWord('pages.route.edit.label.save')\n        }\n      }}\n      onFinish={async () => {\n        await confirmSupplierDatas(dataSource)\n        return true\n      }}\n    >\n      <div className={styles.editContent}>\n        <span>{getWord('quotate-price-tip')}</span>\n        <a\n          onClick={() => changeEditorType(!isEditType)}\n          style={{ width: isEN() ? '120px' : 'auto' }}\n        >\n          {isEditType ? getWord('supplier.edit.finished') : getWord('edit')}\n        </a>\n      </div>\n      <EditableProTable<SupplierDetail>\n        columns={columns}\n        rowKey=\"id\"\n        value={dataSource}\n        onChange={setDataSource}\n        recordCreatorProps={false}\n        scroll={{ y: 300 }}\n        editable={{\n          ...editableConfig,\n          type: 'multiple',\n          editableKeys,\n          onValuesChange: (_record, recordList) => {\n            setDataSource(recordList)\n          },\n          onChange: setEditableRowKeys\n        }}\n      />\n    </ModalForm>\n  )\n}\n", "import { ReactComponent as HelpIcon } from '@/assets/svgs/help.svg'\nimport { materialUnitValueEnum } from '@/constants'\nimport useOptions from '@/hooks/useOptions'\nimport { isReadonlyMaterialRole } from '@/pages/route/util'\nimport type { Material, SupplierDetail } from '@/services/brain'\nimport {\n  fetchSuppliers,\n  getWord,\n  isEN,\n  isValidArray,\n  roundToTwoDecimalPlaces\n} from '@/utils'\nimport { EditableProTable, ProColumns } from '@ant-design/pro-components'\nimport { useAccess, useModel } from '@umijs/max'\nimport { Popover } from 'antd'\nimport { cloneDeep, isEmpty } from 'lodash'\nimport { useEffect, useState } from 'react'\nimport { v4 as uuidv4 } from 'uuid'\nimport { EditorDialog } from '../MoleculeEditor'\nimport SmilesInput from '../SmilesInput'\nimport Supplier from './Supplier'\nimport type { MaterialTableProps } from './index.d'\nimport styles from './index.less'\n\nexport default function QuoteMaterialTable(props: MaterialTableProps) {\n  const { editableConfig } = useOptions()\n  const { quoteMoleculeId, projectReactionId } = props\n  const isQuotation: boolean = location.pathname.includes('quotation-records')\n  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([])\n  const [dataSource, setDataSource] = useState<readonly Material[]>([])\n  const { updateQuotes } = useModel('quotation')\n  const access = useAccess()\n\n  useEffect(() => {\n    setDataSource(props?.materialData)\n  }, [props?.materialData])\n\n  const handleSaveQuoteData = async (values: any, successCb?: () => void) => {\n    const res = await updateQuotes(quoteMoleculeId as string, {\n      ...values,\n      project_reaction_id: projectReactionId\n    })\n    if (res && successCb) successCb()\n  }\n\n  const saveEditItem = async (\n    data: Material,\n    isAdd?: boolean,\n    newDataSource?: Material[]\n  ) => {\n    let params = {\n      project_reaction_id: projectReactionId,\n      role: data?.role,\n      cas_no: data?.cas_no,\n      detail: data?.detail,\n      smi: data?.smi,\n      name_zh: data?.name_zh,\n      name_en: data?.name_en,\n      equivalent: data?.equivalent,\n      unit: data?.unit,\n      required_quantity: data?.required_quantity,\n      set_cost: data?.set_cost,\n      material_no: data?.no,\n      gPerMol: data?.gPerMol,\n      action: isAdd ? 'add' : undefined\n    }\n    await handleSaveQuoteData(params, () => {\n      if (newDataSource) setDataSource(newDataSource)\n    })\n  }\n\n  const getSuppliers = async (\n    _newSmiles: string,\n    record: Material,\n    isAdd?: boolean\n  ) => {\n    let newDataSource = cloneDeep(props?.materialData)\n    const supplierInfo = await fetchSuppliers(_newSmiles)\n    const getTargetValue = (\n      key: 'gPerMol' | 'name_en' | 'name_zh' | 'cas_no'\n    ) => {\n      let targetItem = supplierInfo.find((e) => e[key] && e[key] !== null)\n      return targetItem ? targetItem[key] : undefined\n    }\n    if (supplierInfo) {\n      let _gPerMol = getTargetValue('gPerMol'),\n        name_en = getTargetValue('name_en'),\n        name_zh = getTargetValue('name_zh'),\n        cas_no = getTargetValue('cas_no')\n      let editedData = {\n        ...record,\n        smi: _newSmiles,\n        detail: supplierInfo,\n        gPerMol: _gPerMol,\n        role: record?.role || 'reactant',\n        name_en,\n        cas_no,\n        name_zh\n      }\n      newDataSource.push(editedData)\n      await saveEditItem(editedData as Material, isAdd, newDataSource)\n    }\n  }\n\n  const [columns, setColumns] = useState<ProColumns<Material>[]>([\n    {\n      title: getWord('structural'),\n      dataIndex: 'smi',\n      readonly: true,\n      width: 120,\n      valueType: 'text',\n      renderFormItem: (info: string, { isEditable }) => {\n        const record = info?.entity\n        const smiles = info?.entity?.smi\n        return info?.entity?.step_no === getWord('total') ||\n          isEditable ||\n          smiles === '-' ? (\n          smiles\n        ) : (\n          <div className={styles.smi}>\n            {\n              <SmilesInput\n                multiple={false}\n                onChange={(newSmiles: string) =>\n                  getSuppliers(newSmiles, record)\n                }\n              />\n            }\n          </div>\n        )\n      },\n      render: (smiles, record) => (\n        <div className={styles.smi}>\n          {record?.step_no === getWord('total') ? (\n            '-'\n          ) : (\n            <SmilesInput\n              value={smiles === '-' ? [] : smiles}\n              disabled={isReadonlyMaterialRole(record?.role as string)}\n              multiple={false}\n              onChange={(newSmiles: string) => getSuppliers(newSmiles, record)}\n            />\n          )}\n        </div>\n      )\n    },\n    {\n      title: getWord('role'),\n      dataIndex: 'role',\n      valueType: 'select',\n      width: 110,\n      valueEnum: (row) =>\n        row?.role === 'main_reactant'\n          ? {\n              main_reactant: getWord('main-reactant')\n            }\n          : {\n              reactant: getWord('reactant'),\n              other_reagent: getWord('other-reagent'),\n              solvent: getWord('solvent')\n            }\n    },\n    {\n      title: getWord('chinese-name'),\n      dataIndex: 'name_zh',\n      width: 100,\n      valueType: 'text',\n      hideInTable: isEN()\n    },\n    {\n      title: getWord('english-name'),\n      dataIndex: 'name_en',\n      width: 100,\n      valueType: 'text'\n    },\n    {\n      title: 'CAS',\n      dataIndex: 'cas_no',\n      width: 100,\n      valueType: 'text'\n    },\n    {\n      title: getWord('molecular-mass'),\n      dataIndex: 'gPerMol',\n      width: 100,\n      hideInTable: !isQuotation,\n      valueType: 'digit',\n      renderText: (_, item) => roundToTwoDecimalPlaces(item?.gPerMol as number)\n    },\n    {\n      title: getWord('EWR'),\n      dataIndex: 'equivalent',\n      valueType: 'digit',\n      width: 140,\n      formItemProps: {\n        rules: [\n          {\n            message: getWord('pattern-positive-number'),\n            pattern: /^(0*[1-9]\\d*\\.?\\d*|0+\\.\\d*[1-9]\\d*)$/\n          }\n        ]\n      },\n      renderText: (_, item) =>\n        roundToTwoDecimalPlaces(item?.equivalent as number)\n    },\n    {\n      title: getWord('measurement-method'),\n      dataIndex: 'unit',\n      width: 120,\n      valueEnum: materialUnitValueEnum\n    },\n    {\n      title: `${getWord('required-weight')}/g`,\n      dataIndex: 'required_quantity',\n      valueType: 'digit',\n      width: 100,\n      formItemProps: {\n        rules: [\n          {\n            message: getWord('pattern-positive-number'),\n            pattern: /^(0*[1-9]\\d*\\.?\\d*|0+\\.\\d*[1-9]\\d*)$/\n          }\n        ]\n      },\n      renderText: (_, item) =>\n        roundToTwoDecimalPlaces(item?.required_quantity as number)\n    },\n    {\n      title: () => (\n        <div className=\"flex-align-items-center\">\n          <div>{getWord('min-cost')}/￥</div>\n        </div>\n      ),\n      dataIndex: 'cost',\n      valueType: 'text',\n      width: 140,\n      readonly: true,\n      renderText: (_, item) => {\n        if (item?.step_no === getWord('total')) return ''\n        let digitDes = roundToTwoDecimalPlaces(item?.cost as number)\n        return !isNaN(digitDes) && !isEmpty(digitDes)\n          ? digitDes\n          : getWord('please-quote')\n      }\n    },\n    {\n      title: () => (\n        <div className=\"flex-align-items-center\">\n          <div>{getWord('cost')}/￥</div>\n          <Popover content={getWord('cost-empty-tip')}>\n            <HelpIcon width={18} style={{ cursor: 'pointer' }} />\n          </Popover>\n        </div>\n      ),\n      dataIndex: 'set_cost',\n      valueType: 'text',\n      width: 140,\n      formItemProps: {\n        rules: [\n          {\n            message: getWord('pattern-positive-number'),\n            pattern: /^(0*[1-9]\\d*\\.?\\d*|0+\\.\\d*[1-9]\\d*)$/\n          }\n        ]\n      },\n      renderText: (_, item) => {\n        if (item?.step_no === getWord('total')) return ''\n        let digitDes = roundToTwoDecimalPlaces(\n          item?.set_cost || (item?.cost as number)\n        )\n        return !isNaN(digitDes) && !isEmpty(digitDes) ? digitDes : '-'\n      }\n    }\n  ])\n\n  useEffect(() => {\n    if (!props?.hiddeStep) {\n      let _columns = cloneDeep(columns)\n      _columns.unshift({\n        title: getWord('steps'),\n        width: 60,\n        key: 'step_no',\n        dataIndex: 'step_no',\n        fixed: 'left',\n        readonly: true,\n        render: (_text, record: Material) =>\n          record?.step_no === getWord('total') ? (\n            record?.step_no\n          ) : (\n            <a href={`#${record?.step_no}`}>{record?.step_no}</a>\n          )\n      })\n      setColumns(_columns)\n    }\n  }, [props?.hiddeStep])\n\n  const operate = () => {\n    return [\n      {\n        title: getWord('pages.experiment.label.operation'),\n        valueType: 'option',\n        width: 150,\n        fixed: 'right',\n        render: (\n          _text: string,\n          record: Material,\n          _: any,\n          action: { startEditable: (no: string) => void }\n        ) => [\n          <>\n            {record?.step_no === getWord('total') ? (\n              ''\n            ) : (\n              <a\n                key=\"editable\"\n                onClick={() => {\n                  setEditableRowKeys([...editableKeys, record.no])\n                  action?.startEditable?.(record.no)\n                }}\n              >\n                {getWord('edit')}\n              </a>\n            )}\n            {access?.authCodeList?.includes(\n              'quotation-records.button.supplier'\n            ) && isValidArray(record?.detail) ? (\n              <Supplier\n                supplierDatas={record?.detail as SupplierDetail[]}\n                confirmSupplierDatas={async (values) =>\n                  await updateQuotes(quoteMoleculeId as string, {\n                    material_no: record?.no,\n                    detail: values\n                  })\n                }\n              />\n            ) : (\n              ''\n            )}\n          </>\n        ]\n      }\n    ]\n  }\n\n  const [open, setOpen] = useState<boolean>(false)\n\n  return (\n    <>\n      <EditableProTable<Material>\n        rowKey=\"no\"\n        maxLength={5}\n        scroll={{\n          x: 960\n        }}\n        loading={false}\n        recordCreatorProps={\n          props?.enableEdit && props?.enableAddMaterial\n            ? {\n                position: 'bottom',\n                newRecordType: 'dataSource',\n                creatorButtonText: getWord('add-raw-materials'),\n                onClick: () => setOpen(true),\n                record: () => ({\n                  no: `R-${uuidv4().slice(0, 6)}`,\n                  smi: '-'\n                })\n              }\n            : false\n        }\n        columns={\n          props?.enableEdit\n            ? ([...columns, ...operate()] as ProColumns<Material>[])\n            : columns\n        }\n        value={dataSource}\n        editable={{\n          ...editableConfig,\n          type: 'multiple',\n          editableKeys,\n          actionRender: (_row, _config, defaultDom) =>\n            _row?.role && _row?.role === 'main_reactant'\n              ? [defaultDom?.save, defaultDom?.cancel]\n              : [defaultDom?.save, defaultDom?.cancel, defaultDom?.delete],\n          onSave: async (_rowKey, data) => {\n            if (!data.role) return false\n            await saveEditItem(data)\n          },\n          onDelete: async (_rowKey, data) => {\n            await handleSaveQuoteData({\n              project_reaction_id: projectReactionId,\n              material_no: data?.no,\n              action: 'delete'\n            })\n          },\n          onChange: setEditableRowKeys\n        }}\n      />\n      <EditorDialog\n        open={open}\n        init=\"\"\n        onClose={async (s: string) => {\n          if (s) {\n            await getSuppliers(\n              s,\n              {\n                no: `R-${uuidv4().slice(0, 6)}`,\n                smi: s\n              },\n              true\n            )\n          }\n          setOpen(false)\n        }}\n      />\n    </>\n  )\n}\n", "import QuoteMaterialTable from '@/components/QuoteMaterialTable'\nimport SectionTitle from '@/components/SectionTitle'\nimport type { Material } from '@/services/brain'\nimport { getWord } from '@/utils'\nimport { cloneDeep } from 'lodash'\nimport { useEffect, useState } from 'react'\nimport { useModel } from 'umi'\nimport styles from '../index.less'\nimport type { MaterialListProps } from './index.d'\nexport default function MaterialList(props: MaterialListProps) {\n  const { curQuoteMoleculeId } = props\n  const { updateQuotes } = useModel('quotation')\n  const [allMaterials, setAllMaterials] = useState<Material[]>([])\n  const handleSave = async (values) =>\n    await updateQuotes(curQuoteMoleculeId as string, values)\n  // let sum = 0\n\n  const handleMaterialsStepId = (e: any) => {\n    let newItem = cloneDeep(e)\n    newItem.materials.forEach((item) => {\n      item.step_no = newItem?.step_info?.step_no\n      item.step_id = newItem?.step_info?.step_id\n      // if (item?.material_quotation) sum += item?.material_quotation\n    })\n    return newItem\n  }\n\n  const getAllMaterials = () => {\n    let _materials: Material[] = []\n    let _costDetail = cloneDeep(props?.quotesDetail?.cost_detail)\n    _costDetail.forEach((e) => {\n      let newItem = handleMaterialsStepId(e)\n      _materials = _materials.concat(newItem?.materials)\n    })\n    /* （原）报价总计，后续可能改为其他项的总计，目前先去掉报价列\n    if (sum > 0) {\n      _materials.push({\n        no: uuidv4() as string,\n        step_no: getWord( 'total')\n      })\n    } */\n    setAllMaterials(_materials)\n  }\n\n  useEffect(() => {\n    if (props?.quotesDetail?.cost_detail) getAllMaterials()\n  }, [props?.quotesDetail?.cost_detail])\n\n  return (\n    <>\n      <SectionTitle anchorId=\"materialTable\" word={getWord('material-list')} />\n      <div className={styles.content}>\n        <QuoteMaterialTable\n          enableEdit={!props?.disabled}\n          enableAddMaterial={false}\n          hiddeStep={false}\n          materialData={allMaterials}\n          handleSave={handleSave}\n          quoteMoleculeId={curQuoteMoleculeId as string}\n        />\n      </div>\n    </>\n  )\n}\n", "import { getWord } from '@/utils'\nimport { Space } from 'antd'\nimport cs from 'classnames'\n\nimport type { StepButtonProps } from './index.d'\nexport default function StepButton(props: StepButtonProps) {\n  const { route_index, routesNum, handleStepChange, disabled } = props\n  return (\n    <Space className={cs({ none: routesNum <= 1 || disabled })}>\n      {route_index === 0 ? (\n        <a className=\"disabledTip\">\n          {getWord('previous')}\n        </a>\n      ) : (\n        <a onClick={() => handleStepChange('prev')}>\n          {getWord('previous')}\n        </a>\n      )}\n      {route_index < routesNum - 1 ? (\n        <a onClick={() => handleStepChange('next')}>\n          {getWord('next')}\n        </a>\n      ) : (\n        <a className=\"disabledTip\">\n          {getWord('next')}\n        </a>\n      )}\n    </Space>\n  )\n}\n", "import MoleculeStructure from '@/components/MoleculeStructure'\nimport ProcedureText from '@/components/ProcedureText'\nimport QuoteMaterialTable from '@/components/QuoteMaterialTable'\nimport { service } from '@/services/brain'\nimport { getWord, isEN } from '@/utils'\nimport { ProForm, ProFormDigit } from '@ant-design/pro-components'\nimport { useAccess } from '@umijs/max'\nimport { App, Button, Col, Popconfirm, Popover, Row, Space } from 'antd'\nimport cs from 'classnames'\nimport { useEffect, useState } from 'react'\nimport { useModel, useParams } from 'umi'\nimport { toInt } from '../../../../../utils/common'\nimport StepButton from '../StepButton'\nimport styles from '../index.less'\nimport type { MyReactionProps } from './index.d'\nexport default function MyReaction(props: MyReactionProps) {\n  const { curQuoteMoleculeId, projectCompoundId, routeId } = props\n\n  const { id: projectId } = useParams<{\n    id: string\n  }>()\n  const { step_info, materials, labor } = props?.costDetail\n  const [form] = ProForm.useForm()\n  const { message } = App.useApp()\n  const access = useAccess()\n\n  useEffect(() => {\n    form.setFieldsValue({\n      labor: labor,\n      yields: step_info?.procedure?.yields ? step_info?.procedure?.yields : 50\n    })\n  }, [labor, step_info?.procedure?.yields])\n\n  const { updateQuotes } = useModel('quotation')\n  const [confirmLoading, setConfirmLoading] = useState<boolean>(false)\n  const [yieldLoading, setYieldLoading] = useState<boolean>(false)\n  const [yieldConfirm, setYieldConfirm] = useState<boolean>(false)\n  const [yieldTip, setYieldTip] = useState<string>()\n  const [canEditLabor, setCanEditLabor] = useState<boolean>(false)\n  const [canEditLaborYield, setCanEditLaborYield] = useState<boolean>(false)\n  const [cacheYields, setCacheYields] = useState<string>()\n  const reactionNum: number = step_info?.reaction_ids?.length\n  /**\n   * 更新报价(我的反应 下一步 反应时发送的请求)\n   * @param {('prev' | 'next')} step\n   */\n  const handleStepChange = async (step: 'prev' | 'next') =>\n    await updateQuotes(\n      curQuoteMoleculeId as string,\n      {\n        reaction_from_index:\n          step === 'prev' ? step_info?.index - 1 : step_info?.index + 1,\n        project_reaction_id: step_info?.step_id\n      },\n      '切换成功'\n    )\n\n  const confirm = async () => {\n    let newYields = form.getFieldValue('yields')\n    await updateQuotes(curQuoteMoleculeId as string, {\n      step_id: step_info?.step_id,\n      yields: toInt(newYields)\n    })\n    setYieldConfirm(false)\n  }\n\n  const cancel = () => {\n    setYieldConfirm(false)\n    form.setFieldValue('yields', cacheYields)\n  }\n\n  return (\n    <>\n      <div className={styles.content}>\n        <span className={styles.stepTitle} id={step_info?.step_no}>\n          {step_info?.step_no}\n        </span>\n        {step_info?.rxn ? (\n          <MoleculeStructure\n            structure={step_info?.rxn}\n            className={cs(styles.structure, 'enablePointer')}\n          />\n        ) : (\n          ''\n        )}\n        <Space className={styles.openLinkWrapper}>\n          {access?.authCodeList?.includes(\n            'view-by-backbone.tab.myReaction.reactionDetail'\n          ) && (\n            <Popover content={getWord('open-reaction-details-tip')}>\n              <a\n                className={styles.reactionLink}\n                onClick={() => {\n                  message.warning(getWord('modify-reaction-tip'))\n                  window.open(\n                    `/projects/${projectId}/reaction/${step_info?.step_id}`,\n                    '_blank'\n                  )\n                }}\n              >\n                {getWord('open-reaction-details')}\n              </a>\n            </Popover>\n          )}\n          {access?.authCodeList?.includes(\n            'quotation-records.button.openRoute'\n          ) && (\n            <Popover content={getWord('open-reaction-details-tip')}>\n              <a\n                className={styles.reactionLink}\n                onClick={() => {\n                  message.warning(getWord('modify-reaction-tip'))\n                  const sp = new URLSearchParams()\n                  sp.set('step', step_info?.rxn)\n                  if (projectId && projectCompoundId && routeId) {\n                    window.open(\n                      `/projects/${projectId}/compound/${projectCompoundId}/view/${routeId}?${sp.toString()}`,\n                      '_blank'\n                    )\n                  }\n                }}\n              >\n                {getWord('open-route-reaction')}\n              </a>\n            </Popover>\n          )}\n        </Space>\n        <div className=\"flex-justify-space-between\">\n          <div className={styles.stepTitle} style={{ fontWeight: 'normal' }}>\n            {getWord('menu.list.workspace.myReaction')}\n            {reactionNum ? `(${reactionNum})` : ''}\n          </div>\n          {reactionNum ? (\n            <StepButton\n              routesNum={reactionNum}\n              handleStepChange={handleStepChange}\n              route_index={step_info?.index}\n            />\n          ) : (\n            ''\n          )}\n        </div>\n        <Row>\n          <Col span={12} className={styles.referenceContent}>\n            {step_info?.procedure?.rxn ? (\n              <MoleculeStructure\n                structure={step_info?.procedure?.rxn}\n                className={cs(styles.referenceStructure, 'enablePointer')}\n              />\n            ) : (\n              ''\n            )}\n            <ProForm\n              grid\n              layout=\"horizontal\"\n              submitter={false}\n              form={form}\n              colProps={{ xs: 6, sm: 6, md: 6, lg: 10, offset: 1 }}\n              labelCol={{ span: 10 }}\n              wrapperCol={{ span: 8 }}\n            >\n              <ProFormDigit\n                name=\"yields\"\n                label={`${getWord('yield')}%`}\n                disabled={!canEditLaborYield}\n                max={100}\n                extra={\n                  !props.disabled ? (\n                    <Popconfirm\n                      title={getWord('update-confirm-tip')}\n                      description={yieldTip}\n                      open={yieldConfirm}\n                      onConfirm={confirm}\n                      onCancel={cancel}\n                      overlayStyle={{\n                        width: '500px'\n                      }}\n                    >\n                      <Button\n                        type=\"link\"\n                        loading={yieldLoading}\n                        onClick={async () => {\n                          let newYields = form.getFieldValue('yields')\n                          if (canEditLaborYield) {\n                            setYieldLoading(true)\n                            const { data, error } = await service(\n                              `quote/yields/${curQuoteMoleculeId}/${projectId}`,\n                              {\n                                method: 'POST'\n                              }\n                            ).create({\n                              step_id: step_info?.step_id,\n                              yields: toInt(newYields)\n                            })\n                            if (error?.message) {\n                              form.setFieldValue('yields', cacheYields)\n                              message.error(error?.message)\n                              setCanEditLaborYield(!canEditLaborYield)\n                            }\n                            if (data?.message) {\n                              setYieldConfirm(true)\n                              setYieldTip(data?.message)\n                            }\n                            setYieldLoading(false)\n                          } else {\n                            setCacheYields(newYields)\n                          }\n                          setCanEditLaborYield(!canEditLaborYield)\n                        }}\n                        className={styles.laborEditor}\n                        style={{ right: isEN() ? '-68px' : '-62px' }}\n                      >\n                        {!canEditLaborYield\n                          ? getWord('edit')\n                          : getWord('pages.route.edit.label.confirm')}\n                      </Button>\n                    </Popconfirm>\n                  ) : (\n                    ''\n                  )\n                }\n              />\n              <ProFormDigit\n                name=\"labor\"\n                label={getWord('man-days')}\n                disabled={!canEditLabor}\n                rules={[\n                  {\n                    pattern: /^(0*[1-9]\\d*\\.?\\d*|0+\\.\\d*[1-9]\\d*)$/,\n                    message: getWord('please-input-positive-number')\n                  }\n                ]}\n                extra={\n                  !props.disabled ? (\n                    <Button\n                      type=\"link\"\n                      loading={confirmLoading}\n                      onClick={async () => {\n                        let newLabor = form.getFieldValue('labor')\n                        if (!newLabor || newLabor <= 0)\n                          return message.error(\n                            getWord('please-input-positive-number')\n                          )\n                        if (canEditLabor) {\n                          setConfirmLoading(true)\n                          await updateQuotes(curQuoteMoleculeId as string, {\n                            step_id: step_info?.step_id,\n                            labor: Number.parseFloat(newLabor)\n                          })\n                          setConfirmLoading(false)\n                        }\n                        setCanEditLabor(!canEditLabor)\n                      }}\n                      className={styles.laborEditor}\n                      style={{ right: isEN() ? '-68px' : '-62px' }}\n                    >\n                      {!canEditLabor\n                        ? getWord('edit')\n                        : getWord('pages.route.edit.label.confirm')}\n                    </Button>\n                  ) : (\n                    ''\n                  )\n                }\n              />\n            </ProForm>\n          </Col>\n          <Col span={12}>\n            <ProcedureText\n              rows={6}\n              procedure={{ text: step_info?.procedure?.procedure }}\n            />\n            {step_info?.procedure?.reference_type && (\n              <div className={styles.procedureReference}>\n                {getWord('source')}：{step_info?.procedure?.reference_type}\n              </div>\n            )}\n          </Col>\n        </Row>\n        <QuoteMaterialTable\n          enableEdit={!props.disabled}\n          enableAddMaterial={true}\n          hiddeStep={true}\n          projectReactionId={step_info?.step_id}\n          materialData={materials}\n          quoteMoleculeId={curQuoteMoleculeId as string}\n        />\n      </div>\n    </>\n  )\n}\n", "import SectionTitle from '@/components/SectionTitle'\nimport useOptions from '@/hooks/useOptions'\nimport { getWord, isValidArray } from '@/utils'\nimport { EditableProTable, type ProColumns } from '@ant-design/pro-components'\nimport { cloneDeep, toNumber } from 'lodash'\nimport { useEffect, useState } from 'react'\nimport { useModel } from 'umi'\nimport styles from '../index.less'\nimport type { Columns, ScheduleProps } from './index.d'\nexport default function Schedule(props: ScheduleProps) {\n  const { curQuoteMoleculeId } = props\n  const { editableConfig } = useOptions()\n\n  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([])\n  const [dataSource, setDataSource] = useState<readonly any[]>([])\n  const { updateQuotes } = useModel('quotation')\n  const handleLaborSum = () => {\n    let sum = 0\n    let _costDetail = cloneDeep(props?.quotesDetail?.cost_detail)\n    if (isValidArray(_costDetail) && _costDetail.length > 1) {\n      _costDetail.forEach((e) => {\n        sum += Number(e?.labor)\n      })\n      if (sum > 0) {\n        _costDetail.push({\n          step_id: getWord('total'),\n          labor: !isNaN(sum) && sum ? (toNumber(sum).toFixed(2) as number) : ''\n        })\n      }\n    }\n    setDataSource(_costDetail)\n  }\n\n  useEffect(() => {\n    if (props?.quotesDetail?.cost_detail) handleLaborSum()\n  }, [props?.quotesDetail?.cost_detail])\n\n  const operate = (): ProColumns<Columns>[] => {\n    return [\n      {\n        title: getWord('pages.experiment.label.operation'),\n        valueType: 'option',\n        width: 200,\n        render: (\n          _text: string,\n          record: Columns,\n          _: any,\n          action: { startEditable: (step_id: string) => void }\n        ) => {\n          return [\n            record?.step_id === getWord('total') ? (\n              ''\n            ) : (\n              <a\n                key=\"editable\"\n                onClick={() => {\n                  action?.startEditable?.(record?.step_id)\n                }}\n              >\n                {getWord('edit')}\n              </a>\n            )\n          ]\n        }\n      }\n    ]\n  }\n  const columns: ProColumns<Columns>[] = [\n    {\n      title: getWord('steps'),\n      dataIndex: ['step_info', 'step_no'],\n      readonly: true,\n      render: (_text, record: Columns) =>\n        record?.step_id === getWord('total') ? (\n          record?.step_id\n        ) : (\n          <a href={`#${record?.step_info?.step_no}`}>\n            {record?.step_info?.step_no}\n          </a>\n        )\n    },\n    {\n      title: getWord('estimate-work-day'),\n      dataIndex: 'labor'\n    }\n  ]\n  return (\n    <>\n      <SectionTitle anchorId=\"laborTable\" word={getWord('work-time-detils')} />\n      <div className={styles.content}>\n        <EditableProTable<any>\n          rowKey=\"step_id\"\n          maxLength={5}\n          scroll={{\n            x: 960\n          }}\n          recordCreatorProps={false}\n          loading={false}\n          columns={props?.disabled ? columns : [...columns, ...operate()]}\n          value={dataSource}\n          editable={{\n            ...editableConfig,\n            type: 'multiple',\n            editableKeys,\n            actionRender: (_row, _config, defaultDom) => [\n              defaultDom.save,\n              defaultDom.cancel\n            ],\n            onSave: async (_rowKey, data) => {\n              await updateQuotes(curQuoteMoleculeId as string, {\n                step_id: data?.step_id,\n                labor: Number(data?.labor)\n              })\n            },\n            onChange: setEditableRowKeys\n          }}\n        />\n      </div>\n    </>\n  )\n}\n", "import useOptions from '@/hooks/useOptions'\nimport { query, type Cost_summary } from '@/services/brain'\nimport { IOption } from '@/types/Common'\nimport { getWord, isValidArray } from '@/utils'\nimport type { ProColumns } from '@ant-design/pro-components'\nimport { EditableProTable } from '@ant-design/pro-components'\nimport { message } from 'antd'\nimport { cloneDeep, isArray, isEmpty } from 'lodash'\nimport { useEffect, useState } from 'react'\nimport { useModel } from 'umi'\nimport type { ConfigDicts } from '../../../../../services/brain/types/quotes'\nimport styles from '../index.less'\nimport type { SummaryProps } from './index.d'\ntype QuoteItem = '其他试剂' | '手性分离'\nexport default function Summary(props: SummaryProps) {\n  const { curQuoteMoleculeId } = props\n  const { chargeDes, editableConfig } = useOptions()\n  const { updateQuotes } = useModel('quotation')\n  const [quoteSummaryData, setQuoteSummaryData] = useState<\n    readonly Cost_summary[]\n  >([])\n  const [chargeOptions, setChargeOptions] = useState<IOption[]>([])\n  const [newItemName, setNewItemName] = useState<QuoteItem>(\n    getWord('other-reagent')\n  )\n\n  const handleDisabledOption = (\n    _chargeOptions: IOption[],\n    targetLabel: string,\n    enable?: boolean\n  ) => {\n    if (isEmpty(_chargeOptions)) return\n    let newChargeOptions = cloneDeep(_chargeOptions)\n    if (!isEmpty(newChargeOptions)) {\n      if (enable) {\n        let targetOption = newChargeOptions.find(\n          (e) => e?.value === targetLabel || e?.label === targetLabel\n        )\n        console.log('---newName02---', targetLabel)\n        setNewItemName(targetLabel)\n        if (targetOption) targetOption.disabled = false\n      } else {\n        newChargeOptions.map((e) => {\n          e.disabled = e?.value === targetLabel || e?.label === targetLabel\n          return null\n        })\n      }\n    }\n    setChargeOptions(newChargeOptions)\n  }\n\n  const getNewItemName = (\n    curQuoteSummaryData: Cost_summary[],\n    _chargeOptions: IOption[]\n  ) => {\n    let newName = newItemName\n    let alreadySelectedOther: boolean =\n      curQuoteSummaryData.findIndex(\n        (e) => e?.name === getWord('other-reagent') || e?.name === 'other'\n      ) !== -1\n    let alreadySelectedSeparation: boolean =\n      curQuoteSummaryData.findIndex(\n        (e) =>\n          e?.name === getWord('chiral-separation') || e?.name === 'separation'\n      ) !== -1\n    if (alreadySelectedOther && alreadySelectedSeparation) {\n      handleDisabledOption(_chargeOptions, getWord('other-reagent'))\n      handleDisabledOption(_chargeOptions, getWord('chiral-separation'))\n    } else if (alreadySelectedOther) {\n      newName = getWord('chiral-separation') as QuoteItem\n      handleDisabledOption(_chargeOptions, getWord('other-reagent'))\n    } else if (alreadySelectedSeparation) {\n      handleDisabledOption(_chargeOptions, getWord('chiral-separation'))\n      newName = getWord('other-reagent') as QuoteItem\n    }\n    setNewItemName(newName)\n  }\n\n  const getChargeOptions = async (defaultDataSource: Cost_summary[]) => {\n    const res = await query(`config-dicts`)\n      .filterDeep('category', 'eq', 'cost')\n      .get()\n    let data = res?.data as ConfigDicts[],\n      error = res?.error\n    let finalData: IOption[] = []\n    if (!isEmpty(data) && isArray(data)) {\n      const cloneData = [...data]\n      cloneData.map((e) => {\n        finalData.push({\n          value: e?.code,\n          label: e?.name,\n          disabled:\n            defaultDataSource.findIndex((cur) => cur.name === e?.code) !== -1\n        })\n        return null\n      })\n    }\n    if (error?.message) return message.error(error?.message)\n    if (data) setChargeOptions(finalData)\n    if (!isEmpty(defaultDataSource) && !isEmpty(finalData))\n      getNewItemName(defaultDataSource, finalData)\n  }\n\n  useEffect(() => {\n    if (!isEmpty(quoteSummaryData) && !isEmpty(chargeOptions))\n      getNewItemName(quoteSummaryData as Cost_summary[], chargeOptions)\n  }, [quoteSummaryData])\n\n  const handleDataID = (originData: Cost_summary[]) => {\n    return originData.map((item) => {\n      return {\n        ...item,\n        id: item.name\n      }\n    })\n  }\n\n  useEffect(() => {\n    if (!isValidArray(props?.quotesDetail?.cost_summary)) return\n    const newData = handleDataID(props?.quotesDetail?.cost_summary)\n    setQuoteSummaryData(newData)\n    getChargeOptions(newData)\n  }, [props?.quotesDetail?.cost_summary])\n\n  const columns: ProColumns<Cost_summary>[] = [\n    {\n      title: getWord('charge-items'),\n      dataIndex: 'name',\n      valueType: 'select',\n      fieldProps: (_form, { rowIndex }) => {\n        if (rowIndex + 1 > props?.quotesDetail?.cost_summary?.length) {\n          return {\n            options: chargeOptions\n          }\n        } else {\n          return {\n            options: chargeOptions,\n            disabled: true\n          }\n        }\n      },\n      render: (text) => (chargeDes[text] ? chargeDes[text] : text)\n    },\n    {\n      title: 'RMB',\n      dataIndex: 'RMB',\n      valueType: 'digit'\n    },\n    {\n      title: 'USD',\n      dataIndex: 'USD',\n      readonly: true\n    }\n  ]\n\n  const operate = (): ProColumns<Cost_summary>[] => {\n    return [\n      {\n        title: getWord('pages.experiment.label.operation'),\n        valueType: 'option',\n        width: 200,\n        render: (_text, record, _, action) =>\n          !['total_cost', 'material_cost', 'labor_cost'].includes(record?.name)\n            ? [\n                <a\n                  key=\"editable\"\n                  onClick={() => {\n                    action?.startEditable?.(record.name)\n                  }}\n                >\n                  {getWord('edit')}\n                </a>,\n                <a\n                  key=\"delete\"\n                  onClick={async () => {\n                    setQuoteSummaryData(\n                      quoteSummaryData.filter(\n                        (item) => item.name !== record.name\n                      )\n                    )\n                    handleDisabledOption(chargeOptions, record.name, true)\n                    await updateQuotes(curQuoteMoleculeId, {\n                      other_costs: {\n                        name: record?.name,\n                        RMB: Number(record?.RMB),\n                        delete: true\n                      }\n                    })\n                  }}\n                >\n                  {getWord('del')}\n                </a>\n              ]\n            : '-'\n      }\n    ]\n  }\n\n  return (\n    <>\n      <div className={styles.content}>\n        <div className={styles.title}>{`${getWord('quotation')} Summary`}</div>\n        <div className={styles.quotationSummaryDetail}>\n          {getWord('estimate-delivery')}:&nbsp;\n          {props?.quotesDetail?.delivery_time}\n        </div>\n        <div\n          className={styles.quotationSummaryDetail}\n          style={{ marginBottom: '10px' }}\n        >\n          {`${getWord('quotation')} summary（RMB）`}\n          :&nbsp;\n          {props?.quotesDetail?.quotation_summary}\n        </div>\n        <div className={styles.title} style={{ marginBottom: '18px' }}>\n          {getWord('cost-summary')}\n        </div>\n        <EditableProTable<Cost_summary>\n          rowKey=\"id\"\n          maxLength={5}\n          scroll={{\n            x: 960\n          }}\n          loading={false}\n          recordCreatorProps={\n            props?.disabled\n              ? false\n              : {\n                  position: 'bottom',\n                  creatorButtonText: getWord('add-new-charge'),\n                  record: () => ({ id: (Math.random() * 1000000).toFixed(0) })\n                }\n          }\n          columns={props?.disabled ? columns : [...columns, ...operate()]}\n          value={\n            !isEmpty(chargeOptions) && !isEmpty(quoteSummaryData)\n              ? (quoteSummaryData as Cost_summary[])\n              : []\n          }\n          editable={{\n            ...editableConfig,\n            type: 'multiple',\n            onSave: async (_, data) => {\n              await updateQuotes(curQuoteMoleculeId as string, {\n                other_costs: {\n                  name: data?.name,\n                  RMB: Number(data?.RMB)\n                }\n              })\n            }\n          }}\n        />\n      </div>\n    </>\n  )\n}\n", "import { Cost_detail } from '@/services/brain'\nimport { isEmpty } from 'lodash'\nimport type { DiffCost } from './index.d'\n\nexport const findDiffCosts = (costDetail: Cost_detail[]) => {\n  let diffCosts: DiffCost[] = []\n  if (isEmpty(costDetail) || !costDetail) return []\n  for (let i = 0; i < costDetail.length; i++) {\n    for (let j = 0; j < costDetail[i]?.materials.length; j++) {\n      let curItem = costDetail[i]?.materials[j]\n      if (curItem?.cost_modified) {\n        diffCosts.push({\n          step_no: costDetail[i]?.step_info?.step_no,\n          name_zh: curItem?.name_zh as string,\n          name_en: curItem?.name_en as string,\n          cas_no: curItem?.cas_no as string,\n          minCost: curItem?.cost as number,\n          cost: curItem?.set_cost as string\n        })\n      }\n    }\n  }\n  return diffCosts\n}\n", "import ButtonWithLoading from '@/components/ButtonWithLoading'\nimport ModalBase from '@/components/ModalBase'\nimport { useModalBase } from '@/components/ModalBase/useModalBase'\nimport SectionTitle from '@/components/SectionTitle'\nimport SyntheticRouteCard from '@/components/SyntheticRoutes/SyntheticRouteCard'\nimport { AmendOtherQuote, OtherQuote, OtherQuoteCB } from '@/models/quotation'\nimport { apiDelByQuoteNo, parseResponseResult } from '@/services'\nimport type { IQuote } from '@/services/brain'\nimport { Cost_detail, query } from '@/services/brain'\nimport { getWord, isEN, isValidArray } from '@/utils'\nimport {\n  PageContainer,\n  ProForm,\n  ProFormDigit,\n  ProFormInstance,\n  ProFormText\n} from '@ant-design/pro-components'\nimport { Button, Form, Select, Space, Typography, message } from 'antd'\nimport cs from 'classnames'\nimport { isArray, isEmpty, isNil } from 'lodash'\nimport { ReactNode, useEffect, useRef, useState } from 'react'\nimport { history, useAccess, useModel, useParams, useSearchParams } from 'umi'\nimport CustomAnchor from '../../../../components/Anchor'\nimport OtherQuoteModal from '../../components/OtherQuoteModal'\nimport ConfirmModal from './ConfirmQuoteModal'\nimport MaterialList from './MaterialList'\nimport MyReaction from './MyReaction'\nimport Schedule from './Schedule'\nimport StepButton from './StepButton'\nimport Summary from './Summary'\nimport type { ConrifrmRoutes, DiffCost, FilterForm } from './index.d'\nimport styles from './index.less'\nimport { findDiffCosts } from './utils'\ntype QupteType = 'create' | 'view'\nexport default function QuoteDetail() {\n  const { initialState } = useModel('@@initialState')\n  const [searchParams] = useSearchParams()\n  const {\n    quotesDetail,\n    updateQuotesDetail,\n    createQuote,\n    updateQuotes,\n    updatetDiffCosts,\n    diffCosts\n  } = useModel('quotation')\n  const { getUserConfigs } = useModel('login')\n  const [weighUnit, setWeighUnit] = useState<'mg' | 'g'>('g')\n  const [fetchQuoteLoading, setFetchQuoteLoading] = useState<boolean>(false)\n  let quoteType: QupteType = searchParams.get('type') as QupteType\n  let compound_no: string = searchParams.get('compound_no') as string\n\n  const isCreateType: boolean = quoteType === 'create'\n  const { id: projectId, quoteMoleculeId } = useParams<{\n    id: string\n    quoteMoleculeId: string\n  }>()\n  const access = useAccess()\n\n  const [curQuoteMoleculeId, setCurQuoteMoleculeId] = useState(quoteMoleculeId)\n  const [routesNum, setRoutesNum] = useState<number>()\n  const [conrifrmRoutes, setConrifrmRoutes] = useState<IQuote[] | null>([])\n  const [isEditorType, setIsEditorType] = useState(false)\n  const disabledEditInConfirmedStatus: boolean =\n    !isEditorType && quotesDetail?.status === 'confirmed'\n  const [form] = Form.useForm<OtherQuote>()\n\n  const getQuotationRecords = async () => {\n    const { data, meta } = await query(\n      `quote/routes?project_id=${projectId}&compound_id=${curQuoteMoleculeId}`\n    ).get()\n    setConrifrmRoutes(data)\n    setRoutesNum(meta?.total)\n  }\n\n  const getDiffCosts = (data: IQuote) => {\n    if (!data) return\n    let _diffCosts: DiffCost[] = findDiffCosts(data?.cost_detail)\n    updatetDiffCosts(_diffCosts)\n  }\n\n  useEffect(() => {\n    if (!isNil(quotesDetail)) {\n      getDiffCosts(quotesDetail)\n      setConrifrmRoutes([quotesDetail])\n    }\n  }, [quotesDetail])\n\n  const toEditQuote = (_curQuoteMoleculeId: string) => {\n    history.replace(\n      `/projects/${projectId}/quotation-records/${_curQuoteMoleculeId}/quote-info?type=editor&compound_no=${compound_no}`\n    )\n  }\n\n  const getQuoteDetail = async () => {\n    const { data } = await query(`quotes/${curQuoteMoleculeId}`).get()\n    setConrifrmRoutes([data])\n    setRoutesNum(data?.quote_route?.length)\n    getDiffCosts(data)\n    updateQuotesDetail(data)\n  }\n\n  const formRef = useRef<ProFormInstance>()\n  useEffect(() => {\n    if (formRef?.current) {\n      formRef?.current?.setFieldsValue({\n        target_weight: quotesDetail?.target_weight,\n        purity: quotesDetail?.purity,\n        FTE_unit_price: quotesDetail?.FTE_unit_price,\n        ratio: quotesDetail?.ratio\n      })\n      if (quotesDetail?.target_unit) setWeighUnit(quotesDetail?.target_unit)\n    }\n  }, [quotesDetail])\n\n  useEffect(() => {\n    if (formRef?.current && quotesDetail?.project_compound_id) {\n      formRef?.current?.setFieldsValue({\n        project_compound_id: quotesDetail?.project_compound_id\n      })\n    }\n  }, [quotesDetail?.project_compound_id])\n\n  const [curRouteId, setCurRouteId] = useState<number>()\n  const [createRouteIndex, setCreateRouteIndex] = useState<number>(0)\n  useEffect(() => {\n    if (formRef?.current && compound_no) {\n      formRef?.current?.setFieldsValue({\n        compound_no: compound_no\n      })\n    }\n  }, [compound_no])\n\n  const [openEvent, setOpenEvent] = useState<{ open: boolean }>()\n  const isConfirmedQuote = quotesDetail?.status === 'confirmed'\n  let canSaveDraft =\n    (['draft', 'editing'].includes(quotesDetail?.status) ||\n      !disabledEditInConfirmedStatus) &&\n    access?.authCodeList?.includes('quotation-records.button.saveDraft')\n  let canConfirm =\n    (['draft', 'editing'].includes(quotesDetail?.status) ||\n      !disabledEditInConfirmedStatus) &&\n    access?.authCodeList?.includes('quotation-records.button.confirmQuote')\n  const handelQuoteStatus = async (quoteStatus: 'confirmed' | 'editing') => {\n    let formValues = await formRef?.current?.validateFields()\n    const { error } = await updateQuotes(\n      curQuoteMoleculeId as string,\n      {\n        ...formValues,\n        status: quoteStatus,\n        target_unit: weighUnit,\n        project_id: projectId\n      },\n      quoteStatus === 'confirmed'\n        ? getWord('success-confirm-quotate')\n        : getWord('success-save-draft')\n    )\n    if (!error && quoteStatus === 'confirmed') setIsEditorType(false)\n  }\n\n  const fetchData = async () => {\n    if (fetchQuoteLoading) return\n    setFetchQuoteLoading(true)\n    if (!isCreateType) {\n      await getQuoteDetail()\n    } else {\n      await getQuotationRecords()\n    }\n    setFetchQuoteLoading(false)\n  }\n\n  const getDefaultConfigs = async () => {\n    const data = await getUserConfigs()\n    let quoteDefaultSettings = data?.find(\n      (e) => e.setting_label === 'quotation'\n    )\n    if (formRef?.current && quoteDefaultSettings) {\n      formRef?.current?.setFieldsValue({\n        FTE_unit_price:\n          quotesDetail?.FTE_unit_price ||\n          quoteDefaultSettings?.setting_value?.FTE_rate,\n        ratio: quotesDetail?.ratio || quoteDefaultSettings?.setting_value?.ratio\n      })\n    }\n  }\n\n  /* FIXME render twice */\n  useEffect(() => {\n    if (!quoteType) return\n    fetchData()\n    getDefaultConfigs()\n  }, [isCreateType])\n\n  useEffect(() => {\n    return () => {\n      updateQuotesDetail(null)\n    }\n  }, [])\n\n  const selectAfter = (\n    <Select\n      className={styles.unitSelect}\n      value={weighUnit}\n      style={{ width: '60px' }}\n      onChange={(e) => setWeighUnit(e)}\n      disabled={!isCreateType}\n    >\n      <Select.Option value=\"g\">g</Select.Option>\n      <Select.Option value=\"mg\">mg</Select.Option>\n    </Select>\n  )\n\n  const handleStepChange = async (step: 'prev' | 'next') => {\n    let nextQuoteIndex: number =\n      step === 'prev'\n        ? quotesDetail?.route_index - 1\n        : quotesDetail?.route_index + 1\n    let requestQuoteId: string =\n      quotesDetail?.quote_route[nextQuoteIndex]?.quote_id\n    if (!requestQuoteId) return\n    const { data, error } = await query<any>(`quotes/${requestQuoteId}`).get()\n    if (error?.message) return message.error(error?.message)\n    else if (data) {\n      updateQuotesDetail(data)\n      setCurQuoteMoleculeId(requestQuoteId)\n      window.history.pushState(\n        null,\n        '',\n        `${window.location.origin}/projects/${projectId}/quotation-records/${requestQuoteId}/quote-info?type=editor&compound_no=${compound_no}`\n      )\n    }\n  }\n\n  const handleCreateStepChange = async (step: 'prev' | 'next') => {\n    let nextQuoteIndex: number =\n      step === 'prev' ? createRouteIndex - 1 : createRouteIndex + 1\n    setCreateRouteIndex(nextQuoteIndex)\n  }\n\n  const commonAnchor = [\n    {\n      key: 'quotationParameter',\n      href: '#quotationParameter',\n      title: getWord('quote-parmas')\n    },\n    {\n      key: 'confirmedRoute',\n      href: '#confirmedRoute',\n      title: getWord('confirmed-route')\n    }\n  ]\n\n  const RouteItem = ({\n    stepChange,\n    route,\n    routeIndex\n  }: {\n    stepChange: (step: 'prev' | 'next') => void\n    route: any\n    routeIndex: number\n  }) => {\n    if (!route) return ''\n    const routeId = route?.project_route_id || route?.id\n    setCurRouteId(routeId)\n    return (\n      <>\n        <div className=\"flex-justify-space-between\">\n          <div>\n            {getWord('route-id')}：\n            {routeId && (\n              <Typography.Link\n                href={`/projects/${projectId}/compound/${route?.project_compound_id}/view/${routeId}`}\n                target=\"_blank\"\n              >\n                {routeId}\n              </Typography.Link>\n            )}\n          </div>\n          <StepButton\n            disabled={disabledEditInConfirmedStatus}\n            routesNum={routesNum as number}\n            handleStepChange={stepChange}\n            route_index={routeIndex}\n          />\n        </div>\n        <SyntheticRouteCard key={routeId} route={route} hiddenTitle={true} />\n      </>\n    )\n  }\n\n  const RenderRoutes = (): ReactNode => {\n    if (isEmpty(conrifrmRoutes) || !isArray(conrifrmRoutes)) return ''\n    return !isCreateType ? (\n      conrifrmRoutes?.map((route: ConrifrmRoutes, index: number) => {\n        return (\n          <>\n            <RouteItem\n              key={`routeItem-${index}`}\n              route={route}\n              stepChange={handleStepChange}\n              routeIndex={quotesDetail?.route_index as number}\n            />\n          </>\n        )\n      })\n    ) : (\n      <RouteItem\n        key=\"routeItem-item\"\n        route={conrifrmRoutes[createRouteIndex]}\n        stepChange={handleCreateStepChange}\n        routeIndex={createRouteIndex as number}\n      />\n    )\n  }\n\n  const [repeatedWeight, setRepeatedWeight] = useState<string>()\n  const [quotationNo, setQuotationNo] = useState<string>()\n  const { dialogProps, confirm } = useModalBase()\n\n  const createQuoteCB = (values: OtherQuoteCB, isOtherQuote: boolean) => {\n    const { _curQuoteMoleculeId, _repeatedWeight, _quotationNo } = values\n    if (_repeatedWeight) {\n      confirm()\n      setRepeatedWeight(_repeatedWeight)\n      setQuotationNo(_quotationNo)\n    } else {\n      setCurQuoteMoleculeId(_curQuoteMoleculeId as string)\n      if (!isOtherQuote) {\n        toEditQuote(_curQuoteMoleculeId as string)\n      } else {\n        setCurQuoteMoleculeId(_curQuoteMoleculeId as string)\n        toEditQuote(_curQuoteMoleculeId as string)\n      }\n    }\n  }\n\n  const [createLoading, setCreateLoading] = useState<boolean>(false)\n\n  const createNewQuote = () => {\n    formRef?.current?.validateFields().then(async (formValues) => {\n      setCreateLoading(true)\n      await createQuote(\n        {\n          ...formValues,\n          target_unit: weighUnit,\n          project_id: projectId,\n          route_id: curRouteId\n        },\n        (values: OtherQuoteCB) => createQuoteCB(values, false)\n      )\n      setCreateLoading(false)\n    })\n  }\n\n  /* 删除老的报价 */\n  const repeatedWeightConfirm = async () => {\n    const res = await apiDelByQuoteNo({\n      routeParams: `${quotationNo}/${projectId}`\n    })\n    if (parseResponseResult(res).ok) createNewQuote()\n  }\n\n  const route = conrifrmRoutes?.[createRouteIndex]\n\n  return (\n    <div className={styles.quoteInfo}>\n      <ConfirmModal\n        diffCostData={diffCosts}\n        openEvent={openEvent}\n        confirmDiff={async () => {\n          updatetDiffCosts([])\n          await handelQuoteStatus('confirmed')\n        }}\n      />\n      <PageContainer\n        className={cs(styles.quoteDetail, {\n          [styles['unfoldWidth']]: !initialState?.isMenuCollapsed,\n          [styles['foldWidth']]: initialState?.isMenuCollapsed,\n          [styles['unfoldWidth_EN']]: !initialState?.isMenuCollapsed && isEN(),\n          [styles['foldWidth_EN']]: initialState?.isMenuCollapsed && isEN()\n        })}\n      >\n        <SectionTitle\n          anchorId=\"quotationParameter\"\n          word={getWord('quote-parmas')}\n        />\n        <ProForm<FilterForm>\n          grid\n          layout=\"horizontal\"\n          submitter={false}\n          formRef={formRef}\n          colProps={{ xs: 12, sm: 12, md: 8, lg: 6 }}\n          labelCol={{ span: isEN() ? 12 : 10 }}\n          wrapperCol={{ span: isEN() ? 12 : 14 }}\n          initialValues={{\n            project_compound_id: curQuoteMoleculeId\n          }}\n        >\n          <ProFormText\n            name=\"compound_no\"\n            label={getWord('molecules-no')}\n            labelCol={{ span: isEN() ? 10 : 10 }}\n            wrapperCol={{ span: isEN() ? 14 : 14 }}\n            disabled\n          />\n          <ProFormText\n            name=\"project_compound_id\"\n            label={getWord('project-molecule-id')}\n            labelCol={{ span: isEN() ? 14 : 10 }}\n            wrapperCol={{ span: isEN() ? 10 : 14 }}\n            disabled\n          />\n          <ProFormDigit\n            name=\"target_weight\"\n            disabled={!isCreateType}\n            label={getWord('weight')}\n            extra={selectAfter}\n            rules={[{ required: true }]}\n            labelCol={{ span: 8 }}\n            wrapperCol={{ span: 12 }}\n            min={0.1}\n          />\n          <ProFormDigit\n            name=\"purity\"\n            disabled={!isCreateType}\n            rules={[{ required: true }]}\n            label={getWord('purity')}\n            min={0.1}\n            max={100}\n            labelCol={{ span: isEN() ? 12 : 10 }}\n            wrapperCol={{ span: 8 }}\n            addonAfter={\n              <div\n                className={styles.unitSelect}\n                style={{ right: '-24px', top: '5px' }}\n              >\n                %\n              </div>\n            }\n          />\n          <ProFormDigit\n            name=\"FTE_unit_price\"\n            label={getWord('FTE-per-day')}\n            labelCol={{ span: isEN() ? 10 : 10 }}\n            wrapperCol={{ span: isEN() ? 10 : 10 }}\n            rules={[{ required: true }]}\n            disabled={disabledEditInConfirmedStatus}\n            extra={\n              <div\n                className={styles.unitSelect}\n                style={{ top: '5px', right: isEN() ? '-92px' : '-40px' }}\n              >\n                {getWord('FTE-per-day-unit')}\n              </div>\n            }\n          />\n          <ProFormDigit\n            name=\"ratio\"\n            label={getWord('coefficient')}\n            rules={[{ required: true }]}\n            labelCol={{ span: isEN() ? 14 : 10 }}\n            wrapperCol={{ span: isEN() ? 8 : 14 }}\n            disabled={disabledEditInConfirmedStatus}\n            extra={\n              disabledEditInConfirmedStatus || isCreateType ? (\n                ''\n              ) : (\n                <a\n                  className={styles.unitSelect}\n                  style={{ top: '5px', right: isEN() ? '-130px' : '-60px' }}\n                  onClick={async () => {\n                    await updateQuotes(curQuoteMoleculeId as string, {\n                      FTE_unit_price:\n                        formRef?.current?.getFieldValue('FTE_unit_price'),\n                      ratio: formRef?.current?.getFieldValue('ratio')\n                    })\n                    getQuoteDetail()\n                  }}\n                >\n                  {getWord('calculate-quotation')}\n                </a>\n              )\n            }\n            min={0.1}\n            max={100}\n          />\n          <div className={styles.operateButton}>\n            {!isCreateType || isEditorType ? (\n              <Space>\n                {canSaveDraft ? (\n                  <ButtonWithLoading\n                    onClick={() => handelQuoteStatus('editing')}\n                  >\n                    {getWord('save-draft')}\n                  </ButtonWithLoading>\n                ) : (\n                  ''\n                )}\n                {canConfirm ? (\n                  <ButtonWithLoading\n                    onClick={() => {\n                      if (!isEmpty(diffCosts))\n                        return setOpenEvent({ open: true })\n                      handelQuoteStatus('confirmed')\n                    }}\n                  >\n                    {getWord('confirm-quote')}\n                  </ButtonWithLoading>\n                ) : (\n                  ''\n                )}\n                {isConfirmedQuote ? (\n                  <>\n                    {!isEditorType && (\n                      <>\n                        {access?.authCodeList?.includes(\n                          'quotation-records.button.editQuote'\n                        ) && (\n                          <Button onClick={() => setIsEditorType(true)}>\n                            {getWord('edit')}\n                          </Button>\n                        )}\n                        <OtherQuoteModal\n                          form={form}\n                          initalValues={{\n                            compound_no: compound_no,\n                            purity: quotesDetail?.purity,\n                            ratio: quotesDetail?.ratio\n                          }}\n                          onFinish={(values: AmendOtherQuote) => {\n                            createQuote(\n                              {\n                                ...values,\n                                quote_id: curQuoteMoleculeId as string\n                              },\n                              (values: OtherQuoteCB) =>\n                                createQuoteCB(values, true)\n                            )\n                          }}\n                        />\n                      </>\n                    )}\n                  </>\n                ) : (\n                  ''\n                )}\n              </Space>\n            ) : (\n              <ButtonWithLoading\n                onClick={createNewQuote}\n                loading={createLoading}\n              >\n                {getWord('pages.route.edit.label.confirm')}\n              </ButtonWithLoading>\n            )}\n          </div>\n        </ProForm>\n        <SectionTitle\n          anchorId=\"confirmedRoute\"\n          word={\n            !isCreateType && isValidArray(conrifrmRoutes)\n              ? getWord('quotate-route')\n              : `${getWord('confirmed-route')}${\n                  routesNum ? `（${routesNum}）` : ''\n                }`\n          }\n        />\n        {/* FIXME 切换 增加loading效果 */}\n        <div className={styles.routeInfo}>\n          {/* NOTE 能报价的路线只有 我的路线 */}\n          <RenderRoutes />\n          {!isCreateType ? (\n            <Summary\n              quotesDetail={quotesDetail}\n              disabled={disabledEditInConfirmedStatus}\n              curQuoteMoleculeId={curQuoteMoleculeId as string}\n            />\n          ) : (\n            ''\n          )}\n        </div>\n        {!isCreateType ? (\n          <>\n            <MaterialList\n              quotesDetail={quotesDetail}\n              disabled={disabledEditInConfirmedStatus}\n              curQuoteMoleculeId={curQuoteMoleculeId as string}\n            />\n            <Schedule\n              quotesDetail={quotesDetail}\n              disabled={disabledEditInConfirmedStatus}\n              curQuoteMoleculeId={curQuoteMoleculeId as string}\n            />\n            <SectionTitle\n              anchorId=\"reactionDetail\"\n              word={getWord('reaction-details')}\n            />\n            {!isEmpty(quotesDetail?.cost_detail) &&\n            isArray(quotesDetail?.cost_detail)\n              ? quotesDetail?.cost_detail?.map(\n                  (item: Cost_detail, index: number) => (\n                    <MyReaction\n                      disabled={disabledEditInConfirmedStatus}\n                      costDetail={item}\n                      curQuoteMoleculeId={curQuoteMoleculeId as string}\n                      key={`reaction-${index}`}\n                      projectCompoundId={route?.project_compound_id}\n                      routeId={route?.project_route_id || route?.id}\n                    />\n                  )\n                )\n              : ''}\n          </>\n        ) : (\n          ''\n        )}\n      </PageContainer>\n      {/* TODO better 支持折叠展开 */}\n      {/* FIXME 动态计算steps，支持 物料明细表、工时明细表 点击步骤进行跳转 */}\n      <div style={{ flex: isEN() ? '0 0 170px' : '0 0 120px' }}>\n        <CustomAnchor\n          wrapClassName={isEN() ? styles.anchorEN : styles.anchor}\n          items={\n            !isCreateType\n              ? commonAnchor.concat([\n                  {\n                    key: 'materialTable',\n                    href: '#materialTable',\n                    title: getWord('material-list')\n                  },\n                  {\n                    key: 'laborTable',\n                    href: '#laborTable',\n                    title: getWord('work-time-detils')\n                  },\n                  {\n                    key: 'reactionDetail',\n                    href: '#reactionDetail',\n                    title: getWord('reaction-details')\n                  }\n                ])\n              : commonAnchor\n          }\n        />\n        <ModalBase\n          {...dialogProps}\n          title={getWord('same-quatation')}\n          width={800}\n          onConfirm={repeatedWeightConfirm}\n        >\n          <span style={{ fontSize: '20px' }}>\n            {isEN() ? (\n              `A quotation already exists for the molecule ${repeatedWeight}. Would you like to requote (the previous quotation will be deleted)?`\n            ) : (\n              <>\n                已经存在这个分子&nbsp;\n                <span style={{ color: 'red' }}>{repeatedWeight}</span>&nbsp;\n                的报价，是否重新报价（原来的报价会被删除）?\n              </>\n            )}\n          </span>\n        </ModalBase>\n      </div>\n    </div>\n  )\n}\n", "import {\n  divideTreeChildrenByIfHasChildren,\n  SyntheticTree\n} from './SyntheticTree'\n\nexport interface SyntheticLink {\n  value: string\n  child?: SyntheticLink\n  rxn?: string\n  path?: string[]\n}\n\nconst calTreeDeepth = ({ children = [] }: SyntheticTree): number => {\n  if (!children.length) return 1\n  return (\n    children\n      .map((child) => calTreeDeepth(child))\n      .reduce((acc, cur) => Math.max(acc, cur)) + 1\n  )\n}\n\nconst getMainRoute = (\n  children: SyntheticTree[],\n  mainMaterial?: string\n): [SyntheticTree | null, SyntheticTree[]] => {\n  const [intermediates, materials] = divideTreeChildrenByIfHasChildren(children)\n  if (intermediates.length === 0) {\n    if (materials.length === 0) return [null, []]\n\n    const longest = materials.reduce((acc, cur) =>\n      acc.value.length > cur.value.length ? acc : cur\n    )\n    const mainMaterialTree = materials.find((c) => c.value === mainMaterial)\n    const main = mainMaterialTree || longest\n    const others = materials.filter((child) => child !== main)\n    return [main, others]\n  }\n\n  const intermediateWithLongestPath = intermediates.reduce((acc, cur) =>\n    calTreeDeepth(acc) >= calTreeDeepth(cur) ? acc : cur\n  )\n  const others = intermediates.filter(\n    ({ value }) => value !== intermediateWithLongestPath.value\n  )\n  return [intermediateWithLongestPath, [...others, ...materials]]\n}\n\nexport const syntheticTreeToLink = (\n  { value, mainMaterial, children = [] }: SyntheticTree,\n  path: string[] = []\n): SyntheticLink => {\n  const [mainRoute] = getMainRoute(children, mainMaterial)\n  const currentPath = [...path, value]\n  return {\n    value,\n    child: mainRoute ? syntheticTreeToLink(mainRoute, currentPath) : undefined,\n    rxn: `${children.map((c) => c.value).join('.')}>${value}`,\n    path: currentPath\n  }\n}\n\nexport const backboneToLink = (backbone: string[]): SyntheticLink => {\n  return backbone.reduceRight<SyntheticLink>(\n    (acc, cur, index) => {\n      const currentPath = backbone.slice(0, index + 1)\n      if (acc.value) {\n        return { value: cur, child: acc, path: currentPath }\n      } else {\n        return { value: cur, path: currentPath }\n      }\n    },\n    { value: '', path: [] }\n  )\n}\n", "import { MainTreeForRoute } from '@/pages/route/util'\nimport { groupBy } from 'lodash'\nimport { RetronSyntheticRoute } from './RetronSynthetic'\n\nexport interface SyntheticTree\n  extends Omit<MainTreeForRoute, 'id' | 'children'> {\n  children?: SyntheticTree[]\n  parent?: string\n}\n\nexport const routeTreeToSyntheticTree = (\n  { value, children }: MainTreeForRoute,\n  parent?: string\n): SyntheticTree => {\n  if (!children?.length) return { value, parent }\n  return {\n    value,\n    children: children.map((c) => routeTreeToSyntheticTree(c, value)),\n    parent\n  }\n}\n\nexport const retronToSyntheticTree = (\n  { target, rxn, children }: RetronSyntheticRoute,\n  parent?: string\n): SyntheticTree & { rxn?: string } => {\n  const allChildrenSmiles = rxn?.split('>>')[0]?.split('.')\n  if (!allChildrenSmiles) return { value: target, parent }\n\n  const childrenRoutesMapBySmiles = groupBy(children, ({ target }) => target)\n  return {\n    value: target,\n    children: allChildrenSmiles.map((smiles) => {\n      if (childrenRoutesMapBySmiles[smiles]?.length) {\n        return retronToSyntheticTree(\n          childrenRoutesMapBySmiles[smiles][0],\n          target\n        )\n      }\n      return { value: smiles, parent: target }\n    }),\n    parent,\n    rxn\n  }\n}\n\nexport const divideTreeChildrenByIfHasChildren = (\n  children: SyntheticTree[] = []\n): [SyntheticTree[], SyntheticTree[]] =>\n  children.reduce<[SyntheticTree[], SyntheticTree[]]>(\n    (acc, cur) => {\n      if (cur.children?.length) {\n        acc[0].push(cur)\n      } else {\n        acc[1].push(cur)\n      }\n      return acc\n    },\n    [[], []]\n  )\n\nexport const calSyntheticStep = (tree: SyntheticTree): number => {\n  if (!tree.children?.length) return 0\n  return tree.children.reduce((acc, cur) => acc + calSyntheticStep(cur), 0) + 1\n}\n\nexport const calHasBranch = ({ children }: SyntheticTree): boolean => {\n  if (!children?.length) return false\n  const nOfChildrenWithChildren = children.reduce(\n    (acc, cur) => acc + (cur.children?.length ? 1 : 0),\n    0\n  )\n  if (nOfChildrenWithChildren >= 2) return true\n  return children.reduce((acc, cur) => acc || calHasBranch(cur), false)\n}\n"], "names": ["ExperimentFilled", "props", "ref", "AntdIcon", "RefIcon", "EyeOutlined", "FieldTimeOutlined", "MessageOutlined", "StarOutlined", "PlusOutlined", "_excluded", "_excluded2", "EditableTableActionContext", "RecordCreator", "children", "record", "position", "newRecordType", "parent<PERSON><PERSON>", "actionRef", "_onClick", "_callee", "e", "_children$props$onCli", "_children$props", "_actionRef$current", "isOk", "_context", "onClick", "_x", "EditableTable", "_props$editable2", "_props$editable4", "intl", "onTableChange", "max<PERSON><PERSON><PERSON>", "formItemProps", "recordCreatorProps", "<PERSON><PERSON><PERSON>", "controlled", "defaultValue", "onChange", "editableFormRef", "rest", "preData", "formRef", "_useMergedState", "useMergedState", "_useMergedState2", "value", "setValue", "getRowKey", "index", "coverRowKey", "useRefFunction", "finlayRowKey", "rowData", "_rowIndex", "item", "_getRow<PERSON>ey", "getRowData", "rowIndex", "_finlayRowKey$toStrin", "_formRef$current", "rowKeyName", "getRowsData", "_formRef$current3", "_formRef$current2", "key", "data", "_finlayRowKey$toStrin2", "_formRef$current4", "newRowData", "updateValues", "set", "current", "_formRef$current5", "stringify", "_props$editable", "_ref", "creatorButtonText", "style", "restButtonProps", "isTop", "creatorButtonDom", "runFunction", "buttonRenderProps", "_ref2", "_rest$columns", "className", "_", "dom", "_props$tableViewRende", "_props$tableViewRende2", "editableProps", "newOnValueChange", "r", "dataSource", "_props$editable3", "_props$editable3$onVa", "_props$onValuesChange", "_props$onChange", "_formRef$current6", "newValue", "changeValue", "_props$editable5", "_props$editable5$onVa", "list", "get", "changeItem", "_preData$current", "isDeepEqualReact", "FieldEditableTable", "form", "prev", "next", "name", "error", "Arrow", "_ref$color", "color", "_jsxs", "viewBox", "width", "height", "transform", "_jsx", "id", "markerUnits", "marker<PERSON>id<PERSON>", "markerHeight", "refX", "refY", "orient", "fill", "d", "x1", "y1", "x2", "y2", "strokeWidth", "stroke", "markerEnd", "ButtonWithLoading", "_objectWithoutProperties", "_useState", "useState", "_useState2", "_slicedToArray", "loading", "setLoading", "onClickHandler", "_asyncToGenerator", "_regeneratorRuntime", "mark", "event", "result", "wrap", "sent", "abrupt", "t0", "stop", "apply", "arguments", "<PERSON><PERSON>", "_objectSpread", "commonStatusMap", "created", "editing", "started", "holding", "confirmed", "finished", "cancelled", "canceled", "running", "hold", "completed", "success", "failed", "todo", "checking", "StatusRender", "status", "colorMap", "labelPrefix", "<PERSON><PERSON><PERSON><PERSON>", "label", "getWord", "concat", "Tag", "useOptions", "moleculeStatusOptions", "reactionRoleOptions", "groupOptions", "proportionOptions", "sortStandard", "createdAt", "updatedAt", "no", "typeMap", "target", "building_block", "temp_block", "typeMapForSelect", "omit", "editableConfig", "onlyOneLineEditorAlertMessage", "onlyAddOneLineAlertMessage", "chargeDes", "total_cost", "material_cost", "labor_cost", "materialManageStauts", "draft", "published", "deleted", "aiAIInferenceStauts", "fail", "processing", "aiGenerateStauts", "limited", "pending", "robotStatus", "working", "idle", "EnumSwitcher", "currentValue", "avalibleValues", "onSelect", "_ref$valueRender", "valueRender", "s", "_Fragment", "length", "menuItems", "map", "v", "Dropdown", "menu", "items", "info", "trigger", "Space", "DownOutlined", "OtherQuoteModal", "_access$authCodeList", "onFinish", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initalValues", "weighUnit", "setWeighUnit", "access", "useAccess", "ModalForm", "layout", "title", "authCodeList", "includes", "autoFocusFirstInput", "modalProps", "destroyOnClose", "onCancel", "console", "log", "submitTimeout", "values", "target_weight", "target_unit", "Row", "Col", "span", "ProFormText", "rules", "required", "labelCol", "wrapperCol", "disabled", "ProFormDigit", "addonAfter", "Select", "Option", "min", "max", "ConfirmModal", "openEvent", "_useModalBase", "useModalBase", "dialogProps", "confirm", "diffCostData", "confirmDiff", "columns", "dataIndex", "isEN", "ellipsis", "tableConfig", "bordered", "pagination", "repeatedConfirm", "ModalBase", "styles", "confirmModal", "onConfirm", "CustomTable", "Supplier", "supplierDatas", "confirmSupplierDatas", "_useOptions", "_Form$useForm", "Form", "useForm", "_Form$useForm2", "setDataSource", "_useState3", "_useState4", "editable<PERSON><PERSON>s", "setEditableRowKeys", "_useState5", "_useState6", "isEditType", "setIsEditType", "changeEditorType", "_isEditorType", "isValidArray", "renderQuantity", "quantity", "unit", "readonly", "valueType", "renderText", "text", "_record$material_lib", "_record$material_lib2", "source_link", "window", "open", "material_lib", "version", "renderFormItem", "entity", "render", "initialValue", "valueEnum", "price", "isNaN", "toFixed", "unit_price", "message", "pattern", "sum", "count", "roundToTwoDecimalPlaces", "onOpenChange", "submitter", "resetButtonProps", "display", "submitButtonProps", "undefined", "searchConfig", "submitText", "<PERSON><PERSON><PERSON><PERSON>", "EditableProTable", "scroll", "y", "editable", "type", "onValuesChange", "_record", "recordList", "QuoteMaterialTable", "quoteMoleculeId", "projectReactionId", "isQuotation", "location", "pathname", "_useModel", "useModel", "updateQuotes", "materialData", "handleSaveQuoteData", "successCb", "res", "project_reaction_id", "_x2", "saveEditItem", "_callee2", "isAdd", "newDataSource", "params", "_context2", "role", "cas_no", "detail", "smi", "name_zh", "name_en", "equivalent", "required_quantity", "set_cost", "material_no", "gPerMol", "action", "_x3", "_x4", "_x5", "getSuppliers", "_ref3", "_callee3", "_newSmiles", "supplierInfo", "getTargetValue", "_gPerMol", "editedData", "_context3", "cloneDeep", "fetchSuppliers", "targetItem", "find", "push", "_x6", "_x7", "_x8", "_ref4", "_info$entity", "_info$entity2", "isEditable", "smiles", "step_no", "SmilesInput", "multiple", "newSmiles", "isReadonlyMaterialRole", "row", "main_reactant", "reactant", "other_reagent", "solvent", "hideInTable", "materialUnitValueEnum", "digitDes", "cost", "isEmpty", "Popover", "content", "HelpIcon", "cursor", "setColumns", "hiddeStep", "_columns", "unshift", "fixed", "_text", "href", "operate", "_action$startEditable", "_toConsumableArray", "startEditable", "call", "_ref5", "_callee4", "_context4", "_x9", "_useState7", "_useState8", "<PERSON><PERSON><PERSON>", "x", "enableEdit", "enableAddMaterial", "uuidv4", "slice", "actionRender", "_row", "_config", "defaultDom", "save", "cancel", "onSave", "_onSave", "_callee5", "_row<PERSON>ey", "_context5", "_x10", "_x11", "onDelete", "_onDelete", "_callee6", "_context6", "_x12", "_x13", "EditorDialog", "init", "onClose", "_ref6", "_callee7", "_context7", "_x14", "MaterialList", "_props$quotesDetail3", "curQuoteMoleculeId", "allMaterials", "setAllMaterials", "handleSave", "handleMaterialsStepId", "newItem", "materials", "for<PERSON>ach", "_newItem$step_info", "_newItem$step_info2", "step_info", "step_id", "getAllMaterials", "_props$quotesDetail", "_materials", "_costDetail", "quotesDetail", "cost_detail", "_props$quotesDetail2", "SectionTitle", "anchorId", "word", "StepButton", "route_index", "routesNum", "handleStepChange", "cs", "none", "MyReaction", "_step_info$procedure3", "_step_info$reaction_i", "_access$authCodeList2", "_step_info$procedure4", "_step_info$procedure5", "_step_info$procedure6", "_step_info$procedure7", "_step_info$procedure8", "projectCompoundId", "routeId", "_useParams", "useParams", "projectId", "_props$costDetail", "costDetail", "labor", "_ProForm$useForm", "ProForm", "_ProForm$useForm2", "_App$useApp", "App", "useApp", "_step_info$procedure", "_step_info$procedure2", "yields", "procedure", "confirmLoading", "setConfirmLoading", "yieldLoading", "setYieldLoading", "yieldConfirm", "setYieldConfirm", "yieldTip", "setYieldTip", "_useState9", "_useState10", "canEditLabor", "setCanEditLabor", "_useState11", "_useState12", "canEditLaborYield", "setCanEditLaborYield", "_useState13", "_useState14", "cacheYields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactionNum", "reaction_ids", "step", "reaction_from_index", "new<PERSON><PERSON>s", "getFieldValue", "toInt", "setFieldValue", "step<PERSON>itle", "rxn", "MoleculeStructure", "structure", "openLinkWrapper", "reactionLink", "warning", "sp", "URLSearchParams", "toString", "fontWeight", "reference<PERSON><PERSON>nt", "referenceStructure", "grid", "colProps", "xs", "sm", "md", "lg", "offset", "extra", "Popconfirm", "description", "overlayStyle", "_yield$service$create", "service", "method", "create", "laborEditor", "right", "new<PERSON><PERSON><PERSON>", "Number", "parseFloat", "ProcedureText", "rows", "reference_type", "procedureReference", "Schedule", "handleLaborSum", "toNumber", "_record$step_info", "_record$step_info2", "Summary", "_props$quotesDetail5", "_props$quotesDetail6", "quoteSummaryData", "setQuoteSummaryData", "chargeOptions", "setChargeOptions", "newItemName", "setNewItemName", "handleDisabledOption", "_chargeOptions", "targetLabel", "enable", "newChargeOptions", "targetOption", "getNewItemName", "curQuoteSummaryData", "newName", "alreadySelectedOther", "findIndex", "alreadySelectedSeparation", "getChargeOptions", "defaultDataSource", "finalData", "cloneData", "query", "filterDeep", "isArray", "code", "cur", "handleDataID", "originData", "cost_summary", "newData", "fieldProps", "_form", "_props$quotesDetail4", "options", "filter", "other_costs", "RMB", "quotationSummaryDetail", "delivery_time", "marginBottom", "quotation_summary", "Math", "random", "findDiffCosts", "diffCosts", "i", "j", "_costDetail$i", "_costDetail$i2", "curItem", "cost_modified", "_costDetail$i3", "minCost", "QuoteDetail", "_access$authCodeList3", "_quotesDetail$cost_de", "initialState", "_useSearchParams", "useSearchParams", "_useSearchParams2", "searchParams", "_useModel2", "updateQuotesDetail", "createQuote", "updatetDiffCosts", "_useModel3", "getUserConfigs", "fetchQuoteLoading", "setFetchQuoteLoading", "quoteType", "compound_no", "isCreateType", "setCurQuoteMoleculeId", "setRoutesNum", "conrifrmRoutes", "setConrifrmRoutes", "isEditorType", "setIsEditorType", "disabledEditInConfirmedStatus", "getQuotationRecords", "_yield$query$get", "meta", "total", "getDiffCosts", "_diffCosts", "isNil", "toEditQuote", "_curQuoteMoleculeId", "history", "replace", "getQuoteDetail", "_data$quote_route", "_yield$query$get2", "quote_route", "useRef", "purity", "FTE_unit_price", "ratio", "project_compound_id", "curRouteId", "setCurRouteId", "_useState15", "_useState16", "createRouteIndex", "setCreateRouteIndex", "_useState17", "_useState18", "setOpenEvent", "isConfirmedQuote", "canSaveDraft", "canConfirm", "handelQuoteStatus", "quoteStatus", "formValues", "_yield$updateQuotes", "validateFields", "project_id", "fetchData", "getDefaultConfigs", "quoteDefaultSettings", "_quoteDefaultSettings", "_quoteDefaultSettings2", "setting_label", "setting_value", "FTE_rate", "selectAfter", "unitSelect", "_quotesDetail$quote_r", "nextQuoteIndex", "requestQuoteId", "_yield$query$get3", "quote_id", "pushState", "origin", "handleCreateStepChange", "_ref7", "commonAnchor", "RouteItem", "_ref8", "<PERSON><PERSON><PERSON><PERSON>", "route", "routeIndex", "project_route_id", "Typography", "Link", "SyntheticRouteCard", "hiddenTitle", "RenderRoutes", "_useState19", "_useState20", "repeatedWeight", "setRepeatedWeight", "_useState21", "_useState22", "quotationNo", "setQuotationNo", "createQuoteCB", "isOtherQuote", "_repeatedWeight", "_quotationNo", "_useState23", "_useState24", "createLoading", "setCreateLoading", "createNewQuote", "then", "_ref9", "_callee8", "_context8", "route_id", "repeatedWeightConfirm", "_ref10", "_callee9", "_context9", "apiDelByQuoteNo", "routeParams", "parseResponseResult", "ok", "quoteInfo", "_callee10", "_context10", "<PERSON><PERSON><PERSON><PERSON>", "quoteDetail", "_defineProperty", "isMenuCollapsed", "initialValues", "top", "_callee11", "_formRef$current7", "_formRef$current8", "_context11", "operateButton", "routeInfo", "flex", "CustomAnchor", "wrapClassName", "anchorEN", "anchor", "fontSize", "calTreeDeepth", "_ref$children", "child", "reduce", "acc", "getMainRoute", "mainMaterial", "_divideTreeChildrenBy", "divideTreeChildrenByIfHasChildren", "_divideTreeChildrenBy2", "intermediates", "longest", "mainMaterialTree", "c", "main", "others", "intermediateWithLongestPath", "syntheticTreeToLink", "_ref3$children", "path", "_getMainRoute", "_getMainRoute2", "mainRoute", "currentPath", "join", "backboneToLink", "backbone", "reduceRight", "routeTreeToSyntheticTree", "parent", "retronToSyntheticTree", "_rxn$split$", "allChildrenSmiles", "split", "childrenRoutesMapBySmiles", "groupBy", "_childrenRoutesMapByS", "_cur$children", "calSyntheticStep", "tree", "_tree$children", "calHasBranch", "nOfChildrenWithChildren", "_cur$children2"], "sourceRoot": ""}