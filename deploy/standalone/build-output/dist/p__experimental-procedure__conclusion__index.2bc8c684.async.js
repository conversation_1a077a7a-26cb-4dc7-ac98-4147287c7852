"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[8175],{34804:function(We,le,e){var J=e(1413),u=e(67294),X=e(66023),c=e(84089),R=function(T,r){return u.createElement(c.Z,(0,J.Z)((0,J.Z)({},T),{},{ref:r,icon:X.Z}))},d=u.forwardRef(R);le.Z=d},26704:function(We,le,e){e.d(le,{uk:function(){return q},ZP:function(){return P}});var J=e(1413),u=e(45987),X=e(67294),c=e(48054),R=e(4393),d=e(25378),me=e(96074),T=e(42075),r=e(85893),ae=function(t){var m=t.padding;return(0,r.jsx)("div",{style:{padding:m||"0 24px"},children:(0,r.jsx)(me.Z,{style:{margin:0}})})},ye={xs:2,sm:2,md:4,lg:4,xl:6,xxl:6},ce=function(t){var m=t.size,n=t.active,x=(0,X.useMemo)(function(){return{lg:!0,md:!0,sm:!1,xl:!1,xs:!1,xxl:!1}},[]),j=(0,d.Z)()||x,E=Object.keys(j).filter(function($){return j[$]===!0})[0]||"md",Z=m===void 0?ye[E]||6:m,z=function(B){return B===0?0:Z>2?42:16};return(0,r.jsx)(R.Z,{bordered:!1,style:{marginBlockEnd:16},children:(0,r.jsx)("div",{style:{width:"100%",justifyContent:"space-between",display:"flex"},children:new Array(Z).fill(null).map(function($,B){return(0,r.jsxs)("div",{style:{borderInlineStart:Z>2&&B===1?"1px solid rgba(0,0,0,0.06)":void 0,paddingInlineStart:z(B),flex:1,marginInlineEnd:B===0?16:0},children:[(0,r.jsx)(c.Z,{active:n,paragraph:!1,title:{width:100,style:{marginBlockStart:0}}}),(0,r.jsx)(c.Z.Button,{active:n,style:{height:48}})]},B)})})})},l=function(t){var m=t.active;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(R.Z,{bordered:!1,style:{borderRadius:0},styles:{body:{padding:24}},children:(0,r.jsxs)("div",{style:{width:"100%",display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,r.jsx)("div",{style:{maxWidth:"100%",flex:1},children:(0,r.jsx)(c.Z,{active:m,title:{width:100,style:{marginBlockStart:0}},paragraph:{rows:1,style:{margin:0}}})}),(0,r.jsx)(c.Z.Button,{active:m,size:"small",style:{width:165,marginBlockStart:12}})]})}),(0,r.jsx)(ae,{})]})},je=function(t){var m=t.size,n=t.active,x=n===void 0?!0:n,j=t.actionButton;return(0,r.jsxs)(R.Z,{bordered:!1,styles:{body:{padding:0}},children:[new Array(m).fill(null).map(function(E,Z){return(0,r.jsx)(l,{active:!!x},Z)}),j!==!1&&(0,r.jsx)(R.Z,{bordered:!1,style:{borderStartEndRadius:0,borderTopLeftRadius:0},styles:{body:{display:"flex",alignItems:"center",justifyContent:"center"}},children:(0,r.jsx)(c.Z.Button,{style:{width:102},active:x,size:"small"})})]})},Q=function(t){var m=t.active;return(0,r.jsxs)("div",{style:{marginBlockEnd:16},children:[(0,r.jsx)(c.Z,{paragraph:!1,title:{width:185}}),(0,r.jsx)(c.Z.Button,{active:m,size:"small"})]})},he=function(t){var m=t.active;return(0,r.jsx)(R.Z,{bordered:!1,style:{borderBottomRightRadius:0,borderBottomLeftRadius:0},styles:{body:{paddingBlockEnd:8}},children:(0,r.jsxs)(T.Z,{style:{width:"100%",justifyContent:"space-between"},children:[(0,r.jsx)(c.Z.Button,{active:m,style:{width:200},size:"small"}),(0,r.jsxs)(T.Z,{children:[(0,r.jsx)(c.Z.Button,{active:m,size:"small",style:{width:120}}),(0,r.jsx)(c.Z.Button,{active:m,size:"small",style:{width:80}})]})]})})},be=function(t){var m=t.active,n=m===void 0?!0:m,x=t.statistic,j=t.actionButton,E=t.toolbar,Z=t.pageHeader,z=t.list,$=z===void 0?5:z;return(0,r.jsxs)("div",{style:{width:"100%"},children:[Z!==!1&&(0,r.jsx)(Q,{active:n}),x!==!1&&(0,r.jsx)(ce,{size:x,active:n}),(E!==!1||$!==!1)&&(0,r.jsxs)(R.Z,{bordered:!1,styles:{body:{padding:0}},children:[E!==!1&&(0,r.jsx)(he,{active:n}),$!==!1&&(0,r.jsx)(je,{size:$,active:n,actionButton:j})]})]})},Ee=be,de={xs:1,sm:2,md:3,lg:3,xl:3,xxl:4},pe=function(t){var m=t.active;return(0,r.jsxs)("div",{style:{marginBlockStart:32},children:[(0,r.jsx)(c.Z.Button,{active:m,size:"small",style:{width:100,marginBlockEnd:16}}),(0,r.jsxs)("div",{style:{width:"100%",justifyContent:"space-between",display:"flex"},children:[(0,r.jsxs)("div",{style:{flex:1,marginInlineEnd:24,maxWidth:300},children:[(0,r.jsx)(c.Z,{active:m,paragraph:!1,title:{style:{marginBlockStart:0}}}),(0,r.jsx)(c.Z,{active:m,paragraph:!1,title:{style:{marginBlockStart:8}}}),(0,r.jsx)(c.Z,{active:m,paragraph:!1,title:{style:{marginBlockStart:8}}})]}),(0,r.jsx)("div",{style:{flex:1,alignItems:"center",justifyContent:"center"},children:(0,r.jsxs)("div",{style:{maxWidth:300,margin:"auto"},children:[(0,r.jsx)(c.Z,{active:m,paragraph:!1,title:{style:{marginBlockStart:0}}}),(0,r.jsx)(c.Z,{active:m,paragraph:!1,title:{style:{marginBlockStart:8}}})]})})]})]})},Ze=function(t){var m=t.size,n=t.active,x=(0,X.useMemo)(function(){return{lg:!0,md:!0,sm:!1,xl:!1,xs:!1,xxl:!1}},[]),j=(0,d.Z)()||x,E=Object.keys(j).filter(function(z){return j[z]===!0})[0]||"md",Z=m===void 0?de[E]||3:m;return(0,r.jsx)("div",{style:{width:"100%",justifyContent:"space-between",display:"flex"},children:new Array(Z).fill(null).map(function(z,$){return(0,r.jsxs)("div",{style:{flex:1,paddingInlineStart:$===0?0:24,paddingInlineEnd:$===Z-1?0:24},children:[(0,r.jsx)(c.Z,{active:n,paragraph:!1,title:{style:{marginBlockStart:0}}}),(0,r.jsx)(c.Z,{active:n,paragraph:!1,title:{style:{marginBlockStart:8}}}),(0,r.jsx)(c.Z,{active:n,paragraph:!1,title:{style:{marginBlockStart:8}}})]},$)})})},g=function(t){var m=t.active,n=t.header,x=n===void 0?!1:n,j=(0,X.useMemo)(function(){return{lg:!0,md:!0,sm:!1,xl:!1,xs:!1,xxl:!1}},[]),E=(0,d.Z)()||j,Z=Object.keys(E).filter(function($){return E[$]===!0})[0]||"md",z=de[Z]||3;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{style:{display:"flex",background:x?"rgba(0,0,0,0.02)":"none",padding:"24px 8px"},children:[new Array(z).fill(null).map(function($,B){return(0,r.jsx)("div",{style:{flex:1,paddingInlineStart:x&&B===0?0:20,paddingInlineEnd:32},children:(0,r.jsx)(c.Z,{active:m,paragraph:!1,title:{style:{margin:0,height:24,width:x?"75px":"100%"}}})},B)}),(0,r.jsx)("div",{style:{flex:3,paddingInlineStart:32},children:(0,r.jsx)(c.Z,{active:m,paragraph:!1,title:{style:{margin:0,height:24,width:x?"75px":"100%"}}})})]}),(0,r.jsx)(ae,{padding:"0px 0px"})]})},L=function(t){var m=t.active,n=t.size,x=n===void 0?4:n;return(0,r.jsxs)(R.Z,{bordered:!1,children:[(0,r.jsx)(c.Z.Button,{active:m,size:"small",style:{width:100,marginBlockEnd:16}}),(0,r.jsx)(g,{header:!0,active:m}),new Array(x).fill(null).map(function(j,E){return(0,r.jsx)(g,{active:m},E)}),(0,r.jsx)("div",{style:{display:"flex",justifyContent:"flex-end",paddingBlockStart:16},children:(0,r.jsx)(c.Z,{active:m,paragraph:!1,title:{style:{margin:0,height:32,float:"right",maxWidth:"630px"}}})})]})},Le=function(t){var m=t.active;return(0,r.jsxs)(R.Z,{bordered:!1,style:{borderStartEndRadius:0,borderTopLeftRadius:0},children:[(0,r.jsx)(c.Z.Button,{active:m,size:"small",style:{width:100,marginBlockEnd:16}}),(0,r.jsx)(Ze,{active:m}),(0,r.jsx)(pe,{active:m})]})},N=function(t){var m=t.active,n=m===void 0?!0:m,x=t.pageHeader,j=t.list;return(0,r.jsxs)("div",{style:{width:"100%"},children:[x!==!1&&(0,r.jsx)(Q,{active:n}),(0,r.jsx)(Le,{active:n}),j!==!1&&(0,r.jsx)(ae,{}),j!==!1&&(0,r.jsx)(L,{active:n,size:j})]})},Re=N,Y=function(t){var m=t.active,n=m===void 0?!0:m,x=t.pageHeader;return(0,r.jsxs)("div",{style:{width:"100%"},children:[x!==!1&&(0,r.jsx)(Q,{active:n}),(0,r.jsx)(R.Z,{children:(0,r.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column",padding:128},children:[(0,r.jsx)(c.Z.Avatar,{size:64,style:{marginBlockEnd:32}}),(0,r.jsx)(c.Z.Button,{active:n,style:{width:214,marginBlockEnd:8}}),(0,r.jsx)(c.Z.Button,{active:n,style:{width:328},size:"small"}),(0,r.jsxs)(T.Z,{style:{marginBlockStart:24},children:[(0,r.jsx)(c.Z.Button,{active:n,style:{width:116}}),(0,r.jsx)(c.Z.Button,{active:n,style:{width:116}})]})]})})]})},ge=Y,Pe=["type"],q=function(t){var m=t.type,n=m===void 0?"list":m,x=(0,u.Z)(t,Pe);return n==="result"?(0,r.jsx)(ge,(0,J.Z)({},x)):n==="descriptions"?(0,r.jsx)(Re,(0,J.Z)({},x)):(0,r.jsx)(Ee,(0,J.Z)({},x))},P=q},88280:function(We,le,e){e.d(le,{Z:function(){return m}});var J=e(4942),u=e(97685),X=e(45987),c=e(74165),R=e(15861),d=e(1413),me=e(87462),T=e(67294),r=e(42110),ae=e(43445),ye=function(x,j){return T.createElement(ae.Z,(0,me.Z)({},x,{ref:j,icon:r.Z}))},ce=T.forwardRef(ye),l=ce,je=e(97462),Q=e(952),he=e(10915),be=e(48171),Ee=e(53914),de=e(22270),pe=e(60249),Ze=e(28036),g=e(98138),L=e(21770),Le=e(88306),N=e(8880),Re=e(56337),Y=e(85893),ge=["onTableChange","maxLength","formItemProps","recordCreatorProps","rowKey","controlled","defaultValue","onChange","editableFormRef"],Pe=["record","position","creatorButtonText","newRecordType","parentKey","style"],q=T.createContext(void 0);function P(n){var x=n.children,j=n.record,E=n.position,Z=n.newRecordType,z=n.parentKey,$=(0,T.useContext)(q);return T.cloneElement(x,(0,d.Z)((0,d.Z)({},x.props),{},{onClick:function(){var B=(0,R.Z)((0,c.Z)().mark(function ve(we){var Ne,_,ue,$e;return(0,c.Z)().wrap(function(re){for(;;)switch(re.prev=re.next){case 0:return re.next=2,(Ne=(_=x.props).onClick)===null||Ne===void 0?void 0:Ne.call(_,we);case 2:if($e=re.sent,$e!==!1){re.next=5;break}return re.abrupt("return");case 5:$==null||(ue=$.current)===null||ue===void 0||ue.addEditRecord(j,{position:E,newRecordType:Z,parentKey:z});case 6:case"end":return re.stop()}},ve)}));function Se(ve){return B.apply(this,arguments)}return Se}()}))}function v(n){var x,j,E=(0,he.YB)(),Z=n.onTableChange,z=n.maxLength,$=n.formItemProps,B=n.recordCreatorProps,Se=n.rowKey,ve=n.controlled,we=n.defaultValue,Ne=n.onChange,_=n.editableFormRef,ue=(0,X.Z)(n,ge),$e=(0,T.useRef)(void 0),Te=(0,T.useRef)(),re=(0,T.useRef)();(0,T.useImperativeHandle)(ue.actionRef,function(){return Te.current},[Te.current]);var Fe=(0,L.Z)(function(){return n.value||we||[]},{value:n.value,onChange:n.onChange}),nt=(0,u.Z)(Fe,2),V=nt[0],dt=nt[1],ze=T.useMemo(function(){return typeof Se=="function"?Se:function(M,ee){return M[Se]||ee}},[Se]),Xe=(0,be.J)(function(M){if(typeof M=="number"&&!n.name){if(M>=V.length)return M;var ee=V&&V[M];return ze==null?void 0:ze(ee,M)}if((typeof M=="string"||M>=V.length)&&n.name){var I=V.findIndex(function(D,F){var G;return(ze==null||(G=ze(D,F))===null||G===void 0?void 0:G.toString())===(M==null?void 0:M.toString())});if(I!==-1)return I}return M});(0,T.useImperativeHandle)(_,function(){var M=function(D){var F,G;if(D==null)throw new Error("rowIndex is required");var xe=Xe(D),Ce=[n.name,(F=xe==null?void 0:xe.toString())!==null&&F!==void 0?F:""].flat(1).filter(Boolean);return(G=re.current)===null||G===void 0?void 0:G.getFieldValue(Ce)},ee=function(){var D,F=[n.name].flat(1).filter(Boolean);if(Array.isArray(F)&&F.length===0){var G,xe=(G=re.current)===null||G===void 0?void 0:G.getFieldsValue();return Array.isArray(xe)?xe:Object.keys(xe).map(function(Ce){return xe[Ce]})}return(D=re.current)===null||D===void 0?void 0:D.getFieldValue(F)};return(0,d.Z)((0,d.Z)({},re.current),{},{getRowData:M,getRowsData:ee,setRowData:function(D,F){var G,xe;if(D==null)throw new Error("rowIndex is required");var Ce=Xe(D),ke=[n.name,(G=Ce==null?void 0:Ce.toString())!==null&&G!==void 0?G:""].flat(1).filter(Boolean),it=Object.assign({},(0,d.Z)((0,d.Z)({},M(D)),F||{})),lt=(0,N.Z)({},ke,it);return(xe=re.current)===null||xe===void 0||xe.setFieldsValue(lt),!0}})},[Xe,n.name,re.current]),(0,T.useEffect)(function(){n.controlled&&(V||[]).forEach(function(M,ee){var I;(I=re.current)===null||I===void 0||I.setFieldsValue((0,J.Z)({},"".concat(ze(M,ee)),M))},{})},[(0,Ee.ZP)(V),n.controlled]),(0,T.useEffect)(function(){if(n.name){var M;re.current=n==null||(M=n.editable)===null||M===void 0?void 0:M.form}},[(x=n.editable)===null||x===void 0?void 0:x.form,n.name]);var Ke=B||{},Ve=Ke.record,at=Ke.position,ut=Ke.creatorButtonText,Ie=Ke.newRecordType,rt=Ke.parentKey,ct=Ke.style,mt=(0,X.Z)(Ke,Pe),Qe=at==="top",Me=(0,T.useMemo)(function(){return typeof z=="number"&&z<=(V==null?void 0:V.length)?!1:B!==!1&&(0,Y.jsx)(P,{record:(0,de.h)(Ve,V==null?void 0:V.length,V)||{},position:at,parentKey:(0,de.h)(rt,V==null?void 0:V.length,V),newRecordType:Ie,children:(0,Y.jsx)(Ze.ZP,(0,d.Z)((0,d.Z)({type:"dashed",style:(0,d.Z)({display:"block",margin:"10px 0",width:"100%"},ct),icon:(0,Y.jsx)(l,{})},mt),{},{children:ut||E.getMessage("editableTable.action.add","\u6DFB\u52A0\u4E00\u884C\u6570\u636E")}))})},[B,z,V==null?void 0:V.length]),qe=(0,T.useMemo)(function(){return Me?Qe?{components:{header:{wrapper:function(ee){var I,D=ee.className,F=ee.children;return(0,Y.jsxs)("thead",{className:D,children:[F,(0,Y.jsxs)("tr",{style:{position:"relative"},children:[(0,Y.jsx)("td",{colSpan:0,style:{visibility:"hidden"},children:Me}),(0,Y.jsx)("td",{style:{position:"absolute",left:0,width:"100%"},colSpan:(I=ue.columns)===null||I===void 0?void 0:I.length,children:Me})]})]})}}}}:{tableViewRender:function(ee,I){var D,F;return(0,Y.jsxs)(Y.Fragment,{children:[(D=(F=n.tableViewRender)===null||F===void 0?void 0:F.call(n,ee,I))!==null&&D!==void 0?D:I,Me]})}}:{}},[Qe,Me]),_e=(0,d.Z)({},n.editable),vt=(0,be.J)(function(M,ee){var I,D,F;if((I=n.editable)===null||I===void 0||(D=I.onValuesChange)===null||D===void 0||D.call(I,M,ee),(F=n.onValuesChange)===null||F===void 0||F.call(n,ee,M),n.controlled){var G;n==null||(G=n.onChange)===null||G===void 0||G.call(n,ee)}});return(n!=null&&n.onValuesChange||(j=n.editable)!==null&&j!==void 0&&j.onValuesChange||n.controlled&&n!==null&&n!==void 0&&n.onChange)&&(_e.onValuesChange=vt),(0,Y.jsxs)(Y.Fragment,{children:[(0,Y.jsx)(q.Provider,{value:Te,children:(0,Y.jsx)(Re.Z,(0,d.Z)((0,d.Z)((0,d.Z)({search:!1,options:!1,pagination:!1,rowKey:Se,revalidateOnFocus:!1},ue),qe),{},{tableLayout:"fixed",actionRef:Te,onChange:Z,editable:(0,d.Z)((0,d.Z)({},_e),{},{formProps:(0,d.Z)({formRef:re},_e.formProps)}),dataSource:V,onDataSourceChange:function(ee){if(dt(ee),n.name&&at==="top"){var I,D=(0,N.Z)({},[n.name].flat(1).filter(Boolean),ee);(I=re.current)===null||I===void 0||I.setFieldsValue(D)}}}))}),n.name?(0,Y.jsx)(je.Z,{name:[n.name],children:function(ee){var I,D;if(!$e.current)return $e.current=V,null;var F=(0,Le.Z)(ee,[n.name].flat(1)),G=F==null?void 0:F.find(function(xe,Ce){var ke;return!(0,pe.A)(xe,(ke=$e.current)===null||ke===void 0?void 0:ke[Ce])});return $e.current=V,G&&(n==null||(I=n.editable)===null||I===void 0||(D=I.onValuesChange)===null||D===void 0||D.call(I,G,F)),null}}):null]})}function t(n){var x=Q.ZP.useFormInstance();return n.name?(0,Y.jsx)(g.Z.Item,(0,d.Z)((0,d.Z)({style:{maxWidth:"100%"}},n==null?void 0:n.formItemProps),{},{name:n.name,shouldUpdate:function(E,Z){var z=[n.name].flat(1);try{return JSON.stringify((0,Le.Z)(E,z))!==JSON.stringify((0,Le.Z)(Z,z))}catch($){return!0}},children:(0,Y.jsx)(v,(0,d.Z)((0,d.Z)({tableLayout:"fixed",scroll:{x:"max-content"}},n),{},{editable:(0,d.Z)((0,d.Z)({},n.editable),{},{form:x})}))})):(0,Y.jsx)(v,(0,d.Z)({tableLayout:"fixed",scroll:{x:"max-content"}},n))}t.RecordCreator=P;var m=t},10784:function(We,le,e){var J=e(97857),u=e.n(J),X=e(15009),c=e.n(X),R=e(99289),d=e.n(R),me=e(5574),T=e.n(me),r=e(13769),ae=e.n(r),ye=e(28036),ce=e(67294),l=e(85893),je=["onClick"],Q=function(be){var Ee=be.onClick,de=ae()(be,je),pe=(0,ce.useState)(!1),Ze=T()(pe,2),g=Ze[0],L=Ze[1],Le=function(){var N=d()(c()().mark(function Re(Y){var ge;return c()().wrap(function(q){for(;;)switch(q.prev=q.next){case 0:return L(!0),q.prev=1,q.next=4,Ee==null?void 0:Ee(Y);case 4:return ge=q.sent,L(!1),q.abrupt("return",ge);case 9:return q.prev=9,q.t0=q.catch(1),L(!1),q.abrupt("return","");case 13:case"end":return q.stop()}},Re,null,[[1,9]])}));return function(Y){return N.apply(this,arguments)}}();return(0,l.jsx)(ye.ZP,u()(u()({loading:g},de),{},{onClick:Le}))};le.Z=Q},81012:function(We,le,e){e.d(le,{Z:function(){return me}});var J=e(97857),u=e.n(J),X=e(48054),c=e(67294),R=e(85893),d=(0,c.lazy)(function(){return Promise.all([e.e(6049),e.e(6369),e.e(6891)]).then(e.bind(e,99814)).then(function(T){return{default:T.default}})});function me(T){return(0,R.jsx)(c.Suspense,{fallback:(0,R.jsx)("div",{children:(0,R.jsx)(X.Z,{active:!0})}),children:(0,R.jsx)(d,u()({},T))})}},32222:function(We,le,e){e.d(le,{Z:function(){return Le}});var J=e(15009),u=e.n(J),X=e(99289),c=e.n(X),R=e(97857),d=e.n(R),me=e(5574),T=e.n(me),r=e(42282),ae=e(43851),ye=e(69776),ce=e(64134),l=e(32884),je=e(34994),Q=e(5155),he=e(24739),be=e(64317),Ee=e(5966),de=e(31199),pe=e(98138),Ze=e(67294),g={"filter-form-root":"filter-form-root___idETP","expand-btn-wrapper":"expand-btn-wrapper___mC0Md","confirm-col":"confirm-col___fQPHq","re-retro-btn":"re-retro-btn___chR6c"},L=e(85893);function Le(N){var Re=pe.Z.useForm(),Y=T()(Re,1),ge=Y[0],Pe=(0,ye.Z)(),q=Pe.reactionRoleOptions,P=(0,Ze.useState)([]),v=T()(P,2),t=v[0],m=v[1];(0,Ze.useEffect)(function(){var j,E=N==null||(j=N.material_table)===null||j===void 0?void 0:j.filter(function(Z){return(Z==null?void 0:Z.role)!=="product"});m(E)},[N==null?void 0:N.material_table]),(0,Ze.useEffect)(function(){t&&ge.setFieldsValue({material_table:t})},[t]);var n=location.pathname.includes("experimental-procedure/conclusion"),x=function(E){return E.map(function(Z){return d()(d()({},Z),{},{disabled:(0,ce.J3)(Z==null?void 0:Z.value)})})};return(0,L.jsx)(je.A,{submitter:N!=null&&N.enableAdd?{onSubmit:function(){var j=c()(u()().mark(function Z(){var z;return u()().wrap(function(B){for(;;)switch(B.prev=B.next){case 0:return B.next=2,ge.validateFields();case 2:z=B.sent,N==null||N.updateMaterial(z==null?void 0:z.material_table);case 4:case"end":return B.stop()}},Z)}));function E(){return j.apply(this,arguments)}return E}(),resetButtonProps:{style:{display:"none"}}}:!1,form:ge,children:(0,L.jsx)(Q.u,{name:"material_table",label:(0,l.oz)("material-sheet"),deleteIconProps:N==null?void 0:N.enableAdd,creatorButtonProps:N!=null&&N.enableAdd?{creatorButtonText:(0,l.oz)("add-raw-materials")}:!1,copyIconProps:!1,actionRender:function(E,Z,z){var $=ge.getFieldValue("material_table")[E.name];return(0,ce.J3)($.role)?[]:z},children:function(E){var Z=ge.getFieldValue("material_table")[E.name],z=(0,ce.J3)(Z.role),$=!(N!=null&&N.enableAdd);return(0,L.jsxs)(he.UW,{children:[(0,L.jsx)(be.Z,{disabled:$||z,name:"role",label:(0,l.oz)("role"),width:130,options:x(q.filter(function(B){return B.value!=="product"})),required:!0,rules:[{required:!0}]}),(0,L.jsx)(Ee.Z,{name:"no",width:90,label:(0,l.oz)("material-ID"),disabled:$||z}),(0,L.jsx)(Ee.Z,{name:"name",label:(0,l.oz)("substance-name"),width:140,disabled:$}),(0,L.jsx)(je.A.Item,{className:g["filter-form-root"],name:"smiles",label:(0,l.oz)("structural"),required:!0,rules:[{required:!0}],children:(0,L.jsx)(r.Z,{disabled:$||z,multiple:!1})}),(0,L.jsx)(de.Z,{name:"equivalent",width:185,label:(0,l.oz)("EWR"),disabled:$,required:!0,rules:[{required:!0},{pattern:/^(?!0*(\.0{1,2})?$)\d+(\.\d{1,2})?$/,message:(0,l.oz)("enter-two-decimal")}]}),n?(0,L.jsxs)(L.Fragment,{children:[(0,L.jsx)(de.Z,{name:"value",label:(0,l.oz)("expected-mass"),required:!0,disabled:!0,rules:[{required:!0},{pattern:/^(?!0*(\.0{1,2})?$)\d+(\.\d{1,2})?$/,message:(0,l.oz)("enter-two-decimal")}]}),(0,L.jsx)(de.Z,{name:"real_value",label:(0,l.oz)("actual-mass"),required:!0,disabled:!0,rules:[{required:!0},{pattern:/^(?!0*(\.0{1,2})?$)\d+(\.\d{1,2})?$/,message:(0,l.oz)("enter-two-decimal")}]})]}):"",(0,L.jsx)(be.Z,{name:"unit",label:(0,l.oz)("unit"),width:130,disabled:n||$,options:ae.Pt,required:!0,rules:[{required:!0}]})]},"group")}})})}},831:function(We,le,e){e.d(le,{p:function(){return X}});var J=e(97857),u=e.n(J),X=["created","running","hold","canceled","completed","failed","success"],c=function(d){return _objectSpread({experiment_no:"experiment_no",project_no:"project_no",experiment_design_no:"experiment_design_no",design_name:"design_name",experiment_name:"experiment_name",status:"created",rxn_no:"rxn_no",rxn:"rxn",start_time:new Date,end_time:new Date,owner:"owner",experiment_type:"experiment_type",predict_end_date:new Date,progress:"progress",predict_yield:"predict_yield",flow_data:"flow_data",priority:"P0"},d)}},49444:function(We,le,e){e.d(le,{Z:function(){return ae}});var J=e(97857),u=e.n(J),X=e(81012),c=e(32222),R=e(68918),d=e(17322),me=e(32884),T={structure:"structure___A9T2J"},r=e(85893);function ae(ye){var ce=ye.structure,l=ye.material_table,je=ye.dialogProps;return(0,r.jsx)("div",{onClick:function(he){return he.stopPropagation()},children:(0,r.jsxs)(R.Z,u()(u()({},je),{},{footer:null,cancelButtonProps:{hidden:!0},width:"80%",centered:!0,children:[(0,r.jsx)(d.Z,{word:(0,me.oz)("reaction")}),(0,r.jsx)(X.Z,{className:T.structure,structure:ce}),(0,r.jsx)(d.Z,{word:(0,me.oz)("pages.reaction.label.material-sheet")}),(0,r.jsx)(c.Z,{material_table:l})]}))})}},17322:function(We,le,e){e.d(le,{Z:function(){return R}});var J=e(93967),u=e.n(J),X={sectionTitle:"sectionTitle___KIteW",extraCom:"extraCom___ymouh"},c=e(85893);function R(d){return(0,c.jsxs)("div",{className:u()(X.sectionTitle,d==null?void 0:d.wrapClassName),id:d==null?void 0:d.anchorId,children:[(0,c.jsx)("h2",{children:d==null?void 0:d.word}),d!=null&&d.extra?(0,c.jsx)("div",{className:X.extraCom,children:d==null?void 0:d.extra}):null]})}},15001:function(We,le,e){var J=e(97857),u=e.n(J),X=e(32884),c=e(66309),R=e(85893),d={created:"#F5B544",editing:"#F5B544",started:"#4B9F47",holding:"#E6521F",confirmed:"#4B9F47",finished:"#1890FF",cancelled:"#979797",canceled:"#979797",running:"#2AD259",hold:"#E6521F",completed:"#1890FF",success:"#F51D2C",failed:"#9747FF",todo:"#F5B544",checking:"#4B9F47"},me=function(r){var ae=r.status,ye=r.colorMap,ce=r.labelPrefix,l=r.label,je=r.className,Q=u()(u()({},d),ye)[ae],he=l||(0,X.oz)("".concat(ce,".").concat(ae));return(0,R.jsx)(c.Z,{className:je,color:Q,children:he})};le.Z=me},72345:function(We,le,e){e.r(le),e.d(le,{default:function(){return Lt}});var J=e(15009),u=e.n(J),X=e(99289),c=e.n(X),R=e(5574),d=e.n(R),me=e(42689),T=e(831),r=e(49444),ae=e(37507),ye=e(87172),ce=e(66171),l=e(32884),je=e(11774),Q=e(18221),he=e(70831),be=e(30291),Ee=e(36569),de=e(42075),pe=e(28036),Ze=e(96074),g=e(67294),L=e(30213),Le=e(35351),N=e(14522),Re=e(49677),Y=e.n(Re),ge=e(7972),Pe=e(31418),q=e(71471),P=e(26915),v=e(51971),t=e(85893),m=function(o){var s;Y()(o);var i=(0,he.useModel)("@@initialState"),f=i.initialState,S=f==null||(s=f.userInfo)===null||s===void 0?void 0:s.username,k=Pe.Z.useApp(),b=k.message,C=(0,g.useState)(""),p=d()(C,2),U=p[0],h=p[1],te=(0,g.useState)(""),K=d()(te,2),O=K[0],ne=K[1],A=(0,g.useContext)(L.M),y=A.conclusion,H=A.refetch,Ae=A.loading,w=function(){var se=c()(u()().mark(function fe(){var He,Ye;return u()().wrap(function(oe){for(;;)switch(oe.prev=oe.next){case 0:if(He=y==null?void 0:y.experiment_no,He){oe.next=3;break}return oe.abrupt("return");case 3:if(U){oe.next=7;break}ne("\u672A\u8F93\u5165\u7528\u6237\u540D"),oe.next=24;break;case 7:if(U===S){oe.next=11;break}ne("\u8F93\u5165\u7528\u6237\u540D\u975E\u767B\u5F55\u7528\u6237"),oe.next=24;break;case 11:return ne(""),oe.t0=ae.y6,oe.next=15,(0,ge.Vw)({data:{auditor:U,experiment_no:He}});case 15:if(oe.t1=oe.sent,Ye=(0,oe.t0)(oe.t1),!Ye.ok){oe.next=23;break}return b.success("\u786E\u8BA4\u5BA1\u6838\u6210\u529F"),oe.next=21,H==null?void 0:H();case 21:oe.next=24;break;case 23:b.error(Ye.msg);case 24:case"end":return oe.stop()}},fe)}));return function(){return se.apply(this,arguments)}}();return(0,t.jsx)(v.Z,{title:(0,l.oz)("declaration"),id:"announce",children:(0,t.jsxs)(de.Z,{direction:"vertical",children:[(0,t.jsx)(q.Z.Text,{className:"announce-text",children:(0,t.jsxs)(de.Z,{children:[(0,l.oz)("i"),y!=null&&y.auditor?(0,t.jsx)(q.Z.Text,{strong:!0,children:y.auditor}):(0,t.jsx)(P.Z,{disabled:Ae,placeholder:S,bordered:!1,onChange:function(fe){return h(fe.target.value)},value:U}),(0,l.oz)("declare-tip")]})}),(0,t.jsx)(q.Z.Text,{type:"danger",className:"error-text",children:O}),!(y!=null&&y.auditor)&&(0,t.jsx)(pe.ZP,{type:"primary",size:"small",onClick:w,disabled:Ae,children:(0,l.oz)("confirm")})]})})},n=m,x=e(78045),j=function(o){Y()(o);var s=(0,g.useContext)(L.M),i=s.conclusion,f=s.refetch,S=s.loading,k=Pe.Z.useApp(),b=k.modal,C=k.message,p=function(){var h=c()(u()().mark(function te(K,O){var ne;return u()().wrap(function(y){for(;;)switch(y.prev=y.next){case 0:return y.t0=ae.y6,y.next=3,(0,ge.Qt)({data:{experiment_no:K,conclusion_result:O}});case 3:if(y.t1=y.sent,ne=(0,y.t0)(y.t1),!ne.ok){y.next=11;break}return C.success("\u5B9E\u9A8C\u7ED3\u679C\u786E\u8BA4\u6210\u529F"),y.next=9,f==null?void 0:f();case 9:y.next=12;break;case 11:C.error(ne.msg);case 12:case"end":return y.stop()}},te)}));return function(K,O){return h.apply(this,arguments)}}(),U=function(te){if(te.preventDefault(),!!(i!=null&&i.experiment_no)){var K=te.target.value;b.confirm({onOk:function(){return p(i.experiment_no,K)},title:"\u786E\u8BA4\u5B9E\u9A8C\u7ED3\u679C",content:(0,t.jsxs)(de.Z,{children:["\u786E\u8BA4\u8BBE\u7F6E\u5B9E\u9A8C\u7ED3\u679C\u4E3A",(0,t.jsx)(q.Z.Text,{strong:!0,children:(0,l.oz)("app.general.message.".concat(K))}),"\u5417\uFF0C\u786E\u8BA4\u540E\u5B9E\u9A8C\u8BB0\u5F55\u4E0D\u53EF\u7F16\u8F91\uFF01"]})})}};return(0,t.jsx)(v.Z,{id:"conclusion",title:(0,l.oz)("menu.list.project-list.detail.experiment-conclusion"),children:i!=null&&i.auditor||i!=null&&i.conclusion_result?(0,l.oz)("app.general.message.".concat(i.conclusion_result)):(0,t.jsxs)(x.ZP.Group,{onChange:U,disabled:S,value:i==null?void 0:i.conclusion_result,children:[(0,t.jsx)(x.ZP,{value:"success",children:(0,l.oz)("app.general.message.success")}),(0,t.jsx)(x.ZP,{value:"failed",children:(0,l.oz)("component.notification.statusValue.failed")})]})})},E=j,Z=e(56337),z=e(40411),$=e(92921),B=e(74330),Se=e(93967),ve=e.n(Se),we=e(53124),Ne=e(35792),_=e(85982),ue=e(14747),$e=e(83559),Te=e(83262);const re=a=>{const{componentCls:o,calc:s}=a;return{[o]:Object.assign(Object.assign({},(0,ue.Wf)(a)),{margin:0,padding:0,listStyle:"none",[`${o}-item`]:{position:"relative",margin:0,paddingBottom:a.itemPaddingBottom,fontSize:a.fontSize,listStyle:"none","&-tail":{position:"absolute",insetBlockStart:a.itemHeadSize,insetInlineStart:s(s(a.itemHeadSize).sub(a.tailWidth)).div(2).equal(),height:`calc(100% - ${(0,_.unit)(a.itemHeadSize)})`,borderInlineStart:`${(0,_.unit)(a.tailWidth)} ${a.lineType} ${a.tailColor}`},"&-pending":{[`${o}-item-head`]:{fontSize:a.fontSizeSM,backgroundColor:"transparent"},[`${o}-item-tail`]:{display:"none"}},"&-head":{position:"absolute",width:a.itemHeadSize,height:a.itemHeadSize,backgroundColor:a.dotBg,border:`${(0,_.unit)(a.dotBorderWidth)} ${a.lineType} transparent`,borderRadius:"50%","&-blue":{color:a.colorPrimary,borderColor:a.colorPrimary},"&-red":{color:a.colorError,borderColor:a.colorError},"&-green":{color:a.colorSuccess,borderColor:a.colorSuccess},"&-gray":{color:a.colorTextDisabled,borderColor:a.colorTextDisabled}},"&-head-custom":{position:"absolute",insetBlockStart:s(a.itemHeadSize).div(2).equal(),insetInlineStart:s(a.itemHeadSize).div(2).equal(),width:"auto",height:"auto",marginBlockStart:0,paddingBlock:a.customHeadPaddingVertical,lineHeight:1,textAlign:"center",border:0,borderRadius:0,transform:"translate(-50%, -50%)"},"&-content":{position:"relative",insetBlockStart:s(s(a.fontSize).mul(a.lineHeight).sub(a.fontSize)).mul(-1).add(a.lineWidth).equal(),marginInlineStart:s(a.margin).add(a.itemHeadSize).equal(),marginInlineEnd:0,marginBlockStart:0,marginBlockEnd:0,wordBreak:"break-word"},"&-last":{[`> ${o}-item-tail`]:{display:"none"},[`> ${o}-item-content`]:{minHeight:s(a.controlHeightLG).mul(1.2).equal()}}},[`&${o}-alternate,
        &${o}-right,
        &${o}-label`]:{[`${o}-item`]:{"&-tail, &-head, &-head-custom":{insetInlineStart:"50%"},"&-head":{marginInlineStart:s(a.marginXXS).mul(-1).equal(),"&-custom":{marginInlineStart:s(a.tailWidth).div(2).equal()}},"&-left":{[`${o}-item-content`]:{insetInlineStart:`calc(50% - ${(0,_.unit)(a.marginXXS)})`,width:`calc(50% - ${(0,_.unit)(a.marginSM)})`,textAlign:"start"}},"&-right":{[`${o}-item-content`]:{width:`calc(50% - ${(0,_.unit)(a.marginSM)})`,margin:0,textAlign:"end"}}}},[`&${o}-right`]:{[`${o}-item-right`]:{[`${o}-item-tail,
            ${o}-item-head,
            ${o}-item-head-custom`]:{insetInlineStart:`calc(100% - ${(0,_.unit)(s(s(a.itemHeadSize).add(a.tailWidth)).div(2).equal())})`},[`${o}-item-content`]:{width:`calc(100% - ${(0,_.unit)(s(a.itemHeadSize).add(a.marginXS).equal())})`}}},[`&${o}-pending
        ${o}-item-last
        ${o}-item-tail`]:{display:"block",height:`calc(100% - ${(0,_.unit)(a.margin)})`,borderInlineStart:`${(0,_.unit)(a.tailWidth)} dotted ${a.tailColor}`},[`&${o}-reverse
        ${o}-item-last
        ${o}-item-tail`]:{display:"none"},[`&${o}-reverse ${o}-item-pending`]:{[`${o}-item-tail`]:{insetBlockStart:a.margin,display:"block",height:`calc(100% - ${(0,_.unit)(a.margin)})`,borderInlineStart:`${(0,_.unit)(a.tailWidth)} dotted ${a.tailColor}`},[`${o}-item-content`]:{minHeight:s(a.controlHeightLG).mul(1.2).equal()}},[`&${o}-label`]:{[`${o}-item-label`]:{position:"absolute",insetBlockStart:s(s(a.fontSize).mul(a.lineHeight).sub(a.fontSize)).mul(-1).add(a.tailWidth).equal(),width:`calc(50% - ${(0,_.unit)(a.marginSM)})`,textAlign:"end"},[`${o}-item-right`]:{[`${o}-item-label`]:{insetInlineStart:`calc(50% + ${(0,_.unit)(a.marginSM)})`,width:`calc(50% - ${(0,_.unit)(a.marginSM)})`,textAlign:"start"}}},"&-rtl":{direction:"rtl",[`${o}-item-head-custom`]:{transform:"translate(50%, -50%)"}}})}},Fe=a=>({tailColor:a.colorSplit,tailWidth:a.lineWidthBold,dotBorderWidth:a.wireframe?a.lineWidthBold:a.lineWidth*3,dotBg:a.colorBgContainer,itemPaddingBottom:a.padding*1.25});var nt=(0,$e.I$)("Timeline",a=>{const o=(0,Te.mergeToken)(a,{itemHeadSize:10,customHeadPaddingVertical:a.paddingXXS,paddingInlineEnd:2});return[re(o)]},Fe),V=function(a,o){var s={};for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&o.indexOf(i)<0&&(s[i]=a[i]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var f=0,i=Object.getOwnPropertySymbols(a);f<i.length;f++)o.indexOf(i[f])<0&&Object.prototype.propertyIsEnumerable.call(a,i[f])&&(s[i[f]]=a[i[f]]);return s},ze=a=>{var{prefixCls:o,className:s,color:i="blue",dot:f,pending:S=!1,position:k,label:b,children:C}=a,p=V(a,["prefixCls","className","color","dot","pending","position","label","children"]);const{getPrefixCls:U}=g.useContext(we.E_),h=U("timeline",o),te=ve()(`${h}-item`,{[`${h}-item-pending`]:S},s),K=/blue|red|green|gray/.test(i||"")?void 0:i,O=ve()(`${h}-item-head`,{[`${h}-item-head-custom`]:!!f,[`${h}-item-head-${i}`]:!K});return g.createElement("li",Object.assign({},p,{className:te}),b&&g.createElement("div",{className:`${h}-item-label`},b),g.createElement("div",{className:`${h}-item-tail`}),g.createElement("div",{className:O,style:{borderColor:K,color:K}},f),g.createElement("div",{className:`${h}-item-content`},C))},Xe=e(74902),Ke=e(19267),Ve=function(a,o){var s={};for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&o.indexOf(i)<0&&(s[i]=a[i]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var f=0,i=Object.getOwnPropertySymbols(a);f<i.length;f++)o.indexOf(i[f])<0&&Object.prototype.propertyIsEnumerable.call(a,i[f])&&(s[i[f]]=a[i[f]]);return s},ut=a=>{var{prefixCls:o,className:s,pending:i=!1,children:f,items:S,rootClassName:k,reverse:b=!1,direction:C,hashId:p,pendingDot:U,mode:h=""}=a,te=Ve(a,["prefixCls","className","pending","children","items","rootClassName","reverse","direction","hashId","pendingDot","mode"]);const K=(se,fe)=>h==="alternate"?se==="right"?`${o}-item-right`:se==="left"?`${o}-item-left`:fe%2===0?`${o}-item-left`:`${o}-item-right`:h==="left"?`${o}-item-left`:h==="right"?`${o}-item-right`:se==="right"?`${o}-item-right`:"",O=(0,Xe.Z)(S||[]),ne=typeof i=="boolean"?null:i;i&&O.push({pending:!!i,dot:U||g.createElement(Ke.Z,null),children:ne}),b&&O.reverse();const A=O.length,y=`${o}-item-last`,H=O.filter(se=>!!se).map((se,fe)=>{var He;const Ye=fe===A-2?y:"",st=fe===A-1?y:"",{className:oe}=se,yt=Ve(se,["className"]);return g.createElement(ze,Object.assign({},yt,{className:ve()([oe,!b&&i?Ye:st,K((He=se==null?void 0:se.position)!==null&&He!==void 0?He:"",fe)]),key:(se==null?void 0:se.key)||fe}))}),Ae=O.some(se=>!!(se!=null&&se.label)),w=ve()(o,{[`${o}-pending`]:!!i,[`${o}-reverse`]:!!b,[`${o}-${h}`]:!!h&&!Ae,[`${o}-label`]:Ae,[`${o}-rtl`]:C==="rtl"},s,k,p);return g.createElement("ul",Object.assign({},te,{className:w}),H)},Ie=e(50344);function rt(a,o){return a&&Array.isArray(a)?a:(0,Ie.Z)(o).map(s=>{var i,f;return Object.assign({children:(f=(i=s==null?void 0:s.props)===null||i===void 0?void 0:i.children)!==null&&f!==void 0?f:""},s.props)})}var ct=rt,mt=function(a,o){var s={};for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&o.indexOf(i)<0&&(s[i]=a[i]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var f=0,i=Object.getOwnPropertySymbols(a);f<i.length;f++)o.indexOf(i[f])<0&&Object.prototype.propertyIsEnumerable.call(a,i[f])&&(s[i[f]]=a[i[f]]);return s};const Qe=a=>{const{getPrefixCls:o,direction:s,timeline:i}=g.useContext(we.E_),{prefixCls:f,children:S,items:k,className:b,style:C}=a,p=mt(a,["prefixCls","children","items","className","style"]),U=o("timeline",f),h=(0,Ne.Z)(U),[te,K,O]=nt(U,h),ne=ct(k,S);return te(g.createElement(ut,Object.assign({},p,{className:ve()(i==null?void 0:i.className,b,O,h),style:Object.assign(Object.assign({},i==null?void 0:i.style),C),prefixCls:U,direction:s,items:ne,hashId:K})))};Qe.Item=ze;var Me=Qe,qe=Me,_e=e(27484),vt=e.n(_e),M=e(96486),ee=e(10784),I=e(34994),D=e(64317),F=e(5966),G=e(98138),xe=e(4393),Ce=e(71230),ke=e(15746),it=function(){var o=(0,g.useState)([]),s=d()(o,2),i=s[0],f=s[1],S=function(){var b=c()(u()().mark(function C(){var p;return u()().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:if(!i.length){h.next=2;break}return h.abrupt("return",i);case 2:return h.next=4,(0,ce.JK)({params:{locale:"zh"}});case 4:if(p=h.sent,!(0,ae.y6)(p).ok){h.next=7;break}return h.abrupt("return",p.data);case 7:return h.abrupt("return",[]);case 8:case"end":return h.stop()}},C)}));return function(){return b.apply(this,arguments)}}();(0,g.useEffect)(function(){S().then(f)},[]);var k=(0,g.useMemo)(function(){return i.map(function(b){return[b.handlerMethodNo,b.handlerMethodName]}).reduce(function(b,C){return b[C[0]]=C[1],b},{})},[i]);return{fetch:S,codeToName:k}},lt=function(){var o=(0,g.useState)(),s=d()(o,2),i=s[0],f=s[1],S=function(){var b=c()(u()().mark(function C(){var p;return u()().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:return h.next=2,(0,ce.OS)({});case 2:if(p=h.sent,!(0,ae.y6)(p).ok){h.next=5;break}return h.abrupt("return",Object.entries(p.data).map(function(te){var K=d()(te,2),O=K[0],ne=K[1];return{label:ne,value:O}}));case 5:return h.abrupt("return",[]);case 6:case"end":return h.stop()}},C)}));return function(){return b.apply(this,arguments)}}();(0,g.useEffect)(function(){var b=!0;return S().then(function(C){return b&&f(C)}),function(){b=!1}},[]);var k=(0,g.useMemo)(function(){return i?i.reduce(function(b,C){return C.value&&(b[C.value]=C.label),b},{}):{}},[i]);return[i||[],k]},bt=e(97857),gt=e.n(bt),W=e(69776),ie=e(88280),De={paramsTable:"paramsTable___X1Www"},Be=function(o){var s=o.params,i=o.editMode,f=o.getParamsEvent,S=(0,W.Z)(),k=S.editableConfig,b=(0,g.useState)(function(){return s}),C=d()(b,2),p=C[0],U=C[1],h=(0,g.useState)(),te=d()(h,2),K=te[0],O=te[1];(0,g.useEffect)(function(){O(i?p.map(function(A){return A.name}):[])},[i]),(0,g.useEffect)(function(){var A;f==null||(A=f.getter)===null||A===void 0||A.call(f,p)},[f]);var ne=[{title:"\u4EFB\u52A1\u53C2\u6570",dataIndex:"name",readonly:!0},{title:"\u521D\u59CB\u503C",dataIndex:"value",readonly:!0},{title:"\u4FEE\u6B63\u503C",dataIndex:"amend_value",valueType:"text",formItemProps:{rules:[{required:!0,whitespace:!0,message:"\u6B64\u9879\u662F\u5FC5\u586B\u9879"}]}}];return(0,t.jsx)(ie.Z,{rowKey:"name",loading:!1,columns:ne,value:p,className:De.paramsTable,editable:gt()(gt()({},k),{},{type:"multiple",editableKeys:K,onValuesChange:function(y,H){U(H)},onChange:O}),recordCreatorProps:!1})},ft=Be,Ge=function(o){var s,i,f,S=o.exception,k=o.onUpdateSuccess,b=G.Z.useForm(),C=d()(b,1),p=C[0],U=Pe.Z.useApp(),h=U.message,te=U.notification,K=(0,g.useContext)(L.M),O=K.conclusion,ne=it(),A=ne.codeToName,y=lt(),H=d()(y,2),Ae=H[0],w=H[1],se=!(O!=null&&O.conclusion_result||O!=null&&O.auditor);(0,g.useEffect)(function(){p.setFieldsValue(S.task_exception)},[S.task_exception]);var fe=(0,g.useState)(!1),He=d()(fe,2),Ye=He[0],st=He[1],oe=(0,g.useState)({}),yt=d()(oe,2),Nt=yt[0],Ft=yt[1],Je=!!((s=S.task_params)!==null&&s!==void 0&&s.length),Pt=function(){var ht=c()(u()().mark(function St(tt){var jt,pt;return u()().wrap(function(Oe){for(;;)switch(Oe.prev=Oe.next){case 0:return jt={exception_id:S.exception_id,task_params:tt,task_exception:(0,M.pick)(p.getFieldsValue(),"reason_type","reason")},Oe.t0=ae.y6,Oe.next=4,(0,ce.eX)({data:jt});case 4:if(Oe.t1=Oe.sent,pt=(0,Oe.t0)(Oe.t1),st(!1),!pt.ok){Oe.next=11;break}return h.success("\u8865\u586B\u6210\u529F"),k==null||k(),Oe.abrupt("return",!0);case 11:return te.error({description:pt.msg,message:"\u8865\u586B\u5931\u8D25"}),Oe.abrupt("return",!1);case 13:case"end":return Oe.stop()}},St)}));return function(tt){return ht.apply(this,arguments)}}(),Wt=(0,t.jsx)(Q.vY,{className:ve()("exception-view-wrapper",{"full-width":!Je}),column:Je?1:2,dataSource:S.task_exception,columns:[{title:(0,l.oz)("reason-type"),key:"reason_type",dataIndex:"reason_type",valueType:"select",valueEnum:w},{title:(0,l.oz)("reason"),key:"reason",dataIndex:"reason",valueType:"text"}]}),wt=(0,t.jsxs)(I.A,{form:p,layout:"horizontal",grid:!0,labelCol:{span:6},validateMessages:{required:"${label}"+"".concat((0,l.Ig)()?" ":"").concat((0,l.oz)("is-required"))},submitter:!1,children:[(0,t.jsx)(D.Z,{colProps:{md:Je?24:12},name:"reason_type",label:(0,l.oz)("reason-type"),options:Ae}),(0,t.jsx)(F.Z,{colProps:{md:Je?24:12},name:"reason",label:(0,l.oz)("reason")})]});return(0,t.jsxs)(xe.Z,{size:"small",title:A[((i=S.task_exception)===null||i===void 0?void 0:i.exception_code)||""]||((f=S.task_exception)===null||f===void 0?void 0:f.exception_code),extra:!Ye&&se&&(0,t.jsx)(pe.ZP,{type:"default",size:"small",onClick:function(){return st(!0)},children:"\u8865\u586B"}),children:[(0,t.jsxs)(Ce.Z,{children:[Je&&(0,t.jsx)(ke.Z,{span:12,children:(0,t.jsx)(ft,{params:S.task_params||[],editMode:Ye,getParamsEvent:Nt})}),(0,t.jsx)(ke.Z,{span:Je?11:24,offset:Je?1:0,children:Ye?wt:Wt})]}),Ye&&(0,t.jsx)(Ce.Z,{children:(0,t.jsxs)(de.Z,{className:"exception-actions-wrapper",children:[(0,t.jsx)(pe.ZP,{onClick:function(){return st(!1)},children:(0,l.oz)("pages.experiment.label.operation.cancel")}),(0,t.jsx)(ee.Z,{type:"primary",onClick:c()(u()().mark(function ht(){return u()().wrap(function(tt){for(;;)switch(tt.prev=tt.next){case 0:return tt.abrupt("return",Je?new Promise(function(jt,pt){Ft({getter:function(){var $t=c()(u()().mark(function Tt(Kt){var zt;return u()().wrap(function(ot){for(;;)switch(ot.prev=ot.next){case 0:return ot.next=2,Pt(Kt);case 2:return zt=ot.sent,ot.abrupt("return",zt?jt():pt());case 4:case"end":return ot.stop()}},Tt)}));function Oe(Tt){return $t.apply(this,arguments)}return Oe}()})}):Pt());case 1:case"end":return tt.stop()}},ht)})),children:(0,l.oz)("pages.route.edit.label.confirm")})]})})]})},Ue=Ge,et=function(o){var s=o.experimentNo,i=o.taskNo,f=(0,g.useState)([]),S=d()(f,2),k=S[0],b=S[1],C=(0,g.useState)(!1),p=d()(C,2),U=p[0],h=p[1],te=(0,g.useContext)(L.M),K=te.refetch,O=function(){var A=c()(u()().mark(function y(H){var Ae,w;return u()().wrap(function(fe){for(;;)switch(fe.prev=fe.next){case 0:return h(!0),fe.next=3,(0,ce.Oh)({routeParams:"".concat(s,"/").concat(H)});case 3:w=fe.sent,h(!1),b((w==null||(Ae=w.data)===null||Ae===void 0?void 0:Ae.exceptions)||[]);case 6:case"end":return fe.stop()}},y)}));return function(H){return A.apply(this,arguments)}}();(0,g.useEffect)(function(){O(i)},[i]);var ne=(0,M.sortBy)(k,"task_exception.exception_time").map(function(A){var y;return{label:vt()((y=A.task_exception)===null||y===void 0?void 0:y.exception_time).format("YYYY/MM/DD hh:mm:ss"),children:(0,t.jsx)(Ue,{exception:A,onUpdateSuccess:function(){O(i),K==null||K()}})}});return(0,t.jsx)(B.Z,{spinning:U,children:(0,t.jsx)(qe,{mode:"left",items:ne})})},xt=et,It=[{title:(0,l.oz)("pages.Notification.task"),dataIndex:"task_name",key:"task_name",valueType:"text",render:function(o,s){return(0,t.jsx)("span",{children:(0,t.jsxs)(de.Z,{direction:"horizontal",children:[s.task_name,s.need_amend?(0,t.jsx)(z.Z,{dot:!0,status:"error"}):null]})})}},{title:(0,l.oz)("pages.searchTable.titleDesc"),dataIndex:"task_desc",key:"task_desc",valueType:"text"},{title:(0,l.oz)("pages.searchTable.updateForm.schedulingPeriod.timeLabel"),dataIndex:"start_time",key:"start_time",valueType:"dateTime"},{title:(0,l.oz)("end-time"),dataIndex:"end_time",key:"end_time",valueType:"dateTime"},$.Z.EXPAND_COLUMN],Bt=function(){var o=(0,g.useContext)(L.M),s=o.conclusion,i=o.loading;return(0,t.jsx)(v.Z,{title:(0,l.oz)("task-list"),id:"operations",children:(0,t.jsx)(Z.Z,{dataSource:s==null?void 0:s.operation_records,loading:i,columns:It,search:!1,options:!1,rowKey:"task_id",expandable:{expandedRowRender:function(S){var k=S.task_no,b=S.experiment_no;return(0,t.jsx)(xt,{taskNo:k,experimentNo:b})},rowExpandable:function(S){return!!S.error_count},expandIcon:function(S){var k=S.expanded,b=S.onExpand,C=S.record;return!!C.error_count&&(0,t.jsxs)(pe.ZP,{type:"link",onClick:function(U){return b(C,U)},children:[k?(0,l.oz)("component.tagSelect.collapse"):(0,l.oz)("pages.projectTable.actionLabel.viewDetail"),"\u5F02\u5E38\u8BB0\u5F55"]})}},pagination:!1})})},Ot=Bt,Ct=e(24739),Et=e(31199),Rt=e(90672),Zt=[{title:(0,l.oz)("yield"),key:"experiment_yield",dataIndex:"experiment_yield",valueType:"percent"},{title:(0,l.oz)("purity"),key:"purity",dataIndex:"purity",valueType:"percent"},{title:"".concat((0,l.oz)("pages.experiment.conclusion.label.phase.solid"),"/").concat((0,l.oz)("pages.experiment.conclusion.label.phase.liquid")),key:"phase",dataIndex:"phase",valueEnum:{solid:{text:(0,l.oz)("pages.experiment.conclusion.label.phase.solid")},liquid:{text:(0,l.oz)("pages.experiment.conclusion.label.phase.liquid")}}},{title:(0,l.oz)("product-color"),key:"color",dataIndex:"color",valueType:"text"},{title:(0,l.oz)("other"),key:"other",dataIndex:"other",valueType:"text"},{title:(0,l.oz)("comment"),key:"comment",dataIndex:"comment",valueType:"textarea",className:"note-wrapper"}],Mt=function(){var o=(0,g.useContext)(L.M),s=o.conclusion,i=o.refetch,f=o.loading,S=(0,g.useState)(!1),k=d()(S,2),b=k[0],C=k[1],p=G.Z.useForm(),U=d()(p,1),h=U[0],te=Pe.Z.useApp(),K=te.message;(0,g.useEffect)(function(){h.setFieldsValue((s==null?void 0:s.conclusion)||{})},[s==null?void 0:s.conclusion]);var O=function(){var ne=c()(u()().mark(function A(y){var H;return u()().wrap(function(w){for(;;)switch(w.prev=w.next){case 0:if(s!=null&&s.experiment_no){w.next=2;break}return w.abrupt("return");case 2:return w.t0=ae.y6,w.next=5,(0,ge.xV)({data:{experiment_no:s==null?void 0:s.experiment_no,conclusion:y}});case 5:if(w.t1=w.sent,H=(0,w.t0)(w.t1),!H.ok){w.next=14;break}return K.success("\u5B9E\u9A8C\u7ED3\u679C\u4FDD\u5B58\u6210\u529F"),w.next=11,i==null?void 0:i();case 11:C(!1),w.next=15;break;case 14:K.error(H.msg);case 15:case"end":return w.stop()}},A)}));return function(y){return ne.apply(this,arguments)}}();return b?(0,t.jsxs)(v.Z,{id:"result",title:(0,l.oz)("conclusion"),children:[(0,t.jsxs)(I.A,{form:h,disabled:f,layout:"horizontal",grid:!0,labelCol:{span:6},validateMessages:{required:"${label}"+"".concat((0,l.Ig)()?" ":"").concat((0,l.oz)("is-required"))},onFinish:O,submitter:!1,children:[(0,t.jsxs)(Ct.UW,{title:(0,l.oz)("summary"),children:[(0,t.jsx)(Et.Z,{colProps:{md:8},max:100,min:0,name:"experiment_yield",label:(0,l.oz)("yield")}),(0,t.jsx)(Et.Z,{colProps:{md:8},max:100,min:0,name:"purity",label:(0,l.oz)("purity")})]}),(0,t.jsxs)(Ct.UW,{title:(0,l.oz)("property"),children:[(0,t.jsx)(D.Z,{colProps:{md:8},name:"phase",label:"".concat((0,l.oz)("pages.experiment.conclusion.label.phase.solid"),"/").concat((0,l.oz)("pages.experiment.conclusion.label.phase.liquid")),valueEnum:{solid:{text:(0,l.oz)("pages.experiment.conclusion.label.phase.solid")},liquid:{text:(0,l.oz)("pages.experiment.conclusion.label.phase.liquid")}}}),(0,t.jsx)(F.Z,{colProps:{md:8},name:"color",label:(0,l.oz)("product-color")}),(0,t.jsx)(F.Z,{colProps:{md:8},name:"other",label:(0,l.oz)("other")})]}),(0,t.jsx)(Rt.Z,{labelCol:{md:2},colProps:{md:24},name:"comment",label:(0,l.oz)("comment")})]}),b&&(0,t.jsx)(Ce.Z,{children:(0,t.jsxs)(de.Z,{className:"result-actions-wrapper",children:[(0,t.jsx)(pe.ZP,{onClick:function(){return C(!1)},children:(0,l.oz)("pages.experiment.label.operation.cancel")}),(0,t.jsx)(ee.Z,{type:"primary",onClick:function(){return O(h.getFieldsValue())},children:(0,l.oz)("pages.route.edit.label.confirm")})]})})]}):(0,t.jsxs)(v.Z,{id:"result",title:(0,l.oz)("conclusion"),actionSlot:s!=null&&s.conclusion_result?void 0:(0,t.jsx)(pe.ZP,{type:"default",size:"small",onClick:function(){return C(!0)},children:(0,l.oz)("edit")}),children:[(0,t.jsx)(Q.vY,{column:3,title:(0,l.oz)("summary"),dataSource:s==null?void 0:s.conclusion,columns:Zt.slice(0,2)}),(0,t.jsx)(Q.vY,{column:3,title:(0,l.oz)("property"),dataSource:s==null?void 0:s.conclusion,columns:Zt.slice(2)})]})},Dt=Mt,At=function(){var o=(0,he.useParams)(),s=o.experimentalNo,i=(0,me.I)(),f=i.dialogProps,S=i.confirm,k=/^[A-Za-z0-9+/]*(=){0,2}$/,b=k.test(s)?s&&JSON.parse((0,l.bM)(s)):s,C=(0,L.D)(b),p=C.conclusion,U=(0,g.useState)(""),h=d()(U,2),te=h[0],K=h[1],O=function(){var y=window.location.hash;if(y){var H=document.getElementById(y.slice(1));H&&(H.style.scrollMargin="50px",H.scrollIntoView({behavior:"smooth",block:"start"}),H.style.scrollMargin="")}};(0,g.useEffect)(function(){setTimeout(function(){O()},500)},[]),(0,g.useEffect)(function(){p!=null&&p.experiment_owner&&(0,ye.query)("users/".concat(p.experiment_owner),{normalizeData:!1}).get().then(function(A){K(A.username)})},[p==null?void 0:p.experiment_owner]);var ne=function(){var A=c()(u()().mark(function y(){var H;return u()().wrap(function(w){for(;;)switch(w.prev=w.next){case 0:return w.next=2,(0,ce.Md)({routeParams:b});case 2:if(H=w.sent,(0,ae.y6)(H).ok){w.next=5;break}return w.abrupt("return");case 5:he.history.push("".concat(window.location.href,"/knowledgeBase/").concat((0,l.YW)(JSON.stringify(p==null?void 0:p.experiment_design_id))));case 6:case"end":return w.stop()}},y)}));return function(){return A.apply(this,arguments)}}();return(0,t.jsxs)(je._z,{className:"experiment-conclusion-page-root",children:[(0,t.jsxs)(L.M.Provider,{value:C,children:[(0,t.jsxs)(Q.vY,{dataSource:p,className:"basic-info-wrapper",children:[(0,t.jsx)(Q.vY.Item,{label:(0,l.oz)("pages.experiment.label.no"),valueType:"text",dataIndex:"experiment_no"}),(0,t.jsx)(Q.vY.Item,{label:(0,l.oz)("pages.experiment.label.personInCharge"),valueType:"text",dataIndex:"experiment_owner",render:function(){return te||"-"}}),(0,t.jsx)(Q.vY.Item,{label:(0,l.oz)("pages.experiment.label.status"),valueType:"select",dataIndex:"status",valueEnum:T.p.reduce(function(A,y){return A[y]=(0,l.oz)("pages.experiment.statusLabel.".concat(y)),A},{})}),(0,t.jsx)(Q.vY.Item,{label:(0,l.oz)("yield"),valueType:"percent",dataIndex:"estimate_yield"}),(0,t.jsx)(Q.vY.Item,{label:(0,l.oz)("experiment-actual-start-time"),valueType:"dateTime",dataIndex:"start_time"}),(0,t.jsx)(Q.vY.Item,{label:(0,l.oz)("experiment-actual-end-time"),valueType:"dateTime",dataIndex:"end_time"})]}),(0,t.jsx)(be.Z,{className:"detail-title-wrapper",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(Ee.Z,{direction:"horizontal",className:"anchor",offsetTop:56,affix:!1,getCurrentAnchor:function(y){var H=window.location.hash;return!y&&H?H:y},items:[{key:"operations",href:"#operations",title:(0,l.oz)("task-list")},{key:"analysis",href:"#analysis",title:(0,l.oz)("pages.experiment.label.operation.detectRecord")},{key:"result",href:"#result",title:(0,l.oz)("conclusion")},{key:"conclusion",href:"#conclusion",title:(0,l.oz)("menu.list.project-list.detail.experiment-conclusion")},{key:"announce",href:"#announce",title:(0,l.oz)("declaration")}]}),(0,t.jsxs)(de.Z,{className:"action-buttons-wrapper",children:[(0,t.jsx)(pe.ZP,{type:"default",size:"small",onClick:function(){return S()},children:(0,l.oz)("pages.reaction.label.material-sheet")}),(0,t.jsx)(pe.ZP,{type:"default",size:"small",onClick:ne,children:(0,l.oz)("menu.list.project-list.detail.experiment-conclusion.knowledgeBase")})]}),(0,t.jsx)(Ze.Z,{className:"divider",style:{position:"absolute",top:22}})]})}),(0,t.jsx)(Ot,{}),(0,t.jsx)(N.Z,{analysisData:p==null?void 0:p.analysis_records,experimentalNo:b}),(0,t.jsx)(Dt,{}),(0,t.jsx)(E,{}),(0,t.jsx)(n,{})]}),(0,t.jsx)(r.Z,{dialogProps:f,structure:(p==null?void 0:p.rxn_smiles)||"",material_table:p==null?void 0:p.materials})]})},Lt=At},36569:function(We,le,e){e.d(le,{Z:function(){return q}});var J=e(74902),u=e(67294),X=e(93967),c=e.n(X),R=e(66680),d=e(17423),me=e(66367),T=e(58375),r=e(30291),ae=e(53124),ye=e(35792),l=u.createContext(void 0),Q=P=>{const{href:v,title:t,prefixCls:m,children:n,className:x,target:j,replace:E}=P,Z=u.useContext(l),{registerLink:z,unregisterLink:$,scrollTo:B,onClick:Se,activeLink:ve,direction:we}=Z||{};u.useEffect(()=>(z==null||z(v),()=>{$==null||$(v)}),[v]);const Ne=Fe=>{Se==null||Se(Fe,{title:t,href:v}),B==null||B(v),E&&(Fe.preventDefault(),window.location.replace(v))},{getPrefixCls:_}=u.useContext(ae.E_),ue=_("anchor",m),$e=ve===v,Te=c()(`${ue}-link`,x,{[`${ue}-link-active`]:$e}),re=c()(`${ue}-link-title`,{[`${ue}-link-title-active`]:$e});return u.createElement("div",{className:Te},u.createElement("a",{className:re,href:v,title:typeof t=="string"?t:"",target:j,onClick:Ne},t),we!=="horizontal"?n:null)},he=e(85982),be=e(14747),Ee=e(83559),de=e(83262);const pe=P=>{const{componentCls:v,holderOffsetBlock:t,motionDurationSlow:m,lineWidthBold:n,colorPrimary:x,lineType:j,colorSplit:E,calc:Z}=P;return{[`${v}-wrapper`]:{marginBlockStart:Z(t).mul(-1).equal(),paddingBlockStart:t,[v]:Object.assign(Object.assign({},(0,be.Wf)(P)),{position:"relative",paddingInlineStart:n,[`${v}-link`]:{paddingBlock:P.linkPaddingBlock,paddingInline:`${(0,he.unit)(P.linkPaddingInlineStart)} 0`,"&-title":Object.assign(Object.assign({},be.vS),{position:"relative",display:"block",marginBlockEnd:P.anchorTitleBlock,color:P.colorText,transition:`all ${P.motionDurationSlow}`,"&:only-child":{marginBlockEnd:0}}),[`&-active > ${v}-link-title`]:{color:P.colorPrimary},[`${v}-link`]:{paddingBlock:P.anchorPaddingBlockSecondary}}}),[`&:not(${v}-wrapper-horizontal)`]:{[v]:{"&::before":{position:"absolute",insetInlineStart:0,top:0,height:"100%",borderInlineStart:`${(0,he.unit)(n)} ${j} ${E}`,content:'" "'},[`${v}-ink`]:{position:"absolute",insetInlineStart:0,display:"none",transform:"translateY(-50%)",transition:`top ${m} ease-in-out`,width:n,backgroundColor:x,[`&${v}-ink-visible`]:{display:"inline-block"}}}},[`${v}-fixed ${v}-ink ${v}-ink`]:{display:"none"}}}},Ze=P=>{const{componentCls:v,motionDurationSlow:t,lineWidthBold:m,colorPrimary:n}=P;return{[`${v}-wrapper-horizontal`]:{position:"relative","&::before":{position:"absolute",left:{_skip_check_:!0,value:0},right:{_skip_check_:!0,value:0},bottom:0,borderBottom:`${(0,he.unit)(P.lineWidth)} ${P.lineType} ${P.colorSplit}`,content:'" "'},[v]:{overflowX:"scroll",position:"relative",display:"flex",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"},[`${v}-link:first-of-type`]:{paddingInline:0},[`${v}-ink`]:{position:"absolute",bottom:0,transition:`left ${t} ease-in-out, width ${t} ease-in-out`,height:m,backgroundColor:n}}}}},g=P=>({linkPaddingBlock:P.paddingXXS,linkPaddingInlineStart:P.padding});var L=(0,Ee.I$)("Anchor",P=>{const{fontSize:v,fontSizeLG:t,paddingXXS:m,calc:n}=P,x=(0,de.mergeToken)(P,{holderOffsetBlock:m,anchorPaddingBlockSecondary:n(m).div(2).equal(),anchorTitleBlock:n(v).div(14).mul(3).equal(),anchorBallSize:n(t).div(2).equal()});return[pe(x),Ze(x)]},g);function Le(){return window}function N(P,v){if(!P.getClientRects().length)return 0;const t=P.getBoundingClientRect();return t.width||t.height?v===window?t.top-P.ownerDocument.documentElement.clientTop:t.top-v.getBoundingClientRect().top:t.top}const Re=/#([\S ]+)$/;var ge=P=>{var v;const{rootClassName:t,prefixCls:m,className:n,style:x,offsetTop:j,affix:E=!0,showInkInFixed:Z=!1,children:z,items:$,direction:B="vertical",bounds:Se,targetOffset:ve,onClick:we,onChange:Ne,getContainer:_,getCurrentAnchor:ue,replace:$e}=P,[Te,re]=u.useState([]),[Fe,nt]=u.useState(null),V=u.useRef(Fe),dt=u.useRef(null),ze=u.useRef(null),Xe=u.useRef(!1),{direction:Ke,anchor:Ve,getTargetContainer:at,getPrefixCls:ut}=u.useContext(ae.E_),Ie=ut("anchor",m),rt=(0,ye.Z)(Ie),[ct,mt,Qe]=L(Ie,rt),Me=(v=_!=null?_:at)!==null&&v!==void 0?v:Le,qe=JSON.stringify(Te),_e=(0,R.Z)(W=>{Te.includes(W)||re(ie=>[].concat((0,J.Z)(ie),[W]))}),vt=(0,R.Z)(W=>{Te.includes(W)&&re(ie=>ie.filter(De=>De!==W))}),M=()=>{var W;const ie=(W=dt.current)===null||W===void 0?void 0:W.querySelector(`.${Ie}-link-title-active`);if(ie&&ze.current){const{style:De}=ze.current,Be=B==="horizontal";De.top=Be?"":`${ie.offsetTop+ie.clientHeight/2}px`,De.height=Be?"":`${ie.clientHeight}px`,De.left=Be?`${ie.offsetLeft}px`:"",De.width=Be?`${ie.clientWidth}px`:"",Be&&(0,d.Z)(ie,{scrollMode:"if-needed",block:"nearest"})}},ee=function(W){let ie=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,De=arguments.length>2&&arguments[2]!==void 0?arguments[2]:5;const Be=[],ft=Me();return W.forEach(Ge=>{const Ue=Re.exec(Ge==null?void 0:Ge.toString());if(!Ue)return;const et=document.getElementById(Ue[1]);if(et){const xt=N(et,ft);xt<=ie+De&&Be.push({link:Ge,top:xt})}}),Be.length?Be.reduce((Ue,et)=>et.top>Ue.top?et:Ue).link:""},I=(0,R.Z)(W=>{if(V.current===W)return;const ie=typeof ue=="function"?ue(W):W;nt(ie),V.current=ie,Ne==null||Ne(W)}),D=u.useCallback(()=>{if(Xe.current)return;const W=ee(Te,ve!==void 0?ve:j||0,Se);I(W)},[qe,ve,j]),F=u.useCallback(W=>{I(W);const ie=Re.exec(W);if(!ie)return;const De=document.getElementById(ie[1]);if(!De)return;const Be=Me(),ft=(0,me.Z)(Be),Ge=N(De,Be);let Ue=ft+Ge;Ue-=ve!==void 0?ve:j||0,Xe.current=!0,(0,T.Z)(Ue,{getContainer:Me,callback(){Xe.current=!1}})},[ve,j]),G=c()(mt,Qe,rt,t,`${Ie}-wrapper`,{[`${Ie}-wrapper-horizontal`]:B==="horizontal",[`${Ie}-rtl`]:Ke==="rtl"},n,Ve==null?void 0:Ve.className),xe=c()(Ie,{[`${Ie}-fixed`]:!E&&!Z}),Ce=c()(`${Ie}-ink`,{[`${Ie}-ink-visible`]:Fe}),ke=Object.assign(Object.assign({maxHeight:j?`calc(100vh - ${j}px)`:"100vh"},Ve==null?void 0:Ve.style),x),it=W=>Array.isArray(W)?W.map(ie=>u.createElement(Q,Object.assign({replace:$e},ie,{key:ie.key}),B==="vertical"&&it(ie.children))):null,lt=u.createElement("div",{ref:dt,className:G,style:ke},u.createElement("div",{className:xe},u.createElement("span",{className:Ce,ref:ze}),"items"in P?it($):z));u.useEffect(()=>{const W=Me();return D(),W==null||W.addEventListener("scroll",D),()=>{W==null||W.removeEventListener("scroll",D)}},[qe]),u.useEffect(()=>{typeof ue=="function"&&I(ue(V.current||""))},[ue]),u.useEffect(()=>{M()},[B,ue,qe,Fe]);const bt=u.useMemo(()=>({registerLink:_e,unregisterLink:vt,scrollTo:F,activeLink:Fe,onClick:we,direction:B}),[Fe,we,F,B]),gt=E&&typeof E=="object"?E:void 0;return ct(u.createElement(l.Provider,{value:bt},E?u.createElement(r.Z,Object.assign({offsetTop:j,target:Me},gt),lt):lt))};const Pe=ge;Pe.Link=Q;var q=Pe}}]);

//# sourceMappingURL=p__experimental-procedure__conclusion__index.2bc8c684.async.js.map