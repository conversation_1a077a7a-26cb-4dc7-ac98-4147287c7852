"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[9197],{79090:function(te,F,n){var W=n(1413),m=n(67294),k=n(15294),p=n(84089),z=function(L,K){return m.createElement(p.Z,(0,W.Z)((0,W.Z)({},L),{},{ref:K,icon:k.Z}))},b=m.forwardRef(z);F.Z=b},38545:function(te,F,n){n.d(F,{Z:function(){return L}});var W=n(1413),m=n(67294),k={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"}}]},name:"message",theme:"outlined"},p=k,z=n(84089),b=function(c,O){return m.createElement(z.Z,(0,W.Z)((0,W.Z)({},c),{},{ref:O,icon:p}))},$=m.forwardRef(b),L=$},81012:function(te,F,n){n.d(F,{Z:function(){return $}});var W=n(97857),m=n.n(W),k=n(48054),p=n(67294),z=n(85893),b=(0,p.lazy)(function(){return Promise.all([n.e(6049),n.e(6369),n.e(6891)]).then(n.bind(n,99814)).then(function(L){return{default:L.default}})});function $(L){return(0,z.jsx)(p.Suspense,{fallback:(0,z.jsx)("div",{children:(0,z.jsx)(k.Z,{active:!0})}),children:(0,z.jsx)(b,m()({},L))})}},40802:function(te,F,n){n.d(F,{Z:function(){return w}});var W=n(81012),m=n(32884),k=n(38545),p=n(4393),z=n(66309),b=n(28036),$=n(55241),L=n(93967),K=n.n(L),c=n(70831),O={reactionCard:"reactionCard____5fXG",reactionInfo:"reactionInfo___U4SpH",reactionTitle:"reactionTitle___aJZR0",no:"no___Avx3R",title:"title___FMgkA",status:"status___xYs3R",routes:"routes___zdkV6",structure:"structure___CHZWI"},i=n(85893);function w(C){var H,M,D,t=C.reaction,U=C.isPlayground,G=C.onAction,ee=C.enableToReaction,V=C.displayProject,X=function(j){var se=[];return j.map(function(ae){return se.push(ae==null?void 0:ae.id)}),se},re=(0,c.useParams)(),g=re.id,e=(t==null?void 0:t.commendCount)&&Number(t==null?void 0:t.commendCount)>0,oe=e?"".concat((0,m.oz)("comment"),"\uFF08").concat(t==null?void 0:t.commendCount,"\uFF09"):(0,m.oz)("comment");return(0,i.jsxs)(p.Z,{className:O.reactionCard,children:[t!=null&&t.reaction?(0,i.jsx)(W.Z,{structure:t==null?void 0:t.reaction,className:K()(O.structure,"enablePointer"),clickEvent:ee?function(){var Q;c.history.push("/projects/".concat(g||((Q=t.project)===null||Q===void 0?void 0:Q.id),"/reaction/").concat(t==null?void 0:t.id))}:void 0}):"",(0,i.jsxs)("div",{className:O.reactionInfo,children:[(0,i.jsxs)("div",{className:K()(O.reactionTitle,"flex-justify-space-between"),children:[(0,i.jsxs)("div",{className:K()(O.no,"flex-justify-space-between"),children:[(0,i.jsxs)("span",{className:O.title,children:[(0,m.oz)("reaction-no"),t==null?void 0:t.id]}),U&&e&&t!==null&&t!==void 0&&t.updatedAt?(0,i.jsxs)("div",{children:["\xA0\xA0",(0,m.oz)("last-comment-date"),":"," ",(0,m.H3)(t==null?void 0:t.updatedAt)]}):"",t!=null&&t.collection_subject?(0,i.jsxs)("div",{children:["\xA0\xA0",(0,m.oz)("reaction-step-ID"),": ",t==null?void 0:t.collection_subject]}):""]}),(0,i.jsxs)("div",{className:O.status,children:[U?"":(0,i.jsx)(i.Fragment,{children:t!=null&&t.progress?(0,i.jsx)(z.Z,{color:"processing",children:(0,m.oz)("proceeded")}):(0,i.jsx)(z.Z,{color:"warning",children:(0,m.oz)("to-be-proceeded")})}),U?(0,i.jsx)(b.ZP,{type:"link",onClick:G,children:(0,i.jsxs)($.Z,{content:oe,children:[(0,i.jsx)(k.Z,{}),e?"\uFF08".concat(t==null?void 0:t.commendCount,"\uFF09"):""]})}):""]})]}),(0,i.jsxs)("div",{className:"display-flex",children:[(0,i.jsxs)("div",{children:[(0,m.oz)("nums-of-my-reactions"),"\uFF1A",t!=null&&(H=t.effective_procedures)!==null&&H!==void 0&&H.length?(0,i.jsx)("span",{className:"enablePointer",onClick:function(){var j;return c.history.push("/projects/".concat(g||((j=t.project)===null||j===void 0?void 0:j.id),"/reaction/").concat(t==null?void 0:t.id,"?tab=my-reaction-design"))},children:t==null||(M=t.effective_procedures)===null||M===void 0?void 0:M.length}):0]}),"\xA0\xA0\xA0",(0,i.jsxs)("div",{children:[(0,m.oz)("nums-of-my-experiments"),"\uFF1A",t!=null&&t.experiment_count?(0,i.jsx)("span",{className:"enablePointer",style:{color:"black"},onClick:function(){var j;return c.history.push("/projects/".concat(g||((j=t.project)===null||j===void 0?void 0:j.id),"/reaction/").concat(t==null?void 0:t.id,"?tab=my-experiment"))},children:t==null?void 0:t.experiment_count}):0]})]}),U?"":(0,i.jsxs)(i.Fragment,{children:[V&&(0,i.jsxs)("div",{className:"flex-align-items-center",children:[(0,i.jsxs)("div",{children:[(0,m.oz)("menu.list.project-list"),"\uFF1A"]}),(0,i.jsx)("div",{style:{color:"black"},children:(D=t.project)===null||D===void 0?void 0:D.no})]}),(0,i.jsxs)("div",{className:"display-flex",children:[(0,m.oz)("associated-routes"),"\uFF1A",(0,i.jsx)("span",{className:O.routes,children:(0,m.qt)(t==null?void 0:t.project_routes)?X(t==null?void 0:t.project_routes).join("\u3001"):"\u65E0"})]})]})]})]})}},33547:function(te,F,n){var W=n(5574),m=n.n(W),k=n(79090),p=n(96486),z=n.n(p),b=n(67294),$=n(85893),L=function(c){var O=c.title,i=c.getNumber,w=c.refetchEvent,C=(0,b.useState)(!1),H=m()(C,2),M=H[0],D=H[1],t=(0,b.useState)(),U=m()(t,2),G=U[0],ee=U[1];return(0,b.useEffect)(function(){var V=!1;if(i)return Promise.resolve(i()).then(function(X){return!V&&ee((0,p.isNil)(X)?void 0:X)}).finally(function(){return!V&&D(!1)}),function(){V=!0}},[i,w]),(0,$.jsxs)($.Fragment,{children:[O,M?(0,$.jsx)(k.Z,{}):(0,p.isNil)(G)?"":"(".concat(G,")")]})};F.Z=L},6407:function(te,F,n){n.r(F),n.d(F,{default:function(){return ye}});var W=n(9783),m=n.n(W),k=n(97857),p=n.n(k),z=n(5574),b=n.n(z),$=n(40802),L=n(48632),K=n(17322),c=n(32884),O=n(42525),i=n(11774),w=n(70831),C=n(71230),H=n(93967),M=n.n(H),D=n(67294),t=n(81012),U=n(69776),G=n(4393),ee=n(71471),V=n(66309),X=n(15746),re=n(92413),g={moleculeCardRoot:"moleculeCardRoot___Jg1tC",card:"card___Gaoxb",created:"created___OBFZ2",designing:"designing___H3Z0z",synthesizing:"synthesizing___TRFwR",finished:"finished___naWXu",canceled:"canceled___vI1xN",smileDrawer:"smileDrawer___kAbAk",moleculeInfo:"moleculeInfo___zUD0V",title:"title___j0tEl",routeInfo:"routeInfo___v5qHD",item:"item___y8hoN",label:"label___KroSY"},e=n(85893),oe=function(l){var R,y,_=l.compound,N=(0,re.HD)(),x=N.getById,f=(0,U.Z)(),E=f.moleculeStatusOptions,d=f.typeMap,h=(0,D.useState)([]),u=b()(h,2),r=u[0],o=u[1],a=(0,D.useState)(_),v=b()(a,2),s=v[0],A=v[1],B=s.compound,S=s.project,le=s.id,q=s.status,ue=s.priority,I=s.director_id,P=s.no,_e=s.type,Ce=s.retro_backbones_number,be=s.project_routes_number;return(0,D.useEffect)(function(){x(S==null?void 0:S.id).then(function(ne){return o(ne)})},[S==null?void 0:S.id]),(0,D.useEffect)(function(){A(_)},[_]),(0,e.jsx)("div",{className:g.moleculeCardRoot,children:(0,e.jsx)(G.Z,{className:M()("enablePointer",g.card,q&&g[q]),onClick:function(){return w.history.push("/projects/".concat(S==null?void 0:S.id,"/compound/").concat(le,"?page=1&pageSize=10"))},children:(0,e.jsxs)("div",{className:"flex-justify-space-between",children:[(0,e.jsx)("div",{className:"flex-auto",children:(0,e.jsx)(t.Z,{structure:(B==null?void 0:B.smiles)||"",className:M()("enablePointer",g.smileDrawer)})}),(0,e.jsxs)("div",{className:g.moleculeInfo,children:[(0,e.jsx)(C.Z,{children:(0,e.jsx)(ee.Z.Text,{strong:!0,style:{maxWidth:150},ellipsis:{tooltip:!0},children:P})}),(0,e.jsxs)(C.Z,{className:g.title,children:[(0,e.jsx)(V.Z,{color:"green",children:d[_e]}),(0,e.jsx)(X.Z,{span:12,children:(0,e.jsx)(V.Z,{color:"gold",children:(R=E.find(function(ne){return ne.value===q}))===null||R===void 0?void 0:R.label})})]}),(0,e.jsxs)("div",{className:g.routeInfo,children:[(0,e.jsxs)(C.Z,{className:M()(g.item,"flex-align-items-center"),children:[(0,e.jsxs)("div",{className:g.label,children:[(0,c.oz)("aiGenerated"),"\xA0\xA0"]}),(0,e.jsxs)("div",{children:[Ce||0," ",(0,c.Ig)()?"":"\u6761"]})]}),(0,e.jsxs)(C.Z,{className:M()(g.item,"flex-align-items-center"),children:[(0,e.jsxs)("div",{className:g.label,children:[(0,c.oz)("myRoutes"),"\xA0\xA0"]}),(0,e.jsxs)("div",{children:[be||0," ",(0,c.Ig)()?"":"\u6761"]})]})]}),(0,e.jsxs)(C.Z,{className:M()(g.item,"flex-align-items-center"),children:[(0,e.jsx)("div",{className:g.label,children:(0,c.oz)("pages.experiment.label.owner")}),(0,e.jsx)("div",{children:(y=r.find(function(ne){return ne.user_id===I}))===null||y===void 0||(y=y.user_info)===null||y===void 0?void 0:y.username})]}),(0,e.jsxs)(C.Z,{className:M()(g.item,"flex-align-items-center"),children:[(0,e.jsx)("div",{className:g.label,children:(0,c.oz)("menu.list.project-list")}),(0,e.jsx)("div",{children:(0,e.jsx)(ee.Z.Text,{style:{width:80},ellipsis:{tooltip:S==null?void 0:S.no},children:S==null?void 0:S.no})})]}),(0,e.jsxs)(C.Z,{className:M()(g.item,"flex-align-items-center"),children:[(0,e.jsx)("div",{className:g.label,children:(0,c.oz)("Priority")}),ue]})]})]})})})},Q=oe,j={projectSummary:"projectSummary___H_Jpl",titleContent:"titleContent___EbMnA",title:"title___gpcOD",reportLink:"reportLink___Hz6TY",card:"card___Bwx1A",numTitle:"numTitle___nbCNi",summaryLabel:"summaryLabel___ZCksY",num:"num___H4qNq",summary:"summary___uu554",detail:"detail___ye7NP",diff:"diff___KC7P4",diffValue:"diffValue___Jn6Yl"};function se(J){var l=J.workbenchTotalInfo,R={reaction:(0,c.oz)("reaction"),experiment:(0,c.oz)("pages.experiment"),compound:(0,c.oz)("molecules")},y=(0,D.useState)([]),_=b()(y,2),N=_[0],x=_[1],f=function(){var o=[{type:"compound",data:l==null?void 0:l.compound_key_stats,url:l==null?void 0:l.compound_url},{type:"reaction",data:l==null?void 0:l.reaction_key_stats,url:l==null?void 0:l.reaction_url},{type:"experiment",data:l==null?void 0:l.experiment_key_stats,url:l==null?void 0:l.experiment_url}];x(o)};(0,D.useEffect)(function(){f()},[l]);var E=function(o){var a=o.curItem;return(0,e.jsxs)(G.Z,{className:j.card,children:[(0,e.jsx)("div",{className:j.numTitle,children:a==null?void 0:a.actual_label}),(0,e.jsx)("div",{className:j.num,children:a==null?void 0:a.actual_value}),(0,e.jsxs)("div",{className:(j.detail,"flex-justify-space-between"),children:[(0,e.jsxs)("div",{children:[a==null?void 0:a.target_label,"\xA0\xA0",a==null?void 0:a.target_value]}),(0,e.jsxs)("div",{children:[a==null?void 0:a.progress_label,"\xA0\xA0",a==null?void 0:a.progress_value]})]}),(0,e.jsxs)("div",{className:j.detail,children:[a==null?void 0:a.last_period_label,"\xA0\xA0",a==null?void 0:a.last_period_value]})]})},d=function(o){var a=o.curItem;return(0,e.jsxs)(G.Z,{className:j.card,children:[(0,e.jsx)("div",{className:j.summaryLabel,children:a==null?void 0:a.rank_label}),(0,e.jsx)("div",{className:j.summary,children:a==null?void 0:a.rank_value}),(0,e.jsxs)("div",{className:j.diff,children:[a==null?void 0:a.last_period_label,"\xA0\xA0",(0,e.jsx)("span",{className:j.diffValue,children:a==null?void 0:a.last_period_value})]})]})},h=function(o){var a=o.type;return(0,e.jsxs)("div",{className:M()(j.titleContent,"flex-justify-space-between"),children:[(0,e.jsx)("div",{className:j.title,children:R[a]}),(0,e.jsx)("div",{className:(j.reportLink,"enablePointer"),onClick:function(){var s;w.history.push((s=N.find(function(A){return A.type===a}))===null||s===void 0?void 0:s.url)},children:"\u8BE6\u7EC6\u62A5\u8868"})]})},u=function(o){var a=o.curItem,v=a.hasOwnProperty("rank_label");return v?(0,e.jsx)(d,{curItem:a}):(0,e.jsx)(E,{curItem:a})};return(0,e.jsx)("div",{className:j.projectSummary,children:(0,c.qt)(N)?N==null?void 0:N.map(function(r){return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(h,{type:r==null?void 0:r.type}),(0,e.jsx)(C.Z,{gutter:[16,16],children:(0,c.qt)(r==null?void 0:r.data)?r==null?void 0:r.data.map(function(o){return(0,e.jsx)(X.Z,{span:12,children:(0,e.jsx)(u,{curItem:o})})}):""})]})}):""})}var ae=n(15009),Z=n.n(ae),ce=n(99289),Y=n.n(ce),ie=n(37507),de=n(87172),ve=n(43851),me=n(18536),fe=(0,me.LC)({path:ve.sS}),pe=n(96486),he=function(l){var R=(0,D.useState)(),y=b()(R,2),_=y[0],N=y[1],x=function(){var f=Y()(Z()().mark(function E(){var d;return Z()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return u.next=2,fe({routeParams:String(l)});case 2:d=u.sent,(0,ie.y6)(d).ok&&N(d==null?void 0:d.data);case 4:case"end":return u.stop()}},E)}));return function(){return f.apply(this,arguments)}}();return(0,D.useEffect)(function(){x()},[l]),{workbenchTotalInfo:_}},je=function(l){var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:3,y=function(){return(0,de.query)("project-compounds").equalTo("director_id",l).filterDeep("status","in",["created","designing"]).sortBy([{field:"updatedAt",order:"desc"}])},_=function(){var x=Y()(Z()().mark(function f(){var E,d,h,u;return Z()().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return E=y().paginate(1,R).populateWith("project_routes",["id"]).populateWith("compound",["smiles"]).populateWith("project",["id","no"]).notEqualTo("type","temp_block").populateDeep([{path:"retro_processes",fields:["id"],children:[{key:"retro_backbones",fields:["id"]}]}]),o.next=3,E.get();case 3:return d=o.sent,h=d.data,u=d.meta,h==null||h.forEach(function(a){var v,s;a.project_routes_number=(v=a.project_routes)===null||v===void 0?void 0:v.length,a.retro_backbones_number=(s=a.retro_processes)===null||s===void 0?void 0:s.flatMap(function(A){return A.retro_backbones}).length}),o.abrupt("return",{data:h||[],total:u==null?void 0:u.pagination.total,success:!!h});case 8:case"end":return o.stop()}},f)}));return function(){return x.apply(this,arguments)}}(),N=function(){var x=Y()(Z()().mark(function f(E){var d,h;return Z()().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,y().equalTo("status",E).paginate(1,1).get();case 2:return d=r.sent,h=d.meta,r.abrupt("return",(h==null?void 0:h.pagination.total)||0);case 5:case"end":return r.stop()}},f)}));return function(E){return x.apply(this,arguments)}}();return{fetchData:_,fetchNum:N}},xe=function(l){var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:3,y=(0,D.useState)(),_=b()(y,2),N=_[0],x=_[1],f=function(){var u=Y()(Z()().mark(function r(o){var a;return Z()().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:if(a=N,!(0,pe.isNil)(a)){s.next=6;break}return s.next=4,(0,re.sQ)(o);case 4:a=s.sent,x(a);case 6:return s.abrupt("return",a);case 7:case"end":return s.stop()}},r)}));return function(o){return u.apply(this,arguments)}}(),E=function(){var u=Y()(Z()().mark(function r(){var o;return Z()().wrap(function(v){for(;;)switch(v.prev=v.next){case 0:return v.next=2,f(l);case 2:return o=v.sent,v.abrupt("return",(0,de.query)("project-reactions").filterDeep("project_routes.id","in",o.length?o.map(function(s){return s.id}):[-1]).sortBy([{field:"updatedAt",order:"desc"}]));case 4:case"end":return v.stop()}},r)}));return function(){return u.apply(this,arguments)}}(),d=function(){var u=Y()(Z()().mark(function r(){var o,a,v,s;return Z()().wrap(function(B){for(;;)switch(B.prev=B.next){case 0:return B.next=2,E();case 2:return o=B.sent,o.paginate(1,R).populateWith("project",["id","no"]).populateDeep([{path:"project_routes",fields:["id","name"],children:[{key:"project_compound",fields:["id"]}]}]),B.next=6,o.get();case 6:return a=B.sent,v=a.data,s=a.meta,B.abrupt("return",{data:v||[],total:s==null?void 0:s.pagination.total,success:!!v});case 10:case"end":return B.stop()}},r)}));return function(){return u.apply(this,arguments)}}(),h=function(){var u=Y()(Z()().mark(function r(){var o,a,v;return Z()().wrap(function(A){for(;;)switch(A.prev=A.next){case 0:return A.next=2,E();case 2:return o=A.sent,A.next=5,o.paginate(1,1).get();case 5:return a=A.sent,v=a.meta,A.abrupt("return",(v==null?void 0:v.pagination.total)||0);case 8:case"end":return A.stop()}},r)}));return function(){return u.apply(this,arguments)}}();return{fetchData:d,fetchNum:h}},ge=function(l){var R=function(){var y=Y()(Z()().mark(function _(N){var x,f;return Z()().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:return d.next=2,(0,ie.Ky)({data:{experiment_owner:l,status:[N],page_no:1,page_size:1}});case 2:if(x=d.sent,!(0,ie.y6)(x).ok){d.next=5;break}return d.abrupt("return",((f=x.data)===null||f===void 0||(f=f.meta)===null||f===void 0?void 0:f.total)||0);case 5:return d.abrupt("return",0);case 6:case"end":return d.stop()}},_)}));return function(N){return y.apply(this,arguments)}}();return{fetchNum:R}},T={myWorkbench:"myWorkbench___zDM1V",projectSummary:"projectSummary___XgfoY",unfold:"unfold___nTCk_",fold:"fold___dzGho",detailInfo:"detailInfo___XzaFT",titleContent:"titleContent___d5pzo",subTitle:"subTitle___m3BSy",dot:"dot___iTo3j",reactionCard:"reactionCard___oW371"},Ne=null;function ye(){var J=(0,w.useModel)("@@initialState"),l=J.initialState,R=l===void 0?{}:l,y=R.userInfo,_=y===void 0?void 0:y,N=(0,w.useModel)("@@initialState"),x=N.initialState,f=(_==null?void 0:_.id)||0,E=(0,D.useState)({}),d=b()(E,2),h=d[0],u=d[1],r=he(f),o=r.workbenchTotalInfo,a=je(f),v=a.fetchData,s=a.fetchNum,A=xe(f),B=A.fetchData,S=A.fetchNum,le=ge(f),q=le.fetchNum;(0,D.useEffect)(function(){s("created").then(function(I){return u(function(P){return p()(p()({},P),{},{createdCompound:I})})}),s("designing").then(function(I){return u(function(P){return p()(p()({},P),{},{designingCompound:I})})}),q("created").then(function(I){return u(function(P){return p()(p()({},P),{},{createdExperiment:I})})}),q("running").then(function(I){return u(function(P){return p()(p()({},P),{},{runningExperiment:I})})}),q("completed").then(function(I){return u(function(P){return p()(p()({},P),{},{completedExperiment:I})})}),S().then(function(I){return u(function(P){return p()(p()({},P),{},{reaction:I})})})},[]);var ue=(0,e.jsx)(O.Rs,{ghost:!0,pagination:!1,showActions:"hover",rowSelection:!1,grid:{xs:1,sm:2,md:3,lg:3,xl:3,xxl:3},className:T.reactionCard,renderItem:function(P){return(0,e.jsx)($.Z,{reaction:P,enableToReaction:!0,displayProject:!0})},request:B});return(0,e.jsx)(i._z,{children:(0,e.jsxs)("section",{className:M()(T.myWorkbench,"flex-justify-space-between"),children:[(0,e.jsxs)("div",{className:M()(T.projectSummary,m()(m()({},T.unfold,!(x!=null&&x.isMenuCollapsed)),T.fold,x==null?void 0:x.isMenuCollapsed)),children:[(0,e.jsx)(K.Z,{word:(0,c.oz)("project-summary")}),(0,e.jsx)(se,{workbenchTotalInfo:o})]}),(0,e.jsxs)("div",{className:T.detailInfo,children:[(0,e.jsx)(K.Z,{word:"".concat((0,c.oz)("pages.my-workbench.todo-list"))}),(0,e.jsxs)(C.Z,{justify:"space-between",className:T.titleContent,children:[(0,e.jsxs)("div",{className:"display-flex",children:[(0,e.jsxs)("div",{className:T.subTitle,children:["\u5F85\u8BBE\u8BA1\u7684\u5206\u5B50\xA0\xA0",(0,e.jsxs)("span",{children:[h.createdCompound,"\u4E2A"]})]}),(0,e.jsx)("span",{className:T.dot,children:"\u2022"}),(0,e.jsxs)("div",{className:T.subTitle,children:["\u8BBE\u8BA1\u4E2D\u7684\u5206\u5B50\xA0\xA0",(0,e.jsxs)("span",{children:[h.designingCompound,"\u4E2A"]})]})]}),(0,e.jsx)("div",{className:"enablePointer",onClick:function(){return w.history.push("/workspace/my-compound")},children:(0,c.oz)("pages.projectTable.actionLabel.viewDetail")})]}),(0,e.jsx)(O.Rs,{ghost:!0,pagination:!1,showActions:"hover",rowSelection:!1,grid:{xs:2,sm:2,md:3,lg:3,xl:3,xxl:3},renderItem:function(P){return(0,e.jsx)(Q,{compound:P})},request:v}),(0,e.jsxs)(C.Z,{justify:"space-between",className:T.titleContent,children:[(0,e.jsxs)("div",{children:["\u5F85\u63A8\u8FDB\u7684\u53CD\u5E94\xA0\xA0",h.reaction,"\u4E2A"]}),(0,e.jsx)("div",{className:"enablePointer",onClick:function(){return w.history.push("/workspace/my-reaction")},children:(0,c.oz)("pages.projectTable.actionLabel.viewDetail")})]}),ue,(0,e.jsxs)(C.Z,{justify:"space-between",className:T.titleContent,children:[(0,e.jsxs)("div",{className:"display-flex",children:[(0,e.jsxs)("div",{className:T.subTitle,children:["\u5F85\u5F00\u59CB\u7684\u5B9E\u9A8C\xA0\xA0",(0,e.jsxs)("span",{children:[h.createdExperiment,"\u4E2A"]})]}),(0,e.jsx)("span",{className:T.dot,children:"\u2022"}),(0,e.jsxs)("div",{className:T.subTitle,children:["\u8FDB\u884C\u4E2D\u7684\u5B9E\u9A8C\xA0\xA0",(0,e.jsxs)("span",{children:[h.runningExperiment,"\u4E2A"]})]}),(0,e.jsx)("span",{className:T.dot,children:"\u2022"}),(0,e.jsxs)("div",{className:T.subTitle,children:["\u5F85\u7ED3\u8BBA\u7684\u5B9E\u9A8C\xA0\xA0",(0,e.jsxs)("span",{children:[h.completedExperiment,"\u4E2A"]})]})]}),(0,e.jsx)("div",{className:"enablePointer",onClick:function(){return w.history.push("/workspace/my-experiment")},children:(0,c.oz)("pages.projectTable.actionLabel.viewDetail")})]}),(0,e.jsx)(L.Z,{ownerId:8,isWorkbench:!0})]})]})})}}}]);

//# sourceMappingURL=p__workspace__my-workbench__index.b1177c8e.async.js.map