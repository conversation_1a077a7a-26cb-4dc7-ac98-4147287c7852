"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[4179],{26704:function(Ie,Q,i){i.d(Q,{uk:function(){return de},ZP:function(){return Se}});var y=i(1413),T=i(45987),O=i(67294),c=i(48054),g=i(4393),f=i(25378),le=i(96074),V=i(42075),t=i(85893),K=function(n){var s=n.padding;return(0,t.jsx)("div",{style:{padding:s||"0 24px"},children:(0,t.jsx)(le.Z,{style:{margin:0}})})},w={xs:2,sm:2,md:4,lg:4,xl:6,xxl:6},me=function(n){var s=n.size,r=n.active,o=(0,O.useMemo)(function(){return{lg:!0,md:!0,sm:!1,xl:!1,xs:!1,xxl:!1}},[]),m=(0,f.Z)()||o,a=Object.keys(m).filter(function(S){return m[S]===!0})[0]||"md",j=s===void 0?w[a]||6:s,M=function(u){return u===0?0:j>2?42:16};return(0,t.jsx)(g.Z,{bordered:!1,style:{marginBlockEnd:16},children:(0,t.jsx)("div",{style:{width:"100%",justifyContent:"space-between",display:"flex"},children:new Array(j).fill(null).map(function(S,u){return(0,t.jsxs)("div",{style:{borderInlineStart:j>2&&u===1?"1px solid rgba(0,0,0,0.06)":void 0,paddingInlineStart:M(u),flex:1,marginInlineEnd:u===0?16:0},children:[(0,t.jsx)(c.Z,{active:r,paragraph:!1,title:{width:100,style:{marginBlockStart:0}}}),(0,t.jsx)(c.Z.Button,{active:r,style:{height:48}})]},u)})})})},he=function(n){var s=n.active;return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.Z,{bordered:!1,style:{borderRadius:0},styles:{body:{padding:24}},children:(0,t.jsxs)("div",{style:{width:"100%",display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,t.jsx)("div",{style:{maxWidth:"100%",flex:1},children:(0,t.jsx)(c.Z,{active:s,title:{width:100,style:{marginBlockStart:0}},paragraph:{rows:1,style:{margin:0}}})}),(0,t.jsx)(c.Z.Button,{active:s,size:"small",style:{width:165,marginBlockStart:12}})]})}),(0,t.jsx)(K,{})]})},A=function(n){var s=n.size,r=n.active,o=r===void 0?!0:r,m=n.actionButton;return(0,t.jsxs)(g.Z,{bordered:!1,styles:{body:{padding:0}},children:[new Array(s).fill(null).map(function(a,j){return(0,t.jsx)(he,{active:!!o},j)}),m!==!1&&(0,t.jsx)(g.Z,{bordered:!1,style:{borderStartEndRadius:0,borderTopLeftRadius:0},styles:{body:{display:"flex",alignItems:"center",justifyContent:"center"}},children:(0,t.jsx)(c.Z.Button,{style:{width:102},active:o,size:"small"})})]})},J=function(n){var s=n.active;return(0,t.jsxs)("div",{style:{marginBlockEnd:16},children:[(0,t.jsx)(c.Z,{paragraph:!1,title:{width:185}}),(0,t.jsx)(c.Z.Button,{active:s,size:"small"})]})},q=function(n){var s=n.active;return(0,t.jsx)(g.Z,{bordered:!1,style:{borderBottomRightRadius:0,borderBottomLeftRadius:0},styles:{body:{paddingBlockEnd:8}},children:(0,t.jsxs)(V.Z,{style:{width:"100%",justifyContent:"space-between"},children:[(0,t.jsx)(c.Z.Button,{active:s,style:{width:200},size:"small"}),(0,t.jsxs)(V.Z,{children:[(0,t.jsx)(c.Z.Button,{active:s,size:"small",style:{width:120}}),(0,t.jsx)(c.Z.Button,{active:s,size:"small",style:{width:80}})]})]})})},_=function(n){var s=n.active,r=s===void 0?!0:s,o=n.statistic,m=n.actionButton,a=n.toolbar,j=n.pageHeader,M=n.list,S=M===void 0?5:M;return(0,t.jsxs)("div",{style:{width:"100%"},children:[j!==!1&&(0,t.jsx)(J,{active:r}),o!==!1&&(0,t.jsx)(me,{size:o,active:r}),(a!==!1||S!==!1)&&(0,t.jsxs)(g.Z,{bordered:!1,styles:{body:{padding:0}},children:[a!==!1&&(0,t.jsx)(q,{active:r}),S!==!1&&(0,t.jsx)(A,{size:S,active:r,actionButton:m})]})]})},d=_,oe={xs:1,sm:2,md:3,lg:3,xl:3,xxl:4},ce=function(n){var s=n.active;return(0,t.jsxs)("div",{style:{marginBlockStart:32},children:[(0,t.jsx)(c.Z.Button,{active:s,size:"small",style:{width:100,marginBlockEnd:16}}),(0,t.jsxs)("div",{style:{width:"100%",justifyContent:"space-between",display:"flex"},children:[(0,t.jsxs)("div",{style:{flex:1,marginInlineEnd:24,maxWidth:300},children:[(0,t.jsx)(c.Z,{active:s,paragraph:!1,title:{style:{marginBlockStart:0}}}),(0,t.jsx)(c.Z,{active:s,paragraph:!1,title:{style:{marginBlockStart:8}}}),(0,t.jsx)(c.Z,{active:s,paragraph:!1,title:{style:{marginBlockStart:8}}})]}),(0,t.jsx)("div",{style:{flex:1,alignItems:"center",justifyContent:"center"},children:(0,t.jsxs)("div",{style:{maxWidth:300,margin:"auto"},children:[(0,t.jsx)(c.Z,{active:s,paragraph:!1,title:{style:{marginBlockStart:0}}}),(0,t.jsx)(c.Z,{active:s,paragraph:!1,title:{style:{marginBlockStart:8}}})]})})]})]})},P=function(n){var s=n.size,r=n.active,o=(0,O.useMemo)(function(){return{lg:!0,md:!0,sm:!1,xl:!1,xs:!1,xxl:!1}},[]),m=(0,f.Z)()||o,a=Object.keys(m).filter(function(M){return m[M]===!0})[0]||"md",j=s===void 0?oe[a]||3:s;return(0,t.jsx)("div",{style:{width:"100%",justifyContent:"space-between",display:"flex"},children:new Array(j).fill(null).map(function(M,S){return(0,t.jsxs)("div",{style:{flex:1,paddingInlineStart:S===0?0:24,paddingInlineEnd:S===j-1?0:24},children:[(0,t.jsx)(c.Z,{active:r,paragraph:!1,title:{style:{marginBlockStart:0}}}),(0,t.jsx)(c.Z,{active:r,paragraph:!1,title:{style:{marginBlockStart:8}}}),(0,t.jsx)(c.Z,{active:r,paragraph:!1,title:{style:{marginBlockStart:8}}})]},S)})})},H=function(n){var s=n.active,r=n.header,o=r===void 0?!1:r,m=(0,O.useMemo)(function(){return{lg:!0,md:!0,sm:!1,xl:!1,xs:!1,xxl:!1}},[]),a=(0,f.Z)()||m,j=Object.keys(a).filter(function(S){return a[S]===!0})[0]||"md",M=oe[j]||3;return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{style:{display:"flex",background:o?"rgba(0,0,0,0.02)":"none",padding:"24px 8px"},children:[new Array(M).fill(null).map(function(S,u){return(0,t.jsx)("div",{style:{flex:1,paddingInlineStart:o&&u===0?0:20,paddingInlineEnd:32},children:(0,t.jsx)(c.Z,{active:s,paragraph:!1,title:{style:{margin:0,height:24,width:o?"75px":"100%"}}})},u)}),(0,t.jsx)("div",{style:{flex:3,paddingInlineStart:32},children:(0,t.jsx)(c.Z,{active:s,paragraph:!1,title:{style:{margin:0,height:24,width:o?"75px":"100%"}}})})]}),(0,t.jsx)(K,{padding:"0px 0px"})]})},G=function(n){var s=n.active,r=n.size,o=r===void 0?4:r;return(0,t.jsxs)(g.Z,{bordered:!1,children:[(0,t.jsx)(c.Z.Button,{active:s,size:"small",style:{width:100,marginBlockEnd:16}}),(0,t.jsx)(H,{header:!0,active:s}),new Array(o).fill(null).map(function(m,a){return(0,t.jsx)(H,{active:s},a)}),(0,t.jsx)("div",{style:{display:"flex",justifyContent:"flex-end",paddingBlockStart:16},children:(0,t.jsx)(c.Z,{active:s,paragraph:!1,title:{style:{margin:0,height:32,float:"right",maxWidth:"630px"}}})})]})},ge=function(n){var s=n.active;return(0,t.jsxs)(g.Z,{bordered:!1,style:{borderStartEndRadius:0,borderTopLeftRadius:0},children:[(0,t.jsx)(c.Z.Button,{active:s,size:"small",style:{width:100,marginBlockEnd:16}}),(0,t.jsx)(P,{active:s}),(0,t.jsx)(ce,{active:s})]})},xe=function(n){var s=n.active,r=s===void 0?!0:s,o=n.pageHeader,m=n.list;return(0,t.jsxs)("div",{style:{width:"100%"},children:[o!==!1&&(0,t.jsx)(J,{active:r}),(0,t.jsx)(ge,{active:r}),m!==!1&&(0,t.jsx)(K,{}),m!==!1&&(0,t.jsx)(G,{active:r,size:m})]})},ee=xe,je=function(n){var s=n.active,r=s===void 0?!0:s,o=n.pageHeader;return(0,t.jsxs)("div",{style:{width:"100%"},children:[o!==!1&&(0,t.jsx)(J,{active:r}),(0,t.jsx)(g.Z,{children:(0,t.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column",padding:128},children:[(0,t.jsx)(c.Z.Avatar,{size:64,style:{marginBlockEnd:32}}),(0,t.jsx)(c.Z.Button,{active:r,style:{width:214,marginBlockEnd:8}}),(0,t.jsx)(c.Z.Button,{active:r,style:{width:328},size:"small"}),(0,t.jsxs)(V.Z,{style:{marginBlockStart:24},children:[(0,t.jsx)(c.Z.Button,{active:r,style:{width:116}}),(0,t.jsx)(c.Z.Button,{active:r,style:{width:116}})]})]})})]})},te=je,p=["type"],de=function(n){var s=n.type,r=s===void 0?"list":s,o=(0,T.Z)(n,p);return r==="result"?(0,t.jsx)(te,(0,y.Z)({},o)):r==="descriptions"?(0,t.jsx)(ee,(0,y.Z)({},o)):(0,t.jsx)(d,(0,y.Z)({},o))},Se=de},17322:function(Ie,Q,i){i.d(Q,{Z:function(){return g}});var y=i(93967),T=i.n(y),O={sectionTitle:"sectionTitle___KIteW",extraCom:"extraCom___ymouh"},c=i(85893);function g(f){return(0,c.jsxs)("div",{className:T()(O.sectionTitle,f==null?void 0:f.wrapClassName),id:f==null?void 0:f.anchorId,children:[(0,c.jsx)("h2",{children:f==null?void 0:f.word}),f!=null&&f.extra?(0,c.jsx)("div",{className:O.extraCom,children:f==null?void 0:f.extra}):null]})}},12350:function(Ie,Q,i){i.r(Q),i.d(Q,{default:function(){return S}});var y=i(15009),T=i.n(y),O=i(99289),c=i.n(O),g=i(67294),f=Object.defineProperty,le=Object.getOwnPropertySymbols,V=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,K=(u,x,h)=>x in u?f(u,x,{enumerable:!0,configurable:!0,writable:!0,value:h}):u[x]=h,w=(u,x)=>{for(var h in x||(x={}))V.call(x,h)&&K(u,h,x[h]);if(le)for(var h of le(x))t.call(x,h)&&K(u,h,x[h]);return u};const me=u=>g.createElement("svg",w({viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},u),g.createElement("path",{d:"M13.89 4H10.1c-.55 0-1-.45-1-1s.45-1 1-1h3.79c.55 0 1 .45 1 1s-.45 1-1 1ZM19.16 6.8H4.84c-.55 0-1-.45-1-1s.45-1 1-1h14.32c.55 0 1 .45 1 1s-.45 1-1 1Z"}),g.createElement("path",{d:"M14.51 22H9.48c-1.51 0-2.77-1.14-2.91-2.64L5.32 6.42a1 1 0 0 1 .9-1.09.992.992 0 0 1 1.09.9l1.25 12.94c.05.48.44.84.92.84h5.03c.48 0 .88-.36.92-.84l1.25-12.94c.05-.55.54-.96 1.09-.9a1 1 0 0 1 .9 1.09l-1.25 12.94A2.913 2.913 0 0 1 14.51 22Z"}));var he="data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjQgMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEzLjg5IDRIMTAuMWMtLjU1IDAtMS0uNDUtMS0xcy40NS0xIDEtMWgzLjc5Yy41NSAwIDEgLjQ1IDEgMXMtLjQ1IDEtMSAxWk0xOS4xNiA2LjhINC44NGMtLjU1IDAtMS0uNDUtMS0xcy40NS0xIDEtMWgxNC4zMmMuNTUgMCAxIC40NSAxIDFzLS40NSAxLTEgMVoiLz48cGF0aCBkPSJNMTQuNTEgMjJIOS40OGMtMS41MSAwLTIuNzctMS4xNC0yLjkxLTIuNjRMNS4zMiA2LjQyYTEgMSAwIDAgMSAuOS0xLjA5Ljk5Mi45OTIgMCAwIDEgMS4wOS45bDEuMjUgMTIuOTRjLjA1LjQ4LjQ0Ljg0LjkyLjg0aDUuMDNjLjQ4IDAgLjg4LS4zNi45Mi0uODRsMS4yNS0xMi45NGMuMDUtLjU1LjU0LS45NiAxLjA5LS45YTEgMSAwIDAgMSAuOSAxLjA5bC0xLjI1IDEyLjk0QTIuOTEzIDIuOTEzIDAgMCAxIDE0LjUxIDIyWiIvPjwvc3ZnPg==",A=i(61487),J=i(19904),q=i(52006),_=i(87172),d=i(32884),oe=i(28508),ce=i(42525),P=i(18221),H=i(11774),G=i(70831),ge=i(28036),xe=i(96074),ee=i(42075),je=i(93967),te=i.n(je),p={messageNotification:"messageNotification___BuEnH",content:"content___w4nfj",center:"center___dbaNE",left:"left___zb3zv",right:"right___JLprp",commonContent:"commonContent___lBt9V","notification-list-root":"notification-list-root___mE7ct",delButton:"delButton___a_uSv","title-wrapper":"title-wrapper___ZFZtn",title:"title___op1Xd","title-actions-wrapper":"title-actions-wrapper___H78av","ant-list-item-extra":"ant-list-item-extra___XnbiE","actions-wrapper":"actions-wrapper___zbBDz","top-actions-wrapper":"top-actions-wrapper___lyNZ0","bottom-actions-wrapper":"bottom-actions-wrapper___Bprmw","notification-list":"notification-list___TZLfS",commonTag:"commonTag___Ex_AY",statusDes_completed:"statusDes_completed___lHPVc",statusDes_success:"statusDes_success___EBEQR",statusDes_running:"statusDes_running___NTokl",statusDes_limited:"statusDes_limited___KiQ2t",statusDes_pending:"statusDes_pending___ZGN74",statusDes_failed:"statusDes_failed___ijIBi",wrapSectionTitle:"wrapSectionTitle___wLSE9",listContent:"listContent___QzPh5",systemMessages:"systemMessages___hj9lh",readText:"readText___VKLaG",unReadText:"unReadText___ZluX2",divider:"divider___lOMXL",extraContent:"extraContent___Mwenb"},de=i(17322),Se=i(5574),e=i.n(Se),n=i(96486),s=function(x){var h,N=x.readOnly,L=(0,A.f)(),E=L.fetch,Y=L.loading,F=(0,G.useModel)("@@initialState"),Z=F.initialState,ue=(0,g.useState)({}),ae=e()(ue,2),ne=ae[0],ye=ae[1],D=(0,g.useState)({page:1,pageSize:10}),B=e()(D,2),v=B[0],l=B[1],z=(0,g.useState)([]),C=e()(z,2),b=C[0],U=C[1],$=(0,g.useState)(0),X=e()($,2),fe=X[0],ve=X[1],ze=function(R){var re=[];return R==null||R.map(function(I){if((I==null?void 0:I.system_message)!==null){var ie=(0,d.oz)("test-report-inference-success-message"),be=ie.replace(/check_type|experiment_no|report_url/g,function(pe){var Me,W,Te,Be;switch(pe){case"check_type":return(I==null||(Me=I.system_message)===null||Me===void 0?void 0:Me.check_type)==="M"?(0,d.oz)("intermediate-detection"):(0,d.oz)("product-detection");case"experiment_no":return I==null||(W=I.system_message)===null||W===void 0?void 0:W.experiment_no;case"report_url":return I==null||(Te=I.system_message)===null||Te===void 0?void 0:Te.report_url;case"robot_start_handle_time":return(0,d.H3)(I==null||(Be=I.system_message)===null||Be===void 0?void 0:Be.robot_start_handle_time);default:return pe}});I.system_message.message=be,re.push(I)}}),re},se=function(){var k=c()(T()().mark(function R(){var re,I,ie,be,pe;return T()().wrap(function(W){for(;;)switch(W.prev=W.next){case 0:return W.next=2,(0,_.query)("message-readers").filterDeep("user_id","eq",Z==null||(re=Z.userInfo)===null||re===void 0?void 0:re.id).populate(["system_message"]).sortBy([{field:"createdAt",order:"desc"}]);case 2:return I=W.sent,N&&I.equalTo("readed",!0),W.next=6,E(I.get());case 6:return ie=W.sent,be=ie.data,pe=ie.meta,ve((pe==null?void 0:pe.pagination.total)||0),W.abrupt("return",(0,d.qt)(be)?ze(be):[]);case 11:case"end":return W.stop()}},R)}));return function(){return k.apply(this,arguments)}}(),Ce=function(){var k=c()(T()().mark(function R(re){return T()().wrap(function(ie){for(;;)switch(ie.prev=ie.next){case 0:U(re);case 1:case"end":return ie.stop()}},R)}));return function(re){return k.apply(this,arguments)}}();return(0,g.useEffect)(function(){var k;Z!=null&&(k=Z.userInfo)!==null&&k!==void 0&&k.id&&se().then(function(R){return!(0,n.isNil)(R)&&Ce(R)})},[v,N,Z==null||(h=Z.userInfo)===null||h===void 0?void 0:h.id]),(0,g.useEffect)(function(){l({page:1,pageSize:10})},[ne]),{loading:Y,paginate:v,systemMessagesDatas:b,total:fe,setPaginate:l,updateSystemMessagesDatas:Ce}},r=s,o=i(72269),m=i(66309),a=i(85893);function j(){var u=(0,g.useState)(!1),x=e()(u,2),h=x[0],N=x[1],L=r({readOnly:h}),E=L.loading,Y=L.systemMessagesDatas,F=L.paginate,Z=L.total,ue=L.setPaginate,ae=L.updateSystemMessagesDatas,ne=(0,A.f)(),ye=ne.updateByStrapi,D=function(v){ye("message-readers/".concat(v),{readed:!0},function(){var l=(0,n.cloneDeep)(Y),z=l.find(function(C){return(C==null?void 0:C.id)===v});z.readed=!0,ae(l)})};return(0,a.jsxs)("div",{className:p.systemMessages,children:[(0,a.jsx)(de.Z,{word:(0,d.oz)("message"),wrapClassName:p.wrapSectionTitle,extra:(0,a.jsxs)("div",{className:"flex-align-items-center",style:{height:"40px"},children:[(0,a.jsx)("span",{children:(0,d.oz)("show-read-messages")}),"\xA0",(0,a.jsx)(o.Z,{checkedChildren:(0,d.oz)("yes"),unCheckedChildren:(0,d.oz)("no"),defaultChecked:!1,onChange:function(v){return N(v)}})]})}),(0,a.jsx)(xe.Z,{className:p.divider,orientationMargin:0}),(0,a.jsx)(ce.Rs,{loading:E,dataSource:Y,className:p.listContent,pagination:{current:F.page,pageSize:F.pageSize,simple:!0,size:"small",total:Z,onChange:function(v,l){return ue({page:v,pageSize:l})}},itemLayout:"vertical",metas:{content:{render:function(v,l){return(0,a.jsxs)(P.vY,{dataSource:l,column:1,className:te()({enablePointer:!(l!=null&&l.readed)}),onClick:function(){l!=null&&l.readed||!(l!=null&&l.id)||D(l==null?void 0:l.id)},children:[(0,a.jsx)(P.vY.Item,{dataIndex:"event_level",render:function(C,b){var U,$,X=b==null||(U=b.system_message)===null||U===void 0?void 0:U.message_level;return X?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m.Z,{color:"magenta",children:X}),(0,a.jsx)("div",{dangerouslySetInnerHTML:{__html:b==null||($=b.system_message)===null||$===void 0?void 0:$.message}})]}):""}}),(0,a.jsx)(P.vY.Item,{dataIndex:"createdAt",render:function(C,b){var U,$,X=b==null||(U=b.system_message)===null||U===void 0?void 0:U.createdAt,fe=b==null||($=b.system_message)===null||$===void 0?void 0:$.send_from;return X?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,d.oz)("send-time"),"\uFF1A",(0,d.H3)(X)]}),"\xA0\xA0",(0,a.jsxs)("div",{children:[(0,d.oz)("send-from"),"\uFF1A",fe||"-"]})]}):""}})]})}},extra:{render:function(v,l){return(0,a.jsx)("div",{className:te()(p.extraContent,{enablePointer:!(l!=null&&l.readed)}),onClick:function(){l!=null&&l.readed||!(l!=null&&l.id)||D(l==null?void 0:l.id)},children:(0,a.jsx)("div",{children:l!=null&&l.readed?(0,a.jsx)("span",{className:p.readText,children:(0,d.oz)("read")}):(0,a.jsx)("span",{className:p.unReadText,children:(0,d.oz)("unread")})})})}}}})]})}var M=["limited","pending","running"];function S(){var u=(0,A.f)(),x=u.fetch,h=function(v){var l=document.location.pathname===new URL(v||"",document.location.origin).pathname;l?G.history.go(0):v&&G.history.push(v)},N=(0,J.Z)({unreadOnly:!0,handleCheck:h}),L=N.total,E=N.paginate,Y=N.setPaginate,F=N.notifications,Z=N.refetch,ue=N.loading,ae=function(){var B=c()(T()().mark(function v(l){return T()().wrap(function(C){for(;;)switch(C.prev=C.next){case 0:return C.next=2,x((0,_.service)("job-notifications").deleteOne(l));case 2:Z();case 3:case"end":return C.stop()}},v)}));return function(l){return B.apply(this,arguments)}}(),ne=function(){var B=c()(T()().mark(function v(l){return T()().wrap(function(C){for(;;)switch(C.prev=C.next){case 0:return C.next=2,x((0,_.service)("job-notifications").update(l,{readed:!0}));case 2:Z();case 3:case"end":return C.stop()}},v)}));return function(l){return B.apply(this,arguments)}}(),ye=function(){var B=c()(T()().mark(function v(){return T()().wrap(function(z){for(;;)switch(z.prev=z.next){case 0:return z.next=2,x((0,_.query)("job-notifications/clean",{method:"POST"}).get());case 2:Z();case 3:case"end":return z.stop()}},v)}));return function(){return B.apply(this,arguments)}}(),D=(0,a.jsxs)("div",{className:p["notification-list-root"],children:[(0,a.jsx)(de.Z,{word:(0,d.oz)("pages.Notification.task"),wrapClassName:p.wrapSectionTitle,extra:(0,a.jsx)(ge.ZP,{type:"link",className:te()(p.delButton,"flex-align-items-center"),icon:(0,a.jsx)(me,{width:18,fill:"#1677FF"}),onClick:ye,children:(0,d.oz)("pages.Notification.clear-tasks")})}),(0,a.jsx)(xe.Z,{className:p.divider,orientationMargin:0}),(0,a.jsx)(ce.Rs,{loading:ue,dataSource:F,className:p.listContent,pagination:{current:E.page,pageSize:E.pageSize,simple:!0,size:"small",total:L,onChange:function(v,l){return Y({page:v,pageSize:l})}},itemLayout:"vertical",metas:{content:{render:function(v,l){return(0,a.jsxs)(P.vY,{dataSource:l,column:1,children:[(0,a.jsx)(P.vY.Item,{label:(0,d.oz)("pages.Notification.task-no"),dataIndex:"job_id",render:function(C,b){return(0,a.jsxs)(ee.Z,{children:[b.name||b.job_id||"-",(0,a.jsx)("span",{className:te()(p.statusDes,p.commonTag,p["statusDes_".concat(b.status)]),style:{minWidth:(0,d.Ig)()?"46px":"32px",maxWidth:(0,d.Ig)()?"200px":"64px"},children:(0,d.oz)("component.notification.statusValue.".concat(b.status))})]})}}),(0,a.jsx)(P.vY.Item,{label:(0,d.oz)("project-type"),dataIndex:"job_type",valueEnum:{retro:{text:(0,d.oz)("component.notification.typeValue.retro")}}}),["limited","pending"].includes(l.status)?(0,a.jsxs)(a.Fragment,{children:[l.status==="pending"?(0,a.jsx)(P.vY.Item,{label:(0,d.oz)("tasks-in-queue"),dataIndex:"queue_count"}):"",(0,a.jsx)(P.vY.Item,{label:(0,d.oz)("estimate-start"),dataIndex:"predict_start_time"})]}):""]})}},extra:{render:function(v,l){return(0,a.jsxs)("div",{className:p["actions-wrapper"],children:[(0,a.jsx)("div",{className:p["top-actions-wrapper"],children:l.status!=="running"&&(0,a.jsx)(ge.ZP,{type:"ghost",onClick:function(){return ne(l.id)},size:"small",children:(0,a.jsx)(oe.Z,{})})}),(0,a.jsx)("div",{className:p["bottom-actions-wrapper"],children:(0,a.jsxs)(ee.Z.Compact,{className:p["bottom-actions-wrapepr"],children:[(0,a.jsx)(ge.ZP,{size:"small",type:"link",onClick:function(){return h(l.access_url)},children:(0,d.oz)("pages.projectTable.actionLabel.viewDetail")}),M.includes(l.status)&&(0,a.jsx)(q.Z,{buttonProps:{size:"small"},buttonText:(0,d.oz)("pages.experiment.label.operation.cancel"),title:(0,d.oz)("cancel-task-confirm"),type:"link",onConfirm:function(){return ae(l.id)},description:""},"cancel")]})})]})}}}})]});return(0,a.jsx)(H._z,{className:te()(p.messageNotification),children:(0,a.jsxs)("article",{className:p.content,children:[(0,a.jsx)("div",{className:p.left,children:(0,a.jsx)(j,{})}),(0,a.jsx)("div",{className:p.center}),(0,a.jsx)("div",{className:p.right,children:D})]})})}},52006:function(Ie,Q,i){var y=i(97857),T=i.n(y),O=i(5574),c=i.n(O),g=i(86738),f=i(28036),le=i(67294),V=i(85893),t=function(w){var me=w.title,he=w.description,A=w.onConfirm,J=w.type,q=w.disabled,_=w.buttonText,d=w.buttonProps,oe=(0,le.useState)(!1),ce=c()(oe,2),P=ce[0],H=ce[1];return(0,V.jsx)(g.Z,{title:me,description:he,open:P,onOpenChange:H,onConfirm:function(){H(!1),A==null||A()},onCancel:function(){return H(!1)},children:(0,V.jsx)(f.ZP,T()(T()({},d),{},{type:J,onClick:function(){return H(!0)},disabled:q,children:_}))})};Q.Z=t},66309:function(Ie,Q,i){i.d(Q,{Z:function(){return Se}});var y=i(67294),T=i(93967),O=i.n(T),c=i(98423),g=i(98787),f=i(69760),le=i(96159),V=i(45353),t=i(53124),K=i(85982),w=i(10274),me=i(14747),he=i(83262),A=i(83559);const J=e=>{const{paddingXXS:n,lineWidth:s,tagPaddingHorizontal:r,componentCls:o,calc:m}=e,a=m(r).sub(s).equal(),j=m(n).sub(s).equal();return{[o]:Object.assign(Object.assign({},(0,me.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:a,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,K.unit)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${o}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${o}-close-icon`]:{marginInlineStart:j,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${o}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${o}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:a}}),[`${o}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},q=e=>{const{lineWidth:n,fontSizeIcon:s,calc:r}=e,o=e.fontSizeSM;return(0,he.mergeToken)(e,{tagFontSize:o,tagLineHeight:(0,K.unit)(r(e.lineHeightSM).mul(o).equal()),tagIconSize:r(s).sub(r(n).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},_=e=>({defaultBg:new w.C(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var d=(0,A.I$)("Tag",e=>{const n=q(e);return J(n)},_),oe=function(e,n){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(s[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)n.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(s[r[o]]=e[r[o]]);return s},P=y.forwardRef((e,n)=>{const{prefixCls:s,style:r,className:o,checked:m,onChange:a,onClick:j}=e,M=oe(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:S,tag:u}=y.useContext(t.E_),x=F=>{a==null||a(!m),j==null||j(F)},h=S("tag",s),[N,L,E]=d(h),Y=O()(h,`${h}-checkable`,{[`${h}-checkable-checked`]:m},u==null?void 0:u.className,o,L,E);return N(y.createElement("span",Object.assign({},M,{ref:n,style:Object.assign(Object.assign({},r),u==null?void 0:u.style),className:Y,onClick:x})))}),H=i(98719);const G=e=>(0,H.Z)(e,(n,s)=>{let{textColor:r,lightBorderColor:o,lightColor:m,darkColor:a}=s;return{[`${e.componentCls}${e.componentCls}-${n}`]:{color:r,background:m,borderColor:o,"&-inverse":{color:e.colorTextLightSolid,background:a,borderColor:a},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}});var ge=(0,A.bk)(["Tag","preset"],e=>{const n=q(e);return G(n)},_);function xe(e){return typeof e!="string"?e:e.charAt(0).toUpperCase()+e.slice(1)}const ee=(e,n,s)=>{const r=xe(s);return{[`${e.componentCls}${e.componentCls}-${n}`]:{color:e[`color${s}`],background:e[`color${r}Bg`],borderColor:e[`color${r}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var je=(0,A.bk)(["Tag","status"],e=>{const n=q(e);return[ee(n,"success","Success"),ee(n,"processing","Info"),ee(n,"error","Error"),ee(n,"warning","Warning")]},_),te=function(e,n){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(s[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)n.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(s[r[o]]=e[r[o]]);return s};const de=y.forwardRef((e,n)=>{const{prefixCls:s,className:r,rootClassName:o,style:m,children:a,icon:j,color:M,onClose:S,bordered:u=!0,visible:x}=e,h=te(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:N,direction:L,tag:E}=y.useContext(t.E_),[Y,F]=y.useState(!0),Z=(0,c.Z)(h,["closeIcon","closable"]);y.useEffect(()=>{x!==void 0&&F(x)},[x]);const ue=(0,g.o2)(M),ae=(0,g.yT)(M),ne=ue||ae,ye=Object.assign(Object.assign({backgroundColor:M&&!ne?M:void 0},E==null?void 0:E.style),m),D=N("tag",s),[B,v,l]=d(D),z=O()(D,E==null?void 0:E.className,{[`${D}-${M}`]:ne,[`${D}-has-color`]:M&&!ne,[`${D}-hidden`]:!Y,[`${D}-rtl`]:L==="rtl",[`${D}-borderless`]:!u},r,o,v,l),C=ve=>{ve.stopPropagation(),S==null||S(ve),!ve.defaultPrevented&&F(!1)},[,b]=(0,f.Z)((0,f.w)(e),(0,f.w)(E),{closable:!1,closeIconRender:ve=>{const ze=y.createElement("span",{className:`${D}-close-icon`,onClick:C},ve);return(0,le.wm)(ve,ze,se=>({onClick:Ce=>{var k;(k=se==null?void 0:se.onClick)===null||k===void 0||k.call(se,Ce),C(Ce)},className:O()(se==null?void 0:se.className,`${D}-close-icon`)}))}}),U=typeof h.onClick=="function"||a&&a.type==="a",$=j||null,X=$?y.createElement(y.Fragment,null,$,a&&y.createElement("span",null,a)):a,fe=y.createElement("span",Object.assign({},Z,{ref:n,className:z,style:ye}),X,b,ue&&y.createElement(ge,{key:"preset",prefixCls:D}),ae&&y.createElement(je,{key:"status",prefixCls:D}));return B(U?y.createElement(V.Z,{component:"Tag"},fe):fe)});de.CheckableTag=P;var Se=de}}]);

//# sourceMappingURL=p__message-notification__index.e88cec57.async.js.map