!(function(){var Xt=(xe,G)=>(G=Symbol[xe])?G:Symbol.for("Symbol."+xe),li=xe=>{throw TypeError(xe)};var ui=function(xe,G){this[0]=xe,this[1]=G};var Yt=xe=>{var G=xe[Xt("asyncIterator")],d=!1,Y,q={};return G==null?(G=xe[Xt("iterator")](),Y=Q=>q[Q]=ae=>G[Q](ae)):(G=G.call(xe),Y=Q=>q[Q]=ae=>{if(d){if(d=!1,Q==="throw")throw ae;return ae}return d=!0,{done:!1,value:new ui(new Promise(le=>{var ce=G[Q](ae);ce instanceof Object||li("Object expected"),le(ce)}),1)}}),q[Xt("iterator")]=()=>q,Y("next"),"throw"in G?Y("throw"):q.throw=Q=>{throw Q},"return"in G&&Y("return"),q};(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[6301],{78284:function(xe,G,d){"use strict";d.d(G,{q:function(){return H}});var Y=d(1413),q=d(87462),Q=d(67294),ae=d(62480),le=d(65555),ce=function($,F){return Q.createElement(le.Z,(0,q.Z)({},$,{ref:F,icon:ae.Z}))},ue=Q.forwardRef(ce),ve=ue,J=d(97183),g=d(28459),u=d(93967),Me=d.n(u),de=d(4942),oe=d(98082),b=function($){return(0,de.Z)({},$.componentCls,{marginBlock:0,marginBlockStart:48,marginBlockEnd:24,marginInline:0,paddingBlock:0,paddingInline:16,textAlign:"center","&-list":{marginBlockEnd:8,color:$.colorTextSecondary,"&-link":{color:$.colorTextSecondary,textDecoration:$.linkDecoration},"*:not(:last-child)":{marginInlineEnd:8},"&:hover":{color:$.colorPrimary}},"&-copyright":{fontSize:"14px",color:$.colorText}})};function s(U){return(0,oe.Xj)("ProLayoutFooter",function($){var F=(0,Y.Z)((0,Y.Z)({},$),{},{componentCls:".".concat(U)});return[b(F)]})}var M=d(85893),V=function($){var F=$.className,y=$.prefixCls,p=$.links,h=$.copyright,P=$.style,R=(0,Q.useContext)(g.ZP.ConfigContext),j=R.getPrefixCls(y||"pro-global-footer"),ee=s(j),A=ee.wrapSSR,se=ee.hashId;return(p==null||p===!1||Array.isArray(p)&&p.length===0)&&(h==null||h===!1)?null:A((0,M.jsxs)("div",{className:Me()(j,se,F),style:P,children:[p&&(0,M.jsx)("div",{className:"".concat(j,"-list ").concat(se).trim(),children:p.map(function(me){return(0,M.jsx)("a",{className:"".concat(j,"-list-link ").concat(se).trim(),title:me.key,target:me.blankTarget?"_blank":"_self",href:me.href,rel:"noreferrer",children:me.title},me.key)})}),h&&(0,M.jsx)("div",{className:"".concat(j,"-copyright ").concat(se).trim(),children:h})]}))},W=J.Z.Footer,H=function($){var F=$.links,y=$.copyright,p=$.style,h=$.className,P=$.prefixCls;return(0,M.jsx)(W,{className:h,style:(0,Y.Z)({padding:0},p),children:(0,M.jsx)(V,{links:F,prefixCls:P,copyright:y===!1?null:(0,M.jsxs)(Q.Fragment,{children:[(0,M.jsx)(ve,{})," ",y]})})})}},83832:function(xe,G,d){"use strict";d.d(G,{S:function(){return ue}});var Y=d(1413),q=d(45987),Q=d(74330),ae=d(67294),le=d(85893),ce=["isLoading","pastDelay","timedOut","error","retry"],ue=function(J){var g=J.isLoading,u=J.pastDelay,Me=J.timedOut,de=J.error,oe=J.retry,b=(0,q.Z)(J,ce);return(0,le.jsx)("div",{style:{paddingBlockStart:100,textAlign:"center"},children:(0,le.jsx)(Q.Z,(0,Y.Z)({size:"large"},b))})}},76509:function(xe,G,d){"use strict";d.d(G,{X:function(){return q}});var Y=d(67294),q=(0,Y.createContext)({})},65555:function(xe,G,d){"use strict";d.d(G,{Z:function(){return $}});var Y=d(87462),q=d(97685),Q=d(4942),ae=d(45987),le=d(67294),ce=d(93967),ue=d.n(ce),ve=d(87646),J=d(2446),g=d(1413),u=d(14004),Me=["icon","className","onClick","style","primaryColor","secondaryColor"],de={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function oe(F){var y=F.primaryColor,p=F.secondaryColor;de.primaryColor=y,de.secondaryColor=p||(0,u.pw)(y),de.calculated=!!p}function b(){return(0,g.Z)({},de)}var s=function(y){var p=y.icon,h=y.className,P=y.onClick,R=y.style,j=y.primaryColor,ee=y.secondaryColor,A=(0,ae.Z)(y,Me),se=le.useRef(),me=de;if(j&&(me={primaryColor:j,secondaryColor:ee||(0,u.pw)(j)}),(0,u.C3)(se),(0,u.Kp)((0,u.r)(p),"icon should be icon definiton, but got ".concat(p)),!(0,u.r)(p))return null;var be=p;return be&&typeof be.icon=="function"&&(be=(0,g.Z)((0,g.Z)({},be),{},{icon:be.icon(me.primaryColor,me.secondaryColor)})),(0,u.R_)(be.icon,"svg-".concat(be.name),(0,g.Z)((0,g.Z)({className:h,onClick:P,style:R,"data-icon":be.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},A),{},{ref:se}))};s.displayName="IconReact",s.getTwoToneColors=b,s.setTwoToneColors=oe;var M=s;function V(F){var y=(0,u.H9)(F),p=(0,q.Z)(y,2),h=p[0],P=p[1];return M.setTwoToneColors({primaryColor:h,secondaryColor:P})}function W(){var F=M.getTwoToneColors();return F.calculated?[F.primaryColor,F.secondaryColor]:F.primaryColor}var H=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];V(ve.blue.primary);var U=le.forwardRef(function(F,y){var p=F.className,h=F.icon,P=F.spin,R=F.rotate,j=F.tabIndex,ee=F.onClick,A=F.twoToneColor,se=(0,ae.Z)(F,H),me=le.useContext(J.Z),be=me.prefixCls,je=be===void 0?"anticon":be,He=me.rootClassName,Ee=ue()(He,je,(0,Q.Z)((0,Q.Z)({},"".concat(je,"-").concat(h.name),!!h.name),"".concat(je,"-spin"),!!P||h.name==="loading"),p),X=j;X===void 0&&ee&&(X=-1);var ye=R?{msTransform:"rotate(".concat(R,"deg)"),transform:"rotate(".concat(R,"deg)")}:void 0,pe=(0,u.H9)(A),Ne=(0,q.Z)(pe,2),Ue=Ne[0],ot=Ne[1];return le.createElement("span",(0,Y.Z)({role:"img","aria-label":h.name},se,{ref:y,tabIndex:X,onClick:ee,className:Ee}),le.createElement(M,{icon:h,primaryColor:Ue,secondaryColor:ot,style:ye}))});U.displayName="AntdIcon",U.getTwoToneColor=W,U.setTwoToneColor=V;var $=U},2446:function(xe,G,d){"use strict";var Y=d(67294),q=(0,Y.createContext)({});G.Z=q},14004:function(xe,G,d){"use strict";d.d(G,{C3:function(){return V},H9:function(){return b},Kp:function(){return g},R_:function(){return de},pw:function(){return oe},r:function(){return u},vD:function(){return s}});var Y=d(1413),q=d(71002),Q=d(87646),ae=d(44958),le=d(27571),ce=d(80334),ue=d(67294),ve=d(2446);function J(W){return W.replace(/-(.)/g,function(H,U){return U.toUpperCase()})}function g(W,H){(0,ce.ZP)(W,"[@ant-design/icons] ".concat(H))}function u(W){return(0,q.Z)(W)==="object"&&typeof W.name=="string"&&typeof W.theme=="string"&&((0,q.Z)(W.icon)==="object"||typeof W.icon=="function")}function Me(){var W=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Object.keys(W).reduce(function(H,U){var $=W[U];switch(U){case"class":H.className=$,delete H.class;break;default:delete H[U],H[J(U)]=$}return H},{})}function de(W,H,U){return U?ue.createElement(W.tag,(0,Y.Z)((0,Y.Z)({key:H},Me(W.attrs)),U),(W.children||[]).map(function($,F){return de($,"".concat(H,"-").concat(W.tag,"-").concat(F))})):ue.createElement(W.tag,(0,Y.Z)({key:H},Me(W.attrs)),(W.children||[]).map(function($,F){return de($,"".concat(H,"-").concat(W.tag,"-").concat(F))}))}function oe(W){return(0,Q.generate)(W)[0]}function b(W){return W?Array.isArray(W)?W:[W]:[]}var s={width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",focusable:"false"},M=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,V=function(H){var U=(0,ue.useContext)(ve.Z),$=U.csp,F=U.prefixCls,y=M;F&&(y=y.replace(/anticon/g,F)),(0,ue.useEffect)(function(){var p=H.current,h=(0,le.A)(p);(0,ae.hq)(y,"@ant-design-icons",{prepend:!0,csp:$,attachTo:h})},[])}},90743:function(xe,G){var d;function Y(b){"@babel/helpers - typeof";return Y=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(s){return typeof s}:function(s){return s&&typeof Symbol=="function"&&s.constructor===Symbol&&s!==Symbol.prototype?"symbol":typeof s},Y(b)}d={value:!0},G.Bo=d=d=d=d=d=d=void 0;function q(b){for(var s=[],M=0;M<b.length;){var V=b[M];if(V==="*"||V==="+"||V==="?"){s.push({type:"MODIFIER",index:M,value:b[M++]});continue}if(V==="\\"){s.push({type:"ESCAPED_CHAR",index:M++,value:b[M++]});continue}if(V==="{"){s.push({type:"OPEN",index:M,value:b[M++]});continue}if(V==="}"){s.push({type:"CLOSE",index:M,value:b[M++]});continue}if(V===":"){for(var W="",H=M+1;H<b.length;){var U=b.charCodeAt(H);if(U>=48&&U<=57||U>=65&&U<=90||U>=97&&U<=122||U===95){W+=b[H++];continue}break}if(!W)throw new TypeError("Missing parameter name at "+M);s.push({type:"NAME",index:M,value:W}),M=H;continue}if(V==="("){var $=1,F="",H=M+1;if(b[H]==="?")throw new TypeError('Pattern cannot start with "?" at '+H);for(;H<b.length;){if(b[H]==="\\"){F+=b[H++]+b[H++];continue}if(b[H]===")"){if($--,$===0){H++;break}}else if(b[H]==="("&&($++,b[H+1]!=="?"))throw new TypeError("Capturing groups are not allowed at "+H);F+=b[H++]}if($)throw new TypeError("Unbalanced pattern at "+M);if(!F)throw new TypeError("Missing pattern at "+M);s.push({type:"PATTERN",index:M,value:F}),M=H;continue}s.push({type:"CHAR",index:M,value:b[M++]})}return s.push({type:"END",index:M,value:""}),s}function Q(b,s){s===void 0&&(s={});for(var M=q(b),V=s.prefixes,W=V===void 0?"./":V,H="[^"+ve(s.delimiter||"/#?")+"]+?",U=[],$=0,F=0,y="",p=function(X){if(F<M.length&&M[F].type===X)return M[F++].value},h=function(X){var ye=p(X);if(ye!==void 0)return ye;var pe=M[F],Ne=pe.type,Ue=pe.index;throw new TypeError("Unexpected "+Ne+" at "+Ue+", expected "+X)},P=function(){for(var X="",ye;ye=p("CHAR")||p("ESCAPED_CHAR");)X+=ye;return X};F<M.length;){var R=p("CHAR"),j=p("NAME"),ee=p("PATTERN");if(j||ee){var A=R||"";W.indexOf(A)===-1&&(y+=A,A=""),y&&(U.push(y),y=""),U.push({name:j||$++,prefix:A,suffix:"",pattern:ee||H,modifier:p("MODIFIER")||""});continue}var se=R||p("ESCAPED_CHAR");if(se){y+=se;continue}y&&(U.push(y),y="");var me=p("OPEN");if(me){var A=P(),be=p("NAME")||"",je=p("PATTERN")||"",He=P();h("CLOSE"),U.push({name:be||(je?$++:""),pattern:be&&!je?H:je,prefix:A,suffix:He,modifier:p("MODIFIER")||""});continue}h("END")}return U}d=Q;function ae(b,s){return le(Q(b,s),s)}d=ae;function le(b,s){s===void 0&&(s={});var M=J(s),V=s.encode,W=V===void 0?function(F){return F}:V,H=s.validate,U=H===void 0?!0:H,$=b.map(function(F){if(Y(F)==="object")return new RegExp("^(?:"+F.pattern+")$",M)});return function(F){for(var y="",p=0;p<b.length;p++){var h=b[p];if(typeof h=="string"){y+=h;continue}var P=F?F[h.name]:void 0,R=h.modifier==="?"||h.modifier==="*",j=h.modifier==="*"||h.modifier==="+";if(Array.isArray(P)){if(!j)throw new TypeError('Expected "'+h.name+'" to not repeat, but got an array');if(P.length===0){if(R)continue;throw new TypeError('Expected "'+h.name+'" to not be empty')}for(var ee=0;ee<P.length;ee++){var A=W(P[ee],h);if(U&&!$[p].test(A))throw new TypeError('Expected all "'+h.name+'" to match "'+h.pattern+'", but got "'+A+'"');y+=h.prefix+A+h.suffix}continue}if(typeof P=="string"||typeof P=="number"){var A=W(String(P),h);if(U&&!$[p].test(A))throw new TypeError('Expected "'+h.name+'" to match "'+h.pattern+'", but got "'+A+'"');y+=h.prefix+A+h.suffix;continue}if(!R){var se=j?"an array":"a string";throw new TypeError('Expected "'+h.name+'" to be '+se)}}return y}}d=le;function ce(b,s){var M=[],V=oe(b,M,s);return ue(V,M,s)}d=ce;function ue(b,s,M){M===void 0&&(M={});var V=M.decode,W=V===void 0?function(H){return H}:V;return function(H){var U=b.exec(H);if(!U)return!1;for(var $=U[0],F=U.index,y=Object.create(null),p=function(R){if(U[R]===void 0)return"continue";var j=s[R-1];j.modifier==="*"||j.modifier==="+"?y[j.name]=U[R].split(j.prefix+j.suffix).map(function(ee){return W(ee,j)}):y[j.name]=W(U[R],j)},h=1;h<U.length;h++)p(h);return{path:$,index:F,params:y}}}d=ue;function ve(b){return b.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function J(b){return b&&b.sensitive?"":"i"}function g(b,s){if(!s)return b;var M=b.source.match(/\((?!\?)/g);if(M)for(var V=0;V<M.length;V++)s.push({name:V,prefix:"",suffix:"",modifier:"",pattern:""});return b}function u(b,s,M){var V=b.map(function(W){return oe(W,s,M).source});return new RegExp("(?:"+V.join("|")+")",J(M))}function Me(b,s,M){return de(Q(b,M),s,M)}function de(b,s,M){M===void 0&&(M={});for(var V=M.strict,W=V===void 0?!1:V,H=M.start,U=H===void 0?!0:H,$=M.end,F=$===void 0?!0:$,y=M.encode,p=y===void 0?function(Ee){return Ee}:y,h="["+ve(M.endsWith||"")+"]|$",P="["+ve(M.delimiter||"/#?")+"]",R=U?"^":"",j=0,ee=b;j<ee.length;j++){var A=ee[j];if(typeof A=="string")R+=ve(p(A));else{var se=ve(p(A.prefix)),me=ve(p(A.suffix));if(A.pattern)if(s&&s.push(A),se||me)if(A.modifier==="+"||A.modifier==="*"){var be=A.modifier==="*"?"?":"";R+="(?:"+se+"((?:"+A.pattern+")(?:"+me+se+"(?:"+A.pattern+"))*)"+me+")"+be}else R+="(?:"+se+"("+A.pattern+")"+me+")"+A.modifier;else R+="("+A.pattern+")"+A.modifier;else R+="(?:"+se+me+")"+A.modifier}}if(F)W||(R+=P+"?"),R+=M.endsWith?"(?="+h+")":"$";else{var je=b[b.length-1],He=typeof je=="string"?P.indexOf(je[je.length-1])>-1:je===void 0;W||(R+="(?:"+P+"(?="+h+"))?"),He||(R+="(?="+P+"|"+h+")")}return new RegExp(R,J(M))}d=de;function oe(b,s,M){return b instanceof RegExp?g(b,s):Array.isArray(b)?u(b,s,M):Me(b,s,M)}G.Bo=oe},31707:function(xe,G){"use strict";var d=this&&this.__classPrivateFieldGet||function(y,p,h,P){if(h==="a"&&!P)throw new TypeError("Private accessor was defined without a getter");if(typeof p=="function"?y!==p||!P:!p.has(y))throw new TypeError("Cannot read private member from an object whose class did not declare it");return h==="m"?P:h==="a"?P.call(y):P?P.value:p.get(y)},Y=this&&this.__classPrivateFieldSet||function(y,p,h,P,R){if(P==="m")throw new TypeError("Private method is not writable");if(P==="a"&&!R)throw new TypeError("Private accessor was defined without a setter");if(typeof p=="function"?y!==p||!R:!p.has(y))throw new TypeError("Cannot write private member to an object whose class did not declare it");return P==="a"?R.call(y,h):R?R.value=h:p.set(y,h),h},q;Object.defineProperty(G,"__esModule",{value:!0}),G.TokenData=void 0,G.parse=oe,G.compile=s,G.match=H;const Q="/",ae=y=>y,le=/^[$_\p{ID_Start}]$/u,ce=/^[$\u200c\u200d\p{ID_Continue}]$/u,ue="https://git.new/pathToRegexpError",ve={"{":"{","}":"}","(":"(",")":")","[":"[","]":"]","+":"+","?":"?","!":"!"};function J(y){return y.replace(/[.+*?^${}()[\]|/\\]/g,"\\$&")}function g(y){return y.sensitive?"s":"is"}function*u(y){const p=[...y];let h=0;function P(){let R="";if(le.test(p[++h]))for(R+=p[h];ce.test(p[++h]);)R+=p[h];else if(p[h]==='"'){let j=h;for(;h<p.length;){if(p[++h]==='"'){h++,j=0;break}p[h]==="\\"?R+=p[++h]:R+=p[h]}if(j)throw new TypeError(`Unterminated quote at ${j}: ${ue}`)}if(!R)throw new TypeError(`Missing parameter name at ${h}: ${ue}`);return R}for(;h<p.length;){const R=p[h],j=ve[R];if(j)yield{type:j,index:h++,value:R};else if(R==="\\")yield{type:"ESCAPED",index:h++,value:p[h++]};else if(R===":"){const ee=P();yield{type:"PARAM",index:h,value:ee}}else if(R==="*"){const ee=P();yield{type:"WILDCARD",index:h,value:ee}}else yield{type:"CHAR",index:h,value:p[h++]}}return{type:"END",index:h,value:""}}class Me{constructor(p){this.tokens=p,q.set(this,void 0)}peek(){if(!d(this,q,"f")){const p=this.tokens.next();Y(this,q,p.value,"f")}return d(this,q,"f")}tryConsume(p){const h=this.peek();if(h.type===p)return Y(this,q,void 0,"f"),h.value}consume(p){const h=this.tryConsume(p);if(h!==void 0)return h;const{type:P,index:R}=this.peek();throw new TypeError(`Unexpected ${P} at ${R}, expected ${p}: ${ue}`)}text(){let p="",h;for(;h=this.tryConsume("CHAR")||this.tryConsume("ESCAPED");)p+=h;return p}}q=new WeakMap;class de{constructor(p){this.tokens=p}}G.TokenData=de;function oe(y,p={}){const{encodePath:h=ae}=p,P=new Me(u(y));function R(ee){const A=[];for(;;){const se=P.text();se&&A.push({type:"text",value:h(se)});const me=P.tryConsume("PARAM");if(me){A.push({type:"param",name:me});continue}const be=P.tryConsume("WILDCARD");if(be){A.push({type:"wildcard",name:be});continue}if(P.tryConsume("{")){A.push({type:"group",tokens:R("}")});continue}return P.consume(ee),A}}const j=R("END");return new de(j)}function b(y,p){const{encode:h=encodeURIComponent,delimiter:P=Q}=p,R=M(y.tokens,P,h);return function(ee={}){const[A,...se]=R(ee);if(se.length)throw new TypeError(`Missing parameters: ${se.join(", ")}`);return A}}function s(y,p={}){return b(y instanceof de?y:oe(y,p),p)}function M(y,p,h){const P=y.map(R=>V(R,p,h));return R=>{const j=[""];for(const ee of P){const[A,...se]=ee(R);j[0]+=A,j.push(...se)}return j}}function V(y,p,h){if(y.type==="text")return()=>[y.value];if(y.type==="group"){const R=M(y.tokens,p,h);return j=>{const[ee,...A]=R(j);return A.length?[""]:[ee]}}const P=h||ae;return y.type==="wildcard"&&h!==!1?R=>{const j=R[y.name];if(j==null)return["",y.name];if(!Array.isArray(j)||j.length===0)throw new TypeError(`Expected "${y.name}" to be a non-empty array`);return[j.map((ee,A)=>{if(typeof ee!="string")throw new TypeError(`Expected "${y.name}/${A}" to be a string`);return P(ee)}).join(p)]}:R=>{const j=R[y.name];if(j==null)return["",y.name];if(typeof j!="string")throw new TypeError(`Expected "${y.name}" to be a string`);return[P(j)]}}function W(y,p={}){const{decode:h=decodeURIComponent,delimiter:P=Q,end:R=!0,trailing:j=!0}=p,ee=g(p),A=[],se=[];for(const{tokens:He}of y)for(const Ee of U(He,0,[])){const X=$(Ee,P,se);A.push(X)}let me=`^(?:${A.join("|")})`;j&&(me+=`(?:${J(P)}$)?`),me+=R?"$":`(?=${J(P)}|$)`;const be=new RegExp(me,ee),je=se.map(He=>h===!1?ae:He.type==="param"?h:Ee=>Ee.split(P).map(h));return Object.assign(function(Ee){const X=be.exec(Ee);if(!X)return!1;const{0:ye}=X,pe=Object.create(null);for(let Ne=1;Ne<X.length;Ne++){if(X[Ne]===void 0)continue;const Ue=se[Ne-1],ot=je[Ne-1];pe[Ue.name]=ot(X[Ne])}return{path:ye,params:pe}},{re:be})}function H(y,p={}){const P=(Array.isArray(y)?y:[y]).map(R=>R instanceof de?R:oe(R,p));return W(P,p)}function*U(y,p,h){if(p===y.length)return yield h;const P=y[p];if(P.type==="group"){const R=h.slice();for(const j of U(P.tokens,0,R))yield*Yt(U(y,p+1,j))}else h.push(P);yield*Yt(U(y,p+1,h))}function $(y,p,h){let P="",R="",j=!0;for(let ee=0;ee<y.length;ee++){const A=y[ee];if(A.type==="text"){P+=J(A.value),R=A.value,j||(j=A.value.includes(p));continue}if(A.type==="param"||A.type==="wildcard"){if(!j&&!R)throw new TypeError(`Missing text after "${A.name}": ${ue}`);A.type==="param"?P+=`(${F(p,j?"":R)}+)`:P+="(.+)",h.push(A),R="",j=!1;continue}}return P}function F(y,p){const h=[y,p].filter(Boolean);return h.every(R=>R.length===1)?`[^${J(h.join(""))}]`:`(?:(?!${h.map(J).join("|")}).)`}},73177:function(xe,G,d){"use strict";d.d(G,{X:function(){return ce},b:function(){return le}});var Y=d(67159),q=d(51812),Q=d(1977),ae=d(34155),le=function(){var ve;return typeof ae=="undefined"?Y.Z:((ve=ae)===null||ae===void 0||(ae={NODE_ENV:"production",PUBLIC_PATH:"/"})===null||ae===void 0?void 0:ae.ANTD_VERSION)||Y.Z},ce=function(ve,J){var g=(0,Q.n)(le(),"4.23.0")>-1?{open:ve,onOpenChange:J}:{visible:ve,onVisibleChange:J};return(0,q.Y)(g)}},78164:function(xe,G,d){"use strict";d.d(G,{S:function(){return g}});var Y=d(15671),q=d(43144),Q=d(97326),ae=d(60136),le=d(29388),ce=d(4942),ue=d(29905),ve=d(67294),J=d(85893),g=function(u){(0,ae.Z)(de,u);var Me=(0,le.Z)(de);function de(){var oe;(0,Y.Z)(this,de);for(var b=arguments.length,s=new Array(b),M=0;M<b;M++)s[M]=arguments[M];return oe=Me.call.apply(Me,[this].concat(s)),(0,ce.Z)((0,Q.Z)(oe),"state",{hasError:!1,errorInfo:""}),oe}return(0,q.Z)(de,[{key:"componentDidCatch",value:function(b,s){console.log(b,s)}},{key:"render",value:function(){return this.state.hasError?(0,J.jsx)(ue.ZP,{status:"error",title:"Something went wrong.",extra:this.state.errorInfo}):this.props.children}}],[{key:"getDerivedStateFromError",value:function(b){return{hasError:!0,errorInfo:b.message}}}]),de}(ve.Component)},10178:function(xe,G,d){"use strict";d.d(G,{D:function(){return le}});var Y=d(74165),q=d(15861),Q=d(67294),ae=d(48171);function le(ce,ue){var ve=(0,ae.J)(ce),J=(0,Q.useRef)(),g=(0,Q.useCallback)(function(){J.current&&(clearTimeout(J.current),J.current=null)},[]),u=(0,Q.useCallback)((0,q.Z)((0,Y.Z)().mark(function Me(){var de,oe,b,s=arguments;return(0,Y.Z)().wrap(function(V){for(;;)switch(V.prev=V.next){case 0:for(de=s.length,oe=new Array(de),b=0;b<de;b++)oe[b]=s[b];if(!(ue===0||ue===void 0)){V.next=3;break}return V.abrupt("return",ve.apply(void 0,oe));case 3:return g(),V.abrupt("return",new Promise(function(W){J.current=setTimeout((0,q.Z)((0,Y.Z)().mark(function H(){return(0,Y.Z)().wrap(function($){for(;;)switch($.prev=$.next){case 0:return $.t0=W,$.next=3,ve.apply(void 0,oe);case 3:return $.t1=$.sent,(0,$.t0)($.t1),$.abrupt("return");case 6:case"end":return $.stop()}},H)})),ue)}));case 5:case"end":return V.stop()}},Me)})),[ve,g,ue]);return(0,Q.useEffect)(function(){return g},[g]),{run:u,cancel:g}}},48171:function(xe,G,d){"use strict";d.d(G,{J:function(){return Q}});var Y=d(74902),q=d(67294),Q=function(le){var ce=(0,q.useRef)(null);return ce.current=le,(0,q.useCallback)(function(){for(var ue,ve=arguments.length,J=new Array(ve),g=0;g<ve;g++)J[g]=arguments[g];return(ue=ce.current)===null||ue===void 0?void 0:ue.call.apply(ue,[ce].concat((0,Y.Z)(J)))},[])}},12044:function(xe,G,d){"use strict";d.d(G,{j:function(){return Q}});var Y=d(34155),q=typeof Y!="undefined"&&Y.versions!=null&&Y.versions.node!=null,Q=function(){return typeof window!="undefined"&&typeof window.document!="undefined"&&typeof window.matchMedia!="undefined"&&!q}},51812:function(xe,G,d){"use strict";d.d(G,{Y:function(){return Y}});var Y=function(Q){var ae={};if(Object.keys(Q||{}).forEach(function(le){Q[le]!==void 0&&(ae[le]=Q[le])}),!(Object.keys(ae).length<1))return ae}},75590:function(xe,G,d){"use strict";d.r(G),d.d(G,{default:function(){return Ko}});var Y=d(5574),q=d.n(Y),Q=d(19632),ae=d.n(Q),le=d(97857),ce=d.n(le),ue=d(64599),ve=d.n(ue),J=d(70831),g=d(67294),u=d(4942),Me=d(74165),de=d(15861),oe=d(45987),b=d(97685),s=d(1413),M=d(10915),V=d(21770);function W(a){var e=typeof window=="undefined",t=(0,g.useState)(function(){return e?!1:window.matchMedia(a).matches}),n=(0,b.Z)(t,2),r=n[0],i=n[1];return(0,g.useLayoutEffect)(function(){if(!e){var o=window.matchMedia(a),c=function(v){return i(v.matches)};return o.addListener(c),function(){return o.removeListener(c)}}},[a]),r}var H={xs:{maxWidth:575,matchMedia:"(max-width: 575px)"},sm:{minWidth:576,maxWidth:767,matchMedia:"(min-width: 576px) and (max-width: 767px)"},md:{minWidth:768,maxWidth:991,matchMedia:"(min-width: 768px) and (max-width: 991px)"},lg:{minWidth:992,maxWidth:1199,matchMedia:"(min-width: 992px) and (max-width: 1199px)"},xl:{minWidth:1200,maxWidth:1599,matchMedia:"(min-width: 1200px) and (max-width: 1599px)"},xxl:{minWidth:1600,matchMedia:"(min-width: 1600px)"}},U=function(){var e=void 0;if(typeof window=="undefined")return e;var t=Object.keys(H).find(function(n){var r=H[n].matchMedia;return!!window.matchMedia(r).matches});return e=t,e},$=function(){var e=W(H.md.matchMedia),t=W(H.lg.matchMedia),n=W(H.xxl.matchMedia),r=W(H.xl.matchMedia),i=W(H.sm.matchMedia),o=W(H.xs.matchMedia),c=(0,g.useState)(U()),f=(0,b.Z)(c,2),v=f[0],m=f[1];return(0,g.useEffect)(function(){if(n){m("xxl");return}if(r){m("xl");return}if(t){m("lg");return}if(e){m("md");return}if(i){m("sm");return}if(o){m("xs");return}m("md")},[e,t,n,r,i,o]),v},F=d(12044);function y(a,e){var t=typeof a.pageName=="string"?a.title:e;(0,g.useEffect)(function(){(0,F.j)()&&t&&(document.title=t)},[a.title,t])}var p=d(1977),h=d(73177);function P(a){if((0,p.n)((0,h.b)(),"5.6.0")<0)return a;var e={colorGroupTitle:"groupTitleColor",radiusItem:"itemBorderRadius",radiusSubMenuItem:"subMenuItemBorderRadius",colorItemText:"itemColor",colorItemTextHover:"itemHoverColor",colorItemTextHoverHorizontal:"horizontalItemHoverColor",colorItemTextSelected:"itemSelectedColor",colorItemTextSelectedHorizontal:"horizontalItemSelectedColor",colorItemTextDisabled:"itemDisabledColor",colorDangerItemText:"dangerItemColor",colorDangerItemTextHover:"dangerItemHoverColor",colorDangerItemTextSelected:"dangerItemSelectedColor",colorDangerItemBgActive:"dangerItemActiveBg",colorDangerItemBgSelected:"dangerItemSelectedBg",colorItemBg:"itemBg",colorItemBgHover:"itemHoverBg",colorSubItemBg:"subMenuItemBg",colorItemBgActive:"itemActiveBg",colorItemBgSelected:"itemSelectedBg",colorItemBgSelectedHorizontal:"horizontalItemSelectedBg",colorActiveBarWidth:"activeBarWidth",colorActiveBarHeight:"activeBarHeight",colorActiveBarBorderSize:"activeBarBorderWidth"},t=(0,s.Z)({},a);return Object.keys(e).forEach(function(n){t[n]!==void 0&&(t[e[n]]=t[n],delete t[n])}),t}var R=d(90743);function j(a,e){return e>>>a|e<<32-a}function ee(a,e,t){return a&e^~a&t}function A(a,e,t){return a&e^a&t^e&t}function se(a){return j(2,a)^j(13,a)^j(22,a)}function me(a){return j(6,a)^j(11,a)^j(25,a)}function be(a){return j(7,a)^j(18,a)^a>>>3}function je(a){return j(17,a)^j(19,a)^a>>>10}function He(a,e){return a[e&15]+=je(a[e+14&15])+a[e+9&15]+be(a[e+1&15])}var Ee=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],X,ye,pe,Ne="0123456789abcdef";function Ue(a,e){var t=(a&65535)+(e&65535),n=(a>>16)+(e>>16)+(t>>16);return n<<16|t&65535}function ot(){X=new Array(8),ye=new Array(2),pe=new Array(64),ye[0]=ye[1]=0,X[0]=1779033703,X[1]=3144134277,X[2]=1013904242,X[3]=2773480762,X[4]=1359893119,X[5]=2600822924,X[6]=528734635,X[7]=1541459225}function Pt(){var a,e,t,n,r,i,o,c,f,v,m=new Array(16);a=X[0],e=X[1],t=X[2],n=X[3],r=X[4],i=X[5],o=X[6],c=X[7];for(var I=0;I<16;I++)m[I]=pe[(I<<2)+3]|pe[(I<<2)+2]<<8|pe[(I<<2)+1]<<16|pe[I<<2]<<24;for(var x=0;x<64;x++)f=c+me(r)+ee(r,i,o)+Ee[x],x<16?f+=m[x]:f+=He(m,x),v=se(a)+A(a,e,t),c=o,o=i,i=r,r=Ue(n,f),n=t,t=e,e=a,a=Ue(f,v);X[0]+=a,X[1]+=e,X[2]+=t,X[3]+=n,X[4]+=r,X[5]+=i,X[6]+=o,X[7]+=c}function Un(a,e){var t,n,r=0;n=ye[0]>>3&63;var i=e&63;for((ye[0]+=e<<3)<e<<3&&ye[1]++,ye[1]+=e>>29,t=0;t+63<e;t+=64){for(var o=n;o<64;o++)pe[o]=a.charCodeAt(r++);Pt(),n=0}for(var c=0;c<i;c++)pe[c]=a.charCodeAt(r++)}function kn(){var a=ye[0]>>3&63;if(pe[a++]=128,a<=56)for(var e=a;e<56;e++)pe[e]=0;else{for(var t=a;t<64;t++)pe[t]=0;Pt();for(var n=0;n<56;n++)pe[n]=0}pe[56]=ye[1]>>>24&255,pe[57]=ye[1]>>>16&255,pe[58]=ye[1]>>>8&255,pe[59]=ye[1]&255,pe[60]=ye[0]>>>24&255,pe[61]=ye[0]>>>16&255,pe[62]=ye[0]>>>8&255,pe[63]=ye[0]&255,Pt()}function si(){for(var a=0,e=new Array(32),t=0;t<8;t++)e[a++]=X[t]>>>24&255,e[a++]=X[t]>>>16&255,e[a++]=X[t]>>>8&255,e[a++]=X[t]&255;return e}function Gn(){for(var a=new String,e=0;e<8;e++)for(var t=28;t>=0;t-=4)a+=Ne.charAt(X[e]>>>t&15);return a}function Vn(a){return ot(),Un(a,a.length),kn(),Gn()}var Xn=Vn;function wt(a){"@babel/helpers - typeof";return wt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},wt(a)}var Yn=["pro_layout_parentKeys","children","icon","flatMenu","indexRoute","routes"];function Qn(a,e){return ea(a)||qn(a,e)||Et(a,e)||Jn()}function Jn(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function qn(a,e){var t=a==null?null:typeof Symbol!="undefined"&&a[Symbol.iterator]||a["@@iterator"];if(t!=null){var n=[],r=!0,i=!1,o,c;try{for(t=t.call(a);!(r=(o=t.next()).done)&&(n.push(o.value),!(e&&n.length===e));r=!0);}catch(f){i=!0,c=f}finally{try{!r&&t.return!=null&&t.return()}finally{if(i)throw c}}return n}}function ea(a){if(Array.isArray(a))return a}function ta(a,e){var t=typeof Symbol!="undefined"&&a[Symbol.iterator]||a["@@iterator"];if(!t){if(Array.isArray(a)||(t=Et(a))||e&&a&&typeof a.length=="number"){t&&(a=t);var n=0,r=function(){};return{s:r,n:function(){return n>=a.length?{done:!0}:{done:!1,value:a[n++]}},e:function(v){throw v},f:r}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i=!0,o=!1,c;return{s:function(){t=t.call(a)},n:function(){var v=t.next();return i=v.done,v},e:function(v){o=!0,c=v},f:function(){try{!i&&t.return!=null&&t.return()}finally{if(o)throw c}}}}function na(a,e){if(!(a instanceof e))throw new TypeError("Cannot call a class as a function")}function Qt(a,e){for(var t=0;t<e.length;t++){var n=e[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(a,n.key,n)}}function aa(a,e,t){return e&&Qt(a.prototype,e),t&&Qt(a,t),Object.defineProperty(a,"prototype",{writable:!1}),a}function ra(a,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");a.prototype=Object.create(e&&e.prototype,{constructor:{value:a,writable:!0,configurable:!0}}),Object.defineProperty(a,"prototype",{writable:!1}),e&&it(a,e)}function oa(a){var e=Jt();return function(){var n=lt(a),r;if(e){var i=lt(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return ia(this,r)}}function ia(a,e){if(e&&(wt(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return la(a)}function la(a){if(a===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a}function Bt(a){var e=typeof Map=="function"?new Map:void 0;return Bt=function(n){if(n===null||!ua(n))return n;if(typeof n!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e!="undefined"){if(e.has(n))return e.get(n);e.set(n,r)}function r(){return yt(n,arguments,lt(this).constructor)}return r.prototype=Object.create(n.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),it(r,n)},Bt(a)}function yt(a,e,t){return Jt()?yt=Reflect.construct.bind():yt=function(r,i,o){var c=[null];c.push.apply(c,i);var f=Function.bind.apply(r,c),v=new f;return o&&it(v,o.prototype),v},yt.apply(null,arguments)}function Jt(){if(typeof Reflect=="undefined"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(a){return!1}}function ua(a){return Function.toString.call(a).indexOf("[native code]")!==-1}function it(a,e){return it=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,r){return n.__proto__=r,n},it(a,e)}function lt(a){return lt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},lt(a)}function qt(a){return da(a)||ca(a)||Et(a)||sa()}function sa(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Et(a,e){if(a){if(typeof a=="string")return Lt(a,e);var t=Object.prototype.toString.call(a).slice(8,-1);if(t==="Object"&&a.constructor&&(t=a.constructor.name),t==="Map"||t==="Set")return Array.from(a);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return Lt(a,e)}}function ca(a){if(typeof Symbol!="undefined"&&a[Symbol.iterator]!=null||a["@@iterator"]!=null)return Array.from(a)}function da(a){if(Array.isArray(a))return Lt(a)}function Lt(a,e){(e==null||e>a.length)&&(e=a.length);for(var t=0,n=new Array(e);t<e;t++)n[t]=a[t];return n}function fa(a,e){if(a==null)return{};var t=va(a,e),n,r;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(a);for(r=0;r<i.length;r++)n=i[r],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(a,n)&&(t[n]=a[n])}return t}function va(a,e){if(a==null)return{};var t={},n=Object.keys(a),r,i;for(i=0;i<n.length;i++)r=n[i],!(e.indexOf(r)>=0)&&(t[r]=a[r]);return t}function en(a,e){var t=Object.keys(a);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(a);e&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(a,r).enumerable})),t.push.apply(t,n)}return t}function Ze(a){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?en(Object(t),!0).forEach(function(n){ma(a,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(t)):en(Object(t)).forEach(function(n){Object.defineProperty(a,n,Object.getOwnPropertyDescriptor(t,n))})}return a}function ma(a,e,t){return e in a?Object.defineProperty(a,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):a[e]=t,a}var De="routes";function ut(a){return a.split("?")[0].split("#")[0]}var At=function(e){if(!e.startsWith("http"))return!1;try{var t=new URL(e);return!!t}catch(n){return!1}},ha=function(e){var t=e.path;if(!t||t==="/")try{return"/".concat(Xn(JSON.stringify(e)))}catch(n){}return t&&ut(t)},pa=function(e,t){var n=e.name,r=e.locale;return"locale"in e&&r===!1||!n?!1:e.locale||"".concat(t,".").concat(n)},tn=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"/";return e.endsWith("/*")?e.replace("/*","/"):(e||t).startsWith("/")||At(e)?e:"/".concat(t,"/").concat(e).replace(/\/\//g,"/").replace(/\/\//g,"/")},ga=function(e,t){var n=e.menu,r=n===void 0?{}:n,i=e.indexRoute,o=e.path,c=o===void 0?"":o,f=e.children||[],v=r.name,m=v===void 0?e.name:v,I=r.icon,x=I===void 0?e.icon:I,Z=r.hideChildren,N=Z===void 0?e.hideChildren:Z,D=r.flatMenu,w=D===void 0?e.flatMenu:D,k=i&&Object.keys(i).join(",")!=="redirect"?[Ze({path:c,menu:r},i)].concat(f||[]):f,K=Ze({},e);if(m&&(K.name=m),x&&(K.icon=x),k&&k.length){if(N)return delete K.children,K;var E=Nt(Ze(Ze({},t),{},{data:k}),e);if(w)return E;delete K[De]}return K},Ye=function(e){return Array.isArray(e)&&e.length>0};function Nt(a){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{path:"/"},t=a.data,n=a.formatMessage,r=a.parentName,i=a.locale;return!t||!Array.isArray(t)?[]:t.filter(function(o){return o?Ye(o.children)||o.path||o.originPath||o.layout?!0:(o.redirect||o.unaccessible,!1):!1}).filter(function(o){var c,f;return!(o==null||(c=o.menu)===null||c===void 0)&&c.name||o!=null&&o.flatMenu||!(o==null||(f=o.menu)===null||f===void 0)&&f.flatMenu?!0:o.menu!==!1}).map(function(o){var c=Ze(Ze({},o),{},{path:o.path||o.originPath});return!c.children&&c[De]&&(c.children=c[De],delete c[De]),c.unaccessible&&delete c.name,c.path==="*"&&(c.path="."),c.path==="/*"&&(c.path="."),!c.path&&c.originPath&&(c.path=c.originPath),c}).map(function(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{path:"/"},c=o.children||o[De]||[],f=tn(o.path,e?e.path:"/"),v=o.name,m=pa(o,r||"menu"),I=m!==!1&&i!==!1&&n&&m?n({id:m,defaultMessage:v}):v,x=e.pro_layout_parentKeys,Z=x===void 0?[]:x,N=e.children,D=e.icon,w=e.flatMenu,k=e.indexRoute,K=e.routes,E=fa(e,Yn),T=new Set([].concat(qt(Z),qt(o.parentKeys||[])));e.key&&T.add(e.key);var _=Ze(Ze(Ze({},E),{},{menu:void 0},o),{},{path:f,locale:m,key:o.key||ha(Ze(Ze({},o),{},{path:f})),pro_layout_parentKeys:Array.from(T).filter(function(L){return L&&L!=="/"})});if(I?_.name=I:delete _.name,_.menu===void 0&&delete _.menu,Ye(c)){var C=Nt(Ze(Ze({},a),{},{data:c,parentName:m||""}),_);Ye(C)&&(_.children=C)}return ga(_,a)}).flat(1)}var ya=function a(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.filter(function(t){return t&&(t.name||Ye(t.children))&&!t.hideInMenu&&!t.redirect}).map(function(t){var n=Ze({},t),r=n.children||t[De]||[];if(delete n[De],Ye(r)&&!n.hideChildrenInMenu&&r.some(function(o){return o&&!!o.name})){var i=a(r);if(i.length)return Ze(Ze({},n),{},{children:i})}return Ze({},t)}).filter(function(t){return t})},xa=function(a){ra(t,a);var e=oa(t);function t(){return na(this,t),e.apply(this,arguments)}return aa(t,[{key:"get",value:function(r){var i;try{var o=ta(this.entries()),c;try{for(o.s();!(c=o.n()).done;){var f=Qn(c.value,2),v=f[0],m=f[1],I=ut(v);if(!At(v)&&(0,R.Bo)(I,[]).test(r)){i=m;break}}}catch(x){o.e(x)}finally{o.f()}}catch(x){i=void 0}return i}}]),t}(Bt(Map)),Ca=function(e){var t=new xa,n=function r(i,o){i.forEach(function(c){var f=c.children||c[De]||[];Ye(f)&&r(f,c);var v=tn(c.path,o?o.path:"/");t.set(ut(v),c)})};return n(e),t},ba=function a(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return e.map(function(t){var n=t.children||t[De];if(Ye(n)){var r=a(n);if(r.length)return Ze({},t)}var i=Ze({},t);return delete i[De],delete i.children,i}).filter(function(t){return t})},Sa=function(e,t,n,r){var i=Nt({data:e,formatMessage:n,locale:t}),o=r?ba(i):ya(i),c=Ca(i);return{breadcrumb:c,menuData:o}},Ma=Sa;function nn(a,e){var t=Object.keys(a);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(a);e&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(a,r).enumerable})),t.push.apply(t,n)}return t}function st(a){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?nn(Object(t),!0).forEach(function(n){Za(a,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(t)):nn(Object(t)).forEach(function(n){Object.defineProperty(a,n,Object.getOwnPropertyDescriptor(t,n))})}return a}function Za(a,e,t){return e in a?Object.defineProperty(a,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):a[e]=t,a}var Ia=function a(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t={};return e.forEach(function(n){var r=st({},n);if(!(!r||!r.key)){!r.children&&r[De]&&(r.children=r[De],delete r[De]);var i=r.children||[];t[ut(r.path||r.key||"/")]=st({},r),t[r.key||r.path||"/"]=st({},r),i&&(t=st(st({},t),a(i)))}}),t},Ta=Ia,Ra=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;return e.filter(function(r){if(r==="/"&&t==="/")return!0;if(r!=="/"&&r!=="/*"&&r&&!At(r)){var i=ut(r);try{if(n&&(0,R.Bo)("".concat(i)).test(t)||(0,R.Bo)("".concat(i),[]).test(t)||(0,R.Bo)("".concat(i,"/(.*)")).test(t))return!0}catch(o){}}return!1}).sort(function(r,i){return r===t?10:i===t?-10:r.substr(1).split("/").length-i.substr(1).split("/").length})},ja=function(e,t,n,r){var i=Ta(t),o=Object.keys(i),c=Ra(o,e||"/",r);return!c||c.length<1?[]:(n||(c=[c[c.length-1]]),c.map(function(f){var v=i[f]||{pro_layout_parentKeys:"",key:""},m=new Map,I=(v.pro_layout_parentKeys||[]).map(function(x){return m.has(x)?null:(m.set(x,!0),i[x])}).filter(function(x){return x});return v.key&&I.push(v),I}).flat(1))},Pa=ja,We=d(28459),Je=d(97183),wa=d(93967),fe=d.n(wa),an=d(97435),Ba=d(80334),rn=d(81758),Ea=d(78164),l=d(85893),La=function(e){var t=(0,g.useContext)(M.L_),n=t.hashId,r=e.style,i=e.prefixCls,o=e.children,c=e.hasPageContainer,f=c===void 0?0:c,v=fe()("".concat(i,"-content"),n,(0,u.Z)((0,u.Z)({},"".concat(i,"-has-header"),e.hasHeader),"".concat(i,"-content-has-page-container"),f>0)),m=e.ErrorBoundary||Ea.S;return e.ErrorBoundary===!1?(0,l.jsx)(Je.Z.Content,{className:v,style:r,children:o}):(0,l.jsx)(m,{children:(0,l.jsx)(Je.Z.Content,{className:v,style:r,children:o})})},Aa=function(){return(0,l.jsxs)("svg",{width:"1em",height:"1em",viewBox:"0 0 200 200",children:[(0,l.jsxs)("defs",{children:[(0,l.jsxs)("linearGradient",{x1:"62.1023273%",y1:"0%",x2:"108.19718%",y2:"37.8635764%",id:"linearGradient-1",children:[(0,l.jsx)("stop",{stopColor:"#4285EB",offset:"0%"}),(0,l.jsx)("stop",{stopColor:"#2EC7FF",offset:"100%"})]}),(0,l.jsxs)("linearGradient",{x1:"69.644116%",y1:"0%",x2:"54.0428975%",y2:"108.456714%",id:"linearGradient-2",children:[(0,l.jsx)("stop",{stopColor:"#29CDFF",offset:"0%"}),(0,l.jsx)("stop",{stopColor:"#148EFF",offset:"37.8600687%"}),(0,l.jsx)("stop",{stopColor:"#0A60FF",offset:"100%"})]}),(0,l.jsxs)("linearGradient",{x1:"69.6908165%",y1:"-12.9743587%",x2:"16.7228981%",y2:"117.391248%",id:"linearGradient-3",children:[(0,l.jsx)("stop",{stopColor:"#FA816E",offset:"0%"}),(0,l.jsx)("stop",{stopColor:"#F74A5C",offset:"41.472606%"}),(0,l.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]}),(0,l.jsxs)("linearGradient",{x1:"68.1279872%",y1:"-35.6905737%",x2:"30.4400914%",y2:"114.942679%",id:"linearGradient-4",children:[(0,l.jsx)("stop",{stopColor:"#FA8E7D",offset:"0%"}),(0,l.jsx)("stop",{stopColor:"#F74A5C",offset:"51.2635191%"}),(0,l.jsx)("stop",{stopColor:"#F51D2C",offset:"100%"})]})]}),(0,l.jsx)("g",{stroke:"none",strokeWidth:1,fill:"none",fillRule:"evenodd",children:(0,l.jsx)("g",{transform:"translate(-20.000000, -20.000000)",children:(0,l.jsx)("g",{transform:"translate(20.000000, 20.000000)",children:(0,l.jsxs)("g",{children:[(0,l.jsxs)("g",{fillRule:"nonzero",children:[(0,l.jsxs)("g",{children:[(0,l.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C99.2571609,26.9692191 101.032305,26.9692191 102.20193,28.1378823 L129.985225,55.8983314 C134.193707,60.1033528 141.017005,60.1033528 145.225487,55.8983314 C149.433969,51.69331 149.433969,44.8756232 145.225487,40.6706018 L108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-1)"}),(0,l.jsx)("path",{d:"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C100.999864,25.6271836 105.751642,20.541824 112.729652,19.3524487 C117.915585,18.4685261 123.585219,20.4140239 129.738554,25.1889424 C125.624663,21.0784292 118.571995,14.0340304 108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z",fill:"url(#linearGradient-2)"})]}),(0,l.jsx)("path",{d:"M153.685633,135.854579 C157.894115,140.0596 164.717412,140.0596 168.925894,135.854579 L195.959977,108.842726 C200.659183,104.147384 200.659183,96.5636133 195.960527,91.8688194 L168.690777,64.7181159 C164.472332,60.5180858 157.646868,60.5241425 153.435895,64.7316526 C149.227413,68.936674 149.227413,75.7543607 153.435895,79.9593821 L171.854035,98.3623765 C173.02366,99.5310396 173.02366,101.304724 171.854035,102.473387 L153.685633,120.626849 C149.47715,124.83187 149.47715,131.649557 153.685633,135.854579 Z",fill:"url(#linearGradient-3)"})]}),(0,l.jsx)("ellipse",{fill:"url(#linearGradient-4)",cx:"100.519339",cy:"100.436681",rx:"23.6001926",ry:"23.580786"})]})})})})]})},Na=d(78284),on=function a(e){return(e||[]).reduce(function(t,n){if(n.key&&t.push(n.key),n.children||n.routes){var r=t.concat(a(n.children||n.routes)||[]);return r}return t},[])},ln={techBlue:"#1677FF",daybreak:"#1890ff",dust:"#F5222D",volcano:"#FA541C",sunset:"#FAAD14",cyan:"#13C2C2",green:"#52C41A",geekblue:"#2F54EB",purple:"#722ED1"};function ci(a){return a&&ln[a]?ln[a]:a||""}function xt(a){return a.map(function(e){var t=e.children||[],n=(0,s.Z)({},e);if(!n.children&&n.routes&&(n.children=n.routes),!n.name||n.hideInMenu)return null;if(n&&n!==null&&n!==void 0&&n.children){if(!n.hideChildrenInMenu&&t.some(function(r){return r&&r.name&&!r.hideInMenu}))return(0,s.Z)((0,s.Z)({},e),{},{children:xt(t)});delete n.children}return delete n.routes,n}).filter(function(e){return e})}var Ct=d(87462),Da={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z"}}]},name:"menu",theme:"outlined"},Oa=Da,Ha=d(65555),$a=function(e,t){return g.createElement(Ha.Z,(0,Ct.Z)({},e,{ref:t,icon:Oa}))},_a=g.forwardRef($a),Fa=_a,Wa=d(55241),za=function(){return(0,l.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,l.jsx)("path",{d:"M0 0h3v3H0V0zm4.5 0h3v3h-3V0zM9 0h3v3H9V0zM0 4.5h3v3H0v-3zm4.503 0h3v3h-3v-3zM9 4.5h3v3H9v-3zM0 9h3v3H0V9zm4.503 0h3v3h-3V9zM9 9h3v3H9V9z"})})},Ka=function a(e){var t=e.appList,n=e.baseClassName,r=e.hashId,i=e.itemClick;return(0,l.jsx)("div",{className:"".concat(n,"-content ").concat(r).trim(),children:(0,l.jsx)("ul",{className:"".concat(n,"-content-list ").concat(r).trim(),children:t==null?void 0:t.map(function(o,c){var f;return o!=null&&(f=o.children)!==null&&f!==void 0&&f.length?(0,l.jsxs)("div",{className:"".concat(n,"-content-list-item-group ").concat(r).trim(),children:[(0,l.jsx)("div",{className:"".concat(n,"-content-list-item-group-title ").concat(r).trim(),children:o.title}),(0,l.jsx)(a,{hashId:r,itemClick:i,appList:o==null?void 0:o.children,baseClassName:n})]},c):(0,l.jsx)("li",{className:"".concat(n,"-content-list-item ").concat(r).trim(),onClick:function(m){m.stopPropagation(),i==null||i(o)},children:(0,l.jsxs)("a",{href:i?void 0:o.url,target:o.target,rel:"noreferrer",children:[Ot(o.icon),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{children:o.title}),o.desc?(0,l.jsx)("span",{children:o.desc}):null]})]})},c)})})})},Dt=function(e){if(!e||!e.startsWith("http"))return!1;try{var t=new URL(e);return!!t}catch(n){return!1}},Ua=function(e,t){if(e&&typeof e=="string"&&Dt(e))return(0,l.jsx)("img",{src:e,alt:"logo"});if(typeof e=="function")return e();if(e&&typeof e=="string")return(0,l.jsx)("div",{id:"avatarLogo",children:e});if(!e&&t&&typeof t=="string"){var n=t.substring(0,1);return(0,l.jsx)("div",{id:"avatarLogo",children:n})}return e},ka=function a(e){var t=e.appList,n=e.baseClassName,r=e.hashId,i=e.itemClick;return(0,l.jsx)("div",{className:"".concat(n,"-content ").concat(r).trim(),children:(0,l.jsx)("ul",{className:"".concat(n,"-content-list ").concat(r).trim(),children:t==null?void 0:t.map(function(o,c){var f;return o!=null&&(f=o.children)!==null&&f!==void 0&&f.length?(0,l.jsxs)("div",{className:"".concat(n,"-content-list-item-group ").concat(r).trim(),children:[(0,l.jsx)("div",{className:"".concat(n,"-content-list-item-group-title ").concat(r).trim(),children:o.title}),(0,l.jsx)(a,{hashId:r,itemClick:i,appList:o==null?void 0:o.children,baseClassName:n})]},c):(0,l.jsx)("li",{className:"".concat(n,"-content-list-item ").concat(r).trim(),onClick:function(m){m.stopPropagation(),i==null||i(o)},children:(0,l.jsxs)("a",{href:i?"javascript:;":o.url,target:o.target,rel:"noreferrer",children:[Ua(o.icon,o.title),(0,l.jsx)("div",{children:(0,l.jsx)("div",{children:o.title})})]})},c)})})})},Le=d(98082),Ga=function(e){return{"&-content":{maxHeight:"calc(100vh - 48px)",overflow:"auto","&-list":{boxSizing:"content-box",maxWidth:656,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","&-item":{position:"relative",display:"inline-block",width:328,height:72,paddingInline:16,paddingBlock:16,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.borderRadius,"&-group":{marginBottom:16,"&-title":{margin:"16px 0 8px 12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginTop:12}}},"&:hover":{backgroundColor:e.colorBgTextHover},"* div":Le.Wf===null||Le.Wf===void 0?void 0:(0,Le.Wf)(e),a:{display:"flex",height:"100%",fontSize:12,textDecoration:"none","& > img":{width:40,height:40},"& > div":{marginInlineStart:14,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}}},Va=function(e){return{"&-content":{maxHeight:"calc(100vh - 48px)",overflow:"auto","&-list":{boxSizing:"border-box",maxWidth:376,marginBlock:0,marginInline:0,paddingBlock:0,paddingInline:0,listStyle:"none","&-item":{position:"relative",display:"inline-block",width:104,height:104,marginBlock:8,marginInline:8,paddingInline:24,paddingBlock:24,verticalAlign:"top",listStyleType:"none",transition:"transform 0.2s cubic-bezier(0.333, 0, 0, 1)",borderRadius:e.borderRadius,"&-group":{marginBottom:16,"&-title":{margin:"16px 0 8px 12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginTop:12}}},"&:hover":{backgroundColor:e.colorBgTextHover},a:{display:"flex",flexDirection:"column",alignItems:"center",height:"100%",fontSize:12,textDecoration:"none","& > #avatarLogo":{width:40,height:40,margin:"0 auto",color:e.colorPrimary,fontSize:22,lineHeight:"40px",textAlign:"center",backgroundImage:"linear-gradient(180deg, #E8F0FB 0%, #F6F8FC 100%)",borderRadius:e.borderRadius},"& > img":{width:40,height:40},"& > div":{marginBlockStart:5,marginInlineStart:0,color:e.colorTextHeading,fontSize:14,lineHeight:"22px",whiteSpace:"nowrap",textOverflow:"ellipsis"},"& > div > span":{color:e.colorTextSecondary,fontSize:12,lineHeight:"20px"}}}}}}},Xa=function(e){var t,n,r,i,o;return(0,u.Z)({},e.componentCls,{"&-icon":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInline:4,paddingBlock:0,fontSize:14,lineHeight:"14px",height:28,width:28,cursor:"pointer",color:(t=e.layout)===null||t===void 0?void 0:t.colorTextAppListIcon,borderRadius:e.borderRadius,"&:hover":{color:(n=e.layout)===null||n===void 0?void 0:n.colorTextAppListIconHover,backgroundColor:(r=e.layout)===null||r===void 0?void 0:r.colorBgAppListIconHover},"&-active":{color:(i=e.layout)===null||i===void 0?void 0:i.colorTextAppListIconHover,backgroundColor:(o=e.layout)===null||o===void 0?void 0:o.colorBgAppListIconHover}},"&-item-title":{marginInlineStart:"16px",marginInlineEnd:"8px",marginBlockStart:0,marginBlockEnd:"12px",fontWeight:600,color:"rgba(0, 0, 0, 0.88)",fontSize:16,opacity:.85,lineHeight:1.5,"&:first-child":{marginBlockStart:12}},"&-popover":(0,u.Z)({},"".concat(e.antCls,"-popover-arrow"),{display:"none"}),"&-simple":Va(e),"&-default":Ga(e)})};function Ya(a){return(0,Le.Xj)("AppsLogoComponents",function(e){var t=(0,s.Z)((0,s.Z)({},e),{},{componentCls:".".concat(a)});return[Xa(t)]})}var Ot=function(e){return typeof e=="string"?(0,l.jsx)("img",{width:"auto",height:22,src:e,alt:"logo"}):typeof e=="function"?e():e},Ht=function(e){var t,n=e.appList,r=e.appListRender,i=e.prefixCls,o=i===void 0?"ant-pro":i,c=e.onItemClick,f=g.useRef(null),v=g.useRef(null),m="".concat(o,"-layout-apps"),I=Ya(m),x=I.wrapSSR,Z=I.hashId,N=(0,g.useState)(!1),D=(0,b.Z)(N,2),w=D[0],k=D[1],K=function(L){c==null||c(L,v)},E=(0,g.useMemo)(function(){var C=n==null?void 0:n.some(function(L){return!(L!=null&&L.desc)});return C?(0,l.jsx)(ka,{hashId:Z,appList:n,itemClick:c?K:void 0,baseClassName:"".concat(m,"-simple")}):(0,l.jsx)(Ka,{hashId:Z,appList:n,itemClick:c?K:void 0,baseClassName:"".concat(m,"-default")})},[n,m,Z]);if(!(e!=null&&(t=e.appList)!==null&&t!==void 0&&t.length))return null;var T=r?r(e==null?void 0:e.appList,E):E,_=(0,h.X)(void 0,function(C){return k(C)});return x((0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("div",{ref:f,onClick:function(L){L.stopPropagation(),L.preventDefault()}}),(0,l.jsx)(Wa.Z,(0,s.Z)((0,s.Z)({placement:"bottomRight",trigger:["click"],zIndex:9999,arrow:!1},_),{},{overlayClassName:"".concat(m,"-popover ").concat(Z).trim(),content:T,getPopupContainer:function(){return f.current||document.body},children:(0,l.jsx)("span",{ref:v,onClick:function(L){L.stopPropagation()},className:fe()("".concat(m,"-icon"),Z,(0,u.Z)({},"".concat(m,"-icon-active"),w)),children:(0,l.jsx)(za,{})})}))]}))},$t=d(7134),Qa=d(42075),ct=d(50136);function Ja(){return(0,l.jsx)("svg",{width:"1em",height:"1em",viewBox:"0 0 12 12",fill:"currentColor","aria-hidden":"true",children:(0,l.jsx)("path",{d:"M6.432 7.967a.448.448 0 01-.318.133h-.228a.46.46 0 01-.318-.133L2.488 4.85a.305.305 0 010-.43l.427-.43a.293.293 0 01.42 0L6 6.687l2.665-2.699a.299.299 0 01.426 0l.42.431a.305.305 0 010 .43L6.432 7.967z"})})}var qa=function(e){var t,n,r;return(0,u.Z)({},e.componentCls,{position:"absolute",insetBlockStart:"18px",zIndex:"101",width:"24px",height:"24px",fontSize:["14px","16px"],textAlign:"center",borderRadius:"40px",insetInlineEnd:"-13px",transition:"transform 0.3s",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",color:(t=e.layout)===null||t===void 0||(t=t.sider)===null||t===void 0?void 0:t.colorTextCollapsedButton,backgroundColor:(n=e.layout)===null||n===void 0||(n=n.sider)===null||n===void 0?void 0:n.colorBgCollapsedButton,boxShadow:"0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(25,15,15,0.07), 0 0 1px 0 rgba(0,0,0,0.08)","&:hover":{color:(r=e.layout)===null||r===void 0||(r=r.sider)===null||r===void 0?void 0:r.colorTextCollapsedButtonHover,boxShadow:"0 4px 16px -4px rgba(0,0,0,0.05), 0 2px 8px -2px rgba(25,15,15,0.07), 0 1px 2px 0 rgba(0,0,0,0.08)"},".anticon":{fontSize:"14px"},"& > svg":{transition:"transform  0.3s",transform:"rotate(90deg)"},"&-collapsed":{"& > svg":{transform:"rotate(-90deg)"}}})};function er(a){return(0,Le.Xj)("SiderMenuCollapsedIcon",function(e){var t=(0,s.Z)((0,s.Z)({},e),{},{componentCls:".".concat(a)});return[qa(t)]})}var tr=["isMobile","collapsed"],nr=function(e){var t=e.isMobile,n=e.collapsed,r=(0,oe.Z)(e,tr),i=er(e.className),o=i.wrapSSR,c=i.hashId;return t&&n?null:o((0,l.jsx)("div",(0,s.Z)((0,s.Z)({},r),{},{className:fe()(e.className,c,(0,u.Z)((0,u.Z)({},"".concat(e.className,"-collapsed"),n),"".concat(e.className,"-is-mobile"),t)),children:(0,l.jsx)(Ja,{})})))},bt=d(74902),ar=d(43144),rr=d(15671),or=d(42550),ir=d(2446),St=d(14004),lr=["className","component","viewBox","spin","rotate","tabIndex","onClick","children"],un=g.forwardRef(function(a,e){var t=a.className,n=a.component,r=a.viewBox,i=a.spin,o=a.rotate,c=a.tabIndex,f=a.onClick,v=a.children,m=(0,oe.Z)(a,lr),I=g.useRef(),x=(0,or.x1)(I,e);(0,St.Kp)(!!(n||v),"Should have `component` prop or `children`."),(0,St.C3)(I);var Z=g.useContext(ir.Z),N=Z.prefixCls,D=N===void 0?"anticon":N,w=Z.rootClassName,k=fe()(w,D,(0,u.Z)({},"".concat(D,"-spin"),!!i&&!!n),t),K=fe()((0,u.Z)({},"".concat(D,"-spin"),!!i)),E=o?{msTransform:"rotate(".concat(o,"deg)"),transform:"rotate(".concat(o,"deg)")}:void 0,T=(0,s.Z)((0,s.Z)({},St.vD),{},{className:K,style:E,viewBox:r});r||delete T.viewBox;var _=function(){return n?g.createElement(n,T,v):v?((0,St.Kp)(!!r||g.Children.count(v)===1&&g.isValidElement(v)&&g.Children.only(v).type==="use","Make sure that you provide correct `viewBox` prop (default `0 0 1024 1024`) to the icon."),g.createElement("svg",(0,Ct.Z)({},T,{viewBox:r}),v)):null},C=c;return C===void 0&&f&&(C=-1),g.createElement("span",(0,Ct.Z)({role:"img"},m,{ref:x,tabIndex:C,onClick:f,className:k}),_())});un.displayName="AntdIcon";var ur=un,sr=["type","children"],sn=new Set;function cr(a){return!!(typeof a=="string"&&a.length&&!sn.has(a))}function Mt(a){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,t=a[e];if(cr(t)){var n=document.createElement("script");n.setAttribute("src",t),n.setAttribute("data-namespace",t),a.length>e+1&&(n.onload=function(){Mt(a,e+1)},n.onerror=function(){Mt(a,e+1)}),sn.add(t),document.body.appendChild(n)}}function cn(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=a.scriptUrl,t=a.extraCommonProps,n=t===void 0?{}:t;e&&typeof document!="undefined"&&typeof window!="undefined"&&typeof document.createElement=="function"&&(Array.isArray(e)?Mt(e.reverse()):Mt([e]));var r=g.forwardRef(function(i,o){var c=i.type,f=i.children,v=(0,oe.Z)(i,sr),m=null;return i.type&&(m=g.createElement("use",{xlinkHref:"#".concat(c)})),f&&(m=f),g.createElement(ur,(0,Ct.Z)({},n,v,{ref:o}),m)});return r.displayName="Iconfont",r}function dr(a){return/\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(a)}var fr=d(83062),vr=d(48054),dn={navTheme:"light",layout:"side",contentWidth:"Fluid",fixedHeader:!1,fixSiderbar:!0,iconfontUrl:"",colorPrimary:"#1677FF",splitMenus:!1},mr=function(e,t){var n,r,i=t.includes("horizontal")?(n=e.layout)===null||n===void 0?void 0:n.header:(r=e.layout)===null||r===void 0?void 0:r.sider;return(0,s.Z)((0,s.Z)((0,u.Z)({},"".concat(e.componentCls),(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({background:"transparent",color:i==null?void 0:i.colorTextMenu,border:"none"},"".concat(e.componentCls,"-menu-item"),{transition:"none !important"}),"".concat(e.componentCls,"-submenu-has-icon"),(0,u.Z)({},"> ".concat(e.antCls,"-menu-sub"),{paddingInlineStart:10})),"".concat(e.antCls,"-menu-title-content"),{width:"100%",height:"100%",display:"inline-flex"}),"".concat(e.antCls,"-menu-title-content"),{"&:first-child":{width:"100%"}}),"".concat(e.componentCls,"-item-icon"),{display:"flex",alignItems:"center"}),"&&-collapsed",(0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(e.antCls,`-menu-item, 
        `).concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,`-menu-item, 
        `).concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu > ").concat(e.antCls,`-menu-submenu-title, 
        `).concat(e.antCls,"-menu-submenu > ").concat(e.antCls,"-menu-submenu-title"),{paddingInline:"0 !important",marginBlock:"4px !important"}),"".concat(e.antCls,"-menu-item-group > ").concat(e.antCls,"-menu-item-group-list > ").concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,`-menu-submenu-title, 
        `).concat(e.antCls,"-menu-submenu-selected > ").concat(e.antCls,"-menu-submenu-title"),{backgroundColor:i==null?void 0:i.colorBgMenuItemSelected,borderRadius:e.borderRadiusLG}),"".concat(e.componentCls,"-group"),(0,u.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{paddingInline:0}))),"&-item-title",(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({display:"flex",flexDirection:"row",alignItems:"center",gap:e.marginXS},"".concat(e.componentCls,"-item-text"),{maxWidth:"100%",textOverflow:"ellipsis",overflow:"hidden",wordBreak:"break-all",whiteSpace:"nowrap"}),"&-collapsed",(0,u.Z)((0,u.Z)({minWidth:40,height:40},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px !important",height:"16px"}}),"".concat(e.componentCls,"-item-text-has-icon"),{display:"none !important"})),"&-collapsed-level-0",{flexDirection:"column",justifyContent:"center"}),"&".concat(e.componentCls,"-group-item-title"),{gap:e.marginXS,height:18,overflow:"hidden"}),"&".concat(e.componentCls,"-item-collapsed-show-title"),(0,u.Z)({lineHeight:"16px",gap:0},"&".concat(e.componentCls,"-item-title-collapsed"),(0,u.Z)((0,u.Z)({display:"flex"},"".concat(e.componentCls,"-item-icon"),{height:"16px",width:"16px",lineHeight:"16px !important",".anticon":{lineHeight:"16px!important",height:"16px"}}),"".concat(e.componentCls,"-item-text"),{opacity:"1 !important",display:"inline !important",textAlign:"center",fontSize:12,height:12,lineHeight:"12px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",width:"100%",margin:0,padding:0,marginBlockStart:4})))),"&-group",(0,u.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:12,color:e.colorTextLabel,".anticon":{marginInlineEnd:8}})),"&-group-divider",{color:e.colorTextSecondary,fontSize:12,lineHeight:20})),t.includes("horizontal")?{}:(0,u.Z)({},"".concat(e.antCls,"-menu-submenu").concat(e.antCls,"-menu-submenu-popup"),(0,u.Z)({},"".concat(e.componentCls,"-item-title"),{alignItems:"flex-start"}))),{},(0,u.Z)({},"".concat(e.antCls,"-menu-submenu-popup"),{backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"}))};function hr(a,e){return(0,Le.Xj)("ProLayoutBaseMenu"+e,function(t){var n=(0,s.Z)((0,s.Z)({},t),{},{componentCls:".".concat(a)});return[mr(n,e||"inline")]})}var fn=function(e){var t=(0,g.useState)(e.collapsed),n=(0,b.Z)(t,2),r=n[0],i=n[1],o=(0,g.useState)(!1),c=(0,b.Z)(o,2),f=c[0],v=c[1];return(0,g.useEffect)(function(){v(!1),setTimeout(function(){i(e.collapsed)},400)},[e.collapsed]),e.disable?e.children:(0,l.jsx)(fr.Z,{title:e.title,open:r&&e.collapsed?f:!1,placement:"right",onOpenChange:v,children:e.children})},vn=cn({scriptUrl:dn.iconfontUrl}),mn=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"icon-",n=arguments.length>2?arguments[2]:void 0;if(typeof e=="string"&&e!==""){if(Dt(e)||dr(e))return(0,l.jsx)("img",{width:16,src:e,alt:"icon",className:n},e);if(e.startsWith(t))return(0,l.jsx)(vn,{type:e})}return e},hn=function(e){if(e&&typeof e=="string"){var t=e.substring(0,1).toUpperCase();return t}return null},pr=(0,ar.Z)(function a(e){var t=this;(0,rr.Z)(this,a),(0,u.Z)(this,"props",void 0),(0,u.Z)(this,"getNavMenuItems",function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=arguments.length>1?arguments[1]:void 0,i=arguments.length>2?arguments[2]:void 0;return n.map(function(o){return t.getSubMenuOrItem(o,r,i)}).filter(function(o){return o}).flat(1)}),(0,u.Z)(this,"getSubMenuOrItem",function(n,r,i){var o=t.props,c=o.subMenuItemRender,f=o.baseClassName,v=o.prefixCls,m=o.collapsed,I=o.menu,x=o.iconPrefixes,Z=o.layout,N=(I==null?void 0:I.type)==="group"&&Z!=="top",D=t.props.token,w=t.getIntlName(n),k=(n==null?void 0:n.children)||(n==null?void 0:n.routes),K=N&&r===0?"group":void 0;if(Array.isArray(k)&&k.length>0){var E,T,_,C,L,te=r===0||N&&r===1,z=mn(n.icon,x,"".concat(f,"-icon ").concat((E=t.props)===null||E===void 0?void 0:E.hashId)),O=m&&te?hn(w):null,ie=(0,l.jsxs)("div",{className:fe()("".concat(f,"-item-title"),(T=t.props)===null||T===void 0?void 0:T.hashId,(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(f,"-item-title-collapsed"),m),"".concat(f,"-item-title-collapsed-level-").concat(i),m),"".concat(f,"-group-item-title"),K==="group"),"".concat(f,"-item-collapsed-show-title"),(I==null?void 0:I.collapsedShowTitle)&&m)),children:[K==="group"&&m?null:te&&z?(0,l.jsx)("span",{className:"".concat(f,"-item-icon ").concat((_=t.props)===null||_===void 0?void 0:_.hashId).trim(),children:z}):O,(0,l.jsx)("span",{className:fe()("".concat(f,"-item-text"),(C=t.props)===null||C===void 0?void 0:C.hashId,(0,u.Z)({},"".concat(f,"-item-text-has-icon"),K!=="group"&&te&&(z||O))),children:w})]}),Ce=c?c((0,s.Z)((0,s.Z)({},n),{},{isUrl:!1}),ie,t.props):ie;if(N&&r===0&&t.props.collapsed&&!I.collapsedShowGroupTitle)return t.getNavMenuItems(k,r+1,r);var S=t.getNavMenuItems(k,r+1,N&&r===0&&t.props.collapsed?r:r+1);return[{type:K,key:n.key||n.path,label:Ce,onClick:N?void 0:n.onTitleClick,children:S,className:fe()((0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(f,"-group"),K==="group"),"".concat(f,"-submenu"),K!=="group"),"".concat(f,"-submenu-has-icon"),K!=="group"&&te&&z))},N&&r===0?{type:"divider",prefixCls:v,className:"".concat(f,"-divider"),key:(n.key||n.path)+"-group-divider",style:{padding:0,borderBlockEnd:0,margin:t.props.collapsed?"4px":"6px 16px",marginBlockStart:t.props.collapsed?4:8,borderColor:D==null||(L=D.layout)===null||L===void 0||(L=L.sider)===null||L===void 0?void 0:L.colorMenuItemDivider}}:void 0].filter(Boolean)}return{className:"".concat(f,"-menu-item"),disabled:n.disabled,key:n.key||n.path,onClick:n.onTitleClick,label:t.getMenuItemPath(n,r,i)}}),(0,u.Z)(this,"getIntlName",function(n){var r=n.name,i=n.locale,o=t.props,c=o.menu,f=o.formatMessage,v=r;return i&&(c==null?void 0:c.locale)!==!1&&(v=f==null?void 0:f({id:i,defaultMessage:r})),t.props.menuTextRender?t.props.menuTextRender(n,v,t.props):v}),(0,u.Z)(this,"getMenuItemPath",function(n,r,i){var o,c,f,v,m=t.conversionPath(n.path||"/"),I=t.props,x=I.location,Z=x===void 0?{pathname:"/"}:x,N=I.isMobile,D=I.onCollapse,w=I.menuItemRender,k=I.iconPrefixes,K=t.getIntlName(n),E=t.props,T=E.baseClassName,_=E.menu,C=E.collapsed,L=(_==null?void 0:_.type)==="group",te=r===0||L&&r===1,z=te?mn(n.icon,k,"".concat(T,"-icon ").concat((o=t.props)===null||o===void 0?void 0:o.hashId)):null,O=C&&te?hn(K):null,ie=(0,l.jsxs)("div",{className:fe()("".concat(T,"-item-title"),(c=t.props)===null||c===void 0?void 0:c.hashId,(0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(T,"-item-title-collapsed"),C),"".concat(T,"-item-title-collapsed-level-").concat(i),C),"".concat(T,"-item-collapsed-show-title"),(_==null?void 0:_.collapsedShowTitle)&&C)),children:[(0,l.jsx)("span",{className:"".concat(T,"-item-icon ").concat((f=t.props)===null||f===void 0?void 0:f.hashId).trim(),style:{display:O===null&&!z?"none":""},children:z||(0,l.jsx)("span",{className:"anticon",children:O})}),(0,l.jsx)("span",{className:fe()("".concat(T,"-item-text"),(v=t.props)===null||v===void 0?void 0:v.hashId,(0,u.Z)({},"".concat(T,"-item-text-has-icon"),te&&(z||O))),children:K})]},m),Ce=Dt(m);if(Ce){var S,Se,B;ie=(0,l.jsxs)("span",{onClick:function(){var Ie,ge;(Ie=window)===null||Ie===void 0||(ge=Ie.open)===null||ge===void 0||ge.call(Ie,m,"_blank")},className:fe()("".concat(T,"-item-title"),(S=t.props)===null||S===void 0?void 0:S.hashId,(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(T,"-item-title-collapsed"),C),"".concat(T,"-item-title-collapsed-level-").concat(i),C),"".concat(T,"-item-link"),!0),"".concat(T,"-item-collapsed-show-title"),(_==null?void 0:_.collapsedShowTitle)&&C)),children:[(0,l.jsx)("span",{className:"".concat(T,"-item-icon ").concat((Se=t.props)===null||Se===void 0?void 0:Se.hashId).trim(),style:{display:O===null&&!z?"none":""},children:z||(0,l.jsx)("span",{className:"anticon",children:O})}),(0,l.jsx)("span",{className:fe()("".concat(T,"-item-text"),(B=t.props)===null||B===void 0?void 0:B.hashId,(0,u.Z)({},"".concat(T,"-item-text-has-icon"),te&&(z||O))),children:K})]},m)}if(w){var re=(0,s.Z)((0,s.Z)({},n),{},{isUrl:Ce,itemPath:m,isMobile:N,replace:m===Z.pathname,onClick:function(){return D&&D(!0)},children:void 0});return r===0?(0,l.jsx)(fn,{collapsed:C,title:K,disable:n.disabledTooltip,children:w(re,ie,t.props)}):w(re,ie,t.props)}return r===0?(0,l.jsx)(fn,{collapsed:C,title:K,disable:n.disabledTooltip,children:ie}):ie}),(0,u.Z)(this,"conversionPath",function(n){return n&&n.indexOf("http")===0?n:"/".concat(n||"").replace(/\/+/g,"/")}),this.props=e}),gr=function(e,t){var n=t.layout,r=t.collapsed,i={};return e&&!r&&["side","mix"].includes(n||"mix")&&(i={openKeys:e}),i},pn=function(e){var t=e.mode,n=e.className,r=e.handleOpenChange,i=e.style,o=e.menuData,c=e.prefixCls,f=e.menu,v=e.matchMenuKeys,m=e.iconfontUrl,I=e.selectedKeys,x=e.onSelect,Z=e.menuRenderType,N=e.openKeys,D=(0,g.useContext)(M.L_),w=D.dark,k=D.token,K="".concat(c,"-base-menu-").concat(t),E=(0,g.useRef)([]),T=(0,V.Z)(f==null?void 0:f.defaultOpenAll),_=(0,b.Z)(T,2),C=_[0],L=_[1],te=(0,V.Z)(function(){return f!=null&&f.defaultOpenAll?on(o)||[]:N===!1?!1:[]},{value:N===!1?void 0:N,onChange:r}),z=(0,b.Z)(te,2),O=z[0],ie=z[1],Ce=(0,V.Z)([],{value:I,onChange:x?function(Re){x&&Re&&x(Re)}:void 0}),S=(0,b.Z)(Ce,2),Se=S[0],B=S[1];(0,g.useEffect)(function(){f!=null&&f.defaultOpenAll||N===!1||v&&(ie(v),B(v))},[v.join("-")]),(0,g.useEffect)(function(){m&&(vn=cn({scriptUrl:m}))},[m]),(0,g.useEffect)(function(){if(v.join("-")!==(Se||[]).join("-")&&B(v),!C&&N!==!1&&v.join("-")!==(O||[]).join("-")){var Re=v;(f==null?void 0:f.autoClose)===!1&&(Re=Array.from(new Set([].concat((0,bt.Z)(v),(0,bt.Z)(O||[]))))),ie(Re)}else f!=null&&f.ignoreFlatMenu&&C?ie(on(o)):L(!1)},[v.join("-")]);var re=(0,g.useMemo)(function(){return gr(O,e)},[O&&O.join(","),e.layout,e.collapsed]),he=hr(K,t),Ie=he.wrapSSR,ge=he.hashId,Pe=(0,g.useMemo)(function(){return new pr((0,s.Z)((0,s.Z)({},e),{},{token:k,menuRenderType:Z,baseClassName:K,hashId:ge}))},[e,k,Z,K,ge]);if(f!=null&&f.loading)return(0,l.jsx)("div",{style:t!=null&&t.includes("inline")?{padding:24}:{marginBlockStart:16},children:(0,l.jsx)(vr.Z,{active:!0,title:!1,paragraph:{rows:t!=null&&t.includes("inline")?6:1}})});e.openKeys===!1&&!e.handleOpenChange&&(E.current=v);var Te=e.postMenuData?e.postMenuData(o):o;return Te&&(Te==null?void 0:Te.length)<1?null:Ie((0,g.createElement)(ct.Z,(0,s.Z)((0,s.Z)({},re),{},{_internalDisableMenuItemTitleTooltip:!0,key:"Menu",mode:t,inlineIndent:16,defaultOpenKeys:E.current,theme:w?"dark":"light",selectedKeys:Se,style:(0,s.Z)({backgroundColor:"transparent",border:"none"},i),className:fe()(n,ge,K,(0,u.Z)((0,u.Z)({},"".concat(K,"-horizontal"),t==="horizontal"),"".concat(K,"-collapsed"),e.collapsed)),items:Pe.getNavMenuItems(Te,0,0),onOpenChange:function($e){e.collapsed||ie($e)}},e.menuProps)))};function yr(a,e){var t=e.stylish,n=e.proLayoutCollapsedWidth;return(0,Le.Xj)("ProLayoutSiderMenuStylish",function(r){var i=(0,s.Z)((0,s.Z)({},r),{},{componentCls:".".concat(a),proLayoutCollapsedWidth:n});return t?[(0,u.Z)({},"div".concat(r.proComponentsCls,"-layout"),(0,u.Z)({},"".concat(i.componentCls),t==null?void 0:t(i)))]:[]})}var xr=["title","render"],Cr=g.memo(function(a){return(0,l.jsx)(l.Fragment,{children:a.children})}),br=Je.Z.Sider,gn=Je.Z._InternalSiderContext,Sr=gn===void 0?{Provider:Cr}:gn,_t=function(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"menuHeaderRender",n=e.logo,r=e.title,i=e.layout,o=e[t];if(o===!1)return null;var c=Ot(n),f=(0,l.jsx)("h1",{children:r!=null?r:"Ant Design Pro"});return o?o(c,e.collapsed?null:f,e):e.isMobile?null:i==="mix"&&t==="menuHeaderRender"?!1:e.collapsed?(0,l.jsx)("a",{children:c},"title"):(0,l.jsxs)("a",{children:[c,f]},"title")},yn=function(e){var t,n=e.collapsed,r=e.originCollapsed,i=e.fixSiderbar,o=e.menuFooterRender,c=e.onCollapse,f=e.theme,v=e.siderWidth,m=e.isMobile,I=e.onMenuHeaderClick,x=e.breakpoint,Z=x===void 0?"lg":x,N=e.style,D=e.layout,w=e.menuExtraRender,k=w===void 0?!1:w,K=e.links,E=e.menuContentRender,T=e.collapsedButtonRender,_=e.prefixCls,C=e.avatarProps,L=e.rightContentRender,te=e.actionsRender,z=e.onOpenChange,O=e.stylish,ie=e.logoStyle,Ce=(0,g.useContext)(M.L_),S=Ce.hashId,Se=(0,g.useMemo)(function(){return!(m||D==="mix")},[m,D]),B="".concat(_,"-sider"),re=64,he=yr("".concat(B,".").concat(B,"-stylish"),{stylish:O,proLayoutCollapsedWidth:re}),Ie=fe()("".concat(B),S,(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(B,"-fixed"),i),"".concat(B,"-fixed-mix"),D==="mix"&&!m&&i),"".concat(B,"-collapsed"),e.collapsed),"".concat(B,"-layout-").concat(D),D&&!m),"".concat(B,"-light"),f!=="dark"),"".concat(B,"-mix"),D==="mix"&&!m),"".concat(B,"-stylish"),!!O)),ge=_t(e),Pe=k&&k(e),Te=(0,g.useMemo)(function(){return E!==!1&&(0,g.createElement)(pn,(0,s.Z)((0,s.Z)({},e),{},{key:"base-menu",mode:n&&!m?"vertical":"inline",handleOpenChange:z,style:{width:"100%"},className:"".concat(B,"-menu ").concat(S).trim()}))},[B,S,E,z,e]),Re=(K||[]).map(function(we,Fe){return{className:"".concat(B,"-link"),label:we,key:Fe}}),$e=(0,g.useMemo)(function(){return E?E(e,Te):Te},[E,Te,e]),Ae=(0,g.useMemo)(function(){if(!C)return null;var we=C.title,Fe=C.render,_e=(0,oe.Z)(C,xr),It=(0,l.jsxs)("div",{className:"".concat(B,"-actions-avatar"),children:[_e!=null&&_e.src||_e!=null&&_e.srcSet||_e.icon||_e.children?(0,l.jsx)($t.C,(0,s.Z)({size:28},_e)):null,C.title&&!n&&(0,l.jsx)("span",{children:we})]});return Fe?Fe(C,It,e):It},[C,B,n]),Oe=(0,g.useMemo)(function(){return te?(0,l.jsx)(Qa.Z,{align:"center",size:4,direction:n?"vertical":"horizontal",className:fe()(["".concat(B,"-actions-list"),n&&"".concat(B,"-actions-list-collapsed"),S]),children:[te==null?void 0:te(e)].flat(1).map(function(we,Fe){return(0,l.jsx)("div",{className:"".concat(B,"-actions-list-item ").concat(S).trim(),children:we},Fe)})}):null},[te,B,n]),ze=(0,g.useMemo)(function(){return(0,l.jsx)(Ht,{onItemClick:e.itemClick,appListRender:e.appListRender,appList:e.appList,prefixCls:e.prefixCls})},[e.appList,e.appListRender,e.prefixCls]),ke=(0,g.useMemo)(function(){if(T===!1)return null;var we=(0,l.jsx)(nr,{isMobile:m,collapsed:r,className:"".concat(B,"-collapsed-button"),onClick:function(){c==null||c(!r)}});return T?T(n,we):we},[T,m,r,B,n,c]),Ge=(0,g.useMemo)(function(){return!Ae&&!Oe?null:(0,l.jsxs)("div",{className:fe()("".concat(B,"-actions"),S,n&&"".concat(B,"-actions-collapsed")),children:[Ae,Oe]})},[Oe,Ae,B,n,S]),Ve=(0,g.useMemo)(function(){var we;return e!=null&&(we=e.menu)!==null&&we!==void 0&&we.hideMenuWhenCollapsed&&n?"".concat(B,"-hide-menu-collapsed"):null},[B,n,e==null||(t=e.menu)===null||t===void 0?void 0:t.hideMenuWhenCollapsed]),dt=o&&(o==null?void 0:o(e)),Zt=(0,l.jsxs)(l.Fragment,{children:[ge&&(0,l.jsxs)("div",{className:fe()([fe()("".concat(B,"-logo"),S,(0,u.Z)({},"".concat(B,"-logo-collapsed"),n))]),onClick:Se?I:void 0,id:"logo",style:ie,children:[ge,ze]}),Pe&&(0,l.jsx)("div",{className:fe()(["".concat(B,"-extra"),!ge&&"".concat(B,"-extra-no-logo"),S]),children:Pe}),(0,l.jsx)("div",{style:{flex:1,overflowY:"auto",overflowX:"hidden"},children:$e}),(0,l.jsxs)(Sr.Provider,{value:{},children:[K?(0,l.jsx)("div",{className:"".concat(B,"-links ").concat(S).trim(),children:(0,l.jsx)(ct.Z,{inlineIndent:16,className:"".concat(B,"-link-menu ").concat(S).trim(),selectedKeys:[],openKeys:[],theme:f,mode:"inline",items:Re})}):null,Se&&(0,l.jsxs)(l.Fragment,{children:[Ge,!Oe&&L?(0,l.jsx)("div",{className:fe()("".concat(B,"-actions"),S,(0,u.Z)({},"".concat(B,"-actions-collapsed"),n)),children:L==null?void 0:L(e)}):null]}),dt&&(0,l.jsx)("div",{className:fe()(["".concat(B,"-footer"),S,(0,u.Z)({},"".concat(B,"-footer-collapsed"),n)]),children:dt})]})]});return he.wrapSSR((0,l.jsxs)(l.Fragment,{children:[i&&!m&&!Ve&&(0,l.jsx)("div",{style:(0,s.Z)({width:n?re:v,overflow:"hidden",flex:"0 0 ".concat(n?re:v,"px"),maxWidth:n?re:v,minWidth:n?re:v,transition:"all 0.2s ease 0s"},N)}),(0,l.jsxs)(br,{collapsible:!0,trigger:null,collapsed:n,breakpoint:Z===!1?void 0:Z,onCollapse:function(Fe){m||c==null||c(Fe)},collapsedWidth:re,style:N,theme:f,width:v,className:fe()(Ie,S,Ve),children:[Ve?(0,l.jsx)("div",{className:"".concat(B,"-hide-when-collapsed ").concat(S).trim(),style:{height:"100%",width:"100%",opacity:Ve?0:1},children:Zt}):Zt,ke]})]}))},Mr=d(10178),Zr=d(9220),Ir=function(e){var t,n,r,i,o;return(0,u.Z)({},e.componentCls,{"&-header-actions":{display:"flex",height:"100%",alignItems:"center","&-item":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingBlock:0,paddingInline:2,color:(t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.colorTextRightActionsItem,fontSize:"16px",cursor:"pointer",borderRadius:e.borderRadius,"> *":{paddingInline:6,paddingBlock:6,borderRadius:e.borderRadius,"&:hover":{backgroundColor:(n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.colorBgRightActionsItemHover}}},"&-avatar":{display:"inline-flex",alignItems:"center",justifyContent:"center",paddingInlineStart:e.padding,paddingInlineEnd:e.padding,cursor:"pointer",color:(r=e.layout)===null||r===void 0||(r=r.header)===null||r===void 0?void 0:r.colorTextRightActionsItem,"> div":{height:"44px",color:(i=e.layout)===null||i===void 0||(i=i.header)===null||i===void 0?void 0:i.colorTextRightActionsItem,paddingInline:8,paddingBlock:8,cursor:"pointer",display:"flex",alignItems:"center",lineHeight:"44px",borderRadius:e.borderRadius,"&:hover":{backgroundColor:(o=e.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.colorBgRightActionsItemHover}}}}})};function Tr(a){return(0,Le.Xj)("ProLayoutRightContent",function(e){var t=(0,s.Z)((0,s.Z)({},e),{},{componentCls:".".concat(a)});return[Ir(t)]})}var Rr=["rightContentRender","avatarProps","actionsRender","headerContentRender"],jr=["title","render"],xn=function(e){var t=e.rightContentRender,n=e.avatarProps,r=e.actionsRender,i=e.headerContentRender,o=(0,oe.Z)(e,Rr),c=(0,g.useContext)(We.ZP.ConfigContext),f=c.getPrefixCls,v="".concat(f(),"-pro-global-header"),m=Tr(v),I=m.wrapSSR,x=m.hashId,Z=(0,g.useState)("auto"),N=(0,b.Z)(Z,2),D=N[0],w=N[1],k=(0,g.useMemo)(function(){if(!n)return null;var _=n.title,C=n.render,L=(0,oe.Z)(n,jr),te=[L!=null&&L.src||L!=null&&L.srcSet||L.icon||L.children?(0,g.createElement)($t.C,(0,s.Z)((0,s.Z)({},L),{},{size:28,key:"avatar"})):null,_?(0,l.jsx)("span",{style:{marginInlineStart:8},children:_},"name"):void 0];return C?C(n,(0,l.jsx)("div",{children:te}),o):(0,l.jsx)("div",{children:te})},[n]),K=r||k?function(_){var C=r&&(r==null?void 0:r(_));return!C&&!k?null:Array.isArray(C)?I((0,l.jsxs)("div",{className:"".concat(v,"-header-actions ").concat(x).trim(),children:[C.filter(Boolean).map(function(L,te){var z=!1;if(g.isValidElement(L)){var O;z=!!(L!=null&&(O=L.props)!==null&&O!==void 0&&O["aria-hidden"])}return(0,l.jsx)("div",{className:fe()("".concat(v,"-header-actions-item ").concat(x),(0,u.Z)({},"".concat(v,"-header-actions-hover"),!z)),children:L},te)}),k&&(0,l.jsx)("span",{className:"".concat(v,"-header-actions-avatar ").concat(x).trim(),children:k})]})):I((0,l.jsxs)("div",{className:"".concat(v,"-header-actions ").concat(x).trim(),children:[C,k&&(0,l.jsx)("span",{className:"".concat(v,"-header-actions-avatar ").concat(x).trim(),children:k})]}))}:void 0,E=(0,Mr.D)(function(){var _=(0,de.Z)((0,Me.Z)().mark(function C(L){return(0,Me.Z)().wrap(function(z){for(;;)switch(z.prev=z.next){case 0:w(L);case 1:case"end":return z.stop()}},C)}));return function(C){return _.apply(this,arguments)}}(),160),T=K||t;return(0,l.jsx)("div",{className:"".concat(v,"-right-content ").concat(x).trim(),style:{minWidth:D,height:"100%"},children:(0,l.jsx)("div",{style:{height:"100%"},children:(0,l.jsx)(Zr.default,{onResize:function(C){var L=C.width;E.run(L)},children:T?(0,l.jsx)("div",{style:{display:"flex",alignItems:"center",height:"100%",justifyContent:"flex-end"},children:T((0,s.Z)((0,s.Z)({},o),{},{rightContentSize:D}))}):null})})})},Pr=function(e){var t,n;return(0,u.Z)({},e.componentCls,{position:"relative",width:"100%",height:"100%",backgroundColor:"transparent",".anticon":{color:"inherit"},"&-main":{display:"flex",height:"100%",paddingInlineStart:"16px","&-left":(0,u.Z)({display:"flex",alignItems:"center"},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16,marginInlineStart:-8})},"&-wide":{maxWidth:1152,margin:"0 auto"},"&-logo":{position:"relative",display:"flex",height:"100%",alignItems:"center",overflow:"hidden","> *:first-child":{display:"flex",alignItems:"center",minHeight:"22px",fontSize:"22px"},"> *:first-child > img":{display:"inline-block",height:"32px",verticalAlign:"middle"},"> *:first-child > h1":{display:"inline-block",marginBlock:0,marginInline:0,lineHeight:"24px",marginInlineStart:6,fontWeight:"600",fontSize:"16px",color:(t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.colorHeaderTitle,verticalAlign:"top"}},"&-menu":{minWidth:0,display:"flex",alignItems:"center",paddingInline:6,paddingBlock:6,lineHeight:"".concat(Math.max((((n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.heightLayoutHeader)||56)-12,40),"px")}})};function wr(a){return(0,Le.Xj)("ProLayoutTopNavHeader",function(e){var t=(0,s.Z)((0,s.Z)({},e),{},{componentCls:".".concat(a)});return[Pr(t)]})}var Cn=function(e){var t,n,r,i,o,c,f,v=(0,g.useRef)(null),m=e.onMenuHeaderClick,I=e.contentWidth,x=e.rightContentRender,Z=e.className,N=e.style,D=e.headerContentRender,w=e.layout,k=e.actionsRender,K=(0,g.useContext)(We.ZP.ConfigContext),E=K.getPrefixCls,T=(0,g.useContext)(M.L_),_=T.dark,C="".concat(e.prefixCls||E("pro"),"-top-nav-header"),L=wr(C),te=L.wrapSSR,z=L.hashId,O=void 0;e.menuHeaderRender!==void 0?O="menuHeaderRender":(w==="mix"||w==="top")&&(O="headerTitleRender");var ie=_t((0,s.Z)((0,s.Z)({},e),{},{collapsed:!1}),O),Ce=(0,g.useContext)(M.L_),S=Ce.token,Se=(0,g.useMemo)(function(){var B,re,he,Ie,ge,Pe,Te,Re,$e,Ae,Oe,ze,ke,Ge=(0,l.jsx)(We.ZP,{theme:{hashed:(0,M.nu)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"},Menu:(0,s.Z)({},P({colorItemBg:((B=S.layout)===null||B===void 0||(B=B.header)===null||B===void 0?void 0:B.colorBgHeader)||"transparent",colorSubItemBg:((re=S.layout)===null||re===void 0||(re=re.header)===null||re===void 0?void 0:re.colorBgHeader)||"transparent",radiusItem:S.borderRadius,colorItemBgSelected:((he=S.layout)===null||he===void 0||(he=he.header)===null||he===void 0?void 0:he.colorBgMenuItemSelected)||(S==null?void 0:S.colorBgTextHover),itemHoverBg:((Ie=S.layout)===null||Ie===void 0||(Ie=Ie.header)===null||Ie===void 0?void 0:Ie.colorBgMenuItemHover)||(S==null?void 0:S.colorBgTextHover),colorItemBgSelectedHorizontal:((ge=S.layout)===null||ge===void 0||(ge=ge.header)===null||ge===void 0?void 0:ge.colorBgMenuItemSelected)||(S==null?void 0:S.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:((Pe=S.layout)===null||Pe===void 0||(Pe=Pe.header)===null||Pe===void 0?void 0:Pe.colorTextMenu)||(S==null?void 0:S.colorTextSecondary),colorItemTextHoverHorizontal:((Te=S.layout)===null||Te===void 0||(Te=Te.header)===null||Te===void 0?void 0:Te.colorTextMenuActive)||(S==null?void 0:S.colorText),colorItemTextSelectedHorizontal:((Re=S.layout)===null||Re===void 0||(Re=Re.header)===null||Re===void 0?void 0:Re.colorTextMenuSelected)||(S==null?void 0:S.colorTextBase),horizontalItemBorderRadius:4,colorItemTextHover:(($e=S.layout)===null||$e===void 0||($e=$e.header)===null||$e===void 0?void 0:$e.colorTextMenuActive)||"rgba(0, 0, 0, 0.85)",horizontalItemHoverBg:((Ae=S.layout)===null||Ae===void 0||(Ae=Ae.header)===null||Ae===void 0?void 0:Ae.colorBgMenuItemHover)||"rgba(0, 0, 0, 0.04)",colorItemTextSelected:((Oe=S.layout)===null||Oe===void 0||(Oe=Oe.header)===null||Oe===void 0?void 0:Oe.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:S==null?void 0:S.colorBgElevated,subMenuItemBg:S==null?void 0:S.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:S==null?void 0:S.colorBgElevated}))},token:{colorBgElevated:((ze=S.layout)===null||ze===void 0||(ze=ze.header)===null||ze===void 0?void 0:ze.colorBgHeader)||"transparent"}},children:(0,l.jsx)(pn,(0,s.Z)((0,s.Z)((0,s.Z)({theme:_?"dark":"light"},e),{},{className:"".concat(C,"-base-menu ").concat(z).trim()},e.menuProps),{},{style:(0,s.Z)({width:"100%"},(ke=e.menuProps)===null||ke===void 0?void 0:ke.style),collapsed:!1,menuRenderType:"header",mode:"horizontal"}))});return D?D(e,Ge):Ge},[(t=S.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.colorBgHeader,(n=S.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.colorBgMenuItemSelected,(r=S.layout)===null||r===void 0||(r=r.header)===null||r===void 0?void 0:r.colorBgMenuItemHover,(i=S.layout)===null||i===void 0||(i=i.header)===null||i===void 0?void 0:i.colorTextMenu,(o=S.layout)===null||o===void 0||(o=o.header)===null||o===void 0?void 0:o.colorTextMenuActive,(c=S.layout)===null||c===void 0||(c=c.header)===null||c===void 0?void 0:c.colorTextMenuSelected,(f=S.layout)===null||f===void 0||(f=f.header)===null||f===void 0?void 0:f.colorBgMenuElevated,S.borderRadius,S==null?void 0:S.colorBgTextHover,S==null?void 0:S.colorTextSecondary,S==null?void 0:S.colorText,S==null?void 0:S.colorTextBase,S.colorBgElevated,_,e,C,z,D]);return te((0,l.jsx)("div",{className:fe()(C,z,Z,(0,u.Z)({},"".concat(C,"-light"),!0)),style:N,children:(0,l.jsxs)("div",{ref:v,className:fe()("".concat(C,"-main"),z,(0,u.Z)({},"".concat(C,"-wide"),I==="Fixed"&&w==="top")),children:[ie&&(0,l.jsxs)("div",{className:fe()("".concat(C,"-main-left ").concat(z)),onClick:m,children:[(0,l.jsx)(Ht,(0,s.Z)({},e)),(0,l.jsx)("div",{className:"".concat(C,"-logo ").concat(z).trim(),id:"logo",children:ie},"logo")]}),(0,l.jsx)("div",{style:{flex:1},className:"".concat(C,"-menu ").concat(z).trim(),children:Se}),(x||k||e.avatarProps)&&(0,l.jsx)(xn,(0,s.Z)((0,s.Z)({rightContentRender:x},e),{},{prefixCls:C}))]})}))},Br=function(e){var t,n,r;return(0,u.Z)({},e.componentCls,(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({position:"relative",background:"transparent",display:"flex",alignItems:"center",marginBlock:0,marginInline:16,height:((t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.heightLayoutHeader)||56,boxSizing:"border-box","> a":{height:"100%"}},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginInlineEnd:16}),"&-collapsed-button",{minHeight:"22px",color:(n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.colorHeaderTitle,fontSize:"18px",marginInlineEnd:"16px"}),"&-logo",{position:"relative",marginInlineEnd:"16px",a:{display:"flex",alignItems:"center",height:"100%",minHeight:"22px",fontSize:"20px"},img:{height:"28px"},h1:{height:"32px",marginBlock:0,marginInline:0,marginInlineStart:8,fontWeight:"600",color:((r=e.layout)===null||r===void 0||(r=r.header)===null||r===void 0?void 0:r.colorHeaderTitle)||e.colorTextHeading,fontSize:"18px",lineHeight:"32px"},"&-mix":{display:"flex",alignItems:"center"}}),"&-logo-mobile",{minWidth:"24px",marginInlineEnd:0}))};function Er(a){return(0,Le.Xj)("ProLayoutGlobalHeader",function(e){var t=(0,s.Z)((0,s.Z)({},e),{},{componentCls:".".concat(a)});return[Br(t)]})}var Lr=function(e,t){return e===!1?null:e?e(t,null):t},Ar=function(e){var t=e.isMobile,n=e.logo,r=e.collapsed,i=e.onCollapse,o=e.rightContentRender,c=e.menuHeaderRender,f=e.onMenuHeaderClick,v=e.className,m=e.style,I=e.layout,x=e.children,Z=e.splitMenus,N=e.menuData,D=e.prefixCls,w=(0,g.useContext)(We.ZP.ConfigContext),k=w.getPrefixCls,K=w.direction,E="".concat(D||k("pro"),"-global-header"),T=Er(E),_=T.wrapSSR,C=T.hashId,L=fe()(v,E,C);if(I==="mix"&&!t&&Z){var te=(N||[]).map(function(Ce){return(0,s.Z)((0,s.Z)({},Ce),{},{children:void 0,routes:void 0})}),z=xt(te);return(0,l.jsx)(Cn,(0,s.Z)((0,s.Z)({mode:"horizontal"},e),{},{splitMenus:!1,menuData:z}))}var O=fe()("".concat(E,"-logo"),C,(0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(E,"-logo-rtl"),K==="rtl"),"".concat(E,"-logo-mix"),I==="mix"),"".concat(E,"-logo-mobile"),t)),ie=(0,l.jsx)("span",{className:O,children:(0,l.jsx)("a",{children:Ot(n)})},"logo");return _((0,l.jsxs)("div",{className:L,style:(0,s.Z)({},m),children:[t&&(0,l.jsx)("span",{className:"".concat(E,"-collapsed-button ").concat(C).trim(),onClick:function(){i==null||i(!r)},children:(0,l.jsx)(Fa,{})}),t&&Lr(c,ie),I==="mix"&&!t&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(Ht,(0,s.Z)({},e)),(0,l.jsx)("div",{className:O,onClick:f,children:_t((0,s.Z)((0,s.Z)({},e),{},{collapsed:!1}),"headerTitleRender")})]}),(0,l.jsx)("div",{style:{flex:1},children:x}),(o||e.actionsRender||e.avatarProps)&&(0,l.jsx)(xn,(0,s.Z)({rightContentRender:o},e))]}))},Nr=function(e){var t,n,r,i;return(0,u.Z)({},"".concat(e.proComponentsCls,"-layout"),(0,u.Z)({},"".concat(e.antCls,"-layout-header").concat(e.componentCls),{height:((t=e.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.heightLayoutHeader)||56,lineHeight:"".concat(((n=e.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.heightLayoutHeader)||56,"px"),zIndex:19,width:"100%",paddingBlock:0,paddingInline:0,borderBlockEnd:"1px solid ".concat(e.colorSplit),backgroundColor:((r=e.layout)===null||r===void 0||(r=r.header)===null||r===void 0?void 0:r.colorBgHeader)||"rgba(255, 255, 255, 0.4)",WebkitBackdropFilter:"blur(8px)",backdropFilter:"blur(8px)",transition:"background-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)","&-fixed-header":{position:"fixed",insetBlockStart:0,width:"100%",zIndex:100,insetInlineEnd:0},"&-fixed-header-scroll":{backgroundColor:((i=e.layout)===null||i===void 0||(i=i.header)===null||i===void 0?void 0:i.colorBgScrollHeader)||"rgba(255, 255, 255, 0.8)"},"&-header-actions":{display:"flex",alignItems:"center",fontSize:"16",cursor:"pointer","& &-item":{paddingBlock:0,paddingInline:8,"&:hover":{color:e.colorText}}},"&-header-realDark":{boxShadow:"0 2px 8px 0 rgba(0, 0, 0, 65%)"},"&-header-actions-header-action":{transition:"width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)"}}))};function Dr(a){return(0,Le.Xj)("ProLayoutHeader",function(e){var t=(0,s.Z)((0,s.Z)({},e),{},{componentCls:".".concat(a)});return[Nr(t)]})}function Or(a,e){var t=e.stylish,n=e.proLayoutCollapsedWidth;return(0,Le.Xj)("ProLayoutHeaderStylish",function(r){var i=(0,s.Z)((0,s.Z)({},r),{},{componentCls:".".concat(a),proLayoutCollapsedWidth:n});return t?[(0,u.Z)({},"div".concat(r.proComponentsCls,"-layout"),(0,u.Z)({},"".concat(i.componentCls),t==null?void 0:t(i)))]:[]})}var bn=Je.Z.Header,Hr=function(e){var t,n,r,i=e.isMobile,o=e.fixedHeader,c=e.className,f=e.style,v=e.collapsed,m=e.prefixCls,I=e.onCollapse,x=e.layout,Z=e.headerRender,N=e.headerContentRender,D=(0,g.useContext)(M.L_),w=D.token,k=(0,g.useContext)(We.ZP.ConfigContext),K=(0,g.useState)(!1),E=(0,b.Z)(K,2),T=E[0],_=E[1],C=o||x==="mix",L=(0,g.useCallback)(function(){var B=x==="top",re=xt(e.menuData||[]),he=(0,l.jsx)(Ar,(0,s.Z)((0,s.Z)({onCollapse:I},e),{},{menuData:re,children:N&&N(e,null)}));return B&&!i&&(he=(0,l.jsx)(Cn,(0,s.Z)((0,s.Z)({mode:"horizontal",onCollapse:I},e),{},{menuData:re}))),Z&&typeof Z=="function"?Z(e,he):he},[N,Z,i,x,I,e]);(0,g.useEffect)(function(){var B,re=(k==null||(B=k.getTargetContainer)===null||B===void 0?void 0:B.call(k))||document.body,he=function(){var ge,Pe=re.scrollTop;return Pe>(((ge=w.layout)===null||ge===void 0||(ge=ge.header)===null||ge===void 0?void 0:ge.heightLayoutHeader)||56)&&!T?(_(!0),!0):(T&&_(!1),!1)};if(C&&typeof window!="undefined")return re.addEventListener("scroll",he,{passive:!0}),function(){re.removeEventListener("scroll",he)}},[(t=w.layout)===null||t===void 0||(t=t.header)===null||t===void 0?void 0:t.heightLayoutHeader,C,T]);var te=x==="top",z="".concat(m,"-layout-header"),O=Dr(z),ie=O.wrapSSR,Ce=O.hashId,S=Or("".concat(z,".").concat(z,"-stylish"),{proLayoutCollapsedWidth:64,stylish:e.stylish}),Se=fe()(c,Ce,z,(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(z,"-fixed-header"),C),"".concat(z,"-fixed-header-scroll"),T),"".concat(z,"-mix"),x==="mix"),"".concat(z,"-fixed-header-action"),!v),"".concat(z,"-top-menu"),te),"".concat(z,"-header"),!0),"".concat(z,"-stylish"),!!e.stylish));return x==="side"&&!i?null:S.wrapSSR(ie((0,l.jsx)(l.Fragment,{children:(0,l.jsxs)(We.ZP,{theme:{hashed:(0,M.nu)(),components:{Layout:{headerBg:"transparent",bodyBg:"transparent"}}},children:[C&&(0,l.jsx)(bn,{style:(0,s.Z)({height:((n=w.layout)===null||n===void 0||(n=n.header)===null||n===void 0?void 0:n.heightLayoutHeader)||56,lineHeight:"".concat(((r=w.layout)===null||r===void 0||(r=r.header)===null||r===void 0?void 0:r.heightLayoutHeader)||56,"px"),backgroundColor:"transparent",zIndex:19},f)}),(0,l.jsx)(bn,{className:Se,style:f,children:L()})]})})))},$r=d(83832),_r=d(85265),Fr=d(85982),Sn=new Fr.Keyframes("antBadgeLoadingCircle",{"0%":{display:"none",opacity:0,overflow:"hidden"},"80%":{overflow:"hidden"},"100%":{display:"unset",opacity:1}}),Wr=function(e){var t,n,r,i,o,c,f,v,m,I,x,Z;return(0,u.Z)({},"".concat(e.proComponentsCls,"-layout"),(0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(e.antCls,"-layout-sider").concat(e.componentCls),{background:((t=e.layout)===null||t===void 0||(t=t.sider)===null||t===void 0?void 0:t.colorMenuBackground)||"transparent"}),e.componentCls,(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({position:"relative",boxSizing:"border-box","&-menu":{position:"relative",zIndex:10,minHeight:"100%"}},"& ".concat(e.antCls,"-layout-sider-children"),{position:"relative",display:"flex",flexDirection:"column",height:"100%",paddingInline:(n=e.layout)===null||n===void 0||(n=n.sider)===null||n===void 0?void 0:n.paddingInlineLayoutMenu,paddingBlock:(r=e.layout)===null||r===void 0||(r=r.sider)===null||r===void 0?void 0:r.paddingBlockLayoutMenu,borderInlineEnd:"1px solid ".concat(e.colorSplit),marginInlineEnd:-1}),"".concat(e.antCls,"-menu"),(0,u.Z)((0,u.Z)({},"".concat(e.antCls,"-menu-item-group-title"),{fontSize:e.fontSizeSM,paddingBottom:4}),"".concat(e.antCls,"-menu-item:not(").concat(e.antCls,"-menu-item-selected):hover"),{color:(i=e.layout)===null||i===void 0||(i=i.sider)===null||i===void 0?void 0:i.colorTextMenuItemHover})),"&-logo",{position:"relative",display:"flex",alignItems:"center",justifyContent:"space-between",paddingInline:12,paddingBlock:16,color:(o=e.layout)===null||o===void 0||(o=o.sider)===null||o===void 0?void 0:o.colorTextMenu,cursor:"pointer",borderBlockEnd:"1px solid ".concat((c=e.layout)===null||c===void 0||(c=c.sider)===null||c===void 0?void 0:c.colorMenuItemDivider),"> a":{display:"flex",alignItems:"center",justifyContent:"center",minHeight:22,fontSize:22,"> img":{display:"inline-block",height:22,verticalAlign:"middle"},"> h1":{display:"inline-block",height:22,marginBlock:0,marginInlineEnd:0,marginInlineStart:6,color:(f=e.layout)===null||f===void 0||(f=f.sider)===null||f===void 0?void 0:f.colorTextMenuTitle,animationName:Sn,animationDuration:".4s",animationTimingFunction:"ease",fontWeight:600,fontSize:16,lineHeight:"22px",verticalAlign:"middle"}},"&-collapsed":(0,u.Z)({flexDirection:"column-reverse",margin:0,padding:12},"".concat(e.proComponentsCls,"-layout-apps-icon"),{marginBlockEnd:8,fontSize:16,transition:"font-size 0.2s ease-in-out,color 0.2s ease-in-out"})}),"&-actions",{display:"flex",alignItems:"center",justifyContent:"space-between",marginBlock:4,marginInline:0,color:(v=e.layout)===null||v===void 0||(v=v.sider)===null||v===void 0?void 0:v.colorTextMenu,"&-collapsed":{flexDirection:"column-reverse",paddingBlock:0,paddingInline:8,fontSize:16,transition:"font-size 0.3s ease-in-out"},"&-list":{color:(m=e.layout)===null||m===void 0||(m=m.sider)===null||m===void 0?void 0:m.colorTextMenuSecondary,"&-collapsed":{marginBlockEnd:8,animationName:"none"},"&-item":{paddingInline:6,paddingBlock:6,lineHeight:"16px",fontSize:16,cursor:"pointer",borderRadius:e.borderRadius,"&:hover":{background:e.colorBgTextHover}}},"&-avatar":{fontSize:14,paddingInline:8,paddingBlock:8,display:"flex",alignItems:"center",gap:e.marginXS,borderRadius:e.borderRadius,"& *":{cursor:"pointer"},"&:hover":{background:e.colorBgTextHover}}}),"&-hide-menu-collapsed",{insetInlineStart:"-".concat(e.proLayoutCollapsedWidth-12,"px"),position:"absolute"}),"&-extra",{marginBlockEnd:16,marginBlock:0,marginInline:16,"&-no-logo":{marginBlockStart:16}}),"&-links",{width:"100%",ul:{height:"auto"}}),"&-link-menu",{border:"none",boxShadow:"none",background:"transparent"}),"&-footer",{color:(I=e.layout)===null||I===void 0||(I=I.sider)===null||I===void 0?void 0:I.colorTextMenuSecondary,paddingBlockEnd:16,fontSize:e.fontSize,animationName:Sn,animationDuration:".4s",animationTimingFunction:"ease"})),"".concat(e.componentCls).concat(e.componentCls,"-fixed"),{position:"fixed",insetBlockStart:0,insetInlineStart:0,zIndex:"100",height:"100%","&-mix":{height:"calc(100% - ".concat(((x=e.layout)===null||x===void 0||(x=x.header)===null||x===void 0?void 0:x.heightLayoutHeader)||56,"px)"),insetBlockStart:"".concat(((Z=e.layout)===null||Z===void 0||(Z=Z.header)===null||Z===void 0?void 0:Z.heightLayoutHeader)||56,"px")}}))};function zr(a,e){var t=e.proLayoutCollapsedWidth;return(0,Le.Xj)("ProLayoutSiderMenu",function(n){var r=(0,s.Z)((0,s.Z)({},n),{},{componentCls:".".concat(a),proLayoutCollapsedWidth:t});return[Wr(r)]})}var Mn=function(e){var t,n=e.isMobile,r=e.siderWidth,i=e.collapsed,o=e.onCollapse,c=e.style,f=e.className,v=e.hide,m=e.prefixCls,I=e.getContainer,x=(0,g.useContext)(M.L_),Z=x.token;(0,g.useEffect)(function(){n===!0&&(o==null||o(!0))},[n]);var N=(0,an.Z)(e,["className","style"]),D=g.useContext(We.ZP.ConfigContext),w=D.direction,k=zr("".concat(m,"-sider"),{proLayoutCollapsedWidth:64}),K=k.wrapSSR,E=k.hashId,T=fe()("".concat(m,"-sider"),f,E);if(v)return null;var _=(0,h.X)(!i,function(){return o==null?void 0:o(!0)});return K(n?(0,l.jsx)(_r.Z,(0,s.Z)((0,s.Z)({placement:w==="rtl"?"right":"left",className:fe()("".concat(m,"-drawer-sider"),f)},_),{},{style:(0,s.Z)({padding:0,height:"100vh"},c),onClose:function(){o==null||o(!0)},maskClosable:!0,closable:!1,getContainer:I||!1,width:r,styles:{body:{height:"100vh",padding:0,display:"flex",flexDirection:"row",backgroundColor:(t=Z.layout)===null||t===void 0||(t=t.sider)===null||t===void 0?void 0:t.colorMenuBackground}},children:(0,l.jsx)(yn,(0,s.Z)((0,s.Z)({},N),{},{isMobile:!0,className:T,collapsed:n?!1:i,splitMenus:!1,originCollapsed:i}))})):(0,l.jsx)(yn,(0,s.Z)((0,s.Z)({className:T,originCollapsed:i},N),{},{style:c})))},Zn=d(76509),Ft=d(31707),Kr=function(e,t,n){if(n){var r=(0,bt.Z)(n.keys()).find(function(o){try{return o.startsWith("http")?!1:(0,Ft.match)(o)(e)}catch(c){return console.log("key",o,c),!1}});if(r)return n.get(r)}if(t){var i=Object.keys(t).find(function(o){try{return o!=null&&o.startsWith("http")?!1:(0,Ft.match)(o)(e)}catch(c){return console.log("key",o,c),!1}});if(i)return t[i]}return{path:""}},Wt=function(e,t){var n=e.pathname,r=n===void 0?"/":n,i=e.breadcrumb,o=e.breadcrumbMap,c=e.formatMessage,f=e.title,v=e.menu,m=v===void 0?{locale:!1}:v,I=t?"":f||"",x=Kr(r,i,o);if(!x)return{title:I,id:"",pageName:I};var Z=x.name;return m.locale!==!1&&x.locale&&c&&(Z=c({id:x.locale||"",defaultMessage:x.name})),Z?t||!f?{title:Z,id:x.locale||"",pageName:Z}:{title:"".concat(Z," - ").concat(f),id:x.locale||"",pageName:Z}:{title:I,id:x.locale||"",pageName:I}},di=function(e,t){return Wt(e,t).title},Ur={"app.setting.pagestyle":"Page style setting","app.setting.pagestyle.dark":"Dark Menu style","app.setting.pagestyle.light":"Light Menu style","app.setting.pagestyle.realdark":"Dark style (Beta)","app.setting.content-width":"Content Width","app.setting.content-width.fixed":"Fixed","app.setting.content-width.fluid":"Fluid","app.setting.themecolor":"Theme Color","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blue (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.sidermenutype":"SideMenu Type","app.setting.sidermenutype-sub":"Classic","app.setting.sidermenutype-group":"Grouping","app.setting.navigationmode":"Navigation Mode","app.setting.regionalsettings":"Regional Settings","app.setting.regionalsettings.header":"Header","app.setting.regionalsettings.menu":"Menu","app.setting.regionalsettings.footer":"Footer","app.setting.regionalsettings.menuHeader":"Menu Header","app.setting.sidemenu":"Side Menu Layout","app.setting.topmenu":"Top Menu Layout","app.setting.mixmenu":"Mix Menu Layout","app.setting.splitMenus":"Split Menus","app.setting.fixedheader":"Fixed Header","app.setting.fixedsidebar":"Fixed Sidebar","app.setting.fixedsidebar.hint":"Works on Side Menu Layout","app.setting.hideheader":"Hidden Header when scrolling","app.setting.hideheader.hint":"Works when Hidden Header is enabled","app.setting.othersettings":"Other Settings","app.setting.weakmode":"Weak Mode","app.setting.copy":"Copy Setting","app.setting.loading":"Loading theme","app.setting.copyinfo":"copy success\uFF0Cplease replace defaultSettings in src/models/setting.js","app.setting.production.hint":"Setting panel shows in development environment only, please manually modify"},kr=(0,s.Z)({},Ur),Gr={"app.setting.pagestyle":"Impostazioni di stile","app.setting.pagestyle.dark":"Tema scuro","app.setting.pagestyle.light":"Tema chiaro","app.setting.content-width":"Largezza contenuto","app.setting.content-width.fixed":"Fissa","app.setting.content-width.fluid":"Fluida","app.setting.themecolor":"Colore del tema","app.setting.themecolor.dust":"Rosso polvere","app.setting.themecolor.volcano":"Vulcano","app.setting.themecolor.sunset":"Arancione tramonto","app.setting.themecolor.cyan":"Ciano","app.setting.themecolor.green":"Verde polare","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Blu cielo mattutino","app.setting.themecolor.geekblue":"Blu geek","app.setting.themecolor.purple":"Viola dorato","app.setting.navigationmode":"Modalit\xE0 di navigazione","app.setting.sidemenu":"Menu laterale","app.setting.topmenu":"Menu in testata","app.setting.mixmenu":"Menu misto","app.setting.splitMenus":"Menu divisi","app.setting.fixedheader":"Testata fissa","app.setting.fixedsidebar":"Menu laterale fisso","app.setting.fixedsidebar.hint":"Solo se selezionato Menu laterale","app.setting.hideheader":"Nascondi testata durante lo scorrimento","app.setting.hideheader.hint":"Solo se abilitato Nascondi testata durante lo scorrimento","app.setting.othersettings":"Altre impostazioni","app.setting.weakmode":"Inverti colori","app.setting.copy":"Copia impostazioni","app.setting.loading":"Carico tema...","app.setting.copyinfo":"Impostazioni copiate con successo! Incolla il contenuto in config/defaultSettings.js","app.setting.production.hint":"Questo pannello \xE8 visibile solo durante lo sviluppo. Le impostazioni devono poi essere modificate manulamente"},Vr=(0,s.Z)({},Gr),Xr={"app.setting.pagestyle":"\uC2A4\uD0C0\uC77C \uC124\uC815","app.setting.pagestyle.dark":"\uB2E4\uD06C \uBAA8\uB4DC","app.setting.pagestyle.light":"\uB77C\uC774\uD2B8 \uBAA8\uB4DC","app.setting.content-width":"\uCEE8\uD150\uCE20 \uB108\uBE44","app.setting.content-width.fixed":"\uACE0\uC815","app.setting.content-width.fluid":"\uD750\uB984","app.setting.themecolor":"\uD14C\uB9C8 \uC0C9\uC0C1","app.setting.themecolor.dust":"Dust Red","app.setting.themecolor.volcano":"Volcano","app.setting.themecolor.sunset":"Sunset Orange","app.setting.themecolor.cyan":"Cyan","app.setting.themecolor.green":"Polar Green","app.setting.themecolor.techBlue":"Tech Blu (default)","app.setting.themecolor.daybreak":"Daybreak Blue","app.setting.themecolor.geekblue":"Geek Blue","app.setting.themecolor.purple":"Golden Purple","app.setting.navigationmode":"\uB124\uBE44\uAC8C\uC774\uC158 \uBAA8\uB4DC","app.setting.regionalsettings":"\uC601\uC5ED\uBCC4 \uC124\uC815","app.setting.regionalsettings.header":"\uD5E4\uB354","app.setting.regionalsettings.menu":"\uBA54\uB274","app.setting.regionalsettings.footer":"\uBC14\uB2E5\uAE00","app.setting.regionalsettings.menuHeader":"\uBA54\uB274 \uD5E4\uB354","app.setting.sidemenu":"\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58","app.setting.topmenu":"\uBA54\uB274 \uC0C1\uB2E8 \uBC30\uCE58","app.setting.mixmenu":"\uD63C\uD569\uD615 \uBC30\uCE58","app.setting.splitMenus":"\uBA54\uB274 \uBD84\uB9AC","app.setting.fixedheader":"\uD5E4\uB354 \uACE0\uC815","app.setting.fixedsidebar":"\uC0AC\uC774\uB4DC\uBC14 \uACE0\uC815","app.setting.fixedsidebar.hint":"'\uBA54\uB274 \uC0AC\uC774\uB4DC \uBC30\uCE58'\uB97C \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.hideheader":"\uC2A4\uD06C\uB864 \uC911 \uD5E4\uB354 \uAC10\uCD94\uAE30","app.setting.hideheader.hint":"'\uD5E4\uB354 \uAC10\uCD94\uAE30 \uC635\uC158'\uC744 \uC120\uD0DD\uD588\uC744 \uB54C \uB3D9\uC791\uD568","app.setting.othersettings":"\uB2E4\uB978 \uC124\uC815","app.setting.weakmode":"\uACE0\uB300\uBE44 \uBAA8\uB4DC","app.setting.copy":"\uC124\uC815\uAC12 \uBCF5\uC0AC","app.setting.loading":"\uD14C\uB9C8 \uB85C\uB529 \uC911","app.setting.copyinfo":"\uBCF5\uC0AC \uC131\uACF5. src/models/settings.js\uC5D0 \uC788\uB294 defaultSettings\uB97C \uAD50\uCCB4\uD574 \uC8FC\uC138\uC694.","app.setting.production.hint":"\uC124\uC815 \uD310\uB12C\uC740 \uAC1C\uBC1C \uD658\uACBD\uC5D0\uC11C\uB9CC \uBCF4\uC5EC\uC9D1\uB2C8\uB2E4. \uC9C1\uC811 \uC218\uB3D9\uC73C\uB85C \uBCC0\uACBD\uBC14\uB78D\uB2C8\uB2E4."},Yr=(0,s.Z)({},Xr),Qr={"app.setting.pagestyle":"\u6574\u4F53\u98CE\u683C\u8BBE\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u5355\u98CE\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98CE\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.content-width":"\u5185\u5BB9\u533A\u57DF\u5BBD\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BBD","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u9898\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6781\u5149\u7EFF","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8BA4\uFF09","app.setting.themecolor.daybreak":"\u62C2\u6653","app.setting.themecolor.geekblue":"\u6781\u5BA2\u84DD","app.setting.themecolor.purple":"\u9171\u7D2B","app.setting.navigationmode":"\u5BFC\u822A\u6A21\u5F0F","app.setting.sidermenutype":"\u4FA7\u8FB9\u83DC\u5355\u7C7B\u578B","app.setting.sidermenutype-sub":"\u7ECF\u5178\u6A21\u5F0F","app.setting.sidermenutype-group":"\u5206\u7EC4\u6A21\u5F0F","app.setting.regionalsettings":"\u5185\u5BB9\u533A\u57DF","app.setting.regionalsettings.header":"\u9876\u680F","app.setting.regionalsettings.menu":"\u83DC\u5355","app.setting.regionalsettings.footer":"\u9875\u811A","app.setting.regionalsettings.menuHeader":"\u83DC\u5355\u5934","app.setting.sidemenu":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40","app.setting.topmenu":"\u9876\u90E8\u83DC\u5355\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u5355\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u4FA7\u8FB9\u83DC\u5355","app.setting.fixedsidebar.hint":"\u4FA7\u8FB9\u83DC\u5355\u5E03\u5C40\u65F6\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u65F6\u9690\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u65F6\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8BBE\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8D1D\u8BBE\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F7D\u4E3B\u9898","app.setting.copyinfo":"\u62F7\u8D1D\u6210\u529F\uFF0C\u8BF7\u5230 src/defaultSettings.js \u4E2D\u66FF\u6362\u9ED8\u8BA4\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u680F\u53EA\u5728\u5F00\u53D1\u73AF\u5883\u7528\u4E8E\u9884\u89C8\uFF0C\u751F\u4EA7\u73AF\u5883\u4E0D\u4F1A\u5C55\u73B0\uFF0C\u8BF7\u62F7\u8D1D\u540E\u624B\u52A8\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"},Jr=(0,s.Z)({},Qr),qr={"app.setting.pagestyle":"\u6574\u9AD4\u98A8\u683C\u8A2D\u7F6E","app.setting.pagestyle.dark":"\u6697\u8272\u83DC\u55AE\u98A8\u683C","app.setting.pagestyle.realdark":"\u6697\u8272\u98A8\u683C(\u5B9E\u9A8C\u529F\u80FD)","app.setting.pagestyle.light":"\u4EAE\u8272\u83DC\u55AE\u98A8\u683C","app.setting.content-width":"\u5167\u5BB9\u5340\u57DF\u5BEC\u5EA6","app.setting.content-width.fixed":"\u5B9A\u5BEC","app.setting.content-width.fluid":"\u6D41\u5F0F","app.setting.themecolor":"\u4E3B\u984C\u8272","app.setting.themecolor.dust":"\u8584\u66AE","app.setting.themecolor.volcano":"\u706B\u5C71","app.setting.themecolor.sunset":"\u65E5\u66AE","app.setting.themecolor.cyan":"\u660E\u9752","app.setting.themecolor.green":"\u6975\u5149\u7DA0","app.setting.themecolor.techBlue":"\u79D1\u6280\u84DD\uFF08\u9ED8\u8A8D\uFF09","app.setting.themecolor.daybreak":"\u62C2\u66C9\u85CD","app.setting.themecolor.geekblue":"\u6975\u5BA2\u85CD","app.setting.themecolor.purple":"\u91AC\u7D2B","app.setting.navigationmode":"\u5C0E\u822A\u6A21\u5F0F","app.setting.sidemenu":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40","app.setting.topmenu":"\u9802\u90E8\u83DC\u55AE\u5E03\u5C40","app.setting.mixmenu":"\u6DF7\u5408\u83DC\u55AE\u5E03\u5C40","app.setting.splitMenus":"\u81EA\u52A8\u5206\u5272\u83DC\u5355","app.setting.fixedheader":"\u56FA\u5B9A Header","app.setting.fixedsidebar":"\u56FA\u5B9A\u5074\u908A\u83DC\u55AE","app.setting.fixedsidebar.hint":"\u5074\u908A\u83DC\u55AE\u5E03\u5C40\u6642\u53EF\u914D\u7F6E","app.setting.hideheader":"\u4E0B\u6ED1\u6642\u96B1\u85CF Header","app.setting.hideheader.hint":"\u56FA\u5B9A Header \u6642\u53EF\u914D\u7F6E","app.setting.othersettings":"\u5176\u4ED6\u8A2D\u7F6E","app.setting.weakmode":"\u8272\u5F31\u6A21\u5F0F","app.setting.copy":"\u62F7\u8C9D\u8A2D\u7F6E","app.setting.loading":"\u6B63\u5728\u52A0\u8F09\u4E3B\u984C","app.setting.copyinfo":"\u62F7\u8C9D\u6210\u529F\uFF0C\u8ACB\u5230 src/defaultSettings.js \u4E2D\u66FF\u63DB\u9ED8\u8A8D\u914D\u7F6E","app.setting.production.hint":"\u914D\u7F6E\u6B04\u53EA\u5728\u958B\u767C\u74B0\u5883\u7528\u65BC\u9810\u89BD\uFF0C\u751F\u7522\u74B0\u5883\u4E0D\u6703\u5C55\u73FE\uFF0C\u8ACB\u62F7\u8C9D\u5F8C\u624B\u52D5\u4FEE\u6539\u914D\u7F6E\u6587\u4EF6"},eo=(0,s.Z)({},qr),In={"zh-CN":Jr,"zh-TW":eo,"en-US":kr,"it-IT":Vr,"ko-KR":Yr},to=function(){if(!(0,F.j)())return"zh-CN";var e=window.localStorage.getItem("umi_locale");return e||window.g_locale||navigator.language},no=function(){var e=to();return In[e]||In["zh-CN"]},Qe=d(67159),qe=d(34155),ao=function(){var e;return typeof qe=="undefined"?Qe.Z:((e=qe)===null||qe===void 0||(qe={NODE_ENV:"production",PUBLIC_PATH:"/"})===null||qe===void 0?void 0:qe.ANTD_VERSION)||Qe.Z},ro=function(e){var t,n,r,i,o,c,f,v,m,I,x,Z,N,D,w,k,K,E,T,_,C,L,te,z,O,ie,Ce,S,Se,B,re,he;return(t=ao())!==null&&t!==void 0&&t.startsWith("5")?{}:(0,u.Z)((0,u.Z)((0,u.Z)({},e.componentCls,(0,u.Z)((0,u.Z)({width:"100%",height:"100%"},"".concat(e.proComponentsCls,"-base-menu"),(C={color:(n=e.layout)===null||n===void 0||(n=n.sider)===null||n===void 0?void 0:n.colorTextMenu},(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)(C,"".concat(e.antCls,"-menu-sub"),{backgroundColor:"transparent!important",color:(r=e.layout)===null||r===void 0||(r=r.sider)===null||r===void 0?void 0:r.colorTextMenu}),"& ".concat(e.antCls,"-layout"),{backgroundColor:"transparent",width:"100%"}),"".concat(e.antCls,"-menu-submenu-expand-icon, ").concat(e.antCls,"-menu-submenu-arrow"),{color:"inherit"}),"&".concat(e.antCls,"-menu"),(0,u.Z)((0,u.Z)({color:(i=e.layout)===null||i===void 0||(i=i.sider)===null||i===void 0?void 0:i.colorTextMenu},"".concat(e.antCls,"-menu-item"),{"*":{transition:"none !important"}}),"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-inline"),(0,u.Z)({},"".concat(e.antCls,"-menu-selected::after,").concat(e.antCls,"-menu-item-selected::after"),{display:"none"})),"".concat(e.antCls,"-menu-sub ").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,`-menu-item:active, 
        `).concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"}),"&".concat(e.antCls,"-menu-light"),(0,u.Z)({},"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,`-menu-submenu-active, 
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,u.Z)({color:(o=e.layout)===null||o===void 0||(o=o.sider)===null||o===void 0?void 0:o.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(c=e.layout)===null||c===void 0||(c=c.sider)===null||c===void 0?void 0:c.colorTextMenuActive}))),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,u.Z)((0,u.Z)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:(f=e.layout)===null||f===void 0||(f=f.sider)===null||f===void 0?void 0:f.colorBgMenuItemSelected,borderRadius:e.borderRadius}),"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,u.Z)({color:(v=e.layout)===null||v===void 0||(v=v.sider)===null||v===void 0?void 0:v.colorTextMenuActive,borderRadius:e.borderRadius,backgroundColor:"".concat((m=e.layout)===null||m===void 0||(m=m.header)===null||m===void 0?void 0:m.colorBgMenuItemHover," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(I=e.layout)===null||I===void 0||(I=I.sider)===null||I===void 0?void 0:I.colorTextMenuActive}))),"".concat(e.antCls,"-menu-item-selected"),{color:(x=e.layout)===null||x===void 0||(x=x.sider)===null||x===void 0?void 0:x.colorTextMenuSelected}),(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)(C,"".concat(e.antCls,"-menu-submenu-selected"),{color:(Z=e.layout)===null||Z===void 0||(Z=Z.sider)===null||Z===void 0?void 0:Z.colorTextMenuSelected}),"&".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-inline) ").concat(e.antCls,"-menu-submenu-open"),{color:(N=e.layout)===null||N===void 0||(N=N.sider)===null||N===void 0?void 0:N.colorTextMenuSelected}),"&".concat(e.antCls,"-menu-vertical"),(0,u.Z)({},"".concat(e.antCls,"-menu-submenu-selected"),{borderRadius:e.borderRadius,color:(D=e.layout)===null||D===void 0||(D=D.sider)===null||D===void 0?void 0:D.colorTextMenuSelected})),"".concat(e.antCls,"-menu-submenu:hover > ").concat(e.antCls,"-menu-submenu-title > ").concat(e.antCls,"-menu-submenu-arrow"),{color:(w=e.layout)===null||w===void 0||(w=w.sider)===null||w===void 0?void 0:w.colorTextMenuActive}),"&".concat(e.antCls,"-menu-horizontal"),(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({},"".concat(e.antCls,`-menu-item:hover,
          `).concat(e.antCls,`-menu-submenu:hover,
          `).concat(e.antCls,`-menu-item-active,
          `).concat(e.antCls,"-menu-submenu-active"),{borderRadius:4,transition:"none",color:(k=e.layout)===null||k===void 0||(k=k.header)===null||k===void 0?void 0:k.colorTextMenuActive,backgroundColor:"".concat((K=e.layout)===null||K===void 0||(K=K.header)===null||K===void 0?void 0:K.colorBgMenuItemHover," !important")}),"".concat(e.antCls,`-menu-item-open,
          `).concat(e.antCls,`-menu-submenu-open,
          `).concat(e.antCls,`-menu-item-selected,
          `).concat(e.antCls,"-menu-submenu-selected"),(0,u.Z)({backgroundColor:(E=e.layout)===null||E===void 0||(E=E.header)===null||E===void 0?void 0:E.colorBgMenuItemSelected,borderRadius:e.borderRadius,transition:"none",color:"".concat((T=e.layout)===null||T===void 0||(T=T.header)===null||T===void 0?void 0:T.colorTextMenuSelected," !important")},"".concat(e.antCls,"-menu-submenu-arrow"),{color:"".concat((_=e.layout)===null||_===void 0||(_=_.header)===null||_===void 0?void 0:_.colorTextMenuSelected," !important")})),"> ".concat(e.antCls,"-menu-item, > ").concat(e.antCls,"-menu-submenu"),{paddingInline:16,marginInline:4}),"> ".concat(e.antCls,"-menu-item::after, > ").concat(e.antCls,"-menu-submenu::after"),{display:"none"})))),"".concat(e.proComponentsCls,"-top-nav-header-base-menu"),(0,u.Z)((0,u.Z)({},"&".concat(e.antCls,"-menu"),(0,u.Z)({color:(L=e.layout)===null||L===void 0||(L=L.header)===null||L===void 0?void 0:L.colorTextMenu},"".concat(e.antCls,"-menu-item a"),{color:"inherit"})),"&".concat(e.antCls,"-menu-light"),(0,u.Z)((0,u.Z)({},"".concat(e.antCls,`-menu-item:hover, 
            `).concat(e.antCls,`-menu-item-active,
            `).concat(e.antCls,`-menu-submenu-active, 
            `).concat(e.antCls,"-menu-submenu-title:hover"),(0,u.Z)({color:(te=e.layout)===null||te===void 0||(te=te.header)===null||te===void 0?void 0:te.colorTextMenuActive,borderRadius:e.borderRadius,transition:"none",backgroundColor:(z=e.layout)===null||z===void 0||(z=z.header)===null||z===void 0?void 0:z.colorBgMenuItemSelected},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(O=e.layout)===null||O===void 0||(O=O.header)===null||O===void 0?void 0:O.colorTextMenuActive})),"".concat(e.antCls,"-menu-item-selected"),{color:(ie=e.layout)===null||ie===void 0||(ie=ie.header)===null||ie===void 0?void 0:ie.colorTextMenuSelected,borderRadius:e.borderRadius,backgroundColor:(Ce=e.layout)===null||Ce===void 0||(Ce=Ce.header)===null||Ce===void 0?void 0:Ce.colorBgMenuItemSelected})))),"".concat(e.antCls,"-menu-sub").concat(e.antCls,"-menu-inline"),{backgroundColor:"transparent!important"}),"".concat(e.antCls,"-menu-submenu-popup"),(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({backgroundColor:"rgba(255, 255, 255, 0.42)","-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)"},"".concat(e.antCls,"-menu"),(0,u.Z)({background:"transparent !important",backgroundColor:"transparent !important"},"".concat(e.antCls,`-menu-item:active, 
        `).concat(e.antCls,"-menu-submenu-title:active"),{backgroundColor:"transparent!important"})),"".concat(e.antCls,"-menu-item-selected"),{color:(S=e.layout)===null||S===void 0||(S=S.sider)===null||S===void 0?void 0:S.colorTextMenuSelected}),"".concat(e.antCls,"-menu-submenu-selected"),{color:(Se=e.layout)===null||Se===void 0||(Se=Se.sider)===null||Se===void 0?void 0:Se.colorTextMenuSelected}),"".concat(e.antCls,"-menu:not(").concat(e.antCls,"-menu-horizontal)"),(0,u.Z)((0,u.Z)({},"".concat(e.antCls,"-menu-item-selected"),{backgroundColor:"rgba(0, 0, 0, 0.04)",borderRadius:e.borderRadius,color:(B=e.layout)===null||B===void 0||(B=B.sider)===null||B===void 0?void 0:B.colorTextMenuSelected}),"".concat(e.antCls,`-menu-item:hover, 
          `).concat(e.antCls,`-menu-item-active,
          `).concat(e.antCls,"-menu-submenu-title:hover"),(0,u.Z)({color:(re=e.layout)===null||re===void 0||(re=re.sider)===null||re===void 0?void 0:re.colorTextMenuActive,borderRadius:e.borderRadius},"".concat(e.antCls,"-menu-submenu-arrow"),{color:(he=e.layout)===null||he===void 0||(he=he.sider)===null||he===void 0?void 0:he.colorTextMenuActive}))))},oo=function(e){var t,n,r,i;return(0,u.Z)((0,u.Z)({},"".concat(e.antCls,"-layout"),{backgroundColor:"transparent !important"}),e.componentCls,(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({},"& ".concat(e.antCls,"-layout"),{display:"flex",backgroundColor:"transparent",width:"100%"}),"".concat(e.componentCls,"-content"),{display:"flex",flexDirection:"column",width:"100%",backgroundColor:((t=e.layout)===null||t===void 0||(t=t.pageContainer)===null||t===void 0?void 0:t.colorBgPageContainer)||"transparent",position:"relative",paddingBlock:(n=e.layout)===null||n===void 0||(n=n.pageContainer)===null||n===void 0?void 0:n.paddingBlockPageContainerContent,paddingInline:(r=e.layout)===null||r===void 0||(r=r.pageContainer)===null||r===void 0?void 0:r.paddingInlinePageContainerContent,"&-has-page-container":{padding:0}}),"".concat(e.componentCls,"-container"),{width:"100%",display:"flex",flexDirection:"column",minWidth:0,minHeight:0,backgroundColor:"transparent"}),"".concat(e.componentCls,"-bg-list"),{pointerEvents:"none",position:"fixed",overflow:"hidden",insetBlockStart:0,insetInlineStart:0,zIndex:0,height:"100%",width:"100%",background:(i=e.layout)===null||i===void 0?void 0:i.bgLayout}))};function io(a){return(0,Le.Xj)("ProLayout",function(e){var t=(0,s.Z)((0,s.Z)({},e),{},{componentCls:".".concat(a)});return[oo(t),ro(t)]})}function lo(a){if(!a||a==="/")return["/"];var e=a.split("/").filter(function(t){return t});return e.map(function(t,n){return"/".concat(e.slice(0,n+1).join("/"))})}var et=d(34155),uo=function(){var e;return typeof et=="undefined"?Qe.Z:((e=et)===null||et===void 0||(et={NODE_ENV:"production",PUBLIC_PATH:"/"})===null||et===void 0?void 0:et.ANTD_VERSION)||Qe.Z},so=function(e,t,n){var r=e,i=r.breadcrumbName,o=r.title,c=r.path,f=n.findIndex(function(v){return v.linkPath===e.path})===n.length-1;return f?(0,l.jsx)("span",{children:o||i}):(0,l.jsx)("span",{onClick:c?function(){return location.href=c}:void 0,children:o||i})},co=function(e,t){var n=t.formatMessage,r=t.menu;return e.locale&&n&&(r==null?void 0:r.locale)!==!1?n({id:e.locale,defaultMessage:e.name}):e.name},fo=function(e,t){var n=e.get(t);if(!n){var r=Array.from(e.keys())||[],i=r.find(function(o){try{return o!=null&&o.startsWith("http")?!1:(0,Ft.match)(o.replace("?",""))(t)}catch(c){return console.log("path",o,c),!1}});i&&(n=e.get(i))}return n||{path:""}},vo=function(e){var t=e.location,n=e.breadcrumbMap;return{location:t,breadcrumbMap:n}},mo=function(e,t,n){var r=lo(e==null?void 0:e.pathname),i=r.map(function(o){var c=fo(t,o),f=co(c,n),v=c.hideInBreadcrumb;return f&&!v?{linkPath:o,breadcrumbName:f,title:f,component:c.component}:{linkPath:"",breadcrumbName:"",title:""}}).filter(function(o){return o&&o.linkPath});return i},ho=function(e){var t=vo(e),n=t.location,r=t.breadcrumbMap;return n&&n.pathname&&r?mo(n,r,e):[]},po=function(e,t){var n=e.breadcrumbRender,r=e.itemRender,i=t.breadcrumbProps||{},o=i.minLength,c=o===void 0?2:o,f=ho(e),v=function(x){for(var Z=r||so,N=arguments.length,D=new Array(N>1?N-1:0),w=1;w<N;w++)D[w-1]=arguments[w];return Z==null?void 0:Z.apply(void 0,[(0,s.Z)((0,s.Z)({},x),{},{path:x.linkPath||x.path})].concat(D))},m=f;return n&&(m=n(m||[])||void 0),(m&&m.length<c||n===!1)&&(m=void 0),(0,p.n)(uo(),"5.3.0")>-1?{items:m,itemRender:v}:{routes:m,itemRender:v}};function go(a){return(0,bt.Z)(a).reduce(function(e,t){var n=(0,b.Z)(t,2),r=n[0],i=n[1];return e[r]=i,e},{})}var yo=function a(e,t,n,r){var i=Ma(e,(t==null?void 0:t.locale)||!1,n,!0),o=i.menuData,c=i.breadcrumb;return r?a(r(o),t,n,void 0):{breadcrumb:go(c),breadcrumbMap:c,menuData:o}},xo=d(71002),Co=d(51812),bo=function(e){var t=(0,g.useState)({}),n=(0,b.Z)(t,2),r=n[0],i=n[1];return(0,g.useEffect)(function(){i((0,Co.Y)({layout:(0,xo.Z)(e.layout)!=="object"?e.layout:void 0,navTheme:e.navTheme,menuRender:e.menuRender,footerRender:e.footerRender,menuHeaderRender:e.menuHeaderRender,headerRender:e.headerRender,fixSiderbar:e.fixSiderbar}))},[e.layout,e.navTheme,e.menuRender,e.footerRender,e.menuHeaderRender,e.headerRender,e.fixSiderbar]),r},So=["id","defaultMessage"],Mo=["fixSiderbar","navTheme","layout"],Tn=0,Zo=function(e,t){var n;return e.headerRender===!1||e.pure?null:(0,l.jsx)(Hr,(0,s.Z)((0,s.Z)({matchMenuKeys:t},e),{},{stylish:(n=e.stylish)===null||n===void 0?void 0:n.header}))},Io=function(e){return e.footerRender===!1||e.pure?null:e.footerRender?e.footerRender((0,s.Z)({},e),(0,l.jsx)(Na.q,{})):null},To=function(e,t){var n,r=e.layout,i=e.isMobile,o=e.selectedKeys,c=e.openKeys,f=e.splitMenus,v=e.suppressSiderWhenMenuEmpty,m=e.menuRender;if(e.menuRender===!1||e.pure)return null;var I=e.menuData;if(f&&(c!==!1||r==="mix")&&!i){var x=o||t,Z=(0,b.Z)(x,1),N=Z[0];if(N){var D;I=((D=e.menuData)===null||D===void 0||(D=D.find(function(E){return E.key===N}))===null||D===void 0?void 0:D.children)||[]}else I=[]}var w=xt(I||[]);if(w&&(w==null?void 0:w.length)<1&&(f||v))return null;if(r==="top"&&!i){var k;return(0,l.jsx)(Mn,(0,s.Z)((0,s.Z)({matchMenuKeys:t},e),{},{hide:!0,stylish:(k=e.stylish)===null||k===void 0?void 0:k.sider}))}var K=(0,l.jsx)(Mn,(0,s.Z)((0,s.Z)({matchMenuKeys:t},e),{},{menuData:w,stylish:(n=e.stylish)===null||n===void 0?void 0:n.sider}));return m?m(e,K):K},Ro=function(e,t){var n=t.pageTitleRender,r=Wt(e);if(n===!1)return{title:t.title||"",id:"",pageName:""};if(n){var i=n(e,r.title,r);if(typeof i=="string")return Wt((0,s.Z)((0,s.Z)({},r),{},{title:i}));(0,Ba.ZP)(typeof i=="string","pro-layout: renderPageTitle return value should be a string")}return r},jo=function(e,t,n){return e?t?64:n:0},Po=function(e){var t,n,r,i,o,c,f,v,m,I,x,Z,N,D,w=e||{},k=w.children,K=w.onCollapse,E=w.location,T=E===void 0?{pathname:"/"}:E,_=w.contentStyle,C=w.route,L=w.defaultCollapsed,te=w.style,z=w.siderWidth,O=w.menu,ie=w.siderMenuType,Ce=w.isChildrenLayout,S=w.menuDataRender,Se=w.actionRef,B=w.bgLayoutImgList,re=w.formatMessage,he=w.loading,Ie=(0,g.useMemo)(function(){return z||(e.layout==="mix"?215:256)},[e.layout,z]),ge=(0,g.useContext)(We.ZP.ConfigContext),Pe=(t=e.prefixCls)!==null&&t!==void 0?t:ge.getPrefixCls("pro"),Te=(0,V.Z)(!1,{value:O==null?void 0:O.loading,onChange:O==null?void 0:O.onLoadingChange}),Re=(0,b.Z)(Te,2),$e=Re[0],Ae=Re[1],Oe=(0,g.useState)(function(){return Tn+=1,"pro-layout-".concat(Tn)}),ze=(0,b.Z)(Oe,1),ke=ze[0],Ge=(0,g.useCallback)(function(Be){var Xe=Be.id,jt=Be.defaultMessage,pt=(0,oe.Z)(Be,So);if(re)return re((0,s.Z)({id:Xe,defaultMessage:jt},pt));var gt=no();return gt[Xe]?gt[Xe]:jt},[re]),Ve=(0,rn.ZP)([ke,O==null?void 0:O.params],function(){var Be=(0,de.Z)((0,Me.Z)().mark(function Xe(jt){var pt,gt,zn,Kn;return(0,Me.Z)().wrap(function(rt){for(;;)switch(rt.prev=rt.next){case 0:return gt=(0,b.Z)(jt,2),zn=gt[1],Ae(!0),rt.next=4,O==null||(pt=O.request)===null||pt===void 0?void 0:pt.call(O,zn||{},(C==null?void 0:C.children)||(C==null?void 0:C.routes)||[]);case 4:return Kn=rt.sent,Ae(!1),rt.abrupt("return",Kn);case 7:case"end":return rt.stop()}},Xe)}));return function(Xe){return Be.apply(this,arguments)}}(),{revalidateOnFocus:!1,shouldRetryOnError:!1,revalidateOnReconnect:!1}),dt=Ve.data,Zt=Ve.mutate,we=Ve.isLoading;(0,g.useEffect)(function(){Ae(we)},[we]);var Fe=(0,rn.kY)(),_e=Fe.cache;(0,g.useEffect)(function(){return function(){_e instanceof Map&&_e.delete(ke)}},[]);var It=(0,g.useMemo)(function(){return yo(dt||(C==null?void 0:C.children)||(C==null?void 0:C.routes)||[],O,Ge,S)},[Ge,O,S,dt,C==null?void 0:C.children,C==null?void 0:C.routes]),zt=It||{},Uo=zt.breadcrumb,Pn=zt.breadcrumbMap,wn=zt.menuData,ft=wn===void 0?[]:wn;Se&&O!==null&&O!==void 0&&O.request&&(Se.current={reload:function(){Zt()}});var vt=(0,g.useMemo)(function(){return Pa(T.pathname||"/",ft||[],!0)},[T.pathname,ft]),Kt=(0,g.useMemo)(function(){return Array.from(new Set(vt.map(function(Be){return Be.key||Be.path||""})))},[vt]),Bn=vt[vt.length-1]||{},En=bo(Bn),Tt=(0,s.Z)((0,s.Z)({},e),En),ko=Tt.fixSiderbar,fi=Tt.navTheme,mt=Tt.layout,Go=(0,oe.Z)(Tt,Mo),tt=$(),nt=(0,g.useMemo)(function(){return(tt==="sm"||tt==="xs")&&!e.disableMobile},[tt,e.disableMobile]),Vo=mt!=="top"&&!nt,Xo=(0,V.Z)(function(){return L!==void 0?L:!!(nt||tt==="md")},{value:e.collapsed,onChange:K}),Ln=(0,b.Z)(Xo,2),ht=Ln[0],An=Ln[1],at=(0,an.Z)((0,s.Z)((0,s.Z)((0,s.Z)({prefixCls:Pe},e),{},{siderWidth:Ie},En),{},{formatMessage:Ge,breadcrumb:Uo,menu:(0,s.Z)((0,s.Z)({},O),{},{type:ie||(O==null?void 0:O.type),loading:$e}),layout:mt}),["className","style","breadcrumbRender"]),Ut=Ro((0,s.Z)((0,s.Z)({pathname:T.pathname},at),{},{breadcrumbMap:Pn}),e),Yo=po((0,s.Z)((0,s.Z)({},at),{},{breadcrumbRender:e.breadcrumbRender,breadcrumbMap:Pn}),e),Rt=To((0,s.Z)((0,s.Z)({},at),{},{menuData:ft,onCollapse:An,isMobile:nt,collapsed:ht}),Kt),kt=Zo((0,s.Z)((0,s.Z)({},at),{},{children:null,hasSiderMenu:!!Rt,menuData:ft,isMobile:nt,collapsed:ht,onCollapse:An}),Kt),Nn=Io((0,s.Z)({isMobile:nt,collapsed:ht},at)),Qo=(0,g.useContext)(Zn.X),Jo=Qo.isChildrenLayout,Gt=Ce!==void 0?Ce:Jo,Ke="".concat(Pe,"-layout"),Dn=io(Ke),qo=Dn.wrapSSR,Vt=Dn.hashId,ei=fe()(e.className,Vt,"ant-design-pro",Ke,(0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)((0,u.Z)({},"screen-".concat(tt),tt),"".concat(Ke,"-top-menu"),mt==="top"),"".concat(Ke,"-is-children"),Gt),"".concat(Ke,"-fix-siderbar"),ko),"".concat(Ke,"-").concat(mt),mt)),ti=jo(!!Vo,ht,Ie),On={position:"relative"};(Gt||_&&_.minHeight)&&(On.minHeight=0),(0,g.useEffect)(function(){var Be;(Be=e.onPageChange)===null||Be===void 0||Be.call(e,e.location)},[T.pathname,(n=T.pathname)===null||n===void 0?void 0:n.search]);var ni=(0,g.useState)(!1),Hn=(0,b.Z)(ni,2),$n=Hn[0],ai=Hn[1],ri=(0,g.useState)(0),_n=(0,b.Z)(ri,2),Fn=_n[0],oi=_n[1];y(Ut,e.title||!1);var ii=(0,g.useContext)(M.L_),ne=ii.token,Wn=(0,g.useMemo)(function(){return B&&B.length>0?B==null?void 0:B.map(function(Be,Xe){return(0,l.jsx)("img",{src:Be.src,style:(0,s.Z)({position:"absolute"},Be)},Xe)}):null},[B]);return qo((0,l.jsx)(Zn.X.Provider,{value:(0,s.Z)((0,s.Z)({},at),{},{breadcrumb:Yo,menuData:ft,isMobile:nt,collapsed:ht,hasPageContainer:Fn,setHasPageContainer:oi,isChildrenLayout:!0,title:Ut.pageName,hasSiderMenu:!!Rt,hasHeader:!!kt,siderWidth:ti,hasFooter:!!Nn,hasFooterToolbar:$n,setHasFooterToolbar:ai,pageTitleInfo:Ut,matchMenus:vt,matchMenuKeys:Kt,currentMenu:Bn}),children:e.pure?(0,l.jsx)(l.Fragment,{children:k}):(0,l.jsxs)("div",{className:ei,children:[Wn||(r=ne.layout)!==null&&r!==void 0&&r.bgLayout?(0,l.jsx)("div",{className:fe()("".concat(Ke,"-bg-list"),Vt),children:Wn}):null,(0,l.jsxs)(Je.Z,{style:(0,s.Z)({minHeight:"100%",flexDirection:Rt?"row":void 0},te),children:[(0,l.jsx)(We.ZP,{theme:{hashed:(0,M.nu)(),token:{controlHeightLG:((i=ne.layout)===null||i===void 0||(i=i.sider)===null||i===void 0?void 0:i.menuHeight)||(ne==null?void 0:ne.controlHeightLG)},components:{Menu:P({colorItemBg:((o=ne.layout)===null||o===void 0||(o=o.sider)===null||o===void 0?void 0:o.colorMenuBackground)||"transparent",colorSubItemBg:((c=ne.layout)===null||c===void 0||(c=c.sider)===null||c===void 0?void 0:c.colorMenuBackground)||"transparent",radiusItem:ne.borderRadius,colorItemBgSelected:((f=ne.layout)===null||f===void 0||(f=f.sider)===null||f===void 0?void 0:f.colorBgMenuItemSelected)||(ne==null?void 0:ne.colorBgTextHover),colorItemBgHover:((v=ne.layout)===null||v===void 0||(v=v.sider)===null||v===void 0?void 0:v.colorBgMenuItemHover)||(ne==null?void 0:ne.colorBgTextHover),colorItemBgActive:((m=ne.layout)===null||m===void 0||(m=m.sider)===null||m===void 0?void 0:m.colorBgMenuItemActive)||(ne==null?void 0:ne.colorBgTextActive),colorItemBgSelectedHorizontal:((I=ne.layout)===null||I===void 0||(I=I.sider)===null||I===void 0?void 0:I.colorBgMenuItemSelected)||(ne==null?void 0:ne.colorBgTextHover),colorActiveBarWidth:0,colorActiveBarHeight:0,colorActiveBarBorderSize:0,colorItemText:((x=ne.layout)===null||x===void 0||(x=x.sider)===null||x===void 0?void 0:x.colorTextMenu)||(ne==null?void 0:ne.colorTextSecondary),colorItemTextHover:((Z=ne.layout)===null||Z===void 0||(Z=Z.sider)===null||Z===void 0?void 0:Z.colorTextMenuItemHover)||"rgba(0, 0, 0, 0.85)",colorItemTextSelected:((N=ne.layout)===null||N===void 0||(N=N.sider)===null||N===void 0?void 0:N.colorTextMenuSelected)||"rgba(0, 0, 0, 1)",popupBg:ne==null?void 0:ne.colorBgElevated,subMenuItemBg:ne==null?void 0:ne.colorBgElevated,darkSubMenuItemBg:"transparent",darkPopupBg:ne==null?void 0:ne.colorBgElevated})}},children:Rt}),(0,l.jsxs)("div",{style:On,className:"".concat(Ke,"-container ").concat(Vt).trim(),children:[kt,(0,l.jsx)(La,(0,s.Z)((0,s.Z)({hasPageContainer:Fn,isChildrenLayout:Gt},Go),{},{hasHeader:!!kt,prefixCls:Ke,style:_,children:he?(0,l.jsx)($r.S,{}):k})),Nn,$n&&(0,l.jsx)("div",{className:"".concat(Ke,"-has-footer"),style:{height:64,marginBlockStart:(D=ne.layout)===null||D===void 0||(D=D.pageContainer)===null||D===void 0?void 0:D.paddingBlockPageContainerContent}})]})]})]})}))},wo=function(e){var t=e.colorPrimary,n=e.navTheme!==void 0?{dark:e.navTheme==="realDark"}:{};return(0,l.jsx)(We.ZP,{theme:t?{token:{colorPrimary:t}}:void 0,children:(0,l.jsx)(M._Y,(0,s.Z)((0,s.Z)({},n),{},{token:e.token,prefixCls:e.prefixCls,children:(0,l.jsx)(Po,(0,s.Z)((0,s.Z)({logo:(0,l.jsx)(Aa,{})},dn),{},{location:(0,F.j)()?window.location:void 0},e))}))})},Bo=function(){return(0,l.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 200 200",children:[(0,l.jsxs)("defs",{children:[(0,l.jsxs)("linearGradient",{id:"linearGradient-1",x1:"62.102%",x2:"108.197%",y1:"0%",y2:"37.864%",children:[(0,l.jsx)("stop",{offset:"0%",stopColor:"#4285EB"}),(0,l.jsx)("stop",{offset:"100%",stopColor:"#2EC7FF"})]}),(0,l.jsxs)("linearGradient",{id:"linearGradient-2",x1:"69.644%",x2:"54.043%",y1:"0%",y2:"108.457%",children:[(0,l.jsx)("stop",{offset:"0%",stopColor:"#29CDFF"}),(0,l.jsx)("stop",{offset:"37.86%",stopColor:"#148EFF"}),(0,l.jsx)("stop",{offset:"100%",stopColor:"#0A60FF"})]}),(0,l.jsxs)("linearGradient",{id:"linearGradient-3",x1:"69.691%",x2:"16.723%",y1:"-12.974%",y2:"117.391%",children:[(0,l.jsx)("stop",{offset:"0%",stopColor:"#FA816E"}),(0,l.jsx)("stop",{offset:"41.473%",stopColor:"#F74A5C"}),(0,l.jsx)("stop",{offset:"100%",stopColor:"#F51D2C"})]}),(0,l.jsxs)("linearGradient",{id:"linearGradient-4",x1:"68.128%",x2:"30.44%",y1:"-35.691%",y2:"114.943%",children:[(0,l.jsx)("stop",{offset:"0%",stopColor:"#FA8E7D"}),(0,l.jsx)("stop",{offset:"51.264%",stopColor:"#F74A5C"}),(0,l.jsx)("stop",{offset:"100%",stopColor:"#F51D2C"})]})]}),(0,l.jsx)("g",{fill:"none",fillRule:"evenodd",stroke:"none",strokeWidth:"1",children:(0,l.jsx)("g",{transform:"translate(-20 -20)",children:(0,l.jsx)("g",{transform:"translate(20 20)",children:(0,l.jsxs)("g",{children:[(0,l.jsxs)("g",{fillRule:"nonzero",children:[(0,l.jsxs)("g",{children:[(0,l.jsx)("path",{fill:"url(#linearGradient-1)",d:"M91.588 4.177L4.18 91.513a11.981 11.981 0 000 16.974l87.408 87.336a12.005 12.005 0 0016.989 0l36.648-36.618c4.209-4.205 4.209-11.023 0-15.228-4.208-4.205-11.031-4.205-15.24 0l-27.783 27.76c-1.17 1.169-2.945 1.169-4.114 0l-69.802-69.744c-1.17-1.169-1.17-2.942 0-4.11l69.802-69.745c1.17-1.169 2.944-1.169 4.114 0l27.783 27.76c4.209 4.205 11.032 4.205 15.24 0 4.209-4.205 4.209-11.022 0-15.227L108.581 4.056c-4.719-4.594-12.312-4.557-16.993.12z"}),(0,l.jsx)("path",{fill:"url(#linearGradient-2)",d:"M91.588 4.177L4.18 91.513a11.981 11.981 0 000 16.974l87.408 87.336a12.005 12.005 0 0016.989 0l36.648-36.618c4.209-4.205 4.209-11.023 0-15.228-4.208-4.205-11.031-4.205-15.24 0l-27.783 27.76c-1.17 1.169-2.945 1.169-4.114 0l-69.802-69.744c-1.17-1.169-1.17-2.942 0-4.11l69.802-69.745c2.912-2.51 7.664-7.596 14.642-8.786 5.186-.883 10.855 1.062 17.009 5.837L108.58 4.056c-4.719-4.594-12.312-4.557-16.993.12z"})]}),(0,l.jsx)("path",{fill:"url(#linearGradient-3)",d:"M153.686 135.855c4.208 4.205 11.031 4.205 15.24 0l27.034-27.012c4.7-4.696 4.7-12.28 0-16.974l-27.27-27.15c-4.218-4.2-11.043-4.195-15.254.013-4.209 4.205-4.209 11.022 0 15.227l18.418 18.403c1.17 1.169 1.17 2.943 0 4.111l-18.168 18.154c-4.209 4.205-4.209 11.023 0 15.228z"})]}),(0,l.jsx)("ellipse",{cx:"100.519",cy:"100.437",fill:"url(#linearGradient-4)",rx:"23.6",ry:"23.581"})]})})})})]})},Eo=Bo,Lo=d(29905),Ao=d(28036),No=function(e){var t,n;return!e.route&&(e.noFound||e.notFound)||((t=e.route)===null||t===void 0?void 0:t.unaccessible)&&(e.unAccessible||e.noAccessible)||(!e.route||((n=e.route)===null||n===void 0?void 0:n.unaccessible))&&(0,l.jsx)(Lo.ZP,{status:e.route?"403":"404",title:e.route?"403":"404",subTitle:e.route?"\u62B1\u6B49\uFF0C\u4F60\u65E0\u6743\u8BBF\u95EE\u8BE5\u9875\u9762":"\u62B1\u6B49\uFF0C\u4F60\u8BBF\u95EE\u7684\u9875\u9762\u4E0D\u5B58\u5728",extra:(0,l.jsx)(Ao.ZP,{type:"primary",onClick:function(){return J.history.push("/")},children:"\u8FD4\u56DE\u9996\u9875"})})||e.children},Do=No,Oo=d(74330),Ho=d(85418),$o=d(92443),Rn=d(66999);function _o(a){var e,t,n,r,i;if(a.runtimeConfig.rightRender)return a.runtimeConfig.rightRender(a.initialState,a.setInitialState,a.runtimeConfig);var o=((e=a.initialState)===null||e===void 0?void 0:e.avatar)||((t=a.initialState)===null||t===void 0?void 0:t.name)||a.runtimeConfig.logout,c=((n=a.initialState)===null||n===void 0?void 0:n.avatar)===!1,f=c?"umi-plugin-layout-name umi-plugin-layout-hide-avatar-img":"umi-plugin-layout-name",v=o?(0,l.jsxs)("span",{className:"umi-plugin-layout-action",children:[c?null:(0,l.jsx)($t.C,{size:"small",className:"umi-plugin-layout-avatar",src:((r=a.initialState)===null||r===void 0?void 0:r.avatar)||"https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png",alt:"avatar"}),(0,l.jsx)("span",{className:f,children:(i=a.initialState)===null||i===void 0?void 0:i.name})]}):null;if(a.loading)return(0,l.jsx)("div",{className:"umi-plugin-layout-right",children:(0,l.jsx)(Oo.Z,{size:"small",style:{marginLeft:8,marginRight:8}})});var m={className:"umi-plugin-layout-menu",selectedKeys:[],items:[{key:"logout",label:(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)($o.Z,{}),"\u9000\u51FA\u767B\u5F55"]}),onClick:function(){var Z,N;a==null||(Z=a.runtimeConfig)===null||Z===void 0||(N=Z.logout)===null||N===void 0||N.call(Z,a.initialState)}}]},I;return Qe.Z.startsWith("5.")||Qe.Z.startsWith("4.24.")?I={menu:m}:Qe.Z.startsWith("3.")?I={overlay:(0,l.jsx)(ct.Z,{children:m.items.map(function(x){return(0,l.jsx)(ct.Z.Item,{onClick:x.onClick,children:x.label},x.key)})})}:I={overlay:(0,l.jsx)(ct.Z,ce()({},m))},(0,l.jsxs)("div",{className:"umi-plugin-layout-right anticon",children:[a.runtimeConfig.logout?(0,l.jsx)(Ho.Z,ce()(ce()({},I),{},{overlayClassName:"umi-plugin-layout-container",children:v})):v,(0,l.jsx)(Rn.pD,{})]})}var jn=d(44886),Fo=d(83228),Wo=function a(e,t){if(e.length===0)return[];var n=[],r=ve()(e),i;try{for(r.s();!(i=r.n()).done;){var o=i.value,c=ce()({},o);t(o)?Array.isArray(c.routes)&&n.push.apply(n,ae()(a(c.routes,t))):(Array.isArray(c.children)&&(c.children=a(c.children,t),c.routes=c.children),n.push(c))}}catch(f){r.e(f)}finally{r.f()}return n},zo=function a(e){return e.length===0?[]:e.map(function(t){var n=ce()({},t);return t.originPath&&(n.path=t.originPath),Array.isArray(t.routes)&&(n.routes=a(t.routes)),Array.isArray(t.children)&&(n.children=a(t.children)),n})},Ko=function(a){var e=(0,J.useLocation)(),t=(0,J.useNavigate)(),n=(0,J.useAppData)(),r=n.clientRoutes,i=n.pluginManager,o=jn.t&&(0,jn.t)("@@initialState")||{initialState:void 0,loading:!1,setInitialState:null},c=o.initialState,f=o.loading,v=o.setInitialState,m={locale:!0,navTheme:"light",colorPrimary:"#1677FF",layout:"side",contentWidth:"Fluid",fixedHeader:!1,fixSiderbar:!0,pwa:!0,logo:"/logo.png",splitMenus:!1,siderMenuType:"sub",colorWeak:!1,title:"Labwise",siderWidth:256,iconfontUrl:"",token:{}},I=(0,Rn.YB)(),x=I.formatMessage,Z=i.applyPlugins({key:"layout",type:"modify",initialValue:ce()({},o)}),N=Wo(r.filter(function(E){return E.id==="ant-design-pro-layout"}),function(E){return!!E.isLayout&&E.id!=="ant-design-pro-layout"||!!E.isWrapper}),D=(0,Fo.Mf)(zo(N)),w=q()(D,1),k=w[0],K=(0,g.useMemo)(function(){var E,T;return(E=(0,J.matchRoutes)(k.children,e.pathname))===null||E===void 0||(T=E.pop)===null||T===void 0||(T=T.call(E))===null||T===void 0?void 0:T.route},[e.pathname]);return(0,l.jsx)(wo,ce()(ce()({route:k,location:e,title:m.title||"labwise-web",navTheme:"dark",siderWidth:256,onMenuHeaderClick:function(T){T.stopPropagation(),T.preventDefault(),t("/")},formatMessage:m.formatMessage||x,menu:{locale:m.locale},logo:Eo,menuItemRender:function(T,_){return T.isUrl||T.children?_:T.path&&e.pathname!==T.path?(0,l.jsx)(J.Link,{to:T.path.replace("/*",""),target:T.target,children:_}):_},itemRender:function(T,_,C){var L=T.breadcrumbName,te=T.title,z=T.path,O=te||L,ie=C[C.length-1];return ie&&(ie.path===z||ie.linkPath===z)?(0,l.jsx)("span",{children:O}):(0,l.jsx)(J.Link,{to:z,children:O})},disableContentMargin:!0,fixSiderbar:!0,fixedHeader:!0},Z),{},{rightContentRender:Z.rightContentRender!==!1&&function(E){var T=_o({runtimeConfig:Z,loading:f,initialState:c,setInitialState:v});return Z.rightContentRender?Z.rightContentRender(E,T,{userConfig:m,runtimeConfig:Z,loading:f,initialState:c,setInitialState:v}):T},children:(0,l.jsx)(Do,{route:K,noFound:Z==null?void 0:Z.noFound,notFound:Z==null?void 0:Z.notFound,unAccessible:Z==null?void 0:Z.unAccessible,noAccessible:Z==null?void 0:Z.noAccessible,children:Z.childrenRender?Z.childrenRender((0,l.jsx)(J.Outlet,{}),a):(0,l.jsx)(J.Outlet,{})})}))}}}]);
}());
//# sourceMappingURL=t__plugin-layout__Layout.ee1fc96d.async.js.map