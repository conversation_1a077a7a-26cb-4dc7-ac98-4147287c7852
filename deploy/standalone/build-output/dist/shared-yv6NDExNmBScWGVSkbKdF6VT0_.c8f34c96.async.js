"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[460],{88284:function(ce,Q,r){var o=r(1413),L=r(67294),W=r(32857),V=r(84089),l=function(q,Y){return L.createElement(V.Z,(0,o.Z)((0,o.Z)({},q),{},{ref:Y,icon:W.Z}))},k=L.forwardRef(l);Q.Z=k},28508:function(ce,Q,r){var o=r(1413),L=r(67294),W=r(89503),V=r(84089),l=function(q,Y){return L.createElement(V.Z,(0,o.Z)((0,o.Z)({},q),{},{ref:Y,icon:W.Z}))},k=L.forwardRef(l);Q.Z=k},47389:function(ce,Q,r){var o=r(1413),L=r(67294),W=r(27363),V=r(84089),l=function(q,Y){return L.createElement(V.Z,(0,o.Z)((0,o.Z)({},q),{},{ref:Y,icon:W.Z}))},k=L.forwardRef(l);Q.Z=k},18221:function(ce,Q,r){r.d(Q,{vY:function(){return R}});var o=r(74902),L=r(74165),W=r(15861),V=r(45987),l=r(1413),k=r(28508),te=r(88284),q=r(47389),Y=r(952),ue=r(92755),fe=r(26704),ae=r(2026),he=r(90081),ve=r(1977),Ee=r(77398),re=r(86333),Ce=r(53914),me=r(97685),Se=r(10915),Ze=r(45360),xe=r(56790),ne=r(21770),U=r(67294),_=r(86671),le=function(n){return(Ze.ZP.warn||Ze.ZP.warning)(n)};function Me(i){var n=i.data,c=i.row;return(0,l.Z)((0,l.Z)({},n),c)}function Oe(i){var n=(0,U.useRef)(null),c=i.type||"single",g=(0,Se.YB)(),y=(0,ne.Z)([],{value:i.editableKeys,onChange:i.onChange?function(p){var C;i==null||(C=i.onChange)===null||C===void 0||C.call(i,p,i.dataSource)}:void 0}),N=(0,me.Z)(y,2),E=N[0],D=N[1],M=(0,U.useMemo)(function(){var p=c==="single"?E==null?void 0:E.slice(0,1):E;return new Set(p)},[(E||[]).join(","),c]),w=(0,U.useCallback)(function(p){return!!(E!=null&&E.includes((0,_.sN)(p)))},[(E||[]).join(",")]),f=function(C,O){var u;return M.size>0&&c==="single"?(le(i.onlyOneLineEditorAlertMessage||g.getMessage("editableTable.onlyOneLineEditor","\u53EA\u80FD\u540C\u65F6\u7F16\u8F91\u4E00\u884C")),!1):(n.current=(u=O!=null?O:(0,xe.U2)(i.dataSource,Array.isArray(C)?C:[C]))!==null&&u!==void 0?u:null,M.add((0,_.sN)(C)),D(Array.from(M)),!0)},v=function(C){return M.delete((0,_.sN)(C)),D(Array.from(M)),!0},j=function(){var p=(0,W.Z)((0,L.Z)().mark(function C(O,u,F,z){var A,b;return(0,L.Z)().wrap(function(S){for(;;)switch(S.prev=S.next){case 0:return S.next=2,i==null||(A=i.onCancel)===null||A===void 0?void 0:A.call(i,O,u,F,z);case 2:if(b=S.sent,b!==!1){S.next=5;break}return S.abrupt("return",!1);case 5:return S.abrupt("return",!0);case 6:case"end":return S.stop()}},C)}));return function(O,u,F,z){return p.apply(this,arguments)}}(),T=function(){var p=(0,W.Z)((0,L.Z)().mark(function C(O,u,F){var z,A,b;return(0,L.Z)().wrap(function(S){for(;;)switch(S.prev=S.next){case 0:return S.next=2,i==null||(z=i.onSave)===null||z===void 0?void 0:z.call(i,O,u,F);case 2:if(A=S.sent,A!==!1){S.next=5;break}return S.abrupt("return",!1);case 5:return S.next=7,v(O);case 7:return b={data:i.dataSource,row:u,key:O,childrenColumnName:i.childrenColumnName||"children"},i.setDataSource(Me(b)),S.abrupt("return",!0);case 10:case"end":return S.stop()}},C)}));return function(O,u,F){return p.apply(this,arguments)}}(),X=g.getMessage("editableTable.action.save","\u4FDD\u5B58"),$=g.getMessage("editableTable.action.delete","\u5220\u9664"),B=g.getMessage("editableTable.action.cancel","\u53D6\u6D88"),G=(0,U.useCallback)(function(p,C){var O=(0,l.Z)({recordKey:p,cancelEditable:v,onCancel:j,onSave:T,editableKeys:E,setEditableRowKeys:D,saveText:X,cancelText:B,preEditRowRef:n,deleteText:$,deletePopconfirmMessage:"".concat(g.getMessage("deleteThisLine","\u5220\u9664\u6B64\u9879"),"?"),editorType:"Map"},C),u=(0,_.aX)(i.dataSource,O);return i.actionRender?i.actionRender(i.dataSource,O,{save:u.save,delete:u.delete,cancel:u.cancel}):[u.save,u.delete,u.cancel]},[E&&E.join(","),i.dataSource]);return{editableKeys:E,setEditableRowKeys:D,isEditable:w,actionRender:G,startEditable:f,cancelEditable:v}}var H=r(78164),ie=r(67159),ge=r(42075),oe=r(26412),Pe=r(28459),$e=r(50344),Ie=r(88306),Te=function(n,c){var g=c||{},y=g.onRequestError,N=g.effects,E=g.manual,D=g.dataSource,M=g.defaultDataSource,w=g.onDataSourceChange,f=(0,ne.Z)(M,{value:D,onChange:w}),v=(0,me.Z)(f,2),j=v[0],T=v[1],X=(0,ne.Z)(c==null?void 0:c.loading,{value:c==null?void 0:c.loading,onChange:c==null?void 0:c.onLoadingChange}),$=(0,me.Z)(X,2),B=$[0],G=$[1],p=function(u){T(u),G(!1)},C=function(){var O=(0,W.Z)((0,L.Z)().mark(function u(){var F,z,A;return(0,L.Z)().wrap(function(m){for(;;)switch(m.prev=m.next){case 0:if(!B){m.next=2;break}return m.abrupt("return");case 2:return G(!0),m.prev=3,m.next=6,n();case 6:if(m.t0=m.sent,m.t0){m.next=9;break}m.t0={};case 9:F=m.t0,z=F.data,A=F.success,A!==!1&&p(z),m.next=23;break;case 15:if(m.prev=15,m.t1=m.catch(3),y!==void 0){m.next=21;break}throw new Error(m.t1);case 21:y(m.t1);case 22:G(!1);case 23:return m.prev=23,G(!1),m.finish(23);case 26:case"end":return m.stop()}},u,null,[[3,15,23,26]])}));return function(){return O.apply(this,arguments)}}();return(0,U.useEffect)(function(){E||C()},[].concat((0,o.Z)(N||[]),[E])),{dataSource:j,setDataSource:T,loading:B,reload:function(){return C()}}},je=Te,se=r(98082),I=r(85893),e=["valueEnum","render","renderText","mode","plain","dataIndex","request","params","editable"],t=["request","columns","params","dataSource","onDataSourceChange","formProps","editable","loading","onLoadingChange","actionRef","onRequestError","emptyText","contentStyle"],d=function(n,c){var g=n.dataIndex;if(g){var y=Array.isArray(g)?(0,Ie.Z)(c,g):c[g];if(y!==void 0||y!==null)return y}return n.children},a=function(n){var c,g=n.valueEnum,y=n.action,N=n.index,E=n.text,D=n.entity,M=n.mode,w=n.render,f=n.editableUtils,v=n.valueType,j=n.plain,T=n.dataIndex,X=n.request,$=n.renderFormItem,B=n.params,G=n.emptyText,p=Y.ZP.useFormInstance(),C=(c=se.Ow.useToken)===null||c===void 0?void 0:c.call(se.Ow),O=C.token,u={text:E,valueEnum:g,mode:M||"read",proFieldProps:{emptyText:G,render:w?function(A){return w==null?void 0:w(A,D,N,y,(0,l.Z)((0,l.Z)({},n),{},{type:"descriptions"}))}:void 0},ignoreFormItem:!0,valueType:v,request:X,params:B,plain:j};if(M==="read"||!M||v==="option"){var F=(0,ae.w)(n.fieldProps,void 0,(0,l.Z)((0,l.Z)({},n),{},{rowKey:T,isEditable:!1}));return(0,I.jsx)(ue.Z,(0,l.Z)((0,l.Z)({name:T},u),{},{fieldProps:F}))}var z=function(){var b,m=(0,ae.w)(n.formItemProps,p,(0,l.Z)((0,l.Z)({},n),{},{rowKey:T,isEditable:!0})),S=(0,ae.w)(n.fieldProps,p,(0,l.Z)((0,l.Z)({},n),{},{rowKey:T,isEditable:!0}));return(0,I.jsxs)("div",{style:{display:"flex",gap:O.marginXS,alignItems:"baseline"},children:[(0,I.jsx)(he.U,(0,l.Z)((0,l.Z)({name:T},m),{},{style:(0,l.Z)({margin:0},(m==null?void 0:m.style)||{}),initialValue:E||(m==null?void 0:m.initialValue),children:(0,I.jsx)(ue.Z,(0,l.Z)((0,l.Z)({},u),{},{proFieldProps:(0,l.Z)({},u.proFieldProps),renderFormItem:$?function(){return $==null?void 0:$((0,l.Z)((0,l.Z)({},n),{},{type:"descriptions"}),{isEditable:!0,recordKey:T,record:p.getFieldValue([T].flat(1)),defaultRender:function(){return(0,I.jsx)(ue.Z,(0,l.Z)((0,l.Z)({},u),{},{fieldProps:S}))},type:"descriptions"},p)}:void 0,fieldProps:S}))})),(0,I.jsx)("div",{style:{display:"flex",maxHeight:O.controlHeight,alignItems:"center",gap:O.marginXS},children:f==null||(b=f.actionRender)===null||b===void 0?void 0:b.call(f,T||N,{cancelText:(0,I.jsx)(k.Z,{}),saveText:(0,I.jsx)(te.Z,{}),deleteText:!1})})]})};return(0,I.jsx)("div",{style:{marginTop:-5,marginBottom:-5,marginLeft:0,marginRight:0},children:z()})},s=function(n,c,g,y,N){var E,D=[],M=(0,ve.n)(ie.Z,"5.8.0")>=0,w=n==null||(E=n.map)===null||E===void 0?void 0:E.call(n,function(f,v){var j,T,X;if(U.isValidElement(f))return M?{children:f}:f;var $=f,B=$.valueEnum,G=$.render,p=$.renderText,C=$.mode,O=$.plain,u=$.dataIndex,F=$.request,z=$.params,A=$.editable,b=(0,V.Z)($,e),m=(j=d(f,c))!==null&&j!==void 0?j:b.children,S=p?p(m,c,v,g):m,ee=typeof b.title=="function"?b.title(f,"descriptions",null):b.title,J=typeof b.valueType=="function"?b.valueType(c||{},"descriptions"):b.valueType,Z=y==null?void 0:y.isEditable(u||v),P=C||Z?"edit":"read",de=y&&P==="read"&&A!==!1&&(A==null?void 0:A(S,c,v))!==!1,ye=de?ge.Z:U.Fragment,be=P==="edit"?S:(0,Ee.X)(S,f,S),pe=M&&J!=="option"?(0,l.Z)((0,l.Z)({},b),{},{key:b.key||((T=b.label)===null||T===void 0?void 0:T.toString())||v,label:(ee||b.label||b.tooltip)&&(0,I.jsx)(re.G,{label:ee||b.label,tooltip:b.tooltip,ellipsis:f.ellipsis}),children:(0,I.jsxs)(ye,{children:[(0,U.createElement)(a,(0,l.Z)((0,l.Z)({},f),{},{key:f==null?void 0:f.key,dataIndex:f.dataIndex||v,mode:P,text:be,valueType:J,entity:c,index:v,emptyText:N,action:g,editableUtils:y})),de&&(0,I.jsx)(q.Z,{onClick:function(){y==null||y.startEditable(u||v)}})]})}):(0,U.createElement)(oe.Z.Item,(0,l.Z)((0,l.Z)({},b),{},{key:b.key||((X=b.label)===null||X===void 0?void 0:X.toString())||v,label:(ee||b.label||b.tooltip)&&(0,I.jsx)(re.G,{label:ee||b.label,tooltip:b.tooltip,ellipsis:f.ellipsis})}),(0,I.jsxs)(ye,{children:[(0,I.jsx)(a,(0,l.Z)((0,l.Z)({},f),{},{dataIndex:f.dataIndex||v,mode:P,text:be,valueType:J,entity:c,index:v,action:g,editableUtils:y})),de&&J!=="option"&&(0,I.jsx)(q.Z,{onClick:function(){y==null||y.startEditable(u||v)}})]}));return J==="option"?(D.push(pe),null):pe}).filter(function(f){return f});return{options:D!=null&&D.length?D:null,children:w}},x=function(n){return(0,I.jsx)(oe.Z.Item,(0,l.Z)((0,l.Z)({},n),{},{children:n.children}))};x.displayName="ProDescriptionsItem";var h=function(n){return n.children},R=function(n){var c,g=n.request,y=n.columns,N=n.params,E=n.dataSource,D=n.onDataSourceChange,M=n.formProps,w=n.editable,f=n.loading,v=n.onLoadingChange,j=n.actionRef,T=n.onRequestError,X=n.emptyText,$=n.contentStyle,B=(0,V.Z)(n,t),G=(0,U.useContext)(Pe.ZP.ConfigContext),p=je((0,W.Z)((0,L.Z)().mark(function ee(){var J;return(0,L.Z)().wrap(function(P){for(;;)switch(P.prev=P.next){case 0:if(!g){P.next=6;break}return P.next=3,g(N||{});case 3:P.t0=P.sent,P.next=7;break;case 6:P.t0={data:{}};case 7:return J=P.t0,P.abrupt("return",J);case 9:case"end":return P.stop()}},ee)})),{onRequestError:T,effects:[(0,Ce.ZP)(N)],manual:!g,dataSource:E,loading:f,onLoadingChange:v,onDataSourceChange:D}),C=Oe((0,l.Z)((0,l.Z)({},n.editable),{},{childrenColumnName:void 0,dataSource:p.dataSource,setDataSource:p.setDataSource}));if((0,U.useEffect)(function(){j&&(j.current=(0,l.Z)({reload:p.reload},C))},[p,j,C]),p.loading||p.loading===void 0&&g)return(0,I.jsx)(fe.ZP,{type:"descriptions",list:!1,pageHeader:!1});var O=function(){var J=(0,$e.Z)(n.children).filter(Boolean).map(function(Z){if(!U.isValidElement(Z))return Z;var P=Z==null?void 0:Z.props,de=P.valueEnum,ye=P.valueType,be=P.dataIndex,pe=P.ellipsis,De=P.copyable,Re=P.request;return!ye&&!de&&!be&&!Re&&!pe&&!De&&Z.type.displayName!=="ProDescriptionsItem"?Z:(0,l.Z)((0,l.Z)({},Z==null?void 0:Z.props),{},{entity:E})});return[].concat((0,o.Z)(y||[]),(0,o.Z)(J)).filter(function(Z){return!Z||Z!=null&&Z.valueType&&["index","indexBorder"].includes(Z==null?void 0:Z.valueType)?!1:!(Z!=null&&Z.hideInDescriptions)}).sort(function(Z,P){return P.order||Z.order?(P.order||0)-(Z.order||0):(P.index||0)-(Z.index||0)})},u=s(O(),p.dataSource||{},(j==null?void 0:j.current)||p,w?C:void 0,n.emptyText),F=u.options,z=u.children,A=w?Y.ZP:h,b=null;(B.title||B.tooltip||B.tip)&&(b=(0,I.jsx)(re.G,{label:B.title,tooltip:B.tooltip||B.tip}));var m=G.getPrefixCls("pro-descriptions"),S=(0,ve.n)(ie.Z,"5.8.0")>=0;return(0,I.jsx)(H.S,{children:(0,I.jsx)(A,(0,l.Z)((0,l.Z)({form:(c=n.editable)===null||c===void 0?void 0:c.form,component:!1,submitter:!1},M),{},{onFinish:void 0,children:(0,I.jsx)(oe.Z,(0,l.Z)((0,l.Z)({className:m},B),{},{contentStyle:(0,l.Z)({minWidth:0},$||{}),extra:B.extra?(0,I.jsxs)(ge.Z,{children:[F,B.extra]}):F,title:b,items:S?z:void 0,children:S?null:z}))}),"form")})};R.Item=x;var K=null},26412:function(ce,Q,r){r.d(Q,{Z:function(){return I}});var o=r(67294),L=r(93967),W=r.n(L),V=r(74443),l=r(53124),k=r(98675),te=r(25378),Y={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1},fe=o.createContext({}),ae=r(50344),he=function(e,t){var d={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(d[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,a=Object.getOwnPropertySymbols(e);s<a.length;s++)t.indexOf(a[s])<0&&Object.prototype.propertyIsEnumerable.call(e,a[s])&&(d[a[s]]=e[a[s]]);return d};const ve=e=>(0,ae.Z)(e).map(t=>Object.assign(Object.assign({},t==null?void 0:t.props),{key:t.key}));function Ee(e,t,d){const a=o.useMemo(()=>t||ve(d),[t,d]);return o.useMemo(()=>a.map(x=>{var{span:h}=x,R=he(x,["span"]);return h==="filled"?Object.assign(Object.assign({},R),{filled:!0}):Object.assign(Object.assign({},R),{span:typeof h=="number"?h:(0,V.m9)(e,h)})}),[a,e])}var re=function(e,t){var d={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(d[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,a=Object.getOwnPropertySymbols(e);s<a.length;s++)t.indexOf(a[s])<0&&Object.prototype.propertyIsEnumerable.call(e,a[s])&&(d[a[s]]=e[a[s]]);return d};function Ce(e,t){let d=[],a=[],s=!1,x=0;return e.filter(h=>h).forEach(h=>{const{filled:R}=h,K=re(h,["filled"]);if(R){a.push(K),d.push(a),a=[],x=0;return}const i=t-x;x+=h.span||1,x>=t?(x>t?(s=!0,a.push(Object.assign(Object.assign({},K),{span:i}))):a.push(K),d.push(a),a=[],x=0):a.push(K)}),a.length>0&&d.push(a),d=d.map(h=>{const R=h.reduce((K,i)=>K+(i.span||1),0);if(R<t){const K=h[h.length-1];return K.span=t-R+1,h}return h}),[d,s]}var Se=(e,t)=>{const[d,a]=(0,o.useMemo)(()=>Ce(t,e),[t,e]);return d},xe=e=>{let{children:t}=e;return t};function ne(e){return e!=null}var _=e=>{const{itemPrefixCls:t,component:d,span:a,className:s,style:x,labelStyle:h,contentStyle:R,bordered:K,label:i,content:n,colon:c,type:g}=e,y=d;return K?o.createElement(y,{className:W()({[`${t}-item-label`]:g==="label",[`${t}-item-content`]:g==="content"},s),style:x,colSpan:a},ne(i)&&o.createElement("span",{style:h},i),ne(n)&&o.createElement("span",{style:R},n)):o.createElement(y,{className:W()(`${t}-item`,s),style:x,colSpan:a},o.createElement("div",{className:`${t}-item-container`},(i||i===0)&&o.createElement("span",{className:W()(`${t}-item-label`,{[`${t}-item-no-colon`]:!c}),style:h},i),(n||n===0)&&o.createElement("span",{className:W()(`${t}-item-content`),style:R},n)))};function le(e,t,d){let{colon:a,prefixCls:s,bordered:x}=t,{component:h,type:R,showLabel:K,showContent:i,labelStyle:n,contentStyle:c}=d;return e.map((g,y)=>{let{label:N,children:E,prefixCls:D=s,className:M,style:w,labelStyle:f,contentStyle:v,span:j=1,key:T}=g;return typeof h=="string"?o.createElement(_,{key:`${R}-${T||y}`,className:M,style:w,labelStyle:Object.assign(Object.assign({},n),f),contentStyle:Object.assign(Object.assign({},c),v),span:j,colon:a,component:h,itemPrefixCls:D,bordered:x,label:K?N:null,content:i?E:null,type:R}):[o.createElement(_,{key:`label-${T||y}`,className:M,style:Object.assign(Object.assign(Object.assign({},n),w),f),span:1,colon:a,component:h[0],itemPrefixCls:D,bordered:x,label:N,type:"label"}),o.createElement(_,{key:`content-${T||y}`,className:M,style:Object.assign(Object.assign(Object.assign({},c),w),v),span:j*2-1,component:h[1],itemPrefixCls:D,bordered:x,content:E,type:"content"})]})}var Oe=e=>{const t=o.useContext(fe),{prefixCls:d,vertical:a,row:s,index:x,bordered:h}=e;return a?o.createElement(o.Fragment,null,o.createElement("tr",{key:`label-${x}`,className:`${d}-row`},le(s,e,Object.assign({component:"th",type:"label",showLabel:!0},t))),o.createElement("tr",{key:`content-${x}`,className:`${d}-row`},le(s,e,Object.assign({component:"td",type:"content",showContent:!0},t)))):o.createElement("tr",{key:x,className:`${d}-row`},le(s,e,Object.assign({component:h?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},t)))},H=r(85982),ie=r(14747),ge=r(83559),oe=r(83262);const Pe=e=>{const{componentCls:t,labelBg:d}=e;return{[`&${t}-bordered`]:{[`> ${t}-view`]:{border:`${(0,H.unit)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"> table":{tableLayout:"auto"},[`${t}-row`]:{borderBottom:`${(0,H.unit)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderBottom:"none"},[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,H.unit)(e.padding)} ${(0,H.unit)(e.paddingLG)}`,borderInlineEnd:`${(0,H.unit)(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`> ${t}-item-label`]:{color:e.colorTextSecondary,backgroundColor:d,"&::after":{display:"none"}}}},[`&${t}-middle`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,H.unit)(e.paddingSM)} ${(0,H.unit)(e.paddingLG)}`}}},[`&${t}-small`]:{[`${t}-row`]:{[`> ${t}-item-label, > ${t}-item-content`]:{padding:`${(0,H.unit)(e.paddingXS)} ${(0,H.unit)(e.padding)}`}}}}}},$e=e=>{const{componentCls:t,extraColor:d,itemPaddingBottom:a,itemPaddingEnd:s,colonMarginRight:x,colonMarginLeft:h,titleMarginBottom:R}=e;return{[t]:Object.assign(Object.assign(Object.assign({},(0,ie.Wf)(e)),Pe(e)),{"&-rtl":{direction:"rtl"},[`${t}-header`]:{display:"flex",alignItems:"center",marginBottom:R},[`${t}-title`]:Object.assign(Object.assign({},ie.vS),{flex:"auto",color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}),[`${t}-extra`]:{marginInlineStart:"auto",color:d,fontSize:e.fontSize},[`${t}-view`]:{width:"100%",borderRadius:e.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},[`${t}-row`]:{"> th, > td":{paddingBottom:a,paddingInlineEnd:s},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},[`${t}-item-label`]:{color:e.colorTextTertiary,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${(0,H.unit)(h)} ${(0,H.unit)(x)}`},[`&${t}-item-no-colon::after`]:{content:'""'}},[`${t}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${t}-item-content`]:{display:"table-cell",flex:1,color:e.contentColor,fontSize:e.fontSize,lineHeight:e.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${t}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${t}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${t}-item-content`]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingSM}}},"&-small":{[`${t}-row`]:{"> th, > td":{paddingBottom:e.paddingXS}}}})}},Ie=e=>({labelBg:e.colorFillAlter,titleColor:e.colorText,titleMarginBottom:e.fontSizeSM*e.lineHeightSM,itemPaddingBottom:e.padding,itemPaddingEnd:e.padding,colonMarginRight:e.marginXS,colonMarginLeft:e.marginXXS/2,contentColor:e.colorText,extraColor:e.colorText});var Te=(0,ge.I$)("Descriptions",e=>{const t=(0,oe.mergeToken)(e,{});return $e(t)},Ie),je=function(e,t){var d={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(d[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,a=Object.getOwnPropertySymbols(e);s<a.length;s++)t.indexOf(a[s])<0&&Object.prototype.propertyIsEnumerable.call(e,a[s])&&(d[a[s]]=e[a[s]]);return d};const se=e=>{const{prefixCls:t,title:d,extra:a,column:s,colon:x=!0,bordered:h,layout:R,children:K,className:i,rootClassName:n,style:c,size:g,labelStyle:y,contentStyle:N,items:E}=e,D=je(e,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","items"]),{getPrefixCls:M,direction:w,descriptions:f}=o.useContext(l.E_),v=M("descriptions",t),j=(0,te.Z)(),T=o.useMemo(()=>{var u;return typeof s=="number"?s:(u=(0,V.m9)(j,Object.assign(Object.assign({},Y),s)))!==null&&u!==void 0?u:3},[j,s]),X=Ee(j,E,K),$=(0,k.Z)(g),B=Se(T,X),[G,p,C]=Te(v),O=o.useMemo(()=>({labelStyle:y,contentStyle:N}),[y,N]);return G(o.createElement(fe.Provider,{value:O},o.createElement("div",Object.assign({className:W()(v,f==null?void 0:f.className,{[`${v}-${$}`]:$&&$!=="default",[`${v}-bordered`]:!!h,[`${v}-rtl`]:w==="rtl"},i,n,p,C),style:Object.assign(Object.assign({},f==null?void 0:f.style),c)},D),(d||a)&&o.createElement("div",{className:`${v}-header`},d&&o.createElement("div",{className:`${v}-title`},d),a&&o.createElement("div",{className:`${v}-extra`},a)),o.createElement("div",{className:`${v}-view`},o.createElement("table",null,o.createElement("tbody",null,B.map((u,F)=>o.createElement(Oe,{key:F,index:F,colon:x,prefixCls:v,vertical:R==="vertical",bordered:h,row:u}))))))))};se.Item=xe;var I=se}}]);

//# sourceMappingURL=shared-yv6NDExNmBScWGVSkbKdF6VT0_.c8f34c96.async.js.map