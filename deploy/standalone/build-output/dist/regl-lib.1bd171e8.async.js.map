{"version": 3, "file": "regl-lib.1bd171e8.async.js", "mappings": "kGAAC,SAAUA,GAAQC,GAAS,CACuCC,GAAO,QAAUD,GAAQ,CAG5F,GAAE,KAAO,UAAY,CAAE,aAEvB,IAAIE,GAAe,SAAUC,EAAG,CAC9B,OACEA,aAAa,YACbA,aAAa,aACbA,aAAa,aACbA,aAAa,WACbA,aAAa,YACbA,aAAa,YACbA,aAAa,cACbA,aAAa,cACbA,aAAa,iBAEjB,EAEIC,GAAS,SAAUC,EAAMC,EAAM,CAEjC,QADIC,EAAO,OAAO,KAAKD,CAAI,EAClBE,EAAI,EAAGA,EAAID,EAAK,OAAQ,EAAEC,EACjCH,EAAKE,EAAKC,CAAC,CAAC,EAAIF,EAAKC,EAAKC,CAAC,CAAC,EAE9B,OAAOH,CACT,EAQII,GAAO;AAAA,EAIX,SAASC,GAAWC,EAAK,CACvB,OAAI,OAAO,MAAS,YACX,KAAKA,CAAG,EAEV,UAAYA,CACrB,CAEA,SAASC,GAAOC,EAAS,CACvB,IAAIC,EAAQ,IAAI,MAAM,UAAYD,CAAO,EACzC,cAAQ,MAAMC,CAAK,EACbA,CACR,CAEA,SAASC,GAAOC,EAAMH,EAAS,CACxBG,GACHJ,GAAMC,CAAO,CAEjB,CAEA,SAASI,GAASJ,EAAS,CACzB,OAAIA,EACK,KAAOA,EAET,EACT,CAEA,SAASK,GAAgBC,EAAOC,EAAeP,EAAS,CAChDM,KAASC,GACbR,GAAM,sBAAwBO,EAAQ,IAAMF,GAAQJ,CAAO,EACrD,sBAAwB,OAAO,KAAKO,CAAa,EAAE,KAAK,CAAC,CAEnE,CAEA,SAASC,GAAmBC,EAAMT,EAAS,CACpCX,GAAaoB,CAAI,GACpBV,GACE,yBAA2BK,GAAQJ,CAAO,EAC1C,yBAAyB,CAE/B,CAEA,SAASU,GAAgBC,EAAOC,EAAM,CACpC,OAAQA,EAAM,CACZ,IAAK,SAAU,OAAO,OAAOD,GAAU,SACvC,IAAK,SAAU,OAAO,OAAOA,GAAU,SACvC,IAAK,SAAU,OAAO,OAAOA,GAAU,SACvC,IAAK,UAAW,OAAO,OAAOA,GAAU,UACxC,IAAK,WAAY,OAAO,OAAOA,GAAU,WACzC,IAAK,YAAa,OAAO,OAAOA,GAAU,YAC1C,IAAK,SAAU,OAAO,OAAOA,GAAU,QACzC,CACF,CAEA,SAASE,GAAaF,EAAOC,EAAMZ,EAAS,CACrCU,GAAeC,EAAOC,CAAI,GAC7Bb,GACE,yBAA2BK,GAAQJ,CAAO,EAC1C,cAAgBY,EAAO,SAAY,OAAOD,CAAM,CAEtD,CAEA,SAASG,GAAqBH,EAAOX,EAAS,CACrCW,GAAS,IACRA,EAAQ,KAAOA,GACrBZ,GAAM,4BAA8BY,EAAQ,IAAMP,GAAQJ,CAAO,EAC3D,iCAAiC,CAE3C,CAEA,SAASe,GAAYJ,EAAOK,EAAMhB,EAAS,CACrCgB,EAAK,QAAQL,CAAK,EAAI,GACxBZ,GAAM,gBAAkBK,GAAQJ,CAAO,EAAI,qBAAuBgB,CAAI,CAE1E,CAEA,IAAIC,GAAkB,CACpB,KACA,SACA,YACA,aACA,aACA,aACA,qBACA,UACA,QACF,EAEA,SAASC,GAAkBC,EAAK,CAC9B,OAAO,KAAKA,CAAG,EAAE,QAAQ,SAAUC,EAAK,CAClCH,GAAgB,QAAQG,CAAG,EAAI,GACjCrB,GAAM,sCAAwCqB,EAAM,qBAAuBH,EAAe,CAE9F,CAAC,CACH,CAEA,SAASI,GAASvB,EAAKwB,EAAG,CAExB,IADAxB,EAAMA,EAAM,GACLA,EAAI,OAASwB,GAClBxB,EAAM,IAAMA,EAEd,OAAOA,CACT,CAEA,SAASyB,IAAc,CACrB,KAAK,KAAO,UACZ,KAAK,MAAQ,CAAC,EACd,KAAK,MAAQ,CAAC,EACd,KAAK,UAAY,EACnB,CAEA,SAASC,GAAYC,EAAQC,EAAM,CACjC,KAAK,OAASD,EACd,KAAK,KAAOC,EACZ,KAAK,OAAS,CAAC,CACjB,CAEA,SAASC,GAAaC,EAAYC,EAAY7B,EAAS,CACrD,KAAK,KAAO4B,EACZ,KAAK,KAAOC,EACZ,KAAK,QAAU7B,CACjB,CAEA,SAAS8B,IAAgB,CACvB,IAAI7B,EAAQ,IAAI,MACZ8B,GAAS9B,EAAM,OAASA,GAAO,SAAS,EACxC+B,EAAM,sCAAsC,KAAKD,CAAK,EAC1D,GAAIC,EACF,OAAOA,EAAI,CAAC,EAEd,IAAIC,EAAO,yCAAyC,KAAKF,CAAK,EAC9D,OAAIE,EACKA,EAAK,CAAC,EAER,SACT,CAEA,SAASC,IAAiB,CACxB,IAAIjC,EAAQ,IAAI,MACZ8B,GAAS9B,EAAM,OAASA,GAAO,SAAS,EACxC+B,EAAM,oCAAoC,KAAKD,CAAK,EACxD,GAAIC,EACF,OAAOA,EAAI,CAAC,EAEd,IAAIC,EAAO,mCAAmC,KAAKF,CAAK,EACxD,OAAIE,EACKA,EAAK,CAAC,EAER,SACT,CAEA,SAASE,GAAaC,EAAQC,EAAS,CACrC,IAAIC,EAAQF,EAAO,MAAM;AAAA,CAAI,EACzBP,EAAa,EACbD,EAAa,EACbW,EAAQ,CACV,QAAS,IAAIhB,GACb,EAAG,IAAIA,EACT,EACAgB,EAAM,QAAQ,KAAOA,EAAM,CAAC,EAAE,KAAOF,GAAWP,GAAa,EAC7DS,EAAM,QAAQ,MAAM,KAAK,IAAIf,GAAW,EAAG,EAAE,CAAC,EAC9C,QAAS7B,EAAI,EAAGA,EAAI2C,EAAM,OAAQ,EAAE3C,EAAG,CACrC,IAAI+B,EAAOY,EAAM3C,CAAC,EACd6C,EAAQ,2BAA2B,KAAKd,CAAI,EAChD,GAAIc,EACF,OAAQA,EAAM,CAAC,EAAG,CAChB,IAAK,OACH,IAAIC,EAAiB,iBAAiB,KAAKD,EAAM,CAAC,CAAC,EAC/CC,IACFZ,EAAaY,EAAe,CAAC,EAAI,EAC7BA,EAAe,CAAC,IAClBb,EAAaa,EAAe,CAAC,EAAI,EAC3Bb,KAAcW,IAClBA,EAAMX,CAAU,EAAI,IAAIL,MAI9B,MACF,IAAK,SACH,IAAImB,EAAW,6BAA6B,KAAKF,EAAM,CAAC,CAAC,EACrDE,IACFH,EAAMX,CAAU,EAAE,KAAQc,EAAS,CAAC,EAChC7C,GAAU6C,EAAS,CAAC,CAAC,EACrBA,EAAS,CAAC,GAEhB,KACJ,CAEFH,EAAMX,CAAU,EAAE,MAAM,KAAK,IAAIJ,GAAWK,IAAcH,CAAI,CAAC,CACjE,CACA,cAAO,KAAKa,CAAK,EAAE,QAAQ,SAAUX,EAAY,CAC/C,IAAIe,EAAOJ,EAAMX,CAAU,EAC3Be,EAAK,MAAM,QAAQ,SAAUjB,EAAM,CACjCiB,EAAK,MAAMjB,EAAK,MAAM,EAAIA,CAC5B,CAAC,CACH,CAAC,EACMa,CACT,CAEA,SAASK,GAAeC,EAAQ,CAC9B,IAAIC,EAAS,CAAC,EACd,OAAAD,EAAO,MAAM;AAAA,CAAI,EAAE,QAAQ,SAAUE,EAAQ,CAC3C,GAAI,EAAAA,EAAO,OAAS,GAGpB,KAAIP,EAAQ,iCAAiC,KAAKO,CAAM,EACpDP,EACFM,EAAO,KAAK,IAAInB,GACda,EAAM,CAAC,EAAI,EACXA,EAAM,CAAC,EAAI,EACXA,EAAM,CAAC,EAAE,KAAK,CAAC,CAAC,EACTO,EAAO,OAAS,GACzBD,EAAO,KAAK,IAAInB,GAAY,UAAW,EAAGoB,CAAM,CAAC,EAErD,CAAC,EACMD,CACT,CAEA,SAASE,GAAeT,EAAOU,EAAQ,CACrCA,EAAO,QAAQ,SAAUhD,EAAO,CAC9B,IAAI0C,EAAOJ,EAAMtC,EAAM,IAAI,EAC3B,GAAI0C,EAAM,CACR,IAAIjB,EAAOiB,EAAK,MAAM1C,EAAM,IAAI,EAChC,GAAIyB,EAAM,CACRA,EAAK,OAAO,KAAKzB,CAAK,EACtB0C,EAAK,UAAY,GACjB,MACF,CACF,CACAJ,EAAM,QAAQ,UAAY,GAC1BA,EAAM,QAAQ,MAAM,CAAC,EAAE,OAAO,KAAKtC,CAAK,CAC1C,CAAC,CACH,CAEA,SAASiD,GAAkBC,EAAIC,EAAQhB,EAAQxB,EAAMyB,EAAS,CAC5D,GAAI,CAACc,EAAG,mBAAmBC,EAAQD,EAAG,cAAc,EAAG,CACrD,IAAIN,EAASM,EAAG,iBAAiBC,CAAM,EACnCC,EAAWzC,IAASuC,EAAG,gBAAkB,WAAa,SAC1DG,GAAiBlB,EAAQ,SAAUiB,EAAW,kCAAmChB,CAAO,EACxF,IAAIE,EAAQJ,GAAYC,EAAQC,CAAO,EACnCY,EAASL,GAAcC,CAAM,EACjCG,GAAcT,EAAOU,CAAM,EAE3B,OAAO,KAAKV,CAAK,EAAE,QAAQ,SAAUX,EAAY,CAC/C,IAAIe,EAAOJ,EAAMX,CAAU,EAC3B,GAAI,CAACe,EAAK,UACR,OAGF,IAAIY,EAAU,CAAC,EAAE,EACbC,EAAS,CAAC,EAAE,EAEhB,SAASC,EAAM3D,EAAK4D,EAAO,CACzBH,EAAQ,KAAKzD,CAAG,EAChB0D,EAAO,KAAKE,GAAS,EAAE,CACzB,CAEAD,EAAK,eAAiB7B,EAAa,KAAOe,EAAK,KAAO;AAAA,EAAM,sDAAsD,EAElHA,EAAK,MAAM,QAAQ,SAAUjB,EAAM,CACjC,GAAIA,EAAK,OAAO,OAAS,EAAG,CAC1B+B,EAAKpC,GAAQK,EAAK,OAAQ,CAAC,EAAI,MAAO,2CAA2C,EACjF+B,EAAK/B,EAAK,KAAO9B,GAAM,sDAAsD,EAG7E,IAAI+D,EAAS,EACbjC,EAAK,OAAO,QAAQ,SAAUzB,EAAO,CACnC,IAAID,EAAUC,EAAM,QAChB2D,EAAQ,yBAAyB,KAAK5D,CAAO,EACjD,GAAI4D,EAAO,CACT,IAAIC,EAAWD,EAAM,CAAC,EAEtB,OADA5D,EAAU4D,EAAM,CAAC,EACTC,EAAU,CAChB,IAAK,SACHA,EAAW,IACX,KACJ,CACAF,EAAS,KAAK,IAAIjC,EAAK,KAAK,QAAQmC,EAAUF,CAAM,EAAG,CAAC,CAC1D,MACEA,EAAS,EAGXF,EAAKpC,GAAQ,KAAM,CAAC,CAAC,EACrBoC,EAAKpC,GAAQ,MAAOsC,EAAS,CAAC,EAAI/D,GAAM,kBAAkB,EAC1D6D,EAAKpC,GAAQ,KAAM,CAAC,CAAC,EACrBoC,EAAKzD,EAAUJ,GAAM,kBAAkB,CACzC,CAAC,EACD6D,EAAKpC,GAAQ,KAAM,CAAC,EAAIzB,EAAI,CAC9B,MACE6D,EAAKpC,GAAQK,EAAK,OAAQ,CAAC,EAAI,KAAK,EACpC+B,EAAK/B,EAAK,KAAO9B,GAAM,WAAW,CAEtC,CAAC,EACG,OAAO,UAAa,aAAe,CAAC,OAAO,QAC7C4D,EAAO,CAAC,EAAID,EAAQ,KAAK,IAAI,EAC7B,QAAQ,IAAI,MAAM,QAASC,CAAM,GAEjC,QAAQ,IAAID,EAAQ,KAAK,EAAE,CAAC,CAEhC,CAAC,EAEDrD,GAAM,MAAM,mBAAqBmD,EAAW,YAAcd,EAAM,CAAC,EAAE,IAAI,CACzE,CACF,CAEA,SAASuB,GAAgBX,EAAIY,EAASC,EAAYC,EAAY5B,EAAS,CACrE,GAAI,CAACc,EAAG,oBAAoBY,EAASZ,EAAG,WAAW,EAAG,CACpD,IAAIN,EAASM,EAAG,kBAAkBY,CAAO,EACrCG,EAAY/B,GAAY6B,EAAY3B,CAAO,EAC3C8B,EAAYhC,GAAY8B,EAAY5B,CAAO,EAE3C+B,EAAS,8CACXD,EAAU,CAAC,EAAE,KAAO,2BAA6BD,EAAU,CAAC,EAAE,KAAO,IAEnE,OAAO,UAAa,YACtB,QAAQ,IAAI,KAAOE,EAASxE,GAAO,KAAOiD,EACxC,uDACA,WAAW,EAEb,QAAQ,IAAIuB,EAASxE,GAAOiD,CAAM,EAEpC3C,GAAM,MAAMkE,CAAM,CACpB,CACF,CAEA,SAASC,GAAgBC,EAAQ,CAC/BA,EAAO,YAAcxC,GAAa,CACpC,CAEA,SAASyC,GAAqB9E,EAAM+E,EAAUC,EAAYC,EAAa,CACrEL,GAAe5E,CAAI,EAEnB,SAASkF,EAAI7E,EAAK,CAChB,OAAIA,EACK4E,EAAY,GAAG5E,CAAG,EAEpB,CACT,CACAL,EAAK,QAAUkF,EAAGlF,EAAK,OAAO,IAAI,EAClCA,EAAK,QAAUkF,EAAGlF,EAAK,OAAO,IAAI,EAElC,SAASmF,EAAUC,EAAMC,EAAK,CAC5B,OAAO,KAAKA,CAAG,EAAE,QAAQ,SAAUC,EAAG,CACpCF,EAAKH,EAAY,GAAGK,CAAC,CAAC,EAAI,EAC5B,CAAC,CACH,CAEA,IAAIC,EAAavF,EAAK,YAAc,CAAC,EACrCmF,EAASI,EAAYR,EAAS,MAAM,EACpCI,EAASI,EAAYR,EAAS,OAAO,EAErC,IAAIS,EAAexF,EAAK,cAAgB,CAAC,EACzCmF,EAASK,EAAcR,EAAW,MAAM,EACxCG,EAASK,EAAcR,EAAW,OAAO,EAEzChF,EAAK,UACH,UAAWA,EAAK,QAChB,UAAWA,EAAK,SAChB,aAAcA,EAAK,QACnB,aAAcA,EAAK,OACvB,CAEA,SAASyF,GAAclF,EAASqC,EAAS,CACvC,IAAI8C,EAAWjD,GAAc,EAC7BnC,GAAMC,EACJ,gBAAkBqC,GAAWP,GAAa,IACzCqD,IAAa,UAAY,GAAK,gBAAkBA,EAAS,CAC9D,CAEA,SAASC,GAAcjF,EAAMH,EAASqC,EAAS,CACxClC,GACH+E,GAAalF,EAASqC,GAAWP,GAAa,CAAC,CAEnD,CAEA,SAASuD,GAAuB/E,EAAOC,EAAeP,EAASqC,EAAS,CAChE/B,KAASC,GACb2E,GACE,sBAAwB5E,EAAQ,IAAMF,GAAQJ,CAAO,EACrD,sBAAwB,OAAO,KAAKO,CAAa,EAAE,KAAK,EACxD8B,GAAWP,GAAa,CAAC,CAE/B,CAEA,SAASwB,GAAkB3C,EAAOC,EAAMZ,EAASqC,EAAS,CACnD3B,GAAeC,EAAOC,CAAI,GAC7BsE,GACE,yBAA2B9E,GAAQJ,CAAO,EAC1C,cAAgBY,EAAO,SAAY,OAAOD,EAC1C0B,GAAWP,GAAa,CAAC,CAE/B,CAEA,SAASwD,GAAeC,EAAO,CAC7BA,EAAM,CACR,CAEA,SAASC,GAAwBC,EAAYC,EAAYC,EAAW,CAC9DF,EAAW,QACb1E,GACE0E,EAAW,QAAQ,SAAS,eAC5BC,EACA,2CAA2C,EAE7C3E,GACE0E,EAAW,aAAa,cAAc,OACtCE,EACA,gDAAgD,CAEtD,CAEA,IAAIC,GAAmB,MAEnBC,GAAa,KACbC,GAA4B,KAC5BC,GAA2B,KAC3BC,GAA2B,KAC3BC,GAA0B,KAE1BC,GAAU,KACVC,GAAmB,KACnBC,GAAW,KACXC,GAAoB,KACpBC,GAAS,KACTC,GAAkB,KAClBC,GAAW,KAEXC,GAA4B,MAC5BC,GAA4B,MAC5BC,GAA0B,MAC1BC,GAA6B,MAE7BC,GAAoB,MAEpBC,GAAY,CAAC,EAEjBA,GAAUZ,EAAO,EACjBY,GAAUX,EAAgB,EAAI,EAE9BW,GAAUV,EAAQ,EAClBU,GAAUT,EAAiB,EAC3BS,GAAUD,EAAiB,EAC3BC,GAAUH,EAAuB,EACjCG,GAAUL,EAAyB,EACnCK,GAAUJ,EAAyB,EAAI,EAEvCI,GAAUR,EAAM,EAChBQ,GAAUP,EAAe,EACzBO,GAAUN,EAAQ,EAClBM,GAAUF,EAA0B,EAAI,EAExC,SAASG,GAAWnG,EAAMoG,EAAU,CAClC,OAAIpG,IAAS8F,IACT9F,IAAS6F,IACT7F,IAAS+F,GACJ,EACE/F,IAASgG,GACX,EAEAE,GAAUlG,CAAI,EAAIoG,CAE7B,CAEA,SAASC,GAAQC,EAAG,CAClB,MAAO,EAAEA,EAAKA,EAAI,IAAQ,CAAC,CAACA,CAC9B,CAEA,SAASC,GAAgBC,EAAMC,EAASC,EAAQ,CAC9C,IAAI3H,EACA,EAAI0H,EAAQ,MACZE,EAAIF,EAAQ,OACZG,EAAIH,EAAQ,SAGhBnH,GAAM,EAAI,GAAK,GAAKoH,EAAO,gBACrBC,EAAI,GAAKA,GAAKD,EAAO,eAC3B,uBAAuB,GAGnBF,EAAK,QAAUxB,IAAoBwB,EAAK,QAAUxB,KACpD1F,GAAM+G,GAAO,CAAC,GAAKA,GAAOM,CAAC,EACzB,8EAA8E,EAG9EF,EAAQ,UAAY,EAClB,IAAM,GAAKE,IAAM,GACnBrH,GACEkH,EAAK,YAActB,IACnBsB,EAAK,YAAcpB,IACnBoB,EAAK,YAAcrB,IACnBqB,EAAK,YAAcnB,GACnB,4BAA4B,GAIhC/F,GAAM+G,GAAO,CAAC,GAAKA,GAAOM,CAAC,EACzB,2DAA2D,EAC7DrH,GAAMmH,EAAQ,WAAa,GAAK,GAAK,EACnC,mCAAmC,GAGnCA,EAAQ,OAASb,KACfc,EAAO,WAAW,QAAQ,0BAA0B,EAAI,GAC1DpH,GAAMkH,EAAK,YAAcvB,IAAcuB,EAAK,YAAcvB,GACxD,4DAA4D,EAEhE3F,GAAM,CAACkH,EAAK,WACV,qDAAqD,GAIzD,IAAIK,EAAYJ,EAAQ,OACxB,IAAK1H,EAAI,EAAGA,EAAI,GAAI,EAAEA,EACpB,GAAI8H,EAAU9H,CAAC,EAAG,CAChB,IAAI+H,EAAK,GAAK/H,EACVgI,EAAKJ,GAAK5H,EACdO,GAAMmH,EAAQ,QAAW,GAAK1H,EAAI,qBAAqB,EAEvD,IAAIiI,EAAMH,EAAU9H,CAAC,EAarB,GAXAO,GACE0H,EAAI,QAAUF,GACdE,EAAI,SAAWD,EACf,8BAA8B,EAEhCzH,GACE0H,EAAI,SAAWP,EAAQ,QACvBO,EAAI,iBAAmBP,EAAQ,gBAC/BO,EAAI,OAASP,EAAQ,KACrB,iCAAiC,EAE/B,CAAAO,EAAI,WAED,GAAIA,EAAI,KAAM,CAGnB,IAAIC,EAAU,KAAK,KAAKd,GAAUa,EAAI,KAAMJ,CAAC,EAAIE,EAAKE,EAAI,eAAe,EAAIA,EAAI,gBACjF1H,GAAM0H,EAAI,KAAK,aAAeC,EAAUF,EACtC,uEAAuE,CAC3E,MAAWC,EAAI,SAEJA,EAAI,IAGjB,MAAYR,EAAK,YACflH,IAAOmH,EAAQ,QAAW,GAAK1H,KAAQ,EAAG,mBAAmB,EAI7D0H,EAAQ,YACVnH,GAAM,CAACkH,EAAK,WACV,uDAAuD,CAE7D,CAEA,SAASU,GAAkBC,EAASX,EAAMY,EAAOV,EAAQ,CACvD,IAAI,EAAIS,EAAQ,MACZR,EAAIQ,EAAQ,OACZP,EAAIO,EAAQ,SAGhB7H,GACE,EAAI,GAAK,GAAKoH,EAAO,gBAAkBC,EAAI,GAAKA,GAAKD,EAAO,eAC5D,uBAAuB,EACzBpH,GACE,IAAMqH,EACN,yBAAyB,EAC3BrH,GACEkH,EAAK,QAAUxB,IAAoBwB,EAAK,QAAUxB,GAClD,qCAAqC,EAEvC,QAASjG,EAAI,EAAGA,EAAIqI,EAAM,OAAQ,EAAErI,EAAG,CACrC,IAAIsI,EAAOD,EAAMrI,CAAC,EAClBO,GACE+H,EAAK,QAAU,GAAKA,EAAK,SAAWV,EACpC,kCAAkC,EAEhCH,EAAK,aACPlH,GAAM,CAAC+H,EAAK,WACV,iDAAiD,EACnD/H,GAAM+H,EAAK,UAAY,EACrB,8CAA8C,GAMlD,QADIC,EAAUD,EAAK,OACVE,EAAI,EAAGA,EAAI,GAAI,EAAEA,EAAG,CAC3B,IAAIP,EAAMM,EAAQC,CAAC,EACnB,GAAIP,EAAK,CACP,IAAIF,EAAK,GAAKS,EACVR,EAAKJ,GAAKY,EACdjI,GAAM+H,EAAK,QAAW,GAAKE,EAAI,qBAAqB,EACpDjI,GACE0H,EAAI,QAAUF,GACdE,EAAI,SAAWD,EACf,8BAA8B,EAChCzH,GACE0H,EAAI,SAAWG,EAAQ,QACvBH,EAAI,iBAAmBG,EAAQ,gBAC/BH,EAAI,OAASG,EAAQ,KACrB,iCAAiC,EAE/BH,EAAI,aAEGA,EAAI,KACb1H,GAAM0H,EAAI,KAAK,aAAeF,EAAKC,EACjC,KAAK,IAAIZ,GAAUa,EAAI,KAAMJ,CAAC,EAAGI,EAAI,eAAe,EACtD,uEAAuE,EAC9DA,EAAI,SAEJA,EAAI,KAGjB,CACF,CACF,CACF,CAEA,IAAIQ,EAAU7I,GAAOW,GAAO,CAC1B,SAAUoF,GACV,MAAOvF,GACP,aAAcmF,GACd,QAASE,GACT,UAAW/E,GACX,iBAAkBgF,GAClB,YAAanE,GACb,KAAML,GACN,YAAayC,GACb,aAAc9C,GACd,IAAKM,GACL,MAAOC,GACP,YAAamC,GACb,UAAWY,GACX,SAAU5B,GACV,eAAgBmC,GAChB,aAAcE,GACd,kBAAmBiB,GACnB,aAAc1D,GACd,UAAWqF,GACX,YAAaW,EACf,CAAC,EAEGO,GAAmB,EAEnBC,GAAW,EACXC,GAAe,EACfC,GAAY,EAEhB,SAASC,GAAiB7H,EAAMH,EAAM,CACpC,KAAK,GAAM4H,KACX,KAAK,KAAOzH,EACZ,KAAK,KAAOH,CACd,CAEA,SAASiI,GAAW5I,EAAK,CACvB,OAAOA,EAAI,QAAQ,MAAO,MAAM,EAAE,QAAQ,KAAM,KAAK,CACvD,CAEA,SAAS6I,GAAY7I,EAAK,CACxB,GAAIA,EAAI,SAAW,EACjB,MAAO,CAAC,EAGV,IAAI8I,EAAY9I,EAAI,OAAO,CAAC,EACxB+I,EAAW/I,EAAI,OAAOA,EAAI,OAAS,CAAC,EAExC,GAAIA,EAAI,OAAS,GACb8I,IAAcC,IACbD,IAAc,KAAOA,IAAc,KACtC,MAAO,CAAC,IAAMF,GAAU5I,EAAI,OAAO,EAAGA,EAAI,OAAS,CAAC,CAAC,EAAI,GAAG,EAG9D,IAAI0C,EAAQ,4CAA4C,KAAK1C,CAAG,EAChE,GAAI0C,EACF,OACEmG,GAAW7I,EAAI,OAAO,EAAG0C,EAAM,KAAK,CAAC,EAClC,OAAOmG,GAAWnG,EAAM,CAAC,CAAC,CAAC,EAC3B,OAAOmG,GAAW7I,EAAI,OAAO0C,EAAM,MAAQA,EAAM,CAAC,EAAE,MAAM,CAAC,CAAC,EAInE,IAAIsG,EAAWhJ,EAAI,MAAM,GAAG,EAC5B,GAAIgJ,EAAS,SAAW,EACtB,MAAO,CAAC,IAAMJ,GAAU5I,CAAG,EAAI,GAAG,EAIpC,QADIgD,EAAS,CAAC,EACLnD,EAAI,EAAGA,EAAImJ,EAAS,OAAQ,EAAEnJ,EACrCmD,EAASA,EAAO,OAAO6F,GAAWG,EAASnJ,CAAC,CAAC,CAAC,EAEhD,OAAOmD,CACT,CAEA,SAASiG,GAAkBjJ,EAAK,CAC9B,MAAO,IAAM6I,GAAW7I,CAAG,EAAE,KAAK,IAAI,EAAI,GAC5C,CAEA,SAASkJ,GAAepI,EAAMH,EAAM,CAClC,OAAO,IAAIgI,GAAgB7H,EAAMmI,GAAiBtI,EAAO,EAAE,CAAC,CAC9D,CAEA,SAASwI,GAAW3J,EAAG,CACrB,OAAQ,OAAOA,GAAM,YAAc,CAACA,EAAE,WAAeA,aAAamJ,EACpE,CAEA,SAASS,GAAO5J,EAAG6J,EAAM,CACvB,GAAI,OAAO7J,GAAM,WACf,OAAO,IAAImJ,GAAgBH,GAAUhJ,CAAC,EACjC,GAAI,OAAOA,GAAM,UAAY,OAAOA,GAAM,UAC/C,OAAO,IAAImJ,GAAgBF,GAAcjJ,CAAC,EACrC,GAAI,MAAM,QAAQA,CAAC,EACxB,OAAO,IAAImJ,GAAgBD,GAAWlJ,EAAE,IAAI,CAAC8J,EAAGzJ,IAAMuJ,GAAME,EAAGD,EAAO,IAAMxJ,EAAI,GAAG,CAAC,CAAC,EAChF,GAAIL,aAAamJ,GACtB,OAAOnJ,EAET8I,EAAQ,GAAO,kCAAoCe,CAAI,CACzD,CAEA,IAAIE,GAAU,CACZ,gBAAiBZ,GACjB,OAAQO,GACR,UAAWC,GACX,MAAOC,GACP,SAAUH,EACZ,EAGIO,GAAM,CACR,KAAM,OAAO,uBAA0B,WACnC,SAAUC,EAAI,CAAE,OAAO,sBAAsBA,CAAE,CAAE,EACjD,SAAUA,EAAI,CAAE,OAAO,WAAWA,EAAI,EAAE,CAAE,EAC9C,OAAQ,OAAO,sBAAyB,WACpC,SAAUD,EAAK,CAAE,OAAO,qBAAqBA,CAAG,CAAE,EAClD,YACN,EAGIE,GAAS,OAAO,aAAgB,aAAe,YAAY,IACzD,UAAY,CAAE,OAAO,YAAY,IAAI,CAAE,EACvC,UAAY,CAAE,MAAO,CAAE,IAAI,IAAQ,EAEzC,SAASC,IAAqB,CAC5B,IAAIC,EAAY,CAAE,GAAI,CAAE,EACpBC,EAAe,CAAC,EAAE,EACtB,MAAO,CACL,GAAI,SAAU7J,EAAK,CACjB,IAAIgD,EAAS4G,EAAU5J,CAAG,EAC1B,OAAIgD,IAGJA,EAAS4G,EAAU5J,CAAG,EAAI6J,EAAa,OACvCA,EAAa,KAAK7J,CAAG,EACdgD,EACT,EAEA,IAAK,SAAU6B,EAAI,CACjB,OAAOgF,EAAahF,CAAE,CACxB,CACF,CACF,CAGA,SAASiF,GAAcC,EAASC,EAAQC,EAAY,CAClD,IAAIC,EAAS,SAAS,cAAc,QAAQ,EAC5CzK,GAAOyK,EAAO,MAAO,CACnB,OAAQ,EACR,OAAQ,EACR,QAAS,EACT,IAAK,EACL,KAAM,CACR,CAAC,EACDH,EAAQ,YAAYG,CAAM,EAEtBH,IAAY,SAAS,OACvBG,EAAO,MAAM,SAAW,WACxBzK,GAAOsK,EAAQ,MAAO,CACpB,OAAQ,EACR,QAAS,CACX,CAAC,GAGH,SAASI,GAAU,CACjB,IAAIC,EAAI,OAAO,WACX3C,EAAI,OAAO,YACf,GAAIsC,IAAY,SAAS,KAAM,CAC7B,IAAIM,EAASN,EAAQ,sBAAsB,EAC3CK,EAAIC,EAAO,MAAQA,EAAO,KAC1B5C,EAAI4C,EAAO,OAASA,EAAO,GAC7B,CACAH,EAAO,MAAQD,EAAaG,EAC5BF,EAAO,OAASD,EAAaxC,EAC7BhI,GAAOyK,EAAO,MAAO,CACnB,MAAOE,EAAI,KACX,OAAQ3C,EAAI,IACd,CAAC,CACH,CAEA,IAAI6C,EACAP,IAAY,SAAS,MAAQ,OAAO,gBAAmB,YAGzDO,EAAiB,IAAI,eAAe,UAAY,CAE9C,WAAWH,CAAM,CACnB,CAAC,EACDG,EAAe,QAAQP,CAAO,GAE9B,OAAO,iBAAiB,SAAUI,EAAQ,EAAK,EAGjD,SAASI,GAAa,CAChBD,EACFA,EAAe,WAAW,EAE1B,OAAO,oBAAoB,SAAUH,CAAM,EAE7CJ,EAAQ,YAAYG,CAAM,CAC5B,CAEA,OAAAC,EAAO,EAEA,CACL,OAAQD,EACR,UAAWK,CACb,CACF,CAEA,SAASC,GAAeN,EAAQO,EAAmB,CACjD,SAASC,EAAKC,EAAM,CAClB,GAAI,CACF,OAAOT,EAAO,WAAWS,EAAMF,CAAiB,CAClD,OAASG,EAAG,CACV,OAAO,IACT,CACF,CACA,OACEF,EAAI,OAAO,GACXA,EAAI,oBAAoB,GACxBA,EAAI,oBAAoB,CAE5B,CAEA,SAASG,GAAexJ,EAAK,CAC3B,OACE,OAAOA,EAAI,UAAa,UACxB,OAAOA,EAAI,aAAgB,YAC3B,OAAOA,EAAI,uBAA0B,UAEzC,CAEA,SAASyJ,GAAgBzJ,EAAK,CAC5B,OACE,OAAOA,EAAI,YAAe,YAC1B,OAAOA,EAAI,cAAiB,UAEhC,CAEA,SAAS0J,GAAiBC,EAAO,CAC/B,OAAI,OAAOA,GAAU,SACZA,EAAM,MAAM,GAErB1C,EAAQ,MAAM,QAAQ0C,CAAK,EAAG,yBAAyB,EAChDA,EACT,CAEA,SAASC,GAAYC,EAAM,CACzB,OAAI,OAAOA,GAAS,UAClB5C,EAAQ,OAAO,UAAa,YAAa,8BAA8B,EAChE,SAAS,cAAc4C,CAAI,GAE7BA,CACT,CAEA,SAASC,GAAWC,EAAO,CACzB,IAAIC,EAAOD,GAAS,CAAC,EACjBrB,EAASuB,EAAWpB,EAAQ7G,EAC5BoH,EAAoB,CAAC,EACrBc,EAAa,CAAC,EACdC,EAAqB,CAAC,EACtBvB,EAAc,OAAO,QAAW,YAAc,EAAI,OAAO,iBACzDwB,EAAU,GACVzB,EAAS,SAAU0B,EAAK,CACtBA,GACFpD,EAAQ,MAAMoD,CAAG,CAErB,EACInB,EAAY,UAAY,CAAC,EA0D7B,GAzDI,OAAOc,GAAS,UAClB/C,EACE,OAAO,UAAa,YACpB,oDAAoD,EACtDyB,EAAU,SAAS,cAAcsB,CAAI,EACrC/C,EAAQyB,EAAS,kCAAkC,GAC1C,OAAOsB,GAAS,SACrBR,GAAcQ,CAAI,EACpBtB,EAAUsB,EACDP,GAAeO,CAAI,GAC5BhI,EAAKgI,EACLnB,EAAS7G,EAAG,SAEZiF,EAAQ,YAAY+C,CAAI,EACpB,OAAQA,EACVhI,EAAKgI,EAAK,GACD,WAAYA,EACrBnB,EAASe,GAAWI,EAAK,MAAM,EACtB,cAAeA,IACxBC,EAAYL,GAAWI,EAAK,SAAS,GAEnC,eAAgBA,IAClBZ,EAAoBY,EAAK,WACzB/C,EAAQ,KAAKmC,EAAmB,SAAU,4BAA4B,GAEpE,eAAgBY,IAClBE,EAAaR,GAAgBM,EAAK,UAAU,GAE1C,uBAAwBA,IAC1BG,EAAqBT,GAAgBM,EAAK,kBAAkB,GAE1D,WAAYA,IACd/C,EAAQ,KACN+C,EAAK,OAAQ,WACb,oCAAoC,EACtCrB,EAASqB,EAAK,QAEZ,YAAaA,IACfI,EAAU,CAAC,CAACJ,EAAK,SAEf,eAAgBA,IAClBpB,EAAa,CAACoB,EAAK,WACnB/C,EAAQ2B,EAAa,EAAG,qBAAqB,IAIjD3B,EAAQ,MAAM,2BAA2B,EAGvCyB,IACEA,EAAQ,SAAS,YAAY,IAAM,SACrCG,EAASH,EAETuB,EAAYvB,GAIZ,CAAC1G,EAAI,CACP,GAAI,CAAC6G,EAAQ,CACX5B,EACE,OAAO,UAAa,YACpB,iEAAiE,EACnE,IAAItF,EAAS8G,GAAawB,GAAa,SAAS,KAAMtB,EAAQC,CAAU,EACxE,GAAI,CAACjH,EACH,OAAO,KAETkH,EAASlH,EAAO,OAChBuH,EAAYvH,EAAO,SACrB,CAEIyH,EAAkB,qBAAuB,SAAWA,EAAkB,mBAAqB,IAC/FpH,EAAKmH,GAAcN,EAAQO,CAAiB,CAC9C,CAEA,OAAKpH,EAME,CACL,GAAIA,EACJ,OAAQ6G,EACR,UAAWoB,EACX,WAAYC,EACZ,mBAAoBC,EACpB,WAAYvB,EACZ,QAASwB,EACT,OAAQzB,EACR,UAAWO,CACb,GAfEA,EAAU,EACVP,EAAO,0FAA0F,EAC1F,KAcX,CAEA,SAAS2B,GAAsBtI,EAAIuI,EAAQ,CACzC,IAAIL,EAAa,CAAC,EAElB,SAASM,EAAkBC,EAAO,CAChCxD,EAAQ,KAAKwD,EAAO,SAAU,+BAA+B,EAC7D,IAAInB,EAAOmB,EAAM,YAAY,EACzBC,EACJ,GAAI,CACFA,EAAMR,EAAWZ,CAAI,EAAItH,EAAG,aAAasH,CAAI,CAC/C,OAASC,EAAG,CAAC,CACb,MAAO,CAAC,CAACmB,CACX,CAEA,QAASlM,EAAI,EAAGA,EAAI+L,EAAO,WAAW,OAAQ,EAAE/L,EAAG,CACjD,IAAI8K,EAAOiB,EAAO,WAAW/L,CAAC,EAC9B,GAAI,CAACgM,EAAiBlB,CAAI,EACxB,OAAAiB,EAAO,UAAU,EACjBA,EAAO,OAAO,IAAMjB,EAAO,6GAA6G,EACjI,IAEX,CAEA,OAAAiB,EAAO,mBAAmB,QAAQC,CAAgB,EAE3C,CACL,WAAYN,EACZ,QAAS,UAAY,CACnB,OAAO,KAAKA,CAAU,EAAE,QAAQ,SAAUZ,EAAM,CAC9C,GAAIY,EAAWZ,CAAI,GAAK,CAACkB,EAAiBlB,CAAI,EAC5C,MAAM,IAAI,MAAM,qCAAuCA,CAAI,CAE/D,CAAC,CACH,CACF,CACF,CAEA,SAASqB,GAAMxK,EAAGyK,EAAG,CAEnB,QADIjJ,EAAS,MAAMxB,CAAC,EACX3B,EAAI,EAAGA,EAAI2B,EAAG,EAAE3B,EACvBmD,EAAOnD,CAAC,EAAIoM,EAAEpM,CAAC,EAEjB,OAAOmD,CACT,CAEA,IAAIkJ,GAAY,KACZC,GAAqB,KACrBC,GAAa,KACbC,GAAsB,KACtBC,GAAW,KACXC,GAAoB,KACpBC,GAAa,KAEjB,SAASC,GAAWrF,EAAG,CACrB,QAASvH,EAAI,GAAIA,GAAM,GAAK,GAAKA,GAAK,GACpC,GAAIuH,GAAKvH,EACP,OAAOA,EAGX,MAAO,EACT,CAEA,SAAS6M,GAAMtF,EAAG,CAChB,IAAIuF,EAAGC,EACP,OAAAD,GAAKvF,EAAI,QAAW,EACpBA,KAAOuF,EACPC,GAASxF,EAAI,MAAS,EACtBA,KAAOwF,EAAOD,GAAKC,EACnBA,GAASxF,EAAI,KAAQ,EACrBA,KAAOwF,EAAOD,GAAKC,EACnBA,GAASxF,EAAI,IAAQ,EACrBA,KAAOwF,EAAOD,GAAKC,EACZD,EAAKvF,GAAK,CACnB,CAEA,SAASyF,IAAc,CACrB,IAAIC,EAAad,GAAK,EAAG,UAAY,CACnC,MAAO,CAAC,CACV,CAAC,EAED,SAASe,EAAOvL,EAAG,CACjB,IAAIwL,EAAKP,GAAUjL,CAAC,EAChByL,EAAMH,EAAWJ,GAAKM,CAAE,GAAK,CAAC,EAClC,OAAIC,EAAI,OAAS,EACRA,EAAI,IAAI,EAEV,IAAI,YAAYD,CAAE,CAC3B,CAEA,SAASE,EAAMC,EAAK,CAClBL,EAAWJ,GAAKS,EAAI,UAAU,GAAK,CAAC,EAAE,KAAKA,CAAG,CAChD,CAEA,SAASC,EAAWtM,EAAMU,EAAG,CAC3B,IAAIwB,EAAS,KACb,OAAQlC,EAAM,CACZ,KAAKoL,GACHlJ,EAAS,IAAI,UAAU+J,EAAMvL,CAAC,EAAG,EAAGA,CAAC,EACrC,MACF,KAAK2K,GACHnJ,EAAS,IAAI,WAAW+J,EAAMvL,CAAC,EAAG,EAAGA,CAAC,EACtC,MACF,KAAK4K,GACHpJ,EAAS,IAAI,WAAW+J,EAAM,EAAIvL,CAAC,EAAG,EAAGA,CAAC,EAC1C,MACF,KAAK6K,GACHrJ,EAAS,IAAI,YAAY+J,EAAM,EAAIvL,CAAC,EAAG,EAAGA,CAAC,EAC3C,MACF,KAAK8K,GACHtJ,EAAS,IAAI,WAAW+J,EAAM,EAAIvL,CAAC,EAAG,EAAGA,CAAC,EAC1C,MACF,KAAK+K,GACHvJ,EAAS,IAAI,YAAY+J,EAAM,EAAIvL,CAAC,EAAG,EAAGA,CAAC,EAC3C,MACF,KAAKgL,GACHxJ,EAAS,IAAI,aAAa+J,EAAM,EAAIvL,CAAC,EAAG,EAAGA,CAAC,EAC5C,MACF,QACE,OAAO,IACX,CACA,OAAIwB,EAAO,SAAWxB,EACbwB,EAAO,SAAS,EAAGxB,CAAC,EAEtBwB,CACT,CAEA,SAASqK,EAAUC,EAAO,CACxBJ,EAAKI,EAAM,MAAM,CACnB,CAEA,MAAO,CACL,MAAOP,EACP,KAAMG,EACN,UAAWE,EACX,SAAUC,CACZ,CACF,CAEA,IAAIE,GAAOV,GAAW,EAGtBU,GAAK,KAAOV,GAAW,EAEvB,IAAIW,GAAmB,KACnBC,GAAc,KACdC,GAAgB,KAChBC,GAAe,KACfC,GAAgB,KAChBC,GAAgB,KAChBC,GAAkB,KAElBC,GAA8B,MAC9BC,GAA8B,MAE9BC,GAAsB,KACtBC,GAAuB,KACvBC,GAAwB,MACxBC,GAAgC,MAChCC,GAAyB,MACzBC,GAAsC,MACtCC,GAAoC,MACpCC,GAA6B,MAC7BC,GAAkC,MAClCC,GAA+B,MAC/BC,GAA2B,MAE3BC,GAAY,KACZC,GAAc,KACdC,GAAa,KACbC,GAA8B,MAE9BC,GAAoC,MAEpCC,GAAiC,MACjCC,GAA4B,MAE5BC,GAAgB,KAChBC,GAAsB,MACtBC,GAAiC,MACjCC,GAAc,MACdC,GAAU,KACVC,GAAa,KACbC,GAAqB,KACrBC,GAAiB,MACjBC,GAA0B,MAC1BC,GAAuB,MACvBC,GAAwB,MAExBC,GAAa,SAAUzM,EAAIkI,EAAY,CACzC,IAAIwE,EAAiB,EACjBxE,EAAW,iCACbwE,EAAiB1M,EAAG,aAAa2L,EAAiC,GAGpE,IAAIgB,EAAiB,EACjBC,EAAsB,EACtB1E,EAAW,qBACbyE,EAAiB3M,EAAG,aAAa6L,EAAyB,EAC1De,EAAsB5M,EAAG,aAAa4L,EAA8B,GAItE,IAAIiB,EAAY,CAAC,CAAC3E,EAAW,kBAC7B,GAAI2E,EAAW,CACb,IAAIC,EAAmB9M,EAAG,cAAc,EACxCA,EAAG,YAAY8L,GAAegB,CAAgB,EAC9C9M,EAAG,WAAW8L,GAAe,EAAGI,GAAS,EAAG,EAAG,EAAGA,GAASC,GAAY,IAAI,EAE3E,IAAIY,EAAM/M,EAAG,kBAAkB,EAK/B,GAJAA,EAAG,gBAAgBqM,GAAgBU,CAAG,EACtC/M,EAAG,qBAAqBqM,GAAgBE,GAAsBT,GAAegB,EAAkB,CAAC,EAChG9M,EAAG,YAAY8L,GAAe,IAAI,EAE9B9L,EAAG,uBAAuBqM,EAAc,IAAMC,GAAyBO,EAAY,OAElF,CACH7M,EAAG,SAAS,EAAG,EAAG,EAAG,CAAC,EACtBA,EAAG,WAAW,EAAK,EAAK,EAAK,CAAG,EAChCA,EAAG,MAAMwM,EAAqB,EAC9B,IAAIQ,EAAS9C,GAAK,UAAUiC,GAAY,CAAC,EACzCnM,EAAG,WAAW,EAAG,EAAG,EAAG,EAAGkM,GAASC,GAAYa,CAAM,EAEjDhN,EAAG,SAAS,EAAG6M,EAAY,IAE7B7M,EAAG,kBAAkB+M,CAAG,EACxB/M,EAAG,cAAc8M,CAAgB,EAEjCD,EAAYG,EAAO,CAAC,IAAM,GAG5B9C,GAAK,SAAS8C,CAAM,CACtB,CACF,CAGA,IAAIC,EAAO,OAAO,WAAc,cAAgB,OAAO,KAAK,UAAU,SAAS,GAAK,YAAY,KAAK,UAAU,UAAU,GAAK,OAAO,KAAK,UAAU,SAAS,GAEzJC,EAAkB,GAEtB,GAAI,CAACD,EAAM,CACT,IAAIE,EAAcnN,EAAG,cAAc,EAC/B1C,EAAO4M,GAAK,UAAUkC,GAAoB,EAAE,EAChDpM,EAAG,cAAciM,EAAW,EAC5BjM,EAAG,YAAY+L,GAAqBoB,CAAW,EAC/CnN,EAAG,WAAWgM,GAAgC,EAAGE,GAAS,EAAG,EAAG,EAAGA,GAASE,GAAoB9O,CAAI,EACpG4M,GAAK,SAAS5M,CAAI,EAClB0C,EAAG,YAAY+L,GAAqB,IAAI,EACxC/L,EAAG,cAAcmN,CAAW,EAC5BD,EAAkB,CAAClN,EAAG,SAAS,CACjC,CAEA,MAAO,CAEL,UAAW,CACTA,EAAG,aAAaoK,EAAW,EAC3BpK,EAAG,aAAaqK,EAAa,EAC7BrK,EAAG,aAAasK,EAAY,EAC5BtK,EAAG,aAAauK,EAAa,CAC/B,EACA,UAAWvK,EAAG,aAAawK,EAAa,EACxC,YAAaxK,EAAG,aAAayK,EAAe,EAC5C,aAAczK,EAAG,aAAamK,EAAgB,EAG9C,WAAY,OAAO,KAAKjC,CAAU,EAAE,OAAO,SAAUQ,EAAK,CACxD,MAAO,CAAC,CAACR,EAAWQ,CAAG,CACzB,CAAC,EAGD,eAAgBgE,EAGhB,eAAgBC,EAChB,oBAAqBC,EAGrB,cAAe5M,EAAG,aAAa0K,EAA2B,EAC1D,cAAe1K,EAAG,aAAa2K,EAA2B,EAC1D,gBAAiB3K,EAAG,aAAa6K,EAAoB,EACrD,wBAAyB7K,EAAG,aAAaiL,EAAmC,EAC5E,eAAgBjL,EAAG,aAAaqL,EAA4B,EAC5D,oBAAqBrL,EAAG,aAAasL,EAAwB,EAC7D,gBAAiBtL,EAAG,aAAamL,EAA0B,EAC3D,eAAgBnL,EAAG,aAAa4K,EAAmB,EACnD,cAAe5K,EAAG,aAAa8K,EAAqB,EACpD,kBAAmB9K,EAAG,aAAa+K,EAA6B,EAChE,sBAAuB/K,EAAG,aAAakL,EAAiC,EACxE,kBAAmBlL,EAAG,aAAagL,EAAsB,EACzD,oBAAqBhL,EAAG,aAAaoL,EAA+B,EAGpE,KAAMpL,EAAG,aAAa0L,EAA2B,EACjD,SAAU1L,EAAG,aAAawL,EAAW,EACrC,OAAQxL,EAAG,aAAauL,EAAS,EACjC,QAASvL,EAAG,aAAayL,EAAU,EAGnC,UAAWoB,EACX,gBAAiBK,CACnB,CACF,EAEA,SAASE,GAAepP,EAAK,CAC3B,MACE,CAAC,CAACA,GACF,OAAOA,GAAQ,UACf,MAAM,QAAQA,EAAI,KAAK,GACvB,MAAM,QAAQA,EAAI,MAAM,GACxB,OAAOA,EAAI,QAAW,UACtBA,EAAI,MAAM,SAAWA,EAAI,OAAO,SAC/B,MAAM,QAAQA,EAAI,IAAI,GACrB9B,GAAa8B,EAAI,IAAI,EAC3B,CAEA,IAAIqP,GAAS,SAAUrP,EAAK,CAC1B,OAAO,OAAO,KAAKA,CAAG,EAAE,IAAI,SAAUC,EAAK,CAAE,OAAOD,EAAIC,CAAG,CAAE,CAAC,CAChE,EAEIqP,GAAe,CACjB,MAAOC,GACP,QAASC,EACX,EAEA,SAASC,GAAWxD,EAAOyD,EAAIC,EAAK,CAClC,QAASnR,EAAI,EAAGA,EAAIkR,EAAI,EAAElR,EACxBmR,EAAInR,CAAC,EAAIyN,EAAMzN,CAAC,CAEpB,CAEA,SAASoR,GAAW3D,EAAOyD,EAAIG,EAAIF,EAAK,CAEtC,QADIG,EAAM,EACDtR,EAAI,EAAGA,EAAIkR,EAAI,EAAElR,EAExB,QADIuR,EAAM9D,EAAMzN,CAAC,EACRwI,EAAI,EAAGA,EAAI6I,EAAI,EAAE7I,EACxB2I,EAAIG,GAAK,EAAIC,EAAI/I,CAAC,CAGxB,CAEA,SAASgJ,GAAW/D,EAAOyD,EAAIG,EAAII,EAAIN,EAAKO,EAAM,CAEhD,QADIJ,EAAMI,EACD1R,EAAI,EAAGA,EAAIkR,EAAI,EAAElR,EAExB,QADIuR,EAAM9D,EAAMzN,CAAC,EACRwI,EAAI,EAAGA,EAAI6I,EAAI,EAAE7I,EAExB,QADImJ,EAAMJ,EAAI/I,CAAC,EACNoJ,EAAI,EAAGA,EAAIH,EAAI,EAAEG,EACxBT,EAAIG,GAAK,EAAIK,EAAIC,CAAC,CAI1B,CAEA,SAASC,GAAYpE,EAAOqE,EAAOC,EAAOZ,EAAKG,EAAK,CAElD,QADIU,EAAS,EACJhS,EAAI+R,EAAQ,EAAG/R,EAAI8R,EAAM,OAAQ,EAAE9R,EAC1CgS,GAAUF,EAAM9R,CAAC,EAEnB,IAAI2B,EAAImQ,EAAMC,CAAK,EACnB,GAAID,EAAM,OAASC,IAAU,EAAG,CAC9B,IAAIb,EAAKY,EAAMC,EAAQ,CAAC,EACpBV,EAAKS,EAAMC,EAAQ,CAAC,EACpBN,EAAKK,EAAMC,EAAQ,CAAC,EACxB,IAAK/R,EAAI,EAAGA,EAAI2B,EAAG,EAAE3B,EACnBwR,GAAU/D,EAAMzN,CAAC,EAAGkR,EAAIG,EAAII,EAAIN,EAAKG,CAAG,EACxCA,GAAOU,CAEX,KACE,KAAKhS,EAAI,EAAGA,EAAI2B,EAAG,EAAE3B,EACnB6R,GAAWpE,EAAMzN,CAAC,EAAG8R,EAAOC,EAAQ,EAAGZ,EAAKG,CAAG,EAC/CA,GAAOU,CAGb,CAEA,SAAShB,GAAcvD,EAAOqE,EAAO7Q,EAAMgR,EAAM,CAC/C,IAAI9E,EAAK,EACT,GAAI2E,EAAM,OACR,QAAS9R,EAAI,EAAGA,EAAI8R,EAAM,OAAQ,EAAE9R,EAClCmN,GAAM2E,EAAM9R,CAAC,OAGfmN,EAAK,EAEP,IAAIgE,EAAMc,GAAQvE,GAAK,UAAUzM,EAAMkM,CAAE,EACzC,OAAQ2E,EAAM,OAAQ,CACpB,IAAK,GACH,MACF,IAAK,GACHb,GAAUxD,EAAOqE,EAAM,CAAC,EAAGX,CAAG,EAC9B,MACF,IAAK,GACHC,GAAU3D,EAAOqE,EAAM,CAAC,EAAGA,EAAM,CAAC,EAAGX,CAAG,EACxC,MACF,IAAK,GACHK,GAAU/D,EAAOqE,EAAM,CAAC,EAAGA,EAAM,CAAC,EAAGA,EAAM,CAAC,EAAGX,EAAK,CAAC,EACrD,MACF,QACEU,GAAWpE,EAAOqE,EAAO,EAAGX,EAAK,CAAC,CACtC,CACA,OAAOA,CACT,CAEA,SAASJ,GAAcmB,EAAQ,CAE7B,QADIJ,EAAQ,CAAC,EACJrE,EAAQyE,EAAQzE,EAAM,OAAQA,EAAQA,EAAM,CAAC,EACpDqE,EAAM,KAAKrE,EAAM,MAAM,EAEzB,OAAOqE,CACT,CAEA,IAAIK,GAAc,CACjB,qBAAsB,KACtB,sBAAuB,KACvB,sBAAuB,KACvB,sBAAuB,KACvB,6BAA8B,KAC9B,uBAAwB,KACxB,uBAAwB,KACxB,wBAAyB,KACzB,wBAAyB,KACzB,uBAAwB,IACzB,EAEIC,GAAO,KACPC,GAAQ,KACRC,GAAQ,KACRC,GAAQ,KACRC,GAAS,KACTC,GAAS,KACTC,GAAQ,KACRC,GAAU,KACVC,GAAU,CACb,KAAMR,GACN,MAAOC,GACP,MAAOC,GACP,MAAOC,GACP,OAAQC,GACR,OAAQC,GACR,MAAOC,GACP,QAASC,EACV,EAEIE,GAAY,MACZC,GAAS,MACTC,GAAa,CAChB,QAASF,GACT,OAAQC,GACR,OAAU,KACX,EAEIE,GAAelC,GAAa,QAC5BmC,GAAanC,GAAa,MAE1BoC,GAAiB,MACjBC,GAAiB,MAEjBC,GAAqB,KACrBC,GAAa,KAEbC,GAAe,CAAC,EACpBA,GAAa,IAAI,EAAI,EACrBA,GAAa,IAAI,EAAI,EACrBA,GAAa,IAAI,EAAI,EACrBA,GAAa,IAAI,EAAI,EACrBA,GAAa,IAAI,EAAI,EACrBA,GAAa,IAAI,EAAI,EACrBA,GAAa,IAAI,EAAI,EAErB,SAASC,GAAgBzS,EAAM,CAC7B,OAAOqR,GAAW,OAAO,UAAU,SAAS,KAAKrR,CAAI,CAAC,EAAI,CAC5D,CAEA,SAAS0S,GAAWrC,EAAKsC,EAAK,CAC5B,QAASzT,EAAI,EAAGA,EAAIyT,EAAI,OAAQ,EAAEzT,EAChCmR,EAAInR,CAAC,EAAIyT,EAAIzT,CAAC,CAElB,CAEA,SAAS0T,GACPvQ,EAAQrC,EAAM6S,EAAQC,EAAQC,EAASC,EAAS9P,EAAQ,CAExD,QADIsN,EAAM,EACDtR,EAAI,EAAGA,EAAI2T,EAAQ,EAAE3T,EAC5B,QAASwI,EAAI,EAAGA,EAAIoL,EAAQ,EAAEpL,EAC5BrF,EAAOmO,GAAK,EAAIxQ,EAAK+S,EAAU7T,EAAI8T,EAAUtL,EAAIxE,CAAM,CAG7D,CAEA,SAAS+P,GAAiBvQ,EAAIwQ,EAAOjI,EAAQkI,EAAe,CAC1D,IAAIC,EAAc,EACdC,EAAY,CAAC,EAEjB,SAASC,EAAYnT,EAAM,CACzB,KAAK,GAAKiT,IACV,KAAK,OAAS1Q,EAAG,aAAa,EAC9B,KAAK,KAAOvC,EACZ,KAAK,MAAQiS,GACb,KAAK,WAAa,EAClB,KAAK,UAAY,EACjB,KAAK,MAAQE,GAEb,KAAK,eAAiB,KAElBrH,EAAO,UACT,KAAK,MAAQ,CAAE,KAAM,CAAE,EAE3B,CAEAqI,EAAW,UAAU,KAAO,UAAY,CACtC5Q,EAAG,WAAW,KAAK,KAAM,KAAK,MAAM,CACtC,EAEA4Q,EAAW,UAAU,QAAU,UAAY,CACzCC,EAAQ,IAAI,CACd,EAEA,IAAIC,EAAa,CAAC,EAElB,SAASC,EAActT,EAAMH,EAAM,CACjC,IAAI0T,EAASF,EAAW,IAAI,EAC5B,OAAKE,IACHA,EAAS,IAAIJ,EAAWnT,CAAI,GAE9BuT,EAAO,KAAK,EACZC,EAAmBD,EAAQ1T,EAAMqS,GAAgB,EAAG,EAAG,EAAK,EACrDqB,CACT,CAEA,SAASE,EAAeC,EAAW,CACjCL,EAAW,KAAKK,CAAS,CAC3B,CAEA,SAASC,EAA0BJ,EAAQ1T,EAAM+T,EAAO,CACtDL,EAAO,WAAa1T,EAAK,WACzB0C,EAAG,WAAWgR,EAAO,KAAM1T,EAAM+T,CAAK,CACxC,CAEA,SAASJ,EAAoBD,EAAQ1T,EAAM+T,EAAOC,EAAOC,EAAWC,EAAS,CAC3E,IAAIlD,EAEJ,GADA0C,EAAO,MAAQK,EACX,MAAM,QAAQ/T,CAAI,GAEpB,GADA0T,EAAO,MAAQM,GAASzB,GACpBvS,EAAK,OAAS,EAAG,CACnB,IAAImU,EACJ,GAAI,MAAM,QAAQnU,EAAK,CAAC,CAAC,EAAG,CAC1BgR,EAAQmB,GAAWnS,CAAI,EAEvB,QADIoU,EAAM,EACDlV,EAAI,EAAGA,EAAI8R,EAAM,OAAQ,EAAE9R,EAClCkV,GAAOpD,EAAM9R,CAAC,EAEhBwU,EAAO,UAAYU,EACnBD,EAAWjC,GAAalS,EAAMgR,EAAO0C,EAAO,KAAK,EACjDI,EAAyBJ,EAAQS,EAAUJ,CAAK,EAC5CG,EACFR,EAAO,eAAiBS,EAExBvH,GAAK,SAASuH,CAAQ,CAE1B,SAAW,OAAOnU,EAAK,CAAC,GAAM,SAAU,CACtC0T,EAAO,UAAYO,EACnB,IAAII,EAAYzH,GAAK,UAAU8G,EAAO,MAAO1T,EAAK,MAAM,EACxD0S,GAAU2B,EAAWrU,CAAI,EACzB8T,EAAyBJ,EAAQW,EAAWN,CAAK,EAC7CG,EACFR,EAAO,eAAiBW,EAExBzH,GAAK,SAASyH,CAAS,CAE3B,MAAWzV,GAAaoB,EAAK,CAAC,CAAC,GAC7B0T,EAAO,UAAY1T,EAAK,CAAC,EAAE,OAC3B0T,EAAO,MAAQM,GAASvB,GAAezS,EAAK,CAAC,CAAC,GAAKuS,GACnD4B,EAAWjC,GACTlS,EACA,CAACA,EAAK,OAAQA,EAAK,CAAC,EAAE,MAAM,EAC5B0T,EAAO,KAAK,EACdI,EAAyBJ,EAAQS,EAAUJ,CAAK,EAC5CG,EACFR,EAAO,eAAiBS,EAExBvH,GAAK,SAASuH,CAAQ,GAGxBxM,EAAQ,MAAM,qBAAqB,CAEvC,UACS/I,GAAaoB,CAAI,EAC1B0T,EAAO,MAAQM,GAASvB,GAAezS,CAAI,EAC3C0T,EAAO,UAAYO,EACnBH,EAAyBJ,EAAQ1T,EAAM+T,CAAK,EACxCG,IACFR,EAAO,eAAiB,IAAI,WAAW,IAAI,WAAW1T,EAAK,MAAM,CAAC,WAE3D8P,GAAc9P,CAAI,EAAG,CAC9BgR,EAAQhR,EAAK,MACb,IAAIkR,GAASlR,EAAK,OACdkD,EAASlD,EAAK,OAEd6S,EAAS,EACTC,EAAS,EACTC,GAAU,EACVC,GAAU,EACVhC,EAAM,SAAW,GACnB6B,EAAS7B,EAAM,CAAC,EAChB8B,EAAS,EACTC,GAAU7B,GAAO,CAAC,EAClB8B,GAAU,GACDhC,EAAM,SAAW,GAC1B6B,EAAS7B,EAAM,CAAC,EAChB8B,EAAS9B,EAAM,CAAC,EAChB+B,GAAU7B,GAAO,CAAC,EAClB8B,GAAU9B,GAAO,CAAC,GAElBvJ,EAAQ,MAAM,eAAe,EAG/B+L,EAAO,MAAQM,GAASvB,GAAezS,EAAK,IAAI,GAAKuS,GACrDmB,EAAO,UAAYZ,EAEnB,IAAIwB,EAAgB1H,GAAK,UAAU8G,EAAO,MAAOb,EAASC,CAAM,EAChEF,GAAU0B,EACRtU,EAAK,KACL6S,EAAQC,EACRC,GAASC,GACT9P,CAAM,EACR4Q,EAAyBJ,EAAQY,EAAeP,CAAK,EACjDG,EACFR,EAAO,eAAiBY,EAExB1H,GAAK,SAAS0H,CAAa,CAE/B,MAAWtU,aAAgB,aACzB0T,EAAO,MAAQpB,GACfoB,EAAO,UAAYO,EACnBH,EAAyBJ,EAAQ1T,EAAM+T,CAAK,EACxCG,IACFR,EAAO,eAAiB,IAAI,WAAW,IAAI,WAAW1T,CAAI,CAAC,IAG7D2H,EAAQ,MAAM,qBAAqB,CAEvC,CAEA,SAAS4L,EAASG,EAAQ,CACxBR,EAAM,cAGNC,EAAcO,CAAM,EAEpB,IAAIa,EAASb,EAAO,OACpB/L,EAAQ4M,EAAQ,oCAAoC,EACpD7R,EAAG,aAAa6R,CAAM,EACtBb,EAAO,OAAS,KAChB,OAAOL,EAAUK,EAAO,EAAE,CAC5B,CAEA,SAASc,EAAcC,EAAStU,EAAMuU,EAAWC,EAAY,CAC3DzB,EAAM,cAEN,IAAIQ,EAAS,IAAIJ,EAAWnT,CAAI,EAChCkT,EAAUK,EAAO,EAAE,EAAIA,EAEvB,SAASkB,EAAYH,EAAS,CAC5B,IAAIV,EAAQ3B,GACRpS,EAAO,KACP6U,GAAa,EACbb,EAAQ,EACRC,EAAY,EAChB,OAAI,MAAM,QAAQQ,CAAO,GACrB7V,GAAa6V,CAAO,GACpB3E,GAAc2E,CAAO,GACrBA,aAAmB,YACrBzU,EAAOyU,EACE,OAAOA,GAAY,SAC5BI,GAAaJ,EAAU,EACdA,IACT9M,EAAQ,KACN8M,EAAS,SACT,0DAA0D,EAExD,SAAUA,IACZ9M,EACE3H,IAAS,MACT,MAAM,QAAQA,CAAI,GAClBpB,GAAaoB,CAAI,GACjB8P,GAAc9P,CAAI,EAClB,yBAAyB,EAC3BA,EAAOyU,EAAQ,MAGb,UAAWA,IACb9M,EAAQ,UAAU8M,EAAQ,MAAOxC,GAAY,sBAAsB,EACnE8B,EAAQ9B,GAAWwC,EAAQ,KAAK,GAG9B,SAAUA,IACZ9M,EAAQ,UAAU8M,EAAQ,KAAM3C,GAAS,qBAAqB,EAC9DkC,EAAQlC,GAAQ2C,EAAQ,IAAI,GAG1B,cAAeA,IACjB9M,EAAQ,KAAK8M,EAAQ,UAAW,SAAU,mBAAmB,EAC7DR,EAAYQ,EAAQ,UAAY,GAG9B,WAAYA,IACd9M,EAAQ,IAAIkN,GAAY,6CAA6C,EACrEA,GAAaJ,EAAQ,OAAS,IAIlCf,EAAO,KAAK,EACP1T,EAQH2T,EAAmBD,EAAQ1T,EAAM+T,EAAOC,EAAOC,EAAWU,CAAU,GANhEE,IAAYnS,EAAG,WAAWgR,EAAO,KAAMmB,GAAYd,CAAK,EAC5DL,EAAO,MAAQM,GAAS1B,GACxBoB,EAAO,MAAQK,EACfL,EAAO,UAAYO,EACnBP,EAAO,WAAamB,IAKlB5J,EAAO,UACTyI,EAAO,MAAM,KAAOA,EAAO,WAAalB,GAAakB,EAAO,KAAK,GAG5DkB,CACT,CAEA,SAASE,EAAY9U,EAAMkD,EAAQ,CACjCyE,EAAQzE,EAASlD,EAAK,YAAc0T,EAAO,WACzC,+EAAuF1T,EAAK,WAAa,yBAA2BkD,EAAS,wBAA0BwQ,EAAO,UAAU,EAE1LhR,EAAG,cAAcgR,EAAO,KAAMxQ,EAAQlD,CAAI,CAC5C,CAEA,SAAS+U,EAAS/U,EAAMgV,EAAS,CAC/B,IAAI9R,GAAU8R,GAAW,GAAK,EAC1BhE,GAEJ,GADA0C,EAAO,KAAK,EACR9U,GAAaoB,CAAI,GAAKA,aAAgB,YACxC8U,EAAW9U,EAAMkD,CAAM,UACd,MAAM,QAAQlD,CAAI,GAC3B,GAAIA,EAAK,OAAS,EAChB,GAAI,OAAOA,EAAK,CAAC,GAAM,SAAU,CAC/B,IAAIiV,EAAYrI,GAAK,UAAU8G,EAAO,MAAO1T,EAAK,MAAM,EACxD0S,GAAUuC,EAAWjV,CAAI,EACzB8U,EAAWG,EAAW/R,CAAM,EAC5B0J,GAAK,SAASqI,CAAS,CACzB,SAAW,MAAM,QAAQjV,EAAK,CAAC,CAAC,GAAKpB,GAAaoB,EAAK,CAAC,CAAC,EAAG,CAC1DgR,GAAQmB,GAAWnS,CAAI,EACvB,IAAImU,EAAWjC,GAAalS,EAAMgR,GAAO0C,EAAO,KAAK,EACrDoB,EAAWX,EAAUjR,CAAM,EAC3B0J,GAAK,SAASuH,CAAQ,CACxB,MACExM,EAAQ,MAAM,qBAAqB,UAG9BmI,GAAc9P,CAAI,EAAG,CAC9BgR,GAAQhR,EAAK,MACb,IAAIkR,EAASlR,EAAK,OAEd6S,GAAS,EACTC,GAAS,EACTC,EAAU,EACVC,GAAU,EACVhC,GAAM,SAAW,GACnB6B,GAAS7B,GAAM,CAAC,EAChB8B,GAAS,EACTC,EAAU7B,EAAO,CAAC,EAClB8B,GAAU,GACDhC,GAAM,SAAW,GAC1B6B,GAAS7B,GAAM,CAAC,EAChB8B,GAAS9B,GAAM,CAAC,EAChB+B,EAAU7B,EAAO,CAAC,EAClB8B,GAAU9B,EAAO,CAAC,GAElBvJ,EAAQ,MAAM,eAAe,EAE/B,IAAIqM,GAAQ,MAAM,QAAQhU,EAAK,IAAI,EAC/B0T,EAAO,MACPjB,GAAezS,EAAK,IAAI,EAExBsU,GAAgB1H,GAAK,UAAUoH,GAAOnB,GAASC,EAAM,EACzDF,GAAU0B,GACRtU,EAAK,KACL6S,GAAQC,GACRC,EAASC,GACThT,EAAK,MAAM,EACb8U,EAAWR,GAAepR,CAAM,EAChC0J,GAAK,SAAS0H,EAAa,CAC7B,MACE3M,EAAQ,MAAM,iCAAiC,EAEjD,OAAOiN,CACT,CAEA,OAAKF,GACHE,EAAWH,CAAO,EAGpBG,EAAW,UAAY,SACvBA,EAAW,QAAUlB,EACrBkB,EAAW,QAAUG,EACjB9J,EAAO,UACT2J,EAAW,MAAQlB,EAAO,OAE5BkB,EAAW,QAAU,UAAY,CAAErB,EAAQG,CAAM,CAAE,EAE5CkB,CACT,CAEA,SAASM,GAAkB,CACzBnF,GAAOsD,CAAS,EAAE,QAAQ,SAAUK,EAAQ,CAC1CA,EAAO,OAAShR,EAAG,aAAa,EAChCA,EAAG,WAAWgR,EAAO,KAAMA,EAAO,MAAM,EACxChR,EAAG,WACDgR,EAAO,KAAMA,EAAO,gBAAkBA,EAAO,WAAYA,EAAO,KAAK,CACzE,CAAC,CACH,CAEA,OAAIzI,EAAO,UACTiI,EAAM,mBAAqB,UAAY,CACrC,IAAIiC,EAAQ,EAEZ,cAAO,KAAK9B,CAAS,EAAE,QAAQ,SAAU1S,EAAK,CAC5CwU,GAAS9B,EAAU1S,CAAG,EAAE,MAAM,IAChC,CAAC,EACMwU,CACT,GAGK,CACL,OAAQX,EAER,aAAcf,EACd,cAAeG,EAEf,MAAO,UAAY,CACjB7D,GAAOsD,CAAS,EAAE,QAAQE,CAAO,EACjCC,EAAW,QAAQD,CAAO,CAC5B,EAEA,UAAW,SAAU6B,EAAS,CAC5B,OAAIA,GAAWA,EAAQ,mBAAmB9B,EACjC8B,EAAQ,QAEV,IACT,EAEA,QAASF,EAET,YAAavB,CACf,CACF,CAEA,IAAI0B,GAAS,EACTC,GAAQ,EACRzT,GAAQ,EACRZ,GAAO,EACPsU,GAAY,EACZC,GAAW,EACXC,GAAY,CACf,OAAQJ,GACR,MAAOC,GACP,MAAOzT,GACP,KAAMZ,GACN,UAAWsU,GACX,SAAUC,GACV,YAAa,EACb,aAAc,EACd,iBAAkB,EAClB,eAAgB,CACjB,EAEIE,GAAY,EACZC,GAAW,EACXC,GAAe,EAEfC,GAAY,KACZC,GAAqB,KACrBC,GAAa,KACbC,GAAsB,KACtBC,GAAW,KACXC,GAAoB,KAEpBC,GAA0B,MAE1BC,GAAmB,MACnBC,GAAmB,MAEvB,SAASC,GAAmB5T,EAAIkI,EAAY2L,EAAarD,EAAO,CAC9D,IAAIsD,EAAa,CAAC,EACdC,EAAe,EAEfC,EAAe,CACjB,MAASZ,GACT,OAAUE,EACZ,EAEIpL,EAAW,yBACb8L,EAAa,OAASR,IAGxB,SAASS,EAAmBjD,EAAQ,CAClC,KAAK,GAAK+C,IACVD,EAAW,KAAK,EAAE,EAAI,KACtB,KAAK,OAAS9C,EACd,KAAK,SAAWkC,GAChB,KAAK,UAAY,EACjB,KAAK,KAAO,CACd,CAEAe,EAAkB,UAAU,KAAO,UAAY,CAC7C,KAAK,OAAO,KAAK,CACnB,EAEA,IAAIxK,EAAa,CAAC,EAElB,SAASyK,EAAqB5W,EAAM,CAClC,IAAIqC,EAAS8J,EAAW,IAAI,EAC5B,OAAK9J,IACHA,EAAS,IAAIsU,EAAkBJ,EAAY,OACzC,KACAJ,GACA,GACA,EAAK,EAAE,OAAO,GAElBU,EAAaxU,EAAQrC,EAAMoW,GAAkB,GAAI,GAAI,EAAG,CAAC,EAClD/T,CACT,CAEA,SAASyU,EAAsBC,EAAU,CACvC5K,EAAW,KAAK4K,CAAQ,CAC1B,CAEA,SAASF,EACPE,EACA/W,EACA+T,EACAiD,EACAC,EACApC,EACA1U,EAAM,CACN4W,EAAS,OAAO,KAAK,EACrB,IAAI/C,EACJ,GAAIhU,EAAM,CACR,IAAIkX,EAAgB/W,EAChB,CAACA,IACH,CAACvB,GAAaoB,CAAI,GAChB8P,GAAc9P,CAAI,GAAK,CAACpB,GAAaoB,EAAK,IAAI,KAChDkX,EAAgBtM,EAAW,uBACvBsL,GACAF,IAENO,EAAY,YACVQ,EAAS,OACT/W,EACA+T,EACAmD,EACA,CAAC,CACL,MACExU,EAAG,WAAWyT,GAAyBtB,EAAYd,CAAK,EACxDgD,EAAS,OAAO,MAAQ/C,GAAS8B,GACjCiB,EAAS,OAAO,MAAQhD,EACxBgD,EAAS,OAAO,UAAY,EAC5BA,EAAS,OAAO,WAAalC,EAI/B,GADAb,EAAQ7T,EACJ,CAACA,EAAM,CACT,OAAQ4W,EAAS,OAAO,MAAO,CAC7B,KAAKjB,GACL,KAAKD,GACH7B,EAAQ8B,GACR,MAEF,KAAKE,GACL,KAAKD,GACH/B,EAAQgC,GACR,MAEF,KAAKE,GACL,KAAKD,GACHjC,EAAQkC,GACR,MAEF,QACEvO,EAAQ,MAAM,oCAAoC,CACtD,CACAoP,EAAS,OAAO,MAAQ/C,CAC1B,CACA+C,EAAS,KAAO/C,EAGhBrM,EACEqM,IAAUkC,IACV,CAAC,CAACtL,EAAW,uBACb,2EAA2E,EAG7E,IAAIuM,EAAYF,EACZE,EAAY,IACdA,EAAYJ,EAAS,OAAO,WACxB/C,IAAUgC,GACZmB,IAAc,EACLnD,IAAUkC,KACnBiB,IAAc,IAGlBJ,EAAS,UAAYI,EAGrB,IAAIC,EAAWJ,EACf,GAAIA,EAAO,EAAG,CACZI,EAAWxB,GACX,IAAI3B,EAAY8C,EAAS,OAAO,UAC5B9C,IAAc,IAAGmD,EAAW1B,IAC5BzB,IAAc,IAAGmD,EAAWzB,IAC5B1B,IAAc,IAAGmD,EAAWxB,GAClC,CACAmB,EAAS,SAAWK,CACtB,CAEA,SAASC,EAAiBN,EAAU,CAClC7D,EAAM,gBAENvL,EAAQoP,EAAS,SAAW,KAAM,kCAAkC,EACpE,OAAOP,EAAWO,EAAS,EAAE,EAC7BA,EAAS,OAAO,QAAQ,EACxBA,EAAS,OAAS,IACpB,CAEA,SAASO,EAAgB7C,EAASE,EAAY,CAC5C,IAAIjB,EAAS6C,EAAY,OAAO,KAAMJ,GAAyB,EAAI,EAC/DY,EAAW,IAAIJ,EAAkBjD,EAAO,OAAO,EACnDR,EAAM,gBAEN,SAASqE,EAAc9C,EAAS,CAC9B,GAAI,CAACA,EACHf,EAAO,EACPqD,EAAS,SAAWnB,GACpBmB,EAAS,UAAY,EACrBA,EAAS,KAAOjB,WACP,OAAOrB,GAAY,SAC5Bf,EAAOe,CAAO,EACdsC,EAAS,SAAWnB,GACpBmB,EAAS,UAAYtC,EAAU,EAC/BsC,EAAS,KAAOjB,OACX,CACL,IAAI9V,EAAO,KACP+T,EAAQsC,GACRe,EAAW,GACXD,EAAY,GACZtC,EAAa,EACbb,EAAQ,EACR,MAAM,QAAQS,CAAO,GACrB7V,GAAa6V,CAAO,GACpB3E,GAAc2E,CAAO,EACvBzU,EAAOyU,GAEP9M,EAAQ,KAAK8M,EAAS,SAAU,gCAAgC,EAC5D,SAAUA,IACZzU,EAAOyU,EAAQ,KACf9M,EACE,MAAM,QAAQ3H,CAAI,GAChBpB,GAAaoB,CAAI,GACjB8P,GAAc9P,CAAI,EACpB,iCAAiC,GAEjC,UAAWyU,IACb9M,EAAQ,UACN8M,EAAQ,MACRxC,GACA,8BAA8B,EAChC8B,EAAQ9B,GAAWwC,EAAQ,KAAK,GAE9B,cAAeA,IACjB9M,EAAQ,UACN8M,EAAQ,UACRgB,GACA,kCAAkC,EACpC2B,EAAW3B,GAAUhB,EAAQ,SAAS,GAEpC,UAAWA,IACb9M,EACE,OAAO8M,EAAQ,OAAU,UAAYA,EAAQ,OAAS,EACtD,mCAAmC,EACrC0C,EAAY1C,EAAQ,MAAQ,GAE1B,SAAUA,IACZ9M,EAAQ,UACN8M,EAAQ,KACRiC,EACA,qBAAqB,EACvB1C,EAAQ0C,EAAajC,EAAQ,IAAI,GAE/B,WAAYA,EACdI,EAAaJ,EAAQ,OAAS,GAE9BI,EAAasC,EACTnD,IAAUgC,IAAuBhC,IAAU+B,GAC7ClB,GAAc,GACLb,IAAUkC,IAAqBlC,IAAUiC,MAClDpB,GAAc,KAIpBgC,EACEE,EACA/W,EACA+T,EACAqD,EACAD,EACAtC,EACAb,CAAK,CACT,CAEA,OAAOuD,CACT,CAEA,OAAAA,EAAa9C,CAAO,EAEpB8C,EAAa,UAAY,WACzBA,EAAa,UAAYR,EACzBQ,EAAa,QAAU,SAAUvX,EAAMkD,EAAQ,CAC7C,OAAAwQ,EAAO,QAAQ1T,EAAMkD,CAAM,EACpBqU,CACT,EACAA,EAAa,QAAU,UAAY,CACjCF,EAAgBN,CAAQ,CAC1B,EAEOQ,CACT,CAEA,MAAO,CACL,OAAQD,EACR,aAAcV,EACd,cAAeE,EACf,YAAa,SAAUC,EAAU,CAC/B,OAAI,OAAOA,GAAa,YACpBA,EAAS,qBAAqBJ,EACzBI,EAAS,UAEX,IACT,EACA,MAAO,UAAY,CACjBhH,GAAOyG,CAAU,EAAE,QAAQa,CAAe,CAC5C,CACF,CACF,CAEA,IAAIG,GAAQ,IAAI,aAAa,CAAC,EAC1BC,GAAM,IAAI,YAAYD,GAAM,MAAM,EAElCE,GAAsB,KAE1B,SAASC,GAAoBhL,EAAO,CAGlC,QAFIiL,EAAUhL,GAAK,UAAU8K,GAAqB/K,EAAM,MAAM,EAErDzN,EAAI,EAAGA,EAAIyN,EAAM,OAAQ,EAAEzN,EAClC,GAAI,MAAMyN,EAAMzN,CAAC,CAAC,EAChB0Y,EAAQ1Y,CAAC,EAAI,cACJyN,EAAMzN,CAAC,IAAM,IACtB0Y,EAAQ1Y,CAAC,EAAI,cACJyN,EAAMzN,CAAC,IAAM,KACtB0Y,EAAQ1Y,CAAC,EAAI,UACR,CACLsY,GAAM,CAAC,EAAI7K,EAAMzN,CAAC,EAClB,IAAIL,EAAI4Y,GAAI,CAAC,EAETI,EAAOhZ,IAAM,IAAO,GACpBiZ,GAAQjZ,GAAK,IAAO,IAAM,IAC1BkZ,EAAQlZ,GAAK,GAAQ,KAEzB,GAAIiZ,EAAM,IAERF,EAAQ1Y,CAAC,EAAI2Y,UACJC,EAAM,IAAK,CAEpB,IAAIE,EAAI,IAAMF,EACdF,EAAQ1Y,CAAC,EAAI2Y,GAAQE,EAAQ,MAAaC,EAC5C,MAAWF,EAAM,GAEfF,EAAQ1Y,CAAC,EAAI2Y,EAAM,MAGnBD,EAAQ1Y,CAAC,EAAI2Y,GAAQC,EAAM,IAAO,IAAMC,CAE5C,CAGF,OAAOH,CACT,CAEA,SAASK,GAAaD,EAAG,CACvB,OAAO,MAAM,QAAQA,CAAC,GAAKpZ,GAAaoZ,CAAC,CAC3C,CAEA,IAAIE,GAAW,SAAUzR,EAAG,CAC1B,MAAO,EAAEA,EAAKA,EAAI,IAAQ,CAAC,CAACA,CAC9B,EAEI0R,GAAgC,MAEhCC,GAAkB,KAClBC,GAAwB,MACxBC,GAAmC,MAEnCC,GAAY,KACZC,GAAW,KACXC,GAAS,KACTC,GAAe,KACfC,GAAqB,KAErBC,GAAW,MACXC,GAAa,MACbC,GAAY,MAEZC,GAA8B,MAC9BC,GAA8B,MAC9BC,GAA4B,MAC5BC,GAA+B,MAE/BC,GAAqB,KACrBC,GAAmB,MAEnBC,GAAc,MACdC,GAAoB,MAEpBC,GAAsB,MAEtBC,GAAkC,MAClCC,GAAmC,MACnCC,GAAmC,MACnCC,GAAmC,MAEnCC,GAA8B,MAC9BC,GAA8C,MAC9CC,GAAkD,MAElDC,GAAqC,MACrCC,GAAqC,MACrCC,GAAsC,MACtCC,GAAsC,MAEtCC,GAA+B,MAE/BC,GAAqB,KACrBC,GAAsB,KACtBC,GAAoB,KACpBC,GAAa,KAEbC,GAAoB,MACpBC,GAAoB,MAEpBC,GAAY,MACZC,GAAqB,MACrBC,GAAqB,MAErBC,GAAwB,MACxBC,GAAwB,MAExBC,GAAe,KACfC,GAAY,KACZC,GAA8B,KAC9BC,GAA6B,KAC7BC,GAA6B,KAC7BC,GAA4B,KAE5BC,GAA0B,MAC1BC,GAAe,KACfC,GAAa,KACbC,GAAY,KAEZC,GAAgC,MAEhCC,GAAsB,KACtBC,GAAyB,MACzBC,GAAoC,MACpCC,GAAwC,MAExCC,GAA2B,MAE3BC,GAAgB,MAEhBC,GAAiB,CACnBf,GACAE,GACAD,GACAE,EACF,EAEIa,GAAkB,CACpB,EACAvD,GACAC,GACAF,GACAF,EACF,EAEI2D,GAAkB,CAAC,EACvBA,GAAgBxD,EAAY,EAC5BwD,GAAgB1D,EAAQ,EACxB0D,GAAgB/C,EAAkB,EAAI,EACtC+C,GAAgB9C,EAAgB,EAChC8C,GAAgBvD,EAAkB,EAAI,EACtCuD,GAAgBzD,EAAM,EACtByD,GAAgB7C,EAAW,EAAI,EAC/B6C,GAAgB3D,EAAS,EACzB2D,GAAgB5C,EAAiB,EAAI,EAErC,SAAS6C,GAAY9c,EAAK,CACxB,MAAO,WAAaA,EAAM,GAC5B,CAEA,IAAI+c,GAAeD,GAAW,mBAAmB,EAC7CE,GAAwBF,GAAW,iBAAiB,EACpDG,GAAkBH,GAAW,0BAA0B,EACvDI,GAAeJ,GAAW,aAAa,EACvCK,GAAcL,GAAW,kBAAkB,EAC3CM,GAAcN,GAAW,kBAAkB,EAE3CO,GAAgB,OAAO,KAAKrL,EAAU,EAAE,OAAO,CACjD+K,GACAC,GACAC,GACAC,GACAC,GACAC,EACF,CAAC,EAIGE,GAAa,CAAC,EAClBA,GAAWvC,EAAkB,EAAI,EACjCuC,GAAWpC,EAAU,EAAI,EACzBoC,GAAWpD,EAAmB,EAAI,EAElCoD,GAAWtC,EAAmB,EAAI,EAClCsC,GAAWrC,EAAiB,EAAI,EAEhC,IAAIsC,GAAuB,CAAC,EAC5BA,GAAqBhE,EAAQ,EAAI,EACjCgE,GAAqB/D,EAAU,EAAI,EACnC+D,GAAqB9D,EAAS,EAAI,EAClC8D,GAAqBxD,EAAgB,EAAI,EAEzCwD,GAAqBpD,EAA+B,EAAI,GACxDoD,GAAqBnD,EAAgC,EAAI,GACzDmD,GAAqBlD,EAAgC,EAAI,EACzDkD,GAAqBjD,EAAgC,EAAI,EAEzDiD,GAAqBhD,EAA2B,EAAI,GACpDgD,GAAqB/C,EAA2C,EAAI,EACpE+C,GAAqB9C,EAA+C,EAAI,EAExE8C,GAAqB7C,EAAkC,EAAI,GAC3D6C,GAAqB5C,EAAkC,EAAI,IAC3D4C,GAAqB3C,EAAmC,EAAI,GAC5D2C,GAAqB1C,EAAmC,EAAI,IAE5D0C,GAAqBzC,EAA4B,EAAI,GAErD,SAAS0C,GAAgBC,EAAK,CAC5B,OACE,MAAM,QAAQA,CAAG,IAChBA,EAAI,SAAW,GAChB,OAAOA,EAAI,CAAC,GAAM,SACtB,CAEA,SAASC,GAAaD,EAAK,CACzB,GAAI,CAAC,MAAM,QAAQA,CAAG,EACpB,MAAO,GAET,IAAIE,EAAQF,EAAI,OAChB,MAAI,EAAAE,IAAU,GAAK,CAAC/E,GAAY6E,EAAI,CAAC,CAAC,EAIxC,CAEA,SAASG,GAAape,EAAG,CACvB,OAAO,OAAO,UAAU,SAAS,KAAKA,CAAC,CACzC,CAEA,SAASqe,GAAiBrZ,EAAQ,CAChC,OAAOoZ,GAAYpZ,CAAM,IAAMuY,EACjC,CAEA,SAASe,GAAmBtZ,EAAQ,CAClC,OAAOoZ,GAAYpZ,CAAM,IAAMwY,EACjC,CAEA,SAASe,GAAavZ,EAAQ,CAC5B,OAAOoZ,GAAYpZ,CAAM,IAAMyY,EACjC,CAEA,SAASe,GAAUxZ,EAAQ,CACzB,OAAOoZ,GAAYpZ,CAAM,IAAM0Y,EACjC,CAEA,SAASe,GAAgBzZ,EAAQ,CAC/B,OAAOoZ,GAAYpZ,CAAM,IAAM2Y,EACjC,CAEA,SAASe,GAAgB1Z,EAAQ,CAC/B,OAAOoZ,GAAYpZ,CAAM,IAAM4Y,EACjC,CAEA,SAASe,GAAa3Z,EAAQ,CAC5B,GAAI,CAACA,EACH,MAAO,GAET,IAAI4Z,EAAYR,GAAYpZ,CAAM,EAClC,OAAI6Y,GAAc,QAAQe,CAAS,GAAK,EAC/B,GAGPZ,GAAehZ,CAAM,GACrBkZ,GAAYlZ,CAAM,GAClBiM,GAAcjM,CAAM,CACxB,CAEA,SAAS6Z,GAAkB1d,EAAM,CAC/B,OAAOqR,GAAW,OAAO,UAAU,SAAS,KAAKrR,CAAI,CAAC,EAAI,CAC5D,CAEA,SAAS2d,GAAatb,EAAQrC,EAAM,CAClC,IAAIa,EAAIb,EAAK,OACb,OAAQqC,EAAO,KAAM,CACnB,KAAK+X,GACL,KAAKC,GACL,KAAKC,GACL,KAAKC,GACH,IAAItF,EAAYrI,GAAK,UAAUvK,EAAO,KAAMxB,CAAC,EAC7CoU,EAAU,IAAIjV,CAAI,EAClBqC,EAAO,KAAO4S,EACd,MAEF,KAAKsE,GACHlX,EAAO,KAAOsV,GAAmB3X,CAAI,EACrC,MAEF,QACE2H,EAAQ,MAAM,sDAAsD,CACxE,CACF,CAEA,SAASiW,GAAYC,EAAOhd,EAAG,CAC7B,OAAO+L,GAAK,UACViR,EAAM,OAAStE,GACXgB,GACAsD,EAAM,KAAMhd,CAAC,CACrB,CAEA,SAASid,GAAaD,EAAO7d,EAAM,CAC7B6d,EAAM,OAAStE,IACjBsE,EAAM,KAAOlG,GAAmB3X,CAAI,EACpC4M,GAAK,SAAS5M,CAAI,GAElB6d,EAAM,KAAO7d,CAEjB,CAEA,SAASsU,GAAeuJ,EAAOlR,EAAOoG,EAASC,EAAS+K,EAAS7a,EAAQ,CAQvE,QAPIuG,EAAIoU,EAAM,MACV/W,EAAI+W,EAAM,OACV9W,EAAI8W,EAAM,SACVhd,EAAI4I,EAAI3C,EAAIC,EACZ/G,EAAO4d,GAAWC,EAAOhd,CAAC,EAE1Bmd,EAAI,EACC9e,EAAI,EAAGA,EAAI4H,EAAG,EAAE5H,EACvB,QAASwI,EAAI,EAAGA,EAAI+B,EAAG,EAAE/B,EACvB,QAASoJ,EAAI,EAAGA,EAAI/J,EAAG,EAAE+J,EACvB9Q,EAAKge,GAAG,EAAIrR,EAAMoG,EAAUrL,EAAIsL,EAAU9T,EAAI6e,EAAUjN,EAAI5N,CAAM,EAKxE4a,GAAYD,EAAO7d,CAAI,CACzB,CAEA,SAASie,GAAgBC,EAAQ/d,EAAM6c,EAAOmB,EAAQC,EAAUC,EAAQ,CACtE,IAAIrG,EAYJ,GAXI,OAAO4E,GAAqBsB,CAAM,GAAM,YAE1ClG,EAAI4E,GAAqBsB,CAAM,EAE/BlG,EAAIkE,GAAgBgC,CAAM,EAAIvB,GAAWxc,CAAI,EAG3Cke,IACFrG,GAAK,GAGHoG,EAAU,CAKZ,QAHIjJ,EAAQ,EAER1L,EAAIuT,EACDvT,GAAK,GAGV0L,GAAS6C,EAAIvO,EAAIA,EACjBA,GAAK,EAEP,OAAO0L,CACT,KACE,QAAO6C,EAAIgF,EAAQmB,CAEvB,CAEA,SAASG,GACP5b,EAAIkI,EAAY/D,EAAQ0X,EAAUC,EAActL,EAAOjI,EAAQ,CAI/D,IAAIwT,EAAa,CACf,aAAcnD,GACd,YAAaA,GACb,KAAQE,GACR,KAAQD,EACV,EAEImD,EAAY,CACd,OAAUhE,GACV,MAASC,GACT,OAAUC,EACZ,EAEI+D,EAAa,CACf,QAAW5D,GACX,OAAUC,EACZ,EAEI4D,EAAa9f,GAAO,CACtB,OAAUsc,GACV,yBAA0BH,GAC1B,wBAAyBC,GACzB,wBAAyBC,GACzB,uBAAwBC,EAC1B,EAAGuD,CAAU,EAETE,EAAa,CACf,KAAQ,EACR,QAAW/C,EACb,EAEIgD,EAAe,CACjB,MAAS1E,GACT,MAASrB,GACT,OAAUE,GACV,UAAWD,EACb,EAEI+F,EAAiB,CACnB,MAASvG,GACT,UAAaE,GACb,kBAAmBC,GACnB,IAAOF,GACP,KAAQF,GACR,MAASK,GACT,UAAWC,GACX,OAAUC,EACZ,EAEIkG,EAA2B,CAAC,EAE5BpU,EAAW,WACbmU,EAAe,KAAO1F,GACtB0F,EAAe,MAAQzF,IAGrB1O,EAAW,oBACbkU,EAAa,QAAUA,EAAa,MAAQvE,IAG1C3P,EAAW,yBACbkU,EAAa,QAAaA,EAAa,YAAY,EAAIvF,IAGrD3O,EAAW,sBACb9L,GAAOigB,EAAgB,CACrB,MAAS5F,GACT,gBAAiBC,EACnB,CAAC,EAEDta,GAAOggB,EAAc,CACnB,OAAUzE,GACV,OAAUC,GACV,gBAAiBpB,EACnB,CAAC,GAGCtO,EAAW,+BACb9L,GAAOkgB,EAA0B,CAC/B,gBAAiBxF,GACjB,iBAAkBC,GAClB,iBAAkBC,GAClB,iBAAkBC,EACpB,CAAC,EAGC/O,EAAW,8BACb9L,GAAOkgB,EAA0B,CAC/B,UAAWpF,GACX,0BAA2BC,GAC3B,8BAA+BC,EACjC,CAAC,EAGClP,EAAW,gCACb9L,GAAOkgB,EAA0B,CAC/B,mBAAoBjF,GACpB,mBAAoBC,GACpB,oBAAqBC,GACrB,oBAAqBC,EACvB,CAAC,EAGCtP,EAAW,gCACboU,EAAyB,UAAU,EAAI7E,IAIzC,IAAI8E,EAA6B,MAAM,UAAU,MAAM,KACrDvc,EAAG,aAAayV,EAA6B,CAAC,EAChD,OAAO,KAAK6G,CAAwB,EAAE,QAAQ,SAAUhV,EAAM,CAC5D,IAAIkU,EAASc,EAAyBhV,CAAI,EACtCiV,EAA2B,QAAQf,CAAM,GAAK,IAChDa,EAAe/U,CAAI,EAAIkU,EAE3B,CAAC,EAED,IAAIgB,EAAmB,OAAO,KAAKH,CAAc,EACjDlY,EAAO,eAAiBqY,EAIxB,IAAIC,EAAuB,CAAC,EAC5B,OAAO,KAAKJ,CAAc,EAAE,QAAQ,SAAUpe,EAAK,CACjD,IAAIye,EAAML,EAAepe,CAAG,EAC5Bwe,EAAqBC,CAAG,EAAIze,CAC9B,CAAC,EAID,IAAI0e,EAAqB,CAAC,EAC1B,OAAO,KAAKP,CAAY,EAAE,QAAQ,SAAUne,EAAK,CAC/C,IAAIye,EAAMN,EAAane,CAAG,EAC1B0e,EAAmBD,CAAG,EAAIze,CAC5B,CAAC,EAED,IAAI2e,EAAmB,CAAC,EACxB,OAAO,KAAKX,CAAU,EAAE,QAAQ,SAAUhe,EAAK,CAC7C,IAAIye,EAAMT,EAAWhe,CAAG,EACxB2e,EAAiBF,CAAG,EAAIze,CAC1B,CAAC,EAED,IAAI4e,EAAmB,CAAC,EACxB,OAAO,KAAKX,CAAU,EAAE,QAAQ,SAAUje,EAAK,CAC7C,IAAIye,EAAMR,EAAWje,CAAG,EACxB4e,EAAiBH,CAAG,EAAIze,CAC1B,CAAC,EAED,IAAI6e,EAAkB,CAAC,EACvB,OAAO,KAAKd,CAAS,EAAE,QAAQ,SAAU/d,EAAK,CAC5C,IAAIye,EAAMV,EAAU/d,CAAG,EACvB6e,EAAgBJ,CAAG,EAAIze,CACzB,CAAC,EAID,IAAI8e,EAAeP,EAAiB,OAAO,SAAUQ,EAAO/e,EAAK,CAC/D,IAAIgf,EAASZ,EAAepe,CAAG,EAC/B,OAAIgf,IAAWjH,IACXiH,IAAWnH,IACXmH,IAAWjH,IACXiH,IAAWhH,IACXgH,IAAWxG,IACXwG,IAAWvG,IACVxO,EAAW,WACH+U,IAAWtG,IACXsG,IAAWrG,IACtBoG,EAAMC,CAAM,EAAIA,EACPA,IAAW9G,IAAclY,EAAI,QAAQ,MAAM,GAAK,EACzD+e,EAAMC,CAAM,EAAIpH,GAEhBmH,EAAMC,CAAM,EAAIlH,GAEXiH,CACT,EAAG,CAAC,CAAC,EAEL,SAASE,GAAY,CAEnB,KAAK,eAAiBrH,GACtB,KAAK,OAASA,GACd,KAAK,KAAO6B,GACZ,KAAK,WAAa,GAGlB,KAAK,iBAAmB,GACxB,KAAK,MAAQ,GACb,KAAK,gBAAkB,EACvB,KAAK,WAAa0B,GAGlB,KAAK,MAAQ,EACb,KAAK,OAAS,EACd,KAAK,SAAW,CAClB,CAEA,SAAS+D,EAAWxd,EAAQyd,EAAO,CACjCzd,EAAO,eAAiByd,EAAM,eAC9Bzd,EAAO,OAASyd,EAAM,OACtBzd,EAAO,KAAOyd,EAAM,KACpBzd,EAAO,WAAayd,EAAM,WAE1Bzd,EAAO,iBAAmByd,EAAM,iBAChCzd,EAAO,MAAQyd,EAAM,MACrBzd,EAAO,gBAAkByd,EAAM,gBAC/Bzd,EAAO,WAAayd,EAAM,WAE1Bzd,EAAO,MAAQyd,EAAM,MACrBzd,EAAO,OAASyd,EAAM,OACtBzd,EAAO,SAAWyd,EAAM,QAC1B,CAEA,SAASC,EAAYC,EAAOvL,EAAS,CACnC,GAAI,SAAOA,GAAY,UAAY,CAACA,GA4BpC,IAxBI,qBAAsBA,IACxB9M,EAAQ,KAAK8M,EAAQ,iBAAkB,UACrC,0BAA0B,EAC5BuL,EAAM,iBAAmBvL,EAAQ,kBAG/B,UAAWA,IACb9M,EAAQ,KAAK8M,EAAQ,MAAO,UAC1B,sBAAsB,EACxBuL,EAAM,MAAQvL,EAAQ,OAGpB,cAAeA,IACjB9M,EAAQ,MAAM8M,EAAQ,UAAW,CAAC,EAAG,EAAG,EAAG,CAAC,EAC1C,kCAAkC,EACpCuL,EAAM,gBAAkBvL,EAAQ,WAG9B,eAAgBA,IAClB9M,EAAQ,UAAU8M,EAAQ,WAAYoK,EACpC,oBAAoB,EACtBmB,EAAM,WAAanB,EAAWpK,EAAQ,UAAU,GAG9C,SAAUA,EAAS,CACrB,IAAItU,EAAOsU,EAAQ,KACnB9M,EAAQiD,EAAW,mBACjB,EAAEzK,IAAS,SAAWA,IAAS,WACjC,0FAA0F,EAC1FwH,EAAQiD,EAAW,wBACjB,EAAEzK,IAAS,cAAgBA,IAAS,WACtC,sGAAsG,EACtGwH,EAAQiD,EAAW,qBACjB,EAAEzK,IAAS,UAAYA,IAAS,UAAYA,IAAS,iBACvD,2FAA2F,EAC3FwH,EAAQ,UAAUxH,EAAM2e,EACtB,sBAAsB,EACxBkB,EAAM,KAAOlB,EAAa3e,CAAI,CAChC,CAEA,IAAIsJ,EAAIuW,EAAM,MACVlZ,GAAIkZ,EAAM,OACVjZ,EAAIiZ,EAAM,SACVC,EAAc,GACd,UAAWxL,GACb9M,EAAQ,MAAM,QAAQ8M,EAAQ,KAAK,GAAKA,EAAQ,MAAM,QAAU,EAC9D,wBAAwB,EAC1BhL,EAAIgL,EAAQ,MAAM,CAAC,EACnB3N,GAAI2N,EAAQ,MAAM,CAAC,EACfA,EAAQ,MAAM,SAAW,IAC3B1N,EAAI0N,EAAQ,MAAM,CAAC,EACnB9M,EAAQZ,EAAI,GAAKA,GAAK,EAAG,4BAA4B,EACrDkZ,EAAc,IAEhBtY,EAAQ8B,GAAK,GAAKA,GAAK5C,EAAO,eAAgB,eAAe,EAC7Dc,EAAQb,IAAK,GAAKA,IAAKD,EAAO,eAAgB,gBAAgB,IAE1D,WAAY4N,IACdhL,EAAI3C,GAAI2N,EAAQ,OAChB9M,EAAQ8B,GAAK,GAAKA,GAAK5C,EAAO,eAAgB,gBAAgB,GAE5D,UAAW4N,IACbhL,EAAIgL,EAAQ,MACZ9M,EAAQ8B,GAAK,GAAKA,GAAK5C,EAAO,eAAgB,eAAe,GAE3D,WAAY4N,IACd3N,GAAI2N,EAAQ,OACZ9M,EAAQb,IAAK,GAAKA,IAAKD,EAAO,eAAgB,gBAAgB,GAE5D,aAAc4N,IAChB1N,EAAI0N,EAAQ,SACZ9M,EAAQZ,EAAI,GAAKA,GAAK,EAAG,4BAA4B,EACrDkZ,EAAc,KAGlBD,EAAM,MAAQvW,EAAI,EAClBuW,EAAM,OAASlZ,GAAI,EACnBkZ,EAAM,SAAWjZ,EAAI,EAErB,IAAImZ,EAAY,GAChB,GAAI,WAAYzL,EAAS,CACvB,IAAI0L,EAAY1L,EAAQ,OACxB9M,EAAQiD,EAAW,qBACjB,EAAEuV,IAAc,SAAWA,IAAc,iBAC3C,2FAA2F,EAC3FxY,EAAQ,UAAUwY,EAAWpB,EAC3B,wBAAwB,EAC1B,IAAIqB,EAAiBJ,EAAM,eAAiBjB,EAAeoB,CAAS,EACpEH,EAAM,OAASP,EAAaW,CAAc,EACtCD,KAAarB,IACT,SAAUrK,IACduL,EAAM,KAAOlB,EAAaqB,CAAS,IAGnCA,KAAanB,IACfgB,EAAM,WAAa,IAErBE,EAAY,EACd,CAGI,CAACD,GAAeC,EAClBF,EAAM,SAAW9D,GAAgB8D,EAAM,MAAM,EACpCC,GAAe,CAACC,EACrBF,EAAM,WAAa/D,GAAgB+D,EAAM,MAAM,IACjDA,EAAM,OAASA,EAAM,eAAiB/D,GAAgB+D,EAAM,QAAQ,GAE7DE,GAAaD,GACtBtY,EACEqY,EAAM,WAAa9D,GAAgB8D,EAAM,MAAM,EAC/C,uDAAuD,EAE7D,CAEA,SAASK,GAAUL,EAAO,CACxBtd,EAAG,YAAYiZ,GAAwBqE,EAAM,KAAK,EAClDtd,EAAG,YAAYkZ,GAAmCoE,EAAM,gBAAgB,EACxEtd,EAAG,YAAYmZ,GAAuCmE,EAAM,UAAU,EACtEtd,EAAG,YAAYgZ,GAAqBsE,EAAM,eAAe,CAC3D,CAKA,SAASM,GAAY,CACnBV,EAAS,KAAK,IAAI,EAElB,KAAK,QAAU,EACf,KAAK,QAAU,EAGf,KAAK,KAAO,KACZ,KAAK,UAAY,GAGjB,KAAK,QAAU,KAGf,KAAK,UAAY,EACnB,CAEA,SAASW,EAAY1C,EAAOpJ,EAAS,CACnC,IAAIzU,EAAO,KAsBX,GArBIwd,GAAY/I,CAAO,EACrBzU,EAAOyU,EACEA,IACT9M,EAAQ,KAAK8M,EAAS,SAAU,yBAAyB,EACzDsL,EAAWlC,EAAOpJ,CAAO,EACrB,MAAOA,IACToJ,EAAM,QAAUpJ,EAAQ,EAAI,GAE1B,MAAOA,IACToJ,EAAM,QAAUpJ,EAAQ,EAAI,GAE1B+I,GAAY/I,EAAQ,IAAI,IAC1BzU,EAAOyU,EAAQ,OAInB9M,EACE,CAACkW,EAAM,YACP7d,aAAgB,WAChB,wDAAwD,EAEtDyU,EAAQ,KAAM,CAChB9M,EAAQ,CAAC3H,EAAM,0DAA0D,EACzE,IAAIwgB,EAAQhC,EAAa,cACrBiC,GAAQjC,EAAa,eACzBX,EAAM,MAAQA,EAAM,OAAU2C,EAAQ3C,EAAM,QAC5CA,EAAM,OAASA,EAAM,QAAW4C,GAAQ5C,EAAM,QAC9CA,EAAM,UAAY,GAClBlW,EAAQkW,EAAM,SAAW,GAAKA,EAAM,QAAU2C,GACxC3C,EAAM,SAAW,GAAKA,EAAM,QAAU4C,IACtC5C,EAAM,MAAQ,GAAKA,EAAM,OAAS2C,GAClC3C,EAAM,OAAS,GAAKA,EAAM,QAAU4C,GAC1C,iCAAiC,CACnC,SAAW,CAACzgB,EACV6d,EAAM,MAAQA,EAAM,OAAS,EAC7BA,EAAM,OAASA,EAAM,QAAU,EAC/BA,EAAM,SAAWA,EAAM,UAAY,UAC1Bjf,GAAaoB,CAAI,EAC1B6d,EAAM,SAAWA,EAAM,UAAY,EACnCA,EAAM,KAAO7d,EACT,EAAE,SAAUyU,IAAYoJ,EAAM,OAASzD,KACzCyD,EAAM,KAAOH,GAAiB1d,CAAI,WAE3B6c,GAAe7c,CAAI,EAC5B6d,EAAM,SAAWA,EAAM,UAAY,EACnCF,GAAYE,EAAO7d,CAAI,EACvB6d,EAAM,UAAY,EAClBA,EAAM,UAAY,WACT/N,GAAc9P,CAAI,EAAG,CAC9B,IAAI2M,EAAQ3M,EAAK,KACb,CAAC,MAAM,QAAQ2M,CAAK,GAAKkR,EAAM,OAASzD,KAC1CyD,EAAM,KAAOH,GAAiB/Q,CAAK,GAErC,IAAIqE,EAAQhR,EAAK,MACbkR,EAASlR,EAAK,OACd6S,EAAQC,EAAQ4N,EAAQ3N,EAASC,EAAS+K,EAC1C/M,EAAM,SAAW,GACnB0P,EAAS1P,EAAM,CAAC,EAChB+M,EAAU7M,EAAO,CAAC,IAElBvJ,EAAQqJ,EAAM,SAAW,EAAG,6CAA6C,EACzE0P,EAAS,EACT3C,EAAU,GAEZlL,EAAS7B,EAAM,CAAC,EAChB8B,EAAS9B,EAAM,CAAC,EAChB+B,EAAU7B,EAAO,CAAC,EAClB8B,EAAU9B,EAAO,CAAC,EAClB2M,EAAM,UAAY,EAClBA,EAAM,MAAQhL,EACdgL,EAAM,OAAS/K,EACf+K,EAAM,SAAW6C,EACjB7C,EAAM,OAASA,EAAM,eAAiB5B,GAAgByE,CAAM,EAC5D7C,EAAM,UAAY,GAClBvJ,GAAcuJ,EAAOlR,EAAOoG,EAASC,EAAS+K,EAAS/d,EAAK,MAAM,CACpE,SAAWkd,GAAgBld,CAAI,GAAKmd,GAAkBnd,CAAI,GAAKod,GAAYpd,CAAI,EACzEkd,GAAgBld,CAAI,GAAKmd,GAAkBnd,CAAI,EACjD6d,EAAM,QAAU7d,EAEhB6d,EAAM,QAAU7d,EAAK,OAEvB6d,EAAM,MAAQA,EAAM,QAAQ,MAC5BA,EAAM,OAASA,EAAM,QAAQ,OAC7BA,EAAM,SAAW,UACRR,GAASrd,CAAI,EACtB6d,EAAM,QAAU7d,EAChB6d,EAAM,MAAQ7d,EAAK,MACnB6d,EAAM,OAAS7d,EAAK,OACpB6d,EAAM,SAAW,UACRP,GAAetd,CAAI,EAC5B6d,EAAM,QAAU7d,EAChB6d,EAAM,MAAQ7d,EAAK,aACnB6d,EAAM,OAAS7d,EAAK,cACpB6d,EAAM,SAAW,UACRN,GAAevd,CAAI,EAC5B6d,EAAM,QAAU7d,EAChB6d,EAAM,MAAQ7d,EAAK,WACnB6d,EAAM,OAAS7d,EAAK,YACpB6d,EAAM,SAAW,UACRd,GAAY/c,CAAI,EAAG,CAC5B,IAAIyJ,EAAIoU,EAAM,OAAS7d,EAAK,CAAC,EAAE,OAC3B8G,EAAI+W,EAAM,QAAU7d,EAAK,OACzB+G,EAAI8W,EAAM,SACV5F,GAAYjY,EAAK,CAAC,EAAE,CAAC,CAAC,EACxB+G,EAAIA,GAAK/G,EAAK,CAAC,EAAE,CAAC,EAAE,OAEpB+G,EAAIA,GAAK,EAIX,QAFIoL,EAAanC,GAAa,MAAMhQ,CAAI,EACpCa,EAAI,EACC8f,EAAK,EAAGA,EAAKxO,EAAW,OAAQ,EAAEwO,EACzC9f,GAAKsR,EAAWwO,CAAE,EAEpB,IAAIC,EAAYhD,GAAWC,EAAOhd,CAAC,EACnCmP,GAAa,QAAQhQ,EAAMmS,EAAY,GAAIyO,CAAS,EACpD9C,GAAYD,EAAO+C,CAAS,EAC5B/C,EAAM,UAAY,EAClBA,EAAM,MAAQpU,EACdoU,EAAM,OAAS/W,EACf+W,EAAM,SAAW9W,EACjB8W,EAAM,OAASA,EAAM,eAAiB5B,GAAgBlV,CAAC,EACvD8W,EAAM,UAAY,EACpB,CAEIA,EAAM,OAAStD,GACjB5S,EAAQd,EAAO,WAAW,QAAQ,mBAAmB,GAAK,EACxD,yCAAyC,EAClCgX,EAAM,OAAStE,IACxB5R,EAAQd,EAAO,WAAW,QAAQ,wBAAwB,GAAK,EAC7D,8CAA8C,CAIpD,CAEA,SAASga,EAAUla,EAAMma,EAAQC,EAAU,CACzC,IAAI3X,EAAUzC,EAAK,QACf3G,GAAO2G,EAAK,KACZyZ,EAAiBzZ,EAAK,eACtBuX,EAASvX,EAAK,OACdxG,EAAOwG,EAAK,KACZqW,EAAQrW,EAAK,MACbwX,EAASxX,EAAK,OAElB0Z,GAAS1Z,CAAI,EAETyC,EACF1G,EAAG,WAAWoe,EAAQC,EAAU7C,EAAQA,EAAQ/d,EAAMiJ,CAAO,EACpDzC,EAAK,WACdjE,EAAG,qBAAqBoe,EAAQC,EAAUX,EAAgBpD,EAAOmB,EAAQ,EAAGne,EAAI,EACvE2G,EAAK,WACd4X,EAAS,EACT7b,EAAG,eACDoe,EAAQC,EAAU7C,EAAQvX,EAAK,QAASA,EAAK,QAASqW,EAAOmB,EAAQ,CAAC,GAExEzb,EAAG,WAAWoe,EAAQC,EAAU7C,EAAQlB,EAAOmB,EAAQ,EAAGD,EAAQ/d,EAAMH,IAAQ,IAAI,CAExF,CAEA,SAASghB,GAAara,EAAMma,EAAQjiB,EAAG8J,EAAGoY,GAAU,CAClD,IAAI3X,EAAUzC,EAAK,QACf3G,EAAO2G,EAAK,KACZyZ,EAAiBzZ,EAAK,eACtBuX,EAASvX,EAAK,OACdxG,EAAOwG,EAAK,KACZqW,EAAQrW,EAAK,MACbwX,EAASxX,EAAK,OAElB0Z,GAAS1Z,CAAI,EAETyC,EACF1G,EAAG,cACDoe,EAAQC,GAAUliB,EAAG8J,EAAGuV,EAAQ/d,EAAMiJ,CAAO,EACtCzC,EAAK,WACdjE,EAAG,wBACDoe,EAAQC,GAAUliB,EAAG8J,EAAGyX,EAAgBpD,EAAOmB,EAAQne,CAAI,EACpD2G,EAAK,WACd4X,EAAS,EACT7b,EAAG,kBACDoe,EAAQC,GAAUliB,EAAG8J,EAAGhC,EAAK,QAASA,EAAK,QAASqW,EAAOmB,CAAM,GAEnEzb,EAAG,cACDoe,EAAQC,GAAUliB,EAAG8J,EAAGqU,EAAOmB,EAAQD,EAAQ/d,EAAMH,CAAI,CAE/D,CAGA,IAAIihB,GAAY,CAAC,EAEjB,SAASC,GAAc,CACrB,OAAOD,GAAU,IAAI,GAAK,IAAIX,CAChC,CAEA,SAASa,GAAWtD,EAAO,CACrBA,EAAM,WACRjR,GAAK,SAASiR,EAAM,IAAI,EAE1ByC,EAAS,KAAKzC,CAAK,EACnBoD,GAAU,KAAKpD,CAAK,CACtB,CAKA,SAASuD,IAAU,CACjBxB,EAAS,KAAK,IAAI,EAElB,KAAK,WAAa,GAClB,KAAK,WAAatE,GAClB,KAAK,QAAU,EACf,KAAK,OAAS,MAAM,EAAE,CACxB,CAEA,SAAS+F,GAAsBC,EAAQtE,EAAOmB,EAAQ,CACpD,IAAIhX,EAAMma,EAAO,OAAO,CAAC,EAAIJ,EAAW,EACxCI,EAAO,QAAU,EACjBna,EAAI,MAAQma,EAAO,MAAQtE,EAC3B7V,EAAI,OAASma,EAAO,OAASnD,EAC7BhX,EAAI,SAAWma,EAAO,SAAW,CACnC,CAEA,SAASC,GAAuBD,EAAQ7M,EAAS,CAC/C,IAAI+M,EAAU,KACd,GAAIhE,GAAY/I,CAAO,EACrB+M,EAAUF,EAAO,OAAO,CAAC,EAAIJ,EAAW,EACxCrB,EAAU2B,EAASF,CAAM,EACzBf,EAAWiB,EAAS/M,CAAO,EAC3B6M,EAAO,QAAU,UAEjBvB,EAAWuB,EAAQ7M,CAAO,EACtB,MAAM,QAAQA,EAAQ,MAAM,EAE9B,QADI7N,EAAU6N,EAAQ,OACbvV,GAAI,EAAGA,GAAI0H,EAAQ,OAAQ,EAAE1H,GACpCsiB,EAAUF,EAAO,OAAOpiB,EAAC,EAAIgiB,EAAW,EACxCrB,EAAU2B,EAASF,CAAM,EACzBE,EAAQ,QAAUtiB,GAClBsiB,EAAQ,SAAWtiB,GACnBqhB,EAAWiB,EAAS5a,EAAQ1H,EAAC,CAAC,EAC9BoiB,EAAO,SAAY,GAAKpiB,QAG1BsiB,EAAUF,EAAO,OAAO,CAAC,EAAIJ,EAAW,EACxCrB,EAAU2B,EAASF,CAAM,EACzBf,EAAWiB,EAAS/M,CAAO,EAC3B6M,EAAO,QAAU,EAGrBzB,EAAUyB,EAAQA,EAAO,OAAO,CAAC,CAAC,EAYhCA,EAAO,aAELA,EAAO,iBAAmB9H,IAC1B8H,EAAO,iBAAmB7H,IAC1B6H,EAAO,iBAAmB5H,IAC1B4H,EAAO,iBAAmB3H,KAG5BhS,EAAQ2Z,EAAO,MAAQ,IAAM,GAAKA,EAAO,OAAS,IAAM,EACtD,oGAAoG,CAE1G,CAEA,SAASG,GAAWH,EAAQR,EAAQ,CAElC,QADIY,EAASJ,EAAO,OACXpiB,EAAI,EAAGA,EAAIwiB,EAAO,OAAQ,EAAExiB,EAAG,CACtC,GAAI,CAACwiB,EAAOxiB,CAAC,EACX,OAEF2hB,EAASa,EAAOxiB,CAAC,EAAG4hB,EAAQ5hB,CAAC,CAC/B,CACF,CAEA,IAAIyiB,GAAU,CAAC,EAEf,SAASC,IAAe,CACtB,IAAIvf,EAASsf,GAAQ,IAAI,GAAK,IAAIP,GAClCxB,EAAS,KAAKvd,CAAM,EACpBA,EAAO,QAAU,EACjB,QAASnD,EAAI,EAAGA,EAAI,GAAI,EAAEA,EACxBmD,EAAO,OAAOnD,CAAC,EAAI,KAErB,OAAOmD,CACT,CAEA,SAASwf,GAAYP,EAAQ,CAE3B,QADII,EAASJ,EAAO,OACXpiB,EAAI,EAAGA,EAAIwiB,EAAO,OAAQ,EAAExiB,EAC/BwiB,EAAOxiB,CAAC,GACViiB,GAAUO,EAAOxiB,CAAC,CAAC,EAErBwiB,EAAOxiB,CAAC,EAAI,KAEdyiB,GAAQ,KAAKL,CAAM,CACrB,CAKA,SAASQ,IAAW,CAClB,KAAK,UAAY/G,GACjB,KAAK,UAAYA,GAEjB,KAAK,MAAQJ,GACb,KAAK,MAAQA,GAEb,KAAK,YAAc,EAEnB,KAAK,WAAa,GAClB,KAAK,WAAaW,EACpB,CAEA,SAASyG,GAAcpb,EAAM8N,EAAS,CACpC,GAAI,QAASA,EAAS,CACpB,IAAIuN,EAAYvN,EAAQ,IACxB9M,EAAQ,UAAUqa,EAAWpD,CAAU,EACvCjY,EAAK,UAAYiY,EAAWoD,CAAS,EACjChG,GAAe,QAAQrV,EAAK,SAAS,GAAK,GAAK,EAAE,UAAW8N,KAC9D9N,EAAK,WAAa,GAEtB,CAEA,GAAI,QAAS8N,EAAS,CACpB,IAAIwN,EAAYxN,EAAQ,IACxB9M,EAAQ,UAAUsa,EAAWtD,CAAU,EACvChY,EAAK,UAAYgY,EAAWsD,CAAS,CACvC,CAEA,IAAIC,GAAQvb,EAAK,MACbwb,EAAQxb,EAAK,MACjB,GAAI,SAAU8N,EAAS,CACrB,IAAI2N,EAAO3N,EAAQ,KACf,OAAO2N,GAAS,UAClBza,EAAQ,UAAUya,EAAM1D,CAAS,EACjCwD,GAAQC,EAAQzD,EAAU0D,CAAI,GACrB,MAAM,QAAQA,CAAI,IAC3Bza,EAAQ,UAAUya,EAAK,CAAC,EAAG1D,CAAS,EACpC/W,EAAQ,UAAUya,EAAK,CAAC,EAAG1D,CAAS,EACpCwD,GAAQxD,EAAU0D,EAAK,CAAC,CAAC,EACzBD,EAAQzD,EAAU0D,EAAK,CAAC,CAAC,EAE7B,KAAO,CACL,GAAI,UAAW3N,EAAS,CACtB,IAAI4N,EAAW5N,EAAQ,MACvB9M,EAAQ,UAAU0a,EAAU3D,CAAS,EACrCwD,GAAQxD,EAAU2D,CAAQ,CAC5B,CACA,GAAI,UAAW5N,EAAS,CACtB,IAAI6N,EAAW7N,EAAQ,MACvB9M,EAAQ,UAAU2a,EAAU5D,CAAS,EACrCyD,EAAQzD,EAAU4D,CAAQ,CAC5B,CACF,CAIA,GAHA3b,EAAK,MAAQub,GACbvb,EAAK,MAAQwb,EAET,gBAAiB1N,EAAS,CAC5B,IAAI8N,EAAc9N,EAAQ,YAC1B9M,EAAQ,OAAO4a,GAAgB,UAC5BA,GAAe,GAAKA,GAAe1b,EAAO,eAC7C,sCAAsC,EACtCF,EAAK,YAAc8N,EAAQ,WAC7B,CAEA,GAAI,WAAYA,EAAS,CACvB,IAAI+N,EAAY,GAChB,OAAQ,OAAO/N,EAAQ,OAAQ,CAC7B,IAAK,SACH9M,EAAQ,UAAU8M,EAAQ,OAAQgK,EAChC,qBAAqB,EACvB9X,EAAK,WAAa8X,EAAWhK,EAAQ,MAAM,EAC3C9N,EAAK,WAAa,GAClB6b,EAAY,GACZ,MAEF,IAAK,UACHA,EAAY7b,EAAK,WAAa8N,EAAQ,OACtC,MAEF,IAAK,SACH9M,EAAQ,MAAM,QAAQ8M,EAAQ,MAAM,EAAG,qBAAqB,EAC5D9N,EAAK,WAAa,GAClB6b,EAAY,GACZ,MAEF,QACE7a,EAAQ,MAAM,qBAAqB,CACvC,CACI6a,GAAa,EAAE,QAAS/N,KAC1B9N,EAAK,UAAYsU,GAErB,CACF,CAEA,SAASwH,GAAY9b,EAAMma,EAAQ,CACjCpe,EAAG,cAAcoe,EAAQhG,GAAuBnU,EAAK,SAAS,EAC9DjE,EAAG,cAAcoe,EAAQjG,GAAuBlU,EAAK,SAAS,EAC9DjE,EAAG,cAAcoe,EAAQtG,GAAmB7T,EAAK,KAAK,EACtDjE,EAAG,cAAcoe,EAAQrG,GAAmB9T,EAAK,KAAK,EAClDiE,EAAW,gCACblI,EAAG,cAAcoe,EAAQrF,GAA+B9U,EAAK,WAAW,EAEtEA,EAAK,aACPjE,EAAG,KAAK2Y,GAAyB1U,EAAK,UAAU,EAChDjE,EAAG,eAAeoe,CAAM,EAE5B,CAKA,IAAI4B,GAAe,EACfC,GAAa,CAAC,EACdC,GAAc/b,EAAO,gBACrBgc,GAAe,MAAMD,EAAW,EAAE,IAAI,UAAY,CACpD,OAAO,IACT,CAAC,EAED,SAASE,GAAahC,EAAQ,CAC5BlB,EAAS,KAAK,IAAI,EAClB,KAAK,QAAU,EACf,KAAK,eAAiBrH,GAEtB,KAAK,GAAKmK,KAEV,KAAK,SAAW,EAEhB,KAAK,OAAS5B,EACd,KAAK,QAAUpe,EAAG,cAAc,EAEhC,KAAK,KAAO,GACZ,KAAK,UAAY,EAEjB,KAAK,QAAU,IAAIof,GAEf7W,EAAO,UACT,KAAK,MAAQ,CAAE,KAAM,CAAE,EAE3B,CAEA,SAAS8X,GAAUzb,EAAS,CAC1B5E,EAAG,cAAcqZ,EAAa,EAC9BrZ,EAAG,YAAY4E,EAAQ,OAAQA,EAAQ,OAAO,CAChD,CAEA,SAAS0b,IAAe,CACtB,IAAIC,EAAOJ,GAAa,CAAC,EACrBI,EACFvgB,EAAG,YAAYugB,EAAK,OAAQA,EAAK,OAAO,EAExCvgB,EAAG,YAAY0V,GAAiB,IAAI,CAExC,CAEA,SAAS7E,GAASjM,EAAS,CACzB,IAAIiN,EAASjN,EAAQ,QACrBK,EAAQ4M,EAAQ,iCAAiC,EACjD,IAAI2O,EAAO5b,EAAQ,KACfwZ,EAASxZ,EAAQ,OACjB4b,GAAQ,IACVxgB,EAAG,cAAcqZ,GAAgBmH,CAAI,EACrCxgB,EAAG,YAAYoe,EAAQ,IAAI,EAC3B+B,GAAaK,CAAI,EAAI,MAEvBxgB,EAAG,cAAc6R,CAAM,EACvBjN,EAAQ,QAAU,KAClBA,EAAQ,OAAS,KACjBA,EAAQ,OAAS,KACjBA,EAAQ,SAAW,EACnB,OAAOqb,GAAWrb,EAAQ,EAAE,EAC5B4L,EAAM,cACR,CAEApU,GAAOgkB,GAAY,UAAW,CAC5B,KAAM,UAAY,CAChB,IAAIxb,EAAU,KACdA,EAAQ,WAAa,EACrB,IAAI4b,EAAO5b,EAAQ,KACnB,GAAI4b,EAAO,EAAG,CACZ,QAAShkB,EAAI,EAAGA,EAAI0jB,GAAa,EAAE1jB,EAAG,CACpC,IAAI4gB,EAAQ+C,GAAa3jB,CAAC,EAC1B,GAAI4gB,EAAO,CACT,GAAIA,EAAM,UAAY,EACpB,SAEFA,EAAM,KAAO,EACf,CACA+C,GAAa3jB,CAAC,EAAIoI,EAClB4b,EAAOhkB,EACP,KACF,CACIgkB,GAAQN,IACVjb,EAAQ,MAAM,sCAAsC,EAElDsD,EAAO,SAAWiI,EAAM,gBAAmBgQ,EAAO,IACpDhQ,EAAM,gBAAkBgQ,EAAO,GAEjC5b,EAAQ,KAAO4b,EACfxgB,EAAG,cAAcqZ,GAAgBmH,CAAI,EACrCxgB,EAAG,YAAY4E,EAAQ,OAAQA,EAAQ,OAAO,CAChD,CACA,OAAO4b,CACT,EAEA,OAAQ,UAAY,CAClB,KAAK,WAAa,CACpB,EAEA,OAAQ,UAAY,CACd,EAAE,KAAK,UAAY,GACrB3P,GAAQ,IAAI,CAEhB,CACF,CAAC,EAED,SAAS4P,GAAiBC,EAAGC,EAAG,CAC9B,IAAI/b,EAAU,IAAIwb,GAAY1K,EAAe,EAC7CuK,GAAWrb,EAAQ,EAAE,EAAIA,EACzB4L,EAAM,eAEN,SAASoQ,EAAeF,EAAGC,EAAG,CAC5B,IAAIE,EAAUjc,EAAQ,QACtBwa,GAAQ,KAAKyB,CAAO,EACpB,IAAI3c,EAAUgb,GAAY,EAE1B,OAAI,OAAOwB,GAAM,SACX,OAAOC,GAAM,SACfhC,GAAqBza,EAASwc,EAAI,EAAGC,EAAI,CAAC,EAE1ChC,GAAqBza,EAASwc,EAAI,EAAGA,EAAI,CAAC,EAEnCA,GACTzb,EAAQ,KAAKyb,EAAG,SAAU,mCAAmC,EAC7DrB,GAAawB,EAASH,CAAC,EACvB7B,GAAsB3a,EAASwc,CAAC,GAGhC/B,GAAqBza,EAAS,EAAG,CAAC,EAGhC2c,EAAQ,aACV3c,EAAQ,SAAWA,EAAQ,OAAS,GAAK,GAE3CU,EAAQ,QAAUV,EAAQ,QAE1BiZ,EAAUvY,EAASV,CAAO,EAE1Be,EAAQ,UAAU4b,EAAS3c,EAASC,CAAM,EAC1CS,EAAQ,eAAiBV,EAAQ,eAEjC0c,EAAc,MAAQ1c,EAAQ,MAC9B0c,EAAc,OAAS1c,EAAQ,OAE/Bmc,GAASzb,CAAO,EAChBma,GAAU7a,EAASwR,EAAe,EAClCqK,GAAWc,EAASnL,EAAe,EACnC4K,GAAY,EAEZnB,GAAWjb,CAAO,EAEdqE,EAAO,UACT3D,EAAQ,MAAM,KAAO2W,GACnB3W,EAAQ,eACRA,EAAQ,KACRV,EAAQ,MACRA,EAAQ,OACR2c,EAAQ,WACR,EAAK,GAETD,EAAc,OAASnE,EAAqB7X,EAAQ,cAAc,EAClEgc,EAAc,KAAOjE,EAAmB/X,EAAQ,IAAI,EAEpDgc,EAAc,IAAMhE,EAAiBiE,EAAQ,SAAS,EACtDD,EAAc,IAAM/D,EAAiBgE,EAAQ,SAAS,EAEtDD,EAAc,MAAQ9D,EAAgB+D,EAAQ,KAAK,EACnDD,EAAc,MAAQ9D,EAAgB+D,EAAQ,KAAK,EAE5CD,CACT,CAEA,SAASE,GAAU3F,EAAO4F,EAAIC,EAAIC,EAAQ,CACxChc,EAAQ,CAAC,CAACkW,EAAO,yBAAyB,EAE1C,IAAIhf,EAAI4kB,EAAK,EACT9a,EAAI+a,EAAK,EACTzS,EAAQ0S,EAAS,EAEjBC,EAAY1C,EAAW,EAC3B,OAAArB,EAAU+D,EAAWtc,CAAO,EAC5Bsc,EAAU,MAAQ,EAClBA,EAAU,OAAS,EACnBrD,EAAWqD,EAAW/F,CAAK,EAC3B+F,EAAU,MAAQA,EAAU,QAAWtc,EAAQ,OAAS2J,GAASpS,EACjE+kB,EAAU,OAASA,EAAU,SAAYtc,EAAQ,QAAU2J,GAAStI,EAEpEhB,EACEL,EAAQ,OAASsc,EAAU,MAC3Btc,EAAQ,SAAWsc,EAAU,QAC7Btc,EAAQ,iBAAmBsc,EAAU,eACrC,0CAA0C,EAC5Cjc,EACE9I,GAAK,GAAK8J,GAAK,GACf9J,EAAI+kB,EAAU,OAAStc,EAAQ,OAC/BqB,EAAIib,EAAU,QAAUtc,EAAQ,OAChC,sCAAsC,EACxCK,EACEL,EAAQ,QAAW,GAAK2J,EACxB,qBAAqB,EACvBtJ,EACEic,EAAU,MAAQA,EAAU,SAAWA,EAAU,UACjD,oBAAoB,EAEtBb,GAASzb,CAAO,EAChB0Z,GAAY4C,EAAWxL,GAAiBvZ,EAAG8J,EAAGsI,CAAK,EACnD+R,GAAY,EAEZ7B,GAAUyC,CAAS,EAEZN,CACT,CAEA,SAAS9Z,EAAQqa,EAAIC,EAAI,CACvB,IAAIra,EAAIoa,EAAK,EACT/c,EAAKgd,EAAK,GAAMra,EACpB,GAAIA,IAAMnC,EAAQ,OAASR,IAAMQ,EAAQ,OACvC,OAAOgc,EAGTA,EAAc,MAAQhc,EAAQ,MAAQmC,EACtC6Z,EAAc,OAAShc,EAAQ,OAASR,EAExCic,GAASzb,CAAO,EAEhB,QAASpI,EAAI,EAAGoI,EAAQ,SAAWpI,EAAG,EAAEA,EAAG,CACzC,IAAI6kB,EAAKta,GAAKvK,EACV8kB,EAAKld,GAAK5H,EACd,GAAI,CAAC6kB,GAAM,CAACC,EAAI,MAChBthB,EAAG,WACD0V,GACAlZ,EACAoI,EAAQ,OACRyc,EACAC,EACA,EACA1c,EAAQ,OACRA,EAAQ,KACR,IAAI,CACR,CACA,OAAA0b,GAAY,EAGR/X,EAAO,UACT3D,EAAQ,MAAM,KAAO2W,GACnB3W,EAAQ,eACRA,EAAQ,KACRmC,EACA3C,EACA,GACA,EAAK,GAGFwc,CACT,CAEA,OAAAA,EAAcF,EAAGC,CAAC,EAElBC,EAAc,SAAWE,GACzBF,EAAc,OAAS9Z,EACvB8Z,EAAc,UAAY,YAC1BA,EAAc,SAAWhc,EACrB2D,EAAO,UACTqY,EAAc,MAAQhc,EAAQ,OAEhCgc,EAAc,QAAU,UAAY,CAClChc,EAAQ,OAAO,CACjB,EAEOgc,CACT,CAEA,SAASW,GAAmBC,EAAIC,EAAIC,EAAIC,EAAIC,GAAIC,EAAI,CAClD,IAAIjd,EAAU,IAAIwb,GAAYzK,EAAqB,EACnDsK,GAAWrb,EAAQ,EAAE,EAAIA,EACzB4L,EAAM,YAEN,IAAI3L,EAAQ,IAAI,MAAM,CAAC,EAEvB,SAASid,EAAiBN,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAI,CAChD,IAAIrlB,EACAqkB,EAAUjc,EAAQ,QAEtB,IADAwa,GAAQ,KAAKyB,CAAO,EACfrkB,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACnBqI,EAAMrI,CAAC,EAAI0iB,GAAY,EAGzB,GAAI,OAAOsC,GAAO,UAAY,CAACA,EAAI,CACjC,IAAIlM,EAAKkM,EAAK,GAAM,EACpB,IAAKhlB,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACnBmiB,GAAqB9Z,EAAMrI,CAAC,EAAG8Y,EAAGA,CAAC,CAEvC,SAAW,OAAOkM,GAAO,SACvB,GAAIC,EACF5C,GAAsBha,EAAM,CAAC,EAAG2c,CAAE,EAClC3C,GAAsBha,EAAM,CAAC,EAAG4c,CAAE,EAClC5C,GAAsBha,EAAM,CAAC,EAAG6c,CAAE,EAClC7C,GAAsBha,EAAM,CAAC,EAAG8c,CAAE,EAClC9C,GAAsBha,EAAM,CAAC,EAAG+c,CAAE,EAClC/C,GAAsBha,EAAM,CAAC,EAAGgd,CAAE,UAElCxC,GAAawB,EAASW,CAAE,EACxBnE,EAAWzY,EAAS4c,CAAE,EAClB,UAAWA,EAAI,CACjB,IAAIO,EAAYP,EAAG,MAGnB,IAFAvc,EAAQ,MAAM,QAAQ8c,CAAS,GAAKA,EAAU,SAAW,EACvD,qCAAqC,EAClCvlB,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACnByI,EAAQ,OAAO8c,EAAUvlB,CAAC,GAAM,UAAY,CAAC,CAACulB,EAAUvlB,CAAC,EACvD,iCAAiC,EACnC2gB,EAAUtY,EAAMrI,CAAC,EAAGoI,CAAO,EAC3Bia,GAAsBha,EAAMrI,CAAC,EAAGulB,EAAUvlB,CAAC,CAAC,CAEhD,KACE,KAAKA,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACnBqiB,GAAsBha,EAAMrI,CAAC,EAAGglB,CAAE,OAKxCvc,EAAQ,MAAM,+BAA+B,EAsB/C,IAnBAkY,EAAUvY,EAASC,EAAM,CAAC,CAAC,EAEtBV,EAAO,iBACVc,EAAQuQ,GAAS5Q,EAAQ,KAAK,GAAK4Q,GAAS5Q,EAAQ,MAAM,EAAG,mEAAmE,EAG9Hic,EAAQ,WACVjc,EAAQ,SAAWC,EAAM,CAAC,EAAE,OAAS,GAAK,EAE1CD,EAAQ,QAAUC,EAAM,CAAC,EAAE,QAG7BI,EAAQ,YAAYL,EAASic,EAAShc,EAAOV,CAAM,EACnDS,EAAQ,eAAiBC,EAAM,CAAC,EAAE,eAElCid,EAAgB,MAAQjd,EAAM,CAAC,EAAE,MACjCid,EAAgB,OAASjd,EAAM,CAAC,EAAE,OAElCwb,GAASzb,CAAO,EACXpI,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACnBuiB,GAAUla,EAAMrI,CAAC,EAAGoZ,GAAmCpZ,CAAC,EAwB1D,IAtBAujB,GAAWc,EAASlL,EAAqB,EACzC2K,GAAY,EAER/X,EAAO,UACT3D,EAAQ,MAAM,KAAO2W,GACnB3W,EAAQ,eACRA,EAAQ,KACRkd,EAAgB,MAChBA,EAAgB,OAChBjB,EAAQ,WACR,EAAI,GAGRiB,EAAgB,OAASrF,EAAqB7X,EAAQ,cAAc,EACpEkd,EAAgB,KAAOnF,EAAmB/X,EAAQ,IAAI,EAEtDkd,EAAgB,IAAMlF,EAAiBiE,EAAQ,SAAS,EACxDiB,EAAgB,IAAMjF,EAAiBgE,EAAQ,SAAS,EAExDiB,EAAgB,MAAQhF,EAAgB+D,EAAQ,KAAK,EACrDiB,EAAgB,MAAQhF,EAAgB+D,EAAQ,KAAK,EAEhDrkB,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACnB2iB,GAAWta,EAAMrI,CAAC,CAAC,EAGrB,OAAOslB,CACT,CAEA,SAAShB,EAAUhc,EAAMqW,EAAO4F,EAAIC,EAAIC,EAAQ,CAC9Chc,EAAQ,CAAC,CAACkW,EAAO,yBAAyB,EAC1ClW,EAAQ,OAAOH,GAAS,UAAYA,KAAUA,EAAO,IACnDA,GAAQ,GAAKA,EAAO,EAAG,cAAc,EAEvC,IAAI3I,EAAI4kB,EAAK,EACT9a,EAAI+a,EAAK,EACTzS,EAAQ0S,EAAS,EAEjBC,EAAY1C,EAAW,EAC3B,OAAArB,EAAU+D,EAAWtc,CAAO,EAC5Bsc,EAAU,MAAQ,EAClBA,EAAU,OAAS,EACnBrD,EAAWqD,EAAW/F,CAAK,EAC3B+F,EAAU,MAAQA,EAAU,QAAWtc,EAAQ,OAAS2J,GAASpS,EACjE+kB,EAAU,OAASA,EAAU,SAAYtc,EAAQ,QAAU2J,GAAStI,EAEpEhB,EACEL,EAAQ,OAASsc,EAAU,MAC3Btc,EAAQ,SAAWsc,EAAU,QAC7Btc,EAAQ,iBAAmBsc,EAAU,eACrC,0CAA0C,EAC5Cjc,EACE9I,GAAK,GAAK8J,GAAK,GACf9J,EAAI+kB,EAAU,OAAStc,EAAQ,OAC/BqB,EAAIib,EAAU,QAAUtc,EAAQ,OAChC,sCAAsC,EACxCK,EACEL,EAAQ,QAAW,GAAK2J,EACxB,qBAAqB,EACvBtJ,EACEic,EAAU,MAAQA,EAAU,SAAWA,EAAU,UACjD,oBAAoB,EAEtBb,GAASzb,CAAO,EAChB0Z,GAAY4C,EAAWtL,GAAmC9Q,EAAM3I,EAAG8J,EAAGsI,CAAK,EAC3E+R,GAAY,EAEZ7B,GAAUyC,CAAS,EAEZY,CACT,CAEA,SAAShb,EAAQkb,EAAS,CACxB,IAAIC,EAASD,EAAU,EACvB,GAAIC,IAAWrd,EAAQ,MAIvB,CAAAkd,EAAgB,MAAQld,EAAQ,MAAQqd,EACxCH,EAAgB,OAASld,EAAQ,OAASqd,EAE1C5B,GAASzb,CAAO,EAChB,QAAS,EAAI,EAAG,EAAI,EAAG,EAAE,EACvB,QAASI,EAAI,EAAGJ,EAAQ,SAAWI,EAAG,EAAEA,EACtChF,EAAG,WACD4V,GAAmC,EACnC5Q,EACAJ,EAAQ,OACRqd,GAAUjd,EACVid,GAAUjd,EACV,EACAJ,EAAQ,OACRA,EAAQ,KACR,IAAI,EAGV,OAAA0b,GAAY,EAER/X,EAAO,UACT3D,EAAQ,MAAM,KAAO2W,GACnB3W,EAAQ,eACRA,EAAQ,KACRkd,EAAgB,MAChBA,EAAgB,OAChB,GACA,EAAI,GAGDA,EACT,CAEA,OAAAA,EAAgBN,EAAIC,EAAIC,EAAIC,EAAIC,GAAIC,CAAE,EAEtCC,EAAgB,SAAWhB,EAC3BgB,EAAgB,OAAShb,EACzBgb,EAAgB,UAAY,cAC5BA,EAAgB,SAAWld,EACvB2D,EAAO,UACTuZ,EAAgB,MAAQld,EAAQ,OAElCkd,EAAgB,QAAU,UAAY,CACpCld,EAAQ,OAAO,CACjB,EAEOkd,CACT,CAGA,SAASI,IAAmB,CAC1B,QAAS1lB,EAAI,EAAGA,EAAI0jB,GAAa,EAAE1jB,EACjCwD,EAAG,cAAcqZ,GAAgB7c,CAAC,EAClCwD,EAAG,YAAY0V,GAAiB,IAAI,EACpCyK,GAAa3jB,CAAC,EAAI,KAEpB6Q,GAAO4S,EAAU,EAAE,QAAQpP,EAAO,EAElCL,EAAM,UAAY,EAClBA,EAAM,aAAe,CACvB,CAEIjI,EAAO,UACTiI,EAAM,oBAAsB,UAAY,CACtC,IAAIiC,EAAQ,EACZ,cAAO,KAAKwN,EAAU,EAAE,QAAQ,SAAUhiB,EAAK,CAC7CwU,GAASwN,GAAWhiB,CAAG,EAAE,MAAM,IACjC,CAAC,EACMwU,CACT,GAGF,SAAS0P,IAAmB,CAC1B,QAAS3lB,EAAI,EAAGA,EAAI0jB,GAAa,EAAE1jB,EAAG,CACpC,IAAI4lB,EAAMjC,GAAa3jB,CAAC,EACpB4lB,IACFA,EAAI,UAAY,EAChBA,EAAI,KAAO,GACXjC,GAAa3jB,CAAC,EAAI,KAEtB,CAEA6Q,GAAO4S,EAAU,EAAE,QAAQ,SAAUrb,EAAS,CAC5CA,EAAQ,QAAU5E,EAAG,cAAc,EACnCA,EAAG,YAAY4E,EAAQ,OAAQA,EAAQ,OAAO,EAC9C,QAASpI,EAAI,EAAGA,EAAI,GAAI,EAAEA,EACxB,GAAKoI,EAAQ,QAAW,GAAKpI,EAG7B,GAAIoI,EAAQ,SAAW8Q,GACrB1V,EAAG,WAAW0V,GACZlZ,EACAoI,EAAQ,eACRA,EAAQ,OAASpI,EACjBoI,EAAQ,QAAUpI,EAClB,EACAoI,EAAQ,eACRA,EAAQ,KACR,IAAI,MAEN,SAASI,GAAI,EAAGA,GAAI,EAAG,EAAEA,GACvBhF,EAAG,WAAW4V,GAAmC5Q,GAC/CxI,EACAoI,EAAQ,eACRA,EAAQ,OAASpI,EACjBoI,EAAQ,QAAUpI,EAClB,EACAoI,EAAQ,eACRA,EAAQ,KACR,IAAI,EAIZmb,GAAWnb,EAAQ,QAASA,EAAQ,MAAM,CAC5C,CAAC,CACH,CAEA,SAASyd,IAAmB,CAC1B,QAAS7lB,EAAI,EAAGA,EAAI0jB,GAAa,EAAE1jB,EAAG,CACpC,IAAI4lB,EAAMjC,GAAa3jB,CAAC,EACpB4lB,IACFA,EAAI,UAAY,EAChBA,EAAI,KAAO,GACXjC,GAAa3jB,CAAC,EAAI,MAEpBwD,EAAG,cAAcqZ,GAAgB7c,CAAC,EAClCwD,EAAG,YAAY0V,GAAiB,IAAI,EACpC1V,EAAG,YAAY2V,GAAuB,IAAI,CAC5C,CACF,CAEA,MAAO,CACL,SAAU8K,GACV,WAAYc,GACZ,MAAOW,GACP,WAAY,SAAUxP,EAAS,CAC7B,OAAO,IACT,EACA,QAASyP,GACT,QAASE,EACX,CACF,CAEA,IAAIC,GAAkB,MAElBC,GAAa,MACbC,GAAe,MACfC,GAAc,MACdC,GAAuB,MACvBC,GAAoB,MACpBC,GAAqB,MAErBC,GAAsB,MAEtBC,GAAiB,MAEjBC,GAAiB,MACjBC,GAAgB,MAEhBC,GAAe,CAAC,EAEpBA,GAAaV,EAAU,EAAI,EAC3BU,GAAaT,EAAY,EAAI,EAC7BS,GAAaR,EAAW,EAAI,EAE5BQ,GAAaP,EAAoB,EAAI,EACrCO,GAAaN,EAAiB,EAAI,EAClCM,GAAaL,EAAkB,EAAI,EAEnCK,GAAaJ,EAAmB,EAAI,EACpCI,GAAaH,EAAc,EAAI,GAC/BG,GAAaF,EAAc,EAAI,EAC/BE,GAAaD,EAAa,EAAI,EAE9B,SAASE,GAAqB1H,EAAQlB,EAAOmB,EAAQ,CACnD,OAAOwH,GAAazH,CAAM,EAAIlB,EAAQmB,CACxC,CAEA,IAAI0H,GAAoB,SAAUnjB,EAAIkI,EAAY/D,EAAQqM,EAAOjI,EAAQ,CACvE,IAAI6a,EAAc,CAChB,MAASb,GACT,OAAUE,GACV,UAAWD,GACX,MAASE,GACT,QAAWC,GACX,gBAAiBC,EACnB,EAEI1a,EAAW,WACbkb,EAAY,MAAWP,IAGrB3a,EAAW,8BACbkb,EAAY,QAAaL,GACzBK,EAAY,OAAYJ,IAGtB9a,EAAW,2BACbkb,EAAY,QAAaN,IAG3B,IAAIO,EAAoB,CAAC,EACzB,OAAO,KAAKD,CAAW,EAAE,QAAQ,SAAUnlB,EAAK,CAC9C,IAAIye,EAAM0G,EAAYnlB,CAAG,EACzBolB,EAAkB3G,CAAG,EAAIze,CAC3B,CAAC,EAED,IAAIqlB,EAAoB,EACpBC,EAAkB,CAAC,EAEvB,SAASC,EAAkBC,EAAc,CACvC,KAAK,GAAKH,IACV,KAAK,SAAW,EAEhB,KAAK,aAAeG,EAEpB,KAAK,OAASlB,GACd,KAAK,MAAQ,EACb,KAAK,OAAS,EAEVha,EAAO,UACT,KAAK,MAAQ,CAAE,KAAM,CAAE,EAE3B,CAEAib,EAAiB,UAAU,OAAS,UAAY,CAC1C,EAAE,KAAK,UAAY,GACrB3S,EAAQ,IAAI,CAEhB,EAEA,SAASA,EAAS6S,EAAI,CACpB,IAAI7R,EAAS6R,EAAG,aAChBze,EAAQ4M,EAAQ,sCAAsC,EACtD7R,EAAG,iBAAiBsiB,GAAiB,IAAI,EACzCtiB,EAAG,mBAAmB6R,CAAM,EAC5B6R,EAAG,aAAe,KAClBA,EAAG,SAAW,EACd,OAAOH,EAAgBG,EAAG,EAAE,EAC5BlT,EAAM,mBACR,CAEA,SAASmT,EAAoBjD,EAAGC,EAAG,CACjC,IAAI8C,EAAe,IAAID,EAAiBxjB,EAAG,mBAAmB,CAAC,EAC/DujB,EAAgBE,EAAa,EAAE,EAAIA,EACnCjT,EAAM,oBAEN,SAASoT,EAAkBlD,EAAGC,EAAG,CAC/B,IAAI5Z,EAAI,EACJ3C,EAAI,EACJoX,EAAS+G,GAEb,GAAI,OAAO7B,GAAM,UAAYA,EAAG,CAC9B,IAAI3O,EAAU2O,EACd,GAAI,UAAW3O,EAAS,CACtB,IAAIzD,EAAQyD,EAAQ,MACpB9M,EAAQ,MAAM,QAAQqJ,CAAK,GAAKA,EAAM,QAAU,EAC9C,4BAA4B,EAC9BvH,EAAIuH,EAAM,CAAC,EAAI,EACflK,EAAIkK,EAAM,CAAC,EAAI,CACjB,KACM,WAAYyD,IACdhL,EAAI3C,EAAI2N,EAAQ,OAAS,GAEvB,UAAWA,IACbhL,EAAIgL,EAAQ,MAAQ,GAElB,WAAYA,IACd3N,EAAI2N,EAAQ,OAAS,GAGrB,WAAYA,IACd9M,EAAQ,UAAU8M,EAAQ,OAAQqR,EAChC,6BAA6B,EAC/B5H,EAAS4H,EAAYrR,EAAQ,MAAM,EAEvC,MAAW,OAAO2O,GAAM,UACtB3Z,EAAI2Z,EAAI,EACJ,OAAOC,GAAM,SACfvc,EAAIuc,EAAI,EAERvc,EAAI2C,GAEI2Z,EAGVzb,EAAQ,MAAM,+CAA+C,EAF7D8B,EAAI3C,EAAI,EAWV,GALAa,EACE8B,EAAI,GAAK3C,EAAI,GACb2C,GAAK5C,EAAO,qBAAuBC,GAAKD,EAAO,oBAC/C,2BAA2B,EAEzB,EAAA4C,IAAM0c,EAAa,OACnBrf,IAAMqf,EAAa,QACnBjI,IAAWiI,EAAa,QAI5B,OAAAG,EAAiB,MAAQH,EAAa,MAAQ1c,EAC9C6c,EAAiB,OAASH,EAAa,OAASrf,EAChDqf,EAAa,OAASjI,EAEtBxb,EAAG,iBAAiBsiB,GAAiBmB,EAAa,YAAY,EAC9DzjB,EAAG,oBAAoBsiB,GAAiB9G,EAAQzU,EAAG3C,CAAC,EAEpDa,EACEjF,EAAG,SAAS,IAAM,EAClB,8BAA8B,EAE5BuI,EAAO,UACTkb,EAAa,MAAM,KAAOP,GAAoBO,EAAa,OAAQA,EAAa,MAAOA,EAAa,MAAM,GAE5GG,EAAiB,OAASP,EAAkBI,EAAa,MAAM,EAExDG,CACT,CAEA,SAAS9c,EAAQqa,EAAIC,EAAI,CACvB,IAAIra,EAAIoa,EAAK,EACT/c,EAAKgd,EAAK,GAAMra,EAEpB,OAAIA,IAAM0c,EAAa,OAASrf,IAAMqf,EAAa,SAKnDxe,EACE8B,EAAI,GAAK3C,EAAI,GACb2C,GAAK5C,EAAO,qBAAuBC,GAAKD,EAAO,oBAC/C,2BAA2B,EAE7Byf,EAAiB,MAAQH,EAAa,MAAQ1c,EAC9C6c,EAAiB,OAASH,EAAa,OAASrf,EAEhDpE,EAAG,iBAAiBsiB,GAAiBmB,EAAa,YAAY,EAC9DzjB,EAAG,oBAAoBsiB,GAAiBmB,EAAa,OAAQ1c,EAAG3C,CAAC,EAEjEa,EACEjF,EAAG,SAAS,IAAM,EAClB,8BAA8B,EAG5BuI,EAAO,UACTkb,EAAa,MAAM,KAAOP,GACxBO,EAAa,OAAQA,EAAa,MAAOA,EAAa,MAAM,IAGzDG,CACT,CAEA,OAAAA,EAAiBlD,EAAGC,CAAC,EAErBiD,EAAiB,OAAS9c,EAC1B8c,EAAiB,UAAY,eAC7BA,EAAiB,cAAgBH,EAC7Blb,EAAO,UACTqb,EAAiB,MAAQH,EAAa,OAExCG,EAAiB,QAAU,UAAY,CACrCH,EAAa,OAAO,CACtB,EAEOG,CACT,CAEIrb,EAAO,UACTiI,EAAM,yBAA2B,UAAY,CAC3C,IAAIiC,EAAQ,EACZ,cAAO,KAAK8Q,CAAe,EAAE,QAAQ,SAAUtlB,EAAK,CAClDwU,GAAS8Q,EAAgBtlB,CAAG,EAAE,MAAM,IACtC,CAAC,EACMwU,CACT,GAGF,SAASoR,GAAwB,CAC/BxW,GAAOkW,CAAe,EAAE,QAAQ,SAAUG,EAAI,CAC5CA,EAAG,aAAe1jB,EAAG,mBAAmB,EACxCA,EAAG,iBAAiBsiB,GAAiBoB,EAAG,YAAY,EACpD1jB,EAAG,oBAAoBsiB,GAAiBoB,EAAG,OAAQA,EAAG,MAAOA,EAAG,MAAM,CACxE,CAAC,EACD1jB,EAAG,iBAAiBsiB,GAAiB,IAAI,CAC3C,CAEA,MAAO,CACL,OAAQqB,EACR,MAAO,UAAY,CACjBtW,GAAOkW,CAAe,EAAE,QAAQ1S,CAAO,CACzC,EACA,QAASgT,CACX,CACF,EAGIC,GAAmB,MACnBC,GAAoB,MAEpBC,GAAkB,KAClBC,GAAmC,MAEnCC,GAAyB,MACzBC,GAAsB,MACtBC,GAAwB,MACxBC,GAA8B,MAE9BC,GAA4B,MAC5BC,GAAuC,MACvCC,GAA+C,MAC/CC,GAAuC,MACvCC,GAA6B,MAE7BC,GAAsB,MACtBC,GAAqB,KACrBC,GAAa,KAEbC,GAAW,KACXC,GAAY,KAEZC,GAAuB,KAEvBC,GAA0B,CAC5BH,GACAC,EACF,EAIIG,GAAwB,CAAC,EAC7BA,GAAsBH,EAAS,EAAI,EACnCG,GAAsBJ,EAAQ,EAAI,EAIlC,IAAIK,GAAmB,CAAC,EACxBA,GAAiBP,EAAkB,EAAI,EACvCO,GAAiBN,EAAU,EAAI,EAC/BM,GAAiBR,EAAmB,EAAI,EAExC,IAAIS,GAAa,MACbC,GAAe,MACfC,GAAc,MACdC,GAAyB,MACzBC,GAAsB,MACtBC,GAAqB,MAErBC,GAAwB,MAExBC,GAAmB,MAEnBC,GAAmB,MACnBC,GAAkB,MAElBC,GAA+B,CACjCV,GACAC,GACAC,GACAI,GACAE,GACAC,GACAF,EACF,EAEII,GAAa,CAAC,EAClBA,GAAWzB,EAAyB,EAAI,WACxCyB,GAAWxB,EAAoC,EAAI,wBACnDwB,GAAWtB,EAAoC,EAAI,wBACnDsB,GAAWvB,EAA4C,EAAI,iCAC3DuB,GAAWrB,EAA0B,EAAI,cAEzC,SAASsB,GACPhmB,EACAkI,EACA/D,EACA8hB,EACAC,EACA1V,EAAO,CACP,IAAI2V,EAAmB,CACrB,IAAK,KACL,KAAM,KACN,MAAO,GACP,OAAQ,IACV,EAEIC,EAAsB,CAAC,MAAM,EAC7BC,EAA2B,CAAC,QAAS,SAAU,SAAS,EAExDne,EAAW,UACbme,EAAyB,KAAK,OAAO,EAGnCne,EAAW,6BACbme,EAAyB,KAAK,UAAW,QAAQ,EAG/Cne,EAAW,0BACbme,EAAyB,KAAK,SAAS,EAGzC,IAAIC,EAAa,CAAC,OAAO,EACrBpe,EAAW,wBACboe,EAAW,KAAK,aAAc,SAAS,EAErCpe,EAAW,mBACboe,EAAW,KAAK,QAAS,SAAS,EAGpC,SAASC,EAAuBnI,EAAQxZ,EAAS6e,EAAc,CAC7D,KAAK,OAASrF,EACd,KAAK,QAAUxZ,EACf,KAAK,aAAe6e,EAEpB,IAAI1c,GAAI,EACJ3C,GAAI,EACJQ,GACFmC,GAAInC,EAAQ,MACZR,GAAIQ,EAAQ,QACH6e,IACT1c,GAAI0c,EAAa,MACjBrf,GAAIqf,EAAa,QAEnB,KAAK,MAAQ1c,GACb,KAAK,OAAS3C,EAChB,CAEA,SAASoiB,EAAQlkB,EAAY,CACvBA,IACEA,EAAW,SACbA,EAAW,QAAQ,SAAS,OAAO,EAEjCA,EAAW,cACbA,EAAW,aAAa,cAAc,OAAO,EAGnD,CAEA,SAASmkB,EAAqBnkB,EAAYgY,EAAOmB,EAAQ,CACvD,GAAKnZ,EAGL,GAAIA,EAAW,QAAS,CACtB,IAAIsC,GAAUtC,EAAW,QAAQ,SAC7BokB,GAAK,KAAK,IAAI,EAAG9hB,GAAQ,KAAK,EAC9B+hB,EAAK,KAAK,IAAI,EAAG/hB,GAAQ,MAAM,EACnCK,EAAQyhB,KAAOpM,GAASqM,IAAOlL,EAC7B,gDAAgD,EAClD7W,GAAQ,UAAY,CACtB,KAAO,CACL,IAAI6e,GAAenhB,EAAW,aAAa,cAC3C2C,EACEwe,GAAa,QAAUnJ,GAASmJ,GAAa,SAAWhI,EACxD,4CAA4C,EAC9CgI,GAAa,UAAY,CAC3B,CACF,CAEA,SAASmD,EAAQC,EAAUvkB,EAAY,CACjCA,IACEA,EAAW,QACbtC,EAAG,qBACD8jB,GACA+C,EACAvkB,EAAW,OACXA,EAAW,QAAQ,SAAS,QAC5B,CAAC,EAEHtC,EAAG,wBACD8jB,GACA+C,EACA9C,GACAzhB,EAAW,aAAa,cAAc,YAAY,EAG1D,CAEA,SAASwkB,EAAiBxkB,EAAY,CACpC,IAAI8b,EAAS4F,GACTpf,EAAU,KACV6e,GAAe,KAEfnmB,GAAOgF,EACP,OAAOA,GAAe,WACxBhF,GAAOgF,EAAW,KACd,WAAYA,IACd8b,EAAS9b,EAAW,OAAS,IAIjC2C,EAAQ,KAAK3H,GAAM,WAAY,yBAAyB,EAExD,IAAIG,EAAOH,GAAK,UAChB,OAAIG,IAAS,aACXmH,EAAUtH,GACV2H,EAAQmZ,IAAW4F,EAAe,GACzBvmB,IAAS,eAClBmH,EAAUtH,GACV2H,EACEmZ,GAAU6F,IACV7F,EAAS6F,GAAmC,EAC5C,yBAAyB,GAClBxmB,IAAS,gBAClBgmB,GAAenmB,GACf8gB,EAAS2F,IAET9e,EAAQ,MAAM,oCAAoC,EAG7C,IAAIshB,EAAsBnI,EAAQxZ,EAAS6e,EAAY,CAChE,CAEA,SAASsD,EACPzM,EACAmB,EACAuL,EACAxL,GACA/d,GAAM,CACN,GAAIupB,EAAW,CACb,IAAIpiB,EAAUqhB,EAAa,SAAS,CAClC,MAAO3L,EACP,OAAQmB,EACR,OAAQD,GACR,KAAM/d,EACR,CAAC,EACD,OAAAmH,EAAQ,SAAS,SAAW,EACrB,IAAI2hB,EAAsBvC,GAAiBpf,EAAS,IAAI,CACjE,KAAO,CACL,IAAI8e,GAAKwC,EAAkB,OAAO,CAChC,MAAO5L,EACP,OAAQmB,EACR,OAAQD,EACV,CAAC,EACD,OAAAkI,GAAG,cAAc,SAAW,EACrB,IAAI6C,EAAsBxC,GAAmB,KAAML,EAAE,CAC9D,CACF,CAEA,SAASuD,EAAkB3kB,EAAY,CACrC,OAAOA,IAAeA,EAAW,SAAWA,EAAW,aACzD,CAEA,SAAS4kB,EAAkB5kB,EAAYyE,EAAG3C,EAAG,CACvC9B,IACEA,EAAW,QACbA,EAAW,QAAQ,OAAOyE,EAAG3C,CAAC,EACrB9B,EAAW,cACpBA,EAAW,aAAa,OAAOyE,EAAG3C,CAAC,EAErC9B,EAAW,MAAQyE,EACnBzE,EAAW,OAAS8B,EAExB,CAEA,IAAI+iB,EAAmB,EACnBC,EAAiB,CAAC,EAEtB,SAASC,GAAmB,CAC1B,KAAK,GAAKF,IACVC,EAAe,KAAK,EAAE,EAAI,KAE1B,KAAK,YAAcpnB,EAAG,kBAAkB,EACxC,KAAK,MAAQ,EACb,KAAK,OAAS,EAEd,KAAK,iBAAmB,CAAC,EACzB,KAAK,gBAAkB,KACvB,KAAK,kBAAoB,KACzB,KAAK,uBAAyB,IAChC,CAEA,SAASsnB,EAAYC,EAAa,CAChCA,EAAY,iBAAiB,QAAQf,CAAM,EAC3CA,EAAOe,EAAY,eAAe,EAClCf,EAAOe,EAAY,iBAAiB,EACpCf,EAAOe,EAAY,sBAAsB,CAC3C,CAEA,SAAS1W,EAAS0W,EAAa,CAC7B,IAAI1V,EAAS0V,EAAY,YACzBtiB,EAAQ4M,EAAQ,qCAAqC,EACrD7R,EAAG,kBAAkB6R,CAAM,EAC3B0V,EAAY,YAAc,KAC1B/W,EAAM,mBACN,OAAO4W,EAAeG,EAAY,EAAE,CACtC,CAEA,SAASC,EAAmBD,EAAa,CACvC,IAAI/qB,EAEJwD,EAAG,gBAAgB8jB,GAAkByD,EAAY,WAAW,EAC5D,IAAIE,EAAmBF,EAAY,iBACnC,IAAK/qB,EAAI,EAAGA,EAAIirB,EAAiB,OAAQ,EAAEjrB,EACzCoqB,EAAO1C,GAAyB1nB,EAAGirB,EAAiBjrB,CAAC,CAAC,EAExD,IAAKA,EAAIirB,EAAiB,OAAQjrB,EAAI2H,EAAO,oBAAqB,EAAE3H,EAClEwD,EAAG,qBACD8jB,GACAI,GAAyB1nB,EACzBwnB,GACA,KACA,CAAC,EAGLhkB,EAAG,qBACD8jB,GACAO,GACAL,GACA,KACA,CAAC,EACHhkB,EAAG,qBACD8jB,GACAK,GACAH,GACA,KACA,CAAC,EACHhkB,EAAG,qBACD8jB,GACAM,GACAJ,GACA,KACA,CAAC,EAEH4C,EAAOzC,GAAqBoD,EAAY,eAAe,EACvDX,EAAOxC,GAAuBmD,EAAY,iBAAiB,EAC3DX,EAAOvC,GAA6BkD,EAAY,sBAAsB,EAGtE,IAAIG,GAAS1nB,EAAG,uBAAuB8jB,EAAgB,EACnD,CAAC9jB,EAAG,cAAc,GAAK0nB,KAAWpD,IACpCrf,EAAQ,MAAM,qDACZ8gB,GAAW2B,EAAM,CAAC,EAGtB1nB,EAAG,gBAAgB8jB,GAAkBqC,EAAiB,KAAOA,EAAiB,KAAK,YAAc,IAAI,EACrGA,EAAiB,IAAMA,EAAiB,KAIxCnmB,EAAG,SAAS,CACd,CAEA,SAAS2nB,EAAWnG,EAAIC,EAAI,CAC1B,IAAI8F,EAAc,IAAIF,EACtB7W,EAAM,mBAEN,SAASoX,GAAiBlH,EAAGC,GAAG,CAC9B,IAAInkB,GAEJyI,EAAQkhB,EAAiB,OAASoB,EAChC,sDAAsD,EAExD,IAAIjN,GAAQ,EACRmB,GAAS,EAEToM,GAAa,GACbC,GAAe,GAEfC,GAAc,KACdC,GAAe,GACfC,GAAc,OACdC,GAAY,QACZC,GAAa,EAEbC,GAAc,KACdC,GAAgB,KAChBC,GAAqB,KACrBC,GAAsB,GAE1B,GAAI,OAAO7H,GAAM,SACfpG,GAAQoG,EAAI,EACZjF,GAAUkF,GAAI,GAAMrG,WACX,CAACoG,EACVpG,GAAQmB,GAAS,MACZ,CACLxW,EAAQ,KAAKyb,EAAG,SAAU,mCAAmC,EAC7D,IAAI3O,GAAU2O,EAEd,GAAI,UAAW3O,GAAS,CACtB,IAAIzD,GAAQyD,GAAQ,MACpB9M,EAAQ,MAAM,QAAQqJ,EAAK,GAAKA,GAAM,QAAU,EAC9C,+BAA+B,EACjCgM,GAAQhM,GAAM,CAAC,EACfmN,GAASnN,GAAM,CAAC,CAClB,KACM,WAAYyD,KACduI,GAAQmB,GAAS1J,GAAQ,QAEvB,UAAWA,KACbuI,GAAQvI,GAAQ,OAEd,WAAYA,KACd0J,GAAS1J,GAAQ,SAIjB,UAAWA,IACX,WAAYA,MACdgW,GACEhW,GAAQ,OACRA,GAAQ,OACN,MAAM,QAAQgW,EAAW,GAC3B9iB,EACE8iB,GAAY,SAAW,GAAK7f,EAAW,mBACvC,uCAAuC,GAIxC6f,KACC,eAAgBhW,KAClBoW,GAAapW,GAAQ,WAAa,EAClC9M,EAAQkjB,GAAa,EAAG,4BAA4B,GAGlD,iBAAkBpW,KACpBiW,GAAe,CAAC,CAACjW,GAAQ,aACzBkW,GAAc,SAGZ,cAAelW,KACjBmW,GAAYnW,GAAQ,UACfiW,IAWH/iB,EAAQiD,EAAW,mBACjB,EAAEggB,KAAc,SAAWA,KAAc,WAC3C,sFAAsF,EACtFjjB,EAAQiD,EAAW,wBACjB,EAAEggB,KAAc,cAAgBA,KAAc,WAChD,kGAAkG,GAf9FA,KAAc,cAAgBA,KAAc,WAC9CjjB,EAAQiD,EAAW,4BACjB,0EAA0E,EAC5E+f,GAAc,YACLC,KAAc,SAAWA,KAAc,aAChDjjB,EAAQiD,EAAW,yBACjB,8FAA8F,EAChG+f,GAAc,WAUlBhjB,EAAQ,MAAMijB,GAAW5B,EAAY,oBAAoB,GAGvD,gBAAiBvU,KACnBkW,GAAclW,GAAQ,YAClBqU,EAAoB,QAAQ6B,EAAW,GAAK,EAC9CD,GAAe,GACN3B,EAAyB,QAAQ4B,EAAW,GAAK,EAC1DD,GAAe,GAEXA,GACF/iB,EAAQ,MACN8M,GAAQ,YAAaqU,EACrB,kCAAkC,EAEpCnhB,EAAQ,MACN8M,GAAQ,YAAasU,EACrB,uCAAuC,KAM7C,iBAAkBtU,IAAW,wBAAyBA,MACxDwW,GAAsB,CAAC,EAAExW,GAAQ,cAC/BA,GAAQ,qBACV9M,EAAQ,CAACsjB,IAAuBrgB,EAAW,oBACzC,6CAA6C,GAG7C,UAAW6J,KACT,OAAOA,GAAQ,OAAU,UAC3B8V,GAAa9V,GAAQ,OAErBqW,GAAcrW,GAAQ,MACtB+V,GAAe,KAIf,YAAa/V,KACX,OAAOA,GAAQ,SAAY,UAC7B+V,GAAe/V,GAAQ,SAEvBsW,GAAgBtW,GAAQ,QACxB8V,GAAa,KAIb,iBAAkB9V,KAChB,OAAOA,GAAQ,cAAiB,UAClC8V,GAAaC,GAAe/V,GAAQ,cAEpCuW,GAAqBvW,GAAQ,aAC7B8V,GAAa,GACbC,GAAe,IAGrB,CAGA,IAAIL,GAAmB,KACnBe,GAAkB,KAClBC,GAAoB,KACpBC,GAAyB,KAG7B,GAAI,MAAM,QAAQX,EAAW,EAC3BN,GAAmBM,GAAY,IAAIjB,CAAe,UACzCiB,GACTN,GAAmB,CAACX,EAAgBiB,EAAW,CAAC,MAGhD,KADAN,GAAmB,IAAI,MAAMU,EAAU,EAClC3rB,GAAI,EAAGA,GAAI2rB,GAAY,EAAE3rB,GAC5BirB,GAAiBjrB,EAAC,EAAIuqB,EACpBzM,GACAmB,GACAuM,GACAC,GACAC,EAAS,EAIfjjB,EAAQiD,EAAW,oBAAsBuf,GAAiB,QAAU,EAClE,0FAA0F,EAC5FxiB,EAAQwiB,GAAiB,QAAUtjB,EAAO,oBACxC,2CAA2C,EAE7CmW,GAAQA,IAASmN,GAAiB,CAAC,EAAE,MACrChM,GAASA,IAAUgM,GAAiB,CAAC,EAAE,OAEnCW,GACFI,GAAkB1B,EAAgBsB,EAAW,EACpCP,IAAc,CAACC,KACxBU,GAAkBzB,EAChBzM,GACAmB,GACA8M,GACA,QACA,QAAQ,GAGRF,GACFI,GAAoB3B,EAAgBuB,EAAa,EACxCP,IAAgB,CAACD,KAC1BY,GAAoB1B,EAClBzM,GACAmB,GACA,GACA,UACA,OAAO,GAGP6M,GACFI,GAAyB5B,EAAgBwB,EAAkB,EAClD,CAACF,IAAe,CAACC,IAAiBP,IAAgBD,KAC3Da,GAAyB3B,EACvBzM,GACAmB,GACA8M,GACA,gBACA,eAAe,GAGnBtjB,EACG,CAAC,CAACmjB,GAAgB,CAAC,CAACC,GAAkB,CAAC,CAACC,IAAuB,EAChE,qFAAqF,EAEvF,IAAIK,GAA4B,KAEhC,IAAKnsB,GAAI,EAAGA,GAAIirB,GAAiB,OAAQ,EAAEjrB,GASzC,GARAiqB,EAAoBgB,GAAiBjrB,EAAC,EAAG8d,GAAOmB,EAAM,EACtDxW,EAAQ,CAACwiB,GAAiBjrB,EAAC,GACxBirB,GAAiBjrB,EAAC,EAAE,SACnByoB,GAAwB,QAAQwC,GAAiBjrB,EAAC,EAAE,QAAQ,SAAS,MAAM,GAAK,GACjFirB,GAAiBjrB,EAAC,EAAE,cACnBspB,GAA6B,QAAQ2B,GAAiBjrB,EAAC,EAAE,aAAa,cAAc,MAAM,GAAK,EACnG,gCAAkCA,GAAI,aAAa,EAE/CirB,GAAiBjrB,EAAC,GAAKirB,GAAiBjrB,EAAC,EAAE,QAAS,CACtD,IAAIosB,GACA1D,GAAsBuC,GAAiBjrB,EAAC,EAAE,QAAQ,SAAS,MAAM,EACjE2oB,GAAiBsC,GAAiBjrB,EAAC,EAAE,QAAQ,SAAS,IAAI,EAE1DmsB,KAA8B,KAChCA,GAA4BC,GAK5B3jB,EAAQ0jB,KAA8BC,GACpC,oEAAoE,CAE1E,CAEF,OAAAnC,EAAoB+B,GAAiBlO,GAAOmB,EAAM,EAClDxW,EAAQ,CAACujB,IACNA,GAAgB,SACfA,GAAgB,QAAQ,SAAS,SAAWxD,IAC7CwD,GAAgB,cACfA,GAAgB,aAAa,cAAc,SAAWjD,GAC1D,iDAAiD,EACjDkB,EAAoBgC,GAAmBnO,GAAOmB,EAAM,EACpDxW,EAAQ,CAACwjB,IACNA,GAAkB,cACjBA,GAAkB,aAAa,cAAc,SAAWjD,GAC5D,mDAAmD,EACnDiB,EAAoBiC,GAAwBpO,GAAOmB,EAAM,EACzDxW,EAAQ,CAACyjB,IACNA,GAAuB,SACtBA,GAAuB,QAAQ,SAAS,SAAWjD,IACpDiD,GAAuB,cACtBA,GAAuB,aAAa,cAAc,SAAWjD,GACjE,yDAAyD,EAGzD6B,EAAWC,CAAW,EAEtBA,EAAY,MAAQjN,GACpBiN,EAAY,OAAS9L,GAErB8L,EAAY,iBAAmBE,GAC/BF,EAAY,gBAAkBiB,GAC9BjB,EAAY,kBAAoBkB,GAChClB,EAAY,uBAAyBmB,GAErCd,GAAgB,MAAQH,GAAiB,IAAIR,CAAgB,EAC7DW,GAAgB,MAAQX,EAAiBuB,EAAe,EACxDZ,GAAgB,QAAUX,EAAiBwB,EAAiB,EAC5Db,GAAgB,aAAeX,EAAiByB,EAAsB,EAEtEd,GAAgB,MAAQL,EAAY,MACpCK,GAAgB,OAASL,EAAY,OAErCC,EAAkBD,CAAW,EAEtBK,EACT,CAEA,SAAS9gB,GAAQqa,EAAIC,GAAI,CACvBnc,EAAQkhB,EAAiB,OAASoB,EAChC,wDAAwD,EAE1D,IAAIxgB,GAAI,KAAK,IAAIoa,EAAK,EAAG,CAAC,EACtB/c,GAAI,KAAK,IAAKgd,GAAK,GAAMra,GAAG,CAAC,EACjC,GAAIA,KAAMwgB,EAAY,OAASnjB,KAAMmjB,EAAY,OAC/C,OAAOK,GAKT,QADIH,GAAmBF,EAAY,iBAC1B/qB,GAAI,EAAGA,GAAIirB,GAAiB,OAAQ,EAAEjrB,GAC7C0qB,EAAiBO,GAAiBjrB,EAAC,EAAGuK,GAAG3C,EAAC,EAE5C,OAAA8iB,EAAiBK,EAAY,gBAAiBxgB,GAAG3C,EAAC,EAClD8iB,EAAiBK,EAAY,kBAAmBxgB,GAAG3C,EAAC,EACpD8iB,EAAiBK,EAAY,uBAAwBxgB,GAAG3C,EAAC,EAEzDmjB,EAAY,MAAQK,GAAgB,MAAQ7gB,GAC5CwgB,EAAY,OAASK,GAAgB,OAASxjB,GAE9CojB,EAAkBD,CAAW,EAEtBK,EACT,CAEA,OAAAA,GAAgBpG,EAAIC,CAAE,EAEfrlB,GAAOwrB,GAAiB,CAC7B,OAAQ9gB,GACR,UAAW,cACX,aAAcygB,EACd,QAAS,UAAY,CACnB1W,EAAQ0W,CAAW,EACnBD,EAAWC,CAAW,CACxB,EACA,IAAK,SAAUnlB,EAAO,CACpB+jB,EAAiB,OAAO,CACtB,YAAayB,EACf,EAAGxlB,CAAK,CACV,CACF,CAAC,CACH,CAEA,SAASymB,EAAe9W,EAAS,CAC/B,IAAIlN,EAAQ,MAAM,CAAC,EAEnB,SAASikB,EAAqBpI,GAAG,CAC/B,IAAIlkB,EAEJyI,EAAQJ,EAAM,QAAQshB,EAAiB,IAAI,EAAI,EAC7C,sDAAsD,EAExD,IAAI4C,GAAS,CACX,MAAO,IACT,EAEI9G,GAAS,EAET8F,GAAc,KACdE,GAAc,OACdC,GAAY,QACZC,GAAa,EAEjB,GAAI,OAAOzH,IAAM,SACfuB,GAASvB,GAAI,UACJ,CAACA,GACVuB,GAAS,MACJ,CACLhd,EAAQ,KAAKyb,GAAG,SAAU,mCAAmC,EAC7D,IAAI3O,GAAU2O,GAEd,GAAI,UAAW3O,GAAS,CACtB,IAAIzD,GAAQyD,GAAQ,MACpB9M,EACE,MAAM,QAAQqJ,EAAK,GAAKA,GAAM,QAAU,EACxC,+BAA+B,EACjCrJ,EACEqJ,GAAM,CAAC,IAAMA,GAAM,CAAC,EACpB,iCAAiC,EACnC2T,GAAS3T,GAAM,CAAC,CAClB,KACM,WAAYyD,KACdkQ,GAASlQ,GAAQ,OAAS,GAExB,UAAWA,IACbkQ,GAASlQ,GAAQ,MAAQ,EACrB,WAAYA,IACd9M,EAAQ8M,GAAQ,SAAWkQ,GAAQ,gBAAgB,GAE5C,WAAYlQ,KACrBkQ,GAASlQ,GAAQ,OAAS,IAI1B,UAAWA,IACX,WAAYA,MACdgW,GACEhW,GAAQ,OACRA,GAAQ,OACN,MAAM,QAAQgW,EAAW,GAC3B9iB,EACE8iB,GAAY,SAAW,GAAK7f,EAAW,mBACvC,uCAAuC,GAIxC6f,KACC,eAAgBhW,KAClBoW,GAAapW,GAAQ,WAAa,EAClC9M,EAAQkjB,GAAa,EAAG,4BAA4B,GAGlD,cAAepW,KACjB9M,EAAQ,MACN8M,GAAQ,UAAWuU,EACnB,oBAAoB,EACtB4B,GAAYnW,GAAQ,WAGlB,gBAAiBA,KACnBkW,GAAclW,GAAQ,YACtB9M,EAAQ,MACN8M,GAAQ,YAAaqU,EACrB,kCAAkC,IAIpC,UAAWrU,KACbgX,GAAO,MAAQhX,GAAQ,OAGrB,YAAaA,KACfgX,GAAO,QAAUhX,GAAQ,SAGvB,iBAAkBA,KACpBgX,GAAO,aAAehX,GAAQ,aAElC,CAEA,IAAIiX,GACJ,GAAIjB,GACF,GAAI,MAAM,QAAQA,EAAW,EAE3B,IADAiB,GAAa,CAAC,EACTxsB,EAAI,EAAGA,EAAIurB,GAAY,OAAQ,EAAEvrB,EACpCwsB,GAAWxsB,CAAC,EAAIurB,GAAYvrB,CAAC,OAG/BwsB,GAAa,CAAEjB,EAAY,MAExB,CACLiB,GAAa,MAAMb,EAAU,EAC7B,IAAIc,GAAgB,CAClB,OAAQhH,GACR,OAAQgG,GACR,KAAMC,EACR,EACA,IAAK1rB,EAAI,EAAGA,EAAI2rB,GAAY,EAAE3rB,EAC5BwsB,GAAWxsB,CAAC,EAAIypB,EAAa,WAAWgD,EAAa,CAEzD,CAIA,IADAF,GAAO,MAAQ,MAAMC,GAAW,MAAM,EACjCxsB,EAAI,EAAGA,EAAIwsB,GAAW,OAAQ,EAAExsB,EAAG,CACtC,IAAI0sB,GAAOF,GAAWxsB,CAAC,EACvByI,EACE,OAAOikB,IAAS,YAAcA,GAAK,YAAc,cACjD,kBAAkB,EACpBjH,GAASA,IAAUiH,GAAK,MACxBjkB,EACEikB,GAAK,QAAUjH,IAAUiH,GAAK,SAAWjH,GACzC,wBAAwB,EAC1B8G,GAAO,MAAMvsB,CAAC,EAAI,CAChB,OAAQynB,GACR,KAAM+E,GAAWxsB,CAAC,CACpB,CACF,CAEA,IAAKA,EAAI,EAAGA,EAAI,EAAG,EAAEA,EAAG,CACtB,QAASwI,GAAI,EAAGA,GAAIgkB,GAAW,OAAQ,EAAEhkB,GACvC+jB,GAAO,MAAM/jB,EAAC,EAAE,OAASif,GAAmCznB,EAG1DA,EAAI,IACNusB,GAAO,MAAQlkB,EAAM,CAAC,EAAE,MACxBkkB,GAAO,QAAUlkB,EAAM,CAAC,EAAE,QAC1BkkB,GAAO,aAAelkB,EAAM,CAAC,EAAE,cAE7BA,EAAMrI,CAAC,EACRqI,EAAMrI,CAAC,EAAGusB,EAAM,EAEjBlkB,EAAMrI,CAAC,EAAImrB,EAAUoB,EAAM,CAE/B,CAEA,OAAO3sB,GAAO0sB,EAAqB,CACjC,MAAO7G,GACP,OAAQA,GACR,MAAO+G,EACT,CAAC,CACH,CAEA,SAASliB,GAAQkb,GAAS,CACxB,IAAIxlB,EACAylB,GAASD,GAAU,EAIvB,GAHA/c,EAAQgd,GAAS,GAAKA,IAAU9d,EAAO,eACrC,6BAA6B,EAE3B8d,KAAW6G,EAAoB,MACjC,OAAOA,EAGT,IAAIK,GAASL,EAAoB,MACjC,IAAKtsB,EAAI,EAAGA,EAAI2sB,GAAO,OAAQ,EAAE3sB,EAC/B2sB,GAAO3sB,CAAC,EAAE,OAAOylB,EAAM,EAGzB,IAAKzlB,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACnBqI,EAAMrI,CAAC,EAAE,OAAOylB,EAAM,EAGxB,OAAA6G,EAAoB,MAAQA,EAAoB,OAAS7G,GAElD6G,CACT,CAEA,OAAAA,EAAoB/W,CAAO,EAEpB3V,GAAO0sB,EAAqB,CACjC,MAAOjkB,EACP,OAAQiC,GACR,UAAW,kBACX,QAAS,UAAY,CACnBjC,EAAM,QAAQ,SAAU+D,GAAG,CACzBA,GAAE,QAAQ,CACZ,CAAC,CACH,CACF,CAAC,CACH,CAEA,SAASwgB,IAAuB,CAC9BjD,EAAiB,IAAM,KACvBA,EAAiB,KAAO,KACxBA,EAAiB,MAAQ,GACzB9Y,GAAO+Z,CAAc,EAAE,QAAQ,SAAUiC,EAAI,CAC3CA,EAAG,YAAcrpB,EAAG,kBAAkB,EACtCwnB,EAAkB6B,CAAE,CACtB,CAAC,CACH,CAEA,OAAOjtB,GAAO+pB,EAAkB,CAC9B,eAAgB,SAAUhlB,EAAQ,CAChC,GAAI,OAAOA,GAAW,YAAcA,EAAO,YAAc,cAAe,CACtE,IAAI4L,EAAM5L,EAAO,aACjB,GAAI4L,aAAesa,EACjB,OAAOta,CAEX,CACA,OAAO,IACT,EACA,OAAQ4a,EACR,WAAYkB,EACZ,MAAO,UAAY,CACjBxb,GAAO+Z,CAAc,EAAE,QAAQvW,CAAO,CACxC,EACA,QAASuY,EACX,CAAC,CACH,CAEA,IAAIE,GAAa,KACbC,GAAoB,MAExB,SAASC,IAAmB,CAC1B,KAAK,MAAQ,EAEb,KAAK,EAAI,EACT,KAAK,EAAI,EACT,KAAK,EAAI,EACT,KAAK,EAAI,EAET,KAAK,OAAS,KACd,KAAK,KAAO,EACZ,KAAK,WAAa,GAClB,KAAK,KAAOF,GACZ,KAAK,OAAS,EACd,KAAK,OAAS,EACd,KAAK,QAAU,CACjB,CAEA,SAASG,GACPzpB,EACAkI,EACA/D,EACAqM,EACAqD,EAAa,CAGb,QAFI6V,EAAiBvlB,EAAO,cACxBwlB,EAAoB,IAAI,MAAMD,CAAc,EACvCltB,EAAI,EAAGA,EAAIktB,EAAgB,EAAEltB,EACpCmtB,EAAkBntB,CAAC,EAAI,IAAIgtB,GAE7B,IAAII,EAAW,EACXC,EAAS,CAAC,EAEVC,EAAQ,CACV,OAAQN,GACR,MAAO,CAAC,EACR,MAAOG,EACP,WAAY,KACZ,UAAW,KACX,QAASI,EAAO,EAAIC,EAAa,UAAY,CAAC,EAC9C,UAAWC,EACX,OAAQC,EACR,cAAezZ,EACf,OAAQsZ,EAAO,EAAII,EAAYC,EAC/B,MAAOL,EAAO,EAAIM,EAAgB,UAAY,CAAC,CACjD,EAEA,SAAS5Z,EAAeO,EAAQ,CAC9B,QAASxU,EAAI,EAAGA,EAAImtB,EAAkB,OAAQ,EAAEntB,EAAG,CACjD,IAAI8tB,EAASX,EAAkBntB,CAAC,EAC5B8tB,EAAO,SAAWtZ,IACpBhR,EAAG,yBAAyBxD,CAAC,EAC7B8tB,EAAO,OAAS,KAEpB,CACF,CAEA,SAASP,GAAU,CACjB,OAAO7hB,EAAW,uBACpB,CAEA,SAASqiB,GAAgB,CACvB,OAAOriB,EAAW,sBACpB,CAEA,SAASgiB,EAAQM,EAAK,CACpB,OAAI,OAAOA,GAAQ,YAAcA,EAAI,KAC5BA,EAAI,KAEN,IACT,CAEA,SAASL,EAAWK,EAAK,CACvB,GAAIA,IAAQV,EAAM,WAGlB,KAAIphB,EAAMqhB,EAAO,EACbS,EACF9hB,EAAI,mBAAmB8hB,EAAI,GAAG,EAE9B9hB,EAAI,mBAAmB,IAAI,EAE7BohB,EAAM,WAAaU,EACrB,CAEA,SAASJ,EAAgBI,EAAK,CAC5B,GAAIA,IAAQV,EAAM,WAGlB,IAAIU,EACFA,EAAI,UAAU,MAGd,SADIC,EAAOF,EAAa,EACf/tB,EAAI,EAAGA,EAAImtB,EAAkB,OAAQ,EAAEntB,EAAG,CACjD,IAAIkuB,EAAUf,EAAkBntB,CAAC,EAC7BkuB,EAAQ,QACV1qB,EAAG,wBAAwBxD,CAAC,EAC5BwD,EAAG,oBAAoBxD,EAAGkuB,EAAQ,KAAMA,EAAQ,KAAMA,EAAQ,WAAYA,EAAQ,OAAQA,EAAQ,OAAO,EACrGD,GAAQC,EAAQ,SAClBD,EAAK,yBAAyBjuB,EAAGkuB,EAAQ,OAAO,IAGlD1qB,EAAG,yBAAyBxD,CAAC,EAC7BwD,EAAG,eAAexD,EAAGkuB,EAAQ,EAAGA,EAAQ,EAAGA,EAAQ,EAAGA,EAAQ,CAAC,EAEnE,CAEFZ,EAAM,WAAaU,EACrB,CAEA,SAASH,GAAiB,CACxBhd,GAAOwc,CAAM,EAAE,QAAQ,SAAUW,EAAK,CACpCA,EAAI,QAAQ,CACd,CAAC,CACH,CAEA,SAASG,GAAW,CAClB,KAAK,GAAK,EAAEf,EACZ,KAAK,WAAa,CAAC,EACnB,IAAIgB,EAAYb,EAAO,EACnBa,EACF,KAAK,IAAMA,EAAU,qBAAqB,EAE1C,KAAK,IAAM,KAEbf,EAAO,KAAK,EAAE,EAAI,KAClB,KAAK,QAAU,CAAC,CAClB,CAEAc,EAAQ,UAAU,UAAY,UAAY,CAGxC,QAFIF,EAAOF,EAAa,EACpBjpB,EAAa,KAAK,WACb9E,EAAI,EAAGA,EAAI8E,EAAW,OAAQ,EAAE9E,EAAG,CAC1C,IAAIquB,EAAOvpB,EAAW9E,CAAC,EACnBquB,EAAK,QACP7qB,EAAG,wBAAwBxD,CAAC,EAC5BwD,EAAG,WAAWupB,GAAmBsB,EAAK,OAAO,MAAM,EACnD7qB,EAAG,oBAAoBxD,EAAGquB,EAAK,KAAMA,EAAK,KAAMA,EAAK,WAAYA,EAAK,OAAQA,EAAK,MAAM,EACrFJ,GAAQI,EAAK,SACfJ,EAAK,yBAAyBjuB,EAAGquB,EAAK,OAAO,IAG/C7qB,EAAG,yBAAyBxD,CAAC,EAC7BwD,EAAG,eAAexD,EAAGquB,EAAK,EAAGA,EAAK,EAAGA,EAAK,EAAGA,EAAK,CAAC,EAEvD,CACA,QAAS7lB,EAAI1D,EAAW,OAAQ0D,EAAI0kB,EAAgB,EAAE1kB,EACpDhF,EAAG,yBAAyBgF,CAAC,CAEjC,EAEA2lB,EAAQ,UAAU,QAAU,UAAY,CACtC,IAAIjiB,EAAMqhB,EAAO,EACbrhB,IACFA,EAAI,mBAAmB,KAAK,GAAG,EAC/B,KAAK,UAAU,EACfohB,EAAM,WAAa,KAEvB,EAEAa,EAAQ,UAAU,QAAU,UAAY,CACtC,GAAI,KAAK,IAAK,CACZ,IAAIC,EAAYb,EAAO,EACnB,OAASD,EAAM,aACjBA,EAAM,WAAa,KACnBc,EAAU,mBAAmB,IAAI,GAEnCA,EAAU,qBAAqB,KAAK,GAAG,EACvC,KAAK,IAAM,IACb,CACIf,EAAO,KAAK,EAAE,IAChB,OAAOA,EAAO,KAAK,EAAE,EACrBrZ,EAAM,UAAY,EAEtB,EAEA,SAASwZ,GAAc,CACrB,IAAIthB,EAAMqhB,EAAO,EACbrhB,GACF2E,GAAOwc,CAAM,EAAE,QAAQ,SAAUW,EAAK,CACpCA,EAAI,QAAQ,CACd,CAAC,CAEL,CAEA,SAASP,EAAWa,EAAO,CACzB,IAAIN,EAAM,IAAIG,EACdna,EAAM,UAAY,EAElB,SAASua,EAAWzpB,EAAY,CAC9B2D,EAAQ,MAAM,QAAQ3D,CAAU,EAAG,wDAAwD,EAC3F2D,EAAQ3D,EAAW,OAASooB,EAAgB,qBAAqB,EACjEzkB,EAAQ3D,EAAW,OAAS,EAAG,qCAAqC,EAEpE,IAAI0pB,EAAa,CAAC,EACdC,GAAcT,EAAI,WACtBS,GAAY,OAAS3pB,EAAW,OAChC,QAAS9E,EAAI,EAAGA,EAAI8E,EAAW,OAAQ,EAAE9E,EAAG,CAC1C,IAAI0uB,EAAO5pB,EAAW9E,CAAC,EACnB2uB,EAAMF,GAAYzuB,CAAC,EAAI,IAAIgtB,GAC3BlsB,GAAO4tB,EAAK,MAAQA,EACxB,GAAI,MAAM,QAAQ5tB,EAAI,GAAKpB,GAAaoB,EAAI,GAAK8P,GAAc9P,EAAI,EAAG,CACpE,IAAIwM,GACA0gB,EAAI,QAAQhuB,CAAC,IACfsN,GAAM0gB,EAAI,QAAQhuB,CAAC,EACfN,GAAaoB,EAAI,GAAKwM,GAAI,QAAQ,YAAcxM,GAAK,WACvDwM,GAAI,QAAQxM,EAAI,GAEhBwM,GAAI,QAAQ,EACZ0gB,EAAI,QAAQhuB,CAAC,EAAI,OAGhBguB,EAAI,QAAQhuB,CAAC,IAChBsN,GAAM0gB,EAAI,QAAQhuB,CAAC,EAAIqX,EAAY,OAAOqX,EAAM3B,GAAmB,GAAO,EAAI,GAEhF4B,EAAI,OAAStX,EAAY,UAAU/J,EAAG,EACtCqhB,EAAI,KAAOA,EAAI,OAAO,UAAY,EAClCA,EAAI,WAAa,GACjBA,EAAI,KAAOA,EAAI,OAAO,MACtBA,EAAI,OAAS,EACbA,EAAI,OAAS,EACbA,EAAI,QAAU,EACdA,EAAI,MAAQ,EACZH,EAAWxuB,CAAC,EAAI,CAClB,MAAWqX,EAAY,UAAUqX,CAAI,GACnCC,EAAI,OAAStX,EAAY,UAAUqX,CAAI,EACvCC,EAAI,KAAOA,EAAI,OAAO,UAAY,EAClCA,EAAI,WAAa,GACjBA,EAAI,KAAOA,EAAI,OAAO,MACtBA,EAAI,OAAS,EACbA,EAAI,OAAS,EACbA,EAAI,QAAU,EACdA,EAAI,MAAQ,GACHtX,EAAY,UAAUqX,EAAK,MAAM,GAC1CC,EAAI,OAAStX,EAAY,UAAUqX,EAAK,MAAM,EAC9CC,EAAI,MAAS,CAACD,EAAK,MAASC,EAAI,OAAO,WAAa,EACpDA,EAAI,WAAa,CAAC,CAACD,EAAK,YAAc,GAClC,SAAUA,GACZjmB,EAAQ,UAAUimB,EAAK,KAAM9b,GAAS,qBAAqB,EAC3D+b,EAAI,KAAO/b,GAAQ8b,EAAK,IAAI,GAE5BC,EAAI,KAAOA,EAAI,OAAO,MAExBA,EAAI,QAAUD,EAAK,QAAU,GAAK,EAClCC,EAAI,QAAUD,EAAK,QAAU,GAAK,EAClCC,EAAI,SAAWD,EAAK,SAAW,GAAK,EACpCC,EAAI,MAAQ,EAEZlmB,EAAQkmB,EAAI,MAAQ,GAAKA,EAAI,MAAQ,EAAG,8BAA8B,EACtElmB,EAAQkmB,EAAI,QAAU,EAAG,gBAAgB,EACzClmB,EAAQkmB,EAAI,QAAU,GAAKA,EAAI,QAAU,IAAK,kCAAkC,EAChFlmB,EAAQkmB,EAAI,SAAW,EAAG,0BAA0B,EACpDlmB,EAAQ,CAACkmB,EAAI,SAAW,CAAC,CAACjjB,EAAW,uBAAwB,uDAAuD,GAC3G,MAAOgjB,GAChBjmB,EAAQzI,EAAI,EAAG,wCAAwC,EACvD2uB,EAAI,EAAI,CAACD,EAAK,GAAK,EACnBC,EAAI,EAAI,CAACD,EAAK,GAAK,EACnBC,EAAI,EAAI,CAACD,EAAK,GAAK,EACnBC,EAAI,EAAI,CAACD,EAAK,GAAK,EACnBC,EAAI,MAAQ,GAEZlmB,EAAQ,GAAO,uCAAyCzI,CAAC,CAE7D,CAGA,QAASwI,EAAI,EAAGA,EAAIwlB,EAAI,QAAQ,OAAQ,EAAExlB,EACpC,CAACgmB,EAAWhmB,CAAC,GAAKwlB,EAAI,QAAQxlB,CAAC,IACjCwlB,EAAI,QAAQxlB,CAAC,EAAE,QAAQ,EACvBwlB,EAAI,QAAQxlB,CAAC,EAAI,MAIrB,OAAAwlB,EAAI,QAAQ,EACLO,CACT,CAEA,OAAAA,EAAU,QAAU,UAAY,CAC9B,QAAS/lB,EAAI,EAAGA,EAAIwlB,EAAI,QAAQ,OAAQ,EAAExlB,EACpCwlB,EAAI,QAAQxlB,CAAC,GACfwlB,EAAI,QAAQxlB,CAAC,EAAE,QAAQ,EAG3BwlB,EAAI,QAAQ,OAAS,EACrBA,EAAI,QAAQ,CACd,EAEAO,EAAU,KAAOP,EACjBO,EAAU,UAAY,MAEfA,EAAUD,CAAK,CACxB,CAEA,OAAOhB,CACT,CAEA,IAAIsB,GAAqB,MACrBC,GAAmB,MAEnBC,GAAqB,MACrBC,GAAuB,MAE3B,SAASC,GAAiBxrB,EAAIuB,EAAaiP,EAAOjI,EAAQ,CAIxD,IAAIkjB,EAAc,CAAC,EACfC,EAAc,CAAC,EAEnB,SAASC,EAAYrkB,EAAM9F,EAAIqlB,EAAU5iB,EAAM,CAC7C,KAAK,KAAOqD,EACZ,KAAK,GAAK9F,EACV,KAAK,SAAWqlB,EAChB,KAAK,KAAO5iB,CACd,CAEA,SAAS2nB,EAAkB/tB,EAAMoG,EAAM,CACrC,QAASzH,EAAI,EAAGA,EAAIqB,EAAK,OAAQ,EAAErB,EACjC,GAAIqB,EAAKrB,CAAC,EAAE,KAAOyH,EAAK,GAAI,CAC1BpG,EAAKrB,CAAC,EAAE,SAAWyH,EAAK,SACxB,MACF,CAEFpG,EAAK,KAAKoG,CAAI,CAChB,CAEA,SAAS4nB,EAAWpuB,EAAM+D,EAAItC,EAAS,CACrC,IAAI4sB,EAAQruB,IAAS2tB,GAAqBK,EAAcC,EACpDzrB,EAAS6rB,EAAMtqB,CAAE,EAErB,GAAI,CAACvB,EAAQ,CACX,IAAIhB,EAASsC,EAAY,IAAIC,CAAE,EAC/BvB,EAASD,EAAG,aAAavC,CAAI,EAC7BuC,EAAG,aAAaC,EAAQhB,CAAM,EAC9Be,EAAG,cAAcC,CAAM,EACvBgF,EAAQ,YAAYjF,EAAIC,EAAQhB,EAAQxB,EAAMyB,CAAO,EACrD4sB,EAAMtqB,CAAE,EAAIvB,CACd,CAEA,OAAOA,CACT,CAKA,IAAI8rB,EAAe,CAAC,EAChBC,EAAc,CAAC,EAEfC,EAAkB,EAEtB,SAASC,EAAaC,EAAQC,EAAQ,CACpC,KAAK,GAAKH,IACV,KAAK,OAASE,EACd,KAAK,OAASC,EACd,KAAK,QAAU,KACf,KAAK,SAAW,CAAC,EACjB,KAAK,WAAa,CAAC,EACnB,KAAK,SAAW,EAEZ7jB,EAAO,UACT,KAAK,MAAQ,CACX,cAAe,EACf,gBAAiB,CACnB,EAEJ,CAEA,SAAS8jB,EAAaxkB,EAAM3I,EAASotB,EAAoB,CACvD,IAAI9vB,EAAGyH,EAKHpD,EAAagrB,EAAUT,GAAoBvjB,EAAK,MAAM,EACtD/G,EAAa+qB,EAAUR,GAAkBxjB,EAAK,MAAM,EAEpDjH,EAAUiH,EAAK,QAAU7H,EAAG,cAAc,EAG9C,GAFAA,EAAG,aAAaY,EAASC,CAAU,EACnCb,EAAG,aAAaY,EAASE,CAAU,EAC/BwrB,EACF,IAAK9vB,EAAI,EAAGA,EAAI8vB,EAAmB,OAAQ,EAAE9vB,EAAG,CAC9C,IAAIkuB,EAAU4B,EAAmB9vB,CAAC,EAClCwD,EAAG,mBAAmBY,EAAS8pB,EAAQ,CAAC,EAAGA,EAAQ,CAAC,CAAC,CACvD,CAGF1qB,EAAG,YAAYY,CAAO,EACtBqE,EAAQ,UACNjF,EACAY,EACAW,EAAY,IAAIsG,EAAK,MAAM,EAC3BtG,EAAY,IAAIsG,EAAK,MAAM,EAC3B3I,CAAO,EAKT,IAAIqtB,EAAcvsB,EAAG,oBAAoBY,EAAS0qB,EAAkB,EAChE/iB,EAAO,UACTV,EAAK,MAAM,cAAgB0kB,GAE7B,IAAIlrB,EAAWwG,EAAK,SACpB,IAAKrL,EAAI,EAAGA,EAAI+vB,EAAa,EAAE/vB,EAE7B,GADAyH,EAAOjE,EAAG,iBAAiBY,EAASpE,CAAC,EACjCyH,EACF,GAAIA,EAAK,KAAO,EACd,QAASe,GAAI,EAAGA,GAAIf,EAAK,KAAM,EAAEe,GAAG,CAClC,IAAIsC,EAAOrD,EAAK,KAAK,QAAQ,MAAO,IAAMe,GAAI,GAAG,EACjD4mB,EAAiBvqB,EAAU,IAAIsqB,EAC7BrkB,EACA/F,EAAY,GAAG+F,CAAI,EACnBtH,EAAG,mBAAmBY,EAAS0G,CAAI,EACnCrD,CAAI,CAAC,CACT,MAEA2nB,EAAiBvqB,EAAU,IAAIsqB,EAC7B1nB,EAAK,KACL1C,EAAY,GAAG0C,EAAK,IAAI,EACxBjE,EAAG,mBAAmBY,EAASqD,EAAK,IAAI,EACxCA,CAAI,CAAC,EAQb,IAAIuoB,EAAgBxsB,EAAG,oBAAoBY,EAAS2qB,EAAoB,EACpEhjB,EAAO,UACTV,EAAK,MAAM,gBAAkB2kB,GAG/B,IAAIlrB,EAAauG,EAAK,WACtB,IAAKrL,EAAI,EAAGA,EAAIgwB,EAAe,EAAEhwB,EAC/ByH,EAAOjE,EAAG,gBAAgBY,EAASpE,CAAC,EAChCyH,GACF2nB,EAAiBtqB,EAAY,IAAIqqB,EAC/B1nB,EAAK,KACL1C,EAAY,GAAG0C,EAAK,IAAI,EACxBjE,EAAG,kBAAkBY,EAASqD,EAAK,IAAI,EACvCA,CAAI,CAAC,CAGb,CAEIsE,EAAO,UACTiI,EAAM,oBAAsB,UAAY,CACtC,IAAIic,EAAI,EACR,OAAAT,EAAY,QAAQ,SAAUnkB,EAAM,CAC9BA,EAAK,MAAM,cAAgB4kB,IAC7BA,EAAI5kB,EAAK,MAAM,cAEnB,CAAC,EACM4kB,CACT,EAEAjc,EAAM,sBAAwB,UAAY,CACxC,IAAIic,EAAI,EACR,OAAAT,EAAY,QAAQ,SAAUnkB,EAAM,CAC9BA,EAAK,MAAM,gBAAkB4kB,IAC/BA,EAAI5kB,EAAK,MAAM,gBAEnB,CAAC,EACM4kB,CACT,GAGF,SAASC,GAAkB,CACzBjB,EAAc,CAAC,EACfC,EAAc,CAAC,EACf,QAASlvB,EAAI,EAAGA,EAAIwvB,EAAY,OAAQ,EAAExvB,EACxC6vB,EAAYL,EAAYxvB,CAAC,EAAG,KAAMwvB,EAAYxvB,CAAC,EAAE,WAAW,IAAI,SAAUyH,EAAM,CAC9E,MAAO,CAACA,EAAK,SAAUA,EAAK,IAAI,CAClC,CAAC,CAAC,CAEN,CAEA,MAAO,CACL,MAAO,UAAY,CACjB,IAAI0oB,EAAe3sB,EAAG,aAAa,KAAKA,CAAE,EAC1CqN,GAAOoe,CAAW,EAAE,QAAQkB,CAAY,EACxClB,EAAc,CAAC,EACfpe,GAAOqe,CAAW,EAAE,QAAQiB,CAAY,EACxCjB,EAAc,CAAC,EAEfM,EAAY,QAAQ,SAAUnkB,EAAM,CAClC7H,EAAG,cAAc6H,EAAK,OAAO,CAC/B,CAAC,EACDmkB,EAAY,OAAS,EACrBD,EAAe,CAAC,EAEhBvb,EAAM,YAAc,CACtB,EAEA,QAAS,SAAU4b,EAAQD,EAAQjtB,EAAS0tB,EAAiB,CAC3D3nB,EAAQ,QAAQmnB,GAAU,EAAG,wBAAyBltB,CAAO,EAC7D+F,EAAQ,QAAQknB,GAAU,EAAG,0BAA2BjtB,CAAO,EAE/D,IAAI4sB,EAAQC,EAAaI,CAAM,EAC1BL,IACHA,EAAQC,EAAaI,CAAM,EAAI,CAAC,GAElC,IAAIU,EAAcf,EAAMM,CAAM,EAC9B,GAAIS,IACFA,EAAY,WACR,CAACD,GACH,OAAOC,EAGX,IAAIjsB,EAAU,IAAIsrB,EAAYC,EAAQC,CAAM,EAC5C,OAAA5b,EAAM,cACN6b,EAAYzrB,EAAS1B,EAAS0tB,CAAe,EACxCC,IACHf,EAAMM,CAAM,EAAIxrB,GAElBorB,EAAY,KAAKprB,CAAO,EACjBxE,GAAOwE,EAAS,CACrB,QAAS,UAAY,CAEnB,GADAA,EAAQ,WACJA,EAAQ,UAAY,EAAG,CACzBZ,EAAG,cAAcY,EAAQ,OAAO,EAChC,IAAIksB,EAAMd,EAAY,QAAQprB,CAAO,EACrCorB,EAAY,OAAOc,EAAK,CAAC,EACzBtc,EAAM,aACR,CAEIsb,EAAMlrB,EAAQ,MAAM,EAAE,UAAY,IACpCZ,EAAG,aAAa0rB,EAAY9qB,EAAQ,MAAM,CAAC,EAC3C,OAAO8qB,EAAY9qB,EAAQ,MAAM,EACjC,OAAOmrB,EAAanrB,EAAQ,MAAM,EAAEA,EAAQ,MAAM,GAG/C,OAAO,KAAKmrB,EAAanrB,EAAQ,MAAM,CAAC,EAAE,SAC7CZ,EAAG,aAAayrB,EAAY7qB,EAAQ,MAAM,CAAC,EAC3C,OAAO6qB,EAAY7qB,EAAQ,MAAM,EACjC,OAAOmrB,EAAanrB,EAAQ,MAAM,EAEtC,CACF,CAAC,CACH,EAEA,QAAS8rB,EAET,OAAQb,EAER,KAAM,GACN,KAAM,EACR,CACF,CAEA,IAAIkB,GAAY,KACZC,GAAqB,KACrBC,GAAoB,KACpBC,GAAa,KAEjB,SAASC,GACPntB,EACAmmB,EACAtK,EACAuR,EACAC,EACAnlB,EACA/D,EAAQ,CACR,SAASmpB,EAAgB3lB,EAAO,CAC9B,IAAIlK,EACA0oB,EAAiB,OAAS,MAC5BlhB,EACEooB,EAAa,sBACb,mHAAmH,EACrH5vB,EAAOuvB,KAEP/nB,EACEkhB,EAAiB,KAAK,iBAAiB,CAAC,EAAE,UAAY,KACtD,qCAAqC,EACvC1oB,EAAO0oB,EAAiB,KAAK,iBAAiB,CAAC,EAAE,QAAQ,SAAS,KAE9Dje,EAAW,mBACbjD,EACExH,IAASuvB,IAAsBvvB,IAASyvB,GACxC,8EAAkF,EAEhFzvB,IAASyvB,IACXjoB,EAAQd,EAAO,UAAW,oIAAsI,GAGlKc,EACExH,IAASuvB,GACT,iEAAmE,GAIzE,IAAI7wB,EAAI,EACJ8J,EAAI,EACJqU,EAAQ8S,EAAQ,iBAChB3R,EAAS2R,EAAQ,kBACjB9vB,EAAO,KAEPpB,GAAayL,CAAK,EACpBrK,EAAOqK,EACEA,IACT1C,EAAQ,KAAK0C,EAAO,SAAU,kCAAkC,EAChExL,EAAIwL,EAAM,EAAI,EACd1B,EAAI0B,EAAM,EAAI,EACd1C,EACE9I,GAAK,GAAKA,EAAIixB,EAAQ,iBACtB,gCAAgC,EAClCnoB,EACEgB,GAAK,GAAKA,EAAImnB,EAAQ,kBACtB,gCAAgC,EAClC9S,GAAS3S,EAAM,OAAUylB,EAAQ,iBAAmBjxB,GAAM,EAC1Dsf,GAAU9T,EAAM,QAAWylB,EAAQ,kBAAoBnnB,GAAM,EAC7D3I,EAAOqK,EAAM,MAAQ,MAInBrK,IACEG,IAASuvB,GACX/nB,EACE3H,aAAgB,WAChB,6EAAiF,EAC1EG,IAASyvB,IAClBjoB,EACE3H,aAAgB,aAChB,+EAAmF,GAIzF2H,EACEqV,EAAQ,GAAKA,EAAQne,GAAKixB,EAAQ,iBAClC,+BAA+B,EACjCnoB,EACEwW,EAAS,GAAKA,EAASxV,GAAKmnB,EAAQ,kBACpC,gCAAgC,EAGlCvR,EAAS,EAGT,IAAI0R,EAAOjT,EAAQmB,EAAS,EAG5B,OAAKne,IACCG,IAASuvB,GACX1vB,EAAO,IAAI,WAAWiwB,CAAI,EACjB9vB,IAASyvB,KAClB5vB,EAAOA,GAAQ,IAAI,aAAaiwB,CAAI,IAKxCtoB,EAAQ,aAAa3H,EAAM,kDAAkD,EAC7E2H,EAAQ3H,EAAK,YAAciwB,EAAM,uCAAuC,EAGxEvtB,EAAG,YAAYitB,GAAmB,CAAC,EACnCjtB,EAAG,WAAW7D,EAAG8J,EAAGqU,EAAOmB,EAAQsR,GACjCtvB,EACAH,CAAI,EAECA,CACT,CAEA,SAASkwB,EAAezb,EAAS,CAC/B,IAAIpS,EACJ,OAAAwmB,EAAiB,OAAO,CACtB,YAAapU,EAAQ,WACvB,EAAG,UAAY,CACbpS,EAAS2tB,EAAevb,CAAO,CACjC,CAAC,EACMpS,CACT,CAEA,SAAS8tB,EAAY1b,EAAS,CAC5B,MAAI,CAACA,GAAW,EAAE,gBAAiBA,GAC1Bub,EAAevb,CAAO,EAEtByb,EAAczb,CAAO,CAEhC,CAEA,OAAO0b,CACT,CAEA,SAASC,GAAOvxB,EAAG,CACjB,OAAO,MAAM,UAAU,MAAM,KAAKA,CAAC,CACrC,CAEA,SAASwxB,GAAMxxB,EAAG,CAChB,OAAOuxB,GAAMvxB,CAAC,EAAE,KAAK,EAAE,CACzB,CAEA,SAASyxB,IAAqB,CAE5B,IAAIC,EAAa,EAKbC,EAAc,CAAC,EACfC,EAAe,CAAC,EACpB,SAASC,EAAMxwB,EAAO,CACpB,QAAShB,EAAI,EAAGA,EAAIuxB,EAAa,OAAQ,EAAEvxB,EACzC,GAAIuxB,EAAavxB,CAAC,IAAMgB,EACtB,OAAOswB,EAAYtxB,CAAC,EAIxB,IAAI8K,EAAO,IAAOumB,IAClB,OAAAC,EAAY,KAAKxmB,CAAI,EACrBymB,EAAa,KAAKvwB,CAAK,EAChB8J,CACT,CAGA,SAASlF,GAAS,CAChB,IAAI6rB,EAAO,CAAC,EACZ,SAAS3tB,GAAQ,CACf2tB,EAAK,KAAK,MAAMA,EAAMP,GAAM,SAAS,CAAC,CACxC,CAEA,IAAIQ,EAAO,CAAC,EACZ,SAASC,GAAO,CACd,IAAI7mB,EAAO,IAAOumB,IAClB,OAAAK,EAAK,KAAK5mB,CAAI,EAEV,UAAU,OAAS,IACrB2mB,EAAK,KAAK3mB,EAAM,GAAG,EACnB2mB,EAAK,KAAK,MAAMA,EAAMP,GAAM,SAAS,CAAC,EACtCO,EAAK,KAAK,GAAG,GAGR3mB,CACT,CAEA,OAAOlL,GAAOkE,EAAM,CAClB,IAAK6tB,EACL,SAAU,UAAY,CACpB,OAAOR,GAAK,CACTO,EAAK,OAAS,EAAI,OAASA,EAAK,KAAK,GAAG,EAAI,IAAM,GACnDP,GAAKM,CAAI,CACX,CAAC,CACH,CACF,CAAC,CACH,CAEA,SAASG,GAAS,CAChB,IAAIC,EAAQjsB,EAAM,EACdksB,EAAOlsB,EAAM,EAEbmsB,EAAgBF,EAAM,SACtBG,EAAeF,EAAK,SAExB,SAASG,EAAMttB,EAAQutB,EAAM,CAC3BJ,EAAKntB,EAAQutB,EAAM,IAAKL,EAAM,IAAIltB,EAAQutB,CAAI,EAAG,GAAG,CACtD,CAEA,OAAOtyB,GAAO,UAAY,CACxBiyB,EAAM,MAAMA,EAAOX,GAAM,SAAS,CAAC,CACrC,EAAG,CACD,IAAKW,EAAM,IACX,MAAOA,EACP,KAAMC,EACN,KAAMG,EACN,IAAK,SAAUttB,EAAQutB,EAAMlxB,EAAO,CAClCixB,EAAKttB,EAAQutB,CAAI,EACjBL,EAAMltB,EAAQutB,EAAM,IAAKlxB,EAAO,GAAG,CACrC,EACA,SAAU,UAAY,CACpB,OAAO+wB,EAAc,EAAIC,EAAa,CACxC,CACF,CAAC,CACH,CAEA,SAASG,GAAe,CACtB,IAAI3xB,EAAO2wB,GAAK,SAAS,EACrBiB,EAAYR,EAAM,EAClBS,EAAYT,EAAM,EAElBU,EAAeF,EAAU,SACzBG,EAAeF,EAAU,SAE7B,OAAOzyB,GAAOwyB,EAAW,CACvB,KAAM,UAAY,CAChB,OAAAA,EAAU,MAAMA,EAAWlB,GAAM,SAAS,CAAC,EACpC,IACT,EACA,KAAM,UAAY,CAChB,OAAAmB,EAAU,MAAMA,EAAWnB,GAAM,SAAS,CAAC,EACpC,IACT,EACA,SAAU,UAAY,CACpB,IAAIsB,EAAaD,EAAa,EAC9B,OAAIC,IACFA,EAAa,QAAUA,EAAa,KAE/BrB,GAAK,CACV,MAAO3wB,EAAM,KACb8xB,EAAa,EACb,IAAKE,CACP,CAAC,CACH,CACF,CAAC,CACH,CAGA,IAAIC,EAAc7sB,EAAM,EACpB8sB,EAAa,CAAC,EAClB,SAASC,EAAM7nB,EAAMiN,EAAO,CAC1B,IAAIvM,EAAO,CAAC,EACZ,SAASonB,GAAO,CACd,IAAI9nB,EAAO,IAAMU,EAAK,OACtB,OAAAA,EAAK,KAAKV,CAAI,EACPA,CACT,CAEAiN,EAAQA,GAAS,EACjB,QAAS/X,EAAI,EAAGA,EAAI+X,EAAO,EAAE/X,EAC3B4yB,EAAI,EAGN,IAAIC,EAAOjB,EAAM,EACbkB,EAAeD,EAAK,SAEpB1vB,EAASuvB,EAAW5nB,CAAI,EAAIlL,GAAOizB,EAAM,CAC3C,IAAKD,EACL,SAAU,UAAY,CACpB,OAAOzB,GAAK,CACV,YAAa3lB,EAAK,KAAK,EAAG,KAC1BsnB,EAAa,EACb,GACF,CAAC,CACH,CACF,CAAC,EAED,OAAO3vB,CACT,CAEA,SAAS4vB,GAAW,CAClB,IAAItB,EAAO,CAAC,gBACVgB,EACA,UAAU,EACZ,OAAO,KAAKC,CAAU,EAAE,QAAQ,SAAU5nB,EAAM,CAC9C2mB,EAAK,KAAK,IAAK3mB,EAAM,KAAM4nB,EAAW5nB,CAAI,EAAE,SAAS,EAAG,GAAG,CAC7D,CAAC,EACD2mB,EAAK,KAAK,GAAG,EACb,IAAIuB,EAAM7B,GAAKM,CAAI,EAChB,QAAQ,KAAM;AAAA,CAAK,EACnB,QAAQ,KAAM;AAAA,CAAK,EACnB,QAAQ,KAAM;AAAA,CAAK,EAClBkB,EAAO,SAAS,MAAM,KAAMrB,EAAY,OAAO0B,CAAG,CAAC,EACvD,OAAOL,EAAK,MAAM,KAAMpB,CAAY,CACtC,CAEA,MAAO,CACL,OAAQkB,EACR,KAAMjB,EACN,MAAO5rB,EACP,KAAM+sB,EACN,MAAOf,EACP,KAAMO,EACN,QAASY,CACX,CACF,CAGA,IAAIE,GAAkB,OAAO,MAAM,EAAE,EAEjCC,GAAqB,KAErBC,GAAuB,EACvBC,GAAwB,EAExBC,GAAa,EACbC,GAAa,EACbC,GAAgB,EAChBC,GAAc,EACdC,GAAY,EACZC,GAAiB,EACjBC,GAAc,EAEdC,GAAW,SACXC,GAAiB,eACjBC,GAAgB,cAChBC,GAAmB,iBACnBC,GAAe,aACfC,GAAiB,eACjBC,GAAe,aACfC,GAAgB,cAChBC,GAAe,aACfC,GAAe,YACfC,GAAgB,cAChBC,GAAc,YACdC,GAAe,YACfC,GAAe,YACfC,GAA0B,uBAC1BC,GAA0B,uBAC1BC,GAAiB,eACjBC,GAAkB,gBAClBC,GAAoB,kBACpBC,GAAmB,iBACnBC,GAAiB,eACjBC,GAAiB,eACjBC,GAAoB,kBACpBC,GAAmB,iBACnBC,GAAmB,iBACnBC,GAAgB,cAChBC,GAAa,WAEbC,GAAY,UAEZC,GAAgB,cAChBC,GAAS,OACTC,GAAS,OACTC,GAAa,WACbC,GAAc,YACdC,GAAU,QACVC,GAAW,SACXC,GAAc,YACdC,GAAQ,MAERC,GAAe,QACfC,GAAgB,SAEhBC,GAAsBX,GAAgBS,GACtCG,GAAuBZ,GAAgBU,GACvCG,GAAmBf,GAAaW,GAChCK,GAAoBhB,GAAaY,GACjCK,GAAkB,gBAClBC,GAAwBD,GAAkBN,GAC1CQ,GAAyBF,GAAkBL,GAE3CQ,GAAiB,CACnB1C,GACAD,GACAkB,GACAC,GACAC,GACAL,GACAQ,GACAD,GACAV,EACF,EAEIgC,GAAoB,MACpBC,GAA4B,MAE5BC,GAAuB,MACvBC,GAAqB,MAErBC,GAAkB,KAClBC,GAAwB,MAExBC,GAAe,KACfC,GAAW,KACXC,GAAY,KACZC,GAAkB,KAClBC,GAAgB,KAChBC,GAAkB,KAClBC,GAAyB,MACzBC,GAA8B,MAC9BC,GAAqB,MAErBC,GAAa,KACbC,GAAgB,MAChBC,GAAgB,MAChBC,GAAgB,MAChBC,GAAW,KACXC,GAAc,MACdC,GAAc,MACdC,GAAc,MACdC,GAAU,MACVC,GAAe,MACfC,GAAe,MACfC,GAAe,MACfC,GAAgB,MAChBC,GAAgB,MAChBC,GAAgB,MAChBC,GAAgB,MAChBC,GAAkB,MAElBC,GAAiB,EAEjBC,GAAW,KACXC,GAAU,KACVC,GAAQ,KACRC,GAAS,KACTC,GAAa,MACbC,GAAa,MACbC,GAAY,IACZC,GAAU,KACVC,GAAU,EACVC,GAAS,EACTC,GAAc,MACdC,GAAU,IAEVC,GAAmB,MACnBC,GAAyB,MAEzBC,GAAa,CACf,EAAK,EACL,EAAK,EACL,KAAQ,EACR,IAAO,EACP,YAAa,IACb,sBAAuB,IACvB,YAAa,IACb,sBAAuB,IACvB,YAAa,IACb,sBAAuB,IACvB,YAAa,IACb,sBAAuB,IACvB,iBAAkB,MAClB,2BAA4B,MAC5B,iBAAkB,MAClB,2BAA4B,MAC5B,qBAAsB,GACxB,EAKIC,GAA2B,CAC7B,iCACA,2CACA,2CACA,qDACA,iCACA,2CACA,2CACA,oDACF,EAEIC,GAAe,CACjB,MAAS,IACT,KAAQ,IACR,IAAK,IACL,MAAS,IACT,IAAK,IACL,KAAM,IACN,MAAO,IACP,OAAU,IACV,KAAM,IACN,QAAW,IACX,IAAK,IACL,SAAY,IACZ,KAAM,IACN,MAAO,IACP,OAAU,IACV,KAAM,IACN,OAAU,GACZ,EAEIC,GAAa,CACf,EAAK,EACL,KAAQ,EACR,KAAQ,KACR,QAAW,KACX,UAAa,KACb,UAAa,KACb,iBAAkB,MAClB,iBAAkB,MAClB,OAAU,IACZ,EAEIC,GAAa,CACf,KAAQjD,GACR,KAAQC,EACV,EAEIiD,GAAkB,CACpB,GAAMjB,GACN,IAAOC,EACT,EAEA,SAASiB,GAAcr6B,EAAG,CACxB,OAAO,MAAM,QAAQA,CAAC,GACpBD,GAAaC,CAAC,GACdiR,GAAcjR,CAAC,CACnB,CAGA,SAASs6B,GAAW3M,EAAO,CACzB,OAAOA,EAAM,KAAK,SAAUpJ,EAAGC,EAAG,CAChC,OAAID,IAAMoR,GACD,GACEnR,IAAMmR,GACR,EAEDpR,EAAIC,EAAK,GAAK,CACxB,CAAC,CACH,CAEA,SAAS+V,GAAaC,EAASC,EAAYC,EAASC,EAAQ,CAC1D,KAAK,QAAUH,EACf,KAAK,WAAaC,EAClB,KAAK,QAAUC,EACf,KAAK,OAASC,CAChB,CAEA,SAASC,GAAUC,EAAM,CACvB,OAAOA,GAAQ,EAAEA,EAAK,SAAWA,EAAK,YAAcA,EAAK,QAC3D,CAEA,SAASC,GAAkBH,EAAQ,CACjC,OAAO,IAAIJ,GAAY,GAAO,GAAO,GAAOI,CAAM,CACpD,CAEA,SAASI,GAAmBC,EAAKL,EAAQ,CACvC,IAAIr5B,EAAO05B,EAAI,KACf,GAAI15B,IAASoyB,GAAY,CACvB,IAAIuH,EAAUD,EAAI,KAAK,OACvB,OAAO,IAAIT,GACT,GACAU,GAAW,EACXA,GAAW,EACXN,CAAM,CACV,SAAWr5B,IAASwyB,GAAW,CAC7B,IAAI3yB,EAAO65B,EAAI,KACf,OAAO,IAAIT,GACTp5B,EAAK,QACLA,EAAK,WACLA,EAAK,QACLw5B,CAAM,CACV,KAAO,IAAIr5B,IAASyyB,GAClB,OAAO,IAAIwG,GACT,GACA,GACA,GACAI,CAAM,EACH,GAAIr5B,IAAS0yB,GAAa,CAI/B,QAHIwG,EAAU,GACVC,EAAa,GACbC,EAAU,GACLr6B,EAAI,EAAGA,EAAI26B,EAAI,KAAK,OAAQ,EAAE36B,EAAG,CACxC,IAAI66B,EAASF,EAAI,KAAK36B,CAAC,EACvB,GAAI66B,EAAO,OAASvH,GAClB+G,EAAU,WACDQ,EAAO,OAAStH,GACzB6G,EAAa,WACJS,EAAO,OAASrH,GACzB2G,EAAU,WACDU,EAAO,OAASxH,GAAY,CACrC8G,EAAU,GACV,IAAIW,EAAUD,EAAO,KACjBC,GAAW,IACbV,EAAa,IAEXU,GAAW,IACbT,EAAU,GAEd,MAAWQ,EAAO,OAASpH,KACzB0G,EAAUA,GAAWU,EAAO,KAAK,QACjCT,EAAaA,GAAcS,EAAO,KAAK,WACvCR,EAAUA,GAAWQ,EAAO,KAAK,QAErC,CACA,OAAO,IAAIX,GACTC,EACAC,EACAC,EACAC,CAAM,CACV,KACE,QAAO,IAAIJ,GACTj5B,IAASuyB,GACTvyB,IAASsyB,GACTtyB,IAASqyB,GACTgH,CAAM,EAEZ,CAEA,IAAIS,GAAa,IAAIb,GAAY,GAAO,GAAO,GAAO,UAAY,CAAC,CAAC,EAEpE,SAASc,GACPx3B,EACAuB,EACA2G,EACA/D,EACA0P,EACA4jB,EACAxR,EACAE,EACAuR,EACAC,EACAC,EACAC,EACA/b,EACAgc,EACAvvB,EAAQ,CACR,IAAIihB,EAAkBmO,EAAe,OAEjCI,EAAiB,CACnB,IAAO,MACP,SAAY,MACZ,mBAAoB,KACtB,EACI7vB,EAAW,mBACb6vB,EAAe,IAAMvC,GACrBuC,EAAe,IAAMtC,IAGvB,IAAIuC,EAAgB9vB,EAAW,uBAC3B+vB,EAAiB/vB,EAAW,mBAO5BgwB,EAAe,CACjB,MAAO,GACP,QAAS3vB,EAAO,OAClB,EACI4vB,EAAY,CAAC,EACbC,EAAiB,CAAC,EAClBC,EAAW,CAAC,EACZC,EAAe,CAAC,EAEpB,SAASC,EAAUjxB,EAAM,CACvB,OAAOA,EAAK,QAAQ,IAAK,GAAG,CAC9B,CAEA,SAASkxB,EAAWC,EAAOC,EAAKC,EAAM,CACpC,IAAIrxB,EAAOixB,EAASE,CAAK,EACzBL,EAAe,KAAKK,CAAK,EACzBN,EAAU7wB,CAAI,EAAI4wB,EAAa5wB,CAAI,EAAI,CAAC,CAACqxB,EACzCN,EAAS/wB,CAAI,EAAIoxB,CACnB,CAEA,SAASE,GAAeH,EAAOI,EAAMF,EAAM,CACzC,IAAIrxB,EAAOixB,EAASE,CAAK,EACzBL,EAAe,KAAKK,CAAK,EACrB,MAAM,QAAQE,CAAI,GACpBT,EAAa5wB,CAAI,EAAIqxB,EAAK,MAAM,EAChCR,EAAU7wB,CAAI,EAAIqxB,EAAK,MAAM,GAE7BT,EAAa5wB,CAAI,EAAI6wB,EAAU7wB,CAAI,EAAIqxB,EAEzCL,EAAahxB,CAAI,EAAIuxB,CACvB,CAGAL,EAAUpI,GAAUuD,EAAS,EAG7B6E,EAAUnI,GAAgBqD,EAAQ,EAClCkF,GAActI,GAAe,aAAc,CAAC,EAAG,EAAG,EAAG,CAAC,CAAC,EACvDsI,GAAcrI,GAAkB,wBAC9B,CAACuF,GAAaA,EAAW,CAAC,EAC5B8C,GAAcpI,GAAc,oBAC1B,CAACqF,GAAQD,GAASC,GAAQD,EAAO,CAAC,EAGpC4C,EAAU/H,GAAgBoD,GAAe,EAAI,EAC7C+E,GAAclI,GAAc,YAAaqF,EAAO,EAChD6C,GAAcjI,GAAe,aAAc,CAAC,EAAG,CAAC,CAAC,EACjDiI,GAAchI,GAAc,YAAa,EAAI,EAG7CgI,GAAc/H,GAAcA,GAAc,CAAC,GAAM,GAAM,GAAM,EAAI,CAAC,EAGlE2H,EAAU1H,GAAe2C,EAAY,EACrCmF,GAAc7H,GAAa,WAAYsE,EAAO,EAG9CuD,GAAc5H,GAAcA,GAAcuE,EAAM,EAGhDqD,GAAc3H,GAAcA,GAAc,CAAC,EAG3CuH,EAAUtH,GAAyB6C,EAAsB,EACzD6E,GAAczH,GAAyB,gBAAiB,CAAC,EAAG,CAAC,CAAC,EAG9DqH,EAAUpH,GAAgB4C,EAA2B,EACrDwE,EAAUnH,GAAiB4C,EAAkB,EAC7C2E,GAActH,GAAmB,iBAAkB,CAAC,EAAG,EAAK,CAAC,EAG7DkH,EAAUjH,GAAkBqC,EAAe,EAC3CgF,GAAcpH,GAAgB,cAAe,EAAE,EAC/CoH,GAAcnH,GAAgB,cAAe,CAACiE,GAAW,EAAG,EAAE,CAAC,EAC/DkD,GAAclH,GAAmB,oBAC/B,CAAC0D,GAAUO,GAASA,GAASA,EAAO,CAAC,EACvCiD,GAAcjH,GAAkB,oBAC9B,CAAC0D,GAASM,GAASA,GAASA,EAAO,CAAC,EAGtC6C,EAAU5G,GAAkBkC,EAAe,EAC3C8E,GAAc/G,GAAe,UAC3B,CAAC,EAAG,EAAG7xB,EAAG,mBAAoBA,EAAG,mBAAmB,CAAC,EAGvD44B,GAAc9G,GAAYA,GACxB,CAAC,EAAG,EAAG9xB,EAAG,mBAAoBA,EAAG,mBAAmB,CAAC,EAOvD,IAAI84B,EAAc,CAChB,GAAI94B,EACJ,QAAS8b,EACT,QAASva,EACT,KAAM42B,EACN,QAASD,EACT,KAAML,EACN,SAAUJ,EACV,OAAQ5jB,EACR,OAAQ+jB,EACR,WAAYD,EAAe,MAC3B,IAAKA,EACL,SAAUD,EACV,YAAavR,EACb,WAAYje,EAEZ,MAAO4vB,EACP,aAActB,EAChB,EAEIuC,EAAkB,CACpB,UAAWhmB,GACX,aAAcqjB,GACd,WAAYF,GACZ,eAAgB6B,EAChB,WAAY1B,GACZ,QAASjnB,GACT,gBAAiBmnB,EACnB,EAEAtxB,EAAQ,SAAS,UAAY,CAC3B6zB,EAAY,YAAcvjB,EAC5B,CAAC,EAEG0iB,IACFc,EAAgB,WAAa,CAAC1D,EAAO,EACrC0D,EAAgB,WAAapwB,GAAKxE,EAAO,eAAgB,SAAU3H,EAAG,CACpE,OAAIA,IAAM,EACD,CAAC,CAAC,EAEJmM,GAAKnM,EAAG,SAAUwI,EAAG,CAC1B,OAAOixB,GAAyBjxB,CAClC,CAAC,CACH,CAAC,GAGH,IAAIg0B,EAAkB,EACtB,SAASC,IAAyB,CAChC,IAAIC,EAAMtL,GAAkB,EACxBI,EAAOkL,EAAI,KACXn9B,EAASm9B,EAAI,OACjBA,EAAI,GAAKF,IAETE,EAAI,QAAU,IAGd,IAAIC,EAASnL,EAAK8K,CAAW,EACzBM,EAASF,EAAI,OAAS,CACxB,MAAO,IACT,EACA,OAAO,KAAKJ,CAAW,EAAE,QAAQ,SAAUpK,EAAM,CAC/C0K,EAAO1K,CAAI,EAAI3yB,EAAO,IAAIo9B,EAAQ,IAAKzK,CAAI,CAC7C,CAAC,EAGDzpB,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,MAAQlL,EAAK/oB,CAAO,EACxBi0B,EAAI,WAAaj0B,EAAQ,aAAa,EACtCi0B,EAAI,QAAUlL,EAAKkL,EAAI,UAAU,EACjCA,EAAI,OAAS,SAAU92B,EAAOpF,EAAMH,EAAS,CAC3CuF,EACE,QAASpF,EAAM,KACf,KAAK,MAAO,iBAAkBgxB,EAAKnxB,CAAO,EAAG,IAAK,KAAK,QAAS,IAAI,CACxE,EAEAk8B,EAAgB,yBAA2B5C,EAC7C,CAAC,EAGD,IAAIkD,EAAWH,EAAI,KAAO,CAAC,EACvBI,EAAcJ,EAAI,QAAU,CAAC,EACjC,OAAO,KAAKZ,CAAY,EAAE,QAAQ,SAAUiB,EAAU,CAChD,MAAM,QAAQrB,EAAaqB,CAAQ,CAAC,IACtCF,EAASE,CAAQ,EAAIx9B,EAAO,IAAIq9B,EAAO,KAAM,IAAKG,CAAQ,EAC1DD,EAAYC,CAAQ,EAAIx9B,EAAO,IAAIq9B,EAAO,QAAS,IAAKG,CAAQ,EAEpE,CAAC,EAGD,IAAIC,EAAYN,EAAI,UAAY,CAAC,EACjC,OAAO,KAAKH,CAAe,EAAE,QAAQ,SAAUzxB,EAAM,CACnDkyB,EAAUlyB,CAAI,EAAIvL,EAAO,IAAI,KAAK,UAAUg9B,EAAgBzxB,CAAI,CAAC,CAAC,CACpE,CAAC,EAGD4xB,EAAI,OAAS,SAAU92B,EAAOjG,EAAG,CAC/B,OAAQA,EAAE,KAAM,CACd,KAAK0zB,GACH,IAAI4J,EAAU,CACZ,OACAL,EAAO,QACPA,EAAO,MACPF,EAAI,OACN,EACA,OAAO92B,EAAM,IACX4rB,EAAK7xB,EAAE,IAAI,EAAG,SACds9B,EAAQ,MAAM,EAAG,KAAK,IAAIt9B,EAAE,KAAK,OAAS,EAAG,CAAC,CAAC,EAC/C,GAAG,EACP,KAAK2zB,GACH,OAAO1tB,EAAM,IAAIg3B,EAAO,MAAOj9B,EAAE,IAAI,EACvC,KAAK4zB,GACH,OAAO3tB,EAAM,IAAIg3B,EAAO,QAASj9B,EAAE,IAAI,EACzC,KAAK6zB,GACH,OAAO5tB,EAAM,IAAI,OAAQjG,EAAE,IAAI,EACjC,KAAK8zB,GACH,OAAA9zB,EAAE,KAAK,OAAO+8B,EAAK92B,CAAK,EACjBjG,EAAE,KAAK,IAChB,KAAK+zB,GACH,OAAO/zB,EAAE,KAAK,SAAS,EACzB,KAAKg0B,GACH,OAAOh0B,EAAE,KAAK,IAAI,SAAU8J,EAAG,CAC7B,OAAOizB,EAAI,OAAO92B,EAAO6D,CAAC,CAC5B,CAAC,CACL,CACF,EAEAizB,EAAI,YAAc,CAAC,EAEnB,IAAIQ,EAAe,CAAC,EACpB,OAAAR,EAAI,YAAc,SAAU5xB,EAAM,CAChC,IAAI9F,EAAKD,EAAY,GAAG+F,CAAI,EAC5B,GAAI9F,KAAMk4B,EACR,OAAOA,EAAal4B,CAAE,EAExB,IAAIkpB,EAAUiN,EAAe,MAAMn2B,CAAE,EAChCkpB,IACHA,EAAUiN,EAAe,MAAMn2B,CAAE,EAAI,IAAIgoB,GAE3C,IAAI7pB,EAAS+5B,EAAal4B,CAAE,EAAIwsB,EAAKtD,CAAO,EAC5C,OAAO/qB,CACT,EAEOu5B,CACT,CAOA,SAASS,GAAc5nB,EAAS,CAC9B,IAAI6nB,EAAgB7nB,EAAQ,OACxB8nB,EAAiB9nB,EAAQ,QAEzB+nB,EACJ,GAAI/H,MAAa6H,EAAe,CAC9B,IAAIp8B,EAAQ,CAAC,CAACo8B,EAAc7H,EAAS,EACrC+H,EAAgB7C,GAAiB,SAAUiC,EAAK9K,EAAO,CACrD,OAAO5wB,CACT,CAAC,EACDs8B,EAAc,OAASt8B,CACzB,SAAWu0B,MAAa8H,EAAgB,CACtC,IAAI1C,EAAM0C,EAAe9H,EAAS,EAClC+H,EAAgB5C,GAAkBC,EAAK,SAAU+B,EAAK9K,EAAO,CAC3D,OAAO8K,EAAI,OAAO9K,EAAO+I,CAAG,CAC9B,CAAC,CACH,CAEA,OAAO2C,CACT,CAEA,SAASC,EAAkBhoB,EAASmnB,EAAK,CACvC,IAAIU,EAAgB7nB,EAAQ,OACxB8nB,EAAiB9nB,EAAQ,QAE7B,GAAIigB,MAAiB4H,EAAe,CAClC,IAAIrS,EAAcqS,EAAc5H,EAAa,EAC7C,OAAIzK,GACFA,EAAcpB,EAAiB,eAAeoB,CAAW,EACzDtiB,EAAQ,QAAQsiB,EAAa,4BAA4B,EAClD0P,GAAiB,SAAUiC,EAAK92B,EAAO,CAC5C,IAAI43B,EAAcd,EAAI,KAAK3R,CAAW,EAClC6R,EAASF,EAAI,OACjB92B,EAAM,IACJg3B,EAAO,YACP,QACAY,CAAW,EACb,IAAIC,EAAUb,EAAO,QACrB,OAAAh3B,EAAM,IACJ63B,EACA,IAAMtH,GACNqH,EAAc,QAAQ,EACxB53B,EAAM,IACJ63B,EACA,IAAMrH,GACNoH,EAAc,SAAS,EAClBA,CACT,CAAC,GAEM/C,GAAiB,SAAUiC,EAAK9K,EAAO,CAC5C,IAAIgL,EAASF,EAAI,OACjB9K,EAAM,IACJgL,EAAO,YACP,QACA,MAAM,EACR,IAAIa,EAAUb,EAAO,QACrB,OAAAhL,EAAM,IACJ6L,EACA,IAAMtH,GACNsH,EAAU,IAAMjH,EAAqB,EACvC5E,EAAM,IACJ6L,EACA,IAAMrH,GACNqH,EAAU,IAAMhH,EAAsB,EACjC,MACT,CAAC,CAEL,SAAWjB,MAAiB6H,EAAgB,CAC1C,IAAI1C,EAAM0C,EAAe7H,EAAa,EACtC,OAAOkF,GAAkBC,EAAK,SAAU+B,EAAK9K,EAAO,CAClD,IAAI8L,EAAmBhB,EAAI,OAAO9K,EAAO+I,CAAG,EACxCiC,EAASF,EAAI,OACbiB,EAAoBf,EAAO,YAC3BY,EAAc5L,EAAM,IACtB+L,EAAmB,mBAAoBD,EAAkB,GAAG,EAE9Dj1B,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT,IAAM8L,EAAmB,KAAOF,EAChC,4BAA4B,CAChC,CAAC,EAED5L,EAAM,IACJ+L,EACA,QACAH,CAAW,EACb,IAAIC,EAAUb,EAAO,QACrB,OAAAhL,EAAM,IACJ6L,EACA,IAAMtH,GACNqH,EAAc,IAAMA,EAAc,UAClCC,EAAU,IAAMjH,EAAqB,EACvC5E,EAAM,IACJ6L,EACA,IAAMrH,GACNoH,EACA,IAAMA,EAAc,WACpBC,EAAU,IAAMhH,EAAsB,EACjC+G,CACT,CAAC,CACH,KACE,QAAO,IAEX,CAEA,SAASI,GAAsBroB,EAASwV,EAAa2R,EAAK,CACxD,IAAIU,EAAgB7nB,EAAQ,OACxB8nB,EAAiB9nB,EAAQ,QAE7B,SAASsoB,EAAUl9B,EAAO,CACxB,GAAIA,KAASy8B,EAAe,CAC1B,IAAIU,EAAMV,EAAcz8B,CAAK,EAC7B8H,EAAQ,YAAYq1B,EAAK,SAAU,WAAan9B,EAAO+7B,EAAI,UAAU,EAErE,IAAInC,EAAW,GACX56B,EAAIm+B,EAAI,EAAI,EACZr0B,EAAIq0B,EAAI,EAAI,EACZvzB,EAAG3C,EACP,MAAI,UAAWk2B,GACbvzB,EAAIuzB,EAAI,MAAQ,EAChBr1B,EAAQ,QAAQ8B,GAAK,EAAG,WAAa5J,EAAO+7B,EAAI,UAAU,GAE1DnC,EAAW,GAET,WAAYuD,GACdl2B,EAAIk2B,EAAI,OAAS,EACjBr1B,EAAQ,QAAQb,GAAK,EAAG,WAAajH,EAAO+7B,EAAI,UAAU,GAE1DnC,EAAW,GAGN,IAAIL,GACT,CAACK,GAAYxP,GAAeA,EAAY,QACxC,CAACwP,GAAYxP,GAAeA,EAAY,WACxC,CAACwP,GAAYxP,GAAeA,EAAY,QACxC,SAAU2R,EAAK9K,GAAO,CACpB,IAAI6L,EAAUf,EAAI,OAAO,QACrBqB,GAAQxzB,EACN,UAAWuzB,IACfC,GAAQnM,GAAM,IAAI6L,EAAS,IAAKtH,GAAqB,IAAKx2B,CAAC,GAE7D,IAAIq+B,GAAQp2B,EACZ,MAAM,WAAYk2B,IAChBE,GAAQpM,GAAM,IAAI6L,EAAS,IAAKrH,GAAsB,IAAK3sB,CAAC,GAEvD,CAAC9J,EAAG8J,EAAGs0B,GAAOC,EAAK,CAC5B,CAAC,CACL,SAAWr9B,KAAS08B,EAAgB,CAClC,IAAIY,EAASZ,EAAe18B,CAAK,EAC7BwC,EAASu3B,GAAkBuD,EAAQ,SAAUvB,EAAK9K,GAAO,CAC3D,IAAIsM,EAAMxB,EAAI,OAAO9K,GAAOqM,CAAM,EAElCx1B,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,GACTsM,EAAM,YAAcA,EAAM,cAC1B,WAAav9B,CAAK,CACtB,CAAC,EAED,IAAI88B,GAAUf,EAAI,OAAO,QACrByB,GAAQvM,GAAM,IAAIsM,EAAK,MAAM,EAC7BE,GAAQxM,GAAM,IAAIsM,EAAK,MAAM,EAC7BH,GAAQnM,GAAM,IAChB,cAAesM,EAAK,IAAKA,EAAK,YAC9B,IAAKT,GAAS,IAAKtH,GAAqB,IAAKgI,GAAO,GAAG,EACrDH,GAAQpM,GAAM,IAChB,eAAgBsM,EAAK,IAAKA,EAAK,aAC/B,IAAKT,GAAS,IAAKrH,GAAsB,IAAKgI,GAAO,GAAG,EAE1D,OAAA31B,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,GACTmM,GAAQ,QACRC,GAAQ,MACR,WAAar9B,CAAK,CACtB,CAAC,EAEM,CAACw9B,GAAOC,GAAOL,GAAOC,EAAK,CACpC,CAAC,EACD,OAAIjT,IACF5nB,EAAO,QAAUA,EAAO,SAAW4nB,EAAY,QAC/C5nB,EAAO,WAAaA,EAAO,YAAc4nB,EAAY,WACrD5nB,EAAO,QAAUA,EAAO,SAAW4nB,EAAY,SAE1C5nB,CACT,KAAO,QAAI4nB,EACF,IAAImP,GACTnP,EAAY,QACZA,EAAY,WACZA,EAAY,QACZ,SAAU2R,EAAK9K,GAAO,CACpB,IAAI6L,EAAUf,EAAI,OAAO,QACzB,MAAO,CACL,EAAG,EACH9K,GAAM,IAAI6L,EAAS,IAAKtH,EAAmB,EAC3CvE,GAAM,IAAI6L,EAAS,IAAKrH,EAAoB,CAAC,CACjD,CAAC,EAEI,IAEX,CAEA,IAAIiI,EAAWR,EAASvI,EAAU,EAElC,GAAI+I,EAAU,CACZ,IAAIC,EAAeD,EACnBA,EAAW,IAAInE,GACbmE,EAAS,QACTA,EAAS,WACTA,EAAS,QACT,SAAU3B,EAAK9K,EAAO,CACpB,IAAI2M,EAAWD,EAAa,OAAO5B,EAAK9K,CAAK,EACzC6L,EAAUf,EAAI,OAAO,QACzB,OAAA9K,EAAM,IACJ6L,EACA,IAAMpH,GACNkI,EAAS,CAAC,CAAC,EACb3M,EAAM,IACJ6L,EACA,IAAMnH,GACNiI,EAAS,CAAC,CAAC,EACNA,CACT,CAAC,CACL,CAEA,MAAO,CACL,SAAUF,EACV,YAAaR,EAASxI,EAAa,CACrC,CACF,CAEA,SAASmJ,GAAsBjpB,EAASzQ,EAAY,CAClD,IAAIs4B,EAAgB7nB,EAAQ,OACxBkpB,EACF,OAAOrB,EAAc1H,EAAM,GAAM,UACjC,OAAO0H,EAAc3H,EAAM,GAAM,SACnC,GAAIgJ,EAAe,CACjB,GAAI,OAAO,KAAK35B,EAAW,OAAO,EAAE,OAAS,EAC3C,OAAO,KAET,IAAI45B,EAAmB55B,EAAW,OAC9B65B,EAAc,OAAO,KAAKD,CAAgB,EAC9C,GAAIC,EAAY,OAAS,GAAK,OAAOD,EAAiBC,EAAY,CAAC,CAAC,GAAM,SAAU,CAElF,QADIC,EAAW,CAAC,EACP5+B,EAAI,EAAGA,EAAI2+B,EAAY,OAAQ,EAAE3+B,EACxCyI,EAAQ,OAAOi2B,EAAiBC,EAAY3+B,CAAC,CAAC,GAAM,SAAU,6DAA6D,EAC3H4+B,EAAS,KAAK,CAACF,EAAiBC,EAAY3+B,CAAC,CAAC,EAAI,EAAG2+B,EAAY3+B,CAAC,CAAC,CAAC,EAEtE,OAAO4+B,CACT,CACF,CACA,OAAO,IACT,CAEA,SAASC,GAActpB,EAASmnB,EAAKtM,EAAiB,CACpD,IAAIgN,EAAgB7nB,EAAQ,OACxB8nB,EAAiB9nB,EAAQ,QAE7B,SAASupB,EAAah0B,EAAM,CAC1B,GAAIA,KAAQsyB,EAAe,CACzB,IAAIp4B,EAAKD,EAAY,GAAGq4B,EAActyB,CAAI,CAAC,EAC3CrC,EAAQ,SAAS,UAAY,CAC3B2yB,EAAY,OAAOtB,GAAWhvB,CAAI,EAAG9F,EAAIyD,EAAQ,aAAa,CAAC,CACjE,CAAC,EACD,IAAItF,EAASs3B,GAAiB,UAAY,CACxC,OAAOz1B,CACT,CAAC,EACD,OAAA7B,EAAO,GAAK6B,EACL7B,CACT,SAAW2H,KAAQuyB,EAAgB,CACjC,IAAI1C,EAAM0C,EAAevyB,CAAI,EAC7B,OAAO4vB,GAAkBC,EAAK,SAAU+B,EAAK9K,EAAO,CAClD,IAAIzxB,EAAMu8B,EAAI,OAAO9K,EAAO+I,CAAG,EAC3B31B,EAAK4sB,EAAM,IAAI8K,EAAI,OAAO,QAAS,OAAQv8B,EAAK,GAAG,EACvD,OAAAsI,EAAQ,SAAS,UAAY,CAC3BmpB,EACE8K,EAAI,OAAO,OAAQ,WACnB5C,GAAWhvB,CAAI,EAAG,IAClB9F,EAAI,IACJ03B,EAAI,QAAS,IAAI,CACrB,CAAC,EACM13B,CACT,CAAC,CACH,CACA,OAAO,IACT,CAEA,IAAI+5B,EAAOD,EAAYpJ,EAAM,EACzBsJ,EAAOF,EAAYrJ,EAAM,EAEzBrxB,EAAU,KACV66B,EACJ,OAAI1E,GAASwE,CAAI,GAAKxE,GAASyE,CAAI,GACjC56B,EAAUg3B,EAAY,QAAQ4D,EAAK,GAAID,EAAK,GAAI,KAAM3O,CAAe,EACrE6O,EAAUxE,GAAiB,SAAUiC,EAAK9K,EAAO,CAC/C,OAAO8K,EAAI,KAAKt4B,CAAO,CACzB,CAAC,GAED66B,EAAU,IAAI/E,GACX6E,GAAQA,EAAK,SAAaC,GAAQA,EAAK,QACvCD,GAAQA,EAAK,YAAgBC,GAAQA,EAAK,WAC1CD,GAAQA,EAAK,SAAaC,GAAQA,EAAK,QACxC,SAAUtC,EAAK9K,EAAO,CACpB,IAAIsN,EAAexC,EAAI,OAAO,OAC1B/M,EACAoP,EACFpP,EAASoP,EAAK,OAAOrC,EAAK9K,CAAK,EAE/BjC,EAASiC,EAAM,IAAIsN,EAAc,IAAKxJ,EAAM,EAE9C,IAAI9F,EACAoP,EACFpP,EAASoP,EAAK,OAAOtC,EAAK9K,CAAK,EAE/BhC,EAASgC,EAAM,IAAIsN,EAAc,IAAKzJ,EAAM,EAE9C,IAAI0J,EAAUD,EAAe,YAActP,EAAS,IAAMD,EAC1D,OAAAlnB,EAAQ,SAAS,UAAY,CAC3B02B,GAAW,IAAMzC,EAAI,OACvB,CAAC,EACM9K,EAAM,IAAIuN,EAAU,GAAG,CAChC,CAAC,EAGE,CACL,KAAMJ,EACN,KAAMC,EACN,QAASC,EACT,QAAS76B,CACX,CACF,CAEA,SAASg7B,GAAW7pB,EAASmnB,EAAK,CAChC,IAAIU,EAAgB7nB,EAAQ,OACxB8nB,EAAiB9nB,EAAQ,QAE7B,SAAS8pB,GAAiB,CACxB,GAAI1J,MAAcyH,EAAe,CAC/B,IAAIvlB,EAAWulB,EAAczH,EAAU,EACnCqE,GAAaniB,CAAQ,EACvBA,EAAWojB,EAAa,YAAYA,EAAa,OAAOpjB,EAAU,EAAI,CAAC,EAC9DA,IACTA,EAAWojB,EAAa,YAAYpjB,CAAQ,EAC5CpP,EAAQ,QAAQoP,EAAU,mBAAoB6kB,EAAI,UAAU,GAE9D,IAAIv5B,EAASs3B,GAAiB,SAAUiC,EAAK9K,EAAO,CAClD,GAAI/Z,EAAU,CACZ,IAAI1U,EAASu5B,EAAI,KAAK7kB,CAAQ,EAC9B,OAAA6kB,EAAI,SAAWv5B,EACRA,CACT,CACA,OAAAu5B,EAAI,SAAW,KACR,IACT,CAAC,EACD,OAAAv5B,EAAO,MAAQ0U,EACR1U,CACT,SAAWwyB,MAAc0H,EAAgB,CACvC,IAAI1C,EAAM0C,EAAe1H,EAAU,EACnC,OAAO+E,GAAkBC,EAAK,SAAU+B,EAAK9K,EAAO,CAClD,IAAIgL,EAASF,EAAI,OAEb4C,EAAiB1C,EAAO,aACxB2C,EAAgB3C,EAAO,SAEvB4C,GAAc9C,EAAI,OAAO9K,EAAO+I,CAAG,EACnC9iB,EAAW+Z,EAAM,IAAI,MAAM,EAC3B6N,GAAgB7N,EAAM,IAAI0N,EAAgB,IAAKE,GAAa,GAAG,EAE/DE,GAAOhD,EAAI,KAAK+C,EAAa,EAC9B,KAAK5nB,EAAU,IAAK0nB,EAAe,iBAAkBC,GAAa,IAAI,EACtE,KAAK3nB,EAAU,IAAK0nB,EAAe,gBAAiBC,GAAa,IAAI,EAExE,OAAA/2B,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAOgD,GAAK,KACd,IAAMF,GAAc,KAAO3nB,EAC3B,kBAAkB,CACtB,CAAC,EAED+Z,EAAM,MAAM8N,EAAI,EAChB9N,EAAM,KACJ8K,EAAI,KAAK+C,EAAa,EACnB,KAAKF,EAAe,kBAAmB1nB,EAAU,IAAI,CAAC,EAE3D6kB,EAAI,SAAW7kB,EAERA,CACT,CAAC,CACH,CAEA,OAAO,IACT,CAEA,IAAIA,EAAWwnB,EAAc,EAE7B,SAASM,GAAkB,CACzB,GAAI/J,MAAewH,EAAe,CAChC,IAAIwC,EAAYxC,EAAcxH,EAAW,EACzC,OAAAntB,EAAQ,iBAAiBm3B,EAAWrpB,GAAW,mBAAoBmmB,EAAI,UAAU,EAC1EjC,GAAiB,SAAUiC,EAAK9K,EAAO,CAC5C,OAAOrb,GAAUqpB,CAAS,CAC5B,CAAC,CACH,SAAWhK,MAAeyH,EAAgB,CACxC,IAAIwC,EAAexC,EAAezH,EAAW,EAC7C,OAAO8E,GAAkBmF,EAAc,SAAUnD,EAAK9K,EAAO,CAC3D,IAAIkO,EAAapD,EAAI,UAAU,UAC3B5kB,EAAO4kB,EAAI,OAAO9K,EAAOiO,CAAY,EACzC,OAAAp3B,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT9Z,EAAO,OAASgoB,EAChB,qCAAuC,OAAO,KAAKvpB,EAAS,CAAC,CACjE,CAAC,EACMqb,EAAM,IAAIkO,EAAY,IAAKhoB,EAAM,GAAG,CAC7C,CAAC,CACH,SAAWD,EACT,OAAI0iB,GAAS1iB,CAAQ,EACfA,EAAS,MACJ4iB,GAAiB,SAAUiC,EAAK9K,EAAO,CAC5C,OAAOA,EAAM,IAAI8K,EAAI,SAAU,WAAW,CAC5C,CAAC,EAEMjC,GAAiB,UAAY,CAClC,OAAO9B,EACT,CAAC,EAGI,IAAIuB,GACTriB,EAAS,QACTA,EAAS,WACTA,EAAS,QACT,SAAU6kB,EAAK9K,EAAO,CACpB,IAAI/Z,EAAW6kB,EAAI,SACnB,OAAO9K,EAAM,IAAI/Z,EAAU,IAAKA,EAAU,aAAc8gB,EAAc,CACxE,CAAC,EAGP,OAAO,IACT,CAEA,SAASoH,EAAYp/B,EAAOq/B,EAAU,CACpC,GAAIr/B,KAASy8B,EAAe,CAC1B,IAAIp8B,EAAQo8B,EAAcz8B,CAAK,EAAI,EACnC,OAAA8H,EAAQ,QAAQ,CAACu3B,GAAYh/B,GAAS,EAAG,WAAaL,EAAO+7B,EAAI,UAAU,EACpEjC,GAAiB,SAAUiC,EAAK9K,EAAO,CAC5C,OAAIoO,IACFtD,EAAI,OAAS17B,GAERA,CACT,CAAC,CACH,SAAWL,KAAS08B,EAAgB,CAClC,IAAI4C,EAAW5C,EAAe18B,CAAK,EACnC,OAAO+5B,GAAkBuF,EAAU,SAAUvD,EAAK9K,EAAO,CACvD,IAAIzuB,EAASu5B,EAAI,OAAO9K,EAAOqO,CAAQ,EACvC,OAAID,IACFtD,EAAI,OAASv5B,EACbsF,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACTzuB,EAAS,MACT,WAAaxC,CAAK,CACtB,CAAC,GAEIwC,CACT,CAAC,CACH,SAAW68B,GAAYnoB,EACrB,OAAO4iB,GAAiB,SAAUiC,EAAK9K,EAAO,CAC5C,OAAA8K,EAAI,OAAS,IACN,CACT,CAAC,EAEH,OAAO,IACT,CAEA,IAAIwD,EAASH,EAAWjK,GAAU,EAAI,EAEtC,SAASqK,GAAkB,CACzB,GAAItK,MAAWuH,EAAe,CAC5B,IAAIrlB,EAAQqlB,EAAcvH,EAAO,EAAI,EACrC,OAAAptB,EAAQ,QACN,OAAOsP,GAAU,UAAYA,GAAS,EAAG,uBAAwB2kB,EAAI,UAAU,EAC1EjC,GAAiB,UAAY,CAClC,OAAO1iB,CACT,CAAC,CACH,SAAW8d,MAAWwH,EAAgB,CACpC,IAAI+C,EAAW/C,EAAexH,EAAO,EACrC,OAAO6E,GAAkB0F,EAAU,SAAU1D,EAAK9K,EAAO,CACvD,IAAIzuB,EAASu5B,EAAI,OAAO9K,EAAOwO,CAAQ,EACvC,OAAA33B,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT,UAAYzuB,EAAS,gBACrBA,EAAS,QACTA,EAAS,OAASA,EAAS,MAC3B,sBAAsB,CAC1B,CAAC,EACMA,CACT,CAAC,CACH,SAAW0U,EACT,GAAI0iB,GAAS1iB,CAAQ,EAAG,CACtB,GAAIA,EACF,OAAIqoB,EACK,IAAIhG,GACTgG,EAAO,QACPA,EAAO,WACPA,EAAO,QACP,SAAUxD,EAAK9K,EAAO,CACpB,IAAIzuB,EAASyuB,EAAM,IACjB8K,EAAI,SAAU,cAAeA,EAAI,MAAM,EAEzC,OAAAj0B,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACTzuB,EAAS,MACT,gDAAgD,CACpD,CAAC,EAEMA,CACT,CAAC,EAEIs3B,GAAiB,SAAUiC,EAAK9K,EAAO,CAC5C,OAAOA,EAAM,IAAI8K,EAAI,SAAU,YAAY,CAC7C,CAAC,EAGH,IAAIv5B,EAASs3B,GAAiB,UAAY,CACxC,MAAO,EACT,CAAC,EACD,OAAAhyB,EAAQ,SAAS,UAAY,CAC3BtF,EAAO,QAAU,EACnB,CAAC,EACMA,CAEX,KAAO,CACL,IAAI45B,EAAW,IAAI7C,GACjBriB,EAAS,SAAWqoB,EAAO,QAC3BroB,EAAS,YAAcqoB,EAAO,WAC9BroB,EAAS,SAAWqoB,EAAO,QAC3B,SAAUxD,EAAK9K,EAAO,CACpB,IAAI/Z,EAAW6kB,EAAI,SACnB,OAAIA,EAAI,OACC9K,EAAM,IAAI/Z,EAAU,IAAKA,EAAU,cACxC6kB,EAAI,OAAQ,KAAK,EAEd9K,EAAM,IAAI/Z,EAAU,IAAKA,EAAU,eAAe,CAC3D,CAAC,EACH,OAAApP,EAAQ,SAAS,UAAY,CAC3Bs0B,EAAS,QAAU,EACrB,CAAC,EACMA,CACT,CAEF,OAAO,IACT,CAEA,MAAO,CACL,SAAUllB,EACV,UAAW8nB,EAAe,EAC1B,MAAOQ,EAAe,EACtB,UAAWJ,EAAWhK,GAAa,EAAK,EACxC,OAAQmK,CACV,CACF,CAEA,SAASG,GAAc9qB,EAASmnB,EAAK,CACnC,IAAIU,EAAgB7nB,EAAQ,OACxB8nB,EAAiB9nB,EAAQ,QAEzB+qB,EAAQ,CAAC,EAEb,OAAA1E,EAAe,QAAQ,SAAU1J,EAAM,CACrC,IAAIvxB,EAAQo7B,EAAS7J,CAAI,EAEzB,SAAS6N,EAAYQ,EAAaC,EAAc,CAC9C,GAAItO,KAAQkL,EAAe,CACzB,IAAIp8B,EAAQu/B,EAAYnD,EAAclL,CAAI,CAAC,EAC3CoO,EAAM3/B,CAAK,EAAI85B,GAAiB,UAAY,CAC1C,OAAOz5B,CACT,CAAC,CACH,SAAWkxB,KAAQmL,EAAgB,CACjC,IAAI1C,EAAM0C,EAAenL,CAAI,EAC7BoO,EAAM3/B,CAAK,EAAI+5B,GAAkBC,EAAK,SAAU+B,EAAK9K,EAAO,CAC1D,OAAO4O,EAAa9D,EAAK9K,EAAO8K,EAAI,OAAO9K,EAAO+I,CAAG,CAAC,CACxD,CAAC,CACH,CACF,CAEA,OAAQzI,EAAM,CACZ,KAAKoC,GACL,KAAKT,GACL,KAAKD,GACL,KAAKmB,GACL,KAAKd,GACL,KAAKmB,GACL,KAAKV,GACL,KAAKE,GACL,KAAKC,GACL,KAAKT,GACH,OAAO2L,EACL,SAAU/+B,EAAO,CACf,OAAAyH,EAAQ,YAAYzH,EAAO,UAAWkxB,EAAMwK,EAAI,UAAU,EACnD17B,CACT,EACA,SAAU07B,EAAK9K,EAAO5wB,EAAO,CAC3B,OAAAyH,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT,UAAY5wB,EAAQ,eACpB,gBAAkBkxB,EAAMwK,EAAI,UAAU,CAC1C,CAAC,EACM17B,CACT,CAAC,EAEL,KAAKkzB,GACH,OAAO6L,EACL,SAAU/+B,EAAO,CACf,OAAAyH,EAAQ,iBAAiBzH,EAAO44B,GAAc,WAAa1H,EAAMwK,EAAI,UAAU,EACxE9C,GAAa54B,CAAK,CAC3B,EACA,SAAU07B,EAAK9K,EAAO5wB,EAAO,CAC3B,IAAIy/B,EAAgB/D,EAAI,UAAU,aAClC,OAAAj0B,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT5wB,EAAQ,OAASy/B,EACjB,WAAavO,EAAO,oBAAsB,OAAO,KAAK0H,EAAY,CAAC,CACvE,CAAC,EACMhI,EAAM,IAAI6O,EAAe,IAAKz/B,EAAO,GAAG,CACjD,CAAC,EAEL,KAAKmzB,GACH,OAAO4L,EACL,SAAU/+B,EAAO,CACf,OAAAyH,EAAQ,QACNsQ,GAAY/X,CAAK,GACjBA,EAAM,SAAW,GACjB,OAAOA,EAAM,CAAC,GAAM,UACpB,OAAOA,EAAM,CAAC,GAAM,UACpBA,EAAM,CAAC,GAAKA,EAAM,CAAC,EACnB,0BACA07B,EAAI,UAAU,EACT17B,CACT,EACA,SAAU07B,EAAK9K,EAAO5wB,EAAO,CAC3ByH,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT8K,EAAI,OAAO,YAAc,IAAM17B,EAAQ,MACvCA,EAAQ,uBACIA,EAAQ,0BACRA,EAAQ,mBACpBA,EAAQ,QAAUA,EAAQ,MAC1B,gCAAgC,CACpC,CAAC,EAED,IAAI0/B,EAAS9O,EAAM,IAAI,IAAK5wB,EAAO,KAAK,EACpC2/B,EAAQ/O,EAAM,IAAI,IAAK5wB,EAAO,KAAK,EACvC,MAAO,CAAC0/B,EAAQC,CAAK,CACvB,CAAC,EAEL,KAAK3M,GACH,OAAO+L,EACL,SAAU/+B,EAAO,CACfyH,EAAQ,YAAYzH,EAAO,SAAU,aAAc07B,EAAI,UAAU,EACjE,IAAIkE,EAAU,WAAY5/B,EAAQA,EAAM,OAASA,EAAM,IACnD6/B,EAAY,aAAc7/B,EAAQA,EAAM,SAAWA,EAAM,IACzD8/B,EAAU,WAAY9/B,EAAQA,EAAM,OAASA,EAAM,IACnD+/B,EAAY,aAAc//B,EAAQA,EAAM,SAAWA,EAAM,IAC7D,OAAAyH,EAAQ,iBAAiBm4B,EAAQlH,GAAY/4B,EAAQ,UAAW+7B,EAAI,UAAU,EAC9Ej0B,EAAQ,iBAAiBo4B,EAAUnH,GAAY/4B,EAAQ,YAAa+7B,EAAI,UAAU,EAClFj0B,EAAQ,iBAAiBq4B,EAAQpH,GAAY/4B,EAAQ,UAAW+7B,EAAI,UAAU,EAC9Ej0B,EAAQ,iBAAiBs4B,EAAUrH,GAAY/4B,EAAQ,YAAa+7B,EAAI,UAAU,EAElFj0B,EAAQ,QACLkxB,GAAyB,QAAQiH,EAAS,KAAOE,CAAM,IAAM,GAC9D,sDAAwDF,EAAS,KAAOE,EAAS,IAAKpE,EAAI,UAAU,EAE/F,CACLhD,GAAWkH,CAAM,EACjBlH,GAAWoH,CAAM,EACjBpH,GAAWmH,CAAQ,EACnBnH,GAAWqH,CAAQ,CACrB,CACF,EACA,SAAUrE,EAAK9K,EAAO5wB,EAAO,CAC3B,IAAIggC,EAActE,EAAI,UAAU,WAEhCj0B,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT5wB,EAAQ,YAAcA,EAAQ,cAC9B,uCAAuC,CAC3C,CAAC,EAED,SAASigC,EAAMC,EAAQC,GAAQ,CAC7B,IAAI9E,GAAOzK,EAAM,IACf,IAAKsP,EAAQC,GAAQ,QAASngC,EAC9B,IAAKA,EAAO,IAAKkgC,EAAQC,GACzB,IAAKngC,EAAO,IAAKkgC,CAAM,EAEzB,OAAAz4B,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACTyK,GAAO,OAAS2E,EAChB,WAAa9O,EAAO,IAAMgP,EAASC,GAAS,oBAAsB,OAAO,KAAKzH,EAAU,CAAC,CAC7F,CAAC,EAEM2C,EACT,CAEA,IAAIuE,EAASK,EAAK,MAAO,KAAK,EAC1BH,EAASG,EAAK,MAAO,KAAK,EAE9Bx4B,EAAQ,SAAS,UAAY,CAC3B,IAAI24B,EAA6B1E,EAAI,UAAU,yBAE/CA,EAAI,OAAO9K,EACTwP,EACS,YAAcR,EAAS,SAAWE,EAAS,YACpD,qDACF,CACF,CAAC,EAED,IAAIO,EAAUzP,EAAM,IAAIoP,EAAa,IAAKJ,EAAQ,GAAG,EACjDU,EAAY1P,EAAM,IAAIoP,EAAa,IAAKC,EAAK,MAAO,OAAO,EAAG,GAAG,EACjEM,EAAU3P,EAAM,IAAIoP,EAAa,IAAKF,EAAQ,GAAG,EACjDU,GAAY5P,EAAM,IAAIoP,EAAa,IAAKC,EAAK,MAAO,OAAO,EAAG,GAAG,EAErE,MAAO,CAACI,EAASE,EAASD,EAAWE,EAAS,CAChD,CAAC,EAEL,KAAKzN,GACH,OAAOgM,EACL,SAAU/+B,EAAO,CACf,GAAI,OAAOA,GAAU,SACnB,OAAAyH,EAAQ,iBAAiBzH,EAAOu6B,EAAgB,WAAarJ,EAAMwK,EAAI,UAAU,EAC1E,CACLnB,EAAev6B,CAAK,EACpBu6B,EAAev6B,CAAK,CACtB,EACK,GAAI,OAAOA,GAAU,SAC1B,OAAAyH,EAAQ,iBACNzH,EAAM,IAAKu6B,EAAgBrJ,EAAO,OAAQwK,EAAI,UAAU,EAC1Dj0B,EAAQ,iBACNzH,EAAM,MAAOu6B,EAAgBrJ,EAAO,SAAUwK,EAAI,UAAU,EACvD,CACLnB,EAAev6B,EAAM,GAAG,EACxBu6B,EAAev6B,EAAM,KAAK,CAC5B,EAEAyH,EAAQ,aAAa,yBAA0Bi0B,EAAI,UAAU,CAEjE,EACA,SAAUA,EAAK9K,EAAO5wB,EAAO,CAC3B,IAAIygC,EAAkB/E,EAAI,UAAU,eAEhCgF,EAAM9P,EAAM,IAAI,EAChB+P,EAAQ/P,EAAM,IAAI,EAElB8N,EAAOhD,EAAI,KAAK,UAAW17B,EAAO,aAAa,EAEnD,OAAAyH,EAAQ,SAAS,UAAY,CAC3B,SAASm5B,EAAWh8B,EAAOkF,EAAM9J,GAAO,CACtC07B,EAAI,OAAO92B,EACT5E,GAAQ,OAASygC,EACjB,WAAa32B,EAAO,oBAAsB,OAAO,KAAKywB,CAAc,CAAC,CACzE,CACAqG,EAAUlC,EAAK,KAAMxN,EAAMlxB,CAAK,EAEhC07B,EAAI,OAAOgD,EAAK,KACd1+B,EAAQ,YAAcA,EAAQ,cAC9B,WAAakxB,CAAI,EACnB0P,EAAUlC,EAAK,KAAMxN,EAAO,OAAQlxB,EAAQ,MAAM,EAClD4gC,EAAUlC,EAAK,KAAMxN,EAAO,SAAUlxB,EAAQ,QAAQ,CACxD,CAAC,EAED0+B,EAAK,KACHgC,EAAK,IAAKC,EAAO,IAAKF,EAAiB,IAAKzgC,EAAO,IAAI,EACzD0+B,EAAK,KACHgC,EAAK,IAAKD,EAAiB,IAAKzgC,EAAO,SACvC2gC,EAAO,IAAKF,EAAiB,IAAKzgC,EAAO,UAAU,EAErD4wB,EAAM8N,CAAI,EAEH,CAACgC,EAAKC,CAAK,CACpB,CAAC,EAEL,KAAK7N,GACH,OAAOiM,EACL,SAAU/+B,EAAO,CACf,OAAAyH,EAAQ,QACNsQ,GAAY/X,CAAK,GACjBA,EAAM,SAAW,EACjB,iCAAkC07B,EAAI,UAAU,EAC3CvwB,GAAK,EAAG,SAAUnM,EAAG,CAC1B,MAAO,CAACgB,EAAMhB,CAAC,CACjB,CAAC,CACH,EACA,SAAU08B,EAAK9K,EAAO5wB,EAAO,CAC3B,OAAAyH,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT8K,EAAI,OAAO,YAAc,IAAM17B,EAAQ,MACvCA,EAAQ,cACR,gCAAgC,CACpC,CAAC,EACMmL,GAAK,EAAG,SAAUnM,EAAG,CAC1B,OAAO4xB,EAAM,IAAI,IAAK5wB,EAAO,IAAKhB,EAAG,GAAG,CAC1C,CAAC,CACH,CAAC,EAEL,KAAKg1B,GACH,OAAO+K,EACL,SAAU/+B,EAAO,CACf,OAAAyH,EAAQ,YAAYzH,EAAO,SAAUL,EAAO+7B,EAAI,UAAU,EACnD17B,EAAQ,CACjB,EACA,SAAU07B,EAAK9K,EAAO5wB,EAAO,CAC3B,OAAAyH,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT,UAAY5wB,EAAQ,cACpB,sBAAsB,CAC1B,CAAC,EACM4wB,EAAM,IAAI5wB,EAAO,IAAI,CAC9B,CAAC,EAEL,KAAKi0B,GACH,OAAO8K,EACL,SAAU/+B,EAAO,CACfyH,EAAQ,YAAYzH,EAAO,SAAUL,EAAO+7B,EAAI,UAAU,EAC1D,IAAImF,EAAM7gC,EAAM,KAAO,OACnB8gC,EAAM9gC,EAAM,KAAO,EACnB+gC,EAAO,SAAU/gC,EAAQA,EAAM,KAAO,GAC1C,OAAAyH,EAAQ,iBAAiBo5B,EAAKjI,GAAc1H,EAAO,OAAQwK,EAAI,UAAU,EACzEj0B,EAAQ,YAAYq5B,EAAK,SAAU5P,EAAO,OAAQwK,EAAI,UAAU,EAChEj0B,EAAQ,YAAYs5B,EAAM,SAAU7P,EAAO,QAASwK,EAAI,UAAU,EAC3D,CACL9C,GAAaiI,CAAG,EAChBC,EACAC,CACF,CACF,EACA,SAAUrF,EAAK9K,EAAO5wB,EAAO,CAC3B,IAAIy/B,EAAgB/D,EAAI,UAAU,aAClCj0B,EAAQ,SAAS,UAAY,CAC3B,SAASu5B,GAAU,CACjBtF,EAAI,OAAO9K,EACT,MAAM,UAAU,KAAK,KAAK,UAAW,EAAE,EACvC,sBAAsB,CAC1B,CACAoQ,EAAOhhC,EAAQ,YAAaA,EAAO,aAAa,EAChDghC,EAAO,cAAehhC,EAAO,OAC3BA,EAAO,WAAYy/B,EAAe,GAAG,CACzC,CAAC,EACD,IAAIoB,EAAMjQ,EAAM,IACd,YAAa5wB,EACb,IAAKy/B,EAAe,IAAKz/B,EAAO,QAChC,IAAKm4B,EAAO,EACV2I,EAAMlQ,EAAM,IAAI5wB,EAAO,QAAQ,EAC/B+gC,EAAOnQ,EAAM,IACf,aAAc5wB,EACd,IAAKA,EAAO,YAAY,EAC1B,MAAO,CAAC6gC,EAAKC,EAAKC,CAAI,CACxB,CAAC,EAEL,KAAK7M,GACL,KAAKC,GACH,OAAO4K,EACL,SAAU/+B,EAAO,CACfyH,EAAQ,YAAYzH,EAAO,SAAUL,EAAO+7B,EAAI,UAAU,EAC1D,IAAIuF,EAAOjhC,EAAM,MAAQ,OACrBkhC,EAAQlhC,EAAM,OAAS,OACvBmhC,EAAQnhC,EAAM,OAAS,OAC3B,OAAAyH,EAAQ,iBAAiBw5B,EAAMpI,GAAY3H,EAAO,QAASwK,EAAI,UAAU,EACzEj0B,EAAQ,iBAAiBy5B,EAAOrI,GAAY3H,EAAO,SAAUwK,EAAI,UAAU,EAC3Ej0B,EAAQ,iBAAiB05B,EAAOtI,GAAY3H,EAAO,SAAUwK,EAAI,UAAU,EACpE,CACLxK,IAASiD,GAAmB0D,GAAUD,GACtCiB,GAAWoI,CAAI,EACfpI,GAAWqI,CAAK,EAChBrI,GAAWsI,CAAK,CAClB,CACF,EACA,SAAUzF,EAAK9K,EAAO5wB,EAAO,CAC3B,IAAIohC,EAAc1F,EAAI,UAAU,WAEhCj0B,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT5wB,EAAQ,YAAcA,EAAQ,cAC9B,WAAakxB,CAAI,CACrB,CAAC,EAED,SAAS+O,EAAMn2B,EAAM,CACnB,OAAArC,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT,MAAQ9mB,EAAO,QAAU9J,EAAQ,OAC3BA,EAAQ,IAAM8J,EAAO,OAASs3B,EAAc,IAClD,WAAalQ,EAAO,IAAMpnB,EAAO,oBAAsB,OAAO,KAAK+uB,EAAU,CAAC,CAClF,CAAC,EAEMjI,EAAM,IACX,IAAK9mB,EAAM,QAAS9J,EACpB,IAAKohC,EAAa,IAAKphC,EAAO,IAAK8J,EAAM,KACzCquB,EAAO,CACX,CAEA,MAAO,CACLjH,IAASiD,GAAmB0D,GAAUD,GACtCqI,EAAK,MAAM,EACXA,EAAK,OAAO,EACZA,EAAK,OAAO,CACd,CACF,CAAC,EAEL,KAAKtM,GACH,OAAOoL,EACL,SAAU/+B,EAAO,CACfyH,EAAQ,YAAYzH,EAAO,SAAUL,EAAO+7B,EAAI,UAAU,EAC1D,IAAI2F,EAASrhC,EAAM,OAAS,EACxBshC,EAAQthC,EAAM,MAAQ,EAC1B,OAAAyH,EAAQ,YAAY45B,EAAQ,SAAU1hC,EAAQ,UAAW+7B,EAAI,UAAU,EACvEj0B,EAAQ,YAAY65B,EAAO,SAAU3hC,EAAQ,SAAU+7B,EAAI,UAAU,EAC9D,CAAC2F,EAAQC,CAAK,CACvB,EACA,SAAU5F,EAAK9K,EAAO5wB,EAAO,CAC3ByH,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT5wB,EAAQ,YAAcA,EAAQ,cAC9B,WAAakxB,CAAI,CACrB,CAAC,EAED,IAAIqQ,EAAS3Q,EAAM,IAAI5wB,EAAO,WAAW,EACrCwhC,EAAQ5Q,EAAM,IAAI5wB,EAAO,UAAU,EAEvC,MAAO,CAACuhC,EAAQC,CAAK,CACvB,CAAC,EAEL,KAAKjO,GACH,OAAOwL,EACL,SAAU/+B,EAAO,CACf,IAAIsH,EAAO,EACX,OAAItH,IAAU,QACZsH,EAAOswB,GACE53B,IAAU,SACnBsH,EAAOuwB,IAETpwB,EAAQ,QAAQ,CAAC,CAACH,EAAM3H,EAAO+7B,EAAI,UAAU,EACtCp0B,CACT,EACA,SAAUo0B,EAAK9K,EAAO5wB,EAAO,CAC3B,OAAAyH,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT5wB,EAAQ,eACRA,EAAQ,YACR,mBAAmB,CACvB,CAAC,EACM4wB,EAAM,IAAI5wB,EAAO,cAAe43B,GAAU,IAAKC,EAAO,CAC/D,CAAC,EAEL,KAAKpE,GACH,OAAOsL,EACL,SAAU/+B,EAAO,CACf,OAAAyH,EAAQ,QACN,OAAOzH,GAAU,UACjBA,GAAS2G,EAAO,cAAc,CAAC,GAC/B3G,GAAS2G,EAAO,cAAc,CAAC,EAC/B,yDACAA,EAAO,cAAc,CAAC,EAAI,QAAUA,EAAO,cAAc,CAAC,EAAG+0B,EAAI,UAAU,EACtE17B,CACT,EACA,SAAU07B,EAAK9K,EAAO5wB,EAAO,CAC3B,OAAAyH,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT,UAAY5wB,EAAQ,gBACpBA,EAAQ,KAAO2G,EAAO,cAAc,CAAC,EAAI,KACzC3G,EAAQ,KAAO2G,EAAO,cAAc,CAAC,EACrC,oBAAoB,CACxB,CAAC,EAEM3G,CACT,CAAC,EAEL,KAAKwzB,GACH,OAAOuL,EACL,SAAU/+B,EAAO,CACf,OAAAyH,EAAQ,iBAAiBzH,EAAO+4B,GAAiBp5B,EAAO+7B,EAAI,UAAU,EAC/D3C,GAAgB/4B,CAAK,CAC9B,EACA,SAAU07B,EAAK9K,EAAO5wB,EAAO,CAC3B,OAAAyH,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT5wB,EAAQ,YACRA,EAAQ,WACR,0CAA0C,CAC9C,CAAC,EACM4wB,EAAM,IAAI5wB,EAAQ,WAAa83B,GAAQ,IAAMC,EAAM,CAC5D,CAAC,EAEL,KAAK1E,GACH,OAAO0L,EACL,SAAU/+B,EAAO,CACf,OAAAyH,EAAQ,QACNsQ,GAAY/X,CAAK,GAAKA,EAAM,SAAW,EACvC,oCAAqC07B,EAAI,UAAU,EAC9C17B,EAAM,IAAI,SAAUuG,EAAG,CAAE,MAAO,CAAC,CAACA,CAAE,CAAC,CAC9C,EACA,SAAUm1B,EAAK9K,EAAO5wB,EAAO,CAC3B,OAAAyH,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT8K,EAAI,OAAO,YAAc,IAAM17B,EAAQ,MACvCA,EAAQ,cACR,oBAAoB,CACxB,CAAC,EACMmL,GAAK,EAAG,SAAUnM,EAAG,CAC1B,MAAO,KAAOgB,EAAQ,IAAMhB,EAAI,GAClC,CAAC,CACH,CAAC,EAEL,KAAK80B,GACH,OAAOiL,EACL,SAAU/+B,EAAO,CACfyH,EAAQ,QAAQ,OAAOzH,GAAU,UAAYA,EAAOL,EAAO+7B,EAAI,UAAU,EACzE,IAAI+F,EAAc,UAAWzhC,EAAQA,EAAM,MAAQ,EAC/C0hC,EAAe,CAAC,CAAC1hC,EAAM,OAC3B,OAAAyH,EAAQ,QACN,OAAOg6B,GAAgB,UACvBA,GAAe,GAAKA,GAAe,EACnC,yDAA0D/F,EAAI,UAAU,EACnE,CAAC+F,EAAaC,CAAY,CACnC,EACA,SAAUhG,EAAK9K,EAAO5wB,EAAO,CAC3ByH,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACT5wB,EAAQ,YAAcA,EAAQ,cAC9B,yBAAyB,CAC7B,CAAC,EACD,IAAI2hC,EAAQ/Q,EAAM,IAChB,cAAe5wB,EAAO,KAAMA,EAAO,UAAU,EAC3C4hC,EAAShR,EAAM,IAAI,KAAM5wB,EAAO,SAAS,EAC7C,MAAO,CAAC2hC,EAAOC,CAAM,CACvB,CAAC,CACP,CACF,CAAC,EAEMtC,CACT,CAEA,SAASuC,GAAeh+B,EAAU63B,EAAK,CACrC,IAAIoG,EAAiBj+B,EAAS,OAC1Bk+B,EAAkBl+B,EAAS,QAE3Bm+B,EAAW,CAAC,EAEhB,cAAO,KAAKF,CAAc,EAAE,QAAQ,SAAUh4B,EAAM,CAClD,IAAI9J,EAAQ8hC,EAAeh4B,CAAI,EAC3B3H,EACJ,GAAI,OAAOnC,GAAU,UACjB,OAAOA,GAAU,UACnBmC,EAASs3B,GAAiB,UAAY,CACpC,OAAOz5B,CACT,CAAC,UACQ,OAAOA,GAAU,WAAY,CACtC,IAAIiiC,EAAWjiC,EAAM,UACjBiiC,IAAa,aACbA,IAAa,cACf9/B,EAASs3B,GAAiB,SAAUiC,EAAK,CACvC,OAAOA,EAAI,KAAK17B,CAAK,CACvB,CAAC,EACQiiC,IAAa,eACbA,IAAa,mBACtBx6B,EAAQ,QAAQzH,EAAM,MAAM,OAAS,EACnC,6DAA+D8J,EAAO,IAAK4xB,EAAI,UAAU,EAC3Fv5B,EAASs3B,GAAiB,SAAUiC,EAAK,CACvC,OAAOA,EAAI,KAAK17B,EAAM,MAAM,CAAC,CAAC,CAChC,CAAC,GAEDyH,EAAQ,aAAa,6BAA+BqC,EAAO,IAAK4xB,EAAI,UAAU,CAElF,MAAW3jB,GAAY/X,CAAK,EAC1BmC,EAASs3B,GAAiB,SAAUiC,EAAK,CACvC,IAAIwG,EAAOxG,EAAI,OAAO,IAAI,IACxBvwB,GAAKnL,EAAM,OAAQ,SAAUhB,EAAG,CAC9B,OAAAyI,EAAQ,QACN,OAAOzH,EAAMhB,CAAC,GAAM,UACpB,OAAOgB,EAAMhB,CAAC,GAAM,UACpB,mBAAqB8K,EAAM4xB,EAAI,UAAU,EACpC17B,EAAMhB,CAAC,CAChB,CAAC,EAAG,GAAG,EACT,OAAOkjC,CACT,CAAC,EAEDz6B,EAAQ,aAAa,wCAA0CqC,EAAO,IAAK4xB,EAAI,UAAU,EAE3Fv5B,EAAO,MAAQnC,EACfgiC,EAASl4B,CAAI,EAAI3H,CACnB,CAAC,EAED,OAAO,KAAK4/B,CAAe,EAAE,QAAQ,SAAUthC,EAAK,CAClD,IAAIk5B,EAAMoI,EAAgBthC,CAAG,EAC7BuhC,EAASvhC,CAAG,EAAIi5B,GAAkBC,EAAK,SAAU+B,EAAK9K,EAAO,CAC3D,OAAO8K,EAAI,OAAO9K,EAAO+I,CAAG,CAC9B,CAAC,CACH,CAAC,EAEMqI,CACT,CAEA,SAASG,GAAiBr+B,EAAY43B,EAAK,CACzC,IAAIgC,EAAmB55B,EAAW,OAC9Bs+B,EAAoBt+B,EAAW,QAE/Bu+B,EAAgB,CAAC,EAErB,cAAO,KAAK3E,CAAgB,EAAE,QAAQ,SAAU4E,EAAW,CACzD,IAAItiC,EAAQ09B,EAAiB4E,CAAS,EAClCt+B,EAAKD,EAAY,GAAGu+B,CAAS,EAE7BxV,EAAS,IAAId,EACjB,GAAIgN,GAAah5B,CAAK,EACpB8sB,EAAO,MAAQqF,GACfrF,EAAO,OAASzW,EAAY,UAC1BA,EAAY,OAAOrW,EAAO21B,GAAmB,GAAO,EAAI,CAAC,EAC3D7I,EAAO,KAAO,MACT,CACL,IAAItZ,EAAS6C,EAAY,UAAUrW,CAAK,EACxC,GAAIwT,EACFsZ,EAAO,MAAQqF,GACfrF,EAAO,OAAStZ,EAChBsZ,EAAO,KAAO,UAEdrlB,EAAQ,QAAQ,OAAOzH,GAAU,UAAYA,EAC3C,8BAAgCsiC,EAAW5G,EAAI,UAAU,EACvD,aAAc17B,EAAO,CACvB,IAAIuiC,EAAWviC,EAAM,SACrB8sB,EAAO,OAAS,OAChBA,EAAO,MAAQsF,GACX,OAAOmQ,GAAa,SACtBzV,EAAO,EAAIyV,GAEX96B,EAAQ,QACNsQ,GAAYwqB,CAAQ,GACpBA,EAAS,OAAS,GAClBA,EAAS,QAAU,EACnB,kCAAoCD,EAAW5G,EAAI,UAAU,EAC/DzJ,GAAgB,QAAQ,SAAUprB,EAAG7H,GAAG,CAClCA,GAAIujC,EAAS,SACfzV,EAAOjmB,CAAC,EAAI07B,EAASvjC,EAAC,EAE1B,CAAC,EAEL,KAAO,CACDg6B,GAAah5B,EAAM,MAAM,EAC3BwT,EAAS6C,EAAY,UACnBA,EAAY,OAAOrW,EAAM,OAAQ21B,GAAmB,GAAO,EAAI,CAAC,EAElEniB,EAAS6C,EAAY,UAAUrW,EAAM,MAAM,EAE7CyH,EAAQ,QAAQ,CAAC,CAAC+L,EAAQ,iCAAmC8uB,EAAY,IAAK5G,EAAI,UAAU,EAE5F,IAAI14B,EAAShD,EAAM,OAAS,EAC5ByH,EAAQ,QAAQzE,GAAU,EACxB,iCAAmCs/B,EAAY,IAAK5G,EAAI,UAAU,EAEpE,IAAI1qB,EAAShR,EAAM,OAAS,EAC5ByH,EAAQ,QAAQuJ,GAAU,GAAKA,EAAS,IACtC,iCAAmCsxB,EAAY,uCAAwC5G,EAAI,UAAU,EAEvG,IAAI3L,EAAO/vB,EAAM,KAAO,EACxByH,EAAQ,QAAQ,EAAE,SAAUzH,IAAW+vB,EAAO,GAAKA,GAAQ,EACzD,+BAAiCuS,EAAY,qBAAsB5G,EAAI,UAAU,EAEnF,IAAI8G,EAAa,CAAC,CAACxiC,EAAM,WAErBC,EAAO,EACP,SAAUD,IACZyH,EAAQ,iBACNzH,EAAM,KAAM4R,GACZ,8BAAgC0wB,EAAW5G,EAAI,UAAU,EAC3Dz7B,EAAO2R,GAAQ5R,EAAM,IAAI,GAG3B,IAAIyiC,EAAUziC,EAAM,QAAU,EAC1B,YAAaA,IACfyH,EAAQ,QAAQg7B,IAAY,GAAKjI,EAC/B,yCAA2C8H,EAAY,8BAA+B5G,EAAI,UAAU,EACtGj0B,EAAQ,QAAQg7B,GAAW,EACzB,kCAAoCH,EAAY,IAAK5G,EAAI,UAAU,GAGvEj0B,EAAQ,SAAS,UAAY,CAC3B,IAAI/F,EAAUg6B,EAAI,WAEdgH,GAAa,CACf,SACA,SACA,UACA,aACA,OACA,OACA,QACF,EAEA,OAAO,KAAK1iC,CAAK,EAAE,QAAQ,SAAUkxB,EAAM,CACzCzpB,EAAQ,QACNi7B,GAAW,QAAQxR,CAAI,GAAK,EAC5B,sBAAwBA,EAAO,4BAA8BoR,EAAY,2BAA6BI,GAAa,IACnHhhC,CAAO,CACX,CAAC,CACH,CAAC,EAEDorB,EAAO,OAAStZ,EAChBsZ,EAAO,MAAQqF,GACfrF,EAAO,KAAOiD,EACdjD,EAAO,WAAa0V,EACpB1V,EAAO,KAAO7sB,GAAQuT,EAAO,MAC7BsZ,EAAO,OAAS9pB,EAChB8pB,EAAO,OAAS9b,EAChB8b,EAAO,QAAU2V,CACnB,CAEJ,CAEAJ,EAAcC,CAAS,EAAI7I,GAAiB,SAAUiC,EAAK9K,GAAO,CAChE,IAAItC,EAAQoN,EAAI,YAChB,GAAI13B,KAAMsqB,EACR,OAAOA,EAAMtqB,CAAE,EAEjB,IAAI7B,GAAS,CACX,SAAU,EACZ,EACA,cAAO,KAAK2qB,CAAM,EAAE,QAAQ,SAAUrsB,GAAK,CACzC0B,GAAO1B,EAAG,EAAIqsB,EAAOrsB,EAAG,CAC1B,CAAC,EACGqsB,EAAO,SACT3qB,GAAO,OAASu5B,EAAI,KAAK5O,EAAO,MAAM,EACtC3qB,GAAO,KAAOA,GAAO,MAASA,GAAO,OAAS,UAEhDmsB,EAAMtqB,CAAE,EAAI7B,GACLA,EACT,CAAC,CACH,CAAC,EAED,OAAO,KAAKigC,CAAiB,EAAE,QAAQ,SAAUE,EAAW,CAC1D,IAAI3I,EAAMyI,EAAkBE,CAAS,EAErC,SAASK,EAAqBjH,EAAK92B,EAAO,CACxC,IAAI+8B,EAAQjG,EAAI,OAAO92B,EAAO+0B,CAAG,EAE7BiC,EAASF,EAAI,OACbM,EAAYN,EAAI,UAEhB4C,EAAiB1C,EAAO,aACxBgH,EAAehH,EAAO,OAG1Bn0B,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO92B,EACT+8B,EAAQ,aAAeA,EAAQ,uBAC/BA,EAAQ,oBACRrD,EAAiB,IAAMqD,EAAQ,MAC/BiB,EAAe,cAAgBjB,EAAQ,MACvCiB,EAAe,cAAgBjB,EAAQ,aACvCrD,EAAiB,IAAMqD,EAAQ,4BACXA,EACpB,aAAeA,EAAQ,yBACvB/F,EAAO,YAAc,IAAM+F,EAAQ,gBACnC,8BAAgCW,EAAY,GAAG,CACnD,CAAC,EAGD,IAAIngC,EAAS,CACX,SAAUyC,EAAM,IAAI,EAAK,CAC3B,EACIi+B,EAAgB,IAAI7W,EACxB6W,EAAc,MAAQ1Q,GACtB,OAAO,KAAK0Q,CAAa,EAAE,QAAQ,SAAUpiC,GAAK,CAChD0B,EAAO1B,EAAG,EAAImE,EAAM,IAAI,GAAKi+B,EAAcpiC,EAAG,CAAC,CACjD,CAAC,EAED,IAAIqiC,EAAS3gC,EAAO,OAChB4gC,GAAO5gC,EAAO,KAClByC,EACE,MAAO05B,EAAgB,IAAKqD,EAAO,MACnCx/B,EAAO,SAAU,SACjB2gC,EAAQ,IAAKF,EAAc,iBAAkBjN,GAAmB,IAAKgM,EAAO,KAC5EoB,GAAM,IAAKD,EAAQ,UACnB,SACAA,EAAQ,IAAKF,EAAc,cAAejB,EAAO,KACjD,MAAOmB,EAAQ,KACfC,GAAM,IAAKD,EAAQ,UACnB,0BAA2BnB,EAAO,KAClCx/B,EAAO,MAAO,IAAKiwB,GAAuB,IAC1C,aAAeuP,EAAQ,2BACvBx/B,EAAO8vB,GAAgB,CAAC,CAAC,EAAG,IAAK0P,EAAO,aACxC1P,GAAgB,MAAM,CAAC,EAAE,IAAI,SAAUtxB,GAAG,CACxC,OAAOwB,EAAOxB,EAAC,CACjB,CAAC,EAAE,KAAK,GAAG,EAAG,MACd,SACAsxB,GAAgB,IAAI,SAAUnoB,GAAM9K,GAAG,CACrC,OACEmD,EAAO2H,EAAI,EAAI,IAAM63B,EAAQ,oBAAsB3iC,GACnD,IAAM2iC,EAAQ,aAAe3iC,GAAI,MAErC,CAAC,EAAE,KAAK,EAAE,EACV,UACA,MAAOs/B,EAAgB,IAAKqD,EAAO,aACnCmB,EAAQ,IAAKF,EAAc,iBAAkBjN,GAAmB,IAAKgM,EAAO,YAC5E,SACAmB,EAAQ,IAAKF,EAAc,cAAejB,EAAO,YACjD,IACAoB,GAAM,cAAepB,EAAO,IAC5B3F,EAAU,QAAS,IAAK2F,EAAO,UAAWmB,EAAQ,UAClD3gC,EAAO,WAAY,MAAOw/B,EAAO,cAAc,EACjD,SAASqB,EAAgBl5B,GAAM,CAC7BlF,EAAMzC,EAAO2H,EAAI,EAAG,IAAK63B,EAAO,IAAK73B,GAAM,KAAK,CAClD,CACA,OAAAk5B,EAAe,MAAM,EACrBA,EAAe,QAAQ,EACvBA,EAAe,QAAQ,EACvBA,EAAe,SAAS,EAExBp+B,EAAM,IAAI,EAEVA,EAAM,KACJ,MAAOzC,EAAO,SAAU,KACxBygC,EAAc,kBAAmBE,EAAQ,KACzC,GAAG,EAEE3gC,CACT,CAEAkgC,EAAcC,CAAS,EAAI5I,GAAkBC,EAAKgJ,CAAmB,CACvE,CAAC,EAEMN,CACT,CAEA,SAASY,GAAU1uB,EAASmnB,EAAK,CAC/B,IAAIU,EAAgB7nB,EAAQ,OACxB8nB,EAAiB9nB,EAAQ,QAC7B,GAAIygB,MAASoH,EAAe,CAC1B,IAAIpP,EAAMoP,EAAcpH,EAAK,EAC7B,OAAIhI,IAAQ,MAAQmN,EAAe,OAAOnN,CAAG,IAAM,OACjDA,EAAMmN,EAAe,UAAUnN,CAAG,GAE7ByM,GAAiB,SAAUiC,EAAK,CACrC,OAAOA,EAAI,KAAKvB,EAAe,OAAOnN,CAAG,CAAC,CAC5C,CAAC,CACH,SAAWgI,MAASqH,EAAgB,CAClC,IAAI1C,EAAM0C,EAAerH,EAAK,EAC9B,OAAO0E,GAAkBC,EAAK,SAAU+B,EAAK9K,EAAO,CAClD,IAAIsS,EAASxH,EAAI,OAAO9K,EAAO+I,CAAG,EAClC,OAAO/I,EAAM,IAAI8K,EAAI,OAAO,IAAM,WAAawH,EAAS,GAAG,CAC7D,CAAC,CACH,CACA,OAAO,IACT,CAEA,SAASC,GAAcvT,EAAS,CAC9B,IAAIwT,EAAgBxT,EAAQ,OACxByT,EAAiBzT,EAAQ,QACzBztB,EAAS,CAAC,EAEd,cAAO,KAAKihC,CAAa,EAAE,QAAQ,SAAUt5B,EAAM,CACjD,IAAI9J,EAAQojC,EAAct5B,CAAI,EAC9B3H,EAAO2H,CAAI,EAAI2vB,GAAiB,SAAUiC,EAAK9K,EAAO,CACpD,OAAI,OAAO5wB,GAAU,UAAY,OAAOA,GAAU,UACzC,GAAKA,EAEL07B,EAAI,KAAK17B,CAAK,CAEzB,CAAC,CACH,CAAC,EAED,OAAO,KAAKqjC,CAAc,EAAE,QAAQ,SAAUv5B,EAAM,CAClD,IAAI6vB,EAAM0J,EAAev5B,CAAI,EAC7B3H,EAAO2H,CAAI,EAAI4vB,GAAkBC,EAAK,SAAU+B,EAAK9K,EAAO,CAC1D,OAAO8K,EAAI,OAAO9K,EAAO+I,CAAG,CAC9B,CAAC,CACH,CAAC,EAEMx3B,CACT,CAEA,SAASmhC,GAAgB/uB,EAASzQ,EAAYD,EAAU+rB,EAAS8L,EAAK,CACpE,IAAIU,EAAgB7nB,EAAQ,OACxB8nB,EAAiB9nB,EAAQ,QAE7B9M,EAAQ,SAAS,UAAY,CAC3B,IAAI87B,EAAY,CACd/O,GACAC,GACAC,GACAC,GACAC,GACAE,GACAD,GACAE,GACAR,GACAS,EACF,EAAE,OAAO4F,CAAc,EAEvB,SAAS4I,GAAWt/B,GAAM,CACxB,OAAO,KAAKA,EAAI,EAAE,QAAQ,SAAUzD,GAAK,CACvCgH,EAAQ,QACN87B,EAAU,QAAQ9iC,EAAG,GAAK,EAC1B,sBAAwBA,GAAM,IAC9Bi7B,EAAI,UAAU,CAClB,CAAC,CACH,CAEA8H,GAAUpH,CAAa,EACvBoH,GAAUnH,CAAc,CAC1B,CAAC,EAED,IAAIjN,EAAkBoO,GAAqBjpB,EAASzQ,CAAU,EAE1DimB,EAAcwS,EAAiBhoB,EAASmnB,CAAG,EAC3C+H,EAAqB7G,GAAqBroB,EAASwV,EAAa2R,CAAG,EACnEgI,EAAOtF,GAAU7pB,EAASmnB,CAAG,EAC7BpP,EAAQ+S,GAAa9qB,EAASmnB,CAAG,EACjCj5B,EAASo7B,GAAatpB,EAASmnB,EAAKtM,CAAe,EAEvD,SAASuU,EAAS75B,EAAM,CACtB,IAAI85B,GAAOH,EAAmB35B,CAAI,EAC9B85B,KACFtX,EAAMxiB,CAAI,EAAI85B,GAElB,CACAD,EAAQrP,EAAU,EAClBqP,EAAQ5I,EAAS1G,EAAa,CAAC,EAE/B,IAAIwP,EAAQ,OAAO,KAAKvX,CAAK,EAAE,OAAS,EAEpCnqB,EAAS,CACX,YAAa4nB,EACb,KAAM2Z,EACN,OAAQjhC,EACR,MAAO6pB,EACP,MAAOuX,EACP,SAAU,KACV,QAAS,KACT,OAAQ,GACR,WAAY,CAAC,CACf,EAMA,GAJA1hC,EAAO,QAAUg6B,GAAa5nB,EAASmnB,CAAG,EAC1Cv5B,EAAO,SAAW0/B,GAAch+B,EAAU63B,CAAG,EAC7Cv5B,EAAO,QAAUA,EAAO,SAAW8gC,GAAS1uB,EAASmnB,CAAG,EAEpD,CAACv5B,EAAO,SAAWM,EAAO,SAAW,CAAC2sB,GAAmB1kB,EAAW,uBAAwB,CAC9F,IAAIo5B,EAAS,GACTC,EAAiBthC,EAAO,QAAQ,WAAW,IAAI,SAAU4qB,EAAM,CACjE,IAAIH,GAAUppB,EAAW,OAAOupB,CAAI,EACpC,OAAAyW,EAASA,GAAU,CAAC,CAAC5W,GACdA,EACT,CAAC,EACD,GAAI4W,GAAUC,EAAe,OAAS,EAAG,CACvC,IAAI/W,GAAMmN,EAAe,OAAOA,EAAe,UAAU4J,CAAc,CAAC,EACxE5hC,EAAO,QAAU,IAAI+2B,GAAY,KAAM,KAAM,KAAM,SAAUwC,EAAK9K,GAAO,CACvE,OAAO8K,EAAI,KAAK1O,EAAG,CACrB,CAAC,EACD7qB,EAAO,OAAS,EAClB,CACF,CACA,OAAIitB,EACFjtB,EAAO,OAAS,GAEhBA,EAAO,WAAaggC,GAAgBr+B,EAAY43B,CAAG,EAErDv5B,EAAO,QAAUghC,GAAavT,EAAS8L,CAAG,EACnCv5B,CACT,CAOA,SAAS6hC,GAAatI,EAAK9K,EAAOhB,EAAS,CACzC,IAAIgM,EAASF,EAAI,OACbe,EAAUb,EAAO,QAEjBqI,EAAevI,EAAI,MAAM,EAE7B,OAAO,KAAK9L,CAAO,EAAE,QAAQ,SAAU9lB,EAAM,CAC3C8mB,EAAM,KAAK6L,EAAS,IAAM3yB,CAAI,EAC9B,IAAI85B,EAAOhU,EAAQ9lB,CAAI,EACnB9J,EAAQ4jC,EAAK,OAAOlI,EAAK9K,CAAK,EAC9B,MAAM,QAAQ5wB,CAAK,EACrBikC,EAAaxH,EAAS,IAAK3yB,EAAM,KAAM9J,EAAM,KAAK,EAAG,IAAI,EAEzDikC,EAAaxH,EAAS,IAAK3yB,EAAM,IAAK9J,EAAO,GAAG,CAEpD,CAAC,EAED4wB,EAAMqT,CAAY,CACpB,CAOA,SAASC,GAAqBxI,EAAK9K,EAAO7G,EAAaoa,EAAW,CAChE,IAAIvI,EAASF,EAAI,OAEb0I,EAAKxI,EAAO,GACZe,EAAoBf,EAAO,YAC3ByI,EACA5J,IACF4J,EAAmBzT,EAAM,IAAIgL,EAAO,WAAY,qBAAqB,GAGvE,IAAII,EAAYN,EAAI,UAEhB4I,EAAetI,EAAU,WACzBuI,EAAcvI,EAAU,WAExBwI,EACAza,EACFya,EAAOza,EAAY,OAAO2R,EAAK9K,CAAK,EAEpC4T,EAAO5T,EAAM,IAAI+L,EAAmB,OAAO,EAGxCwH,GACHvT,EAAM,MAAO4T,EAAM,MAAO7H,EAAmB,QAAQ,EAEvD/L,EACE,MAAO4T,EAAM,KACbJ,EAAI,oBAAqB5L,GAAkB,IAAKgM,EAAM,gBAAgB,EACpE/J,GACF7J,EAAMyT,EAAkB,qBACtBC,EAAc,IAAKE,EAAM,6BAA6B,EAE1D5T,EAAM,SACJwT,EAAI,oBAAqB5L,GAAkB,SAAS,EAClDiC,GACF7J,EAAMyT,EAAkB,qBAAsBE,EAAa,IAAI,EAEjE3T,EACE,IACA+L,EAAmB,QAAS6H,EAAM,GAAG,EAClCL,GACHvT,EAAM,GAAG,CAEb,CAEA,SAAS6T,GAAe/I,EAAK9K,EAAOpmB,EAAM,CACxC,IAAIoxB,EAASF,EAAI,OAEb0I,EAAKxI,EAAO,GAEZ8I,EAAehJ,EAAI,QACnBiJ,EAAYjJ,EAAI,KAChBkJ,EAAgBhJ,EAAO,QACvBiJ,EAAajJ,EAAO,KAEpBh3B,EAAQ82B,EAAI,KAAKkJ,EAAe,QAAQ,EAE5ChK,EAAe,QAAQ,SAAU1J,EAAM,CACrC,IAAIvxB,EAAQo7B,EAAS7J,CAAI,EACzB,GAAI,EAAAvxB,KAAS6K,EAAK,OAIlB,KAAIg6B,EAAMM,EACV,GAAInlC,KAASglC,EAAW,CACtBH,EAAOG,EAAUhlC,CAAK,EACtBmlC,EAAUJ,EAAa/kC,CAAK,EAC5B,IAAIkC,EAAQsJ,GAAKuvB,EAAa/6B,CAAK,EAAE,OAAQ,SAAUX,EAAG,CACxD,OAAO4F,EAAM,IAAI4/B,EAAM,IAAKxlC,EAAG,GAAG,CACpC,CAAC,EACD4F,EAAM82B,EAAI,KAAK75B,EAAM,IAAI,SAAUic,EAAG9e,EAAG,CACvC,OAAO8e,EAAI,MAAQgnB,EAAU,IAAM9lC,EAAI,GACzC,CAAC,EAAE,KAAK,IAAI,CAAC,EACV,KACColC,EAAI,IAAKtJ,EAAan7B,CAAK,EAAG,IAAKkC,EAAO,KAC1CA,EAAM,IAAI,SAAUic,EAAG9e,EAAG,CACxB,OAAO8lC,EAAU,IAAM9lC,EAAI,KAAO8e,CACpC,CAAC,EAAE,KAAK,GAAG,EAAG,GAAG,CAAC,CACxB,KAAO,CACL0mB,EAAO5/B,EAAM,IAAIigC,EAAY,IAAKllC,CAAK,EACvC,IAAI++B,EAAOhD,EAAI,KAAK8I,EAAM,MAAOI,EAAe,IAAKjlC,CAAK,EAC1DiF,EAAM85B,CAAI,EACN/+B,KAASk7B,EACX6D,EACEhD,EAAI,KAAK8I,CAAI,EACV,KAAKJ,EAAI,WAAYvJ,EAASl7B,CAAK,EAAG,IAAI,EAC1C,KAAKykC,EAAI,YAAavJ,EAASl7B,CAAK,EAAG,IAAI,EAC9CilC,EAAe,IAAKjlC,EAAO,IAAK6kC,EAAM,GAAG,EAE3C9F,EACE0F,EAAI,IAAKtJ,EAAan7B,CAAK,EAAG,IAAK6kC,EAAM,KACzCI,EAAe,IAAKjlC,EAAO,IAAK6kC,EAAM,GAAG,CAE/C,EACF,CAAC,EACG,OAAO,KAAKh6B,EAAK,KAAK,EAAE,SAAW,GACrC5F,EAAMggC,EAAe,eAAe,EAEtChU,EAAMhsB,CAAK,CACb,CAEA,SAASmgC,GAAgBrJ,EAAK9K,EAAOrc,EAASywB,EAAQ,CACpD,IAAIpJ,EAASF,EAAI,OACbgJ,EAAehJ,EAAI,QACnBkJ,EAAgBhJ,EAAO,QACvBwI,EAAKxI,EAAO,GAChB3C,GAAU,OAAO,KAAK1kB,CAAO,CAAC,EAAE,QAAQ,SAAU5U,EAAO,CACvD,IAAIikC,EAAOrvB,EAAQ5U,CAAK,EACxB,GAAI,EAAAqlC,GAAU,CAACA,EAAOpB,CAAI,GAG1B,KAAI7H,EAAW6H,EAAK,OAAOlI,EAAK9K,CAAK,EACrC,GAAIiK,EAASl7B,CAAK,EAAG,CACnB,IAAIslC,EAAOpK,EAASl7B,CAAK,EACrB45B,GAASqK,CAAI,EACX7H,EACFnL,EAAMwT,EAAI,WAAYa,EAAM,IAAI,EAEhCrU,EAAMwT,EAAI,YAAaa,EAAM,IAAI,EAGnCrU,EAAM8K,EAAI,KAAKK,CAAQ,EACpB,KAAKqI,EAAI,WAAYa,EAAM,IAAI,EAC/B,KAAKb,EAAI,YAAaa,EAAM,IAAI,CAAC,EAEtCrU,EAAMgU,EAAe,IAAKjlC,EAAO,IAAKo8B,EAAU,GAAG,CACrD,SAAWhkB,GAAYgkB,CAAQ,EAAG,CAChC,IAAI+I,EAAUJ,EAAa/kC,CAAK,EAChCixB,EACEwT,EAAI,IAAKtJ,EAAan7B,CAAK,EAAG,IAAKo8B,EAAU,KAC7CA,EAAS,IAAI,SAAUx1B,EAAGvH,EAAG,CAC3B,OAAO8lC,EAAU,IAAM9lC,EAAI,KAAOuH,CACpC,CAAC,EAAE,KAAK,GAAG,EAAG,GAAG,CACrB,MACEqqB,EACEwT,EAAI,IAAKtJ,EAAan7B,CAAK,EAAG,IAAKo8B,EAAU,KAC7C6I,EAAe,IAAKjlC,EAAO,IAAKo8B,EAAU,GAAG,EAEnD,CAAC,CACH,CAEA,SAASmJ,GAAkBxJ,EAAK9K,EAAO,CACjC4J,IACFkB,EAAI,WAAa9K,EAAM,IACrB8K,EAAI,OAAO,WAAY,yBAAyB,EAEtD,CAEA,SAASyJ,GAAazJ,EAAK9K,EAAOpmB,EAAM46B,EAAUC,EAAkB,CAClE,IAAIzJ,EAASF,EAAI,OACb4J,EAAQ5J,EAAI,MACZkJ,EAAgBhJ,EAAO,QACvB2J,EAAQ3J,EAAO,MACf4J,EAAah7B,EAAK,QAEtB,SAASi7B,GAAe,CACtB,OAAI,OAAO,aAAgB,YAClB,aAEA,mBAEX,CAEA,IAAIC,EAAWC,EACf,SAASC,EAAkBhhC,EAAO,CAChC8gC,EAAY9U,EAAM,IAAI,EACtBhsB,EAAM8gC,EAAW,IAAKD,EAAY,EAAG,GAAG,EACpC,OAAOJ,GAAqB,SAC9BzgC,EAAM0gC,EAAO,WAAYD,EAAkB,GAAG,EAE9CzgC,EAAM0gC,EAAO,WAAW,EAEtBhL,IACE8K,GACFO,EAAgB/U,EAAM,IAAI,EAC1BhsB,EAAM+gC,EAAe,IAAKJ,EAAO,0BAA0B,GAE3D3gC,EAAM2gC,EAAO,eAAgBD,EAAO,IAAI,EAG9C,CAEA,SAASO,EAAgBjhC,EAAO,CAC9BA,EAAM0gC,EAAO,aAAcG,EAAY,EAAG,IAAKC,EAAW,GAAG,EACzDpL,IACE8K,EACFxgC,EAAM2gC,EAAO,mBACXI,EAAe,IACfJ,EAAO,2BACPD,EAAO,IAAI,EAEb1gC,EAAM2gC,EAAO,cAAc,EAGjC,CAEA,SAASO,EAAc9lC,EAAO,CAC5B,IAAI+iB,GAAO6N,EAAM,IAAIgU,EAAe,UAAU,EAC9ChU,EAAMgU,EAAe,YAAa5kC,EAAO,GAAG,EAC5C4wB,EAAM,KAAKgU,EAAe,YAAa7hB,GAAM,GAAG,CAClD,CAEA,IAAIgjB,EACJ,GAAIP,EAAY,CACd,GAAIjM,GAASiM,CAAU,EAAG,CACpBA,EAAW,QACbI,EAAiBhV,CAAK,EACtBiV,EAAejV,EAAM,IAAI,EACzBkV,EAAa,MAAM,GAEnBA,EAAa,OAAO,EAEtB,MACF,CACAC,EAAcP,EAAW,OAAO9J,EAAK9K,CAAK,EAC1CkV,EAAaC,CAAW,CAC1B,MACEA,EAAcnV,EAAM,IAAIgU,EAAe,UAAU,EAGnD,IAAIoB,EAAQtK,EAAI,MAAM,EACtBkK,EAAiBI,CAAK,EACtBpV,EAAM,MAAOmV,EAAa,KAAMC,EAAO,GAAG,EAC1C,IAAIC,GAAMvK,EAAI,MAAM,EACpBmK,EAAeI,EAAG,EAClBrV,EAAM,KAAK,MAAOmV,EAAa,KAAME,GAAK,GAAG,CAC/C,CAEA,SAASC,GAAgBxK,EAAK9K,EAAOpmB,EAAM1G,EAAYkhC,EAAQ,CAC7D,IAAIpJ,EAASF,EAAI,OAEjB,SAASyK,EAAYxnC,EAAG,CACtB,OAAQA,EAAG,CACT,KAAKg4B,GACL,KAAKI,GACL,KAAKI,GACH,MAAO,GACT,KAAKP,GACL,KAAKI,GACL,KAAKI,GACH,MAAO,GACT,KAAKP,GACL,KAAKI,GACL,KAAKI,GACH,MAAO,GACT,QACE,MAAO,EACX,CACF,CAEA,SAAS+O,EAAmBC,EAAWtW,EAAMjD,EAAQ,CACnD,IAAIsX,EAAKxI,EAAO,GAEZ0K,EAAW1V,EAAM,IAAIyV,EAAW,WAAW,EAC3CE,EAAU3V,EAAM,IAAIgL,EAAO,WAAY,IAAK0K,EAAU,GAAG,EAEzDhH,EAAQxS,EAAO,MACfgW,EAAShW,EAAO,OAChB0Z,EAAmB,CACrB1Z,EAAO,EACPA,EAAO,EACPA,EAAO,EACPA,EAAO,CACT,EAEI2Z,EAAc,CAChB,SACA,aACA,SACA,QACF,EAEA,SAASC,IAAc,CACrB9V,EACE,OAAQ2V,EAAS,YACjBnC,EAAI,4BAA6BkC,EAAU,KAAK,EAElD,IAAIvD,GAAOjW,EAAO,KACd6Z,GA8BJ,GA7BK7Z,EAAO,KAGV6Z,GAAO/V,EAAM,IAAI9D,EAAO,KAAM,KAAMiD,CAAI,EAFxC4W,GAAO5W,EAKTa,EAAM,MACJ2V,EAAS,WAAYxD,GAAM,KAC3BwD,EAAS,WAAYI,GAAM,KAC3BF,EAAY,IAAI,SAAUhmC,GAAK,CAC7B,OAAO8lC,EAAU,IAAM9lC,GAAM,MAAQqsB,EAAOrsB,EAAG,CACjD,CAAC,EAAE,KAAK,IAAI,EACZ,KACA2jC,EAAI,eAAgBzO,GAAmB,IAAKmN,EAAQ,YACpDsB,EAAI,wBAAyB,CAC3BkC,EACAK,GACA5D,GACAjW,EAAO,WACPA,EAAO,OACPA,EAAO,MACT,EAAG,KACHyZ,EAAS,SAAUxD,GAAM,IACzBwD,EAAS,SAAUI,GAAM,IACzBF,EAAY,IAAI,SAAUhmC,GAAK,CAC7B,OAAO8lC,EAAU,IAAM9lC,GAAM,IAAMqsB,EAAOrsB,EAAG,EAAI,GACnD,CAAC,EAAE,KAAK,EAAE,EACV,GAAG,EAED+5B,EAAe,CACjB,IAAIoM,GAAU9Z,EAAO,QACrB8D,EACE,MAAO2V,EAAS,cAAeK,GAAS,KACxClL,EAAI,WAAY,6BAA8B,CAAC4K,EAAUM,EAAO,EAAG,KACnEL,EAAS,YAAaK,GAAS,IAAI,CACvC,CACF,CAEA,SAASC,GAAgB,CACvBjW,EACE,MAAO2V,EAAS,YAChBnC,EAAI,6BAA8BkC,EAAU,KAC5CC,EAAS,gBACT,OAAQtU,GAAgB,IAAI,SAAUprB,GAAG7H,GAAG,CAC1C,OAAOunC,EAAU,IAAM1/B,GAAI,MAAQ2/B,EAAiBxnC,EAAC,CACvD,CAAC,EAAE,KAAK,IAAI,EAAG,KACfolC,EAAI,mBAAoBkC,EAAU,IAAKE,EAAkB,KACzDvU,GAAgB,IAAI,SAAUprB,GAAG7H,GAAG,CAClC,OAAOunC,EAAU,IAAM1/B,GAAI,IAAM2/B,EAAiBxnC,EAAC,EAAI,GACzD,CAAC,EAAE,KAAK,EAAE,EACV,GAAG,CACP,CAEIsgC,IAAUnN,GACZuU,GAAW,EACFpH,IAAUlN,GACnByU,EAAa,GAEbjW,EAAM,MAAO0O,EAAO,MAAOnN,GAAsB,IAAI,EACrDuU,GAAW,EACX9V,EAAM,QAAQ,EACdiW,EAAa,EACbjW,EAAM,GAAG,EAEb,CAEA9sB,EAAW,QAAQ,SAAUw+B,EAAW,CACtC,IAAIx4B,EAAOw4B,EAAU,KACjB1Q,EAAMpnB,EAAK,WAAWV,CAAI,EAC1BgjB,EACJ,GAAI8E,EAAK,CACP,GAAI,CAACoT,EAAOpT,CAAG,EACb,OAEF9E,EAAS8E,EAAI,OAAO8J,EAAK9K,CAAK,CAChC,KAAO,CACL,GAAI,CAACoU,EAAOjL,EAAU,EACpB,OAEF,IAAI+M,EAAcpL,EAAI,YAAY5xB,CAAI,EACtCrC,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,EACTkW,EAAc,SACd,qBAAuBh9B,CAAI,CAC/B,CAAC,EACDgjB,EAAS,CAAC,EACV,OAAO,KAAK,IAAId,CAAiB,EAAE,QAAQ,SAAUvrB,EAAK,CACxDqsB,EAAOrsB,CAAG,EAAImwB,EAAM,IAAIkW,EAAa,IAAKrmC,CAAG,CAC/C,CAAC,CACH,CACA2lC,EACE1K,EAAI,KAAK4G,CAAS,EAAG6D,EAAW7D,EAAU,KAAK,IAAI,EAAGxV,CAAM,CAChE,CAAC,CACH,CAEA,SAASia,GAAcrL,EAAK9K,EAAOpmB,EAAM3G,EAAUmhC,EAAQ,CAKzD,QAJIpJ,EAASF,EAAI,OACb0I,EAAKxI,EAAO,GAEZoL,EACK,EAAI,EAAG,EAAInjC,EAAS,OAAQ,EAAE,EAAG,CACxC,IAAIojC,EAAUpjC,EAAS,CAAC,EACpBiG,EAAOm9B,EAAQ,KACfhnC,EAAOgnC,EAAQ,KAAK,KACpBrV,EAAMpnB,EAAK,SAASV,CAAI,EACxBo9B,EAAUxL,EAAI,KAAKuL,CAAO,EAC1BX,EAAWY,EAAU,YAErBvF,EACJ,GAAI/P,EAAK,CACP,GAAI,CAACoT,EAAOpT,CAAG,EACb,SAEF,GAAI2H,GAAS3H,CAAG,EAAG,CACjB,IAAI5xB,EAAQ4xB,EAAI,MAIhB,GAHAnqB,EAAQ,QACNzH,IAAU,MAAQ,OAAOA,GAAU,YACnC,oBAAsB8J,EAAO,IAAK4xB,EAAI,UAAU,EAC9Cz7B,IAASw3B,IAAiBx3B,IAASy3B,GAAiB,CACtDjwB,EAAQ,QACN,OAAOzH,GAAU,aACfC,IAASw3B,KACRz3B,EAAM,YAAc,aACrBA,EAAM,YAAc,gBACrBC,IAASy3B,KACP13B,EAAM,YAAc,eACrBA,EAAM,YAAc,oBACtB,+BAAiC8J,EAAM4xB,EAAI,UAAU,EACvD,IAAIyL,EAAYzL,EAAI,KAAK17B,EAAM,UAAYA,EAAM,MAAM,CAAC,EAAE,QAAQ,EAClE4wB,EAAMwT,EAAI,cAAekC,EAAU,IAAKa,EAAY,WAAW,EAC/DvW,EAAM,KAAKuW,EAAW,YAAY,CACpC,SACElnC,IAASq3B,IACTr3B,IAASs3B,IACTt3B,IAASu3B,GAAe,CACxB/vB,EAAQ,SAAS,UAAY,CAC3BA,EAAQ,QAAQsQ,GAAY/X,CAAK,EAC/B,8BAAgC8J,EAAM4xB,EAAI,UAAU,EACtDj0B,EAAQ,QACLxH,IAASq3B,IAAiBt3B,EAAM,SAAW,GAC3CC,IAASs3B,IAAiBv3B,EAAM,SAAW,GAC3CC,IAASu3B,IAAiBx3B,EAAM,SAAW,GAC5C,qCAAuC8J,EAAM4xB,EAAI,UAAU,CAC/D,CAAC,EACD,IAAI0L,GAAY1L,EAAI,OAAO,IAAI,qBAC7B,MAAM,UAAU,MAAM,KAAK17B,CAAK,EAAI,IAAI,EACtCkU,EAAM,EACNjU,IAASs3B,GACXrjB,EAAM,EACGjU,IAASu3B,KAClBtjB,EAAM,GAER0c,EACEwT,EAAI,iBAAkBlwB,EAAK,MAC3BoyB,EAAU,UAAWc,GAAW,IAAI,CACxC,KAAO,CACL,OAAQnnC,EAAM,CACZ,KAAKy2B,GACHjvB,EAAQ,YAAYzH,EAAO,SAAU,WAAa8J,EAAM4xB,EAAI,UAAU,EACtEsL,EAAQ,KACR,MACF,KAAKrQ,GACHlvB,EAAQ,QACNsQ,GAAY/X,CAAK,GAAKA,EAAM,SAAW,EACvC,WAAa8J,EAAM4xB,EAAI,UAAU,EACnCsL,EAAQ,KACR,MACF,KAAKpQ,GACHnvB,EAAQ,QACNsQ,GAAY/X,CAAK,GAAKA,EAAM,SAAW,EACvC,WAAa8J,EAAM4xB,EAAI,UAAU,EACnCsL,EAAQ,KACR,MACF,KAAKnQ,GACHpvB,EAAQ,QACNsQ,GAAY/X,CAAK,GAAKA,EAAM,SAAW,EACvC,WAAa8J,EAAM4xB,EAAI,UAAU,EACnCsL,EAAQ,KACR,MACF,KAAK9P,GACHzvB,EAAQ,YAAYzH,EAAO,UAAW,WAAa8J,EAAM4xB,EAAI,UAAU,EACvEsL,EAAQ,KACR,MACF,KAAKlQ,GACHrvB,EAAQ,YAAYzH,EAAO,SAAU,WAAa8J,EAAM4xB,EAAI,UAAU,EACtEsL,EAAQ,KACR,MACF,KAAK7P,GACH1vB,EAAQ,QACNsQ,GAAY/X,CAAK,GAAKA,EAAM,SAAW,EACvC,WAAa8J,EAAM4xB,EAAI,UAAU,EACnCsL,EAAQ,KACR,MACF,KAAKjQ,GACHtvB,EAAQ,QACNsQ,GAAY/X,CAAK,GAAKA,EAAM,SAAW,EACvC,WAAa8J,EAAM4xB,EAAI,UAAU,EACnCsL,EAAQ,KACR,MACF,KAAK5P,GACH3vB,EAAQ,QACNsQ,GAAY/X,CAAK,GAAKA,EAAM,SAAW,EACvC,WAAa8J,EAAM4xB,EAAI,UAAU,EACnCsL,EAAQ,KACR,MACF,KAAKhQ,GACHvvB,EAAQ,QACNsQ,GAAY/X,CAAK,GAAKA,EAAM,SAAW,EACvC,WAAa8J,EAAM4xB,EAAI,UAAU,EACnCsL,EAAQ,KACR,MACF,KAAK3P,GACH5vB,EAAQ,QACNsQ,GAAY/X,CAAK,GAAKA,EAAM,SAAW,EACvC,WAAa8J,EAAM4xB,EAAI,UAAU,EACnCsL,EAAQ,KACR,MACF,KAAK/P,GACHxvB,EAAQ,QACNsQ,GAAY/X,CAAK,GAAKA,EAAM,SAAW,EACvC,WAAa8J,EAAM4xB,EAAI,UAAU,EACnCsL,EAAQ,KACR,KACJ,CACApW,EAAMwT,EAAI,WAAY4C,EAAO,IAAKV,EAAU,IAC1CvuB,GAAY/X,CAAK,EAAI,MAAM,UAAU,MAAM,KAAKA,CAAK,EAAIA,EACzD,IAAI,CACR,CACA,QACF,MACE2hC,EAAQ/P,EAAI,OAAO8J,EAAK9K,CAAK,CAEjC,KAAO,CACL,GAAI,CAACoU,EAAOjL,EAAU,EACpB,SAEF4H,EAAQ/Q,EAAM,IAAIgL,EAAO,SAAU,IAAK73B,EAAY,GAAG+F,CAAI,EAAG,GAAG,CACnE,CAEI7J,IAASw3B,IACXhwB,EAAQ,CAAC,MAAM,QAAQk6B,CAAK,EAAG,yCAAyC,EACxE/Q,EACE,MAAO+Q,EAAO,KAAMA,EAAO,+BAC3BA,EAAO,IAAKA,EAAO,aACnB,GAAG,GACI1hC,IAASy3B,KAClBjwB,EAAQ,CAAC,MAAM,QAAQk6B,CAAK,EAAG,0CAA0C,EACzE/Q,EACE,MAAO+Q,EAAO,KAAMA,EAAO,mCAC3BA,EAAO,IAAKA,EAAO,aACnB,GAAG,GAIPl6B,EAAQ,SAAS,UAAY,CAC3B,SAAS4/B,GAAW7nC,GAAMH,GAAS,CACjCq8B,EAAI,OAAO9K,EAAOpxB,GAChB,oCAAsCsK,EAAO,OAASzK,EAAO,CACjE,CAEA,SAASioC,GAAWrnC,GAAM,CACxBwH,EAAQ,CAAC,MAAM,QAAQk6B,CAAK,EAAG,4CAA4C,EAC3E0F,GACE,UAAY1F,EAAQ,OAAS1hC,GAAO,IACpC,0BAA4BA,EAAI,CACpC,CAEA,SAASsnC,GAAa5mC,GAAGV,GAAM,CACzB,MAAM,QAAQ0hC,CAAK,EACrBl6B,EAAQk6B,EAAM,SAAWhhC,GAAG,oBAAsBA,EAAC,EAEnD0mC,GACEzL,EAAO,YAAc,IAAM+F,EAAQ,MAAQA,EAAQ,aAAehhC,GAClE,sCAAwCA,GAAG+6B,EAAI,UAAU,CAE/D,CAEA,SAAS8L,GAAc5mB,GAAQ,CAC7BnZ,EAAQ,CAAC,MAAM,QAAQk6B,CAAK,EAAG,+BAA+B,EAC9D0F,GACE,UAAY1F,EAAQ,kBACpBA,EAAQ,yBACP/gB,KAAWmV,GAAkB,KAAO,QAAU,IAC/C,uBAAwB2F,EAAI,UAAU,CAC1C,CAEA,OAAQz7B,EAAM,CACZ,KAAK62B,GACHwQ,GAAU,QAAQ,EAClB,MACF,KAAKvQ,GACHwQ,GAAY,EAAG,QAAQ,EACvB,MACF,KAAKvQ,GACHuQ,GAAY,EAAG,QAAQ,EACvB,MACF,KAAKtQ,GACHsQ,GAAY,EAAG,QAAQ,EACvB,MACF,KAAK7Q,GACH4Q,GAAU,QAAQ,EAClB,MACF,KAAK3Q,GACH4Q,GAAY,EAAG,QAAQ,EACvB,MACF,KAAK3Q,GACH2Q,GAAY,EAAG,QAAQ,EACvB,MACF,KAAK1Q,GACH0Q,GAAY,EAAG,QAAQ,EACvB,MACF,KAAKrQ,GACHoQ,GAAU,SAAS,EACnB,MACF,KAAKnQ,GACHoQ,GAAY,EAAG,SAAS,EACxB,MACF,KAAKnQ,GACHmQ,GAAY,EAAG,SAAS,EACxB,MACF,KAAKlQ,GACHkQ,GAAY,EAAG,SAAS,EACxB,MACF,KAAKjQ,GACHiQ,GAAY,EAAG,QAAQ,EACvB,MACF,KAAKhQ,GACHgQ,GAAY,EAAG,QAAQ,EACvB,MACF,KAAK/P,GACH+P,GAAY,GAAI,QAAQ,EACxB,MACF,KAAK9P,GACH+P,GAAazR,EAAe,EAC5B,MACF,KAAK2B,GACH8P,GAAaxR,EAAqB,EAClC,KACJ,CACF,CAAC,EAED,IAAIyR,GAAS,EACb,OAAQxnC,EAAM,CACZ,KAAKw3B,GACL,KAAKC,GACH,IAAIgQ,GAAM9W,EAAM,IAAI+Q,EAAO,WAAW,EACtC/Q,EAAMwT,EAAI,cAAekC,EAAU,IAAKoB,GAAK,WAAW,EACxD9W,EAAM,KAAK8W,GAAK,YAAY,EAC5B,SAEF,KAAK5Q,GACL,KAAKI,GACH8P,EAAQ,KACR,MAEF,KAAKjQ,GACL,KAAKI,GACH6P,EAAQ,KACRS,GAAS,EACT,MAEF,KAAKzQ,GACL,KAAKI,GACH4P,EAAQ,KACRS,GAAS,EACT,MAEF,KAAKxQ,GACL,KAAKI,GACH2P,EAAQ,KACRS,GAAS,EACT,MAEF,KAAK/Q,GACHsQ,EAAQ,KACR,MAEF,KAAKrQ,GACHqQ,EAAQ,KACRS,GAAS,EACT,MAEF,KAAK7Q,GACHoQ,EAAQ,KACRS,GAAS,EACT,MAEF,KAAK5Q,GACHmQ,EAAQ,KACRS,GAAS,EACT,MAEF,KAAKnQ,GACH0P,EAAQ,YACR,MAEF,KAAKzP,GACHyP,EAAQ,YACR,MAEF,KAAKxP,GACHwP,EAAQ,YACR,KACJ,CAGA,GADApW,EAAMwT,EAAI,WAAY4C,EAAO,IAAKV,EAAU,GAAG,EAC3CU,EAAM,OAAO,CAAC,IAAM,IAAK,CAC3B,IAAIW,GAAU,KAAK,IAAI1nC,EAAOq3B,GAAgB,EAAG,CAAC,EAC9CsQ,GAAUlM,EAAI,OAAO,IAAI,oBAAqBiM,GAAS,GAAG,EAC1D,MAAM,QAAQhG,CAAK,EACrB/Q,EACE,UACAzlB,GAAKw8B,GAAS,SAAU3oC,GAAG,CACzB,OAAO4oC,GAAU,IAAM5oC,GAAI,KAAO2iC,EAAM3iC,EAAC,CAC3C,CAAC,EAAG,IAAK4oC,GAAS,GAAG,EAEvBhX,EACE,wBAAyB+Q,EAAO,MAAOA,EAAO,6BAA8BA,EAAO,KACnFx2B,GAAKw8B,GAAS,SAAU3oC,GAAG,CACzB,OAAO4oC,GAAU,IAAM5oC,GAAI,KAAO2iC,EAAQ,IAAM3iC,GAAI,GACtD,CAAC,EAAG,IAAK4oC,GAAS,GAAG,CAE3B,MAAWH,GAAS,EAClB7W,EAAMzlB,GAAKs8B,GAAQ,SAAUzoC,GAAG,CAC9B,OAAO,MAAM,QAAQ2iC,CAAK,EAAIA,EAAM3iC,EAAC,EAAI2iC,EAAQ,IAAM3iC,GAAI,GAC7D,CAAC,CAAC,GAEFyI,EAAQ,CAAC,MAAM,QAAQk6B,CAAK,EAAG,oCAAoC,EACnE/Q,EAAM+Q,CAAK,GAEb/Q,EAAM,IAAI,CACZ,CACF,CAEA,SAASiX,GAAUnM,EAAKoM,EAAOC,EAAOv9B,EAAM,CAC1C,IAAIoxB,EAASF,EAAI,OACb0I,EAAKxI,EAAO,GACZoM,EAAapM,EAAO,KAEpBqM,EAAcz9B,EAAK,KAEvB,SAAS09B,GAAgB,CACvB,IAAItE,GAAOqE,EAAY,SACnBE,GACAvX,GAAQkX,EACZ,OAAIlE,KACGA,GAAK,YAAcp5B,EAAK,gBAAmBo5B,GAAK,WACnDhT,GAAQmX,GAEVI,GAAWvE,GAAK,OAAOlI,EAAK9K,EAAK,GAEjCuX,GAAWvX,GAAM,IAAIoX,EAAY,IAAKrT,EAAU,EAE9CwT,IACFvX,GACE,MAAQuX,GAAW,IACnB/D,EAAK,eAAiBxO,GAA4B,IAAMuS,GAAW,kBAAkB,EAElFA,EACT,CAEA,SAASC,GAAa,CACpB,IAAIxE,GAAOqE,EAAY,MACnBI,GACAzX,GAAQkX,EACZ,OAAIlE,KACGA,GAAK,YAAcp5B,EAAK,gBAAmBo5B,GAAK,WACnDhT,GAAQmX,GAEVM,GAAQzE,GAAK,OAAOlI,EAAK9K,EAAK,EAC9BnpB,EAAQ,SAAS,UAAY,CACvBm8B,GAAK,SACPlI,EAAI,OAAOoM,EAAO,QAAS,sBAAsB,EAE/ClE,GAAK,SACPlI,EAAI,OAAO9K,GAAOyX,GAAQ,MAAO,sBAAsB,CAE3D,CAAC,IAEDA,GAAQzX,GAAM,IAAIoX,EAAY,IAAKnT,EAAO,EAC1CptB,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,OAAO9K,GAAOyX,GAAQ,MAAO,sBAAsB,CACzD,CAAC,GAEIA,EACT,CAEA,IAAIF,EAAWD,EAAa,EAC5B,SAASI,EAAWx+B,GAAM,CACxB,IAAI85B,GAAOqE,EAAYn+B,EAAI,EAC3B,OAAI85B,GACGA,GAAK,YAAcp5B,EAAK,gBAAmBo5B,GAAK,QAC5CA,GAAK,OAAOlI,EAAKqM,CAAK,EAEtBnE,GAAK,OAAOlI,EAAKoM,CAAK,EAGxBA,EAAM,IAAIE,EAAY,IAAKl+B,EAAI,CAE1C,CAEA,IAAIy+B,EAAYD,EAAU1T,EAAW,EACjCsK,EAASoJ,EAAUxT,EAAQ,EAE3BuT,EAAQD,EAAU,EACtB,GAAI,OAAOC,GAAU,UACnB,GAAIA,IAAU,EACZ,YAGFN,EAAM,MAAOM,EAAO,IAAI,EACxBN,EAAM,KAAK,GAAG,EAGhB,IAAIS,EAAWC,EACXjO,IACFgO,EAAYF,EAAUvT,EAAW,EACjC0T,EAAiB/M,EAAI,YAGvB,IAAIgN,EAAeP,EAAW,QAE1BQ,GAAiBV,EAAY,UAAY1O,GAAS0O,EAAY,QAAQ,EAE1E,SAASW,GAAkB,CACzB,SAASC,IAAgB,CACvBd,EAAMU,EAAgB,+BAAgC,CACpDF,EACAF,EACAK,EACAxJ,EAAS,OAASwJ,EAAe,IAAMxW,GAAqB,QAC5DsW,CACF,EAAG,IAAI,CACT,CAEA,SAASM,IAAc,CACrBf,EAAMU,EAAgB,6BACpB,CAACF,EAAWrJ,EAAQmJ,EAAOG,CAAS,EAAG,IAAI,CAC/C,CAEIL,EACGQ,GAOHE,GAAa,GANbd,EAAM,MAAOI,EAAU,IAAI,EAC3BU,GAAa,EACbd,EAAM,QAAQ,EACde,GAAW,EACXf,EAAM,GAAG,GAKXe,GAAW,CAEf,CAEA,SAASC,IAAe,CACtB,SAASF,IAAgB,CACvBd,EAAM3D,EAAK,iBAAmB,CAC5BmE,EACAF,EACAK,EACAxJ,EAAS,OAASwJ,EAAe,IAAMxW,GAAqB,OAC9D,EAAI,IAAI,CACV,CAEA,SAAS4W,IAAc,CACrBf,EAAM3D,EAAK,eAAiB,CAACmE,EAAWrJ,EAAQmJ,CAAK,EAAI,IAAI,CAC/D,CAEIF,EACGQ,GAOHE,GAAa,GANbd,EAAM,MAAOI,EAAU,IAAI,EAC3BU,GAAa,EACbd,EAAM,QAAQ,EACde,GAAW,EACXf,EAAM,GAAG,GAKXe,GAAW,CAEf,CAEItO,IAAkB,OAAOgO,GAAc,UAAYA,GAAa,GAC9D,OAAOA,GAAc,UACvBT,EAAM,MAAOS,EAAW,MAAM,EAC9BI,EAAe,EACfb,EAAM,YAAaS,EAAW,MAAM,EACpCO,GAAY,EACZhB,EAAM,GAAG,GAETa,EAAe,EAGjBG,GAAY,CAEhB,CAEA,SAASC,GAAYC,EAAUC,EAAW1+B,EAAMpH,EAAS2T,EAAO,CAC9D,IAAI2kB,EAAMD,GAAsB,EAC5B7K,EAAQ8K,EAAI,KAAK,OAAQ3kB,CAAK,EAClC,OAAAtP,EAAQ,SAAS,UAAY,CAC3Bi0B,EAAI,WAAawN,EAAU,WAC3BxN,EAAI,QAAUA,EAAI,KAAKwN,EAAU,UAAU,CAC7C,CAAC,EACG1O,IACFkB,EAAI,WAAa9K,EAAM,IACrB8K,EAAI,OAAO,WAAY,yBAAyB,GAEpDuN,EAASvN,EAAK9K,EAAOpmB,EAAMpH,CAAO,EAC3Bs4B,EAAI,QAAQ,EAAE,IACvB,CAOA,SAASyN,GAAczN,EAAKgI,EAAMl5B,EAAMpH,EAAS,CAC/C8hC,GAAiBxJ,EAAKgI,CAAI,EACtBl5B,EAAK,OACHA,EAAK,QACPk5B,EAAKhI,EAAI,OAAO,IAAK,WAAYlxB,EAAK,QAAQ,OAAOkxB,EAAKgI,CAAI,EAAG,IAAI,EAErEA,EAAKhI,EAAI,OAAO,IAAK,WAAYA,EAAI,OAAO,IAAK,cAAc,GAGjEgI,EAAKhI,EAAI,OAAO,IAAK,gBAAgB,EACrCwK,GAAexK,EAAKgI,EAAMl5B,EAAMpH,EAAQ,WAAY,UAAY,CAC9D,MAAO,EACT,CAAC,GAEH2jC,GAAarL,EAAKgI,EAAMl5B,EAAMpH,EAAQ,SAAU,UAAY,CAC1D,MAAO,EACT,CAAC,EACDykC,GAASnM,EAAKgI,EAAMA,EAAMl5B,CAAI,CAChC,CAEA,SAAS4+B,GAAc1N,EAAKlxB,EAAM,CAChC,IAAIk5B,EAAOhI,EAAI,KAAK,OAAQ,CAAC,EAE7BwJ,GAAiBxJ,EAAKgI,CAAI,EAE1BM,GAAYtI,EAAKgI,EAAMl5B,EAAK,OAAO,EACnC05B,GAAoBxI,EAAKgI,EAAMl5B,EAAK,WAAW,EAE/Ci6B,GAAc/I,EAAKgI,EAAMl5B,CAAI,EAC7Bu6B,GAAerJ,EAAKgI,EAAMl5B,EAAK,KAAK,EAEpC26B,GAAYzJ,EAAKgI,EAAMl5B,EAAM,GAAO,EAAI,EAExC,IAAIpH,EAAUoH,EAAK,OAAO,QAAQ,OAAOkxB,EAAKgI,CAAI,EAGlD,GAFAA,EAAKhI,EAAI,OAAO,GAAI,eAAgBt4B,EAAS,YAAY,EAErDoH,EAAK,OAAO,QACd2+B,GAAazN,EAAKgI,EAAMl5B,EAAMA,EAAK,OAAO,OAAO,MAC5C,CACLk5B,EAAKhI,EAAI,OAAO,IAAK,gBAAgB,EACrC,IAAI2N,EAAY3N,EAAI,OAAO,IAAI,IAAI,EAC/B4N,EAAU5F,EAAK,IAAItgC,EAAS,KAAK,EACjCmmC,EAAc7F,EAAK,IAAI2F,EAAW,IAAKC,EAAS,GAAG,EACvD5F,EACEhI,EAAI,KAAK6N,CAAW,EACjB,KAAKA,EAAa,iBAAiB,EACnC,KACCA,EAAa,IAAKF,EAAW,IAAKC,EAAS,KAC3C5N,EAAI,KAAK,SAAUt4B,EAAS,CAC1B,OAAO4lC,GAAWG,GAAczN,EAAKlxB,EAAMpH,EAAS,CAAC,CACvD,CAAC,EAAG,IAAKA,EAAS,KAClBmmC,EAAa,iBAAiB,CAAC,CACvC,CAEI,OAAO,KAAK/+B,EAAK,KAAK,EAAE,OAAS,GACnCk5B,EAAKhI,EAAI,OAAO,QAAS,cAAc,CAE3C,CAQA,SAAS8N,GAA4B9N,EAAK9K,EAAOpmB,EAAMpH,EAAS,CAC9Ds4B,EAAI,QAAU,KAEdwJ,GAAiBxJ,EAAK9K,CAAK,EAE3B,SAAS6Y,GAAO,CACd,MAAO,EACT,CAEAvD,GAAexK,EAAK9K,EAAOpmB,EAAMpH,EAAQ,WAAYqmC,CAAG,EACxD1C,GAAarL,EAAK9K,EAAOpmB,EAAMpH,EAAQ,SAAUqmC,CAAG,EACpD5B,GAASnM,EAAK9K,EAAOA,EAAOpmB,CAAI,CAClC,CAEA,SAASk/B,GAAehO,EAAK9K,EAAOpmB,EAAMpH,EAAS,CACjD8hC,GAAiBxJ,EAAK9K,CAAK,EAE3B,IAAI+Y,EAAiBn/B,EAAK,WAEtBo/B,EAAWhZ,EAAM,IAAI,EACrBiZ,EAAY,KACZC,EAAY,KACZC,EAAQnZ,EAAM,IAAI,EACtB8K,EAAI,OAAO,MAAQqO,EACnBrO,EAAI,QAAUkO,EAEd,IAAI9B,EAAQpM,EAAI,MAAM,EAClBqM,EAAQrM,EAAI,MAAM,EAEtB9K,EACEkX,EAAM,MACN,OAAQ8B,EAAU,MAAOA,EAAU,IAAKE,EAAW,MAAOF,EAAU,KACpEG,EAAO,IAAKF,EAAW,IAAKD,EAAU,KACtC7B,EACA,IACAD,EAAM,IAAI,EAEZ,SAASkC,EAAapG,EAAM,CAC1B,OAASA,EAAK,YAAc+F,GAAmB/F,EAAK,OACtD,CAEA,SAASqG,EAAarG,EAAM,CAC1B,MAAO,CAACoG,EAAYpG,CAAI,CAC1B,CAcA,GAZIp5B,EAAK,cACPw5B,GAAYtI,EAAKqM,EAAOv9B,EAAK,OAAO,EAElCA,EAAK,kBACP05B,GAAoBxI,EAAKqM,EAAOv9B,EAAK,WAAW,EAElDu6B,GAAerJ,EAAKqM,EAAOv9B,EAAK,MAAOw/B,CAAW,EAE9Cx/B,EAAK,SAAWw/B,EAAYx/B,EAAK,OAAO,GAC1C26B,GAAYzJ,EAAKqM,EAAOv9B,EAAM,GAAO,EAAI,EAGtCpH,EAeCoH,EAAK,OACHA,EAAK,QACHw/B,EAAYx/B,EAAK,OAAO,EAE1Bu9B,EAAMrM,EAAI,OAAO,IAAK,WAAYlxB,EAAK,QAAQ,OAAOkxB,EAAKqM,CAAK,EAAG,IAAI,EAGvED,EAAMpM,EAAI,OAAO,IAAK,WAAYlxB,EAAK,QAAQ,OAAOkxB,EAAKoM,CAAK,EAAG,IAAI,EAIzEA,EAAMpM,EAAI,OAAO,IAAK,WAAYA,EAAI,OAAO,IAAK,cAAc,GAGlEoM,EAAMpM,EAAI,OAAO,IAAK,gBAAgB,EACtCwK,GAAexK,EAAKoM,EAAOt9B,EAAMpH,EAAQ,WAAY6mC,CAAW,EAChE/D,GAAexK,EAAKqM,EAAOv9B,EAAMpH,EAAQ,WAAY4mC,CAAW,GAElEjD,GAAarL,EAAKoM,EAAOt9B,EAAMpH,EAAQ,SAAU6mC,CAAW,EAC5DlD,GAAarL,EAAKqM,EAAOv9B,EAAMpH,EAAQ,SAAU4mC,CAAW,EAC5DnC,GAASnM,EAAKoM,EAAOC,EAAOv9B,CAAI,MAnCpB,CACZ,IAAI0/B,EAAYxO,EAAI,OAAO,IAAI,IAAI,EAC/ByO,EAAU3/B,EAAK,OAAO,QAAQ,OAAOkxB,EAAKqM,CAAK,EAC/CuB,EAAUvB,EAAM,IAAIoC,EAAS,KAAK,EAClCZ,EAAcxB,EAAM,IAAImC,EAAW,IAAKZ,EAAS,GAAG,EACxDvB,EACErM,EAAI,OAAO,GAAI,eAAgByO,EAAS,aACxC,OAAQZ,EAAa,KACrBA,EAAa,IAAKW,EAAW,IAAKZ,EAAS,KAC3C5N,EAAI,KAAK,SAAUt4B,EAAS,CAC1B,OAAO4lC,GACLQ,GAA4B9N,EAAKlxB,EAAMpH,EAAS,CAAC,CACrD,CAAC,EAAG,IAAK+mC,EAAS,MAClBZ,EAAa,iBAAkBK,EAAU,KAAMA,EAAU,IAAI,CACjE,CAuBF,CAEA,SAASQ,EAAe1O,EAAKlxB,EAAM,CACjC,IAAI6/B,EAAQ3O,EAAI,KAAK,QAAS,CAAC,EAC/BA,EAAI,QAAU,IAEdwJ,GAAiBxJ,EAAK2O,CAAK,EAG3B,IAAIV,EAAiB,GACjBW,EAAe,GACnB,OAAO,KAAK9/B,EAAK,OAAO,EAAE,QAAQ,SAAUV,EAAM,CAChD6/B,EAAiBA,GAAkBn/B,EAAK,QAAQV,CAAI,EAAE,OACxD,CAAC,EACI6/B,IACH3F,GAAYtI,EAAK2O,EAAO7/B,EAAK,OAAO,EACpC8/B,EAAe,IAIjB,IAAIvgB,EAAcvf,EAAK,YACnB+/B,EAAmB,GACnBxgB,GACEA,EAAY,QACd4f,EAAiBY,EAAmB,GAC3BxgB,EAAY,YAAc4f,IACnCY,EAAmB,IAEhBA,GACHrG,GAAoBxI,EAAK2O,EAAOtgB,CAAW,GAG7Cma,GAAoBxI,EAAK2O,EAAO,IAAI,EAIlC7/B,EAAK,MAAM,UAAYA,EAAK,MAAM,SAAS,UAC7Cm/B,EAAiB,IAGnB,SAASK,EAAapG,EAAM,CAC1B,OAAQA,EAAK,YAAc+F,GAAmB/F,EAAK,OACrD,CAGAa,GAAc/I,EAAK2O,EAAO7/B,CAAI,EAC9Bu6B,GAAerJ,EAAK2O,EAAO7/B,EAAK,MAAO,SAAUo5B,EAAM,CACrD,MAAO,CAACoG,EAAYpG,CAAI,CAC1B,CAAC,GAEG,CAACp5B,EAAK,SAAW,CAACw/B,EAAYx/B,EAAK,OAAO,IAC5C26B,GAAYzJ,EAAK2O,EAAO7/B,EAAM,GAAO,IAAI,EAI3CA,EAAK,WAAam/B,EAClBn/B,EAAK,aAAe8/B,EACpB9/B,EAAK,iBAAmB+/B,EAGxB,IAAIC,EAAWhgC,EAAK,OAAO,QAC3B,GAAKggC,EAAS,YAAcb,GAAmBa,EAAS,QACtDd,GACEhO,EACA2O,EACA7/B,EACA,IAAI,MACD,CACL,IAAI2/B,EAAUK,EAAS,OAAO9O,EAAK2O,CAAK,EAExC,GADAA,EAAM3O,EAAI,OAAO,GAAI,eAAgByO,EAAS,YAAY,EACtD3/B,EAAK,OAAO,QACdk/B,GACEhO,EACA2O,EACA7/B,EACAA,EAAK,OAAO,OAAO,MAChB,CACL6/B,EAAM3O,EAAI,OAAO,IAAK,gBAAgB,EACtC,IAAI+O,EAAa/O,EAAI,OAAO,IAAI,IAAI,EAChC4N,EAAUe,EAAM,IAAIF,EAAS,KAAK,EAClCZ,EAAcc,EAAM,IAAII,EAAY,IAAKnB,EAAS,GAAG,EACzDe,EACE3O,EAAI,KAAK6N,CAAW,EACjB,KAAKA,EAAa,oBAAoB,EACtC,KACCA,EAAa,IAAKkB,EAAY,IAAKnB,EAAS,KAC5C5N,EAAI,KAAK,SAAUt4B,EAAS,CAC1B,OAAO4lC,GAAWU,GAAehO,EAAKlxB,EAAMpH,EAAS,CAAC,CACxD,CAAC,EAAG,IAAK+mC,EAAS,KAClBZ,EAAa,oBAAoB,CAAC,CAC1C,CACF,CAEI,OAAO,KAAK/+B,EAAK,KAAK,EAAE,OAAS,GACnC6/B,EAAM3O,EAAI,OAAO,QAAS,cAAc,CAE5C,CAOA,SAASgP,EAAehP,EAAKlxB,EAAM,CACjC,IAAIomB,EAAQ8K,EAAI,KAAK,QAAS,CAAC,EAC/BA,EAAI,QAAU,KAEd,IAAIE,EAASF,EAAI,OACbkJ,EAAgBhJ,EAAO,QAE3BoI,GAAYtI,EAAK9K,EAAOpmB,EAAK,OAAO,EAEhCA,EAAK,aACPA,EAAK,YAAY,OAAOkxB,EAAK9K,CAAK,EAGpCqI,GAAU,OAAO,KAAKzuB,EAAK,KAAK,CAAC,EAAE,QAAQ,SAAUV,EAAM,CACzD,IAAI85B,EAAOp5B,EAAK,MAAMV,CAAI,EACtB9J,EAAQ4jC,EAAK,OAAOlI,EAAK9K,CAAK,EAC9B7Y,GAAY/X,CAAK,EACnBA,EAAM,QAAQ,SAAUuG,EAAGvH,EAAG,CAC5B4xB,EAAM,IAAI8K,EAAI,KAAK5xB,CAAI,EAAG,IAAM9K,EAAI,IAAKuH,CAAC,CAC5C,CAAC,EAEDqqB,EAAM,IAAIgL,EAAO,KAAM,IAAM9xB,EAAM9J,CAAK,CAE5C,CAAC,EAEDmlC,GAAYzJ,EAAK9K,EAAOpmB,EAAM,GAAM,EAAI,EAEvC,CAACmqB,GAAYG,GAAUD,GAASE,GAAaH,EAAW,EAAE,QACzD,SAAU+V,EAAK,CACb,IAAI5O,EAAWvxB,EAAK,KAAKmgC,CAAG,EACvB5O,GAGLnL,EAAM,IAAIgL,EAAO,KAAM,IAAM+O,EAAK,GAAK5O,EAAS,OAAOL,EAAK9K,CAAK,CAAC,CACpE,CAAC,EAEH,OAAO,KAAKpmB,EAAK,QAAQ,EAAE,QAAQ,SAAUmgC,EAAK,CAChD,IAAI3qC,EAAQwK,EAAK,SAASmgC,CAAG,EAAE,OAAOjP,EAAK9K,CAAK,EAC5C,MAAM,QAAQ5wB,CAAK,IACrBA,EAAQ,IAAMA,EAAM,KAAK,EAAI,KAE/B4wB,EAAM,IACJgL,EAAO,SACP,IAAM73B,EAAY,GAAG4mC,CAAG,EAAI,IAC5B3qC,CAAK,CACT,CAAC,EAED,OAAO,KAAKwK,EAAK,UAAU,EAAE,QAAQ,SAAUV,EAAM,CACnD,IAAIgjB,EAAStiB,EAAK,WAAWV,CAAI,EAAE,OAAO4xB,EAAK9K,CAAK,EAChDkW,EAAcpL,EAAI,YAAY5xB,CAAI,EACtC,OAAO,KAAK,IAAIkiB,CAAiB,EAAE,QAAQ,SAAUkF,EAAM,CACzDN,EAAM,IAAIkW,EAAa,IAAM5V,EAAMpE,EAAOoE,CAAI,CAAC,CACjD,CAAC,CACH,CAAC,EAEG1mB,EAAK,UACPomB,EAAM,IAAIgL,EAAO,IAAK,aAAcpxB,EAAK,SAAS,OAAOkxB,EAAK9K,CAAK,CAAC,EAGtE,SAASga,EAAY9gC,EAAM,CACzB,IAAIrH,EAAS+H,EAAK,OAAOV,CAAI,EACzBrH,GACFmuB,EAAM,IAAIgL,EAAO,OAAQ,IAAM9xB,EAAMrH,EAAO,OAAOi5B,EAAK9K,CAAK,CAAC,CAElE,CACAga,EAAWnW,EAAM,EACjBmW,EAAWlW,EAAM,EAEb,OAAO,KAAKlqB,EAAK,KAAK,EAAE,OAAS,IACnComB,EAAMgU,EAAe,cAAc,EACnChU,EAAM,KAAKgU,EAAe,cAAc,GAG1ChU,EAAM,MAAO8K,EAAI,OAAO,QAAS,OAAQA,EAAI,QAAS,IAAI,CAC5D,CAEA,SAASmP,EAAiBlnC,EAAQ,CAChC,GAAI,SAAOA,GAAW,UAAYoU,GAAYpU,CAAM,GAIpD,SADImnC,EAAQ,OAAO,KAAKnnC,CAAM,EACrB3E,EAAI,EAAGA,EAAI8rC,EAAM,OAAQ,EAAE9rC,EAClC,GAAI0J,GAAQ,UAAU/E,EAAOmnC,EAAM9rC,CAAC,CAAC,CAAC,EACpC,MAAO,GAGX,MAAO,GACT,CAEA,SAAS+rC,EAAarP,EAAKnnB,EAASzK,EAAM,CACxC,IAAInG,EAAS4Q,EAAQ,OAAOzK,CAAI,EAChC,GAAI,CAACnG,GAAU,CAACknC,EAAgBlnC,CAAM,EACpC,OAGF,IAAIqnC,EAAUtP,EAAI,OACd38B,EAAO,OAAO,KAAK4E,CAAM,EACzBw1B,EAAU,GACVC,EAAa,GACbC,EAAU,GACV4R,EAAYvP,EAAI,OAAO,IAAI,IAAI,EACnC38B,EAAK,QAAQ,SAAU0B,EAAK,CAC1B,IAAIT,EAAQ2D,EAAOlD,CAAG,EACtB,GAAIiI,GAAQ,UAAU1I,CAAK,EAAG,CACxB,OAAOA,GAAU,aACnBA,EAAQ2D,EAAOlD,CAAG,EAAIiI,GAAQ,MAAM1I,CAAK,GAE3C,IAAIkrC,EAAOxR,GAAkB15B,EAAO,IAAI,EACxCm5B,EAAUA,GAAW+R,EAAK,QAC1B7R,EAAUA,GAAW6R,EAAK,QAC1B9R,EAAaA,GAAc8R,EAAK,UAClC,KAAO,CAEL,OADAF,EAAQC,EAAW,IAAKxqC,EAAK,GAAG,EACxB,OAAOT,EAAO,CACpB,IAAK,SACHgrC,EAAQhrC,CAAK,EACb,MACF,IAAK,SACHgrC,EAAQ,IAAKhrC,EAAO,GAAG,EACvB,MACF,IAAK,SACC,MAAM,QAAQA,CAAK,GACrBgrC,EAAQ,IAAKhrC,EAAM,KAAK,EAAG,GAAG,EAEhC,MACF,QACEgrC,EAAQtP,EAAI,KAAK17B,CAAK,CAAC,EACvB,KACJ,CACAgrC,EAAQ,GAAG,CACb,CACF,CAAC,EAED,SAASG,EAAazP,EAAK92B,EAAO,CAChC7F,EAAK,QAAQ,SAAU0B,EAAK,CAC1B,IAAIT,EAAQ2D,EAAOlD,CAAG,EACtB,GAAKiI,GAAQ,UAAU1I,CAAK,EAG5B,KAAI8gC,EAAMpF,EAAI,OAAO92B,EAAO5E,CAAK,EACjC4E,EAAMqmC,EAAW,IAAKxqC,EAAK,IAAKqgC,EAAK,GAAG,EAC1C,CAAC,CACH,CAEAvsB,EAAQ,QAAQzK,CAAI,EAAI,IAAIpB,GAAQ,gBAAgB+pB,GAAW,CAC7D,QAAS0G,EACT,WAAYC,EACZ,QAASC,EACT,IAAK4R,EACL,OAAQE,CACV,CAAC,EACD,OAAO52B,EAAQ,OAAOzK,CAAI,CAC5B,CAOA,SAASshC,GAAgB72B,EAASzQ,EAAYD,EAAU+rB,EAAS5c,EAAO,CACtE,IAAI0oB,EAAMD,GAAsB,EAGhCC,EAAI,MAAQA,EAAI,KAAK1oB,CAAK,EAG1B,OAAO,KAAKlP,EAAW,MAAM,EAAE,QAAQ,SAAUrD,EAAK,CACpDsqC,EAAYrP,EAAK53B,EAAYrD,CAAG,CAClC,CAAC,EACDi1B,GAAe,QAAQ,SAAU5rB,EAAM,CACrCihC,EAAYrP,EAAKnnB,EAASzK,CAAI,CAChC,CAAC,EAED,IAAIU,EAAO84B,GAAe/uB,EAASzQ,EAAYD,EAAU+rB,EAAS8L,CAAG,EAErE,OAAA0N,GAAa1N,EAAKlxB,CAAI,EACtBkgC,EAAchP,EAAKlxB,CAAI,EACvB4/B,EAAc1O,EAAKlxB,CAAI,EAEhB5L,GAAO88B,EAAI,QAAQ,EAAG,CAC3B,QAAS,UAAY,CACnBlxB,EAAK,OAAO,QAAQ,QAAQ,CAC9B,CACF,CAAC,CACH,CAOA,MAAO,CACL,KAAMmwB,EACN,QAASD,EACT,MAAQ,UAAY,CAClB,IAAIgB,EAAMD,GAAsB,EAC5B4P,EAAO3P,EAAI,KAAK,MAAM,EACtB4P,EAAU5P,EAAI,KAAK,SAAS,EAC5B6P,EAAS7P,EAAI,MAAM,EACvB2P,EAAKE,CAAM,EACXD,EAAQC,CAAM,EAEd,IAAI3P,EAASF,EAAI,OACb0I,EAAKxI,EAAO,GACZiJ,EAAajJ,EAAO,KACpBgJ,EAAgBhJ,EAAO,QAE3B2P,EAAO3G,EAAe,eAAe,EAErCV,GAAoBxI,EAAK2P,CAAI,EAC7BnH,GAAoBxI,EAAK4P,EAAS,KAAM,EAAI,EAG5C,IAAIE,EACAhR,IACFgR,EAAa9P,EAAI,KAAKlB,CAAa,GAIjC9vB,EAAW,yBACb4gC,EAAQ5P,EAAI,KAAKhxB,EAAW,uBAAuB,EAAG,4BAA4B,EAEpF,QAAS1L,EAAI,EAAGA,EAAI2H,EAAO,cAAe,EAAE3H,EAAG,CAC7C,IAAIunC,EAAU+E,EAAQ,IAAI1P,EAAO,WAAY,IAAK58B,EAAG,GAAG,EACpD0/B,EAAOhD,EAAI,KAAK6K,EAAS,SAAS,EACtC7H,EAAK,KACH0F,EAAI,4BAA6BplC,EAAG,KACpColC,EAAI,eACJzO,GAAmB,IACnB4Q,EAAS,mBACTnC,EAAI,wBACJplC,EAAG,IACHunC,EAAS,SACTA,EAAS,SACTA,EAAS,eACTA,EAAS,WACTA,EAAS,WACX,EAAE,KACAnC,EAAI,6BAA8BplC,EAAG,KACrColC,EAAI,mBACJplC,EAAG,IACHunC,EAAS,MACTA,EAAS,MACTA,EAAS,MACTA,EAAS,OACTA,EAAS,eAAe,EAC1B+E,EAAQ5M,CAAI,EACRlE,GACF8Q,EACEE,EAAY,6BACZxsC,EAAG,IACHunC,EAAS,YAAY,CAE3B,CACA,OAAA+E,EACE5P,EAAI,OAAO,IAAK,oBAChBA,EAAI,OAAO,IAAK,WAAYA,EAAI,OAAO,IAAK,cAAc,EAE5D,OAAO,KAAKb,CAAQ,EAAE,QAAQ,SAAUoK,EAAM,CAC5C,IAAI/J,EAAML,EAASoK,CAAI,EACnBT,EAAO+G,EAAO,IAAI1G,EAAY,IAAKI,CAAI,EACvCrgC,EAAQ82B,EAAI,MAAM,EACtB92B,EAAM,MAAO4/B,EAAM,KACjBJ,EAAI,WAAYlJ,EAAK,UACrBkJ,EAAI,YAAalJ,EAAK,KACtB0J,EAAe,IAAKK,EAAM,IAAKT,EAAM,GAAG,EAC1C8G,EAAQ1mC,CAAK,EACbymC,EACE,MAAO7G,EAAM,MAAOI,EAAe,IAAKK,EAAM,KAC9CrgC,EACA,GAAG,CACP,CAAC,EAED,OAAO,KAAKk2B,CAAY,EAAE,QAAQ,SAAUhxB,EAAM,CAChD,IAAIuxB,EAAOP,EAAahxB,CAAI,EACxBqxB,EAAOT,EAAa5wB,CAAI,EACxB06B,EAAMM,EACNlgC,EAAQ82B,EAAI,MAAM,EAEtB,GADA92B,EAAMw/B,EAAI,IAAK/I,EAAM,GAAG,EACpBtjB,GAAYojB,CAAI,EAAG,CACrB,IAAIx6B,GAAIw6B,EAAK,OACbqJ,EAAO9I,EAAI,OAAO,IAAImJ,EAAY,IAAK/6B,CAAI,EAC3Cg7B,EAAUpJ,EAAI,OAAO,IAAIkJ,EAAe,IAAK96B,CAAI,EACjDlF,EACEuG,GAAKxK,GAAG,SAAU3B,EAAG,CACnB,OAAOwlC,EAAO,IAAMxlC,EAAI,GAC1B,CAAC,EAAG,KACJmM,GAAKxK,GAAG,SAAU3B,EAAG,CACnB,OAAO8lC,EAAU,IAAM9lC,EAAI,KAAOwlC,EAAO,IAAMxlC,EAAI,IACrD,CAAC,EAAE,KAAK,EAAE,CAAC,EACbqsC,EACE,MAAOlgC,GAAKxK,GAAG,SAAU3B,EAAG,CAC1B,OAAOwlC,EAAO,IAAMxlC,EAAI,OAAS8lC,EAAU,IAAM9lC,EAAI,GACvD,CAAC,EAAE,KAAK,IAAI,EAAG,KACf4F,EACA,GAAG,CACP,MACE4/B,EAAO+G,EAAO,IAAI1G,EAAY,IAAK/6B,CAAI,EACvCg7B,EAAUyG,EAAO,IAAI3G,EAAe,IAAK96B,CAAI,EAC7ClF,EACE4/B,EAAM,KACNI,EAAe,IAAK96B,EAAM,IAAK06B,EAAM,GAAG,EAC1C6G,EACE,MAAO7G,EAAM,MAAOM,EAAS,KAC7BlgC,EACA,GAAG,EAEP0mC,EAAQ1mC,CAAK,CACf,CAAC,EAEM82B,EAAI,QAAQ,CACrB,EAAG,EACH,QAAS0P,EACX,CACF,CAEA,SAASp4B,IAAS,CAChB,MAAO,CACL,SAAU,EACV,YAAa,EACb,cAAe,EACf,iBAAkB,EAClB,YAAa,EACb,aAAc,EACd,UAAW,EACX,kBAAmB,EACnB,gBAAiB,CACnB,CACF,CAEA,IAAIy4B,GAAsB,MACtBC,GAAgC,MAChCC,GAAsB,MAEtBC,GAAc,SAAUppC,EAAIkI,EAAY,CAC1C,GAAI,CAACA,EAAW,yBACd,OAAO,KAIT,IAAImhC,EAAY,CAAC,EACjB,SAASC,GAAc,CACrB,OAAOD,EAAU,IAAI,GAAKnhC,EAAW,yBAAyB,eAAe,CAC/E,CACA,SAASqhC,EAAWC,EAAO,CACzBH,EAAU,KAAKG,CAAK,CACtB,CAGA,IAAIC,EAAiB,CAAC,EACtB,SAASC,EAAYl5B,EAAO,CAC1B,IAAIg5B,EAAQF,EAAW,EACvBphC,EAAW,yBAAyB,cAAcihC,GAAqBK,CAAK,EAC5EC,EAAe,KAAKD,CAAK,EACzBG,EAAeF,EAAe,OAAS,EAAGA,EAAe,OAAQj5B,CAAK,CACxE,CAEA,SAASo5B,GAAY,CACnB1hC,EAAW,yBAAyB,YAAYihC,EAAmB,CACrE,CAKA,SAASU,GAAgB,CACvB,KAAK,gBAAkB,GACvB,KAAK,cAAgB,GACrB,KAAK,IAAM,EACX,KAAK,MAAQ,IACf,CACA,IAAIC,EAAmB,CAAC,EACxB,SAASC,GAAqB,CAC5B,OAAOD,EAAiB,IAAI,GAAK,IAAID,CACvC,CACA,SAASG,EAAkBC,EAAc,CACvCH,EAAiB,KAAKG,CAAY,CACpC,CAGA,IAAIA,EAAe,CAAC,EACpB,SAASN,EAAgBnG,EAAOC,EAAKjzB,EAAO,CAC1C,IAAI05B,EAAKH,EAAkB,EAC3BG,EAAG,gBAAkB1G,EACrB0G,EAAG,cAAgBzG,EACnByG,EAAG,IAAM,EACTA,EAAG,MAAQ15B,EACXy5B,EAAa,KAAKC,CAAE,CACtB,CAIA,IAAIC,EAAU,CAAC,EACXC,EAAW,CAAC,EAChB,SAASC,GAAU,CACjB,IAAIv8B,EAAKtR,EAEL2B,EAAIsrC,EAAe,OACvB,GAAItrC,IAAM,EAKV,CAAAisC,EAAS,OAAS,KAAK,IAAIA,EAAS,OAAQjsC,EAAI,CAAC,EACjDgsC,EAAQ,OAAS,KAAK,IAAIA,EAAQ,OAAQhsC,EAAI,CAAC,EAC/CgsC,EAAQ,CAAC,EAAI,EACbC,EAAS,CAAC,EAAI,EAGd,IAAIE,EAAY,EAEhB,IADAx8B,EAAM,EACDtR,EAAI,EAAGA,EAAIitC,EAAe,OAAQ,EAAEjtC,EAAG,CAC1C,IAAIgtC,EAAQC,EAAejtC,CAAC,EACxB0L,EAAW,yBAAyB,kBAAkBshC,EAAON,EAA6B,GAC5FoB,GAAapiC,EAAW,yBAAyB,kBAAkBshC,EAAOP,EAAmB,EAC7FM,EAAUC,CAAK,GAEfC,EAAe37B,GAAK,EAAI07B,EAE1BW,EAAQ3tC,EAAI,CAAC,EAAI8tC,EACjBF,EAAS5tC,EAAI,CAAC,EAAIsR,CACpB,CAKA,IAJA27B,EAAe,OAAS37B,EAGxBA,EAAM,EACDtR,EAAI,EAAGA,EAAIytC,EAAa,OAAQ,EAAEztC,EAAG,CACxC,IAAIgU,EAAQy5B,EAAaztC,CAAC,EACtBgnC,EAAQhzB,EAAM,gBACdizB,EAAMjzB,EAAM,cAChBA,EAAM,KAAO25B,EAAQ1G,CAAG,EAAI0G,EAAQ3G,CAAK,EACzC,IAAI+G,EAAWH,EAAS5G,CAAK,EACzBgH,GAASJ,EAAS3G,CAAG,EACrB+G,KAAWD,GACb/5B,EAAM,MAAM,SAAWA,EAAM,IAAM,IACnCw5B,EAAiBx5B,CAAK,IAEtBA,EAAM,gBAAkB+5B,EACxB/5B,EAAM,cAAgBg6B,GACtBP,EAAan8B,GAAK,EAAI0C,EAE1B,CACAy5B,EAAa,OAASn8B,EACxB,CAEA,MAAO,CACL,WAAY47B,EACZ,SAAUE,EACV,eAAgBD,EAChB,OAAQU,EACR,qBAAsB,UAAY,CAChC,OAAOZ,EAAe,MACxB,EACA,MAAO,UAAY,CACjBJ,EAAU,KAAK,MAAMA,EAAWI,CAAc,EAC9C,QAASjtC,EAAI,EAAGA,EAAI6sC,EAAU,OAAQ7sC,IACpC0L,EAAW,yBAAyB,eAAemhC,EAAU7sC,CAAC,CAAC,EAEjEitC,EAAe,OAAS,EACxBJ,EAAU,OAAS,CACrB,EACA,QAAS,UAAY,CACnBI,EAAe,OAAS,EACxBJ,EAAU,OAAS,CACrB,CACF,CACF,EAEIoB,GAAsB,MACtBC,GAAsB,IACtBC,GAAwB,KAExBC,GAAkB,MAElBC,GAAqB,mBACrBC,GAAyB,uBAEzBC,GAAW,EACXC,GAAc,EACdC,GAAY,EAEhB,SAASC,GAAMC,EAAUC,EAAQ,CAC/B,QAAS5uC,EAAI,EAAGA,EAAI2uC,EAAS,OAAQ,EAAE3uC,EACrC,GAAI2uC,EAAS3uC,CAAC,IAAM4uC,EAClB,OAAO5uC,EAGX,MAAO,EACT,CAEA,SAAS6uC,GAAUrjC,EAAM,CACvB,IAAIO,EAAST,GAAUE,CAAI,EAC3B,GAAI,CAACO,EACH,OAAO,KAGT,IAAIvI,EAAKuI,EAAO,GACZ8kB,EAAertB,EAAG,qBAAqB,EACvCsrC,EAActrC,EAAG,cAAc,EAE/BurC,EAAiBjjC,GAAqBtI,EAAIuI,CAAM,EACpD,GAAI,CAACgjC,EACH,OAAO,KAGT,IAAIhqC,EAAc+E,GAAkB,EAChCklC,EAAWh7B,GAAM,EACjBtI,EAAaqjC,EAAe,WAC5BzT,EAAQsR,GAAYppC,EAAIkI,CAAU,EAElCujC,EAAaplC,GAAM,EACnBqlC,EAAQ1rC,EAAG,mBACX2rC,EAAS3rC,EAAG,oBAEZ8b,EAAe,CACjB,KAAM,EACN,KAAM,EACN,cAAe4vB,EACf,eAAgBC,EAChB,iBAAkBD,EAClB,kBAAmBC,EACnB,mBAAoBD,EACpB,oBAAqBC,EACrB,WAAYpjC,EAAO,UACrB,EACImvB,EAAe,CAAC,EAChBG,EAAY,CACd,SAAU,KACV,UAAW,EACX,MAAO,GACP,OAAQ,EACR,UAAW,EACb,EAEI1zB,EAASsI,GAAWzM,EAAIkI,CAAU,EAClC2L,EAActD,GAChBvQ,EACAwrC,EACAjjC,EACAkI,CAAa,EACXknB,EAAiBlO,GACnBzpB,EACAkI,EACA/D,EACAqnC,EACA33B,CAAW,EACb,SAASpD,EAAeO,GAAQ,CAC9B,OAAO2mB,EAAe,cAAc3mB,EAAM,CAC5C,CACA,IAAIymB,EAAe7jB,GAAkB5T,EAAIkI,EAAY2L,EAAa23B,CAAQ,EACtE5T,EAAcpM,GAAgBxrB,EAAIuB,EAAaiqC,EAAUjjC,CAAM,EAC/D0d,EAAerK,GACjB5b,EACAkI,EACA/D,EACA,UAAY,CAAEynC,EAAK,MAAM,KAAK,CAAE,EAChC9vB,EACA0vB,EACAjjC,CAAM,EACJ2d,EAAoB/C,GAAkBnjB,EAAIkI,EAAY/D,EAAQqnC,EAAUjjC,CAAM,EAC9E4d,EAAmBH,GACrBhmB,EACAkI,EACA/D,EACA8hB,EACAC,EACAslB,CAAQ,EACNI,EAAOpU,GACTx3B,EACAuB,EACA2G,EACA/D,EACA0P,EACA4jB,EACAxR,EACAE,EACAuR,EACAC,EACAC,EACAC,EACA/b,EACAgc,EACAvvB,CAAM,EACJklB,GAAaN,GACfntB,EACAmmB,EACAylB,EAAK,MAAM,KACX9vB,EACAuR,EAAcnlB,EAAY/D,CAAM,EAE9Bg0B,EAAYyT,EAAK,KACjB/kC,EAAS7G,EAAG,OAEZ6rC,EAAe,CAAC,EAChBC,GAAgB,CAAC,EACjBC,GAAmB,CAAC,EACpBC,EAAmB,CAACzjC,EAAO,SAAS,EAEpC0jC,GAAY,KAChB,SAASC,IAAa,CACpB,GAAIL,EAAa,SAAW,EAAG,CACzB/T,GACFA,EAAM,OAAO,EAEfmU,GAAY,KACZ,MACF,CAGAA,GAAY9lC,GAAI,KAAK+lC,EAAS,EAG9BrD,GAAK,EAGL,QAASrsC,GAAIqvC,EAAa,OAAS,EAAGrvC,IAAK,EAAG,EAAEA,GAAG,CACjD,IAAI4J,GAAKylC,EAAarvC,EAAC,EACnB4J,IACFA,GAAG0V,EAAc,KAAM,CAAC,CAE5B,CAGA9b,EAAG,MAAM,EAGL83B,GACFA,EAAM,OAAO,CAEjB,CAEA,SAASqU,IAAY,CACf,CAACF,IAAaJ,EAAa,OAAS,IACtCI,GAAY9lC,GAAI,KAAK+lC,EAAS,EAElC,CAEA,SAASE,IAAW,CACdH,KACF9lC,GAAI,OAAO+lC,EAAS,EACpBD,GAAY,KAEhB,CAEA,SAASI,GAAmBC,GAAO,CACjCA,GAAM,eAAe,EAGrBhB,EAAc,GAGdc,GAAQ,EAGRN,GAAc,QAAQ,SAAU1lC,GAAI,CAClCA,GAAG,CACL,CAAC,CACH,CAEA,SAASmmC,GAAuBD,GAAO,CAErCtsC,EAAG,SAAS,EAGZsrC,EAAc,GAGdC,EAAe,QAAQ,EACvB3T,EAAY,QAAQ,EACpB/jB,EAAY,QAAQ,EACpBoS,EAAa,QAAQ,EACrBC,EAAkB,QAAQ,EAC1BC,EAAiB,QAAQ,EACzBwR,EAAe,QAAQ,EACnBG,GACFA,EAAM,QAAQ,EAIhB8T,EAAK,MAAM,QAAQ,EAGnBO,GAAS,EAGTJ,GAAiB,QAAQ,SAAU3lC,GAAI,CACrCA,GAAG,CACL,CAAC,CACH,CAEIS,IACFA,EAAO,iBAAiBgkC,GAAoBwB,GAAmB,EAAK,EACpExlC,EAAO,iBAAiBikC,GAAwByB,GAAuB,EAAK,GAG9E,SAAS17B,IAAW,CAClBg7B,EAAa,OAAS,EACtBO,GAAQ,EAEJvlC,IACFA,EAAO,oBAAoBgkC,GAAoBwB,EAAiB,EAChExlC,EAAO,oBAAoBikC,GAAwByB,EAAqB,GAG1E3U,EAAY,MAAM,EAClBzR,EAAiB,MAAM,EACvBD,EAAkB,MAAM,EACxBD,EAAa,MAAM,EACnBwR,EAAa,MAAM,EACnB5jB,EAAY,MAAM,EAClB8jB,EAAe,MAAM,EAEjBG,GACFA,EAAM,MAAM,EAGdkU,EAAiB,QAAQ,SAAU5lC,GAAI,CACrCA,GAAG,CACL,CAAC,CACH,CAEA,SAASomC,GAAkBz6B,GAAS,CAClC9M,EAAQ,CAAC,CAAC8M,GAAS,6BAA6B,EAChD9M,EAAQ,KAAK8M,GAAS,SAAU,6BAA6B,EAE7D,SAAS06B,GAAsB16B,EAAS,CACtC,IAAIpS,EAASvD,GAAO,CAAC,EAAG2V,CAAO,EAC/B,OAAOpS,EAAO,SACd,OAAOA,EAAO,WACd,OAAOA,EAAO,QACd,OAAOA,EAAO,IAEV,YAAaA,GAAUA,EAAO,QAAQ,KACxCA,EAAO,QAAQ,OAASA,EAAO,QAAQ,QAAUA,EAAO,QAAQ,GAChE,OAAOA,EAAO,QAAQ,IAGxB,SAAS+sC,EAAOplC,EAAM,CACpB,GAAIA,KAAQ3H,EAAQ,CAClB,IAAIgtC,EAAQhtC,EAAO2H,CAAI,EACvB,OAAO3H,EAAO2H,CAAI,EAClB,OAAO,KAAKqlC,CAAK,EAAE,QAAQ,SAAUje,EAAM,CACzC/uB,EAAO2H,EAAO,IAAMonB,CAAI,EAAIie,EAAMje,CAAI,CACxC,CAAC,CACH,CACF,CACA,OAAAge,EAAM,OAAO,EACbA,EAAM,OAAO,EACbA,EAAM,MAAM,EACZA,EAAM,SAAS,EACfA,EAAM,eAAe,EACrBA,EAAM,SAAS,EACfA,EAAM,QAAQ,EAEV,QAAS36B,IACXpS,EAAO,IAAMoS,EAAQ,KAGhBpS,CACT,CAEA,SAASitC,GAAiBzrC,EAAQ0rC,EAAW,CAC3C,IAAIC,EAAc,CAAC,EACfC,EAAe,CAAC,EACpB,cAAO,KAAK5rC,CAAM,EAAE,QAAQ,SAAU6rC,EAAQ,CAC5C,IAAIxvC,EAAQ2D,EAAO6rC,CAAM,EACzB,GAAI9mC,GAAQ,UAAU1I,CAAK,EAAG,CAC5BuvC,EAAaC,CAAM,EAAI9mC,GAAQ,MAAM1I,EAAOwvC,CAAM,EAClD,MACF,SAAWH,GAAa,MAAM,QAAQrvC,CAAK,GACzC,QAAShB,EAAI,EAAGA,EAAIgB,EAAM,OAAQ,EAAEhB,EAClC,GAAI0J,GAAQ,UAAU1I,EAAMhB,CAAC,CAAC,EAAG,CAC/BuwC,EAAaC,CAAM,EAAI9mC,GAAQ,MAAM1I,EAAOwvC,CAAM,EAClD,MACF,EAGJF,EAAYE,CAAM,EAAIxvC,CACxB,CAAC,EACM,CACL,QAASuvC,EACT,OAAQD,CACV,CACF,CAGA,IAAI1f,GAAUwf,GAAgB76B,GAAQ,SAAW,CAAC,EAAG,EAAI,EACrD1Q,GAAWurC,GAAgB76B,GAAQ,UAAY,CAAC,EAAG,EAAI,EACvDzQ,GAAasrC,GAAgB76B,GAAQ,YAAc,CAAC,EAAG,EAAK,EAC5DzV,EAAOswC,GAAgBH,GAAqB16B,EAAO,EAAG,EAAK,EAE3Dy5B,EAAW,CACb,QAAS,EACT,QAAS,EACT,MAAO,CACT,EAEIyB,EAAWrB,EAAK,QAAQtvC,EAAMgF,GAAYD,GAAU+rB,GAASoe,CAAQ,EAErEtK,EAAO+L,EAAS,KAChBpF,GAAQoF,EAAS,MACjB7e,EAAQ6e,EAAS,MAIjBC,EAAc,CAAC,EACnB,SAASC,EAAS54B,EAAO,CACvB,KAAO24B,EAAY,OAAS34B,GAC1B24B,EAAY,KAAK,IAAI,EAEvB,OAAOA,CACT,CAEA,SAASE,EAAaplC,EAAMqnB,EAAM,CAChC,IAAI7yB,EAIJ,GAHI8uC,GACFrmC,EAAQ,MAAM,cAAc,EAE1B,OAAO+C,GAAS,WAClB,OAAOomB,EAAM,KAAK,KAAM,KAAMpmB,EAAM,CAAC,EAChC,GAAI,OAAOqnB,GAAS,WACzB,GAAI,OAAOrnB,GAAS,SAClB,IAAKxL,EAAI,EAAGA,EAAIwL,EAAM,EAAExL,EACtB4xB,EAAM,KAAK,KAAM,KAAMiB,EAAM7yB,CAAC,UAEvB,MAAM,QAAQwL,CAAI,EAC3B,IAAKxL,EAAI,EAAGA,EAAIwL,EAAK,OAAQ,EAAExL,EAC7B4xB,EAAM,KAAK,KAAMpmB,EAAKxL,CAAC,EAAG6yB,EAAM7yB,CAAC,MAGnC,QAAO4xB,EAAM,KAAK,KAAMpmB,EAAMqnB,EAAM,CAAC,UAE9B,OAAOrnB,GAAS,UACzB,GAAIA,EAAO,EACT,OAAO6/B,GAAM,KAAK,KAAMsF,EAAQnlC,EAAO,CAAC,EAAGA,EAAO,CAAC,UAE5C,MAAM,QAAQA,CAAI,GAC3B,GAAIA,EAAK,OACP,OAAO6/B,GAAM,KAAK,KAAM7/B,EAAMA,EAAK,MAAM,MAG3C,QAAOk5B,EAAK,KAAK,KAAMl5B,CAAI,CAE/B,CAEA,OAAO5L,GAAOgxC,EAAa,CACzB,MAAO5B,EACP,QAAS,UAAY,CACnByB,EAAS,QAAQ,CACnB,CACF,CAAC,CACH,CAEA,IAAII,GAASlnB,EAAiB,OAASqmB,GAAiB,CACtD,YAAatmC,GAAQ,OAAO,KAAK,KAAM6kC,GAAU,aAAa,CAChE,CAAC,EAED,SAASuC,GAAWC,GAAGx7B,GAAS,CAC9B,IAAIy7B,GAAa,EACjB5B,EAAK,MAAM,KAAK,EAEhB,IAAIvnC,GAAI0N,GAAQ,MACZ1N,KACFrE,EAAG,WAAW,CAACqE,GAAE,CAAC,GAAK,EAAG,CAACA,GAAE,CAAC,GAAK,EAAG,CAACA,GAAE,CAAC,GAAK,EAAG,CAACA,GAAE,CAAC,GAAK,CAAC,EAC5DmpC,IAAc/C,IAEZ,UAAW14B,KACb/R,EAAG,WAAW,CAAC+R,GAAQ,KAAK,EAC5By7B,IAAc9C,IAEZ,YAAa34B,KACf/R,EAAG,aAAa+R,GAAQ,QAAU,CAAC,EACnCy7B,IAAc7C,IAGhB1lC,EAAQ,CAAC,CAACuoC,GAAY,4CAA4C,EAClExtC,EAAG,MAAMwtC,EAAU,CACrB,CAEA,SAASC,GAAO17B,GAAS,CAIvB,GAHA9M,EACE,OAAO8M,IAAY,UAAYA,GAC/B,uCAAuC,EACrC,gBAAiBA,GACnB,GAAIA,GAAQ,aACRA,GAAQ,uBAAyB,kBACnC,QAASvV,GAAI,EAAGA,GAAI,EAAG,EAAEA,GACvB6wC,GAAOjxC,GAAO,CACZ,YAAa2V,GAAQ,YAAY,MAAMvV,EAAC,CAC1C,EAAGuV,EAAO,EAAGu7B,EAAS,OAGxBD,GAAOt7B,GAASu7B,EAAS,OAG3BA,GAAU,KAAMv7B,EAAO,CAE3B,CAEA,SAAS27B,GAAOtnC,GAAI,CAClBnB,EAAQ,KAAKmB,GAAI,WAAY,0CAA0C,EACvEylC,EAAa,KAAKzlC,EAAE,EAEpB,SAASunC,IAAU,CAIjB,IAAInxC,GAAI0uC,GAAKW,EAAczlC,EAAE,EAC7BnB,EAAQzI,IAAK,EAAG,6BAA6B,EAC7C,SAASoxC,IAAiB,CACxB,IAAIC,GAAQ3C,GAAKW,EAAc+B,EAAa,EAC5C/B,EAAagC,EAAK,EAAIhC,EAAaA,EAAa,OAAS,CAAC,EAC1DA,EAAa,QAAU,EACnBA,EAAa,QAAU,GACzBO,GAAQ,CAEZ,CACAP,EAAarvC,EAAC,EAAIoxC,EACpB,CAEA,OAAAzB,GAAS,EAEF,CACL,OAAQwB,EACV,CACF,CAGA,SAASG,IAAgB,CACvB,IAAIjT,GAAW1C,EAAU,SACrB4V,GAAa5V,EAAU,YAC3B0C,GAAS,CAAC,EAAIA,GAAS,CAAC,EAAIkT,GAAW,CAAC,EAAIA,GAAW,CAAC,EAAI,EAC5DjyB,EAAa,cACXA,EAAa,iBACbA,EAAa,mBACb+e,GAAS,CAAC,EACVkT,GAAW,CAAC,EAAI/tC,EAAG,mBACrB8b,EAAa,eACXA,EAAa,kBACbA,EAAa,oBACb+e,GAAS,CAAC,EACVkT,GAAW,CAAC,EAAI/tC,EAAG,mBACvB,CAEA,SAAS6oC,IAAQ,CACf/sB,EAAa,MAAQ,EACrBA,EAAa,KAAOkyB,GAAI,EACxBF,GAAa,EACblC,EAAK,MAAM,KAAK,CAClB,CAEA,SAAS9C,IAAW,CAClB7iB,EAAa,QAAQ,EACrB6nB,GAAa,EACblC,EAAK,MAAM,QAAQ,EACf9T,GACFA,EAAM,OAAO,CAEjB,CAEA,SAASkW,IAAO,CACd,OAAQ3nC,GAAM,EAAIolC,GAAc,GAClC,CAEA3C,GAAQ,EAER,SAASmF,GAAa3B,GAAO4B,GAAU,CACrCjpC,EAAQ,KAAKipC,GAAU,WAAY,sCAAsC,EAEzE,IAAIC,GACJ,OAAQ7B,GAAO,CACb,IAAK,QACH,OAAOoB,GAAMQ,EAAQ,EACvB,IAAK,OACHC,GAAYrC,GACZ,MACF,IAAK,UACHqC,GAAYpC,GACZ,MACF,IAAK,UACHoC,GAAYnC,EACZ,MACF,QACE/mC,EAAQ,MAAM,0DAA0D,CAC5E,CAEA,OAAAkpC,GAAU,KAAKD,EAAQ,EAChB,CACL,OAAQ,UAAY,CAClB,QAAS1xC,GAAI,EAAGA,GAAI2xC,GAAU,OAAQ,EAAE3xC,GACtC,GAAI2xC,GAAU3xC,EAAC,IAAM0xC,GAAU,CAC7BC,GAAU3xC,EAAC,EAAI2xC,GAAUA,GAAU,OAAS,CAAC,EAC7CA,GAAU,IAAI,EACd,MACF,CAEJ,CACF,CACF,CAEA,IAAIC,GAAOhyC,GAAOowC,GAAkB,CAElC,MAAOiB,GAGP,KAAMvnC,GAAQ,OAAO,KAAK,KAAM6kC,EAAQ,EACxC,QAAS7kC,GAAQ,OAAO,KAAK,KAAM8kC,EAAW,EAC9C,KAAM9kC,GAAQ,OAAO,KAAK,KAAM+kC,EAAS,EAGzC,KAAMuB,GAAiB,CAAC,CAAC,EAGzB,OAAQ,SAAUz6B,GAAS,CACzB,OAAO8B,EAAY,OAAO9B,GAAS64B,GAAiB,GAAO,EAAK,CAClE,EACA,SAAU,SAAU74B,GAAS,CAC3B,OAAO0lB,EAAa,OAAO1lB,GAAS,EAAK,CAC3C,EACA,QAASkU,EAAa,SACtB,KAAMA,EAAa,WACnB,aAAcC,EAAkB,OAChC,YAAaC,EAAiB,OAC9B,gBAAiBA,EAAiB,WAClC,IAAKwR,EAAe,UAGpB,WAAYtK,EAGZ,MAAOqgB,GACP,GAAIO,GAGJ,OAAQ9pC,EACR,aAAc,SAAUmD,GAAM,CAC5B,OAAOnD,EAAO,WAAW,QAAQmD,GAAK,YAAY,CAAC,GAAK,CAC1D,EAGA,KAAMmmB,GAGN,QAAS5c,GAGT,IAAK7Q,EACL,SAAU8oC,GAEV,KAAM,UAAY,CAChBD,GAAK,EACD/Q,GACFA,EAAM,OAAO,CAEjB,EAGA,IAAKkW,GAGL,MAAOxC,CACT,CAAC,EAED,OAAAjjC,EAAO,OAAO,KAAM6lC,EAAI,EAEjBA,EACT,CAEA,OAAO/C,EAEP,CAAE,C", "sources": ["webpack://labwise-web/./node_modules/regl/dist/regl.js"], "sourcesContent": ["(function (global, factory) {\n    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n    typeof define === 'function' && define.amd ? define(factory) :\n    (global.createREGL = factory());\n}(this, (function () { 'use strict';\n\nvar isTypedArray = function (x) {\n  return (\n    x instanceof Uint8Array ||\n    x instanceof Uint16Array ||\n    x instanceof Uint32Array ||\n    x instanceof Int8Array ||\n    x instanceof Int16Array ||\n    x instanceof Int32Array ||\n    x instanceof Float32Array ||\n    x instanceof Float64Array ||\n    x instanceof Uint8ClampedArray\n  )\n}\n\nvar extend = function (base, opts) {\n  var keys = Object.keys(opts)\n  for (var i = 0; i < keys.length; ++i) {\n    base[keys[i]] = opts[keys[i]]\n  }\n  return base\n}\n\n// Error checking and parameter validation.\n//\n// Statements for the form `check.someProcedure(...)` get removed by\n// a browserify transform for optimized/minified bundles.\n//\n/* globals atob */\nvar endl = '\\n'\n\n// only used for extracting shader names.  if atob not present, then errors\n// will be slightly crappier\nfunction decodeB64 (str) {\n  if (typeof atob !== 'undefined') {\n    return atob(str)\n  }\n  return 'base64:' + str\n}\n\nfunction raise (message) {\n  var error = new Error('(regl) ' + message)\n  console.error(error)\n  throw error\n}\n\nfunction check (pred, message) {\n  if (!pred) {\n    raise(message)\n  }\n}\n\nfunction encolon (message) {\n  if (message) {\n    return ': ' + message\n  }\n  return ''\n}\n\nfunction checkParameter (param, possibilities, message) {\n  if (!(param in possibilities)) {\n    raise('unknown parameter (' + param + ')' + encolon(message) +\n          '. possible values: ' + Object.keys(possibilities).join())\n  }\n}\n\nfunction checkIsTypedArray (data, message) {\n  if (!isTypedArray(data)) {\n    raise(\n      'invalid parameter type' + encolon(message) +\n      '. must be a typed array')\n  }\n}\n\nfunction standardTypeEh (value, type) {\n  switch (type) {\n    case 'number': return typeof value === 'number'\n    case 'object': return typeof value === 'object'\n    case 'string': return typeof value === 'string'\n    case 'boolean': return typeof value === 'boolean'\n    case 'function': return typeof value === 'function'\n    case 'undefined': return typeof value === 'undefined'\n    case 'symbol': return typeof value === 'symbol'\n  }\n}\n\nfunction checkTypeOf (value, type, message) {\n  if (!standardTypeEh(value, type)) {\n    raise(\n      'invalid parameter type' + encolon(message) +\n      '. expected ' + type + ', got ' + (typeof value))\n  }\n}\n\nfunction checkNonNegativeInt (value, message) {\n  if (!((value >= 0) &&\n        ((value | 0) === value))) {\n    raise('invalid parameter type, (' + value + ')' + encolon(message) +\n          '. must be a nonnegative integer')\n  }\n}\n\nfunction checkOneOf (value, list, message) {\n  if (list.indexOf(value) < 0) {\n    raise('invalid value' + encolon(message) + '. must be one of: ' + list)\n  }\n}\n\nvar constructorKeys = [\n  'gl',\n  'canvas',\n  'container',\n  'attributes',\n  'pixelRatio',\n  'extensions',\n  'optionalExtensions',\n  'profile',\n  'onDone'\n]\n\nfunction checkConstructor (obj) {\n  Object.keys(obj).forEach(function (key) {\n    if (constructorKeys.indexOf(key) < 0) {\n      raise('invalid regl constructor argument \"' + key + '\". must be one of ' + constructorKeys)\n    }\n  })\n}\n\nfunction leftPad (str, n) {\n  str = str + ''\n  while (str.length < n) {\n    str = ' ' + str\n  }\n  return str\n}\n\nfunction ShaderFile () {\n  this.name = 'unknown'\n  this.lines = []\n  this.index = {}\n  this.hasErrors = false\n}\n\nfunction ShaderLine (number, line) {\n  this.number = number\n  this.line = line\n  this.errors = []\n}\n\nfunction ShaderError (fileNumber, lineNumber, message) {\n  this.file = fileNumber\n  this.line = lineNumber\n  this.message = message\n}\n\nfunction guessCommand () {\n  var error = new Error()\n  var stack = (error.stack || error).toString()\n  var pat = /compileProcedure.*\\n\\s*at.*\\((.*)\\)/.exec(stack)\n  if (pat) {\n    return pat[1]\n  }\n  var pat2 = /compileProcedure.*\\n\\s*at\\s+(.*)(\\n|$)/.exec(stack)\n  if (pat2) {\n    return pat2[1]\n  }\n  return 'unknown'\n}\n\nfunction guessCallSite () {\n  var error = new Error()\n  var stack = (error.stack || error).toString()\n  var pat = /at REGLCommand.*\\n\\s+at.*\\((.*)\\)/.exec(stack)\n  if (pat) {\n    return pat[1]\n  }\n  var pat2 = /at REGLCommand.*\\n\\s+at\\s+(.*)\\n/.exec(stack)\n  if (pat2) {\n    return pat2[1]\n  }\n  return 'unknown'\n}\n\nfunction parseSource (source, command) {\n  var lines = source.split('\\n')\n  var lineNumber = 1\n  var fileNumber = 0\n  var files = {\n    unknown: new ShaderFile(),\n    0: new ShaderFile()\n  }\n  files.unknown.name = files[0].name = command || guessCommand()\n  files.unknown.lines.push(new ShaderLine(0, ''))\n  for (var i = 0; i < lines.length; ++i) {\n    var line = lines[i]\n    var parts = /^\\s*#\\s*(\\w+)\\s+(.+)\\s*$/.exec(line)\n    if (parts) {\n      switch (parts[1]) {\n        case 'line':\n          var lineNumberInfo = /(\\d+)(\\s+\\d+)?/.exec(parts[2])\n          if (lineNumberInfo) {\n            lineNumber = lineNumberInfo[1] | 0\n            if (lineNumberInfo[2]) {\n              fileNumber = lineNumberInfo[2] | 0\n              if (!(fileNumber in files)) {\n                files[fileNumber] = new ShaderFile()\n              }\n            }\n          }\n          break\n        case 'define':\n          var nameInfo = /SHADER_NAME(_B64)?\\s+(.*)$/.exec(parts[2])\n          if (nameInfo) {\n            files[fileNumber].name = (nameInfo[1]\n              ? decodeB64(nameInfo[2])\n              : nameInfo[2])\n          }\n          break\n      }\n    }\n    files[fileNumber].lines.push(new ShaderLine(lineNumber++, line))\n  }\n  Object.keys(files).forEach(function (fileNumber) {\n    var file = files[fileNumber]\n    file.lines.forEach(function (line) {\n      file.index[line.number] = line\n    })\n  })\n  return files\n}\n\nfunction parseErrorLog (errLog) {\n  var result = []\n  errLog.split('\\n').forEach(function (errMsg) {\n    if (errMsg.length < 5) {\n      return\n    }\n    var parts = /^ERROR:\\s+(\\d+):(\\d+):\\s*(.*)$/.exec(errMsg)\n    if (parts) {\n      result.push(new ShaderError(\n        parts[1] | 0,\n        parts[2] | 0,\n        parts[3].trim()))\n    } else if (errMsg.length > 0) {\n      result.push(new ShaderError('unknown', 0, errMsg))\n    }\n  })\n  return result\n}\n\nfunction annotateFiles (files, errors) {\n  errors.forEach(function (error) {\n    var file = files[error.file]\n    if (file) {\n      var line = file.index[error.line]\n      if (line) {\n        line.errors.push(error)\n        file.hasErrors = true\n        return\n      }\n    }\n    files.unknown.hasErrors = true\n    files.unknown.lines[0].errors.push(error)\n  })\n}\n\nfunction checkShaderError (gl, shader, source, type, command) {\n  if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {\n    var errLog = gl.getShaderInfoLog(shader)\n    var typeName = type === gl.FRAGMENT_SHADER ? 'fragment' : 'vertex'\n    checkCommandType(source, 'string', typeName + ' shader source must be a string', command)\n    var files = parseSource(source, command)\n    var errors = parseErrorLog(errLog)\n    annotateFiles(files, errors)\n\n    Object.keys(files).forEach(function (fileNumber) {\n      var file = files[fileNumber]\n      if (!file.hasErrors) {\n        return\n      }\n\n      var strings = ['']\n      var styles = ['']\n\n      function push (str, style) {\n        strings.push(str)\n        styles.push(style || '')\n      }\n\n      push('file number ' + fileNumber + ': ' + file.name + '\\n', 'color:red;text-decoration:underline;font-weight:bold')\n\n      file.lines.forEach(function (line) {\n        if (line.errors.length > 0) {\n          push(leftPad(line.number, 4) + '|  ', 'background-color:yellow; font-weight:bold')\n          push(line.line + endl, 'color:red; background-color:yellow; font-weight:bold')\n\n          // try to guess token\n          var offset = 0\n          line.errors.forEach(function (error) {\n            var message = error.message\n            var token = /^\\s*'(.*)'\\s*:\\s*(.*)$/.exec(message)\n            if (token) {\n              var tokenPat = token[1]\n              message = token[2]\n              switch (tokenPat) {\n                case 'assign':\n                  tokenPat = '='\n                  break\n              }\n              offset = Math.max(line.line.indexOf(tokenPat, offset), 0)\n            } else {\n              offset = 0\n            }\n\n            push(leftPad('| ', 6))\n            push(leftPad('^^^', offset + 3) + endl, 'font-weight:bold')\n            push(leftPad('| ', 6))\n            push(message + endl, 'font-weight:bold')\n          })\n          push(leftPad('| ', 6) + endl)\n        } else {\n          push(leftPad(line.number, 4) + '|  ')\n          push(line.line + endl, 'color:red')\n        }\n      })\n      if (typeof document !== 'undefined' && !window.chrome) {\n        styles[0] = strings.join('%c')\n        console.log.apply(console, styles)\n      } else {\n        console.log(strings.join(''))\n      }\n    })\n\n    check.raise('Error compiling ' + typeName + ' shader, ' + files[0].name)\n  }\n}\n\nfunction checkLinkError (gl, program, fragShader, vertShader, command) {\n  if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {\n    var errLog = gl.getProgramInfoLog(program)\n    var fragParse = parseSource(fragShader, command)\n    var vertParse = parseSource(vertShader, command)\n\n    var header = 'Error linking program with vertex shader, \"' +\n      vertParse[0].name + '\", and fragment shader \"' + fragParse[0].name + '\"'\n\n    if (typeof document !== 'undefined') {\n      console.log('%c' + header + endl + '%c' + errLog,\n        'color:red;text-decoration:underline;font-weight:bold',\n        'color:red')\n    } else {\n      console.log(header + endl + errLog)\n    }\n    check.raise(header)\n  }\n}\n\nfunction saveCommandRef (object) {\n  object._commandRef = guessCommand()\n}\n\nfunction saveDrawCommandInfo (opts, uniforms, attributes, stringStore) {\n  saveCommandRef(opts)\n\n  function id (str) {\n    if (str) {\n      return stringStore.id(str)\n    }\n    return 0\n  }\n  opts._fragId = id(opts.static.frag)\n  opts._vertId = id(opts.static.vert)\n\n  function addProps (dict, set) {\n    Object.keys(set).forEach(function (u) {\n      dict[stringStore.id(u)] = true\n    })\n  }\n\n  var uniformSet = opts._uniformSet = {}\n  addProps(uniformSet, uniforms.static)\n  addProps(uniformSet, uniforms.dynamic)\n\n  var attributeSet = opts._attributeSet = {}\n  addProps(attributeSet, attributes.static)\n  addProps(attributeSet, attributes.dynamic)\n\n  opts._hasCount = (\n    'count' in opts.static ||\n    'count' in opts.dynamic ||\n    'elements' in opts.static ||\n    'elements' in opts.dynamic)\n}\n\nfunction commandRaise (message, command) {\n  var callSite = guessCallSite()\n  raise(message +\n    ' in command ' + (command || guessCommand()) +\n    (callSite === 'unknown' ? '' : ' called from ' + callSite))\n}\n\nfunction checkCommand (pred, message, command) {\n  if (!pred) {\n    commandRaise(message, command || guessCommand())\n  }\n}\n\nfunction checkParameterCommand (param, possibilities, message, command) {\n  if (!(param in possibilities)) {\n    commandRaise(\n      'unknown parameter (' + param + ')' + encolon(message) +\n      '. possible values: ' + Object.keys(possibilities).join(),\n      command || guessCommand())\n  }\n}\n\nfunction checkCommandType (value, type, message, command) {\n  if (!standardTypeEh(value, type)) {\n    commandRaise(\n      'invalid parameter type' + encolon(message) +\n      '. expected ' + type + ', got ' + (typeof value),\n      command || guessCommand())\n  }\n}\n\nfunction checkOptional (block) {\n  block()\n}\n\nfunction checkFramebufferFormat (attachment, texFormats, rbFormats) {\n  if (attachment.texture) {\n    checkOneOf(\n      attachment.texture._texture.internalformat,\n      texFormats,\n      'unsupported texture format for attachment')\n  } else {\n    checkOneOf(\n      attachment.renderbuffer._renderbuffer.format,\n      rbFormats,\n      'unsupported renderbuffer format for attachment')\n  }\n}\n\nvar GL_CLAMP_TO_EDGE = 0x812F\n\nvar GL_NEAREST = 0x2600\nvar GL_NEAREST_MIPMAP_NEAREST = 0x2700\nvar GL_LINEAR_MIPMAP_NEAREST = 0x2701\nvar GL_NEAREST_MIPMAP_LINEAR = 0x2702\nvar GL_LINEAR_MIPMAP_LINEAR = 0x2703\n\nvar GL_BYTE = 5120\nvar GL_UNSIGNED_BYTE = 5121\nvar GL_SHORT = 5122\nvar GL_UNSIGNED_SHORT = 5123\nvar GL_INT = 5124\nvar GL_UNSIGNED_INT = 5125\nvar GL_FLOAT = 5126\n\nvar GL_UNSIGNED_SHORT_4_4_4_4 = 0x8033\nvar GL_UNSIGNED_SHORT_5_5_5_1 = 0x8034\nvar GL_UNSIGNED_SHORT_5_6_5 = 0x8363\nvar GL_UNSIGNED_INT_24_8_WEBGL = 0x84FA\n\nvar GL_HALF_FLOAT_OES = 0x8D61\n\nvar TYPE_SIZE = {}\n\nTYPE_SIZE[GL_BYTE] =\nTYPE_SIZE[GL_UNSIGNED_BYTE] = 1\n\nTYPE_SIZE[GL_SHORT] =\nTYPE_SIZE[GL_UNSIGNED_SHORT] =\nTYPE_SIZE[GL_HALF_FLOAT_OES] =\nTYPE_SIZE[GL_UNSIGNED_SHORT_5_6_5] =\nTYPE_SIZE[GL_UNSIGNED_SHORT_4_4_4_4] =\nTYPE_SIZE[GL_UNSIGNED_SHORT_5_5_5_1] = 2\n\nTYPE_SIZE[GL_INT] =\nTYPE_SIZE[GL_UNSIGNED_INT] =\nTYPE_SIZE[GL_FLOAT] =\nTYPE_SIZE[GL_UNSIGNED_INT_24_8_WEBGL] = 4\n\nfunction pixelSize (type, channels) {\n  if (type === GL_UNSIGNED_SHORT_5_5_5_1 ||\n      type === GL_UNSIGNED_SHORT_4_4_4_4 ||\n      type === GL_UNSIGNED_SHORT_5_6_5) {\n    return 2\n  } else if (type === GL_UNSIGNED_INT_24_8_WEBGL) {\n    return 4\n  } else {\n    return TYPE_SIZE[type] * channels\n  }\n}\n\nfunction isPow2 (v) {\n  return !(v & (v - 1)) && (!!v)\n}\n\nfunction checkTexture2D (info, mipData, limits) {\n  var i\n  var w = mipData.width\n  var h = mipData.height\n  var c = mipData.channels\n\n  // Check texture shape\n  check(w > 0 && w <= limits.maxTextureSize &&\n        h > 0 && h <= limits.maxTextureSize,\n  'invalid texture shape')\n\n  // check wrap mode\n  if (info.wrapS !== GL_CLAMP_TO_EDGE || info.wrapT !== GL_CLAMP_TO_EDGE) {\n    check(isPow2(w) && isPow2(h),\n      'incompatible wrap mode for texture, both width and height must be power of 2')\n  }\n\n  if (mipData.mipmask === 1) {\n    if (w !== 1 && h !== 1) {\n      check(\n        info.minFilter !== GL_NEAREST_MIPMAP_NEAREST &&\n        info.minFilter !== GL_NEAREST_MIPMAP_LINEAR &&\n        info.minFilter !== GL_LINEAR_MIPMAP_NEAREST &&\n        info.minFilter !== GL_LINEAR_MIPMAP_LINEAR,\n        'min filter requires mipmap')\n    }\n  } else {\n    // texture must be power of 2\n    check(isPow2(w) && isPow2(h),\n      'texture must be a square power of 2 to support mipmapping')\n    check(mipData.mipmask === (w << 1) - 1,\n      'missing or incomplete mipmap data')\n  }\n\n  if (mipData.type === GL_FLOAT) {\n    if (limits.extensions.indexOf('oes_texture_float_linear') < 0) {\n      check(info.minFilter === GL_NEAREST && info.magFilter === GL_NEAREST,\n        'filter not supported, must enable oes_texture_float_linear')\n    }\n    check(!info.genMipmaps,\n      'mipmap generation not supported with float textures')\n  }\n\n  // check image complete\n  var mipimages = mipData.images\n  for (i = 0; i < 16; ++i) {\n    if (mipimages[i]) {\n      var mw = w >> i\n      var mh = h >> i\n      check(mipData.mipmask & (1 << i), 'missing mipmap data')\n\n      var img = mipimages[i]\n\n      check(\n        img.width === mw &&\n        img.height === mh,\n        'invalid shape for mip images')\n\n      check(\n        img.format === mipData.format &&\n        img.internalformat === mipData.internalformat &&\n        img.type === mipData.type,\n        'incompatible type for mip image')\n\n      if (img.compressed) {\n        // TODO: check size for compressed images\n      } else if (img.data) {\n        // check(img.data.byteLength === mw * mh *\n        // Math.max(pixelSize(img.type, c), img.unpackAlignment),\n        var rowSize = Math.ceil(pixelSize(img.type, c) * mw / img.unpackAlignment) * img.unpackAlignment\n        check(img.data.byteLength === rowSize * mh,\n          'invalid data for image, buffer size is inconsistent with image format')\n      } else if (img.element) {\n        // TODO: check element can be loaded\n      } else if (img.copy) {\n        // TODO: check compatible format and type\n      }\n    } else if (!info.genMipmaps) {\n      check((mipData.mipmask & (1 << i)) === 0, 'extra mipmap data')\n    }\n  }\n\n  if (mipData.compressed) {\n    check(!info.genMipmaps,\n      'mipmap generation for compressed images not supported')\n  }\n}\n\nfunction checkTextureCube (texture, info, faces, limits) {\n  var w = texture.width\n  var h = texture.height\n  var c = texture.channels\n\n  // Check texture shape\n  check(\n    w > 0 && w <= limits.maxTextureSize && h > 0 && h <= limits.maxTextureSize,\n    'invalid texture shape')\n  check(\n    w === h,\n    'cube map must be square')\n  check(\n    info.wrapS === GL_CLAMP_TO_EDGE && info.wrapT === GL_CLAMP_TO_EDGE,\n    'wrap mode not supported by cube map')\n\n  for (var i = 0; i < faces.length; ++i) {\n    var face = faces[i]\n    check(\n      face.width === w && face.height === h,\n      'inconsistent cube map face shape')\n\n    if (info.genMipmaps) {\n      check(!face.compressed,\n        'can not generate mipmap for compressed textures')\n      check(face.mipmask === 1,\n        'can not specify mipmaps and generate mipmaps')\n    } else {\n      // TODO: check mip and filter mode\n    }\n\n    var mipmaps = face.images\n    for (var j = 0; j < 16; ++j) {\n      var img = mipmaps[j]\n      if (img) {\n        var mw = w >> j\n        var mh = h >> j\n        check(face.mipmask & (1 << j), 'missing mipmap data')\n        check(\n          img.width === mw &&\n          img.height === mh,\n          'invalid shape for mip images')\n        check(\n          img.format === texture.format &&\n          img.internalformat === texture.internalformat &&\n          img.type === texture.type,\n          'incompatible type for mip image')\n\n        if (img.compressed) {\n          // TODO: check size for compressed images\n        } else if (img.data) {\n          check(img.data.byteLength === mw * mh *\n            Math.max(pixelSize(img.type, c), img.unpackAlignment),\n          'invalid data for image, buffer size is inconsistent with image format')\n        } else if (img.element) {\n          // TODO: check element can be loaded\n        } else if (img.copy) {\n          // TODO: check compatible format and type\n        }\n      }\n    }\n  }\n}\n\nvar check$1 = extend(check, {\n  optional: checkOptional,\n  raise: raise,\n  commandRaise: commandRaise,\n  command: checkCommand,\n  parameter: checkParameter,\n  commandParameter: checkParameterCommand,\n  constructor: checkConstructor,\n  type: checkTypeOf,\n  commandType: checkCommandType,\n  isTypedArray: checkIsTypedArray,\n  nni: checkNonNegativeInt,\n  oneOf: checkOneOf,\n  shaderError: checkShaderError,\n  linkError: checkLinkError,\n  callSite: guessCallSite,\n  saveCommandRef: saveCommandRef,\n  saveDrawInfo: saveDrawCommandInfo,\n  framebufferFormat: checkFramebufferFormat,\n  guessCommand: guessCommand,\n  texture2D: checkTexture2D,\n  textureCube: checkTextureCube\n});\n\nvar VARIABLE_COUNTER = 0\n\nvar DYN_FUNC = 0\nvar DYN_CONSTANT = 5\nvar DYN_ARRAY = 6\n\nfunction DynamicVariable (type, data) {\n  this.id = (VARIABLE_COUNTER++)\n  this.type = type\n  this.data = data\n}\n\nfunction escapeStr (str) {\n  return str.replace(/\\\\/g, '\\\\\\\\').replace(/\"/g, '\\\\\"')\n}\n\nfunction splitParts (str) {\n  if (str.length === 0) {\n    return []\n  }\n\n  var firstChar = str.charAt(0)\n  var lastChar = str.charAt(str.length - 1)\n\n  if (str.length > 1 &&\n      firstChar === lastChar &&\n      (firstChar === '\"' || firstChar === \"'\")) {\n    return ['\"' + escapeStr(str.substr(1, str.length - 2)) + '\"']\n  }\n\n  var parts = /\\[(false|true|null|\\d+|'[^']*'|\"[^\"]*\")\\]/.exec(str)\n  if (parts) {\n    return (\n      splitParts(str.substr(0, parts.index))\n        .concat(splitParts(parts[1]))\n        .concat(splitParts(str.substr(parts.index + parts[0].length)))\n    )\n  }\n\n  var subparts = str.split('.')\n  if (subparts.length === 1) {\n    return ['\"' + escapeStr(str) + '\"']\n  }\n\n  var result = []\n  for (var i = 0; i < subparts.length; ++i) {\n    result = result.concat(splitParts(subparts[i]))\n  }\n  return result\n}\n\nfunction toAccessorString (str) {\n  return '[' + splitParts(str).join('][') + ']'\n}\n\nfunction defineDynamic (type, data) {\n  return new DynamicVariable(type, toAccessorString(data + ''))\n}\n\nfunction isDynamic (x) {\n  return (typeof x === 'function' && !x._reglType) || (x instanceof DynamicVariable)\n}\n\nfunction unbox (x, path) {\n  if (typeof x === 'function') {\n    return new DynamicVariable(DYN_FUNC, x)\n  } else if (typeof x === 'number' || typeof x === 'boolean') {\n    return new DynamicVariable(DYN_CONSTANT, x)\n  } else if (Array.isArray(x)) {\n    return new DynamicVariable(DYN_ARRAY, x.map((y, i) => unbox(y, path + '[' + i + ']')))\n  } else if (x instanceof DynamicVariable) {\n    return x\n  }\n  check$1(false, 'invalid option type in uniform ' + path)\n}\n\nvar dynamic = {\n  DynamicVariable: DynamicVariable,\n  define: defineDynamic,\n  isDynamic: isDynamic,\n  unbox: unbox,\n  accessor: toAccessorString\n};\n\n/* globals requestAnimationFrame, cancelAnimationFrame */\nvar raf = {\n  next: typeof requestAnimationFrame === 'function'\n    ? function (cb) { return requestAnimationFrame(cb) }\n    : function (cb) { return setTimeout(cb, 16) },\n  cancel: typeof cancelAnimationFrame === 'function'\n    ? function (raf) { return cancelAnimationFrame(raf) }\n    : clearTimeout\n};\n\n/* globals performance */\nvar clock = (typeof performance !== 'undefined' && performance.now)\n    ? function () { return performance.now() }\n    : function () { return +(new Date()) };\n\nfunction createStringStore () {\n  var stringIds = { '': 0 }\n  var stringValues = ['']\n  return {\n    id: function (str) {\n      var result = stringIds[str]\n      if (result) {\n        return result\n      }\n      result = stringIds[str] = stringValues.length\n      stringValues.push(str)\n      return result\n    },\n\n    str: function (id) {\n      return stringValues[id]\n    }\n  }\n}\n\n// Context and canvas creation helper functions\nfunction createCanvas (element, onDone, pixelRatio) {\n  var canvas = document.createElement('canvas')\n  extend(canvas.style, {\n    border: 0,\n    margin: 0,\n    padding: 0,\n    top: 0,\n    left: 0\n  })\n  element.appendChild(canvas)\n\n  if (element === document.body) {\n    canvas.style.position = 'absolute'\n    extend(element.style, {\n      margin: 0,\n      padding: 0\n    })\n  }\n\n  function resize () {\n    var w = window.innerWidth\n    var h = window.innerHeight\n    if (element !== document.body) {\n      var bounds = element.getBoundingClientRect()\n      w = bounds.right - bounds.left\n      h = bounds.bottom - bounds.top\n    }\n    canvas.width = pixelRatio * w\n    canvas.height = pixelRatio * h\n    extend(canvas.style, {\n      width: w + 'px',\n      height: h + 'px'\n    })\n  }\n\n  var resizeObserver\n  if (element !== document.body && typeof ResizeObserver === 'function') {\n    // ignore 'ResizeObserver' is not defined\n    // eslint-disable-next-line\n    resizeObserver = new ResizeObserver(function () {\n      // setTimeout to avoid flicker\n      setTimeout(resize)\n    })\n    resizeObserver.observe(element)\n  } else {\n    window.addEventListener('resize', resize, false)\n  }\n\n  function onDestroy () {\n    if (resizeObserver) {\n      resizeObserver.disconnect()\n    } else {\n      window.removeEventListener('resize', resize)\n    }\n    element.removeChild(canvas)\n  }\n\n  resize()\n\n  return {\n    canvas: canvas,\n    onDestroy: onDestroy\n  }\n}\n\nfunction createContext (canvas, contextAttributes) {\n  function get (name) {\n    try {\n      return canvas.getContext(name, contextAttributes)\n    } catch (e) {\n      return null\n    }\n  }\n  return (\n    get('webgl') ||\n    get('experimental-webgl') ||\n    get('webgl-experimental')\n  )\n}\n\nfunction isHTMLElement (obj) {\n  return (\n    typeof obj.nodeName === 'string' &&\n    typeof obj.appendChild === 'function' &&\n    typeof obj.getBoundingClientRect === 'function'\n  )\n}\n\nfunction isWebGLContext (obj) {\n  return (\n    typeof obj.drawArrays === 'function' ||\n    typeof obj.drawElements === 'function'\n  )\n}\n\nfunction parseExtensions (input) {\n  if (typeof input === 'string') {\n    return input.split()\n  }\n  check$1(Array.isArray(input), 'invalid extension array')\n  return input\n}\n\nfunction getElement (desc) {\n  if (typeof desc === 'string') {\n    check$1(typeof document !== 'undefined', 'not supported outside of DOM')\n    return document.querySelector(desc)\n  }\n  return desc\n}\n\nfunction parseArgs (args_) {\n  var args = args_ || {}\n  var element, container, canvas, gl\n  var contextAttributes = {}\n  var extensions = []\n  var optionalExtensions = []\n  var pixelRatio = (typeof window === 'undefined' ? 1 : window.devicePixelRatio)\n  var profile = false\n  var onDone = function (err) {\n    if (err) {\n      check$1.raise(err)\n    }\n  }\n  var onDestroy = function () {}\n  if (typeof args === 'string') {\n    check$1(\n      typeof document !== 'undefined',\n      'selector queries only supported in DOM enviroments')\n    element = document.querySelector(args)\n    check$1(element, 'invalid query string for element')\n  } else if (typeof args === 'object') {\n    if (isHTMLElement(args)) {\n      element = args\n    } else if (isWebGLContext(args)) {\n      gl = args\n      canvas = gl.canvas\n    } else {\n      check$1.constructor(args)\n      if ('gl' in args) {\n        gl = args.gl\n      } else if ('canvas' in args) {\n        canvas = getElement(args.canvas)\n      } else if ('container' in args) {\n        container = getElement(args.container)\n      }\n      if ('attributes' in args) {\n        contextAttributes = args.attributes\n        check$1.type(contextAttributes, 'object', 'invalid context attributes')\n      }\n      if ('extensions' in args) {\n        extensions = parseExtensions(args.extensions)\n      }\n      if ('optionalExtensions' in args) {\n        optionalExtensions = parseExtensions(args.optionalExtensions)\n      }\n      if ('onDone' in args) {\n        check$1.type(\n          args.onDone, 'function',\n          'invalid or missing onDone callback')\n        onDone = args.onDone\n      }\n      if ('profile' in args) {\n        profile = !!args.profile\n      }\n      if ('pixelRatio' in args) {\n        pixelRatio = +args.pixelRatio\n        check$1(pixelRatio > 0, 'invalid pixel ratio')\n      }\n    }\n  } else {\n    check$1.raise('invalid arguments to regl')\n  }\n\n  if (element) {\n    if (element.nodeName.toLowerCase() === 'canvas') {\n      canvas = element\n    } else {\n      container = element\n    }\n  }\n\n  if (!gl) {\n    if (!canvas) {\n      check$1(\n        typeof document !== 'undefined',\n        'must manually specify webgl context outside of DOM environments')\n      var result = createCanvas(container || document.body, onDone, pixelRatio)\n      if (!result) {\n        return null\n      }\n      canvas = result.canvas\n      onDestroy = result.onDestroy\n    }\n    // workaround for chromium bug, premultiplied alpha value is platform dependent\n    if (contextAttributes.premultipliedAlpha === undefined) contextAttributes.premultipliedAlpha = true\n    gl = createContext(canvas, contextAttributes)\n  }\n\n  if (!gl) {\n    onDestroy()\n    onDone('webgl not supported, try upgrading your browser or graphics drivers http://get.webgl.org')\n    return null\n  }\n\n  return {\n    gl: gl,\n    canvas: canvas,\n    container: container,\n    extensions: extensions,\n    optionalExtensions: optionalExtensions,\n    pixelRatio: pixelRatio,\n    profile: profile,\n    onDone: onDone,\n    onDestroy: onDestroy\n  }\n}\n\nfunction createExtensionCache (gl, config) {\n  var extensions = {}\n\n  function tryLoadExtension (name_) {\n    check$1.type(name_, 'string', 'extension name must be string')\n    var name = name_.toLowerCase()\n    var ext\n    try {\n      ext = extensions[name] = gl.getExtension(name)\n    } catch (e) {}\n    return !!ext\n  }\n\n  for (var i = 0; i < config.extensions.length; ++i) {\n    var name = config.extensions[i]\n    if (!tryLoadExtension(name)) {\n      config.onDestroy()\n      config.onDone('\"' + name + '\" extension is not supported by the current WebGL context, try upgrading your system or a different browser')\n      return null\n    }\n  }\n\n  config.optionalExtensions.forEach(tryLoadExtension)\n\n  return {\n    extensions: extensions,\n    restore: function () {\n      Object.keys(extensions).forEach(function (name) {\n        if (extensions[name] && !tryLoadExtension(name)) {\n          throw new Error('(regl): error restoring extension ' + name)\n        }\n      })\n    }\n  }\n}\n\nfunction loop (n, f) {\n  var result = Array(n)\n  for (var i = 0; i < n; ++i) {\n    result[i] = f(i)\n  }\n  return result\n}\n\nvar GL_BYTE$1 = 5120\nvar GL_UNSIGNED_BYTE$2 = 5121\nvar GL_SHORT$1 = 5122\nvar GL_UNSIGNED_SHORT$1 = 5123\nvar GL_INT$1 = 5124\nvar GL_UNSIGNED_INT$1 = 5125\nvar GL_FLOAT$2 = 5126\n\nfunction nextPow16 (v) {\n  for (var i = 16; i <= (1 << 28); i *= 16) {\n    if (v <= i) {\n      return i\n    }\n  }\n  return 0\n}\n\nfunction log2 (v) {\n  var r, shift\n  r = (v > 0xFFFF) << 4\n  v >>>= r\n  shift = (v > 0xFF) << 3\n  v >>>= shift; r |= shift\n  shift = (v > 0xF) << 2\n  v >>>= shift; r |= shift\n  shift = (v > 0x3) << 1\n  v >>>= shift; r |= shift\n  return r | (v >> 1)\n}\n\nfunction createPool () {\n  var bufferPool = loop(8, function () {\n    return []\n  })\n\n  function alloc (n) {\n    var sz = nextPow16(n)\n    var bin = bufferPool[log2(sz) >> 2]\n    if (bin.length > 0) {\n      return bin.pop()\n    }\n    return new ArrayBuffer(sz)\n  }\n\n  function free (buf) {\n    bufferPool[log2(buf.byteLength) >> 2].push(buf)\n  }\n\n  function allocType (type, n) {\n    var result = null\n    switch (type) {\n      case GL_BYTE$1:\n        result = new Int8Array(alloc(n), 0, n)\n        break\n      case GL_UNSIGNED_BYTE$2:\n        result = new Uint8Array(alloc(n), 0, n)\n        break\n      case GL_SHORT$1:\n        result = new Int16Array(alloc(2 * n), 0, n)\n        break\n      case GL_UNSIGNED_SHORT$1:\n        result = new Uint16Array(alloc(2 * n), 0, n)\n        break\n      case GL_INT$1:\n        result = new Int32Array(alloc(4 * n), 0, n)\n        break\n      case GL_UNSIGNED_INT$1:\n        result = new Uint32Array(alloc(4 * n), 0, n)\n        break\n      case GL_FLOAT$2:\n        result = new Float32Array(alloc(4 * n), 0, n)\n        break\n      default:\n        return null\n    }\n    if (result.length !== n) {\n      return result.subarray(0, n)\n    }\n    return result\n  }\n\n  function freeType (array) {\n    free(array.buffer)\n  }\n\n  return {\n    alloc: alloc,\n    free: free,\n    allocType: allocType,\n    freeType: freeType\n  }\n}\n\nvar pool = createPool()\n\n// zero pool for initial zero data\npool.zero = createPool()\n\nvar GL_SUBPIXEL_BITS = 0x0D50\nvar GL_RED_BITS = 0x0D52\nvar GL_GREEN_BITS = 0x0D53\nvar GL_BLUE_BITS = 0x0D54\nvar GL_ALPHA_BITS = 0x0D55\nvar GL_DEPTH_BITS = 0x0D56\nvar GL_STENCIL_BITS = 0x0D57\n\nvar GL_ALIASED_POINT_SIZE_RANGE = 0x846D\nvar GL_ALIASED_LINE_WIDTH_RANGE = 0x846E\n\nvar GL_MAX_TEXTURE_SIZE = 0x0D33\nvar GL_MAX_VIEWPORT_DIMS = 0x0D3A\nvar GL_MAX_VERTEX_ATTRIBS = 0x8869\nvar GL_MAX_VERTEX_UNIFORM_VECTORS = 0x8DFB\nvar GL_MAX_VARYING_VECTORS = 0x8DFC\nvar GL_MAX_COMBINED_TEXTURE_IMAGE_UNITS = 0x8B4D\nvar GL_MAX_VERTEX_TEXTURE_IMAGE_UNITS = 0x8B4C\nvar GL_MAX_TEXTURE_IMAGE_UNITS = 0x8872\nvar GL_MAX_FRAGMENT_UNIFORM_VECTORS = 0x8DFD\nvar GL_MAX_CUBE_MAP_TEXTURE_SIZE = 0x851C\nvar GL_MAX_RENDERBUFFER_SIZE = 0x84E8\n\nvar GL_VENDOR = 0x1F00\nvar GL_RENDERER = 0x1F01\nvar GL_VERSION = 0x1F02\nvar GL_SHADING_LANGUAGE_VERSION = 0x8B8C\n\nvar GL_MAX_TEXTURE_MAX_ANISOTROPY_EXT = 0x84FF\n\nvar GL_MAX_COLOR_ATTACHMENTS_WEBGL = 0x8CDF\nvar GL_MAX_DRAW_BUFFERS_WEBGL = 0x8824\n\nvar GL_TEXTURE_2D = 0x0DE1\nvar GL_TEXTURE_CUBE_MAP = 0x8513\nvar GL_TEXTURE_CUBE_MAP_POSITIVE_X = 0x8515\nvar GL_TEXTURE0 = 0x84C0\nvar GL_RGBA = 0x1908\nvar GL_FLOAT$1 = 0x1406\nvar GL_UNSIGNED_BYTE$1 = 0x1401\nvar GL_FRAMEBUFFER = 0x8D40\nvar GL_FRAMEBUFFER_COMPLETE = 0x8CD5\nvar GL_COLOR_ATTACHMENT0 = 0x8CE0\nvar GL_COLOR_BUFFER_BIT$1 = 0x4000\n\nvar wrapLimits = function (gl, extensions) {\n  var maxAnisotropic = 1\n  if (extensions.ext_texture_filter_anisotropic) {\n    maxAnisotropic = gl.getParameter(GL_MAX_TEXTURE_MAX_ANISOTROPY_EXT)\n  }\n\n  var maxDrawbuffers = 1\n  var maxColorAttachments = 1\n  if (extensions.webgl_draw_buffers) {\n    maxDrawbuffers = gl.getParameter(GL_MAX_DRAW_BUFFERS_WEBGL)\n    maxColorAttachments = gl.getParameter(GL_MAX_COLOR_ATTACHMENTS_WEBGL)\n  }\n\n  // detect if reading float textures is available (Safari doesn't support)\n  var readFloat = !!extensions.oes_texture_float\n  if (readFloat) {\n    var readFloatTexture = gl.createTexture()\n    gl.bindTexture(GL_TEXTURE_2D, readFloatTexture)\n    gl.texImage2D(GL_TEXTURE_2D, 0, GL_RGBA, 1, 1, 0, GL_RGBA, GL_FLOAT$1, null)\n\n    var fbo = gl.createFramebuffer()\n    gl.bindFramebuffer(GL_FRAMEBUFFER, fbo)\n    gl.framebufferTexture2D(GL_FRAMEBUFFER, GL_COLOR_ATTACHMENT0, GL_TEXTURE_2D, readFloatTexture, 0)\n    gl.bindTexture(GL_TEXTURE_2D, null)\n\n    if (gl.checkFramebufferStatus(GL_FRAMEBUFFER) !== GL_FRAMEBUFFER_COMPLETE) readFloat = false\n\n    else {\n      gl.viewport(0, 0, 1, 1)\n      gl.clearColor(1.0, 0.0, 0.0, 1.0)\n      gl.clear(GL_COLOR_BUFFER_BIT$1)\n      var pixels = pool.allocType(GL_FLOAT$1, 4)\n      gl.readPixels(0, 0, 1, 1, GL_RGBA, GL_FLOAT$1, pixels)\n\n      if (gl.getError()) readFloat = false\n      else {\n        gl.deleteFramebuffer(fbo)\n        gl.deleteTexture(readFloatTexture)\n\n        readFloat = pixels[0] === 1.0\n      }\n\n      pool.freeType(pixels)\n    }\n  }\n\n  // detect non power of two cube textures support (IE doesn't support)\n  var isIE = typeof navigator !== 'undefined' && (/MSIE/.test(navigator.userAgent) || /Trident\\//.test(navigator.appVersion) || /Edge/.test(navigator.userAgent))\n\n  var npotTextureCube = true\n\n  if (!isIE) {\n    var cubeTexture = gl.createTexture()\n    var data = pool.allocType(GL_UNSIGNED_BYTE$1, 36)\n    gl.activeTexture(GL_TEXTURE0)\n    gl.bindTexture(GL_TEXTURE_CUBE_MAP, cubeTexture)\n    gl.texImage2D(GL_TEXTURE_CUBE_MAP_POSITIVE_X, 0, GL_RGBA, 3, 3, 0, GL_RGBA, GL_UNSIGNED_BYTE$1, data)\n    pool.freeType(data)\n    gl.bindTexture(GL_TEXTURE_CUBE_MAP, null)\n    gl.deleteTexture(cubeTexture)\n    npotTextureCube = !gl.getError()\n  }\n\n  return {\n    // drawing buffer bit depth\n    colorBits: [\n      gl.getParameter(GL_RED_BITS),\n      gl.getParameter(GL_GREEN_BITS),\n      gl.getParameter(GL_BLUE_BITS),\n      gl.getParameter(GL_ALPHA_BITS)\n    ],\n    depthBits: gl.getParameter(GL_DEPTH_BITS),\n    stencilBits: gl.getParameter(GL_STENCIL_BITS),\n    subpixelBits: gl.getParameter(GL_SUBPIXEL_BITS),\n\n    // supported extensions\n    extensions: Object.keys(extensions).filter(function (ext) {\n      return !!extensions[ext]\n    }),\n\n    // max aniso samples\n    maxAnisotropic: maxAnisotropic,\n\n    // max draw buffers\n    maxDrawbuffers: maxDrawbuffers,\n    maxColorAttachments: maxColorAttachments,\n\n    // point and line size ranges\n    pointSizeDims: gl.getParameter(GL_ALIASED_POINT_SIZE_RANGE),\n    lineWidthDims: gl.getParameter(GL_ALIASED_LINE_WIDTH_RANGE),\n    maxViewportDims: gl.getParameter(GL_MAX_VIEWPORT_DIMS),\n    maxCombinedTextureUnits: gl.getParameter(GL_MAX_COMBINED_TEXTURE_IMAGE_UNITS),\n    maxCubeMapSize: gl.getParameter(GL_MAX_CUBE_MAP_TEXTURE_SIZE),\n    maxRenderbufferSize: gl.getParameter(GL_MAX_RENDERBUFFER_SIZE),\n    maxTextureUnits: gl.getParameter(GL_MAX_TEXTURE_IMAGE_UNITS),\n    maxTextureSize: gl.getParameter(GL_MAX_TEXTURE_SIZE),\n    maxAttributes: gl.getParameter(GL_MAX_VERTEX_ATTRIBS),\n    maxVertexUniforms: gl.getParameter(GL_MAX_VERTEX_UNIFORM_VECTORS),\n    maxVertexTextureUnits: gl.getParameter(GL_MAX_VERTEX_TEXTURE_IMAGE_UNITS),\n    maxVaryingVectors: gl.getParameter(GL_MAX_VARYING_VECTORS),\n    maxFragmentUniforms: gl.getParameter(GL_MAX_FRAGMENT_UNIFORM_VECTORS),\n\n    // vendor info\n    glsl: gl.getParameter(GL_SHADING_LANGUAGE_VERSION),\n    renderer: gl.getParameter(GL_RENDERER),\n    vendor: gl.getParameter(GL_VENDOR),\n    version: gl.getParameter(GL_VERSION),\n\n    // quirks\n    readFloat: readFloat,\n    npotTextureCube: npotTextureCube\n  }\n}\n\nfunction isNDArrayLike (obj) {\n  return (\n    !!obj &&\n    typeof obj === 'object' &&\n    Array.isArray(obj.shape) &&\n    Array.isArray(obj.stride) &&\n    typeof obj.offset === 'number' &&\n    obj.shape.length === obj.stride.length &&\n    (Array.isArray(obj.data) ||\n      isTypedArray(obj.data)))\n}\n\nvar values = function (obj) {\n  return Object.keys(obj).map(function (key) { return obj[key] })\n}\n\nvar flattenUtils = {\n  shape: arrayShape$1,\n  flatten: flattenArray\n};\n\nfunction flatten1D (array, nx, out) {\n  for (var i = 0; i < nx; ++i) {\n    out[i] = array[i]\n  }\n}\n\nfunction flatten2D (array, nx, ny, out) {\n  var ptr = 0\n  for (var i = 0; i < nx; ++i) {\n    var row = array[i]\n    for (var j = 0; j < ny; ++j) {\n      out[ptr++] = row[j]\n    }\n  }\n}\n\nfunction flatten3D (array, nx, ny, nz, out, ptr_) {\n  var ptr = ptr_\n  for (var i = 0; i < nx; ++i) {\n    var row = array[i]\n    for (var j = 0; j < ny; ++j) {\n      var col = row[j]\n      for (var k = 0; k < nz; ++k) {\n        out[ptr++] = col[k]\n      }\n    }\n  }\n}\n\nfunction flattenRec (array, shape, level, out, ptr) {\n  var stride = 1\n  for (var i = level + 1; i < shape.length; ++i) {\n    stride *= shape[i]\n  }\n  var n = shape[level]\n  if (shape.length - level === 4) {\n    var nx = shape[level + 1]\n    var ny = shape[level + 2]\n    var nz = shape[level + 3]\n    for (i = 0; i < n; ++i) {\n      flatten3D(array[i], nx, ny, nz, out, ptr)\n      ptr += stride\n    }\n  } else {\n    for (i = 0; i < n; ++i) {\n      flattenRec(array[i], shape, level + 1, out, ptr)\n      ptr += stride\n    }\n  }\n}\n\nfunction flattenArray (array, shape, type, out_) {\n  var sz = 1\n  if (shape.length) {\n    for (var i = 0; i < shape.length; ++i) {\n      sz *= shape[i]\n    }\n  } else {\n    sz = 0\n  }\n  var out = out_ || pool.allocType(type, sz)\n  switch (shape.length) {\n    case 0:\n      break\n    case 1:\n      flatten1D(array, shape[0], out)\n      break\n    case 2:\n      flatten2D(array, shape[0], shape[1], out)\n      break\n    case 3:\n      flatten3D(array, shape[0], shape[1], shape[2], out, 0)\n      break\n    default:\n      flattenRec(array, shape, 0, out, 0)\n  }\n  return out\n}\n\nfunction arrayShape$1 (array_) {\n  var shape = []\n  for (var array = array_; array.length; array = array[0]) {\n    shape.push(array.length)\n  }\n  return shape\n}\n\nvar arrayTypes =  {\n\t\"[object Int8Array]\": 5120,\n\t\"[object Int16Array]\": 5122,\n\t\"[object Int32Array]\": 5124,\n\t\"[object Uint8Array]\": 5121,\n\t\"[object Uint8ClampedArray]\": 5121,\n\t\"[object Uint16Array]\": 5123,\n\t\"[object Uint32Array]\": 5125,\n\t\"[object Float32Array]\": 5126,\n\t\"[object Float64Array]\": 5121,\n\t\"[object ArrayBuffer]\": 5121\n};\n\nvar int8 = 5120;\nvar int16 = 5122;\nvar int32 = 5124;\nvar uint8 = 5121;\nvar uint16 = 5123;\nvar uint32 = 5125;\nvar float = 5126;\nvar float32 = 5126;\nvar glTypes = {\n\tint8: int8,\n\tint16: int16,\n\tint32: int32,\n\tuint8: uint8,\n\tuint16: uint16,\n\tuint32: uint32,\n\tfloat: float,\n\tfloat32: float32\n};\n\nvar dynamic$1 = 35048;\nvar stream = 35040;\nvar usageTypes = {\n\tdynamic: dynamic$1,\n\tstream: stream,\n\t\"static\": 35044\n};\n\nvar arrayFlatten = flattenUtils.flatten\nvar arrayShape = flattenUtils.shape\n\nvar GL_STATIC_DRAW = 0x88E4\nvar GL_STREAM_DRAW = 0x88E0\n\nvar GL_UNSIGNED_BYTE$3 = 5121\nvar GL_FLOAT$3 = 5126\n\nvar DTYPES_SIZES = []\nDTYPES_SIZES[5120] = 1 // int8\nDTYPES_SIZES[5122] = 2 // int16\nDTYPES_SIZES[5124] = 4 // int32\nDTYPES_SIZES[5121] = 1 // uint8\nDTYPES_SIZES[5123] = 2 // uint16\nDTYPES_SIZES[5125] = 4 // uint32\nDTYPES_SIZES[5126] = 4 // float32\n\nfunction typedArrayCode (data) {\n  return arrayTypes[Object.prototype.toString.call(data)] | 0\n}\n\nfunction copyArray (out, inp) {\n  for (var i = 0; i < inp.length; ++i) {\n    out[i] = inp[i]\n  }\n}\n\nfunction transpose (\n  result, data, shapeX, shapeY, strideX, strideY, offset) {\n  var ptr = 0\n  for (var i = 0; i < shapeX; ++i) {\n    for (var j = 0; j < shapeY; ++j) {\n      result[ptr++] = data[strideX * i + strideY * j + offset]\n    }\n  }\n}\n\nfunction wrapBufferState (gl, stats, config, destroyBuffer) {\n  var bufferCount = 0\n  var bufferSet = {}\n\n  function REGLBuffer (type) {\n    this.id = bufferCount++\n    this.buffer = gl.createBuffer()\n    this.type = type\n    this.usage = GL_STATIC_DRAW\n    this.byteLength = 0\n    this.dimension = 1\n    this.dtype = GL_UNSIGNED_BYTE$3\n\n    this.persistentData = null\n\n    if (config.profile) {\n      this.stats = { size: 0 }\n    }\n  }\n\n  REGLBuffer.prototype.bind = function () {\n    gl.bindBuffer(this.type, this.buffer)\n  }\n\n  REGLBuffer.prototype.destroy = function () {\n    destroy(this)\n  }\n\n  var streamPool = []\n\n  function createStream (type, data) {\n    var buffer = streamPool.pop()\n    if (!buffer) {\n      buffer = new REGLBuffer(type)\n    }\n    buffer.bind()\n    initBufferFromData(buffer, data, GL_STREAM_DRAW, 0, 1, false)\n    return buffer\n  }\n\n  function destroyStream (stream$$1) {\n    streamPool.push(stream$$1)\n  }\n\n  function initBufferFromTypedArray (buffer, data, usage) {\n    buffer.byteLength = data.byteLength\n    gl.bufferData(buffer.type, data, usage)\n  }\n\n  function initBufferFromData (buffer, data, usage, dtype, dimension, persist) {\n    var shape\n    buffer.usage = usage\n    if (Array.isArray(data)) {\n      buffer.dtype = dtype || GL_FLOAT$3\n      if (data.length > 0) {\n        var flatData\n        if (Array.isArray(data[0])) {\n          shape = arrayShape(data)\n          var dim = 1\n          for (var i = 1; i < shape.length; ++i) {\n            dim *= shape[i]\n          }\n          buffer.dimension = dim\n          flatData = arrayFlatten(data, shape, buffer.dtype)\n          initBufferFromTypedArray(buffer, flatData, usage)\n          if (persist) {\n            buffer.persistentData = flatData\n          } else {\n            pool.freeType(flatData)\n          }\n        } else if (typeof data[0] === 'number') {\n          buffer.dimension = dimension\n          var typedData = pool.allocType(buffer.dtype, data.length)\n          copyArray(typedData, data)\n          initBufferFromTypedArray(buffer, typedData, usage)\n          if (persist) {\n            buffer.persistentData = typedData\n          } else {\n            pool.freeType(typedData)\n          }\n        } else if (isTypedArray(data[0])) {\n          buffer.dimension = data[0].length\n          buffer.dtype = dtype || typedArrayCode(data[0]) || GL_FLOAT$3\n          flatData = arrayFlatten(\n            data,\n            [data.length, data[0].length],\n            buffer.dtype)\n          initBufferFromTypedArray(buffer, flatData, usage)\n          if (persist) {\n            buffer.persistentData = flatData\n          } else {\n            pool.freeType(flatData)\n          }\n        } else {\n          check$1.raise('invalid buffer data')\n        }\n      }\n    } else if (isTypedArray(data)) {\n      buffer.dtype = dtype || typedArrayCode(data)\n      buffer.dimension = dimension\n      initBufferFromTypedArray(buffer, data, usage)\n      if (persist) {\n        buffer.persistentData = new Uint8Array(new Uint8Array(data.buffer))\n      }\n    } else if (isNDArrayLike(data)) {\n      shape = data.shape\n      var stride = data.stride\n      var offset = data.offset\n\n      var shapeX = 0\n      var shapeY = 0\n      var strideX = 0\n      var strideY = 0\n      if (shape.length === 1) {\n        shapeX = shape[0]\n        shapeY = 1\n        strideX = stride[0]\n        strideY = 0\n      } else if (shape.length === 2) {\n        shapeX = shape[0]\n        shapeY = shape[1]\n        strideX = stride[0]\n        strideY = stride[1]\n      } else {\n        check$1.raise('invalid shape')\n      }\n\n      buffer.dtype = dtype || typedArrayCode(data.data) || GL_FLOAT$3\n      buffer.dimension = shapeY\n\n      var transposeData = pool.allocType(buffer.dtype, shapeX * shapeY)\n      transpose(transposeData,\n        data.data,\n        shapeX, shapeY,\n        strideX, strideY,\n        offset)\n      initBufferFromTypedArray(buffer, transposeData, usage)\n      if (persist) {\n        buffer.persistentData = transposeData\n      } else {\n        pool.freeType(transposeData)\n      }\n    } else if (data instanceof ArrayBuffer) {\n      buffer.dtype = GL_UNSIGNED_BYTE$3\n      buffer.dimension = dimension\n      initBufferFromTypedArray(buffer, data, usage)\n      if (persist) {\n        buffer.persistentData = new Uint8Array(new Uint8Array(data))\n      }\n    } else {\n      check$1.raise('invalid buffer data')\n    }\n  }\n\n  function destroy (buffer) {\n    stats.bufferCount--\n\n    // remove attribute link\n    destroyBuffer(buffer)\n\n    var handle = buffer.buffer\n    check$1(handle, 'buffer must not be deleted already')\n    gl.deleteBuffer(handle)\n    buffer.buffer = null\n    delete bufferSet[buffer.id]\n  }\n\n  function createBuffer (options, type, deferInit, persistent) {\n    stats.bufferCount++\n\n    var buffer = new REGLBuffer(type)\n    bufferSet[buffer.id] = buffer\n\n    function reglBuffer (options) {\n      var usage = GL_STATIC_DRAW\n      var data = null\n      var byteLength = 0\n      var dtype = 0\n      var dimension = 1\n      if (Array.isArray(options) ||\n          isTypedArray(options) ||\n          isNDArrayLike(options) ||\n          options instanceof ArrayBuffer) {\n        data = options\n      } else if (typeof options === 'number') {\n        byteLength = options | 0\n      } else if (options) {\n        check$1.type(\n          options, 'object',\n          'buffer arguments must be an object, a number or an array')\n\n        if ('data' in options) {\n          check$1(\n            data === null ||\n            Array.isArray(data) ||\n            isTypedArray(data) ||\n            isNDArrayLike(data),\n            'invalid data for buffer')\n          data = options.data\n        }\n\n        if ('usage' in options) {\n          check$1.parameter(options.usage, usageTypes, 'invalid buffer usage')\n          usage = usageTypes[options.usage]\n        }\n\n        if ('type' in options) {\n          check$1.parameter(options.type, glTypes, 'invalid buffer type')\n          dtype = glTypes[options.type]\n        }\n\n        if ('dimension' in options) {\n          check$1.type(options.dimension, 'number', 'invalid dimension')\n          dimension = options.dimension | 0\n        }\n\n        if ('length' in options) {\n          check$1.nni(byteLength, 'buffer length must be a nonnegative integer')\n          byteLength = options.length | 0\n        }\n      }\n\n      buffer.bind()\n      if (!data) {\n        // #475\n        if (byteLength) gl.bufferData(buffer.type, byteLength, usage)\n        buffer.dtype = dtype || GL_UNSIGNED_BYTE$3\n        buffer.usage = usage\n        buffer.dimension = dimension\n        buffer.byteLength = byteLength\n      } else {\n        initBufferFromData(buffer, data, usage, dtype, dimension, persistent)\n      }\n\n      if (config.profile) {\n        buffer.stats.size = buffer.byteLength * DTYPES_SIZES[buffer.dtype]\n      }\n\n      return reglBuffer\n    }\n\n    function setSubData (data, offset) {\n      check$1(offset + data.byteLength <= buffer.byteLength,\n        'invalid buffer subdata call, buffer is too small. ' + ' Can\\'t write data of size ' + data.byteLength + ' starting from offset ' + offset + ' to a buffer of size ' + buffer.byteLength)\n\n      gl.bufferSubData(buffer.type, offset, data)\n    }\n\n    function subdata (data, offset_) {\n      var offset = (offset_ || 0) | 0\n      var shape\n      buffer.bind()\n      if (isTypedArray(data) || data instanceof ArrayBuffer) {\n        setSubData(data, offset)\n      } else if (Array.isArray(data)) {\n        if (data.length > 0) {\n          if (typeof data[0] === 'number') {\n            var converted = pool.allocType(buffer.dtype, data.length)\n            copyArray(converted, data)\n            setSubData(converted, offset)\n            pool.freeType(converted)\n          } else if (Array.isArray(data[0]) || isTypedArray(data[0])) {\n            shape = arrayShape(data)\n            var flatData = arrayFlatten(data, shape, buffer.dtype)\n            setSubData(flatData, offset)\n            pool.freeType(flatData)\n          } else {\n            check$1.raise('invalid buffer data')\n          }\n        }\n      } else if (isNDArrayLike(data)) {\n        shape = data.shape\n        var stride = data.stride\n\n        var shapeX = 0\n        var shapeY = 0\n        var strideX = 0\n        var strideY = 0\n        if (shape.length === 1) {\n          shapeX = shape[0]\n          shapeY = 1\n          strideX = stride[0]\n          strideY = 0\n        } else if (shape.length === 2) {\n          shapeX = shape[0]\n          shapeY = shape[1]\n          strideX = stride[0]\n          strideY = stride[1]\n        } else {\n          check$1.raise('invalid shape')\n        }\n        var dtype = Array.isArray(data.data)\n          ? buffer.dtype\n          : typedArrayCode(data.data)\n\n        var transposeData = pool.allocType(dtype, shapeX * shapeY)\n        transpose(transposeData,\n          data.data,\n          shapeX, shapeY,\n          strideX, strideY,\n          data.offset)\n        setSubData(transposeData, offset)\n        pool.freeType(transposeData)\n      } else {\n        check$1.raise('invalid data for buffer subdata')\n      }\n      return reglBuffer\n    }\n\n    if (!deferInit) {\n      reglBuffer(options)\n    }\n\n    reglBuffer._reglType = 'buffer'\n    reglBuffer._buffer = buffer\n    reglBuffer.subdata = subdata\n    if (config.profile) {\n      reglBuffer.stats = buffer.stats\n    }\n    reglBuffer.destroy = function () { destroy(buffer) }\n\n    return reglBuffer\n  }\n\n  function restoreBuffers () {\n    values(bufferSet).forEach(function (buffer) {\n      buffer.buffer = gl.createBuffer()\n      gl.bindBuffer(buffer.type, buffer.buffer)\n      gl.bufferData(\n        buffer.type, buffer.persistentData || buffer.byteLength, buffer.usage)\n    })\n  }\n\n  if (config.profile) {\n    stats.getTotalBufferSize = function () {\n      var total = 0\n      // TODO: Right now, the streams are not part of the total count.\n      Object.keys(bufferSet).forEach(function (key) {\n        total += bufferSet[key].stats.size\n      })\n      return total\n    }\n  }\n\n  return {\n    create: createBuffer,\n\n    createStream: createStream,\n    destroyStream: destroyStream,\n\n    clear: function () {\n      values(bufferSet).forEach(destroy)\n      streamPool.forEach(destroy)\n    },\n\n    getBuffer: function (wrapper) {\n      if (wrapper && wrapper._buffer instanceof REGLBuffer) {\n        return wrapper._buffer\n      }\n      return null\n    },\n\n    restore: restoreBuffers,\n\n    _initBuffer: initBufferFromData\n  }\n}\n\nvar points = 0;\nvar point = 0;\nvar lines = 1;\nvar line = 1;\nvar triangles = 4;\nvar triangle = 4;\nvar primTypes = {\n\tpoints: points,\n\tpoint: point,\n\tlines: lines,\n\tline: line,\n\ttriangles: triangles,\n\ttriangle: triangle,\n\t\"line loop\": 2,\n\t\"line strip\": 3,\n\t\"triangle strip\": 5,\n\t\"triangle fan\": 6\n};\n\nvar GL_POINTS = 0\nvar GL_LINES = 1\nvar GL_TRIANGLES = 4\n\nvar GL_BYTE$2 = 5120\nvar GL_UNSIGNED_BYTE$4 = 5121\nvar GL_SHORT$2 = 5122\nvar GL_UNSIGNED_SHORT$2 = 5123\nvar GL_INT$2 = 5124\nvar GL_UNSIGNED_INT$2 = 5125\n\nvar GL_ELEMENT_ARRAY_BUFFER = 34963\n\nvar GL_STREAM_DRAW$1 = 0x88E0\nvar GL_STATIC_DRAW$1 = 0x88E4\n\nfunction wrapElementsState (gl, extensions, bufferState, stats) {\n  var elementSet = {}\n  var elementCount = 0\n\n  var elementTypes = {\n    'uint8': GL_UNSIGNED_BYTE$4,\n    'uint16': GL_UNSIGNED_SHORT$2\n  }\n\n  if (extensions.oes_element_index_uint) {\n    elementTypes.uint32 = GL_UNSIGNED_INT$2\n  }\n\n  function REGLElementBuffer (buffer) {\n    this.id = elementCount++\n    elementSet[this.id] = this\n    this.buffer = buffer\n    this.primType = GL_TRIANGLES\n    this.vertCount = 0\n    this.type = 0\n  }\n\n  REGLElementBuffer.prototype.bind = function () {\n    this.buffer.bind()\n  }\n\n  var bufferPool = []\n\n  function createElementStream (data) {\n    var result = bufferPool.pop()\n    if (!result) {\n      result = new REGLElementBuffer(bufferState.create(\n        null,\n        GL_ELEMENT_ARRAY_BUFFER,\n        true,\n        false)._buffer)\n    }\n    initElements(result, data, GL_STREAM_DRAW$1, -1, -1, 0, 0)\n    return result\n  }\n\n  function destroyElementStream (elements) {\n    bufferPool.push(elements)\n  }\n\n  function initElements (\n    elements,\n    data,\n    usage,\n    prim,\n    count,\n    byteLength,\n    type) {\n    elements.buffer.bind()\n    var dtype\n    if (data) {\n      var predictedType = type\n      if (!type && (\n        !isTypedArray(data) ||\n         (isNDArrayLike(data) && !isTypedArray(data.data)))) {\n        predictedType = extensions.oes_element_index_uint\n          ? GL_UNSIGNED_INT$2\n          : GL_UNSIGNED_SHORT$2\n      }\n      bufferState._initBuffer(\n        elements.buffer,\n        data,\n        usage,\n        predictedType,\n        3)\n    } else {\n      gl.bufferData(GL_ELEMENT_ARRAY_BUFFER, byteLength, usage)\n      elements.buffer.dtype = dtype || GL_UNSIGNED_BYTE$4\n      elements.buffer.usage = usage\n      elements.buffer.dimension = 3\n      elements.buffer.byteLength = byteLength\n    }\n\n    dtype = type\n    if (!type) {\n      switch (elements.buffer.dtype) {\n        case GL_UNSIGNED_BYTE$4:\n        case GL_BYTE$2:\n          dtype = GL_UNSIGNED_BYTE$4\n          break\n\n        case GL_UNSIGNED_SHORT$2:\n        case GL_SHORT$2:\n          dtype = GL_UNSIGNED_SHORT$2\n          break\n\n        case GL_UNSIGNED_INT$2:\n        case GL_INT$2:\n          dtype = GL_UNSIGNED_INT$2\n          break\n\n        default:\n          check$1.raise('unsupported type for element array')\n      }\n      elements.buffer.dtype = dtype\n    }\n    elements.type = dtype\n\n    // Check oes_element_index_uint extension\n    check$1(\n      dtype !== GL_UNSIGNED_INT$2 ||\n      !!extensions.oes_element_index_uint,\n      '32 bit element buffers not supported, enable oes_element_index_uint first')\n\n    // try to guess default primitive type and arguments\n    var vertCount = count\n    if (vertCount < 0) {\n      vertCount = elements.buffer.byteLength\n      if (dtype === GL_UNSIGNED_SHORT$2) {\n        vertCount >>= 1\n      } else if (dtype === GL_UNSIGNED_INT$2) {\n        vertCount >>= 2\n      }\n    }\n    elements.vertCount = vertCount\n\n    // try to guess primitive type from cell dimension\n    var primType = prim\n    if (prim < 0) {\n      primType = GL_TRIANGLES\n      var dimension = elements.buffer.dimension\n      if (dimension === 1) primType = GL_POINTS\n      if (dimension === 2) primType = GL_LINES\n      if (dimension === 3) primType = GL_TRIANGLES\n    }\n    elements.primType = primType\n  }\n\n  function destroyElements (elements) {\n    stats.elementsCount--\n\n    check$1(elements.buffer !== null, 'must not double destroy elements')\n    delete elementSet[elements.id]\n    elements.buffer.destroy()\n    elements.buffer = null\n  }\n\n  function createElements (options, persistent) {\n    var buffer = bufferState.create(null, GL_ELEMENT_ARRAY_BUFFER, true)\n    var elements = new REGLElementBuffer(buffer._buffer)\n    stats.elementsCount++\n\n    function reglElements (options) {\n      if (!options) {\n        buffer()\n        elements.primType = GL_TRIANGLES\n        elements.vertCount = 0\n        elements.type = GL_UNSIGNED_BYTE$4\n      } else if (typeof options === 'number') {\n        buffer(options)\n        elements.primType = GL_TRIANGLES\n        elements.vertCount = options | 0\n        elements.type = GL_UNSIGNED_BYTE$4\n      } else {\n        var data = null\n        var usage = GL_STATIC_DRAW$1\n        var primType = -1\n        var vertCount = -1\n        var byteLength = 0\n        var dtype = 0\n        if (Array.isArray(options) ||\n            isTypedArray(options) ||\n            isNDArrayLike(options)) {\n          data = options\n        } else {\n          check$1.type(options, 'object', 'invalid arguments for elements')\n          if ('data' in options) {\n            data = options.data\n            check$1(\n              Array.isArray(data) ||\n                isTypedArray(data) ||\n                isNDArrayLike(data),\n              'invalid data for element buffer')\n          }\n          if ('usage' in options) {\n            check$1.parameter(\n              options.usage,\n              usageTypes,\n              'invalid element buffer usage')\n            usage = usageTypes[options.usage]\n          }\n          if ('primitive' in options) {\n            check$1.parameter(\n              options.primitive,\n              primTypes,\n              'invalid element buffer primitive')\n            primType = primTypes[options.primitive]\n          }\n          if ('count' in options) {\n            check$1(\n              typeof options.count === 'number' && options.count >= 0,\n              'invalid vertex count for elements')\n            vertCount = options.count | 0\n          }\n          if ('type' in options) {\n            check$1.parameter(\n              options.type,\n              elementTypes,\n              'invalid buffer type')\n            dtype = elementTypes[options.type]\n          }\n          if ('length' in options) {\n            byteLength = options.length | 0\n          } else {\n            byteLength = vertCount\n            if (dtype === GL_UNSIGNED_SHORT$2 || dtype === GL_SHORT$2) {\n              byteLength *= 2\n            } else if (dtype === GL_UNSIGNED_INT$2 || dtype === GL_INT$2) {\n              byteLength *= 4\n            }\n          }\n        }\n        initElements(\n          elements,\n          data,\n          usage,\n          primType,\n          vertCount,\n          byteLength,\n          dtype)\n      }\n\n      return reglElements\n    }\n\n    reglElements(options)\n\n    reglElements._reglType = 'elements'\n    reglElements._elements = elements\n    reglElements.subdata = function (data, offset) {\n      buffer.subdata(data, offset)\n      return reglElements\n    }\n    reglElements.destroy = function () {\n      destroyElements(elements)\n    }\n\n    return reglElements\n  }\n\n  return {\n    create: createElements,\n    createStream: createElementStream,\n    destroyStream: destroyElementStream,\n    getElements: function (elements) {\n      if (typeof elements === 'function' &&\n          elements._elements instanceof REGLElementBuffer) {\n        return elements._elements\n      }\n      return null\n    },\n    clear: function () {\n      values(elementSet).forEach(destroyElements)\n    }\n  }\n}\n\nvar FLOAT = new Float32Array(1)\nvar INT = new Uint32Array(FLOAT.buffer)\n\nvar GL_UNSIGNED_SHORT$4 = 5123\n\nfunction convertToHalfFloat (array) {\n  var ushorts = pool.allocType(GL_UNSIGNED_SHORT$4, array.length)\n\n  for (var i = 0; i < array.length; ++i) {\n    if (isNaN(array[i])) {\n      ushorts[i] = 0xffff\n    } else if (array[i] === Infinity) {\n      ushorts[i] = 0x7c00\n    } else if (array[i] === -Infinity) {\n      ushorts[i] = 0xfc00\n    } else {\n      FLOAT[0] = array[i]\n      var x = INT[0]\n\n      var sgn = (x >>> 31) << 15\n      var exp = ((x << 1) >>> 24) - 127\n      var frac = (x >> 13) & ((1 << 10) - 1)\n\n      if (exp < -24) {\n        // round non-representable denormals to 0\n        ushorts[i] = sgn\n      } else if (exp < -14) {\n        // handle denormals\n        var s = -14 - exp\n        ushorts[i] = sgn + ((frac + (1 << 10)) >> s)\n      } else if (exp > 15) {\n        // round overflow to +/- Infinity\n        ushorts[i] = sgn + 0x7c00\n      } else {\n        // otherwise convert directly\n        ushorts[i] = sgn + ((exp + 15) << 10) + frac\n      }\n    }\n  }\n\n  return ushorts\n}\n\nfunction isArrayLike (s) {\n  return Array.isArray(s) || isTypedArray(s)\n}\n\nvar isPow2$1 = function (v) {\n  return !(v & (v - 1)) && (!!v)\n}\n\nvar GL_COMPRESSED_TEXTURE_FORMATS = 0x86A3\n\nvar GL_TEXTURE_2D$1 = 0x0DE1\nvar GL_TEXTURE_CUBE_MAP$1 = 0x8513\nvar GL_TEXTURE_CUBE_MAP_POSITIVE_X$1 = 0x8515\n\nvar GL_RGBA$1 = 0x1908\nvar GL_ALPHA = 0x1906\nvar GL_RGB = 0x1907\nvar GL_LUMINANCE = 0x1909\nvar GL_LUMINANCE_ALPHA = 0x190A\n\nvar GL_RGBA4 = 0x8056\nvar GL_RGB5_A1 = 0x8057\nvar GL_RGB565 = 0x8D62\n\nvar GL_UNSIGNED_SHORT_4_4_4_4$1 = 0x8033\nvar GL_UNSIGNED_SHORT_5_5_5_1$1 = 0x8034\nvar GL_UNSIGNED_SHORT_5_6_5$1 = 0x8363\nvar GL_UNSIGNED_INT_24_8_WEBGL$1 = 0x84FA\n\nvar GL_DEPTH_COMPONENT = 0x1902\nvar GL_DEPTH_STENCIL = 0x84F9\n\nvar GL_SRGB_EXT = 0x8C40\nvar GL_SRGB_ALPHA_EXT = 0x8C42\n\nvar GL_HALF_FLOAT_OES$1 = 0x8D61\n\nvar GL_COMPRESSED_RGB_S3TC_DXT1_EXT = 0x83F0\nvar GL_COMPRESSED_RGBA_S3TC_DXT1_EXT = 0x83F1\nvar GL_COMPRESSED_RGBA_S3TC_DXT3_EXT = 0x83F2\nvar GL_COMPRESSED_RGBA_S3TC_DXT5_EXT = 0x83F3\n\nvar GL_COMPRESSED_RGB_ATC_WEBGL = 0x8C92\nvar GL_COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL = 0x8C93\nvar GL_COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL = 0x87EE\n\nvar GL_COMPRESSED_RGB_PVRTC_4BPPV1_IMG = 0x8C00\nvar GL_COMPRESSED_RGB_PVRTC_2BPPV1_IMG = 0x8C01\nvar GL_COMPRESSED_RGBA_PVRTC_4BPPV1_IMG = 0x8C02\nvar GL_COMPRESSED_RGBA_PVRTC_2BPPV1_IMG = 0x8C03\n\nvar GL_COMPRESSED_RGB_ETC1_WEBGL = 0x8D64\n\nvar GL_UNSIGNED_BYTE$5 = 0x1401\nvar GL_UNSIGNED_SHORT$3 = 0x1403\nvar GL_UNSIGNED_INT$3 = 0x1405\nvar GL_FLOAT$4 = 0x1406\n\nvar GL_TEXTURE_WRAP_S = 0x2802\nvar GL_TEXTURE_WRAP_T = 0x2803\n\nvar GL_REPEAT = 0x2901\nvar GL_CLAMP_TO_EDGE$1 = 0x812F\nvar GL_MIRRORED_REPEAT = 0x8370\n\nvar GL_TEXTURE_MAG_FILTER = 0x2800\nvar GL_TEXTURE_MIN_FILTER = 0x2801\n\nvar GL_NEAREST$1 = 0x2600\nvar GL_LINEAR = 0x2601\nvar GL_NEAREST_MIPMAP_NEAREST$1 = 0x2700\nvar GL_LINEAR_MIPMAP_NEAREST$1 = 0x2701\nvar GL_NEAREST_MIPMAP_LINEAR$1 = 0x2702\nvar GL_LINEAR_MIPMAP_LINEAR$1 = 0x2703\n\nvar GL_GENERATE_MIPMAP_HINT = 0x8192\nvar GL_DONT_CARE = 0x1100\nvar GL_FASTEST = 0x1101\nvar GL_NICEST = 0x1102\n\nvar GL_TEXTURE_MAX_ANISOTROPY_EXT = 0x84FE\n\nvar GL_UNPACK_ALIGNMENT = 0x0CF5\nvar GL_UNPACK_FLIP_Y_WEBGL = 0x9240\nvar GL_UNPACK_PREMULTIPLY_ALPHA_WEBGL = 0x9241\nvar GL_UNPACK_COLORSPACE_CONVERSION_WEBGL = 0x9243\n\nvar GL_BROWSER_DEFAULT_WEBGL = 0x9244\n\nvar GL_TEXTURE0$1 = 0x84C0\n\nvar MIPMAP_FILTERS = [\n  GL_NEAREST_MIPMAP_NEAREST$1,\n  GL_NEAREST_MIPMAP_LINEAR$1,\n  GL_LINEAR_MIPMAP_NEAREST$1,\n  GL_LINEAR_MIPMAP_LINEAR$1\n]\n\nvar CHANNELS_FORMAT = [\n  0,\n  GL_LUMINANCE,\n  GL_LUMINANCE_ALPHA,\n  GL_RGB,\n  GL_RGBA$1\n]\n\nvar FORMAT_CHANNELS = {}\nFORMAT_CHANNELS[GL_LUMINANCE] =\nFORMAT_CHANNELS[GL_ALPHA] =\nFORMAT_CHANNELS[GL_DEPTH_COMPONENT] = 1\nFORMAT_CHANNELS[GL_DEPTH_STENCIL] =\nFORMAT_CHANNELS[GL_LUMINANCE_ALPHA] = 2\nFORMAT_CHANNELS[GL_RGB] =\nFORMAT_CHANNELS[GL_SRGB_EXT] = 3\nFORMAT_CHANNELS[GL_RGBA$1] =\nFORMAT_CHANNELS[GL_SRGB_ALPHA_EXT] = 4\n\nfunction objectName (str) {\n  return '[object ' + str + ']'\n}\n\nvar CANVAS_CLASS = objectName('HTMLCanvasElement')\nvar OFFSCREENCANVAS_CLASS = objectName('OffscreenCanvas')\nvar CONTEXT2D_CLASS = objectName('CanvasRenderingContext2D')\nvar BITMAP_CLASS = objectName('ImageBitmap')\nvar IMAGE_CLASS = objectName('HTMLImageElement')\nvar VIDEO_CLASS = objectName('HTMLVideoElement')\n\nvar PIXEL_CLASSES = Object.keys(arrayTypes).concat([\n  CANVAS_CLASS,\n  OFFSCREENCANVAS_CLASS,\n  CONTEXT2D_CLASS,\n  BITMAP_CLASS,\n  IMAGE_CLASS,\n  VIDEO_CLASS\n])\n\n// for every texture type, store\n// the size in bytes.\nvar TYPE_SIZES = []\nTYPE_SIZES[GL_UNSIGNED_BYTE$5] = 1\nTYPE_SIZES[GL_FLOAT$4] = 4\nTYPE_SIZES[GL_HALF_FLOAT_OES$1] = 2\n\nTYPE_SIZES[GL_UNSIGNED_SHORT$3] = 2\nTYPE_SIZES[GL_UNSIGNED_INT$3] = 4\n\nvar FORMAT_SIZES_SPECIAL = []\nFORMAT_SIZES_SPECIAL[GL_RGBA4] = 2\nFORMAT_SIZES_SPECIAL[GL_RGB5_A1] = 2\nFORMAT_SIZES_SPECIAL[GL_RGB565] = 2\nFORMAT_SIZES_SPECIAL[GL_DEPTH_STENCIL] = 4\n\nFORMAT_SIZES_SPECIAL[GL_COMPRESSED_RGB_S3TC_DXT1_EXT] = 0.5\nFORMAT_SIZES_SPECIAL[GL_COMPRESSED_RGBA_S3TC_DXT1_EXT] = 0.5\nFORMAT_SIZES_SPECIAL[GL_COMPRESSED_RGBA_S3TC_DXT3_EXT] = 1\nFORMAT_SIZES_SPECIAL[GL_COMPRESSED_RGBA_S3TC_DXT5_EXT] = 1\n\nFORMAT_SIZES_SPECIAL[GL_COMPRESSED_RGB_ATC_WEBGL] = 0.5\nFORMAT_SIZES_SPECIAL[GL_COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL] = 1\nFORMAT_SIZES_SPECIAL[GL_COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL] = 1\n\nFORMAT_SIZES_SPECIAL[GL_COMPRESSED_RGB_PVRTC_4BPPV1_IMG] = 0.5\nFORMAT_SIZES_SPECIAL[GL_COMPRESSED_RGB_PVRTC_2BPPV1_IMG] = 0.25\nFORMAT_SIZES_SPECIAL[GL_COMPRESSED_RGBA_PVRTC_4BPPV1_IMG] = 0.5\nFORMAT_SIZES_SPECIAL[GL_COMPRESSED_RGBA_PVRTC_2BPPV1_IMG] = 0.25\n\nFORMAT_SIZES_SPECIAL[GL_COMPRESSED_RGB_ETC1_WEBGL] = 0.5\n\nfunction isNumericArray (arr) {\n  return (\n    Array.isArray(arr) &&\n    (arr.length === 0 ||\n    typeof arr[0] === 'number'))\n}\n\nfunction isRectArray (arr) {\n  if (!Array.isArray(arr)) {\n    return false\n  }\n  var width = arr.length\n  if (width === 0 || !isArrayLike(arr[0])) {\n    return false\n  }\n  return true\n}\n\nfunction classString (x) {\n  return Object.prototype.toString.call(x)\n}\n\nfunction isCanvasElement (object) {\n  return classString(object) === CANVAS_CLASS\n}\n\nfunction isOffscreenCanvas (object) {\n  return classString(object) === OFFSCREENCANVAS_CLASS\n}\n\nfunction isContext2D (object) {\n  return classString(object) === CONTEXT2D_CLASS\n}\n\nfunction isBitmap (object) {\n  return classString(object) === BITMAP_CLASS\n}\n\nfunction isImageElement (object) {\n  return classString(object) === IMAGE_CLASS\n}\n\nfunction isVideoElement (object) {\n  return classString(object) === VIDEO_CLASS\n}\n\nfunction isPixelData (object) {\n  if (!object) {\n    return false\n  }\n  var className = classString(object)\n  if (PIXEL_CLASSES.indexOf(className) >= 0) {\n    return true\n  }\n  return (\n    isNumericArray(object) ||\n    isRectArray(object) ||\n    isNDArrayLike(object))\n}\n\nfunction typedArrayCode$1 (data) {\n  return arrayTypes[Object.prototype.toString.call(data)] | 0\n}\n\nfunction convertData (result, data) {\n  var n = data.length\n  switch (result.type) {\n    case GL_UNSIGNED_BYTE$5:\n    case GL_UNSIGNED_SHORT$3:\n    case GL_UNSIGNED_INT$3:\n    case GL_FLOAT$4:\n      var converted = pool.allocType(result.type, n)\n      converted.set(data)\n      result.data = converted\n      break\n\n    case GL_HALF_FLOAT_OES$1:\n      result.data = convertToHalfFloat(data)\n      break\n\n    default:\n      check$1.raise('unsupported texture type, must specify a typed array')\n  }\n}\n\nfunction preConvert (image, n) {\n  return pool.allocType(\n    image.type === GL_HALF_FLOAT_OES$1\n      ? GL_FLOAT$4\n      : image.type, n)\n}\n\nfunction postConvert (image, data) {\n  if (image.type === GL_HALF_FLOAT_OES$1) {\n    image.data = convertToHalfFloat(data)\n    pool.freeType(data)\n  } else {\n    image.data = data\n  }\n}\n\nfunction transposeData (image, array, strideX, strideY, strideC, offset) {\n  var w = image.width\n  var h = image.height\n  var c = image.channels\n  var n = w * h * c\n  var data = preConvert(image, n)\n\n  var p = 0\n  for (var i = 0; i < h; ++i) {\n    for (var j = 0; j < w; ++j) {\n      for (var k = 0; k < c; ++k) {\n        data[p++] = array[strideX * j + strideY * i + strideC * k + offset]\n      }\n    }\n  }\n\n  postConvert(image, data)\n}\n\nfunction getTextureSize (format, type, width, height, isMipmap, isCube) {\n  var s\n  if (typeof FORMAT_SIZES_SPECIAL[format] !== 'undefined') {\n    // we have a special array for dealing with weird color formats such as RGB5A1\n    s = FORMAT_SIZES_SPECIAL[format]\n  } else {\n    s = FORMAT_CHANNELS[format] * TYPE_SIZES[type]\n  }\n\n  if (isCube) {\n    s *= 6\n  }\n\n  if (isMipmap) {\n    // compute the total size of all the mipmaps.\n    var total = 0\n\n    var w = width\n    while (w >= 1) {\n      // we can only use mipmaps on a square image,\n      // so we can simply use the width and ignore the height:\n      total += s * w * w\n      w /= 2\n    }\n    return total\n  } else {\n    return s * width * height\n  }\n}\n\nfunction createTextureSet (\n  gl, extensions, limits, reglPoll, contextState, stats, config) {\n  // -------------------------------------------------------\n  // Initialize constants and parameter tables here\n  // -------------------------------------------------------\n  var mipmapHint = {\n    \"don't care\": GL_DONT_CARE,\n    'dont care': GL_DONT_CARE,\n    'nice': GL_NICEST,\n    'fast': GL_FASTEST\n  }\n\n  var wrapModes = {\n    'repeat': GL_REPEAT,\n    'clamp': GL_CLAMP_TO_EDGE$1,\n    'mirror': GL_MIRRORED_REPEAT\n  }\n\n  var magFilters = {\n    'nearest': GL_NEAREST$1,\n    'linear': GL_LINEAR\n  }\n\n  var minFilters = extend({\n    'mipmap': GL_LINEAR_MIPMAP_LINEAR$1,\n    'nearest mipmap nearest': GL_NEAREST_MIPMAP_NEAREST$1,\n    'linear mipmap nearest': GL_LINEAR_MIPMAP_NEAREST$1,\n    'nearest mipmap linear': GL_NEAREST_MIPMAP_LINEAR$1,\n    'linear mipmap linear': GL_LINEAR_MIPMAP_LINEAR$1\n  }, magFilters)\n\n  var colorSpace = {\n    'none': 0,\n    'browser': GL_BROWSER_DEFAULT_WEBGL\n  }\n\n  var textureTypes = {\n    'uint8': GL_UNSIGNED_BYTE$5,\n    'rgba4': GL_UNSIGNED_SHORT_4_4_4_4$1,\n    'rgb565': GL_UNSIGNED_SHORT_5_6_5$1,\n    'rgb5 a1': GL_UNSIGNED_SHORT_5_5_5_1$1\n  }\n\n  var textureFormats = {\n    'alpha': GL_ALPHA,\n    'luminance': GL_LUMINANCE,\n    'luminance alpha': GL_LUMINANCE_ALPHA,\n    'rgb': GL_RGB,\n    'rgba': GL_RGBA$1,\n    'rgba4': GL_RGBA4,\n    'rgb5 a1': GL_RGB5_A1,\n    'rgb565': GL_RGB565\n  }\n\n  var compressedTextureFormats = {}\n\n  if (extensions.ext_srgb) {\n    textureFormats.srgb = GL_SRGB_EXT\n    textureFormats.srgba = GL_SRGB_ALPHA_EXT\n  }\n\n  if (extensions.oes_texture_float) {\n    textureTypes.float32 = textureTypes.float = GL_FLOAT$4\n  }\n\n  if (extensions.oes_texture_half_float) {\n    textureTypes['float16'] = textureTypes['half float'] = GL_HALF_FLOAT_OES$1\n  }\n\n  if (extensions.webgl_depth_texture) {\n    extend(textureFormats, {\n      'depth': GL_DEPTH_COMPONENT,\n      'depth stencil': GL_DEPTH_STENCIL\n    })\n\n    extend(textureTypes, {\n      'uint16': GL_UNSIGNED_SHORT$3,\n      'uint32': GL_UNSIGNED_INT$3,\n      'depth stencil': GL_UNSIGNED_INT_24_8_WEBGL$1\n    })\n  }\n\n  if (extensions.webgl_compressed_texture_s3tc) {\n    extend(compressedTextureFormats, {\n      'rgb s3tc dxt1': GL_COMPRESSED_RGB_S3TC_DXT1_EXT,\n      'rgba s3tc dxt1': GL_COMPRESSED_RGBA_S3TC_DXT1_EXT,\n      'rgba s3tc dxt3': GL_COMPRESSED_RGBA_S3TC_DXT3_EXT,\n      'rgba s3tc dxt5': GL_COMPRESSED_RGBA_S3TC_DXT5_EXT\n    })\n  }\n\n  if (extensions.webgl_compressed_texture_atc) {\n    extend(compressedTextureFormats, {\n      'rgb atc': GL_COMPRESSED_RGB_ATC_WEBGL,\n      'rgba atc explicit alpha': GL_COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL,\n      'rgba atc interpolated alpha': GL_COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL\n    })\n  }\n\n  if (extensions.webgl_compressed_texture_pvrtc) {\n    extend(compressedTextureFormats, {\n      'rgb pvrtc 4bppv1': GL_COMPRESSED_RGB_PVRTC_4BPPV1_IMG,\n      'rgb pvrtc 2bppv1': GL_COMPRESSED_RGB_PVRTC_2BPPV1_IMG,\n      'rgba pvrtc 4bppv1': GL_COMPRESSED_RGBA_PVRTC_4BPPV1_IMG,\n      'rgba pvrtc 2bppv1': GL_COMPRESSED_RGBA_PVRTC_2BPPV1_IMG\n    })\n  }\n\n  if (extensions.webgl_compressed_texture_etc1) {\n    compressedTextureFormats['rgb etc1'] = GL_COMPRESSED_RGB_ETC1_WEBGL\n  }\n\n  // Copy over all texture formats\n  var supportedCompressedFormats = Array.prototype.slice.call(\n    gl.getParameter(GL_COMPRESSED_TEXTURE_FORMATS))\n  Object.keys(compressedTextureFormats).forEach(function (name) {\n    var format = compressedTextureFormats[name]\n    if (supportedCompressedFormats.indexOf(format) >= 0) {\n      textureFormats[name] = format\n    }\n  })\n\n  var supportedFormats = Object.keys(textureFormats)\n  limits.textureFormats = supportedFormats\n\n  // associate with every format string its\n  // corresponding GL-value.\n  var textureFormatsInvert = []\n  Object.keys(textureFormats).forEach(function (key) {\n    var val = textureFormats[key]\n    textureFormatsInvert[val] = key\n  })\n\n  // associate with every type string its\n  // corresponding GL-value.\n  var textureTypesInvert = []\n  Object.keys(textureTypes).forEach(function (key) {\n    var val = textureTypes[key]\n    textureTypesInvert[val] = key\n  })\n\n  var magFiltersInvert = []\n  Object.keys(magFilters).forEach(function (key) {\n    var val = magFilters[key]\n    magFiltersInvert[val] = key\n  })\n\n  var minFiltersInvert = []\n  Object.keys(minFilters).forEach(function (key) {\n    var val = minFilters[key]\n    minFiltersInvert[val] = key\n  })\n\n  var wrapModesInvert = []\n  Object.keys(wrapModes).forEach(function (key) {\n    var val = wrapModes[key]\n    wrapModesInvert[val] = key\n  })\n\n  // colorFormats[] gives the format (channels) associated to an\n  // internalformat\n  var colorFormats = supportedFormats.reduce(function (color, key) {\n    var glenum = textureFormats[key]\n    if (glenum === GL_LUMINANCE ||\n        glenum === GL_ALPHA ||\n        glenum === GL_LUMINANCE ||\n        glenum === GL_LUMINANCE_ALPHA ||\n        glenum === GL_DEPTH_COMPONENT ||\n        glenum === GL_DEPTH_STENCIL ||\n        (extensions.ext_srgb &&\n                (glenum === GL_SRGB_EXT ||\n                 glenum === GL_SRGB_ALPHA_EXT))) {\n      color[glenum] = glenum\n    } else if (glenum === GL_RGB5_A1 || key.indexOf('rgba') >= 0) {\n      color[glenum] = GL_RGBA$1\n    } else {\n      color[glenum] = GL_RGB\n    }\n    return color\n  }, {})\n\n  function TexFlags () {\n    // format info\n    this.internalformat = GL_RGBA$1\n    this.format = GL_RGBA$1\n    this.type = GL_UNSIGNED_BYTE$5\n    this.compressed = false\n\n    // pixel storage\n    this.premultiplyAlpha = false\n    this.flipY = false\n    this.unpackAlignment = 1\n    this.colorSpace = GL_BROWSER_DEFAULT_WEBGL\n\n    // shape info\n    this.width = 0\n    this.height = 0\n    this.channels = 0\n  }\n\n  function copyFlags (result, other) {\n    result.internalformat = other.internalformat\n    result.format = other.format\n    result.type = other.type\n    result.compressed = other.compressed\n\n    result.premultiplyAlpha = other.premultiplyAlpha\n    result.flipY = other.flipY\n    result.unpackAlignment = other.unpackAlignment\n    result.colorSpace = other.colorSpace\n\n    result.width = other.width\n    result.height = other.height\n    result.channels = other.channels\n  }\n\n  function parseFlags (flags, options) {\n    if (typeof options !== 'object' || !options) {\n      return\n    }\n\n    if ('premultiplyAlpha' in options) {\n      check$1.type(options.premultiplyAlpha, 'boolean',\n        'invalid premultiplyAlpha')\n      flags.premultiplyAlpha = options.premultiplyAlpha\n    }\n\n    if ('flipY' in options) {\n      check$1.type(options.flipY, 'boolean',\n        'invalid texture flip')\n      flags.flipY = options.flipY\n    }\n\n    if ('alignment' in options) {\n      check$1.oneOf(options.alignment, [1, 2, 4, 8],\n        'invalid texture unpack alignment')\n      flags.unpackAlignment = options.alignment\n    }\n\n    if ('colorSpace' in options) {\n      check$1.parameter(options.colorSpace, colorSpace,\n        'invalid colorSpace')\n      flags.colorSpace = colorSpace[options.colorSpace]\n    }\n\n    if ('type' in options) {\n      var type = options.type\n      check$1(extensions.oes_texture_float ||\n        !(type === 'float' || type === 'float32'),\n      'you must enable the OES_texture_float extension in order to use floating point textures.')\n      check$1(extensions.oes_texture_half_float ||\n        !(type === 'half float' || type === 'float16'),\n      'you must enable the OES_texture_half_float extension in order to use 16-bit floating point textures.')\n      check$1(extensions.webgl_depth_texture ||\n        !(type === 'uint16' || type === 'uint32' || type === 'depth stencil'),\n      'you must enable the WEBGL_depth_texture extension in order to use depth/stencil textures.')\n      check$1.parameter(type, textureTypes,\n        'invalid texture type')\n      flags.type = textureTypes[type]\n    }\n\n    var w = flags.width\n    var h = flags.height\n    var c = flags.channels\n    var hasChannels = false\n    if ('shape' in options) {\n      check$1(Array.isArray(options.shape) && options.shape.length >= 2,\n        'shape must be an array')\n      w = options.shape[0]\n      h = options.shape[1]\n      if (options.shape.length === 3) {\n        c = options.shape[2]\n        check$1(c > 0 && c <= 4, 'invalid number of channels')\n        hasChannels = true\n      }\n      check$1(w >= 0 && w <= limits.maxTextureSize, 'invalid width')\n      check$1(h >= 0 && h <= limits.maxTextureSize, 'invalid height')\n    } else {\n      if ('radius' in options) {\n        w = h = options.radius\n        check$1(w >= 0 && w <= limits.maxTextureSize, 'invalid radius')\n      }\n      if ('width' in options) {\n        w = options.width\n        check$1(w >= 0 && w <= limits.maxTextureSize, 'invalid width')\n      }\n      if ('height' in options) {\n        h = options.height\n        check$1(h >= 0 && h <= limits.maxTextureSize, 'invalid height')\n      }\n      if ('channels' in options) {\n        c = options.channels\n        check$1(c > 0 && c <= 4, 'invalid number of channels')\n        hasChannels = true\n      }\n    }\n    flags.width = w | 0\n    flags.height = h | 0\n    flags.channels = c | 0\n\n    var hasFormat = false\n    if ('format' in options) {\n      var formatStr = options.format\n      check$1(extensions.webgl_depth_texture ||\n        !(formatStr === 'depth' || formatStr === 'depth stencil'),\n      'you must enable the WEBGL_depth_texture extension in order to use depth/stencil textures.')\n      check$1.parameter(formatStr, textureFormats,\n        'invalid texture format')\n      var internalformat = flags.internalformat = textureFormats[formatStr]\n      flags.format = colorFormats[internalformat]\n      if (formatStr in textureTypes) {\n        if (!('type' in options)) {\n          flags.type = textureTypes[formatStr]\n        }\n      }\n      if (formatStr in compressedTextureFormats) {\n        flags.compressed = true\n      }\n      hasFormat = true\n    }\n\n    // Reconcile channels and format\n    if (!hasChannels && hasFormat) {\n      flags.channels = FORMAT_CHANNELS[flags.format]\n    } else if (hasChannels && !hasFormat) {\n      if (flags.channels !== CHANNELS_FORMAT[flags.format]) {\n        flags.format = flags.internalformat = CHANNELS_FORMAT[flags.channels]\n      }\n    } else if (hasFormat && hasChannels) {\n      check$1(\n        flags.channels === FORMAT_CHANNELS[flags.format],\n        'number of channels inconsistent with specified format')\n    }\n  }\n\n  function setFlags (flags) {\n    gl.pixelStorei(GL_UNPACK_FLIP_Y_WEBGL, flags.flipY)\n    gl.pixelStorei(GL_UNPACK_PREMULTIPLY_ALPHA_WEBGL, flags.premultiplyAlpha)\n    gl.pixelStorei(GL_UNPACK_COLORSPACE_CONVERSION_WEBGL, flags.colorSpace)\n    gl.pixelStorei(GL_UNPACK_ALIGNMENT, flags.unpackAlignment)\n  }\n\n  // -------------------------------------------------------\n  // Tex image data\n  // -------------------------------------------------------\n  function TexImage () {\n    TexFlags.call(this)\n\n    this.xOffset = 0\n    this.yOffset = 0\n\n    // data\n    this.data = null\n    this.needsFree = false\n\n    // html element\n    this.element = null\n\n    // copyTexImage info\n    this.needsCopy = false\n  }\n\n  function parseImage (image, options) {\n    var data = null\n    if (isPixelData(options)) {\n      data = options\n    } else if (options) {\n      check$1.type(options, 'object', 'invalid pixel data type')\n      parseFlags(image, options)\n      if ('x' in options) {\n        image.xOffset = options.x | 0\n      }\n      if ('y' in options) {\n        image.yOffset = options.y | 0\n      }\n      if (isPixelData(options.data)) {\n        data = options.data\n      }\n    }\n\n    check$1(\n      !image.compressed ||\n      data instanceof Uint8Array,\n      'compressed texture data must be stored in a uint8array')\n\n    if (options.copy) {\n      check$1(!data, 'can not specify copy and data field for the same texture')\n      var viewW = contextState.viewportWidth\n      var viewH = contextState.viewportHeight\n      image.width = image.width || (viewW - image.xOffset)\n      image.height = image.height || (viewH - image.yOffset)\n      image.needsCopy = true\n      check$1(image.xOffset >= 0 && image.xOffset < viewW &&\n            image.yOffset >= 0 && image.yOffset < viewH &&\n            image.width > 0 && image.width <= viewW &&\n            image.height > 0 && image.height <= viewH,\n      'copy texture read out of bounds')\n    } else if (!data) {\n      image.width = image.width || 1\n      image.height = image.height || 1\n      image.channels = image.channels || 4\n    } else if (isTypedArray(data)) {\n      image.channels = image.channels || 4\n      image.data = data\n      if (!('type' in options) && image.type === GL_UNSIGNED_BYTE$5) {\n        image.type = typedArrayCode$1(data)\n      }\n    } else if (isNumericArray(data)) {\n      image.channels = image.channels || 4\n      convertData(image, data)\n      image.alignment = 1\n      image.needsFree = true\n    } else if (isNDArrayLike(data)) {\n      var array = data.data\n      if (!Array.isArray(array) && image.type === GL_UNSIGNED_BYTE$5) {\n        image.type = typedArrayCode$1(array)\n      }\n      var shape = data.shape\n      var stride = data.stride\n      var shapeX, shapeY, shapeC, strideX, strideY, strideC\n      if (shape.length === 3) {\n        shapeC = shape[2]\n        strideC = stride[2]\n      } else {\n        check$1(shape.length === 2, 'invalid ndarray pixel data, must be 2 or 3D')\n        shapeC = 1\n        strideC = 1\n      }\n      shapeX = shape[0]\n      shapeY = shape[1]\n      strideX = stride[0]\n      strideY = stride[1]\n      image.alignment = 1\n      image.width = shapeX\n      image.height = shapeY\n      image.channels = shapeC\n      image.format = image.internalformat = CHANNELS_FORMAT[shapeC]\n      image.needsFree = true\n      transposeData(image, array, strideX, strideY, strideC, data.offset)\n    } else if (isCanvasElement(data) || isOffscreenCanvas(data) || isContext2D(data)) {\n      if (isCanvasElement(data) || isOffscreenCanvas(data)) {\n        image.element = data\n      } else {\n        image.element = data.canvas\n      }\n      image.width = image.element.width\n      image.height = image.element.height\n      image.channels = 4\n    } else if (isBitmap(data)) {\n      image.element = data\n      image.width = data.width\n      image.height = data.height\n      image.channels = 4\n    } else if (isImageElement(data)) {\n      image.element = data\n      image.width = data.naturalWidth\n      image.height = data.naturalHeight\n      image.channels = 4\n    } else if (isVideoElement(data)) {\n      image.element = data\n      image.width = data.videoWidth\n      image.height = data.videoHeight\n      image.channels = 4\n    } else if (isRectArray(data)) {\n      var w = image.width || data[0].length\n      var h = image.height || data.length\n      var c = image.channels\n      if (isArrayLike(data[0][0])) {\n        c = c || data[0][0].length\n      } else {\n        c = c || 1\n      }\n      var arrayShape = flattenUtils.shape(data)\n      var n = 1\n      for (var dd = 0; dd < arrayShape.length; ++dd) {\n        n *= arrayShape[dd]\n      }\n      var allocData = preConvert(image, n)\n      flattenUtils.flatten(data, arrayShape, '', allocData)\n      postConvert(image, allocData)\n      image.alignment = 1\n      image.width = w\n      image.height = h\n      image.channels = c\n      image.format = image.internalformat = CHANNELS_FORMAT[c]\n      image.needsFree = true\n    }\n\n    if (image.type === GL_FLOAT$4) {\n      check$1(limits.extensions.indexOf('oes_texture_float') >= 0,\n        'oes_texture_float extension not enabled')\n    } else if (image.type === GL_HALF_FLOAT_OES$1) {\n      check$1(limits.extensions.indexOf('oes_texture_half_float') >= 0,\n        'oes_texture_half_float extension not enabled')\n    }\n\n    // do compressed texture  validation here.\n  }\n\n  function setImage (info, target, miplevel) {\n    var element = info.element\n    var data = info.data\n    var internalformat = info.internalformat\n    var format = info.format\n    var type = info.type\n    var width = info.width\n    var height = info.height\n\n    setFlags(info)\n\n    if (element) {\n      gl.texImage2D(target, miplevel, format, format, type, element)\n    } else if (info.compressed) {\n      gl.compressedTexImage2D(target, miplevel, internalformat, width, height, 0, data)\n    } else if (info.needsCopy) {\n      reglPoll()\n      gl.copyTexImage2D(\n        target, miplevel, format, info.xOffset, info.yOffset, width, height, 0)\n    } else {\n      gl.texImage2D(target, miplevel, format, width, height, 0, format, type, data || null)\n    }\n  }\n\n  function setSubImage (info, target, x, y, miplevel) {\n    var element = info.element\n    var data = info.data\n    var internalformat = info.internalformat\n    var format = info.format\n    var type = info.type\n    var width = info.width\n    var height = info.height\n\n    setFlags(info)\n\n    if (element) {\n      gl.texSubImage2D(\n        target, miplevel, x, y, format, type, element)\n    } else if (info.compressed) {\n      gl.compressedTexSubImage2D(\n        target, miplevel, x, y, internalformat, width, height, data)\n    } else if (info.needsCopy) {\n      reglPoll()\n      gl.copyTexSubImage2D(\n        target, miplevel, x, y, info.xOffset, info.yOffset, width, height)\n    } else {\n      gl.texSubImage2D(\n        target, miplevel, x, y, width, height, format, type, data)\n    }\n  }\n\n  // texImage pool\n  var imagePool = []\n\n  function allocImage () {\n    return imagePool.pop() || new TexImage()\n  }\n\n  function freeImage (image) {\n    if (image.needsFree) {\n      pool.freeType(image.data)\n    }\n    TexImage.call(image)\n    imagePool.push(image)\n  }\n\n  // -------------------------------------------------------\n  // Mip map\n  // -------------------------------------------------------\n  function MipMap () {\n    TexFlags.call(this)\n\n    this.genMipmaps = false\n    this.mipmapHint = GL_DONT_CARE\n    this.mipmask = 0\n    this.images = Array(16)\n  }\n\n  function parseMipMapFromShape (mipmap, width, height) {\n    var img = mipmap.images[0] = allocImage()\n    mipmap.mipmask = 1\n    img.width = mipmap.width = width\n    img.height = mipmap.height = height\n    img.channels = mipmap.channels = 4\n  }\n\n  function parseMipMapFromObject (mipmap, options) {\n    var imgData = null\n    if (isPixelData(options)) {\n      imgData = mipmap.images[0] = allocImage()\n      copyFlags(imgData, mipmap)\n      parseImage(imgData, options)\n      mipmap.mipmask = 1\n    } else {\n      parseFlags(mipmap, options)\n      if (Array.isArray(options.mipmap)) {\n        var mipData = options.mipmap\n        for (var i = 0; i < mipData.length; ++i) {\n          imgData = mipmap.images[i] = allocImage()\n          copyFlags(imgData, mipmap)\n          imgData.width >>= i\n          imgData.height >>= i\n          parseImage(imgData, mipData[i])\n          mipmap.mipmask |= (1 << i)\n        }\n      } else {\n        imgData = mipmap.images[0] = allocImage()\n        copyFlags(imgData, mipmap)\n        parseImage(imgData, options)\n        mipmap.mipmask = 1\n      }\n    }\n    copyFlags(mipmap, mipmap.images[0])\n\n    // For textures of the compressed format WEBGL_compressed_texture_s3tc\n    // we must have that\n    //\n    // \"When level equals zero width and height must be a multiple of 4.\n    // When level is greater than 0 width and height must be 0, 1, 2 or a multiple of 4. \"\n    //\n    // but we do not yet support having multiple mipmap levels for compressed textures,\n    // so we only test for level zero.\n\n    if (\n      mipmap.compressed &&\n      (\n        mipmap.internalformat === GL_COMPRESSED_RGB_S3TC_DXT1_EXT ||\n        mipmap.internalformat === GL_COMPRESSED_RGBA_S3TC_DXT1_EXT ||\n        mipmap.internalformat === GL_COMPRESSED_RGBA_S3TC_DXT3_EXT ||\n        mipmap.internalformat === GL_COMPRESSED_RGBA_S3TC_DXT5_EXT\n      )\n    ) {\n      check$1(mipmap.width % 4 === 0 && mipmap.height % 4 === 0,\n        'for compressed texture formats, mipmap level 0 must have width and height that are a multiple of 4')\n    }\n  }\n\n  function setMipMap (mipmap, target) {\n    var images = mipmap.images\n    for (var i = 0; i < images.length; ++i) {\n      if (!images[i]) {\n        return\n      }\n      setImage(images[i], target, i)\n    }\n  }\n\n  var mipPool = []\n\n  function allocMipMap () {\n    var result = mipPool.pop() || new MipMap()\n    TexFlags.call(result)\n    result.mipmask = 0\n    for (var i = 0; i < 16; ++i) {\n      result.images[i] = null\n    }\n    return result\n  }\n\n  function freeMipMap (mipmap) {\n    var images = mipmap.images\n    for (var i = 0; i < images.length; ++i) {\n      if (images[i]) {\n        freeImage(images[i])\n      }\n      images[i] = null\n    }\n    mipPool.push(mipmap)\n  }\n\n  // -------------------------------------------------------\n  // Tex info\n  // -------------------------------------------------------\n  function TexInfo () {\n    this.minFilter = GL_NEAREST$1\n    this.magFilter = GL_NEAREST$1\n\n    this.wrapS = GL_CLAMP_TO_EDGE$1\n    this.wrapT = GL_CLAMP_TO_EDGE$1\n\n    this.anisotropic = 1\n\n    this.genMipmaps = false\n    this.mipmapHint = GL_DONT_CARE\n  }\n\n  function parseTexInfo (info, options) {\n    if ('min' in options) {\n      var minFilter = options.min\n      check$1.parameter(minFilter, minFilters)\n      info.minFilter = minFilters[minFilter]\n      if (MIPMAP_FILTERS.indexOf(info.minFilter) >= 0 && !('faces' in options)) {\n        info.genMipmaps = true\n      }\n    }\n\n    if ('mag' in options) {\n      var magFilter = options.mag\n      check$1.parameter(magFilter, magFilters)\n      info.magFilter = magFilters[magFilter]\n    }\n\n    var wrapS = info.wrapS\n    var wrapT = info.wrapT\n    if ('wrap' in options) {\n      var wrap = options.wrap\n      if (typeof wrap === 'string') {\n        check$1.parameter(wrap, wrapModes)\n        wrapS = wrapT = wrapModes[wrap]\n      } else if (Array.isArray(wrap)) {\n        check$1.parameter(wrap[0], wrapModes)\n        check$1.parameter(wrap[1], wrapModes)\n        wrapS = wrapModes[wrap[0]]\n        wrapT = wrapModes[wrap[1]]\n      }\n    } else {\n      if ('wrapS' in options) {\n        var optWrapS = options.wrapS\n        check$1.parameter(optWrapS, wrapModes)\n        wrapS = wrapModes[optWrapS]\n      }\n      if ('wrapT' in options) {\n        var optWrapT = options.wrapT\n        check$1.parameter(optWrapT, wrapModes)\n        wrapT = wrapModes[optWrapT]\n      }\n    }\n    info.wrapS = wrapS\n    info.wrapT = wrapT\n\n    if ('anisotropic' in options) {\n      var anisotropic = options.anisotropic\n      check$1(typeof anisotropic === 'number' &&\n         anisotropic >= 1 && anisotropic <= limits.maxAnisotropic,\n      'aniso samples must be between 1 and ')\n      info.anisotropic = options.anisotropic\n    }\n\n    if ('mipmap' in options) {\n      var hasMipMap = false\n      switch (typeof options.mipmap) {\n        case 'string':\n          check$1.parameter(options.mipmap, mipmapHint,\n            'invalid mipmap hint')\n          info.mipmapHint = mipmapHint[options.mipmap]\n          info.genMipmaps = true\n          hasMipMap = true\n          break\n\n        case 'boolean':\n          hasMipMap = info.genMipmaps = options.mipmap\n          break\n\n        case 'object':\n          check$1(Array.isArray(options.mipmap), 'invalid mipmap type')\n          info.genMipmaps = false\n          hasMipMap = true\n          break\n\n        default:\n          check$1.raise('invalid mipmap type')\n      }\n      if (hasMipMap && !('min' in options)) {\n        info.minFilter = GL_NEAREST_MIPMAP_NEAREST$1\n      }\n    }\n  }\n\n  function setTexInfo (info, target) {\n    gl.texParameteri(target, GL_TEXTURE_MIN_FILTER, info.minFilter)\n    gl.texParameteri(target, GL_TEXTURE_MAG_FILTER, info.magFilter)\n    gl.texParameteri(target, GL_TEXTURE_WRAP_S, info.wrapS)\n    gl.texParameteri(target, GL_TEXTURE_WRAP_T, info.wrapT)\n    if (extensions.ext_texture_filter_anisotropic) {\n      gl.texParameteri(target, GL_TEXTURE_MAX_ANISOTROPY_EXT, info.anisotropic)\n    }\n    if (info.genMipmaps) {\n      gl.hint(GL_GENERATE_MIPMAP_HINT, info.mipmapHint)\n      gl.generateMipmap(target)\n    }\n  }\n\n  // -------------------------------------------------------\n  // Full texture object\n  // -------------------------------------------------------\n  var textureCount = 0\n  var textureSet = {}\n  var numTexUnits = limits.maxTextureUnits\n  var textureUnits = Array(numTexUnits).map(function () {\n    return null\n  })\n\n  function REGLTexture (target) {\n    TexFlags.call(this)\n    this.mipmask = 0\n    this.internalformat = GL_RGBA$1\n\n    this.id = textureCount++\n\n    this.refCount = 1\n\n    this.target = target\n    this.texture = gl.createTexture()\n\n    this.unit = -1\n    this.bindCount = 0\n\n    this.texInfo = new TexInfo()\n\n    if (config.profile) {\n      this.stats = { size: 0 }\n    }\n  }\n\n  function tempBind (texture) {\n    gl.activeTexture(GL_TEXTURE0$1)\n    gl.bindTexture(texture.target, texture.texture)\n  }\n\n  function tempRestore () {\n    var prev = textureUnits[0]\n    if (prev) {\n      gl.bindTexture(prev.target, prev.texture)\n    } else {\n      gl.bindTexture(GL_TEXTURE_2D$1, null)\n    }\n  }\n\n  function destroy (texture) {\n    var handle = texture.texture\n    check$1(handle, 'must not double destroy texture')\n    var unit = texture.unit\n    var target = texture.target\n    if (unit >= 0) {\n      gl.activeTexture(GL_TEXTURE0$1 + unit)\n      gl.bindTexture(target, null)\n      textureUnits[unit] = null\n    }\n    gl.deleteTexture(handle)\n    texture.texture = null\n    texture.params = null\n    texture.pixels = null\n    texture.refCount = 0\n    delete textureSet[texture.id]\n    stats.textureCount--\n  }\n\n  extend(REGLTexture.prototype, {\n    bind: function () {\n      var texture = this\n      texture.bindCount += 1\n      var unit = texture.unit\n      if (unit < 0) {\n        for (var i = 0; i < numTexUnits; ++i) {\n          var other = textureUnits[i]\n          if (other) {\n            if (other.bindCount > 0) {\n              continue\n            }\n            other.unit = -1\n          }\n          textureUnits[i] = texture\n          unit = i\n          break\n        }\n        if (unit >= numTexUnits) {\n          check$1.raise('insufficient number of texture units')\n        }\n        if (config.profile && stats.maxTextureUnits < (unit + 1)) {\n          stats.maxTextureUnits = unit + 1 // +1, since the units are zero-based\n        }\n        texture.unit = unit\n        gl.activeTexture(GL_TEXTURE0$1 + unit)\n        gl.bindTexture(texture.target, texture.texture)\n      }\n      return unit\n    },\n\n    unbind: function () {\n      this.bindCount -= 1\n    },\n\n    decRef: function () {\n      if (--this.refCount <= 0) {\n        destroy(this)\n      }\n    }\n  })\n\n  function createTexture2D (a, b) {\n    var texture = new REGLTexture(GL_TEXTURE_2D$1)\n    textureSet[texture.id] = texture\n    stats.textureCount++\n\n    function reglTexture2D (a, b) {\n      var texInfo = texture.texInfo\n      TexInfo.call(texInfo)\n      var mipData = allocMipMap()\n\n      if (typeof a === 'number') {\n        if (typeof b === 'number') {\n          parseMipMapFromShape(mipData, a | 0, b | 0)\n        } else {\n          parseMipMapFromShape(mipData, a | 0, a | 0)\n        }\n      } else if (a) {\n        check$1.type(a, 'object', 'invalid arguments to regl.texture')\n        parseTexInfo(texInfo, a)\n        parseMipMapFromObject(mipData, a)\n      } else {\n        // empty textures get assigned a default shape of 1x1\n        parseMipMapFromShape(mipData, 1, 1)\n      }\n\n      if (texInfo.genMipmaps) {\n        mipData.mipmask = (mipData.width << 1) - 1\n      }\n      texture.mipmask = mipData.mipmask\n\n      copyFlags(texture, mipData)\n\n      check$1.texture2D(texInfo, mipData, limits)\n      texture.internalformat = mipData.internalformat\n\n      reglTexture2D.width = mipData.width\n      reglTexture2D.height = mipData.height\n\n      tempBind(texture)\n      setMipMap(mipData, GL_TEXTURE_2D$1)\n      setTexInfo(texInfo, GL_TEXTURE_2D$1)\n      tempRestore()\n\n      freeMipMap(mipData)\n\n      if (config.profile) {\n        texture.stats.size = getTextureSize(\n          texture.internalformat,\n          texture.type,\n          mipData.width,\n          mipData.height,\n          texInfo.genMipmaps,\n          false)\n      }\n      reglTexture2D.format = textureFormatsInvert[texture.internalformat]\n      reglTexture2D.type = textureTypesInvert[texture.type]\n\n      reglTexture2D.mag = magFiltersInvert[texInfo.magFilter]\n      reglTexture2D.min = minFiltersInvert[texInfo.minFilter]\n\n      reglTexture2D.wrapS = wrapModesInvert[texInfo.wrapS]\n      reglTexture2D.wrapT = wrapModesInvert[texInfo.wrapT]\n\n      return reglTexture2D\n    }\n\n    function subimage (image, x_, y_, level_) {\n      check$1(!!image, 'must specify image data')\n\n      var x = x_ | 0\n      var y = y_ | 0\n      var level = level_ | 0\n\n      var imageData = allocImage()\n      copyFlags(imageData, texture)\n      imageData.width = 0\n      imageData.height = 0\n      parseImage(imageData, image)\n      imageData.width = imageData.width || ((texture.width >> level) - x)\n      imageData.height = imageData.height || ((texture.height >> level) - y)\n\n      check$1(\n        texture.type === imageData.type &&\n        texture.format === imageData.format &&\n        texture.internalformat === imageData.internalformat,\n        'incompatible format for texture.subimage')\n      check$1(\n        x >= 0 && y >= 0 &&\n        x + imageData.width <= texture.width &&\n        y + imageData.height <= texture.height,\n        'texture.subimage write out of bounds')\n      check$1(\n        texture.mipmask & (1 << level),\n        'missing mipmap data')\n      check$1(\n        imageData.data || imageData.element || imageData.needsCopy,\n        'missing image data')\n\n      tempBind(texture)\n      setSubImage(imageData, GL_TEXTURE_2D$1, x, y, level)\n      tempRestore()\n\n      freeImage(imageData)\n\n      return reglTexture2D\n    }\n\n    function resize (w_, h_) {\n      var w = w_ | 0\n      var h = (h_ | 0) || w\n      if (w === texture.width && h === texture.height) {\n        return reglTexture2D\n      }\n\n      reglTexture2D.width = texture.width = w\n      reglTexture2D.height = texture.height = h\n\n      tempBind(texture)\n\n      for (var i = 0; texture.mipmask >> i; ++i) {\n        var _w = w >> i\n        var _h = h >> i\n        if (!_w || !_h) break\n        gl.texImage2D(\n          GL_TEXTURE_2D$1,\n          i,\n          texture.format,\n          _w,\n          _h,\n          0,\n          texture.format,\n          texture.type,\n          null)\n      }\n      tempRestore()\n\n      // also, recompute the texture size.\n      if (config.profile) {\n        texture.stats.size = getTextureSize(\n          texture.internalformat,\n          texture.type,\n          w,\n          h,\n          false,\n          false)\n      }\n\n      return reglTexture2D\n    }\n\n    reglTexture2D(a, b)\n\n    reglTexture2D.subimage = subimage\n    reglTexture2D.resize = resize\n    reglTexture2D._reglType = 'texture2d'\n    reglTexture2D._texture = texture\n    if (config.profile) {\n      reglTexture2D.stats = texture.stats\n    }\n    reglTexture2D.destroy = function () {\n      texture.decRef()\n    }\n\n    return reglTexture2D\n  }\n\n  function createTextureCube (a0, a1, a2, a3, a4, a5) {\n    var texture = new REGLTexture(GL_TEXTURE_CUBE_MAP$1)\n    textureSet[texture.id] = texture\n    stats.cubeCount++\n\n    var faces = new Array(6)\n\n    function reglTextureCube (a0, a1, a2, a3, a4, a5) {\n      var i\n      var texInfo = texture.texInfo\n      TexInfo.call(texInfo)\n      for (i = 0; i < 6; ++i) {\n        faces[i] = allocMipMap()\n      }\n\n      if (typeof a0 === 'number' || !a0) {\n        var s = (a0 | 0) || 1\n        for (i = 0; i < 6; ++i) {\n          parseMipMapFromShape(faces[i], s, s)\n        }\n      } else if (typeof a0 === 'object') {\n        if (a1) {\n          parseMipMapFromObject(faces[0], a0)\n          parseMipMapFromObject(faces[1], a1)\n          parseMipMapFromObject(faces[2], a2)\n          parseMipMapFromObject(faces[3], a3)\n          parseMipMapFromObject(faces[4], a4)\n          parseMipMapFromObject(faces[5], a5)\n        } else {\n          parseTexInfo(texInfo, a0)\n          parseFlags(texture, a0)\n          if ('faces' in a0) {\n            var faceInput = a0.faces\n            check$1(Array.isArray(faceInput) && faceInput.length === 6,\n              'cube faces must be a length 6 array')\n            for (i = 0; i < 6; ++i) {\n              check$1(typeof faceInput[i] === 'object' && !!faceInput[i],\n                'invalid input for cube map face')\n              copyFlags(faces[i], texture)\n              parseMipMapFromObject(faces[i], faceInput[i])\n            }\n          } else {\n            for (i = 0; i < 6; ++i) {\n              parseMipMapFromObject(faces[i], a0)\n            }\n          }\n        }\n      } else {\n        check$1.raise('invalid arguments to cube map')\n      }\n\n      copyFlags(texture, faces[0])\n\n      if (!limits.npotTextureCube) {\n        check$1(isPow2$1(texture.width) && isPow2$1(texture.height), 'your browser does not support non power or two texture dimensions')\n      }\n\n      if (texInfo.genMipmaps) {\n        texture.mipmask = (faces[0].width << 1) - 1\n      } else {\n        texture.mipmask = faces[0].mipmask\n      }\n\n      check$1.textureCube(texture, texInfo, faces, limits)\n      texture.internalformat = faces[0].internalformat\n\n      reglTextureCube.width = faces[0].width\n      reglTextureCube.height = faces[0].height\n\n      tempBind(texture)\n      for (i = 0; i < 6; ++i) {\n        setMipMap(faces[i], GL_TEXTURE_CUBE_MAP_POSITIVE_X$1 + i)\n      }\n      setTexInfo(texInfo, GL_TEXTURE_CUBE_MAP$1)\n      tempRestore()\n\n      if (config.profile) {\n        texture.stats.size = getTextureSize(\n          texture.internalformat,\n          texture.type,\n          reglTextureCube.width,\n          reglTextureCube.height,\n          texInfo.genMipmaps,\n          true)\n      }\n\n      reglTextureCube.format = textureFormatsInvert[texture.internalformat]\n      reglTextureCube.type = textureTypesInvert[texture.type]\n\n      reglTextureCube.mag = magFiltersInvert[texInfo.magFilter]\n      reglTextureCube.min = minFiltersInvert[texInfo.minFilter]\n\n      reglTextureCube.wrapS = wrapModesInvert[texInfo.wrapS]\n      reglTextureCube.wrapT = wrapModesInvert[texInfo.wrapT]\n\n      for (i = 0; i < 6; ++i) {\n        freeMipMap(faces[i])\n      }\n\n      return reglTextureCube\n    }\n\n    function subimage (face, image, x_, y_, level_) {\n      check$1(!!image, 'must specify image data')\n      check$1(typeof face === 'number' && face === (face | 0) &&\n        face >= 0 && face < 6, 'invalid face')\n\n      var x = x_ | 0\n      var y = y_ | 0\n      var level = level_ | 0\n\n      var imageData = allocImage()\n      copyFlags(imageData, texture)\n      imageData.width = 0\n      imageData.height = 0\n      parseImage(imageData, image)\n      imageData.width = imageData.width || ((texture.width >> level) - x)\n      imageData.height = imageData.height || ((texture.height >> level) - y)\n\n      check$1(\n        texture.type === imageData.type &&\n        texture.format === imageData.format &&\n        texture.internalformat === imageData.internalformat,\n        'incompatible format for texture.subimage')\n      check$1(\n        x >= 0 && y >= 0 &&\n        x + imageData.width <= texture.width &&\n        y + imageData.height <= texture.height,\n        'texture.subimage write out of bounds')\n      check$1(\n        texture.mipmask & (1 << level),\n        'missing mipmap data')\n      check$1(\n        imageData.data || imageData.element || imageData.needsCopy,\n        'missing image data')\n\n      tempBind(texture)\n      setSubImage(imageData, GL_TEXTURE_CUBE_MAP_POSITIVE_X$1 + face, x, y, level)\n      tempRestore()\n\n      freeImage(imageData)\n\n      return reglTextureCube\n    }\n\n    function resize (radius_) {\n      var radius = radius_ | 0\n      if (radius === texture.width) {\n        return\n      }\n\n      reglTextureCube.width = texture.width = radius\n      reglTextureCube.height = texture.height = radius\n\n      tempBind(texture)\n      for (var i = 0; i < 6; ++i) {\n        for (var j = 0; texture.mipmask >> j; ++j) {\n          gl.texImage2D(\n            GL_TEXTURE_CUBE_MAP_POSITIVE_X$1 + i,\n            j,\n            texture.format,\n            radius >> j,\n            radius >> j,\n            0,\n            texture.format,\n            texture.type,\n            null)\n        }\n      }\n      tempRestore()\n\n      if (config.profile) {\n        texture.stats.size = getTextureSize(\n          texture.internalformat,\n          texture.type,\n          reglTextureCube.width,\n          reglTextureCube.height,\n          false,\n          true)\n      }\n\n      return reglTextureCube\n    }\n\n    reglTextureCube(a0, a1, a2, a3, a4, a5)\n\n    reglTextureCube.subimage = subimage\n    reglTextureCube.resize = resize\n    reglTextureCube._reglType = 'textureCube'\n    reglTextureCube._texture = texture\n    if (config.profile) {\n      reglTextureCube.stats = texture.stats\n    }\n    reglTextureCube.destroy = function () {\n      texture.decRef()\n    }\n\n    return reglTextureCube\n  }\n\n  // Called when regl is destroyed\n  function destroyTextures () {\n    for (var i = 0; i < numTexUnits; ++i) {\n      gl.activeTexture(GL_TEXTURE0$1 + i)\n      gl.bindTexture(GL_TEXTURE_2D$1, null)\n      textureUnits[i] = null\n    }\n    values(textureSet).forEach(destroy)\n\n    stats.cubeCount = 0\n    stats.textureCount = 0\n  }\n\n  if (config.profile) {\n    stats.getTotalTextureSize = function () {\n      var total = 0\n      Object.keys(textureSet).forEach(function (key) {\n        total += textureSet[key].stats.size\n      })\n      return total\n    }\n  }\n\n  function restoreTextures () {\n    for (var i = 0; i < numTexUnits; ++i) {\n      var tex = textureUnits[i]\n      if (tex) {\n        tex.bindCount = 0\n        tex.unit = -1\n        textureUnits[i] = null\n      }\n    }\n\n    values(textureSet).forEach(function (texture) {\n      texture.texture = gl.createTexture()\n      gl.bindTexture(texture.target, texture.texture)\n      for (var i = 0; i < 32; ++i) {\n        if ((texture.mipmask & (1 << i)) === 0) {\n          continue\n        }\n        if (texture.target === GL_TEXTURE_2D$1) {\n          gl.texImage2D(GL_TEXTURE_2D$1,\n            i,\n            texture.internalformat,\n            texture.width >> i,\n            texture.height >> i,\n            0,\n            texture.internalformat,\n            texture.type,\n            null)\n        } else {\n          for (var j = 0; j < 6; ++j) {\n            gl.texImage2D(GL_TEXTURE_CUBE_MAP_POSITIVE_X$1 + j,\n              i,\n              texture.internalformat,\n              texture.width >> i,\n              texture.height >> i,\n              0,\n              texture.internalformat,\n              texture.type,\n              null)\n          }\n        }\n      }\n      setTexInfo(texture.texInfo, texture.target)\n    })\n  }\n\n  function refreshTextures () {\n    for (var i = 0; i < numTexUnits; ++i) {\n      var tex = textureUnits[i]\n      if (tex) {\n        tex.bindCount = 0\n        tex.unit = -1\n        textureUnits[i] = null\n      }\n      gl.activeTexture(GL_TEXTURE0$1 + i)\n      gl.bindTexture(GL_TEXTURE_2D$1, null)\n      gl.bindTexture(GL_TEXTURE_CUBE_MAP$1, null)\n    }\n  }\n\n  return {\n    create2D: createTexture2D,\n    createCube: createTextureCube,\n    clear: destroyTextures,\n    getTexture: function (wrapper) {\n      return null\n    },\n    restore: restoreTextures,\n    refresh: refreshTextures\n  }\n}\n\nvar GL_RENDERBUFFER = 0x8D41\n\nvar GL_RGBA4$1 = 0x8056\nvar GL_RGB5_A1$1 = 0x8057\nvar GL_RGB565$1 = 0x8D62\nvar GL_DEPTH_COMPONENT16 = 0x81A5\nvar GL_STENCIL_INDEX8 = 0x8D48\nvar GL_DEPTH_STENCIL$1 = 0x84F9\n\nvar GL_SRGB8_ALPHA8_EXT = 0x8C43\n\nvar GL_RGBA32F_EXT = 0x8814\n\nvar GL_RGBA16F_EXT = 0x881A\nvar GL_RGB16F_EXT = 0x881B\n\nvar FORMAT_SIZES = []\n\nFORMAT_SIZES[GL_RGBA4$1] = 2\nFORMAT_SIZES[GL_RGB5_A1$1] = 2\nFORMAT_SIZES[GL_RGB565$1] = 2\n\nFORMAT_SIZES[GL_DEPTH_COMPONENT16] = 2\nFORMAT_SIZES[GL_STENCIL_INDEX8] = 1\nFORMAT_SIZES[GL_DEPTH_STENCIL$1] = 4\n\nFORMAT_SIZES[GL_SRGB8_ALPHA8_EXT] = 4\nFORMAT_SIZES[GL_RGBA32F_EXT] = 16\nFORMAT_SIZES[GL_RGBA16F_EXT] = 8\nFORMAT_SIZES[GL_RGB16F_EXT] = 6\n\nfunction getRenderbufferSize (format, width, height) {\n  return FORMAT_SIZES[format] * width * height\n}\n\nvar wrapRenderbuffers = function (gl, extensions, limits, stats, config) {\n  var formatTypes = {\n    'rgba4': GL_RGBA4$1,\n    'rgb565': GL_RGB565$1,\n    'rgb5 a1': GL_RGB5_A1$1,\n    'depth': GL_DEPTH_COMPONENT16,\n    'stencil': GL_STENCIL_INDEX8,\n    'depth stencil': GL_DEPTH_STENCIL$1\n  }\n\n  if (extensions.ext_srgb) {\n    formatTypes['srgba'] = GL_SRGB8_ALPHA8_EXT\n  }\n\n  if (extensions.ext_color_buffer_half_float) {\n    formatTypes['rgba16f'] = GL_RGBA16F_EXT\n    formatTypes['rgb16f'] = GL_RGB16F_EXT\n  }\n\n  if (extensions.webgl_color_buffer_float) {\n    formatTypes['rgba32f'] = GL_RGBA32F_EXT\n  }\n\n  var formatTypesInvert = []\n  Object.keys(formatTypes).forEach(function (key) {\n    var val = formatTypes[key]\n    formatTypesInvert[val] = key\n  })\n\n  var renderbufferCount = 0\n  var renderbufferSet = {}\n\n  function REGLRenderbuffer (renderbuffer) {\n    this.id = renderbufferCount++\n    this.refCount = 1\n\n    this.renderbuffer = renderbuffer\n\n    this.format = GL_RGBA4$1\n    this.width = 0\n    this.height = 0\n\n    if (config.profile) {\n      this.stats = { size: 0 }\n    }\n  }\n\n  REGLRenderbuffer.prototype.decRef = function () {\n    if (--this.refCount <= 0) {\n      destroy(this)\n    }\n  }\n\n  function destroy (rb) {\n    var handle = rb.renderbuffer\n    check$1(handle, 'must not double destroy renderbuffer')\n    gl.bindRenderbuffer(GL_RENDERBUFFER, null)\n    gl.deleteRenderbuffer(handle)\n    rb.renderbuffer = null\n    rb.refCount = 0\n    delete renderbufferSet[rb.id]\n    stats.renderbufferCount--\n  }\n\n  function createRenderbuffer (a, b) {\n    var renderbuffer = new REGLRenderbuffer(gl.createRenderbuffer())\n    renderbufferSet[renderbuffer.id] = renderbuffer\n    stats.renderbufferCount++\n\n    function reglRenderbuffer (a, b) {\n      var w = 0\n      var h = 0\n      var format = GL_RGBA4$1\n\n      if (typeof a === 'object' && a) {\n        var options = a\n        if ('shape' in options) {\n          var shape = options.shape\n          check$1(Array.isArray(shape) && shape.length >= 2,\n            'invalid renderbuffer shape')\n          w = shape[0] | 0\n          h = shape[1] | 0\n        } else {\n          if ('radius' in options) {\n            w = h = options.radius | 0\n          }\n          if ('width' in options) {\n            w = options.width | 0\n          }\n          if ('height' in options) {\n            h = options.height | 0\n          }\n        }\n        if ('format' in options) {\n          check$1.parameter(options.format, formatTypes,\n            'invalid renderbuffer format')\n          format = formatTypes[options.format]\n        }\n      } else if (typeof a === 'number') {\n        w = a | 0\n        if (typeof b === 'number') {\n          h = b | 0\n        } else {\n          h = w\n        }\n      } else if (!a) {\n        w = h = 1\n      } else {\n        check$1.raise('invalid arguments to renderbuffer constructor')\n      }\n\n      // check shape\n      check$1(\n        w > 0 && h > 0 &&\n        w <= limits.maxRenderbufferSize && h <= limits.maxRenderbufferSize,\n        'invalid renderbuffer size')\n\n      if (w === renderbuffer.width &&\n          h === renderbuffer.height &&\n          format === renderbuffer.format) {\n        return\n      }\n\n      reglRenderbuffer.width = renderbuffer.width = w\n      reglRenderbuffer.height = renderbuffer.height = h\n      renderbuffer.format = format\n\n      gl.bindRenderbuffer(GL_RENDERBUFFER, renderbuffer.renderbuffer)\n      gl.renderbufferStorage(GL_RENDERBUFFER, format, w, h)\n\n      check$1(\n        gl.getError() === 0,\n        'invalid render buffer format')\n\n      if (config.profile) {\n        renderbuffer.stats.size = getRenderbufferSize(renderbuffer.format, renderbuffer.width, renderbuffer.height)\n      }\n      reglRenderbuffer.format = formatTypesInvert[renderbuffer.format]\n\n      return reglRenderbuffer\n    }\n\n    function resize (w_, h_) {\n      var w = w_ | 0\n      var h = (h_ | 0) || w\n\n      if (w === renderbuffer.width && h === renderbuffer.height) {\n        return reglRenderbuffer\n      }\n\n      // check shape\n      check$1(\n        w > 0 && h > 0 &&\n        w <= limits.maxRenderbufferSize && h <= limits.maxRenderbufferSize,\n        'invalid renderbuffer size')\n\n      reglRenderbuffer.width = renderbuffer.width = w\n      reglRenderbuffer.height = renderbuffer.height = h\n\n      gl.bindRenderbuffer(GL_RENDERBUFFER, renderbuffer.renderbuffer)\n      gl.renderbufferStorage(GL_RENDERBUFFER, renderbuffer.format, w, h)\n\n      check$1(\n        gl.getError() === 0,\n        'invalid render buffer format')\n\n      // also, recompute size.\n      if (config.profile) {\n        renderbuffer.stats.size = getRenderbufferSize(\n          renderbuffer.format, renderbuffer.width, renderbuffer.height)\n      }\n\n      return reglRenderbuffer\n    }\n\n    reglRenderbuffer(a, b)\n\n    reglRenderbuffer.resize = resize\n    reglRenderbuffer._reglType = 'renderbuffer'\n    reglRenderbuffer._renderbuffer = renderbuffer\n    if (config.profile) {\n      reglRenderbuffer.stats = renderbuffer.stats\n    }\n    reglRenderbuffer.destroy = function () {\n      renderbuffer.decRef()\n    }\n\n    return reglRenderbuffer\n  }\n\n  if (config.profile) {\n    stats.getTotalRenderbufferSize = function () {\n      var total = 0\n      Object.keys(renderbufferSet).forEach(function (key) {\n        total += renderbufferSet[key].stats.size\n      })\n      return total\n    }\n  }\n\n  function restoreRenderbuffers () {\n    values(renderbufferSet).forEach(function (rb) {\n      rb.renderbuffer = gl.createRenderbuffer()\n      gl.bindRenderbuffer(GL_RENDERBUFFER, rb.renderbuffer)\n      gl.renderbufferStorage(GL_RENDERBUFFER, rb.format, rb.width, rb.height)\n    })\n    gl.bindRenderbuffer(GL_RENDERBUFFER, null)\n  }\n\n  return {\n    create: createRenderbuffer,\n    clear: function () {\n      values(renderbufferSet).forEach(destroy)\n    },\n    restore: restoreRenderbuffers\n  }\n}\n\n// We store these constants so that the minifier can inline them\nvar GL_FRAMEBUFFER$1 = 0x8D40\nvar GL_RENDERBUFFER$1 = 0x8D41\n\nvar GL_TEXTURE_2D$2 = 0x0DE1\nvar GL_TEXTURE_CUBE_MAP_POSITIVE_X$2 = 0x8515\n\nvar GL_COLOR_ATTACHMENT0$1 = 0x8CE0\nvar GL_DEPTH_ATTACHMENT = 0x8D00\nvar GL_STENCIL_ATTACHMENT = 0x8D20\nvar GL_DEPTH_STENCIL_ATTACHMENT = 0x821A\n\nvar GL_FRAMEBUFFER_COMPLETE$1 = 0x8CD5\nvar GL_FRAMEBUFFER_INCOMPLETE_ATTACHMENT = 0x8CD6\nvar GL_FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT = 0x8CD7\nvar GL_FRAMEBUFFER_INCOMPLETE_DIMENSIONS = 0x8CD9\nvar GL_FRAMEBUFFER_UNSUPPORTED = 0x8CDD\n\nvar GL_HALF_FLOAT_OES$2 = 0x8D61\nvar GL_UNSIGNED_BYTE$6 = 0x1401\nvar GL_FLOAT$5 = 0x1406\n\nvar GL_RGB$1 = 0x1907\nvar GL_RGBA$2 = 0x1908\n\nvar GL_DEPTH_COMPONENT$1 = 0x1902\n\nvar colorTextureFormatEnums = [\n  GL_RGB$1,\n  GL_RGBA$2\n]\n\n// for every texture format, store\n// the number of channels\nvar textureFormatChannels = []\ntextureFormatChannels[GL_RGBA$2] = 4\ntextureFormatChannels[GL_RGB$1] = 3\n\n// for every texture type, store\n// the size in bytes.\nvar textureTypeSizes = []\ntextureTypeSizes[GL_UNSIGNED_BYTE$6] = 1\ntextureTypeSizes[GL_FLOAT$5] = 4\ntextureTypeSizes[GL_HALF_FLOAT_OES$2] = 2\n\nvar GL_RGBA4$2 = 0x8056\nvar GL_RGB5_A1$2 = 0x8057\nvar GL_RGB565$2 = 0x8D62\nvar GL_DEPTH_COMPONENT16$1 = 0x81A5\nvar GL_STENCIL_INDEX8$1 = 0x8D48\nvar GL_DEPTH_STENCIL$2 = 0x84F9\n\nvar GL_SRGB8_ALPHA8_EXT$1 = 0x8C43\n\nvar GL_RGBA32F_EXT$1 = 0x8814\n\nvar GL_RGBA16F_EXT$1 = 0x881A\nvar GL_RGB16F_EXT$1 = 0x881B\n\nvar colorRenderbufferFormatEnums = [\n  GL_RGBA4$2,\n  GL_RGB5_A1$2,\n  GL_RGB565$2,\n  GL_SRGB8_ALPHA8_EXT$1,\n  GL_RGBA16F_EXT$1,\n  GL_RGB16F_EXT$1,\n  GL_RGBA32F_EXT$1\n]\n\nvar statusCode = {}\nstatusCode[GL_FRAMEBUFFER_COMPLETE$1] = 'complete'\nstatusCode[GL_FRAMEBUFFER_INCOMPLETE_ATTACHMENT] = 'incomplete attachment'\nstatusCode[GL_FRAMEBUFFER_INCOMPLETE_DIMENSIONS] = 'incomplete dimensions'\nstatusCode[GL_FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT] = 'incomplete, missing attachment'\nstatusCode[GL_FRAMEBUFFER_UNSUPPORTED] = 'unsupported'\n\nfunction wrapFBOState (\n  gl,\n  extensions,\n  limits,\n  textureState,\n  renderbufferState,\n  stats) {\n  var framebufferState = {\n    cur: null,\n    next: null,\n    dirty: false,\n    setFBO: null\n  }\n\n  var colorTextureFormats = ['rgba']\n  var colorRenderbufferFormats = ['rgba4', 'rgb565', 'rgb5 a1']\n\n  if (extensions.ext_srgb) {\n    colorRenderbufferFormats.push('srgba')\n  }\n\n  if (extensions.ext_color_buffer_half_float) {\n    colorRenderbufferFormats.push('rgba16f', 'rgb16f')\n  }\n\n  if (extensions.webgl_color_buffer_float) {\n    colorRenderbufferFormats.push('rgba32f')\n  }\n\n  var colorTypes = ['uint8']\n  if (extensions.oes_texture_half_float) {\n    colorTypes.push('half float', 'float16')\n  }\n  if (extensions.oes_texture_float) {\n    colorTypes.push('float', 'float32')\n  }\n\n  function FramebufferAttachment (target, texture, renderbuffer) {\n    this.target = target\n    this.texture = texture\n    this.renderbuffer = renderbuffer\n\n    var w = 0\n    var h = 0\n    if (texture) {\n      w = texture.width\n      h = texture.height\n    } else if (renderbuffer) {\n      w = renderbuffer.width\n      h = renderbuffer.height\n    }\n    this.width = w\n    this.height = h\n  }\n\n  function decRef (attachment) {\n    if (attachment) {\n      if (attachment.texture) {\n        attachment.texture._texture.decRef()\n      }\n      if (attachment.renderbuffer) {\n        attachment.renderbuffer._renderbuffer.decRef()\n      }\n    }\n  }\n\n  function incRefAndCheckShape (attachment, width, height) {\n    if (!attachment) {\n      return\n    }\n    if (attachment.texture) {\n      var texture = attachment.texture._texture\n      var tw = Math.max(1, texture.width)\n      var th = Math.max(1, texture.height)\n      check$1(tw === width && th === height,\n        'inconsistent width/height for supplied texture')\n      texture.refCount += 1\n    } else {\n      var renderbuffer = attachment.renderbuffer._renderbuffer\n      check$1(\n        renderbuffer.width === width && renderbuffer.height === height,\n        'inconsistent width/height for renderbuffer')\n      renderbuffer.refCount += 1\n    }\n  }\n\n  function attach (location, attachment) {\n    if (attachment) {\n      if (attachment.texture) {\n        gl.framebufferTexture2D(\n          GL_FRAMEBUFFER$1,\n          location,\n          attachment.target,\n          attachment.texture._texture.texture,\n          0)\n      } else {\n        gl.framebufferRenderbuffer(\n          GL_FRAMEBUFFER$1,\n          location,\n          GL_RENDERBUFFER$1,\n          attachment.renderbuffer._renderbuffer.renderbuffer)\n      }\n    }\n  }\n\n  function parseAttachment (attachment) {\n    var target = GL_TEXTURE_2D$2\n    var texture = null\n    var renderbuffer = null\n\n    var data = attachment\n    if (typeof attachment === 'object') {\n      data = attachment.data\n      if ('target' in attachment) {\n        target = attachment.target | 0\n      }\n    }\n\n    check$1.type(data, 'function', 'invalid attachment data')\n\n    var type = data._reglType\n    if (type === 'texture2d') {\n      texture = data\n      check$1(target === GL_TEXTURE_2D$2)\n    } else if (type === 'textureCube') {\n      texture = data\n      check$1(\n        target >= GL_TEXTURE_CUBE_MAP_POSITIVE_X$2 &&\n        target < GL_TEXTURE_CUBE_MAP_POSITIVE_X$2 + 6,\n        'invalid cube map target')\n    } else if (type === 'renderbuffer') {\n      renderbuffer = data\n      target = GL_RENDERBUFFER$1\n    } else {\n      check$1.raise('invalid regl object for attachment')\n    }\n\n    return new FramebufferAttachment(target, texture, renderbuffer)\n  }\n\n  function allocAttachment (\n    width,\n    height,\n    isTexture,\n    format,\n    type) {\n    if (isTexture) {\n      var texture = textureState.create2D({\n        width: width,\n        height: height,\n        format: format,\n        type: type\n      })\n      texture._texture.refCount = 0\n      return new FramebufferAttachment(GL_TEXTURE_2D$2, texture, null)\n    } else {\n      var rb = renderbufferState.create({\n        width: width,\n        height: height,\n        format: format\n      })\n      rb._renderbuffer.refCount = 0\n      return new FramebufferAttachment(GL_RENDERBUFFER$1, null, rb)\n    }\n  }\n\n  function unwrapAttachment (attachment) {\n    return attachment && (attachment.texture || attachment.renderbuffer)\n  }\n\n  function resizeAttachment (attachment, w, h) {\n    if (attachment) {\n      if (attachment.texture) {\n        attachment.texture.resize(w, h)\n      } else if (attachment.renderbuffer) {\n        attachment.renderbuffer.resize(w, h)\n      }\n      attachment.width = w\n      attachment.height = h\n    }\n  }\n\n  var framebufferCount = 0\n  var framebufferSet = {}\n\n  function REGLFramebuffer () {\n    this.id = framebufferCount++\n    framebufferSet[this.id] = this\n\n    this.framebuffer = gl.createFramebuffer()\n    this.width = 0\n    this.height = 0\n\n    this.colorAttachments = []\n    this.depthAttachment = null\n    this.stencilAttachment = null\n    this.depthStencilAttachment = null\n  }\n\n  function decFBORefs (framebuffer) {\n    framebuffer.colorAttachments.forEach(decRef)\n    decRef(framebuffer.depthAttachment)\n    decRef(framebuffer.stencilAttachment)\n    decRef(framebuffer.depthStencilAttachment)\n  }\n\n  function destroy (framebuffer) {\n    var handle = framebuffer.framebuffer\n    check$1(handle, 'must not double destroy framebuffer')\n    gl.deleteFramebuffer(handle)\n    framebuffer.framebuffer = null\n    stats.framebufferCount--\n    delete framebufferSet[framebuffer.id]\n  }\n\n  function updateFramebuffer (framebuffer) {\n    var i\n\n    gl.bindFramebuffer(GL_FRAMEBUFFER$1, framebuffer.framebuffer)\n    var colorAttachments = framebuffer.colorAttachments\n    for (i = 0; i < colorAttachments.length; ++i) {\n      attach(GL_COLOR_ATTACHMENT0$1 + i, colorAttachments[i])\n    }\n    for (i = colorAttachments.length; i < limits.maxColorAttachments; ++i) {\n      gl.framebufferTexture2D(\n        GL_FRAMEBUFFER$1,\n        GL_COLOR_ATTACHMENT0$1 + i,\n        GL_TEXTURE_2D$2,\n        null,\n        0)\n    }\n\n    gl.framebufferTexture2D(\n      GL_FRAMEBUFFER$1,\n      GL_DEPTH_STENCIL_ATTACHMENT,\n      GL_TEXTURE_2D$2,\n      null,\n      0)\n    gl.framebufferTexture2D(\n      GL_FRAMEBUFFER$1,\n      GL_DEPTH_ATTACHMENT,\n      GL_TEXTURE_2D$2,\n      null,\n      0)\n    gl.framebufferTexture2D(\n      GL_FRAMEBUFFER$1,\n      GL_STENCIL_ATTACHMENT,\n      GL_TEXTURE_2D$2,\n      null,\n      0)\n\n    attach(GL_DEPTH_ATTACHMENT, framebuffer.depthAttachment)\n    attach(GL_STENCIL_ATTACHMENT, framebuffer.stencilAttachment)\n    attach(GL_DEPTH_STENCIL_ATTACHMENT, framebuffer.depthStencilAttachment)\n\n    // Check status code\n    var status = gl.checkFramebufferStatus(GL_FRAMEBUFFER$1)\n    if (!gl.isContextLost() && status !== GL_FRAMEBUFFER_COMPLETE$1) {\n      check$1.raise('framebuffer configuration not supported, status = ' +\n        statusCode[status])\n    }\n\n    gl.bindFramebuffer(GL_FRAMEBUFFER$1, framebufferState.next ? framebufferState.next.framebuffer : null)\n    framebufferState.cur = framebufferState.next\n\n    // FIXME: Clear error code here.  This is a work around for a bug in\n    // headless-gl\n    gl.getError()\n  }\n\n  function createFBO (a0, a1) {\n    var framebuffer = new REGLFramebuffer()\n    stats.framebufferCount++\n\n    function reglFramebuffer (a, b) {\n      var i\n\n      check$1(framebufferState.next !== framebuffer,\n        'can not update framebuffer which is currently in use')\n\n      var width = 0\n      var height = 0\n\n      var needsDepth = true\n      var needsStencil = true\n\n      var colorBuffer = null\n      var colorTexture = true\n      var colorFormat = 'rgba'\n      var colorType = 'uint8'\n      var colorCount = 1\n\n      var depthBuffer = null\n      var stencilBuffer = null\n      var depthStencilBuffer = null\n      var depthStencilTexture = false\n\n      if (typeof a === 'number') {\n        width = a | 0\n        height = (b | 0) || width\n      } else if (!a) {\n        width = height = 1\n      } else {\n        check$1.type(a, 'object', 'invalid arguments for framebuffer')\n        var options = a\n\n        if ('shape' in options) {\n          var shape = options.shape\n          check$1(Array.isArray(shape) && shape.length >= 2,\n            'invalid shape for framebuffer')\n          width = shape[0]\n          height = shape[1]\n        } else {\n          if ('radius' in options) {\n            width = height = options.radius\n          }\n          if ('width' in options) {\n            width = options.width\n          }\n          if ('height' in options) {\n            height = options.height\n          }\n        }\n\n        if ('color' in options ||\n            'colors' in options) {\n          colorBuffer =\n            options.color ||\n            options.colors\n          if (Array.isArray(colorBuffer)) {\n            check$1(\n              colorBuffer.length === 1 || extensions.webgl_draw_buffers,\n              'multiple render targets not supported')\n          }\n        }\n\n        if (!colorBuffer) {\n          if ('colorCount' in options) {\n            colorCount = options.colorCount | 0\n            check$1(colorCount > 0, 'invalid color buffer count')\n          }\n\n          if ('colorTexture' in options) {\n            colorTexture = !!options.colorTexture\n            colorFormat = 'rgba4'\n          }\n\n          if ('colorType' in options) {\n            colorType = options.colorType\n            if (!colorTexture) {\n              if (colorType === 'half float' || colorType === 'float16') {\n                check$1(extensions.ext_color_buffer_half_float,\n                  'you must enable EXT_color_buffer_half_float to use 16-bit render buffers')\n                colorFormat = 'rgba16f'\n              } else if (colorType === 'float' || colorType === 'float32') {\n                check$1(extensions.webgl_color_buffer_float,\n                  'you must enable WEBGL_color_buffer_float in order to use 32-bit floating point renderbuffers')\n                colorFormat = 'rgba32f'\n              }\n            } else {\n              check$1(extensions.oes_texture_float ||\n                !(colorType === 'float' || colorType === 'float32'),\n              'you must enable OES_texture_float in order to use floating point framebuffer objects')\n              check$1(extensions.oes_texture_half_float ||\n                !(colorType === 'half float' || colorType === 'float16'),\n              'you must enable OES_texture_half_float in order to use 16-bit floating point framebuffer objects')\n            }\n            check$1.oneOf(colorType, colorTypes, 'invalid color type')\n          }\n\n          if ('colorFormat' in options) {\n            colorFormat = options.colorFormat\n            if (colorTextureFormats.indexOf(colorFormat) >= 0) {\n              colorTexture = true\n            } else if (colorRenderbufferFormats.indexOf(colorFormat) >= 0) {\n              colorTexture = false\n            } else {\n              if (colorTexture) {\n                check$1.oneOf(\n                  options.colorFormat, colorTextureFormats,\n                  'invalid color format for texture')\n              } else {\n                check$1.oneOf(\n                  options.colorFormat, colorRenderbufferFormats,\n                  'invalid color format for renderbuffer')\n              }\n            }\n          }\n        }\n\n        if ('depthTexture' in options || 'depthStencilTexture' in options) {\n          depthStencilTexture = !!(options.depthTexture ||\n            options.depthStencilTexture)\n          check$1(!depthStencilTexture || extensions.webgl_depth_texture,\n            'webgl_depth_texture extension not supported')\n        }\n\n        if ('depth' in options) {\n          if (typeof options.depth === 'boolean') {\n            needsDepth = options.depth\n          } else {\n            depthBuffer = options.depth\n            needsStencil = false\n          }\n        }\n\n        if ('stencil' in options) {\n          if (typeof options.stencil === 'boolean') {\n            needsStencil = options.stencil\n          } else {\n            stencilBuffer = options.stencil\n            needsDepth = false\n          }\n        }\n\n        if ('depthStencil' in options) {\n          if (typeof options.depthStencil === 'boolean') {\n            needsDepth = needsStencil = options.depthStencil\n          } else {\n            depthStencilBuffer = options.depthStencil\n            needsDepth = false\n            needsStencil = false\n          }\n        }\n      }\n\n      // parse attachments\n      var colorAttachments = null\n      var depthAttachment = null\n      var stencilAttachment = null\n      var depthStencilAttachment = null\n\n      // Set up color attachments\n      if (Array.isArray(colorBuffer)) {\n        colorAttachments = colorBuffer.map(parseAttachment)\n      } else if (colorBuffer) {\n        colorAttachments = [parseAttachment(colorBuffer)]\n      } else {\n        colorAttachments = new Array(colorCount)\n        for (i = 0; i < colorCount; ++i) {\n          colorAttachments[i] = allocAttachment(\n            width,\n            height,\n            colorTexture,\n            colorFormat,\n            colorType)\n        }\n      }\n\n      check$1(extensions.webgl_draw_buffers || colorAttachments.length <= 1,\n        'you must enable the WEBGL_draw_buffers extension in order to use multiple color buffers.')\n      check$1(colorAttachments.length <= limits.maxColorAttachments,\n        'too many color attachments, not supported')\n\n      width = width || colorAttachments[0].width\n      height = height || colorAttachments[0].height\n\n      if (depthBuffer) {\n        depthAttachment = parseAttachment(depthBuffer)\n      } else if (needsDepth && !needsStencil) {\n        depthAttachment = allocAttachment(\n          width,\n          height,\n          depthStencilTexture,\n          'depth',\n          'uint32')\n      }\n\n      if (stencilBuffer) {\n        stencilAttachment = parseAttachment(stencilBuffer)\n      } else if (needsStencil && !needsDepth) {\n        stencilAttachment = allocAttachment(\n          width,\n          height,\n          false,\n          'stencil',\n          'uint8')\n      }\n\n      if (depthStencilBuffer) {\n        depthStencilAttachment = parseAttachment(depthStencilBuffer)\n      } else if (!depthBuffer && !stencilBuffer && needsStencil && needsDepth) {\n        depthStencilAttachment = allocAttachment(\n          width,\n          height,\n          depthStencilTexture,\n          'depth stencil',\n          'depth stencil')\n      }\n\n      check$1(\n        (!!depthBuffer) + (!!stencilBuffer) + (!!depthStencilBuffer) <= 1,\n        'invalid framebuffer configuration, can specify exactly one depth/stencil attachment')\n\n      var commonColorAttachmentSize = null\n\n      for (i = 0; i < colorAttachments.length; ++i) {\n        incRefAndCheckShape(colorAttachments[i], width, height)\n        check$1(!colorAttachments[i] ||\n          (colorAttachments[i].texture &&\n            colorTextureFormatEnums.indexOf(colorAttachments[i].texture._texture.format) >= 0) ||\n          (colorAttachments[i].renderbuffer &&\n            colorRenderbufferFormatEnums.indexOf(colorAttachments[i].renderbuffer._renderbuffer.format) >= 0),\n        'framebuffer color attachment ' + i + ' is invalid')\n\n        if (colorAttachments[i] && colorAttachments[i].texture) {\n          var colorAttachmentSize =\n              textureFormatChannels[colorAttachments[i].texture._texture.format] *\n              textureTypeSizes[colorAttachments[i].texture._texture.type]\n\n          if (commonColorAttachmentSize === null) {\n            commonColorAttachmentSize = colorAttachmentSize\n          } else {\n            // We need to make sure that all color attachments have the same number of bitplanes\n            // (that is, the same numer of bits per pixel)\n            // This is required by the GLES2.0 standard. See the beginning of Chapter 4 in that document.\n            check$1(commonColorAttachmentSize === colorAttachmentSize,\n              'all color attachments much have the same number of bits per pixel.')\n          }\n        }\n      }\n      incRefAndCheckShape(depthAttachment, width, height)\n      check$1(!depthAttachment ||\n        (depthAttachment.texture &&\n          depthAttachment.texture._texture.format === GL_DEPTH_COMPONENT$1) ||\n        (depthAttachment.renderbuffer &&\n          depthAttachment.renderbuffer._renderbuffer.format === GL_DEPTH_COMPONENT16$1),\n      'invalid depth attachment for framebuffer object')\n      incRefAndCheckShape(stencilAttachment, width, height)\n      check$1(!stencilAttachment ||\n        (stencilAttachment.renderbuffer &&\n          stencilAttachment.renderbuffer._renderbuffer.format === GL_STENCIL_INDEX8$1),\n      'invalid stencil attachment for framebuffer object')\n      incRefAndCheckShape(depthStencilAttachment, width, height)\n      check$1(!depthStencilAttachment ||\n        (depthStencilAttachment.texture &&\n          depthStencilAttachment.texture._texture.format === GL_DEPTH_STENCIL$2) ||\n        (depthStencilAttachment.renderbuffer &&\n          depthStencilAttachment.renderbuffer._renderbuffer.format === GL_DEPTH_STENCIL$2),\n      'invalid depth-stencil attachment for framebuffer object')\n\n      // decrement references\n      decFBORefs(framebuffer)\n\n      framebuffer.width = width\n      framebuffer.height = height\n\n      framebuffer.colorAttachments = colorAttachments\n      framebuffer.depthAttachment = depthAttachment\n      framebuffer.stencilAttachment = stencilAttachment\n      framebuffer.depthStencilAttachment = depthStencilAttachment\n\n      reglFramebuffer.color = colorAttachments.map(unwrapAttachment)\n      reglFramebuffer.depth = unwrapAttachment(depthAttachment)\n      reglFramebuffer.stencil = unwrapAttachment(stencilAttachment)\n      reglFramebuffer.depthStencil = unwrapAttachment(depthStencilAttachment)\n\n      reglFramebuffer.width = framebuffer.width\n      reglFramebuffer.height = framebuffer.height\n\n      updateFramebuffer(framebuffer)\n\n      return reglFramebuffer\n    }\n\n    function resize (w_, h_) {\n      check$1(framebufferState.next !== framebuffer,\n        'can not resize a framebuffer which is currently in use')\n\n      var w = Math.max(w_ | 0, 1)\n      var h = Math.max((h_ | 0) || w, 1)\n      if (w === framebuffer.width && h === framebuffer.height) {\n        return reglFramebuffer\n      }\n\n      // resize all buffers\n      var colorAttachments = framebuffer.colorAttachments\n      for (var i = 0; i < colorAttachments.length; ++i) {\n        resizeAttachment(colorAttachments[i], w, h)\n      }\n      resizeAttachment(framebuffer.depthAttachment, w, h)\n      resizeAttachment(framebuffer.stencilAttachment, w, h)\n      resizeAttachment(framebuffer.depthStencilAttachment, w, h)\n\n      framebuffer.width = reglFramebuffer.width = w\n      framebuffer.height = reglFramebuffer.height = h\n\n      updateFramebuffer(framebuffer)\n\n      return reglFramebuffer\n    }\n\n    reglFramebuffer(a0, a1)\n\n    return extend(reglFramebuffer, {\n      resize: resize,\n      _reglType: 'framebuffer',\n      _framebuffer: framebuffer,\n      destroy: function () {\n        destroy(framebuffer)\n        decFBORefs(framebuffer)\n      },\n      use: function (block) {\n        framebufferState.setFBO({\n          framebuffer: reglFramebuffer\n        }, block)\n      }\n    })\n  }\n\n  function createCubeFBO (options) {\n    var faces = Array(6)\n\n    function reglFramebufferCube (a) {\n      var i\n\n      check$1(faces.indexOf(framebufferState.next) < 0,\n        'can not update framebuffer which is currently in use')\n\n      var params = {\n        color: null\n      }\n\n      var radius = 0\n\n      var colorBuffer = null\n      var colorFormat = 'rgba'\n      var colorType = 'uint8'\n      var colorCount = 1\n\n      if (typeof a === 'number') {\n        radius = a | 0\n      } else if (!a) {\n        radius = 1\n      } else {\n        check$1.type(a, 'object', 'invalid arguments for framebuffer')\n        var options = a\n\n        if ('shape' in options) {\n          var shape = options.shape\n          check$1(\n            Array.isArray(shape) && shape.length >= 2,\n            'invalid shape for framebuffer')\n          check$1(\n            shape[0] === shape[1],\n            'cube framebuffer must be square')\n          radius = shape[0]\n        } else {\n          if ('radius' in options) {\n            radius = options.radius | 0\n          }\n          if ('width' in options) {\n            radius = options.width | 0\n            if ('height' in options) {\n              check$1(options.height === radius, 'must be square')\n            }\n          } else if ('height' in options) {\n            radius = options.height | 0\n          }\n        }\n\n        if ('color' in options ||\n            'colors' in options) {\n          colorBuffer =\n            options.color ||\n            options.colors\n          if (Array.isArray(colorBuffer)) {\n            check$1(\n              colorBuffer.length === 1 || extensions.webgl_draw_buffers,\n              'multiple render targets not supported')\n          }\n        }\n\n        if (!colorBuffer) {\n          if ('colorCount' in options) {\n            colorCount = options.colorCount | 0\n            check$1(colorCount > 0, 'invalid color buffer count')\n          }\n\n          if ('colorType' in options) {\n            check$1.oneOf(\n              options.colorType, colorTypes,\n              'invalid color type')\n            colorType = options.colorType\n          }\n\n          if ('colorFormat' in options) {\n            colorFormat = options.colorFormat\n            check$1.oneOf(\n              options.colorFormat, colorTextureFormats,\n              'invalid color format for texture')\n          }\n        }\n\n        if ('depth' in options) {\n          params.depth = options.depth\n        }\n\n        if ('stencil' in options) {\n          params.stencil = options.stencil\n        }\n\n        if ('depthStencil' in options) {\n          params.depthStencil = options.depthStencil\n        }\n      }\n\n      var colorCubes\n      if (colorBuffer) {\n        if (Array.isArray(colorBuffer)) {\n          colorCubes = []\n          for (i = 0; i < colorBuffer.length; ++i) {\n            colorCubes[i] = colorBuffer[i]\n          }\n        } else {\n          colorCubes = [ colorBuffer ]\n        }\n      } else {\n        colorCubes = Array(colorCount)\n        var cubeMapParams = {\n          radius: radius,\n          format: colorFormat,\n          type: colorType\n        }\n        for (i = 0; i < colorCount; ++i) {\n          colorCubes[i] = textureState.createCube(cubeMapParams)\n        }\n      }\n\n      // Check color cubes\n      params.color = Array(colorCubes.length)\n      for (i = 0; i < colorCubes.length; ++i) {\n        var cube = colorCubes[i]\n        check$1(\n          typeof cube === 'function' && cube._reglType === 'textureCube',\n          'invalid cube map')\n        radius = radius || cube.width\n        check$1(\n          cube.width === radius && cube.height === radius,\n          'invalid cube map shape')\n        params.color[i] = {\n          target: GL_TEXTURE_CUBE_MAP_POSITIVE_X$2,\n          data: colorCubes[i]\n        }\n      }\n\n      for (i = 0; i < 6; ++i) {\n        for (var j = 0; j < colorCubes.length; ++j) {\n          params.color[j].target = GL_TEXTURE_CUBE_MAP_POSITIVE_X$2 + i\n        }\n        // reuse depth-stencil attachments across all cube maps\n        if (i > 0) {\n          params.depth = faces[0].depth\n          params.stencil = faces[0].stencil\n          params.depthStencil = faces[0].depthStencil\n        }\n        if (faces[i]) {\n          (faces[i])(params)\n        } else {\n          faces[i] = createFBO(params)\n        }\n      }\n\n      return extend(reglFramebufferCube, {\n        width: radius,\n        height: radius,\n        color: colorCubes\n      })\n    }\n\n    function resize (radius_) {\n      var i\n      var radius = radius_ | 0\n      check$1(radius > 0 && radius <= limits.maxCubeMapSize,\n        'invalid radius for cube fbo')\n\n      if (radius === reglFramebufferCube.width) {\n        return reglFramebufferCube\n      }\n\n      var colors = reglFramebufferCube.color\n      for (i = 0; i < colors.length; ++i) {\n        colors[i].resize(radius)\n      }\n\n      for (i = 0; i < 6; ++i) {\n        faces[i].resize(radius)\n      }\n\n      reglFramebufferCube.width = reglFramebufferCube.height = radius\n\n      return reglFramebufferCube\n    }\n\n    reglFramebufferCube(options)\n\n    return extend(reglFramebufferCube, {\n      faces: faces,\n      resize: resize,\n      _reglType: 'framebufferCube',\n      destroy: function () {\n        faces.forEach(function (f) {\n          f.destroy()\n        })\n      }\n    })\n  }\n\n  function restoreFramebuffers () {\n    framebufferState.cur = null\n    framebufferState.next = null\n    framebufferState.dirty = true\n    values(framebufferSet).forEach(function (fb) {\n      fb.framebuffer = gl.createFramebuffer()\n      updateFramebuffer(fb)\n    })\n  }\n\n  return extend(framebufferState, {\n    getFramebuffer: function (object) {\n      if (typeof object === 'function' && object._reglType === 'framebuffer') {\n        var fbo = object._framebuffer\n        if (fbo instanceof REGLFramebuffer) {\n          return fbo\n        }\n      }\n      return null\n    },\n    create: createFBO,\n    createCube: createCubeFBO,\n    clear: function () {\n      values(framebufferSet).forEach(destroy)\n    },\n    restore: restoreFramebuffers\n  })\n}\n\nvar GL_FLOAT$6 = 5126\nvar GL_ARRAY_BUFFER$1 = 34962\n\nfunction AttributeRecord () {\n  this.state = 0\n\n  this.x = 0.0\n  this.y = 0.0\n  this.z = 0.0\n  this.w = 0.0\n\n  this.buffer = null\n  this.size = 0\n  this.normalized = false\n  this.type = GL_FLOAT$6\n  this.offset = 0\n  this.stride = 0\n  this.divisor = 0\n}\n\nfunction wrapAttributeState (\n  gl,\n  extensions,\n  limits,\n  stats,\n  bufferState) {\n  var NUM_ATTRIBUTES = limits.maxAttributes\n  var attributeBindings = new Array(NUM_ATTRIBUTES)\n  for (var i = 0; i < NUM_ATTRIBUTES; ++i) {\n    attributeBindings[i] = new AttributeRecord()\n  }\n  var vaoCount = 0\n  var vaoSet = {}\n\n  var state = {\n    Record: AttributeRecord,\n    scope: {},\n    state: attributeBindings,\n    currentVAO: null,\n    targetVAO: null,\n    restore: extVAO() ? restoreVAO : function () {},\n    createVAO: createVAO,\n    getVAO: getVAO,\n    destroyBuffer: destroyBuffer,\n    setVAO: extVAO() ? setVAOEXT : setVAOEmulated,\n    clear: extVAO() ? destroyVAOEXT : function () {}\n  }\n\n  function destroyBuffer (buffer) {\n    for (var i = 0; i < attributeBindings.length; ++i) {\n      var record = attributeBindings[i]\n      if (record.buffer === buffer) {\n        gl.disableVertexAttribArray(i)\n        record.buffer = null\n      }\n    }\n  }\n\n  function extVAO () {\n    return extensions.oes_vertex_array_object\n  }\n\n  function extInstanced () {\n    return extensions.angle_instanced_arrays\n  }\n\n  function getVAO (vao) {\n    if (typeof vao === 'function' && vao._vao) {\n      return vao._vao\n    }\n    return null\n  }\n\n  function setVAOEXT (vao) {\n    if (vao === state.currentVAO) {\n      return\n    }\n    var ext = extVAO()\n    if (vao) {\n      ext.bindVertexArrayOES(vao.vao)\n    } else {\n      ext.bindVertexArrayOES(null)\n    }\n    state.currentVAO = vao\n  }\n\n  function setVAOEmulated (vao) {\n    if (vao === state.currentVAO) {\n      return\n    }\n    if (vao) {\n      vao.bindAttrs()\n    } else {\n      var exti = extInstanced()\n      for (var i = 0; i < attributeBindings.length; ++i) {\n        var binding = attributeBindings[i]\n        if (binding.buffer) {\n          gl.enableVertexAttribArray(i)\n          gl.vertexAttribPointer(i, binding.size, binding.type, binding.normalized, binding.stride, binding.offfset)\n          if (exti && binding.divisor) {\n            exti.vertexAttribDivisorANGLE(i, binding.divisor)\n          }\n        } else {\n          gl.disableVertexAttribArray(i)\n          gl.vertexAttrib4f(i, binding.x, binding.y, binding.z, binding.w)\n        }\n      }\n    }\n    state.currentVAO = vao\n  }\n\n  function destroyVAOEXT () {\n    values(vaoSet).forEach(function (vao) {\n      vao.destroy()\n    })\n  }\n\n  function REGLVAO () {\n    this.id = ++vaoCount\n    this.attributes = []\n    var extension = extVAO()\n    if (extension) {\n      this.vao = extension.createVertexArrayOES()\n    } else {\n      this.vao = null\n    }\n    vaoSet[this.id] = this\n    this.buffers = []\n  }\n\n  REGLVAO.prototype.bindAttrs = function () {\n    var exti = extInstanced()\n    var attributes = this.attributes\n    for (var i = 0; i < attributes.length; ++i) {\n      var attr = attributes[i]\n      if (attr.buffer) {\n        gl.enableVertexAttribArray(i)\n        gl.bindBuffer(GL_ARRAY_BUFFER$1, attr.buffer.buffer)\n        gl.vertexAttribPointer(i, attr.size, attr.type, attr.normalized, attr.stride, attr.offset)\n        if (exti && attr.divisor) {\n          exti.vertexAttribDivisorANGLE(i, attr.divisor)\n        }\n      } else {\n        gl.disableVertexAttribArray(i)\n        gl.vertexAttrib4f(i, attr.x, attr.y, attr.z, attr.w)\n      }\n    }\n    for (var j = attributes.length; j < NUM_ATTRIBUTES; ++j) {\n      gl.disableVertexAttribArray(j)\n    }\n  }\n\n  REGLVAO.prototype.refresh = function () {\n    var ext = extVAO()\n    if (ext) {\n      ext.bindVertexArrayOES(this.vao)\n      this.bindAttrs()\n      state.currentVAO = this\n    }\n  }\n\n  REGLVAO.prototype.destroy = function () {\n    if (this.vao) {\n      var extension = extVAO()\n      if (this === state.currentVAO) {\n        state.currentVAO = null\n        extension.bindVertexArrayOES(null)\n      }\n      extension.deleteVertexArrayOES(this.vao)\n      this.vao = null\n    }\n    if (vaoSet[this.id]) {\n      delete vaoSet[this.id]\n      stats.vaoCount -= 1\n    }\n  }\n\n  function restoreVAO () {\n    var ext = extVAO()\n    if (ext) {\n      values(vaoSet).forEach(function (vao) {\n        vao.refresh()\n      })\n    }\n  }\n\n  function createVAO (_attr) {\n    var vao = new REGLVAO()\n    stats.vaoCount += 1\n\n    function updateVAO (attributes) {\n      check$1(Array.isArray(attributes), 'arguments to vertex array constructor must be an array')\n      check$1(attributes.length < NUM_ATTRIBUTES, 'too many attributes')\n      check$1(attributes.length > 0, 'must specify at least one attribute')\n\n      var bufUpdated = {}\n      var nattributes = vao.attributes\n      nattributes.length = attributes.length\n      for (var i = 0; i < attributes.length; ++i) {\n        var spec = attributes[i]\n        var rec = nattributes[i] = new AttributeRecord()\n        var data = spec.data || spec\n        if (Array.isArray(data) || isTypedArray(data) || isNDArrayLike(data)) {\n          var buf\n          if (vao.buffers[i]) {\n            buf = vao.buffers[i]\n            if (isTypedArray(data) && buf._buffer.byteLength >= data.byteLength) {\n              buf.subdata(data)\n            } else {\n              buf.destroy()\n              vao.buffers[i] = null\n            }\n          }\n          if (!vao.buffers[i]) {\n            buf = vao.buffers[i] = bufferState.create(spec, GL_ARRAY_BUFFER$1, false, true)\n          }\n          rec.buffer = bufferState.getBuffer(buf)\n          rec.size = rec.buffer.dimension | 0\n          rec.normalized = false\n          rec.type = rec.buffer.dtype\n          rec.offset = 0\n          rec.stride = 0\n          rec.divisor = 0\n          rec.state = 1\n          bufUpdated[i] = 1\n        } else if (bufferState.getBuffer(spec)) {\n          rec.buffer = bufferState.getBuffer(spec)\n          rec.size = rec.buffer.dimension | 0\n          rec.normalized = false\n          rec.type = rec.buffer.dtype\n          rec.offset = 0\n          rec.stride = 0\n          rec.divisor = 0\n          rec.state = 1\n        } else if (bufferState.getBuffer(spec.buffer)) {\n          rec.buffer = bufferState.getBuffer(spec.buffer)\n          rec.size = ((+spec.size) || rec.buffer.dimension) | 0\n          rec.normalized = !!spec.normalized || false\n          if ('type' in spec) {\n            check$1.parameter(spec.type, glTypes, 'invalid buffer type')\n            rec.type = glTypes[spec.type]\n          } else {\n            rec.type = rec.buffer.dtype\n          }\n          rec.offset = (spec.offset || 0) | 0\n          rec.stride = (spec.stride || 0) | 0\n          rec.divisor = (spec.divisor || 0) | 0\n          rec.state = 1\n\n          check$1(rec.size >= 1 && rec.size <= 4, 'size must be between 1 and 4')\n          check$1(rec.offset >= 0, 'invalid offset')\n          check$1(rec.stride >= 0 && rec.stride <= 255, 'stride must be between 0 and 255')\n          check$1(rec.divisor >= 0, 'divisor must be positive')\n          check$1(!rec.divisor || !!extensions.angle_instanced_arrays, 'ANGLE_instanced_arrays must be enabled to use divisor')\n        } else if ('x' in spec) {\n          check$1(i > 0, 'first attribute must not be a constant')\n          rec.x = +spec.x || 0\n          rec.y = +spec.y || 0\n          rec.z = +spec.z || 0\n          rec.w = +spec.w || 0\n          rec.state = 2\n        } else {\n          check$1(false, 'invalid attribute spec for location ' + i)\n        }\n      }\n\n      // retire unused buffers\n      for (var j = 0; j < vao.buffers.length; ++j) {\n        if (!bufUpdated[j] && vao.buffers[j]) {\n          vao.buffers[j].destroy()\n          vao.buffers[j] = null\n        }\n      }\n\n      vao.refresh()\n      return updateVAO\n    }\n\n    updateVAO.destroy = function () {\n      for (var j = 0; j < vao.buffers.length; ++j) {\n        if (vao.buffers[j]) {\n          vao.buffers[j].destroy()\n        }\n      }\n      vao.buffers.length = 0\n      vao.destroy()\n    }\n\n    updateVAO._vao = vao\n    updateVAO._reglType = 'vao'\n\n    return updateVAO(_attr)\n  }\n\n  return state\n}\n\nvar GL_FRAGMENT_SHADER = 35632\nvar GL_VERTEX_SHADER = 35633\n\nvar GL_ACTIVE_UNIFORMS = 0x8B86\nvar GL_ACTIVE_ATTRIBUTES = 0x8B89\n\nfunction wrapShaderState (gl, stringStore, stats, config) {\n  // ===================================================\n  // glsl compilation and linking\n  // ===================================================\n  var fragShaders = {}\n  var vertShaders = {}\n\n  function ActiveInfo (name, id, location, info) {\n    this.name = name\n    this.id = id\n    this.location = location\n    this.info = info\n  }\n\n  function insertActiveInfo (list, info) {\n    for (var i = 0; i < list.length; ++i) {\n      if (list[i].id === info.id) {\n        list[i].location = info.location\n        return\n      }\n    }\n    list.push(info)\n  }\n\n  function getShader (type, id, command) {\n    var cache = type === GL_FRAGMENT_SHADER ? fragShaders : vertShaders\n    var shader = cache[id]\n\n    if (!shader) {\n      var source = stringStore.str(id)\n      shader = gl.createShader(type)\n      gl.shaderSource(shader, source)\n      gl.compileShader(shader)\n      check$1.shaderError(gl, shader, source, type, command)\n      cache[id] = shader\n    }\n\n    return shader\n  }\n\n  // ===================================================\n  // program linking\n  // ===================================================\n  var programCache = {}\n  var programList = []\n\n  var PROGRAM_COUNTER = 0\n\n  function REGLProgram (fragId, vertId) {\n    this.id = PROGRAM_COUNTER++\n    this.fragId = fragId\n    this.vertId = vertId\n    this.program = null\n    this.uniforms = []\n    this.attributes = []\n    this.refCount = 1\n\n    if (config.profile) {\n      this.stats = {\n        uniformsCount: 0,\n        attributesCount: 0\n      }\n    }\n  }\n\n  function linkProgram (desc, command, attributeLocations) {\n    var i, info\n\n    // -------------------------------\n    // compile & link\n    // -------------------------------\n    var fragShader = getShader(GL_FRAGMENT_SHADER, desc.fragId)\n    var vertShader = getShader(GL_VERTEX_SHADER, desc.vertId)\n\n    var program = desc.program = gl.createProgram()\n    gl.attachShader(program, fragShader)\n    gl.attachShader(program, vertShader)\n    if (attributeLocations) {\n      for (i = 0; i < attributeLocations.length; ++i) {\n        var binding = attributeLocations[i]\n        gl.bindAttribLocation(program, binding[0], binding[1])\n      }\n    }\n\n    gl.linkProgram(program)\n    check$1.linkError(\n      gl,\n      program,\n      stringStore.str(desc.fragId),\n      stringStore.str(desc.vertId),\n      command)\n\n    // -------------------------------\n    // grab uniforms\n    // -------------------------------\n    var numUniforms = gl.getProgramParameter(program, GL_ACTIVE_UNIFORMS)\n    if (config.profile) {\n      desc.stats.uniformsCount = numUniforms\n    }\n    var uniforms = desc.uniforms\n    for (i = 0; i < numUniforms; ++i) {\n      info = gl.getActiveUniform(program, i)\n      if (info) {\n        if (info.size > 1) {\n          for (var j = 0; j < info.size; ++j) {\n            var name = info.name.replace('[0]', '[' + j + ']')\n            insertActiveInfo(uniforms, new ActiveInfo(\n              name,\n              stringStore.id(name),\n              gl.getUniformLocation(program, name),\n              info))\n          }\n        } else {\n          insertActiveInfo(uniforms, new ActiveInfo(\n            info.name,\n            stringStore.id(info.name),\n            gl.getUniformLocation(program, info.name),\n            info))\n        }\n      }\n    }\n\n    // -------------------------------\n    // grab attributes\n    // -------------------------------\n    var numAttributes = gl.getProgramParameter(program, GL_ACTIVE_ATTRIBUTES)\n    if (config.profile) {\n      desc.stats.attributesCount = numAttributes\n    }\n\n    var attributes = desc.attributes\n    for (i = 0; i < numAttributes; ++i) {\n      info = gl.getActiveAttrib(program, i)\n      if (info) {\n        insertActiveInfo(attributes, new ActiveInfo(\n          info.name,\n          stringStore.id(info.name),\n          gl.getAttribLocation(program, info.name),\n          info))\n      }\n    }\n  }\n\n  if (config.profile) {\n    stats.getMaxUniformsCount = function () {\n      var m = 0\n      programList.forEach(function (desc) {\n        if (desc.stats.uniformsCount > m) {\n          m = desc.stats.uniformsCount\n        }\n      })\n      return m\n    }\n\n    stats.getMaxAttributesCount = function () {\n      var m = 0\n      programList.forEach(function (desc) {\n        if (desc.stats.attributesCount > m) {\n          m = desc.stats.attributesCount\n        }\n      })\n      return m\n    }\n  }\n\n  function restoreShaders () {\n    fragShaders = {}\n    vertShaders = {}\n    for (var i = 0; i < programList.length; ++i) {\n      linkProgram(programList[i], null, programList[i].attributes.map(function (info) {\n        return [info.location, info.name]\n      }))\n    }\n  }\n\n  return {\n    clear: function () {\n      var deleteShader = gl.deleteShader.bind(gl)\n      values(fragShaders).forEach(deleteShader)\n      fragShaders = {}\n      values(vertShaders).forEach(deleteShader)\n      vertShaders = {}\n\n      programList.forEach(function (desc) {\n        gl.deleteProgram(desc.program)\n      })\n      programList.length = 0\n      programCache = {}\n\n      stats.shaderCount = 0\n    },\n\n    program: function (vertId, fragId, command, attribLocations) {\n      check$1.command(vertId >= 0, 'missing vertex shader', command)\n      check$1.command(fragId >= 0, 'missing fragment shader', command)\n\n      var cache = programCache[fragId]\n      if (!cache) {\n        cache = programCache[fragId] = {}\n      }\n      var prevProgram = cache[vertId]\n      if (prevProgram) {\n        prevProgram.refCount++\n        if (!attribLocations) {\n          return prevProgram\n        }\n      }\n      var program = new REGLProgram(fragId, vertId)\n      stats.shaderCount++\n      linkProgram(program, command, attribLocations)\n      if (!prevProgram) {\n        cache[vertId] = program\n      }\n      programList.push(program)\n      return extend(program, {\n        destroy: function () {\n          program.refCount--\n          if (program.refCount <= 0) {\n            gl.deleteProgram(program.program)\n            var idx = programList.indexOf(program)\n            programList.splice(idx, 1)\n            stats.shaderCount--\n          }\n          // no program is linked to this vert anymore\n          if (cache[program.vertId].refCount <= 0) {\n            gl.deleteShader(vertShaders[program.vertId])\n            delete vertShaders[program.vertId]\n            delete programCache[program.fragId][program.vertId]\n          }\n          // no program is linked to this frag anymore\n          if (!Object.keys(programCache[program.fragId]).length) {\n            gl.deleteShader(fragShaders[program.fragId])\n            delete fragShaders[program.fragId]\n            delete programCache[program.fragId]\n          }\n        }\n      })\n    },\n\n    restore: restoreShaders,\n\n    shader: getShader,\n\n    frag: -1,\n    vert: -1\n  }\n}\n\nvar GL_RGBA$3 = 6408\nvar GL_UNSIGNED_BYTE$7 = 5121\nvar GL_PACK_ALIGNMENT = 0x0D05\nvar GL_FLOAT$7 = 0x1406 // 5126\n\nfunction wrapReadPixels (\n  gl,\n  framebufferState,\n  reglPoll,\n  context,\n  glAttributes,\n  extensions,\n  limits) {\n  function readPixelsImpl (input) {\n    var type\n    if (framebufferState.next === null) {\n      check$1(\n        glAttributes.preserveDrawingBuffer,\n        'you must create a webgl context with \"preserveDrawingBuffer\":true in order to read pixels from the drawing buffer')\n      type = GL_UNSIGNED_BYTE$7\n    } else {\n      check$1(\n        framebufferState.next.colorAttachments[0].texture !== null,\n        'You cannot read from a renderbuffer')\n      type = framebufferState.next.colorAttachments[0].texture._texture.type\n\n      if (extensions.oes_texture_float) {\n        check$1(\n          type === GL_UNSIGNED_BYTE$7 || type === GL_FLOAT$7,\n          'Reading from a framebuffer is only allowed for the types \\'uint8\\' and \\'float\\'')\n\n        if (type === GL_FLOAT$7) {\n          check$1(limits.readFloat, 'Reading \\'float\\' values is not permitted in your browser. For a fallback, please see: https://www.npmjs.com/package/glsl-read-float')\n        }\n      } else {\n        check$1(\n          type === GL_UNSIGNED_BYTE$7,\n          'Reading from a framebuffer is only allowed for the type \\'uint8\\'')\n      }\n    }\n\n    var x = 0\n    var y = 0\n    var width = context.framebufferWidth\n    var height = context.framebufferHeight\n    var data = null\n\n    if (isTypedArray(input)) {\n      data = input\n    } else if (input) {\n      check$1.type(input, 'object', 'invalid arguments to regl.read()')\n      x = input.x | 0\n      y = input.y | 0\n      check$1(\n        x >= 0 && x < context.framebufferWidth,\n        'invalid x offset for regl.read')\n      check$1(\n        y >= 0 && y < context.framebufferHeight,\n        'invalid y offset for regl.read')\n      width = (input.width || (context.framebufferWidth - x)) | 0\n      height = (input.height || (context.framebufferHeight - y)) | 0\n      data = input.data || null\n    }\n\n    // sanity check input.data\n    if (data) {\n      if (type === GL_UNSIGNED_BYTE$7) {\n        check$1(\n          data instanceof Uint8Array,\n          'buffer must be \\'Uint8Array\\' when reading from a framebuffer of type \\'uint8\\'')\n      } else if (type === GL_FLOAT$7) {\n        check$1(\n          data instanceof Float32Array,\n          'buffer must be \\'Float32Array\\' when reading from a framebuffer of type \\'float\\'')\n      }\n    }\n\n    check$1(\n      width > 0 && width + x <= context.framebufferWidth,\n      'invalid width for read pixels')\n    check$1(\n      height > 0 && height + y <= context.framebufferHeight,\n      'invalid height for read pixels')\n\n    // Update WebGL state\n    reglPoll()\n\n    // Compute size\n    var size = width * height * 4\n\n    // Allocate data\n    if (!data) {\n      if (type === GL_UNSIGNED_BYTE$7) {\n        data = new Uint8Array(size)\n      } else if (type === GL_FLOAT$7) {\n        data = data || new Float32Array(size)\n      }\n    }\n\n    // Type check\n    check$1.isTypedArray(data, 'data buffer for regl.read() must be a typedarray')\n    check$1(data.byteLength >= size, 'data buffer for regl.read() too small')\n\n    // Run read pixels\n    gl.pixelStorei(GL_PACK_ALIGNMENT, 4)\n    gl.readPixels(x, y, width, height, GL_RGBA$3,\n      type,\n      data)\n\n    return data\n  }\n\n  function readPixelsFBO (options) {\n    var result\n    framebufferState.setFBO({\n      framebuffer: options.framebuffer\n    }, function () {\n      result = readPixelsImpl(options)\n    })\n    return result\n  }\n\n  function readPixels (options) {\n    if (!options || !('framebuffer' in options)) {\n      return readPixelsImpl(options)\n    } else {\n      return readPixelsFBO(options)\n    }\n  }\n\n  return readPixels\n}\n\nfunction slice (x) {\n  return Array.prototype.slice.call(x)\n}\n\nfunction join (x) {\n  return slice(x).join('')\n}\n\nfunction createEnvironment () {\n  // Unique variable id counter\n  var varCounter = 0\n\n  // Linked values are passed from this scope into the generated code block\n  // Calling link() passes a value into the generated scope and returns\n  // the variable name which it is bound to\n  var linkedNames = []\n  var linkedValues = []\n  function link (value) {\n    for (var i = 0; i < linkedValues.length; ++i) {\n      if (linkedValues[i] === value) {\n        return linkedNames[i]\n      }\n    }\n\n    var name = 'g' + (varCounter++)\n    linkedNames.push(name)\n    linkedValues.push(value)\n    return name\n  }\n\n  // create a code block\n  function block () {\n    var code = []\n    function push () {\n      code.push.apply(code, slice(arguments))\n    }\n\n    var vars = []\n    function def () {\n      var name = 'v' + (varCounter++)\n      vars.push(name)\n\n      if (arguments.length > 0) {\n        code.push(name, '=')\n        code.push.apply(code, slice(arguments))\n        code.push(';')\n      }\n\n      return name\n    }\n\n    return extend(push, {\n      def: def,\n      toString: function () {\n        return join([\n          (vars.length > 0 ? 'var ' + vars.join(',') + ';' : ''),\n          join(code)\n        ])\n      }\n    })\n  }\n\n  function scope () {\n    var entry = block()\n    var exit = block()\n\n    var entryToString = entry.toString\n    var exitToString = exit.toString\n\n    function save (object, prop) {\n      exit(object, prop, '=', entry.def(object, prop), ';')\n    }\n\n    return extend(function () {\n      entry.apply(entry, slice(arguments))\n    }, {\n      def: entry.def,\n      entry: entry,\n      exit: exit,\n      save: save,\n      set: function (object, prop, value) {\n        save(object, prop)\n        entry(object, prop, '=', value, ';')\n      },\n      toString: function () {\n        return entryToString() + exitToString()\n      }\n    })\n  }\n\n  function conditional () {\n    var pred = join(arguments)\n    var thenBlock = scope()\n    var elseBlock = scope()\n\n    var thenToString = thenBlock.toString\n    var elseToString = elseBlock.toString\n\n    return extend(thenBlock, {\n      then: function () {\n        thenBlock.apply(thenBlock, slice(arguments))\n        return this\n      },\n      else: function () {\n        elseBlock.apply(elseBlock, slice(arguments))\n        return this\n      },\n      toString: function () {\n        var elseClause = elseToString()\n        if (elseClause) {\n          elseClause = 'else{' + elseClause + '}'\n        }\n        return join([\n          'if(', pred, '){',\n          thenToString(),\n          '}', elseClause\n        ])\n      }\n    })\n  }\n\n  // procedure list\n  var globalBlock = block()\n  var procedures = {}\n  function proc (name, count) {\n    var args = []\n    function arg () {\n      var name = 'a' + args.length\n      args.push(name)\n      return name\n    }\n\n    count = count || 0\n    for (var i = 0; i < count; ++i) {\n      arg()\n    }\n\n    var body = scope()\n    var bodyToString = body.toString\n\n    var result = procedures[name] = extend(body, {\n      arg: arg,\n      toString: function () {\n        return join([\n          'function(', args.join(), '){',\n          bodyToString(),\n          '}'\n        ])\n      }\n    })\n\n    return result\n  }\n\n  function compile () {\n    var code = ['\"use strict\";',\n      globalBlock,\n      'return {']\n    Object.keys(procedures).forEach(function (name) {\n      code.push('\"', name, '\":', procedures[name].toString(), ',')\n    })\n    code.push('}')\n    var src = join(code)\n      .replace(/;/g, ';\\n')\n      .replace(/}/g, '}\\n')\n      .replace(/{/g, '{\\n')\n    var proc = Function.apply(null, linkedNames.concat(src))\n    return proc.apply(null, linkedValues)\n  }\n\n  return {\n    global: globalBlock,\n    link: link,\n    block: block,\n    proc: proc,\n    scope: scope,\n    cond: conditional,\n    compile: compile\n  }\n}\n\n// \"cute\" names for vector components\nvar CUTE_COMPONENTS = 'xyzw'.split('')\n\nvar GL_UNSIGNED_BYTE$8 = 5121\n\nvar ATTRIB_STATE_POINTER = 1\nvar ATTRIB_STATE_CONSTANT = 2\n\nvar DYN_FUNC$1 = 0\nvar DYN_PROP$1 = 1\nvar DYN_CONTEXT$1 = 2\nvar DYN_STATE$1 = 3\nvar DYN_THUNK = 4\nvar DYN_CONSTANT$1 = 5\nvar DYN_ARRAY$1 = 6\n\nvar S_DITHER = 'dither'\nvar S_BLEND_ENABLE = 'blend.enable'\nvar S_BLEND_COLOR = 'blend.color'\nvar S_BLEND_EQUATION = 'blend.equation'\nvar S_BLEND_FUNC = 'blend.func'\nvar S_DEPTH_ENABLE = 'depth.enable'\nvar S_DEPTH_FUNC = 'depth.func'\nvar S_DEPTH_RANGE = 'depth.range'\nvar S_DEPTH_MASK = 'depth.mask'\nvar S_COLOR_MASK = 'colorMask'\nvar S_CULL_ENABLE = 'cull.enable'\nvar S_CULL_FACE = 'cull.face'\nvar S_FRONT_FACE = 'frontFace'\nvar S_LINE_WIDTH = 'lineWidth'\nvar S_POLYGON_OFFSET_ENABLE = 'polygonOffset.enable'\nvar S_POLYGON_OFFSET_OFFSET = 'polygonOffset.offset'\nvar S_SAMPLE_ALPHA = 'sample.alpha'\nvar S_SAMPLE_ENABLE = 'sample.enable'\nvar S_SAMPLE_COVERAGE = 'sample.coverage'\nvar S_STENCIL_ENABLE = 'stencil.enable'\nvar S_STENCIL_MASK = 'stencil.mask'\nvar S_STENCIL_FUNC = 'stencil.func'\nvar S_STENCIL_OPFRONT = 'stencil.opFront'\nvar S_STENCIL_OPBACK = 'stencil.opBack'\nvar S_SCISSOR_ENABLE = 'scissor.enable'\nvar S_SCISSOR_BOX = 'scissor.box'\nvar S_VIEWPORT = 'viewport'\n\nvar S_PROFILE = 'profile'\n\nvar S_FRAMEBUFFER = 'framebuffer'\nvar S_VERT = 'vert'\nvar S_FRAG = 'frag'\nvar S_ELEMENTS = 'elements'\nvar S_PRIMITIVE = 'primitive'\nvar S_COUNT = 'count'\nvar S_OFFSET = 'offset'\nvar S_INSTANCES = 'instances'\nvar S_VAO = 'vao'\n\nvar SUFFIX_WIDTH = 'Width'\nvar SUFFIX_HEIGHT = 'Height'\n\nvar S_FRAMEBUFFER_WIDTH = S_FRAMEBUFFER + SUFFIX_WIDTH\nvar S_FRAMEBUFFER_HEIGHT = S_FRAMEBUFFER + SUFFIX_HEIGHT\nvar S_VIEWPORT_WIDTH = S_VIEWPORT + SUFFIX_WIDTH\nvar S_VIEWPORT_HEIGHT = S_VIEWPORT + SUFFIX_HEIGHT\nvar S_DRAWINGBUFFER = 'drawingBuffer'\nvar S_DRAWINGBUFFER_WIDTH = S_DRAWINGBUFFER + SUFFIX_WIDTH\nvar S_DRAWINGBUFFER_HEIGHT = S_DRAWINGBUFFER + SUFFIX_HEIGHT\n\nvar NESTED_OPTIONS = [\n  S_BLEND_FUNC,\n  S_BLEND_EQUATION,\n  S_STENCIL_FUNC,\n  S_STENCIL_OPFRONT,\n  S_STENCIL_OPBACK,\n  S_SAMPLE_COVERAGE,\n  S_VIEWPORT,\n  S_SCISSOR_BOX,\n  S_POLYGON_OFFSET_OFFSET\n]\n\nvar GL_ARRAY_BUFFER$2 = 34962\nvar GL_ELEMENT_ARRAY_BUFFER$1 = 34963\n\nvar GL_FRAGMENT_SHADER$1 = 35632\nvar GL_VERTEX_SHADER$1 = 35633\n\nvar GL_TEXTURE_2D$3 = 0x0DE1\nvar GL_TEXTURE_CUBE_MAP$2 = 0x8513\n\nvar GL_CULL_FACE = 0x0B44\nvar GL_BLEND = 0x0BE2\nvar GL_DITHER = 0x0BD0\nvar GL_STENCIL_TEST = 0x0B90\nvar GL_DEPTH_TEST = 0x0B71\nvar GL_SCISSOR_TEST = 0x0C11\nvar GL_POLYGON_OFFSET_FILL = 0x8037\nvar GL_SAMPLE_ALPHA_TO_COVERAGE = 0x809E\nvar GL_SAMPLE_COVERAGE = 0x80A0\n\nvar GL_FLOAT$8 = 5126\nvar GL_FLOAT_VEC2 = 35664\nvar GL_FLOAT_VEC3 = 35665\nvar GL_FLOAT_VEC4 = 35666\nvar GL_INT$3 = 5124\nvar GL_INT_VEC2 = 35667\nvar GL_INT_VEC3 = 35668\nvar GL_INT_VEC4 = 35669\nvar GL_BOOL = 35670\nvar GL_BOOL_VEC2 = 35671\nvar GL_BOOL_VEC3 = 35672\nvar GL_BOOL_VEC4 = 35673\nvar GL_FLOAT_MAT2 = 35674\nvar GL_FLOAT_MAT3 = 35675\nvar GL_FLOAT_MAT4 = 35676\nvar GL_SAMPLER_2D = 35678\nvar GL_SAMPLER_CUBE = 35680\n\nvar GL_TRIANGLES$1 = 4\n\nvar GL_FRONT = 1028\nvar GL_BACK = 1029\nvar GL_CW = 0x0900\nvar GL_CCW = 0x0901\nvar GL_MIN_EXT = 0x8007\nvar GL_MAX_EXT = 0x8008\nvar GL_ALWAYS = 519\nvar GL_KEEP = 7680\nvar GL_ZERO = 0\nvar GL_ONE = 1\nvar GL_FUNC_ADD = 0x8006\nvar GL_LESS = 513\n\nvar GL_FRAMEBUFFER$2 = 0x8D40\nvar GL_COLOR_ATTACHMENT0$2 = 0x8CE0\n\nvar blendFuncs = {\n  '0': 0,\n  '1': 1,\n  'zero': 0,\n  'one': 1,\n  'src color': 768,\n  'one minus src color': 769,\n  'src alpha': 770,\n  'one minus src alpha': 771,\n  'dst color': 774,\n  'one minus dst color': 775,\n  'dst alpha': 772,\n  'one minus dst alpha': 773,\n  'constant color': 32769,\n  'one minus constant color': 32770,\n  'constant alpha': 32771,\n  'one minus constant alpha': 32772,\n  'src alpha saturate': 776\n}\n\n// There are invalid values for srcRGB and dstRGB. See:\n// https://www.khronos.org/registry/webgl/specs/1.0/#6.13\n// https://github.com/KhronosGroup/WebGL/blob/0d3201f5f7ec3c0060bc1f04077461541f1987b9/conformance-suites/1.0.3/conformance/misc/webgl-specific.html#L56\nvar invalidBlendCombinations = [\n  'constant color, constant alpha',\n  'one minus constant color, constant alpha',\n  'constant color, one minus constant alpha',\n  'one minus constant color, one minus constant alpha',\n  'constant alpha, constant color',\n  'constant alpha, one minus constant color',\n  'one minus constant alpha, constant color',\n  'one minus constant alpha, one minus constant color'\n]\n\nvar compareFuncs = {\n  'never': 512,\n  'less': 513,\n  '<': 513,\n  'equal': 514,\n  '=': 514,\n  '==': 514,\n  '===': 514,\n  'lequal': 515,\n  '<=': 515,\n  'greater': 516,\n  '>': 516,\n  'notequal': 517,\n  '!=': 517,\n  '!==': 517,\n  'gequal': 518,\n  '>=': 518,\n  'always': 519\n}\n\nvar stencilOps = {\n  '0': 0,\n  'zero': 0,\n  'keep': 7680,\n  'replace': 7681,\n  'increment': 7682,\n  'decrement': 7683,\n  'increment wrap': 34055,\n  'decrement wrap': 34056,\n  'invert': 5386\n}\n\nvar shaderType = {\n  'frag': GL_FRAGMENT_SHADER$1,\n  'vert': GL_VERTEX_SHADER$1\n}\n\nvar orientationType = {\n  'cw': GL_CW,\n  'ccw': GL_CCW\n}\n\nfunction isBufferArgs (x) {\n  return Array.isArray(x) ||\n    isTypedArray(x) ||\n    isNDArrayLike(x)\n}\n\n// Make sure viewport is processed first\nfunction sortState (state) {\n  return state.sort(function (a, b) {\n    if (a === S_VIEWPORT) {\n      return -1\n    } else if (b === S_VIEWPORT) {\n      return 1\n    }\n    return (a < b) ? -1 : 1\n  })\n}\n\nfunction Declaration (thisDep, contextDep, propDep, append) {\n  this.thisDep = thisDep\n  this.contextDep = contextDep\n  this.propDep = propDep\n  this.append = append\n}\n\nfunction isStatic (decl) {\n  return decl && !(decl.thisDep || decl.contextDep || decl.propDep)\n}\n\nfunction createStaticDecl (append) {\n  return new Declaration(false, false, false, append)\n}\n\nfunction createDynamicDecl (dyn, append) {\n  var type = dyn.type\n  if (type === DYN_FUNC$1) {\n    var numArgs = dyn.data.length\n    return new Declaration(\n      true,\n      numArgs >= 1,\n      numArgs >= 2,\n      append)\n  } else if (type === DYN_THUNK) {\n    var data = dyn.data\n    return new Declaration(\n      data.thisDep,\n      data.contextDep,\n      data.propDep,\n      append)\n  } else if (type === DYN_CONSTANT$1) {\n    return new Declaration(\n      false,\n      false,\n      false,\n      append)\n  } else if (type === DYN_ARRAY$1) {\n    var thisDep = false\n    var contextDep = false\n    var propDep = false\n    for (var i = 0; i < dyn.data.length; ++i) {\n      var subDyn = dyn.data[i]\n      if (subDyn.type === DYN_PROP$1) {\n        propDep = true\n      } else if (subDyn.type === DYN_CONTEXT$1) {\n        contextDep = true\n      } else if (subDyn.type === DYN_STATE$1) {\n        thisDep = true\n      } else if (subDyn.type === DYN_FUNC$1) {\n        thisDep = true\n        var subArgs = subDyn.data\n        if (subArgs >= 1) {\n          contextDep = true\n        }\n        if (subArgs >= 2) {\n          propDep = true\n        }\n      } else if (subDyn.type === DYN_THUNK) {\n        thisDep = thisDep || subDyn.data.thisDep\n        contextDep = contextDep || subDyn.data.contextDep\n        propDep = propDep || subDyn.data.propDep\n      }\n    }\n    return new Declaration(\n      thisDep,\n      contextDep,\n      propDep,\n      append)\n  } else {\n    return new Declaration(\n      type === DYN_STATE$1,\n      type === DYN_CONTEXT$1,\n      type === DYN_PROP$1,\n      append)\n  }\n}\n\nvar SCOPE_DECL = new Declaration(false, false, false, function () {})\n\nfunction reglCore (\n  gl,\n  stringStore,\n  extensions,\n  limits,\n  bufferState,\n  elementState,\n  textureState,\n  framebufferState,\n  uniformState,\n  attributeState,\n  shaderState,\n  drawState,\n  contextState,\n  timer,\n  config) {\n  var AttributeRecord = attributeState.Record\n\n  var blendEquations = {\n    'add': 32774,\n    'subtract': 32778,\n    'reverse subtract': 32779\n  }\n  if (extensions.ext_blend_minmax) {\n    blendEquations.min = GL_MIN_EXT\n    blendEquations.max = GL_MAX_EXT\n  }\n\n  var extInstancing = extensions.angle_instanced_arrays\n  var extDrawBuffers = extensions.webgl_draw_buffers\n\n  // ===================================================\n  // ===================================================\n  // WEBGL STATE\n  // ===================================================\n  // ===================================================\n  var currentState = {\n    dirty: true,\n    profile: config.profile\n  }\n  var nextState = {}\n  var GL_STATE_NAMES = []\n  var GL_FLAGS = {}\n  var GL_VARIABLES = {}\n\n  function propName (name) {\n    return name.replace('.', '_')\n  }\n\n  function stateFlag (sname, cap, init) {\n    var name = propName(sname)\n    GL_STATE_NAMES.push(sname)\n    nextState[name] = currentState[name] = !!init\n    GL_FLAGS[name] = cap\n  }\n\n  function stateVariable (sname, func, init) {\n    var name = propName(sname)\n    GL_STATE_NAMES.push(sname)\n    if (Array.isArray(init)) {\n      currentState[name] = init.slice()\n      nextState[name] = init.slice()\n    } else {\n      currentState[name] = nextState[name] = init\n    }\n    GL_VARIABLES[name] = func\n  }\n\n  // Dithering\n  stateFlag(S_DITHER, GL_DITHER)\n\n  // Blending\n  stateFlag(S_BLEND_ENABLE, GL_BLEND)\n  stateVariable(S_BLEND_COLOR, 'blendColor', [0, 0, 0, 0])\n  stateVariable(S_BLEND_EQUATION, 'blendEquationSeparate',\n    [GL_FUNC_ADD, GL_FUNC_ADD])\n  stateVariable(S_BLEND_FUNC, 'blendFuncSeparate',\n    [GL_ONE, GL_ZERO, GL_ONE, GL_ZERO])\n\n  // Depth\n  stateFlag(S_DEPTH_ENABLE, GL_DEPTH_TEST, true)\n  stateVariable(S_DEPTH_FUNC, 'depthFunc', GL_LESS)\n  stateVariable(S_DEPTH_RANGE, 'depthRange', [0, 1])\n  stateVariable(S_DEPTH_MASK, 'depthMask', true)\n\n  // Color mask\n  stateVariable(S_COLOR_MASK, S_COLOR_MASK, [true, true, true, true])\n\n  // Face culling\n  stateFlag(S_CULL_ENABLE, GL_CULL_FACE)\n  stateVariable(S_CULL_FACE, 'cullFace', GL_BACK)\n\n  // Front face orientation\n  stateVariable(S_FRONT_FACE, S_FRONT_FACE, GL_CCW)\n\n  // Line width\n  stateVariable(S_LINE_WIDTH, S_LINE_WIDTH, 1)\n\n  // Polygon offset\n  stateFlag(S_POLYGON_OFFSET_ENABLE, GL_POLYGON_OFFSET_FILL)\n  stateVariable(S_POLYGON_OFFSET_OFFSET, 'polygonOffset', [0, 0])\n\n  // Sample coverage\n  stateFlag(S_SAMPLE_ALPHA, GL_SAMPLE_ALPHA_TO_COVERAGE)\n  stateFlag(S_SAMPLE_ENABLE, GL_SAMPLE_COVERAGE)\n  stateVariable(S_SAMPLE_COVERAGE, 'sampleCoverage', [1, false])\n\n  // Stencil\n  stateFlag(S_STENCIL_ENABLE, GL_STENCIL_TEST)\n  stateVariable(S_STENCIL_MASK, 'stencilMask', -1)\n  stateVariable(S_STENCIL_FUNC, 'stencilFunc', [GL_ALWAYS, 0, -1])\n  stateVariable(S_STENCIL_OPFRONT, 'stencilOpSeparate',\n    [GL_FRONT, GL_KEEP, GL_KEEP, GL_KEEP])\n  stateVariable(S_STENCIL_OPBACK, 'stencilOpSeparate',\n    [GL_BACK, GL_KEEP, GL_KEEP, GL_KEEP])\n\n  // Scissor\n  stateFlag(S_SCISSOR_ENABLE, GL_SCISSOR_TEST)\n  stateVariable(S_SCISSOR_BOX, 'scissor',\n    [0, 0, gl.drawingBufferWidth, gl.drawingBufferHeight])\n\n  // Viewport\n  stateVariable(S_VIEWPORT, S_VIEWPORT,\n    [0, 0, gl.drawingBufferWidth, gl.drawingBufferHeight])\n\n  // ===================================================\n  // ===================================================\n  // ENVIRONMENT\n  // ===================================================\n  // ===================================================\n  var sharedState = {\n    gl: gl,\n    context: contextState,\n    strings: stringStore,\n    next: nextState,\n    current: currentState,\n    draw: drawState,\n    elements: elementState,\n    buffer: bufferState,\n    shader: shaderState,\n    attributes: attributeState.state,\n    vao: attributeState,\n    uniforms: uniformState,\n    framebuffer: framebufferState,\n    extensions: extensions,\n\n    timer: timer,\n    isBufferArgs: isBufferArgs\n  }\n\n  var sharedConstants = {\n    primTypes: primTypes,\n    compareFuncs: compareFuncs,\n    blendFuncs: blendFuncs,\n    blendEquations: blendEquations,\n    stencilOps: stencilOps,\n    glTypes: glTypes,\n    orientationType: orientationType\n  }\n\n  check$1.optional(function () {\n    sharedState.isArrayLike = isArrayLike\n  })\n\n  if (extDrawBuffers) {\n    sharedConstants.backBuffer = [GL_BACK]\n    sharedConstants.drawBuffer = loop(limits.maxDrawbuffers, function (i) {\n      if (i === 0) {\n        return [0]\n      }\n      return loop(i, function (j) {\n        return GL_COLOR_ATTACHMENT0$2 + j\n      })\n    })\n  }\n\n  var drawCallCounter = 0\n  function createREGLEnvironment () {\n    var env = createEnvironment()\n    var link = env.link\n    var global = env.global\n    env.id = drawCallCounter++\n\n    env.batchId = '0'\n\n    // link shared state\n    var SHARED = link(sharedState)\n    var shared = env.shared = {\n      props: 'a0'\n    }\n    Object.keys(sharedState).forEach(function (prop) {\n      shared[prop] = global.def(SHARED, '.', prop)\n    })\n\n    // Inject runtime assertion stuff for debug builds\n    check$1.optional(function () {\n      env.CHECK = link(check$1)\n      env.commandStr = check$1.guessCommand()\n      env.command = link(env.commandStr)\n      env.assert = function (block, pred, message) {\n        block(\n          'if(!(', pred, '))',\n          this.CHECK, '.commandRaise(', link(message), ',', this.command, ');')\n      }\n\n      sharedConstants.invalidBlendCombinations = invalidBlendCombinations\n    })\n\n    // Copy GL state variables over\n    var nextVars = env.next = {}\n    var currentVars = env.current = {}\n    Object.keys(GL_VARIABLES).forEach(function (variable) {\n      if (Array.isArray(currentState[variable])) {\n        nextVars[variable] = global.def(shared.next, '.', variable)\n        currentVars[variable] = global.def(shared.current, '.', variable)\n      }\n    })\n\n    // Initialize shared constants\n    var constants = env.constants = {}\n    Object.keys(sharedConstants).forEach(function (name) {\n      constants[name] = global.def(JSON.stringify(sharedConstants[name]))\n    })\n\n    // Helper function for calling a block\n    env.invoke = function (block, x) {\n      switch (x.type) {\n        case DYN_FUNC$1:\n          var argList = [\n            'this',\n            shared.context,\n            shared.props,\n            env.batchId\n          ]\n          return block.def(\n            link(x.data), '.call(',\n            argList.slice(0, Math.max(x.data.length + 1, 4)),\n            ')')\n        case DYN_PROP$1:\n          return block.def(shared.props, x.data)\n        case DYN_CONTEXT$1:\n          return block.def(shared.context, x.data)\n        case DYN_STATE$1:\n          return block.def('this', x.data)\n        case DYN_THUNK:\n          x.data.append(env, block)\n          return x.data.ref\n        case DYN_CONSTANT$1:\n          return x.data.toString()\n        case DYN_ARRAY$1:\n          return x.data.map(function (y) {\n            return env.invoke(block, y)\n          })\n      }\n    }\n\n    env.attribCache = {}\n\n    var scopeAttribs = {}\n    env.scopeAttrib = function (name) {\n      var id = stringStore.id(name)\n      if (id in scopeAttribs) {\n        return scopeAttribs[id]\n      }\n      var binding = attributeState.scope[id]\n      if (!binding) {\n        binding = attributeState.scope[id] = new AttributeRecord()\n      }\n      var result = scopeAttribs[id] = link(binding)\n      return result\n    }\n\n    return env\n  }\n\n  // ===================================================\n  // ===================================================\n  // PARSING\n  // ===================================================\n  // ===================================================\n  function parseProfile (options) {\n    var staticOptions = options.static\n    var dynamicOptions = options.dynamic\n\n    var profileEnable\n    if (S_PROFILE in staticOptions) {\n      var value = !!staticOptions[S_PROFILE]\n      profileEnable = createStaticDecl(function (env, scope) {\n        return value\n      })\n      profileEnable.enable = value\n    } else if (S_PROFILE in dynamicOptions) {\n      var dyn = dynamicOptions[S_PROFILE]\n      profileEnable = createDynamicDecl(dyn, function (env, scope) {\n        return env.invoke(scope, dyn)\n      })\n    }\n\n    return profileEnable\n  }\n\n  function parseFramebuffer (options, env) {\n    var staticOptions = options.static\n    var dynamicOptions = options.dynamic\n\n    if (S_FRAMEBUFFER in staticOptions) {\n      var framebuffer = staticOptions[S_FRAMEBUFFER]\n      if (framebuffer) {\n        framebuffer = framebufferState.getFramebuffer(framebuffer)\n        check$1.command(framebuffer, 'invalid framebuffer object')\n        return createStaticDecl(function (env, block) {\n          var FRAMEBUFFER = env.link(framebuffer)\n          var shared = env.shared\n          block.set(\n            shared.framebuffer,\n            '.next',\n            FRAMEBUFFER)\n          var CONTEXT = shared.context\n          block.set(\n            CONTEXT,\n            '.' + S_FRAMEBUFFER_WIDTH,\n            FRAMEBUFFER + '.width')\n          block.set(\n            CONTEXT,\n            '.' + S_FRAMEBUFFER_HEIGHT,\n            FRAMEBUFFER + '.height')\n          return FRAMEBUFFER\n        })\n      } else {\n        return createStaticDecl(function (env, scope) {\n          var shared = env.shared\n          scope.set(\n            shared.framebuffer,\n            '.next',\n            'null')\n          var CONTEXT = shared.context\n          scope.set(\n            CONTEXT,\n            '.' + S_FRAMEBUFFER_WIDTH,\n            CONTEXT + '.' + S_DRAWINGBUFFER_WIDTH)\n          scope.set(\n            CONTEXT,\n            '.' + S_FRAMEBUFFER_HEIGHT,\n            CONTEXT + '.' + S_DRAWINGBUFFER_HEIGHT)\n          return 'null'\n        })\n      }\n    } else if (S_FRAMEBUFFER in dynamicOptions) {\n      var dyn = dynamicOptions[S_FRAMEBUFFER]\n      return createDynamicDecl(dyn, function (env, scope) {\n        var FRAMEBUFFER_FUNC = env.invoke(scope, dyn)\n        var shared = env.shared\n        var FRAMEBUFFER_STATE = shared.framebuffer\n        var FRAMEBUFFER = scope.def(\n          FRAMEBUFFER_STATE, '.getFramebuffer(', FRAMEBUFFER_FUNC, ')')\n\n        check$1.optional(function () {\n          env.assert(scope,\n            '!' + FRAMEBUFFER_FUNC + '||' + FRAMEBUFFER,\n            'invalid framebuffer object')\n        })\n\n        scope.set(\n          FRAMEBUFFER_STATE,\n          '.next',\n          FRAMEBUFFER)\n        var CONTEXT = shared.context\n        scope.set(\n          CONTEXT,\n          '.' + S_FRAMEBUFFER_WIDTH,\n          FRAMEBUFFER + '?' + FRAMEBUFFER + '.width:' +\n          CONTEXT + '.' + S_DRAWINGBUFFER_WIDTH)\n        scope.set(\n          CONTEXT,\n          '.' + S_FRAMEBUFFER_HEIGHT,\n          FRAMEBUFFER +\n          '?' + FRAMEBUFFER + '.height:' +\n          CONTEXT + '.' + S_DRAWINGBUFFER_HEIGHT)\n        return FRAMEBUFFER\n      })\n    } else {\n      return null\n    }\n  }\n\n  function parseViewportScissor (options, framebuffer, env) {\n    var staticOptions = options.static\n    var dynamicOptions = options.dynamic\n\n    function parseBox (param) {\n      if (param in staticOptions) {\n        var box = staticOptions[param]\n        check$1.commandType(box, 'object', 'invalid ' + param, env.commandStr)\n\n        var isStatic = true\n        var x = box.x | 0\n        var y = box.y | 0\n        var w, h\n        if ('width' in box) {\n          w = box.width | 0\n          check$1.command(w >= 0, 'invalid ' + param, env.commandStr)\n        } else {\n          isStatic = false\n        }\n        if ('height' in box) {\n          h = box.height | 0\n          check$1.command(h >= 0, 'invalid ' + param, env.commandStr)\n        } else {\n          isStatic = false\n        }\n\n        return new Declaration(\n          !isStatic && framebuffer && framebuffer.thisDep,\n          !isStatic && framebuffer && framebuffer.contextDep,\n          !isStatic && framebuffer && framebuffer.propDep,\n          function (env, scope) {\n            var CONTEXT = env.shared.context\n            var BOX_W = w\n            if (!('width' in box)) {\n              BOX_W = scope.def(CONTEXT, '.', S_FRAMEBUFFER_WIDTH, '-', x)\n            }\n            var BOX_H = h\n            if (!('height' in box)) {\n              BOX_H = scope.def(CONTEXT, '.', S_FRAMEBUFFER_HEIGHT, '-', y)\n            }\n            return [x, y, BOX_W, BOX_H]\n          })\n      } else if (param in dynamicOptions) {\n        var dynBox = dynamicOptions[param]\n        var result = createDynamicDecl(dynBox, function (env, scope) {\n          var BOX = env.invoke(scope, dynBox)\n\n          check$1.optional(function () {\n            env.assert(scope,\n              BOX + '&&typeof ' + BOX + '===\"object\"',\n              'invalid ' + param)\n          })\n\n          var CONTEXT = env.shared.context\n          var BOX_X = scope.def(BOX, '.x|0')\n          var BOX_Y = scope.def(BOX, '.y|0')\n          var BOX_W = scope.def(\n            '\"width\" in ', BOX, '?', BOX, '.width|0:',\n            '(', CONTEXT, '.', S_FRAMEBUFFER_WIDTH, '-', BOX_X, ')')\n          var BOX_H = scope.def(\n            '\"height\" in ', BOX, '?', BOX, '.height|0:',\n            '(', CONTEXT, '.', S_FRAMEBUFFER_HEIGHT, '-', BOX_Y, ')')\n\n          check$1.optional(function () {\n            env.assert(scope,\n              BOX_W + '>=0&&' +\n              BOX_H + '>=0',\n              'invalid ' + param)\n          })\n\n          return [BOX_X, BOX_Y, BOX_W, BOX_H]\n        })\n        if (framebuffer) {\n          result.thisDep = result.thisDep || framebuffer.thisDep\n          result.contextDep = result.contextDep || framebuffer.contextDep\n          result.propDep = result.propDep || framebuffer.propDep\n        }\n        return result\n      } else if (framebuffer) {\n        return new Declaration(\n          framebuffer.thisDep,\n          framebuffer.contextDep,\n          framebuffer.propDep,\n          function (env, scope) {\n            var CONTEXT = env.shared.context\n            return [\n              0, 0,\n              scope.def(CONTEXT, '.', S_FRAMEBUFFER_WIDTH),\n              scope.def(CONTEXT, '.', S_FRAMEBUFFER_HEIGHT)]\n          })\n      } else {\n        return null\n      }\n    }\n\n    var viewport = parseBox(S_VIEWPORT)\n\n    if (viewport) {\n      var prevViewport = viewport\n      viewport = new Declaration(\n        viewport.thisDep,\n        viewport.contextDep,\n        viewport.propDep,\n        function (env, scope) {\n          var VIEWPORT = prevViewport.append(env, scope)\n          var CONTEXT = env.shared.context\n          scope.set(\n            CONTEXT,\n            '.' + S_VIEWPORT_WIDTH,\n            VIEWPORT[2])\n          scope.set(\n            CONTEXT,\n            '.' + S_VIEWPORT_HEIGHT,\n            VIEWPORT[3])\n          return VIEWPORT\n        })\n    }\n\n    return {\n      viewport: viewport,\n      scissor_box: parseBox(S_SCISSOR_BOX)\n    }\n  }\n\n  function parseAttribLocations (options, attributes) {\n    var staticOptions = options.static\n    var staticProgram =\n      typeof staticOptions[S_FRAG] === 'string' &&\n      typeof staticOptions[S_VERT] === 'string'\n    if (staticProgram) {\n      if (Object.keys(attributes.dynamic).length > 0) {\n        return null\n      }\n      var staticAttributes = attributes.static\n      var sAttributes = Object.keys(staticAttributes)\n      if (sAttributes.length > 0 && typeof staticAttributes[sAttributes[0]] === 'number') {\n        var bindings = []\n        for (var i = 0; i < sAttributes.length; ++i) {\n          check$1(typeof staticAttributes[sAttributes[i]] === 'number', 'must specify all vertex attribute locations when using vaos')\n          bindings.push([staticAttributes[sAttributes[i]] | 0, sAttributes[i]])\n        }\n        return bindings\n      }\n    }\n    return null\n  }\n\n  function parseProgram (options, env, attribLocations) {\n    var staticOptions = options.static\n    var dynamicOptions = options.dynamic\n\n    function parseShader (name) {\n      if (name in staticOptions) {\n        var id = stringStore.id(staticOptions[name])\n        check$1.optional(function () {\n          shaderState.shader(shaderType[name], id, check$1.guessCommand())\n        })\n        var result = createStaticDecl(function () {\n          return id\n        })\n        result.id = id\n        return result\n      } else if (name in dynamicOptions) {\n        var dyn = dynamicOptions[name]\n        return createDynamicDecl(dyn, function (env, scope) {\n          var str = env.invoke(scope, dyn)\n          var id = scope.def(env.shared.strings, '.id(', str, ')')\n          check$1.optional(function () {\n            scope(\n              env.shared.shader, '.shader(',\n              shaderType[name], ',',\n              id, ',',\n              env.command, ');')\n          })\n          return id\n        })\n      }\n      return null\n    }\n\n    var frag = parseShader(S_FRAG)\n    var vert = parseShader(S_VERT)\n\n    var program = null\n    var progVar\n    if (isStatic(frag) && isStatic(vert)) {\n      program = shaderState.program(vert.id, frag.id, null, attribLocations)\n      progVar = createStaticDecl(function (env, scope) {\n        return env.link(program)\n      })\n    } else {\n      progVar = new Declaration(\n        (frag && frag.thisDep) || (vert && vert.thisDep),\n        (frag && frag.contextDep) || (vert && vert.contextDep),\n        (frag && frag.propDep) || (vert && vert.propDep),\n        function (env, scope) {\n          var SHADER_STATE = env.shared.shader\n          var fragId\n          if (frag) {\n            fragId = frag.append(env, scope)\n          } else {\n            fragId = scope.def(SHADER_STATE, '.', S_FRAG)\n          }\n          var vertId\n          if (vert) {\n            vertId = vert.append(env, scope)\n          } else {\n            vertId = scope.def(SHADER_STATE, '.', S_VERT)\n          }\n          var progDef = SHADER_STATE + '.program(' + vertId + ',' + fragId\n          check$1.optional(function () {\n            progDef += ',' + env.command\n          })\n          return scope.def(progDef + ')')\n        })\n    }\n\n    return {\n      frag: frag,\n      vert: vert,\n      progVar: progVar,\n      program: program\n    }\n  }\n\n  function parseDraw (options, env) {\n    var staticOptions = options.static\n    var dynamicOptions = options.dynamic\n\n    function parseElements () {\n      if (S_ELEMENTS in staticOptions) {\n        var elements = staticOptions[S_ELEMENTS]\n        if (isBufferArgs(elements)) {\n          elements = elementState.getElements(elementState.create(elements, true))\n        } else if (elements) {\n          elements = elementState.getElements(elements)\n          check$1.command(elements, 'invalid elements', env.commandStr)\n        }\n        var result = createStaticDecl(function (env, scope) {\n          if (elements) {\n            var result = env.link(elements)\n            env.ELEMENTS = result\n            return result\n          }\n          env.ELEMENTS = null\n          return null\n        })\n        result.value = elements\n        return result\n      } else if (S_ELEMENTS in dynamicOptions) {\n        var dyn = dynamicOptions[S_ELEMENTS]\n        return createDynamicDecl(dyn, function (env, scope) {\n          var shared = env.shared\n\n          var IS_BUFFER_ARGS = shared.isBufferArgs\n          var ELEMENT_STATE = shared.elements\n\n          var elementDefn = env.invoke(scope, dyn)\n          var elements = scope.def('null')\n          var elementStream = scope.def(IS_BUFFER_ARGS, '(', elementDefn, ')')\n\n          var ifte = env.cond(elementStream)\n            .then(elements, '=', ELEMENT_STATE, '.createStream(', elementDefn, ');')\n            .else(elements, '=', ELEMENT_STATE, '.getElements(', elementDefn, ');')\n\n          check$1.optional(function () {\n            env.assert(ifte.else,\n              '!' + elementDefn + '||' + elements,\n              'invalid elements')\n          })\n\n          scope.entry(ifte)\n          scope.exit(\n            env.cond(elementStream)\n              .then(ELEMENT_STATE, '.destroyStream(', elements, ');'))\n\n          env.ELEMENTS = elements\n\n          return elements\n        })\n      }\n\n      return null\n    }\n\n    var elements = parseElements()\n\n    function parsePrimitive () {\n      if (S_PRIMITIVE in staticOptions) {\n        var primitive = staticOptions[S_PRIMITIVE]\n        check$1.commandParameter(primitive, primTypes, 'invalid primitve', env.commandStr)\n        return createStaticDecl(function (env, scope) {\n          return primTypes[primitive]\n        })\n      } else if (S_PRIMITIVE in dynamicOptions) {\n        var dynPrimitive = dynamicOptions[S_PRIMITIVE]\n        return createDynamicDecl(dynPrimitive, function (env, scope) {\n          var PRIM_TYPES = env.constants.primTypes\n          var prim = env.invoke(scope, dynPrimitive)\n          check$1.optional(function () {\n            env.assert(scope,\n              prim + ' in ' + PRIM_TYPES,\n              'invalid primitive, must be one of ' + Object.keys(primTypes))\n          })\n          return scope.def(PRIM_TYPES, '[', prim, ']')\n        })\n      } else if (elements) {\n        if (isStatic(elements)) {\n          if (elements.value) {\n            return createStaticDecl(function (env, scope) {\n              return scope.def(env.ELEMENTS, '.primType')\n            })\n          } else {\n            return createStaticDecl(function () {\n              return GL_TRIANGLES$1\n            })\n          }\n        } else {\n          return new Declaration(\n            elements.thisDep,\n            elements.contextDep,\n            elements.propDep,\n            function (env, scope) {\n              var elements = env.ELEMENTS\n              return scope.def(elements, '?', elements, '.primType:', GL_TRIANGLES$1)\n            })\n        }\n      }\n      return null\n    }\n\n    function parseParam (param, isOffset) {\n      if (param in staticOptions) {\n        var value = staticOptions[param] | 0\n        check$1.command(!isOffset || value >= 0, 'invalid ' + param, env.commandStr)\n        return createStaticDecl(function (env, scope) {\n          if (isOffset) {\n            env.OFFSET = value\n          }\n          return value\n        })\n      } else if (param in dynamicOptions) {\n        var dynValue = dynamicOptions[param]\n        return createDynamicDecl(dynValue, function (env, scope) {\n          var result = env.invoke(scope, dynValue)\n          if (isOffset) {\n            env.OFFSET = result\n            check$1.optional(function () {\n              env.assert(scope,\n                result + '>=0',\n                'invalid ' + param)\n            })\n          }\n          return result\n        })\n      } else if (isOffset && elements) {\n        return createStaticDecl(function (env, scope) {\n          env.OFFSET = '0'\n          return 0\n        })\n      }\n      return null\n    }\n\n    var OFFSET = parseParam(S_OFFSET, true)\n\n    function parseVertCount () {\n      if (S_COUNT in staticOptions) {\n        var count = staticOptions[S_COUNT] | 0\n        check$1.command(\n          typeof count === 'number' && count >= 0, 'invalid vertex count', env.commandStr)\n        return createStaticDecl(function () {\n          return count\n        })\n      } else if (S_COUNT in dynamicOptions) {\n        var dynCount = dynamicOptions[S_COUNT]\n        return createDynamicDecl(dynCount, function (env, scope) {\n          var result = env.invoke(scope, dynCount)\n          check$1.optional(function () {\n            env.assert(scope,\n              'typeof ' + result + '===\"number\"&&' +\n              result + '>=0&&' +\n              result + '===(' + result + '|0)',\n              'invalid vertex count')\n          })\n          return result\n        })\n      } else if (elements) {\n        if (isStatic(elements)) {\n          if (elements) {\n            if (OFFSET) {\n              return new Declaration(\n                OFFSET.thisDep,\n                OFFSET.contextDep,\n                OFFSET.propDep,\n                function (env, scope) {\n                  var result = scope.def(\n                    env.ELEMENTS, '.vertCount-', env.OFFSET)\n\n                  check$1.optional(function () {\n                    env.assert(scope,\n                      result + '>=0',\n                      'invalid vertex offset/element buffer too small')\n                  })\n\n                  return result\n                })\n            } else {\n              return createStaticDecl(function (env, scope) {\n                return scope.def(env.ELEMENTS, '.vertCount')\n              })\n            }\n          } else {\n            var result = createStaticDecl(function () {\n              return -1\n            })\n            check$1.optional(function () {\n              result.MISSING = true\n            })\n            return result\n          }\n        } else {\n          var variable = new Declaration(\n            elements.thisDep || OFFSET.thisDep,\n            elements.contextDep || OFFSET.contextDep,\n            elements.propDep || OFFSET.propDep,\n            function (env, scope) {\n              var elements = env.ELEMENTS\n              if (env.OFFSET) {\n                return scope.def(elements, '?', elements, '.vertCount-',\n                  env.OFFSET, ':-1')\n              }\n              return scope.def(elements, '?', elements, '.vertCount:-1')\n            })\n          check$1.optional(function () {\n            variable.DYNAMIC = true\n          })\n          return variable\n        }\n      }\n      return null\n    }\n\n    return {\n      elements: elements,\n      primitive: parsePrimitive(),\n      count: parseVertCount(),\n      instances: parseParam(S_INSTANCES, false),\n      offset: OFFSET\n    }\n  }\n\n  function parseGLState (options, env) {\n    var staticOptions = options.static\n    var dynamicOptions = options.dynamic\n\n    var STATE = {}\n\n    GL_STATE_NAMES.forEach(function (prop) {\n      var param = propName(prop)\n\n      function parseParam (parseStatic, parseDynamic) {\n        if (prop in staticOptions) {\n          var value = parseStatic(staticOptions[prop])\n          STATE[param] = createStaticDecl(function () {\n            return value\n          })\n        } else if (prop in dynamicOptions) {\n          var dyn = dynamicOptions[prop]\n          STATE[param] = createDynamicDecl(dyn, function (env, scope) {\n            return parseDynamic(env, scope, env.invoke(scope, dyn))\n          })\n        }\n      }\n\n      switch (prop) {\n        case S_CULL_ENABLE:\n        case S_BLEND_ENABLE:\n        case S_DITHER:\n        case S_STENCIL_ENABLE:\n        case S_DEPTH_ENABLE:\n        case S_SCISSOR_ENABLE:\n        case S_POLYGON_OFFSET_ENABLE:\n        case S_SAMPLE_ALPHA:\n        case S_SAMPLE_ENABLE:\n        case S_DEPTH_MASK:\n          return parseParam(\n            function (value) {\n              check$1.commandType(value, 'boolean', prop, env.commandStr)\n              return value\n            },\n            function (env, scope, value) {\n              check$1.optional(function () {\n                env.assert(scope,\n                  'typeof ' + value + '===\"boolean\"',\n                  'invalid flag ' + prop, env.commandStr)\n              })\n              return value\n            })\n\n        case S_DEPTH_FUNC:\n          return parseParam(\n            function (value) {\n              check$1.commandParameter(value, compareFuncs, 'invalid ' + prop, env.commandStr)\n              return compareFuncs[value]\n            },\n            function (env, scope, value) {\n              var COMPARE_FUNCS = env.constants.compareFuncs\n              check$1.optional(function () {\n                env.assert(scope,\n                  value + ' in ' + COMPARE_FUNCS,\n                  'invalid ' + prop + ', must be one of ' + Object.keys(compareFuncs))\n              })\n              return scope.def(COMPARE_FUNCS, '[', value, ']')\n            })\n\n        case S_DEPTH_RANGE:\n          return parseParam(\n            function (value) {\n              check$1.command(\n                isArrayLike(value) &&\n                value.length === 2 &&\n                typeof value[0] === 'number' &&\n                typeof value[1] === 'number' &&\n                value[0] <= value[1],\n                'depth range is 2d array',\n                env.commandStr)\n              return value\n            },\n            function (env, scope, value) {\n              check$1.optional(function () {\n                env.assert(scope,\n                  env.shared.isArrayLike + '(' + value + ')&&' +\n                  value + '.length===2&&' +\n                  'typeof ' + value + '[0]===\"number\"&&' +\n                  'typeof ' + value + '[1]===\"number\"&&' +\n                  value + '[0]<=' + value + '[1]',\n                  'depth range must be a 2d array')\n              })\n\n              var Z_NEAR = scope.def('+', value, '[0]')\n              var Z_FAR = scope.def('+', value, '[1]')\n              return [Z_NEAR, Z_FAR]\n            })\n\n        case S_BLEND_FUNC:\n          return parseParam(\n            function (value) {\n              check$1.commandType(value, 'object', 'blend.func', env.commandStr)\n              var srcRGB = ('srcRGB' in value ? value.srcRGB : value.src)\n              var srcAlpha = ('srcAlpha' in value ? value.srcAlpha : value.src)\n              var dstRGB = ('dstRGB' in value ? value.dstRGB : value.dst)\n              var dstAlpha = ('dstAlpha' in value ? value.dstAlpha : value.dst)\n              check$1.commandParameter(srcRGB, blendFuncs, param + '.srcRGB', env.commandStr)\n              check$1.commandParameter(srcAlpha, blendFuncs, param + '.srcAlpha', env.commandStr)\n              check$1.commandParameter(dstRGB, blendFuncs, param + '.dstRGB', env.commandStr)\n              check$1.commandParameter(dstAlpha, blendFuncs, param + '.dstAlpha', env.commandStr)\n\n              check$1.command(\n                (invalidBlendCombinations.indexOf(srcRGB + ', ' + dstRGB) === -1),\n                'unallowed blending combination (srcRGB, dstRGB) = (' + srcRGB + ', ' + dstRGB + ')', env.commandStr)\n\n              return [\n                blendFuncs[srcRGB],\n                blendFuncs[dstRGB],\n                blendFuncs[srcAlpha],\n                blendFuncs[dstAlpha]\n              ]\n            },\n            function (env, scope, value) {\n              var BLEND_FUNCS = env.constants.blendFuncs\n\n              check$1.optional(function () {\n                env.assert(scope,\n                  value + '&&typeof ' + value + '===\"object\"',\n                  'invalid blend func, must be an object')\n              })\n\n              function read (prefix, suffix) {\n                var func = scope.def(\n                  '\"', prefix, suffix, '\" in ', value,\n                  '?', value, '.', prefix, suffix,\n                  ':', value, '.', prefix)\n\n                check$1.optional(function () {\n                  env.assert(scope,\n                    func + ' in ' + BLEND_FUNCS,\n                    'invalid ' + prop + '.' + prefix + suffix + ', must be one of ' + Object.keys(blendFuncs))\n                })\n\n                return func\n              }\n\n              var srcRGB = read('src', 'RGB')\n              var dstRGB = read('dst', 'RGB')\n\n              check$1.optional(function () {\n                var INVALID_BLEND_COMBINATIONS = env.constants.invalidBlendCombinations\n\n                env.assert(scope,\n                  INVALID_BLEND_COMBINATIONS +\n                           '.indexOf(' + srcRGB + '+\", \"+' + dstRGB + ') === -1 ',\n                  'unallowed blending combination for (srcRGB, dstRGB)'\n                )\n              })\n\n              var SRC_RGB = scope.def(BLEND_FUNCS, '[', srcRGB, ']')\n              var SRC_ALPHA = scope.def(BLEND_FUNCS, '[', read('src', 'Alpha'), ']')\n              var DST_RGB = scope.def(BLEND_FUNCS, '[', dstRGB, ']')\n              var DST_ALPHA = scope.def(BLEND_FUNCS, '[', read('dst', 'Alpha'), ']')\n\n              return [SRC_RGB, DST_RGB, SRC_ALPHA, DST_ALPHA]\n            })\n\n        case S_BLEND_EQUATION:\n          return parseParam(\n            function (value) {\n              if (typeof value === 'string') {\n                check$1.commandParameter(value, blendEquations, 'invalid ' + prop, env.commandStr)\n                return [\n                  blendEquations[value],\n                  blendEquations[value]\n                ]\n              } else if (typeof value === 'object') {\n                check$1.commandParameter(\n                  value.rgb, blendEquations, prop + '.rgb', env.commandStr)\n                check$1.commandParameter(\n                  value.alpha, blendEquations, prop + '.alpha', env.commandStr)\n                return [\n                  blendEquations[value.rgb],\n                  blendEquations[value.alpha]\n                ]\n              } else {\n                check$1.commandRaise('invalid blend.equation', env.commandStr)\n              }\n            },\n            function (env, scope, value) {\n              var BLEND_EQUATIONS = env.constants.blendEquations\n\n              var RGB = scope.def()\n              var ALPHA = scope.def()\n\n              var ifte = env.cond('typeof ', value, '===\"string\"')\n\n              check$1.optional(function () {\n                function checkProp (block, name, value) {\n                  env.assert(block,\n                    value + ' in ' + BLEND_EQUATIONS,\n                    'invalid ' + name + ', must be one of ' + Object.keys(blendEquations))\n                }\n                checkProp(ifte.then, prop, value)\n\n                env.assert(ifte.else,\n                  value + '&&typeof ' + value + '===\"object\"',\n                  'invalid ' + prop)\n                checkProp(ifte.else, prop + '.rgb', value + '.rgb')\n                checkProp(ifte.else, prop + '.alpha', value + '.alpha')\n              })\n\n              ifte.then(\n                RGB, '=', ALPHA, '=', BLEND_EQUATIONS, '[', value, '];')\n              ifte.else(\n                RGB, '=', BLEND_EQUATIONS, '[', value, '.rgb];',\n                ALPHA, '=', BLEND_EQUATIONS, '[', value, '.alpha];')\n\n              scope(ifte)\n\n              return [RGB, ALPHA]\n            })\n\n        case S_BLEND_COLOR:\n          return parseParam(\n            function (value) {\n              check$1.command(\n                isArrayLike(value) &&\n                value.length === 4,\n                'blend.color must be a 4d array', env.commandStr)\n              return loop(4, function (i) {\n                return +value[i]\n              })\n            },\n            function (env, scope, value) {\n              check$1.optional(function () {\n                env.assert(scope,\n                  env.shared.isArrayLike + '(' + value + ')&&' +\n                  value + '.length===4',\n                  'blend.color must be a 4d array')\n              })\n              return loop(4, function (i) {\n                return scope.def('+', value, '[', i, ']')\n              })\n            })\n\n        case S_STENCIL_MASK:\n          return parseParam(\n            function (value) {\n              check$1.commandType(value, 'number', param, env.commandStr)\n              return value | 0\n            },\n            function (env, scope, value) {\n              check$1.optional(function () {\n                env.assert(scope,\n                  'typeof ' + value + '===\"number\"',\n                  'invalid stencil.mask')\n              })\n              return scope.def(value, '|0')\n            })\n\n        case S_STENCIL_FUNC:\n          return parseParam(\n            function (value) {\n              check$1.commandType(value, 'object', param, env.commandStr)\n              var cmp = value.cmp || 'keep'\n              var ref = value.ref || 0\n              var mask = 'mask' in value ? value.mask : -1\n              check$1.commandParameter(cmp, compareFuncs, prop + '.cmp', env.commandStr)\n              check$1.commandType(ref, 'number', prop + '.ref', env.commandStr)\n              check$1.commandType(mask, 'number', prop + '.mask', env.commandStr)\n              return [\n                compareFuncs[cmp],\n                ref,\n                mask\n              ]\n            },\n            function (env, scope, value) {\n              var COMPARE_FUNCS = env.constants.compareFuncs\n              check$1.optional(function () {\n                function assert () {\n                  env.assert(scope,\n                    Array.prototype.join.call(arguments, ''),\n                    'invalid stencil.func')\n                }\n                assert(value + '&&typeof ', value, '===\"object\"')\n                assert('!(\"cmp\" in ', value, ')||(',\n                  value, '.cmp in ', COMPARE_FUNCS, ')')\n              })\n              var cmp = scope.def(\n                '\"cmp\" in ', value,\n                '?', COMPARE_FUNCS, '[', value, '.cmp]',\n                ':', GL_KEEP)\n              var ref = scope.def(value, '.ref|0')\n              var mask = scope.def(\n                '\"mask\" in ', value,\n                '?', value, '.mask|0:-1')\n              return [cmp, ref, mask]\n            })\n\n        case S_STENCIL_OPFRONT:\n        case S_STENCIL_OPBACK:\n          return parseParam(\n            function (value) {\n              check$1.commandType(value, 'object', param, env.commandStr)\n              var fail = value.fail || 'keep'\n              var zfail = value.zfail || 'keep'\n              var zpass = value.zpass || 'keep'\n              check$1.commandParameter(fail, stencilOps, prop + '.fail', env.commandStr)\n              check$1.commandParameter(zfail, stencilOps, prop + '.zfail', env.commandStr)\n              check$1.commandParameter(zpass, stencilOps, prop + '.zpass', env.commandStr)\n              return [\n                prop === S_STENCIL_OPBACK ? GL_BACK : GL_FRONT,\n                stencilOps[fail],\n                stencilOps[zfail],\n                stencilOps[zpass]\n              ]\n            },\n            function (env, scope, value) {\n              var STENCIL_OPS = env.constants.stencilOps\n\n              check$1.optional(function () {\n                env.assert(scope,\n                  value + '&&typeof ' + value + '===\"object\"',\n                  'invalid ' + prop)\n              })\n\n              function read (name) {\n                check$1.optional(function () {\n                  env.assert(scope,\n                    '!(\"' + name + '\" in ' + value + ')||' +\n                    '(' + value + '.' + name + ' in ' + STENCIL_OPS + ')',\n                    'invalid ' + prop + '.' + name + ', must be one of ' + Object.keys(stencilOps))\n                })\n\n                return scope.def(\n                  '\"', name, '\" in ', value,\n                  '?', STENCIL_OPS, '[', value, '.', name, ']:',\n                  GL_KEEP)\n              }\n\n              return [\n                prop === S_STENCIL_OPBACK ? GL_BACK : GL_FRONT,\n                read('fail'),\n                read('zfail'),\n                read('zpass')\n              ]\n            })\n\n        case S_POLYGON_OFFSET_OFFSET:\n          return parseParam(\n            function (value) {\n              check$1.commandType(value, 'object', param, env.commandStr)\n              var factor = value.factor | 0\n              var units = value.units | 0\n              check$1.commandType(factor, 'number', param + '.factor', env.commandStr)\n              check$1.commandType(units, 'number', param + '.units', env.commandStr)\n              return [factor, units]\n            },\n            function (env, scope, value) {\n              check$1.optional(function () {\n                env.assert(scope,\n                  value + '&&typeof ' + value + '===\"object\"',\n                  'invalid ' + prop)\n              })\n\n              var FACTOR = scope.def(value, '.factor|0')\n              var UNITS = scope.def(value, '.units|0')\n\n              return [FACTOR, UNITS]\n            })\n\n        case S_CULL_FACE:\n          return parseParam(\n            function (value) {\n              var face = 0\n              if (value === 'front') {\n                face = GL_FRONT\n              } else if (value === 'back') {\n                face = GL_BACK\n              }\n              check$1.command(!!face, param, env.commandStr)\n              return face\n            },\n            function (env, scope, value) {\n              check$1.optional(function () {\n                env.assert(scope,\n                  value + '===\"front\"||' +\n                  value + '===\"back\"',\n                  'invalid cull.face')\n              })\n              return scope.def(value, '===\"front\"?', GL_FRONT, ':', GL_BACK)\n            })\n\n        case S_LINE_WIDTH:\n          return parseParam(\n            function (value) {\n              check$1.command(\n                typeof value === 'number' &&\n                value >= limits.lineWidthDims[0] &&\n                value <= limits.lineWidthDims[1],\n                'invalid line width, must be a positive number between ' +\n                limits.lineWidthDims[0] + ' and ' + limits.lineWidthDims[1], env.commandStr)\n              return value\n            },\n            function (env, scope, value) {\n              check$1.optional(function () {\n                env.assert(scope,\n                  'typeof ' + value + '===\"number\"&&' +\n                  value + '>=' + limits.lineWidthDims[0] + '&&' +\n                  value + '<=' + limits.lineWidthDims[1],\n                  'invalid line width')\n              })\n\n              return value\n            })\n\n        case S_FRONT_FACE:\n          return parseParam(\n            function (value) {\n              check$1.commandParameter(value, orientationType, param, env.commandStr)\n              return orientationType[value]\n            },\n            function (env, scope, value) {\n              check$1.optional(function () {\n                env.assert(scope,\n                  value + '===\"cw\"||' +\n                  value + '===\"ccw\"',\n                  'invalid frontFace, must be one of cw,ccw')\n              })\n              return scope.def(value + '===\"cw\"?' + GL_CW + ':' + GL_CCW)\n            })\n\n        case S_COLOR_MASK:\n          return parseParam(\n            function (value) {\n              check$1.command(\n                isArrayLike(value) && value.length === 4,\n                'color.mask must be length 4 array', env.commandStr)\n              return value.map(function (v) { return !!v })\n            },\n            function (env, scope, value) {\n              check$1.optional(function () {\n                env.assert(scope,\n                  env.shared.isArrayLike + '(' + value + ')&&' +\n                  value + '.length===4',\n                  'invalid color.mask')\n              })\n              return loop(4, function (i) {\n                return '!!' + value + '[' + i + ']'\n              })\n            })\n\n        case S_SAMPLE_COVERAGE:\n          return parseParam(\n            function (value) {\n              check$1.command(typeof value === 'object' && value, param, env.commandStr)\n              var sampleValue = 'value' in value ? value.value : 1\n              var sampleInvert = !!value.invert\n              check$1.command(\n                typeof sampleValue === 'number' &&\n                sampleValue >= 0 && sampleValue <= 1,\n                'sample.coverage.value must be a number between 0 and 1', env.commandStr)\n              return [sampleValue, sampleInvert]\n            },\n            function (env, scope, value) {\n              check$1.optional(function () {\n                env.assert(scope,\n                  value + '&&typeof ' + value + '===\"object\"',\n                  'invalid sample.coverage')\n              })\n              var VALUE = scope.def(\n                '\"value\" in ', value, '?+', value, '.value:1')\n              var INVERT = scope.def('!!', value, '.invert')\n              return [VALUE, INVERT]\n            })\n      }\n    })\n\n    return STATE\n  }\n\n  function parseUniforms (uniforms, env) {\n    var staticUniforms = uniforms.static\n    var dynamicUniforms = uniforms.dynamic\n\n    var UNIFORMS = {}\n\n    Object.keys(staticUniforms).forEach(function (name) {\n      var value = staticUniforms[name]\n      var result\n      if (typeof value === 'number' ||\n          typeof value === 'boolean') {\n        result = createStaticDecl(function () {\n          return value\n        })\n      } else if (typeof value === 'function') {\n        var reglType = value._reglType\n        if (reglType === 'texture2d' ||\n            reglType === 'textureCube') {\n          result = createStaticDecl(function (env) {\n            return env.link(value)\n          })\n        } else if (reglType === 'framebuffer' ||\n                   reglType === 'framebufferCube') {\n          check$1.command(value.color.length > 0,\n            'missing color attachment for framebuffer sent to uniform \"' + name + '\"', env.commandStr)\n          result = createStaticDecl(function (env) {\n            return env.link(value.color[0])\n          })\n        } else {\n          check$1.commandRaise('invalid data for uniform \"' + name + '\"', env.commandStr)\n        }\n      } else if (isArrayLike(value)) {\n        result = createStaticDecl(function (env) {\n          var ITEM = env.global.def('[',\n            loop(value.length, function (i) {\n              check$1.command(\n                typeof value[i] === 'number' ||\n                typeof value[i] === 'boolean',\n                'invalid uniform ' + name, env.commandStr)\n              return value[i]\n            }), ']')\n          return ITEM\n        })\n      } else {\n        check$1.commandRaise('invalid or missing data for uniform \"' + name + '\"', env.commandStr)\n      }\n      result.value = value\n      UNIFORMS[name] = result\n    })\n\n    Object.keys(dynamicUniforms).forEach(function (key) {\n      var dyn = dynamicUniforms[key]\n      UNIFORMS[key] = createDynamicDecl(dyn, function (env, scope) {\n        return env.invoke(scope, dyn)\n      })\n    })\n\n    return UNIFORMS\n  }\n\n  function parseAttributes (attributes, env) {\n    var staticAttributes = attributes.static\n    var dynamicAttributes = attributes.dynamic\n\n    var attributeDefs = {}\n\n    Object.keys(staticAttributes).forEach(function (attribute) {\n      var value = staticAttributes[attribute]\n      var id = stringStore.id(attribute)\n\n      var record = new AttributeRecord()\n      if (isBufferArgs(value)) {\n        record.state = ATTRIB_STATE_POINTER\n        record.buffer = bufferState.getBuffer(\n          bufferState.create(value, GL_ARRAY_BUFFER$2, false, true))\n        record.type = 0\n      } else {\n        var buffer = bufferState.getBuffer(value)\n        if (buffer) {\n          record.state = ATTRIB_STATE_POINTER\n          record.buffer = buffer\n          record.type = 0\n        } else {\n          check$1.command(typeof value === 'object' && value,\n            'invalid data for attribute ' + attribute, env.commandStr)\n          if ('constant' in value) {\n            var constant = value.constant\n            record.buffer = 'null'\n            record.state = ATTRIB_STATE_CONSTANT\n            if (typeof constant === 'number') {\n              record.x = constant\n            } else {\n              check$1.command(\n                isArrayLike(constant) &&\n                constant.length > 0 &&\n                constant.length <= 4,\n                'invalid constant for attribute ' + attribute, env.commandStr)\n              CUTE_COMPONENTS.forEach(function (c, i) {\n                if (i < constant.length) {\n                  record[c] = constant[i]\n                }\n              })\n            }\n          } else {\n            if (isBufferArgs(value.buffer)) {\n              buffer = bufferState.getBuffer(\n                bufferState.create(value.buffer, GL_ARRAY_BUFFER$2, false, true))\n            } else {\n              buffer = bufferState.getBuffer(value.buffer)\n            }\n            check$1.command(!!buffer, 'missing buffer for attribute \"' + attribute + '\"', env.commandStr)\n\n            var offset = value.offset | 0\n            check$1.command(offset >= 0,\n              'invalid offset for attribute \"' + attribute + '\"', env.commandStr)\n\n            var stride = value.stride | 0\n            check$1.command(stride >= 0 && stride < 256,\n              'invalid stride for attribute \"' + attribute + '\", must be integer betweeen [0, 255]', env.commandStr)\n\n            var size = value.size | 0\n            check$1.command(!('size' in value) || (size > 0 && size <= 4),\n              'invalid size for attribute \"' + attribute + '\", must be 1,2,3,4', env.commandStr)\n\n            var normalized = !!value.normalized\n\n            var type = 0\n            if ('type' in value) {\n              check$1.commandParameter(\n                value.type, glTypes,\n                'invalid type for attribute ' + attribute, env.commandStr)\n              type = glTypes[value.type]\n            }\n\n            var divisor = value.divisor | 0\n            if ('divisor' in value) {\n              check$1.command(divisor === 0 || extInstancing,\n                'cannot specify divisor for attribute \"' + attribute + '\", instancing not supported', env.commandStr)\n              check$1.command(divisor >= 0,\n                'invalid divisor for attribute \"' + attribute + '\"', env.commandStr)\n            }\n\n            check$1.optional(function () {\n              var command = env.commandStr\n\n              var VALID_KEYS = [\n                'buffer',\n                'offset',\n                'divisor',\n                'normalized',\n                'type',\n                'size',\n                'stride'\n              ]\n\n              Object.keys(value).forEach(function (prop) {\n                check$1.command(\n                  VALID_KEYS.indexOf(prop) >= 0,\n                  'unknown parameter \"' + prop + '\" for attribute pointer \"' + attribute + '\" (valid parameters are ' + VALID_KEYS + ')',\n                  command)\n              })\n            })\n\n            record.buffer = buffer\n            record.state = ATTRIB_STATE_POINTER\n            record.size = size\n            record.normalized = normalized\n            record.type = type || buffer.dtype\n            record.offset = offset\n            record.stride = stride\n            record.divisor = divisor\n          }\n        }\n      }\n\n      attributeDefs[attribute] = createStaticDecl(function (env, scope) {\n        var cache = env.attribCache\n        if (id in cache) {\n          return cache[id]\n        }\n        var result = {\n          isStream: false\n        }\n        Object.keys(record).forEach(function (key) {\n          result[key] = record[key]\n        })\n        if (record.buffer) {\n          result.buffer = env.link(record.buffer)\n          result.type = result.type || (result.buffer + '.dtype')\n        }\n        cache[id] = result\n        return result\n      })\n    })\n\n    Object.keys(dynamicAttributes).forEach(function (attribute) {\n      var dyn = dynamicAttributes[attribute]\n\n      function appendAttributeCode (env, block) {\n        var VALUE = env.invoke(block, dyn)\n\n        var shared = env.shared\n        var constants = env.constants\n\n        var IS_BUFFER_ARGS = shared.isBufferArgs\n        var BUFFER_STATE = shared.buffer\n\n        // Perform validation on attribute\n        check$1.optional(function () {\n          env.assert(block,\n            VALUE + '&&(typeof ' + VALUE + '===\"object\"||typeof ' +\n            VALUE + '===\"function\")&&(' +\n            IS_BUFFER_ARGS + '(' + VALUE + ')||' +\n            BUFFER_STATE + '.getBuffer(' + VALUE + ')||' +\n            BUFFER_STATE + '.getBuffer(' + VALUE + '.buffer)||' +\n            IS_BUFFER_ARGS + '(' + VALUE + '.buffer)||' +\n            '(\"constant\" in ' + VALUE +\n            '&&(typeof ' + VALUE + '.constant===\"number\"||' +\n            shared.isArrayLike + '(' + VALUE + '.constant))))',\n            'invalid dynamic attribute \"' + attribute + '\"')\n        })\n\n        // allocate names for result\n        var result = {\n          isStream: block.def(false)\n        }\n        var defaultRecord = new AttributeRecord()\n        defaultRecord.state = ATTRIB_STATE_POINTER\n        Object.keys(defaultRecord).forEach(function (key) {\n          result[key] = block.def('' + defaultRecord[key])\n        })\n\n        var BUFFER = result.buffer\n        var TYPE = result.type\n        block(\n          'if(', IS_BUFFER_ARGS, '(', VALUE, ')){',\n          result.isStream, '=true;',\n          BUFFER, '=', BUFFER_STATE, '.createStream(', GL_ARRAY_BUFFER$2, ',', VALUE, ');',\n          TYPE, '=', BUFFER, '.dtype;',\n          '}else{',\n          BUFFER, '=', BUFFER_STATE, '.getBuffer(', VALUE, ');',\n          'if(', BUFFER, '){',\n          TYPE, '=', BUFFER, '.dtype;',\n          '}else if(\"constant\" in ', VALUE, '){',\n          result.state, '=', ATTRIB_STATE_CONSTANT, ';',\n          'if(typeof ' + VALUE + '.constant === \"number\"){',\n          result[CUTE_COMPONENTS[0]], '=', VALUE, '.constant;',\n          CUTE_COMPONENTS.slice(1).map(function (n) {\n            return result[n]\n          }).join('='), '=0;',\n          '}else{',\n          CUTE_COMPONENTS.map(function (name, i) {\n            return (\n              result[name] + '=' + VALUE + '.constant.length>' + i +\n              '?' + VALUE + '.constant[' + i + ']:0;'\n            )\n          }).join(''),\n          '}}else{',\n          'if(', IS_BUFFER_ARGS, '(', VALUE, '.buffer)){',\n          BUFFER, '=', BUFFER_STATE, '.createStream(', GL_ARRAY_BUFFER$2, ',', VALUE, '.buffer);',\n          '}else{',\n          BUFFER, '=', BUFFER_STATE, '.getBuffer(', VALUE, '.buffer);',\n          '}',\n          TYPE, '=\"type\" in ', VALUE, '?',\n          constants.glTypes, '[', VALUE, '.type]:', BUFFER, '.dtype;',\n          result.normalized, '=!!', VALUE, '.normalized;')\n        function emitReadRecord (name) {\n          block(result[name], '=', VALUE, '.', name, '|0;')\n        }\n        emitReadRecord('size')\n        emitReadRecord('offset')\n        emitReadRecord('stride')\n        emitReadRecord('divisor')\n\n        block('}}')\n\n        block.exit(\n          'if(', result.isStream, '){',\n          BUFFER_STATE, '.destroyStream(', BUFFER, ');',\n          '}')\n\n        return result\n      }\n\n      attributeDefs[attribute] = createDynamicDecl(dyn, appendAttributeCode)\n    })\n\n    return attributeDefs\n  }\n\n  function parseVAO (options, env) {\n    var staticOptions = options.static\n    var dynamicOptions = options.dynamic\n    if (S_VAO in staticOptions) {\n      var vao = staticOptions[S_VAO]\n      if (vao !== null && attributeState.getVAO(vao) === null) {\n        vao = attributeState.createVAO(vao)\n      }\n      return createStaticDecl(function (env) {\n        return env.link(attributeState.getVAO(vao))\n      })\n    } else if (S_VAO in dynamicOptions) {\n      var dyn = dynamicOptions[S_VAO]\n      return createDynamicDecl(dyn, function (env, scope) {\n        var vaoRef = env.invoke(scope, dyn)\n        return scope.def(env.shared.vao + '.getVAO(' + vaoRef + ')')\n      })\n    }\n    return null\n  }\n\n  function parseContext (context) {\n    var staticContext = context.static\n    var dynamicContext = context.dynamic\n    var result = {}\n\n    Object.keys(staticContext).forEach(function (name) {\n      var value = staticContext[name]\n      result[name] = createStaticDecl(function (env, scope) {\n        if (typeof value === 'number' || typeof value === 'boolean') {\n          return '' + value\n        } else {\n          return env.link(value)\n        }\n      })\n    })\n\n    Object.keys(dynamicContext).forEach(function (name) {\n      var dyn = dynamicContext[name]\n      result[name] = createDynamicDecl(dyn, function (env, scope) {\n        return env.invoke(scope, dyn)\n      })\n    })\n\n    return result\n  }\n\n  function parseArguments (options, attributes, uniforms, context, env) {\n    var staticOptions = options.static\n    var dynamicOptions = options.dynamic\n\n    check$1.optional(function () {\n      var KEY_NAMES = [\n        S_FRAMEBUFFER,\n        S_VERT,\n        S_FRAG,\n        S_ELEMENTS,\n        S_PRIMITIVE,\n        S_OFFSET,\n        S_COUNT,\n        S_INSTANCES,\n        S_PROFILE,\n        S_VAO\n      ].concat(GL_STATE_NAMES)\n\n      function checkKeys (dict) {\n        Object.keys(dict).forEach(function (key) {\n          check$1.command(\n            KEY_NAMES.indexOf(key) >= 0,\n            'unknown parameter \"' + key + '\"',\n            env.commandStr)\n        })\n      }\n\n      checkKeys(staticOptions)\n      checkKeys(dynamicOptions)\n    })\n\n    var attribLocations = parseAttribLocations(options, attributes)\n\n    var framebuffer = parseFramebuffer(options, env)\n    var viewportAndScissor = parseViewportScissor(options, framebuffer, env)\n    var draw = parseDraw(options, env)\n    var state = parseGLState(options, env)\n    var shader = parseProgram(options, env, attribLocations)\n\n    function copyBox (name) {\n      var defn = viewportAndScissor[name]\n      if (defn) {\n        state[name] = defn\n      }\n    }\n    copyBox(S_VIEWPORT)\n    copyBox(propName(S_SCISSOR_BOX))\n\n    var dirty = Object.keys(state).length > 0\n\n    var result = {\n      framebuffer: framebuffer,\n      draw: draw,\n      shader: shader,\n      state: state,\n      dirty: dirty,\n      scopeVAO: null,\n      drawVAO: null,\n      useVAO: false,\n      attributes: {}\n    }\n\n    result.profile = parseProfile(options, env)\n    result.uniforms = parseUniforms(uniforms, env)\n    result.drawVAO = result.scopeVAO = parseVAO(options, env)\n    // special case: check if we can statically allocate a vertex array object for this program\n    if (!result.drawVAO && shader.program && !attribLocations && extensions.angle_instanced_arrays) {\n      var useVAO = true\n      var staticBindings = shader.program.attributes.map(function (attr) {\n        var binding = attributes.static[attr]\n        useVAO = useVAO && !!binding\n        return binding\n      })\n      if (useVAO && staticBindings.length > 0) {\n        var vao = attributeState.getVAO(attributeState.createVAO(staticBindings))\n        result.drawVAO = new Declaration(null, null, null, function (env, scope) {\n          return env.link(vao)\n        })\n        result.useVAO = true\n      }\n    }\n    if (attribLocations) {\n      result.useVAO = true\n    } else {\n      result.attributes = parseAttributes(attributes, env)\n    }\n    result.context = parseContext(context, env)\n    return result\n  }\n\n  // ===================================================\n  // ===================================================\n  // COMMON UPDATE FUNCTIONS\n  // ===================================================\n  // ===================================================\n  function emitContext (env, scope, context) {\n    var shared = env.shared\n    var CONTEXT = shared.context\n\n    var contextEnter = env.scope()\n\n    Object.keys(context).forEach(function (name) {\n      scope.save(CONTEXT, '.' + name)\n      var defn = context[name]\n      var value = defn.append(env, scope)\n      if (Array.isArray(value)) {\n        contextEnter(CONTEXT, '.', name, '=[', value.join(), '];')\n      } else {\n        contextEnter(CONTEXT, '.', name, '=', value, ';')\n      }\n    })\n\n    scope(contextEnter)\n  }\n\n  // ===================================================\n  // ===================================================\n  // COMMON DRAWING FUNCTIONS\n  // ===================================================\n  // ===================================================\n  function emitPollFramebuffer (env, scope, framebuffer, skipCheck) {\n    var shared = env.shared\n\n    var GL = shared.gl\n    var FRAMEBUFFER_STATE = shared.framebuffer\n    var EXT_DRAW_BUFFERS\n    if (extDrawBuffers) {\n      EXT_DRAW_BUFFERS = scope.def(shared.extensions, '.webgl_draw_buffers')\n    }\n\n    var constants = env.constants\n\n    var DRAW_BUFFERS = constants.drawBuffer\n    var BACK_BUFFER = constants.backBuffer\n\n    var NEXT\n    if (framebuffer) {\n      NEXT = framebuffer.append(env, scope)\n    } else {\n      NEXT = scope.def(FRAMEBUFFER_STATE, '.next')\n    }\n\n    if (!skipCheck) {\n      scope('if(', NEXT, '!==', FRAMEBUFFER_STATE, '.cur){')\n    }\n    scope(\n      'if(', NEXT, '){',\n      GL, '.bindFramebuffer(', GL_FRAMEBUFFER$2, ',', NEXT, '.framebuffer);')\n    if (extDrawBuffers) {\n      scope(EXT_DRAW_BUFFERS, '.drawBuffersWEBGL(',\n        DRAW_BUFFERS, '[', NEXT, '.colorAttachments.length]);')\n    }\n    scope('}else{',\n      GL, '.bindFramebuffer(', GL_FRAMEBUFFER$2, ',null);')\n    if (extDrawBuffers) {\n      scope(EXT_DRAW_BUFFERS, '.drawBuffersWEBGL(', BACK_BUFFER, ');')\n    }\n    scope(\n      '}',\n      FRAMEBUFFER_STATE, '.cur=', NEXT, ';')\n    if (!skipCheck) {\n      scope('}')\n    }\n  }\n\n  function emitPollState (env, scope, args) {\n    var shared = env.shared\n\n    var GL = shared.gl\n\n    var CURRENT_VARS = env.current\n    var NEXT_VARS = env.next\n    var CURRENT_STATE = shared.current\n    var NEXT_STATE = shared.next\n\n    var block = env.cond(CURRENT_STATE, '.dirty')\n\n    GL_STATE_NAMES.forEach(function (prop) {\n      var param = propName(prop)\n      if (param in args.state) {\n        return\n      }\n\n      var NEXT, CURRENT\n      if (param in NEXT_VARS) {\n        NEXT = NEXT_VARS[param]\n        CURRENT = CURRENT_VARS[param]\n        var parts = loop(currentState[param].length, function (i) {\n          return block.def(NEXT, '[', i, ']')\n        })\n        block(env.cond(parts.map(function (p, i) {\n          return p + '!==' + CURRENT + '[' + i + ']'\n        }).join('||'))\n          .then(\n            GL, '.', GL_VARIABLES[param], '(', parts, ');',\n            parts.map(function (p, i) {\n              return CURRENT + '[' + i + ']=' + p\n            }).join(';'), ';'))\n      } else {\n        NEXT = block.def(NEXT_STATE, '.', param)\n        var ifte = env.cond(NEXT, '!==', CURRENT_STATE, '.', param)\n        block(ifte)\n        if (param in GL_FLAGS) {\n          ifte(\n            env.cond(NEXT)\n              .then(GL, '.enable(', GL_FLAGS[param], ');')\n              .else(GL, '.disable(', GL_FLAGS[param], ');'),\n            CURRENT_STATE, '.', param, '=', NEXT, ';')\n        } else {\n          ifte(\n            GL, '.', GL_VARIABLES[param], '(', NEXT, ');',\n            CURRENT_STATE, '.', param, '=', NEXT, ';')\n        }\n      }\n    })\n    if (Object.keys(args.state).length === 0) {\n      block(CURRENT_STATE, '.dirty=false;')\n    }\n    scope(block)\n  }\n\n  function emitSetOptions (env, scope, options, filter) {\n    var shared = env.shared\n    var CURRENT_VARS = env.current\n    var CURRENT_STATE = shared.current\n    var GL = shared.gl\n    sortState(Object.keys(options)).forEach(function (param) {\n      var defn = options[param]\n      if (filter && !filter(defn)) {\n        return\n      }\n      var variable = defn.append(env, scope)\n      if (GL_FLAGS[param]) {\n        var flag = GL_FLAGS[param]\n        if (isStatic(defn)) {\n          if (variable) {\n            scope(GL, '.enable(', flag, ');')\n          } else {\n            scope(GL, '.disable(', flag, ');')\n          }\n        } else {\n          scope(env.cond(variable)\n            .then(GL, '.enable(', flag, ');')\n            .else(GL, '.disable(', flag, ');'))\n        }\n        scope(CURRENT_STATE, '.', param, '=', variable, ';')\n      } else if (isArrayLike(variable)) {\n        var CURRENT = CURRENT_VARS[param]\n        scope(\n          GL, '.', GL_VARIABLES[param], '(', variable, ');',\n          variable.map(function (v, i) {\n            return CURRENT + '[' + i + ']=' + v\n          }).join(';'), ';')\n      } else {\n        scope(\n          GL, '.', GL_VARIABLES[param], '(', variable, ');',\n          CURRENT_STATE, '.', param, '=', variable, ';')\n      }\n    })\n  }\n\n  function injectExtensions (env, scope) {\n    if (extInstancing) {\n      env.instancing = scope.def(\n        env.shared.extensions, '.angle_instanced_arrays')\n    }\n  }\n\n  function emitProfile (env, scope, args, useScope, incrementCounter) {\n    var shared = env.shared\n    var STATS = env.stats\n    var CURRENT_STATE = shared.current\n    var TIMER = shared.timer\n    var profileArg = args.profile\n\n    function perfCounter () {\n      if (typeof performance === 'undefined') {\n        return 'Date.now()'\n      } else {\n        return 'performance.now()'\n      }\n    }\n\n    var CPU_START, QUERY_COUNTER\n    function emitProfileStart (block) {\n      CPU_START = scope.def()\n      block(CPU_START, '=', perfCounter(), ';')\n      if (typeof incrementCounter === 'string') {\n        block(STATS, '.count+=', incrementCounter, ';')\n      } else {\n        block(STATS, '.count++;')\n      }\n      if (timer) {\n        if (useScope) {\n          QUERY_COUNTER = scope.def()\n          block(QUERY_COUNTER, '=', TIMER, '.getNumPendingQueries();')\n        } else {\n          block(TIMER, '.beginQuery(', STATS, ');')\n        }\n      }\n    }\n\n    function emitProfileEnd (block) {\n      block(STATS, '.cpuTime+=', perfCounter(), '-', CPU_START, ';')\n      if (timer) {\n        if (useScope) {\n          block(TIMER, '.pushScopeStats(',\n            QUERY_COUNTER, ',',\n            TIMER, '.getNumPendingQueries(),',\n            STATS, ');')\n        } else {\n          block(TIMER, '.endQuery();')\n        }\n      }\n    }\n\n    function scopeProfile (value) {\n      var prev = scope.def(CURRENT_STATE, '.profile')\n      scope(CURRENT_STATE, '.profile=', value, ';')\n      scope.exit(CURRENT_STATE, '.profile=', prev, ';')\n    }\n\n    var USE_PROFILE\n    if (profileArg) {\n      if (isStatic(profileArg)) {\n        if (profileArg.enable) {\n          emitProfileStart(scope)\n          emitProfileEnd(scope.exit)\n          scopeProfile('true')\n        } else {\n          scopeProfile('false')\n        }\n        return\n      }\n      USE_PROFILE = profileArg.append(env, scope)\n      scopeProfile(USE_PROFILE)\n    } else {\n      USE_PROFILE = scope.def(CURRENT_STATE, '.profile')\n    }\n\n    var start = env.block()\n    emitProfileStart(start)\n    scope('if(', USE_PROFILE, '){', start, '}')\n    var end = env.block()\n    emitProfileEnd(end)\n    scope.exit('if(', USE_PROFILE, '){', end, '}')\n  }\n\n  function emitAttributes (env, scope, args, attributes, filter) {\n    var shared = env.shared\n\n    function typeLength (x) {\n      switch (x) {\n        case GL_FLOAT_VEC2:\n        case GL_INT_VEC2:\n        case GL_BOOL_VEC2:\n          return 2\n        case GL_FLOAT_VEC3:\n        case GL_INT_VEC3:\n        case GL_BOOL_VEC3:\n          return 3\n        case GL_FLOAT_VEC4:\n        case GL_INT_VEC4:\n        case GL_BOOL_VEC4:\n          return 4\n        default:\n          return 1\n      }\n    }\n\n    function emitBindAttribute (ATTRIBUTE, size, record) {\n      var GL = shared.gl\n\n      var LOCATION = scope.def(ATTRIBUTE, '.location')\n      var BINDING = scope.def(shared.attributes, '[', LOCATION, ']')\n\n      var STATE = record.state\n      var BUFFER = record.buffer\n      var CONST_COMPONENTS = [\n        record.x,\n        record.y,\n        record.z,\n        record.w\n      ]\n\n      var COMMON_KEYS = [\n        'buffer',\n        'normalized',\n        'offset',\n        'stride'\n      ]\n\n      function emitBuffer () {\n        scope(\n          'if(!', BINDING, '.buffer){',\n          GL, '.enableVertexAttribArray(', LOCATION, ');}')\n\n        var TYPE = record.type\n        var SIZE\n        if (!record.size) {\n          SIZE = size\n        } else {\n          SIZE = scope.def(record.size, '||', size)\n        }\n\n        scope('if(',\n          BINDING, '.type!==', TYPE, '||',\n          BINDING, '.size!==', SIZE, '||',\n          COMMON_KEYS.map(function (key) {\n            return BINDING + '.' + key + '!==' + record[key]\n          }).join('||'),\n          '){',\n          GL, '.bindBuffer(', GL_ARRAY_BUFFER$2, ',', BUFFER, '.buffer);',\n          GL, '.vertexAttribPointer(', [\n            LOCATION,\n            SIZE,\n            TYPE,\n            record.normalized,\n            record.stride,\n            record.offset\n          ], ');',\n          BINDING, '.type=', TYPE, ';',\n          BINDING, '.size=', SIZE, ';',\n          COMMON_KEYS.map(function (key) {\n            return BINDING + '.' + key + '=' + record[key] + ';'\n          }).join(''),\n          '}')\n\n        if (extInstancing) {\n          var DIVISOR = record.divisor\n          scope(\n            'if(', BINDING, '.divisor!==', DIVISOR, '){',\n            env.instancing, '.vertexAttribDivisorANGLE(', [LOCATION, DIVISOR], ');',\n            BINDING, '.divisor=', DIVISOR, ';}')\n        }\n      }\n\n      function emitConstant () {\n        scope(\n          'if(', BINDING, '.buffer){',\n          GL, '.disableVertexAttribArray(', LOCATION, ');',\n          BINDING, '.buffer=null;',\n          '}if(', CUTE_COMPONENTS.map(function (c, i) {\n            return BINDING + '.' + c + '!==' + CONST_COMPONENTS[i]\n          }).join('||'), '){',\n          GL, '.vertexAttrib4f(', LOCATION, ',', CONST_COMPONENTS, ');',\n          CUTE_COMPONENTS.map(function (c, i) {\n            return BINDING + '.' + c + '=' + CONST_COMPONENTS[i] + ';'\n          }).join(''),\n          '}')\n      }\n\n      if (STATE === ATTRIB_STATE_POINTER) {\n        emitBuffer()\n      } else if (STATE === ATTRIB_STATE_CONSTANT) {\n        emitConstant()\n      } else {\n        scope('if(', STATE, '===', ATTRIB_STATE_POINTER, '){')\n        emitBuffer()\n        scope('}else{')\n        emitConstant()\n        scope('}')\n      }\n    }\n\n    attributes.forEach(function (attribute) {\n      var name = attribute.name\n      var arg = args.attributes[name]\n      var record\n      if (arg) {\n        if (!filter(arg)) {\n          return\n        }\n        record = arg.append(env, scope)\n      } else {\n        if (!filter(SCOPE_DECL)) {\n          return\n        }\n        var scopeAttrib = env.scopeAttrib(name)\n        check$1.optional(function () {\n          env.assert(scope,\n            scopeAttrib + '.state',\n            'missing attribute ' + name)\n        })\n        record = {}\n        Object.keys(new AttributeRecord()).forEach(function (key) {\n          record[key] = scope.def(scopeAttrib, '.', key)\n        })\n      }\n      emitBindAttribute(\n        env.link(attribute), typeLength(attribute.info.type), record)\n    })\n  }\n\n  function emitUniforms (env, scope, args, uniforms, filter) {\n    var shared = env.shared\n    var GL = shared.gl\n\n    var infix\n    for (var i = 0; i < uniforms.length; ++i) {\n      var uniform = uniforms[i]\n      var name = uniform.name\n      var type = uniform.info.type\n      var arg = args.uniforms[name]\n      var UNIFORM = env.link(uniform)\n      var LOCATION = UNIFORM + '.location'\n\n      var VALUE\n      if (arg) {\n        if (!filter(arg)) {\n          continue\n        }\n        if (isStatic(arg)) {\n          var value = arg.value\n          check$1.command(\n            value !== null && typeof value !== 'undefined',\n            'missing uniform \"' + name + '\"', env.commandStr)\n          if (type === GL_SAMPLER_2D || type === GL_SAMPLER_CUBE) {\n            check$1.command(\n              typeof value === 'function' &&\n              ((type === GL_SAMPLER_2D &&\n                (value._reglType === 'texture2d' ||\n                value._reglType === 'framebuffer')) ||\n              (type === GL_SAMPLER_CUBE &&\n                (value._reglType === 'textureCube' ||\n                value._reglType === 'framebufferCube'))),\n              'invalid texture for uniform ' + name, env.commandStr)\n            var TEX_VALUE = env.link(value._texture || value.color[0]._texture)\n            scope(GL, '.uniform1i(', LOCATION, ',', TEX_VALUE + '.bind());')\n            scope.exit(TEX_VALUE, '.unbind();')\n          } else if (\n            type === GL_FLOAT_MAT2 ||\n            type === GL_FLOAT_MAT3 ||\n            type === GL_FLOAT_MAT4) {\n            check$1.optional(function () {\n              check$1.command(isArrayLike(value),\n                'invalid matrix for uniform ' + name, env.commandStr)\n              check$1.command(\n                (type === GL_FLOAT_MAT2 && value.length === 4) ||\n                (type === GL_FLOAT_MAT3 && value.length === 9) ||\n                (type === GL_FLOAT_MAT4 && value.length === 16),\n                'invalid length for matrix uniform ' + name, env.commandStr)\n            })\n            var MAT_VALUE = env.global.def('new Float32Array([' +\n              Array.prototype.slice.call(value) + '])')\n            var dim = 2\n            if (type === GL_FLOAT_MAT3) {\n              dim = 3\n            } else if (type === GL_FLOAT_MAT4) {\n              dim = 4\n            }\n            scope(\n              GL, '.uniformMatrix', dim, 'fv(',\n              LOCATION, ',false,', MAT_VALUE, ');')\n          } else {\n            switch (type) {\n              case GL_FLOAT$8:\n                check$1.commandType(value, 'number', 'uniform ' + name, env.commandStr)\n                infix = '1f'\n                break\n              case GL_FLOAT_VEC2:\n                check$1.command(\n                  isArrayLike(value) && value.length === 2,\n                  'uniform ' + name, env.commandStr)\n                infix = '2f'\n                break\n              case GL_FLOAT_VEC3:\n                check$1.command(\n                  isArrayLike(value) && value.length === 3,\n                  'uniform ' + name, env.commandStr)\n                infix = '3f'\n                break\n              case GL_FLOAT_VEC4:\n                check$1.command(\n                  isArrayLike(value) && value.length === 4,\n                  'uniform ' + name, env.commandStr)\n                infix = '4f'\n                break\n              case GL_BOOL:\n                check$1.commandType(value, 'boolean', 'uniform ' + name, env.commandStr)\n                infix = '1i'\n                break\n              case GL_INT$3:\n                check$1.commandType(value, 'number', 'uniform ' + name, env.commandStr)\n                infix = '1i'\n                break\n              case GL_BOOL_VEC2:\n                check$1.command(\n                  isArrayLike(value) && value.length === 2,\n                  'uniform ' + name, env.commandStr)\n                infix = '2i'\n                break\n              case GL_INT_VEC2:\n                check$1.command(\n                  isArrayLike(value) && value.length === 2,\n                  'uniform ' + name, env.commandStr)\n                infix = '2i'\n                break\n              case GL_BOOL_VEC3:\n                check$1.command(\n                  isArrayLike(value) && value.length === 3,\n                  'uniform ' + name, env.commandStr)\n                infix = '3i'\n                break\n              case GL_INT_VEC3:\n                check$1.command(\n                  isArrayLike(value) && value.length === 3,\n                  'uniform ' + name, env.commandStr)\n                infix = '3i'\n                break\n              case GL_BOOL_VEC4:\n                check$1.command(\n                  isArrayLike(value) && value.length === 4,\n                  'uniform ' + name, env.commandStr)\n                infix = '4i'\n                break\n              case GL_INT_VEC4:\n                check$1.command(\n                  isArrayLike(value) && value.length === 4,\n                  'uniform ' + name, env.commandStr)\n                infix = '4i'\n                break\n            }\n            scope(GL, '.uniform', infix, '(', LOCATION, ',',\n              isArrayLike(value) ? Array.prototype.slice.call(value) : value,\n              ');')\n          }\n          continue\n        } else {\n          VALUE = arg.append(env, scope)\n        }\n      } else {\n        if (!filter(SCOPE_DECL)) {\n          continue\n        }\n        VALUE = scope.def(shared.uniforms, '[', stringStore.id(name), ']')\n      }\n\n      if (type === GL_SAMPLER_2D) {\n        check$1(!Array.isArray(VALUE), 'must specify a scalar prop for textures')\n        scope(\n          'if(', VALUE, '&&', VALUE, '._reglType===\"framebuffer\"){',\n          VALUE, '=', VALUE, '.color[0];',\n          '}')\n      } else if (type === GL_SAMPLER_CUBE) {\n        check$1(!Array.isArray(VALUE), 'must specify a scalar prop for cube maps')\n        scope(\n          'if(', VALUE, '&&', VALUE, '._reglType===\"framebufferCube\"){',\n          VALUE, '=', VALUE, '.color[0];',\n          '}')\n      }\n\n      // perform type validation\n      check$1.optional(function () {\n        function emitCheck (pred, message) {\n          env.assert(scope, pred,\n            'bad data or missing for uniform \"' + name + '\".  ' + message)\n        }\n\n        function checkType (type) {\n          check$1(!Array.isArray(VALUE), 'must not specify an array type for uniform')\n          emitCheck(\n            'typeof ' + VALUE + '===\"' + type + '\"',\n            'invalid type, expected ' + type)\n        }\n\n        function checkVector (n, type) {\n          if (Array.isArray(VALUE)) {\n            check$1(VALUE.length === n, 'must have length ' + n)\n          } else {\n            emitCheck(\n              shared.isArrayLike + '(' + VALUE + ')&&' + VALUE + '.length===' + n,\n              'invalid vector, should have length ' + n, env.commandStr)\n          }\n        }\n\n        function checkTexture (target) {\n          check$1(!Array.isArray(VALUE), 'must not specify a value type')\n          emitCheck(\n            'typeof ' + VALUE + '===\"function\"&&' +\n            VALUE + '._reglType===\"texture' +\n            (target === GL_TEXTURE_2D$3 ? '2d' : 'Cube') + '\"',\n            'invalid texture type', env.commandStr)\n        }\n\n        switch (type) {\n          case GL_INT$3:\n            checkType('number')\n            break\n          case GL_INT_VEC2:\n            checkVector(2, 'number')\n            break\n          case GL_INT_VEC3:\n            checkVector(3, 'number')\n            break\n          case GL_INT_VEC4:\n            checkVector(4, 'number')\n            break\n          case GL_FLOAT$8:\n            checkType('number')\n            break\n          case GL_FLOAT_VEC2:\n            checkVector(2, 'number')\n            break\n          case GL_FLOAT_VEC3:\n            checkVector(3, 'number')\n            break\n          case GL_FLOAT_VEC4:\n            checkVector(4, 'number')\n            break\n          case GL_BOOL:\n            checkType('boolean')\n            break\n          case GL_BOOL_VEC2:\n            checkVector(2, 'boolean')\n            break\n          case GL_BOOL_VEC3:\n            checkVector(3, 'boolean')\n            break\n          case GL_BOOL_VEC4:\n            checkVector(4, 'boolean')\n            break\n          case GL_FLOAT_MAT2:\n            checkVector(4, 'number')\n            break\n          case GL_FLOAT_MAT3:\n            checkVector(9, 'number')\n            break\n          case GL_FLOAT_MAT4:\n            checkVector(16, 'number')\n            break\n          case GL_SAMPLER_2D:\n            checkTexture(GL_TEXTURE_2D$3)\n            break\n          case GL_SAMPLER_CUBE:\n            checkTexture(GL_TEXTURE_CUBE_MAP$2)\n            break\n        }\n      })\n\n      var unroll = 1\n      switch (type) {\n        case GL_SAMPLER_2D:\n        case GL_SAMPLER_CUBE:\n          var TEX = scope.def(VALUE, '._texture')\n          scope(GL, '.uniform1i(', LOCATION, ',', TEX, '.bind());')\n          scope.exit(TEX, '.unbind();')\n          continue\n\n        case GL_INT$3:\n        case GL_BOOL:\n          infix = '1i'\n          break\n\n        case GL_INT_VEC2:\n        case GL_BOOL_VEC2:\n          infix = '2i'\n          unroll = 2\n          break\n\n        case GL_INT_VEC3:\n        case GL_BOOL_VEC3:\n          infix = '3i'\n          unroll = 3\n          break\n\n        case GL_INT_VEC4:\n        case GL_BOOL_VEC4:\n          infix = '4i'\n          unroll = 4\n          break\n\n        case GL_FLOAT$8:\n          infix = '1f'\n          break\n\n        case GL_FLOAT_VEC2:\n          infix = '2f'\n          unroll = 2\n          break\n\n        case GL_FLOAT_VEC3:\n          infix = '3f'\n          unroll = 3\n          break\n\n        case GL_FLOAT_VEC4:\n          infix = '4f'\n          unroll = 4\n          break\n\n        case GL_FLOAT_MAT2:\n          infix = 'Matrix2fv'\n          break\n\n        case GL_FLOAT_MAT3:\n          infix = 'Matrix3fv'\n          break\n\n        case GL_FLOAT_MAT4:\n          infix = 'Matrix4fv'\n          break\n      }\n\n      scope(GL, '.uniform', infix, '(', LOCATION, ',')\n      if (infix.charAt(0) === 'M') {\n        var matSize = Math.pow(type - GL_FLOAT_MAT2 + 2, 2)\n        var STORAGE = env.global.def('new Float32Array(', matSize, ')')\n        if (Array.isArray(VALUE)) {\n          scope(\n            'false,(',\n            loop(matSize, function (i) {\n              return STORAGE + '[' + i + ']=' + VALUE[i]\n            }), ',', STORAGE, ')')\n        } else {\n          scope(\n            'false,(Array.isArray(', VALUE, ')||', VALUE, ' instanceof Float32Array)?', VALUE, ':(',\n            loop(matSize, function (i) {\n              return STORAGE + '[' + i + ']=' + VALUE + '[' + i + ']'\n            }), ',', STORAGE, ')')\n        }\n      } else if (unroll > 1) {\n        scope(loop(unroll, function (i) {\n          return Array.isArray(VALUE) ? VALUE[i] : VALUE + '[' + i + ']'\n        }))\n      } else {\n        check$1(!Array.isArray(VALUE), 'uniform value must not be an array')\n        scope(VALUE)\n      }\n      scope(');')\n    }\n  }\n\n  function emitDraw (env, outer, inner, args) {\n    var shared = env.shared\n    var GL = shared.gl\n    var DRAW_STATE = shared.draw\n\n    var drawOptions = args.draw\n\n    function emitElements () {\n      var defn = drawOptions.elements\n      var ELEMENTS\n      var scope = outer\n      if (defn) {\n        if ((defn.contextDep && args.contextDynamic) || defn.propDep) {\n          scope = inner\n        }\n        ELEMENTS = defn.append(env, scope)\n      } else {\n        ELEMENTS = scope.def(DRAW_STATE, '.', S_ELEMENTS)\n      }\n      if (ELEMENTS) {\n        scope(\n          'if(' + ELEMENTS + ')' +\n          GL + '.bindBuffer(' + GL_ELEMENT_ARRAY_BUFFER$1 + ',' + ELEMENTS + '.buffer.buffer);')\n      }\n      return ELEMENTS\n    }\n\n    function emitCount () {\n      var defn = drawOptions.count\n      var COUNT\n      var scope = outer\n      if (defn) {\n        if ((defn.contextDep && args.contextDynamic) || defn.propDep) {\n          scope = inner\n        }\n        COUNT = defn.append(env, scope)\n        check$1.optional(function () {\n          if (defn.MISSING) {\n            env.assert(outer, 'false', 'missing vertex count')\n          }\n          if (defn.DYNAMIC) {\n            env.assert(scope, COUNT + '>=0', 'missing vertex count')\n          }\n        })\n      } else {\n        COUNT = scope.def(DRAW_STATE, '.', S_COUNT)\n        check$1.optional(function () {\n          env.assert(scope, COUNT + '>=0', 'missing vertex count')\n        })\n      }\n      return COUNT\n    }\n\n    var ELEMENTS = emitElements()\n    function emitValue (name) {\n      var defn = drawOptions[name]\n      if (defn) {\n        if ((defn.contextDep && args.contextDynamic) || defn.propDep) {\n          return defn.append(env, inner)\n        } else {\n          return defn.append(env, outer)\n        }\n      } else {\n        return outer.def(DRAW_STATE, '.', name)\n      }\n    }\n\n    var PRIMITIVE = emitValue(S_PRIMITIVE)\n    var OFFSET = emitValue(S_OFFSET)\n\n    var COUNT = emitCount()\n    if (typeof COUNT === 'number') {\n      if (COUNT === 0) {\n        return\n      }\n    } else {\n      inner('if(', COUNT, '){')\n      inner.exit('}')\n    }\n\n    var INSTANCES, EXT_INSTANCING\n    if (extInstancing) {\n      INSTANCES = emitValue(S_INSTANCES)\n      EXT_INSTANCING = env.instancing\n    }\n\n    var ELEMENT_TYPE = ELEMENTS + '.type'\n\n    var elementsStatic = drawOptions.elements && isStatic(drawOptions.elements)\n\n    function emitInstancing () {\n      function drawElements () {\n        inner(EXT_INSTANCING, '.drawElementsInstancedANGLE(', [\n          PRIMITIVE,\n          COUNT,\n          ELEMENT_TYPE,\n          OFFSET + '<<((' + ELEMENT_TYPE + '-' + GL_UNSIGNED_BYTE$8 + ')>>1)',\n          INSTANCES\n        ], ');')\n      }\n\n      function drawArrays () {\n        inner(EXT_INSTANCING, '.drawArraysInstancedANGLE(',\n          [PRIMITIVE, OFFSET, COUNT, INSTANCES], ');')\n      }\n\n      if (ELEMENTS) {\n        if (!elementsStatic) {\n          inner('if(', ELEMENTS, '){')\n          drawElements()\n          inner('}else{')\n          drawArrays()\n          inner('}')\n        } else {\n          drawElements()\n        }\n      } else {\n        drawArrays()\n      }\n    }\n\n    function emitRegular () {\n      function drawElements () {\n        inner(GL + '.drawElements(' + [\n          PRIMITIVE,\n          COUNT,\n          ELEMENT_TYPE,\n          OFFSET + '<<((' + ELEMENT_TYPE + '-' + GL_UNSIGNED_BYTE$8 + ')>>1)'\n        ] + ');')\n      }\n\n      function drawArrays () {\n        inner(GL + '.drawArrays(' + [PRIMITIVE, OFFSET, COUNT] + ');')\n      }\n\n      if (ELEMENTS) {\n        if (!elementsStatic) {\n          inner('if(', ELEMENTS, '){')\n          drawElements()\n          inner('}else{')\n          drawArrays()\n          inner('}')\n        } else {\n          drawElements()\n        }\n      } else {\n        drawArrays()\n      }\n    }\n\n    if (extInstancing && (typeof INSTANCES !== 'number' || INSTANCES >= 0)) {\n      if (typeof INSTANCES === 'string') {\n        inner('if(', INSTANCES, '>0){')\n        emitInstancing()\n        inner('}else if(', INSTANCES, '<0){')\n        emitRegular()\n        inner('}')\n      } else {\n        emitInstancing()\n      }\n    } else {\n      emitRegular()\n    }\n  }\n\n  function createBody (emitBody, parentEnv, args, program, count) {\n    var env = createREGLEnvironment()\n    var scope = env.proc('body', count)\n    check$1.optional(function () {\n      env.commandStr = parentEnv.commandStr\n      env.command = env.link(parentEnv.commandStr)\n    })\n    if (extInstancing) {\n      env.instancing = scope.def(\n        env.shared.extensions, '.angle_instanced_arrays')\n    }\n    emitBody(env, scope, args, program)\n    return env.compile().body\n  }\n\n  // ===================================================\n  // ===================================================\n  // DRAW PROC\n  // ===================================================\n  // ===================================================\n  function emitDrawBody (env, draw, args, program) {\n    injectExtensions(env, draw)\n    if (args.useVAO) {\n      if (args.drawVAO) {\n        draw(env.shared.vao, '.setVAO(', args.drawVAO.append(env, draw), ');')\n      } else {\n        draw(env.shared.vao, '.setVAO(', env.shared.vao, '.targetVAO);')\n      }\n    } else {\n      draw(env.shared.vao, '.setVAO(null);')\n      emitAttributes(env, draw, args, program.attributes, function () {\n        return true\n      })\n    }\n    emitUniforms(env, draw, args, program.uniforms, function () {\n      return true\n    })\n    emitDraw(env, draw, draw, args)\n  }\n\n  function emitDrawProc (env, args) {\n    var draw = env.proc('draw', 1)\n\n    injectExtensions(env, draw)\n\n    emitContext(env, draw, args.context)\n    emitPollFramebuffer(env, draw, args.framebuffer)\n\n    emitPollState(env, draw, args)\n    emitSetOptions(env, draw, args.state)\n\n    emitProfile(env, draw, args, false, true)\n\n    var program = args.shader.progVar.append(env, draw)\n    draw(env.shared.gl, '.useProgram(', program, '.program);')\n\n    if (args.shader.program) {\n      emitDrawBody(env, draw, args, args.shader.program)\n    } else {\n      draw(env.shared.vao, '.setVAO(null);')\n      var drawCache = env.global.def('{}')\n      var PROG_ID = draw.def(program, '.id')\n      var CACHED_PROC = draw.def(drawCache, '[', PROG_ID, ']')\n      draw(\n        env.cond(CACHED_PROC)\n          .then(CACHED_PROC, '.call(this,a0);')\n          .else(\n            CACHED_PROC, '=', drawCache, '[', PROG_ID, ']=',\n            env.link(function (program) {\n              return createBody(emitDrawBody, env, args, program, 1)\n            }), '(', program, ');',\n            CACHED_PROC, '.call(this,a0);'))\n    }\n\n    if (Object.keys(args.state).length > 0) {\n      draw(env.shared.current, '.dirty=true;')\n    }\n  }\n\n  // ===================================================\n  // ===================================================\n  // BATCH PROC\n  // ===================================================\n  // ===================================================\n\n  function emitBatchDynamicShaderBody (env, scope, args, program) {\n    env.batchId = 'a1'\n\n    injectExtensions(env, scope)\n\n    function all () {\n      return true\n    }\n\n    emitAttributes(env, scope, args, program.attributes, all)\n    emitUniforms(env, scope, args, program.uniforms, all)\n    emitDraw(env, scope, scope, args)\n  }\n\n  function emitBatchBody (env, scope, args, program) {\n    injectExtensions(env, scope)\n\n    var contextDynamic = args.contextDep\n\n    var BATCH_ID = scope.def()\n    var PROP_LIST = 'a0'\n    var NUM_PROPS = 'a1'\n    var PROPS = scope.def()\n    env.shared.props = PROPS\n    env.batchId = BATCH_ID\n\n    var outer = env.scope()\n    var inner = env.scope()\n\n    scope(\n      outer.entry,\n      'for(', BATCH_ID, '=0;', BATCH_ID, '<', NUM_PROPS, ';++', BATCH_ID, '){',\n      PROPS, '=', PROP_LIST, '[', BATCH_ID, '];',\n      inner,\n      '}',\n      outer.exit)\n\n    function isInnerDefn (defn) {\n      return ((defn.contextDep && contextDynamic) || defn.propDep)\n    }\n\n    function isOuterDefn (defn) {\n      return !isInnerDefn(defn)\n    }\n\n    if (args.needsContext) {\n      emitContext(env, inner, args.context)\n    }\n    if (args.needsFramebuffer) {\n      emitPollFramebuffer(env, inner, args.framebuffer)\n    }\n    emitSetOptions(env, inner, args.state, isInnerDefn)\n\n    if (args.profile && isInnerDefn(args.profile)) {\n      emitProfile(env, inner, args, false, true)\n    }\n\n    if (!program) {\n      var progCache = env.global.def('{}')\n      var PROGRAM = args.shader.progVar.append(env, inner)\n      var PROG_ID = inner.def(PROGRAM, '.id')\n      var CACHED_PROC = inner.def(progCache, '[', PROG_ID, ']')\n      inner(\n        env.shared.gl, '.useProgram(', PROGRAM, '.program);',\n        'if(!', CACHED_PROC, '){',\n        CACHED_PROC, '=', progCache, '[', PROG_ID, ']=',\n        env.link(function (program) {\n          return createBody(\n            emitBatchDynamicShaderBody, env, args, program, 2)\n        }), '(', PROGRAM, ');}',\n        CACHED_PROC, '.call(this,a0[', BATCH_ID, '],', BATCH_ID, ');')\n    } else {\n      if (args.useVAO) {\n        if (args.drawVAO) {\n          if (isInnerDefn(args.drawVAO)) {\n            // vao is a prop\n            inner(env.shared.vao, '.setVAO(', args.drawVAO.append(env, inner), ');')\n          } else {\n            // vao is invariant\n            outer(env.shared.vao, '.setVAO(', args.drawVAO.append(env, outer), ');')\n          }\n        } else {\n          // scoped vao binding\n          outer(env.shared.vao, '.setVAO(', env.shared.vao, '.targetVAO);')\n        }\n      } else {\n        outer(env.shared.vao, '.setVAO(null);')\n        emitAttributes(env, outer, args, program.attributes, isOuterDefn)\n        emitAttributes(env, inner, args, program.attributes, isInnerDefn)\n      }\n      emitUniforms(env, outer, args, program.uniforms, isOuterDefn)\n      emitUniforms(env, inner, args, program.uniforms, isInnerDefn)\n      emitDraw(env, outer, inner, args)\n    }\n  }\n\n  function emitBatchProc (env, args) {\n    var batch = env.proc('batch', 2)\n    env.batchId = '0'\n\n    injectExtensions(env, batch)\n\n    // Check if any context variables depend on props\n    var contextDynamic = false\n    var needsContext = true\n    Object.keys(args.context).forEach(function (name) {\n      contextDynamic = contextDynamic || args.context[name].propDep\n    })\n    if (!contextDynamic) {\n      emitContext(env, batch, args.context)\n      needsContext = false\n    }\n\n    // framebuffer state affects framebufferWidth/height context vars\n    var framebuffer = args.framebuffer\n    var needsFramebuffer = false\n    if (framebuffer) {\n      if (framebuffer.propDep) {\n        contextDynamic = needsFramebuffer = true\n      } else if (framebuffer.contextDep && contextDynamic) {\n        needsFramebuffer = true\n      }\n      if (!needsFramebuffer) {\n        emitPollFramebuffer(env, batch, framebuffer)\n      }\n    } else {\n      emitPollFramebuffer(env, batch, null)\n    }\n\n    // viewport is weird because it can affect context vars\n    if (args.state.viewport && args.state.viewport.propDep) {\n      contextDynamic = true\n    }\n\n    function isInnerDefn (defn) {\n      return (defn.contextDep && contextDynamic) || defn.propDep\n    }\n\n    // set webgl options\n    emitPollState(env, batch, args)\n    emitSetOptions(env, batch, args.state, function (defn) {\n      return !isInnerDefn(defn)\n    })\n\n    if (!args.profile || !isInnerDefn(args.profile)) {\n      emitProfile(env, batch, args, false, 'a1')\n    }\n\n    // Save these values to args so that the batch body routine can use them\n    args.contextDep = contextDynamic\n    args.needsContext = needsContext\n    args.needsFramebuffer = needsFramebuffer\n\n    // determine if shader is dynamic\n    var progDefn = args.shader.progVar\n    if ((progDefn.contextDep && contextDynamic) || progDefn.propDep) {\n      emitBatchBody(\n        env,\n        batch,\n        args,\n        null)\n    } else {\n      var PROGRAM = progDefn.append(env, batch)\n      batch(env.shared.gl, '.useProgram(', PROGRAM, '.program);')\n      if (args.shader.program) {\n        emitBatchBody(\n          env,\n          batch,\n          args,\n          args.shader.program)\n      } else {\n        batch(env.shared.vao, '.setVAO(null);')\n        var batchCache = env.global.def('{}')\n        var PROG_ID = batch.def(PROGRAM, '.id')\n        var CACHED_PROC = batch.def(batchCache, '[', PROG_ID, ']')\n        batch(\n          env.cond(CACHED_PROC)\n            .then(CACHED_PROC, '.call(this,a0,a1);')\n            .else(\n              CACHED_PROC, '=', batchCache, '[', PROG_ID, ']=',\n              env.link(function (program) {\n                return createBody(emitBatchBody, env, args, program, 2)\n              }), '(', PROGRAM, ');',\n              CACHED_PROC, '.call(this,a0,a1);'))\n      }\n    }\n\n    if (Object.keys(args.state).length > 0) {\n      batch(env.shared.current, '.dirty=true;')\n    }\n  }\n\n  // ===================================================\n  // ===================================================\n  // SCOPE COMMAND\n  // ===================================================\n  // ===================================================\n  function emitScopeProc (env, args) {\n    var scope = env.proc('scope', 3)\n    env.batchId = 'a2'\n\n    var shared = env.shared\n    var CURRENT_STATE = shared.current\n\n    emitContext(env, scope, args.context)\n\n    if (args.framebuffer) {\n      args.framebuffer.append(env, scope)\n    }\n\n    sortState(Object.keys(args.state)).forEach(function (name) {\n      var defn = args.state[name]\n      var value = defn.append(env, scope)\n      if (isArrayLike(value)) {\n        value.forEach(function (v, i) {\n          scope.set(env.next[name], '[' + i + ']', v)\n        })\n      } else {\n        scope.set(shared.next, '.' + name, value)\n      }\n    })\n\n    emitProfile(env, scope, args, true, true)\n\n    ;[S_ELEMENTS, S_OFFSET, S_COUNT, S_INSTANCES, S_PRIMITIVE].forEach(\n      function (opt) {\n        var variable = args.draw[opt]\n        if (!variable) {\n          return\n        }\n        scope.set(shared.draw, '.' + opt, '' + variable.append(env, scope))\n      })\n\n    Object.keys(args.uniforms).forEach(function (opt) {\n      var value = args.uniforms[opt].append(env, scope)\n      if (Array.isArray(value)) {\n        value = '[' + value.join() + ']'\n      }\n      scope.set(\n        shared.uniforms,\n        '[' + stringStore.id(opt) + ']',\n        value)\n    })\n\n    Object.keys(args.attributes).forEach(function (name) {\n      var record = args.attributes[name].append(env, scope)\n      var scopeAttrib = env.scopeAttrib(name)\n      Object.keys(new AttributeRecord()).forEach(function (prop) {\n        scope.set(scopeAttrib, '.' + prop, record[prop])\n      })\n    })\n\n    if (args.scopeVAO) {\n      scope.set(shared.vao, '.targetVAO', args.scopeVAO.append(env, scope))\n    }\n\n    function saveShader (name) {\n      var shader = args.shader[name]\n      if (shader) {\n        scope.set(shared.shader, '.' + name, shader.append(env, scope))\n      }\n    }\n    saveShader(S_VERT)\n    saveShader(S_FRAG)\n\n    if (Object.keys(args.state).length > 0) {\n      scope(CURRENT_STATE, '.dirty=true;')\n      scope.exit(CURRENT_STATE, '.dirty=true;')\n    }\n\n    scope('a1(', env.shared.context, ',a0,', env.batchId, ');')\n  }\n\n  function isDynamicObject (object) {\n    if (typeof object !== 'object' || isArrayLike(object)) {\n      return\n    }\n    var props = Object.keys(object)\n    for (var i = 0; i < props.length; ++i) {\n      if (dynamic.isDynamic(object[props[i]])) {\n        return true\n      }\n    }\n    return false\n  }\n\n  function splatObject (env, options, name) {\n    var object = options.static[name]\n    if (!object || !isDynamicObject(object)) {\n      return\n    }\n\n    var globals = env.global\n    var keys = Object.keys(object)\n    var thisDep = false\n    var contextDep = false\n    var propDep = false\n    var objectRef = env.global.def('{}')\n    keys.forEach(function (key) {\n      var value = object[key]\n      if (dynamic.isDynamic(value)) {\n        if (typeof value === 'function') {\n          value = object[key] = dynamic.unbox(value)\n        }\n        var deps = createDynamicDecl(value, null)\n        thisDep = thisDep || deps.thisDep\n        propDep = propDep || deps.propDep\n        contextDep = contextDep || deps.contextDep\n      } else {\n        globals(objectRef, '.', key, '=')\n        switch (typeof value) {\n          case 'number':\n            globals(value)\n            break\n          case 'string':\n            globals('\"', value, '\"')\n            break\n          case 'object':\n            if (Array.isArray(value)) {\n              globals('[', value.join(), ']')\n            }\n            break\n          default:\n            globals(env.link(value))\n            break\n        }\n        globals(';')\n      }\n    })\n\n    function appendBlock (env, block) {\n      keys.forEach(function (key) {\n        var value = object[key]\n        if (!dynamic.isDynamic(value)) {\n          return\n        }\n        var ref = env.invoke(block, value)\n        block(objectRef, '.', key, '=', ref, ';')\n      })\n    }\n\n    options.dynamic[name] = new dynamic.DynamicVariable(DYN_THUNK, {\n      thisDep: thisDep,\n      contextDep: contextDep,\n      propDep: propDep,\n      ref: objectRef,\n      append: appendBlock\n    })\n    delete options.static[name]\n  }\n\n  // ===========================================================================\n  // ===========================================================================\n  // MAIN DRAW COMMAND\n  // ===========================================================================\n  // ===========================================================================\n  function compileCommand (options, attributes, uniforms, context, stats) {\n    var env = createREGLEnvironment()\n\n    // link stats, so that we can easily access it in the program.\n    env.stats = env.link(stats)\n\n    // splat options and attributes to allow for dynamic nested properties\n    Object.keys(attributes.static).forEach(function (key) {\n      splatObject(env, attributes, key)\n    })\n    NESTED_OPTIONS.forEach(function (name) {\n      splatObject(env, options, name)\n    })\n\n    var args = parseArguments(options, attributes, uniforms, context, env)\n\n    emitDrawProc(env, args)\n    emitScopeProc(env, args)\n    emitBatchProc(env, args)\n\n    return extend(env.compile(), {\n      destroy: function () {\n        args.shader.program.destroy()\n      }\n    })\n  }\n\n  // ===========================================================================\n  // ===========================================================================\n  // POLL / REFRESH\n  // ===========================================================================\n  // ===========================================================================\n  return {\n    next: nextState,\n    current: currentState,\n    procs: (function () {\n      var env = createREGLEnvironment()\n      var poll = env.proc('poll')\n      var refresh = env.proc('refresh')\n      var common = env.block()\n      poll(common)\n      refresh(common)\n\n      var shared = env.shared\n      var GL = shared.gl\n      var NEXT_STATE = shared.next\n      var CURRENT_STATE = shared.current\n\n      common(CURRENT_STATE, '.dirty=false;')\n\n      emitPollFramebuffer(env, poll)\n      emitPollFramebuffer(env, refresh, null, true)\n\n      // Refresh updates all attribute state changes\n      var INSTANCING\n      if (extInstancing) {\n        INSTANCING = env.link(extInstancing)\n      }\n\n      // update vertex array bindings\n      if (extensions.oes_vertex_array_object) {\n        refresh(env.link(extensions.oes_vertex_array_object), '.bindVertexArrayOES(null);')\n      }\n      for (var i = 0; i < limits.maxAttributes; ++i) {\n        var BINDING = refresh.def(shared.attributes, '[', i, ']')\n        var ifte = env.cond(BINDING, '.buffer')\n        ifte.then(\n          GL, '.enableVertexAttribArray(', i, ');',\n          GL, '.bindBuffer(',\n          GL_ARRAY_BUFFER$2, ',',\n          BINDING, '.buffer.buffer);',\n          GL, '.vertexAttribPointer(',\n          i, ',',\n          BINDING, '.size,',\n          BINDING, '.type,',\n          BINDING, '.normalized,',\n          BINDING, '.stride,',\n          BINDING, '.offset);'\n        ).else(\n          GL, '.disableVertexAttribArray(', i, ');',\n          GL, '.vertexAttrib4f(',\n          i, ',',\n          BINDING, '.x,',\n          BINDING, '.y,',\n          BINDING, '.z,',\n          BINDING, '.w);',\n          BINDING, '.buffer=null;')\n        refresh(ifte)\n        if (extInstancing) {\n          refresh(\n            INSTANCING, '.vertexAttribDivisorANGLE(',\n            i, ',',\n            BINDING, '.divisor);')\n        }\n      }\n      refresh(\n        env.shared.vao, '.currentVAO=null;',\n        env.shared.vao, '.setVAO(', env.shared.vao, '.targetVAO);')\n\n      Object.keys(GL_FLAGS).forEach(function (flag) {\n        var cap = GL_FLAGS[flag]\n        var NEXT = common.def(NEXT_STATE, '.', flag)\n        var block = env.block()\n        block('if(', NEXT, '){',\n          GL, '.enable(', cap, ')}else{',\n          GL, '.disable(', cap, ')}',\n          CURRENT_STATE, '.', flag, '=', NEXT, ';')\n        refresh(block)\n        poll(\n          'if(', NEXT, '!==', CURRENT_STATE, '.', flag, '){',\n          block,\n          '}')\n      })\n\n      Object.keys(GL_VARIABLES).forEach(function (name) {\n        var func = GL_VARIABLES[name]\n        var init = currentState[name]\n        var NEXT, CURRENT\n        var block = env.block()\n        block(GL, '.', func, '(')\n        if (isArrayLike(init)) {\n          var n = init.length\n          NEXT = env.global.def(NEXT_STATE, '.', name)\n          CURRENT = env.global.def(CURRENT_STATE, '.', name)\n          block(\n            loop(n, function (i) {\n              return NEXT + '[' + i + ']'\n            }), ');',\n            loop(n, function (i) {\n              return CURRENT + '[' + i + ']=' + NEXT + '[' + i + '];'\n            }).join(''))\n          poll(\n            'if(', loop(n, function (i) {\n              return NEXT + '[' + i + ']!==' + CURRENT + '[' + i + ']'\n            }).join('||'), '){',\n            block,\n            '}')\n        } else {\n          NEXT = common.def(NEXT_STATE, '.', name)\n          CURRENT = common.def(CURRENT_STATE, '.', name)\n          block(\n            NEXT, ');',\n            CURRENT_STATE, '.', name, '=', NEXT, ';')\n          poll(\n            'if(', NEXT, '!==', CURRENT, '){',\n            block,\n            '}')\n        }\n        refresh(block)\n      })\n\n      return env.compile()\n    })(),\n    compile: compileCommand\n  }\n}\n\nfunction stats () {\n  return {\n    vaoCount: 0,\n    bufferCount: 0,\n    elementsCount: 0,\n    framebufferCount: 0,\n    shaderCount: 0,\n    textureCount: 0,\n    cubeCount: 0,\n    renderbufferCount: 0,\n    maxTextureUnits: 0\n  }\n}\n\nvar GL_QUERY_RESULT_EXT = 0x8866\nvar GL_QUERY_RESULT_AVAILABLE_EXT = 0x8867\nvar GL_TIME_ELAPSED_EXT = 0x88BF\n\nvar createTimer = function (gl, extensions) {\n  if (!extensions.ext_disjoint_timer_query) {\n    return null\n  }\n\n  // QUERY POOL BEGIN\n  var queryPool = []\n  function allocQuery () {\n    return queryPool.pop() || extensions.ext_disjoint_timer_query.createQueryEXT()\n  }\n  function freeQuery (query) {\n    queryPool.push(query)\n  }\n  // QUERY POOL END\n\n  var pendingQueries = []\n  function beginQuery (stats) {\n    var query = allocQuery()\n    extensions.ext_disjoint_timer_query.beginQueryEXT(GL_TIME_ELAPSED_EXT, query)\n    pendingQueries.push(query)\n    pushScopeStats(pendingQueries.length - 1, pendingQueries.length, stats)\n  }\n\n  function endQuery () {\n    extensions.ext_disjoint_timer_query.endQueryEXT(GL_TIME_ELAPSED_EXT)\n  }\n\n  //\n  // Pending stats pool.\n  //\n  function PendingStats () {\n    this.startQueryIndex = -1\n    this.endQueryIndex = -1\n    this.sum = 0\n    this.stats = null\n  }\n  var pendingStatsPool = []\n  function allocPendingStats () {\n    return pendingStatsPool.pop() || new PendingStats()\n  }\n  function freePendingStats (pendingStats) {\n    pendingStatsPool.push(pendingStats)\n  }\n  // Pending stats pool end\n\n  var pendingStats = []\n  function pushScopeStats (start, end, stats) {\n    var ps = allocPendingStats()\n    ps.startQueryIndex = start\n    ps.endQueryIndex = end\n    ps.sum = 0\n    ps.stats = stats\n    pendingStats.push(ps)\n  }\n\n  // we should call this at the beginning of the frame,\n  // in order to update gpuTime\n  var timeSum = []\n  var queryPtr = []\n  function update () {\n    var ptr, i\n\n    var n = pendingQueries.length\n    if (n === 0) {\n      return\n    }\n\n    // Reserve space\n    queryPtr.length = Math.max(queryPtr.length, n + 1)\n    timeSum.length = Math.max(timeSum.length, n + 1)\n    timeSum[0] = 0\n    queryPtr[0] = 0\n\n    // Update all pending timer queries\n    var queryTime = 0\n    ptr = 0\n    for (i = 0; i < pendingQueries.length; ++i) {\n      var query = pendingQueries[i]\n      if (extensions.ext_disjoint_timer_query.getQueryObjectEXT(query, GL_QUERY_RESULT_AVAILABLE_EXT)) {\n        queryTime += extensions.ext_disjoint_timer_query.getQueryObjectEXT(query, GL_QUERY_RESULT_EXT)\n        freeQuery(query)\n      } else {\n        pendingQueries[ptr++] = query\n      }\n      timeSum[i + 1] = queryTime\n      queryPtr[i + 1] = ptr\n    }\n    pendingQueries.length = ptr\n\n    // Update all pending stat queries\n    ptr = 0\n    for (i = 0; i < pendingStats.length; ++i) {\n      var stats = pendingStats[i]\n      var start = stats.startQueryIndex\n      var end = stats.endQueryIndex\n      stats.sum += timeSum[end] - timeSum[start]\n      var startPtr = queryPtr[start]\n      var endPtr = queryPtr[end]\n      if (endPtr === startPtr) {\n        stats.stats.gpuTime += stats.sum / 1e6\n        freePendingStats(stats)\n      } else {\n        stats.startQueryIndex = startPtr\n        stats.endQueryIndex = endPtr\n        pendingStats[ptr++] = stats\n      }\n    }\n    pendingStats.length = ptr\n  }\n\n  return {\n    beginQuery: beginQuery,\n    endQuery: endQuery,\n    pushScopeStats: pushScopeStats,\n    update: update,\n    getNumPendingQueries: function () {\n      return pendingQueries.length\n    },\n    clear: function () {\n      queryPool.push.apply(queryPool, pendingQueries)\n      for (var i = 0; i < queryPool.length; i++) {\n        extensions.ext_disjoint_timer_query.deleteQueryEXT(queryPool[i])\n      }\n      pendingQueries.length = 0\n      queryPool.length = 0\n    },\n    restore: function () {\n      pendingQueries.length = 0\n      queryPool.length = 0\n    }\n  }\n}\n\nvar GL_COLOR_BUFFER_BIT = 16384\nvar GL_DEPTH_BUFFER_BIT = 256\nvar GL_STENCIL_BUFFER_BIT = 1024\n\nvar GL_ARRAY_BUFFER = 34962\n\nvar CONTEXT_LOST_EVENT = 'webglcontextlost'\nvar CONTEXT_RESTORED_EVENT = 'webglcontextrestored'\n\nvar DYN_PROP = 1\nvar DYN_CONTEXT = 2\nvar DYN_STATE = 3\n\nfunction find (haystack, needle) {\n  for (var i = 0; i < haystack.length; ++i) {\n    if (haystack[i] === needle) {\n      return i\n    }\n  }\n  return -1\n}\n\nfunction wrapREGL (args) {\n  var config = parseArgs(args)\n  if (!config) {\n    return null\n  }\n\n  var gl = config.gl\n  var glAttributes = gl.getContextAttributes()\n  var contextLost = gl.isContextLost()\n\n  var extensionState = createExtensionCache(gl, config)\n  if (!extensionState) {\n    return null\n  }\n\n  var stringStore = createStringStore()\n  var stats$$1 = stats()\n  var extensions = extensionState.extensions\n  var timer = createTimer(gl, extensions)\n\n  var START_TIME = clock()\n  var WIDTH = gl.drawingBufferWidth\n  var HEIGHT = gl.drawingBufferHeight\n\n  var contextState = {\n    tick: 0,\n    time: 0,\n    viewportWidth: WIDTH,\n    viewportHeight: HEIGHT,\n    framebufferWidth: WIDTH,\n    framebufferHeight: HEIGHT,\n    drawingBufferWidth: WIDTH,\n    drawingBufferHeight: HEIGHT,\n    pixelRatio: config.pixelRatio\n  }\n  var uniformState = {}\n  var drawState = {\n    elements: null,\n    primitive: 4, // GL_TRIANGLES\n    count: -1,\n    offset: 0,\n    instances: -1\n  }\n\n  var limits = wrapLimits(gl, extensions)\n  var bufferState = wrapBufferState(\n    gl,\n    stats$$1,\n    config,\n    destroyBuffer)\n  var attributeState = wrapAttributeState(\n    gl,\n    extensions,\n    limits,\n    stats$$1,\n    bufferState)\n  function destroyBuffer (buffer) {\n    return attributeState.destroyBuffer(buffer)\n  }\n  var elementState = wrapElementsState(gl, extensions, bufferState, stats$$1)\n  var shaderState = wrapShaderState(gl, stringStore, stats$$1, config)\n  var textureState = createTextureSet(\n    gl,\n    extensions,\n    limits,\n    function () { core.procs.poll() },\n    contextState,\n    stats$$1,\n    config)\n  var renderbufferState = wrapRenderbuffers(gl, extensions, limits, stats$$1, config)\n  var framebufferState = wrapFBOState(\n    gl,\n    extensions,\n    limits,\n    textureState,\n    renderbufferState,\n    stats$$1)\n  var core = reglCore(\n    gl,\n    stringStore,\n    extensions,\n    limits,\n    bufferState,\n    elementState,\n    textureState,\n    framebufferState,\n    uniformState,\n    attributeState,\n    shaderState,\n    drawState,\n    contextState,\n    timer,\n    config)\n  var readPixels = wrapReadPixels(\n    gl,\n    framebufferState,\n    core.procs.poll,\n    contextState,\n    glAttributes, extensions, limits)\n\n  var nextState = core.next\n  var canvas = gl.canvas\n\n  var rafCallbacks = []\n  var lossCallbacks = []\n  var restoreCallbacks = []\n  var destroyCallbacks = [config.onDestroy]\n\n  var activeRAF = null\n  function handleRAF () {\n    if (rafCallbacks.length === 0) {\n      if (timer) {\n        timer.update()\n      }\n      activeRAF = null\n      return\n    }\n\n    // schedule next animation frame\n    activeRAF = raf.next(handleRAF)\n\n    // poll for changes\n    poll()\n\n    // fire a callback for all pending rafs\n    for (var i = rafCallbacks.length - 1; i >= 0; --i) {\n      var cb = rafCallbacks[i]\n      if (cb) {\n        cb(contextState, null, 0)\n      }\n    }\n\n    // flush all pending webgl calls\n    gl.flush()\n\n    // poll GPU timers *after* gl.flush so we don't delay command dispatch\n    if (timer) {\n      timer.update()\n    }\n  }\n\n  function startRAF () {\n    if (!activeRAF && rafCallbacks.length > 0) {\n      activeRAF = raf.next(handleRAF)\n    }\n  }\n\n  function stopRAF () {\n    if (activeRAF) {\n      raf.cancel(handleRAF)\n      activeRAF = null\n    }\n  }\n\n  function handleContextLoss (event) {\n    event.preventDefault()\n\n    // set context lost flag\n    contextLost = true\n\n    // pause request animation frame\n    stopRAF()\n\n    // lose context\n    lossCallbacks.forEach(function (cb) {\n      cb()\n    })\n  }\n\n  function handleContextRestored (event) {\n    // clear error code\n    gl.getError()\n\n    // clear context lost flag\n    contextLost = false\n\n    // refresh state\n    extensionState.restore()\n    shaderState.restore()\n    bufferState.restore()\n    textureState.restore()\n    renderbufferState.restore()\n    framebufferState.restore()\n    attributeState.restore()\n    if (timer) {\n      timer.restore()\n    }\n\n    // refresh state\n    core.procs.refresh()\n\n    // restart RAF\n    startRAF()\n\n    // restore context\n    restoreCallbacks.forEach(function (cb) {\n      cb()\n    })\n  }\n\n  if (canvas) {\n    canvas.addEventListener(CONTEXT_LOST_EVENT, handleContextLoss, false)\n    canvas.addEventListener(CONTEXT_RESTORED_EVENT, handleContextRestored, false)\n  }\n\n  function destroy () {\n    rafCallbacks.length = 0\n    stopRAF()\n\n    if (canvas) {\n      canvas.removeEventListener(CONTEXT_LOST_EVENT, handleContextLoss)\n      canvas.removeEventListener(CONTEXT_RESTORED_EVENT, handleContextRestored)\n    }\n\n    shaderState.clear()\n    framebufferState.clear()\n    renderbufferState.clear()\n    textureState.clear()\n    elementState.clear()\n    bufferState.clear()\n    attributeState.clear()\n\n    if (timer) {\n      timer.clear()\n    }\n\n    destroyCallbacks.forEach(function (cb) {\n      cb()\n    })\n  }\n\n  function compileProcedure (options) {\n    check$1(!!options, 'invalid args to regl({...})')\n    check$1.type(options, 'object', 'invalid args to regl({...})')\n\n    function flattenNestedOptions (options) {\n      var result = extend({}, options)\n      delete result.uniforms\n      delete result.attributes\n      delete result.context\n      delete result.vao\n\n      if ('stencil' in result && result.stencil.op) {\n        result.stencil.opBack = result.stencil.opFront = result.stencil.op\n        delete result.stencil.op\n      }\n\n      function merge (name) {\n        if (name in result) {\n          var child = result[name]\n          delete result[name]\n          Object.keys(child).forEach(function (prop) {\n            result[name + '.' + prop] = child[prop]\n          })\n        }\n      }\n      merge('blend')\n      merge('depth')\n      merge('cull')\n      merge('stencil')\n      merge('polygonOffset')\n      merge('scissor')\n      merge('sample')\n\n      if ('vao' in options) {\n        result.vao = options.vao\n      }\n\n      return result\n    }\n\n    function separateDynamic (object, useArrays) {\n      var staticItems = {}\n      var dynamicItems = {}\n      Object.keys(object).forEach(function (option) {\n        var value = object[option]\n        if (dynamic.isDynamic(value)) {\n          dynamicItems[option] = dynamic.unbox(value, option)\n          return\n        } else if (useArrays && Array.isArray(value)) {\n          for (var i = 0; i < value.length; ++i) {\n            if (dynamic.isDynamic(value[i])) {\n              dynamicItems[option] = dynamic.unbox(value, option)\n              return\n            }\n          }\n        }\n        staticItems[option] = value\n      })\n      return {\n        dynamic: dynamicItems,\n        static: staticItems\n      }\n    }\n\n    // Treat context variables separate from other dynamic variables\n    var context = separateDynamic(options.context || {}, true)\n    var uniforms = separateDynamic(options.uniforms || {}, true)\n    var attributes = separateDynamic(options.attributes || {}, false)\n    var opts = separateDynamic(flattenNestedOptions(options), false)\n\n    var stats$$1 = {\n      gpuTime: 0.0,\n      cpuTime: 0.0,\n      count: 0\n    }\n\n    var compiled = core.compile(opts, attributes, uniforms, context, stats$$1)\n\n    var draw = compiled.draw\n    var batch = compiled.batch\n    var scope = compiled.scope\n\n    // FIXME: we should modify code generation for batch commands so this\n    // isn't necessary\n    var EMPTY_ARRAY = []\n    function reserve (count) {\n      while (EMPTY_ARRAY.length < count) {\n        EMPTY_ARRAY.push(null)\n      }\n      return EMPTY_ARRAY\n    }\n\n    function REGLCommand (args, body) {\n      var i\n      if (contextLost) {\n        check$1.raise('context lost')\n      }\n      if (typeof args === 'function') {\n        return scope.call(this, null, args, 0)\n      } else if (typeof body === 'function') {\n        if (typeof args === 'number') {\n          for (i = 0; i < args; ++i) {\n            scope.call(this, null, body, i)\n          }\n        } else if (Array.isArray(args)) {\n          for (i = 0; i < args.length; ++i) {\n            scope.call(this, args[i], body, i)\n          }\n        } else {\n          return scope.call(this, args, body, 0)\n        }\n      } else if (typeof args === 'number') {\n        if (args > 0) {\n          return batch.call(this, reserve(args | 0), args | 0)\n        }\n      } else if (Array.isArray(args)) {\n        if (args.length) {\n          return batch.call(this, args, args.length)\n        }\n      } else {\n        return draw.call(this, args)\n      }\n    }\n\n    return extend(REGLCommand, {\n      stats: stats$$1,\n      destroy: function () {\n        compiled.destroy()\n      }\n    })\n  }\n\n  var setFBO = framebufferState.setFBO = compileProcedure({\n    framebuffer: dynamic.define.call(null, DYN_PROP, 'framebuffer')\n  })\n\n  function clearImpl (_, options) {\n    var clearFlags = 0\n    core.procs.poll()\n\n    var c = options.color\n    if (c) {\n      gl.clearColor(+c[0] || 0, +c[1] || 0, +c[2] || 0, +c[3] || 0)\n      clearFlags |= GL_COLOR_BUFFER_BIT\n    }\n    if ('depth' in options) {\n      gl.clearDepth(+options.depth)\n      clearFlags |= GL_DEPTH_BUFFER_BIT\n    }\n    if ('stencil' in options) {\n      gl.clearStencil(options.stencil | 0)\n      clearFlags |= GL_STENCIL_BUFFER_BIT\n    }\n\n    check$1(!!clearFlags, 'called regl.clear with no buffer specified')\n    gl.clear(clearFlags)\n  }\n\n  function clear (options) {\n    check$1(\n      typeof options === 'object' && options,\n      'regl.clear() takes an object as input')\n    if ('framebuffer' in options) {\n      if (options.framebuffer &&\n          options.framebuffer_reglType === 'framebufferCube') {\n        for (var i = 0; i < 6; ++i) {\n          setFBO(extend({\n            framebuffer: options.framebuffer.faces[i]\n          }, options), clearImpl)\n        }\n      } else {\n        setFBO(options, clearImpl)\n      }\n    } else {\n      clearImpl(null, options)\n    }\n  }\n\n  function frame (cb) {\n    check$1.type(cb, 'function', 'regl.frame() callback must be a function')\n    rafCallbacks.push(cb)\n\n    function cancel () {\n      // FIXME:  should we check something other than equals cb here?\n      // what if a user calls frame twice with the same callback...\n      //\n      var i = find(rafCallbacks, cb)\n      check$1(i >= 0, 'cannot cancel a frame twice')\n      function pendingCancel () {\n        var index = find(rafCallbacks, pendingCancel)\n        rafCallbacks[index] = rafCallbacks[rafCallbacks.length - 1]\n        rafCallbacks.length -= 1\n        if (rafCallbacks.length <= 0) {\n          stopRAF()\n        }\n      }\n      rafCallbacks[i] = pendingCancel\n    }\n\n    startRAF()\n\n    return {\n      cancel: cancel\n    }\n  }\n\n  // poll viewport\n  function pollViewport () {\n    var viewport = nextState.viewport\n    var scissorBox = nextState.scissor_box\n    viewport[0] = viewport[1] = scissorBox[0] = scissorBox[1] = 0\n    contextState.viewportWidth =\n      contextState.framebufferWidth =\n      contextState.drawingBufferWidth =\n      viewport[2] =\n      scissorBox[2] = gl.drawingBufferWidth\n    contextState.viewportHeight =\n      contextState.framebufferHeight =\n      contextState.drawingBufferHeight =\n      viewport[3] =\n      scissorBox[3] = gl.drawingBufferHeight\n  }\n\n  function poll () {\n    contextState.tick += 1\n    contextState.time = now()\n    pollViewport()\n    core.procs.poll()\n  }\n\n  function refresh () {\n    textureState.refresh()\n    pollViewport()\n    core.procs.refresh()\n    if (timer) {\n      timer.update()\n    }\n  }\n\n  function now () {\n    return (clock() - START_TIME) / 1000.0\n  }\n\n  refresh()\n\n  function addListener (event, callback) {\n    check$1.type(callback, 'function', 'listener callback must be a function')\n\n    var callbacks\n    switch (event) {\n      case 'frame':\n        return frame(callback)\n      case 'lost':\n        callbacks = lossCallbacks\n        break\n      case 'restore':\n        callbacks = restoreCallbacks\n        break\n      case 'destroy':\n        callbacks = destroyCallbacks\n        break\n      default:\n        check$1.raise('invalid event, must be one of frame,lost,restore,destroy')\n    }\n\n    callbacks.push(callback)\n    return {\n      cancel: function () {\n        for (var i = 0; i < callbacks.length; ++i) {\n          if (callbacks[i] === callback) {\n            callbacks[i] = callbacks[callbacks.length - 1]\n            callbacks.pop()\n            return\n          }\n        }\n      }\n    }\n  }\n\n  var regl = extend(compileProcedure, {\n    // Clear current FBO\n    clear: clear,\n\n    // Short cuts for dynamic variables\n    prop: dynamic.define.bind(null, DYN_PROP),\n    context: dynamic.define.bind(null, DYN_CONTEXT),\n    this: dynamic.define.bind(null, DYN_STATE),\n\n    // executes an empty draw command\n    draw: compileProcedure({}),\n\n    // Resources\n    buffer: function (options) {\n      return bufferState.create(options, GL_ARRAY_BUFFER, false, false)\n    },\n    elements: function (options) {\n      return elementState.create(options, false)\n    },\n    texture: textureState.create2D,\n    cube: textureState.createCube,\n    renderbuffer: renderbufferState.create,\n    framebuffer: framebufferState.create,\n    framebufferCube: framebufferState.createCube,\n    vao: attributeState.createVAO,\n\n    // Expose context attributes\n    attributes: glAttributes,\n\n    // Frame rendering\n    frame: frame,\n    on: addListener,\n\n    // System limits\n    limits: limits,\n    hasExtension: function (name) {\n      return limits.extensions.indexOf(name.toLowerCase()) >= 0\n    },\n\n    // Read pixels\n    read: readPixels,\n\n    // Destroy regl and all associated resources\n    destroy: destroy,\n\n    // Direct GL state manipulation\n    _gl: gl,\n    _refresh: refresh,\n\n    poll: function () {\n      poll()\n      if (timer) {\n        timer.update()\n      }\n    },\n\n    // Current time\n    now: now,\n\n    // regl Statistics Information\n    stats: stats$$1\n  })\n\n  config.onDone(null, regl)\n\n  return regl\n}\n\nreturn wrapREGL;\n\n})));\n//# sourceMappingURL=regl.js.map\n"], "names": ["global", "factory", "module", "isTypedArray", "x", "extend", "base", "opts", "keys", "i", "endl", "decodeB64", "str", "raise", "message", "error", "check", "pred", "encolon", "checkParameter", "param", "possibilities", "checkIsTypedArray", "data", "standardTypeEh", "value", "type", "checkTypeOf", "checkNonNegativeInt", "checkOneOf", "list", "constructorKeys", "checkConstructor", "obj", "key", "leftPad", "n", "ShaderFile", "ShaderLine", "number", "line", "ShaderError", "fileNumber", "lineNumber", "guessCommand", "stack", "pat", "pat2", "guessCallSite", "parseSource", "source", "command", "lines", "files", "parts", "lineNumberInfo", "nameInfo", "file", "parseErrorLog", "err<PERSON><PERSON>", "result", "errMsg", "annotateFiles", "errors", "checkShaderError", "gl", "shader", "typeName", "checkCommandType", "strings", "styles", "push", "style", "offset", "token", "tokenPat", "checkLinkError", "program", "fragS<PERSON>er", "vert<PERSON><PERSON><PERSON>", "fragParse", "vertParse", "header", "saveCommandRef", "object", "saveDrawCommandInfo", "uniforms", "attributes", "stringStore", "id", "addProps", "dict", "set", "u", "uniformSet", "attributeSet", "commandRaise", "callSite", "checkCommand", "checkParameterCommand", "checkOptional", "block", "checkFramebufferFormat", "attachment", "texFormats", "rbFormats", "GL_CLAMP_TO_EDGE", "GL_NEAREST", "GL_NEAREST_MIPMAP_NEAREST", "GL_LINEAR_MIPMAP_NEAREST", "GL_NEAREST_MIPMAP_LINEAR", "GL_LINEAR_MIPMAP_LINEAR", "GL_BYTE", "GL_UNSIGNED_BYTE", "GL_SHORT", "GL_UNSIGNED_SHORT", "GL_INT", "GL_UNSIGNED_INT", "GL_FLOAT", "GL_UNSIGNED_SHORT_4_4_4_4", "GL_UNSIGNED_SHORT_5_5_5_1", "GL_UNSIGNED_SHORT_5_6_5", "GL_UNSIGNED_INT_24_8_WEBGL", "GL_HALF_FLOAT_OES", "TYPE_SIZE", "pixelSize", "channels", "isPow2", "v", "checkTexture2D", "info", "mipData", "limits", "h", "c", "mipimages", "mw", "mh", "img", "rowSize", "checkTextureCube", "texture", "faces", "face", "mipmaps", "j", "check$1", "VARIABLE_COUNTER", "DYN_FUNC", "DYN_CONSTANT", "DYN_ARRAY", "DynamicVariable", "escapeStr", "splitParts", "firstChar", "lastChar", "subparts", "toAccessorString", "defineDynamic", "isDynamic", "unbox", "path", "y", "dynamic", "raf", "cb", "clock", "createStringStore", "stringIds", "stringValues", "createCanvas", "element", "onDone", "pixelRatio", "canvas", "resize", "w", "bounds", "resizeObserver", "onDestroy", "createContext", "contextAttributes", "get", "name", "e", "isHTMLElement", "isWebGLContext", "parseExtensions", "input", "getElement", "desc", "parseArgs", "args_", "args", "container", "extensions", "optionalExtensions", "profile", "err", "createExtensionCache", "config", "tryLoadExtension", "name_", "ext", "loop", "f", "GL_BYTE$1", "GL_UNSIGNED_BYTE$2", "GL_SHORT$1", "GL_UNSIGNED_SHORT$1", "GL_INT$1", "GL_UNSIGNED_INT$1", "GL_FLOAT$2", "nextPow16", "log2", "r", "shift", "createPool", "bufferPool", "alloc", "sz", "bin", "free", "buf", "allocType", "freeType", "array", "pool", "GL_SUBPIXEL_BITS", "GL_RED_BITS", "GL_GREEN_BITS", "GL_BLUE_BITS", "GL_ALPHA_BITS", "GL_DEPTH_BITS", "GL_STENCIL_BITS", "GL_ALIASED_POINT_SIZE_RANGE", "GL_ALIASED_LINE_WIDTH_RANGE", "GL_MAX_TEXTURE_SIZE", "GL_MAX_VIEWPORT_DIMS", "GL_MAX_VERTEX_ATTRIBS", "GL_MAX_VERTEX_UNIFORM_VECTORS", "GL_MAX_VARYING_VECTORS", "GL_MAX_COMBINED_TEXTURE_IMAGE_UNITS", "GL_MAX_VERTEX_TEXTURE_IMAGE_UNITS", "GL_MAX_TEXTURE_IMAGE_UNITS", "GL_MAX_FRAGMENT_UNIFORM_VECTORS", "GL_MAX_CUBE_MAP_TEXTURE_SIZE", "GL_MAX_RENDERBUFFER_SIZE", "GL_VENDOR", "GL_RENDERER", "GL_VERSION", "GL_SHADING_LANGUAGE_VERSION", "GL_MAX_TEXTURE_MAX_ANISOTROPY_EXT", "GL_MAX_COLOR_ATTACHMENTS_WEBGL", "GL_MAX_DRAW_BUFFERS_WEBGL", "GL_TEXTURE_2D", "GL_TEXTURE_CUBE_MAP", "GL_TEXTURE_CUBE_MAP_POSITIVE_X", "GL_TEXTURE0", "GL_RGBA", "GL_FLOAT$1", "GL_UNSIGNED_BYTE$1", "GL_FRAMEBUFFER", "GL_FRAMEBUFFER_COMPLETE", "GL_COLOR_ATTACHMENT0", "GL_COLOR_BUFFER_BIT$1", "wrapLimits", "maxAnisotropic", "maxDrawbuffers", "maxColorAttachments", "readFloat", "readFloatTexture", "fbo", "pixels", "isIE", "npotTextureCube", "cubeTexture", "isNDArrayLike", "values", "flattenUtils", "arrayShape$1", "flattenArray", "flatten1D", "nx", "out", "flatten2D", "ny", "ptr", "row", "flatten3D", "nz", "ptr_", "col", "k", "flattenRec", "shape", "level", "stride", "out_", "array_", "arrayTypes", "int8", "int16", "int32", "uint8", "uint16", "uint32", "float", "float32", "glTypes", "dynamic$1", "stream", "usageTypes", "arrayFlatten", "arrayShape", "GL_STATIC_DRAW", "GL_STREAM_DRAW", "GL_UNSIGNED_BYTE$3", "GL_FLOAT$3", "DTYPES_SIZES", "typedArrayCode", "copyArray", "inp", "transpose", "shapeX", "shapeY", "strideX", "strideY", "wrapBufferState", "stats", "destroy<PERSON>uffer", "bufferCount", "bufferSet", "REGL<PERSON>uffer", "destroy", "streamPool", "createStream", "buffer", "initBufferFromData", "destroyStream", "stream$$1", "initBufferFromTypedArray", "usage", "dtype", "dimension", "persist", "flatData", "dim", "typedData", "transposeData", "handle", "createBuffer", "options", "deferInit", "persistent", "reg<PERSON><PERSON><PERSON><PERSON>", "byteLength", "setSubData", "subdata", "offset_", "converted", "restoreBuffers", "total", "wrapper", "points", "point", "triangles", "triangle", "primTypes", "GL_POINTS", "GL_LINES", "GL_TRIANGLES", "GL_BYTE$2", "GL_UNSIGNED_BYTE$4", "GL_SHORT$2", "GL_UNSIGNED_SHORT$2", "GL_INT$2", "GL_UNSIGNED_INT$2", "GL_ELEMENT_ARRAY_BUFFER", "GL_STREAM_DRAW$1", "GL_STATIC_DRAW$1", "wrapElementsState", "bufferState", "elementSet", "elementCount", "elementTypes", "R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createElementStream", "initElements", "destroyElementStream", "elements", "prim", "count", "predictedType", "vertCount", "primType", "destroyElements", "createElements", "reglElements", "FLOAT", "INT", "GL_UNSIGNED_SHORT$4", "convertToHalfFloat", "ushorts", "sgn", "exp", "frac", "s", "isArrayLike", "isPow2$1", "GL_COMPRESSED_TEXTURE_FORMATS", "GL_TEXTURE_2D$1", "GL_TEXTURE_CUBE_MAP$1", "GL_TEXTURE_CUBE_MAP_POSITIVE_X$1", "GL_RGBA$1", "GL_ALPHA", "GL_RGB", "GL_LUMINANCE", "GL_LUMINANCE_ALPHA", "GL_RGBA4", "GL_RGB5_A1", "GL_RGB565", "GL_UNSIGNED_SHORT_4_4_4_4$1", "GL_UNSIGNED_SHORT_5_5_5_1$1", "GL_UNSIGNED_SHORT_5_6_5$1", "GL_UNSIGNED_INT_24_8_WEBGL$1", "GL_DEPTH_COMPONENT", "GL_DEPTH_STENCIL", "GL_SRGB_EXT", "GL_SRGB_ALPHA_EXT", "GL_HALF_FLOAT_OES$1", "GL_COMPRESSED_RGB_S3TC_DXT1_EXT", "GL_COMPRESSED_RGBA_S3TC_DXT1_EXT", "GL_COMPRESSED_RGBA_S3TC_DXT3_EXT", "GL_COMPRESSED_RGBA_S3TC_DXT5_EXT", "GL_COMPRESSED_RGB_ATC_WEBGL", "GL_COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL", "GL_COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL", "GL_COMPRESSED_RGB_PVRTC_4BPPV1_IMG", "GL_COMPRESSED_RGB_PVRTC_2BPPV1_IMG", "GL_COMPRESSED_RGBA_PVRTC_4BPPV1_IMG", "GL_COMPRESSED_RGBA_PVRTC_2BPPV1_IMG", "GL_COMPRESSED_RGB_ETC1_WEBGL", "GL_UNSIGNED_BYTE$5", "GL_UNSIGNED_SHORT$3", "GL_UNSIGNED_INT$3", "GL_FLOAT$4", "GL_TEXTURE_WRAP_S", "GL_TEXTURE_WRAP_T", "GL_REPEAT", "GL_CLAMP_TO_EDGE$1", "GL_MIRRORED_REPEAT", "GL_TEXTURE_MAG_FILTER", "GL_TEXTURE_MIN_FILTER", "GL_NEAREST$1", "GL_LINEAR", "GL_NEAREST_MIPMAP_NEAREST$1", "GL_LINEAR_MIPMAP_NEAREST$1", "GL_NEAREST_MIPMAP_LINEAR$1", "GL_LINEAR_MIPMAP_LINEAR$1", "GL_GENERATE_MIPMAP_HINT", "GL_DONT_CARE", "GL_FASTEST", "GL_NICEST", "GL_TEXTURE_MAX_ANISOTROPY_EXT", "GL_UNPACK_ALIGNMENT", "GL_UNPACK_FLIP_Y_WEBGL", "GL_UNPACK_PREMULTIPLY_ALPHA_WEBGL", "GL_UNPACK_COLORSPACE_CONVERSION_WEBGL", "GL_BROWSER_DEFAULT_WEBGL", "GL_TEXTURE0$1", "MIPMAP_FILTERS", "CHANNELS_FORMAT", "FORMAT_CHANNELS", "objectName", "CANVAS_CLASS", "OFFSCREENCANVAS_CLASS", "CONTEXT2D_CLASS", "BITMAP_CLASS", "IMAGE_CLASS", "VIDEO_CLASS", "PIXEL_CLASSES", "TYPE_SIZES", "FORMAT_SIZES_SPECIAL", "isNumericArray", "arr", "isRectArray", "width", "classString", "isCanvasElement", "isOffscreenCanvas", "isContext2D", "isBitmap", "isImageElement", "isVideoElement", "isPixelData", "className", "typedArrayCode$1", "convertData", "preConvert", "image", "postConvert", "strideC", "p", "getTextureSize", "format", "height", "isMipmap", "isCube", "createTextureSet", "reglPoll", "contextState", "mipmapHint", "wrapModes", "magFilters", "minFilters", "colorSpace", "textureTypes", "textureFormats", "compressedTextureFormats", "supportedCompressedFormats", "supportedFormats", "textureFormatsInvert", "val", "textureTypesInvert", "magFiltersInvert", "minFiltersInvert", "wrapModesInvert", "colorFormats", "color", "glenum", "TexFlags", "copyFlags", "other", "parseFlags", "flags", "hasChannels", "hasFormat", "formatStr", "internalformat", "setFlags", "TexImage", "parseImage", "viewW", "viewH", "shapeC", "dd", "allocData", "setImage", "target", "miplevel", "setSubImage", "imagePool", "allocImage", "freeImage", "MipMap", "parseMipMapFromShape", "mipmap", "parseMipMapFromObject", "imgData", "setMipMap", "images", "mipPool", "allocMipMap", "freeMipMap", "TexInfo", "parseTexInfo", "minFilter", "magFilter", "wrapS", "wrapT", "wrap", "optWrapS", "optWrapT", "anisotropic", "hasMipMap", "setTexInfo", "textureCount", "textureSet", "numTexUnits", "textureUnits", "REGLTexture", "tempBind", "tempRestore", "prev", "unit", "createTexture2D", "a", "b", "reglTexture2D", "texInfo", "subimage", "x_", "y_", "level_", "imageData", "w_", "h_", "_w", "_h", "createTextureCube", "a0", "a1", "a2", "a3", "a4", "a5", "reglTextureCube", "faceInput", "radius_", "radius", "destroyTextures", "restoreTextures", "tex", "refreshTextures", "GL_RENDERBUFFER", "GL_RGBA4$1", "GL_RGB5_A1$1", "GL_RGB565$1", "GL_DEPTH_COMPONENT16", "GL_STENCIL_INDEX8", "GL_DEPTH_STENCIL$1", "GL_SRGB8_ALPHA8_EXT", "GL_RGBA32F_EXT", "GL_RGBA16F_EXT", "GL_RGB16F_EXT", "FORMAT_SIZES", "getRenderbufferSize", "wrapRenderbuffers", "formatTypes", "formatTypesInvert", "renderbufferCount", "renderbufferSet", "REGLRenderbuffer", "renderbuffer", "rb", "createRenderbuffer", "regl<PERSON><PERSON><PERSON><PERSON>", "restoreRenderbuffers", "GL_FRAMEBUFFER$1", "GL_RENDERBUFFER$1", "GL_TEXTURE_2D$2", "GL_TEXTURE_CUBE_MAP_POSITIVE_X$2", "GL_COLOR_ATTACHMENT0$1", "GL_DEPTH_ATTACHMENT", "GL_STENCIL_ATTACHMENT", "GL_DEPTH_STENCIL_ATTACHMENT", "GL_FRAMEBUFFER_COMPLETE$1", "GL_FRAMEBUFFER_INCOMPLETE_ATTACHMENT", "GL_FRAMEBUFFER_INCOMPLETE_MISSING_ATTACHMENT", "GL_FRAMEBUFFER_INCOMPLETE_DIMENSIONS", "GL_FRAMEBUFFER_UNSUPPORTED", "GL_HALF_FLOAT_OES$2", "GL_UNSIGNED_BYTE$6", "GL_FLOAT$5", "GL_RGB$1", "GL_RGBA$2", "GL_DEPTH_COMPONENT$1", "colorTextureFormatEnums", "textureFormatChannels", "textureTypeSizes", "GL_RGBA4$2", "GL_RGB5_A1$2", "GL_RGB565$2", "GL_DEPTH_COMPONENT16$1", "GL_STENCIL_INDEX8$1", "GL_DEPTH_STENCIL$2", "GL_SRGB8_ALPHA8_EXT$1", "GL_RGBA32F_EXT$1", "GL_RGBA16F_EXT$1", "GL_RGB16F_EXT$1", "colorRenderbufferFormatEnums", "statusCode", "wrapFBOState", "textureState", "renderbufferState", "framebufferState", "colorTextureFormats", "colorRenderbufferFormats", "colorTypes", "FramebufferAttachment", "decRef", "incRefAndCheckShape", "tw", "th", "attach", "location", "parseAttachment", "allocAttachment", "isTexture", "unwrapAttachment", "resizeAttachment", "framebufferCount", "framebufferSet", "REGLFramebuffer", "decFBORefs", "framebuffer", "updateFramebuffer", "colorAttachments", "status", "createFBO", "reg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "needsStencil", "colorBuffer", "colorTexture", "colorFormat", "colorType", "colorCount", "depthBuffer", "stencil<PERSON>uffer", "depthStencilBuffer", "depthStencilTexture", "depthAttachment", "stencilAttachment", "depthStencilAttachment", "commonColorAttachmentSize", "colorAttachmentSize", "createCubeFBO", "reglFramebufferCube", "params", "colorCubes", "cubeMapParams", "cube", "colors", "restoreFramebuffers", "fb", "GL_FLOAT$6", "GL_ARRAY_BUFFER$1", "AttributeRecord", "wrapAttributeState", "NUM_ATTRIBUTES", "attributeBindings", "vaoCount", "vaoSet", "state", "extVAO", "restoreVAO", "createVAO", "getVAO", "setVAOEXT", "setVAOEmulated", "destroyVAOEXT", "record", "extInstanced", "vao", "exti", "binding", "REGLVAO", "extension", "attr", "_attr", "updateVAO", "bufUpdated", "nattributes", "spec", "rec", "GL_FRAGMENT_SHADER", "GL_VERTEX_SHADER", "GL_ACTIVE_UNIFORMS", "GL_ACTIVE_ATTRIBUTES", "wrapShaderState", "fragShaders", "vertShaders", "ActiveInfo", "insertActiveInfo", "<PERSON><PERSON><PERSON><PERSON>", "cache", "programCache", "programList", "PROGRAM_COUNTER", "REGLProgram", "fragId", "vertId", "linkProgram", "attributeLocations", "numUniforms", "numAttributes", "m", "restoreShaders", "deleteShader", "attribLocations", "prevProgram", "idx", "GL_RGBA$3", "GL_UNSIGNED_BYTE$7", "GL_PACK_ALIGNMENT", "GL_FLOAT$7", "wrapReadPixels", "context", "glAttributes", "readPixelsImpl", "size", "readPixelsFBO", "readPixels", "slice", "join", "createEnvironment", "<PERSON><PERSON><PERSON><PERSON>", "linkedNames", "linkedValues", "link", "code", "vars", "def", "scope", "entry", "exit", "entryToString", "exitToString", "save", "prop", "conditional", "thenBlock", "else<PERSON>lock", "thenToString", "elseToString", "<PERSON><PERSON><PERSON><PERSON>", "globalBlock", "procedures", "proc", "arg", "body", "bodyToString", "compile", "src", "CUTE_COMPONENTS", "GL_UNSIGNED_BYTE$8", "ATTRIB_STATE_POINTER", "ATTRIB_STATE_CONSTANT", "DYN_FUNC$1", "DYN_PROP$1", "DYN_CONTEXT$1", "DYN_STATE$1", "DYN_THUNK", "DYN_CONSTANT$1", "DYN_ARRAY$1", "S_DITHER", "S_BLEND_ENABLE", "S_BLEND_COLOR", "S_BLEND_EQUATION", "S_BLEND_FUNC", "S_DEPTH_ENABLE", "S_DEPTH_FUNC", "S_DEPTH_RANGE", "S_DEPTH_MASK", "S_COLOR_MASK", "S_CULL_ENABLE", "S_CULL_FACE", "S_FRONT_FACE", "S_LINE_WIDTH", "S_POLYGON_OFFSET_ENABLE", "S_POLYGON_OFFSET_OFFSET", "S_SAMPLE_ALPHA", "S_SAMPLE_ENABLE", "S_SAMPLE_COVERAGE", "S_STENCIL_ENABLE", "S_STENCIL_MASK", "S_STENCIL_FUNC", "S_STENCIL_OPFRONT", "S_STENCIL_OPBACK", "S_SCISSOR_ENABLE", "S_SCISSOR_BOX", "S_VIEWPORT", "S_PROFILE", "S_FRAMEBUFFER", "S_VERT", "S_FRAG", "S_ELEMENTS", "S_PRIMITIVE", "S_COUNT", "S_OFFSET", "S_INSTANCES", "S_VAO", "SUFFIX_WIDTH", "SUFFIX_HEIGHT", "S_FRAMEBUFFER_WIDTH", "S_FRAMEBUFFER_HEIGHT", "S_VIEWPORT_WIDTH", "S_VIEWPORT_HEIGHT", "S_DRAWINGBUFFER", "S_DRAWINGBUFFER_WIDTH", "S_DRAWINGBUFFER_HEIGHT", "NESTED_OPTIONS", "GL_ARRAY_BUFFER$2", "GL_ELEMENT_ARRAY_BUFFER$1", "GL_FRAGMENT_SHADER$1", "GL_VERTEX_SHADER$1", "GL_TEXTURE_2D$3", "GL_TEXTURE_CUBE_MAP$2", "GL_CULL_FACE", "GL_BLEND", "GL_DITHER", "GL_STENCIL_TEST", "GL_DEPTH_TEST", "GL_SCISSOR_TEST", "GL_POLYGON_OFFSET_FILL", "GL_SAMPLE_ALPHA_TO_COVERAGE", "GL_SAMPLE_COVERAGE", "GL_FLOAT$8", "GL_FLOAT_VEC2", "GL_FLOAT_VEC3", "GL_FLOAT_VEC4", "GL_INT$3", "GL_INT_VEC2", "GL_INT_VEC3", "GL_INT_VEC4", "GL_BOOL", "GL_BOOL_VEC2", "GL_BOOL_VEC3", "GL_BOOL_VEC4", "GL_FLOAT_MAT2", "GL_FLOAT_MAT3", "GL_FLOAT_MAT4", "GL_SAMPLER_2D", "GL_SAMPLER_CUBE", "GL_TRIANGLES$1", "GL_FRONT", "GL_BACK", "GL_CW", "GL_CCW", "GL_MIN_EXT", "GL_MAX_EXT", "GL_ALWAYS", "GL_KEEP", "GL_ZERO", "GL_ONE", "GL_FUNC_ADD", "GL_LESS", "GL_FRAMEBUFFER$2", "GL_COLOR_ATTACHMENT0$2", "blendFuncs", "invalidBlendCombinations", "compareFuncs", "stencilOps", "shaderType", "orientationType", "isBufferArgs", "sortState", "Declaration", "thisDep", "contextDep", "propDep", "append", "isStatic", "decl", "createStaticDecl", "createDynamicDecl", "dyn", "numArgs", "subDyn", "subArgs", "SCOPE_DECL", "reglCore", "elementState", "uniformState", "attributeState", "shaderState", "drawState", "timer", "blendEquations", "extInstancing", "extDrawBuffers", "currentState", "nextState", "GL_STATE_NAMES", "GL_FLAGS", "GL_VARIABLES", "propName", "stateFlag", "sname", "cap", "init", "stateVariable", "func", "sharedState", "sharedConstants", "drawCallCounter", "createREGLEnvironment", "env", "SHARED", "shared", "nextVars", "currentVars", "variable", "constants", "argList", "scopeAttribs", "parseProfile", "staticOptions", "dynamicOptions", "profileEnable", "parse<PERSON><PERSON><PERSON><PERSON><PERSON>", "FRAMEBUFFER", "CONTEXT", "FRAMEBUFFER_FUNC", "FRAMEBUFFER_STATE", "parseViewportScissor", "parseBox", "box", "BOX_W", "BOX_H", "dynBox", "BOX", "BOX_X", "BOX_Y", "viewport", "prevViewport", "VIEWPORT", "parseAttribLocations", "staticProgram", "staticAttributes", "sAttributes", "bindings", "parseProgram", "<PERSON>se<PERSON><PERSON><PERSON>", "frag", "vert", "progVar", "SHADER_STATE", "progDef", "parseDraw", "parseElements", "IS_BUFFER_ARGS", "ELEMENT_STATE", "elementDefn", "elementStream", "ifte", "parsePrimitive", "primitive", "dynPrimitive", "PRIM_TYPES", "parseParam", "isOffset", "dynValue", "OFFSET", "parse<PERSON><PERSON><PERSON>ount", "dynCount", "parseGLState", "STATE", "parseStatic", "parseDynamic", "COMPARE_FUNCS", "Z_NEAR", "Z_FAR", "srcRGB", "srcAlpha", "dstRGB", "dstAlpha", "BLEND_FUNCS", "read", "prefix", "suffix", "INVALID_BLEND_COMBINATIONS", "SRC_RGB", "SRC_ALPHA", "DST_RGB", "DST_ALPHA", "BLEND_EQUATIONS", "RGB", "ALPHA", "checkProp", "cmp", "ref", "mask", "assert", "fail", "<PERSON><PERSON><PERSON>", "zpass", "STENCIL_OPS", "factor", "units", "FACTOR", "UNITS", "sampleValue", "sampleInvert", "VALUE", "INVERT", "parseUniforms", "staticUniforms", "dynamicUniforms", "UNIFORMS", "reglType", "ITEM", "parseAttributes", "dynamicAttributes", "attributeDefs", "attribute", "constant", "normalized", "divisor", "VALID_KEYS", "appendAttributeCode", "BUFFER_STATE", "defaultRecord", "BUFFER", "TYPE", "emitReadRecord", "parseVAO", "vaoRef", "parseContext", "staticContext", "dynamicContext", "parseArguments", "KEY_NAMES", "checkKeys", "viewportAndScissor", "draw", "copyBox", "defn", "dirty", "useVAO", "staticBindings", "emitContext", "contextEnter", "emitPollFramebuffer", "<PERSON><PERSON><PERSON><PERSON>", "GL", "EXT_DRAW_BUFFERS", "DRAW_BUFFERS", "BACK_BUFFER", "NEXT", "emitPollState", "CURRENT_VARS", "NEXT_VARS", "CURRENT_STATE", "NEXT_STATE", "CURRENT", "emitSetOptions", "filter", "flag", "injectExtensions", "emitProfile", "useScope", "incrementCounter", "STATS", "TIMER", "profileArg", "perfCounter", "CPU_START", "QUERY_COUNTER", "emitProfileStart", "emitProfileEnd", "scopeProfile", "USE_PROFILE", "start", "end", "emitAttributes", "typeLength", "emitBindAttribute", "ATTRIBUTE", "LOCATION", "BINDING", "CONST_COMPONENTS", "COMMON_KEYS", "emitB<PERSON>er", "SIZE", "DIVISOR", "emitConstant", "scopeAttrib", "emitUniforms", "infix", "uniform", "UNIFORM", "TEX_VALUE", "MAT_VALUE", "emit<PERSON><PERSON><PERSON>", "checkType", "checkVector", "checkTexture", "unroll", "TEX", "matSize", "STORAGE", "emitDraw", "outer", "inner", "DRAW_STATE", "drawOptions", "emitElements", "ELEMENTS", "emitCount", "COUNT", "emitValue", "PRIMITIVE", "INSTANCES", "EXT_INSTANCING", "ELEMENT_TYPE", "elementsStatic", "emitInstancing", "drawElements", "drawArrays", "emitRegular", "createBody", "emitBody", "parentEnv", "emitDrawBody", "emitDrawProc", "drawCache", "PROG_ID", "CACHED_PROC", "emitBatchDynamicShaderBody", "all", "emitBatchBody", "contextDynamic", "BATCH_ID", "PROP_LIST", "NUM_PROPS", "PROPS", "isInnerDefn", "isOuterDefn", "prog<PERSON><PERSON>", "PROGRAM", "emitBatchProc", "batch", "needsContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "progDefn", "batchCache", "emitScopeProc", "opt", "save<PERSON><PERSON><PERSON>", "isDynamicObject", "props", "splatObject", "globals", "objectRef", "deps", "appendBlock", "compileCommand", "poll", "refresh", "common", "INSTANCING", "GL_QUERY_RESULT_EXT", "GL_QUERY_RESULT_AVAILABLE_EXT", "GL_TIME_ELAPSED_EXT", "createTimer", "queryPool", "allocQuery", "freeQuery", "query", "pendingQueries", "begin<PERSON><PERSON>y", "pushScopeStats", "<PERSON><PERSON><PERSON><PERSON>", "PendingStats", "pendingStatsPool", "allocPendingStats", "freePendingStats", "pendingStats", "ps", "timeSum", "queryPtr", "update", "queryTime", "startPtr", "endPtr", "GL_COLOR_BUFFER_BIT", "GL_DEPTH_BUFFER_BIT", "GL_STENCIL_BUFFER_BIT", "GL_ARRAY_BUFFER", "CONTEXT_LOST_EVENT", "CONTEXT_RESTORED_EVENT", "DYN_PROP", "DYN_CONTEXT", "DYN_STATE", "find", "haystack", "needle", "wrapREGL", "contextLost", "extensionState", "stats$$1", "START_TIME", "WIDTH", "HEIGHT", "core", "rafCallbacks", "lossCallbacks", "restoreCallbacks", "destroyCallbacks", "activeRAF", "handleRAF", "startRAF", "stopRAF", "handleContextLoss", "event", "handleContextRestored", "compileProcedure", "flattenNestedOptions", "merge", "child", "separateDynamic", "useArrays", "staticItems", "dynamicItems", "option", "compiled", "EMPTY_ARRAY", "reserve", "REGLCommand", "setFBO", "clearImpl", "_", "clearFlags", "clear", "frame", "cancel", "pendingCancel", "index", "pollViewport", "scissor<PERSON><PERSON>", "now", "addListener", "callback", "callbacks", "regl"], "sourceRoot": ""}