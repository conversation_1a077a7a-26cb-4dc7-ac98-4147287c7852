{"version": 3, "file": "wrappers__auth.d7fe3fb1.async.js", "mappings": "wLAYA,EAAe,CACb,CAAEA,KAAM,IAAKC,SAAU,WAAY,EACnC,CACED,KAAM,QACNE,OAAQ,GACRC,OAAQ,CACN,CACEH,KAAM,cACNI,UAAW,cACb,CAAC,CAEL,EACA,CACEC,KAAM,mBACNL,KAAM,YACNM,SAAU,WACVF,UAAW,wBACXG,SAAU,CAAC,iBAAiB,EAC5BC,WAAY,EACd,EACA,CACEH,KAAM,kBACNI,KAAM,YACNC,OAAQ,gBACRJ,SAAU,kBACVN,KAAM,cACNG,OAAQ,CACN,CACEH,KAAM,cACNO,SAAU,CAAC,iBAAiB,EAC5BN,SAAU,qBACZ,EACA,CACEI,KAAM,UACNI,KAAM,YACNT,KAAM,sBACNI,UAAW,eACXE,SAAU,gBACVC,SAAU,CAAC,iBAAiB,EAC5BI,MAAO,GACPD,OAAQ,eACV,EACA,CACEL,KAAM,iBACNL,KAAM,mDACNW,MAAO,GACPP,UAAW,iCACXI,WAAY,EACd,EACA,CACEH,KAAM,YACNI,KAAM,YACNE,MAAO,GACPL,SAAU,aACVC,SAAU,CAAC,iBAAiB,EAC5BP,KAAM,yBACNI,UAAW,oBACXM,OAAQ,eACV,CAAC,CAEL,EACA,CACEL,KAAM,yBACNI,KAAM,wBACNT,KAAM,qBACNM,SAAU,WACVK,MAAO,GACPR,OAAQ,CACN,CAAEH,KAAM,qBAAsBI,UAAW,2BAA4B,EACrE,CACEC,KAAM,SACNG,WAAY,GACZR,KAAM,4BACNI,UAAW,mCACXO,MAAO,EACT,CAAC,CAEL,EACA,CACEN,KAAM,oBACNI,KAAM,cACNH,SAAU,UACVN,KAAM,YACNG,OAAQ,CACN,CACEH,KAAM,YACNO,SAAU,CAAC,iBAAiB,EAC5BH,UAAW,mBACXO,MAAO,GACPH,WAAY,EACd,EACA,CACEH,KAAM,SACNL,KAAM,gBACNW,MAAO,GACPP,UAAW,0BACXI,WAAY,EACd,EACA,CACEH,KAAM,kBACNL,KAAM,qCACNQ,WAAY,GACZL,OAAQ,CACN,CACEH,KAAM,qCACNI,UAAW,mBACXO,MAAO,GACPH,WAAY,EACd,EACA,CACEH,KAAM,iBACNL,KAAM,+EACNI,UAAW,+CACXO,MAAO,GACPH,WAAY,EACd,CAAC,CAEL,EACA,CACEH,KAAM,+BACNL,KAAM,8FACNQ,WAAY,GACZL,OAAQ,CACN,CACEH,KAAM,8FACNI,UAAW,wCACXO,MAAO,GACPH,WAAY,EACd,CAAC,CAEL,EACA,CACEH,KAAM,+BACNL,KAAM,8FACNQ,WAAY,GACZL,OAAQ,CACN,CACEH,KAAM,8FACNI,UAAW,4CACXO,MAAO,EACT,EACA,CACEN,KAAM,gBACNL,KAAM,gIACNI,UAAW,wCACXO,MAAO,GACPH,WAAY,EACd,EACA,CACEH,KAAM,eACNL,KAAM,qHACNI,UAAW,gDACXO,MAAO,GACPH,WAAY,EACd,CAAC,CAEL,EACA,CACEH,KAAM,YACNM,MAAO,GACPX,KAAM,kCACNI,UAAW,qCACXI,WAAY,EACd,EACA,CACEH,KAAM,qBACNL,KAAM,8DACNI,UAAW,4CACXO,MAAO,GACPH,WAAY,EACd,EACA,CACEH,KAAM,qBACNL,KAAM,6BACNI,UAAW,uBACXO,MAAO,GACPH,WAAY,EACd,EACA,CACEH,KAAM,kBACNL,KAAM,qCACNQ,WAAY,GACZL,OAAQ,CACN,CACEE,KAAM,SACNL,KAAM,qCACNI,UAAW,4BACXO,MAAO,GACPH,WAAY,EACd,EACA,CACEH,KAAM,gBACNL,KAAM,4CACNQ,WAAY,GACZL,OAAQ,CACN,CACEH,KAAM,4CACNI,UAAW,iBACXO,MAAO,GACPH,WAAY,EACd,CAAC,CAEL,EACA,CACEH,KAAM,iBACNL,KAAM,kEACNI,UAAW,iCACXO,MAAO,GACPH,WAAY,EACd,EACA,CACEH,KAAM,iBACNL,KAAM,mDACNI,UAAW,eACXI,WAAY,EACd,EACA,CACEH,KAAM,OACNL,KAAM,mDACNI,UAAW,eACXI,WAAY,EACd,CAAC,CAEL,CAAC,CAEL,EACA,CACEH,KAAM,iBACNI,KAAM,OACNC,OAAQ,gBACRJ,SAAU,OACVN,KAAM,aACNW,MAAO,GACPR,OAAQ,CACN,CAAEH,KAAM,aAAcO,SAAU,CAAC,iBAAiB,EAAGN,SAAU,MAAO,EACtE,CACEI,KAAM,cACNL,KAAM,eACNI,UAAW,iCACXE,SAAU,YACVC,SAAU,CAAC,iBAAiB,EAC5BI,MAAO,EACT,EACA,CACEN,KAAM,aACNL,KAAM,cACNI,UAAW,gCACXE,SAAU,cACVC,SAAU,CAAC,iBAAiB,EAC5BI,MAAO,EACT,EACA,CACEN,KAAM,aACNL,KAAM,cACNI,UAAW,gCACXE,SAAU,cACVC,SAAU,CAAC,iBAAiB,EAC5BI,MAAO,EACT,EACA,CACEN,KAAM,eACNL,KAAM,gBACNM,SAAU,gBACVC,SAAU,CAAC,iBAAiB,EAC5BH,UAAW,kCACXO,MAAO,EACT,CAAC,CAEL,EACA,CACEN,KAAM,aACNG,WAAY,GACZL,OAAQ,CACN,CACEE,KAAM,iBACNL,KAAM,sCACNI,UAAW,2BACXI,WAAY,EACd,EACA,CACEH,KAAM,OACNL,KAAM,uBACNI,UAAW,eACXI,WAAY,EACd,CAAC,CAEL,EACA,CACEH,KAAM,gBACNG,WAAY,GACZL,OAAQ,CACN,CACEE,KAAM,SACNL,KAAM,wBACNI,UAAW,aACXI,WAAY,EACd,CAAC,CAEL,EACA,CACEH,KAAM,kBACNL,KAAM,cACNS,KAAM,cACND,WAAY,GACZL,OAAQ,CACN,CACEE,KAAM,OACNL,KAAM,8BACNI,UAAW,qCACXO,MAAO,EACT,EACA,CACEN,KAAM,UACNL,KAAM,iCACNI,UAAW,wCACXO,MAAO,EACT,EACA,CACEN,KAAM,iBACNL,KAAM,wDACNI,UAAW,+CACXO,MAAO,GACPH,WAAY,EACd,CAAC,CAEL,EACA,CACEH,KAAM,iBACNI,KAAM,qBACNT,KAAM,0BACNQ,WAAY,GACZG,MAAO,GACPR,OAAQ,CACN,CACEE,KAAM,SACNL,KAAM,0BACNI,UAAW,iCACXO,MAAO,GACPH,WAAY,EACd,EACA,CACEH,KAAM,SACNL,KAAM,sDACNI,UAAW,wCACXO,MAAO,GACPH,WAAY,EACd,CAAC,CAEL,EACA,CACEH,KAAM,uBACNI,KAAM,qBACNT,KAAM,YACNM,SAAU,WACVK,MAAO,GACPR,OAAQ,CACN,CACEH,KAAM,YACNO,SAAU,CAAC,iBAAiB,EAC5BN,SAAU,QACZ,EACA,CACEI,KAAM,UACNL,KAAM,mBACNI,UAAW,0BACXE,SAAU,eACVC,SAAU,CAAC,iBAAiB,EAC5BI,MAAO,EACT,EACA,CACEN,KAAM,kBACNL,KAAM,mCACNI,UAAW,uBACXO,MAAO,GACPH,WAAY,EACd,EACA,CACEH,KAAM,aACNL,KAAM,uBACNI,UAAW,qCACXE,SAAU,qBACVC,SAAU,CAAC,iBAAiB,EAC5BI,MAAO,EACT,EACA,CACEN,KAAM,iBACNL,KAAM,2BACNI,UAAW,yCACXO,MAAO,GACPH,WAAY,EACd,CAAC,CAEL,EACA,CACER,KAAM,wBACNI,UAAW,+BACXO,MAAO,GACPH,WAAY,EACd,EACA,CACEH,KAAM,mBACNI,KAAM,SACNT,KAAM,eACNI,UAAW,sBACXO,MAAO,EACT,EACA,CACEX,KAAM,IACNE,OAAQ,GACRE,UAAW,OACb,CAAC,E,WCnaH,EAAe,UAAM,CACnB,IAAMM,KAASE,EAAAA,WAAU,EACzB,SAASC,EAAYC,EAAcC,EAAU,CAC3CD,OAAAA,EAAaE,QAAQ,SAACC,EAAS,CAC7BF,EAASG,KAAKD,CAAI,EACdA,EAAKd,QAAUc,EAAKd,OAAOgB,SAAW,IACxCF,EAAKF,SAAWF,EAAYI,EAAKd,OAAQY,CAAQ,EAErD,CAAC,EACMA,CACT,CACA,IAAMK,KAAaC,EAAAA,eAAc,EAE3BC,EAAYT,EAAYV,EAAQ,CAAC,CAAC,EAClCoB,EAAcD,EAAUE,UAC5B,SAACP,EAAM,CAAF,OAAKA,EAAKjB,OAASoB,EAAWK,UAAU,CAC/C,EAEA,GAAIF,EAAc,GAAI,KAAAG,EAAAC,EACdC,GAASF,EAAGJ,EAAUC,CAAW,KAAC,MAAAG,IAAA,cAAtBA,EAAwBpB,SAC1C,OAAIsB,GAAalB,IAAM,MAANA,IAAM,SAAAiB,EAANjB,EAAQmB,gBAAY,MAAAF,IAAA,QAApBA,EAAsBG,SAASF,CAAS,KAChDG,EAAAA,KAACC,EAAAA,OAAM,EAAE,KAETD,EAAAA,KAACE,EAAAA,SAAQ,CAACC,GAAG,YAAY,CAAE,CAEtC,CACA,SAAOH,EAAAA,KAACE,EAAAA,SAAQ,CAACC,GAAIC,EAAAA,EAAW,CAAE,CACpC,C", "sources": ["webpack://labwise-web/./config/routes.ts", "webpack://labwise-web/./src/wrappers/auth.tsx"], "sourcesContent": ["/**\n * @name umi 的路由配置\n * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置\n * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。\n * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。\n * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。\n * @param redirect 配置路由跳转\n * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验\n * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题\n * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User\n * @doc https://umijs.org/docs/guides/routes\n */\nexport default [\n  { path: '/', redirect: '/projects' }, // 重定向\n  {\n    path: '/user',\n    layout: false,\n    routes: [\n      {\n        path: '/user/login',\n        component: './User/Login'\n      }\n    ]\n  },\n  {\n    name: 'account.settings',\n    path: '/settings',\n    resource: 'settings',\n    component: '@/pages/user-settings',\n    wrappers: ['@/wrappers/auth'],\n    hideInMenu: true\n  },\n  {\n    name: 'list.playground',\n    icon: 'chart|svg',\n    access: 'internalStaff',\n    resource: 'inner_workspace',\n    path: '/playground',\n    routes: [\n      {\n        path: '/playground',\n        wrappers: ['@/wrappers/auth'],\n        redirect: '/playground/commend'\n      },\n      {\n        name: 'commend',\n        icon: 'chart|svg',\n        path: '/playground/commend',\n        component: './playground',\n        resource: 'inner_comment',\n        wrappers: ['@/wrappers/auth'],\n        exact: true,\n        access: 'internalStaff'\n      },\n      {\n        name: 'viewByBackbone',\n        path: '/playground/commend/view-by-backbone/:backboneId',\n        exact: true,\n        component: '@/pages/route/view-by-backbone',\n        hideInMenu: true\n      },\n      {\n        name: 'AISandbox',\n        icon: 'chart|svg',\n        exact: true,\n        resource: 'ai_sandbox',\n        wrappers: ['@/wrappers/auth'],\n        path: '/playground/ai-sandbox',\n        component: '@/pages/AISandbox',\n        access: 'internalStaff'\n      }\n    ]\n  },\n  {\n    name: 'list.experimental-zone',\n    icon: 'experimental_zone|svg',\n    path: '/experimental-zone',\n    resource: 'exp_zone',\n    exact: true,\n    routes: [\n      { path: '/experimental-zone', component: '@/pages/experimental-zone' },\n      {\n        name: 'search',\n        hideInMenu: true,\n        path: '/experimental-zone/search',\n        component: '@/pages/experimental-zone/search',\n        exact: true\n      }\n    ]\n  },\n  {\n    name: 'list.project-list',\n    icon: 'project|svg',\n    resource: 'project',\n    path: '/projects',\n    routes: [\n      {\n        path: '/projects',\n        wrappers: ['@/wrappers/auth'],\n        component: '@/pages/projects',\n        exact: true,\n        hideInMenu: true\n      },\n      {\n        name: 'detail',\n        path: '/projects/:id',\n        exact: true,\n        component: '@/pages/projects/detail',\n        hideInMenu: true\n      },\n      {\n        name: 'detail.reaction',\n        path: '/projects/:id/reaction/:reactionId',\n        hideInMenu: true,\n        routes: [\n          {\n            path: '/projects/:id/reaction/:reactionId',\n            component: '@/pages/reaction',\n            exact: true,\n            hideInMenu: true\n          },\n          {\n            name: 'execute.detail',\n            path: '/projects/:id/reaction/:reactionId/experiment-execute/detail/:experimentalNo', // 实验详情\n            component: '@/pages/experiment/experiment-execute/detail',\n            exact: true,\n            hideInMenu: true\n          }\n        ]\n      },\n      {\n        name: 'detail.experimentalProcedure',\n        path: '/projects/:projectId/reaction/:reactionId/experimental-procedure/detail/:experimentDesignId',\n        hideInMenu: true,\n        routes: [\n          {\n            path: '/projects/:projectId/reaction/:reactionId/experimental-procedure/detail/:experimentDesignId',\n            component: '@/pages/experimental-procedure/detail',\n            exact: true,\n            hideInMenu: true\n          }\n        ]\n      },\n      {\n        name: 'detail.experiment-conclusion',\n        path: '/projects/:projectId/reaction/:reactionId/experimental-procedure/conclusion/:experimentalNo',\n        hideInMenu: true,\n        routes: [\n          {\n            path: '/projects/:projectId/reaction/:reactionId/experimental-procedure/conclusion/:experimentalNo',\n            component: '@/pages/experimental-procedure/conclusion',\n            exact: true\n          },\n          {\n            name: 'knowledgeBase',\n            path: '/projects/:projectId/reaction/:reactionId/experimental-procedure/conclusion/:experimentalNo/knowledgeBase/:experimentDesignId',\n            component: '@/pages/experimental-procedure/detail',\n            exact: true,\n            hideInMenu: true\n          },\n          {\n            name: 'reportDetail',\n            path: '/projects/:projectId/reaction/:reactionId/experimental-procedure/conclusion/:experimentalNo/reportDetail/:check_no',\n            component: '@/pages/experimental-procedure/report-details',\n            exact: true,\n            hideInMenu: true\n          }\n        ]\n      },\n      {\n        name: 'quotation',\n        exact: true,\n        path: '/projects/:id/quotation-records',\n        component: '@/pages/projects/quotation-records',\n        hideInMenu: true\n      },\n      {\n        name: 'detail.quoteDetail',\n        path: '/projects/:id/quotation-records/:quoteMoleculeId/quote-info',\n        component: '@/pages/projects/quotation-records/detail',\n        exact: true,\n        hideInMenu: true\n      },\n      {\n        name: 'detail.addMolecule',\n        path: '/projects/:id/add-molecule',\n        component: '@/pages/add-molecule',\n        exact: true,\n        hideInMenu: true\n      },\n      {\n        name: 'detail.compound',\n        path: '/projects/:id/compound/:compoundId',\n        hideInMenu: true,\n        routes: [\n          {\n            name: 'detail',\n            path: '/projects/:id/compound/:compoundId',\n            component: '@/pages/compound/refactor',\n            exact: true,\n            hideInMenu: true\n          },\n          {\n            name: 'detail.create',\n            path: '/projects/:id/compound/:compoundId/create',\n            hideInMenu: true,\n            routes: [\n              {\n                path: '/projects/:id/compound/:compoundId/create',\n                component: './route/create',\n                exact: true,\n                hideInMenu: true\n              }\n            ]\n          },\n          {\n            name: 'viewByBackbone',\n            path: '/projects/:id/compound/:compoundId/view-by-backbone/:backboneId',\n            component: '@/pages/route/view-by-backbone',\n            exact: true,\n            hideInMenu: true\n          },\n          {\n            name: 'viewByBackbone',\n            path: '/projects/:id/compound/:compoundId/view/:routeId',\n            component: './route/view',\n            hideInMenu: true\n          },\n          {\n            name: 'edit',\n            path: '/projects/:id/compound/:compoundId/edit/:routeId',\n            component: './route/edit',\n            hideInMenu: true\n          }\n        ]\n      }\n    ]\n  },\n  {\n    name: 'list.workspace',\n    icon: 'home',\n    access: 'internalStaff',\n    resource: 'home',\n    path: '/workspace',\n    exact: true,\n    routes: [\n      { path: '/workspace', wrappers: ['@/wrappers/auth'], redirect: 'home' },\n      {\n        name: 'myWorkbench',\n        path: 'my-workbench',\n        component: '@/pages/workspace/my-workbench',\n        resource: 'workbench',\n        wrappers: ['@/wrappers/auth'],\n        exact: true\n      },\n      {\n        name: 'myCompound',\n        path: 'my-compound',\n        component: '@/pages/workspace/my-compound',\n        resource: 'my_compound',\n        wrappers: ['@/wrappers/auth'],\n        exact: true\n      },\n      {\n        name: 'myReaction',\n        path: 'my-reaction',\n        component: '@/pages/workspace/my-reaction',\n        resource: 'my_reaction',\n        wrappers: ['@/wrappers/auth'],\n        exact: true\n      },\n      {\n        name: 'myExperiment',\n        path: 'my-experiment',\n        resource: 'my_experiment',\n        wrappers: ['@/wrappers/auth'],\n        component: '@/pages/workspace/my-experiment',\n        exact: true\n      }\n    ]\n  },\n  {\n    name: 'list.route',\n    hideInMenu: true,\n    routes: [\n      {\n        name: 'viewByBackbone',\n        path: '/route/view-by-backbone/:backboneId',\n        component: './route/view-by-backbone',\n        hideInMenu: true\n      },\n      {\n        name: 'view',\n        path: '/route/view/:routeId',\n        component: './route/view',\n        hideInMenu: true\n      }\n    ]\n  },\n  {\n    name: 'list.reaction',\n    hideInMenu: true,\n    routes: [\n      {\n        name: 'detail',\n        path: '/reaction/:reactionId',\n        component: './reaction',\n        hideInMenu: true\n      }\n    ]\n  },\n  {\n    name: 'list.experiment',\n    path: '/experiment',\n    icon: 'execute|svg',\n    hideInMenu: true,\n    routes: [\n      {\n        name: 'plan',\n        path: '/experiment/experiment-plan',\n        component: '@/pages/experiment/experiment-plan',\n        exact: true\n      },\n      {\n        name: 'execute',\n        path: '/experiment/experiment-execute',\n        component: '@/pages/experiment/experiment-execute',\n        exact: true\n      },\n      {\n        name: 'execute.detail',\n        path: '/experiment/experiment-execute/detail/:experimentalNo', // 实验详情\n        component: '@/pages/experiment/experiment-execute/detail',\n        exact: true,\n        hideInMenu: true\n      }\n    ]\n  },\n  {\n    name: 'list.procedure',\n    icon: 'experimentPlan|svg',\n    path: '/experimental-procedure',\n    hideInMenu: true,\n    exact: true,\n    routes: [\n      {\n        name: 'detail',\n        path: '/experimental-procedure',\n        component: '@/pages/experimental-procedure',\n        exact: true,\n        hideInMenu: true\n      },\n      {\n        name: 'detail',\n        path: '/experimental-procedure/detail/:experimentDesignId ',\n        component: '@/pages/experimental-procedure/detail',\n        exact: true,\n        hideInMenu: true\n      }\n    ]\n  },\n  {\n    name: 'list.material-manage',\n    icon: 'materialManage|svg',\n    path: '/material',\n    resource: 'material',\n    exact: true,\n    routes: [\n      {\n        path: '/material',\n        wrappers: ['@/wrappers/auth'],\n        redirect: 'manage'\n      },\n      {\n        name: 'storage',\n        path: '/material/manage',\n        component: '@/pages/material-manage',\n        resource: 'material_lib',\n        wrappers: ['@/wrappers/auth'],\n        exact: true\n      },\n      {\n        name: 'search-molecule',\n        path: '/material/manage/search-molecule',\n        component: '@/pages/add-molecule',\n        exact: true,\n        hideInMenu: true\n      },\n      {\n        name: 'black-list',\n        path: '/material/black-list',\n        component: '@/pages/material-manage/black-list',\n        resource: 'material_blacklist',\n        wrappers: ['@/wrappers/auth'],\n        exact: true\n      },\n      {\n        name: 'black-list.add',\n        path: '/material/black-list/add',\n        component: '@/pages/material-manage/black-list/add',\n        exact: true,\n        hideInMenu: true\n      }\n    ]\n  },\n  {\n    path: '/message-notification',\n    component: '@/pages/message-notification',\n    exact: true,\n    hideInMenu: true\n  },\n  {\n    name: 'list.batch-retro',\n    icon: 'upload',\n    path: '/batch-retro',\n    component: '@/pages/batch-retro',\n    exact: true\n  },\n  {\n    path: '*',\n    layout: false,\n    component: './404'\n  }\n]\n", "import { LOGIN_PATH } from '@/constants'\nimport { Navigate, Outlet, useAccess, useRouteProps } from 'umi'\nimport routes from '../../config/routes'\nexport default () => {\n  const access = useAccess()\n  function convertMenu(menuTreeList, children) {\n    menuTreeList.forEach((item) => {\n      children.push(item)\n      if (item.routes && item.routes.length !== 0) {\n        item.children = convertMenu(item.routes, children)\n      }\n    })\n    return children\n  }\n  const routeProps = useRouteProps()\n\n  const allRoutes = convertMenu(routes, [])\n  const curRouteIdx = allRoutes.findIndex(\n    (item) => item.path === routeProps.originPath\n  )\n\n  if (curRouteIdx > -1) {\n    const routeCode = allRoutes[curRouteIdx]?.resource\n    if (routeCode && access?.authCodeList?.includes(routeCode)) {\n      return <Outlet />\n    } else {\n      return <Navigate to=\"/workspace\" />\n    }\n  }\n  return <Navigate to={LOGIN_PATH} />\n}\n"], "names": ["path", "redirect", "layout", "routes", "component", "name", "resource", "wrappers", "hideInMenu", "icon", "access", "exact", "useAccess", "convertMenu", "menuTreeList", "children", "for<PERSON>ach", "item", "push", "length", "routeProps", "useRouteProps", "allRoutes", "curRouteIdx", "findIndex", "originPath", "_allRoutes$curRouteId", "_access$authCodeList", "routeCode", "authCodeList", "includes", "_jsx", "Outlet", "Navigate", "to", "LOGIN_PATH"], "sourceRoot": ""}