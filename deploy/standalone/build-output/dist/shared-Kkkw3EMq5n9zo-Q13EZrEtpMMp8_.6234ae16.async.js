"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[1104],{51042:function(z,p,e){var o=e(1413),r=e(67294),d=e(42110),a=e(84089),M=function(C,P){return r.createElement(a.Z,(0,o.Z)((0,o.Z)({},C),{},{ref:P,icon:d.Z}))},i=r.forwardRef(M);p.Z=i},31199:function(z,p,e){var o=e(1413),r=e(45987),d=e(67294),a=e(92755),M=e(85893),i=["fieldProps","min","proFieldProps","max"],D=function(l,Z){var g=l.fieldProps,T=l.min,W=l.proFieldProps,I=l.max,x=(0,r.Z)(l,i);return(0,M.jsx)(a.Z,(0,o.Z)({valueType:"digit",fieldProps:(0,o.Z)({min:T,max:I},g),ref:Z,filedConfig:{defaultProps:{width:"100%"}},proFieldProps:W},x))},C=d.forwardRef(D);p.Z=C},5966:function(z,p,e){var o=e(97685),r=e(1413),d=e(45987),a=e(21770),M=e(98138),i=e(55241),D=e(97435),C=e(67294),P=e(92755),l=e(85893),Z=["fieldProps","proFieldProps"],g=["fieldProps","proFieldProps"],T="text",W=function(s){var _=s.fieldProps,R=s.proFieldProps,n=(0,d.Z)(s,Z);return(0,l.jsx)(P.Z,(0,r.Z)({valueType:T,fieldProps:_,filedConfig:{valueType:T},proFieldProps:R},n))},I=function(s){var _=(0,a.Z)(s.open||!1,{value:s.open,onChange:s.onOpenChange}),R=(0,o.Z)(_,2),n=R[0],f=R[1];return(0,l.jsx)(M.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(b){var m,S=b.getFieldValue(s.name||[]);return(0,l.jsx)(i.Z,(0,r.Z)((0,r.Z)({getPopupContainer:function(t){return t&&t.parentNode?t.parentNode:t},onOpenChange:function(t){return f(t)},content:(0,l.jsxs)("div",{style:{padding:"4px 0"},children:[(m=s.statusRender)===null||m===void 0?void 0:m.call(s,S),s.strengthText?(0,l.jsx)("div",{style:{marginTop:10},children:(0,l.jsx)("span",{children:s.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},s.popoverProps),{},{open:n,children:s.children}))}})},x=function(s){var _=s.fieldProps,R=s.proFieldProps,n=(0,d.Z)(s,g),f=(0,C.useState)(!1),B=(0,o.Z)(f,2),b=B[0],m=B[1];return _!=null&&_.statusRender&&n.name?(0,l.jsx)(I,{name:n.name,statusRender:_==null?void 0:_.statusRender,popoverProps:_==null?void 0:_.popoverProps,strengthText:_==null?void 0:_.strengthText,open:b,onOpenChange:m,children:(0,l.jsx)("div",{children:(0,l.jsx)(P.Z,(0,r.Z)({valueType:"password",fieldProps:(0,r.Z)((0,r.Z)({},(0,D.Z)(_,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(A){var t;_==null||(t=_.onBlur)===null||t===void 0||t.call(_,A),m(!1)},onClick:function(A){var t;_==null||(t=_.onClick)===null||t===void 0||t.call(_,A),m(!0)}}),proFieldProps:R,filedConfig:{valueType:T}},n))})}):(0,l.jsx)(P.Z,(0,r.Z)({valueType:"password",fieldProps:_,proFieldProps:R,filedConfig:{valueType:T}},n))},v=W;v.Password=x,v.displayName="ProFormComponent",p.Z=v},61282:function(z,p,e){var o=e(97857),r=e.n(o),d=e(15009),a=e.n(d),M=e(99289),i=e.n(M),D=e(5574),C=e.n(D),P=e(15415),l=e(32884),Z=e(85576),g=e(67294),T=e(19828),W=e(85893),I=function(v){var F=v.init,s=v.open,_=v.width,R=_===void 0?"80%":_,n=v.onClose,f=v.clearWhenClose,B=f===void 0?!1:f,b=(0,P.Z)(F),m=b.getSmiles,S=b.setSmiles,A=b.props,t=(0,g.useState)(!1),y=C()(t,2),G=y[0],$=y[1],L=function(){var K=i()(a()().mark(function U(O){var N;return a()().wrap(function(E){for(;;)switch(E.prev=E.next){case 0:if($(!0),O){E.next=6;break}n==null||n(null),$(!1),E.next=12;break;case 6:return E.next=8,m();case 8:return N=E.sent,E.next=11,n==null?void 0:n(N||null);case 11:$(!1);case 12:B&&S("");case 13:case"end":return E.stop()}},U)}));return function(O){return K.apply(this,arguments)}}();return(0,W.jsx)(Z.Z,{title:(0,l.oz)("edit-molecule"),width:R,open:s,onOk:i()(a()().mark(function K(){return a()().wrap(function(O){for(;;)switch(O.prev=O.next){case 0:return O.abrupt("return",L(!0));case 1:case"end":return O.stop()}},K)})),onCancel:i()(a()().mark(function K(){return a()().wrap(function(O){for(;;)switch(O.prev=O.next){case 0:return O.abrupt("return",L());case 1:case"end":return O.stop()}},K)})),confirmLoading:G,focusTriggerAfterClose:!0,destroyOnClose:!0,centered:!0,children:(0,W.jsx)(T.Z,r()({},A))})};p.Z=I},19828:function(z,p,e){var o=e(97857),r=e.n(o),d=e(67294),a=e(3671),M=e(85893),i=(0,d.lazy)(function(){return Promise.all([e.e(17),e.e(1905),e.e(8424),e.e(7818),e.e(8232),e.e(168),e.e(94),e.e(5842),e.e(8857)]).then(e.bind(e,33314))}),D=function(P){return(0,M.jsx)(d.Suspense,{fallback:(0,M.jsx)(a.Z,{}),children:(0,M.jsx)(i,r()({},P))})};p.Z=D},36295:function(z,p,e){e.d(p,{H:function(){return o.Z},OL:function(){return d.O},PE:function(){return r.Z}});var o=e(61282),r=e(19828),d=e(39442)},39442:function(z,p,e){e.d(p,{O:function(){return a}});var o=e(5574),r=e.n(o),d=e(67294),a=function(){var i=(0,d.useState)({}),D=r()(i,2),C=D[0],P=D[1],l=function(g){var T=new Promise(function(W,I){P({init:g||"",open:!0,clearWhenClose:!0,onClose:function(v){v===null?I():W(v),P({open:!1,init:""})}})});return T};return{dialogProps:C,inputSmiles:l}}},42282:function(z,p,e){var o=e(97857),r=e.n(o),d=e(15009),a=e.n(d),M=e(19632),i=e.n(M),D=e(99289),C=e.n(D),P=e(5574),l=e.n(P),Z=e(36295),g=e(61282),T=e(99814),W=e(32884),I=e(15370),x=e(51042),v=e(78367),F=e(67294),s=e(85893),_=function(n){var f=n.value,B=n.onChange,b=n.multiple,m=b===void 0?!0:b,S=n.className,A=n.disabled,t=n.type,y=t===void 0?"molecule":t,G=(0,F.useState)(typeof f=="string"?[f]:f||[]),$=l()(G,2),L=$[0],K=$[1],U=(0,Z.OL)(),O=U.dialogProps,N=U.inputSmiles,H=function(c){K(c),B==null||B(m?c:c[0])};(0,F.useEffect)(function(){K(typeof f=="string"?[f]:f||[])},[f]);var E=(0,s.jsxs)("div",{className:"add-button",onClick:C()(a()().mark(function j(){var c;return a()().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:if(!A){h.next=2;break}return h.abrupt("return");case 2:return h.next=4,N("");case 4:if(c=h.sent,c){h.next=7;break}return h.abrupt("return");case 7:if(!L.includes(c)){h.next=10;break}return I.Z.warn({message:(0,W.oz)("molecule-exist"),description:""}),h.abrupt("return");case 10:H(m?[].concat(i()(L),[c]):[c]);case 11:case"end":return h.stop()}},j)})),children:[(0,s.jsx)(x.Z,{}),(0,s.jsx)("div",{style:{marginTop:8},children:"Add"})]});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v.Z,{listType:"picture-card",disabled:A,className:"smiles-list ".concat(y==="reaction"?"reaction-list":""," ").concat(S||""," ").concat(m?"":L!=null&&L.length?"hide-upload-btn":""),multiple:m,maxCount:m?void 0:1,openFileDialogOnClick:!1,iconRender:function(c){return(0,s.jsx)(T.default,{structure:c.name,copyBtn:!1,expandBtn:!1})},onChange:function(c){var V=c.fileList;H(V.map(function(h){var J=h.name;return J}))},fileList:L.map(function(j){return{uid:j,name:j}}),showUploadList:{showDownloadIcon:!1,showPreviewIcon:!1,showRemoveIcon:!0},children:E}),(0,s.jsx)(g.Z,r()({},O))]})};p.Z=_},15415:function(z,p,e){var o=e(15009),r=e.n(o),d=e(99289),a=e.n(d),M=e(5574),i=e.n(M),D=e(32884),C=e(35607),P=e(31418),l=e(96486),Z=e.n(l),g=e(67294),T=function(I){var x=(0,g.useState)(),v=i()(x,2),F=v[0],s=v[1],_=(0,g.useState)(),R=i()(_,2),n=R[0],f=R[1],B=P.Z.useApp(),b=B.message,m=function(){var A=a()(r()().mark(function t(){var y,G,$,L,K,U,O,N,H,E,j,c,V,h,J,X,Y,Q=arguments;return r()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return y=Q.length>0&&Q[0]!==void 0?Q[0]:{},G=y.kekulize,$=G===void 0?!0:G,L=y.inchify,K=L===void 0?!1:L,U=y.validate,O=U===void 0?!0:U,N=y.forbidMultiple,H=N===void 0?!1:N,u.next=4,n==null?void 0:n.getSmiles();case 4:if(E=u.sent,O){u.next=7;break}return u.abrupt("return",E||!1);case 7:if(!((0,l.isNil)(E)||(0,l.isEmpty)(E))){u.next=10;break}return b.error((0,D.oz)("no-molecule-tip")),u.abrupt("return",!1);case 10:if(!(H&&E.includes("."))){u.next=13;break}return b.error("\u4E00\u6B21\u53EA\u80FD\u521B\u5EFA\u4E00\u4E2A\u5206\u5B50\uFF0C\u5982\u679C\u60A8\u60F3\u521B\u5EFA\u591A\u4E2A\u5206\u5B50\uFF0C\u8BF7\u5206\u591A\u6B21\u521B\u5EFA"),u.abrupt("return",!1);case 13:if(j=E,!$){u.next=21;break}return u.next=17,(0,C.G)([E]);case 17:c=u.sent,V=i()(c,1),h=V[0],j=h;case 21:if(!K){u.next=28;break}return u.next=24,(0,C.d)([j]);case 24:J=u.sent,X=i()(J,1),Y=X[0],j=Y;case 28:if(j.length){u.next=31;break}return b.error((0,D.oz)("valid-molecule-enter")),u.abrupt("return",!1);case 31:return u.abrupt("return",j);case 32:case"end":return u.stop()}},t)}));return function(){return A.apply(this,arguments)}}(),S=function(t){s({update:t||""})};return{ketcher:n,getSmiles:m,setSmiles:S,props:{getSmiles:m,setSmiles:S,onKetcherUpdate:f,updateEvent:F,initMoleculeSmiles:I}}};p.Z=T}}]);

//# sourceMappingURL=shared-Kkkw3EMq5n9zo-Q13EZrEtpMMp8_.6234ae16.async.js.map