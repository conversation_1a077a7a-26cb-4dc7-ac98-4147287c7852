{"version": 3, "file": "umi.c89497d5.css", "mappings": "AACA,WACE,YAAa,eACb,IAAK,MAAM,cAAc,CAAE,MAAM,mBAAmB,CAAE,MAAM,kBAAkB,CAA9E,iBAWF,CAPA,WACE,YAAa,gBACb,IAAK,MAAM,QAAQ,CAAE,MAAM,YAAY,CAAE,MAAM,WASjD,CANA,CAAC,cACC,MAAO,KACP,OAAQ,KACR,SAAU,OACV,OAAQ,IAAI,MAAM,QAhBpB,cAiBiB,GAQjB,CAbA,CAAC,cAAc,IAOX,MAAO,KACP,OAAQ,IASZ,CALA,EACE,WAAY,qBACZ,UAAW,MA1Bb,OA2BU,EA3BV,QA4BW,EACT,YAAa,yBAMb,gBAAiB,IAKnB,CAhBA,EAAE,IAaE,gBAAiB,iBACjB,gBAAiB,eACjB,gBAAiB,0BACjB,gBAAiB,YACjB,uBAAwB,gBAM5B,CAFA,KACE,MAAO,KACP,UAAW,IAIb,CADA,K,WAGE,OAAQ,KArDV,OAsDU,EAtDV,QAuDW,CAGX,EAAC,UACC,OAAQ,OAAO,IAEjB,CACA,CAAC,gBA9DD,cA+DiB,GACjB,CAEA,CAAC,2BACC,oBAAqB,eAAvB,CAGA,CAAC,gBACC,WAAY,QAvEd,cAwEiB,GADjB,CAIA,CAAC,WACC,WAAY,KAFd,CAGE,kBACE,iBAAkB,IADtB,CAKA,CAAC,cACC,MAAO,IACP,OAAQ,IAHV,CAMA,CAAC,yBACC,UAAW,KAAK,MAAM,EAAE,iBACxB,WAAY,gBACZ,WAAY,MAJd,CAOA,CAAC,kBACC,eAAgB,cALlB,CAQA,CAAC,aAAa,CApBZ,gBAoB6B,CAAC,oBAC9B,KAAM,MACN,iBAAkB,IANpB,CASA,CAAC,mBAtGD,QAuGW,IAAT,iBACA,SAAU,MAPZ,CAUA,OACE,QAAS,KARX,CAWA,KACE,MAAO,KAEP,OAAQ,MACR,WAAY,iBAGZ,WAAY,KACZ,WAAY,QACZ,eAAgB,mBAChB,uBAAwB,YACxB,wBAAyB,UACzB,sBAAuB,IAZzB,CAeA,G,GAEE,WAAY,IAbd,CAgBA,OAAO,CAAC,SAAS,EAAE,OACjB,CAAC,UACC,MAAO,KACP,WAAY,IAdd,CAeE,uB,wBAAA,e,8BAII,YAAa,GAbnB,CASE,4B,6BAAA,e,wCAMM,QAAS,KATjB,CACF,CAgBA,IAAI,iBACF,MAAO,IACP,OAAQ,IAvJV,cAwJiB,aAbjB,CAiBA,IAAI,oBACF,MAAO,eACP,OAAQ,eA9JV,cA+JiB,aAdjB,CAkBA,IAAI,0BACF,WAAY,eApKd,cAqKiB,aAfjB,CAmBA,IAAI,0BACF,WAAY,kBA1Kd,cA2KiB,aAhBjB,CAmBA,KA9KA,OA+KU,EA/KV,QAgLW,CAjBX,CAeA,KAAK,G,sNA9KL,OAwMY,EAxMZ,QAyMa,CAhBb,CAoBA,K,sDAWE,YAAa,GAlBf,CAqBA,G,sDAYE,UAAW,IAnBb,CAsBA,G,eA1OA,OAgPU,CApBV,CA4BA,M,yBAKE,QAAS,EAAE,IAtBb,CAyBA,CAAC,aACC,QAAS,IAvBX,CA0BA,CAAC,wBAHC,QAAS,KAKT,YAAa,MAxBf,CA2BA,CAAC,oBARC,QAAS,KAUT,gBAAiB,MAzBnB,CA4BA,CAAC,2BAbC,QAAS,KAeT,gBAAiB,UA1BnB,CA6BA,CAAC,wBAlBC,QAAS,KAoBT,gBAAiB,QA3BnB,CA8BA,CAAC,uBAvBC,QAAS,KAyBT,YAAa,UA5Bf,CA+BA,CAAC,2BA5BC,QAAS,KA8BT,gBAAiB,aA7BnB,CAgCA,CAAC,UACC,KAAM,EAAN,CA9BF,CAiCA,CAAC,yBACC,QAAS,KACT,UAAW,KACX,cAAe,aA/BjB,CAkCA,CAAC,YA3CC,QAAS,KA6CT,YAAa,OACb,gBAAiB,MAhCnB,CAmCA,CAAC,YACC,OAAQ,KAlDR,QAAS,KA6CT,YAAa,OACb,gBAAiB,MA1BnB,CAqCA,CAAC,YACC,MAAO,OACP,OAAQ,KA5TV,OA6TU,EAAE,IAlCZ,CAqCA,CAAC,aAEC,SAAU,OACV,IAAK,CAnCP,CAsCA,CAAC,kBACC,YAAa,cApCf,CAuCA,CAAC,WA1UD,OA2UU,KAAK,EA3Uf,QA4UW,KACT,iBAAkB,IArCpB,CAkCA,CAAC,WAAW,QAAZ,eA1UA,QAiVe,EAAE,IAtCjB,CA6CA,CAAC,iBAAiB,QAAlB,kBAxVA,cA4VqB,GA7CrB,CAyCA,CAAC,iBAAiB,QAAlB,mBAxVA,cA+VqB,GA7CrB,CAsCA,CAAC,iBAAiB,QAAlB,gBAxVA,QAkWe,KAAK,EAAI,YAClB,UAAW,KACX,YAAa,OACb,UAAW,UA7CjB,CAgCA,CAAC,iBAAiB,QAAlB,sCAxVA,OAwWc,KAAK,EACb,cAAe,IA7CrB,CAkDA,CAAC,aACC,QAAS,eACT,KAAM,EAAN,EACA,YAAa,qBACb,gBAAiB,kBAhDnB,CAmDA,CAAC,KACC,QAAS,cAjDX,CAoDA,CAAC,OACC,WAAY,MAlDd,CAsDA,CAAC,SACC,SAAU,OACV,YAAa,OACb,cAAe,SACf,WAAY,SAnDd,CAuDA,CAAC,kBACC,QAAS,YACT,SAAU,OACV,cAAe,SACf,mBAAoB,EACpB,mBAAoB,QApDtB,CAwDA,CAAC,cAAc,CAAf,oBAEI,WAAY,GAtDhB,CAoDA,CAAC,cAAc,CAAf,+CAIM,MAAO,IACP,OAAQ,IACR,iBAAkB,IArDxB,CA+CA,CAAC,cAAc,CAAf,uCA/YA,QAwZe,KACT,MAAO,eACP,UAAW,eACX,iBAAkB,eA3ZxB,cA4ZqB,GArDrB,CA2DA,CAAC,IACC,MAAO,GAxDT,CA2DA,CAAC,YACC,MAAO,QACP,YAAa,GAzDf,CA2DA,CAAC,cAKD,CAAC,aAAa,OAJZ,MAAO,QACP,OAAQ,iBAzDV,CAgEA,CAAC,kBACC,OAAQ,KApbV,QAqbW,YACT,WAAY,OA1Dd,CA8DA,CAAC,qBA1bD,QA2bW,WA3DX,CA0DA,CAAC,qBAAqB,CAAtB,0CA1bA,QA6ba,YACT,WAAY,MA1DhB,CA8DA,CAAC,gBAKD,CAAC,uBAJC,MAAO,eACP,WAAY,gBA5Dd,CA+DA,CAAC,uBAAuB,CAAxB,gBAvcA,QA0ca,KAAK,EAAE,KAAK,cA3DzB,CAwDA,CAAC,uBAAuB,CAAxB,yCAKM,QAAS,IA1Df,CAgEA,CAAC,SACC,MAAO,OA7DT,CA4DA,CAAC,SAAS,IAGN,KAAM,OA5DV,CAgEA,CAAC,cACC,SAAU,SACV,IAAK,QACL,KAAM,OA9DR,CAiEA,CAAC,kBACC,SAAU,SACV,MAAO,EACP,QAAS,KACT,gBAAiB,IACjB,cAAe,IA/DjB,CA0DA,CAAC,kBAAkB,OA9NjB,QAAS,KAKT,YAAa,MAmKf,CAsDA,CAAC,kBAAkB,MAAnB,WAWM,KAAM,OA9DZ,CAmDA,CAAC,kBAAkB,MAAnB,gBAaQ,OAAQ,OA7DhB,CAmEA,CAAC,SACC,WAAY,KAnfd,cAofiB,GAjEjB,CAoEA,CAAC,kBACC,WAAY,KAAZ,aAlEF,CAqEA,CAAC,YACC,MAAO,eACP,OAAQ,qBAnEV,CAsEA,CAAC,sBACC,QAAS,EApEX,CAuEA,CAAC,gBACC,MAAO,MACP,UAAW,MACX,OAAQ,KACR,OAAQ,IAAI,MAAM,OArEpB,CAwEA,CAAC,WANC,MAAO,MACP,UAAW,MACX,OAAQ,KACR,OAAQ,IAAI,MAAM,QAKlB,OAAQ,eACR,WAAY,IAnEd,CAsEA,CAAC,IACC,WAAY,GApEd,CAuEA,CAAC,KACC,WAAY,IArEd,CAwEA,CAAC,KACC,WAAY,IAtEd,CAyEA,CAAC,KACC,cAAe,IAvEjB,CA0EA,CAAC,KACC,cAAe,IAxEjB,CA2EA,CAAC,SACC,YAAa,IACb,UAAW,KACX,YAAa,KACb,eAAgB,GAzElB,CA4EA,CAAC,cAAc,CAAf,cA5iBA,QA8iBa,CA3Eb,CAgFA,CAAC,oBAAoB,CAAC,e,oDAKtB,CAAC,a,YAHC,QAAS,cA7EX,CCveA,CAAC,UACC,eAAgB,IAClB,CAEA,CAJC,UAIU,CAAC,IACV,WAAY,KAEZ,SAAU,MACV,QAAS,KACT,IAAK,EACL,KAAM,EAEN,MAAO,KACP,OAAQ,GACV,CAGA,CAjBC,UAiBU,CAAC,IACV,QAAS,MACT,SAAU,SACV,MAAO,EACP,MAAO,MACP,OAAQ,KACR,WAAY,EAAE,EAAE,KAAK,IAAI,CAAE,EAAE,EAAE,IAAI,KACnC,QAAS,EAID,UAAW,OAAO,MAAM,WAAe,KACjD,CAGA,CAhCC,UAgCU,CAAC,QACV,QAAS,MACT,SAAU,MACV,QAAS,KACT,IAAK,KACL,MAAO,IACT,CAEA,CAxCC,UAwCU,CAAC,aACV,MAAO,KACP,OAAQ,KACR,WAAY,WAEZ,OAAQ,MAAM,IAAI,YAClB,iBAAkB,KAClB,kBAAmB,KDhDrB,cCiDiB,IAGP,UAAW,kBAAkB,IAAM,OAAO,QACpD,CAEA,CAAC,wBACC,SAAU,OACV,SAAU,QACZ,CAEA,CALC,wBAKwB,CA3DxB,UA2DmC,CA3BxB,Q,CAsBX,wB,CAtDA,U,CAIW,IAyDV,SAAU,QACZ,CAMA,WAjBqB,kBAkBnB,GAAO,UAAW,OAAO,EAAO,CAChC,GAAO,UAAW,OAAO,OAAS,CACpC,CCtEA,CAAC,qBACC,MAAO,QACP,UAAW,IAUb,CAZA,CAAC,qBAAD,gBAII,MAAO,KACP,OAAQ,KACR,WAAY,MAWhB,CAjBA,CAAC,qBAAD,kBAQM,MAAO,QACP,WAAY,IAAI,GAYtB,CAXM,CAVL,qBAUK,CAVN,eAUM,QACE,MAAO,OAaf,CC1BA,CAAC,mBACC,WAAY,MACd,CAFA,CAAC,mBAAD,KAGI,MAAO,UACP,UAAW,IAEf,CCLA,CAAC,gBAAD,IAEI,MAAO,KACP,OAAQ,IAUZ,CANA,CAAC,yBAAD,IAEI,MAAO,KACP,OAAQ,IAOZ,CAHA,CAdC,eAcD,O,CAPC,wB,OASC,OAAQ,OAKV,CAFA,CAAC,+BACC,MAAO,MACP,QAAS,CAIX,CANA,CAAC,+BAAD,uBAII,QAAS,KACT,YAAa,MAKjB,CAVA,CAAC,+BAAD,sCJpBA,OI2Bc,CAMd,CAbA,CAAC,+BAAD,+BAWI,YAAa,IAKjB,CAhBA,CAAC,+BAAD,iCAeM,OAAQ,IAId,CAnBA,CAAC,+BAAD,0DAiBQ,QAAS,KACT,eAAgB,OAChB,OAAQ,KACR,YAAa,IAKrB,CAzBA,CAAC,+BAAD,uFAsBU,YAAa,IAMvB,CA5BA,CAAC,+BAAD,0FAyBU,WAAY,IAMtB,CA/BA,CAAC,+BAAD,iBJpBA,OImDY,IAAI,CAGhB,CAlCA,CAAC,+BAAD,2BAkCI,WAAY,KACZ,WAAY,IAGhB,CAtCA,CAAC,+BAAD,CJyEC,kBA7FD,QI4De,KAAK,CACpB,CAzCA,CAAC,+BAAD,CJyEC,kBIzED,0BA0CQ,cAAe,CAEvB,CA5CA,CAAC,+BAAD,CJyEC,kBIzED,yBA6CQ,eAAgB,GAExB,CA/CA,CAAC,+BAAD,mBAmDI,QAAS,KACT,YAAa,OACb,MAAO,YACP,OAAQ,KJ1EZ,QI2Ea,EAAE,IACX,UAAW,IADf,CAII,CA3DH,+BA2DG,6B,CA3DH,+B,2BA6DK,MAAO,QACP,iBAAkB,QAClB,OAAQ,IAAI,MAAM,OAFxB,CAII,CAjEH,+BAiEG,2BACE,MAAO,QACP,OAAQ,IAAI,MAAM,OAFxB,CAII,CArEH,+BAqEG,2BAIA,CAzEH,+BAyEG,2BAHE,MAAO,QACP,OAAQ,IAAI,MAAM,OAFxB,CAQI,CA7EH,+BA6EG,0BACE,MAAO,QACP,OAAQ,IAAI,MAAM,OAFxB,CC/FA,CAAC,qBACC,QAAS,KACT,YAAa,OACb,gBAAiB,IACjB,aAAc,ILNhB,cKOiB,GAUjB,CATE,CAND,oBAMC,mBACE,SAAU,SACV,OAAQ,KACR,MAAO,KACP,OAAQ,KACR,cAAe,IAWnB,CATI,CAbH,oBAaG,CAPF,iBAOE,OACE,MAAO,QACP,iBAAkB,SAWxB,CApBE,CAND,oBAMC,uCAaI,MAAO,KACP,OAAQ,IAUd,CA9BA,CAAC,qBAAD,CAME,mBAmBE,SAAU,SACV,MAAO,KACP,OAAQ,KL7BZ,OK8BY,IACR,SAAU,OACV,OAAQ,OAQZ,CAtCA,CAAC,qBAAD,CAME,mBANF,KAiCM,YAAa,KACb,YAAa,yBAQnB,CA1CA,CAAC,qBAAD,CAME,mBANF,IAsCM,SAAU,SACV,IAAK,EACL,KAAM,EACN,MAAO,KACP,OAAQ,IAOd,CClDA,CAAC,kBACC,MAAO,KACP,cAAe,KACf,OAAQ,OAWV,CAdA,CAAC,kBAAD,IAKI,MAAO,KACP,OAAQ,KACR,KAAM,OAYV,CAnBA,CAAC,kBAAD,wBAUI,QAAS,KACT,YAAa,MAYjB,CAvBA,CAAC,kBAAD,4CAaM,SAAU,SACV,MAAO,GAab,CA3BA,CAAC,kBAAD,2CAkBQ,YAAa,KACb,YAAa,yBAYrB,CA/BA,CAAC,kBAAD,8CAwBQ,SAAU,SACV,KAAM,KACN,MAAO,IAUf,CApCA,CAAC,kBAAD,oDA+BQ,KAAM,OAQd,CAFA,CArCA,YAqCA,OACE,MAAO,QACP,OAAQ,OAIV,CANA,CArCA,YAqCA,WAII,MAAO,KACP,KAAM,OAKV,CAFA,CAAC,wBACC,QAAS,KACT,YAAa,OACb,gBAAiB,OACjB,MAAO,KACP,OAAQ,IAIV,CADA,CAAC,iBACC,SAAU,SACV,OAAQ,KACR,eAAgB,eAChB,UAAW,KACX,WAAY,UAAU,IAAK,YAC3B,cAAe,EACf,eAAgB,GAGlB,CAVA,CAAC,iBAAD,IASI,SAAU,SACV,KAAM,GAIV,CAdA,CAAC,iBAAD,CArDA,iBA8CE,QAAS,KACT,YAAa,OACb,gBAAiB,OACjB,MAAO,KACP,OAAQ,KAiBN,YAAa,ENpEjB,cMqEmB,GAQnB,CAvBA,CAAC,iBAAD,CArDA,iBAqDA,IAiBM,SAAU,SACV,KAAM,EACN,MAAO,IASb,CA5BA,CAAC,iBAAD,CArDA,gBAqDA,O,CAAC,iB,CArDD,Y,OA6EI,iBAAkB,SAQtB,CAhCA,CAAC,iBAAD,CArDA,gBAqDA,W,CAAC,iB,CArDD,Y,WA+EM,KAAM,OAUZ,CApCA,CAAC,iBAAD,CArDA,aAmFI,WAAY,IACZ,cAAe,KACf,YAAa,ENtFjB,cMuFmB,IAxCjB,QAAS,KACT,YAAa,OACb,gBAAiB,OACjB,MAAO,KACP,OAAQ,IAkDV,CA/CA,CAAC,iBAAD,CArDA,aAqDA,IAoCM,SAAU,SACV,KAAM,EACN,MAAO,IAcb,CATA,qBAEI,SAAU,SACV,OAAQ,KACR,QAAS,GACT,MAAO,KAAK,KAAK,EAAE,gBACnB,YAAa,gBACb,cAAe,aAUnB,CCjHA,CAAC,qBACC,MAAO,KACP,OAAQ,KACR,cAAe,KACf,OAAQ,OAWV,CAfA,CAAC,qBAAD,IAMI,MAAO,KACP,OAAQ,KACR,KAAM,OAYV,CApBA,CAAC,qBAAD,+CAaQ,YAAa,KACb,YAAa,yBAUrB,CAxBA,CAAC,qBAAD,gDAkBM,MAAO,QACP,OAAQ,OASd,CA5BA,CAAC,qBAAD,oDAqBQ,MAAO,KACP,KAAM,OAUd,CAJA,CAAC,wBACC,QAAS,KACT,YAAa,OACb,gBAAiB,OACjB,MAAO,KACP,OAAQ,IAMV,CAHA,CAAC,iBACC,SAAU,SACV,OAAQ,KACR,eAAgB,eAChB,cAAe,KACf,UAAW,KACX,WAAY,UAAU,IAAK,YAC3B,cAAe,EACf,eAAgB,GAKlB,CAbA,CAAC,iBAAD,IAUI,SAAU,SACV,KAAM,GAMV,CAjBA,CAAC,iBAAD,CApCA,iBAkDI,WAAY,IACZ,YAAa,EPpDjB,cOqDmB,IAvBjB,QAAS,KACT,YAAa,OACb,gBAAiB,OACjB,MAAO,KACP,OAAQ,IA8BV,CA3BA,CAAC,iBAAD,CApCA,iBAoCA,IAmBM,SAAU,SACV,KAAM,EACN,MAAO,IAWb,CAhCA,CAAC,iBAAD,CApCA,gBAoCA,OAyBI,iBAAkB,SAUtB,CAnCA,CAAC,iBAAD,CApCA,gBAoCA,WA2BM,KAAM,OAWZ,CC3EA,CAAC,wB,2CACC,SAAU,MACV,IAAK,EACL,KAAM,EACN,MAAO,KACP,OAAQ,QACR,SAAU,OACV,eAAgB,IAGlB,EAAC,2B,4FACC,WAAY,KACZ,UAAW,KAAK,MAIlB,CADA,CAAC,wB,iFACC,QAAS,KACT,gBAAiB,cRjBnB,QQkBW,KAAK,KACd,UAAW,OAAO,OAKpB,CAFA,CAAC,mB,mHACC,UAAW,KACX,YAAa,IAOf,CAJA,CAAC,YACC,MAAO,KACP,YAAa,GAMf,CAFA,C,uBAAA,C,0BAGI,YAAa,MAGjB,CANA,C,uBAAA,C,kBAAA,KAQM,MAAO,KACP,QAAS,GACf,CAKA,C,kBAAA,C,qBAGI,YAAa,MAJjB,CACA,C,kBAAA,C,kBAAA,KAOM,MAAO,KACP,QAAS,EALf,CAUA,CAAC,uBACC,IAAK,EACL,KAAM,KACN,MAAO,IART,CAWA,CAAC,2BACC,YAAa,GATf,CAYA,CAAC,gCACC,YAAa,GAVf,CAaA,C,kBR3EA,QQ4EW,KAAK,WAXhB,CAUA,C,kBAAA,KR3EA,OQ8EY,eACR,MAAO,eACP,QAAS,YAVb,CCrEA,K,KAEE,MAAO,KACP,OAAQ,IACV,CACA,KAAK,Y,kBAEH,QAAS,IACX,CACA,E,iBAGE,WAAY,UACd,CACA,KACE,YAAa,WACb,YAAa,KACb,yBAA0B,KAC1B,qBAAsB,KACtB,mBAAoB,UACpB,4BAA6B,KAAK,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,EAC7C,CAIA,KT1BA,OS2BU,CACV,CACA,CAAC,cAAc,OACb,QAAS,IACX,CACA,GACE,WAAY,YACZ,OAAQ,EACR,SAAU,OACZ,CACA,G,eAME,WAAY,EACZ,cAAe,KACf,YAAa,GACf,CACA,EACE,WAAY,EACZ,cAAe,GACjB,CACA,IAAI,CAAC,O,0BAGH,gBAAiB,UACjB,gBAAiB,UAAU,OAC3B,cAAe,EACf,OAAQ,IACV,CACA,QACE,cAAe,IACf,WAAY,OACZ,YAAa,OACf,CACA,KAAK,CAAC,W,iDAIJ,mBAAoB,IACtB,CACA,G,MAGE,WAAY,EACZ,cAAe,GACjB,CACA,GAAG,G,kBAID,cAAe,CACjB,CACA,GACE,YAAa,GACf,CACA,GACE,cAAe,KACf,YAAa,CACf,CACA,WTzFA,OS0FU,EAAE,EAAE,GACd,CACA,IACE,WAAY,MACd,CACA,E,OAEE,YAAa,MACf,CACA,MACE,UAAW,GACb,CACA,I,IAEE,SAAU,SACV,UAAW,IACX,YAAa,EACb,eAAgB,QAClB,CACA,IACE,OAAQ,MACV,CACA,IACE,IAAK,KACP,CACA,I,cAIE,UAAW,IACX,YAAa,cAAgB,CAAE,QAAQ,CAAE,eAAiB,CAAE,KAAK,CAAE,OAAO,CAAE,SAC9E,CACA,IACE,WAAY,EACZ,cAAe,IACf,SAAU,IACZ,CACA,OT/HA,OSgIU,EAAE,EAAE,GACd,CACA,IACE,eAAgB,OAChB,aAAc,IAChB,CACA,E,gFASE,aAAc,YAChB,CACA,MACE,gBAAiB,QACnB,CACA,QACE,YAAa,MACb,eAAgB,KAChB,WAAY,KACZ,aAAc,MAChB,CACA,M,gCT1JA,OS+JU,EACR,MAAO,QACP,UAAW,QACX,YAAa,QACb,YAAa,OACf,CACA,O,MAEE,SAAU,OACZ,CACA,O,OAEE,eAAgB,IAClB,CACA,O,8CAIE,mBAAoB,MACtB,CACA,MAAM,mB,+FTnLN,QSuLW,EACT,aAAc,IAChB,CACA,KAAK,CAAC,Y,qBAEJ,WAAY,WT5Ld,QS6LW,CACX,CACA,KAAK,CAAC,W,8DAIJ,mBAAoB,OACtB,CACA,SACE,SAAU,KACV,OAAQ,QACV,CACA,SACE,UAAW,ET1Mb,OS2MU,ET3MV,QS4MW,EACT,OAAQ,CACV,CACA,OACE,QAAS,MACT,MAAO,KACP,UAAW,KACX,cAAe,KTnNjB,QSoNW,EACT,MAAO,QACP,UAAW,MACX,YAAa,QACb,YAAa,MACf,CACA,SACE,eAAgB,QAClB,CACA,CAAC,YAAc,4B,yCAEb,OAAQ,IACV,CACA,CAAC,aACC,eAAgB,KAChB,mBAAoB,IACtB,CACA,CAAC,YAAc,+B,yCAEb,mBAAoB,IACtB,CACA,6BACE,KAAM,QACN,mBAAoB,MACtB,CACA,OACE,QAAS,YACX,CACA,QACE,QAAS,SACX,CACA,SACE,QAAS,IACX,CACA,CAAC,QACC,QAAS,cACX,CACA,KTzPA,QS0PW,KACT,iBAAkB,OACpB", "sources": ["webpack://labwise-web/./src/global.less", "webpack://labwise-web/./node_modules/nprogress/nprogress.css", "webpack://labwise-web/./src/components/GlobalFooter/index.less", "webpack://labwise-web/./src/components/LoadingTip/index.less", "webpack://labwise-web/./src/components/Notification/index.less", "webpack://labwise-web/./src/components/LabBot/index.less", "webpack://labwise-web/./src/components/UserGuide/index.less", "webpack://labwise-web/./src/components/UserSettings/index.less", "webpack://labwise-web/./src/components/WaterMark/index.less", "webpack://labwise-web/./node_modules/antd/dist/reset.css"], "sourcesContent": ["@import '@/style/variables.less';\n@font-face {\n  font-family: 'PinFang-Medium';\n  src: local('PingFang SC'), local('Hiragino Sans GB'), local('Microsoft Yahei'),\n    local(sans-serif);\n}\n\n@font-face {\n  font-family: 'NotoSans-Medium';\n  src: local('Arial'), local('Helvetica'), local(sans-serif);\n}\n\n.labWiseAvatar {\n  width: 25px;\n  height: 25px;\n  overflow: hidden;\n  border: 1px solid @color-design-a;\n  border-radius: 50%;\n  img {\n    width: 25px;\n    height: 25px;\n  }\n}\n\n* {\n  box-sizing: border-box !important;\n  max-width: 100vw;\n  margin: 0;\n  padding: 0;\n  font-family: 'PinFang-Medium' !important;\n  /* font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,\n    'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji',\n    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji' !important; */\n  // outline-color: #32c5ff !important;\n  // scrollbar-color: #66ccff rgb(239, 239, 239);\n  scrollbar-width: thin;\n  img {\n    image-rendering: -moz-crisp-edges;\n    image-rendering: -o-crisp-edges;\n    image-rendering: -webkit-optimize-contrast;\n    image-rendering: crisp-edges;\n    -ms-interpolation-mode: nearest-neighbor;\n  }\n}\n\nhtml {\n  color: #000;\n  font-size: 12px;\n}\n\nhtml,\nbody,\n#root {\n  height: 100%;\n  margin: 0;\n  padding: 0;\n}\n\n.colorWeak {\n  filter: invert(80%);\n}\n\n.ant-btn-default {\n  border-radius: 2px;\n}\n\n.ant-page-header-breadcrumb {\n  padding-block-start: unset !important;\n}\n\n.ant-btn-primary {\n  background: @fill-bg-primary-button;\n  border-radius: 2px;\n}\n\n.ant-layout {\n  min-height: 100vh;\n  &-sider {\n    background-color: #fff;\n  }\n}\n\n.ant-float-btn {\n  right: 5px;\n  bottom: 10px;\n}\n\n.ant-pro-layout-container {\n  min-width: calc(100vw - @siderWidth) !important;\n  min-height: 100vh !important;\n  overflow-x: hidden;\n}\n\n.ant-pro-card-body {\n  padding-inline: 12px !important;\n}\n\n.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {\n  left: unset;\n  background-color: #fff;\n}\n\n.ant-layout-content {\n  padding: @main-layout-padding_top @layout-horizontal-padding 0 !important;\n  overflow: hidden;\n}\n\ncanvas {\n  display: block;\n}\n\nbody {\n  width: 100%;\n  // user-select: none; // 禁止复制网页文本\n  height: 100vh;\n  overflow-x: hidden !important;\n  // min-width: 1366px !important;\n  // overflow-x: scroll !important;\n  overflow-y: auto;\n  overflow-y: overlay;\n  text-rendering: optimizeLegibility;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  overscroll-behavior-y: none;\n}\n\nul,\nol {\n  list-style: none;\n}\n\n@media (max-width: 768px) {\n  .ant-table {\n    width: 100%;\n    overflow-x: auto;\n    &-thead > tr,\n    &-tbody > tr {\n      > th,\n      > td {\n        white-space: pre;\n        > span {\n          display: block;\n        }\n      }\n    }\n  }\n}\n\n/* 自定义滚动条样式 */\nbody::-moz-scrollbar {\n  width: 6px;\n  height: 6px;\n  border-radius: 5px !important;\n}\n\n/* 滚动条宽 */\nbody::-webkit-scrollbar {\n  width: @scrollbar-width !important;\n  height: 10px !important;\n  border-radius: 3px !important;\n}\n\n/* 滚动条 拖动条 */\nbody::-webkit-scrollbar-thumb {\n  background: #66ccff !important;\n  border-radius: 6px !important;\n}\n\n/* 滚动条背景槽 */\nbody::-webkit-scrollbar-track {\n  background: rgb(239, 239, 239) !important;\n  border-radius: 3px !important;\n}\n\nbody {\n  margin: 0;\n  padding: 0;\n  ul,\n  ol,\n  dl,\n  dd,\n  h1,\n  h2,\n  h3,\n  h4,\n  h5,\n  h6,\n  figure,\n  form,\n  fieldset,\n  legend,\n  input,\n  textarea,\n  button,\n  p,\n  blockquote,\n  th,\n  td,\n  pre,\n  xmp {\n    margin: 0;\n    padding: 0;\n  }\n}\n\nbody,\ninput,\ntextarea,\nbutton,\nselect,\npre,\nxmp,\ntt,\ncode,\nkbd,\nsamp {\n  line-height: 1.5;\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nsmall,\nbig,\ninput,\ntextarea,\nbutton,\nselect {\n  font-size: 100%;\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  margin: 0;\n}\n\ntextarea {\n  overflow: auto;\n  resize: vertical;\n}\n\ninput,\ntextarea,\nbutton,\nselect,\na {\n  outline: 0 none;\n}\n\n.display-flex {\n  display: flex;\n}\n\n.flex-align-items-center {\n  .display-flex;\n  align-items: center;\n}\n\n.display-flex-center {\n  .display-flex;\n  justify-content: center;\n}\n\n.flex-justify-content-start {\n  .display-flex;\n  justify-content: flex-start;\n}\n\n.flex-justfy-content-end {\n  .display-flex;\n  justify-content: flex-end;\n}\n\n.flex-align-items-start {\n  .display-flex;\n  align-items: flex-start;\n}\n\n.flex-justify-space-between {\n  .display-flex;\n  justify-content: space-between;\n}\n\n.flex-auto {\n  flex: 1;\n}\n\n.flex-align-space-between {\n  display: flex;\n  flex-wrap: wrap;\n  align-content: space-between;\n}\n\n.flex-center {\n  .display-flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.loadingPage {\n  height: 100%;\n  .flex-center;\n}\n\n// -------------\n\n/* 1400宽页面 */\n.mainContent {\n  width: 1400px;\n  height: auto;\n  margin: 0 auto;\n}\n\n.stickyHeader {\n  position: -webkit-sticky;\n  position: sticky;\n  top: 0px;\n}\n\n.enabledUserSelect {\n  user-select: text !important;\n}\n\n.searchForm {\n  margin: 10px 0;\n  padding: 15px 15px;\n  background-color: #fff;\n\n  :global {\n    .ant-form-item {\n      padding: 0 10px;\n    }\n  }\n}\n\n/* Custom Style */\n// .ant-modal-content antd面板内容零边距\n.paddingZeroModal {\n  // user-select: none !important;\n  :global {\n    .ant-modal-header {\n      border-radius: 8px;\n    }\n    .ant-modal-content {\n      border-radius: 9px;\n    }\n    .ant-modal-body {\n      padding: 20px 0px 0px 0px !important;\n      font-size: 14px;\n      line-height: 1.5715;\n      word-wrap: break-word;\n    }\n    .ant-table-pagination.ant-paginatgion {\n      margin: 16px 0px;\n      padding-right: 16px;\n    }\n  }\n}\n\n.query-button {\n  display: flex !important;\n  flex: 1;\n  align-items: flex-start !important;\n  justify-content: flex-end !important;\n}\n\n.none {\n  display: none !important;\n}\n\n.hidden {\n  visibility: hidden;\n}\n\n/* 超长缩略隐藏 */\n.ellipsis {\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  word-break: break-all;\n}\n\n/* 多行省略号(2行) */\n.multi-ellipsis-l2 {\n  display: -webkit-box;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n/* tooltip样式 */\n.globa1Too1Tip {\n  .ant-tooltip-content {\n    margin-top: 2px;\n    .ant-tooltip-arrow-content {\n      width: 9px;\n      height: 9px;\n      background-color: white;\n    }\n    .ant-tooltip-inner {\n      padding: 10px;\n      color: rgba(102, 102, 102, 1) !important;\n      font-size: 12px !important;\n      background-color: #fff !important;\n      border-radius: 6px;\n    }\n  }\n}\n\n/* 红色字体 */\n.red {\n  color: red;\n}\n\n.importColor {\n  color: #29b7b7;\n  font-weight: bold;\n}\n.enablePointer {\n  color: @fill-cursor;\n  cursor: pointer !important;\n}\n\n.cursorPointer:hover {\n  .enablePointer;\n}\n\n.ant-layout-footer {\n  height: @footer-height;\n  padding: 0 !important;\n  background: #f5f5f5;\n}\n\n/* custom layout style */\n.ant-pro-grid-content {\n  padding: 0px !important;\n  .ant-pro-page-container-children-container {\n    padding: 0px !important;\n    overflow-x: hidden;\n  }\n}\n\n.commonContainer {\n  width: 100% !important;\n  overflow-x: hidden !important;\n}\n\n.ant-pro-page-container {\n  .commonContainer;\n  .ant-page-header {\n    padding: 10px 0 10px 10px !important;\n    .ant-page-header-heading {\n      display: none; // 页面title不展示，由面包屑替代\n    }\n  }\n}\n\n/* 已操作/已阅 */\n.complete {\n  color: @fill-purple;\n  svg {\n    fill: @fill-purple;\n  }\n}\n\n.hiddenElement {\n  position: absolute;\n  top: -9999px;\n  left: -9999px;\n}\n\n.layoutRighButtons {\n  position: absolute;\n  right: 0px;\n  display: flex;\n  justify-content: end;\n  margin-bottom: 10px;\n  button {\n    .flex-align-items-center;\n  }\n  button:hover {\n    svg {\n      fill: @color-design-e;\n      path {\n        stroke: @color-design-e;\n      }\n    }\n  }\n}\n\n.ant-form {\n  background: #fff;\n  border-radius: 8px;\n}\n\n.fullLayoutContent {\n  min-height: @main-content-height_hasCrumbs;\n}\n\n.disabledTip {\n  color: @fill-disabled !important;\n  cursor: not-allowed !important;\n}\n\n.ant-pro-sider-actions {\n  z-index: @sider-extra-ZIndex;\n}\n\n.commonStructure {\n  width: 160px;\n  max-width: 240px;\n  height: auto;\n  border: 1px solid @border-color-base;\n}\n\n.smilesItem {\n  .commonStructure;\n  height: 68px !important;\n  max-height: 68px;\n}\n\n.mt8 {\n  margin-top: 8px;\n}\n\n.mt10 {\n  margin-top: 10px;\n}\n\n.mt20 {\n  margin-top: 20px;\n}\n\n.mb16 {\n  margin-bottom: 16px;\n}\n\n.mb10 {\n  margin-bottom: 10px;\n}\n\n.subTitle {\n  font-weight: 650;\n  font-size: 16px;\n  line-height: 20px;\n  letter-spacing: 0em;\n}\n\n.noPaddingCard {\n  .ant-card-body {\n    padding: 0px;\n  }\n}\n\n/* custom Plyr video style */\n.plyr--pip-supported [data-plyr='pip'],\n.plyr--full-ui.plyr--video .plyr__control--overlaid {\n  display: none !important;\n}\n\n.plyr__volume,\n.plyr__menu {\n  display: none !important;\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", "/* Make clicks pass-through */\n#nprogress {\n  pointer-events: none;\n}\n\n#nprogress .bar {\n  background: #29d;\n\n  position: fixed;\n  z-index: 1031;\n  top: 0;\n  left: 0;\n\n  width: 100%;\n  height: 2px;\n}\n\n/* Fancy blur effect */\n#nprogress .peg {\n  display: block;\n  position: absolute;\n  right: 0px;\n  width: 100px;\n  height: 100%;\n  box-shadow: 0 0 10px #29d, 0 0 5px #29d;\n  opacity: 1.0;\n\n  -webkit-transform: rotate(3deg) translate(0px, -4px);\n      -ms-transform: rotate(3deg) translate(0px, -4px);\n          transform: rotate(3deg) translate(0px, -4px);\n}\n\n/* Remove these to get rid of the spinner */\n#nprogress .spinner {\n  display: block;\n  position: fixed;\n  z-index: 1031;\n  top: 15px;\n  right: 15px;\n}\n\n#nprogress .spinner-icon {\n  width: 18px;\n  height: 18px;\n  box-sizing: border-box;\n\n  border: solid 2px transparent;\n  border-top-color: #29d;\n  border-left-color: #29d;\n  border-radius: 50%;\n\n  -webkit-animation: nprogress-spinner 400ms linear infinite;\n          animation: nprogress-spinner 400ms linear infinite;\n}\n\n.nprogress-custom-parent {\n  overflow: hidden;\n  position: relative;\n}\n\n.nprogress-custom-parent #nprogress .spinner,\n.nprogress-custom-parent #nprogress .bar {\n  position: absolute;\n}\n\n@-webkit-keyframes nprogress-spinner {\n  0%   { -webkit-transform: rotate(0deg); }\n  100% { -webkit-transform: rotate(360deg); }\n}\n@keyframes nprogress-spinner {\n  0%   { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n", "@import '@/style/variables.less';\n\n.globalFooter {\n  color: @color-footer-text;\n  font-size: @font-size-base;\n  .footer {\n    width: 100%;\n    height: @footer-height;\n    text-align: center;\n    a {\n      color: @color-footer-text;\n      transition: all 0.3s;\n      &:hover {\n        color: @brand-primary;\n      }\n    }\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", ".loadingCom {\n  text-align: center;\n  span {\n    color: rgba(0, 0, 0, 0.88);\n    font-size: 23px;\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", "@import '@/style/variables.less';\n.tipIcon {\n  svg {\n    width: 22px;\n    height: auto;\n  }\n}\n\n.collapsedTipIcon {\n  svg {\n    width: 20px;\n    height: auto;\n  }\n}\n\n.tipIcon:hover,\n.collapsedTipIcon:hover {\n  cursor: pointer;\n}\n\n.notification-list-root {\n  width: 400px;\n  padding: 4;\n  .title-wrapper {\n    display: flex;\n    align-items: center;\n    .title {\n      margin: 0;\n    }\n  }\n  .title-actions-wrapper {\n    margin-left: auto;\n  }\n  .ant-list-item-extra {\n    > div {\n      height: 100%;\n      .actions-wrapper {\n        display: flex;\n        flex-direction: column;\n        height: 100%;\n        margin-left: auto;\n        .top-actions-wrapper {\n          margin-left: auto;\n        }\n        .bottom-actions-wrapper {\n          margin-top: auto;\n        }\n      }\n    }\n  }\n  .divider {\n    margin: 4px 0;\n  }\n  .notification-list {\n    max-height: 70vh;\n    overflow-y: auto;\n  }\n\n  :global {\n    .ant-pro-card-body {\n      padding: 16px 0;\n      .ant-pro-list-row-content {\n        margin-inline: 0;\n      }\n      td.ant-descriptions-item {\n        padding-bottom: 4px;\n      }\n    }\n  }\n\n  .commonTag {\n    display: flex;\n    align-items: center;\n    width: max-content;\n    height: 24px;\n    padding: 0 4px;\n    font-size: 12px;\n  }\n  .statusDes {\n    &_completed,\n    &_success {\n      color: @color-completed;\n      background-color: #ecf8ef;\n      border: 1px solid @color-completed;\n    }\n    &_running {\n      color: @color-running;\n      border: 1px solid @color-running;\n    }\n    &_limited {\n      color: @color-pending;\n      border: 1px solid @color-pending;\n    }\n    &_pending {\n      color: @color-pending;\n      border: 1px solid @color-pending;\n    }\n    &_failed {\n      color: @color-failed;\n      border: 1px solid @color-failed;\n    }\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", "@import '@/style/variables.less';\n\n.labbotButton {\n  display: flex;\n  align-items: center;\n  justify-content: end;\n  margin-right: 8px;\n  border-radius: 8px;\n  &.collapsed {\n    position: relative;\n    bottom: 38px;\n    width: 32px;\n    height: 32px;\n    margin-bottom: 10px;\n\n    &:hover {\n      color: @brand-primary;\n      background-color: rgba(0, 0, 0, 0.06);\n    }\n\n    .svgWrapper {\n      width: 100%;\n      height: 100%;\n    }\n  }\n\n  .svgWrapper {\n    position: relative;\n    width: 42px;\n    height: 42px;\n    margin: 2px;\n    overflow: hidden;\n    cursor: pointer;\n\n    span {\n      margin-left: 10px;\n      font-family: 'NotoSans-Medium' !important;\n    }\n\n    svg {\n      position: absolute;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n    }\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", "@import '@/style/variables.less';\n.userGuide {\n  width: 100%;\n  padding-right: 10px;\n  cursor: pointer;\n  svg {\n    width: 24px;\n    height: 24px;\n    fill: @color-text-a;\n  }\n  .operateContent {\n    display: flex;\n    align-items: center;\n    .langSwitch {\n      position: relative;\n      right: 4px;\n    }\n    .help {\n      span {\n        margin-left: 10px;\n        font-family: 'NotoSans-Medium' !important;\n      }\n    }\n    .langIcon {\n      svg {\n        position: relative;\n        left: 11px;\n        width: 18px;\n      }\n    }\n    .langIcon:hover {\n      svg {\n        fill: @brand-primary;\n      }\n    }\n  }\n}\n\n.help:hover {\n  color: @brand-primary;\n  cursor: pointer;\n  svg {\n    width: 24px;\n    fill: @brand-primary;\n  }\n}\n.commonIconStyle {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32px;\n  height: 32px;\n}\n\n.foldType {\n  position: relative;\n  bottom: 38px;\n  flex-direction: column-reverse;\n  font-size: 16px;\n  transition: font-size 0.3s ease-in-out;\n  padding-block: 0;\n  padding-inline: 0px;\n  svg {\n    position: relative;\n    left: 3px;\n  }\n  .langIcon {\n    .commonIconStyle;\n    margin-left: 0px;\n    border-radius: 8px;\n    svg {\n      position: relative;\n      left: 0px;\n      width: 22px;\n    }\n  }\n  .langIcon:hover,\n  .help:hover {\n    background-color: rgba(0, 0, 0, 0.06);\n    svg {\n      fill: @brand-primary;\n    }\n  }\n  .help {\n    margin-top: 5px;\n    margin-bottom: 15px;\n    margin-left: 0px;\n    border-radius: 8px;\n    .commonIconStyle;\n    svg {\n      position: relative;\n      left: 0px;\n      width: 22px;\n    }\n  }\n}\n\n:global {\n  .ant-pro-sider-extra {\n    position: absolute;\n    bottom: 45px;\n    z-index: @sider-extra-ZIndex;\n    width: calc(100% - 16px) !important;\n    font-family: unset !important;\n    margin-inline: 8px !important;\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", "@import '@/style/variables.less';\n.userSettings {\n  width: 100%;\n  height: 30px;\n  padding-right: 10px;\n  cursor: pointer;\n  svg {\n    width: 24px;\n    height: 24px;\n    fill: @color-text-a;\n  }\n  .operateContent {\n    .settings {\n      span {\n        margin-left: 10px;\n        font-family: 'NotoSans-Medium' !important;\n      }\n    }\n    .settings:hover {\n      color: @brand-primary;\n      cursor: pointer;\n      svg {\n        width: 24px;\n        fill: @brand-primary;\n      }\n    }\n  }\n}\n\n.commonIconStyle {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32px;\n  height: 32px;\n}\n\n.foldType {\n  position: relative;\n  bottom: 38px;\n  flex-direction: column-reverse;\n  margin-bottom: 10px;\n  font-size: 16px;\n  transition: font-size 0.3s ease-in-out;\n  padding-block: 0;\n  padding-inline: 0px;\n  svg {\n    position: relative;\n    left: 3px;\n  }\n  .settings {\n    margin-top: 5px;\n    margin-left: 0px;\n    border-radius: 8px;\n    .commonIconStyle;\n    svg {\n      position: relative;\n      left: 0px;\n      width: 24px;\n    }\n  }\n  .settings:hover {\n    background-color: rgba(0, 0, 0, 0.06);\n    svg {\n      fill: @brand-primary;\n    }\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", ".commonWatermark {\n  position: fixed;\n  top: 0; // 60px if has nav header\n  left: 0;\n  width: 100%;\n  height: inherit;\n  overflow: hidden;\n  pointer-events: none;\n}\n\n.commonWatermarkBox {\n  margin-top: -25%;\n  transform: skew(-4deg);\n}\n\n.commonStylesDiv {\n  display: flex;\n  justify-content: space-between;\n  padding: 40px 20px;\n  transform: rotate(-30deg);\n}\n\n.commonFont {\n  font-size: 16px;\n  user-select: none;\n}\n\n.two {\n  width: 100%;\n  margin-left: 11%;\n}\n\n/* 盲水印比明水印z-index大1 */\n.blindWatermark {\n  &:extend(.commonWatermark);\n  .blindWatermarkBox:extend(.commonWatermarkBox) {\n    margin-left: -800px;\n  }\n\n  .stylesDiv:extend(.commonStylesDiv) {\n    span:extend(.commonFont) {\n      color: #000;\n      opacity: 0.01;\n    }\n  }\n}\n\n/* 明水印(比Antd弹窗大2，与顶部Header保持一致) */\n.watermark {\n  &:extend(.commonWatermark);\n  .watermarkBox:extend(.commonWatermarkBox) {\n    margin-left: -400px;\n  }\n  .stylesDiv:extend(.commonStylesDiv) {\n    span:extend(.commonFont) {\n      color: #999999;\n      opacity: 0.2;\n    }\n  }\n}\n\n.mbileWatermark {\n  top: 0px;\n  left: -25%;\n  width: 150%;\n}\n\n.mobileWatermarkBox {\n  margin-left: -5%;\n}\n\n.mobileBlindWatermarkBox {\n  margin-left: -8%;\n}\n\n.mobileDiv {\n  padding: 20px 0px !important;\n  span:extend(.commonFont) {\n    margin: 20px 20px !important;\n    color: #aaaaaa !important;\n    opacity: 0.1 !important;\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", "/* stylelint-disable */\nhtml,\nbody {\n  width: 100%;\n  height: 100%;\n}\ninput::-ms-clear,\ninput::-ms-reveal {\n  display: none;\n}\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\nhtml {\n  font-family: sans-serif;\n  line-height: 1.15;\n  -webkit-text-size-adjust: 100%;\n  -ms-text-size-adjust: 100%;\n  -ms-overflow-style: scrollbar;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n}\n@-ms-viewport {\n  width: device-width;\n}\nbody {\n  margin: 0;\n}\n[tabindex='-1']:focus {\n  outline: none;\n}\nhr {\n  box-sizing: content-box;\n  height: 0;\n  overflow: visible;\n}\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  margin-top: 0;\n  margin-bottom: 0.5em;\n  font-weight: 500;\n}\np {\n  margin-top: 0;\n  margin-bottom: 1em;\n}\nabbr[title],\nabbr[data-original-title] {\n  -webkit-text-decoration: underline dotted;\n  text-decoration: underline;\n  text-decoration: underline dotted;\n  border-bottom: 0;\n  cursor: help;\n}\naddress {\n  margin-bottom: 1em;\n  font-style: normal;\n  line-height: inherit;\n}\ninput[type='text'],\ninput[type='password'],\ninput[type='number'],\ntextarea {\n  -webkit-appearance: none;\n}\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1em;\n}\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\ndt {\n  font-weight: 500;\n}\ndd {\n  margin-bottom: 0.5em;\n  margin-left: 0;\n}\nblockquote {\n  margin: 0 0 1em;\n}\ndfn {\n  font-style: italic;\n}\nb,\nstrong {\n  font-weight: bolder;\n}\nsmall {\n  font-size: 80%;\n}\nsub,\nsup {\n  position: relative;\n  font-size: 75%;\n  line-height: 0;\n  vertical-align: baseline;\n}\nsub {\n  bottom: -0.25em;\n}\nsup {\n  top: -0.5em;\n}\npre,\ncode,\nkbd,\nsamp {\n  font-size: 1em;\n  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;\n}\npre {\n  margin-top: 0;\n  margin-bottom: 1em;\n  overflow: auto;\n}\nfigure {\n  margin: 0 0 1em;\n}\nimg {\n  vertical-align: middle;\n  border-style: none;\n}\na,\narea,\nbutton,\n[role='button'],\ninput:not([type='range']),\nlabel,\nselect,\nsummary,\ntextarea {\n  touch-action: manipulation;\n}\ntable {\n  border-collapse: collapse;\n}\ncaption {\n  padding-top: 0.75em;\n  padding-bottom: 0.3em;\n  text-align: left;\n  caption-side: bottom;\n}\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0;\n  color: inherit;\n  font-size: inherit;\n  font-family: inherit;\n  line-height: inherit;\n}\nbutton,\ninput {\n  overflow: visible;\n}\nbutton,\nselect {\n  text-transform: none;\n}\nbutton,\nhtml [type='button'],\n[type='reset'],\n[type='submit'] {\n  -webkit-appearance: button;\n}\nbutton::-moz-focus-inner,\n[type='button']::-moz-focus-inner,\n[type='reset']::-moz-focus-inner,\n[type='submit']::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\ninput[type='radio'],\ninput[type='checkbox'] {\n  box-sizing: border-box;\n  padding: 0;\n}\ninput[type='date'],\ninput[type='time'],\ninput[type='datetime-local'],\ninput[type='month'] {\n  -webkit-appearance: listbox;\n}\ntextarea {\n  overflow: auto;\n  resize: vertical;\n}\nfieldset {\n  min-width: 0;\n  margin: 0;\n  padding: 0;\n  border: 0;\n}\nlegend {\n  display: block;\n  width: 100%;\n  max-width: 100%;\n  margin-bottom: 0.5em;\n  padding: 0;\n  color: inherit;\n  font-size: 1.5em;\n  line-height: inherit;\n  white-space: normal;\n}\nprogress {\n  vertical-align: baseline;\n}\n[type='number']::-webkit-inner-spin-button,\n[type='number']::-webkit-outer-spin-button {\n  height: auto;\n}\n[type='search'] {\n  outline-offset: -2px;\n  -webkit-appearance: none;\n}\n[type='search']::-webkit-search-cancel-button,\n[type='search']::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n::-webkit-file-upload-button {\n  font: inherit;\n  -webkit-appearance: button;\n}\noutput {\n  display: inline-block;\n}\nsummary {\n  display: list-item;\n}\ntemplate {\n  display: none;\n}\n[hidden] {\n  display: none !important;\n}\nmark {\n  padding: 0.2em;\n  background-color: #feffe6;\n}\n"], "names": [], "sourceRoot": ""}