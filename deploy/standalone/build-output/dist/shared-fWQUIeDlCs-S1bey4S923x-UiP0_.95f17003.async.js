(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[7847],{47356:function(m,c){"use strict";Object.defineProperty(c,"__esModule",{value:!0});var a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 000 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"arrow-left",theme:"outlined"};c.default=a},44149:function(m,c){"use strict";Object.defineProperty(c,"__esModule",{value:!0});var a={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M869 487.8L491.2 159.9c-2.9-2.5-6.6-3.9-10.5-3.9h-88.5c-7.4 0-10.8 9.2-5.2 14l350.2 304H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h585.1L386.9 854c-5.6 4.9-2.2 14 5.2 14h91.5c1.9 0 3.8-.7 5.2-2L869 536.2a32.07 32.07 0 000-48.4z"}}]},name:"arrow-right",theme:"outlined"};c.default=a},11774:function(m,c,a){"use strict";a.d(c,{_z:function(){return Ft}});var t=a(4942),h=a(45987),o=a(1413),f=a(71002),C=a(10915),x=a(48096),$=a(67159),B=a(28459),X=a(30291),k=a(93967),R=a.n(k),P=a(67294),ce=a(76509),Z=a(12044),ve=a(97435),Ge=a(73935),Y=a(98082),vt=function(r){return(0,t.Z)({},r.componentCls,{position:"fixed",insetInlineEnd:0,bottom:0,zIndex:99,display:"flex",alignItems:"center",width:"100%",paddingInline:24,paddingBlock:0,boxSizing:"border-box",lineHeight:"64px",backgroundColor:(0,Y.uK)(r.colorBgElevated,.6),borderBlockStart:"1px solid ".concat(r.colorSplit),"-webkit-backdrop-filter":"blur(8px)",backdropFilter:"blur(8px)",color:r.colorText,transition:"all 0.2s ease 0s","&-left":{flex:1,color:r.colorText},"&-right":{color:r.colorText,"> *":{marginInlineEnd:8,"&:last-child":{marginBlock:0,marginInline:0}}}})};function G(z){return(0,Y.Xj)("ProLayoutFooterToolbar",function(r){var u=(0,o.Z)((0,o.Z)({},r),{},{componentCls:".".concat(z)});return[vt(u)]})}function H(z,r){var u=r.stylish;return(0,Y.Xj)("ProLayoutFooterToolbarStylish",function(T){var I=(0,o.Z)((0,o.Z)({},T),{},{componentCls:".".concat(z)});return u?[(0,t.Z)({},"".concat(I.componentCls),u==null?void 0:u(I))]:[]})}var i=a(85893),j=["children","className","extra","portalDom","style","renderContent"],L=function(r){var u=r.children,T=r.className,I=r.extra,q=r.portalDom,oe=q===void 0?!0:q,be=r.style,Ie=r.renderContent,te=(0,h.Z)(r,j),me=(0,P.useContext)(B.ZP.ConfigContext),ie=me.getPrefixCls,Se=me.getTargetContainer,pe=r.prefixCls||ie("pro"),ne="".concat(pe,"-footer-bar"),K=G(ne),Pe=K.wrapSSR,U=K.hashId,_=(0,P.useContext)(ce.X),Ue=(0,P.useMemo)(function(){var it=_.hasSiderMenu,bt=_.isMobile,Nt=_.siderWidth;if(it)return Nt?bt?"100%":"calc(100% - ".concat(Nt,"px)"):"100%"},[_.collapsed,_.hasSiderMenu,_.isMobile,_.siderWidth]),Ye=(0,P.useMemo)(function(){return(typeof window=="undefined"?"undefined":(0,f.Z)(window))===void 0||(typeof document=="undefined"?"undefined":(0,f.Z)(document))===void 0?null:(Se==null?void 0:Se())||document.body},[]),Rt=H("".concat(ne,".").concat(ne,"-stylish"),{stylish:r.stylish}),$e=(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:"".concat(ne,"-left ").concat(U).trim(),children:I}),(0,i.jsx)("div",{className:"".concat(ne,"-right ").concat(U).trim(),children:u})]});(0,P.useEffect)(function(){return!_||!(_!=null&&_.setHasFooterToolbar)?function(){}:(_==null||_.setHasFooterToolbar(!0),function(){var it;_==null||(it=_.setHasFooterToolbar)===null||it===void 0||it.call(_,!1)})},[]);var je=(0,i.jsx)("div",(0,o.Z)((0,o.Z)({className:R()(T,U,ne,(0,t.Z)({},"".concat(ne,"-stylish"),!!r.stylish)),style:(0,o.Z)({width:Ue},be)},(0,ve.Z)(te,["prefixCls"])),{},{children:Ie?Ie((0,o.Z)((0,o.Z)((0,o.Z)({},r),_),{},{leftWidth:Ue}),$e):$e})),zt=!(0,Z.j)()||!oe||!Ye?je:(0,Ge.createPortal)(je,Ye,ne);return Rt.wrapSSR(Pe((0,i.jsx)(P.Fragment,{children:zt},ne)))},F=function(r){return(0,t.Z)({},r.componentCls,{width:"100%","&-wide":{maxWidth:1152,margin:"0 auto"}})};function Ce(z){return(0,Y.Xj)("ProLayoutGridContent",function(r){var u=(0,o.Z)((0,o.Z)({},r),{},{componentCls:".".concat(z)});return[F(u)]})}var Ae=function(r){var u=(0,P.useContext)(ce.X),T=r.children,I=r.contentWidth,q=r.className,oe=r.style,be=(0,P.useContext)(B.ZP.ConfigContext),Ie=be.getPrefixCls,te=r.prefixCls||Ie("pro"),me=I||u.contentWidth,ie="".concat(te,"-grid-content"),Se=Ce(ie),pe=Se.wrapSSR,ne=Se.hashId,K=me==="Fixed"&&u.layout==="top";return pe((0,i.jsx)("div",{className:R()(ie,ne,q,(0,t.Z)({},"".concat(ie,"-wide"),K)),style:oe,children:(0,i.jsx)("div",{className:"".concat(te,"-grid-content-children ").concat(ne).trim(),children:T})}))},he=a(97685),ze=a(3770),ct=a.n(ze),St=a(77059),st=a.n(St),Ke=a(85673),Ht=a(7134),Ut=a(42075),d=a(9220),E=a(80334),O=function(){return{overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}},y=function(r){var u;return(0,t.Z)({},r.componentCls,(0,o.Z)((0,o.Z)({},Y.Wf===null||Y.Wf===void 0?void 0:(0,Y.Wf)(r)),{},(0,t.Z)((0,t.Z)((0,t.Z)((0,t.Z)((0,t.Z)((0,t.Z)((0,t.Z)((0,t.Z)({position:"relative",backgroundColor:r.colorWhite,paddingBlock:r.pageHeaderPaddingVertical+2,paddingInline:r.pageHeaderPadding,"&&-ghost":{backgroundColor:r.pageHeaderBgGhost},"&-no-children":{height:(u=r.layout)===null||u===void 0||(u=u.pageContainer)===null||u===void 0?void 0:u.paddingBlockPageContainerContent},"&&-has-breadcrumb":{paddingBlockStart:r.pageHeaderPaddingBreadCrumb},"&&-has-footer":{paddingBlockEnd:0},"& &-back":(0,t.Z)({marginInlineEnd:r.margin,fontSize:16,lineHeight:1,"&-button":(0,o.Z)((0,o.Z)({fontSize:16},Y.Nd===null||Y.Nd===void 0?void 0:(0,Y.Nd)(r)),{},{color:r.pageHeaderColorBack,cursor:"pointer"})},"".concat(r.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:0})},"& ".concat("ant","-divider-vertical"),{height:14,marginBlock:0,marginInline:r.marginSM,verticalAlign:"middle"}),"& &-breadcrumb + &-heading",{marginBlockStart:r.marginXS}),"& &-heading",{display:"flex",justifyContent:"space-between","&-left":{display:"flex",alignItems:"center",marginBlock:r.marginXS/2,marginInlineEnd:0,marginInlineStart:0,overflow:"hidden"},"&-title":(0,o.Z)((0,o.Z)({marginInlineEnd:r.marginSM,marginBlockEnd:0,color:r.colorTextHeading,fontWeight:600,fontSize:r.pageHeaderFontSizeHeaderTitle,lineHeight:r.controlHeight+"px"},O()),{},(0,t.Z)({},"".concat(r.componentCls,"-rlt &"),{marginInlineEnd:0,marginInlineStart:r.marginSM})),"&-avatar":(0,t.Z)({marginInlineEnd:r.marginSM},"".concat(r.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:r.marginSM}),"&-tags":(0,t.Z)({},"".concat(r.componentCls,"-rlt &"),{float:"right"}),"&-sub-title":(0,o.Z)((0,o.Z)({marginInlineEnd:r.marginSM,color:r.colorTextSecondary,fontSize:r.pageHeaderFontSizeHeaderSubTitle,lineHeight:r.lineHeight},O()),{},(0,t.Z)({},"".concat(r.componentCls,"-rlt &"),{float:"right",marginInlineEnd:0,marginInlineStart:12})),"&-extra":(0,t.Z)((0,t.Z)({marginBlock:r.marginXS/2,marginInlineEnd:0,marginInlineStart:0,whiteSpace:"nowrap","> *":(0,t.Z)({"white-space":"unset"},"".concat(r.componentCls,"-rlt &"),{marginInlineEnd:r.marginSM,marginInlineStart:0})},"".concat(r.componentCls,"-rlt &"),{float:"left"}),"*:first-child",(0,t.Z)({},"".concat(r.componentCls,"-rlt &"),{marginInlineEnd:0}))}),"&-content",{paddingBlockStart:r.pageHeaderPaddingContentPadding}),"&-footer",{marginBlockStart:r.margin}),"&-compact &-heading",{flexWrap:"wrap"}),"&-wide",{maxWidth:1152,margin:"0 auto"}),"&-rtl",{direction:"rtl"})))};function N(z){return(0,Y.Xj)("ProLayoutPageHeader",function(r){var u=(0,o.Z)((0,o.Z)({},r),{},{componentCls:".".concat(z),pageHeaderBgGhost:"transparent",pageHeaderPadding:16,pageHeaderPaddingVertical:4,pageHeaderPaddingBreadCrumb:r.paddingSM,pageHeaderColorBack:r.colorTextHeading,pageHeaderFontSizeHeaderTitle:r.fontSizeHeading4,pageHeaderFontSizeHeaderSubTitle:14,pageHeaderPaddingContentPadding:r.paddingSM});return[y(u)]})}var Fe=function(r,u,T,I){return!T||!I?null:(0,i.jsx)("div",{className:"".concat(r,"-back ").concat(u).trim(),children:(0,i.jsx)("div",{role:"button",onClick:function(oe){I==null||I(oe)},className:"".concat(r,"-back-button ").concat(u).trim(),"aria-label":"back",children:T})})},Je=function(r,u){var T;return(T=r.items)!==null&&T!==void 0&&T.length?(0,i.jsx)(Ke.Z,(0,o.Z)((0,o.Z)({},r),{},{className:R()("".concat(u,"-breadcrumb"),r.className)})):null},Ve=function(r){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"ltr";return r.backIcon!==void 0?r.backIcon:u==="rtl"?(0,i.jsx)(st(),{}):(0,i.jsx)(ct(),{})},gt=function(r,u){var T=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"ltr",I=arguments.length>3?arguments[3]:void 0,q=u.title,oe=u.avatar,be=u.subTitle,Ie=u.tags,te=u.extra,me=u.onBack,ie="".concat(r,"-heading"),Se=q||be||Ie||te;if(!Se)return null;var pe=Ve(u,T),ne=Fe(r,I,pe,me),K=ne||oe||Se;return(0,i.jsxs)("div",{className:ie+" "+I,children:[K&&(0,i.jsxs)("div",{className:"".concat(ie,"-left ").concat(I).trim(),children:[ne,oe&&(0,i.jsx)(Ht.C,(0,o.Z)({className:R()("".concat(ie,"-avatar"),I,oe.className)},oe)),q&&(0,i.jsx)("span",{className:"".concat(ie,"-title ").concat(I).trim(),title:typeof q=="string"?q:void 0,children:q}),be&&(0,i.jsx)("span",{className:"".concat(ie,"-sub-title ").concat(I).trim(),title:typeof be=="string"?be:void 0,children:be}),Ie&&(0,i.jsx)("span",{className:"".concat(ie,"-tags ").concat(I).trim(),children:Ie})]}),te&&(0,i.jsx)("span",{className:"".concat(ie,"-extra ").concat(I).trim(),children:(0,i.jsx)(Ut.Z,{children:te})})]})},ht=function(r,u,T){return u?(0,i.jsx)("div",{className:"".concat(r,"-footer ").concat(T).trim(),children:u}):null},mt=function(r,u,T){return(0,i.jsx)("div",{className:"".concat(r,"-content ").concat(T).trim(),children:u})},It=function z(r){return r==null?void 0:r.map(function(u){var T;return(0,E.ET)(!!u.breadcrumbName,"Route.breadcrumbName is deprecated, please use Route.title instead."),(0,o.Z)((0,o.Z)({},u),{},{breadcrumbName:void 0,children:void 0,title:u.title||u.breadcrumbName},(T=u.children)!==null&&T!==void 0&&T.length?{menu:{items:z(u.children)}}:{})})},yt=function(r){var u,T=P.useState(!1),I=(0,he.Z)(T,2),q=I[0],oe=I[1],be=function(rn){var dn=rn.width;return oe(dn<768)},Ie=P.useContext(B.ZP.ConfigContext),te=Ie.getPrefixCls,me=Ie.direction,ie=r.prefixCls,Se=r.style,pe=r.footer,ne=r.children,K=r.breadcrumb,Pe=r.breadcrumbRender,U=r.className,_=r.contentWidth,Ue=r.layout,Ye=r.ghost,Rt=Ye===void 0?!0:Ye,$e=te("page-header",ie),je=N($e),zt=je.wrapSSR,it=je.hashId,bt=function(){return K&&!(K!=null&&K.items)&&K!==null&&K!==void 0&&K.routes&&((0,E.ET)(!1,"The routes of Breadcrumb is deprecated, please use items instead."),K.items=It(K.routes)),K!=null&&K.items?Je(K,$e):null},Nt=bt(),Jt=K&&"props"in K,D=(u=Pe==null?void 0:Pe((0,o.Z)((0,o.Z)({},r),{},{prefixCls:$e}),Nt))!==null&&u!==void 0?u:Nt,pt=Jt?K:D,Yt=R()($e,it,U,(0,t.Z)((0,t.Z)((0,t.Z)((0,t.Z)((0,t.Z)((0,t.Z)({},"".concat($e,"-has-breadcrumb"),!!pt),"".concat($e,"-has-footer"),!!pe),"".concat($e,"-rtl"),me==="rtl"),"".concat($e,"-compact"),q),"".concat($e,"-wide"),_==="Fixed"&&Ue=="top"),"".concat($e,"-ghost"),Rt)),Xt=gt($e,r,me,it),Gt=ne&&mt($e,ne,it),qe=ht($e,pe,it);return!pt&&!Xt&&!qe&&!Gt?(0,i.jsx)("div",{className:R()(it,["".concat($e,"-no-children")])}):zt((0,i.jsx)(d.default,{onResize:be,children:(0,i.jsxs)("div",{className:Yt,style:Se,children:[pt,Xt,Gt,qe]})}))},ke=a(83832),Ee=function(r){if(!r)return 1;var u=r.backingStorePixelRatio||r.webkitBackingStorePixelRatio||r.mozBackingStorePixelRatio||r.msBackingStorePixelRatio||r.oBackingStorePixelRatio||1;return(window.devicePixelRatio||1)/u},Zt=function(r){var u=(0,Y.dQ)(),T=u.token,I=r.children,q=r.style,oe=r.className,be=r.markStyle,Ie=r.markClassName,te=r.zIndex,me=te===void 0?9:te,ie=r.gapX,Se=ie===void 0?212:ie,pe=r.gapY,ne=pe===void 0?222:pe,K=r.width,Pe=K===void 0?120:K,U=r.height,_=U===void 0?64:U,Ue=r.rotate,Ye=Ue===void 0?-22:Ue,Rt=r.image,$e=r.offsetLeft,je=r.offsetTop,zt=r.fontStyle,it=zt===void 0?"normal":zt,bt=r.fontWeight,Nt=bt===void 0?"normal":bt,Jt=r.fontColor,D=Jt===void 0?T.colorFill:Jt,pt=r.fontSize,Yt=pt===void 0?16:pt,Xt=r.fontFamily,Gt=Xt===void 0?"sans-serif":Xt,qe=r.prefixCls,wt=(0,P.useContext)(B.ZP.ConfigContext),rn=wt.getPrefixCls,dn=rn("pro-layout-watermark",qe),wn=R()("".concat(dn,"-wrapper"),oe),En=R()(dn,Ie),In=(0,P.useState)(""),bn=(0,he.Z)(In,2),pn=bn[0],yn=bn[1];return(0,P.useEffect)(function(){var on=document.createElement("canvas"),Kt=on.getContext("2d"),_t=Ee(Kt),Zn="".concat((Se+Pe)*_t,"px"),Cn="".concat((ne+_)*_t,"px"),On=$e||Se/2,e=je||ne/2;if(on.setAttribute("width",Zn),on.setAttribute("height",Cn),!Kt){console.error("\u5F53\u524D\u73AF\u5883\u4E0D\u652F\u6301Canvas");return}Kt.translate(On*_t,e*_t),Kt.rotate(Math.PI/180*Number(Ye));var n=Pe*_t,l=_*_t,s=function(p){var g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,S=Number(Yt)*_t;Kt.font="".concat(it," normal ").concat(Nt," ").concat(S,"px/").concat(l,"px ").concat(Gt),Kt.fillStyle=D,Array.isArray(p)?p==null||p.forEach(function(w,A){return Kt.fillText(w,0,A*S+g)}):Kt.fillText(p,0,g?g+S:0),yn(on.toDataURL())};if(Rt){var v=new Image;v.crossOrigin="anonymous",v.referrerPolicy="no-referrer",v.src=Rt,v.onload=function(){if(Kt.drawImage(v,0,0,n,l),yn(on.toDataURL()),r.content){s(r.content,v.height+8);return}};return}if(r.content){s(r.content);return}},[Se,ne,$e,je,Ye,it,Nt,Pe,_,Gt,D,Rt,r.content,Yt]),(0,i.jsxs)("div",{style:(0,o.Z)({position:"relative"},q),className:wn,children:[I,(0,i.jsx)("div",{className:En,style:(0,o.Z)((0,o.Z)({zIndex:me,position:"absolute",left:0,top:0,width:"100%",height:"100%",backgroundSize:"".concat(Se+Pe,"px"),pointerEvents:"none",backgroundRepeat:"repeat"},pn?{backgroundImage:"url('".concat(pn,"')")}:{}),be)})]})},Ct=[576,768,992,1200].map(function(z){return"@media (max-width: ".concat(z,"px)")}),rt=(0,he.Z)(Ct,4),Pt=rt[0],$t=rt[1],ot=rt[2],xt=rt[3],jt=function(r){var u,T,I,q,oe,be,Ie,te,me,ie,Se,pe,ne,K,Pe,U,_,Ue;return(0,t.Z)({},r.componentCls,(0,t.Z)((0,t.Z)((0,t.Z)((0,t.Z)((0,t.Z)((0,t.Z)({position:"relative","&-children-container":{paddingBlockStart:0,paddingBlockEnd:(u=r.layout)===null||u===void 0||(u=u.pageContainer)===null||u===void 0?void 0:u.paddingBlockPageContainerContent,paddingInline:(T=r.layout)===null||T===void 0||(T=T.pageContainer)===null||T===void 0?void 0:T.paddingInlinePageContainerContent},"&-children-container-no-header":{paddingBlockStart:(I=r.layout)===null||I===void 0||(I=I.pageContainer)===null||I===void 0?void 0:I.paddingBlockPageContainerContent},"&-affix":(0,t.Z)({},"".concat(r.antCls,"-affix"),(0,t.Z)({},"".concat(r.componentCls,"-warp"),{backgroundColor:(q=r.layout)===null||q===void 0||(q=q.pageContainer)===null||q===void 0?void 0:q.colorBgPageContainerFixed,transition:"background-color 0.3s",boxShadow:"0 2px 8px #f0f1f2"}))},"& &-warp-page-header",(0,t.Z)((0,t.Z)((0,t.Z)((0,t.Z)({paddingBlockStart:((oe=(be=r.layout)===null||be===void 0||(be=be.pageContainer)===null||be===void 0?void 0:be.paddingBlockPageContainerContent)!==null&&oe!==void 0?oe:40)/4,paddingBlockEnd:((Ie=(te=r.layout)===null||te===void 0||(te=te.pageContainer)===null||te===void 0?void 0:te.paddingBlockPageContainerContent)!==null&&Ie!==void 0?Ie:40)/2,paddingInlineStart:(me=r.layout)===null||me===void 0||(me=me.pageContainer)===null||me===void 0?void 0:me.paddingInlinePageContainerContent,paddingInlineEnd:(ie=r.layout)===null||ie===void 0||(ie=ie.pageContainer)===null||ie===void 0?void 0:ie.paddingInlinePageContainerContent},"& ~ ".concat(r.proComponentsCls,"-grid-content"),(0,t.Z)({},"".concat(r.proComponentsCls,"-page-container-children-content"),{paddingBlock:((Se=(pe=r.layout)===null||pe===void 0||(pe=pe.pageContainer)===null||pe===void 0?void 0:pe.paddingBlockPageContainerContent)!==null&&Se!==void 0?Se:24)/3})),"".concat(r.antCls,"-page-header-breadcrumb"),{paddingBlockStart:((ne=(K=r.layout)===null||K===void 0||(K=K.pageContainer)===null||K===void 0?void 0:K.paddingBlockPageContainerContent)!==null&&ne!==void 0?ne:40)/4+10}),"".concat(r.antCls,"-page-header-heading"),{paddingBlockStart:((Pe=(U=r.layout)===null||U===void 0||(U=U.pageContainer)===null||U===void 0?void 0:U.paddingBlockPageContainerContent)!==null&&Pe!==void 0?Pe:40)/4}),"".concat(r.antCls,"-page-header-footer"),{marginBlockStart:((_=(Ue=r.layout)===null||Ue===void 0||(Ue=Ue.pageContainer)===null||Ue===void 0?void 0:Ue.paddingBlockPageContainerContent)!==null&&_!==void 0?_:40)/4})),"&-detail",(0,t.Z)({display:"flex"},Pt,{display:"block"})),"&-main",{width:"100%"}),"&-row",(0,t.Z)({display:"flex",width:"100%"},$t,{display:"block"})),"&-content",{flex:"auto",width:"100%"}),"&-extraContent",(0,t.Z)((0,t.Z)((0,t.Z)((0,t.Z)({flex:"0 1 auto",minWidth:"242px",marginInlineStart:88,textAlign:"end"},xt,{marginInlineStart:44}),ot,{marginInlineStart:20}),$t,{marginInlineStart:0,textAlign:"start"}),Pt,{marginInlineStart:0})))};function nn(z,r){return(0,Y.Xj)("ProLayoutPageContainer",function(u){var T,I=(0,o.Z)((0,o.Z)({},u),{},{componentCls:".".concat(z),layout:(0,o.Z)((0,o.Z)({},u==null?void 0:u.layout),{},{pageContainer:(0,o.Z)((0,o.Z)({},u==null||(T=u.layout)===null||T===void 0?void 0:T.pageContainer),r)})});return[jt(I)]})}function Ot(z,r){var u=r.stylish;return(0,Y.Xj)("ProLayoutPageContainerStylish",function(T){var I=(0,o.Z)((0,o.Z)({},T),{},{componentCls:".".concat(z)});return u?[(0,t.Z)({},"div".concat(I.componentCls),u==null?void 0:u(I))]:[]})}var Bt=a(1977),Tt=["title","content","pageHeaderRender","header","prefixedClassName","extraContent","childrenContentStyle","style","prefixCls","hashId","value","breadcrumbRender"],Oe=["children","loading","className","style","footer","affixProps","token","fixedHeader","breadcrumbRender","footerToolBarProps","childrenContentStyle"];function xe(z){return(0,f.Z)(z)==="object"?z:{spinning:z}}var de=function(r){var u=r.tabList,T=r.tabActiveKey,I=r.onTabChange,q=r.hashId,oe=r.tabBarExtraContent,be=r.tabProps,Ie=r.prefixedClassName;return Array.isArray(u)||oe?(0,i.jsx)(x.Z,(0,o.Z)((0,o.Z)({className:"".concat(Ie,"-tabs ").concat(q).trim(),activeKey:T,onChange:function(me){I&&I(me)},tabBarExtraContent:oe,items:u==null?void 0:u.map(function(te,me){var ie;return(0,o.Z)((0,o.Z)({label:te.tab},te),{},{key:((ie=te.key)===null||ie===void 0?void 0:ie.toString())||(me==null?void 0:me.toString())})})},be),{},{children:(0,Bt.n)($.Z,"4.23.0")<0?u==null?void 0:u.map(function(te,me){return(0,i.jsx)(x.Z.TabPane,(0,o.Z)({tab:te.tab},te),te.key||me)}):null})):null},At=function(r,u,T,I){return!r&&!u?null:(0,i.jsx)("div",{className:"".concat(T,"-detail ").concat(I).trim(),children:(0,i.jsx)("div",{className:"".concat(T,"-main ").concat(I).trim(),children:(0,i.jsxs)("div",{className:"".concat(T,"-row ").concat(I).trim(),children:[r&&(0,i.jsx)("div",{className:"".concat(T,"-content ").concat(I).trim(),children:r}),u&&(0,i.jsx)("div",{className:"".concat(T,"-extraContent ").concat(I).trim(),children:u})]})})})},Lt=function(r){var u=useContext(RouteContext);return _jsx("div",{style:{height:"100%",display:"flex",alignItems:"center"},children:_jsx(Breadcrumb,_objectSpread(_objectSpread(_objectSpread({},u==null?void 0:u.breadcrumb),u==null?void 0:u.breadcrumbProps),r))})},Wt=function(r){var u,T=r.title,I=r.content,q=r.pageHeaderRender,oe=r.header,be=r.prefixedClassName,Ie=r.extraContent,te=r.childrenContentStyle,me=r.style,ie=r.prefixCls,Se=r.hashId,pe=r.value,ne=r.breadcrumbRender,K=(0,h.Z)(r,Tt),Pe=function(){if(ne)return ne};if(q===!1)return null;if(q)return(0,i.jsxs)(i.Fragment,{children:[" ",q((0,o.Z)((0,o.Z)({},r),pe))]});var U=T;!T&&T!==!1&&(U=pe.title);var _=(0,o.Z)((0,o.Z)((0,o.Z)({},pe),{},{title:U},K),{},{footer:de((0,o.Z)((0,o.Z)({},K),{},{hashId:Se,breadcrumbRender:ne,prefixedClassName:be}))},oe),Ue=_,Ye=Ue.breadcrumb,Rt=(!Ye||!(Ye!=null&&Ye.itemRender)&&!(Ye!=null&&(u=Ye.items)!==null&&u!==void 0&&u.length))&&!ne;return["title","subTitle","extra","tags","footer","avatar","backIcon"].every(function($e){return!_[$e]})&&Rt&&!I&&!Ie?null:(0,i.jsx)(yt,(0,o.Z)((0,o.Z)({},_),{},{className:"".concat(be,"-warp-page-header ").concat(Se).trim(),breadcrumb:ne===!1?void 0:(0,o.Z)((0,o.Z)({},_.breadcrumb),pe.breadcrumbProps),breadcrumbRender:Pe(),prefixCls:ie,children:(oe==null?void 0:oe.children)||At(I,Ie,be,Se)}))},vn=function(r){var u,T,I=r.children,q=r.loading,oe=q===void 0?!1:q,be=r.className,Ie=r.style,te=r.footer,me=r.affixProps,ie=r.token,Se=r.fixedHeader,pe=r.breadcrumbRender,ne=r.footerToolBarProps,K=r.childrenContentStyle,Pe=(0,h.Z)(r,Oe),U=(0,P.useContext)(ce.X);(0,P.useEffect)(function(){var qe;return!U||!(U!=null&&U.setHasPageContainer)?function(){}:(U==null||(qe=U.setHasPageContainer)===null||qe===void 0||qe.call(U,function(wt){return wt+1}),function(){var wt;U==null||(wt=U.setHasPageContainer)===null||wt===void 0||wt.call(U,function(rn){return rn-1})})},[]);var _=(0,P.useContext)(C.L_),Ue=_.token,Ye=(0,P.useContext)(B.ZP.ConfigContext),Rt=Ye.getPrefixCls,$e=r.prefixCls||Rt("pro"),je="".concat($e,"-page-container"),zt=nn(je,ie),it=zt.wrapSSR,bt=zt.hashId,Nt=Ot("".concat(je,".").concat(je,"-stylish"),{stylish:r.stylish}),Jt=(0,P.useMemo)(function(){var qe;return pe==!1?!1:pe||(Pe==null||(qe=Pe.header)===null||qe===void 0?void 0:qe.breadcrumbRender)},[pe,Pe==null||(u=Pe.header)===null||u===void 0?void 0:u.breadcrumbRender]),D=Wt((0,o.Z)((0,o.Z)({},Pe),{},{breadcrumbRender:Jt,ghost:!0,hashId:bt,prefixCls:void 0,prefixedClassName:je,value:U})),pt=(0,P.useMemo)(function(){if(P.isValidElement(oe))return oe;if(typeof oe=="boolean"&&!oe)return null;var qe=xe(oe);return qe.spinning?(0,i.jsx)(ke.S,(0,o.Z)({},qe)):null},[oe]),Yt=(0,P.useMemo)(function(){return I?(0,i.jsx)(i.Fragment,{children:(0,i.jsx)("div",{className:R()(bt,"".concat(je,"-children-container"),(0,t.Z)({},"".concat(je,"-children-container-no-header"),!D)),style:K,children:I})}):null},[I,je,K,bt]),Xt=(0,P.useMemo)(function(){var qe=pt||Yt;if(r.waterMarkProps||U.waterMarkProps){var wt=(0,o.Z)((0,o.Z)({},U.waterMarkProps),r.waterMarkProps);return(0,i.jsx)(Zt,(0,o.Z)((0,o.Z)({},wt),{},{children:qe}))}return qe},[r.waterMarkProps,U.waterMarkProps,pt,Yt]),Gt=R()(je,bt,be,(0,t.Z)((0,t.Z)((0,t.Z)({},"".concat(je,"-with-footer"),te),"".concat(je,"-with-affix"),Se&&D),"".concat(je,"-stylish"),!!Pe.stylish));return it(Nt.wrapSSR((0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{style:Ie,className:Gt,children:[Se&&D?(0,i.jsx)(X.Z,(0,o.Z)((0,o.Z)({offsetTop:U.hasHeader&&U.fixedHeader?(T=Ue.layout)===null||T===void 0||(T=T.header)===null||T===void 0?void 0:T.heightLayoutHeader:1},me),{},{className:"".concat(je,"-affix ").concat(bt).trim(),children:(0,i.jsx)("div",{className:"".concat(je,"-warp ").concat(bt).trim(),children:D})})):D,Xt&&(0,i.jsx)(Ae,{children:Xt})]}),te&&(0,i.jsx)(L,(0,o.Z)((0,o.Z)({stylish:Pe.footerStylish,prefixCls:$e},ne),{},{children:te}))]})))},Ft=function(r){return(0,i.jsx)(C._Y,{needDeps:!0,children:(0,i.jsx)(vn,(0,o.Z)({},r))})},an=function(r){var u=useContext(RouteContext);return Wt(_objectSpread(_objectSpread({},r),{},{hashId:"",value:u}))}},83832:function(m,c,a){"use strict";a.d(c,{S:function(){return $}});var t=a(1413),h=a(45987),o=a(74330),f=a(67294),C=a(85893),x=["isLoading","pastDelay","timedOut","error","retry"],$=function(X){var k=X.isLoading,R=X.pastDelay,P=X.timedOut,ce=X.error,Z=X.retry,ve=(0,h.Z)(X,x);return(0,C.jsx)("div",{style:{paddingBlockStart:100,textAlign:"center"},children:(0,C.jsx)(o.Z,(0,t.Z)({size:"large"},ve))})}},76509:function(m,c,a){"use strict";a.d(c,{X:function(){return h}});var t=a(67294),h=(0,t.createContext)({})},3770:function(m,c,a){"use strict";Object.defineProperty(c,"__esModule",{value:!0}),c.default=void 0;const t=h(a(27863));function h(f){return f&&f.__esModule?f:{default:f}}const o=t;c.default=o,m.exports=o},77059:function(m,c,a){"use strict";Object.defineProperty(c,"__esModule",{value:!0}),c.default=void 0;const t=h(a(21379));function h(f){return f&&f.__esModule?f:{default:f}}const o=t;c.default=o,m.exports=o},33046:function(m,c,a){"use strict";"use client";var t=a(64836).default,h=a(75263).default;Object.defineProperty(c,"__esModule",{value:!0}),c.default=void 0;var o=t(a(10434)),f=t(a(27424)),C=t(a(38416)),x=t(a(70215)),$=h(a(67294)),B=t(a(93967)),X=a(87646),k=t(a(61711)),R=t(a(27727)),P=a(26814),ce=a(72014),Z=["className","icon","spin","rotate","tabIndex","onClick","twoToneColor"];(0,P.setTwoToneColor)(X.blue.primary);var ve=$.forwardRef(function(Y,vt){var G=Y.className,H=Y.icon,i=Y.spin,j=Y.rotate,L=Y.tabIndex,F=Y.onClick,Ce=Y.twoToneColor,Ae=(0,x.default)(Y,Z),he=$.useContext(k.default),ze=he.prefixCls,ct=ze===void 0?"anticon":ze,St=he.rootClassName,st=(0,B.default)(St,ct,(0,C.default)((0,C.default)({},"".concat(ct,"-").concat(H.name),!!H.name),"".concat(ct,"-spin"),!!i||H.name==="loading"),G),Ke=L;Ke===void 0&&F&&(Ke=-1);var Ht=j?{msTransform:"rotate(".concat(j,"deg)"),transform:"rotate(".concat(j,"deg)")}:void 0,Ut=(0,ce.normalizeTwoToneColors)(Ce),d=(0,f.default)(Ut,2),E=d[0],O=d[1];return $.createElement("span",(0,o.default)({role:"img","aria-label":H.name},Ae,{ref:vt,tabIndex:Ke,onClick:F,className:st}),$.createElement(R.default,{icon:H,primaryColor:E,secondaryColor:O,style:Ht}))});ve.displayName="AntdIcon",ve.getTwoToneColor=P.getTwoToneColor,ve.setTwoToneColor=P.setTwoToneColor;var Ge=c.default=ve},61711:function(m,c,a){"use strict";Object.defineProperty(c,"__esModule",{value:!0}),c.default=void 0;var t=a(67294),h=(0,t.createContext)({}),o=c.default=h},27727:function(m,c,a){"use strict";var t=a(64836).default,h=a(75263).default;Object.defineProperty(c,"__esModule",{value:!0}),c.default=void 0;var o=t(a(70215)),f=t(a(42122)),C=h(a(67294)),x=a(72014),$=["icon","className","onClick","style","primaryColor","secondaryColor"],B={primaryColor:"#333",secondaryColor:"#E6E6E6",calculated:!1};function X(ce){var Z=ce.primaryColor,ve=ce.secondaryColor;B.primaryColor=Z,B.secondaryColor=ve||(0,x.getSecondaryColor)(Z),B.calculated=!!ve}function k(){return(0,f.default)({},B)}var R=function(Z){var ve=Z.icon,Ge=Z.className,Y=Z.onClick,vt=Z.style,G=Z.primaryColor,H=Z.secondaryColor,i=(0,o.default)(Z,$),j=C.useRef(),L=B;if(G&&(L={primaryColor:G,secondaryColor:H||(0,x.getSecondaryColor)(G)}),(0,x.useInsertStyles)(j),(0,x.warning)((0,x.isIconDefinition)(ve),"icon should be icon definiton, but got ".concat(ve)),!(0,x.isIconDefinition)(ve))return null;var F=ve;return F&&typeof F.icon=="function"&&(F=(0,f.default)((0,f.default)({},F),{},{icon:F.icon(L.primaryColor,L.secondaryColor)})),(0,x.generate)(F.icon,"svg-".concat(F.name),(0,f.default)((0,f.default)({className:Ge,onClick:Y,style:vt,"data-icon":F.name,width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true"},i),{},{ref:j}))};R.displayName="IconReact",R.getTwoToneColors=k,R.setTwoToneColors=X;var P=c.default=R},26814:function(m,c,a){"use strict";var t=a(64836).default;Object.defineProperty(c,"__esModule",{value:!0}),c.getTwoToneColor=x,c.setTwoToneColor=C;var h=t(a(27424)),o=t(a(27727)),f=a(72014);function C($){var B=(0,f.normalizeTwoToneColors)($),X=(0,h.default)(B,2),k=X[0],R=X[1];return o.default.setTwoToneColors({primaryColor:k,secondaryColor:R})}function x(){var $=o.default.getTwoToneColors();return $.calculated?[$.primaryColor,$.secondaryColor]:$.primaryColor}},27863:function(m,c,a){"use strict";var t=a(75263).default,h=a(64836).default;Object.defineProperty(c,"__esModule",{value:!0}),c.default=void 0;var o=h(a(10434)),f=t(a(67294)),C=h(a(47356)),x=h(a(33046)),$=function(R,P){return f.createElement(x.default,(0,o.default)({},R,{ref:P,icon:C.default}))},B=f.forwardRef($),X=c.default=B},21379:function(m,c,a){"use strict";var t=a(75263).default,h=a(64836).default;Object.defineProperty(c,"__esModule",{value:!0}),c.default=void 0;var o=h(a(10434)),f=t(a(67294)),C=h(a(44149)),x=h(a(33046)),$=function(R,P){return f.createElement(x.default,(0,o.default)({},R,{ref:P,icon:C.default}))},B=f.forwardRef($),X=c.default=B},72014:function(m,c,a){"use strict";var t=a(75263).default,h=a(64836).default;Object.defineProperty(c,"__esModule",{value:!0}),c.generate=ve,c.getSecondaryColor=Ge,c.iconStyles=void 0,c.isIconDefinition=ce,c.normalizeAttrs=Z,c.normalizeTwoToneColors=Y,c.useInsertStyles=c.svgBaseProps=void 0,c.warning=P;var o=h(a(42122)),f=h(a(18698)),C=a(87646),x=a(93399),$=a(63298),B=h(a(45520)),X=t(a(67294)),k=h(a(61711));function R(i){return i.replace(/-(.)/g,function(j,L){return L.toUpperCase()})}function P(i,j){(0,B.default)(i,"[@ant-design/icons] ".concat(j))}function ce(i){return(0,f.default)(i)==="object"&&typeof i.name=="string"&&typeof i.theme=="string"&&((0,f.default)(i.icon)==="object"||typeof i.icon=="function")}function Z(){var i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};return Object.keys(i).reduce(function(j,L){var F=i[L];switch(L){case"class":j.className=F,delete j.class;break;default:delete j[L],j[R(L)]=F}return j},{})}function ve(i,j,L){return L?X.default.createElement(i.tag,(0,o.default)((0,o.default)({key:j},Z(i.attrs)),L),(i.children||[]).map(function(F,Ce){return ve(F,"".concat(j,"-").concat(i.tag,"-").concat(Ce))})):X.default.createElement(i.tag,(0,o.default)({key:j},Z(i.attrs)),(i.children||[]).map(function(F,Ce){return ve(F,"".concat(j,"-").concat(i.tag,"-").concat(Ce))}))}function Ge(i){return(0,C.generate)(i)[0]}function Y(i){return i?Array.isArray(i)?i:[i]:[]}var vt=c.svgBaseProps={width:"1em",height:"1em",fill:"currentColor","aria-hidden":"true",focusable:"false"},G=c.iconStyles=`
.anticon {
  display: inline-flex;
  align-items: center;
  color: inherit;
  font-style: normal;
  line-height: 0;
  text-align: center;
  text-transform: none;
  vertical-align: -0.125em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.anticon > * {
  line-height: 1;
}

.anticon svg {
  display: inline-block;
}

.anticon::before {
  display: none;
}

.anticon .anticon-icon {
  display: block;
}

.anticon[tabindex] {
  cursor: pointer;
}

.anticon-spin::before,
.anticon-spin {
  display: inline-block;
  -webkit-animation: loadingCircle 1s infinite linear;
  animation: loadingCircle 1s infinite linear;
}

@-webkit-keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes loadingCircle {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
`,H=c.useInsertStyles=function(j){var L=(0,X.useContext)(k.default),F=L.csp,Ce=L.prefixCls,Ae=G;Ce&&(Ae=Ae.replace(/anticon/g,Ce)),(0,X.useEffect)(function(){var he=j.current,ze=(0,$.getShadowRoot)(he);(0,x.updateCSS)(Ae,"@ant-design-icons",{prepend:!0,csp:F,attachTo:ze})},[])}},12044:function(m,c,a){"use strict";a.d(c,{j:function(){return o}});var t=a(34155),h=typeof t!="undefined"&&t.versions!=null&&t.versions.node!=null,o=function(){return typeof window!="undefined"&&typeof window.document!="undefined"&&typeof window.matchMedia!="undefined"&&!h}},30291:function(m,c,a){"use strict";a.d(c,{Z:function(){return H}});var t=a(67294),h=a(93967),o=a.n(h),f=a(9220),C=a(98423),x=a(48783),$=a(53124),B=a(83559);const X=i=>{const{componentCls:j}=i;return{[j]:{position:"fixed",zIndex:i.zIndexPopup}}},k=i=>({zIndexPopup:i.zIndexBase+10});var R=(0,B.I$)("Affix",X,k);function P(i){return i!==window?i.getBoundingClientRect():{top:0,bottom:window.innerHeight}}function ce(i,j,L){if(L!==void 0&&Math.round(j.top)>Math.round(i.top)-L)return L+j.top}function Z(i,j,L){if(L!==void 0&&Math.round(j.bottom)<Math.round(i.bottom)+L){const F=window.innerHeight-j.bottom;return L+F}}const ve=["resize","scroll","touchstart","touchmove","touchend","pageshow","load"];function Ge(){return typeof window!="undefined"?window:null}const Y=0,vt=1;var H=t.forwardRef((i,j)=>{var L;const{style:F,offsetTop:Ce,offsetBottom:Ae,prefixCls:he,className:ze,rootClassName:ct,children:St,target:st,onChange:Ke}=i,{getPrefixCls:Ht,getTargetContainer:Ut}=t.useContext($.E_),d=Ht("affix",he),[E,O]=t.useState(!1),[y,N]=t.useState(),[Fe,Je]=t.useState(),Ve=t.useRef(Y),gt=t.useRef(null),ht=t.useRef(),mt=t.useRef(null),It=t.useRef(null),yt=t.useRef(null),ke=(L=st!=null?st:Ut)!==null&&L!==void 0?L:Ge,Ee=Ae===void 0&&Ce===void 0?0:Ce,Zt=()=>{if(Ve.current!==vt||!It.current||!mt.current||!ke)return;const Oe=ke();if(Oe){const xe={status:Y},de=P(mt.current);if(de.top===0&&de.left===0&&de.width===0&&de.height===0)return;const At=P(Oe),Lt=ce(de,At,Ee),Wt=Z(de,At,Ae);Lt!==void 0?(xe.affixStyle={position:"fixed",top:Lt,width:de.width,height:de.height},xe.placeholderStyle={width:de.width,height:de.height}):Wt!==void 0&&(xe.affixStyle={position:"fixed",bottom:Wt,width:de.width,height:de.height},xe.placeholderStyle={width:de.width,height:de.height}),xe.lastAffix=!!xe.affixStyle,E!==xe.lastAffix&&(Ke==null||Ke(xe.lastAffix)),Ve.current=xe.status,N(xe.affixStyle),Je(xe.placeholderStyle),O(xe.lastAffix)}},Ct=()=>{var Oe;Ve.current=vt,Zt()},rt=(0,x.Z)(()=>{Ct()}),Pt=(0,x.Z)(()=>{if(ke&&y){const Oe=ke();if(Oe&&mt.current){const xe=P(Oe),de=P(mt.current),At=ce(de,xe,Ee),Lt=Z(de,xe,Ae);if(At!==void 0&&y.top===At||Lt!==void 0&&y.bottom===Lt)return}}Ct()}),$t=()=>{const Oe=ke==null?void 0:ke();Oe&&(ve.forEach(xe=>{var de;ht.current&&((de=gt.current)===null||de===void 0||de.removeEventListener(xe,ht.current)),Oe==null||Oe.addEventListener(xe,Pt)}),gt.current=Oe,ht.current=Pt)},ot=()=>{yt.current&&(clearTimeout(yt.current),yt.current=null);const Oe=ke==null?void 0:ke();ve.forEach(xe=>{var de;Oe==null||Oe.removeEventListener(xe,Pt),ht.current&&((de=gt.current)===null||de===void 0||de.removeEventListener(xe,ht.current))}),rt.cancel(),Pt.cancel()};t.useImperativeHandle(j,()=>({updatePosition:rt})),t.useEffect(()=>(yt.current=setTimeout($t),()=>ot()),[]),t.useEffect(()=>{$t()},[st,y]),t.useEffect(()=>{rt()},[st,Ce,Ae]);const[xt,jt,nn]=R(d),Ot=o()(ct,jt,d,nn),Bt=o()({[Ot]:y});let Tt=(0,C.Z)(i,["prefixCls","offsetTop","offsetBottom","target","onChange","rootClassName"]);return xt(t.createElement(f.default,{onResize:rt},t.createElement("div",Object.assign({style:F,className:ze,ref:mt},Tt),y&&t.createElement("div",{style:Fe,"aria-hidden":"true"}),t.createElement("div",{className:Bt,ref:It,style:y},t.createElement(f.default,{onResize:rt},St)))))})},85673:function(m,c,a){"use strict";a.d(c,{Z:function(){return Ut}});var t=a(67294),h=a(93967),o=a.n(h),f=a(50344),C=a(64217),x=a(96159),$=a(53124),B=a(13622),X=a(27758);const k=d=>{let{children:E}=d;const{getPrefixCls:O}=t.useContext($.E_),y=O("breadcrumb");return t.createElement("li",{className:`${y}-separator`,"aria-hidden":"true"},E===""?E:E||"/")};k.__ANT_BREADCRUMB_SEPARATOR=!0;var R=k,P=function(d,E){var O={};for(var y in d)Object.prototype.hasOwnProperty.call(d,y)&&E.indexOf(y)<0&&(O[y]=d[y]);if(d!=null&&typeof Object.getOwnPropertySymbols=="function")for(var N=0,y=Object.getOwnPropertySymbols(d);N<y.length;N++)E.indexOf(y[N])<0&&Object.prototype.propertyIsEnumerable.call(d,y[N])&&(O[y[N]]=d[y[N]]);return O};function ce(d,E){if(d.title===void 0||d.title===null)return null;const O=Object.keys(E).join("|");return typeof d.title=="object"?d.title:String(d.title).replace(new RegExp(`:(${O})`,"g"),(y,N)=>E[N]||y)}function Z(d,E,O,y){if(O==null)return null;const{className:N,onClick:Fe}=E,Je=P(E,["className","onClick"]),Ve=Object.assign(Object.assign({},(0,C.Z)(Je,{data:!0,aria:!0})),{onClick:Fe});return y!==void 0?t.createElement("a",Object.assign({},Ve,{className:o()(`${d}-link`,N),href:y}),O):t.createElement("span",Object.assign({},Ve,{className:o()(`${d}-link`,N)}),O)}function ve(d,E){return(y,N,Fe,Je,Ve)=>{if(E)return E(y,N,Fe,Je);const gt=ce(y,N);return Z(d,y,gt,Ve)}}var Ge=function(d,E){var O={};for(var y in d)Object.prototype.hasOwnProperty.call(d,y)&&E.indexOf(y)<0&&(O[y]=d[y]);if(d!=null&&typeof Object.getOwnPropertySymbols=="function")for(var N=0,y=Object.getOwnPropertySymbols(d);N<y.length;N++)E.indexOf(y[N])<0&&Object.prototype.propertyIsEnumerable.call(d,y[N])&&(O[y[N]]=d[y[N]]);return O};const Y=d=>{const{prefixCls:E,separator:O="/",children:y,menu:N,overlay:Fe,dropdownProps:Je,href:Ve}=d,ht=(mt=>{if(N||Fe){const It=Object.assign({},Je);if(N){const yt=N||{},{items:ke}=yt,Ee=Ge(yt,["items"]);It.menu=Object.assign(Object.assign({},Ee),{items:ke==null?void 0:ke.map((Zt,Ct)=>{var{key:rt,title:Pt,label:$t,path:ot}=Zt,xt=Ge(Zt,["key","title","label","path"]);let jt=$t!=null?$t:Pt;return ot&&(jt=t.createElement("a",{href:`${Ve}${ot}`},jt)),Object.assign(Object.assign({},xt),{key:rt!=null?rt:Ct,label:jt})})})}else Fe&&(It.overlay=Fe);return t.createElement(X.Z,Object.assign({placement:"bottom"},It),t.createElement("span",{className:`${E}-overlay-link`},mt,t.createElement(B.Z,null)))}return mt})(y);return ht!=null?t.createElement(t.Fragment,null,t.createElement("li",null,ht),O&&t.createElement(R,null,O)):null},vt=d=>{const{prefixCls:E,children:O,href:y}=d,N=Ge(d,["prefixCls","children","href"]),{getPrefixCls:Fe}=t.useContext($.E_),Je=Fe("breadcrumb",E);return t.createElement(Y,Object.assign({},N,{prefixCls:Je}),Z(Je,N,O,y))};vt.__ANT_BREADCRUMB_ITEM=!0;var G=vt,H=a(85982),i=a(14747),j=a(83559),L=a(83262);const F=d=>{const{componentCls:E,iconCls:O,calc:y}=d;return{[E]:Object.assign(Object.assign({},(0,i.Wf)(d)),{color:d.itemColor,fontSize:d.fontSize,[O]:{fontSize:d.iconFontSize},ol:{display:"flex",flexWrap:"wrap",margin:0,padding:0,listStyle:"none"},a:Object.assign({color:d.linkColor,transition:`color ${d.motionDurationMid}`,padding:`0 ${(0,H.unit)(d.paddingXXS)}`,borderRadius:d.borderRadiusSM,height:d.fontHeight,display:"inline-block",marginInline:y(d.marginXXS).mul(-1).equal(),"&:hover":{color:d.linkHoverColor,backgroundColor:d.colorBgTextHover}},(0,i.Qy)(d)),"li:last-child":{color:d.lastItemColor},[`${E}-separator`]:{marginInline:d.separatorMargin,color:d.separatorColor},[`${E}-link`]:{[`
          > ${O} + span,
          > ${O} + a
        `]:{marginInlineStart:d.marginXXS}},[`${E}-overlay-link`]:{borderRadius:d.borderRadiusSM,height:d.fontHeight,display:"inline-block",padding:`0 ${(0,H.unit)(d.paddingXXS)}`,marginInline:y(d.marginXXS).mul(-1).equal(),[`> ${O}`]:{marginInlineStart:d.marginXXS,fontSize:d.fontSizeIcon},"&:hover":{color:d.linkHoverColor,backgroundColor:d.colorBgTextHover,a:{color:d.linkHoverColor}},a:{"&:hover":{backgroundColor:"transparent"}}},[`&${d.componentCls}-rtl`]:{direction:"rtl"}})}},Ce=d=>({itemColor:d.colorTextDescription,lastItemColor:d.colorText,iconFontSize:d.fontSize,linkColor:d.colorTextDescription,linkHoverColor:d.colorText,separatorColor:d.colorTextDescription,separatorMargin:d.marginXS});var Ae=(0,j.I$)("Breadcrumb",d=>{const E=(0,L.mergeToken)(d,{});return F(E)},Ce),he=function(d,E){var O={};for(var y in d)Object.prototype.hasOwnProperty.call(d,y)&&E.indexOf(y)<0&&(O[y]=d[y]);if(d!=null&&typeof Object.getOwnPropertySymbols=="function")for(var N=0,y=Object.getOwnPropertySymbols(d);N<y.length;N++)E.indexOf(y[N])<0&&Object.prototype.propertyIsEnumerable.call(d,y[N])&&(O[y[N]]=d[y[N]]);return O};function ze(d){const{breadcrumbName:E,children:O}=d,y=he(d,["breadcrumbName","children"]),N=Object.assign({title:E},y);return O&&(N.menu={items:O.map(Fe=>{var{breadcrumbName:Je}=Fe,Ve=he(Fe,["breadcrumbName"]);return Object.assign(Object.assign({},Ve),{title:Je})})}),N}function ct(d,E){return(0,t.useMemo)(()=>d||(E?E.map(ze):null),[d,E])}var St=function(d,E){var O={};for(var y in d)Object.prototype.hasOwnProperty.call(d,y)&&E.indexOf(y)<0&&(O[y]=d[y]);if(d!=null&&typeof Object.getOwnPropertySymbols=="function")for(var N=0,y=Object.getOwnPropertySymbols(d);N<y.length;N++)E.indexOf(y[N])<0&&Object.prototype.propertyIsEnumerable.call(d,y[N])&&(O[y[N]]=d[y[N]]);return O};const st=(d,E)=>{if(E===void 0)return E;let O=(E||"").replace(/^\//,"");return Object.keys(d).forEach(y=>{O=O.replace(`:${y}`,d[y])}),O},Ke=d=>{const{prefixCls:E,separator:O="/",style:y,className:N,rootClassName:Fe,routes:Je,items:Ve,children:gt,itemRender:ht,params:mt={}}=d,It=St(d,["prefixCls","separator","style","className","rootClassName","routes","items","children","itemRender","params"]),{getPrefixCls:yt,direction:ke,breadcrumb:Ee}=t.useContext($.E_);let Zt;const Ct=yt("breadcrumb",E),[rt,Pt,$t]=Ae(Ct),ot=ct(Ve,Je),xt=ve(Ct,ht);if(ot&&ot.length>0){const Ot=[],Bt=Ve||Je;Zt=ot.map((Tt,Oe)=>{const{path:xe,key:de,type:At,menu:Lt,overlay:Wt,onClick:vn,className:Ft,separator:an,dropdownProps:z}=Tt,r=st(mt,xe);r!==void 0&&Ot.push(r);const u=de!=null?de:Oe;if(At==="separator")return t.createElement(R,{key:u},an);const T={},I=Oe===ot.length-1;Lt?T.menu=Lt:Wt&&(T.overlay=Wt);let{href:q}=Tt;return Ot.length&&r!==void 0&&(q=`#/${Ot.join("/")}`),t.createElement(Y,Object.assign({key:u},T,(0,C.Z)(Tt,{data:!0,aria:!0}),{className:Ft,dropdownProps:z,href:q,separator:I?"":O,onClick:vn,prefixCls:Ct}),xt(Tt,mt,Bt,Ot,q))})}else if(gt){const Ot=(0,f.Z)(gt).length;Zt=(0,f.Z)(gt).map((Bt,Tt)=>{if(!Bt)return Bt;const Oe=Tt===Ot-1;return(0,x.Tm)(Bt,{separator:Oe?"":O,key:Tt})})}const jt=o()(Ct,Ee==null?void 0:Ee.className,{[`${Ct}-rtl`]:ke==="rtl"},N,Fe,Pt,$t),nn=Object.assign(Object.assign({},Ee==null?void 0:Ee.style),y);return rt(t.createElement("nav",Object.assign({className:jt,style:nn},It),t.createElement("ol",null,Zt)))};Ke.Item=G,Ke.Separator=R;var Ht=Ke,Ut=Ht},48096:function(m,c,a){"use strict";a.d(c,{Z:function(){return On}});var t=a(67294),h=a(62208),o=a(35872),f=a(87462),C=a(42110),x=a(93771),$=function(n,l){return t.createElement(x.Z,(0,f.Z)({},n,{ref:l,icon:C.Z}))},B=t.forwardRef($),X=B,k=a(93967),R=a.n(k),P=a(4942),ce=a(1413),Z=a(97685),ve=a(71002),Ge=a(45987),Y=a(21770),vt=a(31131),G=(0,t.createContext)(null),H=a(74902),i=a(9220),j=a(66680),L=a(42550),F=a(75164),Ce=function(n){var l=n.activeTabOffset,s=n.horizontal,v=n.rtl,b=n.indicator,p=b===void 0?{}:b,g=p.size,S=p.align,w=S===void 0?"center":S,A=(0,t.useState)(),M=(0,Z.Z)(A,2),V=M[0],Re=M[1],Be=(0,t.useRef)(),ye=t.useCallback(function(Q){return typeof g=="function"?g(Q):typeof g=="number"?g:Q},[g]);function Ne(){F.Z.cancel(Be.current)}return(0,t.useEffect)(function(){var Q={};if(l)if(s){Q.width=ye(l.width);var W=v?"right":"left";w==="start"&&(Q[W]=l[W]),w==="center"&&(Q[W]=l[W]+l.width/2,Q.transform=v?"translateX(50%)":"translateX(-50%)"),w==="end"&&(Q[W]=l[W]+l.width,Q.transform="translateX(-100%)")}else Q.height=ye(l.height),w==="start"&&(Q.top=l.top),w==="center"&&(Q.top=l.top+l.height/2,Q.transform="translateY(-50%)"),w==="end"&&(Q.top=l.top+l.height,Q.transform="translateY(-100%)");return Ne(),Be.current=(0,F.Z)(function(){Re(Q)}),Ne},[l,s,v,w,ye]),{style:V}},Ae=Ce,he={width:0,height:0,left:0,top:0};function ze(e,n,l){return(0,t.useMemo)(function(){for(var s,v=new Map,b=n.get((s=e[0])===null||s===void 0?void 0:s.key)||he,p=b.left+b.width,g=0;g<e.length;g+=1){var S=e[g].key,w=n.get(S);if(!w){var A;w=n.get((A=e[g-1])===null||A===void 0?void 0:A.key)||he}var M=v.get(S)||(0,ce.Z)({},w);M.right=p-M.left-M.width,v.set(S,M)}return v},[e.map(function(s){return s.key}).join("_"),n,l])}function ct(e,n){var l=t.useRef(e),s=t.useState({}),v=(0,Z.Z)(s,2),b=v[1];function p(g){var S=typeof g=="function"?g(l.current):g;S!==l.current&&n(S,l.current),l.current=S,b({})}return[l.current,p]}var St=.1,st=.01,Ke=20,Ht=Math.pow(.995,Ke);function Ut(e,n){var l=(0,t.useState)(),s=(0,Z.Z)(l,2),v=s[0],b=s[1],p=(0,t.useState)(0),g=(0,Z.Z)(p,2),S=g[0],w=g[1],A=(0,t.useState)(0),M=(0,Z.Z)(A,2),V=M[0],Re=M[1],Be=(0,t.useState)(),ye=(0,Z.Z)(Be,2),Ne=ye[0],Q=ye[1],W=(0,t.useRef)();function Te(re){var we=re.touches[0],le=we.screenX,se=we.screenY;b({x:le,y:se}),window.clearInterval(W.current)}function Ze(re){if(v){var we=re.touches[0],le=we.screenX,se=we.screenY;b({x:le,y:se});var ee=le-v.x,ue=se-v.y;n(ee,ue);var dt=Date.now();w(dt),Re(dt-S),Q({x:ee,y:ue})}}function De(){if(v&&(b(null),Q(null),Ne)){var re=Ne.x/V,we=Ne.y/V,le=Math.abs(re),se=Math.abs(we);if(Math.max(le,se)<St)return;var ee=re,ue=we;W.current=window.setInterval(function(){if(Math.abs(ee)<st&&Math.abs(ue)<st){window.clearInterval(W.current);return}ee*=Ht,ue*=Ht,n(ee*Ke,ue*Ke)},Ke)}}var _e=(0,t.useRef)();function He(re){var we=re.deltaX,le=re.deltaY,se=0,ee=Math.abs(we),ue=Math.abs(le);ee===ue?se=_e.current==="x"?we:le:ee>ue?(se=we,_e.current="x"):(se=le,_e.current="y"),n(-se,-se)&&re.preventDefault()}var ae=(0,t.useRef)(null);ae.current={onTouchStart:Te,onTouchMove:Ze,onTouchEnd:De,onWheel:He},t.useEffect(function(){function re(ee){ae.current.onTouchStart(ee)}function we(ee){ae.current.onTouchMove(ee)}function le(ee){ae.current.onTouchEnd(ee)}function se(ee){ae.current.onWheel(ee)}return document.addEventListener("touchmove",we,{passive:!1}),document.addEventListener("touchend",le,{passive:!0}),e.current.addEventListener("touchstart",re,{passive:!0}),e.current.addEventListener("wheel",se,{passive:!1}),function(){document.removeEventListener("touchmove",we),document.removeEventListener("touchend",le)}},[])}var d=a(8410);function E(e){var n=(0,t.useState)(0),l=(0,Z.Z)(n,2),s=l[0],v=l[1],b=(0,t.useRef)(0),p=(0,t.useRef)();return p.current=e,(0,d.o)(function(){var g;(g=p.current)===null||g===void 0||g.call(p)},[s]),function(){b.current===s&&(b.current+=1,v(b.current))}}function O(e){var n=(0,t.useRef)([]),l=(0,t.useState)({}),s=(0,Z.Z)(l,2),v=s[1],b=(0,t.useRef)(typeof e=="function"?e():e),p=E(function(){var S=b.current;n.current.forEach(function(w){S=w(S)}),n.current=[],b.current=S,v({})});function g(S){n.current.push(S),p()}return[b.current,g]}var y={width:0,height:0,left:0,top:0,right:0};function N(e,n,l,s,v,b,p){var g=p.tabs,S=p.tabPosition,w=p.rtl,A,M,V;return["top","bottom"].includes(S)?(A="width",M=w?"right":"left",V=Math.abs(l)):(A="height",M="top",V=-l),(0,t.useMemo)(function(){if(!g.length)return[0,0];for(var Re=g.length,Be=Re,ye=0;ye<Re;ye+=1){var Ne=e.get(g[ye].key)||y;if(Math.floor(Ne[M]+Ne[A])>Math.floor(V+n)){Be=ye-1;break}}for(var Q=0,W=Re-1;W>=0;W-=1){var Te=e.get(g[W].key)||y;if(Te[M]<V){Q=W+1;break}}return Q>=Be?[0,0]:[Q,Be]},[e,n,s,v,b,V,S,g.map(function(Re){return Re.key}).join("_"),w])}function Fe(e){var n;return e instanceof Map?(n={},e.forEach(function(l,s){n[s]=l})):n=e,JSON.stringify(n)}var Je="TABS_DQ";function Ve(e){return String(e).replace(/"/g,Je)}function gt(e,n,l,s){return!(!l||s||e===!1||e===void 0&&(n===!1||n===null))}var ht=t.forwardRef(function(e,n){var l=e.prefixCls,s=e.editable,v=e.locale,b=e.style;return!s||s.showAdd===!1?null:t.createElement("button",{ref:n,type:"button",className:"".concat(l,"-nav-add"),style:b,"aria-label":(v==null?void 0:v.addAriaLabel)||"Add tab",onClick:function(g){s.onEdit("add",{event:g})}},s.addIcon||"+")}),mt=ht,It=t.forwardRef(function(e,n){var l=e.position,s=e.prefixCls,v=e.extra;if(!v)return null;var b,p={};return(0,ve.Z)(v)==="object"&&!t.isValidElement(v)?p=v:p.right=v,l==="right"&&(b=p.right),l==="left"&&(b=p.left),b?t.createElement("div",{className:"".concat(s,"-extra-content"),ref:n},b):null}),yt=It,ke=a(40228),Ee=a(15105),Zt=Ee.Z.ESC,Ct=Ee.Z.TAB;function rt(e){var n=e.visible,l=e.triggerRef,s=e.onVisibleChange,v=e.autoFocus,b=e.overlayRef,p=t.useRef(!1),g=function(){if(n){var M,V;(M=l.current)===null||M===void 0||(V=M.focus)===null||V===void 0||V.call(M),s==null||s(!1)}},S=function(){var M;return(M=b.current)!==null&&M!==void 0&&M.focus?(b.current.focus(),p.current=!0,!0):!1},w=function(M){switch(M.keyCode){case Zt:g();break;case Ct:{var V=!1;p.current||(V=S()),V?M.preventDefault():g();break}}};t.useEffect(function(){return n?(window.addEventListener("keydown",w),v&&(0,F.Z)(S,3),function(){window.removeEventListener("keydown",w),p.current=!1}):function(){p.current=!1}},[n])}var Pt=(0,t.forwardRef)(function(e,n){var l=e.overlay,s=e.arrow,v=e.prefixCls,b=(0,t.useMemo)(function(){var g;return typeof l=="function"?g=l():g=l,g},[l]),p=(0,L.sQ)(n,b==null?void 0:b.ref);return t.createElement(t.Fragment,null,s&&t.createElement("div",{className:"".concat(v,"-arrow")}),t.cloneElement(b,{ref:(0,L.Yr)(b)?p:void 0}))}),$t=Pt,ot={adjustX:1,adjustY:1},xt=[0,0],jt={topLeft:{points:["bl","tl"],overflow:ot,offset:[0,-4],targetOffset:xt},top:{points:["bc","tc"],overflow:ot,offset:[0,-4],targetOffset:xt},topRight:{points:["br","tr"],overflow:ot,offset:[0,-4],targetOffset:xt},bottomLeft:{points:["tl","bl"],overflow:ot,offset:[0,4],targetOffset:xt},bottom:{points:["tc","bc"],overflow:ot,offset:[0,4],targetOffset:xt},bottomRight:{points:["tr","br"],overflow:ot,offset:[0,4],targetOffset:xt}},nn=jt,Ot=["arrow","prefixCls","transitionName","animation","align","placement","placements","getPopupContainer","showAction","hideAction","overlayClassName","overlayStyle","visible","trigger","autoFocus","overlay","children","onVisibleChange"];function Bt(e,n){var l,s=e.arrow,v=s===void 0?!1:s,b=e.prefixCls,p=b===void 0?"rc-dropdown":b,g=e.transitionName,S=e.animation,w=e.align,A=e.placement,M=A===void 0?"bottomLeft":A,V=e.placements,Re=V===void 0?nn:V,Be=e.getPopupContainer,ye=e.showAction,Ne=e.hideAction,Q=e.overlayClassName,W=e.overlayStyle,Te=e.visible,Ze=e.trigger,De=Ze===void 0?["hover"]:Ze,_e=e.autoFocus,He=e.overlay,ae=e.children,re=e.onVisibleChange,we=(0,Ge.Z)(e,Ot),le=t.useState(),se=(0,Z.Z)(le,2),ee=se[0],ue=se[1],dt="visible"in e?Te:ee,Dt=t.useRef(null),J=t.useRef(null),lt=t.useRef(null);t.useImperativeHandle(n,function(){return Dt.current});var Mt=function(Xe){ue(Xe),re==null||re(Xe)};rt({visible:dt,triggerRef:lt,onVisibleChange:Mt,autoFocus:_e,overlayRef:J});var et=function(Xe){var ft=e.onOverlayClick;ue(!1),ft&&ft(Xe)},ut=function(){return t.createElement($t,{ref:J,overlay:He,prefixCls:p,arrow:v})},Vt=function(){return typeof He=="function"?ut:ut()},ge=function(){var Xe=e.minOverlayWidthMatchTrigger,ft=e.alignPoint;return"minOverlayWidthMatchTrigger"in e?Xe:!ft},Le=function(){var Xe=e.openClassName;return Xe!==void 0?Xe:"".concat(p,"-open")},tt=t.cloneElement(ae,{className:R()((l=ae.props)===null||l===void 0?void 0:l.className,dt&&Le()),ref:(0,L.Yr)(ae)?(0,L.sQ)(lt,ae.ref):void 0}),nt=Ne;return!nt&&De.indexOf("contextMenu")!==-1&&(nt=["click"]),t.createElement(ke.Z,(0,f.Z)({builtinPlacements:Re},we,{prefixCls:p,ref:Dt,popupClassName:R()(Q,(0,P.Z)({},"".concat(p,"-show-arrow"),v)),popupStyle:W,action:De,showAction:ye,hideAction:nt,popupPlacement:M,popupAlign:w,popupTransitionName:g,popupAnimation:S,popupVisible:dt,stretch:ge()?"minWidth":"",popup:Vt(),onPopupVisibleChange:Mt,onPopupClick:et,getPopupContainer:Be}),tt)}var Tt=t.forwardRef(Bt),Oe=Tt,xe=a(72512),de=t.forwardRef(function(e,n){var l=e.prefixCls,s=e.id,v=e.tabs,b=e.locale,p=e.mobile,g=e.more,S=g===void 0?{}:g,w=e.style,A=e.className,M=e.editable,V=e.tabBarGutter,Re=e.rtl,Be=e.removeAriaLabel,ye=e.onTabClick,Ne=e.getPopupContainer,Q=e.popupClassName,W=(0,t.useState)(!1),Te=(0,Z.Z)(W,2),Ze=Te[0],De=Te[1],_e=(0,t.useState)(null),He=(0,Z.Z)(_e,2),ae=He[0],re=He[1],we=S.icon,le=we===void 0?"More":we,se="".concat(s,"-more-popup"),ee="".concat(l,"-dropdown"),ue=ae!==null?"".concat(se,"-").concat(ae):null,dt=b==null?void 0:b.dropdownAriaLabel;function Dt(ge,Le){ge.preventDefault(),ge.stopPropagation(),M.onEdit("remove",{key:Le,event:ge})}var J=t.createElement(xe.ZP,{onClick:function(Le){var tt=Le.key,nt=Le.domEvent;ye(tt,nt),De(!1)},prefixCls:"".concat(ee,"-menu"),id:se,tabIndex:-1,role:"listbox","aria-activedescendant":ue,selectedKeys:[ae],"aria-label":dt!==void 0?dt:"expanded dropdown"},v.map(function(ge){var Le=ge.closable,tt=ge.disabled,nt=ge.closeIcon,Qe=ge.key,Xe=ge.label,ft=gt(Le,nt,M,tt);return t.createElement(xe.sN,{key:Qe,id:"".concat(se,"-").concat(Qe),role:"option","aria-controls":s&&"".concat(s,"-panel-").concat(Qe),disabled:tt},t.createElement("span",null,Xe),ft&&t.createElement("button",{type:"button","aria-label":Be||"remove",tabIndex:0,className:"".concat(ee,"-menu-item-remove"),onClick:function(qt){qt.stopPropagation(),Dt(qt,Qe)}},nt||M.removeIcon||"\xD7"))}));function lt(ge){for(var Le=v.filter(function(ft){return!ft.disabled}),tt=Le.findIndex(function(ft){return ft.key===ae})||0,nt=Le.length,Qe=0;Qe<nt;Qe+=1){tt=(tt+ge+nt)%nt;var Xe=Le[tt];if(!Xe.disabled){re(Xe.key);return}}}function Mt(ge){var Le=ge.which;if(!Ze){[Ee.Z.DOWN,Ee.Z.SPACE,Ee.Z.ENTER].includes(Le)&&(De(!0),ge.preventDefault());return}switch(Le){case Ee.Z.UP:lt(-1),ge.preventDefault();break;case Ee.Z.DOWN:lt(1),ge.preventDefault();break;case Ee.Z.ESC:De(!1);break;case Ee.Z.SPACE:case Ee.Z.ENTER:ae!==null&&ye(ae,ge);break}}(0,t.useEffect)(function(){var ge=document.getElementById(ue);ge&&ge.scrollIntoView&&ge.scrollIntoView(!1)},[ae]),(0,t.useEffect)(function(){Ze||re(null)},[Ze]);var et=(0,P.Z)({},Re?"marginRight":"marginLeft",V);v.length||(et.visibility="hidden",et.order=1);var ut=R()((0,P.Z)({},"".concat(ee,"-rtl"),Re)),Vt=p?null:t.createElement(Oe,(0,f.Z)({prefixCls:ee,overlay:J,visible:v.length?Ze:!1,onVisibleChange:De,overlayClassName:R()(ut,Q),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:Ne},S),t.createElement("button",{type:"button",className:"".concat(l,"-nav-more"),style:et,tabIndex:-1,"aria-hidden":"true","aria-haspopup":"listbox","aria-controls":se,id:"".concat(s,"-more"),"aria-expanded":Ze,onKeyDown:Mt},le));return t.createElement("div",{className:R()("".concat(l,"-nav-operations"),A),style:w,ref:n},Vt,t.createElement(mt,{prefixCls:l,locale:b,editable:M}))}),At=t.memo(de,function(e,n){return n.tabMoving}),Lt=function(n){var l=n.prefixCls,s=n.id,v=n.active,b=n.tab,p=b.key,g=b.label,S=b.disabled,w=b.closeIcon,A=b.icon,M=n.closable,V=n.renderWrapper,Re=n.removeAriaLabel,Be=n.editable,ye=n.onClick,Ne=n.onFocus,Q=n.style,W="".concat(l,"-tab"),Te=gt(M,w,Be,S);function Ze(ae){S||ye(ae)}function De(ae){ae.preventDefault(),ae.stopPropagation(),Be.onEdit("remove",{key:p,event:ae})}var _e=t.useMemo(function(){return A&&typeof g=="string"?t.createElement("span",null,g):g},[g,A]),He=t.createElement("div",{key:p,"data-node-key":Ve(p),className:R()(W,(0,P.Z)((0,P.Z)((0,P.Z)({},"".concat(W,"-with-remove"),Te),"".concat(W,"-active"),v),"".concat(W,"-disabled"),S)),style:Q,onClick:Ze},t.createElement("div",{role:"tab","aria-selected":v,id:s&&"".concat(s,"-tab-").concat(p),className:"".concat(W,"-btn"),"aria-controls":s&&"".concat(s,"-panel-").concat(p),"aria-disabled":S,tabIndex:S?null:0,onClick:function(re){re.stopPropagation(),Ze(re)},onKeyDown:function(re){[Ee.Z.SPACE,Ee.Z.ENTER].includes(re.which)&&(re.preventDefault(),Ze(re))},onFocus:Ne},A&&t.createElement("span",{className:"".concat(W,"-icon")},A),g&&_e),Te&&t.createElement("button",{type:"button","aria-label":Re||"remove",tabIndex:0,className:"".concat(W,"-remove"),onClick:function(re){re.stopPropagation(),De(re)}},w||Be.removeIcon||"\xD7"));return V?V(He):He},Wt=Lt,vn=function(n,l){var s=n.offsetWidth,v=n.offsetHeight,b=n.offsetTop,p=n.offsetLeft,g=n.getBoundingClientRect(),S=g.width,w=g.height,A=g.left,M=g.top;return Math.abs(S-s)<1?[S,w,A-l.left,M-l.top]:[s,v,p,b]},Ft=function(n){var l=n.current||{},s=l.offsetWidth,v=s===void 0?0:s,b=l.offsetHeight,p=b===void 0?0:b;if(n.current){var g=n.current.getBoundingClientRect(),S=g.width,w=g.height;if(Math.abs(S-v)<1)return[S,w]}return[v,p]},an=function(n,l){return n[l?0:1]},z=t.forwardRef(function(e,n){var l=e.className,s=e.style,v=e.id,b=e.animated,p=e.activeKey,g=e.rtl,S=e.extra,w=e.editable,A=e.locale,M=e.tabPosition,V=e.tabBarGutter,Re=e.children,Be=e.onTabClick,ye=e.onTabScroll,Ne=e.indicator,Q=t.useContext(G),W=Q.prefixCls,Te=Q.tabs,Ze=(0,t.useRef)(null),De=(0,t.useRef)(null),_e=(0,t.useRef)(null),He=(0,t.useRef)(null),ae=(0,t.useRef)(null),re=(0,t.useRef)(null),we=(0,t.useRef)(null),le=M==="top"||M==="bottom",se=ct(0,function(We,fe){le&&ye&&ye({direction:We>fe?"left":"right"})}),ee=(0,Z.Z)(se,2),ue=ee[0],dt=ee[1],Dt=ct(0,function(We,fe){!le&&ye&&ye({direction:We>fe?"top":"bottom"})}),J=(0,Z.Z)(Dt,2),lt=J[0],Mt=J[1],et=(0,t.useState)([0,0]),ut=(0,Z.Z)(et,2),Vt=ut[0],ge=ut[1],Le=(0,t.useState)([0,0]),tt=(0,Z.Z)(Le,2),nt=tt[0],Qe=tt[1],Xe=(0,t.useState)([0,0]),ft=(0,Z.Z)(Xe,2),un=ft[0],qt=ft[1],ln=(0,t.useState)([0,0]),fn=(0,Z.Z)(ln,2),Me=fn[0],Qt=fn[1],mn=O(new Map),Hn=(0,Z.Z)(mn,2),ta=Hn[0],na=Hn[1],xn=ze(Te,ta,nt[0]),Nn=an(Vt,le),gn=an(nt,le),Mn=an(un,le),Wn=an(Me,le),Fn=Math.floor(Nn)<Math.floor(gn+Mn),kt=Fn?Nn-Wn:Nn-Mn,aa="".concat(W,"-nav-operations-hidden"),en=0,cn=0;le&&g?(en=0,cn=Math.max(0,gn-kt)):(en=Math.min(0,kt-gn),cn=0);function jn(We){return We<en?en:We>cn?cn:We}var Bn=(0,t.useRef)(null),ra=(0,t.useState)(),Xn=(0,Z.Z)(ra,2),Sn=Xn[0],Gn=Xn[1];function An(){Gn(Date.now())}function Ln(){Bn.current&&clearTimeout(Bn.current)}Ut(He,function(We,fe){function at(Et,sn){Et(function(tn){var Tn=jn(tn+sn);return Tn})}return Fn?(le?at(dt,We):at(Mt,fe),Ln(),An(),!0):!1}),(0,t.useEffect)(function(){return Ln(),Sn&&(Bn.current=setTimeout(function(){Gn(0)},100)),Ln},[Sn]);var oa=N(xn,kt,le?ue:lt,gn,Mn,Wn,(0,ce.Z)((0,ce.Z)({},e),{},{tabs:Te})),Kn=(0,Z.Z)(oa,2),ia=Kn[0],la=Kn[1],Vn=(0,j.Z)(function(){var We=arguments.length>0&&arguments[0]!==void 0?arguments[0]:p,fe=xn.get(We)||{width:0,height:0,left:0,right:0,top:0};if(le){var at=ue;g?fe.right<ue?at=fe.right:fe.right+fe.width>ue+kt&&(at=fe.right+fe.width-kt):fe.left<-ue?at=-fe.left:fe.left+fe.width>-ue+kt&&(at=-(fe.left+fe.width-kt)),Mt(0),dt(jn(at))}else{var Et=lt;fe.top<-lt?Et=-fe.top:fe.top+fe.height>-lt+kt&&(Et=-(fe.top+fe.height-kt)),dt(0),Mt(jn(Et))}}),Pn={};M==="top"||M==="bottom"?Pn[g?"marginRight":"marginLeft"]=V:Pn.marginTop=V;var kn=Te.map(function(We,fe){var at=We.key;return t.createElement(Wt,{id:v,prefixCls:W,key:at,tab:We,style:fe===0?void 0:Pn,closable:We.closable,editable:w,active:at===p,renderWrapper:Re,removeAriaLabel:A==null?void 0:A.removeAriaLabel,onClick:function(sn){Be(at,sn)},onFocus:function(){Vn(at),An(),He.current&&(g||(He.current.scrollLeft=0),He.current.scrollTop=0)}})}),Un=function(){return na(function(){var fe,at=new Map,Et=(fe=ae.current)===null||fe===void 0?void 0:fe.getBoundingClientRect();return Te.forEach(function(sn){var tn,Tn=sn.key,ea=(tn=ae.current)===null||tn===void 0?void 0:tn.querySelector('[data-node-key="'.concat(Ve(Tn),'"]'));if(ea){var fa=vn(ea,Et),Rn=(0,Z.Z)(fa,4),va=Rn[0],ma=Rn[1],ga=Rn[2],ha=Rn[3];at.set(Tn,{width:va,height:ma,left:ga,top:ha})}}),at})};(0,t.useEffect)(function(){Un()},[Te.map(function(We){return We.key}).join("_")]);var $n=E(function(){var We=Ft(Ze),fe=Ft(De),at=Ft(_e);ge([We[0]-fe[0]-at[0],We[1]-fe[1]-at[1]]);var Et=Ft(we);qt(Et);var sn=Ft(re);Qt(sn);var tn=Ft(ae);Qe([tn[0]-Et[0],tn[1]-Et[1]]),Un()}),ca=Te.slice(0,ia),sa=Te.slice(la+1),Yn=[].concat((0,H.Z)(ca),(0,H.Z)(sa)),_n=xn.get(p),da=Ae({activeTabOffset:_n,horizontal:le,indicator:Ne,rtl:g}),ua=da.style;(0,t.useEffect)(function(){Vn()},[p,en,cn,Fe(_n),Fe(xn),le]),(0,t.useEffect)(function(){$n()},[g]);var Qn=!!Yn.length,hn="".concat(W,"-nav-wrap"),zn,Dn,Jn,qn;return le?g?(Dn=ue>0,zn=ue!==cn):(zn=ue<0,Dn=ue!==en):(Jn=lt<0,qn=lt!==en),t.createElement(i.default,{onResize:$n},t.createElement("div",{ref:(0,L.x1)(n,Ze),role:"tablist",className:R()("".concat(W,"-nav"),l),style:s,onKeyDown:function(){An()}},t.createElement(yt,{ref:De,position:"left",extra:S,prefixCls:W}),t.createElement(i.default,{onResize:$n},t.createElement("div",{className:R()(hn,(0,P.Z)((0,P.Z)((0,P.Z)((0,P.Z)({},"".concat(hn,"-ping-left"),zn),"".concat(hn,"-ping-right"),Dn),"".concat(hn,"-ping-top"),Jn),"".concat(hn,"-ping-bottom"),qn)),ref:He},t.createElement(i.default,{onResize:$n},t.createElement("div",{ref:ae,className:"".concat(W,"-nav-list"),style:{transform:"translate(".concat(ue,"px, ").concat(lt,"px)"),transition:Sn?"none":void 0}},kn,t.createElement(mt,{ref:we,prefixCls:W,locale:A,editable:w,style:(0,ce.Z)((0,ce.Z)({},kn.length===0?void 0:Pn),{},{visibility:Qn?"hidden":null})}),t.createElement("div",{className:R()("".concat(W,"-ink-bar"),(0,P.Z)({},"".concat(W,"-ink-bar-animated"),b.inkBar)),style:ua}))))),t.createElement(At,(0,f.Z)({},e,{removeAriaLabel:A==null?void 0:A.removeAriaLabel,ref:re,prefixCls:W,tabs:Yn,className:!Qn&&aa,tabMoving:!!Sn})),t.createElement(yt,{ref:_e,position:"right",extra:S,prefixCls:W})))}),r=z,u=t.forwardRef(function(e,n){var l=e.prefixCls,s=e.className,v=e.style,b=e.id,p=e.active,g=e.tabKey,S=e.children;return t.createElement("div",{id:b&&"".concat(b,"-panel-").concat(g),role:"tabpanel",tabIndex:p?0:-1,"aria-labelledby":b&&"".concat(b,"-tab-").concat(g),"aria-hidden":!p,style:v,className:R()(l,p&&"".concat(l,"-active"),s),ref:n},S)}),T=u,I=["renderTabBar"],q=["label","key"],oe=function(n){var l=n.renderTabBar,s=(0,Ge.Z)(n,I),v=t.useContext(G),b=v.tabs;if(l){var p=(0,ce.Z)((0,ce.Z)({},s),{},{panes:b.map(function(g){var S=g.label,w=g.key,A=(0,Ge.Z)(g,q);return t.createElement(T,(0,f.Z)({tab:S,key:w,tabKey:w},A))})});return l(p,r)}return t.createElement(r,s)},be=oe,Ie=a(29372),te=["key","forceRender","style","className","destroyInactiveTabPane"],me=function(n){var l=n.id,s=n.activeKey,v=n.animated,b=n.tabPosition,p=n.destroyInactiveTabPane,g=t.useContext(G),S=g.prefixCls,w=g.tabs,A=v.tabPane,M="".concat(S,"-tabpane");return t.createElement("div",{className:R()("".concat(S,"-content-holder"))},t.createElement("div",{className:R()("".concat(S,"-content"),"".concat(S,"-content-").concat(b),(0,P.Z)({},"".concat(S,"-content-animated"),A))},w.map(function(V){var Re=V.key,Be=V.forceRender,ye=V.style,Ne=V.className,Q=V.destroyInactiveTabPane,W=(0,Ge.Z)(V,te),Te=Re===s;return t.createElement(Ie.default,(0,f.Z)({key:Re,visible:Te,forceRender:Be,removeOnLeave:!!(p||Q),leavedClassName:"".concat(M,"-hidden")},v.tabPaneMotion),function(Ze,De){var _e=Ze.style,He=Ze.className;return t.createElement(T,(0,f.Z)({},W,{prefixCls:M,id:l,tabKey:Re,animated:A,active:Te,style:(0,ce.Z)((0,ce.Z)({},ye),_e),className:R()(Ne,He),ref:De}))})})))},ie=me,Se=a(80334);function pe(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{inkBar:!0,tabPane:!1},n;return e===!1?n={inkBar:!1,tabPane:!1}:e===!0?n={inkBar:!0,tabPane:!1}:n=(0,ce.Z)({inkBar:!0},(0,ve.Z)(e)==="object"?e:{}),n.tabPaneMotion&&n.tabPane===void 0&&(n.tabPane=!0),!n.tabPaneMotion&&n.tabPane&&(n.tabPane=!1),n}var ne=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],K=0,Pe=t.forwardRef(function(e,n){var l=e.id,s=e.prefixCls,v=s===void 0?"rc-tabs":s,b=e.className,p=e.items,g=e.direction,S=e.activeKey,w=e.defaultActiveKey,A=e.editable,M=e.animated,V=e.tabPosition,Re=V===void 0?"top":V,Be=e.tabBarGutter,ye=e.tabBarStyle,Ne=e.tabBarExtraContent,Q=e.locale,W=e.more,Te=e.destroyInactiveTabPane,Ze=e.renderTabBar,De=e.onChange,_e=e.onTabClick,He=e.onTabScroll,ae=e.getPopupContainer,re=e.popupClassName,we=e.indicator,le=(0,Ge.Z)(e,ne),se=t.useMemo(function(){return(p||[]).filter(function(Me){return Me&&(0,ve.Z)(Me)==="object"&&"key"in Me})},[p]),ee=g==="rtl",ue=pe(M),dt=(0,t.useState)(!1),Dt=(0,Z.Z)(dt,2),J=Dt[0],lt=Dt[1];(0,t.useEffect)(function(){lt((0,vt.Z)())},[]);var Mt=(0,Y.Z)(function(){var Me;return(Me=se[0])===null||Me===void 0?void 0:Me.key},{value:S,defaultValue:w}),et=(0,Z.Z)(Mt,2),ut=et[0],Vt=et[1],ge=(0,t.useState)(function(){return se.findIndex(function(Me){return Me.key===ut})}),Le=(0,Z.Z)(ge,2),tt=Le[0],nt=Le[1];(0,t.useEffect)(function(){var Me=se.findIndex(function(mn){return mn.key===ut});if(Me===-1){var Qt;Me=Math.max(0,Math.min(tt,se.length-1)),Vt((Qt=se[Me])===null||Qt===void 0?void 0:Qt.key)}nt(Me)},[se.map(function(Me){return Me.key}).join("_"),ut,tt]);var Qe=(0,Y.Z)(null,{value:l}),Xe=(0,Z.Z)(Qe,2),ft=Xe[0],un=Xe[1];(0,t.useEffect)(function(){l||(un("rc-tabs-".concat(K)),K+=1)},[]);function qt(Me,Qt){_e==null||_e(Me,Qt);var mn=Me!==ut;Vt(Me),mn&&(De==null||De(Me))}var ln={id:ft,activeKey:ut,animated:ue,tabPosition:Re,rtl:ee,mobile:J},fn=(0,ce.Z)((0,ce.Z)({},ln),{},{editable:A,locale:Q,more:W,tabBarGutter:Be,onTabClick:qt,onTabScroll:He,extra:Ne,style:ye,panes:null,getPopupContainer:ae,popupClassName:re,indicator:we});return t.createElement(G.Provider,{value:{tabs:se,prefixCls:v}},t.createElement("div",(0,f.Z)({ref:n,id:l,className:R()(v,"".concat(v,"-").concat(Re),(0,P.Z)((0,P.Z)((0,P.Z)({},"".concat(v,"-mobile"),J),"".concat(v,"-editable"),A),"".concat(v,"-rtl"),ee),b)},le),t.createElement(be,(0,f.Z)({},fn,{renderTabBar:Ze})),t.createElement(ie,(0,f.Z)({destroyInactiveTabPane:Te},ln,{animated:ue}))))}),U=Pe,_=U,Ue=a(53124),Ye=a(35792),Rt=a(98675),$e=a(33603);const je={motionAppear:!1,motionEnter:!0,motionLeave:!0};function zt(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{inkBar:!0,tabPane:!1},l;return n===!1?l={inkBar:!1,tabPane:!1}:n===!0?l={inkBar:!0,tabPane:!0}:l=Object.assign({inkBar:!0},typeof n=="object"?n:{}),l.tabPane&&(l.tabPaneMotion=Object.assign(Object.assign({},je),{motionName:(0,$e.m)(e,"switch")})),l}var it=a(50344),bt=function(e,n){var l={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&n.indexOf(s)<0&&(l[s]=e[s]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var v=0,s=Object.getOwnPropertySymbols(e);v<s.length;v++)n.indexOf(s[v])<0&&Object.prototype.propertyIsEnumerable.call(e,s[v])&&(l[s[v]]=e[s[v]]);return l};function Nt(e){return e.filter(n=>n)}function Jt(e,n){if(e)return e;const l=(0,it.Z)(n).map(s=>{if(t.isValidElement(s)){const{key:v,props:b}=s,p=b||{},{tab:g}=p,S=bt(p,["tab"]);return Object.assign(Object.assign({key:String(v)},S),{label:g})}return null});return Nt(l)}var D=a(85982),pt=a(14747),Yt=a(83559),Xt=a(83262),Gt=a(48611),wt=e=>{const{componentCls:n,motionDurationSlow:l}=e;return[{[n]:{[`${n}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${l}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${l}`}}}}},[(0,Gt.oN)(e,"slide-up"),(0,Gt.oN)(e,"slide-down")]]};const rn=e=>{const{componentCls:n,tabsCardPadding:l,cardBg:s,cardGutter:v,colorBorderSecondary:b,itemSelectedColor:p}=e;return{[`${n}-card`]:{[`> ${n}-nav, > div > ${n}-nav`]:{[`${n}-tab`]:{margin:0,padding:l,background:s,border:`${(0,D.unit)(e.lineWidth)} ${e.lineType} ${b}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${n}-tab-active`]:{color:p,background:e.colorBgContainer},[`${n}-ink-bar`]:{visibility:"hidden"}},[`&${n}-top, &${n}-bottom`]:{[`> ${n}-nav, > div > ${n}-nav`]:{[`${n}-tab + ${n}-tab`]:{marginLeft:{_skip_check_:!0,value:(0,D.unit)(v)}}}},[`&${n}-top`]:{[`> ${n}-nav, > div > ${n}-nav`]:{[`${n}-tab`]:{borderRadius:`${(0,D.unit)(e.borderRadiusLG)} ${(0,D.unit)(e.borderRadiusLG)} 0 0`},[`${n}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${n}-bottom`]:{[`> ${n}-nav, > div > ${n}-nav`]:{[`${n}-tab`]:{borderRadius:`0 0 ${(0,D.unit)(e.borderRadiusLG)} ${(0,D.unit)(e.borderRadiusLG)}`},[`${n}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${n}-left, &${n}-right`]:{[`> ${n}-nav, > div > ${n}-nav`]:{[`${n}-tab + ${n}-tab`]:{marginTop:(0,D.unit)(v)}}},[`&${n}-left`]:{[`> ${n}-nav, > div > ${n}-nav`]:{[`${n}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,D.unit)(e.borderRadiusLG)} 0 0 ${(0,D.unit)(e.borderRadiusLG)}`}},[`${n}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${n}-right`]:{[`> ${n}-nav, > div > ${n}-nav`]:{[`${n}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,D.unit)(e.borderRadiusLG)} ${(0,D.unit)(e.borderRadiusLG)} 0`}},[`${n}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},dn=e=>{const{componentCls:n,itemHoverColor:l,dropdownEdgeChildVerticalPadding:s}=e;return{[`${n}-dropdown`]:Object.assign(Object.assign({},(0,pt.Wf)(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${n}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${(0,D.unit)(s)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},pt.vS),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${(0,D.unit)(e.paddingXXS)} ${(0,D.unit)(e.paddingSM)}`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:l}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},wn=e=>{const{componentCls:n,margin:l,colorBorderSecondary:s,horizontalMargin:v,verticalItemPadding:b,verticalItemMargin:p,calc:g}=e;return{[`${n}-top, ${n}-bottom`]:{flexDirection:"column",[`> ${n}-nav, > div > ${n}-nav`]:{margin:v,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${(0,D.unit)(e.lineWidth)} ${e.lineType} ${s}`,content:"''"},[`${n}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},
            right ${e.motionDurationSlow}`}},[`${n}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${n}-nav-wrap-ping-left::before`]:{opacity:1},[`&${n}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${n}-top`]:{[`> ${n}-nav,
        > div > ${n}-nav`]:{"&::before":{bottom:0},[`${n}-ink-bar`]:{bottom:0}}},[`${n}-bottom`]:{[`> ${n}-nav, > div > ${n}-nav`]:{order:1,marginTop:l,marginBottom:0,"&::before":{top:0},[`${n}-ink-bar`]:{top:0}},[`> ${n}-content-holder, > div > ${n}-content-holder`]:{order:0}},[`${n}-left, ${n}-right`]:{[`> ${n}-nav, > div > ${n}-nav`]:{flexDirection:"column",minWidth:g(e.controlHeight).mul(1.25).equal(),[`${n}-tab`]:{padding:b,textAlign:"center"},[`${n}-tab + ${n}-tab`]:{margin:p},[`${n}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${n}-nav-wrap-ping-top::before`]:{opacity:1},[`&${n}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${n}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${n}-nav-list, ${n}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${n}-left`]:{[`> ${n}-nav, > div > ${n}-nav`]:{[`${n}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${n}-content-holder, > div > ${n}-content-holder`]:{marginLeft:{_skip_check_:!0,value:(0,D.unit)(g(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${(0,D.unit)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${n}-content > ${n}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${n}-right`]:{[`> ${n}-nav, > div > ${n}-nav`]:{order:1,[`${n}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${n}-content-holder, > div > ${n}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:g(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${(0,D.unit)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${n}-content > ${n}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},En=e=>{const{componentCls:n,cardPaddingSM:l,cardPaddingLG:s,horizontalItemPaddingSM:v,horizontalItemPaddingLG:b}=e;return{[n]:{"&-small":{[`> ${n}-nav`]:{[`${n}-tab`]:{padding:v,fontSize:e.titleFontSizeSM}}},"&-large":{[`> ${n}-nav`]:{[`${n}-tab`]:{padding:b,fontSize:e.titleFontSizeLG}}}},[`${n}-card`]:{[`&${n}-small`]:{[`> ${n}-nav`]:{[`${n}-tab`]:{padding:l}},[`&${n}-bottom`]:{[`> ${n}-nav ${n}-tab`]:{borderRadius:`0 0 ${(0,D.unit)(e.borderRadius)} ${(0,D.unit)(e.borderRadius)}`}},[`&${n}-top`]:{[`> ${n}-nav ${n}-tab`]:{borderRadius:`${(0,D.unit)(e.borderRadius)} ${(0,D.unit)(e.borderRadius)} 0 0`}},[`&${n}-right`]:{[`> ${n}-nav ${n}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${(0,D.unit)(e.borderRadius)} ${(0,D.unit)(e.borderRadius)} 0`}}},[`&${n}-left`]:{[`> ${n}-nav ${n}-tab`]:{borderRadius:{_skip_check_:!0,value:`${(0,D.unit)(e.borderRadius)} 0 0 ${(0,D.unit)(e.borderRadius)}`}}}},[`&${n}-large`]:{[`> ${n}-nav`]:{[`${n}-tab`]:{padding:s}}}}}},In=e=>{const{componentCls:n,itemActiveColor:l,itemHoverColor:s,iconCls:v,tabsHorizontalItemMargin:b,horizontalItemPadding:p,itemSelectedColor:g,itemColor:S}=e,w=`${n}-tab`;return{[w]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:p,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:S,"&-btn, &-remove":Object.assign({"&:focus:not(:focus-visible), &:active":{color:l}},(0,pt.Qy)(e)),"&-btn":{outline:"none",transition:`all ${e.motionDurationSlow}`,[`${w}-icon:not(:last-child)`]:{marginInlineEnd:e.marginSM}},"&-remove":{flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorTextDescription,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},"&:hover":{color:s},[`&${w}-active ${w}-btn`]:{color:g,textShadow:e.tabsActiveTextShadow},[`&${w}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${w}-disabled ${w}-btn, &${w}-disabled ${n}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${w}-remove ${v}`]:{margin:0},[`${v}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${w} + ${w}`]:{margin:{_skip_check_:!0,value:b}}}},bn=e=>{const{componentCls:n,tabsHorizontalItemMarginRTL:l,iconCls:s,cardGutter:v,calc:b}=e;return{[`${n}-rtl`]:{direction:"rtl",[`${n}-nav`]:{[`${n}-tab`]:{margin:{_skip_check_:!0,value:l},[`${n}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[s]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:(0,D.unit)(e.marginSM)}},[`${n}-tab-remove`]:{marginRight:{_skip_check_:!0,value:(0,D.unit)(e.marginXS)},marginLeft:{_skip_check_:!0,value:(0,D.unit)(b(e.marginXXS).mul(-1).equal())},[s]:{margin:0}}}},[`&${n}-left`]:{[`> ${n}-nav`]:{order:1},[`> ${n}-content-holder`]:{order:0}},[`&${n}-right`]:{[`> ${n}-nav`]:{order:0},[`> ${n}-content-holder`]:{order:1}},[`&${n}-card${n}-top, &${n}-card${n}-bottom`]:{[`> ${n}-nav, > div > ${n}-nav`]:{[`${n}-tab + ${n}-tab`]:{marginRight:{_skip_check_:!0,value:v},marginLeft:{_skip_check_:!0,value:0}}}}},[`${n}-dropdown-rtl`]:{direction:"rtl"},[`${n}-menu-item`]:{[`${n}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},pn=e=>{const{componentCls:n,tabsCardPadding:l,cardHeight:s,cardGutter:v,itemHoverColor:b,itemActiveColor:p,colorBorderSecondary:g}=e;return{[n]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,pt.Wf)(e)),{display:"flex",[`> ${n}-nav, > div > ${n}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${n}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${n}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${n}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${n}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${n}-nav-more`]:{position:"relative",padding:l,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${n}-nav-add`]:Object.assign({minWidth:s,minHeight:s,marginLeft:{_skip_check_:!0,value:v},padding:`0 ${(0,D.unit)(e.paddingXS)}`,background:"transparent",border:`${(0,D.unit)(e.lineWidth)} ${e.lineType} ${g}`,borderRadius:`${(0,D.unit)(e.borderRadiusLG)} ${(0,D.unit)(e.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:b},"&:active, &:focus:not(:focus-visible)":{color:p}},(0,pt.Qy)(e))},[`${n}-extra-content`]:{flex:"none"},[`${n}-ink-bar`]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),In(e)),{[`${n}-content`]:{position:"relative",width:"100%"},[`${n}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${n}-tabpane`]:{outline:"none","&-hidden":{display:"none"}}}),[`${n}-centered`]:{[`> ${n}-nav, > div > ${n}-nav`]:{[`${n}-nav-wrap`]:{[`&:not([class*='${n}-nav-wrap-ping']) > ${n}-nav-list`]:{margin:"auto"}}}}}},yn=e=>{const n=e.controlHeightLG;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:n,cardPadding:`${(n-Math.round(e.fontSize*e.lineHeight))/2-e.lineWidth}px ${e.padding}px`,cardPaddingSM:`${e.paddingXXS*1.5}px ${e.padding}px`,cardPaddingLG:`${e.paddingXS}px ${e.padding}px ${e.paddingXXS*1.5}px`,titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:`0 0 ${e.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${e.paddingSM}px 0`,horizontalItemPaddingSM:`${e.paddingXS}px 0`,horizontalItemPaddingLG:`${e.padding}px 0`,verticalItemPadding:`${e.paddingXS}px ${e.paddingLG}px`,verticalItemMargin:`${e.margin}px 0 0 0`,itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}};var on=(0,Yt.I$)("Tabs",e=>{const n=(0,Xt.mergeToken)(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${(0,D.unit)(e.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${(0,D.unit)(e.horizontalItemGutter)}`});return[En(n),bn(n),wn(n),dn(n),rn(n),pn(n),wt(n)]},yn),_t=()=>null,Zn=function(e,n){var l={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&n.indexOf(s)<0&&(l[s]=e[s]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var v=0,s=Object.getOwnPropertySymbols(e);v<s.length;v++)n.indexOf(s[v])<0&&Object.prototype.propertyIsEnumerable.call(e,s[v])&&(l[s[v]]=e[s[v]]);return l};const Cn=e=>{var n,l,s,v,b,p,g,S,w,A,M;const{type:V,className:Re,rootClassName:Be,size:ye,onEdit:Ne,hideAdd:Q,centered:W,addIcon:Te,removeIcon:Ze,moreIcon:De,more:_e,popupClassName:He,children:ae,items:re,animated:we,style:le,indicatorSize:se,indicator:ee}=e,ue=Zn(e,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator"]),{prefixCls:dt}=ue,{direction:Dt,tabs:J,getPrefixCls:lt,getPopupContainer:Mt}=t.useContext(Ue.E_),et=lt("tabs",dt),ut=(0,Ye.Z)(et),[Vt,ge,Le]=on(et,ut);let tt;V==="editable-card"&&(tt={onEdit:(ln,fn)=>{let{key:Me,event:Qt}=fn;Ne==null||Ne(ln==="add"?Qt:Me,ln)},removeIcon:(n=Ze!=null?Ze:J==null?void 0:J.removeIcon)!==null&&n!==void 0?n:t.createElement(h.Z,null),addIcon:(Te!=null?Te:J==null?void 0:J.addIcon)||t.createElement(X,null),showAdd:Q!==!0});const nt=lt(),Qe=(0,Rt.Z)(ye),Xe=Jt(re,ae),ft=zt(et,we),un=Object.assign(Object.assign({},J==null?void 0:J.style),le),qt={align:(l=ee==null?void 0:ee.align)!==null&&l!==void 0?l:(s=J==null?void 0:J.indicator)===null||s===void 0?void 0:s.align,size:(g=(b=(v=ee==null?void 0:ee.size)!==null&&v!==void 0?v:se)!==null&&b!==void 0?b:(p=J==null?void 0:J.indicator)===null||p===void 0?void 0:p.size)!==null&&g!==void 0?g:J==null?void 0:J.indicatorSize};return Vt(t.createElement(_,Object.assign({direction:Dt,getPopupContainer:Mt},ue,{items:Xe,className:R()({[`${et}-${Qe}`]:Qe,[`${et}-card`]:["card","editable-card"].includes(V),[`${et}-editable-card`]:V==="editable-card",[`${et}-centered`]:W},J==null?void 0:J.className,Re,Be,ge,Le,ut),popupClassName:R()(He,ge,Le,ut),style:un,editable:tt,more:Object.assign({icon:(M=(A=(w=(S=J==null?void 0:J.more)===null||S===void 0?void 0:S.icon)!==null&&w!==void 0?w:J==null?void 0:J.moreIcon)!==null&&A!==void 0?A:De)!==null&&M!==void 0?M:t.createElement(o.Z,null),transitionName:`${nt}-slide-up`},_e),prefixCls:et,animated:ft,indicator:qt})))};Cn.TabPane=_t;var On=Cn},19158:function(m,c){"use strict";Object.defineProperty(c,"__esModule",{value:!0}),c.default=a;function a(){return!!(typeof window!="undefined"&&window.document&&window.document.createElement)}},32191:function(m,c){"use strict";Object.defineProperty(c,"__esModule",{value:!0}),c.default=a;function a(t,h){if(!t)return!1;if(t.contains)return t.contains(h);for(var o=h;o;){if(o===t)return!0;o=o.parentNode}return!1}},93399:function(m,c,a){"use strict";var t=a(64836).default;Object.defineProperty(c,"__esModule",{value:!0}),c.clearContainerCache=Y,c.injectCSS=ce,c.removeCSS=ve,c.updateCSS=vt;var h=t(a(42122)),o=t(a(19158)),f=t(a(32191)),C="data-rc-order",x="data-rc-priority",$="rc-util-key",B=new Map;function X(){var G=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},H=G.mark;return H?H.startsWith("data-")?H:"data-".concat(H):$}function k(G){if(G.attachTo)return G.attachTo;var H=document.querySelector("head");return H||document.body}function R(G){return G==="queue"?"prependQueue":G?"prepend":"append"}function P(G){return Array.from((B.get(G)||G).children).filter(function(H){return H.tagName==="STYLE"})}function ce(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!(0,o.default)())return null;var i=H.csp,j=H.prepend,L=H.priority,F=L===void 0?0:L,Ce=R(j),Ae=Ce==="prependQueue",he=document.createElement("style");he.setAttribute(C,Ce),Ae&&F&&he.setAttribute(x,"".concat(F)),i!=null&&i.nonce&&(he.nonce=i==null?void 0:i.nonce),he.innerHTML=G;var ze=k(H),ct=ze.firstChild;if(j){if(Ae){var St=(H.styles||P(ze)).filter(function(st){if(!["prepend","prependQueue"].includes(st.getAttribute(C)))return!1;var Ke=Number(st.getAttribute(x)||0);return F>=Ke});if(St.length)return ze.insertBefore(he,St[St.length-1].nextSibling),he}ze.insertBefore(he,ct)}else ze.appendChild(he);return he}function Z(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=k(H);return(H.styles||P(i)).find(function(j){return j.getAttribute(X(H))===G})}function ve(G){var H=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=Z(G,H);if(i){var j=k(H);j.removeChild(i)}}function Ge(G,H){var i=B.get(G);if(!i||!(0,f.default)(document,i)){var j=ce("",H),L=j.parentNode;B.set(G,L),G.removeChild(j)}}function Y(){B.clear()}function vt(G,H){var i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},j=k(i),L=P(j),F=(0,h.default)((0,h.default)({},i),{},{styles:L});Ge(j,F);var Ce=Z(H,F);if(Ce){var Ae,he;if((Ae=F.csp)!==null&&Ae!==void 0&&Ae.nonce&&Ce.nonce!==((he=F.csp)===null||he===void 0?void 0:he.nonce)){var ze;Ce.nonce=(ze=F.csp)===null||ze===void 0?void 0:ze.nonce}return Ce.innerHTML!==G&&(Ce.innerHTML=G),Ce}var ct=ce(G,F);return ct.setAttribute(X(F),H),ct}},63298:function(m,c){"use strict";Object.defineProperty(c,"__esModule",{value:!0}),c.getShadowRoot=h,c.inShadow=t;function a(o){var f;return o==null||(f=o.getRootNode)===null||f===void 0?void 0:f.call(o)}function t(o){return a(o)instanceof ShadowRoot}function h(o){return t(o)?a(o):null}},45520:function(m,c){"use strict";Object.defineProperty(c,"__esModule",{value:!0}),c.call=x,c.default=void 0,c.note=f,c.noteOnce=B,c.preMessage=void 0,c.resetWarned=C,c.warning=o,c.warningOnce=$;var a={},t=[],h=c.preMessage=function(R){t.push(R)};function o(k,R){if(0)var P}function f(k,R){if(0)var P}function C(){a={}}function x(k,R,P){!R&&!a[P]&&(k(!1,P),a[P]=!0)}function $(k,R){x(o,k,R)}function B(k,R){x(f,k,R)}$.preMessage=h,$.resetWarned=C,$.noteOnce=B;var X=c.default=$},73897:function(m){function c(a,t){(t==null||t>a.length)&&(t=a.length);for(var h=0,o=Array(t);h<t;h++)o[h]=a[h];return o}m.exports=c,m.exports.__esModule=!0,m.exports.default=m.exports},85372:function(m){function c(a){if(Array.isArray(a))return a}m.exports=c,m.exports.__esModule=!0,m.exports.default=m.exports},38416:function(m,c,a){var t=a(64062);function h(o,f,C){return(f=t(f))in o?Object.defineProperty(o,f,{value:C,enumerable:!0,configurable:!0,writable:!0}):o[f]=C,o}m.exports=h,m.exports.__esModule=!0,m.exports.default=m.exports},10434:function(m){function c(){return m.exports=c=Object.assign?Object.assign.bind():function(a){for(var t=1;t<arguments.length;t++){var h=arguments[t];for(var o in h)({}).hasOwnProperty.call(h,o)&&(a[o]=h[o])}return a},m.exports.__esModule=!0,m.exports.default=m.exports,c.apply(null,arguments)}m.exports=c,m.exports.__esModule=!0,m.exports.default=m.exports},64836:function(m){function c(a){return a&&a.__esModule?a:{default:a}}m.exports=c,m.exports.__esModule=!0,m.exports.default=m.exports},75263:function(m,c,a){var t=a(18698).default;function h(f){if(typeof WeakMap!="function")return null;var C=new WeakMap,x=new WeakMap;return(h=function(B){return B?x:C})(f)}function o(f,C){if(!C&&f&&f.__esModule)return f;if(f===null||t(f)!="object"&&typeof f!="function")return{default:f};var x=h(C);if(x&&x.has(f))return x.get(f);var $={__proto__:null},B=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var X in f)if(X!=="default"&&{}.hasOwnProperty.call(f,X)){var k=B?Object.getOwnPropertyDescriptor(f,X):null;k&&(k.get||k.set)?Object.defineProperty($,X,k):$[X]=f[X]}return $.default=f,x&&x.set(f,$),$}m.exports=o,m.exports.__esModule=!0,m.exports.default=m.exports},68872:function(m){function c(a,t){var h=a==null?null:typeof Symbol!="undefined"&&a[Symbol.iterator]||a["@@iterator"];if(h!=null){var o,f,C,x,$=[],B=!0,X=!1;try{if(C=(h=h.call(a)).next,t===0){if(Object(h)!==h)return;B=!1}else for(;!(B=(o=C.call(h)).done)&&($.push(o.value),$.length!==t);B=!0);}catch(k){X=!0,f=k}finally{try{if(!B&&h.return!=null&&(x=h.return(),Object(x)!==x))return}finally{if(X)throw f}}return $}}m.exports=c,m.exports.__esModule=!0,m.exports.default=m.exports},12218:function(m){function c(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}m.exports=c,m.exports.__esModule=!0,m.exports.default=m.exports},42122:function(m,c,a){var t=a(38416);function h(f,C){var x=Object.keys(f);if(Object.getOwnPropertySymbols){var $=Object.getOwnPropertySymbols(f);C&&($=$.filter(function(B){return Object.getOwnPropertyDescriptor(f,B).enumerable})),x.push.apply(x,$)}return x}function o(f){for(var C=1;C<arguments.length;C++){var x=arguments[C]!=null?arguments[C]:{};C%2?h(Object(x),!0).forEach(function($){t(f,$,x[$])}):Object.getOwnPropertyDescriptors?Object.defineProperties(f,Object.getOwnPropertyDescriptors(x)):h(Object(x)).forEach(function($){Object.defineProperty(f,$,Object.getOwnPropertyDescriptor(x,$))})}return f}m.exports=o,m.exports.__esModule=!0,m.exports.default=m.exports},70215:function(m,c,a){var t=a(7071);function h(o,f){if(o==null)return{};var C,x,$=t(o,f);if(Object.getOwnPropertySymbols){var B=Object.getOwnPropertySymbols(o);for(x=0;x<B.length;x++)C=B[x],f.includes(C)||{}.propertyIsEnumerable.call(o,C)&&($[C]=o[C])}return $}m.exports=h,m.exports.__esModule=!0,m.exports.default=m.exports},7071:function(m){function c(a,t){if(a==null)return{};var h={};for(var o in a)if({}.hasOwnProperty.call(a,o)){if(t.includes(o))continue;h[o]=a[o]}return h}m.exports=c,m.exports.__esModule=!0,m.exports.default=m.exports},27424:function(m,c,a){var t=a(85372),h=a(68872),o=a(86116),f=a(12218);function C(x,$){return t(x)||h(x,$)||o(x,$)||f()}m.exports=C,m.exports.__esModule=!0,m.exports.default=m.exports},95036:function(m,c,a){var t=a(18698).default;function h(o,f){if(t(o)!="object"||!o)return o;var C=o[Symbol.toPrimitive];if(C!==void 0){var x=C.call(o,f||"default");if(t(x)!="object")return x;throw new TypeError("@@toPrimitive must return a primitive value.")}return(f==="string"?String:Number)(o)}m.exports=h,m.exports.__esModule=!0,m.exports.default=m.exports},64062:function(m,c,a){var t=a(18698).default,h=a(95036);function o(f){var C=h(f,"string");return t(C)=="symbol"?C:C+""}m.exports=o,m.exports.__esModule=!0,m.exports.default=m.exports},86116:function(m,c,a){var t=a(73897);function h(o,f){if(o){if(typeof o=="string")return t(o,f);var C={}.toString.call(o).slice(8,-1);return C==="Object"&&o.constructor&&(C=o.constructor.name),C==="Map"||C==="Set"?Array.from(o):C==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(C)?t(o,f):void 0}}m.exports=h,m.exports.__esModule=!0,m.exports.default=m.exports}}]);

//# sourceMappingURL=shared-fWQUIeDlCs-S1bey4S923x-UiP0_.95f17003.async.js.map