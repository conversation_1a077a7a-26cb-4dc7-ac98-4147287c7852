(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[9109],{32667:function(k,r,u){var i=u(9950),l=u(25419),c=u(17381);function S(s){return s==null}function e(s){var w={};for(var L in s)w[L]=s[L];return w}function p(s){s=e(s||{}),s.whiteList=s.whiteList||i.whiteList,s.onAttr=s.onAttr||i.onAttr,s.onIgnoreAttr=s.onIgnoreAttr||i.onIgnoreAttr,s.safeAttrValue=s.safeAttrValue||i.safeAttrValue,this.options=s}p.prototype.process=function(s){if(s=s||"",s=s.toString(),!s)return"";var w=this,L=w.options,W=L.whiteList,I=L.onAttr,t=L.onIgnoreAttr,f=L.safeAttrValue,a=l(s,function(A,x,g,E,h){var d=W[g],T=!1;if(d===!0?T=d:typeof d=="function"?T=d(E):d instanceof RegExp&&(T=d.test(E)),T!==!0&&(T=!1),E=f(g,E),!!E){var y={position:x,sourcePosition:A,source:h,isWhite:T};if(T){var b=I(g,E,y);return S(b)?g+":"+E:b}else{var b=t(g,E,y);if(!S(b))return b}}});return a},k.exports=p},9950:function(k,r){function u(){var e={};return e["align-content"]=!1,e["align-items"]=!1,e["align-self"]=!1,e["alignment-adjust"]=!1,e["alignment-baseline"]=!1,e.all=!1,e["anchor-point"]=!1,e.animation=!1,e["animation-delay"]=!1,e["animation-direction"]=!1,e["animation-duration"]=!1,e["animation-fill-mode"]=!1,e["animation-iteration-count"]=!1,e["animation-name"]=!1,e["animation-play-state"]=!1,e["animation-timing-function"]=!1,e.azimuth=!1,e["backface-visibility"]=!1,e.background=!0,e["background-attachment"]=!0,e["background-clip"]=!0,e["background-color"]=!0,e["background-image"]=!0,e["background-origin"]=!0,e["background-position"]=!0,e["background-repeat"]=!0,e["background-size"]=!0,e["baseline-shift"]=!1,e.binding=!1,e.bleed=!1,e["bookmark-label"]=!1,e["bookmark-level"]=!1,e["bookmark-state"]=!1,e.border=!0,e["border-bottom"]=!0,e["border-bottom-color"]=!0,e["border-bottom-left-radius"]=!0,e["border-bottom-right-radius"]=!0,e["border-bottom-style"]=!0,e["border-bottom-width"]=!0,e["border-collapse"]=!0,e["border-color"]=!0,e["border-image"]=!0,e["border-image-outset"]=!0,e["border-image-repeat"]=!0,e["border-image-slice"]=!0,e["border-image-source"]=!0,e["border-image-width"]=!0,e["border-left"]=!0,e["border-left-color"]=!0,e["border-left-style"]=!0,e["border-left-width"]=!0,e["border-radius"]=!0,e["border-right"]=!0,e["border-right-color"]=!0,e["border-right-style"]=!0,e["border-right-width"]=!0,e["border-spacing"]=!0,e["border-style"]=!0,e["border-top"]=!0,e["border-top-color"]=!0,e["border-top-left-radius"]=!0,e["border-top-right-radius"]=!0,e["border-top-style"]=!0,e["border-top-width"]=!0,e["border-width"]=!0,e.bottom=!1,e["box-decoration-break"]=!0,e["box-shadow"]=!0,e["box-sizing"]=!0,e["box-snap"]=!0,e["box-suppress"]=!0,e["break-after"]=!0,e["break-before"]=!0,e["break-inside"]=!0,e["caption-side"]=!1,e.chains=!1,e.clear=!0,e.clip=!1,e["clip-path"]=!1,e["clip-rule"]=!1,e.color=!0,e["color-interpolation-filters"]=!0,e["column-count"]=!1,e["column-fill"]=!1,e["column-gap"]=!1,e["column-rule"]=!1,e["column-rule-color"]=!1,e["column-rule-style"]=!1,e["column-rule-width"]=!1,e["column-span"]=!1,e["column-width"]=!1,e.columns=!1,e.contain=!1,e.content=!1,e["counter-increment"]=!1,e["counter-reset"]=!1,e["counter-set"]=!1,e.crop=!1,e.cue=!1,e["cue-after"]=!1,e["cue-before"]=!1,e.cursor=!1,e.direction=!1,e.display=!0,e["display-inside"]=!0,e["display-list"]=!0,e["display-outside"]=!0,e["dominant-baseline"]=!1,e.elevation=!1,e["empty-cells"]=!1,e.filter=!1,e.flex=!1,e["flex-basis"]=!1,e["flex-direction"]=!1,e["flex-flow"]=!1,e["flex-grow"]=!1,e["flex-shrink"]=!1,e["flex-wrap"]=!1,e.float=!1,e["float-offset"]=!1,e["flood-color"]=!1,e["flood-opacity"]=!1,e["flow-from"]=!1,e["flow-into"]=!1,e.font=!0,e["font-family"]=!0,e["font-feature-settings"]=!0,e["font-kerning"]=!0,e["font-language-override"]=!0,e["font-size"]=!0,e["font-size-adjust"]=!0,e["font-stretch"]=!0,e["font-style"]=!0,e["font-synthesis"]=!0,e["font-variant"]=!0,e["font-variant-alternates"]=!0,e["font-variant-caps"]=!0,e["font-variant-east-asian"]=!0,e["font-variant-ligatures"]=!0,e["font-variant-numeric"]=!0,e["font-variant-position"]=!0,e["font-weight"]=!0,e.grid=!1,e["grid-area"]=!1,e["grid-auto-columns"]=!1,e["grid-auto-flow"]=!1,e["grid-auto-rows"]=!1,e["grid-column"]=!1,e["grid-column-end"]=!1,e["grid-column-start"]=!1,e["grid-row"]=!1,e["grid-row-end"]=!1,e["grid-row-start"]=!1,e["grid-template"]=!1,e["grid-template-areas"]=!1,e["grid-template-columns"]=!1,e["grid-template-rows"]=!1,e["hanging-punctuation"]=!1,e.height=!0,e.hyphens=!1,e.icon=!1,e["image-orientation"]=!1,e["image-resolution"]=!1,e["ime-mode"]=!1,e["initial-letters"]=!1,e["inline-box-align"]=!1,e["justify-content"]=!1,e["justify-items"]=!1,e["justify-self"]=!1,e.left=!1,e["letter-spacing"]=!0,e["lighting-color"]=!0,e["line-box-contain"]=!1,e["line-break"]=!1,e["line-grid"]=!1,e["line-height"]=!1,e["line-snap"]=!1,e["line-stacking"]=!1,e["line-stacking-ruby"]=!1,e["line-stacking-shift"]=!1,e["line-stacking-strategy"]=!1,e["list-style"]=!0,e["list-style-image"]=!0,e["list-style-position"]=!0,e["list-style-type"]=!0,e.margin=!0,e["margin-bottom"]=!0,e["margin-left"]=!0,e["margin-right"]=!0,e["margin-top"]=!0,e["marker-offset"]=!1,e["marker-side"]=!1,e.marks=!1,e.mask=!1,e["mask-box"]=!1,e["mask-box-outset"]=!1,e["mask-box-repeat"]=!1,e["mask-box-slice"]=!1,e["mask-box-source"]=!1,e["mask-box-width"]=!1,e["mask-clip"]=!1,e["mask-image"]=!1,e["mask-origin"]=!1,e["mask-position"]=!1,e["mask-repeat"]=!1,e["mask-size"]=!1,e["mask-source-type"]=!1,e["mask-type"]=!1,e["max-height"]=!0,e["max-lines"]=!1,e["max-width"]=!0,e["min-height"]=!0,e["min-width"]=!0,e["move-to"]=!1,e["nav-down"]=!1,e["nav-index"]=!1,e["nav-left"]=!1,e["nav-right"]=!1,e["nav-up"]=!1,e["object-fit"]=!1,e["object-position"]=!1,e.opacity=!1,e.order=!1,e.orphans=!1,e.outline=!1,e["outline-color"]=!1,e["outline-offset"]=!1,e["outline-style"]=!1,e["outline-width"]=!1,e.overflow=!1,e["overflow-wrap"]=!1,e["overflow-x"]=!1,e["overflow-y"]=!1,e.padding=!0,e["padding-bottom"]=!0,e["padding-left"]=!0,e["padding-right"]=!0,e["padding-top"]=!0,e.page=!1,e["page-break-after"]=!1,e["page-break-before"]=!1,e["page-break-inside"]=!1,e["page-policy"]=!1,e.pause=!1,e["pause-after"]=!1,e["pause-before"]=!1,e.perspective=!1,e["perspective-origin"]=!1,e.pitch=!1,e["pitch-range"]=!1,e["play-during"]=!1,e.position=!1,e["presentation-level"]=!1,e.quotes=!1,e["region-fragment"]=!1,e.resize=!1,e.rest=!1,e["rest-after"]=!1,e["rest-before"]=!1,e.richness=!1,e.right=!1,e.rotation=!1,e["rotation-point"]=!1,e["ruby-align"]=!1,e["ruby-merge"]=!1,e["ruby-position"]=!1,e["shape-image-threshold"]=!1,e["shape-outside"]=!1,e["shape-margin"]=!1,e.size=!1,e.speak=!1,e["speak-as"]=!1,e["speak-header"]=!1,e["speak-numeral"]=!1,e["speak-punctuation"]=!1,e["speech-rate"]=!1,e.stress=!1,e["string-set"]=!1,e["tab-size"]=!1,e["table-layout"]=!1,e["text-align"]=!0,e["text-align-last"]=!0,e["text-combine-upright"]=!0,e["text-decoration"]=!0,e["text-decoration-color"]=!0,e["text-decoration-line"]=!0,e["text-decoration-skip"]=!0,e["text-decoration-style"]=!0,e["text-emphasis"]=!0,e["text-emphasis-color"]=!0,e["text-emphasis-position"]=!0,e["text-emphasis-style"]=!0,e["text-height"]=!0,e["text-indent"]=!0,e["text-justify"]=!0,e["text-orientation"]=!0,e["text-overflow"]=!0,e["text-shadow"]=!0,e["text-space-collapse"]=!0,e["text-transform"]=!0,e["text-underline-position"]=!0,e["text-wrap"]=!0,e.top=!1,e.transform=!1,e["transform-origin"]=!1,e["transform-style"]=!1,e.transition=!1,e["transition-delay"]=!1,e["transition-duration"]=!1,e["transition-property"]=!1,e["transition-timing-function"]=!1,e["unicode-bidi"]=!1,e["vertical-align"]=!1,e.visibility=!1,e["voice-balance"]=!1,e["voice-duration"]=!1,e["voice-family"]=!1,e["voice-pitch"]=!1,e["voice-range"]=!1,e["voice-rate"]=!1,e["voice-stress"]=!1,e["voice-volume"]=!1,e.volume=!1,e["white-space"]=!1,e.widows=!1,e.width=!0,e["will-change"]=!1,e["word-break"]=!0,e["word-spacing"]=!0,e["word-wrap"]=!0,e["wrap-flow"]=!1,e["wrap-through"]=!1,e["writing-mode"]=!1,e["z-index"]=!1,e}function i(e,p,s){}function l(e,p,s){}var c=/javascript\s*\:/img;function S(e,p){return c.test(p)?"":p}r.whiteList=u(),r.getDefaultWhiteList=u,r.onAttr=i,r.onIgnoreAttr=l,r.safeAttrValue=S},29970:function(k,r,u){var i=u(9950),l=u(32667);function c(e,p){var s=new l(p);return s.process(e)}r=k.exports=c,r.FilterCSS=l;for(var S in i)r[S]=i[S];typeof window!="undefined"&&(window.filterCSS=k.exports)},25419:function(k,r,u){var i=u(17381);function l(c,S){c=i.trimRight(c),c[c.length-1]!==";"&&(c+=";");var e=c.length,p=!1,s=0,w=0,L="";function W(){if(!p){var f=i.trim(c.slice(s,w)),a=f.indexOf(":");if(a!==-1){var A=i.trim(f.slice(0,a)),x=i.trim(f.slice(a+1));if(A){var g=S(s,L.length,A,x,f);g&&(L+=g+"; ")}}}s=w+1}for(;w<e;w++){var I=c[w];if(I==="/"&&c[w+1]==="*"){var t=c.indexOf("*/",w+2);if(t===-1)break;w=t+1,s=w+1,p=!1}else I==="("?p=!0:I===")"?p=!1:I===";"?p||W():I===`
`&&W()}return i.trim(L)}k.exports=l},17381:function(k){k.exports={indexOf:function(r,u){var i,l;if(Array.prototype.indexOf)return r.indexOf(u);for(i=0,l=r.length;i<l;i++)if(r[i]===u)return i;return-1},forEach:function(r,u,i){var l,c;if(Array.prototype.forEach)return r.forEach(u,i);for(l=0,c=r.length;l<c;l++)u.call(i,r[l],l,r)},trim:function(r){return String.prototype.trim?r.trim():r.replace(/(^\s*)|(\s*$)/g,"")},trimRight:function(r){return String.prototype.trimRight?r.trimRight():r.replace(/(\s*$)/g,"")}}},82302:function(k,r,u){var i=u(29970).FilterCSS,l=u(29970).getDefaultWhiteList,c=u(75938);function S(){return{a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height","loading"],ins:["datetime"],kbd:[],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]}}var e=new i;function p(n,m,o){}function s(n,m,o){}function w(n,m,o){}function L(n,m,o){}function W(n){return n.replace(t,"&lt;").replace(f,"&gt;")}function I(n,m,o,v){if(o=B(o),m==="href"||m==="src"){if(o=c.trim(o),o==="#")return"#";if(!(o.substr(0,7)==="http://"||o.substr(0,8)==="https://"||o.substr(0,7)==="mailto:"||o.substr(0,4)==="tel:"||o.substr(0,11)==="data:image/"||o.substr(0,6)==="ftp://"||o.substr(0,2)==="./"||o.substr(0,3)==="../"||o[0]==="#"||o[0]==="/"))return""}else if(m==="background"){if(h.lastIndex=0,h.test(o))return""}else if(m==="style"){if(d.lastIndex=0,d.test(o)||(T.lastIndex=0,T.test(o)&&(h.lastIndex=0,h.test(o))))return"";v!==!1&&(v=v||e,o=v.process(o))}return o=O(o),o}var t=/</g,f=/>/g,a=/"/g,A=/&quot;/g,x=/&#([a-zA-Z0-9]*);?/gim,g=/&colon;?/gim,E=/&newline;?/gim,h=/((j\s*a\s*v\s*a|v\s*b|l\s*i\s*v\s*e)\s*s\s*c\s*r\s*i\s*p\s*t\s*|m\s*o\s*c\s*h\s*a):/gi,d=/e\s*x\s*p\s*r\s*e\s*s\s*s\s*i\s*o\s*n\s*\(.*/gi,T=/u\s*r\s*l\s*\(.*/gi;function y(n){return n.replace(a,"&quot;")}function b(n){return n.replace(A,'"')}function C(n){return n.replace(x,function(o,v){return v[0]==="x"||v[0]==="X"?String.fromCharCode(parseInt(v.substr(1),16)):String.fromCharCode(parseInt(v,10))})}function R(n){return n.replace(g,":").replace(E," ")}function N(n){for(var m="",o=0,v=n.length;o<v;o++)m+=n.charCodeAt(o)<32?" ":n.charAt(o);return c.trim(m)}function B(n){return n=b(n),n=C(n),n=R(n),n=N(n),n}function O(n){return n=y(n),n=W(n),n}function F(){return""}function H(n,m){typeof m!="function"&&(m=function(){});var o=!Array.isArray(n);function v(P){return o?!0:c.indexOf(n,P)!==-1}var V=[],X=!1;return{onIgnoreTag:function(P,U,G){if(v(P))if(G.isClosing){var z="[/removed]",Q=G.position+z.length;return V.push([X!==!1?X:G.position,Q]),X=!1,z}else return X||(X=G.position),"[removed]";else return m(P,U,G)},remove:function(P){var U="",G=0;return c.forEach(V,function(z){U+=P.slice(G,z[0]),G=z[1]}),U+=P.slice(G),U}}}function _(n){for(var m="",o=0;o<n.length;){var v=n.indexOf("<!--",o);if(v===-1){m+=n.slice(o);break}m+=n.slice(o,v);var V=n.indexOf("-->",v);if(V===-1)break;o=V+3}return m}function D(n){var m=n.split("");return m=m.filter(function(o){var v=o.charCodeAt(0);return v===127?!1:v<=31?v===10||v===13:!0}),m.join("")}r.whiteList=S(),r.getDefaultWhiteList=S,r.onTag=p,r.onIgnoreTag=s,r.onTagAttr=w,r.onIgnoreTagAttr=L,r.safeAttrValue=I,r.escapeHtml=W,r.escapeQuote=y,r.unescapeQuote=b,r.escapeHtmlEntities=C,r.escapeDangerHtml5Entities=R,r.clearNonPrintableCharacter=N,r.friendlyAttrValue=B,r.escapeAttrValue=O,r.onIgnoreTagStripAll=F,r.StripTagBody=H,r.stripCommentTag=_,r.stripBlankChar=D,r.attributeWrapSign='"',r.cssFilter=e,r.getDefaultCSSWhiteList=l},68924:function(k,r,u){var i=u(82302),l=u(73268),c=u(82973);function S(p,s){var w=new c(s);return w.process(p)}r=k.exports=S,r.filterXSS=S,r.FilterXSS=c,function(){for(var p in i)r[p]=i[p];for(var s in l)r[s]=l[s]}(),typeof window!="undefined"&&(window.filterXSS=k.exports);function e(){return typeof self!="undefined"&&typeof DedicatedWorkerGlobalScope!="undefined"&&self instanceof DedicatedWorkerGlobalScope}e()&&(self.filterXSS=k.exports)},73268:function(k,r,u){var i=u(75938);function l(t){var f=i.spaceIndex(t),a;return f===-1?a=t.slice(1,-1):a=t.slice(1,f+1),a=i.trim(a).toLowerCase(),a.slice(0,1)==="/"&&(a=a.slice(1)),a.slice(-1)==="/"&&(a=a.slice(0,-1)),a}function c(t){return t.slice(0,2)==="</"}function S(t,f,a){"use strict";var A="",x=0,g=!1,E=!1,h=0,d=t.length,T="",y="";e:for(h=0;h<d;h++){var b=t.charAt(h);if(g===!1){if(b==="<"){g=h;continue}}else if(E===!1){if(b==="<"){A+=a(t.slice(x,h)),g=h,x=h;continue}if(b===">"||h===d-1){A+=a(t.slice(x,g)),y=t.slice(g,h+1),T=l(y),A+=f(g,A.length,T,y,c(y)),x=h+1,g=!1;continue}if(b==='"'||b==="'")for(var C=1,R=t.charAt(h-C);R.trim()===""||R==="=";){if(R==="="){E=b;continue e}R=t.charAt(h-++C)}}else if(b===E){E=!1;continue}}return x<d&&(A+=a(t.substr(x))),A}var e=/[^a-zA-Z0-9\\_:.-]/gim;function p(t,f){"use strict";var a=0,A=0,x=[],g=!1,E=t.length;function h(C,R){if(C=i.trim(C),C=C.replace(e,"").toLowerCase(),!(C.length<1)){var N=f(C,R||"");N&&x.push(N)}}for(var d=0;d<E;d++){var T=t.charAt(d),y,b;if(g===!1&&T==="="){g=t.slice(a,d),a=d+1,A=t.charAt(a)==='"'||t.charAt(a)==="'"?a:w(t,d+1);continue}if(g!==!1&&d===A){if(b=t.indexOf(T,d+1),b===-1)break;y=i.trim(t.slice(A+1,b)),h(g,y),g=!1,d=b,a=d+1;continue}if(/\s|\n|\t/.test(T))if(t=t.replace(/\s|\n|\t/g," "),g===!1)if(b=s(t,d),b===-1){y=i.trim(t.slice(a,d)),h(y),g=!1,a=d+1;continue}else{d=b-1;continue}else if(b=L(t,d-1),b===-1){y=i.trim(t.slice(a,d)),y=I(y),h(g,y),g=!1,a=d+1;continue}else continue}return a<t.length&&(g===!1?h(t.slice(a)):h(g,I(i.trim(t.slice(a))))),i.trim(x.join(" "))}function s(t,f){for(;f<t.length;f++){var a=t[f];if(a!==" ")return a==="="?f:-1}}function w(t,f){for(;f<t.length;f++){var a=t[f];if(a!==" ")return a==="'"||a==='"'?f:-1}}function L(t,f){for(;f>0;f--){var a=t[f];if(a!==" ")return a==="="?f:-1}}function W(t){return t[0]==='"'&&t[t.length-1]==='"'||t[0]==="'"&&t[t.length-1]==="'"}function I(t){return W(t)?t.substr(1,t.length-2):t}r.parseTag=S,r.parseAttr=p},75938:function(k){k.exports={indexOf:function(r,u){var i,l;if(Array.prototype.indexOf)return r.indexOf(u);for(i=0,l=r.length;i<l;i++)if(r[i]===u)return i;return-1},forEach:function(r,u,i){var l,c;if(Array.prototype.forEach)return r.forEach(u,i);for(l=0,c=r.length;l<c;l++)u.call(i,r[l],l,r)},trim:function(r){return String.prototype.trim?r.trim():r.replace(/(^\s*)|(\s*$)/g,"")},spaceIndex:function(r){var u=/\s|\n|\t/,i=u.exec(r);return i?i.index:-1}}},82973:function(k,r,u){var i=u(29970).FilterCSS,l=u(82302),c=u(73268),S=c.parseTag,e=c.parseAttr,p=u(75938);function s(t){return t==null}function w(t){var f=p.spaceIndex(t);if(f===-1)return{html:"",closing:t[t.length-2]==="/"};t=p.trim(t.slice(f+1,-1));var a=t[t.length-1]==="/";return a&&(t=p.trim(t.slice(0,-1))),{html:t,closing:a}}function L(t){var f={};for(var a in t)f[a]=t[a];return f}function W(t){var f={};for(var a in t)Array.isArray(t[a])?f[a.toLowerCase()]=t[a].map(function(A){return A.toLowerCase()}):f[a.toLowerCase()]=t[a];return f}function I(t){t=L(t||{}),t.stripIgnoreTag&&(t.onIgnoreTag&&console.error('Notes: cannot use these two options "stripIgnoreTag" and "onIgnoreTag" at the same time'),t.onIgnoreTag=l.onIgnoreTagStripAll),t.whiteList||t.allowList?t.whiteList=W(t.whiteList||t.allowList):t.whiteList=l.whiteList,this.attributeWrapSign=t.singleQuotedAttributeValue===!0?"'":l.attributeWrapSign,t.onTag=t.onTag||l.onTag,t.onTagAttr=t.onTagAttr||l.onTagAttr,t.onIgnoreTag=t.onIgnoreTag||l.onIgnoreTag,t.onIgnoreTagAttr=t.onIgnoreTagAttr||l.onIgnoreTagAttr,t.safeAttrValue=t.safeAttrValue||l.safeAttrValue,t.escapeHtml=t.escapeHtml||l.escapeHtml,this.options=t,t.css===!1?this.cssFilter=!1:(t.css=t.css||{},this.cssFilter=new i(t.css))}I.prototype.process=function(t){if(t=t||"",t=t.toString(),!t)return"";var f=this,a=f.options,A=a.whiteList,x=a.onTag,g=a.onIgnoreTag,E=a.onTagAttr,h=a.onIgnoreTagAttr,d=a.safeAttrValue,T=a.escapeHtml,y=f.attributeWrapSign,b=f.cssFilter;a.stripBlankChar&&(t=l.stripBlankChar(t)),a.allowCommentTag||(t=l.stripCommentTag(t));var C=!1;a.stripIgnoreTagBody&&(C=l.StripTagBody(a.stripIgnoreTagBody,g),g=C.onIgnoreTag);var R=S(t,function(N,B,O,F,H){var _={sourcePosition:N,position:B,isClosing:H,isWhite:Object.prototype.hasOwnProperty.call(A,O)},D=x(O,F,_);if(!s(D))return D;if(_.isWhite){if(_.isClosing)return"</"+O+">";var n=w(F),m=A[O],o=e(n.html,function(v,V){var X=p.indexOf(m,v)!==-1,P=E(O,v,V,X);return s(P)?X?(V=d(O,v,V,b),V?v+"="+y+V+y:v):(P=h(O,v,V,X),s(P)?void 0:P):P});return F="<"+O,o&&(F+=" "+o),n.closing&&(F+=" /"),F+=">",F}else return D=g(O,F,_),s(D)?T(F):D},T);return C&&(R=C.remove(R)),R},k.exports=I}}]);

//# sourceMappingURL=shared-AGVJfLV6rONrM6LDRHvgHGpOcQ4_.7ef965d1.async.js.map