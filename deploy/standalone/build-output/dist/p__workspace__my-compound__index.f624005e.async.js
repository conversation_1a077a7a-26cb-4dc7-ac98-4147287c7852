"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[9735],{63434:function(U,C,e){var c=e(1413),d=e(45987),t=e(22270),m=e(84567),O=e(67294),f=e(90789),x=e(92755),p=e(85893),S=["options","fieldProps","proFieldProps","valueEnum"],l=O.forwardRef(function(g,y){var z=g.options,P=g.fieldProps,N=g.proFieldProps,b=g.valueEnum,D=(0,d.Z)(g,S);return(0,p.jsx)(x.Z,(0,c.Z)({ref:y,valueType:"checkbox",valueEnum:(0,t.h)(b,void 0),fieldProps:(0,c.Z)({options:z},P),lightProps:(0,c.Z)({labelFormatter:function(){return(0,p.jsx)(x.Z,(0,c.Z)({ref:y,valueType:"checkbox",mode:"read",valueEnum:(0,t.h)(b,void 0),filedConfig:{customLightMode:!0},fieldProps:(0,c.Z)({options:z},P),proFieldProps:N},D))}},D.lightProps),proFieldProps:N},D))}),o=O.forwardRef(function(g,y){var z=g.fieldProps,P=g.children;return(0,p.jsx)(m.Z,(0,c.Z)((0,c.Z)({ref:y},z),{},{children:P}))}),i=(0,f.G)(o,{valuePropName:"checked"}),v=i;v.Group=l,C.Z=v},2390:function(U,C,e){e.d(C,{D:function(){return f}});var c=e(97857),d=e.n(c),t=e(55932),m=e(70831),O=function(){var p=(0,m.useAppData)(),S=p.clientRoutes,l=(0,m.useLocation)(),o=(0,m.matchRoutes)(S,l.pathname),i=o==null?void 0:o[o.length-1].route;return{matches:o,currentRoute:i}},f=function(p){var S=O(),l=S.currentRoute,o=(0,t.s5)(d()({prefix:l==null?void 0:l.path},p)),i=o.get,v=o.set;return[i,v]}},69776:function(U,C,e){var c=e(96486),d=e.n(c),t=e(63686),m=function(){var f=[{value:"created",label:(0,t.oz)("molecules-status.created")},{value:"designing",label:(0,t.oz)("molecules-status.designing")},{value:"synthesizing",label:(0,t.oz)("molecules-status.synthesizing")},{value:"finished",label:(0,t.oz)("component.notification.statusValue.success")},{value:"canceled",label:(0,t.oz)("pages.projectTable.statusLabel.cancelled")}],x=[{value:"product",label:(0,t.oz)("product")},{value:"main_reactant",label:(0,t.oz)("main-reactant")},{value:"reactant",label:(0,t.oz)("reactant")},{value:"other_reagent",label:(0,t.oz)("other-reagent")}],p=[{label:(0,t.oz)("same-key-material"),value:"start_material"},{label:(0,t.oz)("algorithm-cluster"),value:"cluster"},{label:(0,t.oz)("not-grouped"),value:"ungrouped"}],S=[{label:(0,t.oz)("algorithmic-score"),value:"score"},{label:(0,t.oz)("known-reaction-proportion"),value:"known_reaction_rate"},{label:(0,t.oz)("longest-chain-l"),value:"backbone_length"},{label:(0,t.oz)("route-length"),value:"min_n_main_tree_steps"}],l={createdAt:(0,t.oz)("creation-time"),updatedAt:(0,t.oz)("last-update-time"),no:(0,t.oz)("name")},o={target:(0,t.oz)("target-molecules"),building_block:(0,t.oz)("key-intermediate"),temp_block:(0,t.oz)("show-materials")},i=(0,c.omit)(o,"temp_block"),v={onlyOneLineEditorAlertMessage:(0,t.oz)("only-one-edit"),onlyAddOneLineAlertMessage:(0,t.oz)("only-one-added")},g={total_cost:(0,t.oz)("total"),material_cost:(0,t.oz)("material-cost"),labor_cost:(0,t.oz)("labor-cost")},y={draft:(0,t.oz)("draft"),published:(0,t.oz)("in-use"),deleted:(0,t.oz)("deleted")},z={success:(0,t.oz)("pages.experiment.statusLabel.success"),fail:(0,t.oz)("pages.experiment.statusLabel.failed"),processing:(0,t.oz)("pages.experiment.statusLabel.running")},P={limited:(0,t.oz)("component.notification.statusValue.limited"),completed:(0,t.oz)("component.notification.statusValue.success"),running:(0,t.oz)("component.notification.statusValue.running"),pending:(0,t.oz)("component.notification.statusValue.pending"),failed:(0,t.oz)("component.notification.statusValue.failed")},N={working:(0,t.oz)("working"),holding:(0,t.oz)("hold"),error:(0,t.oz)("unavailable"),idle:(0,t.oz)("idle")};return{moleculeStatusOptions:f,reactionRoleOptions:x,groupOptions:p,proportionOptions:S,typeMap:o,sortStandard:l,typeMapForSelect:i,editableConfig:v,chargeDes:g,materialManageStauts:y,aiGenerateStauts:P,aiAIInferenceStauts:z,robotStatus:N}};C.Z=m},96905:function(U,C,e){e.d(C,{Z:function(){return i}});var c=e(9783),d=e.n(c),t=e(5574),m=e.n(t),O=e(39175),f=e(93967),x=e.n(f),p=e(67294),S={sortButton:"sortButton___pXWAI",antRotate:"antRotate___Yubs4",desc:"desc___RW1_9"},l=e(85893),o=function(g){var y=g.value,z=y===void 0?"asc":y,P=g.onChange,N=(0,p.useState)(z),b=m()(N,2),D=b[0],J=b[1],Z=function(H){J(H),P==null||P(H)};return(0,l.jsx)(O.r,{value:z,className:x()(S.sortButton,d()({},S.desc,D==="desc")),onClick:function(){return Z(z==="asc"?"desc":"asc")}})},i=o},62089:function(U,C,e){e.r(C),e.d(C,{default:function(){return Te}});var c=e(15009),d=e.n(c),t=e(99289),m=e.n(t),O=e(97857),f=e.n(O),x=e(5574),p=e.n(x),S=e(61487),l=e(2390),o=e(69776),i=e(87172),v=e(32884),g=e(17496),y=e(64317),z=e(63434),P=e(34994),N=e(42525),b=e(11774),D=e(70831),J=e(4584),Z=e(67294),ie=e(81012),H=e(43851),Pe=e(31418),Ee=e(4393),w=e(71230),L=e(15746),Ae=e(71471),Ce=e(66309),Ie=e(42075),ue=e(74656),Oe=e(93967),_=e.n(Oe),xe=e(92413),j={moleculeCardRoot:"moleculeCardRoot___WuryM",card:"card___MKq0R",created:"created___Otu8u",designing:"designing___k7x3w",synthesizing:"synthesizing___JT7E0",finished:"finished___mBstU",canceled:"canceled___p2vqW",routeNum:"routeNum___dYUEk",label:"label___tdzHV",actionsWrapper:"actionsWrapper____dPpv",actionWrapper:"actionWrapper___Do2_P",tagsWrapper:"tagsWrapper___vFIVe",alignRight:"alignRight___PjaZl",desItem:"desItem___b6Yn0",routesAmount:"routesAmount___NRY2X",moleculeNo:"moleculeNo___Cmipi",valueItem:"valueItem___GJU0g",normalText:"normalText___ZZm6m"},a=e(85893),Ne={created:["designing","canceled"],designing:["synthesizing","canceled"],synthesizing:["finished","canceled"],finished:[],canceled:[]},De=function(de){var V=de.compound,X=(0,xe.HD)(),ce=X.getById,pe=(0,Z.useState)([]),k=p()(pe,2),q=k[0],ve=k[1],G=(0,S.f)(),$=G.fetch,F=G.loading,me=Pe.Z.useApp(),fe=me.message,ge=(0,Z.useState)(V),R=p()(ge,2),E=R[0],K=R[1],Y=E.compound,h=E.project,ee=E.id,T=E.status,he=E.priority,te=E.director_id,ae=E.no,re=E.type,je=E.retro_backbones_number,oe=E.project_routes_number;(0,Z.useEffect)(function(){ce(h==null?void 0:h.id).then(function(r){return ve(r)})},[h==null?void 0:h.id]),(0,Z.useEffect)(function(){K(V)},[V]);var Q=function(){var r=m()(d()().mark(function n(M){var u,s;return d()().wrap(function(I){for(;;)switch(I.prev=I.next){case 0:return I.next=2,$((0,i.service)("project-compounds").update(ee,M));case 2:u=I.sent,s=u.data,s&&(fe.success((0,v.oz)("success-update")),K(function(W){return f()(f()({},W),s)}));case 5:case"end":return I.stop()}},n)}));return function(M){return r.apply(this,arguments)}}(),ne=(0,o.Z)(),Se=ne.moleculeStatusOptions,A=ne.typeMap;return(0,a.jsx)("div",{className:j.moleculeCardRoot,children:(0,a.jsxs)(Ee.Z,{className:_()("enablePointer",j.card,T&&j[T]),onClick:function(){return D.history.push("/projects/".concat(h==null?void 0:h.id,"/compound/").concat(ee,"?page=1&pageSize=10"))},children:[(0,a.jsx)(w.Z,{children:(0,a.jsx)(L.Z,{flex:"auto",children:(0,a.jsx)(ie.Z,{structure:(Y==null?void 0:Y.smiles)||"",height:300,className:_()("enablePointer")})})}),(0,a.jsxs)(w.Z,{justify:"space-between",align:"middle",wrap:!1,children:[(0,a.jsx)(L.Z,{flex:"auto",children:(0,a.jsx)(Ae.Z.Text,{strong:!0,ellipsis:{tooltip:ae},children:ae})}),(0,a.jsxs)(L.Z,{className:j.tagsWrapper,flex:"none",children:[(0,a.jsx)(Ce.Z,{color:"green",children:A[re]}),(h==null?void 0:h.no)&&(0,a.jsx)(Ce.Z,{color:"blue",children:h.no})]})]}),(0,a.jsx)(w.Z,{children:(0,a.jsx)(L.Z,{className:_()(j.routeNum,j.label),children:(0,a.jsxs)(Ie.Z,{children:[(0,a.jsxs)("div",{children:[(0,v.oz)("aiGenerated")," ",je||0," ",(0,v.Ig)()?"":"\u6761"]}),(0,a.jsx)("div",{children:"\u2022"}),(0,a.jsxs)("div",{children:[(0,v.oz)("myRoutes")," ",oe||0," ",(0,v.Ig)()?"":"\u6761"]})]})})}),(0,a.jsxs)("div",{onClick:function(n){return n.stopPropagation()},className:_()(j.actionsWrapper,j.label),children:[(0,a.jsxs)(w.Z,{children:[(0,a.jsxs)(L.Z,{span:12,className:j.actionWrapper,children:[(0,a.jsx)("span",{className:j.label,children:(0,v.oz)("status")}),(0,a.jsx)(ue.Z,{rootClassName:j.alignRight,onChange:function(){var r=m()(d()().mark(function n(M){return d()().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.abrupt("return",Q({status:M}));case 1:case"end":return s.stop()}},n)}));return function(n){return r.apply(this,arguments)}}(),disabled:F,size:"small",style:{width:"60%"},value:T,options:Se.filter(function(r){return Ne[T].includes(r.value)||r.value===T})})]}),(0,a.jsxs)(L.Z,{span:12,className:j.actionWrapper,children:[(0,a.jsx)("div",{className:j.label,children:(0,v.oz)("Priority")}),(0,a.jsx)(ue.Z,{size:"small",value:he,style:{width:"60%"},options:H.jY,onChange:function(){var r=m()(d()().mark(function n(M){return d()().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.abrupt("return",Q({priority:M}));case 1:case"end":return s.stop()}},n)}));return function(n){return r.apply(this,arguments)}}()})]})]}),(0,a.jsx)(w.Z,{children:(0,a.jsxs)(L.Z,{span:24,className:j.actionWrapper,children:[(0,a.jsx)("div",{className:j.label,children:(0,v.oz)("pages.experiment.label.owner")}),(0,a.jsx)(ue.Z,{disabled:F,size:"small",value:te,style:{width:"100%"},options:q.map(function(r){var n;return{label:(n=r.user_info)===null||n===void 0?void 0:n.username,value:r.user_id}}),onChange:function(){var r=m()(d()().mark(function n(M){return d()().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:return s.abrupt("return",Q({director_id:M}));case 1:case"end":return s.stop()}},n)}));return function(n){return r.apply(this,arguments)}}()})]})})]})]})})},Ze=De,be=e(10839),Le=e(96905),Fe={sort:"sort___SQK6t"},ze={status:["created","designing","synthesizing","finished"],sortOrder:"asc",sortBy:"no"},Re=function(){var de=(0,o.Z)(),V=de.typeMapForSelect,X=(0,o.Z)(),ce=X.moleculeStatusOptions,pe=X.sortStandard,k=(0,D.useModel)("@@initialState"),q=k.initialState,ve=q===void 0?{}:q,G=ve.userInfo,$=G===void 0?void 0:G,F=$==null?void 0:$.id,me=(0,be.i)(F,void 0,!0),fe=(0,J.Z)(),ge=p()(fe,1),R=ge[0],E=(0,Z.useState)(),K=p()(E,2),Y=K[0],h=K[1],ee=(0,S.f)(),T=ee.fetch,he=(0,l.D)(),te=p()(he,2),ae=te[0],re=te[1];if(R.setFieldsValue(f()(f()({},ze),ae())),(0,Z.useEffect)(function(){h(R.getFieldsValue())},[]),!F)return null;var je=function(){var A=m()(d()().mark(function r(n){var M,u,s,se,I,W;return d()().wrap(function(B){for(;;)switch(B.prev=B.next){case 0:return u=f()(f()({},ze),{},{current:1,pageSize:10},n),s=(0,i.query)("project-compounds").equalTo("director_id",F).paginate(u.current,u.pageSize).sortBy([{field:u.sortBy,order:u.sortOrder}]).populateWith("project_routes",["id"]).populateWith("compound",["smiles"]).populateWith("project",["id","no"]).notEqualTo("type","temp_block").populateDeep([{path:"retro_processes",fields:["id"],children:[{key:"retro_backbones",fields:["id"]}]}]),u.no&&s.equalTo("no",u.no),u.projectId&&s.filterDeep("project.id","eq",u.projectId),(M=u.status)!==null&&M!==void 0&&M.length&&s.filterDeep("status","in",u.status),u.type&&s.equalTo("type",u.type),B.next=8,T(s.get());case 8:return se=B.sent,I=se.data,W=se.meta,I==null||I.forEach(function(le){var ye,Me;le.project_routes_number=(ye=le.project_routes)===null||ye===void 0?void 0:ye.length,le.retro_backbones_number=(Me=le.retro_processes)===null||Me===void 0?void 0:Me.flatMap(function(Be){return Be.retro_backbones}).length}),B.abrupt("return",{data:I||[],total:W==null?void 0:W.pagination.total,success:!!I});case 13:case"end":return B.stop()}},r)}));return function(n){return A.apply(this,arguments)}}(),oe=function(){var A=m()(d()().mark(function r(n){return d()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:h(n);case 1:case"end":return u.stop()}},r)}));return function(n){return A.apply(this,arguments)}}(),Q=(0,a.jsxs)(g.M,{bordered:!0,onFinish:oe,form:R,onValuesChange:function(r,n){return re(n)},children:[(0,a.jsx)(y.Z,{name:"projectId",placeholder:(0,v.oz)("project-ID"),request:function(r){var n=r.userId;return(0,xe.TL)(n)},params:{userId:F},debounceTime:300,fieldProps:{onClick:function(r){return r.stopPropagation()}},showSearch:!0}),(0,a.jsx)(y.Z,f()({},me)),(0,a.jsx)(y.Z,{name:"type",placeholder:(0,v.oz)("molecules-type"),valueEnum:V,fieldProps:{popupMatchSelectWidth:!1,onClick:function(r){return r.stopPropagation()}}}),(0,a.jsx)(z.Z.Group,{name:"status",placeholder:(0,v.oz)("molecules-status"),options:ce})]}),ne=(0,a.jsxs)(g.M,{bordered:!0,form:R,onFinish:oe,className:Fe.sort,onValuesChange:function(r,n){return re(n)},children:[(0,a.jsx)(y.Z,{name:"sortBy",placeholder:"\u6392\u5E8F\u65B9\u5F0F",valueEnum:pe,allowClear:!1,fieldProps:{popupMatchSelectWidth:!1,onClick:function(r){return r.stopPropagation()}}}),(0,a.jsx)(P.A.Item,{name:"sortOrder",children:(0,a.jsx)(Le.Z,{})})]}),Se=(0,a.jsx)(N.Rs,{ghost:!0,pagination:{defaultPageSize:12,showSizeChanger:!1},showActions:"hover",rowSelection:!1,grid:{xs:1,sm:2,md:3,lg:3,xl:4,xxl:4},renderItem:function(r){return(0,a.jsx)(Ze,{compound:r})},request:je,params:Y});return(0,a.jsx)(b._z,{ghost:!0,content:Q,extraContent:ne,children:Se})},Te=Re},39175:function(U,C,e){e.d(C,{r:function(){return p}});var c=e(67294),d=Object.defineProperty,t=Object.getOwnPropertySymbols,m=Object.prototype.hasOwnProperty,O=Object.prototype.propertyIsEnumerable,f=(l,o,i)=>o in l?d(l,o,{enumerable:!0,configurable:!0,writable:!0,value:i}):l[o]=i,x=(l,o)=>{for(var i in o||(o={}))m.call(o,i)&&f(l,i,o[i]);if(t)for(var i of t(o))O.call(o,i)&&f(l,i,o[i]);return l};const p=l=>c.createElement("svg",x({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},l),c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.25 6a.75.75 0 0 0 0-1.5h-12a.75.75 0 1 0 0 1.5h12ZM14.25 10.5a.75.75 0 0 0 0-1.5h-12a.75.75 0 1 0 0 1.5h12ZM14.25 15a.75.75 0 0 0 0-1.5h-12a.75.75 0 1 0 0 1.5h12ZM14.25 19.5a.75.75 0 0 0 0-1.5h-12a.75.75 0 1 0 0 1.5h12ZM18 3.784c0-.697.807-1.046 1.28-.553l3 3.13a.807.807 0 0 1 0 1.107.728.728 0 0 1-1.061 0l-1.72-1.796v14.546a.774.774 0 0 1-.615.77L18.75 21c-.414 0-.75-.35-.75-.782V3.784Z",fill:"#1A90FF"}));var S="data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjQgMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNC4yNSA2YS43NS43NSAwIDAgMCAwLTEuNWgtMTJhLjc1Ljc1IDAgMSAwIDAgMS41aDEyWk0xNC4yNSAxMC41YS43NS43NSAwIDAgMCAwLTEuNWgtMTJhLjc1Ljc1IDAgMSAwIDAgMS41aDEyWk0xNC4yNSAxNWEuNzUuNzUgMCAwIDAgMC0xLjVoLTEyYS43NS43NSAwIDEgMCAwIDEuNWgxMlpNMTQuMjUgMTkuNWEuNzUuNzUgMCAwIDAgMC0xLjVoLTEyYS43NS43NSAwIDEgMCAwIDEuNWgxMlpNMTggMy43ODRjMC0uNjk3LjgwNy0xLjA0NiAxLjI4LS41NTNsMyAzLjEzYS44MDcuODA3IDAgMCAxIDAgMS4xMDcuNzI4LjcyOCAwIDAgMS0xLjA2MSAwbC0xLjcyLTEuNzk2djE0LjU0NmEuNzc0Ljc3NCAwIDAgMS0uNjE1Ljc3TDE4Ljc1IDIxYy0uNDE0IDAtLjc1LS4zNS0uNzUtLjc4MlYzLjc4NFoiIGZpbGw9IiMxQTkwRkYiLz48L3N2Zz4="}}]);

//# sourceMappingURL=p__workspace__my-compound__index.f624005e.async.js.map