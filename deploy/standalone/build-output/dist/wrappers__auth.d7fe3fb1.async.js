"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[5899],{39244:function(w,u,e){e.r(u),e.d(u,{default:function(){return g}});var h=e(43851),t=e(70831),x=[{path:"/",redirect:"/projects"},{path:"/user",layout:!1,routes:[{path:"/user/login",component:"./User/Login"}]},{name:"account.settings",path:"/settings",resource:"settings",component:"@/pages/user-settings",wrappers:["@/wrappers/auth"],hideInMenu:!0},{name:"list.playground",icon:"chart|svg",access:"internalStaff",resource:"inner_workspace",path:"/playground",routes:[{path:"/playground",wrappers:["@/wrappers/auth"],redirect:"/playground/commend"},{name:"commend",icon:"chart|svg",path:"/playground/commend",component:"./playground",resource:"inner_comment",wrappers:["@/wrappers/auth"],exact:!0,access:"internalStaff"},{name:"viewByBackbone",path:"/playground/commend/view-by-backbone/:backboneId",exact:!0,component:"@/pages/route/view-by-backbone",hideInMenu:!0},{name:"AISandbox",icon:"chart|svg",exact:!0,resource:"ai_sandbox",wrappers:["@/wrappers/auth"],path:"/playground/ai-sandbox",component:"@/pages/AISandbox",access:"internalStaff"}]},{name:"list.experimental-zone",icon:"experimental_zone|svg",path:"/experimental-zone",resource:"exp_zone",exact:!0,routes:[{path:"/experimental-zone",component:"@/pages/experimental-zone"},{name:"search",hideInMenu:!0,path:"/experimental-zone/search",component:"@/pages/experimental-zone/search",exact:!0}]},{name:"list.project-list",icon:"project|svg",resource:"project",path:"/projects",routes:[{path:"/projects",wrappers:["@/wrappers/auth"],component:"@/pages/projects",exact:!0,hideInMenu:!0},{name:"detail",path:"/projects/:id",exact:!0,component:"@/pages/projects/detail",hideInMenu:!0},{name:"detail.reaction",path:"/projects/:id/reaction/:reactionId",hideInMenu:!0,routes:[{path:"/projects/:id/reaction/:reactionId",component:"@/pages/reaction",exact:!0,hideInMenu:!0},{name:"execute.detail",path:"/projects/:id/reaction/:reactionId/experiment-execute/detail/:experimentalNo",component:"@/pages/experiment/experiment-execute/detail",exact:!0,hideInMenu:!0}]},{name:"detail.experimentalProcedure",path:"/projects/:projectId/reaction/:reactionId/experimental-procedure/detail/:experimentDesignId",hideInMenu:!0,routes:[{path:"/projects/:projectId/reaction/:reactionId/experimental-procedure/detail/:experimentDesignId",component:"@/pages/experimental-procedure/detail",exact:!0,hideInMenu:!0}]},{name:"detail.experiment-conclusion",path:"/projects/:projectId/reaction/:reactionId/experimental-procedure/conclusion/:experimentalNo",hideInMenu:!0,routes:[{path:"/projects/:projectId/reaction/:reactionId/experimental-procedure/conclusion/:experimentalNo",component:"@/pages/experimental-procedure/conclusion",exact:!0},{name:"knowledgeBase",path:"/projects/:projectId/reaction/:reactionId/experimental-procedure/conclusion/:experimentalNo/knowledgeBase/:experimentDesignId",component:"@/pages/experimental-procedure/detail",exact:!0,hideInMenu:!0},{name:"reportDetail",path:"/projects/:projectId/reaction/:reactionId/experimental-procedure/conclusion/:experimentalNo/reportDetail/:check_no",component:"@/pages/experimental-procedure/report-details",exact:!0,hideInMenu:!0}]},{name:"quotation",exact:!0,path:"/projects/:id/quotation-records",component:"@/pages/projects/quotation-records",hideInMenu:!0},{name:"detail.quoteDetail",path:"/projects/:id/quotation-records/:quoteMoleculeId/quote-info",component:"@/pages/projects/quotation-records/detail",exact:!0,hideInMenu:!0},{name:"detail.addMolecule",path:"/projects/:id/add-molecule",component:"@/pages/add-molecule",exact:!0,hideInMenu:!0},{name:"detail.compound",path:"/projects/:id/compound/:compoundId",hideInMenu:!0,routes:[{name:"detail",path:"/projects/:id/compound/:compoundId",component:"@/pages/compound/refactor",exact:!0,hideInMenu:!0},{name:"detail.create",path:"/projects/:id/compound/:compoundId/create",hideInMenu:!0,routes:[{path:"/projects/:id/compound/:compoundId/create",component:"./route/create",exact:!0,hideInMenu:!0}]},{name:"viewByBackbone",path:"/projects/:id/compound/:compoundId/view-by-backbone/:backboneId",component:"@/pages/route/view-by-backbone",exact:!0,hideInMenu:!0},{name:"viewByBackbone",path:"/projects/:id/compound/:compoundId/view/:routeId",component:"./route/view",hideInMenu:!0},{name:"edit",path:"/projects/:id/compound/:compoundId/edit/:routeId",component:"./route/edit",hideInMenu:!0}]}]},{name:"list.workspace",icon:"home",access:"internalStaff",resource:"home",path:"/workspace",exact:!0,routes:[{path:"/workspace",wrappers:["@/wrappers/auth"],redirect:"home"},{name:"myWorkbench",path:"my-workbench",component:"@/pages/workspace/my-workbench",resource:"workbench",wrappers:["@/wrappers/auth"],exact:!0},{name:"myCompound",path:"my-compound",component:"@/pages/workspace/my-compound",resource:"my_compound",wrappers:["@/wrappers/auth"],exact:!0},{name:"myReaction",path:"my-reaction",component:"@/pages/workspace/my-reaction",resource:"my_reaction",wrappers:["@/wrappers/auth"],exact:!0},{name:"myExperiment",path:"my-experiment",resource:"my_experiment",wrappers:["@/wrappers/auth"],component:"@/pages/workspace/my-experiment",exact:!0}]},{name:"list.route",hideInMenu:!0,routes:[{name:"viewByBackbone",path:"/route/view-by-backbone/:backboneId",component:"./route/view-by-backbone",hideInMenu:!0},{name:"view",path:"/route/view/:routeId",component:"./route/view",hideInMenu:!0}]},{name:"list.reaction",hideInMenu:!0,routes:[{name:"detail",path:"/reaction/:reactionId",component:"./reaction",hideInMenu:!0}]},{name:"list.experiment",path:"/experiment",icon:"execute|svg",hideInMenu:!0,routes:[{name:"plan",path:"/experiment/experiment-plan",component:"@/pages/experiment/experiment-plan",exact:!0},{name:"execute",path:"/experiment/experiment-execute",component:"@/pages/experiment/experiment-execute",exact:!0},{name:"execute.detail",path:"/experiment/experiment-execute/detail/:experimentalNo",component:"@/pages/experiment/experiment-execute/detail",exact:!0,hideInMenu:!0}]},{name:"list.procedure",icon:"experimentPlan|svg",path:"/experimental-procedure",hideInMenu:!0,exact:!0,routes:[{name:"detail",path:"/experimental-procedure",component:"@/pages/experimental-procedure",exact:!0,hideInMenu:!0},{name:"detail",path:"/experimental-procedure/detail/:experimentDesignId ",component:"@/pages/experimental-procedure/detail",exact:!0,hideInMenu:!0}]},{name:"list.material-manage",icon:"materialManage|svg",path:"/material",resource:"material",exact:!0,routes:[{path:"/material",wrappers:["@/wrappers/auth"],redirect:"manage"},{name:"storage",path:"/material/manage",component:"@/pages/material-manage",resource:"material_lib",wrappers:["@/wrappers/auth"],exact:!0},{name:"search-molecule",path:"/material/manage/search-molecule",component:"@/pages/add-molecule",exact:!0,hideInMenu:!0},{name:"black-list",path:"/material/black-list",component:"@/pages/material-manage/black-list",resource:"material_blacklist",wrappers:["@/wrappers/auth"],exact:!0},{name:"black-list.add",path:"/material/black-list/add",component:"@/pages/material-manage/black-list/add",exact:!0,hideInMenu:!0}]},{path:"/message-notification",component:"@/pages/message-notification",exact:!0,hideInMenu:!0},{name:"list.batch-retro",icon:"upload",path:"/batch-retro",component:"@/pages/batch-retro",exact:!0},{path:"*",layout:!1,component:"./404"}],n=e(85893),g=function(){var r=(0,t.useAccess)();function s(c,i){return c.forEach(function(a){i.push(a),a.routes&&a.routes.length!==0&&(a.children=s(a.routes,i))}),i}var I=(0,t.useRouteProps)(),m=s(x,[]),d=m.findIndex(function(c){return c.path===I.originPath});if(d>-1){var o,p,l=(o=m[d])===null||o===void 0?void 0:o.resource;return l&&r!==null&&r!==void 0&&(p=r.authCodeList)!==null&&p!==void 0&&p.includes(l)?(0,n.jsx)(t.Outlet,{}):(0,n.jsx)(t.Navigate,{to:"/workspace"})}return(0,n.jsx)(t.Navigate,{to:h.wm})}}}]);

//# sourceMappingURL=wrappers__auth.d7fe3fb1.async.js.map