{"version": 3, "file": "shared-Z0qsNOl9dn3JRzdZDyWyVGTV4_.a422b140.async.js", "mappings": "sRAQWA,EAAO,SAAcC,EAAM,CACpC,IAAIC,EAAUD,EAAK,QACnB,SAAoB,OAAK,MAAO,CAC9B,MAAO,CACL,QAASC,GAAW,QACtB,EACA,YAAuB,OAAK,KAAS,CACnC,MAAO,CACL,OAAQ,CACV,CACF,CAAC,CACH,CAAC,CACH,EACWC,GAAoB,CAC7B,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,IAAK,CACP,EACIC,EAAoB,SAA2BC,EAAO,CACxD,IAAIC,EAAOD,EAAM,KACfE,EAASF,EAAM,OACbG,KAAa,WAAQ,UAAY,CACnC,MAAO,CACL,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,IAAK,EACP,CACF,EAAG,CAAC,CAAC,EACDC,KAAMC,EAAA,GAAc,GAAKF,EACzBG,EAAU,OAAO,KAAKF,CAAG,EAAE,OAAO,SAAUG,EAAK,CACnD,OAAOH,EAAIG,CAAG,IAAM,EACtB,CAAC,EAAE,CAAC,GAAK,KACLC,EAAYP,IAAS,OAAYH,GAAkBQ,CAAO,GAAK,EAAIL,EACnEQ,EAAa,SAAoBC,EAAO,CAC1C,OAAIA,IAAU,EACL,EAELF,EAAY,EACP,GAEF,EACT,EACA,SAAoB,OAAK,IAAM,CAC7B,SAAU,GACV,MAAO,CACL,eAAgB,EAClB,EACA,YAAuB,OAAK,MAAO,CACjC,MAAO,CACL,MAAO,OACP,eAAgB,gBAChB,QAAS,MACX,EACA,SAAU,IAAI,MAAMA,CAAS,EAAE,KAAK,IAAI,EAAE,IAAI,SAAUG,EAAGD,EAAO,CAChE,SAAoB,QAAM,MAAO,CAC/B,MAAO,CACL,kBAAmBF,EAAY,GAAKE,IAAU,EAAI,6BAA+B,OACjF,mBAAoBD,EAAWC,CAAK,EACpC,KAAM,EACN,gBAAiBA,IAAU,EAAI,GAAK,CACtC,EACA,SAAU,IAAc,OAAK,IAAU,CACrC,OAAQR,EACR,UAAW,GACX,MAAO,CACL,MAAO,IACP,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,KAAgB,OAAK,IAAS,OAAQ,CACrC,OAAQA,EACR,MAAO,CACL,OAAQ,EACV,CACF,CAAC,CAAC,CACJ,EAAGQ,CAAK,CACV,CAAC,CACH,CAAC,CACH,CAAC,CACH,EAGWE,GAAmB,SAA0BC,EAAO,CAC7D,IAAIX,EAASW,EAAM,OACnB,SAAoB,QAAM,WAAW,CACnC,SAAU,IAAc,OAAK,IAAM,CACjC,SAAU,GAGV,MAAO,CACL,aAAc,CAChB,EACA,OAAQ,CACN,KAAM,CACJ,QAAS,EACX,CACF,EACA,YAAuB,QAAM,MAAO,CAClC,MAAO,CACL,MAAO,OACP,QAAS,OACT,WAAY,SACZ,eAAgB,eAClB,EACA,SAAU,IAAc,OAAK,MAAO,CAClC,MAAO,CACL,SAAU,OACV,KAAM,CACR,EACA,YAAuB,OAAK,IAAU,CACpC,OAAQX,EACR,MAAO,CACL,MAAO,IACP,MAAO,CACL,iBAAkB,CACpB,CACF,EACA,UAAW,CACT,KAAM,EACN,MAAO,CACL,OAAQ,CACV,CACF,CACF,CAAC,CACH,CAAC,KAAgB,OAAK,IAAS,OAAQ,CACrC,OAAQA,EACR,KAAM,QACN,MAAO,CACL,MAAO,IACP,iBAAkB,EACpB,CACF,CAAC,CAAC,CACJ,CAAC,CACH,CAAC,KAAgB,OAAKP,EAAM,CAAC,CAAC,CAAC,CACjC,CAAC,CACH,EAGWmB,EAAe,SAAsBC,EAAO,CACrD,IAAId,EAAOc,EAAM,KACfC,EAAeD,EAAM,OACrBb,EAASc,IAAiB,OAAS,GAAOA,EAC1CC,EAAeF,EAAM,aACvB,SAAoB,QAAM,IAAM,CAC9B,SAAU,GACV,OAAQ,CACN,KAAM,CACJ,QAAS,CACX,CACF,EACA,SAAU,CAAC,IAAI,MAAMd,CAAI,EAAE,KAAK,IAAI,EAAE,IAAI,SAAUU,EAAGD,EAAO,CAC5D,SAGE,OAAKE,GAAkB,CACrB,OAAQ,CAAC,CAACV,CACZ,EAAGQ,CAAK,CAEZ,CAAC,EAAGO,IAAiB,OAAsB,OAAK,IAAM,CACpD,SAAU,GACV,MAAO,CACL,qBAAsB,EACtB,oBAAqB,CACvB,EACA,OAAQ,CACN,KAAM,CACJ,QAAS,OACT,WAAY,SACZ,eAAgB,QAClB,CACF,EACA,YAAuB,OAAK,IAAS,OAAQ,CAC3C,MAAO,CACL,MAAO,GACT,EACA,OAAQf,EACR,KAAM,OACR,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CACH,EAOWgB,EAAqB,SAA4BC,EAAO,CACjE,IAAIjB,EAASiB,EAAM,OACnB,SAAoB,QAAM,MAAO,CAC/B,MAAO,CACL,eAAgB,EAClB,EACA,SAAU,IAAc,OAAK,IAAU,CACrC,UAAW,GACX,MAAO,CACL,MAAO,GACT,CACF,CAAC,KAAgB,OAAK,IAAS,OAAQ,CACrC,OAAQjB,EACR,KAAM,OACR,CAAC,CAAC,CACJ,CAAC,CACH,EAMWkB,EAAsB,SAA6BC,EAAO,CACnE,IAAInB,EAASmB,EAAM,OACnB,SAAoB,OAAK,IAAM,CAC7B,SAAU,GACV,MAAO,CACL,wBAAyB,EACzB,uBAAwB,CAC1B,EACA,OAAQ,CACN,KAAM,CACJ,gBAAiB,CACnB,CACF,EACA,YAAuB,QAAM,IAAO,CAClC,MAAO,CACL,MAAO,OACP,eAAgB,eAClB,EACA,SAAU,IAAc,OAAK,IAAS,OAAQ,CAC5C,OAAQnB,EACR,MAAO,CACL,MAAO,GACT,EACA,KAAM,OACR,CAAC,KAAgB,QAAM,IAAO,CAC5B,SAAU,IAAc,OAAK,IAAS,OAAQ,CAC5C,OAAQA,EACR,KAAM,QACN,MAAO,CACL,MAAO,GACT,CACF,CAAC,KAAgB,OAAK,IAAS,OAAQ,CACrC,OAAQA,EACR,KAAM,QACN,MAAO,CACL,MAAO,EACT,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,CACH,CAAC,CACH,EACIoB,EAAmB,SAA0BC,EAAO,CACtD,IAAIC,EAAeD,EAAM,OACvBrB,EAASsB,IAAiB,OAAS,GAAOA,EAC1CC,EAAYF,EAAM,UAClBN,EAAeM,EAAM,aACrBG,EAAUH,EAAM,QAChBI,EAAaJ,EAAM,WACnBK,EAAaL,EAAM,KACnBM,EAAOD,IAAe,OAAS,EAAIA,EACrC,SAAoB,QAAM,MAAO,CAC/B,MAAO,CACL,MAAO,MACT,EACA,SAAU,CAACD,IAAe,OAAsB,OAAKT,EAAoB,CACvE,OAAQhB,CACV,CAAC,EAAGuB,IAAc,OAAsB,OAAK1B,EAAmB,CAC9D,KAAM0B,EACN,OAAQvB,CACV,CAAC,GAAIwB,IAAY,IAASG,IAAS,QAAuB,QAAM,IAAM,CACpE,SAAU,GACV,OAAQ,CACN,KAAM,CACJ,QAAS,CACX,CACF,EACA,SAAU,CAACH,IAAY,OAAsB,OAAKN,EAAqB,CACrE,OAAQlB,CACV,CAAC,EAAG2B,IAAS,OAAsB,OAAKf,EAAc,CACpD,KAAMe,EACN,OAAQ3B,EACR,aAAce,CAChB,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,CACH,EACA,EAAeK,ECtSX,GAAoB,CACtB,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,IAAK,CACP,EACIQ,GAAgC,SAAuClC,EAAM,CAC/E,IAAIM,EAASN,EAAK,OAClB,SAAoB,QAAM,MAAO,CAC/B,MAAO,CACL,iBAAkB,EACpB,EACA,SAAU,IAAc,OAAK,IAAS,OAAQ,CAC5C,OAAQM,EACR,KAAM,QACN,MAAO,CACL,MAAO,IACP,eAAgB,EAClB,CACF,CAAC,KAAgB,QAAM,MAAO,CAC5B,MAAO,CACL,MAAO,OACP,eAAgB,gBAChB,QAAS,MACX,EACA,SAAU,IAAc,QAAM,MAAO,CACnC,MAAO,CACL,KAAM,EACN,gBAAiB,GACjB,SAAU,GACZ,EACA,SAAU,IAAc,OAAK,IAAU,CACrC,OAAQA,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,KAAgB,OAAK,IAAU,CAC9B,OAAQA,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,KAAgB,OAAK,IAAU,CAC9B,OAAQA,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,CAAC,CACJ,CAAC,KAAgB,OAAK,MAAO,CAC3B,MAAO,CACL,KAAM,EACN,WAAY,SACZ,eAAgB,QAClB,EACA,YAAuB,QAAM,MAAO,CAClC,MAAO,CACL,SAAU,IACV,OAAQ,MACV,EACA,SAAU,IAAc,OAAK,IAAU,CACrC,OAAQA,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,KAAgB,OAAK,IAAU,CAC9B,OAAQA,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,CACH,EACI6B,GAA2B,SAAkC/B,EAAO,CACtE,IAAIC,EAAOD,EAAM,KACfE,EAASF,EAAM,OACbG,KAAa,WAAQ,UAAY,CACnC,MAAO,CACL,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,IAAK,EACP,CACF,EAAG,CAAC,CAAC,EACDC,KAAMC,EAAA,GAAc,GAAKF,EACzBG,EAAU,OAAO,KAAKF,CAAG,EAAE,OAAO,SAAUG,EAAK,CACnD,OAAOH,EAAIG,CAAG,IAAM,EACtB,CAAC,EAAE,CAAC,GAAK,KACLC,EAAYP,IAAS,OAAY,GAAkBK,CAAO,GAAK,EAAIL,EACvE,SAAoB,OAAK,MAAO,CAC9B,MAAO,CACL,MAAO,OACP,eAAgB,gBAChB,QAAS,MACX,EACA,SAAU,IAAI,MAAMO,CAAS,EAAE,KAAK,IAAI,EAAE,IAAI,SAAUG,EAAGD,EAAO,CAChE,SAAoB,QAAM,MAAO,CAC/B,MAAO,CACL,KAAM,EACN,mBAAoBA,IAAU,EAAI,EAAI,GACtC,iBAAkBA,IAAUF,EAAY,EAAI,EAAI,EAClD,EACA,SAAU,IAAc,OAAK,IAAU,CACrC,OAAQN,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,KAAgB,OAAK,IAAU,CAC9B,OAAQA,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,KAAgB,OAAK,IAAU,CAC9B,OAAQA,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,CAAC,CACJ,EAAGQ,CAAK,CACV,CAAC,CACH,CAAC,CACH,EAOWsB,GAAoB,SAA2BnB,EAAO,CAC/D,IAAIX,EAASW,EAAM,OACjBoB,EAAepB,EAAM,OACrBqB,EAASD,IAAiB,OAAS,GAAQA,EACzC9B,KAAa,WAAQ,UAAY,CACnC,MAAO,CACL,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,IAAK,EACP,CACF,EAAG,CAAC,CAAC,EACDC,KAAMC,EAAA,GAAc,GAAKF,EACzBG,EAAU,OAAO,KAAKF,CAAG,EAAE,OAAO,SAAUG,EAAK,CACnD,OAAOH,EAAIG,CAAG,IAAM,EACtB,CAAC,EAAE,CAAC,GAAK,KACLC,EAAY,GAAkBF,CAAO,GAAK,EAC9C,SAAoB,QAAM,WAAW,CACnC,SAAU,IAAc,QAAM,MAAO,CACnC,MAAO,CACL,QAAS,OACT,WAAY4B,EAAS,mBAAqB,OAC1C,QAAS,UACX,EACA,SAAU,CAAC,IAAI,MAAM1B,CAAS,EAAE,KAAK,IAAI,EAAE,IAAI,SAAUG,EAAGD,EAAO,CACjE,SAAoB,OAAK,MAAO,CAC9B,MAAO,CACL,KAAM,EACN,mBAAoBwB,GAAUxB,IAAU,EAAI,EAAI,GAChD,iBAAkB,EACpB,EACA,YAAuB,OAAK,IAAU,CACpC,OAAQR,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,OAAQ,EACR,OAAQ,GACR,MAAOgC,EAAS,OAAS,MAC3B,CACF,CACF,CAAC,CACH,EAAGxB,CAAK,CACV,CAAC,KAAgB,OAAK,MAAO,CAC3B,MAAO,CACL,KAAM,EACN,mBAAoB,EACtB,EACA,YAAuB,OAAK,IAAU,CACpC,OAAQR,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,OAAQ,EACR,OAAQ,GACR,MAAOgC,EAAS,OAAS,MAC3B,CACF,CACF,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,KAAgB,OAAKvC,EAAM,CAC1B,QAAS,SACX,CAAC,CAAC,CACJ,CAAC,CACH,EAOWwC,GAAgB,SAAuBpB,EAAO,CACvD,IAAIb,EAASa,EAAM,OACjBqB,EAAarB,EAAM,KACnBd,EAAOmC,IAAe,OAAS,EAAIA,EACrC,SAAoB,QAAM,IAAM,CAC9B,SAAU,GACV,SAAU,IAAc,OAAK,IAAS,OAAQ,CAC5C,OAAQlC,EACR,KAAM,QACN,MAAO,CACL,MAAO,IACP,eAAgB,EAClB,CACF,CAAC,KAAgB,OAAK8B,GAAmB,CACvC,OAAQ,GACR,OAAQ9B,CACV,CAAC,EAAG,IAAI,MAAMD,CAAI,EAAE,KAAK,IAAI,EAAE,IAAI,SAAUU,EAAGD,EAAO,CACrD,SAGE,OAAKsB,GAAmB,CACtB,OAAQ9B,CACV,EAAGQ,CAAK,CAEZ,CAAC,KAAgB,OAAK,MAAO,CAC3B,MAAO,CACL,QAAS,OACT,eAAgB,WAChB,kBAAmB,EACrB,EACA,YAAuB,OAAK,IAAU,CACpC,OAAQR,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,OAAQ,EACR,OAAQ,GACR,MAAO,QACP,SAAU,OACZ,CACF,CACF,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CACH,EACWmC,GAAuB,SAA8BlB,EAAO,CACrE,IAAIjB,EAASiB,EAAM,OACnB,SAAoB,QAAM,IAAM,CAC9B,SAAU,GACV,MAAO,CACL,qBAAsB,EACtB,oBAAqB,CACvB,EACA,SAAU,IAAc,OAAK,IAAS,OAAQ,CAC5C,OAAQjB,EACR,KAAM,QACN,MAAO,CACL,MAAO,IACP,eAAgB,EAClB,CACF,CAAC,KAAgB,OAAK6B,GAA0B,CAC9C,OAAQ7B,CACV,CAAC,KAAgB,OAAK4B,GAA+B,CACnD,OAAQ5B,CACV,CAAC,CAAC,CACJ,CAAC,CACH,EACIoC,GAA2B,SAAkCjB,EAAO,CACtE,IAAIkB,EAAelB,EAAM,OACvBnB,EAASqC,IAAiB,OAAS,GAAOA,EAC1CZ,EAAaN,EAAM,WACnBQ,EAAOR,EAAM,KACf,SAAoB,QAAM,MAAO,CAC/B,MAAO,CACL,MAAO,MACT,EACA,SAAU,CAACM,IAAe,OAAsB,OAAKT,EAAoB,CACvE,OAAQhB,CACV,CAAC,KAAgB,OAAKmC,GAAsB,CAC1C,OAAQnC,CACV,CAAC,EAAG2B,IAAS,OAAsB,OAAKlC,EAAM,CAAC,CAAC,EAAGkC,IAAS,OAAsB,OAAKM,GAAe,CACpG,OAAQjC,EACR,KAAM2B,CACR,CAAC,CAAC,CACJ,CAAC,CACH,EACA,GAAeS,GC9TXE,EAAqB,SAA4B5C,EAAM,CACzD,IAAI6C,EAAc7C,EAAK,OACrBM,EAASuC,IAAgB,OAAS,GAAOA,EACzCd,EAAa/B,EAAK,WACpB,SAAoB,QAAM,MAAO,CAC/B,MAAO,CACL,MAAO,MACT,EACA,SAAU,CAAC+B,IAAe,OAAsB,OAAKT,EAAoB,CACvE,OAAQhB,CACV,CAAC,KAAgB,OAAK,IAAM,CAC1B,YAAuB,QAAM,MAAO,CAClC,MAAO,CACL,QAAS,OACT,eAAgB,SAChB,WAAY,SACZ,cAAe,SACf,QAAS,GACX,EACA,SAAU,IAAc,OAAK,IAAS,OAAQ,CAC5C,KAAM,GACN,MAAO,CACL,eAAgB,EAClB,CACF,CAAC,KAAgB,OAAK,IAAS,OAAQ,CACrC,OAAQA,EACR,MAAO,CACL,MAAO,IACP,eAAgB,CAClB,CACF,CAAC,KAAgB,OAAK,IAAS,OAAQ,CACrC,OAAQA,EACR,MAAO,CACL,MAAO,GACT,EACA,KAAM,OACR,CAAC,KAAgB,QAAM,IAAO,CAC5B,MAAO,CACL,iBAAkB,EACpB,EACA,SAAU,IAAc,OAAK,IAAS,OAAQ,CAC5C,OAAQA,EACR,MAAO,CACL,MAAO,GACT,CACF,CAAC,KAAgB,OAAK,IAAS,OAAQ,CACrC,OAAQA,EACR,MAAO,CACL,MAAO,GACT,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CACH,EACA,GAAesC,EC3DXE,GAAY,CAAC,MAAM,EAOnBC,GAAc,SAAqB/C,EAAM,CAC3C,IAAIgD,EAAYhD,EAAK,KACnBiD,EAAOD,IAAc,OAAS,OAASA,EACvCE,KAAO,MAAyBlD,EAAM8C,EAAS,EACjD,OAAIG,IAAS,YACS,OAAKE,MAAoB,KAAc,CAAC,EAAGD,CAAI,CAAC,EAElED,IAAS,kBACS,OAAKG,MAA0B,KAAc,CAAC,EAAGF,CAAI,CAAC,KAExD,OAAKG,KAAkB,KAAc,CAAC,EAAGH,CAAI,CAAC,CACpE,EAEA,GAAeH,E,6KCfXO,GAAe,SAAsBC,EAAOC,EAAK,CACnD,OAAoB,gBAAoBC,EAAA,KAAU,MAAS,CAAC,EAAGF,EAAO,CACpE,IAAKC,EACL,KAAM,GACR,CAAC,CAAC,CACJ,EAGIE,EAAuB,aAAiBJ,EAAY,EAIxD,GAAeI,E,+JCbXZ,GAAY,CAAC,gBAAiB,YAAa,gBAAiB,qBAAsB,SAAU,aAAc,eAAgB,WAAY,iBAAiB,EACzJa,GAAa,CAAC,SAAU,WAAY,oBAAqB,gBAAiB,YAAa,OAAO,EAc5FC,GAA0C,gBAAoB,MAAS,EAG3E,SAASC,GAAcN,EAAO,CAC5B,IAAIO,EAAWP,EAAM,SACnBQ,EAASR,EAAM,OACfS,EAAWT,EAAM,SACjBU,EAAgBV,EAAM,cACtBW,EAAYX,EAAM,UAChBY,KAAY,cAAWP,EAA0B,EACrD,OAAoB,eAAmBE,KAAU,QAAc,KAAc,CAAC,EAAGA,EAAS,KAAK,EAAG,CAAC,EAAG,CACpG,QAAS,UAAY,CACnB,IAAIM,KAAW,QAAgC,KAAoB,EAAE,KAAK,SAASC,EAAQC,EAAG,CAC5F,IAAIC,EAAuBC,EAAiBC,EACxCC,EACJ,SAAO,KAAoB,EAAE,KAAK,SAAkBC,EAAU,CAC5D,OAAU,OAAQA,EAAS,KAAOA,EAAS,KAAM,CAC/C,IAAK,GACH,OAAAA,EAAS,KAAO,GACRJ,GAAyBC,EAAkBV,EAAS,OAAO,WAAa,MAAQS,IAA0B,OAAS,OAASA,EAAsB,KAAKC,EAAiBF,CAAC,EACnL,IAAK,GAEH,GADAI,EAAOC,EAAS,KACVD,IAAS,GAAQ,CACrBC,EAAS,KAAO,EAChB,KACF,CACA,OAAOA,EAAS,OAAO,QAAQ,EACjC,IAAK,GACHR,GAAc,OAAiCM,EAAqBN,EAAU,WAAa,MAAQM,IAAuB,QAAUA,EAAmB,cAAcV,EAAQ,CAC3K,SAAUC,EACV,cAAeC,EACf,UAAWC,CACb,CAAC,EACH,IAAK,GACL,IAAK,MACH,OAAOS,EAAS,KAAK,CACzB,CACF,EAAGN,CAAO,CACZ,CAAC,CAAC,EACF,SAASO,EAAQC,EAAI,CACnB,OAAOT,EAAS,MAAM,KAAM,SAAS,CACvC,CACA,OAAOQ,CACT,EAAE,CACJ,CAAC,CAAC,CACJ,CAOA,SAASE,EAAcvB,EAAO,CAC5B,IAAIwB,EAAkBC,EAClBC,KAAO,MAAQ,EACfC,EAAgB3B,EAAM,cACxB4B,EAAY5B,EAAM,UAClB6B,EAAgB7B,EAAM,cACtB8B,EAAqB9B,EAAM,mBAC3B+B,EAAS/B,EAAM,OACfgC,EAAahC,EAAM,WACnBiC,EAAejC,EAAM,aACrBkC,EAAWlC,EAAM,SACjBmC,EAAkBnC,EAAM,gBACxBL,KAAO,KAAyBK,EAAOT,EAAS,EAC9C6C,KAAU,UAAO,MAAS,EAC1BxB,KAAY,UAAO,EACnByB,KAAU,UAAO,KAGrB,uBAAoB1C,EAAK,UAAW,UAAY,CAC9C,OAAOiB,EAAU,OACnB,EAAG,CAACA,EAAU,OAAO,CAAC,EACtB,IAAI0B,KAAkBC,GAAA,GAAe,UAAY,CAC7C,OAAOvC,EAAM,OAASiC,GAAgB,CAAC,CACzC,EAAG,CACD,MAAOjC,EAAM,MACb,SAAUA,EAAM,QAClB,CAAC,EACDwC,MAAmB,MAAeF,EAAiB,CAAC,EACpDG,EAAQD,GAAiB,CAAC,EAC1BE,EAAWF,GAAiB,CAAC,EAC3BG,EAAY,UAAc,UAAY,CACxC,OAAI,OAAOZ,GAAW,WACbA,EAEF,SAAUvB,EAAQjD,EAAO,CAC9B,OAAOiD,EAAOuB,CAAM,GAAKxE,CAC3B,CACF,EAAG,CAACwE,CAAM,CAAC,EAOPa,KAAcC,EAAA,GAAe,SAAUC,EAAc,CAIvD,GAAI,OAAOA,GAAiB,UAAY,CAAC9C,EAAM,KAAM,CACnD,GAAI8C,GAAgBL,EAAM,OAAQ,OAAOK,EACzC,IAAIC,EAAUN,GAASA,EAAMK,CAAY,EACzC,OAAOH,GAAc,KAA+B,OAASA,EAAUI,EAASD,CAAY,CAC9F,CAKA,IAAK,OAAOA,GAAiB,UAAYA,GAAgBL,EAAM,SAAWzC,EAAM,KAAM,CACpF,IAAIgD,EAAYP,EAAM,UAAU,SAAUQ,EAAM1F,EAAO,CACrD,IAAI2F,EACJ,OAAQP,GAAc,OAAiCO,EAAaP,EAAUM,EAAM1F,CAAK,KAAO,MAAQ2F,IAAe,OAAS,OAASA,EAAW,SAAS,MAAQJ,GAAiB,KAAkC,OAASA,EAAa,SAAS,EACzP,CAAC,EACD,GAAIE,IAAc,GAAI,OAAOA,CAC/B,CACA,OAAOF,CACT,CAAC,KAGD,uBAAoBX,EAAiB,UAAY,CAM/C,IAAIgB,EAAa,SAAoBC,EAAU,CAC7C,IAAIC,EAAuBC,EAC3B,GAAIF,GAAY,KACd,MAAM,IAAI,MAAM,sBAAsB,EAExC,IAAIN,EAAeF,EAAYQ,CAAQ,EACnCG,GAAa,CAACvD,EAAM,MAAOqD,EAAwBP,GAAiB,KAAkC,OAASA,EAAa,SAAS,KAAO,MAAQO,IAA0B,OAASA,EAAwB,EAAE,EAAE,KAAK,CAAC,EAAE,OAAO,OAAO,EAC7O,OAAQC,EAAmBjB,EAAQ,WAAa,MAAQiB,IAAqB,OAAS,OAASA,EAAiB,cAAcC,EAAU,CAC1I,EAMIC,EAAc,UAAuB,CACvC,IAAIC,EACAF,EAAa,CAACvD,EAAM,IAAI,EAAE,KAAK,CAAC,EAAE,OAAO,OAAO,EACpD,GAAI,MAAM,QAAQuD,CAAU,GAAKA,EAAW,SAAW,EAAG,CACxD,IAAIG,EACAX,GAAWW,EAAoBrB,EAAQ,WAAa,MAAQqB,IAAsB,OAAS,OAASA,EAAkB,eAAe,EACzI,OAAI,MAAM,QAAQX,CAAO,EAAUA,EAC5B,OAAO,KAAKA,CAAO,EAAE,IAAI,SAAU3F,GAAK,CAC7C,OAAO2F,EAAQ3F,EAAG,CACpB,CAAC,CACH,CACA,OAAQqG,EAAoBpB,EAAQ,WAAa,MAAQoB,IAAsB,OAAS,OAASA,EAAkB,cAAcF,CAAU,CAC7I,EACA,SAAO,QAAc,KAAc,CAAC,EAAGlB,EAAQ,OAAO,EAAG,CAAC,EAAG,CAC3D,WAAYc,EACZ,YAAaK,EAOb,WAAY,SAAoBJ,EAAUO,EAAM,CAC9C,IAAIC,EAAwBC,EAC5B,GAAIT,GAAY,KACd,MAAM,IAAI,MAAM,sBAAsB,EAExC,IAAIN,GAAeF,EAAYQ,CAAQ,EACnCG,GAAa,CAACvD,EAAM,MAAO4D,EAAyBd,IAAiB,KAAkC,OAASA,GAAa,SAAS,KAAO,MAAQc,IAA2B,OAASA,EAAyB,EAAE,EAAE,KAAK,CAAC,EAAE,OAAO,OAAO,EAC5OE,GAAa,OAAO,OAAO,CAAC,KAAG,QAAc,KAAc,CAAC,EAAGX,EAAWC,CAAQ,CAAC,EAAGO,GAAQ,CAAC,CAAC,CAAC,EACjGI,MAAeC,GAAA,GAAI,CAAC,EAAGT,GAAYO,EAAU,EACjD,OAACD,EAAoBxB,EAAQ,WAAa,MAAQwB,IAAsB,QAAUA,EAAkB,eAAeE,EAAY,EACxH,EACT,CACF,CAAC,CACH,EAAG,CAACnB,EAAa5C,EAAM,KAAMqC,EAAQ,OAAO,CAAC,KAC7C,aAAU,UAAY,CACfrC,EAAM,aACVyC,GAAS,CAAC,GAAG,QAAQ,SAAUwB,EAAS1G,EAAO,CAC9C,IAAI2G,GACHA,EAAoB7B,EAAQ,WAAa,MAAQ6B,IAAsB,QAAUA,EAAkB,kBAAe,KAAgB,CAAC,EAAG,GAAG,OAAOvB,EAAUsB,EAAS1G,CAAK,CAAC,EAAG0G,CAAO,CAAC,CACvL,EAAG,CAAC,CAAC,CAEP,EAAG,IAACE,EAAA,IAAU1B,CAAK,EAAGzC,EAAM,UAAU,CAAC,KACvC,aAAU,UAAY,CACpB,GAAIA,EAAM,KAAM,CACd,IAAIoE,EACJ/B,EAAQ,QAAUrC,GAAU,OAA6BoE,EAAkBpE,EAAM,YAAc,MAAQoE,IAAoB,OAAS,OAASA,EAAgB,IAC/J,CACF,EAAG,EAAE5C,EAAmBxB,EAAM,YAAc,MAAQwB,IAAqB,OAAS,OAASA,EAAiB,KAAMxB,EAAM,IAAI,CAAC,EAC7H,IAAIvD,EAAOqF,GAAsB,CAAC,EAChCtB,EAAS/D,EAAK,OACdgE,EAAWhE,EAAK,SAChB4H,GAAoB5H,EAAK,kBACzBiE,EAAgBjE,EAAK,cACrBkE,GAAYlE,EAAK,UACjB6H,EAAQ7H,EAAK,MACb8H,MAAkB,KAAyB9H,EAAM2D,EAAU,EACzDoE,EAAQ/D,IAAa,MACrBgE,MAAmB,WAAQ,UAAY,CACzC,OAAI,OAAO7C,GAAc,UAAYA,IAAca,GAAU,KAA2B,OAASA,EAAM,QAC9F,GAEFX,IAAuB,OAAsB,OAAKxB,GAAe,CACtE,UAAQoE,GAAA,GAAYlE,EAAQiC,GAAU,KAA2B,OAASA,EAAM,OAAQA,CAAK,GAAK,CAAC,EACnG,SAAUhC,EACV,aAAWiE,GAAA,GAAY/D,GAAW8B,GAAU,KAA2B,OAASA,EAAM,OAAQA,CAAK,EACnG,cAAe/B,EACf,YAAuB,OAAK,SAAQ,QAAc,KAAc,CAC9D,KAAM,SACN,SAAO,KAAc,CACnB,QAAS,QACT,OAAQ,SACR,MAAO,MACT,EAAG4D,CAAK,EACR,QAAmB,OAAK,GAAc,CAAC,CAAC,CAC1C,EAAGC,EAAe,EAAG,CAAC,EAAG,CACvB,SAAUF,IAAqB3C,EAAK,WAAW,2BAA4B,sCAAQ,CACrF,CAAC,CAAC,CACJ,CAAC,CAEH,EAAG,CAACI,EAAoBF,EAAWa,GAAU,KAA2B,OAASA,EAAM,MAAM,CAAC,EAC1FkC,MAAoB,WAAQ,UAAY,CAC1C,OAAKF,GAGDD,EACK,CACL,WAAY,CACV,OAAQ,CACN,QAAS,SAAiB3H,EAAO,CAC/B,IAAI+H,EACAC,EAAYhI,EAAM,UACpB0D,EAAW1D,EAAM,SACnB,SAAoB,QAAM,QAAS,CACjC,UAAWgI,EACX,SAAU,CAACtE,KAAuB,QAAM,KAAM,CAC5C,MAAO,CACL,SAAU,UACZ,EACA,SAAU,IAAc,OAAK,KAAM,CACjC,QAAS,EACT,MAAO,CACL,WAAY,QACd,EACA,SAAUkE,EACZ,CAAC,KAAgB,OAAK,KAAM,CAC1B,MAAO,CACL,SAAU,WACV,KAAM,EACN,MAAO,MACT,EACA,SAAUG,EAAgBjF,EAAK,WAAa,MAAQiF,IAAkB,OAAS,OAASA,EAAc,OACtG,SAAUH,EACZ,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,CACH,CACF,CACF,CACF,EAEK,CACL,gBAAiB,SAAyBjH,EAAGsH,EAAK,CAChD,IAAIC,EAAuBC,EAC3B,SAAoB,QAAM,WAAW,CACnC,SAAU,EAAED,GAAyBC,EAAyBhF,EAAM,mBAAqB,MAAQgF,IAA2B,OAAS,OAASA,EAAuB,KAAKhF,EAAOxC,EAAGsH,CAAG,KAAO,MAAQC,IAA0B,OAASA,EAAwBD,EAAKL,EAAgB,CACxR,CAAC,CACH,CACF,EA7CS,CAAC,CA+CZ,EAAG,CAACD,EAAOC,EAAgB,CAAC,EACxBQ,MAAgB,KAAc,CAAC,EAAGjF,EAAM,QAAQ,EAOhDkF,MAAmBrC,EAAA,GAAe,SAAUsC,EAAGC,EAAY,CAC7D,IAAIC,EAAkBC,EAAuBC,EAG7C,IAFCF,EAAmBrF,EAAM,YAAc,MAAQqF,IAAqB,SAAWC,EAAwBD,EAAiB,kBAAoB,MAAQC,IAA0B,QAAUA,EAAsB,KAAKD,EAAkBF,EAAGC,CAAU,GAClPG,EAAwBvF,EAAM,kBAAoB,MAAQuF,IAA0B,QAAUA,EAAsB,KAAKvF,EAAOoF,EAAYD,CAAC,EAC1InF,EAAM,WAAY,CACpB,IAAIwF,EACJxF,GAAU,OAA6BwF,EAAkBxF,EAAM,YAAc,MAAQwF,IAAoB,QAAUA,EAAgB,KAAKxF,EAAOoF,CAAU,CAC3J,CACF,CAAC,EACD,OAAIpF,GAAU,MAA4BA,EAAM,iBAAmByB,EAAmBzB,EAAM,YAAc,MAAQyB,IAAqB,QAAUA,EAAiB,gBAElKzB,EAAM,YAAcA,IAAU,MAAQA,IAAU,QAAUA,EAAM,YAC9DiF,GAAc,eAAiBC,OAEb,QAAM,WAAW,CACnC,SAAU,IAAc,OAAK7E,GAA2B,SAAU,CAChE,MAAOO,EACP,YAAuB,OAAK,QAAU,QAAc,QAAc,KAAc,CAC9E,OAAQ,GACR,QAAS,GACT,WAAY,GACZ,OAAQmB,EACR,kBAAmB,EACrB,EAAGpC,CAAI,EAAGgF,EAAiB,EAAG,CAAC,EAAG,CAChC,YAAa,QACb,UAAW/D,EACX,SAAUe,EACV,YAAU,QAAc,KAAc,CAAC,EAAGsD,EAAa,EAAG,CAAC,EAAG,CAC5D,aAAW,KAAc,CACvB,QAAS5C,CACX,EAAG4C,GAAc,SAAS,CAC5B,CAAC,EACD,WAAYxC,EACZ,mBAAoB,SAA4B2C,EAAY,CAK1D,GAJA1C,EAAS0C,CAAU,EAIfpF,EAAM,MAAQS,IAAa,MAAO,CACpC,IAAIgF,EACAC,KAAW1B,GAAA,GAAI,CAAC,EAAG,CAAChE,EAAM,IAAI,EAAE,KAAK,CAAC,EAAE,OAAO,OAAO,EAAGoF,CAAU,GACtEK,EAAoBpD,EAAQ,WAAa,MAAQoD,IAAsB,QAAUA,EAAkB,eAAeC,CAAQ,CAC7H,CACF,CACF,CAAC,CAAC,CACJ,CAAC,EAAG1F,EAAM,QAAoB,OAAK,IAAmB,CACpD,KAAM,CAACA,EAAM,IAAI,EACjB,SAAU,SAAkB2F,EAAa,CACvC,IAAIC,EAAkBC,EACtB,GAAI,CAACzD,EAAQ,QACX,OAAAA,EAAQ,QAAUK,EACX,KAET,IAAI/D,KAAOoH,GAAA,GAAIH,EAAa,CAAC3F,EAAM,IAAI,EAAE,KAAK,CAAC,CAAC,EAC5C+F,EAAarH,GAAS,KAA0B,OAASA,EAAK,KAAK,SAAUuE,EAAM1F,GAAO,CAC5F,IAAIyI,GACJ,MAAO,IAACC,GAAA,GAAiBhD,GAAO+C,GAAmB5D,EAAQ,WAAa,MAAQ4D,KAAqB,OAAS,OAASA,GAAiBzI,EAAK,CAAC,CAChJ,CAAC,EAED,OADA6E,EAAQ,QAAUK,EACbsD,IAEL/F,GAAU,OAA6B4F,EAAmB5F,EAAM,YAAc,MAAQ4F,IAAqB,SAAWC,EAAwBD,EAAiB,kBAAoB,MAAQC,IAA0B,QAAUA,EAAsB,KAAKD,EAAkBG,EAAYrH,CAAI,GACrR,IACT,CACF,CAAC,EAAI,IAAI,CACX,CAAC,CACH,CAOA,SAASwH,EAAmBlG,EAAO,CACjC,IAAImG,EAAO,KAAQ,gBAAgB,EACnC,OAAKnG,EAAM,QAMS,OAAK,KAAK,QAAM,QAAc,KAAc,CAC9D,MAAO,CACL,SAAU,MACZ,CACF,EAAGA,GAAU,KAA2B,OAASA,EAAM,aAAa,EAAG,CAAC,EAAG,CACzE,KAAMA,EAAM,KACZ,aAAc,SAAsBoG,EAAMC,EAAM,CAC9C,IAAIC,EAAO,CAACtG,EAAM,IAAI,EAAE,KAAK,CAAC,EAC9B,GAAI,CACF,OAAO,KAAK,aAAU8F,GAAA,GAAIM,EAAME,CAAI,CAAC,IAAM,KAAK,aAAUR,GAAA,GAAIO,EAAMC,CAAI,CAAC,CAC3E,OAASC,EAAO,CACd,MAAO,EACT,CACF,EACA,YAAuB,OAAKhF,KAAe,QAAc,KAAc,CACrE,YAAa,QACb,OAAQ,CACN,EAAG,aACL,CACF,EAAGvB,CAAK,EAAG,CAAC,EAAG,CACb,YAAU,QAAc,KAAc,CAAC,EAAGA,EAAM,QAAQ,EAAG,CAAC,EAAG,CAC7D,KAAMmG,CACR,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CAAC,KA9BmC,OAAK5E,KAAe,KAAc,CACrE,YAAa,QACb,OAAQ,CACN,EAAG,aACL,CACF,EAAGvB,CAAK,CAAC,CA0BX,CACAkG,EAAmB,cAAgB5F,GACnC,MAAe4F,C,0ECxZXM,GAAY,OAAO,eACnBC,EAAsB,OAAO,sBAC7BC,EAAe,OAAO,UAAU,eAChCC,EAAe,OAAO,UAAU,qBAChCC,EAAkB,CAACC,EAAKzJ,EAAKqF,IAAUrF,KAAOyJ,EAAML,GAAUK,EAAKzJ,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAqF,CAAM,CAAC,EAAIoE,EAAIzJ,CAAG,EAAIqF,EACtJqE,GAAiB,CAAC,EAAGC,IAAM,CAC7B,QAASC,KAAQD,IAAMA,EAAI,CAAC,GACtBL,EAAa,KAAKK,EAAGC,CAAI,GAC3BJ,EAAgB,EAAGI,EAAMD,EAAEC,CAAI,CAAC,EACpC,GAAIP,EACF,QAASO,KAAQP,EAAoBM,CAAC,EAChCJ,EAAa,KAAKI,EAAGC,CAAI,GAC3BJ,EAAgB,EAAGI,EAAMD,EAAEC,CAAI,CAAC,EAEtC,OAAO,CACT,EAEA,MAAMC,EAAYjH,GAA0B,gBAAoB,MAAO8G,GAAe,CAAE,GAAI,+BAAgC,YAAa,mBAAoB,MAAO,6BAA8B,QAAS,WAAY,EAAG9G,CAAK,EAAmB,gBAAoB,OAAQ,KAAsB,gBAAoB,QAAS,KAAM,meAAme,CAAC,EAAmB,gBAAoB,UAAW,CAAE,GAAI,MAAO,GAAI,MAAO,GAAI,EAAG,GAAI,EAAG,MAAO,CACx4B,KAAM,SACR,CAAE,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,UAAW,mBAAoB,EAAG,6KAA8K,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,UAAW,mBAAoB,EAAG,0HAA2H,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,UAAW,oBAAqB,EAAG,oBAAqB,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,UAAW,mBAAoB,EAAG,6IAA8I,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,yJAA0J,MAAO,CACn/B,OAAQ,OACR,YAAa,OACb,cAAe,QACf,eAAgB,QAChB,KAAM,SACR,CAAE,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,UAAW,oBAAqB,EAAG,qBAAsB,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,sSAAuS,MAAO,CAC9c,KAAM,SACR,CAAE,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,6hBAA8hB,MAAO,CAC1lB,KAAM,SACR,CAAE,CAAC,EAAmB,gBAAoB,UAAW,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,GAAI,IAAM,GAAI,KAAM,UAAW,6BAA8B,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,UAAW,mBAAoB,EAAG,8LAA+L,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,IAAK,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,KAAM,EAAG,GAAK,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,GAAK,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,GAAK,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,GAAK,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,UAAW,mBAAoB,EAAG,mFAAoF,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,GAAK,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,GAAK,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,GAAK,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,UAAW,oBAAqB,EAAG,wCAAyC,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,UAAW,oBAAqB,EAAG,uDAAwD,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,UAAW,mBAAoB,EAAG,iEAAkE,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,GAAK,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,GAAK,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,GAAK,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,GAAK,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,GAAK,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,GAAK,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,GAAK,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,mGAAoG,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,EAAI,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,qGAAsG,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,UAAW,mBAAoB,EAAG,mFAAoF,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,IAAK,CAAC,EAAmB,gBAAoB,SAAU,CAAE,UAAW,mBAAoB,GAAI,MAAO,GAAI,MAAO,EAAG,IAAK,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,UAAW,oBAAqB,EAAG,2JAA4J,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,UAAW,mBAAoB,EAAG,usBAAwsB,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,UAAW,mBAAoB,EAAG,upBAAwpB,CAAC,CAAC,EAE5sK,MAAe,ikP,iCC9Bf,GAAe,CAAC,UAAY,mBAAmB,E,WCMhC,SAASkH,EAAUlH,EAAuB,CACvD,SACEmH,EAAAA,KAAA,OACEtC,UAAWuC,EAAAA,EAAGC,GAAOC,UAAW,cAAetH,GAAK,YAALA,EAAOuH,gBAAgB,EAAEhH,YAExE4G,EAAAA,KAACK,EAAAA,EAAK,CACJC,MAAOzH,GAAK,MAALA,EAAOyH,MAAQzH,GAAK,YAALA,EAAOyH,SAAQN,EAAAA,KAACO,EAAS,EAAE,EACjDC,WAAY,CAAEC,OAAQ,GAAI,EAC1BC,eACEV,EAAAA,KAAA,QAAA5G,SACGP,GAAK,MAALA,EAAO8H,cACNX,EAAAA,KAAA,KAAG9F,QAASrB,GAAK,YAALA,EAAO8H,WAAWvH,SAAEP,GAAK,YAALA,EAAO+H,GAAG,CAAI,EAE9C/H,GAAK,YAALA,EAAO+H,GACR,CACG,CACP,CACF,CAAC,CACC,CAET,C,maCnBMC,GAAoD,SAAHvL,EAKjD,KAJJwL,EAAOxL,EAAPwL,QACA1H,EAAQ9D,EAAR8D,SACA2H,EAAQzL,EAARyL,SACGC,EAAUC,EAAAA,EAAA3L,EAAA8C,EAAA,EAEb8I,KAAwBC,GAAAA,UAAkB,EAAK,EAACC,EAAAC,EAAAA,EAAAH,EAAA,GAAzCI,EAAIF,EAAA,GAAEG,EAAOH,EAAA,GAEdI,KAAaC,GAAAA,SAAQ,UAAM,CAC/B,GAAKX,EACL,OAAOY,GAAAA,aAAmBZ,EAAOa,EAAAA,EAAAA,EAAAA,EAAA,CAC/B1L,IAAK,SAAS,EACX6K,EAAQjI,KAAK,MAChBqB,QAAS,UAAF,KAAAR,EAAAkI,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAnI,EAAOC,GAAQ,CAAF,IAAAmI,EAAAC,EAAA,OAAAH,EAAAA,EAAA,EAAAI,KAAA,SAAAhI,EAAE,CAAF,cAAAA,EAAAgF,KAAAhF,EAAAiF,KAAE,CAAF,OACpBqC,EAAQ,CAACD,CAAI,GACbS,EAAAjB,EAAQjI,SAAK,MAAAkJ,IAAA,SAAAC,EAAbD,EAAe7H,WAAO,MAAA8H,IAAA,QAAtBA,EAAAE,KAAAH,EAAyBnI,EAAC,EAAC,wBAAAK,EAAAkI,KAAA,IAAAxI,CAAA,EAC5B,YAAAO,EAAAC,EAAA,QAAAT,EAAA0I,MAAA,KAAAC,SAAA,SAAAnI,CAAA,KACF,CACH,EAAG,CAACqH,EAAST,EAASQ,CAAI,CAAC,EAE3B,SACEgB,GAAAA,MAAAC,GAAAA,SAAA,CAAAnJ,SAAA,CACGoI,KACDxB,GAAAA,KAACwC,GAAAA,EAAKb,EAAAA,EAAAA,EAAAA,EAAA,CACJZ,SAAU,SAACnH,EAAM,CACfmH,GAAQ,MAARA,EAAWnH,CAAC,EACZ2H,EAAQ,EAAK,CACf,EACAD,KAAMA,CAAK,EACPN,CAAU,MAAA5H,SAEbA,CAAQ,CAAC,CACL,CAAC,EACR,CAEN,EAEA,EAAeyH,GCLT4B,EAAW,SAACC,EAA+BC,EAAwB,CAAF,IAAAC,EAAA,MACrE,EACEF,EAAOG,UAAYF,KACnBC,EAAAF,EAAOI,QAAI,MAAAF,IAAA,cAAXA,EAAaG,QAASC,GAAAA,GACvB,EAEGC,EAGqC,SACzCC,EACAC,EAAkB,OACf,CACH,CACEC,SAAOC,EAAAA,IAAQ,QAAQ,EACvBC,UAAW,UACXC,UAAW,SACXC,QAAS,UAAF,KAAAC,EAAA7B,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAnI,EAAArE,EAAA,KAAAoO,EAAAC,EAAAnH,EAAA,OAAAqF,EAAAA,EAAA,EAAAI,KAAA,SAAAhI,EAAA,eAAAA,EAAAgF,KAAAhF,EAAAiF,KAAA,QAAiB,GAARwE,EAAQpO,EAARoO,SACXA,GAAQ,MAARA,EAAUE,OAAQ,CAAF3J,EAAAiF,KAAA,eAAAjF,EAAA4J,OAAA,SACZV,GAAsB,CAAC,CAAC,SAAAlJ,OAAAA,EAAAiF,KAAA,KAEV4E,EAAAA,SAAO,kBAAAC,OACVL,CAAQ,CAC5B,EACGM,OAAO,EACPrF,IAAI,EAAC,OAAAgF,OAAAA,EAAA1J,EAAAgK,KAJAzH,EAAImH,EAAJnH,KAAIvC,EAAA4J,OAAA,UAOVrH,GAAI,YAAJA,EAAM0H,IAAI,SAACC,EAAG,CAAF,MAAM,CAChB7I,MAAO6I,EAAEC,GACTC,MAAO,GAAFN,OAAKI,EAAEG,QAAQ,CACtB,CAAC,CAAC,IAAK,CAAC,CAAC,0BAAArK,EAAAkI,KAAA,IAAAxI,CAAA,EAEZ,YAAA6J,EAAArJ,EAAA,QAAAsJ,EAAArB,MAAA,KAAAC,SAAA,SAAAmB,CAAA,IACDe,WAAY,SAAClO,EAAGgD,EAAW,KAAAmL,EACzB,OAAOnL,GAAM,OAAAmL,EAANnL,EAAQoL,aAAS,MAAAD,IAAA,cAAjBA,EAAmBF,QAC5B,EACA5J,cAAe,CACbgK,MAAO,CAAC,CAAEC,SAAU,EAAK,CAAC,CAC5B,EACAC,WAAY,CACVC,WAAY,EACd,CACF,EACA,CACEzB,SAAOC,EAAAA,IAAQ,MAAM,EACrBC,UAAW,CAAC,OAAQ,MAAM,EAC1BC,UAAW,SACXC,QAAS,UAAF,KAAAsB,EAAAlD,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAiD,GAAA,KAAAC,EAAAxI,EAAA,OAAAqF,EAAAA,EAAA,EAAAI,KAAA,SAAAgD,EAAA,eAAAA,EAAAhG,KAAAgG,EAAA/F,KAAA,QAAA+F,OAAAA,EAAA/F,KAAA,KACgBgG,EAAAA,OAAmB,eAAe,EACtDC,SAAS,EAAG,GAAI,EAChBxG,IAAI,EAAC,OAAAqG,OAAAA,EAAAC,EAAAhB,KAFAzH,EAAIwI,EAAJxI,KAAIyI,EAAApB,OAAA,UAGJrH,GAAQ,CAAC,GAAG0H,IAAI,SAAClG,EAAG,CAAF,MAAM,CAAE1C,MAAO0C,EAAE+E,KAAMsB,MAAOrG,EAAEoH,OAAQ,CAAC,CAAC,CAAC,0BAAAH,EAAA9C,KAAA,IAAA4C,CAAA,EACtE,YAAAvB,GAAA,QAAAsB,EAAA1C,MAAA,KAAAC,SAAA,SAAAmB,CAAA,IACD9I,cAAe,CAAEgK,MAAO,CAAC,CAAEC,SAAU,EAAK,CAAC,CAAE,CAC/C,EACA,CACEvB,SAAOC,EAAAA,IAAQ,kCAAkC,EACjDE,UAAW,SACX8B,OAAQ,SAAChP,EAAGgD,EAAQiM,EAAIC,EAAW,CACjC,OAAI9C,EAASpJ,EAAQ6J,GAAW,YAAXA,EAAakB,EAAE,EAC3B,IACLpE,GAAAA,KAAA,KAAkB9F,QAAS,eAAAsL,EAAA,OAAMD,GAAM,OAAAC,EAAND,EAAQE,iBAAa,MAAAD,IAAA,cAArBA,EAAAtD,KAAAqD,EAAwBlM,EAAO+K,EAAE,CAAC,EAAChL,YACjEiK,EAAAA,IAAQ,MAAM,CAAC,EADX,UAEJ,CAAC,EAGD,IACT,CACF,CAAC,CACF,EAEKqC,EAA4C,SAAHhQ,EAIzC,KAHJiQ,EAASjQ,EAATiQ,UACAC,EAAOlQ,EAAPkQ,QACAC,EAASnQ,EAATmQ,UAEAC,KAA2BC,EAAAA,GAAW,EAA9BC,EAAcF,EAAdE,eACFC,KAAWC,GAAAA,GAAgB,EACjCC,KAAyBC,GAAAA,UAAS,gBAAgB,EAA1CC,EAAYF,EAAZE,aACFC,EAAY,IAAIC,IAAIX,GAAO,YAAPA,EAAS1B,IAAI,SAACsC,EAAG,CAAF,IAAAC,EAAA,OAAAA,EAAKD,EAAE/B,aAAS,MAAAgC,IAAA,cAAXA,EAAarC,EAAE,EAAC,EACxDsC,EAAUd,GAAO,YAAPA,EACZ1B,IAAI,SAACsC,EAAG,CAAF,OAAKA,EAAE/B,SAAS,GACvBkC,OAA2C,SAACC,EAAKC,EAAQ,CACxD,OAAIA,GAAG,MAAHA,EAAKzC,IAAMkC,EAAUQ,IAAID,EAAIzC,EAAE,IACjCwC,EAAIG,KAAK,CAAEzL,MAAOuL,EAAIzC,GAAIC,MAAOwC,EAAIvC,QAAS,CAAC,EAC/CgC,EAAS,OAAQO,EAAIzC,EAAE,GAElBwC,CACT,EAAG,CAAC,CAAC,EAEP,SACE5G,GAAAA,KAACgH,EAAAA,EAAgB,CACfpM,OAAO,KACPqM,eAAa5D,EAAAA,IAAQ,iBAAiB,EACtC1I,mBAAoB,CAClBuC,qBAAmBmG,EAAAA,IAAQ,YAAY,EACvChK,OAAQ,iBACL,CACC+K,IAAK8C,KAAKC,OAAO,EAAI,KAASC,QAAQ,CAAC,CACzC,CAAC,CACL,EACAC,QAASpE,EAAoBoD,GAAY,YAAZA,EAAciB,SAAUZ,CAAO,EAC5DpL,MAAOsK,GAAO,YAAPA,EAAS1B,IAAI,SAACsC,EAAG,CAAF,OAAA7E,EAAAA,EAAAA,EAAAA,EAAA,GACjB6E,CAAC,MACJ3D,QAAS0E,OAAOC,SAAShB,EAAE3D,OAAO,CAAC,GACnC,EACFJ,SAAQd,EAAAA,EAAAA,EAAAA,EAAA,GACHqE,CAAc,MACjBzN,KAAM,SACNkP,SAAU,UAAF,KAAAC,EAAA9F,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAA6F,GAAOtR,EAAGuR,EAAK,CAAF,IAAAC,EAAAzI,EAAA,OAAAyC,EAAAA,EAAA,EAAAI,KAAA,SAAA6F,EAAE,CAAF,cAAAA,EAAA7I,KAAA6I,EAAA5I,KAAE,CAAF,OAAA4I,OAAAA,EAAA5I,KAAA,KACG4E,EAAAA,SACtB,iBACF,EAAEiE,UAAUH,EAAIxD,EAAE,EAAC,OAFN,GAEMyD,EAAAC,EAAA7D,KAFX7E,EAAKyI,EAALzI,MAAK,CAGTA,EAAO,CAAF0I,EAAA5I,KAAA,cAAQE,EAAK,OACtByG,OAAAA,GAAS,MAATA,EAAY,EAACiC,EAAAjE,OAAA,SACN,EAAI,0BAAAiE,EAAA3F,KAAA,IAAAwF,EAAA,EACZ,YAAAF,EAAAO,GAAAC,EAAA,QAAAP,EAAAtF,MAAA,KAAAC,SAAA,SAAAoF,CAAA,IACDS,OAAQ,UAAF,KAAAC,EAAAvG,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAsG,GAAO/R,EAAGmG,EAAM8I,EAAI+C,EAAS,CAAF,IAAAC,EAAAC,EAAAC,EAAAC,GAAArJ,EAAAsJ,GAAAC,EAAA,OAAA9G,EAAAA,EAAA,EAAAI,KAAA,SAAA2G,EAAE,CAAF,cAAAA,EAAA3J,KAAA2J,EAAA1J,KAAE,CAAF,OAGiC,GAF5DqJ,EACJ,OAAO/L,EAAKqG,SAAY,SAAWrG,EAAKqG,QAAUrG,EAAKiI,UAAUL,GAC7DoE,GAAMF,EAAGrC,EAAS4C,KAAK,SAAC7K,GAAG,CAAF,OAAKA,GAAE+E,OAASvG,EAAKsG,KAAKC,IAAI,MAAC,MAAAuF,IAAA,cAA/CA,EAAiDlE,GAAE,EAC9D,CAACoE,GAAU,CAACD,GAAM,CAAAK,EAAA1J,KAAA,eAAA0J,EAAA/E,OAAA,sBAClBwE,GAAW,OAAO7L,EAAK4H,IAAO,UAAQ,CAAAwE,EAAA1J,KAAA,SAAA0J,OAAAA,EAAA1J,KAAA,KAChB4E,EAAAA,SACtB,iBACF,EAAEgF,OAAO,CACPC,QAAS,CAAE3E,GAAIuB,CAAU,EACzB7C,KAAM,CAAEsB,GAAIoE,CAAO,EACnB3F,QAAS,GAAFkB,OAAKwE,CAAM,CACpB,CAAkB,EAAC,OAC8B,GAD9BE,GAAAG,EAAA3E,KANX7E,EAAKqJ,GAALrJ,MAOJA,GAAK,MAALA,EAAO4J,SAASA,GAAAA,GAAQ5J,MAAMA,GAAK,YAALA,EAAO4J,OAAO,EAAC,CAC7C5J,EAAO,CAAFwJ,EAAA1J,KAAA,eAAQE,EAAK,QAAAwJ,EAAA1J,KAAA,iBAAA0J,OAAAA,EAAA1J,KAAA,MAEE4E,EAAAA,SACtB,iBACF,EAAEmF,OAAOzM,EAAK4H,GAAI,CAChBtB,KAAM,CAAEsB,GAAIoE,CAAO,EACnB3F,QAAS,GAAFkB,OAAKwE,CAAM,CACpB,CAAC,EAAC,QAC+C,GAD/CG,GAAAE,EAAA3E,KALM7E,EAAKsJ,GAALtJ,MAMJA,GAAK,MAALA,EAAO4J,SAASA,GAAAA,GAAQ5J,MAAMA,GAAK,YAALA,EAAO4J,OAAO,EAAC,CAC7C5J,EAAO,CAAFwJ,EAAA1J,KAAA,eAAQE,EAAK,QAExByG,OAAAA,GAAS,MAATA,EAAY,EAAC+C,EAAA/E,OAAA,SACN,EAAI,2BAAA+E,EAAAzG,KAAA,IAAAiG,EAAA,EACZ,YAAAF,EAAAgB,GAAAC,EAAAC,EAAAC,EAAA,QAAAlB,EAAA/F,MAAA,KAAAC,SAAA,SAAA6F,CAAA,KACD,CACe,CAEvB,EAEMoB,EAC2C,CAC/C,CACElG,SAAOC,EAAAA,IAAQ,6BAA6B,EAC5CC,UAAW,KACXiG,OAAQ,GACR9G,SAAU,GACV+G,MAAO,EACT,EACA,CACEpG,SAAOC,EAAAA,IAAQ,+BAA+B,EAC9CC,UAAW,OACXb,SAAU,GACV8G,OAAQ,GACRC,MAAO,EACT,CAAC,EAAAzF,OAAA0F,EAAAA,KACGC,EAAAA,IAAe,EACf,CAAC,EACD,CACE,CACEtG,SAAOC,EAAAA,IAAQ,mCAAmC,EAClDC,UAAW,WACXb,SAAU,GACV8G,OAAQ,GACRC,MAAO,EACT,CAAC,CACF,GACL,CACEpG,SAAOC,EAAAA,IAAQ,+BAA+B,EAC9CC,UAAW,OACXb,SAAU,GACV+G,MAAO,EACPG,UAAWC,GAAAA,GACXrG,UAAW,QACb,EACA,CACEH,SAAOC,EAAAA,IAAQ,uCAAuC,EACtDE,UAAW,OACXD,UAAW,gBACXsB,WAAY,CACViF,aAAc,SAAC/M,EAAgB,CAAF,OAC3BA,GAAWA,GAAWgN,GAAAA,EAAM,EAAEC,QAAQ,KAAK,CAAC,CAChD,CACF,CAAC,GAUGC,EAA0C,SAAHzT,EAMvC,KALJuK,EAAOvK,EAAPuK,QACA6E,EAASpP,EAAToP,UAASsE,EAAA1T,EACT8Q,QAAAA,EAAO4C,IAAA,OAAGX,EAAoBW,EAC9BjJ,EAAUzK,EAAVyK,WACAkJ,EAAY3T,EAAZ2T,aAEAC,KAA2BpE,EAAAA,GAAW,EAA9BC,EAAcmE,EAAdnE,eACR9E,KAA8BC,GAAAA,UAAkB,EAACC,EAAAC,EAAAA,EAAAH,EAAA,GAA1C6H,EAAO3H,EAAA,GAAEgJ,EAAUhJ,EAAA,GAC1BiJ,MAAkBC,GAAAA,GAAc,EAAxBC,EAAKF,GAALE,MACFC,EAAO,eAAA/T,EAAAmL,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAA2I,EAAOrG,EAAU,KAAAsG,EAAAlO,EAAA,OAAAqF,EAAAA,EAAA,EAAAI,KAAA,SAAA0I,EAAA,eAAAA,EAAA1L,KAAA0L,EAAAzL,KAAA,QAAAyL,OAAAA,EAAAzL,KAAA,EACRqL,KACrBzG,EAAAA,SAAiB,UAAU,EACxB8G,eAAe,CAACxG,CAAE,CAAC,EACnByG,aAAa,kBAAmB,CAAC,UAAW,MAAM,EAAG,EAAI,EACzDlM,IAAI,CACT,EAAC,OAAA+L,OAAAA,EAAAC,EAAA1G,KALOzH,EAAIkO,EAAJlO,KAAImO,EAAA9G,OAAA,SAMLrH,GAAI,YAAJA,EAAO,CAAC,CAAC,0BAAAmO,EAAAxI,KAAA,IAAAsI,CAAA,EACjB,mBARYK,EAAA,QAAArU,EAAA2L,MAAA,KAAAC,SAAA,MAUb,SACEC,GAAAA,MAACzB,EAAgBc,EAAAA,EAAAA,EAAAA,EAAA,CACfb,QAASA,EACTiK,OAAQ,KACRC,MAAO,GAAI,EACPhK,CAAU,MAAA5H,SAAA,IAEd4G,GAAAA,KAACiL,EAAAA,GAAe,CACd7H,OAAO2F,GAAO,YAAPA,EAAS5J,QAAQ4J,GAAO,YAAPA,EAASmC,IACjCC,OAAQ,CAAEC,GAAI,EAAGC,GAAI,CAAE,EACvB5I,SAAQd,EAAAA,EAAAA,EAAAA,EAAA,GACHqE,CAAc,MACjBkC,OAAQ,UAAF,KAAAoD,EAAA1J,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAyJ,EAAOC,EAASC,EAAS,CAAF,IAAAC,GAAAtM,EAAA,OAAAyC,EAAAA,EAAA,EAAAI,KAAA,SAAA0J,EAAE,CAAF,cAAAA,EAAA1M,KAAA0M,EAAAzM,KAAE,CAAF,OAAAyM,OAAAA,EAAAzM,KAAA,KACL4E,EAAAA,SAAiB,UAAU,EAAEmF,OACnDwC,EAAQrH,GAAEwH,GAAAA,EAAA,GACPJ,EAAoBC,EAAQD,CAAO,CAAkB,CAC1D,EAAC,OAHY,GAGZE,GAAAC,EAAA1H,KAHO7E,EAAKsM,GAALtM,MAAK,CAITA,EAAO,CAAFuM,EAAAzM,KAAA,cAAQE,EAAK,OACtB,OAAI8K,GAAcA,EAAa,EAACyB,EAAA9H,OAAA,SACzB,EAAI,0BAAA8H,EAAAxJ,KAAA,IAAAoJ,CAAA,EACZ,YAAArD,EAAA2D,EAAAC,EAAA,QAAAR,EAAAlJ,MAAA,KAAAC,SAAA,SAAA6F,CAAA,MAEH1E,QAAO5B,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAiK,GAAA,KAAAvP,EAAA,OAAAqF,EAAAA,EAAA,EAAAI,KAAA,SAAA+J,EAAA,eAAAA,EAAA/M,KAAA+M,EAAA9M,KAAA,QAAA8M,OAAAA,EAAA9M,KAAA,EACYsL,EAAQ7E,CAAS,EAAC,OAA3B,GAAJnJ,EAAIwP,EAAA/H,KACLzH,EAAM,CAAFwP,EAAA9M,KAAA,eAAA8M,EAAAnI,OAAA,SACA,CAAEoI,QAAS,EAAM,CAAC,SAE3B7B,OAAAA,EAAW5N,CAAI,EAACwP,EAAAnI,OAAA,SACT,CAAEoI,QAAS,GAAMzP,KAAAA,CAAK,CAAC,0BAAAwP,EAAA7J,KAAA,IAAA4J,CAAA,EAC/B,GACD1E,QAASA,CAAQ,CAClB,KACDrH,GAAAA,KAAC0F,EAAY,CACXE,QAASmD,GAAO,YAAPA,EAASmD,gBAClBvG,UAAWA,EACXE,UAASjE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAqK,GAAA,QAAAtK,EAAAA,EAAA,EAAAI,KAAA,SAAAmK,EAAA,eAAAA,EAAAnN,KAAAmN,EAAAlN,KAAA,QAAAkN,OAAAA,EAAAC,GACTjC,EAAUgC,EAAAlN,KAAA,EAAOsL,EAAQ7E,CAAS,EAAC,OAAAyG,EAAAE,GAAAF,EAAAnI,QAAAmI,EAAAC,IAAAD,EAAAE,EAAA,EAC/BpC,GAAcA,EAAa,EAAC,wBAAAkC,EAAAjK,KAAA,IAAAgK,CAAA,EACjC,EAAC,CACH,CAAC,GACc,CAEtB,EAEA,EAAenC,C,iHCjTT9D,EAAkB,UAAM,CAC5B,IAAAhF,MAAgCC,EAAAA,UAAwB,CAAC,CAAC,EAACC,EAAAC,EAAAA,EAAAH,GAAA,GAApD+E,GAAQ7E,EAAA,GAAEmL,EAAWnL,EAAA,GACtBoL,EAAgB,eAAAlX,EAAAsM,EAAAA,EAAAC,GAAAA,EAAA,EAAAC,KAAG,SAAAnI,GAAA,KAAAqL,EAAAxI,GAAA,OAAAqF,GAAAA,EAAA,EAAAI,KAAA,SAAAhI,GAAA,eAAAA,GAAAgF,KAAAhF,GAAAiF,KAAA,QAAAjF,OAAAA,GAAAiF,KAAA,KACAgG,GAAAA,OAAmB,eAAe,EACtDC,SAAS,EAAG,GAAI,EAChBxG,IAAI,EAAC,OAAAqG,OAAAA,EAAA/K,GAAAgK,KAFAzH,GAAIwI,EAAJxI,KAAIvC,GAAA4J,OAAA,SAGLrH,IAAQ,CAAC,CAAC,0BAAAvC,GAAAkI,KAAA,IAAAxI,CAAA,EAClB,oBALqB,QAAArE,EAAA8M,MAAA,KAAAC,SAAA,MAOtBoK,SAAAA,EAAAA,WAAU,UAAM,CACdD,EAAiB,EAAEE,KAAK,SAACC,EAAO,CAAF,OAAKJ,GAAW,YAAXA,EAAcI,CAAK,CAAC,EACzD,EAAG,CAAC,CAAC,EAEE1G,EACT,EAEA,KAAeC,C,6LCHFlD,EAAyB,KAEzB4J,GAAuB,SAACpQ,EAAa,KAAAqQ,GAAAC,EAAA,OAAAnL,GAAAA,EAAAA,GAAAA,EAAA,GAC7CnF,CAAI,MACPuQ,UAAQF,GAAErQ,EAAK0P,mBAAe,MAAAW,KAAA,cAApBA,GAAsBG,OAC9B,SAACxG,EAAG,CAAF,IAAAyG,EAAA,QAAKA,EAAAzG,EAAE1D,QAAI,MAAAmK,IAAA,cAANA,EAAQlK,QAASC,CAAsB,CAChD,EACAkK,YAAUJ,EAAEtQ,EAAK2Q,yBAAqB,MAAAL,IAAA,SAAAA,EAA1BA,EAA4BM,KAAK,SAACC,EAAGzN,EAAG,CAAF,OAC/CyN,EAAEC,WAAa,IAAM1N,EAAE0N,WAAa,GAAK,GAAK,CAAC,CAClD,KAAC,MAAAR,IAAA,cAFWA,EAER,CAAC,CAAC,IAGKS,EAA6D,CACxEC,QAAS,CAAC,YAAa,SAAS,EAChCC,UAAW,CAAC,EACZC,SAAU,CAAC,EACXC,QAAS,CAAC,YAAa,SAAS,EAChCC,QAAS,CAAC,YAAa,WAAY,SAAS,CAC9C,EAEMC,EAA0B,CAC9BC,IAAK,CACHC,QAAM1K,EAAAA,IAAQ,kCAAkC,CAClD,EACA2K,IAAK,CACHD,QAAM1K,EAAAA,IAAQ,kCAAkC,CAClD,EACA4K,SAAU,CACRF,QAAM1K,EAAAA,IAAQ,uCAAuC,CACvD,CACF,EAEauG,KAAmBF,EAAAA,IAAe,KAC3CwE,EAAAA,MAAKL,EAAyB,CAAC,MAAO,KAAK,CAAC,EAC5CA,C", "sources": ["webpack://labwise-web/./node_modules/@ant-design/pro-skeleton/es/components/List/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-skeleton/es/components/Descriptions/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-skeleton/es/components/Result/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-skeleton/es/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-table/node_modules/@ant-design/icons/es/icons/PlusOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/pro-table/es/components/EditableTable/index.js", "webpack://labwise-web/./src/assets/svgs/empty.svg", "webpack://labwise-web/./src/components/StatusTip/index.less?b46e", "webpack://labwise-web/./src/components/StatusTip/index.tsx", "webpack://labwise-web/./src/pages/projects/components/ModelWithTrigger.tsx", "webpack://labwise-web/./src/pages/projects/components/ConfigModel.tsx", "webpack://labwise-web/./src/pages/projects/hooks/useProjectRoles.ts", "webpack://labwise-web/./src/pages/projects/utils.tsx"], "sourcesContent": ["import { Card, Divider, Skeleton, Space } from 'antd';\nimport useBreakpoint from 'antd/es/grid/hooks/useBreakpoint';\nimport React, { useMemo } from 'react';\n\n/** 一条分割线 */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nexport var Line = function Line(_ref) {\n  var padding = _ref.padding;\n  return /*#__PURE__*/_jsx(\"div\", {\n    style: {\n      padding: padding || '0 24px'\n    },\n    children: /*#__PURE__*/_jsx(Divider, {\n      style: {\n        margin: 0\n      }\n    })\n  });\n};\nexport var MediaQueryKeyEnum = {\n  xs: 2,\n  sm: 2,\n  md: 4,\n  lg: 4,\n  xl: 6,\n  xxl: 6\n};\nvar StatisticSkeleton = function StatisticSkeleton(_ref2) {\n  var size = _ref2.size,\n    active = _ref2.active;\n  var defaultCol = useMemo(function () {\n    return {\n      lg: true,\n      md: true,\n      sm: false,\n      xl: false,\n      xs: false,\n      xxl: false\n    };\n  }, []);\n  var col = useBreakpoint() || defaultCol;\n  var colSize = Object.keys(col).filter(function (key) {\n    return col[key] === true;\n  })[0] || 'md';\n  var arraySize = size === undefined ? MediaQueryKeyEnum[colSize] || 6 : size;\n  var firstWidth = function firstWidth(index) {\n    if (index === 0) {\n      return 0;\n    }\n    if (arraySize > 2) {\n      return 42;\n    }\n    return 16;\n  };\n  return /*#__PURE__*/_jsx(Card, {\n    bordered: false,\n    style: {\n      marginBlockEnd: 16\n    },\n    children: /*#__PURE__*/_jsx(\"div\", {\n      style: {\n        width: '100%',\n        justifyContent: 'space-between',\n        display: 'flex'\n      },\n      children: new Array(arraySize).fill(null).map(function (_, index) {\n        return /*#__PURE__*/_jsxs(\"div\", {\n          style: {\n            borderInlineStart: arraySize > 2 && index === 1 ? '1px solid rgba(0,0,0,0.06)' : undefined,\n            paddingInlineStart: firstWidth(index),\n            flex: 1,\n            marginInlineEnd: index === 0 ? 16 : 0\n          },\n          children: [/*#__PURE__*/_jsx(Skeleton, {\n            active: active,\n            paragraph: false,\n            title: {\n              width: 100,\n              style: {\n                marginBlockStart: 0\n              }\n            }\n          }), /*#__PURE__*/_jsx(Skeleton.Button, {\n            active: active,\n            style: {\n              height: 48\n            }\n          })]\n        }, index);\n      })\n    })\n  });\n};\n\n/** 列表子项目骨架屏 */\nexport var ListSkeletonItem = function ListSkeletonItem(_ref3) {\n  var active = _ref3.active;\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [/*#__PURE__*/_jsx(Card, {\n      bordered: false\n      // eslint-disable-next-line react/no-array-index-key\n      ,\n      style: {\n        borderRadius: 0\n      },\n      styles: {\n        body: {\n          padding: 24\n        }\n      },\n      children: /*#__PURE__*/_jsxs(\"div\", {\n        style: {\n          width: '100%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsx(\"div\", {\n          style: {\n            maxWidth: '100%',\n            flex: 1\n          },\n          children: /*#__PURE__*/_jsx(Skeleton, {\n            active: active,\n            title: {\n              width: 100,\n              style: {\n                marginBlockStart: 0\n              }\n            },\n            paragraph: {\n              rows: 1,\n              style: {\n                margin: 0\n              }\n            }\n          })\n        }), /*#__PURE__*/_jsx(Skeleton.Button, {\n          active: active,\n          size: \"small\",\n          style: {\n            width: 165,\n            marginBlockStart: 12\n          }\n        })]\n      })\n    }), /*#__PURE__*/_jsx(Line, {})]\n  });\n};\n\n/** 列表骨架屏 */\nexport var ListSkeleton = function ListSkeleton(_ref4) {\n  var size = _ref4.size,\n    _ref4$active = _ref4.active,\n    active = _ref4$active === void 0 ? true : _ref4$active,\n    actionButton = _ref4.actionButton;\n  return /*#__PURE__*/_jsxs(Card, {\n    bordered: false,\n    styles: {\n      body: {\n        padding: 0\n      }\n    },\n    children: [new Array(size).fill(null).map(function (_, index) {\n      return (\n        /*#__PURE__*/\n        // eslint-disable-next-line react/no-array-index-key\n        _jsx(ListSkeletonItem, {\n          active: !!active\n        }, index)\n      );\n    }), actionButton !== false && /*#__PURE__*/_jsx(Card, {\n      bordered: false,\n      style: {\n        borderStartEndRadius: 0,\n        borderTopLeftRadius: 0\n      },\n      styles: {\n        body: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        }\n      },\n      children: /*#__PURE__*/_jsx(Skeleton.Button, {\n        style: {\n          width: 102\n        },\n        active: active,\n        size: \"small\"\n      })\n    })]\n  });\n};\n\n/**\n * 面包屑的 骨架屏\n *\n * @param param0\n */\nexport var PageHeaderSkeleton = function PageHeaderSkeleton(_ref5) {\n  var active = _ref5.active;\n  return /*#__PURE__*/_jsxs(\"div\", {\n    style: {\n      marginBlockEnd: 16\n    },\n    children: [/*#__PURE__*/_jsx(Skeleton, {\n      paragraph: false,\n      title: {\n        width: 185\n      }\n    }), /*#__PURE__*/_jsx(Skeleton.Button, {\n      active: active,\n      size: \"small\"\n    })]\n  });\n};\n/**\n * 列表操作栏的骨架屏\n *\n * @param param0\n */\nexport var ListToolbarSkeleton = function ListToolbarSkeleton(_ref6) {\n  var active = _ref6.active;\n  return /*#__PURE__*/_jsx(Card, {\n    bordered: false,\n    style: {\n      borderBottomRightRadius: 0,\n      borderBottomLeftRadius: 0\n    },\n    styles: {\n      body: {\n        paddingBlockEnd: 8\n      }\n    },\n    children: /*#__PURE__*/_jsxs(Space, {\n      style: {\n        width: '100%',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsx(Skeleton.Button, {\n        active: active,\n        style: {\n          width: 200\n        },\n        size: \"small\"\n      }), /*#__PURE__*/_jsxs(Space, {\n        children: [/*#__PURE__*/_jsx(Skeleton.Button, {\n          active: active,\n          size: \"small\",\n          style: {\n            width: 120\n          }\n        }), /*#__PURE__*/_jsx(Skeleton.Button, {\n          active: active,\n          size: \"small\",\n          style: {\n            width: 80\n          }\n        })]\n      })]\n    })\n  });\n};\nvar ListPageSkeleton = function ListPageSkeleton(_ref7) {\n  var _ref7$active = _ref7.active,\n    active = _ref7$active === void 0 ? true : _ref7$active,\n    statistic = _ref7.statistic,\n    actionButton = _ref7.actionButton,\n    toolbar = _ref7.toolbar,\n    pageHeader = _ref7.pageHeader,\n    _ref7$list = _ref7.list,\n    list = _ref7$list === void 0 ? 5 : _ref7$list;\n  return /*#__PURE__*/_jsxs(\"div\", {\n    style: {\n      width: '100%'\n    },\n    children: [pageHeader !== false && /*#__PURE__*/_jsx(PageHeaderSkeleton, {\n      active: active\n    }), statistic !== false && /*#__PURE__*/_jsx(StatisticSkeleton, {\n      size: statistic,\n      active: active\n    }), (toolbar !== false || list !== false) && /*#__PURE__*/_jsxs(Card, {\n      bordered: false,\n      styles: {\n        body: {\n          padding: 0\n        }\n      },\n      children: [toolbar !== false && /*#__PURE__*/_jsx(ListToolbarSkeleton, {\n        active: active\n      }), list !== false && /*#__PURE__*/_jsx(ListSkeleton, {\n        size: list,\n        active: active,\n        actionButton: actionButton\n      })]\n    })]\n  });\n};\nexport default ListPageSkeleton;", "import { Card, Skeleton } from 'antd';\nimport useBreakpoint from 'antd/es/grid/hooks/useBreakpoint';\nimport React, { useMemo } from 'react';\nimport { Line, PageHeaderSkeleton } from \"../List\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nvar MediaQueryKeyEnum = {\n  xs: 1,\n  sm: 2,\n  md: 3,\n  lg: 3,\n  xl: 3,\n  xxl: 4\n};\nvar DescriptionsLargeItemSkeleton = function DescriptionsLargeItemSkeleton(_ref) {\n  var active = _ref.active;\n  return /*#__PURE__*/_jsxs(\"div\", {\n    style: {\n      marginBlockStart: 32\n    },\n    children: [/*#__PURE__*/_jsx(Skeleton.Button, {\n      active: active,\n      size: \"small\",\n      style: {\n        width: 100,\n        marginBlockEnd: 16\n      }\n    }), /*#__PURE__*/_jsxs(\"div\", {\n      style: {\n        width: '100%',\n        justifyContent: 'space-between',\n        display: 'flex'\n      },\n      children: [/*#__PURE__*/_jsxs(\"div\", {\n        style: {\n          flex: 1,\n          marginInlineEnd: 24,\n          maxWidth: 300\n        },\n        children: [/*#__PURE__*/_jsx(Skeleton, {\n          active: active,\n          paragraph: false,\n          title: {\n            style: {\n              marginBlockStart: 0\n            }\n          }\n        }), /*#__PURE__*/_jsx(Skeleton, {\n          active: active,\n          paragraph: false,\n          title: {\n            style: {\n              marginBlockStart: 8\n            }\n          }\n        }), /*#__PURE__*/_jsx(Skeleton, {\n          active: active,\n          paragraph: false,\n          title: {\n            style: {\n              marginBlockStart: 8\n            }\n          }\n        })]\n      }), /*#__PURE__*/_jsx(\"div\", {\n        style: {\n          flex: 1,\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: /*#__PURE__*/_jsxs(\"div\", {\n          style: {\n            maxWidth: 300,\n            margin: 'auto'\n          },\n          children: [/*#__PURE__*/_jsx(Skeleton, {\n            active: active,\n            paragraph: false,\n            title: {\n              style: {\n                marginBlockStart: 0\n              }\n            }\n          }), /*#__PURE__*/_jsx(Skeleton, {\n            active: active,\n            paragraph: false,\n            title: {\n              style: {\n                marginBlockStart: 8\n              }\n            }\n          })]\n        })\n      })]\n    })]\n  });\n};\nvar DescriptionsItemSkeleton = function DescriptionsItemSkeleton(_ref2) {\n  var size = _ref2.size,\n    active = _ref2.active;\n  var defaultCol = useMemo(function () {\n    return {\n      lg: true,\n      md: true,\n      sm: false,\n      xl: false,\n      xs: false,\n      xxl: false\n    };\n  }, []);\n  var col = useBreakpoint() || defaultCol;\n  var colSize = Object.keys(col).filter(function (key) {\n    return col[key] === true;\n  })[0] || 'md';\n  var arraySize = size === undefined ? MediaQueryKeyEnum[colSize] || 3 : size;\n  return /*#__PURE__*/_jsx(\"div\", {\n    style: {\n      width: '100%',\n      justifyContent: 'space-between',\n      display: 'flex'\n    },\n    children: new Array(arraySize).fill(null).map(function (_, index) {\n      return /*#__PURE__*/_jsxs(\"div\", {\n        style: {\n          flex: 1,\n          paddingInlineStart: index === 0 ? 0 : 24,\n          paddingInlineEnd: index === arraySize - 1 ? 0 : 24\n        },\n        children: [/*#__PURE__*/_jsx(Skeleton, {\n          active: active,\n          paragraph: false,\n          title: {\n            style: {\n              marginBlockStart: 0\n            }\n          }\n        }), /*#__PURE__*/_jsx(Skeleton, {\n          active: active,\n          paragraph: false,\n          title: {\n            style: {\n              marginBlockStart: 8\n            }\n          }\n        }), /*#__PURE__*/_jsx(Skeleton, {\n          active: active,\n          paragraph: false,\n          title: {\n            style: {\n              marginBlockStart: 8\n            }\n          }\n        })]\n      }, index);\n    })\n  });\n};\n\n/**\n * Table 的子项目骨架屏\n *\n * @param param0\n */\nexport var TableItemSkeleton = function TableItemSkeleton(_ref3) {\n  var active = _ref3.active,\n    _ref3$header = _ref3.header,\n    header = _ref3$header === void 0 ? false : _ref3$header;\n  var defaultCol = useMemo(function () {\n    return {\n      lg: true,\n      md: true,\n      sm: false,\n      xl: false,\n      xs: false,\n      xxl: false\n    };\n  }, []);\n  var col = useBreakpoint() || defaultCol;\n  var colSize = Object.keys(col).filter(function (key) {\n    return col[key] === true;\n  })[0] || 'md';\n  var arraySize = MediaQueryKeyEnum[colSize] || 3;\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [/*#__PURE__*/_jsxs(\"div\", {\n      style: {\n        display: 'flex',\n        background: header ? 'rgba(0,0,0,0.02)' : 'none',\n        padding: '24px 8px'\n      },\n      children: [new Array(arraySize).fill(null).map(function (_, index) {\n        return /*#__PURE__*/_jsx(\"div\", {\n          style: {\n            flex: 1,\n            paddingInlineStart: header && index === 0 ? 0 : 20,\n            paddingInlineEnd: 32\n          },\n          children: /*#__PURE__*/_jsx(Skeleton, {\n            active: active,\n            paragraph: false,\n            title: {\n              style: {\n                margin: 0,\n                height: 24,\n                width: header ? '75px' : '100%'\n              }\n            }\n          })\n        }, index);\n      }), /*#__PURE__*/_jsx(\"div\", {\n        style: {\n          flex: 3,\n          paddingInlineStart: 32\n        },\n        children: /*#__PURE__*/_jsx(Skeleton, {\n          active: active,\n          paragraph: false,\n          title: {\n            style: {\n              margin: 0,\n              height: 24,\n              width: header ? '75px' : '100%'\n            }\n          }\n        })\n      })]\n    }), /*#__PURE__*/_jsx(Line, {\n      padding: \"0px 0px\"\n    })]\n  });\n};\n\n/**\n * Table 骨架屏\n *\n * @param param0\n */\nexport var TableSkeleton = function TableSkeleton(_ref4) {\n  var active = _ref4.active,\n    _ref4$size = _ref4.size,\n    size = _ref4$size === void 0 ? 4 : _ref4$size;\n  return /*#__PURE__*/_jsxs(Card, {\n    bordered: false,\n    children: [/*#__PURE__*/_jsx(Skeleton.Button, {\n      active: active,\n      size: \"small\",\n      style: {\n        width: 100,\n        marginBlockEnd: 16\n      }\n    }), /*#__PURE__*/_jsx(TableItemSkeleton, {\n      header: true,\n      active: active\n    }), new Array(size).fill(null).map(function (_, index) {\n      return (\n        /*#__PURE__*/\n        // eslint-disable-next-line react/no-array-index-key\n        _jsx(TableItemSkeleton, {\n          active: active\n        }, index)\n      );\n    }), /*#__PURE__*/_jsx(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        paddingBlockStart: 16\n      },\n      children: /*#__PURE__*/_jsx(Skeleton, {\n        active: active,\n        paragraph: false,\n        title: {\n          style: {\n            margin: 0,\n            height: 32,\n            float: 'right',\n            maxWidth: '630px'\n          }\n        }\n      })\n    })]\n  });\n};\nexport var DescriptionsSkeleton = function DescriptionsSkeleton(_ref5) {\n  var active = _ref5.active;\n  return /*#__PURE__*/_jsxs(Card, {\n    bordered: false,\n    style: {\n      borderStartEndRadius: 0,\n      borderTopLeftRadius: 0\n    },\n    children: [/*#__PURE__*/_jsx(Skeleton.Button, {\n      active: active,\n      size: \"small\",\n      style: {\n        width: 100,\n        marginBlockEnd: 16\n      }\n    }), /*#__PURE__*/_jsx(DescriptionsItemSkeleton, {\n      active: active\n    }), /*#__PURE__*/_jsx(DescriptionsLargeItemSkeleton, {\n      active: active\n    })]\n  });\n};\nvar DescriptionsPageSkeleton = function DescriptionsPageSkeleton(_ref6) {\n  var _ref6$active = _ref6.active,\n    active = _ref6$active === void 0 ? true : _ref6$active,\n    pageHeader = _ref6.pageHeader,\n    list = _ref6.list;\n  return /*#__PURE__*/_jsxs(\"div\", {\n    style: {\n      width: '100%'\n    },\n    children: [pageHeader !== false && /*#__PURE__*/_jsx(PageHeaderSkeleton, {\n      active: active\n    }), /*#__PURE__*/_jsx(DescriptionsSkeleton, {\n      active: active\n    }), list !== false && /*#__PURE__*/_jsx(Line, {}), list !== false && /*#__PURE__*/_jsx(TableSkeleton, {\n      active: active,\n      size: list\n    })]\n  });\n};\nexport default DescriptionsPageSkeleton;", "import { Card, Skeleton, Space } from 'antd';\nimport React from 'react';\nimport { PageHeaderSkeleton } from \"../List\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nvar ResultPageSkeleton = function ResultPageSkeleton(_ref) {\n  var _ref$active = _ref.active,\n    active = _ref$active === void 0 ? true : _ref$active,\n    pageHeader = _ref.pageHeader;\n  return /*#__PURE__*/_jsxs(\"div\", {\n    style: {\n      width: '100%'\n    },\n    children: [pageHeader !== false && /*#__PURE__*/_jsx(PageHeaderSkeleton, {\n      active: active\n    }), /*#__PURE__*/_jsx(Card, {\n      children: /*#__PURE__*/_jsxs(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          flexDirection: 'column',\n          padding: 128\n        },\n        children: [/*#__PURE__*/_jsx(Skeleton.Avatar, {\n          size: 64,\n          style: {\n            marginBlockEnd: 32\n          }\n        }), /*#__PURE__*/_jsx(Skeleton.Button, {\n          active: active,\n          style: {\n            width: 214,\n            marginBlockEnd: 8\n          }\n        }), /*#__PURE__*/_jsx(Skeleton.Button, {\n          active: active,\n          style: {\n            width: 328\n          },\n          size: \"small\"\n        }), /*#__PURE__*/_jsxs(Space, {\n          style: {\n            marginBlockStart: 24\n          },\n          children: [/*#__PURE__*/_jsx(Skeleton.Button, {\n            active: active,\n            style: {\n              width: 116\n            }\n          }), /*#__PURE__*/_jsx(Skeleton.Button, {\n            active: active,\n            style: {\n              width: 116\n            }\n          })]\n        })]\n      })\n    })]\n  });\n};\nexport default ResultPageSkeleton;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"type\"];\nimport \"antd/es/skeleton/style\";\nimport React from 'react';\nimport DescriptionsPageSkeleton, { DescriptionsSkeleton, TableItemSkeleton, TableSkeleton } from \"./components/Descriptions\";\nimport ListPageSkeleton, { ListSkeleton, ListSkeletonItem, ListToolbarSkeleton, PageHeaderSkeleton } from \"./components/List\";\nimport ResultPageSkeleton from \"./components/Result\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar ProSkeleton = function ProSkeleton(_ref) {\n  var _ref$type = _ref.type,\n    type = _ref$type === void 0 ? 'list' : _ref$type,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  if (type === 'result') {\n    return /*#__PURE__*/_jsx(ResultPageSkeleton, _objectSpread({}, rest));\n  }\n  if (type === 'descriptions') {\n    return /*#__PURE__*/_jsx(DescriptionsPageSkeleton, _objectSpread({}, rest));\n  }\n  return /*#__PURE__*/_jsx(ListPageSkeleton, _objectSpread({}, rest));\n};\nexport { DescriptionsSkeleton, ListPageSkeleton, ListSkeleton, ListSkeletonItem, ListToolbarSkeleton, PageHeaderSkeleton, ProSkeleton, TableItemSkeleton, TableSkeleton };\nexport default ProSkeleton;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport PlusOutlinedSvg from \"@ant-design/icons-svg/es/asn/PlusOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar PlusOutlined = function PlusOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: PlusOutlinedSvg\n  }));\n};\n\n/**![plus](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4MiAxNTJoNjBxOCAwIDggOHY3MDRxMCA4LTggOGgtNjBxLTggMC04LThWMTYwcTAtOCA4LTh6IiAvPjxwYXRoIGQ9Ik0xOTIgNDc0aDY3MnE4IDAgOCA4djYwcTAgOC04IDhIMTYwcS04IDAtOC04di02MHEwLTggOC04eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(PlusOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'PlusOutlined';\n}\nexport default RefIcon;", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar _excluded = [\"onTableChange\", \"maxLength\", \"formItemProps\", \"recordCreatorProps\", \"rowKey\", \"controlled\", \"defaultValue\", \"onChange\", \"editableFormRef\"],\n  _excluded2 = [\"record\", \"position\", \"creatorButtonText\", \"newRecordType\", \"parentKey\", \"style\"];\nimport { PlusOutlined } from '@ant-design/icons';\nimport ProForm, { ProFormDependency } from '@ant-design/pro-form';\nimport { useIntl } from '@ant-design/pro-provider';\nimport { isDeepEqualReact, runFunction, stringify, useRefFunction } from '@ant-design/pro-utils';\nimport { Button, Form } from 'antd';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport get from \"rc-util/es/utils/get\";\nimport set from \"rc-util/es/utils/set\";\nimport React, { useContext, useEffect, useImperativeHandle, useMemo, useRef } from 'react';\nimport ProTable from \"../../Table\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nvar EditableTableActionContext = /*#__PURE__*/React.createContext(undefined);\n\n/** 可编辑表格的按钮 */\nfunction RecordCreator(props) {\n  var children = props.children,\n    record = props.record,\n    position = props.position,\n    newRecordType = props.newRecordType,\n    parentKey = props.parentKey;\n  var actionRef = useContext(EditableTableActionContext);\n  return /*#__PURE__*/React.cloneElement(children, _objectSpread(_objectSpread({}, children.props), {}, {\n    onClick: function () {\n      var _onClick = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(e) {\n        var _children$props$onCli, _children$props, _actionRef$current;\n        var isOk;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return (_children$props$onCli = (_children$props = children.props).onClick) === null || _children$props$onCli === void 0 ? void 0 : _children$props$onCli.call(_children$props, e);\n            case 2:\n              isOk = _context.sent;\n              if (!(isOk === false)) {\n                _context.next = 5;\n                break;\n              }\n              return _context.abrupt(\"return\");\n            case 5:\n              actionRef === null || actionRef === void 0 || (_actionRef$current = actionRef.current) === null || _actionRef$current === void 0 || _actionRef$current.addEditRecord(record, {\n                position: position,\n                newRecordType: newRecordType,\n                parentKey: parentKey\n              });\n            case 6:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      function onClick(_x) {\n        return _onClick.apply(this, arguments);\n      }\n      return onClick;\n    }()\n  }));\n}\n\n/**\n * 可以直接放到 Form 中的可编辑表格\n * A React component that is used to create a table.\n * @param props\n */\nfunction EditableTable(props) {\n  var _props$editable2, _props$editable4;\n  var intl = useIntl();\n  var onTableChange = props.onTableChange,\n    maxLength = props.maxLength,\n    formItemProps = props.formItemProps,\n    recordCreatorProps = props.recordCreatorProps,\n    rowKey = props.rowKey,\n    controlled = props.controlled,\n    defaultValue = props.defaultValue,\n    onChange = props.onChange,\n    editableFormRef = props.editableFormRef,\n    rest = _objectWithoutProperties(props, _excluded);\n  var preData = useRef(undefined);\n  var actionRef = useRef();\n  var formRef = useRef();\n\n  // 设置 ref\n  useImperativeHandle(rest.actionRef, function () {\n    return actionRef.current;\n  }, [actionRef.current]);\n  var _useMergedState = useMergedState(function () {\n      return props.value || defaultValue || [];\n    }, {\n      value: props.value,\n      onChange: props.onChange\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    value = _useMergedState2[0],\n    setValue = _useMergedState2[1];\n  var getRowKey = React.useMemo(function () {\n    if (typeof rowKey === 'function') {\n      return rowKey;\n    }\n    return function (record, index) {\n      return record[rowKey] || index;\n    };\n  }, [rowKey]);\n\n  /**\n   * 根据不同的情况返回不同的 rowKey\n   * @param finlayRowKey\n   * @returns string | number\n   */\n  var coverRowKey = useRefFunction(function (finlayRowKey) {\n    /**\n     * 如果是 prop.name 的模式，就需要把行号转化成具体的rowKey。\n     */\n    if (typeof finlayRowKey === 'number' && !props.name) {\n      if (finlayRowKey >= value.length) return finlayRowKey;\n      var rowData = value && value[finlayRowKey];\n      return getRowKey === null || getRowKey === void 0 ? void 0 : getRowKey(rowData, finlayRowKey);\n    }\n\n    /**\n     * 如果是 prop.name 的模式，就直接返回行号\n     */\n    if ((typeof finlayRowKey === 'string' || finlayRowKey >= value.length) && props.name) {\n      var _rowIndex = value.findIndex(function (item, index) {\n        var _getRowKey;\n        return (getRowKey === null || getRowKey === void 0 || (_getRowKey = getRowKey(item, index)) === null || _getRowKey === void 0 ? void 0 : _getRowKey.toString()) === (finlayRowKey === null || finlayRowKey === void 0 ? void 0 : finlayRowKey.toString());\n      });\n      if (_rowIndex !== -1) return _rowIndex;\n    }\n    return finlayRowKey;\n  });\n\n  // 设置 editableFormRef\n  useImperativeHandle(editableFormRef, function () {\n    /**\n     * 获取一行数据的\n     * @param rowIndex\n     * @returns T | undefined\n     */\n    var getRowData = function getRowData(rowIndex) {\n      var _finlayRowKey$toStrin, _formRef$current;\n      if (rowIndex == undefined) {\n        throw new Error('rowIndex is required');\n      }\n      var finlayRowKey = coverRowKey(rowIndex);\n      var rowKeyName = [props.name, (_finlayRowKey$toStrin = finlayRowKey === null || finlayRowKey === void 0 ? void 0 : finlayRowKey.toString()) !== null && _finlayRowKey$toStrin !== void 0 ? _finlayRowKey$toStrin : ''].flat(1).filter(Boolean);\n      return (_formRef$current = formRef.current) === null || _formRef$current === void 0 ? void 0 : _formRef$current.getFieldValue(rowKeyName);\n    };\n\n    /**\n     * 获取整个 table 的数据\n     * @returns T[] | undefined\n     */\n    var getRowsData = function getRowsData() {\n      var _formRef$current3;\n      var rowKeyName = [props.name].flat(1).filter(Boolean);\n      if (Array.isArray(rowKeyName) && rowKeyName.length === 0) {\n        var _formRef$current2;\n        var rowData = (_formRef$current2 = formRef.current) === null || _formRef$current2 === void 0 ? void 0 : _formRef$current2.getFieldsValue();\n        if (Array.isArray(rowData)) return rowData;\n        return Object.keys(rowData).map(function (key) {\n          return rowData[key];\n        });\n      }\n      return (_formRef$current3 = formRef.current) === null || _formRef$current3 === void 0 ? void 0 : _formRef$current3.getFieldValue(rowKeyName);\n    };\n    return _objectSpread(_objectSpread({}, formRef.current), {}, {\n      getRowData: getRowData,\n      getRowsData: getRowsData,\n      /**\n       * 设置一行的数据，会将数据进行简单的 merge\n       * @param rowIndex\n       * @param data\n       * @returns void\n       */\n      setRowData: function setRowData(rowIndex, data) {\n        var _finlayRowKey$toStrin2, _formRef$current4;\n        if (rowIndex == undefined) {\n          throw new Error('rowIndex is required');\n        }\n        var finlayRowKey = coverRowKey(rowIndex);\n        var rowKeyName = [props.name, (_finlayRowKey$toStrin2 = finlayRowKey === null || finlayRowKey === void 0 ? void 0 : finlayRowKey.toString()) !== null && _finlayRowKey$toStrin2 !== void 0 ? _finlayRowKey$toStrin2 : ''].flat(1).filter(Boolean);\n        var newRowData = Object.assign({}, _objectSpread(_objectSpread({}, getRowData(rowIndex)), data || {}));\n        var updateValues = set({}, rowKeyName, newRowData);\n        (_formRef$current4 = formRef.current) === null || _formRef$current4 === void 0 || _formRef$current4.setFieldsValue(updateValues);\n        return true;\n      }\n    });\n  }, [coverRowKey, props.name, formRef.current]);\n  useEffect(function () {\n    if (!props.controlled) return;\n    (value || []).forEach(function (current, index) {\n      var _formRef$current5;\n      (_formRef$current5 = formRef.current) === null || _formRef$current5 === void 0 || _formRef$current5.setFieldsValue(_defineProperty({}, \"\".concat(getRowKey(current, index)), current));\n    }, {});\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [stringify(value), props.controlled]);\n  useEffect(function () {\n    if (props.name) {\n      var _props$editable;\n      formRef.current = props === null || props === void 0 || (_props$editable = props.editable) === null || _props$editable === void 0 ? void 0 : _props$editable.form;\n    }\n  }, [(_props$editable2 = props.editable) === null || _props$editable2 === void 0 ? void 0 : _props$editable2.form, props.name]);\n  var _ref = recordCreatorProps || {},\n    record = _ref.record,\n    position = _ref.position,\n    creatorButtonText = _ref.creatorButtonText,\n    newRecordType = _ref.newRecordType,\n    parentKey = _ref.parentKey,\n    style = _ref.style,\n    restButtonProps = _objectWithoutProperties(_ref, _excluded2);\n  var isTop = position === 'top';\n  var creatorButtonDom = useMemo(function () {\n    if (typeof maxLength === 'number' && maxLength <= (value === null || value === void 0 ? void 0 : value.length)) {\n      return false;\n    }\n    return recordCreatorProps !== false && /*#__PURE__*/_jsx(RecordCreator, {\n      record: runFunction(record, value === null || value === void 0 ? void 0 : value.length, value) || {},\n      position: position,\n      parentKey: runFunction(parentKey, value === null || value === void 0 ? void 0 : value.length, value),\n      newRecordType: newRecordType,\n      children: /*#__PURE__*/_jsx(Button, _objectSpread(_objectSpread({\n        type: \"dashed\",\n        style: _objectSpread({\n          display: 'block',\n          margin: '10px 0',\n          width: '100%'\n        }, style),\n        icon: /*#__PURE__*/_jsx(PlusOutlined, {})\n      }, restButtonProps), {}, {\n        children: creatorButtonText || intl.getMessage('editableTable.action.add', '添加一行数据')\n      }))\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [recordCreatorProps, maxLength, value === null || value === void 0 ? void 0 : value.length]);\n  var buttonRenderProps = useMemo(function () {\n    if (!creatorButtonDom) {\n      return {};\n    }\n    if (isTop) {\n      return {\n        components: {\n          header: {\n            wrapper: function wrapper(_ref2) {\n              var _rest$columns;\n              var className = _ref2.className,\n                children = _ref2.children;\n              return /*#__PURE__*/_jsxs(\"thead\", {\n                className: className,\n                children: [children, /*#__PURE__*/_jsxs(\"tr\", {\n                  style: {\n                    position: 'relative'\n                  },\n                  children: [/*#__PURE__*/_jsx(\"td\", {\n                    colSpan: 0,\n                    style: {\n                      visibility: 'hidden'\n                    },\n                    children: creatorButtonDom\n                  }), /*#__PURE__*/_jsx(\"td\", {\n                    style: {\n                      position: 'absolute',\n                      left: 0,\n                      width: '100%'\n                    },\n                    colSpan: (_rest$columns = rest.columns) === null || _rest$columns === void 0 ? void 0 : _rest$columns.length,\n                    children: creatorButtonDom\n                  })]\n                })]\n              });\n            }\n          }\n        }\n      };\n    }\n    return {\n      tableViewRender: function tableViewRender(_, dom) {\n        var _props$tableViewRende, _props$tableViewRende2;\n        return /*#__PURE__*/_jsxs(_Fragment, {\n          children: [(_props$tableViewRende = (_props$tableViewRende2 = props.tableViewRender) === null || _props$tableViewRende2 === void 0 ? void 0 : _props$tableViewRende2.call(props, _, dom)) !== null && _props$tableViewRende !== void 0 ? _props$tableViewRende : dom, creatorButtonDom]\n        });\n      }\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isTop, creatorButtonDom]);\n  var editableProps = _objectSpread({}, props.editable);\n\n  /**\n   * 防止闭包的onchange\n   *\n   * >>>>>>为了性能好辛苦\n   */\n  var newOnValueChange = useRefFunction(function (r, dataSource) {\n    var _props$editable3, _props$editable3$onVa, _props$onValuesChange;\n    (_props$editable3 = props.editable) === null || _props$editable3 === void 0 || (_props$editable3$onVa = _props$editable3.onValuesChange) === null || _props$editable3$onVa === void 0 || _props$editable3$onVa.call(_props$editable3, r, dataSource);\n    (_props$onValuesChange = props.onValuesChange) === null || _props$onValuesChange === void 0 || _props$onValuesChange.call(props, dataSource, r);\n    if (props.controlled) {\n      var _props$onChange;\n      props === null || props === void 0 || (_props$onChange = props.onChange) === null || _props$onChange === void 0 || _props$onChange.call(props, dataSource);\n    }\n  });\n  if (props !== null && props !== void 0 && props.onValuesChange || (_props$editable4 = props.editable) !== null && _props$editable4 !== void 0 && _props$editable4.onValuesChange ||\n  // 受控模式需要触发 onchange\n  props.controlled && props !== null && props !== void 0 && props.onChange) {\n    editableProps.onValuesChange = newOnValueChange;\n  }\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [/*#__PURE__*/_jsx(EditableTableActionContext.Provider, {\n      value: actionRef,\n      children: /*#__PURE__*/_jsx(ProTable, _objectSpread(_objectSpread(_objectSpread({\n        search: false,\n        options: false,\n        pagination: false,\n        rowKey: rowKey,\n        revalidateOnFocus: false\n      }, rest), buttonRenderProps), {}, {\n        tableLayout: \"fixed\",\n        actionRef: actionRef,\n        onChange: onTableChange,\n        editable: _objectSpread(_objectSpread({}, editableProps), {}, {\n          formProps: _objectSpread({\n            formRef: formRef\n          }, editableProps.formProps)\n        }),\n        dataSource: value,\n        onDataSourceChange: function onDataSourceChange(dataSource) {\n          setValue(dataSource);\n          /**\n           * 如果是top，需要重新设置一下 form，不然会导致 id 相同数据混淆\n           */\n          if (props.name && position === 'top') {\n            var _formRef$current6;\n            var newValue = set({}, [props.name].flat(1).filter(Boolean), dataSource);\n            (_formRef$current6 = formRef.current) === null || _formRef$current6 === void 0 || _formRef$current6.setFieldsValue(newValue);\n          }\n        }\n      }))\n    }), props.name ? /*#__PURE__*/_jsx(ProFormDependency, {\n      name: [props.name],\n      children: function children(changeValue) {\n        var _props$editable5, _props$editable5$onVa;\n        if (!preData.current) {\n          preData.current = value;\n          return null;\n        }\n        var list = get(changeValue, [props.name].flat(1));\n        var changeItem = list === null || list === void 0 ? void 0 : list.find(function (item, index) {\n          var _preData$current;\n          return !isDeepEqualReact(item, (_preData$current = preData.current) === null || _preData$current === void 0 ? void 0 : _preData$current[index]);\n        });\n        preData.current = value;\n        if (!changeItem) return null;\n        // 如果不存在 preData 说明是初始化，此时不需要触发 onValuesChange\n        props === null || props === void 0 || (_props$editable5 = props.editable) === null || _props$editable5 === void 0 || (_props$editable5$onVa = _props$editable5.onValuesChange) === null || _props$editable5$onVa === void 0 || _props$editable5$onVa.call(_props$editable5, changeItem, list);\n        return null;\n      }\n    }) : null]\n  });\n}\n\n/**\n * 可以直接放到 Form 中的可编辑表格\n * A React component that is used to create a table.\n * @param props\n */\nfunction FieldEditableTable(props) {\n  var form = ProForm.useFormInstance();\n  if (!props.name) return /*#__PURE__*/_jsx(EditableTable, _objectSpread({\n    tableLayout: \"fixed\",\n    scroll: {\n      x: 'max-content'\n    }\n  }, props));\n  return /*#__PURE__*/_jsx(Form.Item, _objectSpread(_objectSpread({\n    style: {\n      maxWidth: '100%'\n    }\n  }, props === null || props === void 0 ? void 0 : props.formItemProps), {}, {\n    name: props.name,\n    shouldUpdate: function shouldUpdate(prev, next) {\n      var name = [props.name].flat(1);\n      try {\n        return JSON.stringify(get(prev, name)) !== JSON.stringify(get(next, name));\n      } catch (error) {\n        return true;\n      }\n    },\n    children: /*#__PURE__*/_jsx(EditableTable, _objectSpread(_objectSpread({\n      tableLayout: \"fixed\",\n      scroll: {\n        x: 'max-content'\n      }\n    }, props), {}, {\n      editable: _objectSpread(_objectSpread({}, props.editable), {}, {\n        form: form\n      })\n    }))\n  }));\n}\nFieldEditableTable.RecordCreator = RecordCreator;\nexport default FieldEditableTable;", "var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nimport * as React from \"react\";\nconst SvgEmpty = (props) => /* @__PURE__ */ React.createElement(\"svg\", __spreadValues({ id: \"empty_svg___\\\\u56FE\\\\u5C42_1\", \"data-name\": \"\\\\u56FE\\\\u5C42 1\", xmlns: \"http://www.w3.org/2000/svg\", viewBox: \"0 0 96 96\" }, props), /* @__PURE__ */ React.createElement(\"defs\", null, /* @__PURE__ */ React.createElement(\"style\", null, \".empty_svg__cls-3,.empty_svg__cls-4{fill:#fff}.empty_svg__cls-6{fill:#1c82ba}.empty_svg__cls-7{fill:#9acdf7}.empty_svg__cls-10,.empty_svg__cls-11,.empty_svg__cls-4,.empty_svg__cls-9{stroke-linecap:round;stroke-linejoin:round}.empty_svg__cls-10,.empty_svg__cls-4{stroke:#000;stroke-width:.5px}.empty_svg__cls-9{stroke:#1c82ba}.empty_svg__cls-10,.empty_svg__cls-11,.empty_svg__cls-9{fill:none}.empty_svg__cls-11,.empty_svg__cls-9{stroke-width:.25px}.empty_svg__cls-11{stroke:#9acdf7}\")), /* @__PURE__ */ React.createElement(\"ellipse\", { cx: 42.66, cy: 78.07, rx: 8, ry: 2, style: {\n  fill: \"#dbdbdb\"\n} }), /* @__PURE__ */ React.createElement(\"path\", { className: \"empty_svg__cls-4\", d: \"M24.2 53.21c0 10.34 8.38 18.73 18.73 18.73s18.73-8.38 18.73-18.73-8.38-18.73-18.73-18.73c-2.32 0-4.54.42-6.6 1.2-2.92 1.1-5.5 2.91-7.51 5.22-2.88 3.29-4.62 7.6-4.62 12.32Z\" }), /* @__PURE__ */ React.createElement(\"path\", { className: \"empty_svg__cls-4\", d: \"M26.37 37.29h0l2.45 3.59c2.01-2.31 4.59-4.12 7.51-5.22l-2.46-3.33h0v-.01c-1.33.08-5.14.7-7.46 4.89-.01.03-.03.05-.04.08Z\" }), /* @__PURE__ */ React.createElement(\"path\", { className: \"empty_svg__cls-10\", d: \"m26.33 ***********\" }), /* @__PURE__ */ React.createElement(\"path\", { className: \"empty_svg__cls-4\", d: \"m22.31 33.25 2.08 3.05c2.3-5.44 7.38-6.07 9.64-6.07L32 26.91c-.33-.54-.91-.88-1.54-.91-5.5-.27-7.64 3.49-8.37 5.39-.24.62-.16 1.31.22 1.86Z\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"m24.4 36.3.07.11c.43.62 1.15.92 ********** 0 .06 0 .08-.01 2.31-4.2 6.12-4.81 7.46-4.89h.03c.6-.55.64-1.24.18-2.04l-.04-.06c-2.26 0-7.34.64-9.64 6.07Z\", style: {\n  stroke: \"#000\",\n  strokeWidth: \".5px\",\n  strokeLinecap: \"round\",\n  strokeLinejoin: \"round\",\n  fill: \"#e0dede\"\n} }), /* @__PURE__ */ React.createElement(\"path\", { className: \"empty_svg__cls-10\", d: \"m33.88 32.34.02-.02\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M32.57 65.43s.01.03.02.05c.77 1.39 4.96 3.57 7.43 3.95 2.84.44 5.65.27 7.91-.36 3.91-1.09 6.38-3.76 6.38-3.76h-.03c-.97.28-1.16.27-3.22-.05-1.38-.22-3.21-.09-4.27.02-.52.05-1.06.06-1.59.01-1.28-.11-2.73.05-3.87.23a9.71 9.71 0 0 1-3.57-.11c-1.25-.29-2.21-.11-2.21-.11-1.89.57-2.98.12-2.98.12Z\", style: {\n  fill: \"#0047bb\"\n} }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M32.67 65.6s-.01-.03-.02-.05c0 0 1.07.5 2.95-.14 0 0 .95-.19 2.2.12 1.1.28 2.38.33 3.54.12 1.14-.21 2.57-.38 3.84-.26.52.05 1.06.05 1.58-.01 1.05-.12 2.86-.26 4.23-.02 2.04.36 2.23.36 **********-.13.26-.29-.73-.56a6.87 6.87 0 0 0-2.05-.21c-.46.02-1.07.04-1.73.05-.74.01-1.54.01-2.26-.02-.35-.01-.68-.03-.97-.06-.35-.03-.64-.08-.86-.14 0 0-.11-.02-.32-.05-.53-.07-1.69-.18-3.12 0-.37.05-.77.11-1.17.2-.1.02-.21.05-.31.07 0 0-.25.02-.65.03-.57.02-1.43.03-2.29-.02-.56-.03-1.12-.09-1.59-.19-.7-.14-1.48-.18-2.21-.07-.92.14-1.81.45-1.25 1.14Z\", style: {\n  fill: \"#033884\"\n} }), /* @__PURE__ */ React.createElement(\"ellipse\", { className: \"empty_svg__cls-7\", cx: 47.93, cy: 37.72, rx: 0.54, ry: 1.83, transform: \"rotate(-70.51 47.931 37.72)\" }), /* @__PURE__ */ React.createElement(\"path\", { className: \"empty_svg__cls-7\", d: \"M58.43 46.24c-.41-1.17-1.77-4.38-5.06-6.4-.16-.1-.34-.15-.52-.17-.3-.02-.71.02-.9.38-.15.29-.***********.66.51 2.35 2.05 4.52 **********.***********.***********.93.21.43-.13.65-.62.5-1.04Z\" }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-6\", cx: 31.21, cy: 47.41, r: 1.08 }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-6\", cx: 28.71, cy: 46.6, r: 0.61 }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-6\", cx: 31.55, cy: 45.25, r: 0.61 }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-6\", cx: 30.19, cy: 49.44, r: 0.61 }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-6\", cx: 34.12, cy: 48.22, r: 0.61 }), /* @__PURE__ */ React.createElement(\"path\", { className: \"empty_svg__cls-9\", d: \"m30.42 49.04.4-.7M30.22 47.26l-1.05-.41M31.37 46.34l.06-.58M33.67 47.96l-1.48-.28\" }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-7\", cx: 51.72, cy: 63.62, r: 0.95 }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-7\", cx: 50.23, cy: 61.72, r: 0.81 }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-7\", cx: 52.39, cy: 61.05, r: 0.68 }), /* @__PURE__ */ React.createElement(\"path\", { className: \"empty_svg__cls-11\", d: \"m50.69 62.39.38.54M50.99 61.45l.74-.26\" }), /* @__PURE__ */ React.createElement(\"path\", { className: \"empty_svg__cls-10\", d: \"M27.11 28.06s-2.43.68-3.52 3.92M27.51 37.25l1.36 1.76\" }), /* @__PURE__ */ React.createElement(\"path\", { className: \"empty_svg__cls-3\", d: \"M31.98 47.26c-.36-.41-.68-.51-1.22-.54.44-.37 1.23-.04 1.22.54Z\" }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-3\", cx: 34.21, cy: 48.13, r: 0.14 }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-3\", cx: 52.46, cy: 60.85, r: 0.14 }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-3\", cx: 50.16, cy: 61.39, r: 0.14 }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-3\", cx: 51.11, cy: 63.96, r: 0.14 }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-3\", cx: 31.64, cy: 45.03, r: 0.14 }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-3\", cx: 28.39, cy: 46.38, r: 0.14 }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-3\", cx: 30.08, cy: 49.55, r: 0.07 }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M47.68 52.69c0 1.98 1.6 3.58 3.58 3.58s3.58-1.6 3.58-3.58-1.62-3.52-3.6-3.52-3.57 1.54-3.57 3.52Z\" }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-3\", cx: 50.69, cy: 53.22, r: 0.6 }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M41.76 54.69c0 1.98-1.6 3.58-3.58 3.58s-3.58-1.6-3.58-3.58 1.64-3.52 3.62-3.52 3.55 1.54 3.55 3.52Z\" }), /* @__PURE__ */ React.createElement(\"path\", { className: \"empty_svg__cls-3\", d: \"M38.48 55.12c0 .33-.27.6-.6.6-.33 0-.6-.27-.6-.6 0-.33.27-.6.6-.6.33 0 .6.27.6.6Z\" }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-3\", cx: 52.01, cy: 51.54, r: 1.25 }), /* @__PURE__ */ React.createElement(\"circle\", { className: \"empty_svg__cls-3\", cx: 39.28, cy: 53.54, r: 1.25 }), /* @__PURE__ */ React.createElement(\"path\", { className: \"empty_svg__cls-10\", d: \"M33.82 51.42s.12-2.19 2.18-1.7M47.01 49.38s.13-2.19 2.19-1.66M36.22 60.19s8.92 6.64 17.97-2.36M36.38 59.29s-1.09-.29-1.18.96M55.13 57.7s-.17-1.12-1.35-.7\" }), /* @__PURE__ */ React.createElement(\"path\", { className: \"empty_svg__cls-3\", d: \"M72.87 78.57c.43-.23.83-.5 1.2-.8l-1.2.8Zm0 0c.43-.23.83-.5 1.2-.8l-1.2.8Zm0 0c.43-.23.83-.5 1.2-.8l-1.2.8ZM67.9 62.94h-.07s-2.87 2.25-5.48 1.5l.02.15c-5.13-.3-4.84-2.51-4.84-2.51-2.9-2.38.4-5.72.4-5.72 2.04-1.77.26-3.81.26-3.81-.88-1.03-.92-1.68.32-2.35 2.11-1.13 4.16.18 4.52 1.75.22.94-.02 1.99-.35 2.85-.02.04-.03.08-.05.12l.14-.09c1.3-.6 1.76-1.28 2.05-2.01.33-.82.47-1.71.52-2.59.04-.72.1-1.53.17-1.73.51-1.44 2.14-1.86 3.13-1.79.81.05 1.88.96 2.33 1.72.4.68.42 1.56.18 2.42-.04.14-.08.28-.12.44a8.525 8.525 0 0 1-2.75 4.31c-.07.06-.14.11-.21.17l-1.2.8c-.12.07-.24.14-.37.2v.05l.37-.25c.43-.23.83-.5 1.2-.8l.23-.16c.57.39 1.52 1.13 1.57 1.17.81.72 1.04 1.5.31 2.23l.55-.16c3.77 3.28-2.83 4.09-2.83 4.09Z\" }), /* @__PURE__ */ React.createElement(\"path\", { className: \"empty_svg__cls-4\", d: \"M62.42 64.7c-5.13-.31-4.84-2.51-4.84-2.51-2.91-2.37.4-5.72.4-5.72 2.04-1.77.26-3.81.26-3.81-.89-1.02-.93-1.68.31-2.34 2.11-1.13 4.16.17 4.52 1.74.48 2.11-1.29 4.7-1.29 4.7 1.53 1.34.19 3.81.19 3.81m4.56-3.69h0c.64-.31 1.24-.71 1.79-1.17 1.32-1.12 2.31-2.64 2.75-4.3.04-.16.08-.31.12-.45.24-.85.22-1.74-.19-2.42-.45-.76-1.52-1.67-2.33-1.72-.99-.07-2.62.35-3.13 1.79-.07.21-.13 1.01-.17 1.73-.05.88-.19 1.77-.52 2.59-.29.72-.75 1.41-2.05 2.01m1.87 3.24s-.03.03-.08.1c-.99 1.34.16 2.37 1.23 2.5s3.48-.8 4.3-1.56c.83-.76.61-1.58-.23-2.33-.04-.04-.99-.78-1.56-1.16m-7.91 7.51c2.86 3.4 7.43-.18 7.43-.18-2.93-.88-1.4-2.22-1.4-2.22m1.37 2.21s6.71-.8 2.93-4.08l-.26-.15\" }));\nexport { SvgEmpty as ReactComponent };\nexport default \"data:image/svg+xml;base64,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\";\n", "// extracted by mini-css-extract-plugin\nexport default {\"statusTip\":\"statusTip___IIrJs\"};", "import { ReactComponent as EmptyIcon } from '@/assets/svgs/empty.svg'\n\nimport { Empty } from 'antd'\nimport cs from 'classnames'\nimport type { StatusTipProps } from './index.d'\nimport styles from './index.less'\n\nexport default function StatusTip(props: StatusTipProps) {\n  return (\n    <div\n      className={cs(styles.statusTip, 'flex-center', props?.wrapperClassName)}\n    >\n      <Empty\n        image={props?.image ? props?.image : <EmptyIcon />}\n        imageStyle={{ height: 200 }}\n        description={\n          <span>\n            {props?.clickEvent ? (\n              <a onClick={props?.clickEvent}>{props?.des}</a>\n            ) : (\n              props?.des\n            )}\n          </span>\n        }\n      />\n    </div>\n  )\n}\n", "import { Modal, ModalProps } from 'antd'\nimport React, { useMemo, useState } from 'react'\n\nexport interface ModelWithTriggerProps\n  extends React.PropsWithChildren<ModalProps> {\n  trigger?: JSX.Element\n}\n\nconst ModelWithTrigger: React.FC<ModelWithTriggerProps> = ({\n  trigger,\n  children,\n  onCancel,\n  ...modelProps\n}) => {\n  const [open, setOpen] = useState<boolean>(false)\n\n  const triggerDom = useMemo(() => {\n    if (!trigger) return\n    return React.cloneElement(trigger, {\n      key: 'trigger',\n      ...trigger.props,\n      onClick: async (e: any) => {\n        setOpen(!open)\n        trigger.props?.onClick?.(e)\n      }\n    })\n  }, [setOpen, trigger, open])\n\n  return (\n    <>\n      {triggerDom}\n      <Modal\n        onCancel={(e) => {\n          onCancel?.(e)\n          setOpen(false)\n        }}\n        open={open}\n        {...modelProps}\n      >\n        {children}\n      </Modal>\n    </>\n  )\n}\n\nexport default ModelWithTrigger\n", "import { use<PERSON>rainFetch } from '@/hooks/useBrainFetch'\nimport useOptions from '@/hooks/useOptions'\nimport {\n  Project,\n  ProjectMember,\n  ProjectRole,\n  query,\n  service\n} from '@/services/brain'\nimport { UserBasicInfo } from '@/types/Common'\nimport { getWord, isPartenerMode } from '@/utils'\nimport {\n  EditableProTable,\n  ProColumns,\n  ProDescriptions,\n  ProDescriptionsItemProps,\n  ProDescriptionsProps\n} from '@ant-design/pro-components'\nimport { useModel } from '@umijs/max'\nimport { ModalProps, message } from 'antd'\nimport dayjs, { Dayjs } from 'dayjs'\nimport React, { useState } from 'react'\nimport useProjectRoles from '../hooks/useProjectRoles'\nimport {\n  ProjectListItem,\n  productManagerRoleCode,\n  projectTypeEnums\n} from '../utils'\nimport ModelWithTrigger from './ModelWithTrigger'\n\nexport interface MemberEditorProps {\n  projectId: number\n  members: Project['project_members']\n  onUpdated?: () => void\n}\n\ninterface ProjectMemberFormItem extends Omit<ProjectMember, 'user_id'> {\n  user_id: number\n}\n\nconst editable = (member: ProjectMemberFormItem, currentUserId?: number) =>\n  !(\n    member.user_id === currentUserId &&\n    member.role?.code === productManagerRoleCode\n  )\n\nconst memberEditorColumns: (\n  currentUser?: UserBasicInfo,\n  defaultUserOptions?: { value: number; label: string }[]\n) => ProColumns<ProjectMemberFormItem>[] = (\n  currentUser,\n  defaultUserOptions\n) => [\n  {\n    title: getWord('member'),\n    dataIndex: 'user_id',\n    valueType: 'select',\n    request: async ({ keyWords }) => {\n      if (!keyWords?.length) {\n        return defaultUserOptions || []\n      }\n      const { data } = await service<UserBasicInfo>(\n        `users/search?q=${keyWords}`\n      )\n        .select()\n        .get()\n\n      return (\n        data?.map((u) => ({\n          value: u.id,\n          label: `${u.username}`\n        })) || []\n      )\n    },\n    renderText: (_, record) => {\n      return record?.user_info?.username\n    },\n    formItemProps: {\n      rules: [{ required: true }]\n    },\n    fieldProps: {\n      showSearch: true\n    }\n  },\n  {\n    title: getWord('role'),\n    dataIndex: ['role', 'code'],\n    valueType: 'select',\n    request: async () => {\n      const { data } = await query<ProjectRole>('project-roles')\n        .paginate(1, 1000)\n        .get()\n      return (data || []).map((r) => ({ value: r.code, label: r.name_zh }))\n    },\n    formItemProps: { rules: [{ required: true }] }\n  },\n  {\n    title: getWord('pages.experiment.label.operation'),\n    valueType: 'option',\n    render: (_, record, __, action) => {\n      if (editable(record, currentUser?.id)) {\n        return [\n          <a key=\"editable\" onClick={() => action?.startEditable?.(record.id)}>\n            {getWord('edit')}\n          </a>\n        ]\n      }\n      return null\n    }\n  }\n]\n\nconst MemberEditor: React.FC<MemberEditorProps> = ({\n  projectId,\n  members,\n  onUpdated\n}) => {\n  const { editableConfig } = useOptions()\n  const allRoles = useProjectRoles()\n  const { initialState } = useModel('@@initialState')\n  const optionIds = new Set(members?.map((m) => m.user_info?.id))\n  const options = members\n    ?.map((m) => m.user_info)\n    .reduce<{ value: number; label: string }[]>((acc, cur) => {\n      if (cur?.id && optionIds.has(cur.id)) {\n        acc.push({ value: cur.id, label: cur.username })\n        optionIds.delete(cur.id)\n      }\n      return acc\n    }, [])\n\n  return (\n    <EditableProTable<ProjectMemberFormItem>\n      rowKey=\"id\"\n      headerTitle={getWord('team-allocation')}\n      recordCreatorProps={{\n        creatorButtonText: getWord('add-member'),\n        record: () =>\n          ({\n            id: (Math.random() * 1000000).toFixed(0)\n          } as unknown as ProjectMemberFormItem)\n      }}\n      columns={memberEditorColumns(initialState?.userInfo, options)}\n      value={members?.map((m) => ({\n        ...m,\n        user_id: Number.parseInt(m.user_id)\n      }))}\n      editable={{\n        ...editableConfig,\n        type: 'single',\n        onDelete: async (_, row) => {\n          const { error } = await service<ProjectMember>(\n            'project-members'\n          ).deleteOne(row.id)\n          if (error) throw error\n          onUpdated?.()\n          return true\n        },\n        onSave: async (_, data, __, newLine) => {\n          const userId =\n            typeof data.user_id === 'number' ? data.user_id : data.user_info.id\n          const roleId = allRoles.find((r) => r.code === data.role.code)?.id\n          if (!roleId || !userId) return\n          if (newLine || typeof data.id === 'string') {\n            const { error } = await service<ProjectMember>(\n              'project-members'\n            ).create({\n              project: { id: projectId } as ProjectMember['project'],\n              role: { id: roleId } as ProjectMember['role'],\n              user_id: `${userId}`\n            } as ProjectMember)\n            if (error?.message) message.error(error?.message)\n            if (error) throw error\n          } else {\n            const { error } = await service<ProjectMember>(\n              'project-members'\n            ).update(data.id, {\n              role: { id: roleId } as ProjectMember['role'],\n              user_id: `${userId}`\n            })\n            if (error?.message) message.error(error?.message)\n            if (error) throw error\n          }\n          onUpdated?.()\n          return true\n        }\n      }}\n    ></EditableProTable>\n  )\n}\n\nconst projectConfigColumns: (ProColumns<ProjectListItem> &\n  ProDescriptionsItemProps<ProjectListItem>)[] = [\n  {\n    title: getWord('pages.projectTable.label.no'),\n    dataIndex: 'no',\n    sorter: true,\n    editable: false,\n    order: 10\n  },\n  {\n    title: getWord('pages.projectTable.label.name'),\n    dataIndex: 'name',\n    editable: false,\n    sorter: true,\n    order: 10\n  },\n  ...(isPartenerMode()\n    ? []\n    : [\n        {\n          title: getWord('pages.projectTable.label.customer'),\n          dataIndex: 'customer',\n          editable: false,\n          sorter: true,\n          order: 10\n        }\n      ]),\n  {\n    title: getWord('pages.projectTable.label.type'),\n    dataIndex: 'type',\n    editable: false,\n    order: 5,\n    valueEnum: projectTypeEnums,\n    valueType: 'select'\n  },\n  {\n    title: getWord('pages.projectTable.label.deliveryDate'),\n    valueType: 'date',\n    dataIndex: 'delivery_date',\n    fieldProps: {\n      disabledDate: (current: Dayjs) =>\n        current && current <= dayjs().startOf('day')\n    }\n  }\n]\nexport interface ConfigModelProps {\n  projectId: number\n  trigger?: JSX.Element\n  columns?: ProDescriptionsProps<ProjectListItem>['columns']\n  modelProps?: ModalProps\n  refreshEvent?: () => void\n}\n\nconst ConfigModel: React.FC<ConfigModelProps> = ({\n  trigger,\n  projectId,\n  columns = projectConfigColumns,\n  modelProps,\n  refreshEvent\n}) => {\n  const { editableConfig } = useOptions()\n  const [project, setProject] = useState<Project>()\n  const { fetch } = useBrainFetch()\n  const getInfo = async (id: number): Promise<Project | undefined> => {\n    const { data } = await fetch(\n      service<Project>('projects')\n        .selectManyByID([id])\n        .populateWith('project_members', ['user_id', 'role'], true)\n        .get()\n    )\n    return data?.[0]\n  }\n\n  return (\n    <ModelWithTrigger\n      trigger={trigger}\n      footer={null}\n      width={800}\n      {...modelProps}\n    >\n      <ProDescriptions<ProjectListItem>\n        title={project?.name || project?.no}\n        column={{ xs: 1, md: 2 }}\n        editable={{\n          ...editableConfig,\n          onSave: async (keypath, newInfo) => {\n            const { error } = await service<Project>('projects').update(\n              newInfo.id,\n              { [keypath as string]: newInfo[keypath as keyof Project] }\n            )\n            if (error) throw error\n            if (refreshEvent) refreshEvent()\n            return true\n          }\n        }}\n        request={async () => {\n          const data = await getInfo(projectId)\n          if (!data) {\n            return { success: false }\n          }\n          setProject(data)\n          return { success: true, data }\n        }}\n        columns={columns}\n      />\n      <MemberEditor\n        members={project?.project_members}\n        projectId={projectId}\n        onUpdated={async () => {\n          setProject(await getInfo(projectId))\n          if (refreshEvent) refreshEvent()\n        }}\n      />\n    </ModelWithTrigger>\n  )\n}\n\nexport default ConfigModel\n", "import { ProjectRole, query } from '@/services/brain'\nimport { useEffect, useState } from 'react'\n\nconst useProjectRoles = () => {\n  const [allRoles, setAllRoles] = useState<ProjectRole[]>([])\n  const fetchRoleOptions = async () => {\n    const { data } = await query<ProjectRole>('project-roles')\n      .paginate(1, 1000)\n      .get()\n    return data || []\n  }\n\n  useEffect(() => {\n    fetchRoleOptions().then((roles) => setAllRoles?.(roles))\n  }, [])\n\n  return allRoles\n}\n\nexport default useProjectRoles\n", "import {\n  Project,\n  ProjectMember,\n  ProjectStatus,\n  ProjectStatusAudit\n} from '@/services/brain'\nimport { getWord, isPartenerMode } from '@/utils'\nimport { omit } from 'lodash'\n\ninterface ProjectCustomFields {\n  managers?: ProjectMember[]\n  last_audit?: ProjectStatusAudit\n}\n\nexport type ProjectListItem = Project & ProjectCustomFields\n\nexport const productManagerRoleCode = 'pm' as const\n\nexport const parseProjectResponse = (data: Project): ProjectListItem => ({\n  ...data,\n  managers: data.project_members?.filter(\n    (m) => m.role?.code === productManagerRoleCode\n  ),\n  last_audit: data.project_status_audits?.sort((a, b) =>\n    (a.createdAt || 0) > (b.createdAt || 0) ? -1 : 1\n  )?.[0]\n})\n\nexport const statusTransformMap: Record<ProjectStatus, ProjectStatus[]> = {\n  created: ['cancelled', 'started'],\n  cancelled: [],\n  finished: [],\n  holding: ['cancelled', 'started'],\n  started: ['cancelled', 'finished', 'holding']\n}\n\nconst defaultProjectTypeEnums = {\n  fte: {\n    text: getWord('pages.projectTable.typeValue.fte')\n  },\n  ffs: {\n    text: getWord('pages.projectTable.typeValue.ffs')\n  },\n  personal: {\n    text: getWord('pages.projectTable.typeValue.personal')\n  }\n}\n\nexport const projectTypeEnums = isPartenerMode()\n  ? omit(defaultProjectTypeEnums, ['fte', 'ffs'])\n  : defaultProjectTypeEnums\n"], "names": ["Line", "_ref", "padding", "MediaQueryKeyEnum", "StatisticSkeleton", "_ref2", "size", "active", "defaultCol", "col", "useBreakpoint", "colSize", "key", "arraySize", "firstWidth", "index", "_", "ListSkeletonItem", "_ref3", "ListSkeleton", "_ref4", "_ref4$active", "actionButton", "PageHeaderSkeleton", "_ref5", "ListToolbarSkeleton", "_ref6", "ListPageSkeleton", "_ref7", "_ref7$active", "statistic", "toolbar", "pageHeader", "_ref7$list", "list", "DescriptionsLargeItemSkeleton", "DescriptionsItemSkeleton", "TableItemSkeleton", "_ref3$header", "header", "TableSkeleton", "_ref4$size", "DescriptionsSkeleton", "DescriptionsPageSkeleton", "_ref6$active", "ResultPageSkeleton", "_ref$active", "_excluded", "ProSkeleton", "_ref$type", "type", "rest", "Result", "Descriptions", "List", "PlusOutlined", "props", "ref", "AntdIcon", "RefIcon", "_excluded2", "EditableTableActionContext", "RecordCreator", "children", "record", "position", "newRecordType", "parent<PERSON><PERSON>", "actionRef", "_onClick", "_callee", "e", "_children$props$onCli", "_children$props", "_actionRef$current", "isOk", "_context", "onClick", "_x", "EditableTable", "_props$editable2", "_props$editable4", "intl", "onTableChange", "max<PERSON><PERSON><PERSON>", "formItemProps", "recordCreatorProps", "<PERSON><PERSON><PERSON>", "controlled", "defaultValue", "onChange", "editableFormRef", "preData", "formRef", "_useMergedState", "useMergedState", "_useMergedState2", "value", "setValue", "getRowKey", "coverRowKey", "useRefFunction", "finlayRowKey", "rowData", "_rowIndex", "item", "_getRow<PERSON>ey", "getRowData", "rowIndex", "_finlayRowKey$toStrin", "_formRef$current", "rowKeyName", "getRowsData", "_formRef$current3", "_formRef$current2", "data", "_finlayRowKey$toStrin2", "_formRef$current4", "newRowData", "updateValues", "set", "current", "_formRef$current5", "stringify", "_props$editable", "creatorButtonText", "style", "restButtonProps", "isTop", "creatorButtonDom", "runFunction", "buttonRenderProps", "_rest$columns", "className", "dom", "_props$tableViewRende", "_props$tableViewRende2", "editableProps", "newOnValueChange", "r", "dataSource", "_props$editable3", "_props$editable3$onVa", "_props$onValuesChange", "_props$onChange", "_formRef$current6", "newValue", "changeValue", "_props$editable5", "_props$editable5$onVa", "get", "changeItem", "_preData$current", "isDeepEqualReact", "FieldEditableTable", "form", "prev", "next", "name", "error", "__defProp", "__getOwnPropSymbols", "__hasOwnProp", "__propIsEnum", "__defNormalProp", "obj", "__spreadValues", "b", "prop", "SvgEmpty", "StatusTip", "_jsx", "cs", "styles", "statusTip", "wrapperClassName", "Empty", "image", "EmptyIcon", "imageStyle", "height", "description", "clickEvent", "des", "ModelWithTrigger", "trigger", "onCancel", "modelProps", "_objectWithoutProperties", "_useState", "useState", "_useState2", "_slicedToArray", "open", "<PERSON><PERSON><PERSON>", "triggerDom", "useMemo", "React", "_objectSpread", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_trigger$props", "_trigger$props$onClic", "wrap", "call", "stop", "apply", "arguments", "_jsxs", "_Fragment", "Modal", "editable", "member", "currentUserId", "_member$role", "user_id", "role", "code", "productManagerRoleCode", "memberEditorColumns", "currentUser", "defaultUserOptions", "title", "getWord", "dataIndex", "valueType", "request", "_request", "key<PERSON>ords", "_yield$service$select", "length", "abrupt", "service", "concat", "select", "sent", "map", "u", "id", "label", "username", "renderText", "_record$user_info", "user_info", "rules", "required", "fieldProps", "showSearch", "_request2", "_callee2", "_yield$query$paginate", "_context2", "query", "paginate", "name_zh", "render", "__", "action", "_action$startEditable", "startEditable", "MemberEditor", "projectId", "members", "onUpdated", "_useOptions", "useOptions", "editableConfig", "allRoles", "useProjectRoles", "_useModel", "useModel", "initialState", "optionIds", "Set", "m", "_m$user_info", "options", "reduce", "acc", "cur", "has", "push", "EditableProTable", "headerTitle", "Math", "random", "toFixed", "columns", "userInfo", "Number", "parseInt", "onDelete", "_onDelete", "_callee3", "row", "_yield$service$delete", "_context3", "deleteOne", "_x2", "_x3", "onSave", "_onSave", "_callee4", "newLine", "_allRoles$find", "userId", "roleId", "_yield$service$create", "_yield$service$update", "_error", "_context4", "find", "create", "project", "message", "update", "_x4", "_x5", "_x6", "_x7", "projectConfigColumns", "sorter", "order", "_toConsumableArray", "isPartenerMode", "valueEnum", "projectTypeEnums", "disabledDate", "dayjs", "startOf", "ConfigModel", "_ref3$columns", "refreshEvent", "_useOptions2", "setProject", "_useBrainFetch", "useBrainFetch", "fetch", "getInfo", "_callee5", "_yield$fetch", "_context5", "selectManyByID", "populateWith", "_x8", "footer", "width", "ProDescriptions", "no", "column", "xs", "md", "_onSave2", "_callee6", "keypath", "newInfo", "_yield$service$update2", "_context6", "_defineProperty", "_x9", "_x10", "_callee7", "_context7", "success", "project_members", "_callee8", "_context8", "t0", "t1", "setAllRoles", "fetchRoleOptions", "useEffect", "then", "roles", "parseProjectResponse", "_data$project_members", "_data$project_status_", "managers", "filter", "_m$role", "last_audit", "project_status_audits", "sort", "a", "createdAt", "statusTransformMap", "created", "cancelled", "finished", "holding", "started", "defaultProjectTypeEnums", "fte", "text", "ffs", "personal", "omit"], "sourceRoot": ""}