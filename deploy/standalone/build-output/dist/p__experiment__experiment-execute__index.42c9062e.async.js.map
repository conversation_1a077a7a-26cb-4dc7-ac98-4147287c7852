{"version": 3, "file": "p__experiment__experiment-execute__index.42c9062e.async.js", "mappings": "sVAMaA,EAAqC,CAChD,CACEC,MAAO,uCACPC,UAAW,cACXC,MAAO,OACPC,MAAO,GACT,EACA,CACEH,SAAOI,EAAAA,IAAQ,6BAA6B,EAC5CH,UAAW,kBACXC,MAAO,OACPC,MAAO,GACT,EACA,CACEH,SAAOI,EAAAA,IAAQ,+BAA+B,EAC9CH,UAAW,SACXC,MAAO,SACPC,MAAO,IACPE,OAAQ,SAACC,EAAS,CAChB,IAAMC,EAAuB,CAC3BC,WAASJ,EAAAA,IAAQ,4CAA4C,EAC7DK,QAAML,EAAAA,IAAQ,oBAAoB,EAClCM,YAAUN,EAAAA,IAAQ,0CAA0C,EAC5DO,aAAWP,EAAAA,IAAQ,4CAA4C,EAC/DQ,WAASR,EAAAA,IAAQ,6BAA6B,EAC9CS,YAAUT,EAAAA,IAAQ,sBAAsB,EACxCU,UAAQV,EAAAA,IAAQ,2CAA2C,CAC7D,EACA,OACEE,MACES,EAAAA,KAACC,EAAAA,EAAG,CAACC,MAAOC,EAAAA,GAAYZ,CAAI,EAAYa,SACrCZ,EAAqBD,CAAI,CAAC,CACxB,CAGX,CACF,EACA,CACEN,SAAOI,EAAAA,IAAQ,8BAA8B,EAC7CH,UAAW,aACXC,MAAO,OACPC,MAAO,IACPE,OAAQ,SAACC,EAAa,CAAF,OAAMA,KAAOc,EAAAA,IAAcd,CAAI,EAAI,EAAE,CAC3D,EACA,CACEN,SAAOI,EAAAA,IAAQ,4BAA4B,EAC3CH,UAAW,WACXE,MAAO,IACPD,MAAO,OACPG,OAAQ,SAACC,EAAa,CAAF,OAAMA,KAAOc,EAAAA,IAAcd,CAAI,EAAI,EAAE,CAC3D,EACA,CACEN,SAAOI,EAAAA,IAAQ,kCAAkC,EACjDF,MAAO,SACPmB,MAAO,QACPlB,MAAO,IACPE,OAAQ,SAACiB,EAACC,EAAwB,KAApBC,EAAaD,EAAbC,cACZ,SAAOT,EAAAA,KAAA,KAAGU,QAAS,oBAAMC,EAAAA,IAAmBF,CAAa,CAAC,EAACL,SAAC,0BAAI,CAAG,CACrE,CACF,CAAC,EChEH,EAAe,CAAC,kBAAoB,2BAA2B,ECClDQ,EAAyB,CACpC,CACEC,SAAOxB,EAAAA,IAAQ,+BAA+B,EAC9CyB,MAAO,SACPC,IAAK,SACLC,MAAO,CACL,CACEH,SAAOxB,EAAAA,IAAQ,4CAA4C,EAC3D4B,MAAO,SACT,EACA,CACEJ,SAAOxB,EAAAA,IAAQ,sBAAsB,EACrC4B,MAAO,UACT,EACA,CACEJ,SAAOxB,EAAAA,IAAQ,oBAAoB,EACnC4B,MAAO,MACT,EACA,CACEJ,SAAOxB,EAAAA,IAAQ,2CAA2C,EAC1D4B,MAAO,QACT,EACA,CACEJ,SAAOxB,EAAAA,IAAQ,4CAA4C,EAC3D4B,MAAO,WACT,EACA,CACEJ,SAAOxB,EAAAA,IAAQ,6BAA6B,EAC5C4B,MAAO,SACT,CAAC,EAEHC,eAAa7B,EAAAA,IAAQ,WAAW,EAChC8B,GAAI,CAAEC,IAAK,EAAGC,WAAY,EAAGC,aAAc,EAAG,CAChD,EACA,CACET,SAAOxB,EAAAA,IAAQ,aAAa,EAC5ByB,MAAO,QACPC,IAAK,SACLG,eAAa7B,EAAAA,IAAQ,YAAY,EACjC8B,GAAI,CAAEC,IAAK,EAAGC,WAAY,EAAGC,aAAc,EAAG,CAChD,EACA,CACET,SAAOxB,EAAAA,IAAQ,6BAA6B,EAC5CyB,MAAO,QACPC,IAAK,kBACLG,eAAa7B,EAAAA,IAAQ,WAAW,EAChC8B,GAAI,CAAEC,IAAK,EAAGC,WAAY,EAAGC,aAAc,EAAG,CAChD,CAAC,ECpCY,SAASC,GAAoB,CAC1C,IAAAC,KAAsCC,EAAAA,UAAcC,EAAAA,EAAU,EAACC,EAAAC,EAAAA,EAAAJ,EAAA,GAAxDK,EAAWF,EAAA,GAAEG,EAAcH,EAAA,GAClCI,KAAqCC,EAAAA,GACnCH,EACAI,EAAAA,EACF,EAHQC,EAAOH,EAAPG,QAASC,EAAQJ,EAARI,SAAUC,EAAKL,EAALK,MAKrBC,EAAc,CAClBH,QAAAA,EACAI,SAAU,GACVC,WAAYJ,EACZK,WAAY,CACVJ,MAAAA,EACAK,QAASZ,EAAYa,QACrBC,SAAUd,EAAYe,UACtBC,UAAW,0BAAAC,OAAUV,EAAK,uBAC1BW,gBAAiB,GACjBC,gBAAiB,EACnB,CACF,EAEA,SACEC,EAAAA,MAACC,EAAAA,GAAa,CACZC,iBAAkB,SAAA3C,EAAoB,KAAjB4C,EAAU5C,EAAV4C,WACfC,EAAqBD,GAAU,YAAVA,EAAYE,MACrC,SAAOC,EAAAA,SAAQF,CAAM,GAAK,IAACG,EAAAA,SAAQH,CAAM,KACvCrD,EAAAA,KAACyD,EAAAA,EAAU,CAAArD,SACRiD,EAAOK,IAAI,SAACC,EAAgBC,EAAkB,CAC7C,SACE5D,EAAAA,KAACyD,EAAAA,EAAWI,KAAI,CACdnD,QAAS,SAACoD,EAAU,CACdF,IAAU,GAAGE,EAAMC,eAAe,CACxC,EAEAC,KAAML,GAAI,YAAJA,EAAMM,SAAmB7D,SAE9BuD,EAAKO,cAAc,EAHfP,GAAI,YAAJA,EAAMM,QAII,CAErB,CAAC,CAAC,CACQ,EAEZ,EAEJ,EACAE,UAAWC,EAAAA,EAAGC,EAAOC,iBAAiB,EAAElE,SAAA,IAExCJ,EAAAA,KAACuE,EAAAA,EAAU,CACTC,SAAU5D,EACV6D,SAAU,SAACC,EAAa,CAAF,OACpB5C,EAAc6C,EAAAA,EAAAA,EAAAA,EAAAA,EAAAA,EAAC,CAAC,EAAI9C,CAAW,EAAK6C,CAAM,MAAEE,OAAQ,CAAC,EAAE,CAAC,EAE1DC,QAAS,kBAAM/C,EAAeJ,EAAAA,EAAU,CAAC,CAAC,CAC3C,KAED1B,EAAAA,KAAC8E,EAAAA,EAAWH,EAAAA,EAAAA,EAAAA,EAAA,GACNtC,CAAW,MACfrD,QAASA,EACT+F,OAAO,uBACPC,SAAU,SAACvC,EAASE,EAAa,CAC/Bb,EAAc6C,EAAAA,EAAAA,EAAAA,EAAC,CAAC,EACX9C,CAAW,MACda,QAASD,EACTG,UAAWD,CAAQ,EACpB,CACH,CAAE,EACH,CAAC,EACW,CAEnB,C", "sources": ["webpack://labwise-web/./src/pages/experiment/experiment-execute/column.tsx", "webpack://labwise-web/./src/pages/experiment/experiment-execute/index.less?0ae3", "webpack://labwise-web/./src/pages/experiment/experiment-execute/query-config.ts", "webpack://labwise-web/./src/pages/experiment/experiment-execute/index.tsx"], "sourcesContent": ["import { statusColor } from '@/constants'\nimport { formatYTSTime, getWord, toExperimentDetail } from '@/utils'\nimport type { ExperimentVo } from '@types'\nimport { Tag } from 'antd'\nimport type { ColumnsType } from 'antd/lib/table'\nimport type { Dayjs } from 'dayjs'\nexport const columns: ColumnsType<ExperimentVo> = [\n  {\n    title: '实验流程名称',\n    dataIndex: 'design_name',\n    align: 'left',\n    width: 180\n  },\n  {\n    title: getWord('pages.experiment.label.name'),\n    dataIndex: 'experiment_name',\n    align: 'left',\n    width: 180\n  },\n  {\n    title: getWord('pages.experiment.label.status'),\n    dataIndex: 'status',\n    align: 'center',\n    width: 150,\n    render: (text) => {\n      const experimentExecuteDes = {\n        running: getWord('component.notification.statusValue.running'),\n        hold: getWord('experiment-pending'),\n        canceled: getWord('pages.projectTable.statusLabel.cancelled'),\n        completed: getWord('component.notification.statusValue.success'),\n        success: getWord('app.general.message.success'),\n        incident: getWord('experiment-exception'),\n        failed: getWord('component.notification.statusValue.failed')\n      }\n      return (\n        text && (\n          <Tag color={statusColor[text] as string}>\n            {experimentExecuteDes[text]}\n          </Tag>\n        )\n      )\n    }\n  },\n  {\n    title: getWord('experiment-actual-start-time'),\n    dataIndex: 'start_time',\n    align: 'left',\n    width: 150,\n    render: (text: Dayjs) => (text ? formatYTSTime(text) : '')\n  },\n  {\n    title: getWord('experiment-actual-end-time'),\n    dataIndex: 'end_time',\n    width: 150,\n    align: 'left',\n    render: (text: Dayjs) => (text ? formatYTSTime(text) : '')\n  },\n  {\n    title: getWord('pages.experiment.label.operation'),\n    align: 'center',\n    fixed: 'right',\n    width: 120,\n    render: (_, { experiment_no }) => {\n      return <a onClick={() => toExperimentDetail(experiment_no)}>实验详情</a>\n    }\n  }\n]\n", "// extracted by mini-css-extract-plugin\nexport default {\"experimentExecute\":\"experimentExecute___qnrzL\"};", "import type { IFormData } from '@/components/SearchForm/index.d'\nimport { getWord } from '@/utils'\nexport const queryData: IFormData[] = [\n  {\n    label: getWord('pages.experiment.label.status'),\n    ctype: 'select',\n    key: 'status',\n    enums: [\n      {\n        label: getWord('component.notification.statusValue.running'),\n        value: 'running'\n      },\n      {\n        label: getWord('experiment-exception'),\n        value: 'incident'\n      },\n      {\n        label: getWord('experiment-pending'),\n        value: 'hold'\n      },\n      {\n        label: getWord('component.notification.statusValue.failed'),\n        value: 'failed'\n      },\n      {\n        label: getWord('component.notification.statusValue.success'),\n        value: 'completed'\n      },\n      {\n        label: getWord('app.general.message.success'),\n        value: 'success'\n      }\n    ],\n    placeholder: getWord('input-tip'),\n    XL: { col: 5, labelWidth: 7, wrapperWidth: 17 }\n  },\n  {\n    label: getWord('reaction-no'),\n    ctype: 'input',\n    key: 'rxn_no',\n    placeholder: getWord('select-tip'),\n    XL: { col: 7, labelWidth: 6, wrapperWidth: 17 }\n  },\n  {\n    label: getWord('pages.experiment.label.name'),\n    ctype: 'input',\n    key: 'experiment_name',\n    placeholder: getWord('input-tip'),\n    XL: { col: 5, labelWidth: 7, wrapperWidth: 17 }\n  }\n]\n", "import CustomTable from '@/components/CustomTable'\nimport SearchForm from '@/components/SearchForm'\nimport { EXPERIMENT_SEARCH, initFilter } from '@/constants'\nimport useFetchData from '@/hooks/useFetchData'\nimport type { ItemType } from '@/types/common'\nimport { PageContainer } from '@ant-design/pro-components'\nimport { Breadcrumb } from 'antd'\nimport cs from 'classnames'\nimport { isArray, isEmpty } from 'lodash'\nimport { useState } from 'react'\nimport { columns } from './column'\nimport styles from './index.less'\nimport { queryData } from './query-config'\nexport default function ExperimentExecute() {\n  const [queryParams, setQueryParams] = useState<any>(initFilter)\n  const { loading, listData, total } = useFetchData(\n    queryParams,\n    EXPERIMENT_SEARCH\n  )\n\n  const tableConfig = {\n    loading,\n    bordered: true,\n    dataSource: listData,\n    pagination: {\n      total,\n      current: queryParams.page_no,\n      pageSize: queryParams.page_size,\n      showTotal: () => `共${total}条记录`,\n      showQuickJumper: true,\n      showSizeChanger: true\n    }\n  }\n\n  return (\n    <PageContainer\n      breadcrumbRender={({ breadcrumb }) => {\n        let routes: ItemType[] = breadcrumb?.items as ItemType[]\n        return isArray(routes) && !isEmpty(routes) ? (\n          <Breadcrumb>\n            {routes.map((item: ItemType, index: number) => {\n              return (\n                <Breadcrumb.Item\n                  onClick={(event) => {\n                    if (index === 0) event.preventDefault()\n                  }}\n                  key={item?.linkPath}\n                  href={item?.linkPath as string}\n                >\n                  {item.breadcrumbName}\n                </Breadcrumb.Item>\n              )\n            })}\n          </Breadcrumb>\n        ) : (\n          ''\n        )\n      }}\n      className={cs(styles.experimentExecute)}\n    >\n      <SearchForm\n        formData={queryData}\n        onSubmit={(values: any) =>\n          setQueryParams({ ...queryParams, ...values, pageNo: 1 })\n        }\n        onReset={() => setQueryParams(initFilter)}\n      />\n      {/* FIXME 接口响应字段缺少暂停时间字段 */}\n      <CustomTable\n        {...tableConfig}\n        columns={columns}\n        rowKey=\"experiment_design_no\"\n        onChange={(current, pageSize) => {\n          setQueryParams({\n            ...queryParams,\n            page_no: current,\n            page_size: pageSize\n          })\n        }}\n      />\n    </PageContainer>\n  )\n}\n"], "names": ["columns", "title", "dataIndex", "align", "width", "getWord", "render", "text", "experimentExecuteDes", "running", "hold", "canceled", "completed", "success", "incident", "failed", "_jsx", "Tag", "color", "statusColor", "children", "formatYTSTime", "fixed", "_", "_ref", "experiment_no", "onClick", "toExperimentDetail", "queryData", "label", "ctype", "key", "enums", "value", "placeholder", "XL", "col", "labelWidth", "wrapperWidth", "ExperimentExecute", "_useState", "useState", "initFilter", "_useState2", "_slicedToArray", "queryParams", "setQueryParams", "_useFetchData", "useFetchData", "EXPERIMENT_SEARCH", "loading", "listData", "total", "tableConfig", "bordered", "dataSource", "pagination", "current", "page_no", "pageSize", "page_size", "showTotal", "concat", "showQuickJumper", "showSizeChanger", "_jsxs", "<PERSON><PERSON><PERSON><PERSON>", "breadcrumbRender", "breadcrumb", "routes", "items", "isArray", "isEmpty", "Breadcrumb", "map", "item", "index", "<PERSON><PERSON>", "event", "preventDefault", "href", "linkPath", "breadcrumbName", "className", "cs", "styles", "experimentExecute", "SearchForm", "formData", "onSubmit", "values", "_objectSpread", "pageNo", "onReset", "CustomTable", "<PERSON><PERSON><PERSON>", "onChange"], "sourceRoot": ""}