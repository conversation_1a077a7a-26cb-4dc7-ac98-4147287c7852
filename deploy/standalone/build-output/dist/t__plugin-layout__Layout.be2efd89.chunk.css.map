{"version": 3, "file": "t__plugin-layout__Layout.be2efd89.chunk.css", "mappings": "AAAA,OAAO,OAAO,IAAI,CAAC,SAAS,EAAE,OAE5B,CAAC,4BACC,MAAO,cACT,CACA,CAHC,2BAG4B,CAAE,EALjC,cAMmB,WACjB,CACF,CACA,CAAC,uBAAuB,CAAC,QACvB,aAAc,GAChB,CACA,CAHC,uBAGuB,CAAC,uBACvB,UAAW,KACb,CACA,CAAC,wBACC,QAAS,eACT,MAAO,MACP,OAAQ,KACR,YAAa,KACb,SAAU,MACZ,CACA,CAPC,wBAOwB,CAAC,yBACxB,QAAS,KACT,YAAa,OACb,OAAQ,KAzBV,QA0BW,EAAE,KACX,OAAQ,QACR,WAAY,IAAI,GAClB,CACA,CAfC,wBAewB,CARC,wBAQyB,CAAE,EACnD,MAAO,UACP,eAAgB,MAClB,CACA,CAnBC,wBAmBwB,CAZC,wBAYwB,OAGlD,CAtBC,wBAsBwB,CAfC,wBAewB,CAAC,OAFjD,WAAY,SACd,CAIA,CAzBC,wBAyBwB,CAAC,yBAxC1B,QAyCW,EAAE,IACb,CACA,CA5BC,wBA4BwB,CAHC,wBAGwB,OAChD,WAAY,WACd,CACA,CAAC,uBACC,YAAa,GACf,CACA,CAHC,sBAGsB,CAAC,kCACtB,YAAa,CACf", "sources": ["webpack://labwise-web/./src/.umi-production/plugin-layout/Layout.css"], "sourcesContent": ["@media screen and (max-width: 480px) {\n  /* 在小屏幕的时候可以有更好的体验 */\n  .umi-plugin-layout-container {\n    width: 100% !important;\n  }\n  .umi-plugin-layout-container > * {\n    border-radius: 0 !important;\n  }\n}\n.umi-plugin-layout-menu .anticon {\n  margin-right: 8px;\n}\n.umi-plugin-layout-menu .ant-dropdown-menu-item {\n  min-width: 160px;\n}\n.umi-plugin-layout-right {\n  display: flex !important;\n  float: right;\n  height: 100%;\n  margin-left: auto;\n  overflow: hidden;\n}\n.umi-plugin-layout-right .umi-plugin-layout-action {\n  display: flex;\n  align-items: center;\n  height: 100%;\n  padding: 0 12px;\n  cursor: pointer;\n  transition: all 0.3s;\n}\n.umi-plugin-layout-right .umi-plugin-layout-action > i {\n  color: rgba(255, 255, 255, 0.85);\n  vertical-align: middle;\n}\n.umi-plugin-layout-right .umi-plugin-layout-action:hover {\n  background: rgba(0, 0, 0, 0.025);\n}\n.umi-plugin-layout-right .umi-plugin-layout-action.opened {\n  background: rgba(0, 0, 0, 0.025);\n}\n.umi-plugin-layout-right .umi-plugin-layout-search {\n  padding: 0 12px;\n}\n.umi-plugin-layout-right .umi-plugin-layout-search:hover {\n  background: transparent;\n}\n.umi-plugin-layout-name {\n  margin-left: 8px;\n}\n.umi-plugin-layout-name.umi-plugin-layout-hide-avatar-img {\n  margin-left: 0;\n}\n"], "names": [], "sourceRoot": ""}