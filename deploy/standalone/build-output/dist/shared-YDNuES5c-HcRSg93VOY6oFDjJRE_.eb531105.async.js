!(function(){"use strict";var zt=Object.defineProperty,wt=Object.defineProperties;var jt=Object.getOwnPropertyDescriptors;var Ot=Object.getOwnPropertySymbols;var Ut=Object.prototype.hasOwnProperty,Nt=Object.prototype.propertyIsEnumerable;var Lt=(Z,D,e)=>D in Z?zt(Z,D,{enumerable:!0,configurable:!0,writable:!0,value:e}):Z[D]=e,ut=(Z,D)=>{for(var e in D||(D={}))Ut.call(D,e)&&Lt(Z,e,D[e]);if(Ot)for(var e of Ot(D))Nt.call(D,e)&&Lt(Z,e,D[e]);return Z},Rt=(Z,D)=>wt(Z,jt(D));var pt=(Z,D,e)=>new Promise((a,_)=>{var y=u=>{try{w(e.next(u))}catch(k){_(k)}},s=u=>{try{w(e.throw(u))}catch(k){_(k)}},w=u=>u.done?a(u.value):Promise.resolve(u.value).then(y,s);w((e=e.apply(Z,D)).next())});(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[663],{87646:function(Z,D,e){e.r(D),e.d(D,{blue:function(){return Ee},blueDark:function(){return J},cyan:function(){return ae},cyanDark:function(){return S},geekblue:function(){return ye},geekblueDark:function(){return $},generate:function(){return I},gold:function(){return G},goldDark:function(){return A},gray:function(){return Fe},green:function(){return X},greenDark:function(){return U},grey:function(){return oe},greyDark:function(){return W},lime:function(){return Q},limeDark:function(){return B},magenta:function(){return ve},magentaDark:function(){return N},orange:function(){return q},orangeDark:function(){return g},presetDarkPalettes:function(){return ee},presetPalettes:function(){return E},presetPrimaryColors:function(){return L},purple:function(){return xe},purpleDark:function(){return ie},red:function(){return M},redDark:function(){return r},volcano:function(){return H},volcanoDark:function(){return i},yellow:function(){return re},yellowDark:function(){return V}});var a=e(86500),_=e(1350),y=2,s=.16,w=.05,u=.05,k=.15,p=5,x=4,P=[{index:7,opacity:.15},{index:6,opacity:.25},{index:5,opacity:.3},{index:5,opacity:.45},{index:5,opacity:.65},{index:5,opacity:.85},{index:4,opacity:.9},{index:3,opacity:.95},{index:2,opacity:.97},{index:1,opacity:.98}];function C(b){var R=b.r,F=b.g,T=b.b,K=(0,a.py)(R,F,T);return{h:K.h*360,s:K.s,v:K.v}}function o(b){var R=b.r,F=b.g,T=b.b;return"#".concat((0,a.vq)(R,F,T,!1))}function n(b,R,F){var T=F/100,K={r:(R.r-b.r)*T+b.r,g:(R.g-b.g)*T+b.g,b:(R.b-b.b)*T+b.b};return K}function l(b,R,F){var T;return Math.round(b.h)>=60&&Math.round(b.h)<=240?T=F?Math.round(b.h)-y*R:Math.round(b.h)+y*R:T=F?Math.round(b.h)+y*R:Math.round(b.h)-y*R,T<0?T+=360:T>=360&&(T-=360),T}function d(b,R,F){if(b.h===0&&b.s===0)return b.s;var T;return F?T=b.s-s*R:R===x?T=b.s+s:T=b.s+w*R,T>1&&(T=1),F&&R===p&&T>.1&&(T=.1),T<.06&&(T=.06),Number(T.toFixed(2))}function c(b,R,F){var T;return F?T=b.v+u*R:T=b.v-k*R,T>1&&(T=1),Number(T.toFixed(2))}function I(b){for(var R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},F=[],T=(0,_.uA)(b),K=p;K>0;K-=1){var fe=C(T),ce=o((0,_.uA)({h:l(fe,K,!0),s:d(fe,K,!0),v:c(fe,K,!0)}));F.push(ce)}F.push(o(T));for(var he=1;he<=x;he+=1){var _e=C(T),Ie=o((0,_.uA)({h:l(_e,he),s:d(_e,he),v:c(_e,he)}));F.push(Ie)}return R.theme==="dark"?P.map(function(De){var me=De.index,Se=De.opacity,ge=o(n((0,_.uA)(R.backgroundColor||"#141414"),(0,_.uA)(F[me]),Se*100));return ge}):F}var L={red:"#F5222D",volcano:"#FA541C",orange:"#FA8C16",gold:"#FAAD14",yellow:"#FADB14",lime:"#A0D911",green:"#52C41A",cyan:"#13C2C2",blue:"#1677FF",geekblue:"#2F54EB",purple:"#722ED1",magenta:"#EB2F96",grey:"#666666"},M=["#fff1f0","#ffccc7","#ffa39e","#ff7875","#ff4d4f","#f5222d","#cf1322","#a8071a","#820014","#5c0011"];M.primary=M[5];var H=["#fff2e8","#ffd8bf","#ffbb96","#ff9c6e","#ff7a45","#fa541c","#d4380d","#ad2102","#871400","#610b00"];H.primary=H[5];var q=["#fff7e6","#ffe7ba","#ffd591","#ffc069","#ffa940","#fa8c16","#d46b08","#ad4e00","#873800","#612500"];q.primary=q[5];var G=["#fffbe6","#fff1b8","#ffe58f","#ffd666","#ffc53d","#faad14","#d48806","#ad6800","#874d00","#613400"];G.primary=G[5];var re=["#feffe6","#ffffb8","#fffb8f","#fff566","#ffec3d","#fadb14","#d4b106","#ad8b00","#876800","#614700"];re.primary=re[5];var Q=["#fcffe6","#f4ffb8","#eaff8f","#d3f261","#bae637","#a0d911","#7cb305","#5b8c00","#3f6600","#254000"];Q.primary=Q[5];var X=["#f6ffed","#d9f7be","#b7eb8f","#95de64","#73d13d","#52c41a","#389e0d","#237804","#135200","#092b00"];X.primary=X[5];var ae=["#e6fffb","#b5f5ec","#87e8de","#5cdbd3","#36cfc9","#13c2c2","#08979c","#006d75","#00474f","#002329"];ae.primary=ae[5];var Ee=["#e6f4ff","#bae0ff","#91caff","#69b1ff","#4096ff","#1677ff","#0958d9","#003eb3","#002c8c","#001d66"];Ee.primary=Ee[5];var ye=["#f0f5ff","#d6e4ff","#adc6ff","#85a5ff","#597ef7","#2f54eb","#1d39c4","#10239e","#061178","#030852"];ye.primary=ye[5];var xe=["#f9f0ff","#efdbff","#d3adf7","#b37feb","#9254de","#722ed1","#531dab","#391085","#22075e","#120338"];xe.primary=xe[5];var ve=["#fff0f6","#ffd6e7","#ffadd2","#ff85c0","#f759ab","#eb2f96","#c41d7f","#9e1068","#780650","#520339"];ve.primary=ve[5];var oe=["#a6a6a6","#999999","#8c8c8c","#808080","#737373","#666666","#404040","#1a1a1a","#000000","#000000"];oe.primary=oe[5];var Fe=oe,E={red:M,volcano:H,orange:q,gold:G,yellow:re,lime:Q,green:X,cyan:ae,blue:Ee,geekblue:ye,purple:xe,magenta:ve,grey:oe},r=["#2a1215","#431418","#58181c","#791a1f","#a61d24","#d32029","#e84749","#f37370","#f89f9a","#fac8c3"];r.primary=r[5];var i=["#2b1611","#441d12","#592716","#7c3118","#aa3e19","#d84a1b","#e87040","#f3956a","#f8b692","#fad4bc"];i.primary=i[5];var g=["#2b1d11","#442a11","#593815","#7c4a15","#aa6215","#d87a16","#e89a3c","#f3b765","#f8cf8d","#fae3b7"];g.primary=g[5];var A=["#2b2111","#443111","#594214","#7c5914","#aa7714","#d89614","#e8b339","#f3cc62","#f8df8b","#faedb5"];A.primary=A[5];var V=["#2b2611","#443b11","#595014","#7c6e14","#aa9514","#d8bd14","#e8d639","#f3ea62","#f8f48b","#fafab5"];V.primary=V[5];var B=["#1f2611","#2e3c10","#3e4f13","#536d13","#6f9412","#8bbb11","#a9d134","#c9e75d","#e4f88b","#f0fab5"];B.primary=B[5];var U=["#162312","#1d3712","#274916","#306317","#3c8618","#49aa19","#6abe39","#8fd460","#b2e58b","#d5f2bb"];U.primary=U[5];var S=["#112123","#113536","#144848","#146262","#138585","#13a8a8","#33bcb7","#58d1c9","#84e2d8","#b2f1e8"];S.primary=S[5];var J=["#111a2c","#112545","#15325b","#15417e","#1554ad","#1668dc","#3c89e8","#65a9f3","#8dc5f8","#b7dcfa"];J.primary=J[5];var $=["#131629","#161d40","#1c2755","#203175","#263ea0","#2b4acb","#5273e0","#7f9ef3","#a8c1f8","#d2e0fa"];$.primary=$[5];var ie=["#1a1325","#24163a","#301c4d","#3e2069","#51258f","#642ab5","#854eca","#ab7ae0","#cda8f0","#ebd7fa"];ie.primary=ie[5];var N=["#291321","#40162f","#551c3b","#75204f","#a02669","#cb2b83","#e0529c","#f37fb7","#f8a8cc","#fad2e3"];N.primary=N[5];var W=["#151515","#1f1f1f","#2d2d2d","#393939","#494949","#5a5a5a","#6a6a6a","#7b7b7b","#888888","#969696"];W.primary=W[5];var ee={red:r,volcano:i,orange:g,gold:A,yellow:V,lime:B,green:U,cyan:S,blue:J,geekblue:$,purple:ie,magenta:N,grey:W}},10915:function(Z,D,e){e.d(D,{_Y:function(){return xe},L_:function(){return oe},ZP:function(){return Fe},nu:function(){return Q},YB:function(){return ve}});var a=e(74902),_=e(97685),y=e(45987),s=e(1413),w=e(85982),u=e(28459),k=e(37029),p=e(67294),x=e(81758),P=e(51779),C=e(27484),o=e.n(C),n=e(98082),l=function(r,i){var g,A,V,B,U,S=(0,s.Z)({},r);return(0,s.Z)((0,s.Z)({bgLayout:"linear-gradient(".concat(i.colorBgContainer,", ").concat(i.colorBgLayout," 28%)"),colorTextAppListIcon:i.colorTextSecondary,appListIconHoverBgColor:S==null||(g=S.sider)===null||g===void 0?void 0:g.colorBgMenuItemSelected,colorBgAppListIconHover:(0,n.uK)(i.colorTextBase,.04),colorTextAppListIconHover:i.colorTextBase},S),{},{header:(0,s.Z)({colorBgHeader:(0,n.uK)(i.colorBgElevated,.6),colorBgScrollHeader:(0,n.uK)(i.colorBgElevated,.8),colorHeaderTitle:i.colorText,colorBgMenuItemHover:(0,n.uK)(i.colorTextBase,.03),colorBgMenuItemSelected:"transparent",colorBgMenuElevated:(S==null||(A=S.header)===null||A===void 0?void 0:A.colorBgHeader)!=="rgba(255, 255, 255, 0.6)"?(V=S.header)===null||V===void 0?void 0:V.colorBgHeader:i.colorBgElevated,colorTextMenuSelected:(0,n.uK)(i.colorTextBase,.95),colorBgRightActionsItemHover:(0,n.uK)(i.colorTextBase,.03),colorTextRightActionsItem:i.colorTextTertiary,heightLayoutHeader:56,colorTextMenu:i.colorTextSecondary,colorTextMenuSecondary:i.colorTextTertiary,colorTextMenuTitle:i.colorText,colorTextMenuActive:i.colorText},S.header),sider:(0,s.Z)({paddingInlineLayoutMenu:8,paddingBlockLayoutMenu:0,colorBgCollapsedButton:i.colorBgElevated,colorTextCollapsedButtonHover:i.colorTextSecondary,colorTextCollapsedButton:(0,n.uK)(i.colorTextBase,.25),colorMenuBackground:"transparent",colorMenuItemDivider:(0,n.uK)(i.colorTextBase,.06),colorBgMenuItemHover:(0,n.uK)(i.colorTextBase,.03),colorBgMenuItemSelected:(0,n.uK)(i.colorTextBase,.04),colorTextMenuItemHover:i.colorText,colorTextMenuSelected:(0,n.uK)(i.colorTextBase,.95),colorTextMenuActive:i.colorText,colorTextMenu:i.colorTextSecondary,colorTextMenuSecondary:i.colorTextTertiary,colorTextMenuTitle:i.colorText,colorTextSubMenuSelected:(0,n.uK)(i.colorTextBase,.95)},S.sider),pageContainer:(0,s.Z)({colorBgPageContainer:"transparent",paddingInlinePageContainerContent:((B=S.pageContainer)===null||B===void 0?void 0:B.marginInlinePageContainerContent)||40,paddingBlockPageContainerContent:((U=S.pageContainer)===null||U===void 0?void 0:U.marginBlockPageContainerContent)||32,colorBgPageContainerFixed:i.colorBgElevated},S.pageContainer)})},d=e(67804),c=e(71002),I=function(){for(var r={},i=arguments.length,g=new Array(i),A=0;A<i;A++)g[A]=arguments[A];for(var V=g.length,B,U=0;U<V;U+=1)for(B in g[U])g[U].hasOwnProperty(B)&&((0,c.Z)(r[B])==="object"&&(0,c.Z)(g[U][B])==="object"&&r[B]!==void 0&&r[B]!==null&&!Array.isArray(r[B])&&!Array.isArray(g[U][B])?r[B]=(0,s.Z)((0,s.Z)({},r[B]),g[U][B]):r[B]=g[U][B]);return r},L=e(33852),M=e(85893),H=e(34155),q=["locale","getPrefixCls"],G=["locale","theme"],re=function(r){var i={};if(Object.keys(r||{}).forEach(function(g){r[g]!==void 0&&(i[g]=r[g])}),!(Object.keys(i).length<1))return i},Q=function(){var r,i;return!(typeof H!="undefined"&&(((r="production")===null||r===void 0?void 0:r.toUpperCase())==="TEST"||((i="production")===null||i===void 0?void 0:i.toUpperCase())==="DEV"))},X=p.createContext({intl:(0,s.Z)((0,s.Z)({},P.Hi),{},{locale:"default"}),valueTypeMap:{},theme:d.emptyTheme,hashed:!0,dark:!1,token:d.defaultToken}),ae=X.Consumer,Ee=function(){var r=(0,x.kY)(),i=r.cache;return(0,p.useEffect)(function(){return function(){i.clear()}},[]),null},ye=function(r){var i,g=r.children,A=r.dark,V=r.valueTypeMap,B=r.autoClearCache,U=B===void 0?!1:B,S=r.token,J=r.prefixCls,$=r.intl,ie=(0,p.useContext)(u.ZP.ConfigContext),N=ie.locale,W=ie.getPrefixCls,ee=(0,y.Z)(ie,q),b=(i=n.Ow.useToken)===null||i===void 0?void 0:i.call(n.Ow),R=(0,p.useContext)(X),F=J?".".concat(J):".".concat(W(),"-pro"),T="."+W(),K="".concat(F),fe=(0,p.useMemo)(function(){return l(S||{},b.token||d.defaultToken)},[S,b.token]),ce=(0,p.useMemo)(function(){var Ae,be=N==null?void 0:N.locale,We=(0,P.Vy)(be),Ge=$!=null?$:be&&((Ae=R.intl)===null||Ae===void 0?void 0:Ae.locale)==="default"?P.Go[We]:R.intl||P.Go[We];return(0,s.Z)((0,s.Z)({},R),{},{dark:A!=null?A:R.dark,token:I(R.token,b.token,{proComponentsCls:F,antCls:T,themeId:b.theme.id,layout:fe}),intl:Ge||P.Hi})},[N==null?void 0:N.locale,R,A,b.token,b.theme.id,F,T,fe,$]),he=(0,s.Z)((0,s.Z)({},ce.token||{}),{},{proComponentsCls:F}),_e=(0,w.useCacheToken)(b.theme,[b.token,he!=null?he:{}],{salt:K,override:he}),Ie=(0,_.Z)(_e,2),De=Ie[0],me=Ie[1],Se=(0,p.useMemo)(function(){return!(r.hashed===!1||R.hashed===!1)},[R.hashed,r.hashed]),ge=(0,p.useMemo)(function(){return r.hashed===!1||R.hashed===!1||Q()===!1?"":b.hashId?b.hashId:me},[me,R.hashed,r.hashed]);(0,p.useEffect)(function(){o().locale((N==null?void 0:N.locale)||"zh-cn")},[N==null?void 0:N.locale]);var se=(0,p.useMemo)(function(){return(0,s.Z)((0,s.Z)({},ee.theme),{},{hashId:ge,hashed:Se&&Q()})},[ee.theme,ge,Se,Q()]),Ne=(0,p.useMemo)(function(){return(0,s.Z)((0,s.Z)({},ce),{},{valueTypeMap:V||(ce==null?void 0:ce.valueTypeMap),token:De,theme:b.theme,hashed:Se,hashId:ge})},[ce,V,De,b.theme,Se,ge]),we=(0,p.useMemo)(function(){return(0,M.jsx)(u.ZP,(0,s.Z)((0,s.Z)({},ee),{},{theme:se,children:(0,M.jsx)(X.Provider,{value:Ne,children:(0,M.jsxs)(M.Fragment,{children:[U&&(0,M.jsx)(Ee,{}),g]})})}))},[ee,se,Ne,U,g]);return U?(0,M.jsx)(x.J$,{value:{provider:function(){return new Map}},children:we}):we},xe=function(r){var i=r.needDeps,g=r.dark,A=r.token,V=(0,p.useContext)(X),B=(0,p.useContext)(u.ZP.ConfigContext),U=B.locale,S=B.theme,J=(0,y.Z)(B,G),$=i&&V.hashId!==void 0&&Object.keys(r).sort().join("-")==="children-needDeps";if($)return(0,M.jsx)(M.Fragment,{children:r.children});var ie=function(){var ee=g!=null?g:V.dark;return ee&&!Array.isArray(S==null?void 0:S.algorithm)?[n.Ow.darkAlgorithm,S==null?void 0:S.algorithm].filter(Boolean):ee&&Array.isArray(S==null?void 0:S.algorithm)?[n.Ow.darkAlgorithm].concat((0,a.Z)((S==null?void 0:S.algorithm)||[])).filter(Boolean):S==null?void 0:S.algorithm},N=(0,s.Z)((0,s.Z)({},J),{},{locale:U||k.Z,theme:re((0,s.Z)((0,s.Z)({},S),{},{algorithm:ie()}))});return(0,M.jsx)(u.ZP,(0,s.Z)((0,s.Z)({},N),{},{children:(0,M.jsx)(ye,(0,s.Z)((0,s.Z)({},r),{},{token:A}))}))};function ve(){var E=(0,p.useContext)(u.ZP.ConfigContext),r=E.locale,i=(0,p.useContext)(X),g=i.intl;return g&&g.locale!=="default"?g||P.Hi:r!=null&&r.locale&&P.Go[(0,P.Vy)(r.locale)]||P.Hi}X.displayName="ProProvider";var oe=X,Fe=X},51779:function(Z,D,e){e.d(D,{Vy:function(){return Je},Go:function(){return We},Hi:function(){return A}});var a=e(56790),_={moneySymbol:"$",form:{lightFilter:{more:"\u0627\u0644\u0645\u0632\u064A\u062F",clear:"\u0646\u0638\u0641",confirm:"\u062A\u0623\u0643\u064A\u062F",itemUnit:"\u0639\u0646\u0627\u0635\u0631"}},tableForm:{search:"\u0627\u0628\u062D\u062B",reset:"\u0625\u0639\u0627\u062F\u0629 \u062A\u0639\u064A\u064A\u0646",submit:"\u0627\u0631\u0633\u0627\u0644",collapsed:"\u0645\u064F\u0642\u0644\u0635",expand:"\u0645\u064F\u0648\u0633\u0639",inputPlaceholder:"\u0627\u0644\u0631\u062C\u0627\u0621 \u0627\u0644\u0625\u062F\u062E\u0627\u0644",selectPlaceholder:"\u0627\u0644\u0631\u062C\u0627\u0621 \u0627\u0644\u0625\u062E\u062A\u064A\u0627\u0631"},alert:{clear:"\u0646\u0638\u0641",selected:"\u0645\u062D\u062F\u062F",item:"\u0639\u0646\u0635\u0631"},pagination:{total:{range:" ",total:"\u0645\u0646",item:"\u0639\u0646\u0627\u0635\u0631"}},tableToolBar:{leftPin:"\u062B\u0628\u062A \u0639\u0644\u0649 \u0627\u0644\u064A\u0633\u0627\u0631",rightPin:"\u062B\u0628\u062A \u0639\u0644\u0649 \u0627\u0644\u064A\u0645\u064A\u0646",noPin:"\u0627\u0644\u063A\u0627\u0621 \u0627\u0644\u062A\u062B\u0628\u064A\u062A",leftFixedTitle:"\u0644\u0635\u0642 \u0639\u0644\u0649 \u0627\u0644\u064A\u0633\u0627\u0631",rightFixedTitle:"\u0644\u0635\u0642 \u0639\u0644\u0649 \u0627\u0644\u064A\u0645\u064A\u0646",noFixedTitle:"\u0625\u0644\u063A\u0627\u0621 \u0627\u0644\u0625\u0644\u0635\u0627\u0642",reset:"\u0625\u0639\u0627\u062F\u0629 \u062A\u0639\u064A\u064A\u0646",columnDisplay:"\u0627\u0644\u0623\u0639\u0645\u062F\u0629 \u0627\u0644\u0645\u0639\u0631\u0648\u0636\u0629",columnSetting:"\u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A",fullScreen:"\u0648\u0636\u0639 \u0643\u0627\u0645\u0644 \u0627\u0644\u0634\u0627\u0634\u0629",exitFullScreen:"\u0627\u0644\u062E\u0631\u0648\u062C \u0645\u0646 \u0648\u0636\u0639 \u0643\u0627\u0645\u0644 \u0627\u0644\u0634\u0627\u0634\u0629",reload:"\u062A\u062D\u062F\u064A\u062B",density:"\u0627\u0644\u0643\u062B\u0627\u0641\u0629",densityDefault:"\u0627\u0641\u062A\u0631\u0627\u0636\u064A",densityLarger:"\u0623\u0643\u0628\u0631",densityMiddle:"\u0648\u0633\u0637",densitySmall:"\u0645\u062F\u0645\u062C"},stepsForm:{next:"\u0627\u0644\u062A\u0627\u0644\u064A",prev:"\u0627\u0644\u0633\u0627\u0628\u0642",submit:"\u0623\u0646\u0647\u0649"},loginForm:{submitText:"\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062F\u062E\u0648\u0644"},editableTable:{action:{save:"\u0623\u0646\u0642\u0630",cancel:"\u0625\u0644\u063A\u0627\u0621 \u0627\u0644\u0623\u0645\u0631",delete:"\u062D\u0630\u0641",add:"\u0625\u0636\u0627\u0641\u0629 \u0635\u0641 \u0645\u0646 \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A"}},switch:{open:"\u0645\u0641\u062A\u0648\u062D",close:"\u063A\u0644\u0642"}},y={moneySymbol:"\u20AC",form:{lightFilter:{more:"M\xE9s",clear:"Netejar",confirm:"Confirmar",itemUnit:"Elements"}},tableForm:{search:"Cercar",reset:"Netejar",submit:"Enviar",collapsed:"Expandir",expand:"Col\xB7lapsar",inputPlaceholder:"Introdu\xEFu valor",selectPlaceholder:"Seleccioneu valor"},alert:{clear:"Netejar",selected:"Seleccionat",item:"Article"},pagination:{total:{range:" ",total:"de",item:"articles"}},tableToolBar:{leftPin:"Pin a l'esquerra",rightPin:"Pin a la dreta",noPin:"Sense Pin",leftFixedTitle:"Fixat a l'esquerra",rightFixedTitle:"Fixat a la dreta",noFixedTitle:"Sense fixar",reset:"Reiniciar",columnDisplay:"Mostrar Columna",columnSetting:"Configuraci\xF3",fullScreen:"Pantalla Completa",exitFullScreen:"Sortir Pantalla Completa",reload:"Refrescar",density:"Densitat",densityDefault:"Per Defecte",densityLarger:"Llarg",densityMiddle:"Mitj\xE0",densitySmall:"Compacte"},stepsForm:{next:"Seg\xFCent",prev:"Anterior",submit:"Finalizar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Guardar",cancel:"Cancel\xB7lar",delete:"Eliminar",add:"afegir una fila de dades"}},switch:{open:"obert",close:"tancat"}},s={moneySymbol:"K\u010D",deleteThisLine:"Smazat tento \u0159\xE1dek",copyThisLine:"Kop\xEDrovat tento \u0159\xE1dek",form:{lightFilter:{more:"V\xEDc",clear:"Vymazat",confirm:"Potvrdit",itemUnit:"Polo\u017Eky"}},tableForm:{search:"Dotaz",reset:"Resetovat",submit:"Odeslat",collapsed:"Zv\u011Bt\u0161it",expand:"Zmen\u0161it",inputPlaceholder:"Zadejte pros\xEDm",selectPlaceholder:"Vyberte pros\xEDm"},alert:{clear:"Vymazat",selected:"Vybran\xFD",item:"Polo\u017Eka"},pagination:{total:{range:" ",total:"z",item:"polo\u017Eek"}},tableToolBar:{leftPin:"P\u0159ipnout doleva",rightPin:"P\u0159ipnout doprava",noPin:"Odepnuto",leftFixedTitle:"Fixov\xE1no nalevo",rightFixedTitle:"Fixov\xE1no napravo",noFixedTitle:"Neopraveno",reset:"Resetovat",columnDisplay:"Zobrazen\xED sloupc\u016F",columnSetting:"Nastaven\xED",fullScreen:"Cel\xE1 obrazovka",exitFullScreen:"Ukon\u010Dete celou obrazovku",reload:"Obnovit",density:"Hustota",densityDefault:"V\xFDchoz\xED",densityLarger:"V\u011Bt\u0161\xED",densityMiddle:"St\u0159edn\xED",densitySmall:"Kompaktn\xED"},stepsForm:{next:"Dal\u0161\xED",prev:"P\u0159edchoz\xED",submit:"Dokon\u010Dit"},loginForm:{submitText:"P\u0159ihl\xE1sit se"},editableTable:{onlyOneLineEditor:"Upravit lze pouze jeden \u0159\xE1dek",action:{save:"Ulo\u017Eit",cancel:"Zru\u0161it",delete:"Vymazat",add:"p\u0159idat \u0159\xE1dek dat"}},switch:{open:"otev\u0159\xEDt",close:"zav\u0159\xEDt"}},w={moneySymbol:"\u20AC",form:{lightFilter:{more:"Mehr",clear:"Zur\xFCcksetzen",confirm:"Best\xE4tigen",itemUnit:"Eintr\xE4ge"}},tableForm:{search:"Suchen",reset:"Zur\xFCcksetzen",submit:"Absenden",collapsed:"Zeige mehr",expand:"Zeige weniger",inputPlaceholder:"Bitte eingeben",selectPlaceholder:"Bitte ausw\xE4hlen"},alert:{clear:"Zur\xFCcksetzen",selected:"Ausgew\xE4hlt",item:"Eintrag"},pagination:{total:{range:" ",total:"von",item:"Eintr\xE4gen"}},tableToolBar:{leftPin:"Links anheften",rightPin:"Rechts anheften",noPin:"Nicht angeheftet",leftFixedTitle:"Links fixiert",rightFixedTitle:"Rechts fixiert",noFixedTitle:"Nicht fixiert",reset:"Zur\xFCcksetzen",columnDisplay:"Angezeigte Reihen",columnSetting:"Einstellungen",fullScreen:"Vollbild",exitFullScreen:"Vollbild verlassen",reload:"Aktualisieren",density:"Abstand",densityDefault:"Standard",densityLarger:"Gr\xF6\xDFer",densityMiddle:"Mittel",densitySmall:"Kompakt"},stepsForm:{next:"Weiter",prev:"Zur\xFCck",submit:"Abschlie\xDFen"},loginForm:{submitText:"Anmelden"},editableTable:{action:{save:"Retten",cancel:"Abbrechen",delete:"L\xF6schen",add:"Hinzuf\xFCgen einer Datenzeile"}},switch:{open:"offen",close:"schlie\xDFen"}},u={moneySymbol:"\xA3",form:{lightFilter:{more:"More",clear:"Clear",confirm:"Confirm",itemUnit:"Items"}},tableForm:{search:"Query",reset:"Reset",submit:"Submit",collapsed:"Expand",expand:"Collapse",inputPlaceholder:"Please enter",selectPlaceholder:"Please select"},alert:{clear:"Clear",selected:"Selected",item:"Item"},pagination:{total:{range:" ",total:"of",item:"items"}},tableToolBar:{leftPin:"Pin to left",rightPin:"Pin to right",noPin:"Unpinned",leftFixedTitle:"Fixed to the left",rightFixedTitle:"Fixed to the right",noFixedTitle:"Not Fixed",reset:"Reset",columnDisplay:"Column Display",columnSetting:"Table Settings",fullScreen:"Full Screen",exitFullScreen:"Exit Full Screen",reload:"Refresh",density:"Density",densityDefault:"Default",densityLarger:"Larger",densityMiddle:"Middle",densitySmall:"Compact"},stepsForm:{next:"Next",prev:"Previous",submit:"Finish"},loginForm:{submitText:"Login"},editableTable:{onlyOneLineEditor:"Only one line can be edited",onlyAddOneLine:"Only one line can be added",action:{save:"Save",cancel:"Cancel",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"close"}},k={moneySymbol:"$",deleteThisLine:"Delete this line",copyThisLine:"Copy this line",form:{lightFilter:{more:"More",clear:"Clear",confirm:"Confirm",itemUnit:"Items"}},tableForm:{search:"Query",reset:"Reset",submit:"Submit",collapsed:"Expand",expand:"Collapse",inputPlaceholder:"Please enter",selectPlaceholder:"Please select"},alert:{clear:"Clear",selected:"Selected",item:"Item"},pagination:{total:{range:" ",total:"of",item:"items"}},tableToolBar:{leftPin:"Pin to left",rightPin:"Pin to right",noPin:"Unpinned",leftFixedTitle:"Fixed to the left",rightFixedTitle:"Fixed to the right",noFixedTitle:"Not Fixed",reset:"Reset",columnDisplay:"Column Display",columnSetting:"Table Settings",fullScreen:"Full Screen",exitFullScreen:"Exit Full Screen",reload:"Refresh",density:"Density",densityDefault:"Default",densityLarger:"Larger",densityMiddle:"Middle",densitySmall:"Compact"},stepsForm:{next:"Next",prev:"Previous",submit:"Finish"},loginForm:{submitText:"Login"},editableTable:{onlyOneLineEditor:"Only one line can be edited",onlyAddOneLine:"Only one line can be added",action:{save:"Save",cancel:"Cancel",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"close"}},p={moneySymbol:"\u20AC",form:{lightFilter:{more:"M\xE1s",clear:"Limpiar",confirm:"Confirmar",itemUnit:"art\xEDculos"}},tableForm:{search:"Buscar",reset:"Limpiar",submit:"Submit",collapsed:"Expandir",expand:"Colapsar",inputPlaceholder:"Ingrese valor",selectPlaceholder:"Seleccione valor"},alert:{clear:"Limpiar",selected:"Seleccionado",item:"Articulo"},pagination:{total:{range:" ",total:"de",item:"art\xEDculos"}},tableToolBar:{leftPin:"Pin a la izquierda",rightPin:"Pin a la derecha",noPin:"Sin Pin",leftFixedTitle:"Fijado a la izquierda",rightFixedTitle:"Fijado a la derecha",noFixedTitle:"Sin Fijar",reset:"Reiniciar",columnDisplay:"Mostrar Columna",columnSetting:"Configuraci\xF3n",fullScreen:"Pantalla Completa",exitFullScreen:"Salir Pantalla Completa",reload:"Refrescar",density:"Densidad",densityDefault:"Por Defecto",densityLarger:"Largo",densityMiddle:"Medio",densitySmall:"Compacto"},stepsForm:{next:"Siguiente",prev:"Anterior",submit:"Finalizar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Guardar",cancel:"Descartar",delete:"Borrar",add:"a\xF1adir una fila de datos"}},switch:{open:"abrir",close:"cerrar"}},x={moneySymbol:"\u062A\u0648\u0645\u0627\u0646",form:{lightFilter:{more:"\u0628\u06CC\u0634\u062A\u0631",clear:"\u067E\u0627\u06A9 \u06A9\u0631\u062F\u0646",confirm:"\u062A\u0627\u06CC\u06CC\u062F",itemUnit:"\u0645\u0648\u0631\u062F"}},tableForm:{search:"\u062C\u0633\u062A\u062C\u0648",reset:"\u0628\u0627\u0632\u0646\u0634\u0627\u0646\u06CC",submit:"\u062A\u0627\u06CC\u06CC\u062F",collapsed:"\u0646\u0645\u0627\u06CC\u0634 \u0628\u06CC\u0634\u062A\u0631",expand:"\u0646\u0645\u0627\u06CC\u0634 \u06A9\u0645\u062A\u0631",inputPlaceholder:"\u067E\u06CC\u062F\u0627 \u06A9\u0646\u06CC\u062F",selectPlaceholder:"\u0627\u0646\u062A\u062E\u0627\u0628 \u06A9\u0646\u06CC\u062F"},alert:{clear:"\u067E\u0627\u06A9 \u0633\u0627\u0632\u06CC",selected:"\u0627\u0646\u062A\u062E\u0627\u0628",item:"\u0645\u0648\u0631\u062F"},pagination:{total:{range:" ",total:"\u0627\u0632",item:"\u0645\u0648\u0631\u062F"}},tableToolBar:{leftPin:"\u0633\u0646\u062C\u0627\u0642 \u0628\u0647 \u0686\u067E",rightPin:"\u0633\u0646\u062C\u0627\u0642 \u0628\u0647 \u0631\u0627\u0633\u062A",noPin:"\u0633\u0646\u062C\u0627\u0642 \u0646\u0634\u062F\u0647",leftFixedTitle:"\u062B\u0627\u0628\u062A \u0634\u062F\u0647 \u062F\u0631 \u0686\u067E",rightFixedTitle:"\u062B\u0627\u0628\u062A \u0634\u062F\u0647 \u062F\u0631 \u0631\u0627\u0633\u062A",noFixedTitle:"\u0634\u0646\u0627\u0648\u0631",reset:"\u0628\u0627\u0632\u0646\u0634\u0627\u0646\u06CC",columnDisplay:"\u0646\u0645\u0627\u06CC\u0634 \u0647\u0645\u0647",columnSetting:"\u062A\u0646\u0638\u06CC\u0645\u0627\u062A",fullScreen:"\u062A\u0645\u0627\u0645 \u0635\u0641\u062D\u0647",exitFullScreen:"\u062E\u0631\u0648\u062C \u0627\u0632 \u062D\u0627\u0644\u062A \u062A\u0645\u0627\u0645 \u0635\u0641\u062D\u0647",reload:"\u062A\u0627\u0632\u0647 \u0633\u0627\u0632\u06CC",density:"\u062A\u0631\u0627\u06A9\u0645",densityDefault:"\u067E\u06CC\u0634 \u0641\u0631\u0636",densityLarger:"\u0628\u0632\u0631\u06AF",densityMiddle:"\u0645\u062A\u0648\u0633\u0637",densitySmall:"\u06A9\u0648\u0686\u06A9"},stepsForm:{next:"\u0628\u0639\u062F\u06CC",prev:"\u0642\u0628\u0644\u06CC",submit:"\u0627\u062A\u0645\u0627\u0645"},loginForm:{submitText:"\u0648\u0631\u0648\u062F"},editableTable:{action:{save:"\u0630\u062E\u06CC\u0631\u0647",cancel:"\u0644\u063A\u0648",delete:"\u062D\u0630\u0641",add:"\u06CC\u06A9 \u0631\u062F\u06CC\u0641 \u062F\u0627\u062F\u0647 \u0627\u0636\u0627\u0641\u0647 \u06A9\u0646\u06CC\u062F"}},switch:{open:"\u0628\u0627\u0632",close:"\u0646\u0632\u062F\u06CC\u06A9"}},P={moneySymbol:"\u20AC",form:{lightFilter:{more:"Plus",clear:"Effacer",confirm:"Confirmer",itemUnit:"Items"}},tableForm:{search:"Rechercher",reset:"R\xE9initialiser",submit:"Envoyer",collapsed:"Agrandir",expand:"R\xE9duire",inputPlaceholder:"Entrer une valeur",selectPlaceholder:"S\xE9lectionner une valeur"},alert:{clear:"R\xE9initialiser",selected:"S\xE9lectionn\xE9",item:"Item"},pagination:{total:{range:" ",total:"sur",item:"\xE9l\xE9ments"}},tableToolBar:{leftPin:"\xC9pingler \xE0 gauche",rightPin:"\xC9pingler \xE0 gauche",noPin:"Sans \xE9pingle",leftFixedTitle:"Fixer \xE0 gauche",rightFixedTitle:"Fixer \xE0 droite",noFixedTitle:"Non fix\xE9",reset:"R\xE9initialiser",columnDisplay:"Affichage colonne",columnSetting:"R\xE9glages",fullScreen:"Plein \xE9cran",exitFullScreen:"Quitter Plein \xE9cran",reload:"Rafraichir",density:"Densit\xE9",densityDefault:"Par d\xE9faut",densityLarger:"Larger",densityMiddle:"Moyenne",densitySmall:"Compacte"},stepsForm:{next:"Suivante",prev:"Pr\xE9c\xE9dente",submit:"Finaliser"},loginForm:{submitText:"Se connecter"},editableTable:{action:{save:"Sauvegarder",cancel:"Annuler",delete:"Supprimer",add:"ajouter une ligne de donn\xE9es"}},switch:{open:"ouvert",close:"pr\xE8s"}},C={moneySymbol:"\u20AA",deleteThisLine:"\u05DE\u05D7\u05E7 \u05E9\u05D5\u05E8\u05D4 \u05D6\u05D5",copyThisLine:"\u05D4\u05E2\u05EA\u05E7 \u05E9\u05D5\u05E8\u05D4 \u05D6\u05D5",form:{lightFilter:{more:"\u05D9\u05D5\u05EA\u05E8",clear:"\u05E0\u05E7\u05D4",confirm:"\u05D0\u05D9\u05E9\u05D5\u05E8",itemUnit:"\u05E4\u05E8\u05D9\u05D8\u05D9\u05DD"}},tableForm:{search:"\u05D7\u05D9\u05E4\u05D5\u05E9",reset:"\u05D0\u05D9\u05E4\u05D5\u05E1",submit:"\u05E9\u05DC\u05D7",collapsed:"\u05D4\u05E8\u05D7\u05D1",expand:"\u05DB\u05D5\u05D5\u05E5",inputPlaceholder:"\u05D0\u05E0\u05D0 \u05D4\u05DB\u05E0\u05E1",selectPlaceholder:"\u05D0\u05E0\u05D0 \u05D1\u05D7\u05E8"},alert:{clear:"\u05E0\u05E7\u05D4",selected:"\u05E0\u05D1\u05D7\u05E8",item:"\u05E4\u05E8\u05D9\u05D8"},pagination:{total:{range:" ",total:"\u05DE\u05EA\u05D5\u05DA",item:"\u05E4\u05E8\u05D9\u05D8\u05D9\u05DD"}},tableToolBar:{leftPin:"\u05D4\u05E6\u05DE\u05D3 \u05DC\u05E9\u05DE\u05D0\u05DC",rightPin:"\u05D4\u05E6\u05DE\u05D3 \u05DC\u05D9\u05DE\u05D9\u05DF",noPin:"\u05DC\u05D0 \u05DE\u05E6\u05D5\u05E8\u05E3",leftFixedTitle:"\u05DE\u05D5\u05E6\u05DE\u05D3 \u05DC\u05E9\u05DE\u05D0\u05DC",rightFixedTitle:"\u05DE\u05D5\u05E6\u05DE\u05D3 \u05DC\u05D9\u05DE\u05D9\u05DF",noFixedTitle:"\u05DC\u05D0 \u05DE\u05D5\u05E6\u05DE\u05D3",reset:"\u05D0\u05D9\u05E4\u05D5\u05E1",columnDisplay:"\u05EA\u05E6\u05D5\u05D2\u05EA \u05E2\u05DE\u05D5\u05D3\u05D5\u05EA",columnSetting:"\u05D4\u05D2\u05D3\u05E8\u05D5\u05EA",fullScreen:"\u05DE\u05E1\u05DA \u05DE\u05DC\u05D0",exitFullScreen:"\u05E6\u05D0 \u05DE\u05DE\u05E1\u05DA \u05DE\u05DC\u05D0",reload:"\u05E8\u05E2\u05E0\u05DF",density:"\u05E8\u05D6\u05D5\u05DC\u05D5\u05E6\u05D9\u05D4",densityDefault:"\u05D1\u05E8\u05D9\u05E8\u05EA \u05DE\u05D7\u05D3\u05DC",densityLarger:"\u05D2\u05D3\u05D5\u05DC",densityMiddle:"\u05D1\u05D9\u05E0\u05D5\u05E0\u05D9",densitySmall:"\u05E7\u05D8\u05DF"},stepsForm:{next:"\u05D4\u05D1\u05D0",prev:"\u05E7\u05D5\u05D3\u05DD",submit:"\u05E1\u05D9\u05D5\u05DD"},loginForm:{submitText:"\u05DB\u05E0\u05D9\u05E1\u05D4"},editableTable:{onlyOneLineEditor:"\u05E0\u05D9\u05EA\u05DF \u05DC\u05E2\u05E8\u05D5\u05DA \u05E8\u05E7 \u05E9\u05D5\u05E8\u05D4 \u05D0\u05D7\u05EA",action:{save:"\u05E9\u05DE\u05D5\u05E8",cancel:"\u05D1\u05D9\u05D8\u05D5\u05DC",delete:"\u05DE\u05D7\u05D9\u05E7\u05D4",add:"\u05D4\u05D5\u05E1\u05E3 \u05E9\u05D5\u05E8\u05EA \u05E0\u05EA\u05D5\u05E0\u05D9\u05DD"}},switch:{open:"\u05E4\u05EA\u05D7",close:"\u05E1\u05D2\u05D5\u05E8"}},o={moneySymbol:"kn",form:{lightFilter:{more:"Vi\u0161e",clear:"O\u010Disti",confirm:"Potvrdi",itemUnit:"Stavke"}},tableForm:{search:"Pretra\u017Ei",reset:"Poni\u0161ti",submit:"Potvrdi",collapsed:"Ra\u0161iri",expand:"Skupi",inputPlaceholder:"Unesite",selectPlaceholder:"Odaberite"},alert:{clear:"O\u010Disti",selected:"Odaberi",item:"stavke"},pagination:{total:{range:" ",total:"od",item:"stavke"}},tableToolBar:{leftPin:"Prika\u010Di lijevo",rightPin:"Prika\u010Di desno",noPin:"Bez prika\u010Denja",leftFixedTitle:"Fiksiraj lijevo",rightFixedTitle:"Fiksiraj desno",noFixedTitle:"Bez fiksiranja",reset:"Resetiraj",columnDisplay:"Prikaz stupaca",columnSetting:"Postavke",fullScreen:"Puni zaslon",exitFullScreen:"Iza\u0111i iz punog zaslona",reload:"Ponovno u\u010Ditaj",density:"Veli\u010Dina",densityDefault:"Zadano",densityLarger:"Veliko",densityMiddle:"Srednje",densitySmall:"Malo"},stepsForm:{next:"Sljede\u0107i",prev:"Prethodni",submit:"Kraj"},loginForm:{submitText:"Prijava"},editableTable:{action:{save:"Spremi",cancel:"Odustani",delete:"Obri\u0161i",add:"dodajte red podataka"}},switch:{open:"otvori",close:"zatvori"}},n={moneySymbol:"RP",form:{lightFilter:{more:"Lebih",clear:"Hapus",confirm:"Konfirmasi",itemUnit:"Unit"}},tableForm:{search:"Cari",reset:"Atur ulang",submit:"Kirim",collapsed:"Lebih sedikit",expand:"Lebih banyak",inputPlaceholder:"Masukkan pencarian",selectPlaceholder:"Pilih"},alert:{clear:"Hapus",selected:"Dipilih",item:"Butir"},pagination:{total:{range:" ",total:"Dari",item:"Butir"}},tableToolBar:{leftPin:"Pin kiri",rightPin:"Pin kanan",noPin:"Tidak ada pin",leftFixedTitle:"Rata kiri",rightFixedTitle:"Rata kanan",noFixedTitle:"Tidak tetap",reset:"Atur ulang",columnDisplay:"Tampilan kolom",columnSetting:"Pengaturan",fullScreen:"Layar penuh",exitFullScreen:"Keluar layar penuh",reload:"Atur ulang",density:"Kerapatan",densityDefault:"Standar",densityLarger:"Lebih besar",densityMiddle:"Sedang",densitySmall:"Rapat"},stepsForm:{next:"Selanjutnya",prev:"Sebelumnya",submit:"Selesai"},loginForm:{submitText:"Login"},editableTable:{action:{save:"simpan",cancel:"batal",delete:"hapus",add:"Tambahkan baris data"}},switch:{open:"buka",close:"tutup"}},l={moneySymbol:"\u20AC",form:{lightFilter:{more:"pi\xF9",clear:"pulisci",confirm:"conferma",itemUnit:"elementi"}},tableForm:{search:"Filtra",reset:"Pulisci",submit:"Invia",collapsed:"Espandi",expand:"Contrai",inputPlaceholder:"Digita",selectPlaceholder:"Seleziona"},alert:{clear:"Rimuovi",selected:"Selezionati",item:"elementi"},pagination:{total:{range:" ",total:"di",item:"elementi"}},tableToolBar:{leftPin:"Fissa a sinistra",rightPin:"Fissa a destra",noPin:"Ripristina posizione",leftFixedTitle:"Fissato a sinistra",rightFixedTitle:"Fissato a destra",noFixedTitle:"Non fissato",reset:"Ripristina",columnDisplay:"Disposizione colonne",columnSetting:"Impostazioni",fullScreen:"Modalit\xE0 schermo intero",exitFullScreen:"Esci da modalit\xE0 schermo intero",reload:"Ricarica",density:"Grandezza tabella",densityDefault:"predefinito",densityLarger:"Grande",densityMiddle:"Media",densitySmall:"Compatta"},stepsForm:{next:"successivo",prev:"precedente",submit:"finisci"},loginForm:{submitText:"Accedi"},editableTable:{action:{save:"salva",cancel:"annulla",delete:"Delete",add:"add a row of data"}},switch:{open:"open",close:"chiudi"}},d={moneySymbol:"\xA5",form:{lightFilter:{more:"\u66F4\u306B",clear:"\u30AF\u30EA\u30A2",confirm:"\u78BA\u8A8D",itemUnit:"\u30A2\u30A4\u30C6\u30E0"}},tableForm:{search:"\u691C\u7D22",reset:"\u30EA\u30BB\u30C3\u30C8",submit:"\u9001\u4FE1",collapsed:"\u62E1\u5927",expand:"\u6298\u7573",inputPlaceholder:"\u5165\u529B\u3057\u3066\u304F\u3060\u3055\u3044",selectPlaceholder:"\u9078\u629E\u3057\u3066\u304F\u3060\u3055\u3044"},alert:{clear:"\u30AF\u30EA\u30A2",selected:"\u9078\u629E\u3057\u305F",item:"\u30A2\u30A4\u30C6\u30E0"},pagination:{total:{range:"\u30EC\u30B3\u30FC\u30C9",total:"/\u5408\u8A08",item:" "}},tableToolBar:{leftPin:"\u5DE6\u306B\u56FA\u5B9A",rightPin:"\u53F3\u306B\u56FA\u5B9A",noPin:"\u30AD\u30E3\u30F3\u30BB\u30EB",leftFixedTitle:"\u5DE6\u306B\u56FA\u5B9A\u3055\u308C\u305F\u9805\u76EE",rightFixedTitle:"\u53F3\u306B\u56FA\u5B9A\u3055\u308C\u305F\u9805\u76EE",noFixedTitle:"\u56FA\u5B9A\u3055\u308C\u3066\u306A\u3044\u9805\u76EE",reset:"\u30EA\u30BB\u30C3\u30C8",columnDisplay:"\u8868\u793A\u5217",columnSetting:"\u5217\u8868\u793A\u8A2D\u5B9A",fullScreen:"\u30D5\u30EB\u30B9\u30AF\u30EA\u30FC\u30F3",exitFullScreen:"\u7D42\u4E86",reload:"\u66F4\u65B0",density:"\u884C\u9AD8",densityDefault:"\u30C7\u30D5\u30A9\u30EB\u30C8",densityLarger:"\u5927",densityMiddle:"\u4E2D",densitySmall:"\u5C0F"},stepsForm:{next:"\u6B21\u3078",prev:"\u524D\u3078",submit:"\u9001\u4FE1"},loginForm:{submitText:"\u30ED\u30B0\u30A4\u30F3"},editableTable:{action:{save:"\u4FDD\u5B58",cancel:"\u30AD\u30E3\u30F3\u30BB\u30EB",delete:"\u524A\u9664",add:"\u8FFD\u52A0"}},switch:{open:"\u958B\u304F",close:"\u9589\u3058\u308B"}},c={moneySymbol:"\u20A9",form:{lightFilter:{more:"\uB354\uBCF4\uAE30",clear:"\uCD08\uAE30\uD654",confirm:"\uD655\uC778",itemUnit:"\uAC74\uC218"}},tableForm:{search:"\uC870\uD68C",reset:"\uCD08\uAE30\uD654",submit:"\uC81C\uCD9C",collapsed:"\uD655\uC7A5",expand:"\uB2EB\uAE30",inputPlaceholder:"\uC785\uB825\uD574 \uC8FC\uC138\uC694",selectPlaceholder:"\uC120\uD0DD\uD574 \uC8FC\uC138\uC694"},alert:{clear:"\uCDE8\uC18C",selected:"\uC120\uD0DD",item:"\uAC74"},pagination:{total:{range:" ",total:"/ \uCD1D",item:"\uAC74"}},tableToolBar:{leftPin:"\uC67C\uCABD\uC73C\uB85C \uD540",rightPin:"\uC624\uB978\uCABD\uC73C\uB85C \uD540",noPin:"\uD540 \uC81C\uAC70",leftFixedTitle:"\uC67C\uCABD\uC73C\uB85C \uACE0\uC815",rightFixedTitle:"\uC624\uB978\uCABD\uC73C\uB85C \uACE0\uC815",noFixedTitle:"\uBE44\uACE0\uC815",reset:"\uCD08\uAE30\uD654",columnDisplay:"\uCEEC\uB7FC \uD45C\uC2DC",columnSetting:"\uC124\uC815",fullScreen:"\uC804\uCCB4 \uD654\uBA74",exitFullScreen:"\uC804\uCCB4 \uD654\uBA74 \uCDE8\uC18C",reload:"\uC0C8\uB85C \uACE0\uCE68",density:"\uC5EC\uBC31",densityDefault:"\uAE30\uBCF8",densityLarger:"\uB9CE\uC740 \uC5EC\uBC31",densityMiddle:"\uC911\uAC04 \uC5EC\uBC31",densitySmall:"\uC881\uC740 \uC5EC\uBC31"},stepsForm:{next:"\uB2E4\uC74C",prev:"\uC774\uC804",submit:"\uC885\uB8CC"},loginForm:{submitText:"\uB85C\uADF8\uC778"},editableTable:{action:{save:"\uC800\uC7A5",cancel:"\uCDE8\uC18C",delete:"\uC0AD\uC81C",add:"\uB370\uC774\uD130 \uD589 \uCD94\uAC00"}},switch:{open:"\uC5F4",close:"\uAC00\uAE4C \uC6B4"}},I={moneySymbol:"\u20AE",form:{lightFilter:{more:"\u0418\u043B\u04AF\u04AF",clear:"\u0426\u044D\u0432\u044D\u0440\u043B\u044D\u0445",confirm:"\u0411\u0430\u0442\u0430\u043B\u0433\u0430\u0430\u0436\u0443\u0443\u043B\u0430\u0445",itemUnit:"\u041D\u044D\u0433\u0436\u04AF\u04AF\u0434"}},tableForm:{search:"\u0425\u0430\u0439\u0445",reset:"\u0428\u0438\u043D\u044D\u0447\u043B\u044D\u0445",submit:"\u0418\u043B\u0433\u044D\u044D\u0445",collapsed:"\u04E8\u0440\u0433\u04E9\u0442\u0433\u04E9\u0445",expand:"\u0425\u0443\u0440\u0430\u0430\u0445",inputPlaceholder:"\u0423\u0442\u0433\u0430 \u043E\u0440\u0443\u0443\u043B\u043D\u0430 \u0443\u0443",selectPlaceholder:"\u0423\u0442\u0433\u0430 \u0441\u043E\u043D\u0433\u043E\u043D\u043E \u0443\u0443"},alert:{clear:"\u0426\u044D\u0432\u044D\u0440\u043B\u044D\u0445",selected:"\u0421\u043E\u043D\u0433\u043E\u0433\u0434\u0441\u043E\u043D",item:"\u041D\u044D\u0433\u0436"},pagination:{total:{range:" ",total:"\u041D\u0438\u0439\u0442",item:"\u043C\u04E9\u0440"}},tableToolBar:{leftPin:"\u0417\u04AF\u04AF\u043D \u0442\u0438\u0439\u0448 \u0431\u044D\u0445\u043B\u044D\u0445",rightPin:"\u0411\u0430\u0440\u0443\u0443\u043D \u0442\u0438\u0439\u0448 \u0431\u044D\u0445\u043B\u044D\u0445",noPin:"\u0411\u044D\u0445\u043B\u044D\u0445\u0433\u04AF\u0439",leftFixedTitle:"\u0417\u04AF\u04AF\u043D \u0437\u044D\u0440\u044D\u0433\u0446\u04AF\u04AF\u043B\u044D\u0445",rightFixedTitle:"\u0411\u0430\u0440\u0443\u0443\u043D \u0437\u044D\u0440\u044D\u0433\u0446\u04AF\u04AF\u043B\u044D\u0445",noFixedTitle:"\u0417\u044D\u0440\u044D\u0433\u0446\u04AF\u04AF\u043B\u044D\u0445\u0433\u04AF\u0439",reset:"\u0428\u0438\u043D\u044D\u0447\u043B\u044D\u0445",columnDisplay:"\u0411\u0430\u0433\u0430\u043D\u0430\u0430\u0440 \u0445\u0430\u0440\u0443\u0443\u043B\u0430\u0445",columnSetting:"\u0422\u043E\u0445\u0438\u0440\u0433\u043E\u043E",fullScreen:"\u0411\u04AF\u0442\u044D\u043D \u0434\u044D\u043B\u0433\u044D\u0446\u044D\u044D\u0440",exitFullScreen:"\u0411\u04AF\u0442\u044D\u043D \u0434\u044D\u043B\u0433\u044D\u0446 \u0446\u0443\u0446\u043B\u0430\u0445",reload:"\u0428\u0438\u043D\u044D\u0447\u043B\u044D\u0445",density:"\u0425\u044D\u043C\u0436\u044D\u044D",densityDefault:"\u0425\u044D\u0432\u0438\u0439\u043D",densityLarger:"\u0422\u043E\u043C",densityMiddle:"\u0414\u0443\u043D\u0434",densitySmall:"\u0416\u0438\u0436\u0438\u0433"},stepsForm:{next:"\u0414\u0430\u0440\u0430\u0430\u0445",prev:"\u04E8\u043C\u043D\u04E9\u0445",submit:"\u0414\u0443\u0443\u0441\u0433\u0430\u0445"},loginForm:{submitText:"\u041D\u044D\u0432\u0442\u0440\u044D\u0445"},editableTable:{action:{save:"\u0425\u0430\u0434\u0433\u0430\u043B\u0430\u0445",cancel:"\u0426\u0443\u0446\u043B\u0430\u0445",delete:"\u0423\u0441\u0442\u0433\u0430\u0445",add:"\u041C\u04E9\u0440 \u043D\u044D\u043C\u044D\u0445"}},switch:{open:"\u041D\u044D\u044D\u0445",close:"\u0425\u0430\u0430\u0445"}},L={moneySymbol:"RM",form:{lightFilter:{more:"Lebih banyak",clear:"Jelas",confirm:"Mengesahkan",itemUnit:"Item"}},tableForm:{search:"Cari",reset:"Menetapkan semula",submit:"Hantar",collapsed:"Kembang",expand:"Kuncup",inputPlaceholder:"Sila masuk",selectPlaceholder:"Sila pilih"},alert:{clear:"Padam",selected:"Dipilih",item:"Item"},pagination:{total:{range:" ",total:"daripada",item:"item"}},tableToolBar:{leftPin:"Pin ke kiri",rightPin:"Pin ke kanan",noPin:"Tidak pin",leftFixedTitle:"Tetap ke kiri",rightFixedTitle:"Tetap ke kanan",noFixedTitle:"Tidak Tetap",reset:"Menetapkan semula",columnDisplay:"Lajur",columnSetting:"Settings",fullScreen:"Full Screen",exitFullScreen:"Keluar Full Screen",reload:"Muat Semula",density:"Densiti",densityDefault:"Biasa",densityLarger:"Besar",densityMiddle:"Tengah",densitySmall:"Kecil"},stepsForm:{next:"Seterusnya",prev:"Sebelumnya",submit:"Selesai"},loginForm:{submitText:"Log Masuk"},editableTable:{action:{save:"Simpan",cancel:"Membatalkan",delete:"Menghapuskan",add:"tambah baris data"}},switch:{open:"Terbuka",close:"Tutup"}},M={moneySymbol:"z\u0142",form:{lightFilter:{more:"Wi\u0119cej",clear:"Wyczy\u015B\u0107",confirm:"Potwierd\u017A",itemUnit:"Ilo\u015B\u0107"}},tableForm:{search:"Szukaj",reset:"Reset",submit:"Zatwierd\u017A",collapsed:"Poka\u017C wiecej",expand:"Poka\u017C mniej",inputPlaceholder:"Prosz\u0119 poda\u0107",selectPlaceholder:"Prosz\u0119 wybra\u0107"},alert:{clear:"Wyczy\u015B\u0107",selected:"Wybrane",item:"Wpis"},pagination:{total:{range:" ",total:"z",item:"Wpis\xF3w"}},tableToolBar:{leftPin:"Przypnij do lewej",rightPin:"Przypnij do prawej",noPin:"Odepnij",leftFixedTitle:"Przypi\u0119te do lewej",rightFixedTitle:"Przypi\u0119te do prawej",noFixedTitle:"Nieprzypi\u0119te",reset:"Reset",columnDisplay:"Wy\u015Bwietlane wiersze",columnSetting:"Ustawienia",fullScreen:"Pe\u0142en ekran",exitFullScreen:"Zamknij pe\u0142en ekran",reload:"Od\u015Bwie\u017C",density:"Odst\u0119p",densityDefault:"Standard",densityLarger:"Wiekszy",densityMiddle:"Sredni",densitySmall:"Kompaktowy"},stepsForm:{next:"Weiter",prev:"Zur\xFCck",submit:"Abschlie\xDFen"},loginForm:{submitText:"Zaloguj si\u0119"},editableTable:{action:{save:"Zapisa\u0107",cancel:"Anuluj",delete:"Usun\u0105\u0107",add:"dodawanie wiersza danych"}},switch:{open:"otwiera\u0107",close:"zamyka\u0107"}},H={moneySymbol:"R$",form:{lightFilter:{more:"Mais",clear:"Limpar",confirm:"Confirmar",itemUnit:"Itens"}},tableForm:{search:"Filtrar",reset:"Limpar",submit:"Confirmar",collapsed:"Expandir",expand:"Colapsar",inputPlaceholder:"Por favor insira",selectPlaceholder:"Por favor selecione"},alert:{clear:"Limpar",selected:"Selecionado(s)",item:"Item(s)"},pagination:{total:{range:" ",total:"de",item:"itens"}},tableToolBar:{leftPin:"Fixar \xE0 esquerda",rightPin:"Fixar \xE0 direita",noPin:"Desfixado",leftFixedTitle:"Fixado \xE0 esquerda",rightFixedTitle:"Fixado \xE0 direita",noFixedTitle:"N\xE3o fixado",reset:"Limpar",columnDisplay:"Mostrar Coluna",columnSetting:"Configura\xE7\xF5es",fullScreen:"Tela Cheia",exitFullScreen:"Sair da Tela Cheia",reload:"Atualizar",density:"Densidade",densityDefault:"Padr\xE3o",densityLarger:"Largo",densityMiddle:"M\xE9dio",densitySmall:"Compacto"},stepsForm:{next:"Pr\xF3ximo",prev:"Anterior",submit:"Enviar"},loginForm:{submitText:"Entrar"},editableTable:{action:{save:"Salvar",cancel:"Cancelar",delete:"Apagar",add:"adicionar uma linha de dados"}},switch:{open:"abrir",close:"fechar"}},q={moneySymbol:"\u20BD",form:{lightFilter:{more:"\u0415\u0449\u0435",clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C",confirm:"\u041E\u041A",itemUnit:"\u041F\u043E\u0437\u0438\u0446\u0438\u0438"}},tableForm:{search:"\u041D\u0430\u0439\u0442\u0438",reset:"\u0421\u0431\u0440\u043E\u0441",submit:"\u041E\u0442\u043F\u0440\u0430\u0432\u0438\u0442\u044C",collapsed:"\u0420\u0430\u0437\u0432\u0435\u0440\u043D\u0443\u0442\u044C",expand:"\u0421\u0432\u0435\u0440\u043D\u0443\u0442\u044C",inputPlaceholder:"\u0412\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435",selectPlaceholder:"\u0412\u044B\u0431\u0435\u0440\u0438\u0442\u0435 \u0437\u043D\u0430\u0447\u0435\u043D\u0438\u0435"},alert:{clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u044C",selected:"\u0412\u044B\u0431\u0440\u0430\u043D\u043E",item:"\u044D\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u0432"},pagination:{total:{range:" ",total:"\u0438\u0437",item:"\u044D\u043B\u0435\u043C\u0435\u043D\u0442\u043E\u0432"}},tableToolBar:{leftPin:"\u0417\u0430\u043A\u0440\u0435\u043F\u0438\u0442\u044C \u0441\u043B\u0435\u0432\u0430",rightPin:"\u0417\u0430\u043A\u0440\u0435\u043F\u0438\u0442\u044C \u0441\u043F\u0440\u0430\u0432\u0430",noPin:"\u041E\u0442\u043A\u0440\u0435\u043F\u0438\u0442\u044C",leftFixedTitle:"\u0417\u0430\u043A\u0440\u0435\u043F\u043B\u0435\u043D\u043E \u0441\u043B\u0435\u0432\u0430",rightFixedTitle:"\u0417\u0430\u043A\u0440\u0435\u043F\u043B\u0435\u043D\u043E \u0441\u043F\u0440\u0430\u0432\u0430",noFixedTitle:"\u041D\u0435 \u0437\u0430\u043A\u0440\u0435\u043F\u043B\u0435\u043D\u043E",reset:"\u0421\u0431\u0440\u043E\u0441",columnDisplay:"\u041E\u0442\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u0438\u0435 \u0441\u0442\u043E\u043B\u0431\u0446\u0430",columnSetting:"\u041D\u0430\u0441\u0442\u0440\u043E\u0439\u043A\u0438",fullScreen:"\u041F\u043E\u043B\u043D\u044B\u0439 \u044D\u043A\u0440\u0430\u043D",exitFullScreen:"\u0412\u044B\u0439\u0442\u0438 \u0438\u0437 \u043F\u043E\u043B\u043D\u043E\u044D\u043A\u0440\u0430\u043D\u043D\u043E\u0433\u043E \u0440\u0435\u0436\u0438\u043C\u0430",reload:"\u041E\u0431\u043D\u043E\u0432\u0438\u0442\u044C",density:"\u0420\u0430\u0437\u043C\u0435\u0440",densityDefault:"\u041F\u043E \u0443\u043C\u043E\u043B\u0447\u0430\u043D\u0438\u044E",densityLarger:"\u0411\u043E\u043B\u044C\u0448\u043E\u0439",densityMiddle:"\u0421\u0440\u0435\u0434\u043D\u0438\u0439",densitySmall:"\u0421\u0436\u0430\u0442\u044B\u0439"},stepsForm:{next:"\u0421\u043B\u0435\u0434\u0443\u044E\u0449\u0438\u0439",prev:"\u041F\u0440\u0435\u0434\u044B\u0434\u0443\u0449\u0438\u0439",submit:"\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u044C"},loginForm:{submitText:"\u0412\u0445\u043E\u0434"},editableTable:{action:{save:"\u0421\u043E\u0445\u0440\u0430\u043D\u0438\u0442\u044C",cancel:"\u041E\u0442\u043C\u0435\u043D\u0438\u0442\u044C",delete:"\u0423\u0434\u0430\u043B\u0438\u0442\u044C",add:"\u0434\u043E\u0431\u0430\u0432\u0438\u0442\u044C \u0440\u044F\u0434 \u0434\u0430\u043D\u043D\u044B\u0445"}},switch:{open:"\u041E\u0442\u043A\u0440\u044B\u0442\u044B\u0439 \u0447\u0435\u043C\u043F\u0438\u043E\u043D\u0430\u0442 \u043C\u0438\u0440\u0430 \u043F\u043E \u0442\u0435\u043D\u043D\u0438\u0441\u0443",close:"\u041F\u043E \u0430\u0434\u0440\u0435\u0441\u0443:"}},G={moneySymbol:"\u20AC",deleteThisLine:"Odstr\xE1ni\u0165 tento riadok",copyThisLine:"Skop\xEDrujte tento riadok",form:{lightFilter:{more:"Viac",clear:"Vy\u010Disti\u0165",confirm:"Potvr\u010Fte",itemUnit:"Polo\u017Eky"}},tableForm:{search:"Vyhlada\u0165",reset:"Resetova\u0165",submit:"Odosla\u0165",collapsed:"Rozbali\u0165",expand:"Zbali\u0165",inputPlaceholder:"Pros\xEDm, zadajte",selectPlaceholder:"Pros\xEDm, vyberte"},alert:{clear:"Vy\u010Disti\u0165",selected:"Vybran\xFD",item:"Polo\u017Eka"},pagination:{total:{range:" ",total:"z",item:"polo\u017Eiek"}},tableToolBar:{leftPin:"Pripn\xFA\u0165 v\u013Eavo",rightPin:"Pripn\xFA\u0165 vpravo",noPin:"Odopnut\xE9",leftFixedTitle:"Fixovan\xE9 na \u013Eavo",rightFixedTitle:"Fixovan\xE9 na pravo",noFixedTitle:"Nefixovan\xE9",reset:"Resetova\u0165",columnDisplay:"Zobrazenie st\u013Apcov",columnSetting:"Nastavenia",fullScreen:"Cel\xE1 obrazovka",exitFullScreen:"Ukon\u010Di\u0165 cel\xFA obrazovku",reload:"Obnovi\u0165",density:"Hustota",densityDefault:"Predvolen\xE9",densityLarger:"V\xE4\u010D\u0161ie",densityMiddle:"Stredn\xE9",densitySmall:"Kompaktn\xE9"},stepsForm:{next:"\u010Eal\u0161ie",prev:"Predch\xE1dzaj\xFAce",submit:"Potvrdi\u0165"},loginForm:{submitText:"Prihl\xE1si\u0165 sa"},editableTable:{onlyOneLineEditor:"Upravova\u0165 mo\u017Eno iba jeden riadok",action:{save:"Ulo\u017Ei\u0165",cancel:"Zru\u0161i\u0165",delete:"Odstr\xE1ni\u0165",add:"prida\u0165 riadok \xFAdajov"}},switch:{open:"otvori\u0165",close:"zavrie\u0165"}},re={moneySymbol:"RSD",form:{lightFilter:{more:"Vi\u0161e",clear:"O\u010Disti",confirm:"Potvrdi",itemUnit:"Stavke"}},tableForm:{search:"Prona\u0111i",reset:"Resetuj",submit:"Po\u0161alji",collapsed:"Pro\u0161iri",expand:"Skupi",inputPlaceholder:"Molimo unesite",selectPlaceholder:"Molimo odaberite"},alert:{clear:"O\u010Disti",selected:"Odabrano",item:"Stavka"},pagination:{total:{range:" ",total:"od",item:"stavki"}},tableToolBar:{leftPin:"Zaka\u010Di levo",rightPin:"Zaka\u010Di desno",noPin:"Nije zaka\u010Deno",leftFixedTitle:"Fiksirano levo",rightFixedTitle:"Fiksirano desno",noFixedTitle:"Nije fiksirano",reset:"Resetuj",columnDisplay:"Prikaz kolona",columnSetting:"Pode\u0161avanja",fullScreen:"Pun ekran",exitFullScreen:"Zatvori pun ekran",reload:"Osve\u017Ei",density:"Veli\u010Dina",densityDefault:"Podrazumevana",densityLarger:"Ve\u0107a",densityMiddle:"Srednja",densitySmall:"Kompaktna"},stepsForm:{next:"Dalje",prev:"Nazad",submit:"Gotovo"},loginForm:{submitText:"Prijavi se"},editableTable:{action:{save:"Sa\u010Duvaj",cancel:"Poni\u0161ti",delete:"Obri\u0161i",add:"dodajte red podataka"}},switch:{open:"\u041E\u0442\u0432\u043E\u0440\u0438\u0442\u0435",close:"\u0417\u0430\u0442\u0432\u043E\u0440\u0438\u0442\u0435"}},Q={moneySymbol:"\u0E3F",deleteThisLine:"\u0E25\u0E1A\u0E1A\u0E23\u0E23\u0E17\u0E31\u0E14\u0E19\u0E35\u0E49",copyThisLine:"\u0E04\u0E31\u0E14\u0E25\u0E2D\u0E01\u0E1A\u0E23\u0E23\u0E17\u0E31\u0E14\u0E19\u0E35\u0E49",form:{lightFilter:{more:"\u0E21\u0E32\u0E01\u0E01\u0E27\u0E48\u0E32",clear:"\u0E0A\u0E31\u0E14\u0E40\u0E08\u0E19",confirm:"\u0E22\u0E37\u0E19\u0E22\u0E31\u0E19",itemUnit:"\u0E23\u0E32\u0E22\u0E01\u0E32\u0E23"}},tableForm:{search:"\u0E2A\u0E2D\u0E1A\u0E16\u0E32\u0E21",reset:"\u0E23\u0E35\u0E40\u0E0B\u0E47\u0E15",submit:"\u0E2A\u0E48\u0E07",collapsed:"\u0E02\u0E22\u0E32\u0E22",expand:"\u0E17\u0E23\u0E38\u0E14",inputPlaceholder:"\u0E01\u0E23\u0E38\u0E13\u0E32\u0E1B\u0E49\u0E2D\u0E19",selectPlaceholder:"\u0E42\u0E1B\u0E23\u0E14\u0E40\u0E25\u0E37\u0E2D\u0E01"},alert:{clear:"\u0E0A\u0E31\u0E14\u0E40\u0E08\u0E19",selected:"\u0E40\u0E25\u0E37\u0E2D\u0E01\u0E41\u0E25\u0E49\u0E27",item:"\u0E23\u0E32\u0E22\u0E01\u0E32\u0E23"},pagination:{total:{range:" ",total:"\u0E02\u0E2D\u0E07",item:"\u0E23\u0E32\u0E22\u0E01\u0E32\u0E23"}},tableToolBar:{leftPin:"\u0E1B\u0E31\u0E01\u0E2B\u0E21\u0E38\u0E14\u0E44\u0E1B\u0E17\u0E32\u0E07\u0E0B\u0E49\u0E32\u0E22",rightPin:"\u0E1B\u0E31\u0E01\u0E2B\u0E21\u0E38\u0E14\u0E44\u0E1B\u0E17\u0E32\u0E07\u0E02\u0E27\u0E32",noPin:"\u0E40\u0E25\u0E34\u0E01\u0E15\u0E23\u0E36\u0E07\u0E41\u0E25\u0E49\u0E27",leftFixedTitle:"\u0E41\u0E01\u0E49\u0E44\u0E02\u0E14\u0E49\u0E32\u0E19\u0E0B\u0E49\u0E32\u0E22",rightFixedTitle:"\u0E41\u0E01\u0E49\u0E44\u0E02\u0E14\u0E49\u0E32\u0E19\u0E02\u0E27\u0E32",noFixedTitle:"\u0E44\u0E21\u0E48\u0E04\u0E07\u0E17\u0E35\u0E48",reset:"\u0E23\u0E35\u0E40\u0E0B\u0E47\u0E15",columnDisplay:"\u0E01\u0E32\u0E23\u0E41\u0E2A\u0E14\u0E07\u0E04\u0E2D\u0E25\u0E31\u0E21\u0E19\u0E4C",columnSetting:"\u0E01\u0E32\u0E23\u0E15\u0E31\u0E49\u0E07\u0E04\u0E48\u0E32",fullScreen:"\u0E40\u0E15\u0E47\u0E21\u0E08\u0E2D",exitFullScreen:"\u0E2D\u0E2D\u0E01\u0E08\u0E32\u0E01\u0E42\u0E2B\u0E21\u0E14\u0E40\u0E15\u0E47\u0E21\u0E2B\u0E19\u0E49\u0E32\u0E08\u0E2D",reload:"\u0E23\u0E35\u0E40\u0E1F\u0E23\u0E0A",density:"\u0E04\u0E27\u0E32\u0E21\u0E2B\u0E19\u0E32\u0E41\u0E19\u0E48\u0E19",densityDefault:"\u0E04\u0E48\u0E32\u0E40\u0E23\u0E34\u0E48\u0E21\u0E15\u0E49\u0E19",densityLarger:"\u0E02\u0E19\u0E32\u0E14\u0E43\u0E2B\u0E0D\u0E48\u0E02\u0E36\u0E49\u0E19",densityMiddle:"\u0E01\u0E25\u0E32\u0E07",densitySmall:"\u0E01\u0E30\u0E17\u0E31\u0E14\u0E23\u0E31\u0E14"},stepsForm:{next:"\u0E16\u0E31\u0E14\u0E44\u0E1B",prev:"\u0E01\u0E48\u0E2D\u0E19\u0E2B\u0E19\u0E49\u0E32",submit:"\u0E40\u0E2A\u0E23\u0E47\u0E08"},loginForm:{submitText:"\u0E40\u0E02\u0E49\u0E32\u0E2A\u0E39\u0E48\u0E23\u0E30\u0E1A\u0E1A"},editableTable:{onlyOneLineEditor:"\u0E41\u0E01\u0E49\u0E44\u0E02\u0E44\u0E14\u0E49\u0E40\u0E1E\u0E35\u0E22\u0E07\u0E1A\u0E23\u0E23\u0E17\u0E31\u0E14\u0E40\u0E14\u0E35\u0E22\u0E27\u0E40\u0E17\u0E48\u0E32\u0E19\u0E31\u0E49\u0E19",action:{save:"\u0E1A\u0E31\u0E19\u0E17\u0E36\u0E01",cancel:"\u0E22\u0E01\u0E40\u0E25\u0E34\u0E01",delete:"\u0E25\u0E1A",add:"\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E41\u0E16\u0E27\u0E02\u0E2D\u0E07\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25"}},switch:{open:"\u0E40\u0E1B\u0E34\u0E14",close:"\u0E1B\u0E34\u0E14"}},X={moneySymbol:"\u20BA",form:{lightFilter:{more:"Daha Fazla",clear:"Temizle",confirm:"Onayla",itemUnit:"\xD6\u011Feler"}},tableForm:{search:"Filtrele",reset:"S\u0131f\u0131rla",submit:"G\xF6nder",collapsed:"Daha fazla",expand:"Daha az",inputPlaceholder:"Filtrelemek i\xE7in bir de\u011Fer girin",selectPlaceholder:"Filtrelemek i\xE7in bir de\u011Fer se\xE7in"},alert:{clear:"Temizle",selected:"Se\xE7ili",item:"\xD6\u011Fe"},pagination:{total:{range:" ",total:"Toplam",item:"\xD6\u011Fe"}},tableToolBar:{leftPin:"Sola sabitle",rightPin:"Sa\u011Fa sabitle",noPin:"Sabitlemeyi kald\u0131r",leftFixedTitle:"Sola sabitlendi",rightFixedTitle:"Sa\u011Fa sabitlendi",noFixedTitle:"Sabitlenmedi",reset:"S\u0131f\u0131rla",columnDisplay:"Kolon G\xF6r\xFCn\xFCm\xFC",columnSetting:"Ayarlar",fullScreen:"Tam Ekran",exitFullScreen:"Tam Ekrandan \xC7\u0131k",reload:"Yenile",density:"Kal\u0131nl\u0131k",densityDefault:"Varsay\u0131lan",densityLarger:"B\xFCy\xFCk",densityMiddle:"Orta",densitySmall:"K\xFC\xE7\xFCk"},stepsForm:{next:"S\u0131radaki",prev:"\xD6nceki",submit:"G\xF6nder"},loginForm:{submitText:"Giri\u015F Yap"},editableTable:{action:{save:"Kaydet",cancel:"Vazge\xE7",delete:"Sil",add:"foegje in rige gegevens ta"}},switch:{open:"a\xE7\u0131k",close:"kapatmak"}},ae={moneySymbol:"\u20B4",deleteThisLine:"\u0412\u0438\u0434\u0430\u0442\u0438\u043B\u0438 \u0440\u044F\u0434\u043E\u043A",copyThisLine:"\u0421\u043A\u043E\u043F\u0456\u044E\u0432\u0430\u0442\u0438 \u0440\u044F\u0434\u043E\u043A",form:{lightFilter:{more:"\u0429\u0435",clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u0438",confirm:"\u041E\u043A",itemUnit:"\u041F\u043E\u0437\u0438\u0446\u0456\u0457"}},tableForm:{search:"\u041F\u043E\u0448\u0443\u043A",reset:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u0438",submit:"\u0412\u0456\u0434\u043F\u0440\u0430\u0432\u0438\u0442\u0438",collapsed:"\u0420\u043E\u0437\u0433\u043E\u0440\u043D\u0443\u0442\u0438",expand:"\u0417\u0433\u043E\u0440\u043D\u0443\u0442\u0438",inputPlaceholder:"\u0412\u0432\u0435\u0434\u0456\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u043D\u044F",selectPlaceholder:"\u041E\u0431\u0435\u0440\u0456\u0442\u044C \u0437\u043D\u0430\u0447\u0435\u043D\u043D\u044F"},alert:{clear:"\u041E\u0447\u0438\u0441\u0442\u0438\u0442\u0438",selected:"\u041E\u0431\u0440\u0430\u043D\u043E",item:"\u0435\u043B\u0435\u043C\u0435\u043D\u0442\u0456\u0432"},pagination:{total:{range:" ",total:"\u0437",item:"\u0435\u043B\u0435\u043C\u0435\u043D\u0442\u0456\u0432"}},tableToolBar:{leftPin:"\u0417\u0430\u043A\u0440\u0456\u043F\u0438\u0442\u0438 \u0437\u043B\u0456\u0432\u0430",rightPin:"\u0417\u0430\u043A\u0440\u0456\u043F\u0438\u0442\u0438 \u0441\u043F\u0440\u0430\u0432\u0430",noPin:"\u0412\u0456\u0434\u043A\u0440\u0456\u043F\u0438\u0442\u0438",leftFixedTitle:"\u0417\u0430\u043A\u0440\u0456\u043F\u043B\u0435\u043D\u043E \u0437\u043B\u0456\u0432\u0430",rightFixedTitle:"\u0417\u0430\u043A\u0440\u0456\u043F\u043B\u0435\u043D\u043E \u0441\u043F\u0440\u0430\u0432\u0430",noFixedTitle:"\u041D\u0435 \u0437\u0430\u043A\u0440\u0456\u043F\u043B\u0435\u043D\u043E",reset:"\u0421\u043A\u0438\u043D\u0443\u0442\u0438",columnDisplay:"\u0412\u0456\u0434\u043E\u0431\u0440\u0430\u0436\u0435\u043D\u043D\u044F \u0441\u0442\u043E\u0432\u043F\u0446\u0456\u0432",columnSetting:"\u041D\u0430\u043B\u0430\u0448\u0442\u0443\u0432\u0430\u043D\u043D\u044F",fullScreen:"\u041F\u043E\u0432\u043D\u043E\u0435\u043A\u0440\u0430\u043D\u043D\u0438\u0439 \u0440\u0435\u0436\u0438\u043C",exitFullScreen:"\u0412\u0438\u0439\u0442\u0438 \u0437 \u043F\u043E\u0432\u043D\u043E\u0435\u043A\u0440\u0430\u043D\u043D\u043E\u0433\u043E \u0440\u0435\u0436\u0438\u043C\u0443",reload:"\u041E\u043D\u043E\u0432\u0438\u0442\u0438",density:"\u0420\u043E\u0437\u043C\u0456\u0440",densityDefault:"\u0417\u0430 \u0437\u0430\u043C\u043E\u0432\u0447\u0443\u0432\u0430\u043D\u043D\u044F\u043C",densityLarger:"\u0412\u0435\u043B\u0438\u043A\u0438\u0439",densityMiddle:"\u0421\u0435\u0440\u0435\u0434\u043D\u0456\u0439",densitySmall:"\u0421\u0442\u0438\u0441\u043B\u0438\u0439"},stepsForm:{next:"\u041D\u0430\u0441\u0442\u0443\u043F\u043D\u0438\u0439",prev:"\u041F\u043E\u043F\u0435\u0440\u0435\u0434\u043D\u0456\u0439",submit:"\u0417\u0430\u0432\u0435\u0440\u0448\u0438\u0442\u0438"},loginForm:{submitText:"\u0412\u0445\u0456\u0445"},editableTable:{onlyOneLineEditor:"\u0422\u0456\u043B\u044C\u043A\u0438 \u043E\u0434\u0438\u043D \u0440\u044F\u0434\u043E\u043A \u043C\u043E\u0436\u0435 \u0431\u0443\u0442\u0438 \u0440\u0435\u0434\u0430\u0433\u043E\u0432\u0430\u043D\u0438\u0439 \u043E\u0434\u043D\u043E\u0447\u0430\u0441\u043D\u043E",action:{save:"\u0417\u0431\u0435\u0440\u0435\u0433\u0442\u0438",cancel:"\u0412\u0456\u0434\u043C\u0456\u043D\u0438\u0442\u0438",delete:"\u0412\u0438\u0434\u0430\u043B\u0438\u0442\u0438",add:"\u0434\u043E\u0434\u0430\u0442\u0438 \u0440\u044F\u0434\u043E\u043A"}},switch:{open:"\u0412\u0456\u0434\u043A\u0440\u0438\u0442\u043E",close:"\u0417\u0430\u043A\u0440\u0438\u0442\u043E"}},Ee={moneySymbol:"UZS",form:{lightFilter:{more:"Yana",clear:"Tozalash",confirm:"OK",itemUnit:"Pozitsiyalar"}},tableForm:{search:"Qidirish",reset:"Qayta tiklash",submit:"Yuborish",collapsed:"Yig\u2018ish",expand:"Kengaytirish",inputPlaceholder:"Qiymatni kiriting",selectPlaceholder:"Qiymatni tanlang"},alert:{clear:"Tozalash",selected:"Tanlangan",item:"elementlar"},pagination:{total:{range:" ",total:"dan",item:"elementlar"}},tableToolBar:{leftPin:"Chapga mahkamlash",rightPin:"O\u2018ngga mahkamlash",noPin:"Mahkamlashni olib tashlash",leftFixedTitle:"Chapga mahkamlangan",rightFixedTitle:"O\u2018ngga mahkamlangan",noFixedTitle:"Mahkamlashsiz",reset:"Qayta tiklash",columnDisplay:"Ustunni ko\u2018rsatish",columnSetting:"Sozlamalar",fullScreen:"To\u2018liq ekran",exitFullScreen:"To\u2018liq ekrandan chiqish",reload:"Yangilash",density:"O\u2018lcham",densityDefault:"Standart",densityLarger:"Katta",densityMiddle:"O\u2018rtacha",densitySmall:"Kichik"},stepsForm:{next:"Keyingi",prev:"Oldingi",submit:"Tugatish"},loginForm:{submitText:"Kirish"},editableTable:{action:{save:"Saqlash",cancel:"Bekor qilish",delete:"O\u2018chirish",add:"ma\u02BClumotlar qatorini qo\u2018shish"}},switch:{open:"Ochish",close:"Yopish"}},ye={moneySymbol:"\u20AB",form:{lightFilter:{more:"Nhi\u1EC1u h\u01A1n",clear:"Trong",confirm:"X\xE1c nh\u1EADn",itemUnit:"M\u1EE5c"}},tableForm:{search:"T\xECm ki\u1EBFm",reset:"L\xE0m l\u1EA1i",submit:"G\u1EEDi \u0111i",collapsed:"M\u1EDF r\u1ED9ng",expand:"Thu g\u1ECDn",inputPlaceholder:"nh\u1EADp d\u1EEF li\u1EC7u",selectPlaceholder:"Vui l\xF2ng ch\u1ECDn"},alert:{clear:"X\xF3a",selected:"\u0111\xE3 ch\u1ECDn",item:"m\u1EE5c"},pagination:{total:{range:" ",total:"tr\xEAn",item:"m\u1EB7t h\xE0ng"}},tableToolBar:{leftPin:"Ghim tr\xE1i",rightPin:"Ghim ph\u1EA3i",noPin:"B\u1ECF ghim",leftFixedTitle:"C\u1ED1 \u0111\u1ECBnh tr\xE1i",rightFixedTitle:"C\u1ED1 \u0111\u1ECBnh ph\u1EA3i",noFixedTitle:"Ch\u01B0a c\u1ED1 \u0111\u1ECBnh",reset:"L\xE0m l\u1EA1i",columnDisplay:"C\u1ED9t hi\u1EC3n th\u1ECB",columnSetting:"C\u1EA5u h\xECnh",fullScreen:"Ch\u1EBF \u0111\u1ED9 to\xE0n m\xE0n h\xECnh",exitFullScreen:"Tho\xE1t ch\u1EBF \u0111\u1ED9 to\xE0n m\xE0n h\xECnh",reload:"L\xE0m m\u1EDBi",density:"M\u1EADt \u0111\u1ED9 hi\u1EC3n th\u1ECB",densityDefault:"M\u1EB7c \u0111\u1ECBnh",densityLarger:"M\u1EB7c \u0111\u1ECBnh",densityMiddle:"Trung b\xECnh",densitySmall:"Ch\u1EADt"},stepsForm:{next:"Sau",prev:"Tr\u01B0\u1EDBc",submit:"K\u1EBFt th\xFAc"},loginForm:{submitText:"\u0110\u0103ng nh\u1EADp"},editableTable:{action:{save:"C\u1EE9u",cancel:"H\u1EE7y",delete:"X\xF3a",add:"th\xEAm m\u1ED9t h\xE0ng d\u1EEF li\u1EC7u"}},switch:{open:"m\u1EDF",close:"\u0111\xF3ng"}},xe={moneySymbol:"\xA5",deleteThisLine:"\u5220\u9664\u6B64\u9879",copyThisLine:"\u590D\u5236\u6B64\u9879",form:{lightFilter:{more:"\u66F4\u591A\u7B5B\u9009",clear:"\u6E05\u9664",confirm:"\u786E\u8BA4",itemUnit:"\u9879"}},tableForm:{search:"\u67E5\u8BE2",reset:"\u91CD\u7F6E",submit:"\u63D0\u4EA4",collapsed:"\u5C55\u5F00",expand:"\u6536\u8D77",inputPlaceholder:"\u8BF7\u8F93\u5165",selectPlaceholder:"\u8BF7\u9009\u62E9"},alert:{clear:"\u53D6\u6D88\u9009\u62E9",selected:"\u5DF2\u9009\u62E9",item:"\u9879"},pagination:{total:{range:"\u7B2C",total:"\u6761/\u603B\u5171",item:"\u6761"}},tableToolBar:{leftPin:"\u56FA\u5B9A\u5728\u5217\u9996",rightPin:"\u56FA\u5B9A\u5728\u5217\u5C3E",noPin:"\u4E0D\u56FA\u5B9A",leftFixedTitle:"\u56FA\u5B9A\u5728\u5DE6\u4FA7",rightFixedTitle:"\u56FA\u5B9A\u5728\u53F3\u4FA7",noFixedTitle:"\u4E0D\u56FA\u5B9A",reset:"\u91CD\u7F6E",columnDisplay:"\u5217\u5C55\u793A",columnSetting:"\u5217\u8BBE\u7F6E",fullScreen:"\u5168\u5C4F",exitFullScreen:"\u9000\u51FA\u5168\u5C4F",reload:"\u5237\u65B0",density:"\u5BC6\u5EA6",densityDefault:"\u6B63\u5E38",densityLarger:"\u5BBD\u677E",densityMiddle:"\u4E2D\u7B49",densitySmall:"\u7D27\u51D1"},stepsForm:{next:"\u4E0B\u4E00\u6B65",prev:"\u4E0A\u4E00\u6B65",submit:"\u63D0\u4EA4"},loginForm:{submitText:"\u767B\u5F55"},editableTable:{onlyOneLineEditor:"\u53EA\u80FD\u540C\u65F6\u7F16\u8F91\u4E00\u884C",action:{save:"\u4FDD\u5B58",cancel:"\u53D6\u6D88",delete:"\u5220\u9664",add:"\u6DFB\u52A0\u4E00\u884C\u6570\u636E"}},switch:{open:"\u6253\u5F00",close:"\u5173\u95ED"}},ve={moneySymbol:"NT$",deleteThisLine:"\u522A\u9664\u6B64\u9879",copyThisLine:"\u8907\u88FD\u6B64\u9879",form:{lightFilter:{more:"\u66F4\u591A\u7BE9\u9078",clear:"\u6E05\u9664",confirm:"\u78BA\u8A8D",itemUnit:"\u9805"}},tableForm:{search:"\u67E5\u8A62",reset:"\u91CD\u7F6E",submit:"\u63D0\u4EA4",collapsed:"\u5C55\u958B",expand:"\u6536\u8D77",inputPlaceholder:"\u8ACB\u8F38\u5165",selectPlaceholder:"\u8ACB\u9078\u64C7"},alert:{clear:"\u53D6\u6D88\u9078\u64C7",selected:"\u5DF2\u9078\u64C7",item:"\u9805"},pagination:{total:{range:"\u7B2C",total:"\u689D/\u7E3D\u5171",item:"\u689D"}},tableToolBar:{leftPin:"\u56FA\u5B9A\u5230\u5DE6\u908A",rightPin:"\u56FA\u5B9A\u5230\u53F3\u908A",noPin:"\u4E0D\u56FA\u5B9A",leftFixedTitle:"\u56FA\u5B9A\u5728\u5DE6\u5074",rightFixedTitle:"\u56FA\u5B9A\u5728\u53F3\u5074",noFixedTitle:"\u4E0D\u56FA\u5B9A",reset:"\u91CD\u7F6E",columnDisplay:"\u5217\u5C55\u793A",columnSetting:"\u5217\u8A2D\u7F6E",fullScreen:"\u5168\u5C4F",exitFullScreen:"\u9000\u51FA\u5168\u5C4F",reload:"\u5237\u65B0",density:"\u5BC6\u5EA6",densityDefault:"\u6B63\u5E38",densityLarger:"\u5BEC\u9B06",densityMiddle:"\u4E2D\u7B49",densitySmall:"\u7DCA\u6E4A"},stepsForm:{next:"\u4E0B\u4E00\u6B65",prev:"\u4E0A\u4E00\u6B65",submit:"\u5B8C\u6210"},loginForm:{submitText:"\u767B\u5165"},editableTable:{onlyOneLineEditor:"\u53EA\u80FD\u540C\u6642\u7DE8\u8F2F\u4E00\u884C",action:{save:"\u4FDD\u5B58",cancel:"\u53D6\u6D88",delete:"\u522A\u9664",add:"\u65B0\u589E\u4E00\u884C\u8CC7\u6599"}},switch:{open:"\u6253\u958B",close:"\u95DC\u9589"}},oe={moneySymbol:"\u20AC",deleteThisLine:"Verwijder deze regel",copyThisLine:"Kopieer deze regel",form:{lightFilter:{more:"Meer filters",clear:"Wissen",confirm:"Bevestigen",itemUnit:"item"}},tableForm:{search:"Zoeken",reset:"Resetten",submit:"Indienen",collapsed:"Uitvouwen",expand:"Inklappen",inputPlaceholder:"Voer in",selectPlaceholder:"Selecteer"},alert:{clear:"Selectie annuleren",selected:"Geselecteerd",item:"item"},pagination:{total:{range:"Van",total:"items/totaal",item:"items"}},tableToolBar:{leftPin:"Vastzetten aan begin",rightPin:"Vastzetten aan einde",noPin:"Niet vastzetten",leftFixedTitle:"Vastzetten aan de linkerkant",rightFixedTitle:"Vastzetten aan de rechterkant",noFixedTitle:"Niet vastzetten",reset:"Resetten",columnDisplay:"Kolomweergave",columnSetting:"Kolominstellingen",fullScreen:"Volledig scherm",exitFullScreen:"Verlaat volledig scherm",reload:"Vernieuwen",density:"Dichtheid",densityDefault:"Normaal",densityLarger:"Ruim",densityMiddle:"Gemiddeld",densitySmall:"Compact"},stepsForm:{next:"Volgende stap",prev:"Vorige stap",submit:"Indienen"},loginForm:{submitText:"Inloggen"},editableTable:{onlyOneLineEditor:"Slechts \xE9\xE9n regel tegelijk bewerken",action:{save:"Opslaan",cancel:"Annuleren",delete:"Verwijderen",add:"Een regel toevoegen"}},switch:{open:"Openen",close:"Sluiten"}},Fe={moneySymbol:"RON",deleteThisLine:"\u0218terge acest r\xE2nd",copyThisLine:"Copiaz\u0103 acest r\xE2nd",form:{lightFilter:{more:"Mai multe filtre",clear:"Cur\u0103\u021B\u0103",confirm:"Confirm\u0103",itemUnit:"elemente"}},tableForm:{search:"Caut\u0103",reset:"Reseteaz\u0103",submit:"Trimite",collapsed:"Extinde",expand:"Restr\xE2nge",inputPlaceholder:"Introduce\u021Bi",selectPlaceholder:"Selecta\u021Bi"},alert:{clear:"Anuleaz\u0103 selec\u021Bia",selected:"Selectat",item:"elemente"},pagination:{total:{range:"De la",total:"elemente/total",item:"elemente"}},tableToolBar:{leftPin:"Fixeaz\u0103 la \xEEnceput",rightPin:"Fixeaz\u0103 la sf\xE2r\u0219it",noPin:"Nu fixa",leftFixedTitle:"Fixeaz\u0103 \xEEn st\xE2nga",rightFixedTitle:"Fixeaz\u0103 \xEEn dreapta",noFixedTitle:"Nu fixa",reset:"Reseteaz\u0103",columnDisplay:"Afi\u0219are coloane",columnSetting:"Set\u0103ri coloane",fullScreen:"Ecran complet",exitFullScreen:"Ie\u0219i din ecran complet",reload:"Re\xEEncarc\u0103",density:"Densitate",densityDefault:"Normal",densityLarger:"Larg",densityMiddle:"Mediu",densitySmall:"Compact"},stepsForm:{next:"Pasul urm\u0103tor",prev:"Pasul anterior",submit:"Trimite"},loginForm:{submitText:"Autentificare"},editableTable:{onlyOneLineEditor:"Se poate edita doar un r\xE2nd simultan",action:{save:"Salveaz\u0103",cancel:"Anuleaz\u0103",delete:"\u0218terge",add:"Adaug\u0103 un r\xE2nd"}},switch:{open:"Deschide",close:"\xCEnchide"}},E={moneySymbol:"SEK",deleteThisLine:"Radera denna rad",copyThisLine:"Kopiera denna rad",form:{lightFilter:{more:"Fler filter",clear:"Rensa",confirm:"Bekr\xE4fta",itemUnit:"objekt"}},tableForm:{search:"S\xF6k",reset:"\xC5terst\xE4ll",submit:"Skicka",collapsed:"Expandera",expand:"F\xE4ll ihop",inputPlaceholder:"V\xE4nligen ange",selectPlaceholder:"V\xE4nligen v\xE4lj"},alert:{clear:"Avbryt val",selected:"Vald",item:"objekt"},pagination:{total:{range:"Fr\xE5n",total:"objekt/totalt",item:"objekt"}},tableToolBar:{leftPin:"F\xE4st till v\xE4nster",rightPin:"F\xE4st till h\xF6ger",noPin:"Inte f\xE4st",leftFixedTitle:"F\xE4st till v\xE4nster",rightFixedTitle:"F\xE4st till h\xF6ger",noFixedTitle:"Inte f\xE4st",reset:"\xC5terst\xE4ll",columnDisplay:"Kolumnvisning",columnSetting:"Kolumninst\xE4llningar",fullScreen:"Fullsk\xE4rm",exitFullScreen:"Avsluta fullsk\xE4rm",reload:"Ladda om",density:"T\xE4thet",densityDefault:"Normal",densityLarger:"L\xF6s",densityMiddle:"Medium",densitySmall:"Kompakt"},stepsForm:{next:"N\xE4sta steg",prev:"F\xF6reg\xE5ende steg",submit:"Skicka"},loginForm:{submitText:"Logga in"},editableTable:{onlyOneLineEditor:"Endast en rad kan redigeras \xE5t g\xE5ngen",action:{save:"Spara",cancel:"Avbryt",delete:"Radera",add:"L\xE4gg till en rad"}},switch:{open:"\xD6ppna",close:"St\xE4ng"}},r=function(Xe,Ye){return{getMessage:function(m,h){var O=(0,a.U2)(Ye,m.replace(/\[(\d+)\]/g,".$1").split("."))||"";if(O)return O;var f=Xe.replace("_","-");if(f==="zh-CN")return h;var v=We["zh-CN"];return v?v.getMessage(m,h):h},locale:Xe}},i=r("mn_MN",I),g=r("ar_EG",_),A=r("zh_CN",xe),V=r("en_US",k),B=r("en_GB",u),U=r("vi_VN",ye),S=r("it_IT",l),J=r("ja_JP",d),$=r("es_ES",p),ie=r("ca_ES",y),N=r("ru_RU",q),W=r("sr_RS",re),ee=r("ms_MY",L),b=r("zh_TW",ve),R=r("fr_FR",P),F=r("pt_BR",H),T=r("ko_KR",c),K=r("id_ID",n),fe=r("de_DE",w),ce=r("fa_IR",x),he=r("tr_TR",X),_e=r("pl_PL",M),Ie=r("hr_",o),De=r("th_TH",Q),me=r("cs_cz",s),Se=r("sk_SK",G),ge=r("he_IL",C),se=r("uk_UA",ae),Ne=r("uz_UZ",Ee),we=r("nl_NL",oe),Ae=r("ro_RO",Fe),be=r("sv_SE",E),We={"mn-MN":i,"ar-EG":g,"zh-CN":A,"en-US":V,"en-GB":B,"vi-VN":U,"it-IT":S,"ja-JP":J,"es-ES":$,"ca-ES":ie,"ru-RU":N,"sr-RS":W,"ms-MY":ee,"zh-TW":b,"fr-FR":R,"pt-BR":F,"ko-KR":T,"id-ID":K,"de-DE":fe,"fa-IR":ce,"tr-TR":he,"pl-PL":_e,"hr-HR":Ie,"th-TH":De,"cs-CZ":me,"sk-SK":Se,"he-IL":ge,"uk-UA":se,"uz-UZ":Ne,"nl-NL":we,"ro-RO":Ae,"sv-SE":be},Ge=Object.keys(We),Je=function(Xe){var Ye=(Xe||"zh-CN").toLocaleLowerCase();return Ge.find(function(t){var m=t.toLocaleLowerCase();return m.includes(Ye)})}},98082:function(Z,D,e){e.d(D,{Nd:function(){return d},Ow:function(){return o},Wf:function(){return l},Xj:function(){return c},dQ:function(){return n},uK:function(){return x}});var a=e(1413),_=e(85982),y=e(10274),s=e(9361),w=e(28459),u=e(67294),k=e(10915),p=e(67804),x=function(L,M){return new y.C(L).setAlpha(M).toRgbString()},P=function(L,M){var H=new TinyColor(L);return H.lighten(M).toHexString()},C=function(){return typeof s.Z=="undefined"||!s.Z?p:s.Z},o=C(),n=o.useToken,l=function(L){return{boxSizing:"border-box",margin:0,padding:0,color:L.colorText,fontSize:L.fontSize,lineHeight:L.lineHeight,listStyle:"none"}},d=function(L){return{color:L.colorLink,outline:"none",cursor:"pointer",transition:"color ".concat(L.motionDurationSlow),"&:focus, &:hover":{color:L.colorLinkHover},"&:active":{color:L.colorLinkActive}}};function c(I,L){var M,H=(0,u.useContext)(k.L_),q=H.token,G=q===void 0?{}:q,re=(0,u.useContext)(k.L_),Q=re.hashed,X=n(),ae=X.token,Ee=X.hashId,ye=(0,u.useContext)(k.L_),xe=ye.theme,ve=(0,u.useContext)(w.ZP.ConfigContext),oe=ve.getPrefixCls,Fe=ve.csp;return G.layout||(G=(0,a.Z)({},ae)),G.proComponentsCls=(M=G.proComponentsCls)!==null&&M!==void 0?M:".".concat(oe("pro")),G.antCls=".".concat(oe()),{wrapSSR:(0,_.useStyleRegister)({theme:xe,token:G,path:[I],nonce:Fe==null?void 0:Fe.nonce},function(){return L(G)}),hashId:Q?Ee:""}}},67804:function(Z,D,e){e.r(D),e.d(D,{defaultToken:function(){return w},emptyTheme:function(){return k},hashCode:function(){return u},token:function(){return p},useToken:function(){return x}});var a=e(1413),_=e(85982),y=e(9361),s,w={blue:"#1677ff",purple:"#722ED1",cyan:"#13C2C2",green:"#52C41A",magenta:"#EB2F96",pink:"#eb2f96",red:"#F5222D",orange:"#FA8C16",yellow:"#FADB14",volcano:"#FA541C",geekblue:"#2F54EB",gold:"#FAAD14",lime:"#A0D911",colorPrimary:"#1677ff",colorSuccess:"#52c41a",colorWarning:"#faad14",colorError:"#ff7875",colorInfo:"#1677ff",colorTextBase:"#000",colorBgBase:"#fff",fontFamily:"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'",fontSize:14,lineWidth:1,lineType:"solid",motionUnit:.1,motionBase:0,motionEaseOutCirc:"cubic-bezier(0.08, 0.82, 0.17, 1)",motionEaseInOutCirc:"cubic-bezier(0.78, 0.14, 0.15, 0.86)",motionEaseOut:"cubic-bezier(0.215, 0.61, 0.355, 1)",motionEaseInOut:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutBack:"cubic-bezier(0.12, 0.4, 0.29, 1.46)",motionEaseInQuint:"cubic-bezier(0.645, 0.045, 0.355, 1)",motionEaseOutQuint:"cubic-bezier(0.23, 1, 0.32, 1)",borderRadius:4,sizeUnit:4,sizeStep:4,sizePopupArrow:16,controlHeight:32,zIndexBase:0,zIndexPopupBase:1e3,opacityImage:1,wireframe:!1,"blue-1":"#e6f4ff","blue-2":"#bae0ff","blue-3":"#91caff","blue-4":"#69b1ff","blue-5":"#4096ff","blue-6":"#1677ff","blue-7":"#0958d9","blue-8":"#003eb3","blue-9":"#002c8c","blue-10":"#001d66","purple-1":"#f9f0ff","purple-2":"#efdbff","purple-3":"#d3adf7","purple-4":"#b37feb","purple-5":"#9254de","purple-6":"#722ed1","purple-7":"#531dab","purple-8":"#391085","purple-9":"#22075e","purple-10":"#120338","cyan-1":"#e6fffb","cyan-2":"#b5f5ec","cyan-3":"#87e8de","cyan-4":"#5cdbd3","cyan-5":"#36cfc9","cyan-6":"#13c2c2","cyan-7":"#08979c","cyan-8":"#006d75","cyan-9":"#00474f","cyan-10":"#002329","green-1":"#f6ffed","green-2":"#d9f7be","green-3":"#b7eb8f","green-4":"#95de64","green-5":"#73d13d","green-6":"#52c41a","green-7":"#389e0d","green-8":"#237804","green-9":"#135200","green-10":"#092b00","magenta-1":"#fff0f6","magenta-2":"#ffd6e7","magenta-3":"#ffadd2","magenta-4":"#ff85c0","magenta-5":"#f759ab","magenta-6":"#eb2f96","magenta-7":"#c41d7f","magenta-8":"#9e1068","magenta-9":"#780650","magenta-10":"#520339","pink-1":"#fff0f6","pink-2":"#ffd6e7","pink-3":"#ffadd2","pink-4":"#ff85c0","pink-5":"#f759ab","pink-6":"#eb2f96","pink-7":"#c41d7f","pink-8":"#9e1068","pink-9":"#780650","pink-10":"#520339","red-1":"#fff1f0","red-2":"#ffccc7","red-3":"#ffa39e","red-4":"#ff7875","red-5":"#ff4d4f","red-6":"#f5222d","red-7":"#cf1322","red-8":"#a8071a","red-9":"#820014","red-10":"#5c0011","orange-1":"#fff7e6","orange-2":"#ffe7ba","orange-3":"#ffd591","orange-4":"#ffc069","orange-5":"#ffa940","orange-6":"#fa8c16","orange-7":"#d46b08","orange-8":"#ad4e00","orange-9":"#873800","orange-10":"#612500","yellow-1":"#feffe6","yellow-2":"#ffffb8","yellow-3":"#fffb8f","yellow-4":"#fff566","yellow-5":"#ffec3d","yellow-6":"#fadb14","yellow-7":"#d4b106","yellow-8":"#ad8b00","yellow-9":"#876800","yellow-10":"#614700","volcano-1":"#fff2e8","volcano-2":"#ffd8bf","volcano-3":"#ffbb96","volcano-4":"#ff9c6e","volcano-5":"#ff7a45","volcano-6":"#fa541c","volcano-7":"#d4380d","volcano-8":"#ad2102","volcano-9":"#871400","volcano-10":"#610b00","geekblue-1":"#f0f5ff","geekblue-2":"#d6e4ff","geekblue-3":"#adc6ff","geekblue-4":"#85a5ff","geekblue-5":"#597ef7","geekblue-6":"#2f54eb","geekblue-7":"#1d39c4","geekblue-8":"#10239e","geekblue-9":"#061178","geekblue-10":"#030852","gold-1":"#fffbe6","gold-2":"#fff1b8","gold-3":"#ffe58f","gold-4":"#ffd666","gold-5":"#ffc53d","gold-6":"#faad14","gold-7":"#d48806","gold-8":"#ad6800","gold-9":"#874d00","gold-10":"#613400","lime-1":"#fcffe6","lime-2":"#f4ffb8","lime-3":"#eaff8f","lime-4":"#d3f261","lime-5":"#bae637","lime-6":"#a0d911","lime-7":"#7cb305","lime-8":"#5b8c00","lime-9":"#3f6600","lime-10":"#254000",colorText:"rgba(0, 0, 0, 0.88)",colorTextSecondary:"rgba(0, 0, 0, 0.65)",colorTextTertiary:"rgba(0, 0, 0, 0.45)",colorTextQuaternary:"rgba(0, 0, 0, 0.25)",colorFill:"rgba(0, 0, 0, 0.15)",colorFillSecondary:"rgba(0, 0, 0, 0.06)",colorFillTertiary:"rgba(0, 0, 0, 0.04)",colorFillQuaternary:"rgba(0, 0, 0, 0.02)",colorBgLayout:"hsl(220,23%,97%)",colorBgContainer:"#ffffff",colorBgElevated:"#ffffff",colorBgSpotlight:"rgba(0, 0, 0, 0.85)",colorBorder:"#d9d9d9",colorBorderSecondary:"#f0f0f0",colorPrimaryBg:"#e6f4ff",colorPrimaryBgHover:"#bae0ff",colorPrimaryBorder:"#91caff",colorPrimaryBorderHover:"#69b1ff",colorPrimaryHover:"#4096ff",colorPrimaryActive:"#0958d9",colorPrimaryTextHover:"#4096ff",colorPrimaryText:"#1677ff",colorPrimaryTextActive:"#0958d9",colorSuccessBg:"#f6ffed",colorSuccessBgHover:"#d9f7be",colorSuccessBorder:"#b7eb8f",colorSuccessBorderHover:"#95de64",colorSuccessHover:"#95de64",colorSuccessActive:"#389e0d",colorSuccessTextHover:"#73d13d",colorSuccessText:"#52c41a",colorSuccessTextActive:"#389e0d",colorErrorBg:"#fff2f0",colorErrorBgHover:"#fff1f0",colorErrorBorder:"#ffccc7",colorErrorBorderHover:"#ffa39e",colorErrorHover:"#ffa39e",colorErrorActive:"#d9363e",colorErrorTextHover:"#ff7875",colorErrorText:"#ff4d4f",colorErrorTextActive:"#d9363e",colorWarningBg:"#fffbe6",colorWarningBgHover:"#fff1b8",colorWarningBorder:"#ffe58f",colorWarningBorderHover:"#ffd666",colorWarningHover:"#ffd666",colorWarningActive:"#d48806",colorWarningTextHover:"#ffc53d",colorWarningText:"#faad14",colorWarningTextActive:"#d48806",colorInfoBg:"#e6f4ff",colorInfoBgHover:"#bae0ff",colorInfoBorder:"#91caff",colorInfoBorderHover:"#69b1ff",colorInfoHover:"#69b1ff",colorInfoActive:"#0958d9",colorInfoTextHover:"#4096ff",colorInfoText:"#1677ff",colorInfoTextActive:"#0958d9",colorBgMask:"rgba(0, 0, 0, 0.45)",colorWhite:"#fff",sizeXXL:48,sizeXL:32,sizeLG:24,sizeMD:20,sizeMS:16,size:16,sizeSM:12,sizeXS:8,sizeXXS:4,controlHeightSM:24,controlHeightXS:16,controlHeightLG:40,motionDurationFast:"0.1s",motionDurationMid:"0.2s",motionDurationSlow:"0.3s",fontSizes:[12,14,16,20,24,30,38,46,56,68],lineHeights:[1.6666666666666667,1.5714285714285714,1.5,1.4,1.3333333333333333,1.2666666666666666,1.2105263157894737,1.173913043478261,1.1428571428571428,1.1176470588235294],lineWidthBold:2,borderRadiusXS:1,borderRadiusSM:4,borderRadiusLG:8,borderRadiusOuter:4,colorLink:"#1677ff",colorLinkHover:"#69b1ff",colorLinkActive:"#0958d9",colorFillContent:"rgba(0, 0, 0, 0.06)",colorFillContentHover:"rgba(0, 0, 0, 0.15)",colorFillAlter:"rgba(0, 0, 0, 0.02)",colorBgContainerDisabled:"rgba(0, 0, 0, 0.04)",colorBorderBg:"#ffffff",colorSplit:"rgba(5, 5, 5, 0.06)",colorTextPlaceholder:"rgba(0, 0, 0, 0.25)",colorTextDisabled:"rgba(0, 0, 0, 0.25)",colorTextHeading:"rgba(0, 0, 0, 0.88)",colorTextLabel:"rgba(0, 0, 0, 0.65)",colorTextDescription:"rgba(0, 0, 0, 0.45)",colorTextLightSolid:"#fff",colorHighlight:"#ff7875",colorBgTextHover:"rgba(0, 0, 0, 0.06)",colorBgTextActive:"rgba(0, 0, 0, 0.15)",colorIcon:"rgba(0, 0, 0, 0.45)",colorIconHover:"rgba(0, 0, 0, 0.88)",colorErrorOutline:"rgba(255, 38, 5, 0.06)",colorWarningOutline:"rgba(255, 215, 5, 0.1)",fontSizeSM:12,fontSizeLG:16,fontSizeXL:20,fontSizeHeading1:38,fontSizeHeading2:30,fontSizeHeading3:24,fontSizeHeading4:20,fontSizeHeading5:16,fontSizeIcon:12,lineHeight:1.5714285714285714,lineHeightLG:1.5,lineHeightSM:1.6666666666666667,lineHeightHeading1:1.2105263157894737,lineHeightHeading2:1.2666666666666666,lineHeightHeading3:1.3333333333333333,lineHeightHeading4:1.4,lineHeightHeading5:1.5,controlOutlineWidth:2,controlInteractiveSize:16,controlItemBgHover:"rgba(0, 0, 0, 0.04)",controlItemBgActive:"#e6f4ff",controlItemBgActiveHover:"#bae0ff",controlItemBgActiveDisabled:"rgba(0, 0, 0, 0.15)",controlTmpOutline:"rgba(0, 0, 0, 0.02)",controlOutline:"rgba(5, 145, 255, 0.1)",fontWeightStrong:600,opacityLoading:.65,linkDecoration:"none",linkHoverDecoration:"none",linkFocusDecoration:"none",controlPaddingHorizontal:12,controlPaddingHorizontalSM:8,paddingXXS:4,paddingXS:8,paddingSM:12,padding:16,paddingMD:20,paddingLG:24,paddingXL:32,paddingContentHorizontalLG:24,paddingContentVerticalLG:16,paddingContentHorizontal:16,paddingContentVertical:12,paddingContentHorizontalSM:16,paddingContentVerticalSM:8,marginXXS:4,marginXS:8,marginSM:12,margin:16,marginMD:20,marginLG:24,marginXL:32,marginXXL:48,boxShadow:"0 1px 2px 0 rgba(0, 0, 0, 0.03),0 1px 6px -1px rgba(0, 0, 0, 0.02),0 2px 4px 0 rgba(0, 0, 0, 0.02)",boxShadowSecondary:"0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05)",screenXS:480,screenXSMin:480,screenXSMax:479,screenSM:576,screenSMMin:576,screenSMMax:575,screenMD:768,screenMDMin:768,screenMDMax:767,screenLG:992,screenLGMin:992,screenLGMax:991,screenXL:1200,screenXLMin:1200,screenXLMax:1199,screenXXL:1600,screenXXLMin:1600,screenXXLMax:1599,boxShadowPopoverArrow:"3px 3px 7px rgba(0, 0, 0, 0.1)",boxShadowCard:"0 1px 2px -2px rgba(0, 0, 0, 0.16),0 3px 6px 0 rgba(0, 0, 0, 0.12),0 5px 12px 4px rgba(0, 0, 0, 0.09)",boxShadowDrawerRight:"-6px 0 16px 0 rgba(0, 0, 0, 0.08),-3px 0 6px -4px rgba(0, 0, 0, 0.12),-9px 0 28px 8px rgba(0, 0, 0, 0.05)",boxShadowDrawerLeft:"6px 0 16px 0 rgba(0, 0, 0, 0.08),3px 0 6px -4px rgba(0, 0, 0, 0.12),9px 0 28px 8px rgba(0, 0, 0, 0.05)",boxShadowDrawerUp:"0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05)",boxShadowDrawerDown:"0 -6px 16px 0 rgba(0, 0, 0, 0.08),0 -3px 6px -4px rgba(0, 0, 0, 0.12),0 -9px 28px 8px rgba(0, 0, 0, 0.05)",boxShadowTabsOverflowLeft:"inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowRight:"inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowTop:"inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)",boxShadowTabsOverflowBottom:"inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)",_tokenKey:"19w80ff",_hashId:"css-dev-only-do-not-override-i2zu9q"},u=function(C){for(var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,n=3735928559^o,l=1103547991^o,d=0,c;d<C.length;d++)c=C.charCodeAt(d),n=Math.imul(n^c,2654435761),l=Math.imul(l^c,1597334677);return n=Math.imul(n^n>>>16,2246822507)^Math.imul(l^l>>>13,3266489909),l=Math.imul(l^l>>>16,2246822507)^Math.imul(n^n>>>13,3266489909),4294967296*(2097151&l)+(n>>>0)},k=(0,_.createTheme)(function(P){return P}),p={theme:k,token:(0,a.Z)((0,a.Z)({},w),y.Z===null||y.Z===void 0||(s=y.Z.defaultAlgorithm)===null||s===void 0?void 0:s.call(y.Z,y.Z===null||y.Z===void 0?void 0:y.Z.defaultSeed)),hashId:"pro-".concat(u(JSON.stringify(w)))},x=function(){return p}},1977:function(Z,D,e){e.d(D,{n:function(){return P}});var a=e(97685),_=e(71002),y=/^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z\-]+(?:\.[\da-z\-]+)*))?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i,s=function(o){return o==="*"||o==="x"||o==="X"},w=function(o){var n=parseInt(o,10);return isNaN(n)?o:n},u=function(o,n){return(0,_.Z)(o)!==(0,_.Z)(n)?[String(o),String(n)]:[o,n]},k=function(o,n){if(s(o)||s(n))return 0;var l=u(w(o),w(n)),d=(0,a.Z)(l,2),c=d[0],I=d[1];return c>I?1:c<I?-1:0},p=function(o,n){for(var l=0;l<Math.max(o.length,n.length);l++){var d=k(o[l]||"0",n[l]||"0");if(d!==0)return d}return 0},x=function(o){var n,l=o.match(y);return l==null||(n=l.shift)===null||n===void 0||n.call(l),l},P=function(o,n){var l=x(o),d=x(n),c=l.pop(),I=d.pop(),L=p(l,d);return L!==0?L:c||I?c?-1:1:0}},81643:function(Z,D,e){e.d(D,{Z:function(){return a}});const a=_=>_?typeof _=="function"?_():_:null},57838:function(Z,D,e){e.d(D,{Z:function(){return _}});var a=e(67294);function _(){const[,y]=a.useReducer(s=>s+1,0);return y}},74443:function(Z,D,e){e.d(D,{ZP:function(){return u},c4:function(){return y},m9:function(){return k}});var a=e(67294),_=e(29691);const y=["xxl","xl","lg","md","sm","xs"],s=p=>({xs:`(max-width: ${p.screenXSMax}px)`,sm:`(min-width: ${p.screenSM}px)`,md:`(min-width: ${p.screenMD}px)`,lg:`(min-width: ${p.screenLG}px)`,xl:`(min-width: ${p.screenXL}px)`,xxl:`(min-width: ${p.screenXXL}px)`}),w=p=>{const x=p,P=[].concat(y).reverse();return P.forEach((C,o)=>{const n=C.toUpperCase(),l=`screen${n}Min`,d=`screen${n}`;if(!(x[l]<=x[d]))throw new Error(`${l}<=${d} fails : !(${x[l]}<=${x[d]})`);if(o<P.length-1){const c=`screen${n}Max`;if(!(x[d]<=x[c]))throw new Error(`${d}<=${c} fails : !(${x[d]}<=${x[c]})`);const L=`screen${P[o+1].toUpperCase()}Min`;if(!(x[c]<=x[L]))throw new Error(`${c}<=${L} fails : !(${x[c]}<=${x[L]})`)}}),p};function u(){const[,p]=(0,_.ZP)(),x=s(w(p));return a.useMemo(()=>{const P=new Map;let C=-1,o={};return{matchHandlers:{},dispatch(n){return o=n,P.forEach(l=>l(o)),P.size>=1},subscribe(n){return P.size||this.register(),C+=1,P.set(C,n),n(o),C},unsubscribe(n){P.delete(n),P.size||this.unregister()},unregister(){Object.keys(x).forEach(n=>{const l=x[n],d=this.matchHandlers[l];d==null||d.mql.removeListener(d==null?void 0:d.listener)}),P.clear()},register(){Object.keys(x).forEach(n=>{const l=x[n],d=I=>{let{matches:L}=I;this.dispatch(Object.assign(Object.assign({},o),{[n]:L}))},c=window.matchMedia(l);c.addListener(d),this.matchHandlers[l]={mql:c,listener:d},d(c)})},responsiveMap:x}},[p])}const k=(p,x)=>{if(x&&typeof x=="object")for(let P=0;P<y.length;P++){const C=y[P];if(p[C]&&x[C]!==void 0)return x[C]}}},7134:function(Z,D,e){e.d(D,{C:function(){return Fe}});var a=e(67294),_=e(93967),y=e.n(_),s=e(9220),w=e(42550),u=e(74443),k=e(53124),p=e(35792),x=e(98675),P=e(25378),o=a.createContext({}),n=e(85982),l=e(14747),d=e(83559),c=e(83262);const I=E=>{const{antCls:r,componentCls:i,iconCls:g,avatarBg:A,avatarColor:V,containerSize:B,containerSizeLG:U,containerSizeSM:S,textFontSize:J,textFontSizeLG:$,textFontSizeSM:ie,borderRadius:N,borderRadiusLG:W,borderRadiusSM:ee,lineWidth:b,lineType:R}=E,F=(T,K,fe)=>({width:T,height:T,borderRadius:"50%",[`&${i}-square`]:{borderRadius:fe},[`&${i}-icon`]:{fontSize:K,[`> ${g}`]:{margin:0}}});return{[i]:Object.assign(Object.assign(Object.assign(Object.assign({},(0,l.Wf)(E)),{position:"relative",display:"inline-flex",justifyContent:"center",alignItems:"center",overflow:"hidden",color:V,whiteSpace:"nowrap",textAlign:"center",verticalAlign:"middle",background:A,border:`${(0,n.unit)(b)} ${R} transparent`,"&-image":{background:"transparent"},[`${r}-image-img`]:{display:"block"}}),F(B,J,N)),{"&-lg":Object.assign({},F(U,$,W)),"&-sm":Object.assign({},F(S,ie,ee)),"> img":{display:"block",width:"100%",height:"100%",objectFit:"cover"}})}},L=E=>{const{componentCls:r,groupBorderColor:i,groupOverlapping:g,groupSpace:A}=E;return{[`${r}-group`]:{display:"inline-flex",[r]:{borderColor:i},"> *:not(:first-child)":{marginInlineStart:g}},[`${r}-group-popover`]:{[`${r} + ${r}`]:{marginInlineStart:A}}}},M=E=>{const{controlHeight:r,controlHeightLG:i,controlHeightSM:g,fontSize:A,fontSizeLG:V,fontSizeXL:B,fontSizeHeading3:U,marginXS:S,marginXXS:J,colorBorderBg:$}=E;return{containerSize:r,containerSizeLG:i,containerSizeSM:g,textFontSize:Math.round((V+B)/2),textFontSizeLG:U,textFontSizeSM:A,groupSpace:J,groupOverlapping:-S,groupBorderColor:$}};var H=(0,d.I$)("Avatar",E=>{const{colorTextLightSolid:r,colorTextPlaceholder:i}=E,g=(0,c.mergeToken)(E,{avatarBg:i,avatarColor:r});return[I(g),L(g)]},M),q=function(E,r){var i={};for(var g in E)Object.prototype.hasOwnProperty.call(E,g)&&r.indexOf(g)<0&&(i[g]=E[g]);if(E!=null&&typeof Object.getOwnPropertySymbols=="function")for(var A=0,g=Object.getOwnPropertySymbols(E);A<g.length;A++)r.indexOf(g[A])<0&&Object.prototype.propertyIsEnumerable.call(E,g[A])&&(i[g[A]]=E[g[A]]);return i};const G=(E,r)=>{const[i,g]=a.useState(1),[A,V]=a.useState(!1),[B,U]=a.useState(!0),S=a.useRef(null),J=a.useRef(null),$=(0,w.sQ)(r,S),{getPrefixCls:ie,avatar:N}=a.useContext(k.E_),W=a.useContext(o),ee=()=>{if(!J.current||!S.current)return;const f=J.current.offsetWidth,v=S.current.offsetWidth;if(f!==0&&v!==0){const{gap:z=4}=E;z*2<v&&g(v-z*2<f?(v-z*2)/f:1)}};a.useEffect(()=>{V(!0)},[]),a.useEffect(()=>{U(!0),g(1)},[E.src]),a.useEffect(ee,[E.gap]);const b=()=>{const{onError:f}=E;(f==null?void 0:f())!==!1&&U(!1)},{prefixCls:R,shape:F,size:T,src:K,srcSet:fe,icon:ce,className:he,rootClassName:_e,alt:Ie,draggable:De,children:me,crossOrigin:Se}=E,ge=q(E,["prefixCls","shape","size","src","srcSet","icon","className","rootClassName","alt","draggable","children","crossOrigin"]),se=(0,x.Z)(f=>{var v,z;return(z=(v=T!=null?T:W==null?void 0:W.size)!==null&&v!==void 0?v:f)!==null&&z!==void 0?z:"default"}),Ne=Object.keys(typeof se=="object"?se||{}:{}).some(f=>["xs","sm","md","lg","xl","xxl"].includes(f)),we=(0,P.Z)(Ne),Ae=a.useMemo(()=>{if(typeof se!="object")return{};const f=u.c4.find(z=>we[z]),v=se[f];return v?{width:v,height:v,fontSize:v&&(ce||me)?v/2:18}:{}},[we,se]),be=ie("avatar",R),We=(0,p.Z)(be),[Ge,Je,st]=H(be,We),Xe=y()({[`${be}-lg`]:se==="large",[`${be}-sm`]:se==="small"}),Ye=a.isValidElement(K),t=F||(W==null?void 0:W.shape)||"circle",m=y()(be,Xe,N==null?void 0:N.className,`${be}-${t}`,{[`${be}-image`]:Ye||K&&B,[`${be}-icon`]:!!ce},st,We,he,_e,Je),h=typeof se=="number"?{width:se,height:se,fontSize:ce?se/2:18}:{};let O;if(typeof K=="string"&&B)O=a.createElement("img",{src:K,draggable:De,srcSet:fe,onError:b,alt:Ie,crossOrigin:Se});else if(Ye)O=K;else if(ce)O=ce;else if(A||i!==1){const f=`scale(${i})`,v={msTransform:f,WebkitTransform:f,transform:f};O=a.createElement(s.default,{onResize:ee},a.createElement("span",{className:`${be}-string`,ref:J,style:Object.assign({},v)},me))}else O=a.createElement("span",{className:`${be}-string`,style:{opacity:0},ref:J},me);return delete ge.onError,delete ge.gap,Ge(a.createElement("span",Object.assign({},ge,{style:Object.assign(Object.assign(Object.assign(Object.assign({},h),Ae),N==null?void 0:N.style),ge.style),className:m,ref:$}),O))};var Q=a.forwardRef(G),X=e(50344),ae=e(96159),Ee=e(55241);const ye=E=>{const{size:r,shape:i}=a.useContext(o),g=a.useMemo(()=>({size:E.size||r,shape:E.shape||i}),[E.size,E.shape,r,i]);return a.createElement(o.Provider,{value:g},E.children)};var ve=E=>{var r,i,g;const{getPrefixCls:A,direction:V}=a.useContext(k.E_),{prefixCls:B,className:U,rootClassName:S,style:J,maxCount:$,maxStyle:ie,size:N,shape:W,maxPopoverPlacement:ee,maxPopoverTrigger:b,children:R,max:F}=E,T=A("avatar",B),K=`${T}-group`,fe=(0,p.Z)(T),[ce,he,_e]=H(T,fe),Ie=y()(K,{[`${K}-rtl`]:V==="rtl"},_e,fe,U,S,he),De=(0,X.Z)(R).map((ge,se)=>(0,ae.Tm)(ge,{key:`avatar-key-${se}`})),me=(F==null?void 0:F.count)||$,Se=De.length;if(me&&me<Se){const ge=De.slice(0,me),se=De.slice(me,Se),Ne=(F==null?void 0:F.style)||ie,we=((r=F==null?void 0:F.popover)===null||r===void 0?void 0:r.trigger)||b||"hover",Ae=((i=F==null?void 0:F.popover)===null||i===void 0?void 0:i.placement)||ee||"top",be=Object.assign(Object.assign({content:se},F==null?void 0:F.popover),{overlayClassName:y()(`${K}-popover`,(g=F==null?void 0:F.popover)===null||g===void 0?void 0:g.overlayClassName),placement:Ae,trigger:we});return ge.push(a.createElement(Ee.Z,Object.assign({key:"avatar-popover-key",destroyTooltipOnHide:!0},be),a.createElement(Q,{style:Ne},`+${Se-me}`))),ce(a.createElement(ye,{shape:W,size:N},a.createElement("div",{className:Ie,style:J},ge)))}return ce(a.createElement(ye,{shape:W,size:N},a.createElement("div",{className:Ie,style:J},De)))};const oe=Q;oe.Group=ve;var Fe=oe},25378:function(Z,D,e){var a=e(67294),_=e(8410),y=e(57838),s=e(74443);function w(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;const k=(0,a.useRef)({}),p=(0,y.Z)(),x=(0,s.ZP)();return(0,_.Z)(()=>{const P=x.subscribe(C=>{k.current=C,u&&p()});return()=>x.unsubscribe(P)},[]),k.current}D.Z=w},66330:function(Z,D,e){e.d(D,{aV:function(){return x}});var a=e(67294),_=e(93967),y=e.n(_),s=e(92419),w=e(81643),u=e(53124),k=e(20136),p=function(o,n){var l={};for(var d in o)Object.prototype.hasOwnProperty.call(o,d)&&n.indexOf(d)<0&&(l[d]=o[d]);if(o!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,d=Object.getOwnPropertySymbols(o);c<d.length;c++)n.indexOf(d[c])<0&&Object.prototype.propertyIsEnumerable.call(o,d[c])&&(l[d[c]]=o[d[c]]);return l};const x=o=>{let{title:n,content:l,prefixCls:d}=o;return!n&&!l?null:a.createElement(a.Fragment,null,n&&a.createElement("div",{className:`${d}-title`},n),l&&a.createElement("div",{className:`${d}-inner-content`},l))},P=o=>{const{hashId:n,prefixCls:l,className:d,style:c,placement:I="top",title:L,content:M,children:H}=o,q=(0,w.Z)(L),G=(0,w.Z)(M),re=y()(n,l,`${l}-pure`,`${l}-placement-${I}`,d);return a.createElement("div",{className:re,style:c},a.createElement("div",{className:`${l}-arrow`}),a.createElement(s.Popup,Object.assign({},o,{className:n,prefixCls:l}),H||a.createElement(x,{prefixCls:l,title:q,content:G})))},C=o=>{const{prefixCls:n,className:l}=o,d=p(o,["prefixCls","className"]),{getPrefixCls:c}=a.useContext(u.E_),I=c("popover",n),[L,M,H]=(0,k.Z)(I);return L(a.createElement(P,Object.assign({},d,{prefixCls:I,hashId:M,className:y()(l,H)})))};D.ZP=C},55241:function(Z,D,e){var a=e(67294),_=e(93967),y=e.n(_),s=e(21770),w=e(15105),u=e(81643),k=e(33603),p=e(96159),x=e(53124),P=e(83062),C=e(66330),o=e(20136),n=function(c,I){var L={};for(var M in c)Object.prototype.hasOwnProperty.call(c,M)&&I.indexOf(M)<0&&(L[M]=c[M]);if(c!=null&&typeof Object.getOwnPropertySymbols=="function")for(var H=0,M=Object.getOwnPropertySymbols(c);H<M.length;H++)I.indexOf(M[H])<0&&Object.prototype.propertyIsEnumerable.call(c,M[H])&&(L[M[H]]=c[M[H]]);return L};const d=a.forwardRef((c,I)=>{var L,M;const{prefixCls:H,title:q,content:G,overlayClassName:re,placement:Q="top",trigger:X="hover",children:ae,mouseEnterDelay:Ee=.1,mouseLeaveDelay:ye=.1,onOpenChange:xe,overlayStyle:ve={}}=c,oe=n(c,["prefixCls","title","content","overlayClassName","placement","trigger","children","mouseEnterDelay","mouseLeaveDelay","onOpenChange","overlayStyle"]),{getPrefixCls:Fe}=a.useContext(x.E_),E=Fe("popover",H),[r,i,g]=(0,o.Z)(E),A=Fe(),V=y()(re,i,g),[B,U]=(0,s.Z)(!1,{value:(L=c.open)!==null&&L!==void 0?L:c.visible,defaultValue:(M=c.defaultOpen)!==null&&M!==void 0?M:c.defaultVisible}),S=(W,ee)=>{U(W,!0),xe==null||xe(W,ee)},J=W=>{W.keyCode===w.Z.ESC&&S(!1,W)},$=W=>{S(W)},ie=(0,u.Z)(q),N=(0,u.Z)(G);return r(a.createElement(P.Z,Object.assign({placement:Q,trigger:X,mouseEnterDelay:Ee,mouseLeaveDelay:ye,overlayStyle:ve},oe,{prefixCls:E,overlayClassName:V,ref:I,open:B,onOpenChange:$,overlay:ie||N?a.createElement(C.aV,{prefixCls:E,title:ie,content:N}):null,transitionName:(0,k.m)(A,"zoom-big",oe.transitionName),"data-popover-inject":!0}),(0,p.Tm)(ae,{onKeyDown:W=>{var ee,b;a.isValidElement(ae)&&((b=ae==null?void 0:(ee=ae.props).onKeyDown)===null||b===void 0||b.call(ee,W)),J(W)}})))});d._InternalPanelDoNotUseOrYouWillBeFired=C.ZP,D.Z=d},20136:function(Z,D,e){var a=e(14747),_=e(50438),y=e(97414),s=e(79511),w=e(8796),u=e(83559),k=e(83262);const p=C=>{const{componentCls:o,popoverColor:n,titleMinWidth:l,fontWeightStrong:d,innerPadding:c,boxShadowSecondary:I,colorTextHeading:L,borderRadiusLG:M,zIndexPopup:H,titleMarginBottom:q,colorBgElevated:G,popoverBg:re,titleBorderBottom:Q,innerContentPadding:X,titlePadding:ae}=C;return[{[o]:Object.assign(Object.assign({},(0,a.Wf)(C)),{position:"absolute",top:0,left:{_skip_check_:!0,value:0},zIndex:H,fontWeight:"normal",whiteSpace:"normal",textAlign:"start",cursor:"auto",userSelect:"text","--valid-offset-x":"var(--arrow-offset-horizontal, var(--arrow-x))",transformOrigin:["var(--valid-offset-x, 50%)","var(--arrow-y, 50%)"].join(" "),"--antd-arrow-background-color":G,width:"max-content",maxWidth:"100vw","&-rtl":{direction:"rtl"},"&-hidden":{display:"none"},[`${o}-content`]:{position:"relative"},[`${o}-inner`]:{backgroundColor:re,backgroundClip:"padding-box",borderRadius:M,boxShadow:I,padding:c},[`${o}-title`]:{minWidth:l,marginBottom:q,color:L,fontWeight:d,borderBottom:Q,padding:ae},[`${o}-inner-content`]:{color:n,padding:X}})},(0,y.ZP)(C,"var(--antd-arrow-background-color)"),{[`${o}-pure`]:{position:"relative",maxWidth:"none",margin:C.sizePopupArrow,display:"inline-block",[`${o}-content`]:{display:"inline-block"}}}]},x=C=>{const{componentCls:o}=C;return{[o]:w.i.map(n=>{const l=C[`${n}6`];return{[`&${o}-${n}`]:{"--antd-arrow-background-color":l,[`${o}-inner`]:{backgroundColor:l},[`${o}-arrow`]:{background:"transparent"}}}})}},P=C=>{const{lineWidth:o,controlHeight:n,fontHeight:l,padding:d,wireframe:c,zIndexPopupBase:I,borderRadiusLG:L,marginXS:M,lineType:H,colorSplit:q,paddingSM:G}=C,re=n-l,Q=re/2,X=re/2-o,ae=d;return Object.assign(Object.assign(Object.assign({titleMinWidth:177,zIndexPopup:I+30},(0,s.w)(C)),(0,y.wZ)({contentRadius:L,limitVerticalRadius:!0})),{innerPadding:c?0:12,titleMarginBottom:c?0:M,titlePadding:c?`${Q}px ${ae}px ${X}px`:0,titleBorderBottom:c?`${o}px ${H} ${q}`:"none",innerContentPadding:c?`${G}px ${ae}px`:0})};D.Z=(0,u.I$)("Popover",C=>{const{colorBgElevated:o,colorText:n}=C,l=(0,k.mergeToken)(C,{popoverBg:o,popoverColor:n});return[p(l),x(l),(0,_._y)(l,"zoom-big")]},P,{resetStyle:!1,deprecatedTokens:[["width","titleMinWidth"],["minWidth","titleMinWidth"]]})},97435:function(Z,D){function e(a,_){for(var y=Object.assign({},a),s=0;s<_.length;s+=1){var w=_[s];delete y[w]}return y}D.Z=e},46326:function(Z,D,e){var a=e(67294);function _(o,n){return o===n&&(o!==0||1/o===1/n)||o!==o&&n!==n}var y=typeof Object.is=="function"?Object.is:_,s=a.useState,w=a.useEffect,u=a.useLayoutEffect,k=a.useDebugValue;function p(o,n){var l=n(),d=s({inst:{value:l,getSnapshot:n}}),c=d[0].inst,I=d[1];return u(function(){c.value=l,c.getSnapshot=n,x(c)&&I({inst:c})},[o,l,n]),w(function(){return x(c)&&I({inst:c}),o(function(){x(c)&&I({inst:c})})},[o]),k(l),l}function x(o){var n=o.getSnapshot;o=o.value;try{var l=n();return!y(o,l)}catch(d){return!0}}function P(o,n){return n()}var C=typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"?P:p;D.useSyncExternalStore=a.useSyncExternalStore!==void 0?a.useSyncExternalStore:C},98557:function(Z,D,e){Z.exports=e(46326)},81758:function(Z,D,e){e.d(D,{J$:function(){return Xe},ZP:function(){return Ye},kY:function(){return Se}});var a=e(67294),_=e(98557);const y=()=>{},s=y(),w=Object,u=t=>t===s,k=t=>typeof t=="function",p=(t,m)=>ut(ut({},t),m),x=t=>k(t.then),P=new WeakMap;let C=0;const o=t=>{const m=typeof t,h=t&&t.constructor,O=h==Date;let f,v;if(w(t)===t&&!O&&h!=RegExp){if(f=P.get(t),f)return f;if(f=++C+"~",P.set(t,f),h==Array){for(f="@",v=0;v<t.length;v++)f+=o(t[v])+",";P.set(t,f)}if(h==w){f="#";const z=w.keys(t).sort();for(;!u(v=z.pop());)u(t[v])||(f+=v+":"+o(t[v])+",");P.set(t,f)}}else f=O?t.toJSON():m=="symbol"?t.toString():m=="string"?JSON.stringify(t):""+t;return f},n=new WeakMap,l={},d={},c="undefined",I=typeof window!=c,L=typeof document!=c,M=()=>I&&typeof window.requestAnimationFrame!=c,H=(t,m)=>{const h=n.get(t);return[()=>!u(m)&&t.get(m)||l,O=>{if(!u(m)){const f=t.get(m);m in d||(d[m]=f),h[5](m,p(f,O),f||l)}},h[6],()=>!u(m)&&m in d?d[m]:!u(m)&&t.get(m)||l]};let q=!0;const G=()=>q,[re,Q]=I&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[y,y],X=()=>{const t=L&&document.visibilityState;return u(t)||t!=="hidden"},ae=t=>(L&&document.addEventListener("visibilitychange",t),re("focus",t),()=>{L&&document.removeEventListener("visibilitychange",t),Q("focus",t)}),Ee=t=>{const m=()=>{q=!0,t()},h=()=>{q=!1};return re("online",m),re("offline",h),()=>{Q("online",m),Q("offline",h)}},ye={isOnline:G,isVisible:X},xe={initFocus:ae,initReconnect:Ee},ve=!a.useId,oe=!I||"Deno"in window,Fe=t=>M()?window.requestAnimationFrame(t):setTimeout(t,1),E=oe?a.useEffect:a.useLayoutEffect,r=typeof navigator!="undefined"&&navigator.connection,i=!oe&&r&&(["slow-2g","2g"].includes(r.effectiveType)||r.saveData),g=t=>{if(k(t))try{t=t()}catch(h){t=""}const m=t;return t=typeof t=="string"?t:(Array.isArray(t)?t.length:t)?o(t):"",[t,m]};let A=0;const V=()=>++A,B=0,U=1,S=2;var $={__proto__:null,ERROR_REVALIDATE_EVENT:3,FOCUS_EVENT:B,MUTATE_EVENT:S,RECONNECT_EVENT:U};function ie(...t){return pt(this,null,function*(){const[m,h,O,f]=t,v=p({populateCache:!0,throwOnError:!0},typeof f=="boolean"?{revalidate:f}:f||{});let z=v.populateCache;const le=v.rollbackOnError;let Y=v.optimisticData;const je=Re=>typeof le=="function"?le(Re):le!==!1,Me=v.throwOnError;if(k(h)){const Re=h,pe=[],He=m.keys();for(const Pe of He)!/^\$(inf|sub)\$/.test(Pe)&&Re(m.get(Pe)._k)&&pe.push(Pe);return Promise.all(pe.map(Te))}return Te(h);function Te(Re){return pt(this,null,function*(){const[pe]=g(Re);if(!pe)return;const[He,Pe]=H(m,pe),[vt,j,ft,ct]=n.get(m),Qe=()=>{const Ue=vt[pe];return(k(v.revalidate)?v.revalidate(He().data,Re):v.revalidate!==!1)&&(delete ft[pe],delete ct[pe],Ue&&Ue[0])?Ue[0](S).then(()=>He().data):He().data};if(t.length<3)return Qe();let Oe=O,Be;const et=V();j[pe]=[et,0];const ue=!u(Y),tt=He(),Ve=tt.data,nt=tt._c,qe=u(nt)?Ve:nt;if(ue&&(Y=k(Y)?Y(qe,Ve):Y,Pe({data:Y,_c:qe})),k(Oe))try{Oe=Oe(qe)}catch(Ue){Be=Ue}if(Oe&&x(Oe))if(Oe=yield Oe.catch(Ue=>{Be=Ue}),et!==j[pe][0]){if(Be)throw Be;return Oe}else Be&&ue&&je(Be)&&(z=!0,Pe({data:qe,_c:s}));if(z&&!Be)if(k(z)){const Ue=z(Oe,qe);Pe({data:Ue,error:s,_c:s})}else Pe({data:Oe,error:s,_c:s});if(j[pe][1]=V(),Promise.resolve(Qe()).then(()=>{Pe({_c:s})}),Be){if(Me)throw Be;return}return Oe})}})}const N=(t,m)=>{for(const h in t)t[h][0]&&t[h][0](m)},W=(t,m)=>{if(!n.has(t)){const h=p(xe,m),O={},f=ie.bind(s,t);let v=y;const z={},le=(Me,Te)=>{const Re=z[Me]||[];return z[Me]=Re,Re.push(Te),()=>Re.splice(Re.indexOf(Te),1)},Y=(Me,Te,Re)=>{t.set(Me,Te);const pe=z[Me];if(pe)for(const He of pe)He(Te,Re)},je=()=>{if(!n.has(t)&&(n.set(t,[O,{},{},{},f,Y,le]),!oe)){const Me=h.initFocus(setTimeout.bind(s,N.bind(s,O,B))),Te=h.initReconnect(setTimeout.bind(s,N.bind(s,O,U)));v=()=>{Me&&Me(),Te&&Te(),n.delete(t)}}};return je(),[t,f,je,v]}return[t,n.get(t)[4]]},ee=(t,m,h,O,f)=>{const v=h.errorRetryCount,z=f.retryCount,le=~~((Math.random()+.5)*(1<<(z<8?z:8)))*h.errorRetryInterval;!u(v)&&z>v||setTimeout(O,le,f)},b=(t,m)=>o(t)==o(m),[R,F]=W(new Map),T=p({onLoadingSlow:y,onSuccess:y,onError:y,onErrorRetry:ee,onDiscarded:y,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:i?1e4:5e3,focusThrottleInterval:5*1e3,dedupingInterval:2*1e3,loadingTimeout:i?5e3:3e3,compare:b,isPaused:()=>!1,cache:R,mutate:F,fallback:{}},ye),K=(t,m)=>{const h=p(t,m);if(m){const{use:O,fallback:f}=t,{use:v,fallback:z}=m;O&&v&&(h.use=O.concat(v)),f&&z&&(h.fallback=p(f,z))}return h},fe=(0,a.createContext)({}),ce=t=>{const{value:m}=t,h=(0,a.useContext)(fe),O=k(m),f=(0,a.useMemo)(()=>O?m(h):m,[O,h,m]),v=(0,a.useMemo)(()=>O?f:K(h,f),[O,h,f]),z=f&&f.provider,le=(0,a.useRef)(s);z&&!le.current&&(le.current=W(z(v.cache||R),f));const Y=le.current;return Y&&(v.cache=Y[0],v.mutate=Y[1]),E(()=>{if(Y)return Y[2]&&Y[2](),Y[3]},[]),(0,a.createElement)(fe.Provider,p(t,{value:v}))},he="$inf$",_e=I&&window.__SWR_DEVTOOLS_USE__,Ie=_e?window.__SWR_DEVTOOLS_USE__:[],De=()=>{_e&&(window.__SWR_DEVTOOLS_REACT__=a)},me=t=>k(t[1])?[t[0],t[1],t[2]||{}]:[t[0],null,(t[1]===null?t[2]:t[1])||{}],Se=()=>p(T,(0,a.useContext)(fe)),ge=(t,m)=>{const[h,O]=g(t),[,,,f]=n.get(R);if(f[h])return f[h];const v=m(O);return f[h]=v,v},se=t=>(m,h,O)=>t(m,h&&((...v)=>{const[z]=g(m),[,,,le]=n.get(R);if(z.startsWith(he))return h(...v);const Y=le[z];return u(Y)?h(...v):(delete le[z],Y)}),O),Ne=Ie.concat(se),we=t=>function(...h){const O=Se(),[f,v,z]=me(h),le=K(O,z);let Y=t;const{use:je}=le,Me=(je||[]).concat(Ne);for(let Te=Me.length;Te--;)Y=Me[Te](Y);return Y(f,v||le.fetcher||null,le)},Ae=(t,m,h)=>{const O=m[t]||(m[t]=[]);return O.push(h),()=>{const f=O.indexOf(h);f>=0&&(O[f]=O[O.length-1],O.pop())}},be=(t,m)=>(...h)=>{const[O,f,v]=me(h),z=(v.use||[]).concat(m);return t(O,f,Rt(ut({},v),{use:z}))};De();const We=t=>serialize(t)[0],Ge=a.use||(t=>{if(t.status==="pending")throw t;if(t.status==="fulfilled")return t.value;throw t.status==="rejected"?t.reason:(t.status="pending",t.then(m=>{t.status="fulfilled",t.value=m},m=>{t.status="rejected",t.reason=m}),t)}),Je={dedupe:!0},st=(t,m,h)=>{const{cache:O,compare:f,suspense:v,fallbackData:z,revalidateOnMount:le,revalidateIfStale:Y,refreshInterval:je,refreshWhenHidden:Me,refreshWhenOffline:Te,keepPreviousData:Re}=h,[pe,He,Pe,vt]=n.get(O),[j,ft]=g(t),ct=(0,a.useRef)(!1),Qe=(0,a.useRef)(!1),Oe=(0,a.useRef)(j),Be=(0,a.useRef)(m),et=(0,a.useRef)(h),ue=()=>et.current,tt=()=>ue().isVisible()&&ue().isOnline(),[Ve,nt,qe,Ue]=H(O,j),rt=(0,a.useRef)({}).current,kt=u(z)?h.fallback[j]:z,yt=(te,ne)=>{for(const Le in rt){const de=Le;if(de==="data"){if(!f(te[de],ne[de])&&(!u(te[de])||!f(gt,ne[de])))return!1}else if(ne[de]!==te[de])return!1}return!0},xt=(0,a.useMemo)(()=>{const te=!j||!m?!1:u(le)?ue().isPaused()||v?!1:u(Y)?!0:Y:le,ne=ke=>{const Ke=p(ke);return delete Ke._k,te?ut({isValidating:!0,isLoading:!0},Ke):Ke},Le=Ve(),de=Ue(),Ze=ne(Le),lt=Le===de?Ze:ne(de);let Ce=Ze;return[()=>{const ke=ne(Ve());return yt(ke,Ce)?(Ce.data=ke.data,Ce.isLoading=ke.isLoading,Ce.isValidating=ke.isValidating,Ce.error=ke.error,Ce):(Ce=ke,ke)},()=>lt]},[O,j]),at=(0,_.useSyncExternalStore)((0,a.useCallback)(te=>qe(j,(ne,Le)=>{yt(Le,ne)||te()}),[O,j]),xt[0],xt[1]),St=!ct.current,_t=pe[j]&&pe[j].length>0,ot=at.data,it=u(ot)?kt:ot,mt=at.error,Tt=(0,a.useRef)(it),gt=Re?u(ot)?Tt.current:ot:it,Pt=_t&&!u(mt)?!1:St&&!u(le)?le:ue().isPaused()?!1:v?u(it)?!1:Y:u(it)||Y,Ct=!!(j&&m&&St&&Pt),It=u(at.isValidating)?Ct:at.isValidating,Bt=u(at.isLoading)?Ct:at.isLoading,dt=(0,a.useCallback)(te=>pt(this,null,function*(){const ne=Be.current;if(!j||!ne||Qe.current||ue().isPaused())return!1;let Le,de,Ze=!0;const lt=te||{},Ce=!Pe[j]||!lt.dedupe,ke=()=>ve?!Qe.current&&j===Oe.current&&ct.current:j===Oe.current,Ke={isValidating:!1,isLoading:!1},Ft=()=>{nt(Ke)},Dt=()=>{const ze=Pe[j];ze&&ze[1]===de&&delete Pe[j]},Mt={isValidating:!0};u(Ve().data)&&(Mt.isLoading=!0);try{if(Ce&&(nt(Mt),h.loadingTimeout&&u(Ve().data)&&setTimeout(()=>{Ze&&ke()&&ue().onLoadingSlow(j,h)},h.loadingTimeout),Pe[j]=[ne(ft),V()]),[Le,de]=Pe[j],Le=yield Le,Ce&&setTimeout(Dt,h.dedupingInterval),!Pe[j]||Pe[j][1]!==de)return Ce&&ke()&&ue().onDiscarded(j),!1;Ke.error=s;const ze=He[j];if(!u(ze)&&(de<=ze[0]||de<=ze[1]||ze[1]===0))return Ft(),Ce&&ke()&&ue().onDiscarded(j),!1;const $e=Ve().data;Ke.data=f($e,Le)?$e:Le,Ce&&ke()&&ue().onSuccess(Le,j,h)}catch(ze){Dt();const $e=ue(),{shouldRetryOnError:ht}=$e;$e.isPaused()||(Ke.error=ze,Ce&&ke()&&($e.onError(ze,j,$e),(ht===!0||k(ht)&&ht(ze))&&(!ue().revalidateOnFocus||!ue().revalidateOnReconnect||tt())&&$e.onErrorRetry(ze,j,$e,At=>{const bt=pe[j];bt&&bt[0]&&bt[0]($.ERROR_REVALIDATE_EVENT,At)},{retryCount:(lt.retryCount||0)+1,dedupe:!0})))}return Ze=!1,Ft(),!0}),[j,O]),Et=(0,a.useCallback)((...te)=>ie(O,Oe.current,...te),[]);if(E(()=>{Be.current=m,et.current=h,u(ot)||(Tt.current=ot)}),E(()=>{if(!j)return;const te=dt.bind(s,Je);let ne=0;const de=Ae(j,pe,(Ze,lt={})=>{if(Ze==$.FOCUS_EVENT){const Ce=Date.now();ue().revalidateOnFocus&&Ce>ne&&tt()&&(ne=Ce+ue().focusThrottleInterval,te())}else if(Ze==$.RECONNECT_EVENT)ue().revalidateOnReconnect&&tt()&&te();else{if(Ze==$.MUTATE_EVENT)return dt();if(Ze==$.ERROR_REVALIDATE_EVENT)return dt(lt)}});return Qe.current=!1,Oe.current=j,ct.current=!0,nt({_k:ft}),Pt&&(u(it)||oe?te():Fe(te)),()=>{Qe.current=!0,de()}},[j]),E(()=>{let te;function ne(){const de=k(je)?je(Ve().data):je;de&&te!==-1&&(te=setTimeout(Le,de))}function Le(){!Ve().error&&(Me||ue().isVisible())&&(Te||ue().isOnline())?dt(Je).then(ne):ne()}return ne(),()=>{te&&(clearTimeout(te),te=-1)}},[je,Me,Te,j]),(0,a.useDebugValue)(gt),v&&u(it)&&j){if(!ve&&oe)throw new Error("Fallback data is required when using suspense in SSR.");Be.current=m,et.current=h,Qe.current=!1;const te=vt[j];if(!u(te)){const ne=Et(te);Ge(ne)}if(u(mt)){const ne=dt(Je);u(gt)||(ne.status="fulfilled",ne.value=!0),Ge(ne)}else throw mt}return{mutate:Et,get data(){return rt.data=!0,gt},get error(){return rt.error=!0,mt},get isValidating(){return rt.isValidating=!0,It},get isLoading(){return rt.isLoading=!0,Bt}}},Xe=w.defineProperty(ce,"defaultValue",{value:T}),Ye=we(st)}}]);
}());
//# sourceMappingURL=shared-YDNuES5c-HcRSg93VOY6oFDjJRE_.eb531105.async.js.map