"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[2460],{64317:function(X,T,a){var l=a(1413),M=a(45987),o=a(22270),p=a(67294),b=a(66758),m=a(92755),P=a(85893),B=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","showSearch","options"],g=["fieldProps","children","params","proFieldProps","mode","valueEnum","request","options"],S=function(i,f){var O=i.fieldProps,j=i.children,V=i.params,A=i.proFieldProps,R=i.mode,W=i.valueEnum,Z=i.request,N=i.showSearch,y=i.options,U=(0,M.Z)(i,B),G=(0,p.useContext)(b.Z);return(0,P.jsx)(m.Z,(0,l.Z)((0,l.Z)({valueEnum:(0,o.h)(W),request:Z,params:V,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,l.Z)({options:y,mode:R,showSearch:N,getPopupContainer:G.getPopupContainer},O),ref:f,proFieldProps:A},U),{},{children:j}))},E=p.forwardRef(function(d,i){var f=d.fieldProps,O=d.children,j=d.params,V=d.proFieldProps,A=d.mode,R=d.valueEnum,W=d.request,Z=d.options,N=(0,M.Z)(d,g),y=(0,l.Z)({options:Z,mode:A||"multiple",labelInValue:!0,showSearch:!0,suffixIcon:null,autoClearSearchValue:!0,optionLabelProp:"label"},f),U=(0,p.useContext)(b.Z);return(0,P.jsx)(m.Z,(0,l.Z)((0,l.Z)({valueEnum:(0,o.h)(R),request:W,params:j,valueType:"select",filedConfig:{customLightMode:!0},fieldProps:(0,l.Z)({getPopupContainer:U.getPopupContainer},y),ref:i,proFieldProps:V},N),{},{children:O}))}),I=p.forwardRef(S),_=E,C=I;C.SearchSelect=_,C.displayName="ProFormComponent",T.Z=C},90672:function(X,T,a){var l=a(1413),M=a(45987),o=a(67294),p=a(92755),b=a(85893),m=["fieldProps","proFieldProps"],P=function(g,S){var E=g.fieldProps,I=g.proFieldProps,_=(0,M.Z)(g,m);return(0,b.jsx)(p.Z,(0,l.Z)({ref:S,valueType:"textarea",fieldProps:E,proFieldProps:I},_))};T.Z=o.forwardRef(P)},69776:function(X,T,a){var l=a(96486),M=a.n(l),o=a(63686),p=function(){var m=[{value:"created",label:(0,o.oz)("molecules-status.created")},{value:"designing",label:(0,o.oz)("molecules-status.designing")},{value:"synthesizing",label:(0,o.oz)("molecules-status.synthesizing")},{value:"finished",label:(0,o.oz)("component.notification.statusValue.success")},{value:"canceled",label:(0,o.oz)("pages.projectTable.statusLabel.cancelled")}],P=[{value:"product",label:(0,o.oz)("product")},{value:"main_reactant",label:(0,o.oz)("main-reactant")},{value:"reactant",label:(0,o.oz)("reactant")},{value:"other_reagent",label:(0,o.oz)("other-reagent")}],B=[{label:(0,o.oz)("same-key-material"),value:"start_material"},{label:(0,o.oz)("algorithm-cluster"),value:"cluster"},{label:(0,o.oz)("not-grouped"),value:"ungrouped"}],g=[{label:(0,o.oz)("algorithmic-score"),value:"score"},{label:(0,o.oz)("known-reaction-proportion"),value:"known_reaction_rate"},{label:(0,o.oz)("longest-chain-l"),value:"backbone_length"},{label:(0,o.oz)("route-length"),value:"min_n_main_tree_steps"}],S={createdAt:(0,o.oz)("creation-time"),updatedAt:(0,o.oz)("last-update-time"),no:(0,o.oz)("name")},E={target:(0,o.oz)("target-molecules"),building_block:(0,o.oz)("key-intermediate"),temp_block:(0,o.oz)("show-materials")},I=(0,l.omit)(E,"temp_block"),_={onlyOneLineEditorAlertMessage:(0,o.oz)("only-one-edit"),onlyAddOneLineAlertMessage:(0,o.oz)("only-one-added")},C={total_cost:(0,o.oz)("total"),material_cost:(0,o.oz)("material-cost"),labor_cost:(0,o.oz)("labor-cost")},d={draft:(0,o.oz)("draft"),published:(0,o.oz)("in-use"),deleted:(0,o.oz)("deleted")},i={success:(0,o.oz)("pages.experiment.statusLabel.success"),fail:(0,o.oz)("pages.experiment.statusLabel.failed"),processing:(0,o.oz)("pages.experiment.statusLabel.running")},f={limited:(0,o.oz)("component.notification.statusValue.limited"),completed:(0,o.oz)("component.notification.statusValue.success"),running:(0,o.oz)("component.notification.statusValue.running"),pending:(0,o.oz)("component.notification.statusValue.pending"),failed:(0,o.oz)("component.notification.statusValue.failed")},O={working:(0,o.oz)("working"),holding:(0,o.oz)("hold"),error:(0,o.oz)("unavailable"),idle:(0,o.oz)("idle")};return{moleculeStatusOptions:m,reactionRoleOptions:P,groupOptions:B,proportionOptions:g,typeMap:E,sortStandard:S,typeMapForSelect:I,editableConfig:_,chargeDes:C,materialManageStauts:d,aiGenerateStauts:f,aiAIInferenceStauts:i,robotStatus:O}};T.Z=p},66309:function(X,T,a){a.d(T,{Z:function(){return se}});var l=a(67294),M=a(93967),o=a.n(M),p=a(98423),b=a(98787),m=a(69760),P=a(96159),B=a(45353),g=a(53124),S=a(85982),E=a(10274),I=a(14747),_=a(83262),C=a(83559);const d=e=>{const{paddingXXS:n,lineWidth:s,tagPaddingHorizontal:t,componentCls:r,calc:v}=e,c=v(t).sub(s).equal(),$=v(n).sub(s).equal();return{[r]:Object.assign(Object.assign({},(0,I.Wf)(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:c,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${(0,S.unit)(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${r}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${r}-close-icon`]:{marginInlineStart:$,fontSize:e.tagIconSize,color:e.colorTextDescription,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${r}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${r}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:c}}),[`${r}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},i=e=>{const{lineWidth:n,fontSizeIcon:s,calc:t}=e,r=e.fontSizeSM;return(0,_.mergeToken)(e,{tagFontSize:r,tagLineHeight:(0,S.unit)(t(e.lineHeightSM).mul(r).equal()),tagIconSize:t(s).sub(t(n).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},f=e=>({defaultBg:new E.C(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText});var O=(0,C.I$)("Tag",e=>{const n=i(e);return d(n)},f),j=function(e,n){var s={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(s[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(s[t[r]]=e[t[r]]);return s},A=l.forwardRef((e,n)=>{const{prefixCls:s,style:t,className:r,checked:v,onChange:c,onClick:$}=e,z=j(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:K,tag:x}=l.useContext(g.E_),w=H=>{c==null||c(!v),$==null||$(H)},D=K("tag",s),[Q,J,h]=O(D),Y=o()(D,`${D}-checkable`,{[`${D}-checkable-checked`]:v},x==null?void 0:x.className,r,J,h);return Q(l.createElement("span",Object.assign({},z,{ref:n,style:Object.assign(Object.assign({},t),x==null?void 0:x.style),className:Y,onClick:w})))}),R=a(98719);const W=e=>(0,R.Z)(e,(n,s)=>{let{textColor:t,lightBorderColor:r,lightColor:v,darkColor:c}=s;return{[`${e.componentCls}${e.componentCls}-${n}`]:{color:t,background:v,borderColor:r,"&-inverse":{color:e.colorTextLightSolid,background:c,borderColor:c},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}});var Z=(0,C.bk)(["Tag","preset"],e=>{const n=i(e);return W(n)},f);function N(e){return typeof e!="string"?e:e.charAt(0).toUpperCase()+e.slice(1)}const y=(e,n,s)=>{const t=N(s);return{[`${e.componentCls}${e.componentCls}-${n}`]:{color:e[`color${s}`],background:e[`color${t}Bg`],borderColor:e[`color${t}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}};var U=(0,C.bk)(["Tag","status"],e=>{const n=i(e);return[y(n,"success","Success"),y(n,"processing","Info"),y(n,"error","Error"),y(n,"warning","Warning")]},f),G=function(e,n){var s={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.indexOf(t)<0&&(s[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)n.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(s[t[r]]=e[t[r]]);return s};const ee=l.forwardRef((e,n)=>{const{prefixCls:s,className:t,rootClassName:r,style:v,children:c,icon:$,color:z,onClose:K,bordered:x=!0,visible:w}=e,D=G(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:Q,direction:J,tag:h}=l.useContext(g.E_),[Y,H]=l.useState(!0),ie=(0,p.Z)(D,["closeIcon","closable"]);l.useEffect(()=>{w!==void 0&&H(w)},[w]);const oe=(0,b.o2)(z),te=(0,b.yT)(z),k=oe||te,ce=Object.assign(Object.assign({backgroundColor:z&&!k?z:void 0},h==null?void 0:h.style),v),u=Q("tag",s),[de,ue,pe]=O(u),me=o()(u,h==null?void 0:h.className,{[`${u}-${z}`]:k,[`${u}-has-color`]:z&&!k,[`${u}-hidden`]:!Y,[`${u}-rtl`]:J==="rtl",[`${u}-borderless`]:!x},t,r,ue,pe),ae=L=>{L.stopPropagation(),K==null||K(L),!L.defaultPrevented&&H(!1)},[,ge]=(0,m.Z)((0,m.w)(e),(0,m.w)(h),{closable:!1,closeIconRender:L=>{const fe=l.createElement("span",{className:`${u}-close-icon`,onClick:ae},L);return(0,P.wm)(L,fe,F=>({onClick:ne=>{var q;(q=F==null?void 0:F.onClick)===null||q===void 0||q.call(F,ne),ae(ne)},className:o()(F==null?void 0:F.className,`${u}-close-icon`)}))}}),ve=typeof D.onClick=="function"||c&&c.type==="a",re=$||null,Ce=re?l.createElement(l.Fragment,null,re,c&&l.createElement("span",null,c)):c,le=l.createElement("span",Object.assign({},ie,{ref:n,className:me,style:ce}),Ce,ge,oe&&l.createElement(Z,{key:"preset",prefixCls:u}),te&&l.createElement(U,{key:"status",prefixCls:u}));return de(ve?l.createElement(B.Z,{component:"Tag"},le):le)});ee.CheckableTag=A;var se=ee}}]);

//# sourceMappingURL=shared-PxBGGq97v1CfUAvqQNK6Wl3fpQ_.5729fae3.async.js.map