"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[5490],{43008:function(ve,L,e){e.d(L,{Z:function(){return p}});var c=e(1413),f=e(67294),A={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494zM544 472c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v108H372c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h108v108c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V644h108c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V472z"}}]},name:"file-add",theme:"outlined"},y=A,S=e(84089),d=function(v,a){return f.createElement(S.Z,(0,c.Z)((0,c.Z)({},v),{},{ref:a,icon:y}))},T=f.forwardRef(d),p=T},79090:function(ve,L,e){var c=e(1413),f=e(67294),A=e(15294),y=e(84089),S=function(p,ne){return f.createElement(y.Z,(0,c.Z)((0,c.Z)({},p),{},{ref:ne,icon:A.Z}))},d=f.forwardRef(S);L.Z=d},38545:function(ve,L,e){e.d(L,{Z:function(){return p}});var c=e(1413),f=e(67294),A={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"}}]},name:"message",theme:"outlined"},y=A,S=e(84089),d=function(v,a){return f.createElement(S.Z,(0,c.Z)((0,c.Z)({},v),{},{ref:a,icon:y}))},T=f.forwardRef(d),p=T},63434:function(ve,L,e){var c=e(1413),f=e(45987),A=e(22270),y=e(84567),S=e(67294),d=e(90789),T=e(92755),p=e(85893),ne=["options","fieldProps","proFieldProps","valueEnum"],v=S.forwardRef(function(R,Q){var ae=R.options,oe=R.fieldProps,l=R.proFieldProps,w=R.valueEnum,J=(0,f.Z)(R,ne);return(0,p.jsx)(T.Z,(0,c.Z)({ref:Q,valueType:"checkbox",valueEnum:(0,A.h)(w,void 0),fieldProps:(0,c.Z)({options:ae},oe),lightProps:(0,c.Z)({labelFormatter:function(){return(0,p.jsx)(T.Z,(0,c.Z)({ref:Q,valueType:"checkbox",mode:"read",valueEnum:(0,A.h)(w,void 0),filedConfig:{customLightMode:!0},fieldProps:(0,c.Z)({options:ae},oe),proFieldProps:l},J))}},J.lightProps),proFieldProps:l},J))}),a=S.forwardRef(function(R,Q){var ae=R.fieldProps,oe=R.children;return(0,p.jsx)(y.Z,(0,c.Z)((0,c.Z)({ref:Q},ae),{},{children:oe}))}),s=(0,d.G)(a,{valuePropName:"checked"}),pe=s;pe.Group=v,L.Z=pe},81012:function(ve,L,e){e.d(L,{Z:function(){return T}});var c=e(97857),f=e.n(c),A=e(48054),y=e(67294),S=e(85893),d=(0,y.lazy)(function(){return Promise.all([e.e(6049),e.e(6369),e.e(6891)]).then(e.bind(e,99814)).then(function(p){return{default:p.default}})});function T(p){return(0,S.jsx)(y.Suspense,{fallback:(0,S.jsx)("div",{children:(0,S.jsx)(A.Z,{active:!0})}),children:(0,S.jsx)(d,f()({},p))})}},40802:function(ve,L,e){e.d(L,{Z:function(){return pe}});var c=e(81012),f=e(32884),A=e(38545),y=e(4393),S=e(66309),d=e(28036),T=e(55241),p=e(93967),ne=e.n(p),v=e(70831),a={reactionCard:"reactionCard____5fXG",reactionInfo:"reactionInfo___U4SpH",reactionTitle:"reactionTitle___aJZR0",no:"no___Avx3R",title:"title___FMgkA",status:"status___xYs3R",routes:"routes___zdkV6",structure:"structure___CHZWI"},s=e(85893);function pe(R){var Q,ae,oe,l=R.reaction,w=R.isPlayground,J=R.onAction,Me=R.enableToReaction,ye=R.displayProject,Ee=function(se){var Ae=[];return se.map(function(Ie){return Ae.push(Ie==null?void 0:Ie.id)}),Ae},_=(0,v.useParams)(),X=_.id,C=(l==null?void 0:l.commendCount)&&Number(l==null?void 0:l.commendCount)>0,K=C?"".concat((0,f.oz)("comment"),"\uFF08").concat(l==null?void 0:l.commendCount,"\uFF09"):(0,f.oz)("comment");return(0,s.jsxs)(y.Z,{className:a.reactionCard,children:[l!=null&&l.reaction?(0,s.jsx)(c.Z,{structure:l==null?void 0:l.reaction,className:ne()(a.structure,"enablePointer"),clickEvent:Me?function(){var t;v.history.push("/projects/".concat(X||((t=l.project)===null||t===void 0?void 0:t.id),"/reaction/").concat(l==null?void 0:l.id))}:void 0}):"",(0,s.jsxs)("div",{className:a.reactionInfo,children:[(0,s.jsxs)("div",{className:ne()(a.reactionTitle,"flex-justify-space-between"),children:[(0,s.jsxs)("div",{className:ne()(a.no,"flex-justify-space-between"),children:[(0,s.jsxs)("span",{className:a.title,children:[(0,f.oz)("reaction-no"),l==null?void 0:l.id]}),w&&C&&l!==null&&l!==void 0&&l.updatedAt?(0,s.jsxs)("div",{children:["\xA0\xA0",(0,f.oz)("last-comment-date"),":"," ",(0,f.H3)(l==null?void 0:l.updatedAt)]}):"",l!=null&&l.collection_subject?(0,s.jsxs)("div",{children:["\xA0\xA0",(0,f.oz)("reaction-step-ID"),": ",l==null?void 0:l.collection_subject]}):""]}),(0,s.jsxs)("div",{className:a.status,children:[w?"":(0,s.jsx)(s.Fragment,{children:l!=null&&l.progress?(0,s.jsx)(S.Z,{color:"processing",children:(0,f.oz)("proceeded")}):(0,s.jsx)(S.Z,{color:"warning",children:(0,f.oz)("to-be-proceeded")})}),w?(0,s.jsx)(d.ZP,{type:"link",onClick:J,children:(0,s.jsxs)(T.Z,{content:K,children:[(0,s.jsx)(A.Z,{}),C?"\uFF08".concat(l==null?void 0:l.commendCount,"\uFF09"):""]})}):""]})]}),(0,s.jsxs)("div",{className:"display-flex",children:[(0,s.jsxs)("div",{children:[(0,f.oz)("nums-of-my-reactions"),"\uFF1A",l!=null&&(Q=l.effective_procedures)!==null&&Q!==void 0&&Q.length?(0,s.jsx)("span",{className:"enablePointer",onClick:function(){var se;return v.history.push("/projects/".concat(X||((se=l.project)===null||se===void 0?void 0:se.id),"/reaction/").concat(l==null?void 0:l.id,"?tab=my-reaction-design"))},children:l==null||(ae=l.effective_procedures)===null||ae===void 0?void 0:ae.length}):0]}),"\xA0\xA0\xA0",(0,s.jsxs)("div",{children:[(0,f.oz)("nums-of-my-experiments"),"\uFF1A",l!=null&&l.experiment_count?(0,s.jsx)("span",{className:"enablePointer",style:{color:"black"},onClick:function(){var se;return v.history.push("/projects/".concat(X||((se=l.project)===null||se===void 0?void 0:se.id),"/reaction/").concat(l==null?void 0:l.id,"?tab=my-experiment"))},children:l==null?void 0:l.experiment_count}):0]})]}),w?"":(0,s.jsxs)(s.Fragment,{children:[ye&&(0,s.jsxs)("div",{className:"flex-align-items-center",children:[(0,s.jsxs)("div",{children:[(0,f.oz)("menu.list.project-list"),"\uFF1A"]}),(0,s.jsx)("div",{style:{color:"black"},children:(oe=l.project)===null||oe===void 0?void 0:oe.no})]}),(0,s.jsxs)("div",{className:"display-flex",children:[(0,f.oz)("associated-routes"),"\uFF1A",(0,s.jsx)("span",{className:a.routes,children:(0,f.qt)(l==null?void 0:l.project_routes)?Ee(l==null?void 0:l.project_routes).join("\u3001"):"\u65E0"})]})]})]})]})}},33547:function(ve,L,e){var c=e(5574),f=e.n(c),A=e(79090),y=e(96486),S=e.n(y),d=e(67294),T=e(85893),p=function(v){var a=v.title,s=v.getNumber,pe=v.refetchEvent,R=(0,d.useState)(!1),Q=f()(R,2),ae=Q[0],oe=Q[1],l=(0,d.useState)(),w=f()(l,2),J=w[0],Me=w[1];return(0,d.useEffect)(function(){var ye=!1;if(s)return Promise.resolve(s()).then(function(Ee){return!ye&&Me((0,y.isNil)(Ee)?void 0:Ee)}).finally(function(){return!ye&&oe(!1)}),function(){ye=!0}},[s,pe]),(0,T.jsxs)(T.Fragment,{children:[a,ae?(0,T.jsx)(A.Z,{}):(0,y.isNil)(J)?"":"(".concat(J,")")]})};L.Z=p},54453:function(ve,L,e){e.d(L,{Z:function(){return A}});var c=e(32884),f=e(85893);function A(y){var S={fte:(0,c.oz)("pages.projectTable.typeValue.fte"),ffs:(0,c.oz)("pages.projectTable.typeValue.ffs"),personal:(0,c.oz)("pages.projectTable.typeValue.personal"),designing:(0,c.oz)("molecules-status.designing"),synthesizing:(0,c.oz)("molecules-status.synthesizing"),created:(0,c.oz)("pages.projectTable.statusLabel.created"),started:(0,c.oz)("pages.projectTable.statusLabel.started"),finished:(0,c.oz)("component.notification.statusValue.success"),holding:(0,c.oz)("pages.projectTable.statusLabel.holding"),cancelled:(0,c.oz)("pages.projectTable.statusLabel.cancelled"),canceled:(0,c.oz)("pages.projectTable.statusLabel.cancelled")},d=y.word;return(0,f.jsx)(f.Fragment,{children:S["".concat(d)]||""})}},42311:function(ve,L,e){e.r(L),e.d(L,{default:function(){return Bt}});var c=e(64599),f=e.n(c),A=e(97857),y=e.n(A),S=e(15009),d=e.n(S),T=e(99289),p=e.n(T),ne=e(5574),v=e.n(ne),a=e(32884),s=e(70831),pe=e(3671),R=e(40802),Q=e(30042),ae=e(2390),oe=e(34994),l=e(24739),w=e(64317),J=e(71230),Me=e(15746),ye=e(72252),Ee=e(93967),_=e.n(Ee),X=e(96486),C=e(67294),K={detail:"detail___RQSXl",projectInfo:"projectInfo___lV5kp",operate:"operate___q0hc4",offersButton:"offersButton___STLVC",infoContent:"infoContent___NR0z4",infoItem:"infoItem___PZQwV",label:"label___vvjaj",value:"value___SKg3e",cardContent:"cardContent___QIt0S",pagination:"pagination___fr4uC",reaction:"reaction___yoLvj"},t=e(85893);function se(n){var m,h=n.reactionList,W=n.reactionTotal,B=n.changeValues,r=n.pagenate,re=n.paginationChange,me=n.restData,Y=n.isLoading,D=(0,C.useRef)(),o=(0,s.useModel)("molecule"),I=o.targetMoleculeList,F=(0,s.useModel)("routes"),N=F.routesOption,j=!(0,X.isEmpty)(h)&&(0,X.isArray)(h),fe=(0,ae.D)({suffix:"reaction"}),k=v()(fe,2),b=k[0],Ce=k[1];return(m=D.current)===null||m===void 0||m.setFieldsValue(b()),(0,t.jsxs)("div",{className:K.reaction,children:[(0,t.jsx)(oe.A,{style:{height:"32px"},layout:"horizontal",formRef:D,submitter:!1,onValuesChange:p()(d()().mark(function z(){var ie,V;return d()().wrap(function(H){for(;;)switch(H.prev=H.next){case 0:return H.next=2,D==null||(ie=D.current)===null||ie===void 0?void 0:ie.getFieldsValue();case 2:V=H.sent,Ce(V),B(V);case 5:case"end":return H.stop()}},z)})),children:(0,t.jsxs)(l.UW,{children:[(0,t.jsx)(w.Z,{name:"compounds",label:(0,a.oz)("target-molecules"),mode:"multiple",showSearch:!0,onChange:function(ie){var V;(0,X.isNil)(ie)&&me(),D==null||(V=D.current)===null||V===void 0||V.setFieldValue("routes",void 0)},options:I}),(0,t.jsx)(w.Z,{name:"routes",mode:"multiple",label:(0,a.oz)("Routes"),showSearch:!0,options:N})]})}),(0,t.jsx)("div",{className:_()(K.cardContent,"noPaddingCard",{"flex-center":Y}),children:Y?(0,t.jsx)("div",{className:"loadingPage",children:(0,t.jsx)(pe.Z,{})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(J.Z,{children:j?h==null?void 0:h.map(function(z){return(0,t.jsx)(Me.Z,{sm:24,md:12,children:(0,t.jsx)(R.Z,{reaction:z,enableToReaction:!0})},z==null?void 0:z.id)}):(0,t.jsx)(Q.Z,{des:(0,a.oz)("noticeIcon.empty"),wrapperClassName:"fullLayoutContent"})}),j&&(0,t.jsx)(ye.Z,{className:_()(K.pagination,"flex-justfy-content-end"),total:W,current:r==null?void 0:r.page,pageSize:r==null?void 0:r.pageSize,showSizeChanger:!1,onChange:re})]})})]})}var Ae=e(48632),Ie=e(37507),Le=e(87172),rt=e(11774),dt=e(77598),ut=e(31418),ct=e(48096),vt=e(85673),be={"page-header-root":"page-header-root___TXaUu",leftSide:"leftSide___iks78",rightSide:"rightSide___wOw8V"},mt=function(m){var h=m.leftSlot,W=m.rightSlot,B=m.breadcrumbs,r=m.minRightWidth;return!h&&B&&(h=(0,t.jsx)(vt.Z,{items:B})),(0,t.jsxs)("div",{className:be["page-header-root"],children:[(0,t.jsx)("div",{className:be.leftSide,children:h}),(0,t.jsx)("div",{className:be.rightSide,style:{minWidth:r?"".concat(r,"px"):"unset"},children:W})]})},ft=mt,gt=e(9783),ht=e.n(gt),jt=e(39175),Pe=e(74656),Ke={sortButton:"sortButton___z6ufX",antRotate:"antRotate___jMo_X",sortButton_des:"sortButton_des___tdcSK"};function pt(n){var m=(0,C.useState)(n==null?void 0:n.isAsc),h=v()(m,2),W=h[0],B=h[1];return(0,t.jsxs)("div",{className:_()(n==null?void 0:n.wrapClassName,"flex-align-items-center"),children:[n!=null&&n.isForm?(0,t.jsx)(w.Z,{name:"sort",valueEnum:n==null?void 0:n.valueEnum,style:{width:"".concat(n==null?void 0:n.width,"px")||0},placeholder:(0,a.oz)("select-tip"),allowClear:!1}):(0,t.jsx)(Pe.Z,{onChange:n==null?void 0:n.sortFiledChange,defaultValue:n==null?void 0:n.defaultSortFiled,options:n==null?void 0:n.valueEnum,style:{width:"".concat(n==null?void 0:n.width,"px")||0},placeholder:(0,a.oz)("select-tip"),allowClear:!1}),(0,t.jsx)(jt.r,{className:_()(Ke.sortButton,ht()({},Ke.sortButton_des,!W)),width:25,style:{marginTop:n!=null&&n.isForm?"-22px":"0px"},onClick:function(){n==null||n.handleSort(W?"desc":"asc"),B(!W)}})]})}var De=e(69776),xt=e(43008),Mt=e(63434),Ze=e(28036),yt=e(81012),Ye=e(43275),Ct=e(4393),St=e(71471),Nt=e(66309),Re=e(45360),O={moleculeCard:"moleculeCard___gpp97",desItem:"desItem___HabNs",routesAmount:"routesAmount___iomcg",moleculeNo:"moleculeNo___DGJht",routesNum:"routesNum___TXcgR",labelItem:"labelItem___owpYC",labelItem_en:"labelItem_en___KVb7Z",valueItem:"valueItem___YS3Fb",normalText:"normalText___vrSa5",created:"created___V5IBs",designing:"designing___Ypivk",synthesizing:"synthesizing___TD6F4",finished:"finished___feWgy",canceled:"canceled___R3zCp"};function Et(n){var m=n.detailData,h=(0,s.useModel)("project"),W=h.userList,B=(0,s.useParams)(),r=B.id,re=(0,De.Z)(),me=re.moleculeStatusOptions,Y=re.typeMap,D=function(I){function F(N){return me.filter(function(j){return N.includes(j.value)})}switch(I){case"created":return F(["created","designing","canceled"]);case"designing":return F(["designing","canceled","synthesizing"]);case"synthesizing":return F(["synthesizing","canceled","finished"]);case"finished":return F(["finished"]);default:return F(["canceled"])}return me};return(0,t.jsx)("div",{children:(0,t.jsx)(J.Z,{gutter:16,children:m.map(function(o,I){var F;return(0,t.jsx)(Me.Z,{lg:6,md:8,className:_()(O.moleculeCard),children:(0,t.jsxs)(Ct.Z,{className:_()("enablePointer",o.status?O[o.status]:null),bordered:!1,onClick:function(){return s.history.push("/projects/".concat(r,"/compound/").concat(o==null?void 0:o.id,"?page=1&pageSize=10"))},children:[(0,t.jsx)(yt.Z,{structure:(o==null||(F=o.compound)===null||F===void 0?void 0:F.smiles)||"",className:_()(O.structure,"enablePointer"),height:300}),(0,t.jsxs)("div",{className:_()("flex-justify-space-between flex-align-items-center",O.desItem),children:[(0,t.jsx)(St.Z.Text,{style:{width:172},className:O.moleculeNo,ellipsis:{tooltip:o==null?void 0:o.no},children:o==null?void 0:o.no}),(0,t.jsx)(Nt.Z,{color:"green",children:Y[o==null?void 0:o.type]})]}),(0,t.jsxs)("div",{className:_()(O.routesNum,O.labelItem,"display-flex"),children:[(0,t.jsxs)("div",{children:[(0,a.oz)("aiGenerated")," ",(o==null?void 0:o.retro_backbones_number)||0," ",(0,a.Ig)()?"":"\u6761"]}),"\xA0\xA0\u2022\xA0\xA0",(0,t.jsxs)("div",{children:[(0,a.oz)("myRoutes")," ",(o==null?void 0:o.project_routes_number)||0," ",(0,a.Ig)()?"":"\u6761"]})]}),(0,a.Ig)()?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:_()("flex-align-items-center",O.desItem),onClick:function(j){return j.stopPropagation()},children:[(0,t.jsx)("div",{className:O.labelItem_en,children:(0,a.oz)("status")}),"\xA0",(0,t.jsx)("div",{className:O.normalText,style:{width:"100%"},children:(0,t.jsx)(Pe.Z,{size:"small",defaultValue:o==null?void 0:o.status,style:{width:"100%"},onChange:function(j){return n==null?void 0:n.statusChange(j,o==null?void 0:o.id)},options:D(o==null?void 0:o.status)})})]}),(0,t.jsxs)("div",{className:_()("flex-align-items-center",O.desItem),onClick:function(j){return j.stopPropagation()},children:[(0,t.jsx)("div",{className:O.labelItem_en,children:(0,a.oz)("Priority")}),"\xA0",(0,t.jsx)("div",{className:O.normalText,style:{width:"100%"},children:(0,t.jsx)(Pe.Z,{size:"small",disabled:n==null?void 0:n.disabled,defaultValue:o==null?void 0:o.priority,style:{width:"100%"},onChange:function(j){return n==null?void 0:n.priorityChange(j,o==null?void 0:o.id)},options:Ye.jY})})]})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(J.Z,{className:_()(O.desItem),onClick:function(j){return j.stopPropagation()},children:[(0,t.jsx)("div",{className:O.labelItem,children:(0,a.oz)("status")}),"\xA0",(0,t.jsx)(Pe.Z,{size:"small",defaultValue:o==null?void 0:o.status,style:{width:(0,a.Ig)()?"100%":"130px"},onChange:function(j){return n==null?void 0:n.statusChange(j,o==null?void 0:o.id)},options:D(o==null?void 0:o.status)}),"\xA0"]}),(0,t.jsxs)(J.Z,{className:_()(O.desItem),onClick:function(j){return j.stopPropagation()},children:[(0,t.jsx)("div",{className:O.labelItem,children:(0,a.oz)("Priority")}),"\xA0",(0,t.jsx)(Pe.Z,{size:"small",disabled:n==null?void 0:n.disabled,defaultValue:o==null?void 0:o.priority,style:{width:(0,a.Ig)()?"100%":"130px"},onChange:function(j){return n==null?void 0:n.priorityChange(j,o==null?void 0:o.id)},options:Ye.jY})]})]}),(0,t.jsxs)("div",{className:_()("flex-align-items-center",O.desItem),onClick:function(j){return j.stopPropagation()},children:[(0,t.jsx)("div",{className:(0,a.Ig)()?O.labelItem_en:O.labelItem,style:{display:"flex"},children:(0,a.oz)("pages.experiment.label.owner")}),"\xA0",(0,t.jsx)("div",{className:O.normalText,style:{width:"100%"},children:(0,t.jsx)(Pe.Z,{size:"small",defaultValue:o==null?void 0:o.director_id,style:{width:(0,a.Ig)()?"100%":"130px"},onChange:function(){var N=p()(d()().mark(function j(fe){var k,b;return d()().wrap(function(z){for(;;)switch(z.prev=z.next){case 0:return z.next=2,(0,Le.service)("project-compounds").update(o==null?void 0:o.id,{director_id:fe});case 2:k=z.sent,b=k.error,b?Re.ZP.error(b==null?void 0:b.message):Re.ZP.success((0,a.oz)("success-update"));case 5:case"end":return z.stop()}},j)}));return function(j){return N.apply(this,arguments)}}(),options:W})})]})]})},"".concat(I,"-detailCard"))})})})}var It=e(45117);function Pt(n){var m,h=(0,De.Z)(),W=h.sortStandard,B=h.typeMapForSelect,r=n.projectDetail,re=n.isLoading,me=n.moleculeTotal,Y=n.moleculePagenate,D=n.statusChange,o=n.projectInfo,I=n.priorityChange,F=n.formValue,N=n.changeValues,j=n.paginationChange,fe=n.canAddMolecule,k=n.handleUpdate,b=n.update,Ce=n.userList,z=(0,s.useAccess)(),ie=(0,s.useParams)(),V=ie.id,q=(0,De.Z)(),H=q.moleculeStatusOptions,xe=function(){return s.history.push("/projects/".concat(V,"/add-molecule"))},$=(0,C.useRef)(),Te=(0,s.useModel)("molecule"),ge=Te.moleculeList,Oe=!(0,X.isEmpty)(r)&&(0,X.isArray)(r);return(0,t.jsxs)("div",{className:K==null?void 0:K.detail,children:[(0,t.jsx)(ft,{minRightWidth:145,leftSlot:(0,t.jsx)(oe.A,{layout:"horizontal",formRef:$,submitter:!1,onValuesChange:p()(d()().mark(function he(){var de,ee;return d()().wrap(function(le){for(;;)switch(le.prev=le.next){case 0:ee=$==null||(de=$.current)===null||de===void 0?void 0:de.getFieldsValue(),N(ee);case 2:case"end":return le.stop()}},he)})),initialValues:F,children:(0,t.jsxs)(l.UW,{children:[(0,t.jsx)(w.Z,{name:"no",label:(0,a.oz)("molecules-no"),showSearch:!0,options:ge}),(0,t.jsx)(Mt.Z.Group,{name:"status",label:(0,a.oz)("molecules-status"),options:H}),(0,t.jsx)(w.Z,{name:"director_id",label:(0,a.oz)("pages.experiment.label.owner"),showSearch:!0,options:Ce}),(0,t.jsx)(w.Z,{name:"type",width:150,label:(0,a.oz)("molecules-type"),valueEnum:B,placeholder:(0,a.oz)("select-tip")}),(0,t.jsx)(pt,{valueEnum:W,handleSort:function(){var he=p()(d()().mark(function de(ee){var je,le;return d()().wrap(function(Se){for(;;)switch(Se.prev=Se.next){case 0:return Se.next=2,$==null||(je=$.current)===null||je===void 0?void 0:je.getFieldsValue();case 2:le=Se.sent,N(y()(y()({},le),{},{order:ee}));case 4:case"end":return Se.stop()}},de)}));return function(de){return he.apply(this,arguments)}}(),isAsc:F.order==="asc",isForm:!0})]})}),rightSlot:fe&&(z==null||(m=z.authCodeList)===null||m===void 0?void 0:m.includes("projects.button.add-molecule"))&&(0,t.jsx)(Ze.ZP,{type:"primary",shape:"round",icon:(0,t.jsx)(xt.Z,{}),onClick:xe,children:(0,a.oz)("menu.list.project-list.detail.addMolecule")})}),b&&(0,a.u9)(b==null?void 0:b.status)&&(0,t.jsx)(It.Z,{operateTargetName:(0,a.oz)("molecules"),status:b.status,trigger:{open:!!b},onCancel:function(){return k==null?void 0:k()},onFinished:function(){var he=p()(d()().mark(function de(ee){return d()().wrap(function(le){for(;;)switch(le.prev=le.next){case 0:return le.abrupt("return",k(b.id,{status:ee==null?void 0:ee.status,status_update_note:ee==null?void 0:ee.status_update_note}));case 1:case"end":return le.stop()}},de)}));return function(de){return he.apply(this,arguments)}}()}),Oe||re?re?(0,t.jsx)("div",{className:"loadingPage",style:{minHeight:"400px"},children:(0,t.jsx)(pe.Z,{})}):(0,t.jsx)(Et,{detailData:r,statusChange:D,disabled:(0,a.u9)(o==null?void 0:o.status),priorityChange:I}):(0,t.jsx)(Q.Z,{des:(0,a.oz)("add-molecule-tip"),clickEvent:xe,wrapperClassName:"fullLayoutContent"}),Oe&&(0,t.jsx)(ye.Z,{className:_()(K.pagination,"flex-justfy-content-end"),total:me,current:Y==null?void 0:Y.page,pageSize:Y==null?void 0:Y.pageSize,showSizeChanger:!1,onChange:j})]})}var zt=Object.defineProperty,He=Object.getOwnPropertySymbols,Lt=Object.prototype.hasOwnProperty,At=Object.prototype.propertyIsEnumerable,Ge=(n,m,h)=>m in n?zt(n,m,{enumerable:!0,configurable:!0,writable:!0,value:h}):n[m]=h,Tt=(n,m)=>{for(var h in m||(m={}))Lt.call(m,h)&&Ge(n,h,m[h]);if(He)for(var h of He(m))At.call(m,h)&&Ge(n,h,m[h]);return n};const Ot=n=>C.createElement("svg",Tt({className:"money_svg__icon",viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg"},n),C.createElement("path",{d:"M512 97.524c228.913 0 414.476 185.563 414.476 414.476S740.913 926.476 512 926.476 97.524 740.913 97.524 512 283.087 97.524 512 97.524zm0 73.143c-188.514 0-341.333 152.82-341.333 341.333S323.487 853.333 512 853.333 853.333 700.513 853.333 512 700.513 170.667 512 170.667zm96.061 120.442 51.688 51.687-96.013 96.061h131.121V512H548.571v60.952h146.286v73.143H548.571v97.524H475.43v-97.524H329.143v-73.143h146.286V512H329.143v-73.143h131.145l-96.061-96.06 51.736-51.688L512 387.12l96.061-96.061z"}));var mn="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNTEyIDk3LjUyNGMyMjguOTEzIDAgNDE0LjQ3NiAxODUuNTYzIDQxNC40NzYgNDE0LjQ3NlM3NDAuOTEzIDkyNi40NzYgNTEyIDkyNi40NzYgOTcuNTI0IDc0MC45MTMgOTcuNTI0IDUxMiAyODMuMDg3IDk3LjUyNCA1MTIgOTcuNTI0em0wIDczLjE0M2MtMTg4LjUxNCAwLTM0MS4zMzMgMTUyLjgyLTM0MS4zMzMgMzQxLjMzM1MzMjMuNDg3IDg1My4zMzMgNTEyIDg1My4zMzMgODUzLjMzMyA3MDAuNTEzIDg1My4zMzMgNTEyIDcwMC41MTMgMTcwLjY2NyA1MTIgMTcwLjY2N3ptOTYuMDYxIDEyMC40NDIgNTEuNjg4IDUxLjY4Ny05Ni4wMTMgOTYuMDYxaDEzMS4xMjFWNTEySDU0OC41NzF2NjAuOTUyaDE0Ni4yODZ2NzMuMTQzSDU0OC41NzF2OTcuNTI0SDQ3NS40M3YtOTcuNTI0SDMyOS4xNDN2LTczLjE0M2gxNDYuMjg2VjUxMkgzMjkuMTQzdi03My4xNDNoMTMxLjE0NWwtOTYuMDYxLTk2LjA2IDUxLjczNi01MS42ODhMNTEyIDM4Ny4xMmw5Ni4wNjEtOTYuMDYxeiIvPjwvc3ZnPg==",Qe=e(54453),Je=e(1413),_t={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512.5 390.6c-29.9 0-57.9 11.6-79.1 32.8-21.1 21.2-32.8 49.2-32.8 79.1 0 29.9 11.7 57.9 32.8 79.1 21.2 21.1 49.2 32.8 79.1 32.8 29.9 0 57.9-11.7 79.1-32.8 21.1-21.2 32.8-49.2 32.8-79.1 0-29.9-11.7-57.9-32.8-79.1a110.96 110.96 0 00-79.1-32.8zm412.3 235.5l-65.4-55.9c3.1-19 4.7-38.4 4.7-57.7s-1.6-38.8-4.7-57.7l65.4-55.9a32.03 32.03 0 009.3-35.2l-.9-2.6a442.5 442.5 0 00-79.6-137.7l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.2 28.9c-30-24.6-63.4-44-99.6-57.5l-15.7-84.9a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52-9.4-106.8-9.4-158.8 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.3a353.44 353.44 0 00-98.9 57.3l-81.8-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a445.93 445.93 0 00-79.6 137.7l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.2 56.5c-3.1 18.8-4.6 38-4.6 57 0 19.2 1.5 38.4 4.6 57l-66 56.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.3 44.8 96.8 79.6 137.7l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.8-29.1c29.8 24.5 63 43.9 98.9 57.3l15.8 85.3a32.05 32.05 0 0025.8 25.7l2.7.5a448.27 448.27 0 00158.8 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-84.9c36.2-13.6 69.6-32.9 99.6-57.5l81.2 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.5-87.4 79.6-137.7l.9-2.6c4.3-12.4.6-26.3-9.5-35zm-412.3 52.2c-97.1 0-175.8-78.7-175.8-175.8s78.7-175.8 175.8-175.8 175.8 78.7 175.8 175.8-78.7 175.8-175.8 175.8z"}}]},name:"setting",theme:"filled"},bt=_t,Dt=e(84089),Zt=function(m,h){return C.createElement(Dt.Z,(0,Je.Z)((0,Je.Z)({},m),{},{ref:h,icon:bt}))},Rt=C.forwardRef(Zt),Ft=Rt,Ut=e(42075),wt=e(97015);function Wt(n){var m,h,W,B=(0,s.useModel)("project"),r=B.projectInfo,re=B.getProjectInfo,me=(0,s.useParams)(),Y=me.id,D=function(){return s.history.push("/projects/".concat(Y,"/quotation-records"))},o=function(q){var H=q.label,xe=q.value;return(0,t.jsxs)(Me.Z,{span:6,className:_()("flex-align-items-center",K.infoItem),children:[(0,t.jsx)("span",{className:K.label,style:{minWidth:(0,a.Ig)()?"80px":"60px"},children:H}),(0,t.jsx)("span",{className:K.value,children:xe})]})},I=(0,s.useAccess)(),F=(0,C.useState)(),N=v()(F,2),j=N[0],fe=N[1],k=(0,C.useState)(!1),b=v()(k,2),Ce=b[0],z=b[1],ie=function(){var V=p()(d()().mark(function q(){var H,xe,$;return d()().wrap(function(ge){for(;;)switch(ge.prev=ge.next){case 0:return ge.next=2,(0,Le.service)("projects").selectManyByID([Y]).populateWith("project_members",["user_id","role"],!0).get();case 2:H=ge.sent,xe=H.data,$=H.error,$?Re.ZP.error($==null?void 0:$.message):(fe(xe[0]),z(!0));case 6:case"end":return ge.stop()}},q)}));return function(){return V.apply(this,arguments)}}();return(0,t.jsxs)("div",{className:K.projectInfo,children:[(0,t.jsxs)("div",{className:_()("commonContainer","flex-justify-space-between",K.operate),children:[(0,t.jsx)("h1",{children:(0,a.oz)("project-details")}),(0,t.jsxs)(Ut.Z,{children:[I!=null&&(m=I.authCodeList)!==null&&m!==void 0&&m.includes("project.button.settings")?(0,t.jsx)(Ze.ZP,{icon:(0,t.jsx)(Ft,{}),onClick:ie,children:(0,a.oz)("pages.project.settings")}):"",I!=null&&(h=I.authCodeList)!==null&&h!==void 0&&h.includes("project.button.quote")&&n!==null&&n!==void 0&&(W=n.projectDetail)!==null&&W!==void 0&&W.length?(0,t.jsx)(Ze.ZP,{onClick:D,icon:(0,t.jsx)(Ot,{width:18}),className:K.offersButton,children:(0,a.oz)("menu.list.project-list.quotation")}):""]})]}),(0,t.jsx)(wt.Z,{projectId:(j==null?void 0:j.id)||0,refreshEvent:function(){return re(String(j==null?void 0:j.id))},modelProps:{open:Ce,onCancel:function(){var q;z(!1),n==null||(q=n.onUpdate)===null||q===void 0||q.call(n)}}},"config"),(0,t.jsxs)(J.Z,{className:_()(K.infoContent,"commonContainer"),children:[(0,t.jsx)(o,{label:(0,a.oz)("project-ID"),value:r==null?void 0:r.no}),(0,t.jsx)(o,{label:(0,a.oz)("project-name"),value:r==null?void 0:r.name}),(0,a.SV)()?null:(0,t.jsx)(o,{label:(0,a.oz)("pages.projectTable.label.customer"),value:r==null?void 0:r.customer}),(0,t.jsx)(o,{label:(0,a.oz)("project-type"),value:(0,t.jsx)(Qe.Z,{word:r==null?void 0:r.type})}),(0,t.jsx)(o,{label:(0,a.oz)("project-status"),value:(0,t.jsx)(Qe.Z,{word:r==null?void 0:r.status})}),(0,t.jsx)(o,{label:(0,a.oz)("pages.projectTable.label.pm"),value:r!=null&&r.PM?r==null?void 0:r.PM.split("@")[0]:r==null?void 0:r.PM}),(0,t.jsx)(o,{label:(0,a.oz)("project-delivery-date"),value:(0,a.$Y)(r==null?void 0:r.delivery_date)})]})]})}function Bt(){var n=(0,s.useModel)("project"),m=n.projectInfo,h=n.getProjectInfo,W=n.userList,B=(0,s.useModel)("molecule"),r=B.queryMoleculeList,re=B.queryTargetMoleculeList,me=B.targetMoleculeList,Y=(0,s.useModel)("routes"),D=Y.queryRoutesOption,o=(0,s.useParams)(),I=o.id,F=ut.Z.useApp(),N=F.message,j=(0,C.useState)(0),fe=v()(j,2),k=fe[0],b=fe[1],Ce=(0,C.useState)([]),z=v()(Ce,2),ie=z[0],V=z[1],q=(0,C.useState)(null),H=v()(q,2),xe=H[0],$=H[1],Te=(0,C.useState)(!1),ge=v()(Te,2),Oe=ge[0],he=ge[1],de=(0,C.useState)({page:1,pageSize:12}),ee=v()(de,2),je=ee[0],le=ee[1],ze=function(){var Z=p()(d()().mark(function u(i){var g,M,P,U,x;return d()().wrap(function(E){for(;;)switch(E.prev=E.next){case 0:return he(!0),E.next=3,(0,Le.query)("project-compounds").filterDeep("project.id","eq",I).populateWith("project_routes",["id"]).populateDeep([{path:"retro_processes",fields:["id"],children:[{key:"retro_backbones",fields:["id"]}]}]).populateWith("compound",["smiles"]).notEqualTo("type","temp_block").paginate(je.page,je.pageSize);case 3:return M=E.sent,i!=null&&i.sort&&(M=M.sortBy([{field:i==null?void 0:i.sort,order:i==null?void 0:i.order}])),i!=null&&i.no&&(M=M.equalTo("no",i==null?void 0:i.no)),i!=null&&i.status&&M.filterDeep("status","in",i==null?void 0:i.status),i!=null&&i.type&&(M=M.equalTo("type",i==null?void 0:i.type)),i!=null&&i.director_id&&(M=M.equalTo("director_id",i==null?void 0:i.director_id)),E.next=11,M.get();case 11:P=E.sent,U=P.data,x=P.meta,U==null||U.forEach(function(G){var ce,$e;G.project_routes_number=(ce=G.project_routes)===null||ce===void 0?void 0:ce.length,G.retro_backbones_number=($e=G.retro_processes)===null||$e===void 0?void 0:$e.flatMap(function(vn){return vn.retro_backbones}).length}),V(U),b((x==null||(g=x.pagination)===null||g===void 0?void 0:g.total)||0),he(!1);case 18:case"end":return E.stop()}},u)}));return function(i){return Z.apply(this,arguments)}}(),Se=(0,C.useState)("molecule"),Xe=v()(Se,2),Fe=Xe[0],Vt=Xe[1],$t=(0,C.useState)(0),ke=v()($t,2),Kt=ke[0],Yt=ke[1],Ht=(0,C.useState)([]),qe=v()(Ht,2),Gt=qe[0],et=qe[1],Qt=(0,C.useState)(!1),tt=v()(Qt,2),Jt=tt[0],Ue=tt[1],Xt=(0,ae.D)(),nt=v()(Xt,2),kt=nt[0],qt=nt[1],en=(0,C.useState)(y()(y()({status:["created","designing","synthesizing","finished"]},kt()),{},{sort:"createdAt",order:"desc"})),at=v()(en,2),Ne=at[0],tn=at[1],we=(0,s.useAccess)(),nn=(0,C.useState)([]),ot=v()(nn,2),We=ot[0],_e=ot[1],an=(0,C.useState)({page:1,pageSize:10}),lt=v()(an,2),te=lt[0],on=lt[1],it=function(u){et(u),Ue(!1)},ln=function(){var Z=p()(d()().mark(function u(i){var g,M,P;return d()().wrap(function(x){for(;;)switch(x.prev=x.next){case 0:return g=(0,X.cloneDeep)(i),x.next=3,(0,Ie.pT)({data:{ids:g.map(function(ue){return ue.id})}});case 3:if(M=x.sent,!(0,Ie.y6)(M).ok){x.next=8;break}if((0,a.qt)(M==null||(P=M.data)===null||P===void 0?void 0:P.data)){x.next=7;break}return x.abrupt("return",it([]));case 7:g.map(function(ue){var E,G=M==null||(E=M.data)===null||E===void 0?void 0:E.data.find(function(ce){return(ce==null?void 0:ce.project_reaction_id)===(ue==null?void 0:ue.id)});return G&&(ue.progress=G==null?void 0:G.progress,ue.experiment_count=G==null?void 0:G.experiment_count),null});case 8:it(g);case 9:case"end":return x.stop()}},u)}));return function(i){return Z.apply(this,arguments)}}(),Be=function(){var Z=p()(d()().mark(function u(){var i,g,M,P,U,x;return d()().wrap(function(E){for(;;)switch(E.prev=E.next){case 0:if(et([]),Ue(!0),i=(0,Le.service)("project-reactions").select(["id","reaction","effective_procedures"]).filterDeep("project.id","eq",I).populateDeep([{path:"project_routes",fields:["id","name"],children:[{key:"project_compound",fields:["id"]}]}]),(0,X.isEmpty)(We)){E.next=9;break}return E.next=6,i.filterDeep("project_routes.id","in",We).paginate(te==null?void 0:te.page,te==null?void 0:te.pageSize).get();case 6:E.t0=E.sent,E.next=12;break;case 9:return E.next=11,i.paginate(te==null?void 0:te.page,te==null?void 0:te.pageSize).get();case 11:E.t0=E.sent;case 12:g=E.t0,M=g.data,P=g.meta,U=g.error,U!=null&&U.message?(N.error(U==null?void 0:U.message),Ue(!1)):(Yt((P==null||(x=P.pagination)===null||x===void 0?void 0:x.total)||0),ln(M));case 17:case"end":return E.stop()}},u)}));return function(){return Z.apply(this,arguments)}}();(0,C.useEffect)(function(){D(I),r(I),re(I),h(I)},[]),(0,dt.Z)(function(){Be()},[We,te]);var Ve=function(){var Z=p()(d()().mark(function u(i,g){var M,P;return d()().wrap(function(x){for(;;)switch(x.prev=x.next){case 0:if(!(!i||!g)){x.next=3;break}return $(null),x.abrupt("return");case 3:return x.next=5,(0,Le.service)("project-compounds").update(i,{priority:g==null?void 0:g.priority,status:g==null?void 0:g.status,status_update_note:g==null?void 0:g.status_update_note});case 5:M=x.sent,P=M.error,P?N.error(P==null?void 0:P.message):(N.success((0,a.oz)("success-update")),ze(Ne)),$(null);case 9:case"end":return x.stop()}},u)}));return function(i,g){return Z.apply(this,arguments)}}(),sn=function(u,i){return Ve(i,{priority:u})},rn=function(){var Z=p()(d()().mark(function u(i,g){return d()().wrap(function(P){for(;;)switch(P.prev=P.next){case 0:$({id:g,status:i}),["finished","canceled"].includes(i)||Ve(g,{status:i});case 2:case"end":return P.stop()}},u)}));return function(i,g){return Z.apply(this,arguments)}}(),dn=["created","started"].includes(m==null?void 0:m.status),st=[{key:"molecule",label:(0,a.oz)("molecules"),authCode:"projects.tab.molecule",children:(0,t.jsx)(Pt,{isLoading:Oe,moleculeTotal:k,getDetail:ze,moleculePagenate:je,canAddMolecule:dn,userList:W,paginationChange:function(u,i){return le({page:u,pageSize:i})},projectDetail:ie,update:xe,statusChange:rn,handleUpdate:Ve,projectInfo:m,priorityChange:sn,formValue:Ne,changeValues:function(u){var i=y()(y()({},u),{},{order:u!=null&&u.order?u==null?void 0:u.order:Ne==null?void 0:Ne.order});tn(i),qt(i),ze(i)}})},{key:"reaction",label:(0,a.oz)("reaction-tab"),authCode:"projects.tab.reaction",children:(0,t.jsx)(se,{isLoading:Jt,reactionList:Gt,reactionTotal:Kt,pagenate:te,paginationChange:function(u,i){return on({page:u,pageSize:i})},restData:function(){_e([]),Be()},changeValues:function(u){if(u!=null&&u.routes)_e(u==null?void 0:u.routes);else{D(I);var i=[],g=f()(me),M;try{var P=function(){var x=M.value;if(u!=null&&u.compounds.includes(x==null?void 0:x.value))if((0,X.isEmpty)(x==null?void 0:x.project_routes))i.push(-1);else{var ue=function(G){if((0,X.isEmpty)(G))return _e([]);G.forEach(function(ce){return i.push(ce==null?void 0:ce.id)})};ue(x==null?void 0:x.project_routes)}};for(g.s();!(M=g.n()).done;)P()}catch(U){g.e(U)}finally{g.f()}_e(i),D(I,i)}}})},{key:"experiment",authCode:"projects.tab.experiment",label:(0,a.oz)("pages.experiment"),children:(0,t.jsx)(Ae.Z,{projectId:(0,a.Hq)(I)})}],un=function(){if((0,X.isEmpty)(st))return[];var u=st.filter(function(i){var g;return we==null||(g=we.authCodeList)===null||g===void 0?void 0:g.includes(i.authCode)});return u};(0,C.useEffect)(function(){Fe==="reaction"?Be():Fe==="molecule"&&ze(Ne)},[Fe,je]);var cn=function(u){return Vt(u)};return(0,t.jsxs)(rt._z,{children:[(0,t.jsx)(Wt,{projectDetail:ie,onUpdate:function(){return ze(Ne)}}),(0,t.jsx)(ct.Z,{defaultActiveKey:"molecule",items:un(),onChange:cn})]})}},39175:function(ve,L,e){e.d(L,{r:function(){return p}});var c=e(67294),f=Object.defineProperty,A=Object.getOwnPropertySymbols,y=Object.prototype.hasOwnProperty,S=Object.prototype.propertyIsEnumerable,d=(v,a,s)=>a in v?f(v,a,{enumerable:!0,configurable:!0,writable:!0,value:s}):v[a]=s,T=(v,a)=>{for(var s in a||(a={}))y.call(a,s)&&d(v,s,a[s]);if(A)for(var s of A(a))S.call(a,s)&&d(v,s,a[s]);return v};const p=v=>c.createElement("svg",T({viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},v),c.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.25 6a.75.75 0 0 0 0-1.5h-12a.75.75 0 1 0 0 1.5h12ZM14.25 10.5a.75.75 0 0 0 0-1.5h-12a.75.75 0 1 0 0 1.5h12ZM14.25 15a.75.75 0 0 0 0-1.5h-12a.75.75 0 1 0 0 1.5h12ZM14.25 19.5a.75.75 0 0 0 0-1.5h-12a.75.75 0 1 0 0 1.5h12ZM18 3.784c0-.697.807-1.046 1.28-.553l3 3.13a.807.807 0 0 1 0 1.107.728.728 0 0 1-1.061 0l-1.72-1.796v14.546a.774.774 0 0 1-.615.77L18.75 21c-.414 0-.75-.35-.75-.782V3.784Z",fill:"#1A90FF"}));var ne="data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjQgMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNC4yNSA2YS43NS43NSAwIDAgMCAwLTEuNWgtMTJhLjc1Ljc1IDAgMSAwIDAgMS41aDEyWk0xNC4yNSAxMC41YS43NS43NSAwIDAgMCAwLTEuNWgtMTJhLjc1Ljc1IDAgMSAwIDAgMS41aDEyWk0xNC4yNSAxNWEuNzUuNzUgMCAwIDAgMC0xLjVoLTEyYS43NS43NSAwIDEgMCAwIDEuNWgxMlpNMTQuMjUgMTkuNWEuNzUuNzUgMCAwIDAgMC0xLjVoLTEyYS43NS43NSAwIDEgMCAwIDEuNWgxMlpNMTggMy43ODRjMC0uNjk3LjgwNy0xLjA0NiAxLjI4LS41NTNsMyAzLjEzYS44MDcuODA3IDAgMCAxIDAgMS4xMDcuNzI4LjcyOCAwIDAgMS0xLjA2MSAwbC0xLjcyLTEuNzk2djE0LjU0NmEuNzc0Ljc3NCAwIDAgMS0uNjE1Ljc3TDE4Ljc1IDIxYy0uNDE0IDAtLjc1LS4zNS0uNzUtLjc4MlYzLjc4NFoiIGZpbGw9IiMxQTkwRkYiLz48L3N2Zz4="},77598:function(ve,L,e){e.d(L,{Z:function(){return y}});var c=e(67294),f=function(S){return function(d,T){var p=(0,c.useRef)(!1);S(function(){return function(){p.current=!1}},[]),S(function(){if(!p.current)p.current=!0;else return d()},T)}},A=null,y=f(c.useEffect)}}]);

//# sourceMappingURL=p__projects__detail__index.846ff268.async.js.map