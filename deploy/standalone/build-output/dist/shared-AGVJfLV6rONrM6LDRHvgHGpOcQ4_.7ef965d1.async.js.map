{"version": 3, "file": "shared-AGVJfLV6rONrM6LDRHvgHGpOcQ4_.7ef965d1.async.js", "mappings": "oGAMA,IAAIA,EAAU,EAAQ,IAAW,EAC7BC,EAAa,EAAQ,KAAU,EAC/BC,EAAI,EAAQ,KAAQ,EASxB,SAASC,EAAQC,EAAK,CACpB,OAA6BA,GAAQ,IACvC,CAQA,SAASC,EAAmBD,EAAK,CAC/B,IAAIE,EAAM,CAAC,EACX,QAASC,KAAKH,EACZE,EAAIC,CAAC,EAAIH,EAAIG,CAAC,EAEhB,OAAOD,CACT,CAWA,SAASE,EAAWC,EAAS,CAC3BA,EAAUJ,EAAkBI,GAAW,CAAC,CAAC,EACzCA,EAAQ,UAAYA,EAAQ,WAAaT,EAAQ,UACjDS,EAAQ,OAASA,EAAQ,QAAUT,EAAQ,OAC3CS,EAAQ,aAAeA,EAAQ,cAAgBT,EAAQ,aACvDS,EAAQ,cAAgBA,EAAQ,eAAiBT,EAAQ,cACzD,KAAK,QAAUS,CACjB,CAEAD,EAAU,UAAU,QAAU,SAAUE,EAAK,CAI3C,GAFAA,EAAMA,GAAO,GACbA,EAAMA,EAAI,SAAS,EACf,CAACA,EAAK,MAAO,GAEjB,IAAIC,EAAK,KACLF,EAAUE,EAAG,QACbC,EAAYH,EAAQ,UACpBI,EAASJ,EAAQ,OACjBK,EAAeL,EAAQ,aACvBM,EAAgBN,EAAQ,cAExBO,EAASf,EAAWS,EAAK,SAAUO,EAAgBC,EAAUC,EAAMC,EAAOC,EAAQ,CAEpF,IAAIC,EAAQV,EAAUO,CAAI,EACtBI,EAAU,GAQd,GAPID,IAAU,GAAMC,EAAUD,EACrB,OAAOA,GAAU,WAAYC,EAAUD,EAAMF,CAAK,EAClDE,aAAiB,SAAQC,EAAUD,EAAM,KAAKF,CAAK,GACxDG,IAAY,KAAMA,EAAU,IAGhCH,EAAQL,EAAcI,EAAMC,CAAK,EAC7B,EAACA,EAEL,KAAII,EAAO,CACT,SAAUN,EACV,eAAgBD,EAChB,OAAQI,EACR,QAASE,CACX,EAEA,GAAIA,EAAS,CAEX,IAAIjB,EAAMO,EAAOM,EAAMC,EAAOI,CAAI,EAClC,OAAIrB,EAAOG,CAAG,EACLa,EAAO,IAAMC,EAEbd,CAGX,KAAO,CAEL,IAAIA,EAAMQ,EAAaK,EAAMC,EAAOI,CAAI,EACxC,GAAI,CAACrB,EAAOG,CAAG,EACb,OAAOA,CAGX,EACF,CAAC,EAED,OAAOU,CACT,EAGAS,EAAO,QAAUjB,C,qBCvGjB,SAASkB,GAAuB,CAM9B,IAAId,EAAY,CAAC,EAEjB,OAAAA,EAAU,eAAe,EAAI,GAC7BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,YAAY,EAAI,GAC1BA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,oBAAoB,EAAI,GAClCA,EAAU,IAAS,GACnBA,EAAU,cAAc,EAAI,GAC5BA,EAAU,UAAe,GACzBA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,oBAAoB,EAAI,GAClCA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,2BAA2B,EAAI,GACzCA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,sBAAsB,EAAI,GACpCA,EAAU,2BAA2B,EAAI,GACzCA,EAAU,QAAa,GACvBA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,WAAgB,GAC1BA,EAAU,uBAAuB,EAAI,GACrCA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,QAAa,GACvBA,EAAU,MAAW,GACrBA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,OAAY,GACtBA,EAAU,eAAe,EAAI,GAC7BA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,2BAA2B,EAAI,GACzCA,EAAU,4BAA4B,EAAI,GAC1CA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,oBAAoB,EAAI,GAClCA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,oBAAoB,EAAI,GAClCA,EAAU,aAAa,EAAI,GAC3BA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,eAAe,EAAI,GAC7BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,oBAAoB,EAAI,GAClCA,EAAU,oBAAoB,EAAI,GAClCA,EAAU,oBAAoB,EAAI,GAClCA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,YAAY,EAAI,GAC1BA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,wBAAwB,EAAI,GACtCA,EAAU,yBAAyB,EAAI,GACvCA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,cAAc,EAAI,GAC5BA,EAAU,OAAY,GACtBA,EAAU,sBAAsB,EAAI,GACpCA,EAAU,YAAY,EAAI,GAC1BA,EAAU,YAAY,EAAI,GAC1BA,EAAU,UAAU,EAAI,GACxBA,EAAU,cAAc,EAAI,GAC5BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,OAAY,GACtBA,EAAU,MAAW,GACrBA,EAAU,KAAU,GACpBA,EAAU,WAAW,EAAI,GACzBA,EAAU,WAAW,EAAI,GACzBA,EAAU,MAAW,GACrBA,EAAU,6BAA6B,EAAI,GAC3CA,EAAU,cAAc,EAAI,GAC5BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,YAAY,EAAI,GAC1BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,aAAa,EAAI,GAC3BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,QAAa,GACvBA,EAAU,QAAa,GACvBA,EAAU,QAAa,GACvBA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,eAAe,EAAI,GAC7BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,KAAU,GACpBA,EAAU,IAAS,GACnBA,EAAU,WAAW,EAAI,GACzBA,EAAU,YAAY,EAAI,GAC1BA,EAAU,OAAY,GACtBA,EAAU,UAAe,GACzBA,EAAU,QAAa,GACvBA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,UAAe,GACzBA,EAAU,aAAa,EAAI,GAC3BA,EAAU,OAAY,GACtBA,EAAU,KAAU,GACpBA,EAAU,YAAY,EAAI,GAC1BA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,WAAW,EAAI,GACzBA,EAAU,WAAW,EAAI,GACzBA,EAAU,aAAa,EAAI,GAC3BA,EAAU,WAAW,EAAI,GACzBA,EAAU,MAAW,GACrBA,EAAU,cAAc,EAAI,GAC5BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,eAAe,EAAI,GAC7BA,EAAU,WAAW,EAAI,GACzBA,EAAU,WAAW,EAAI,GACzBA,EAAU,KAAU,GACpBA,EAAU,aAAa,EAAI,GAC3BA,EAAU,uBAAuB,EAAI,GACrCA,EAAU,cAAc,EAAI,GAC5BA,EAAU,wBAAwB,EAAI,GACtCA,EAAU,WAAW,EAAI,GACzBA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,cAAc,EAAI,GAC5BA,EAAU,YAAY,EAAI,GAC1BA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,yBAAyB,EAAI,GACvCA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,yBAAyB,EAAI,GACvCA,EAAU,wBAAwB,EAAI,GACtCA,EAAU,sBAAsB,EAAI,GACpCA,EAAU,uBAAuB,EAAI,GACrCA,EAAU,aAAa,EAAI,GAC3BA,EAAU,KAAU,GACpBA,EAAU,WAAW,EAAI,GACzBA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,UAAU,EAAI,GACxBA,EAAU,cAAc,EAAI,GAC5BA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,eAAe,EAAI,GAC7BA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,uBAAuB,EAAI,GACrCA,EAAU,oBAAoB,EAAI,GAClCA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,OAAY,GACtBA,EAAU,QAAa,GACvBA,EAAU,KAAU,GACpBA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,UAAU,EAAI,GACxBA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,eAAe,EAAI,GAC7BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,KAAU,GACpBA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,YAAY,EAAI,GAC1BA,EAAU,WAAW,EAAI,GACzBA,EAAU,aAAa,EAAI,GAC3BA,EAAU,WAAW,EAAI,GACzBA,EAAU,eAAe,EAAI,GAC7BA,EAAU,oBAAoB,EAAI,GAClCA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,wBAAwB,EAAI,GACtCA,EAAU,YAAY,EAAI,GAC1BA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,OAAY,GACtBA,EAAU,eAAe,EAAI,GAC7BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,YAAY,EAAI,GAC1BA,EAAU,eAAe,EAAI,GAC7BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,MAAW,GACrBA,EAAU,KAAU,GACpBA,EAAU,UAAU,EAAI,GACxBA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,WAAW,EAAI,GACzBA,EAAU,YAAY,EAAI,GAC1BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,eAAe,EAAI,GAC7BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,WAAW,EAAI,GACzBA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,WAAW,EAAI,GACzBA,EAAU,YAAY,EAAI,GAC1BA,EAAU,WAAW,EAAI,GACzBA,EAAU,WAAW,EAAI,GACzBA,EAAU,YAAY,EAAI,GAC1BA,EAAU,WAAW,EAAI,GACzBA,EAAU,SAAS,EAAI,GACvBA,EAAU,UAAU,EAAI,GACxBA,EAAU,WAAW,EAAI,GACzBA,EAAU,UAAU,EAAI,GACxBA,EAAU,WAAW,EAAI,GACzBA,EAAU,QAAQ,EAAI,GACtBA,EAAU,YAAY,EAAI,GAC1BA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,QAAa,GACvBA,EAAU,MAAW,GACrBA,EAAU,QAAa,GACvBA,EAAU,QAAa,GACvBA,EAAU,eAAe,EAAI,GAC7BA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,eAAe,EAAI,GAC7BA,EAAU,eAAe,EAAI,GAC7BA,EAAU,SAAc,GACxBA,EAAU,eAAe,EAAI,GAC7BA,EAAU,YAAY,EAAI,GAC1BA,EAAU,YAAY,EAAI,GAC1BA,EAAU,QAAa,GACvBA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,eAAe,EAAI,GAC7BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,KAAU,GACpBA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,aAAa,EAAI,GAC3BA,EAAU,MAAW,GACrBA,EAAU,aAAa,EAAI,GAC3BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,YAAiB,GAC3BA,EAAU,oBAAoB,EAAI,GAClCA,EAAU,MAAW,GACrBA,EAAU,aAAa,EAAI,GAC3BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,SAAc,GACxBA,EAAU,oBAAoB,EAAI,GAClCA,EAAU,OAAY,GACtBA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,OAAY,GACtBA,EAAU,KAAU,GACpBA,EAAU,YAAY,EAAI,GAC1BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,SAAc,GACxBA,EAAU,MAAW,GACrBA,EAAU,SAAc,GACxBA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,YAAY,EAAI,GAC1BA,EAAU,YAAY,EAAI,GAC1BA,EAAU,eAAe,EAAI,GAC7BA,EAAU,uBAAuB,EAAI,GACrCA,EAAU,eAAe,EAAI,GAC7BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,KAAU,GACpBA,EAAU,MAAW,GACrBA,EAAU,UAAU,EAAI,GACxBA,EAAU,cAAc,EAAI,GAC5BA,EAAU,eAAe,EAAI,GAC7BA,EAAU,mBAAmB,EAAI,GACjCA,EAAU,aAAa,EAAI,GAC3BA,EAAU,OAAY,GACtBA,EAAU,YAAY,EAAI,GAC1BA,EAAU,UAAU,EAAI,GACxBA,EAAU,cAAc,EAAI,GAC5BA,EAAU,YAAY,EAAI,GAC1BA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,sBAAsB,EAAI,GACpCA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,uBAAuB,EAAI,GACrCA,EAAU,sBAAsB,EAAI,GACpCA,EAAU,sBAAsB,EAAI,GACpCA,EAAU,uBAAuB,EAAI,GACrCA,EAAU,eAAe,EAAI,GAC7BA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,wBAAwB,EAAI,GACtCA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,aAAa,EAAI,GAC3BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,eAAe,EAAI,GAC7BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,yBAAyB,EAAI,GACvCA,EAAU,WAAW,EAAI,GACzBA,EAAU,IAAS,GACnBA,EAAU,UAAe,GACzBA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,iBAAiB,EAAI,GAC/BA,EAAU,WAAgB,GAC1BA,EAAU,kBAAkB,EAAI,GAChCA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,qBAAqB,EAAI,GACnCA,EAAU,4BAA4B,EAAI,GAC1CA,EAAU,cAAc,EAAI,GAC5BA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,WAAgB,GAC1BA,EAAU,eAAe,EAAI,GAC7BA,EAAU,gBAAgB,EAAI,GAC9BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,aAAa,EAAI,GAC3BA,EAAU,YAAY,EAAI,GAC1BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,OAAY,GACtBA,EAAU,aAAa,EAAI,GAC3BA,EAAU,OAAY,GACtBA,EAAU,MAAW,GACrBA,EAAU,aAAa,EAAI,GAC3BA,EAAU,YAAY,EAAI,GAC1BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,WAAW,EAAI,GACzBA,EAAU,WAAW,EAAI,GACzBA,EAAU,cAAc,EAAI,GAC5BA,EAAU,cAAc,EAAI,GAC5BA,EAAU,SAAS,EAAI,GAEhBA,CACT,CAWA,SAASC,EAAQM,EAAMC,EAAOX,EAAS,CAEvC,CAUA,SAASK,EAAcK,EAAMC,EAAOX,EAAS,CAE7C,CAEA,IAAIkB,EAAwB,qBAS5B,SAASZ,EAAcI,EAAMC,EAAO,CAClC,OAAIO,EAAsB,KAAKP,CAAK,EAAU,GACvCA,CACT,CAGAQ,EAAQ,UAAYF,EAAoB,EACxCE,EAAQ,oBAAsBF,EAC9BE,EAAQ,OAASf,EACjBe,EAAQ,aAAed,EACvBc,EAAQ,cAAgBb,C,wBCvYxB,IAAIf,EAAU,EAAQ,IAAW,EAC7BQ,EAAY,EAAQ,KAAO,EAU/B,SAASqB,EAAWC,EAAMrB,EAAS,CACjC,IAAIsB,EAAM,IAAIvB,EAAUC,CAAO,EAC/B,OAAOsB,EAAI,QAAQD,CAAI,CACzB,CAIAF,EAAUH,EAAO,QAAUI,EAC3BD,EAAQ,UAAYpB,EACpB,QAASD,KAAKP,EAAS4B,EAAQrB,CAAC,EAAIP,EAAQO,CAAC,EAGzC,OAAO,QAAW,cACpB,OAAO,UAAYkB,EAAO,Q,wBCxB5B,IAAIvB,EAAI,EAAQ,KAAQ,EAWxB,SAASD,EAAYS,EAAKG,EAAQ,CAChCH,EAAMR,EAAE,UAAUQ,CAAG,EACjBA,EAAIA,EAAI,OAAS,CAAC,IAAM,MAAKA,GAAO,KACxC,IAAIsB,EAAYtB,EAAI,OAChBuB,EAAoB,GACpBC,EAAU,EACV3B,EAAI,EACJS,EAAS,GAEb,SAASmB,GAAc,CAErB,GAAI,CAACF,EAAmB,CACtB,IAAIZ,EAASnB,EAAE,KAAKQ,EAAI,MAAMwB,EAAS3B,CAAC,CAAC,EACrC6B,EAAIf,EAAO,QAAQ,GAAG,EAC1B,GAAIe,IAAM,GAAI,CACZ,IAAIjB,EAAOjB,EAAE,KAAKmB,EAAO,MAAM,EAAGe,CAAC,CAAC,EAChChB,EAAQlB,EAAE,KAAKmB,EAAO,MAAMe,EAAI,CAAC,CAAC,EAEtC,GAAIjB,EAAM,CACR,IAAIb,EAAMO,EAAOqB,EAASlB,EAAO,OAAQG,EAAMC,EAAOC,CAAM,EACxDf,IAAKU,GAAUV,EAAM,KAC3B,CACF,CACF,CACA4B,EAAU3B,EAAI,CAChB,CAEA,KAAOA,EAAIyB,EAAWzB,IAAK,CACzB,IAAI8B,EAAI3B,EAAIH,CAAC,EACb,GAAI8B,IAAM,KAAO3B,EAAIH,EAAI,CAAC,IAAM,IAAK,CAEnC,IAAI6B,EAAI1B,EAAI,QAAQ,KAAMH,EAAI,CAAC,EAE/B,GAAI6B,IAAM,GAAI,MAEd7B,EAAI6B,EAAI,EACRF,EAAU3B,EAAI,EACd0B,EAAoB,EACtB,MAAWI,IAAM,IACfJ,EAAoB,GACXI,IAAM,IACfJ,EAAoB,GACXI,IAAM,IACXJ,GAGFE,EAAW,EAEJE,IAAM;AAAA,GACfF,EAAW,CAEf,CAEA,OAAOjC,EAAE,KAAKc,CAAM,CACtB,CAEAS,EAAO,QAAUxB,C,oBCzEjBwB,EAAO,QAAU,CACf,QAAS,SAAUa,EAAKC,EAAM,CAC5B,IAAI,EAAGH,EACP,GAAI,MAAM,UAAU,QAClB,OAAOE,EAAI,QAAQC,CAAI,EAEzB,IAAK,EAAI,EAAGH,EAAIE,EAAI,OAAQ,EAAIF,EAAG,IACjC,GAAIE,EAAI,CAAC,IAAMC,EACb,OAAO,EAGX,MAAO,EACT,EACA,QAAS,SAAUD,EAAKE,EAAIC,EAAO,CACjC,IAAIlC,EAAG6B,EACP,GAAI,MAAM,UAAU,QAClB,OAAOE,EAAI,QAAQE,EAAIC,CAAK,EAE9B,IAAKlC,EAAI,EAAG6B,EAAIE,EAAI,OAAQ/B,EAAI6B,EAAG7B,IACjCiC,EAAG,KAAKC,EAAOH,EAAI/B,CAAC,EAAGA,EAAG+B,CAAG,CAEjC,EACA,KAAM,SAAUI,EAAK,CACnB,OAAI,OAAO,UAAU,KACZA,EAAI,KAAK,EAEXA,EAAI,QAAQ,iBAAkB,EAAE,CACzC,EACA,UAAW,SAAUA,EAAK,CACxB,OAAI,OAAO,UAAU,UACZA,EAAI,UAAU,EAEhBA,EAAI,QAAQ,UAAW,EAAE,CAClC,CACF,C,wBC5BA,IAAIlC,EAAY,mBACZmC,EAAyB,6BACzBzC,EAAI,EAAQ,KAAQ,EAExB,SAASwB,GAAsB,CAC7B,MAAO,CACL,EAAG,CAAC,SAAU,OAAQ,OAAO,EAC7B,KAAM,CAAC,OAAO,EACd,QAAS,CAAC,EACV,KAAM,CAAC,QAAS,SAAU,OAAQ,KAAK,EACvC,QAAS,CAAC,EACV,MAAO,CAAC,EACR,MAAO,CACL,WACA,WACA,cACA,OACA,QACA,UACA,KACF,EACA,EAAG,CAAC,EACJ,IAAK,CAAC,KAAK,EACX,IAAK,CAAC,KAAK,EACX,IAAK,CAAC,EACN,WAAY,CAAC,MAAM,EACnB,GAAI,CAAC,EACL,QAAS,CAAC,EACV,OAAQ,CAAC,EACT,KAAM,CAAC,EACP,KAAM,CAAC,EACP,IAAK,CAAC,QAAS,SAAU,OAAQ,OAAO,EACxC,SAAU,CAAC,QAAS,SAAU,OAAQ,OAAO,EAC7C,GAAI,CAAC,EACL,IAAK,CAAC,UAAU,EAChB,QAAS,CAAC,MAAM,EAChB,IAAK,CAAC,EACN,GAAI,CAAC,EACL,GAAI,CAAC,EACL,GAAI,CAAC,EACL,WAAY,CAAC,EACb,OAAQ,CAAC,EACT,KAAM,CAAC,QAAS,OAAQ,MAAM,EAC9B,OAAQ,CAAC,EACT,GAAI,CAAC,EACL,GAAI,CAAC,EACL,GAAI,CAAC,EACL,GAAI,CAAC,EACL,GAAI,CAAC,EACL,GAAI,CAAC,EACL,OAAQ,CAAC,EACT,GAAI,CAAC,EACL,EAAG,CAAC,EACJ,IAAK,CAAC,MAAO,MAAO,QAAS,QAAS,SAAU,SAAS,EACzD,IAAK,CAAC,UAAU,EAChB,IAAK,CAAC,EACN,GAAI,CAAC,EACL,KAAM,CAAC,EACP,IAAK,CAAC,EACN,GAAI,CAAC,EACL,EAAG,CAAC,EACJ,IAAK,CAAC,EACN,EAAG,CAAC,EACJ,QAAS,CAAC,EACV,MAAO,CAAC,EACR,KAAM,CAAC,EACP,IAAK,CAAC,EACN,QAAS,CAAC,EACV,IAAK,CAAC,EACN,OAAQ,CAAC,EACT,OAAQ,CAAC,EACT,MAAO,CAAC,QAAS,SAAU,QAAS,QAAQ,EAC5C,MAAO,CAAC,QAAS,QAAQ,EACzB,GAAI,CAAC,QAAS,UAAW,UAAW,QAAS,QAAQ,EACrD,MAAO,CAAC,QAAS,QAAQ,EACzB,GAAI,CAAC,QAAS,UAAW,UAAW,QAAS,QAAQ,EACrD,MAAO,CAAC,QAAS,QAAQ,EACzB,GAAI,CAAC,UAAW,QAAS,QAAQ,EACjC,GAAI,CAAC,EACL,EAAG,CAAC,EACJ,GAAI,CAAC,EACL,MAAO,CACL,WACA,WACA,cACA,OACA,QACA,cACA,SACA,UACA,MACA,SACA,OACF,CACF,CACF,CAEA,IAAIkB,EAAmB,IAAIpC,EAU3B,SAASqC,EAAMC,EAAKhB,EAAMrB,EAAS,CAEnC,CAUA,SAASsC,EAAYD,EAAKhB,EAAMrB,EAAS,CAEzC,CAUA,SAASuC,EAAUF,EAAK3B,EAAMC,EAAO,CAErC,CAUA,SAAS6B,EAAgBH,EAAK3B,EAAMC,EAAO,CAE3C,CAOA,SAAS8B,EAAWpB,EAAM,CACxB,OAAOA,EAAK,QAAQqB,EAAW,MAAM,EAAE,QAAQC,EAAW,MAAM,CAClE,CAWA,SAASrC,EAAc+B,EAAK3B,EAAMC,EAAOiC,EAAW,CAIlD,GAFAjC,EAAQkC,EAAkBlC,CAAK,EAE3BD,IAAS,QAAUA,IAAS,MAAO,CAIrC,GADAC,EAAQlB,EAAE,KAAKkB,CAAK,EAChBA,IAAU,IAAK,MAAO,IAC1B,GACE,EACEA,EAAM,OAAO,EAAG,CAAC,IAAM,WACvBA,EAAM,OAAO,EAAG,CAAC,IAAM,YACvBA,EAAM,OAAO,EAAG,CAAC,IAAM,WACvBA,EAAM,OAAO,EAAG,CAAC,IAAM,QACvBA,EAAM,OAAO,EAAG,EAAE,IAAM,eACxBA,EAAM,OAAO,EAAG,CAAC,IAAM,UACvBA,EAAM,OAAO,EAAG,CAAC,IAAM,MACvBA,EAAM,OAAO,EAAG,CAAC,IAAM,OACvBA,EAAM,CAAC,IAAM,KACbA,EAAM,CAAC,IAAM,KAGf,MAAO,EAEX,SAAWD,IAAS,cAIlB,GADAoC,EAA6B,UAAY,EACrCA,EAA6B,KAAKnC,CAAK,EACzC,MAAO,WAEAD,IAAS,QAAS,CAQ3B,GANAqC,EAA6B,UAAY,EACrCA,EAA6B,KAAKpC,CAAK,IAI3CqC,EAA6B,UAAY,EACrCA,EAA6B,KAAKrC,CAAK,IACzCmC,EAA6B,UAAY,EACrCA,EAA6B,KAAKnC,CAAK,IACzC,MAAO,GAGPiC,IAAc,KAChBA,EAAYA,GAAaT,EACzBxB,EAAQiC,EAAU,QAAQjC,CAAK,EAEnC,CAGA,OAAAA,EAAQsC,EAAgBtC,CAAK,EACtBA,CACT,CAGA,IAAI+B,EAAY,KACZC,EAAY,KACZO,EAAe,KACfC,EAAiB,UACjBC,EAAsB,wBACtBC,EAA0B,cAC1BC,EAA4B,gBAE5BR,EACF,wFAGEC,EACF,iDACEC,EAA+B,qBAQnC,SAASO,EAAYtB,EAAK,CACxB,OAAOA,EAAI,QAAQiB,EAAc,QAAQ,CAC3C,CAQA,SAASM,EAAcvB,EAAK,CAC1B,OAAOA,EAAI,QAAQkB,EAAgB,GAAG,CACxC,CAQA,SAASM,EAAmBxB,EAAK,CAC/B,OAAOA,EAAI,QAAQmB,EAAqB,SAAwBnB,EAAKyB,EAAM,CACzE,OAAOA,EAAK,CAAC,IAAM,KAAOA,EAAK,CAAC,IAAM,IAClC,OAAO,aAAa,SAASA,EAAK,OAAO,CAAC,EAAG,EAAE,CAAC,EAChD,OAAO,aAAa,SAASA,EAAM,EAAE,CAAC,CAC5C,CAAC,CACH,CAQA,SAASC,EAA0B1B,EAAK,CACtC,OAAOA,EACJ,QAAQoB,EAAyB,GAAG,EACpC,QAAQC,EAA2B,GAAG,CAC3C,CAQA,SAASM,EAA2B3B,EAAK,CAEvC,QADI4B,EAAO,GACF/D,EAAI,EAAGgE,EAAM7B,EAAI,OAAQnC,EAAIgE,EAAKhE,IACzC+D,GAAQ5B,EAAI,WAAWnC,CAAC,EAAI,GAAK,IAAMmC,EAAI,OAAOnC,CAAC,EAErD,OAAOL,EAAE,KAAKoE,CAAI,CACpB,CAQA,SAAShB,EAAkBZ,EAAK,CAC9B,OAAAA,EAAMuB,EAAcvB,CAAG,EACvBA,EAAMwB,EAAmBxB,CAAG,EAC5BA,EAAM0B,EAA0B1B,CAAG,EACnCA,EAAM2B,EAA2B3B,CAAG,EAC7BA,CACT,CAQA,SAASgB,EAAgBhB,EAAK,CAC5B,OAAAA,EAAMsB,EAAYtB,CAAG,EACrBA,EAAMQ,EAAWR,CAAG,EACbA,CACT,CAKA,SAAS8B,GAAsB,CAC7B,MAAO,EACT,CASA,SAASC,EAAaC,EAAMC,EAAM,CAC5B,OAAOA,GAAS,aAClBA,EAAO,UAAY,CAAC,GAGtB,IAAIC,EAAiB,CAAC,MAAM,QAAQF,CAAI,EACxC,SAASG,EAAY/B,EAAK,CACxB,OAAI8B,EAAuB,GACpB1E,EAAE,QAAQwE,EAAM5B,CAAG,IAAM,EAClC,CAEA,IAAIgC,EAAa,CAAC,EACdC,EAAW,GAEf,MAAO,CACL,YAAa,SAAUjC,EAAKhB,EAAMrB,EAAS,CACzC,GAAIoE,EAAY/B,CAAG,EACjB,GAAIrC,EAAQ,UAAW,CACrB,IAAIH,EAAM,aACN0E,EAAMvE,EAAQ,SAAWH,EAAI,OACjC,OAAAwE,EAAW,KAAK,CACdC,IAAa,GAAQA,EAAWtE,EAAQ,SACxCuE,CACF,CAAC,EACDD,EAAW,GACJzE,CACT,KACE,QAAKyE,IACHA,EAAWtE,EAAQ,UAEd,gBAGT,QAAOkE,EAAK7B,EAAKhB,EAAMrB,CAAO,CAElC,EACA,OAAQ,SAAUqB,EAAM,CACtB,IAAImD,EAAU,GACV/C,EAAU,EACd,OAAAhC,EAAE,QAAQ4E,EAAY,SAAUI,EAAK,CACnCD,GAAWnD,EAAK,MAAMI,EAASgD,EAAI,CAAC,CAAC,EACrChD,EAAUgD,EAAI,CAAC,CACjB,CAAC,EACDD,GAAWnD,EAAK,MAAMI,CAAO,EACtB+C,CACT,CACF,CACF,CAQA,SAASE,EAAgBrD,EAAM,CAG7B,QAFIsD,EAAU,GACVlD,EAAU,EACPA,EAAUJ,EAAK,QAAQ,CAC5B,IAAIvB,EAAIuB,EAAK,QAAQ,OAAQI,CAAO,EACpC,GAAI3B,IAAM,GAAI,CACZ6E,GAAWtD,EAAK,MAAMI,CAAO,EAC7B,KACF,CACAkD,GAAWtD,EAAK,MAAMI,EAAS3B,CAAC,EAChC,IAAI6B,EAAIN,EAAK,QAAQ,MAAOvB,CAAC,EAC7B,GAAI6B,IAAM,GACR,MAEFF,EAAUE,EAAI,CAChB,CACA,OAAOgD,CACT,CAQA,SAASC,EAAevD,EAAM,CAC5B,IAAIwD,EAAQxD,EAAK,MAAM,EAAE,EACzB,OAAAwD,EAAQA,EAAM,OAAO,SAAUC,EAAM,CACnC,IAAIlD,EAAIkD,EAAK,WAAW,CAAC,EACzB,OAAIlD,IAAM,IAAY,GAClBA,GAAK,GACHA,IAAM,IAAMA,IAAM,GAGjB,EACT,CAAC,EACMiD,EAAM,KAAK,EAAE,CACtB,CAEA1D,EAAQ,UAAYF,EAAoB,EACxCE,EAAQ,oBAAsBF,EAC9BE,EAAQ,MAAQiB,EAChBjB,EAAQ,YAAcmB,EACtBnB,EAAQ,UAAYoB,EACpBpB,EAAQ,gBAAkBqB,EAC1BrB,EAAQ,cAAgBb,EACxBa,EAAQ,WAAasB,EACrBtB,EAAQ,YAAcoC,EACtBpC,EAAQ,cAAgBqC,EACxBrC,EAAQ,mBAAqBsC,EAC7BtC,EAAQ,0BAA4BwC,EACpCxC,EAAQ,2BAA6ByC,EACrCzC,EAAQ,kBAAoB0B,EAC5B1B,EAAQ,gBAAkB8B,EAC1B9B,EAAQ,oBAAsB4C,EAC9B5C,EAAQ,aAAe6C,EACvB7C,EAAQ,gBAAkBuD,EAC1BvD,EAAQ,eAAiByD,EACzBzD,EAAQ,kBAAoB,IAC5BA,EAAQ,UAAYgB,EACpBhB,EAAQ,uBAAyBe,C,wBCtcjC,IAAI3C,EAAU,EAAQ,KAAW,EAC7BwF,EAAS,EAAQ,KAAU,EAC3BC,EAAY,EAAQ,KAAO,EAS/B,SAASC,EAAU5D,EAAMrB,EAAS,CAChC,IAAIsB,EAAM,IAAI0D,EAAUhF,CAAO,EAC/B,OAAOsB,EAAI,QAAQD,CAAI,CACzB,CAEAF,EAAUH,EAAO,QAAUiE,EAC3B9D,EAAQ,UAAY8D,EACpB9D,EAAQ,UAAY6D,EAEnB,UAAY,CACX,QAASlF,KAAKP,EACZ4B,EAAQrB,CAAC,EAAIP,EAAQO,CAAC,EAExB,QAAS6B,KAAKoD,EACZ5D,EAAQQ,CAAC,EAAIoD,EAAOpD,CAAC,CAEzB,EAAG,EAGC,OAAO,QAAW,cACpB,OAAO,UAAYX,EAAO,SAI5B,SAASkE,GAAc,CACrB,OACE,OAAO,MAAS,aAChB,OAAO,4BAA+B,aACtC,gBAAgB,0BAEpB,CACIA,EAAY,IACd,KAAK,UAAYlE,EAAO,Q,wBC3C1B,IAAIvB,EAAI,EAAQ,KAAQ,EAQxB,SAAS0F,EAAW9D,EAAM,CACxB,IAAIvB,EAAIL,EAAE,WAAW4B,CAAI,EACrB+D,EACJ,OAAItF,IAAM,GACRsF,EAAU/D,EAAK,MAAM,EAAG,EAAE,EAE1B+D,EAAU/D,EAAK,MAAM,EAAGvB,EAAI,CAAC,EAE/BsF,EAAU3F,EAAE,KAAK2F,CAAO,EAAE,YAAY,EAClCA,EAAQ,MAAM,EAAG,CAAC,IAAM,MAAKA,EAAUA,EAAQ,MAAM,CAAC,GACtDA,EAAQ,MAAM,EAAE,IAAM,MAAKA,EAAUA,EAAQ,MAAM,EAAG,EAAE,GACrDA,CACT,CAQA,SAASC,EAAUhE,EAAM,CACvB,OAAOA,EAAK,MAAM,EAAG,CAAC,IAAM,IAC9B,CAUA,SAASiE,EAASjE,EAAMe,EAAOK,EAAY,CACzC,aAEA,IAAI+B,EAAU,GACV/C,EAAU,EACV8D,EAAW,GACXC,EAAa,GACbC,EAAa,EACb3B,EAAMzC,EAAK,OACXqE,EAAiB,GACjBC,EAAc,GAElBC,EAAc,IAAKH,EAAa,EAAGA,EAAa3B,EAAK2B,IAAc,CACjE,IAAI7D,EAAIP,EAAK,OAAOoE,CAAU,EAC9B,GAAIF,IAAa,IACf,GAAI3D,IAAM,IAAK,CACb2D,EAAWE,EACX,QACF,UAEID,IAAe,GAAO,CACxB,GAAI5D,IAAM,IAAK,CACb4C,GAAW/B,EAAWpB,EAAK,MAAMI,EAASgE,CAAU,CAAC,EACrDF,EAAWE,EACXhE,EAAUgE,EACV,QACF,CACA,GAAI7D,IAAM,KAAO6D,IAAe3B,EAAM,EAAG,CACvCU,GAAW/B,EAAWpB,EAAK,MAAMI,EAAS8D,CAAQ,CAAC,EACnDI,EAActE,EAAK,MAAMkE,EAAUE,EAAa,CAAC,EACjDC,EAAiBP,EAAWQ,CAAW,EACvCnB,GAAWpC,EACTmD,EACAf,EAAQ,OACRkB,EACAC,EACAN,EAAUM,CAAW,CACvB,EACAlE,EAAUgE,EAAa,EACvBF,EAAW,GACX,QACF,CACA,GAAI3D,IAAM,KAAOA,IAAM,IAIrB,QAHI9B,EAAI,EACJ+F,EAAKxE,EAAK,OAAOoE,EAAa3F,CAAC,EAE5B+F,EAAG,KAAK,IAAM,IAAMA,IAAO,KAAK,CACrC,GAAIA,IAAO,IAAK,CACdL,EAAa5D,EACb,SAASgE,CACX,CACAC,EAAKxE,EAAK,OAAOoE,EAAa,EAAE3F,CAAC,CACnC,CAEJ,SACM8B,IAAM4D,EAAY,CACpBA,EAAa,GACb,QACF,CAGN,CACA,OAAI/D,EAAUqC,IACZU,GAAW/B,EAAWpB,EAAK,OAAOI,CAAO,CAAC,GAGrC+C,CACT,CAEA,IAAIsB,EAA2B,wBAS/B,SAASC,EAAU1E,EAAMjB,EAAQ,CAC/B,aAEA,IAAIqB,EAAU,EACVuE,EAAc,EACdC,EAAW,CAAC,EACZC,EAAU,GACVpC,EAAMzC,EAAK,OAEf,SAAS8E,EAAQzF,EAAMC,EAAO,CAG5B,GAFAD,EAAOjB,EAAE,KAAKiB,CAAI,EAClBA,EAAOA,EAAK,QAAQoF,EAA0B,EAAE,EAAE,YAAY,EAC1D,EAAApF,EAAK,OAAS,GAClB,KAAIb,EAAMO,EAAOM,EAAMC,GAAS,EAAE,EAC9Bd,GAAKoG,EAAS,KAAKpG,CAAG,EAC5B,CAGA,QAASC,EAAI,EAAGA,EAAIgE,EAAKhE,IAAK,CAC5B,IAAI8B,EAAIP,EAAK,OAAOvB,CAAC,EACjBsG,EAAGzE,EACP,GAAIuE,IAAY,IAAStE,IAAM,IAAK,CAClCsE,EAAU7E,EAAK,MAAMI,EAAS3B,CAAC,EAC/B2B,EAAU3B,EAAI,EACdkG,EAAc3E,EAAK,OAAOI,CAAO,IAAM,KAAOJ,EAAK,OAAOI,CAAO,IAAM,IAAMA,EAAU4E,EAAsBhF,EAAMvB,EAAI,CAAC,EACxH,QACF,CACA,GAAIoG,IAAY,IAEZpG,IAAMkG,EACN,CAEA,GADArE,EAAIN,EAAK,QAAQO,EAAG9B,EAAI,CAAC,EACrB6B,IAAM,GACR,MAEAyE,EAAI3G,EAAE,KAAK4B,EAAK,MAAM2E,EAAc,EAAGrE,CAAC,CAAC,EACzCwE,EAAQD,EAASE,CAAC,EAClBF,EAAU,GACVpG,EAAI6B,EACJF,EAAU3B,EAAI,EACd,QAEJ,CAEF,GAAI,WAAW,KAAK8B,CAAC,EAEnB,GADAP,EAAOA,EAAK,QAAQ,YAAa,GAAG,EAChC6E,IAAY,GAEd,GADAvE,EAAI2E,EAAcjF,EAAMvB,CAAC,EACrB6B,IAAM,GAAI,CACZyE,EAAI3G,EAAE,KAAK4B,EAAK,MAAMI,EAAS3B,CAAC,CAAC,EACjCqG,EAAQC,CAAC,EACTF,EAAU,GACVzE,EAAU3B,EAAI,EACd,QACF,KAAO,CACLA,EAAI6B,EAAI,EACR,QACF,SAEAA,EAAI4E,EAAgBlF,EAAMvB,EAAI,CAAC,EAC3B6B,IAAM,GAAI,CACZyE,EAAI3G,EAAE,KAAK4B,EAAK,MAAMI,EAAS3B,CAAC,CAAC,EACjCsG,EAAII,EAAeJ,CAAC,EACpBD,EAAQD,EAASE,CAAC,EAClBF,EAAU,GACVzE,EAAU3B,EAAI,EACd,QACF,KACE,SAIR,CAEA,OAAI2B,EAAUJ,EAAK,SACb6E,IAAY,GACdC,EAAQ9E,EAAK,MAAMI,CAAO,CAAC,EAE3B0E,EAAQD,EAASM,EAAe/G,EAAE,KAAK4B,EAAK,MAAMI,CAAO,CAAC,CAAC,CAAC,GAIzDhC,EAAE,KAAKwG,EAAS,KAAK,GAAG,CAAC,CAClC,CAEA,SAASK,EAAcrE,EAAKnC,EAAG,CAC7B,KAAOA,EAAImC,EAAI,OAAQnC,IAAK,CAC1B,IAAI8B,EAAIK,EAAInC,CAAC,EACb,GAAI8B,IAAM,IACV,OAAIA,IAAM,IAAY9B,EACf,EACT,CACF,CAEA,SAASuG,EAAsBpE,EAAKnC,EAAG,CACrC,KAAOA,EAAImC,EAAI,OAAQnC,IAAK,CAC1B,IAAI8B,EAAIK,EAAInC,CAAC,EACb,GAAI8B,IAAM,IACV,OAAIA,IAAM,KAAOA,IAAM,IAAY9B,EAC5B,EACT,CACF,CAEA,SAASyG,EAAgBtE,EAAKnC,EAAG,CAC/B,KAAOA,EAAI,EAAGA,IAAK,CACjB,IAAI8B,EAAIK,EAAInC,CAAC,EACb,GAAI8B,IAAM,IACV,OAAIA,IAAM,IAAY9B,EACf,EACT,CACF,CAEA,SAAS2G,EAAkBC,EAAM,CAC/B,OACGA,EAAK,CAAC,IAAM,KAAOA,EAAKA,EAAK,OAAS,CAAC,IAAM,KAC7CA,EAAK,CAAC,IAAM,KAAOA,EAAKA,EAAK,OAAS,CAAC,IAAM,GAMlD,CAEA,SAASF,EAAeE,EAAM,CAC5B,OAAID,EAAkBC,CAAI,EACjBA,EAAK,OAAO,EAAGA,EAAK,OAAS,CAAC,EAE9BA,CAEX,CAEAvF,EAAQ,SAAWmE,EACnBnE,EAAQ,UAAY4E,C,oBChQpB/E,EAAO,QAAU,CACf,QAAS,SAAUa,EAAKC,EAAM,CAC5B,IAAI,EAAGH,EACP,GAAI,MAAM,UAAU,QAClB,OAAOE,EAAI,QAAQC,CAAI,EAEzB,IAAK,EAAI,EAAGH,EAAIE,EAAI,OAAQ,EAAIF,EAAG,IACjC,GAAIE,EAAI,CAAC,IAAMC,EACb,OAAO,EAGX,MAAO,EACT,EACA,QAAS,SAAUD,EAAKE,EAAIC,EAAO,CACjC,IAAIlC,EAAG6B,EACP,GAAI,MAAM,UAAU,QAClB,OAAOE,EAAI,QAAQE,EAAIC,CAAK,EAE9B,IAAKlC,EAAI,EAAG6B,EAAIE,EAAI,OAAQ/B,EAAI6B,EAAG7B,IACjCiC,EAAG,KAAKC,EAAOH,EAAI/B,CAAC,EAAGA,EAAG+B,CAAG,CAEjC,EACA,KAAM,SAAUI,EAAK,CACnB,OAAI,OAAO,UAAU,KACZA,EAAI,KAAK,EAEXA,EAAI,QAAQ,iBAAkB,EAAE,CACzC,EACA,WAAY,SAAUA,EAAK,CACzB,IAAI0E,EAAM,WACNC,EAAQD,EAAI,KAAK1E,CAAG,EACxB,OAAO2E,EAAQA,EAAM,MAAQ,EAC/B,CACF,C,wBC3BA,IAAI7G,EAAY,mBACZR,EAAU,EAAQ,KAAW,EAC7BwF,EAAS,EAAQ,KAAU,EAC3BO,EAAWP,EAAO,SAClBgB,EAAYhB,EAAO,UACnBtF,EAAI,EAAQ,KAAQ,EAQxB,SAASC,EAAOC,EAAK,CACnB,OAA4BA,GAAQ,IACtC,CAUA,SAASkH,EAASxF,EAAM,CACtB,IAAIvB,EAAIL,EAAE,WAAW4B,CAAI,EACzB,GAAIvB,IAAM,GACR,MAAO,CACL,KAAM,GACN,QAASuB,EAAKA,EAAK,OAAS,CAAC,IAAM,GACrC,EAEFA,EAAO5B,EAAE,KAAK4B,EAAK,MAAMvB,EAAI,EAAG,EAAE,CAAC,EACnC,IAAIuF,EAAYhE,EAAKA,EAAK,OAAS,CAAC,IAAM,IAC1C,OAAIgE,IAAWhE,EAAO5B,EAAE,KAAK4B,EAAK,MAAM,EAAG,EAAE,CAAC,GACvC,CACL,KAAMA,EACN,QAASgE,CACX,CACF,CAQA,SAASzF,EAAkBD,EAAK,CAC9B,IAAIE,EAAM,CAAC,EACX,QAASC,KAAKH,EACZE,EAAIC,CAAC,EAAIH,EAAIG,CAAC,EAEhB,OAAOD,CACT,CAEA,SAASiH,EAAgBnH,EAAK,CAC5B,IAAIE,EAAM,CAAC,EACX,QAASC,KAAKH,EACR,MAAM,QAAQA,EAAIG,CAAC,CAAC,EACtBD,EAAIC,EAAE,YAAY,CAAC,EAAIH,EAAIG,CAAC,EAAE,IAAI,SAAUgC,EAAM,CAChD,OAAOA,EAAK,YAAY,CAC1B,CAAC,EAEDjC,EAAIC,EAAE,YAAY,CAAC,EAAIH,EAAIG,CAAC,EAGhC,OAAOD,CACT,CAWA,SAASmF,EAAUhF,EAAS,CAC1BA,EAAUJ,EAAkBI,GAAW,CAAC,CAAC,EAErCA,EAAQ,iBACNA,EAAQ,aACV,QAAQ,MACN,yFACF,EAEFA,EAAQ,YAAcT,EAAQ,qBAE5BS,EAAQ,WAAaA,EAAQ,UAC/BA,EAAQ,UAAY8G,EAAgB9G,EAAQ,WAAaA,EAAQ,SAAS,EAE1EA,EAAQ,UAAYT,EAAQ,UAG9B,KAAK,kBAAoBS,EAAQ,6BAA+B,GAAO,IAAMT,EAAQ,kBAErFS,EAAQ,MAAQA,EAAQ,OAAST,EAAQ,MACzCS,EAAQ,UAAYA,EAAQ,WAAaT,EAAQ,UACjDS,EAAQ,YAAcA,EAAQ,aAAeT,EAAQ,YACrDS,EAAQ,gBAAkBA,EAAQ,iBAAmBT,EAAQ,gBAC7DS,EAAQ,cAAgBA,EAAQ,eAAiBT,EAAQ,cACzDS,EAAQ,WAAaA,EAAQ,YAAcT,EAAQ,WACnD,KAAK,QAAUS,EAEXA,EAAQ,MAAQ,GAClB,KAAK,UAAY,IAEjBA,EAAQ,IAAMA,EAAQ,KAAO,CAAC,EAC9B,KAAK,UAAY,IAAID,EAAUC,EAAQ,GAAG,EAE9C,CAQAgF,EAAU,UAAU,QAAU,SAAU3D,EAAM,CAI5C,GAFAA,EAAOA,GAAQ,GACfA,EAAOA,EAAK,SAAS,EACjB,CAACA,EAAM,MAAO,GAElB,IAAInB,EAAK,KACLF,EAAUE,EAAG,QACbC,EAAYH,EAAQ,UACpBoC,EAAQpC,EAAQ,MAChBsC,EAActC,EAAQ,YACtBuC,EAAYvC,EAAQ,UACpBwC,EAAkBxC,EAAQ,gBAC1BM,EAAgBN,EAAQ,cACxByC,EAAazC,EAAQ,WACrB+G,EAAoB7G,EAAG,kBACvB0C,EAAY1C,EAAG,UAGfF,EAAQ,iBACVqB,EAAO9B,EAAQ,eAAe8B,CAAI,GAI/BrB,EAAQ,kBACXqB,EAAO9B,EAAQ,gBAAgB8B,CAAI,GAIrC,IAAI2F,EAAqB,GACrBhH,EAAQ,qBACVgH,EAAqBzH,EAAQ,aAC3BS,EAAQ,mBACRsC,CACF,EACAA,EAAc0E,EAAmB,aAGnC,IAAIrC,EAAUW,EACZjE,EACA,SAAUb,EAAgBC,EAAU4B,EAAKhB,EAAMgE,EAAW,CACxD,IAAI4B,EAAO,CACT,eAAgBzG,EAChB,SAAUC,EACV,UAAW4E,EACX,QAAS,OAAO,UAAU,eAAe,KAAKlF,EAAWkC,CAAG,CAC9D,EAGIxC,EAAMuC,EAAMC,EAAKhB,EAAM4F,CAAI,EAC/B,GAAI,CAACvH,EAAOG,CAAG,EAAG,OAAOA,EAEzB,GAAIoH,EAAK,QAAS,CAChB,GAAIA,EAAK,UACP,MAAO,KAAO5E,EAAM,IAGtB,IAAI6E,EAAQL,EAASxF,CAAI,EACrB8F,EAAgBhH,EAAUkC,CAAG,EAC7B+E,EAAYrB,EAAUmB,EAAM,KAAM,SAAUxG,EAAMC,EAAO,CAE3D,IAAI0G,EAAc5H,EAAE,QAAQ0H,EAAezG,CAAI,IAAM,GACjDb,EAAM0C,EAAUF,EAAK3B,EAAMC,EAAO0G,CAAW,EACjD,OAAK3H,EAAOG,CAAG,EAEXwH,GAEF1G,EAAQL,EAAc+B,EAAK3B,EAAMC,EAAOiC,CAAS,EAC7CjC,EACKD,EAAO,IAAMqG,EAAoBpG,EAAQoG,EAEzCrG,IAITb,EAAM2C,EAAgBH,EAAK3B,EAAMC,EAAO0G,CAAW,EAC9C3H,EAAOG,CAAG,EACf,OADyBA,GAbFA,CAgB3B,CAAC,EAGD,OAAAwB,EAAO,IAAMgB,EACT+E,IAAW/F,GAAQ,IAAM+F,GACzBF,EAAM,UAAS7F,GAAQ,MAC3BA,GAAQ,IACDA,CACT,KAGE,QADAxB,EAAMyC,EAAYD,EAAKhB,EAAM4F,CAAI,EAC5BvH,EAAOG,CAAG,EACR4C,EAAWpB,CAAI,EADGxB,CAG7B,EACA4C,CACF,EAGA,OAAIuE,IACFrC,EAAUqC,EAAmB,OAAOrC,CAAO,GAGtCA,CACT,EAEA3D,EAAO,QAAUgE,C", "sources": ["webpack://labwise-web/./node_modules/cssfilter/lib/css.js", "webpack://labwise-web/./node_modules/cssfilter/lib/default.js", "webpack://labwise-web/./node_modules/cssfilter/lib/index.js", "webpack://labwise-web/./node_modules/cssfilter/lib/parser.js", "webpack://labwise-web/./node_modules/cssfilter/lib/util.js", "webpack://labwise-web/./node_modules/xss/lib/default.js", "webpack://labwise-web/./node_modules/xss/lib/index.js", "webpack://labwise-web/./node_modules/xss/lib/parser.js", "webpack://labwise-web/./node_modules/xss/lib/util.js", "webpack://labwise-web/./node_modules/xss/lib/xss.js"], "sourcesContent": ["/**\n * cssfilter\n *\n * <AUTHOR>\n */\n\nvar DEFAULT = require('./default');\nvar parseStyle = require('./parser');\nvar _ = require('./util');\n\n\n/**\n * 返回值是否为空\n *\n * @param {Object} obj\n * @return {Boolean}\n */\nfunction isNull (obj) {\n  return (obj === undefined || obj === null);\n}\n\n/**\n * 浅拷贝对象\n *\n * @param {Object} obj\n * @return {Object}\n */\nfunction shallowCopyObject (obj) {\n  var ret = {};\n  for (var i in obj) {\n    ret[i] = obj[i];\n  }\n  return ret;\n}\n\n/**\n * 创建CSS过滤器\n *\n * @param {Object} options\n *   - {Object} whiteList\n *   - {Function} onAttr\n *   - {Function} onIgnoreAttr\n *   - {Function} safeAttrValue\n */\nfunction FilterCSS (options) {\n  options = shallowCopyObject(options || {});\n  options.whiteList = options.whiteList || DEFAULT.whiteList;\n  options.onAttr = options.onAttr || DEFAULT.onAttr;\n  options.onIgnoreAttr = options.onIgnoreAttr || DEFAULT.onIgnoreAttr;\n  options.safeAttrValue = options.safeAttrValue || DEFAULT.safeAttrValue;\n  this.options = options;\n}\n\nFilterCSS.prototype.process = function (css) {\n  // 兼容各种奇葩输入\n  css = css || '';\n  css = css.toString();\n  if (!css) return '';\n\n  var me = this;\n  var options = me.options;\n  var whiteList = options.whiteList;\n  var onAttr = options.onAttr;\n  var onIgnoreAttr = options.onIgnoreAttr;\n  var safeAttrValue = options.safeAttrValue;\n\n  var retCSS = parseStyle(css, function (sourcePosition, position, name, value, source) {\n\n    var check = whiteList[name];\n    var isWhite = false;\n    if (check === true) isWhite = check;\n    else if (typeof check === 'function') isWhite = check(value);\n    else if (check instanceof RegExp) isWhite = check.test(value);\n    if (isWhite !== true) isWhite = false;\n\n    // 如果过滤后 value 为空则直接忽略\n    value = safeAttrValue(name, value);\n    if (!value) return;\n\n    var opts = {\n      position: position,\n      sourcePosition: sourcePosition,\n      source: source,\n      isWhite: isWhite\n    };\n\n    if (isWhite) {\n\n      var ret = onAttr(name, value, opts);\n      if (isNull(ret)) {\n        return name + ':' + value;\n      } else {\n        return ret;\n      }\n\n    } else {\n\n      var ret = onIgnoreAttr(name, value, opts);\n      if (!isNull(ret)) {\n        return ret;\n      }\n\n    }\n  });\n\n  return retCSS;\n};\n\n\nmodule.exports = FilterCSS;\n", "/**\n * cssfilter\n *\n * <AUTHOR>\n */\n\nfunction getDefaultWhiteList () {\n  // 白名单值说明：\n  // true: 允许该属性\n  // Function: function (val) { } 返回true表示允许该属性，其他值均表示不允许\n  // RegExp: regexp.test(val) 返回true表示允许该属性，其他值均表示不允许\n  // 除上面列出的值外均表示不允许\n  var whiteList = {};\n\n  whiteList['align-content'] = false; // default: auto\n  whiteList['align-items'] = false; // default: auto\n  whiteList['align-self'] = false; // default: auto\n  whiteList['alignment-adjust'] = false; // default: auto\n  whiteList['alignment-baseline'] = false; // default: baseline\n  whiteList['all'] = false; // default: depending on individual properties\n  whiteList['anchor-point'] = false; // default: none\n  whiteList['animation'] = false; // default: depending on individual properties\n  whiteList['animation-delay'] = false; // default: 0\n  whiteList['animation-direction'] = false; // default: normal\n  whiteList['animation-duration'] = false; // default: 0\n  whiteList['animation-fill-mode'] = false; // default: none\n  whiteList['animation-iteration-count'] = false; // default: 1\n  whiteList['animation-name'] = false; // default: none\n  whiteList['animation-play-state'] = false; // default: running\n  whiteList['animation-timing-function'] = false; // default: ease\n  whiteList['azimuth'] = false; // default: center\n  whiteList['backface-visibility'] = false; // default: visible\n  whiteList['background'] = true; // default: depending on individual properties\n  whiteList['background-attachment'] = true; // default: scroll\n  whiteList['background-clip'] = true; // default: border-box\n  whiteList['background-color'] = true; // default: transparent\n  whiteList['background-image'] = true; // default: none\n  whiteList['background-origin'] = true; // default: padding-box\n  whiteList['background-position'] = true; // default: 0% 0%\n  whiteList['background-repeat'] = true; // default: repeat\n  whiteList['background-size'] = true; // default: auto\n  whiteList['baseline-shift'] = false; // default: baseline\n  whiteList['binding'] = false; // default: none\n  whiteList['bleed'] = false; // default: 6pt\n  whiteList['bookmark-label'] = false; // default: content()\n  whiteList['bookmark-level'] = false; // default: none\n  whiteList['bookmark-state'] = false; // default: open\n  whiteList['border'] = true; // default: depending on individual properties\n  whiteList['border-bottom'] = true; // default: depending on individual properties\n  whiteList['border-bottom-color'] = true; // default: current color\n  whiteList['border-bottom-left-radius'] = true; // default: 0\n  whiteList['border-bottom-right-radius'] = true; // default: 0\n  whiteList['border-bottom-style'] = true; // default: none\n  whiteList['border-bottom-width'] = true; // default: medium\n  whiteList['border-collapse'] = true; // default: separate\n  whiteList['border-color'] = true; // default: depending on individual properties\n  whiteList['border-image'] = true; // default: none\n  whiteList['border-image-outset'] = true; // default: 0\n  whiteList['border-image-repeat'] = true; // default: stretch\n  whiteList['border-image-slice'] = true; // default: 100%\n  whiteList['border-image-source'] = true; // default: none\n  whiteList['border-image-width'] = true; // default: 1\n  whiteList['border-left'] = true; // default: depending on individual properties\n  whiteList['border-left-color'] = true; // default: current color\n  whiteList['border-left-style'] = true; // default: none\n  whiteList['border-left-width'] = true; // default: medium\n  whiteList['border-radius'] = true; // default: 0\n  whiteList['border-right'] = true; // default: depending on individual properties\n  whiteList['border-right-color'] = true; // default: current color\n  whiteList['border-right-style'] = true; // default: none\n  whiteList['border-right-width'] = true; // default: medium\n  whiteList['border-spacing'] = true; // default: 0\n  whiteList['border-style'] = true; // default: depending on individual properties\n  whiteList['border-top'] = true; // default: depending on individual properties\n  whiteList['border-top-color'] = true; // default: current color\n  whiteList['border-top-left-radius'] = true; // default: 0\n  whiteList['border-top-right-radius'] = true; // default: 0\n  whiteList['border-top-style'] = true; // default: none\n  whiteList['border-top-width'] = true; // default: medium\n  whiteList['border-width'] = true; // default: depending on individual properties\n  whiteList['bottom'] = false; // default: auto\n  whiteList['box-decoration-break'] = true; // default: slice\n  whiteList['box-shadow'] = true; // default: none\n  whiteList['box-sizing'] = true; // default: content-box\n  whiteList['box-snap'] = true; // default: none\n  whiteList['box-suppress'] = true; // default: show\n  whiteList['break-after'] = true; // default: auto\n  whiteList['break-before'] = true; // default: auto\n  whiteList['break-inside'] = true; // default: auto\n  whiteList['caption-side'] = false; // default: top\n  whiteList['chains'] = false; // default: none\n  whiteList['clear'] = true; // default: none\n  whiteList['clip'] = false; // default: auto\n  whiteList['clip-path'] = false; // default: none\n  whiteList['clip-rule'] = false; // default: nonzero\n  whiteList['color'] = true; // default: implementation dependent\n  whiteList['color-interpolation-filters'] = true; // default: auto\n  whiteList['column-count'] = false; // default: auto\n  whiteList['column-fill'] = false; // default: balance\n  whiteList['column-gap'] = false; // default: normal\n  whiteList['column-rule'] = false; // default: depending on individual properties\n  whiteList['column-rule-color'] = false; // default: current color\n  whiteList['column-rule-style'] = false; // default: medium\n  whiteList['column-rule-width'] = false; // default: medium\n  whiteList['column-span'] = false; // default: none\n  whiteList['column-width'] = false; // default: auto\n  whiteList['columns'] = false; // default: depending on individual properties\n  whiteList['contain'] = false; // default: none\n  whiteList['content'] = false; // default: normal\n  whiteList['counter-increment'] = false; // default: none\n  whiteList['counter-reset'] = false; // default: none\n  whiteList['counter-set'] = false; // default: none\n  whiteList['crop'] = false; // default: auto\n  whiteList['cue'] = false; // default: depending on individual properties\n  whiteList['cue-after'] = false; // default: none\n  whiteList['cue-before'] = false; // default: none\n  whiteList['cursor'] = false; // default: auto\n  whiteList['direction'] = false; // default: ltr\n  whiteList['display'] = true; // default: depending on individual properties\n  whiteList['display-inside'] = true; // default: auto\n  whiteList['display-list'] = true; // default: none\n  whiteList['display-outside'] = true; // default: inline-level\n  whiteList['dominant-baseline'] = false; // default: auto\n  whiteList['elevation'] = false; // default: level\n  whiteList['empty-cells'] = false; // default: show\n  whiteList['filter'] = false; // default: none\n  whiteList['flex'] = false; // default: depending on individual properties\n  whiteList['flex-basis'] = false; // default: auto\n  whiteList['flex-direction'] = false; // default: row\n  whiteList['flex-flow'] = false; // default: depending on individual properties\n  whiteList['flex-grow'] = false; // default: 0\n  whiteList['flex-shrink'] = false; // default: 1\n  whiteList['flex-wrap'] = false; // default: nowrap\n  whiteList['float'] = false; // default: none\n  whiteList['float-offset'] = false; // default: 0 0\n  whiteList['flood-color'] = false; // default: black\n  whiteList['flood-opacity'] = false; // default: 1\n  whiteList['flow-from'] = false; // default: none\n  whiteList['flow-into'] = false; // default: none\n  whiteList['font'] = true; // default: depending on individual properties\n  whiteList['font-family'] = true; // default: implementation dependent\n  whiteList['font-feature-settings'] = true; // default: normal\n  whiteList['font-kerning'] = true; // default: auto\n  whiteList['font-language-override'] = true; // default: normal\n  whiteList['font-size'] = true; // default: medium\n  whiteList['font-size-adjust'] = true; // default: none\n  whiteList['font-stretch'] = true; // default: normal\n  whiteList['font-style'] = true; // default: normal\n  whiteList['font-synthesis'] = true; // default: weight style\n  whiteList['font-variant'] = true; // default: normal\n  whiteList['font-variant-alternates'] = true; // default: normal\n  whiteList['font-variant-caps'] = true; // default: normal\n  whiteList['font-variant-east-asian'] = true; // default: normal\n  whiteList['font-variant-ligatures'] = true; // default: normal\n  whiteList['font-variant-numeric'] = true; // default: normal\n  whiteList['font-variant-position'] = true; // default: normal\n  whiteList['font-weight'] = true; // default: normal\n  whiteList['grid'] = false; // default: depending on individual properties\n  whiteList['grid-area'] = false; // default: depending on individual properties\n  whiteList['grid-auto-columns'] = false; // default: auto\n  whiteList['grid-auto-flow'] = false; // default: none\n  whiteList['grid-auto-rows'] = false; // default: auto\n  whiteList['grid-column'] = false; // default: depending on individual properties\n  whiteList['grid-column-end'] = false; // default: auto\n  whiteList['grid-column-start'] = false; // default: auto\n  whiteList['grid-row'] = false; // default: depending on individual properties\n  whiteList['grid-row-end'] = false; // default: auto\n  whiteList['grid-row-start'] = false; // default: auto\n  whiteList['grid-template'] = false; // default: depending on individual properties\n  whiteList['grid-template-areas'] = false; // default: none\n  whiteList['grid-template-columns'] = false; // default: none\n  whiteList['grid-template-rows'] = false; // default: none\n  whiteList['hanging-punctuation'] = false; // default: none\n  whiteList['height'] = true; // default: auto\n  whiteList['hyphens'] = false; // default: manual\n  whiteList['icon'] = false; // default: auto\n  whiteList['image-orientation'] = false; // default: auto\n  whiteList['image-resolution'] = false; // default: normal\n  whiteList['ime-mode'] = false; // default: auto\n  whiteList['initial-letters'] = false; // default: normal\n  whiteList['inline-box-align'] = false; // default: last\n  whiteList['justify-content'] = false; // default: auto\n  whiteList['justify-items'] = false; // default: auto\n  whiteList['justify-self'] = false; // default: auto\n  whiteList['left'] = false; // default: auto\n  whiteList['letter-spacing'] = true; // default: normal\n  whiteList['lighting-color'] = true; // default: white\n  whiteList['line-box-contain'] = false; // default: block inline replaced\n  whiteList['line-break'] = false; // default: auto\n  whiteList['line-grid'] = false; // default: match-parent\n  whiteList['line-height'] = false; // default: normal\n  whiteList['line-snap'] = false; // default: none\n  whiteList['line-stacking'] = false; // default: depending on individual properties\n  whiteList['line-stacking-ruby'] = false; // default: exclude-ruby\n  whiteList['line-stacking-shift'] = false; // default: consider-shifts\n  whiteList['line-stacking-strategy'] = false; // default: inline-line-height\n  whiteList['list-style'] = true; // default: depending on individual properties\n  whiteList['list-style-image'] = true; // default: none\n  whiteList['list-style-position'] = true; // default: outside\n  whiteList['list-style-type'] = true; // default: disc\n  whiteList['margin'] = true; // default: depending on individual properties\n  whiteList['margin-bottom'] = true; // default: 0\n  whiteList['margin-left'] = true; // default: 0\n  whiteList['margin-right'] = true; // default: 0\n  whiteList['margin-top'] = true; // default: 0\n  whiteList['marker-offset'] = false; // default: auto\n  whiteList['marker-side'] = false; // default: list-item\n  whiteList['marks'] = false; // default: none\n  whiteList['mask'] = false; // default: border-box\n  whiteList['mask-box'] = false; // default: see individual properties\n  whiteList['mask-box-outset'] = false; // default: 0\n  whiteList['mask-box-repeat'] = false; // default: stretch\n  whiteList['mask-box-slice'] = false; // default: 0 fill\n  whiteList['mask-box-source'] = false; // default: none\n  whiteList['mask-box-width'] = false; // default: auto\n  whiteList['mask-clip'] = false; // default: border-box\n  whiteList['mask-image'] = false; // default: none\n  whiteList['mask-origin'] = false; // default: border-box\n  whiteList['mask-position'] = false; // default: center\n  whiteList['mask-repeat'] = false; // default: no-repeat\n  whiteList['mask-size'] = false; // default: border-box\n  whiteList['mask-source-type'] = false; // default: auto\n  whiteList['mask-type'] = false; // default: luminance\n  whiteList['max-height'] = true; // default: none\n  whiteList['max-lines'] = false; // default: none\n  whiteList['max-width'] = true; // default: none\n  whiteList['min-height'] = true; // default: 0\n  whiteList['min-width'] = true; // default: 0\n  whiteList['move-to'] = false; // default: normal\n  whiteList['nav-down'] = false; // default: auto\n  whiteList['nav-index'] = false; // default: auto\n  whiteList['nav-left'] = false; // default: auto\n  whiteList['nav-right'] = false; // default: auto\n  whiteList['nav-up'] = false; // default: auto\n  whiteList['object-fit'] = false; // default: fill\n  whiteList['object-position'] = false; // default: 50% 50%\n  whiteList['opacity'] = false; // default: 1\n  whiteList['order'] = false; // default: 0\n  whiteList['orphans'] = false; // default: 2\n  whiteList['outline'] = false; // default: depending on individual properties\n  whiteList['outline-color'] = false; // default: invert\n  whiteList['outline-offset'] = false; // default: 0\n  whiteList['outline-style'] = false; // default: none\n  whiteList['outline-width'] = false; // default: medium\n  whiteList['overflow'] = false; // default: depending on individual properties\n  whiteList['overflow-wrap'] = false; // default: normal\n  whiteList['overflow-x'] = false; // default: visible\n  whiteList['overflow-y'] = false; // default: visible\n  whiteList['padding'] = true; // default: depending on individual properties\n  whiteList['padding-bottom'] = true; // default: 0\n  whiteList['padding-left'] = true; // default: 0\n  whiteList['padding-right'] = true; // default: 0\n  whiteList['padding-top'] = true; // default: 0\n  whiteList['page'] = false; // default: auto\n  whiteList['page-break-after'] = false; // default: auto\n  whiteList['page-break-before'] = false; // default: auto\n  whiteList['page-break-inside'] = false; // default: auto\n  whiteList['page-policy'] = false; // default: start\n  whiteList['pause'] = false; // default: implementation dependent\n  whiteList['pause-after'] = false; // default: implementation dependent\n  whiteList['pause-before'] = false; // default: implementation dependent\n  whiteList['perspective'] = false; // default: none\n  whiteList['perspective-origin'] = false; // default: 50% 50%\n  whiteList['pitch'] = false; // default: medium\n  whiteList['pitch-range'] = false; // default: 50\n  whiteList['play-during'] = false; // default: auto\n  whiteList['position'] = false; // default: static\n  whiteList['presentation-level'] = false; // default: 0\n  whiteList['quotes'] = false; // default: text\n  whiteList['region-fragment'] = false; // default: auto\n  whiteList['resize'] = false; // default: none\n  whiteList['rest'] = false; // default: depending on individual properties\n  whiteList['rest-after'] = false; // default: none\n  whiteList['rest-before'] = false; // default: none\n  whiteList['richness'] = false; // default: 50\n  whiteList['right'] = false; // default: auto\n  whiteList['rotation'] = false; // default: 0\n  whiteList['rotation-point'] = false; // default: 50% 50%\n  whiteList['ruby-align'] = false; // default: auto\n  whiteList['ruby-merge'] = false; // default: separate\n  whiteList['ruby-position'] = false; // default: before\n  whiteList['shape-image-threshold'] = false; // default: 0.0\n  whiteList['shape-outside'] = false; // default: none\n  whiteList['shape-margin'] = false; // default: 0\n  whiteList['size'] = false; // default: auto\n  whiteList['speak'] = false; // default: auto\n  whiteList['speak-as'] = false; // default: normal\n  whiteList['speak-header'] = false; // default: once\n  whiteList['speak-numeral'] = false; // default: continuous\n  whiteList['speak-punctuation'] = false; // default: none\n  whiteList['speech-rate'] = false; // default: medium\n  whiteList['stress'] = false; // default: 50\n  whiteList['string-set'] = false; // default: none\n  whiteList['tab-size'] = false; // default: 8\n  whiteList['table-layout'] = false; // default: auto\n  whiteList['text-align'] = true; // default: start\n  whiteList['text-align-last'] = true; // default: auto\n  whiteList['text-combine-upright'] = true; // default: none\n  whiteList['text-decoration'] = true; // default: none\n  whiteList['text-decoration-color'] = true; // default: currentColor\n  whiteList['text-decoration-line'] = true; // default: none\n  whiteList['text-decoration-skip'] = true; // default: objects\n  whiteList['text-decoration-style'] = true; // default: solid\n  whiteList['text-emphasis'] = true; // default: depending on individual properties\n  whiteList['text-emphasis-color'] = true; // default: currentColor\n  whiteList['text-emphasis-position'] = true; // default: over right\n  whiteList['text-emphasis-style'] = true; // default: none\n  whiteList['text-height'] = true; // default: auto\n  whiteList['text-indent'] = true; // default: 0\n  whiteList['text-justify'] = true; // default: auto\n  whiteList['text-orientation'] = true; // default: mixed\n  whiteList['text-overflow'] = true; // default: clip\n  whiteList['text-shadow'] = true; // default: none\n  whiteList['text-space-collapse'] = true; // default: collapse\n  whiteList['text-transform'] = true; // default: none\n  whiteList['text-underline-position'] = true; // default: auto\n  whiteList['text-wrap'] = true; // default: normal\n  whiteList['top'] = false; // default: auto\n  whiteList['transform'] = false; // default: none\n  whiteList['transform-origin'] = false; // default: 50% 50% 0\n  whiteList['transform-style'] = false; // default: flat\n  whiteList['transition'] = false; // default: depending on individual properties\n  whiteList['transition-delay'] = false; // default: 0s\n  whiteList['transition-duration'] = false; // default: 0s\n  whiteList['transition-property'] = false; // default: all\n  whiteList['transition-timing-function'] = false; // default: ease\n  whiteList['unicode-bidi'] = false; // default: normal\n  whiteList['vertical-align'] = false; // default: baseline\n  whiteList['visibility'] = false; // default: visible\n  whiteList['voice-balance'] = false; // default: center\n  whiteList['voice-duration'] = false; // default: auto\n  whiteList['voice-family'] = false; // default: implementation dependent\n  whiteList['voice-pitch'] = false; // default: medium\n  whiteList['voice-range'] = false; // default: medium\n  whiteList['voice-rate'] = false; // default: normal\n  whiteList['voice-stress'] = false; // default: normal\n  whiteList['voice-volume'] = false; // default: medium\n  whiteList['volume'] = false; // default: medium\n  whiteList['white-space'] = false; // default: normal\n  whiteList['widows'] = false; // default: 2\n  whiteList['width'] = true; // default: auto\n  whiteList['will-change'] = false; // default: auto\n  whiteList['word-break'] = true; // default: normal\n  whiteList['word-spacing'] = true; // default: normal\n  whiteList['word-wrap'] = true; // default: normal\n  whiteList['wrap-flow'] = false; // default: auto\n  whiteList['wrap-through'] = false; // default: wrap\n  whiteList['writing-mode'] = false; // default: horizontal-tb\n  whiteList['z-index'] = false; // default: auto\n\n  return whiteList;\n}\n\n\n/**\n * 匹配到白名单上的一个属性时\n *\n * @param {String} name\n * @param {String} value\n * @param {Object} options\n * @return {String}\n */\nfunction onAttr (name, value, options) {\n  // do nothing\n}\n\n/**\n * 匹配到不在白名单上的一个属性时\n *\n * @param {String} name\n * @param {String} value\n * @param {Object} options\n * @return {String}\n */\nfunction onIgnoreAttr (name, value, options) {\n  // do nothing\n}\n\nvar REGEXP_URL_JAVASCRIPT = /javascript\\s*\\:/img;\n\n/**\n * 过滤属性值\n *\n * @param {String} name\n * @param {String} value\n * @return {String}\n */\nfunction safeAttrValue(name, value) {\n  if (REGEXP_URL_JAVASCRIPT.test(value)) return '';\n  return value;\n}\n\n\nexports.whiteList = getDefaultWhiteList();\nexports.getDefaultWhiteList = getDefaultWhiteList;\nexports.onAttr = onAttr;\nexports.onIgnoreAttr = onIgnoreAttr;\nexports.safeAttrValue = safeAttrValue;\n", "/**\n * cssfilter\n *\n * <AUTHOR>\n */\n\nvar DEFAULT = require('./default');\nvar FilterCSS = require('./css');\n\n\n/**\n * XSS过滤\n *\n * @param {String} css 要过滤的CSS代码\n * @param {Object} options 选项：whiteList, onAttr, onIgnoreAttr\n * @return {String}\n */\nfunction filterCSS (html, options) {\n  var xss = new FilterCSS(options);\n  return xss.process(html);\n}\n\n\n// 输出\nexports = module.exports = filterCSS;\nexports.FilterCSS = FilterCSS;\nfor (var i in DEFAULT) exports[i] = DEFAULT[i];\n\n// 在浏览器端使用\nif (typeof window !== 'undefined') {\n  window.filterCSS = module.exports;\n}\n", "/**\n * cssfilter\n *\n * <AUTHOR>\n */\n\nvar _ = require('./util');\n\n\n/**\n * 解析style\n *\n * @param {String} css\n * @param {Function} onAttr 处理属性的函数\n *   参数格式： function (sourcePosition, position, name, value, source)\n * @return {String}\n */\nfunction parseStyle (css, onAttr) {\n  css = _.trimRight(css);\n  if (css[css.length - 1] !== ';') css += ';';\n  var cssLength = css.length;\n  var isParenthesisOpen = false;\n  var lastPos = 0;\n  var i = 0;\n  var retCSS = '';\n\n  function addNewAttr () {\n    // 如果没有正常的闭合圆括号，则直接忽略当前属性\n    if (!isParenthesisOpen) {\n      var source = _.trim(css.slice(lastPos, i));\n      var j = source.indexOf(':');\n      if (j !== -1) {\n        var name = _.trim(source.slice(0, j));\n        var value = _.trim(source.slice(j + 1));\n        // 必须有属性名称\n        if (name) {\n          var ret = onAttr(lastPos, retCSS.length, name, value, source);\n          if (ret) retCSS += ret + '; ';\n        }\n      }\n    }\n    lastPos = i + 1;\n  }\n\n  for (; i < cssLength; i++) {\n    var c = css[i];\n    if (c === '/' && css[i + 1] === '*') {\n      // 备注开始\n      var j = css.indexOf('*/', i + 2);\n      // 如果没有正常的备注结束，则后面的部分全部跳过\n      if (j === -1) break;\n      // 直接将当前位置调到备注结尾，并且初始化状态\n      i = j + 1;\n      lastPos = i + 1;\n      isParenthesisOpen = false;\n    } else if (c === '(') {\n      isParenthesisOpen = true;\n    } else if (c === ')') {\n      isParenthesisOpen = false;\n    } else if (c === ';') {\n      if (isParenthesisOpen) {\n        // 在圆括号里面，忽略\n      } else {\n        addNewAttr();\n      }\n    } else if (c === '\\n') {\n      addNewAttr();\n    }\n  }\n\n  return _.trim(retCSS);\n}\n\nmodule.exports = parseStyle;\n", "module.exports = {\n  indexOf: function (arr, item) {\n    var i, j;\n    if (Array.prototype.indexOf) {\n      return arr.indexOf(item);\n    }\n    for (i = 0, j = arr.length; i < j; i++) {\n      if (arr[i] === item) {\n        return i;\n      }\n    }\n    return -1;\n  },\n  forEach: function (arr, fn, scope) {\n    var i, j;\n    if (Array.prototype.forEach) {\n      return arr.forEach(fn, scope);\n    }\n    for (i = 0, j = arr.length; i < j; i++) {\n      fn.call(scope, arr[i], i, arr);\n    }\n  },\n  trim: function (str) {\n    if (String.prototype.trim) {\n      return str.trim();\n    }\n    return str.replace(/(^\\s*)|(\\s*$)/g, '');\n  },\n  trimRight: function (str) {\n    if (String.prototype.trimRight) {\n      return str.trimRight();\n    }\n    return str.replace(/(\\s*$)/g, '');\n  }\n};\n", "/**\n * default settings\n *\n * <AUTHOR>\n */\n\nvar FilterCSS = require(\"cssfilter\").FilterCSS;\nvar getDefaultCSSWhiteList = require(\"cssfilter\").getDefaultWhiteList;\nvar _ = require(\"./util\");\n\nfunction getDefaultWhiteList() {\n  return {\n    a: [\"target\", \"href\", \"title\"],\n    abbr: [\"title\"],\n    address: [],\n    area: [\"shape\", \"coords\", \"href\", \"alt\"],\n    article: [],\n    aside: [],\n    audio: [\n      \"autoplay\",\n      \"controls\",\n      \"crossorigin\",\n      \"loop\",\n      \"muted\",\n      \"preload\",\n      \"src\",\n    ],\n    b: [],\n    bdi: [\"dir\"],\n    bdo: [\"dir\"],\n    big: [],\n    blockquote: [\"cite\"],\n    br: [],\n    caption: [],\n    center: [],\n    cite: [],\n    code: [],\n    col: [\"align\", \"valign\", \"span\", \"width\"],\n    colgroup: [\"align\", \"valign\", \"span\", \"width\"],\n    dd: [],\n    del: [\"datetime\"],\n    details: [\"open\"],\n    div: [],\n    dl: [],\n    dt: [],\n    em: [],\n    figcaption: [],\n    figure: [],\n    font: [\"color\", \"size\", \"face\"],\n    footer: [],\n    h1: [],\n    h2: [],\n    h3: [],\n    h4: [],\n    h5: [],\n    h6: [],\n    header: [],\n    hr: [],\n    i: [],\n    img: [\"src\", \"alt\", \"title\", \"width\", \"height\", \"loading\"],\n    ins: [\"datetime\"],\n    kbd: [],\n    li: [],\n    mark: [],\n    nav: [],\n    ol: [],\n    p: [],\n    pre: [],\n    s: [],\n    section: [],\n    small: [],\n    span: [],\n    sub: [],\n    summary: [],\n    sup: [],\n    strong: [],\n    strike: [],\n    table: [\"width\", \"border\", \"align\", \"valign\"],\n    tbody: [\"align\", \"valign\"],\n    td: [\"width\", \"rowspan\", \"colspan\", \"align\", \"valign\"],\n    tfoot: [\"align\", \"valign\"],\n    th: [\"width\", \"rowspan\", \"colspan\", \"align\", \"valign\"],\n    thead: [\"align\", \"valign\"],\n    tr: [\"rowspan\", \"align\", \"valign\"],\n    tt: [],\n    u: [],\n    ul: [],\n    video: [\n      \"autoplay\",\n      \"controls\",\n      \"crossorigin\",\n      \"loop\",\n      \"muted\",\n      \"playsinline\",\n      \"poster\",\n      \"preload\",\n      \"src\",\n      \"height\",\n      \"width\",\n    ],\n  };\n}\n\nvar defaultCSSFilter = new FilterCSS();\n\n/**\n * default onTag function\n *\n * @param {String} tag\n * @param {String} html\n * @param {Object} options\n * @return {String}\n */\nfunction onTag(tag, html, options) {\n  // do nothing\n}\n\n/**\n * default onIgnoreTag function\n *\n * @param {String} tag\n * @param {String} html\n * @param {Object} options\n * @return {String}\n */\nfunction onIgnoreTag(tag, html, options) {\n  // do nothing\n}\n\n/**\n * default onTagAttr function\n *\n * @param {String} tag\n * @param {String} name\n * @param {String} value\n * @return {String}\n */\nfunction onTagAttr(tag, name, value) {\n  // do nothing\n}\n\n/**\n * default onIgnoreTagAttr function\n *\n * @param {String} tag\n * @param {String} name\n * @param {String} value\n * @return {String}\n */\nfunction onIgnoreTagAttr(tag, name, value) {\n  // do nothing\n}\n\n/**\n * default escapeHtml function\n *\n * @param {String} html\n */\nfunction escapeHtml(html) {\n  return html.replace(REGEXP_LT, \"&lt;\").replace(REGEXP_GT, \"&gt;\");\n}\n\n/**\n * default safeAttrValue function\n *\n * @param {String} tag\n * @param {String} name\n * @param {String} value\n * @param {Object} cssFilter\n * @return {String}\n */\nfunction safeAttrValue(tag, name, value, cssFilter) {\n  // unescape attribute value firstly\n  value = friendlyAttrValue(value);\n\n  if (name === \"href\" || name === \"src\") {\n    // filter `href` and `src` attribute\n    // only allow the value that starts with `http://` | `https://` | `mailto:` | `/` | `#`\n    value = _.trim(value);\n    if (value === \"#\") return \"#\";\n    if (\n      !(\n        value.substr(0, 7) === \"http://\" ||\n        value.substr(0, 8) === \"https://\" ||\n        value.substr(0, 7) === \"mailto:\" ||\n        value.substr(0, 4) === \"tel:\" ||\n        value.substr(0, 11) === \"data:image/\" ||\n        value.substr(0, 6) === \"ftp://\" ||\n        value.substr(0, 2) === \"./\" ||\n        value.substr(0, 3) === \"../\" ||\n        value[0] === \"#\" ||\n        value[0] === \"/\"\n      )\n    ) {\n      return \"\";\n    }\n  } else if (name === \"background\") {\n    // filter `background` attribute (maybe no use)\n    // `javascript:`\n    REGEXP_DEFAULT_ON_TAG_ATTR_4.lastIndex = 0;\n    if (REGEXP_DEFAULT_ON_TAG_ATTR_4.test(value)) {\n      return \"\";\n    }\n  } else if (name === \"style\") {\n    // `expression()`\n    REGEXP_DEFAULT_ON_TAG_ATTR_7.lastIndex = 0;\n    if (REGEXP_DEFAULT_ON_TAG_ATTR_7.test(value)) {\n      return \"\";\n    }\n    // `url()`\n    REGEXP_DEFAULT_ON_TAG_ATTR_8.lastIndex = 0;\n    if (REGEXP_DEFAULT_ON_TAG_ATTR_8.test(value)) {\n      REGEXP_DEFAULT_ON_TAG_ATTR_4.lastIndex = 0;\n      if (REGEXP_DEFAULT_ON_TAG_ATTR_4.test(value)) {\n        return \"\";\n      }\n    }\n    if (cssFilter !== false) {\n      cssFilter = cssFilter || defaultCSSFilter;\n      value = cssFilter.process(value);\n    }\n  }\n\n  // escape `<>\"` before returns\n  value = escapeAttrValue(value);\n  return value;\n}\n\n// RegExp list\nvar REGEXP_LT = /</g;\nvar REGEXP_GT = />/g;\nvar REGEXP_QUOTE = /\"/g;\nvar REGEXP_QUOTE_2 = /&quot;/g;\nvar REGEXP_ATTR_VALUE_1 = /&#([a-zA-Z0-9]*);?/gim;\nvar REGEXP_ATTR_VALUE_COLON = /&colon;?/gim;\nvar REGEXP_ATTR_VALUE_NEWLINE = /&newline;?/gim;\n// var REGEXP_DEFAULT_ON_TAG_ATTR_3 = /\\/\\*|\\*\\//gm;\nvar REGEXP_DEFAULT_ON_TAG_ATTR_4 =\n  /((j\\s*a\\s*v\\s*a|v\\s*b|l\\s*i\\s*v\\s*e)\\s*s\\s*c\\s*r\\s*i\\s*p\\s*t\\s*|m\\s*o\\s*c\\s*h\\s*a):/gi;\n// var REGEXP_DEFAULT_ON_TAG_ATTR_5 = /^[\\s\"'`]*(d\\s*a\\s*t\\s*a\\s*)\\:/gi;\n// var REGEXP_DEFAULT_ON_TAG_ATTR_6 = /^[\\s\"'`]*(d\\s*a\\s*t\\s*a\\s*)\\:\\s*image\\//gi;\nvar REGEXP_DEFAULT_ON_TAG_ATTR_7 =\n  /e\\s*x\\s*p\\s*r\\s*e\\s*s\\s*s\\s*i\\s*o\\s*n\\s*\\(.*/gi;\nvar REGEXP_DEFAULT_ON_TAG_ATTR_8 = /u\\s*r\\s*l\\s*\\(.*/gi;\n\n/**\n * escape double quote\n *\n * @param {String} str\n * @return {String} str\n */\nfunction escapeQuote(str) {\n  return str.replace(REGEXP_QUOTE, \"&quot;\");\n}\n\n/**\n * unescape double quote\n *\n * @param {String} str\n * @return {String} str\n */\nfunction unescapeQuote(str) {\n  return str.replace(REGEXP_QUOTE_2, '\"');\n}\n\n/**\n * escape html entities\n *\n * @param {String} str\n * @return {String}\n */\nfunction escapeHtmlEntities(str) {\n  return str.replace(REGEXP_ATTR_VALUE_1, function replaceUnicode(str, code) {\n    return code[0] === \"x\" || code[0] === \"X\"\n      ? String.fromCharCode(parseInt(code.substr(1), 16))\n      : String.fromCharCode(parseInt(code, 10));\n  });\n}\n\n/**\n * escape html5 new danger entities\n *\n * @param {String} str\n * @return {String}\n */\nfunction escapeDangerHtml5Entities(str) {\n  return str\n    .replace(REGEXP_ATTR_VALUE_COLON, \":\")\n    .replace(REGEXP_ATTR_VALUE_NEWLINE, \" \");\n}\n\n/**\n * clear nonprintable characters\n *\n * @param {String} str\n * @return {String}\n */\nfunction clearNonPrintableCharacter(str) {\n  var str2 = \"\";\n  for (var i = 0, len = str.length; i < len; i++) {\n    str2 += str.charCodeAt(i) < 32 ? \" \" : str.charAt(i);\n  }\n  return _.trim(str2);\n}\n\n/**\n * get friendly attribute value\n *\n * @param {String} str\n * @return {String}\n */\nfunction friendlyAttrValue(str) {\n  str = unescapeQuote(str);\n  str = escapeHtmlEntities(str);\n  str = escapeDangerHtml5Entities(str);\n  str = clearNonPrintableCharacter(str);\n  return str;\n}\n\n/**\n * unescape attribute value\n *\n * @param {String} str\n * @return {String}\n */\nfunction escapeAttrValue(str) {\n  str = escapeQuote(str);\n  str = escapeHtml(str);\n  return str;\n}\n\n/**\n * `onIgnoreTag` function for removing all the tags that are not in whitelist\n */\nfunction onIgnoreTagStripAll() {\n  return \"\";\n}\n\n/**\n * remove tag body\n * specify a `tags` list, if the tag is not in the `tags` list then process by the specify function (optional)\n *\n * @param {array} tags\n * @param {function} next\n */\nfunction StripTagBody(tags, next) {\n  if (typeof next !== \"function\") {\n    next = function () {};\n  }\n\n  var isRemoveAllTag = !Array.isArray(tags);\n  function isRemoveTag(tag) {\n    if (isRemoveAllTag) return true;\n    return _.indexOf(tags, tag) !== -1;\n  }\n\n  var removeList = [];\n  var posStart = false;\n\n  return {\n    onIgnoreTag: function (tag, html, options) {\n      if (isRemoveTag(tag)) {\n        if (options.isClosing) {\n          var ret = \"[/removed]\";\n          var end = options.position + ret.length;\n          removeList.push([\n            posStart !== false ? posStart : options.position,\n            end,\n          ]);\n          posStart = false;\n          return ret;\n        } else {\n          if (!posStart) {\n            posStart = options.position;\n          }\n          return \"[removed]\";\n        }\n      } else {\n        return next(tag, html, options);\n      }\n    },\n    remove: function (html) {\n      var rethtml = \"\";\n      var lastPos = 0;\n      _.forEach(removeList, function (pos) {\n        rethtml += html.slice(lastPos, pos[0]);\n        lastPos = pos[1];\n      });\n      rethtml += html.slice(lastPos);\n      return rethtml;\n    },\n  };\n}\n\n/**\n * remove html comments\n *\n * @param {String} html\n * @return {String}\n */\nfunction stripCommentTag(html) {\n  var retHtml = \"\";\n  var lastPos = 0;\n  while (lastPos < html.length) {\n    var i = html.indexOf(\"<!--\", lastPos);\n    if (i === -1) {\n      retHtml += html.slice(lastPos);\n      break;\n    }\n    retHtml += html.slice(lastPos, i);\n    var j = html.indexOf(\"-->\", i);\n    if (j === -1) {\n      break;\n    }\n    lastPos = j + 3;\n  }\n  return retHtml;\n}\n\n/**\n * remove invisible characters\n *\n * @param {String} html\n * @return {String}\n */\nfunction stripBlankChar(html) {\n  var chars = html.split(\"\");\n  chars = chars.filter(function (char) {\n    var c = char.charCodeAt(0);\n    if (c === 127) return false;\n    if (c <= 31) {\n      if (c === 10 || c === 13) return true;\n      return false;\n    }\n    return true;\n  });\n  return chars.join(\"\");\n}\n\nexports.whiteList = getDefaultWhiteList();\nexports.getDefaultWhiteList = getDefaultWhiteList;\nexports.onTag = onTag;\nexports.onIgnoreTag = onIgnoreTag;\nexports.onTagAttr = onTagAttr;\nexports.onIgnoreTagAttr = onIgnoreTagAttr;\nexports.safeAttrValue = safeAttrValue;\nexports.escapeHtml = escapeHtml;\nexports.escapeQuote = escapeQuote;\nexports.unescapeQuote = unescapeQuote;\nexports.escapeHtmlEntities = escapeHtmlEntities;\nexports.escapeDangerHtml5Entities = escapeDangerHtml5Entities;\nexports.clearNonPrintableCharacter = clearNonPrintableCharacter;\nexports.friendlyAttrValue = friendlyAttrValue;\nexports.escapeAttrValue = escapeAttrValue;\nexports.onIgnoreTagStripAll = onIgnoreTagStripAll;\nexports.StripTagBody = StripTagBody;\nexports.stripCommentTag = stripCommentTag;\nexports.stripBlankChar = stripBlankChar;\nexports.attributeWrapSign = '\"';\nexports.cssFilter = defaultCSSFilter;\nexports.getDefaultCSSWhiteList = getDefaultCSSWhiteList;\n", "/**\n * xss\n *\n * <AUTHOR>\n */\n\nvar DEFAULT = require(\"./default\");\nvar parser = require(\"./parser\");\nvar FilterXSS = require(\"./xss\");\n\n/**\n * filter xss function\n *\n * @param {String} html\n * @param {Object} options { whiteList, onTag, onTagAttr, onIgnoreTag, onIgnoreTagAttr, safeAttrValue, escapeHtml }\n * @return {String}\n */\nfunction filterXSS(html, options) {\n  var xss = new FilterXSS(options);\n  return xss.process(html);\n}\n\nexports = module.exports = filterXSS;\nexports.filterXSS = filterXSS;\nexports.FilterXSS = FilterXSS;\n\n(function () {\n  for (var i in DEFAULT) {\n    exports[i] = DEFAULT[i];\n  }\n  for (var j in parser) {\n    exports[j] = parser[j];\n  }\n})();\n\n// using `xss` on the browser, output `filterXSS` to the globals\nif (typeof window !== \"undefined\") {\n  window.filterXSS = module.exports;\n}\n\n// using `xss` on the WebWorker, output `filterXSS` to the globals\nfunction isWorkerEnv() {\n  return (\n    typeof self !== \"undefined\" &&\n    typeof DedicatedWorkerGlobalScope !== \"undefined\" &&\n    self instanceof DedicatedWorkerGlobalScope\n  );\n}\nif (isWorkerEnv()) {\n  self.filterXSS = module.exports;\n}\n", "/**\n * Simple HTML Parser\n *\n * <AUTHOR>\n */\n\nvar _ = require(\"./util\");\n\n/**\n * get tag name\n *\n * @param {String} html e.g. '<a hef=\"#\">'\n * @return {String}\n */\nfunction getTagName(html) {\n  var i = _.spaceIndex(html);\n  var tagName;\n  if (i === -1) {\n    tagName = html.slice(1, -1);\n  } else {\n    tagName = html.slice(1, i + 1);\n  }\n  tagName = _.trim(tagName).toLowerCase();\n  if (tagName.slice(0, 1) === \"/\") tagName = tagName.slice(1);\n  if (tagName.slice(-1) === \"/\") tagName = tagName.slice(0, -1);\n  return tagName;\n}\n\n/**\n * is close tag?\n *\n * @param {String} html 如：'<a hef=\"#\">'\n * @return {Boolean}\n */\nfunction isClosing(html) {\n  return html.slice(0, 2) === \"</\";\n}\n\n/**\n * parse input html and returns processed html\n *\n * @param {String} html\n * @param {Function} onTag e.g. function (sourcePosition, position, tag, html, isClosing)\n * @param {Function} escapeHtml\n * @return {String}\n */\nfunction parseTag(html, onTag, escapeHtml) {\n  \"use strict\";\n\n  var rethtml = \"\";\n  var lastPos = 0;\n  var tagStart = false;\n  var quoteStart = false;\n  var currentPos = 0;\n  var len = html.length;\n  var currentTagName = \"\";\n  var currentHtml = \"\";\n\n  chariterator: for (currentPos = 0; currentPos < len; currentPos++) {\n    var c = html.charAt(currentPos);\n    if (tagStart === false) {\n      if (c === \"<\") {\n        tagStart = currentPos;\n        continue;\n      }\n    } else {\n      if (quoteStart === false) {\n        if (c === \"<\") {\n          rethtml += escapeHtml(html.slice(lastPos, currentPos));\n          tagStart = currentPos;\n          lastPos = currentPos;\n          continue;\n        }\n        if (c === \">\" || currentPos === len - 1) {\n          rethtml += escapeHtml(html.slice(lastPos, tagStart));\n          currentHtml = html.slice(tagStart, currentPos + 1);\n          currentTagName = getTagName(currentHtml);\n          rethtml += onTag(\n            tagStart,\n            rethtml.length,\n            currentTagName,\n            currentHtml,\n            isClosing(currentHtml)\n          );\n          lastPos = currentPos + 1;\n          tagStart = false;\n          continue;\n        }\n        if (c === '\"' || c === \"'\") {\n          var i = 1;\n          var ic = html.charAt(currentPos - i);\n\n          while (ic.trim() === \"\" || ic === \"=\") {\n            if (ic === \"=\") {\n              quoteStart = c;\n              continue chariterator;\n            }\n            ic = html.charAt(currentPos - ++i);\n          }\n        }\n      } else {\n        if (c === quoteStart) {\n          quoteStart = false;\n          continue;\n        }\n      }\n    }\n  }\n  if (lastPos < len) {\n    rethtml += escapeHtml(html.substr(lastPos));\n  }\n\n  return rethtml;\n}\n\nvar REGEXP_ILLEGAL_ATTR_NAME = /[^a-zA-Z0-9\\\\_:.-]/gim;\n\n/**\n * parse input attributes and returns processed attributes\n *\n * @param {String} html e.g. `href=\"#\" target=\"_blank\"`\n * @param {Function} onAttr e.g. `function (name, value)`\n * @return {String}\n */\nfunction parseAttr(html, onAttr) {\n  \"use strict\";\n\n  var lastPos = 0;\n  var lastMarkPos = 0;\n  var retAttrs = [];\n  var tmpName = false;\n  var len = html.length;\n\n  function addAttr(name, value) {\n    name = _.trim(name);\n    name = name.replace(REGEXP_ILLEGAL_ATTR_NAME, \"\").toLowerCase();\n    if (name.length < 1) return;\n    var ret = onAttr(name, value || \"\");\n    if (ret) retAttrs.push(ret);\n  }\n\n  // 逐个分析字符\n  for (var i = 0; i < len; i++) {\n    var c = html.charAt(i);\n    var v, j;\n    if (tmpName === false && c === \"=\") {\n      tmpName = html.slice(lastPos, i);\n      lastPos = i + 1;\n      lastMarkPos = html.charAt(lastPos) === '\"' || html.charAt(lastPos) === \"'\" ? lastPos : findNextQuotationMark(html, i + 1);\n      continue;\n    }\n    if (tmpName !== false) {\n      if (\n        i === lastMarkPos\n      ) {\n        j = html.indexOf(c, i + 1);\n        if (j === -1) {\n          break;\n        } else {\n          v = _.trim(html.slice(lastMarkPos + 1, j));\n          addAttr(tmpName, v);\n          tmpName = false;\n          i = j;\n          lastPos = i + 1;\n          continue;\n        }\n      }\n    }\n    if (/\\s|\\n|\\t/.test(c)) {\n      html = html.replace(/\\s|\\n|\\t/g, \" \");\n      if (tmpName === false) {\n        j = findNextEqual(html, i);\n        if (j === -1) {\n          v = _.trim(html.slice(lastPos, i));\n          addAttr(v);\n          tmpName = false;\n          lastPos = i + 1;\n          continue;\n        } else {\n          i = j - 1;\n          continue;\n        }\n      } else {\n        j = findBeforeEqual(html, i - 1);\n        if (j === -1) {\n          v = _.trim(html.slice(lastPos, i));\n          v = stripQuoteWrap(v);\n          addAttr(tmpName, v);\n          tmpName = false;\n          lastPos = i + 1;\n          continue;\n        } else {\n          continue;\n        }\n      }\n    }\n  }\n\n  if (lastPos < html.length) {\n    if (tmpName === false) {\n      addAttr(html.slice(lastPos));\n    } else {\n      addAttr(tmpName, stripQuoteWrap(_.trim(html.slice(lastPos))));\n    }\n  }\n\n  return _.trim(retAttrs.join(\" \"));\n}\n\nfunction findNextEqual(str, i) {\n  for (; i < str.length; i++) {\n    var c = str[i];\n    if (c === \" \") continue;\n    if (c === \"=\") return i;\n    return -1;\n  }\n}\n\nfunction findNextQuotationMark(str, i) {\n  for (; i < str.length; i++) {\n    var c = str[i];\n    if (c === \" \") continue;\n    if (c === \"'\" || c === '\"') return i;\n    return -1;\n  }\n}\n\nfunction findBeforeEqual(str, i) {\n  for (; i > 0; i--) {\n    var c = str[i];\n    if (c === \" \") continue;\n    if (c === \"=\") return i;\n    return -1;\n  }\n}\n\nfunction isQuoteWrapString(text) {\n  if (\n    (text[0] === '\"' && text[text.length - 1] === '\"') ||\n    (text[0] === \"'\" && text[text.length - 1] === \"'\")\n  ) {\n    return true;\n  } else {\n    return false;\n  }\n}\n\nfunction stripQuoteWrap(text) {\n  if (isQuoteWrapString(text)) {\n    return text.substr(1, text.length - 2);\n  } else {\n    return text;\n  }\n}\n\nexports.parseTag = parseTag;\nexports.parseAttr = parseAttr;\n", "module.exports = {\n  indexOf: function (arr, item) {\n    var i, j;\n    if (Array.prototype.indexOf) {\n      return arr.indexOf(item);\n    }\n    for (i = 0, j = arr.length; i < j; i++) {\n      if (arr[i] === item) {\n        return i;\n      }\n    }\n    return -1;\n  },\n  forEach: function (arr, fn, scope) {\n    var i, j;\n    if (Array.prototype.forEach) {\n      return arr.forEach(fn, scope);\n    }\n    for (i = 0, j = arr.length; i < j; i++) {\n      fn.call(scope, arr[i], i, arr);\n    }\n  },\n  trim: function (str) {\n    if (String.prototype.trim) {\n      return str.trim();\n    }\n    return str.replace(/(^\\s*)|(\\s*$)/g, \"\");\n  },\n  spaceIndex: function (str) {\n    var reg = /\\s|\\n|\\t/;\n    var match = reg.exec(str);\n    return match ? match.index : -1;\n  },\n};\n", "/**\n * filter xss\n *\n * <AUTHOR>\n */\n\nvar FilterCSS = require(\"cssfilter\").FilterCSS;\nvar DEFAULT = require(\"./default\");\nvar parser = require(\"./parser\");\nvar parseTag = parser.parseTag;\nvar parseAttr = parser.parseAttr;\nvar _ = require(\"./util\");\n\n/**\n * returns `true` if the input value is `undefined` or `null`\n *\n * @param {Object} obj\n * @return {Boolean}\n */\nfunction isNull(obj) {\n  return obj === undefined || obj === null;\n}\n\n/**\n * get attributes for a tag\n *\n * @param {String} html\n * @return {Object}\n *   - {String} html\n *   - {Boolean} closing\n */\nfunction getAttrs(html) {\n  var i = _.spaceIndex(html);\n  if (i === -1) {\n    return {\n      html: \"\",\n      closing: html[html.length - 2] === \"/\",\n    };\n  }\n  html = _.trim(html.slice(i + 1, -1));\n  var isClosing = html[html.length - 1] === \"/\";\n  if (isClosing) html = _.trim(html.slice(0, -1));\n  return {\n    html: html,\n    closing: isClosing,\n  };\n}\n\n/**\n * shallow copy\n *\n * @param {Object} obj\n * @return {Object}\n */\nfunction shallowCopyObject(obj) {\n  var ret = {};\n  for (var i in obj) {\n    ret[i] = obj[i];\n  }\n  return ret;\n}\n\nfunction keysToLowerCase(obj) {\n  var ret = {};\n  for (var i in obj) {\n    if (Array.isArray(obj[i])) {\n      ret[i.toLowerCase()] = obj[i].map(function (item) {\n        return item.toLowerCase();\n      });\n    } else {\n      ret[i.toLowerCase()] = obj[i];\n    }\n  }\n  return ret;\n}\n\n/**\n * FilterXSS class\n *\n * @param {Object} options\n *        whiteList (or allowList), onTag, onTagAttr, onIgnoreTag,\n *        onIgnoreTagAttr, safeAttrValue, escapeHtml\n *        stripIgnoreTagBody, allowCommentTag, stripBlankChar\n *        css{whiteList, onAttr, onIgnoreAttr} `css=false` means don't use `cssfilter`\n */\nfunction FilterXSS(options) {\n  options = shallowCopyObject(options || {});\n\n  if (options.stripIgnoreTag) {\n    if (options.onIgnoreTag) {\n      console.error(\n        'Notes: cannot use these two options \"stripIgnoreTag\" and \"onIgnoreTag\" at the same time'\n      );\n    }\n    options.onIgnoreTag = DEFAULT.onIgnoreTagStripAll;\n  }\n  if (options.whiteList || options.allowList) {\n    options.whiteList = keysToLowerCase(options.whiteList || options.allowList);\n  } else {\n    options.whiteList = DEFAULT.whiteList;\n  }\n\n  this.attributeWrapSign = options.singleQuotedAttributeValue === true ? \"'\" : DEFAULT.attributeWrapSign;\n\n  options.onTag = options.onTag || DEFAULT.onTag;\n  options.onTagAttr = options.onTagAttr || DEFAULT.onTagAttr;\n  options.onIgnoreTag = options.onIgnoreTag || DEFAULT.onIgnoreTag;\n  options.onIgnoreTagAttr = options.onIgnoreTagAttr || DEFAULT.onIgnoreTagAttr;\n  options.safeAttrValue = options.safeAttrValue || DEFAULT.safeAttrValue;\n  options.escapeHtml = options.escapeHtml || DEFAULT.escapeHtml;\n  this.options = options;\n\n  if (options.css === false) {\n    this.cssFilter = false;\n  } else {\n    options.css = options.css || {};\n    this.cssFilter = new FilterCSS(options.css);\n  }\n}\n\n/**\n * start process and returns result\n *\n * @param {String} html\n * @return {String}\n */\nFilterXSS.prototype.process = function (html) {\n  // compatible with the input\n  html = html || \"\";\n  html = html.toString();\n  if (!html) return \"\";\n\n  var me = this;\n  var options = me.options;\n  var whiteList = options.whiteList;\n  var onTag = options.onTag;\n  var onIgnoreTag = options.onIgnoreTag;\n  var onTagAttr = options.onTagAttr;\n  var onIgnoreTagAttr = options.onIgnoreTagAttr;\n  var safeAttrValue = options.safeAttrValue;\n  var escapeHtml = options.escapeHtml;\n  var attributeWrapSign = me.attributeWrapSign;\n  var cssFilter = me.cssFilter;\n\n  // remove invisible characters\n  if (options.stripBlankChar) {\n    html = DEFAULT.stripBlankChar(html);\n  }\n\n  // remove html comments\n  if (!options.allowCommentTag) {\n    html = DEFAULT.stripCommentTag(html);\n  }\n\n  // if enable stripIgnoreTagBody\n  var stripIgnoreTagBody = false;\n  if (options.stripIgnoreTagBody) {\n    stripIgnoreTagBody = DEFAULT.StripTagBody(\n      options.stripIgnoreTagBody,\n      onIgnoreTag\n    );\n    onIgnoreTag = stripIgnoreTagBody.onIgnoreTag;\n  }\n\n  var retHtml = parseTag(\n    html,\n    function (sourcePosition, position, tag, html, isClosing) {\n      var info = {\n        sourcePosition: sourcePosition,\n        position: position,\n        isClosing: isClosing,\n        isWhite: Object.prototype.hasOwnProperty.call(whiteList, tag),\n      };\n\n      // call `onTag()`\n      var ret = onTag(tag, html, info);\n      if (!isNull(ret)) return ret;\n\n      if (info.isWhite) {\n        if (info.isClosing) {\n          return \"</\" + tag + \">\";\n        }\n\n        var attrs = getAttrs(html);\n        var whiteAttrList = whiteList[tag];\n        var attrsHtml = parseAttr(attrs.html, function (name, value) {\n          // call `onTagAttr()`\n          var isWhiteAttr = _.indexOf(whiteAttrList, name) !== -1;\n          var ret = onTagAttr(tag, name, value, isWhiteAttr);\n          if (!isNull(ret)) return ret;\n\n          if (isWhiteAttr) {\n            // call `safeAttrValue()`\n            value = safeAttrValue(tag, name, value, cssFilter);\n            if (value) {\n              return name + '=' + attributeWrapSign + value + attributeWrapSign;\n            } else {\n              return name;\n            }\n          } else {\n            // call `onIgnoreTagAttr()`\n            ret = onIgnoreTagAttr(tag, name, value, isWhiteAttr);\n            if (!isNull(ret)) return ret;\n            return;\n          }\n        });\n\n        // build new tag html\n        html = \"<\" + tag;\n        if (attrsHtml) html += \" \" + attrsHtml;\n        if (attrs.closing) html += \" /\";\n        html += \">\";\n        return html;\n      } else {\n        // call `onIgnoreTag()`\n        ret = onIgnoreTag(tag, html, info);\n        if (!isNull(ret)) return ret;\n        return escapeHtml(html);\n      }\n    },\n    escapeHtml\n  );\n\n  // if enable stripIgnoreTagBody\n  if (stripIgnoreTagBody) {\n    retHtml = stripIgnoreTagBody.remove(retHtml);\n  }\n\n  return retHtml;\n};\n\nmodule.exports = FilterXSS;\n"], "names": ["DEFAULT", "parseStyle", "_", "isNull", "obj", "shallowCopyObject", "ret", "i", "FilterCSS", "options", "css", "me", "whiteList", "onAttr", "onIgnoreAttr", "safeAttrValue", "retCSS", "sourcePosition", "position", "name", "value", "source", "check", "<PERSON><PERSON><PERSON><PERSON>", "opts", "module", "getDefaultWhiteList", "REGEXP_URL_JAVASCRIPT", "exports", "filterCSS", "html", "xss", "cssLength", "isParenthesisOpen", "lastPos", "addNewAttr", "j", "c", "arr", "item", "fn", "scope", "str", "getDefaultCSSWhiteList", "defaultCSSFilter", "onTag", "tag", "onIgnoreTag", "onTagAttr", "onIgnoreTagAttr", "escapeHtml", "REGEXP_LT", "REGEXP_GT", "cssFilter", "friendlyAttrValue", "REGEXP_DEFAULT_ON_TAG_ATTR_4", "REGEXP_DEFAULT_ON_TAG_ATTR_7", "REGEXP_DEFAULT_ON_TAG_ATTR_8", "escapeAttrValue", "REGEXP_QUOTE", "REGEXP_QUOTE_2", "REGEXP_ATTR_VALUE_1", "REGEXP_ATTR_VALUE_COLON", "REGEXP_ATTR_VALUE_NEWLINE", "escapeQuote", "unescapeQuote", "escapeHtmlEntities", "code", "escapeDangerHtml5Entities", "clearNonPrintableCharacter", "str2", "len", "onIgnoreTagStripAll", "StripTagBody", "tags", "next", "isRemoveAllTag", "isRemoveTag", "removeList", "posStart", "end", "rethtml", "pos", "stripCommentTag", "retHtml", "stripBlankChar", "chars", "char", "parser", "FilterXSS", "filterXSS", "isWorkerEnv", "getTagName", "tagName", "isClosing", "parseTag", "tagStart", "quoteStart", "currentPos", "currentTagName", "currentHtml", "chariterator", "ic", "REGEXP_ILLEGAL_ATTR_NAME", "parseAttr", "lastMarkPos", "retAttrs", "tmpName", "addAttr", "v", "findNextQuotationMark", "findNextEqual", "findBeforeEqual", "stripQuoteWrap", "isQuoteWrapString", "text", "reg", "match", "getAttrs", "keysToLowerCase", "attributeWrapSign", "stripIgnoreTagBody", "info", "attrs", "whiteAttrList", "attrsHtml", "isWhiteAttr"], "sourceRoot": ""}