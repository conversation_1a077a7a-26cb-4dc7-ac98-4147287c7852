{"version": 3, "file": "p__route__create__index.75f860f2.async.js", "mappings": "oQAOMA,EAAwB,UAAM,KAAAC,EAAAC,EAClCC,KAAmCC,EAAAA,WAAkC,EAACC,EAAAF,EAA9DG,WAAYC,EAAKF,IAAA,OAAG,GAAEA,EACxBG,EAAKC,OAAOC,SAASH,CAAK,EAChCI,KAAgCC,EAAAA,UAA0B,EAACC,EAAAC,EAAAA,EAAAH,EAAA,GAApDI,EAAQF,EAAA,GAAEG,EAAWH,EAAA,GAC5BI,EAAyBC,EAAAA,EAAIC,OAAO,EAA5BC,EAAYH,EAAZG,aAEFC,EAAa,eAAAC,EAAAC,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAC,EAAOlB,EAAY,CAAF,IAAAmB,EAAAC,EAAAC,EAAA,OAAAL,EAAAA,EAAA,EAAAM,KAAA,SAAAC,EAAE,CAAF,cAAAA,EAAAC,KAAAD,EAAAE,KAAE,CAAF,OAAAF,OAAAA,EAAAE,KAAA,KACPC,EAAAA,SAAO,qBAAAC,OACd3B,CAAE,CACzB,EACG4B,OAAO,CAAC,KAAM,QAAQ,CAAC,EACvBC,aAAa,WAAY,CAAC,QAAQ,CAAC,EACnCA,aAAa,UAAW,CAAC,IAAI,CAAC,EAC9BC,IAAI,EAAC,OAAAX,EAAAI,EAAAQ,KANAX,EAAID,EAAJC,KAAMC,EAAKF,EAALE,MAOV,CAACD,GAAQC,GACXT,EAAaS,MAAM,CAAEW,SAASX,GAAK,YAALA,EAAOW,UAAW,gCAAQ,CAAC,EACzDC,EAAAA,QAAQC,KAAK,MAAM,GAEnB1B,EAAYY,CAAkC,EAC/C,wBAAAG,EAAAY,KAAA,IAAAjB,CAAA,EACF,mBAdkBkB,EAAA,QAAAtB,EAAAuB,MAAA,KAAAC,SAAA,MAoBnB,SAJAC,EAAAA,WAAU,UAAM,CACd1B,EAAcb,CAAE,CAClB,EAAG,CAACA,CAAE,CAAC,EAEFO,GAAQ,OAAAd,EAARc,EAAUA,YAAQ,MAAAd,IAAA,QAAlBA,EAAoB+C,UAIvBC,EAAAA,KAACC,EAAAA,QAAS,CACRC,SAAU,CACR3C,GAAI,GAAF2B,UAAKiB,EAAAA,GAAG,CAAC,EACXC,MAAOtC,EAASA,SAASiC,MAC3B,EACA1C,WAAYE,EACZ8C,WAASpD,EAAEa,EAASwC,WAAO,MAAArD,IAAA,cAAhBA,EAAkBM,GAC7BgD,SAAU,kBAAMf,EAAAA,QAAQgB,KAAK,CAAC,CAAC,CAChC,KAXMR,EAAAA,KAAAS,EAAAA,SAAA,EAAI,CAaf,EAEA,UAAe1D,C", "sources": ["webpack://labwise-web/./src/pages/route/create/index.tsx"], "sourcesContent": ["import { ProjectCompound, service } from '@/services/brain'\nimport { history, useParams } from '@umijs/max'\nimport { App } from 'antd'\nimport React, { useEffect, useState } from 'react'\nimport { v4 } from 'uuid'\nimport EditRoute from '../edit'\n\nconst CreateRoute: React.FC = () => {\n  const { compoundId: idStr = '' } = useParams<{ compoundId: string }>()\n  const id = Number.parseInt(idStr)\n  const [compound, setCompound] = useState<ProjectCompound>()\n  const { notification } = App.useApp()\n\n  const fetchCompound = async (id: number) => {\n    const { data, error } = await service<ProjectCompound>(\n      `project-compounds/${id}`\n    )\n      .select(['id', 'status'])\n      .populateWith('compound', ['smiles'])\n      .populateWith('project', ['id'])\n      .get()\n    if (!data || error) {\n      notification.error({ message: error?.message || '分子未找到' })\n      history.push('/404')\n    } else {\n      setCompound(data as unknown as ProjectCompound)\n    }\n  }\n\n  useEffect(() => {\n    fetchCompound(id)\n  }, [id])\n\n  if (!compound?.compound?.smiles) {\n    return <></>\n  }\n  return (\n    <EditRoute\n      mainTree={{\n        id: `${v4()}`,\n        value: compound.compound.smiles\n      }}\n      compoundId={id}\n      projectId={compound.project?.id}\n      onCancel={() => history.back()}\n    />\n  )\n}\n\nexport default CreateRoute\n"], "names": ["CreateRoute", "_compound$compound", "_compound$project", "_useParams", "useParams", "_useParams$compoundId", "compoundId", "idStr", "id", "Number", "parseInt", "_useState", "useState", "_useState2", "_slicedToArray", "compound", "setCompound", "_App$useApp", "App", "useApp", "notification", "fetchCompound", "_ref", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$service$select", "data", "error", "wrap", "_context", "prev", "next", "service", "concat", "select", "populateWith", "get", "sent", "message", "history", "push", "stop", "_x", "apply", "arguments", "useEffect", "smiles", "_jsx", "EditRoute", "mainTree", "v4", "value", "projectId", "project", "onCancel", "back", "_Fragment"], "sourceRoot": ""}