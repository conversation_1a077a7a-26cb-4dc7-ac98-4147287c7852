"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[5921],{38545:function($,E,t){t.d(E,{Z:function(){return _}});var g=t(1413),o=t(67294),I={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"}}]},name:"message",theme:"outlined"},u=I,P=t(84089),f=function(s,r){return o.createElement(P.Z,(0,g.Z)((0,g.Z)({},s),{},{ref:r,icon:u}))},D=o.forwardRef(f),_=D},40802:function($,E,t){t.d(E,{Z:function(){return R}});var g=t(81012),o=t(32884),I=t(38545),u=t(4393),P=t(66309),f=t(28036),D=t(55241),_=t(93967),p=t.n(_),s=t(70831),r={reactionCard:"reactionCard____5fXG",reactionInfo:"reactionInfo___U4SpH",reactionTitle:"reactionTitle___aJZR0",no:"no___Avx3R",title:"title___FMgkA",status:"status___xYs3R",routes:"routes___zdkV6",structure:"structure___CHZWI"},n=t(85893);function R(O){var x,B,L,e=O.reaction,T=O.isPlayground,K=O.onAction,M=O.enableToReaction,V=O.displayProject,W=function(v){var z=[];return v.map(function(A){return z.push(A==null?void 0:A.id)}),z},h=(0,s.useParams)(),U=h.id,N=(e==null?void 0:e.commendCount)&&Number(e==null?void 0:e.commendCount)>0,H=N?"".concat((0,o.oz)("comment"),"\uFF08").concat(e==null?void 0:e.commendCount,"\uFF09"):(0,o.oz)("comment");return(0,n.jsxs)(u.Z,{className:r.reactionCard,children:[e!=null&&e.reaction?(0,n.jsx)(g.Z,{structure:e==null?void 0:e.reaction,className:p()(r.structure,"enablePointer"),clickEvent:M?function(){var j;s.history.push("/projects/".concat(U||((j=e.project)===null||j===void 0?void 0:j.id),"/reaction/").concat(e==null?void 0:e.id))}:void 0}):"",(0,n.jsxs)("div",{className:r.reactionInfo,children:[(0,n.jsxs)("div",{className:p()(r.reactionTitle,"flex-justify-space-between"),children:[(0,n.jsxs)("div",{className:p()(r.no,"flex-justify-space-between"),children:[(0,n.jsxs)("span",{className:r.title,children:[(0,o.oz)("reaction-no"),e==null?void 0:e.id]}),T&&N&&e!==null&&e!==void 0&&e.updatedAt?(0,n.jsxs)("div",{children:["\xA0\xA0",(0,o.oz)("last-comment-date"),":"," ",(0,o.H3)(e==null?void 0:e.updatedAt)]}):"",e!=null&&e.collection_subject?(0,n.jsxs)("div",{children:["\xA0\xA0",(0,o.oz)("reaction-step-ID"),": ",e==null?void 0:e.collection_subject]}):""]}),(0,n.jsxs)("div",{className:r.status,children:[T?"":(0,n.jsx)(n.Fragment,{children:e!=null&&e.progress?(0,n.jsx)(P.Z,{color:"processing",children:(0,o.oz)("proceeded")}):(0,n.jsx)(P.Z,{color:"warning",children:(0,o.oz)("to-be-proceeded")})}),T?(0,n.jsx)(f.ZP,{type:"link",onClick:K,children:(0,n.jsxs)(D.Z,{content:H,children:[(0,n.jsx)(I.Z,{}),N?"\uFF08".concat(e==null?void 0:e.commendCount,"\uFF09"):""]})}):""]})]}),(0,n.jsxs)("div",{className:"display-flex",children:[(0,n.jsxs)("div",{children:[(0,o.oz)("nums-of-my-reactions"),"\uFF1A",e!=null&&(x=e.effective_procedures)!==null&&x!==void 0&&x.length?(0,n.jsx)("span",{className:"enablePointer",onClick:function(){var v;return s.history.push("/projects/".concat(U||((v=e.project)===null||v===void 0?void 0:v.id),"/reaction/").concat(e==null?void 0:e.id,"?tab=my-reaction-design"))},children:e==null||(B=e.effective_procedures)===null||B===void 0?void 0:B.length}):0]}),"\xA0\xA0\xA0",(0,n.jsxs)("div",{children:[(0,o.oz)("nums-of-my-experiments"),"\uFF1A",e!=null&&e.experiment_count?(0,n.jsx)("span",{className:"enablePointer",style:{color:"black"},onClick:function(){var v;return s.history.push("/projects/".concat(U||((v=e.project)===null||v===void 0?void 0:v.id),"/reaction/").concat(e==null?void 0:e.id,"?tab=my-experiment"))},children:e==null?void 0:e.experiment_count}):0]})]}),T?"":(0,n.jsxs)(n.Fragment,{children:[V&&(0,n.jsxs)("div",{className:"flex-align-items-center",children:[(0,n.jsxs)("div",{children:[(0,o.oz)("menu.list.project-list"),"\uFF1A"]}),(0,n.jsx)("div",{style:{color:"black"},children:(L=e.project)===null||L===void 0?void 0:L.no})]}),(0,n.jsxs)("div",{className:"display-flex",children:[(0,o.oz)("associated-routes"),"\uFF1A",(0,n.jsx)("span",{className:r.routes,children:(0,o.qt)(e==null?void 0:e.project_routes)?W(e==null?void 0:e.project_routes).join("\u3001"):"\u65E0"})]})]})]})]})}},2390:function($,E,t){t.d(E,{D:function(){return f}});var g=t(97857),o=t.n(g),I=t(55932),u=t(70831),P=function(){var _=(0,u.useAppData)(),p=_.clientRoutes,s=(0,u.useLocation)(),r=(0,u.matchRoutes)(p,s.pathname),n=r==null?void 0:r[r.length-1].route;return{matches:r,currentRoute:n}},f=function(_){var p=P(),s=p.currentRoute,r=(0,I.s5)(o()({prefix:s==null?void 0:s.path},_)),n=r.get,R=r.set;return[n,R]}},71958:function($,E,t){t.r(E);var g=t(15009),o=t.n(g),I=t(97857),u=t.n(I),P=t(99289),f=t.n(P),D=t(5574),_=t.n(D),p=t(40802),s=t(61487),r=t(2390),n=t(87172),R=t(32884),O=t(17496),x=t(64317),B=t(42525),L=t(11774),e=t(70831),T=t(4584),K=t(12617),M=t(67294),V=t(10839),W=t(92413),h=t(85893),U=function(){var H=(0,e.useModel)("@@initialState"),j=H.initialState,v=j===void 0?{}:j,z=v.userInfo,A=z===void 0?void 0:z,y=A==null?void 0:A.id,ne=(0,T.Z)(),oe=_()(ne,1),F=oe[0],k=(0,K.useWatch)("compoundNo",F),G=(0,K.useWatch)("projectId",F),re=(0,V.i)(y,G,!0),ae=(0,M.useState)([]),w=_()(ae,2),Q=w[0],se=w[1],ie=(0,M.useState)(),q=_()(ie,2),ue=q[0],ee=q[1],le=(0,s.f)(),de=le.fetch,ce=(0,r.D)(),te=_()(ce,2),_e=te[0],ve=te[1];F.setFieldsValue(_e()),(0,M.useEffect)(function(){ee(F.getFieldsValue())},[]),(0,M.useEffect)(function(){return F.setFieldsValue({compoundNo:void 0,routeId:void 0})},[G]),(0,M.useEffect)(function(){return F.setFieldsValue({routeId:void 0})},[k]),(0,M.useEffect)(function(){var l=!1;return(0,W.sQ)(y).then(function(a){l||se(a)}),function(){l=!1}},[y]);var me=function(){var l=f()(o()().mark(function a(d){var i,c,S,b,Z,m;return o()().wrap(function(C){for(;;)switch(C.prev=C.next){case 0:if(i=u()({current:1,pageSize:10,allRouteIds:[]},d),i.allRouteIds.length){C.next=3;break}return C.abrupt("return",{data:[],success:!1});case 3:return c=(0,n.query)("project-reactions").paginate(i.current,i.pageSize).sortBy([{field:"updatedAt",order:"desc"}]).populateWith("project",["id","no"]).populateDeep([{path:"project_routes",fields:["id","name"],children:[{key:"project_compound",fields:["id"]}]}]),i.projectId&&c.filterDeep("project.id","eq",i.projectId),i.routeId?c.filterDeep("project_routes.id","eq",i.routeId):(S=i.compoundNo?Q.filter(function(X){var Y;return((Y=X.project_compound)===null||Y===void 0?void 0:Y.no)===i.compoundNo}):Q,c.filterDeep("project_routes.id","in",S.length?S.map(function(X){return X.id}):[-1])),C.next=8,de(c.get());case 8:return b=C.sent,Z=b.data,m=b.meta,C.abrupt("return",{data:Z||[],total:m==null?void 0:m.pagination.total,success:!!Z});case 12:case"end":return C.stop()}},a)}));return function(d){return l.apply(this,arguments)}}();if(!y)return null;var pe=function(){var l=f()(o()().mark(function a(d){return o()().wrap(function(c){for(;;)switch(c.prev=c.next){case 0:return c.abrupt("return",ee(d));case 1:case"end":return c.stop()}},a)}));return function(d){return l.apply(this,arguments)}}(),fe=(0,h.jsxs)(O.M,{bordered:!0,onFinish:pe,form:F,onValuesChange:function(a,d){return ve(d)},children:[(0,h.jsx)(x.Z,{name:"projectId",placeholder:(0,R.oz)("project-ID"),request:function(a){var d=a.userId;return(0,W.TL)(d)},params:{userId:y},debounceTime:300,fieldProps:{onClick:function(a){return a.stopPropagation()}},showSearch:!0}),(0,h.jsx)(x.Z,u()(u()({},re),{},{name:"compoundNo",placeholder:(0,R.oz)("target-molecules")})),(0,h.jsx)(x.Z,{name:"routeId",placeholder:(0,R.oz)("Routes"),request:function(){var l=f()(o()().mark(function a(d){var i,c,S,b;return o()().wrap(function(m){for(;;)switch(m.prev=m.next){case 0:return i=d.userId,c=d.compoundNo,S=d.projectId,m.next=3,(0,W.sQ)(i,S,c);case 3:return b=m.sent,m.abrupt("return",b.map(function(J){return{label:J.name,value:J.id}}));case 5:case"end":return m.stop()}},a)}));return function(a){return l.apply(this,arguments)}}(),params:{userId:y,compoundNo:k,projectId:G},debounceTime:300,fieldProps:{onClick:function(a){return a.stopPropagation()}},showSearch:!0})]}),he=(0,h.jsx)(B.Rs,{ghost:!0,pagination:{defaultPageSize:10,showSizeChanger:!1},showActions:"hover",rowSelection:!1,grid:{column:2},renderItem:function(a){return(0,h.jsx)(p.Z,{reaction:a,enableToReaction:!0,displayProject:!0})},request:me,params:u()(u()({},ue),{},{allRouteIds:Q}),className:"noPaddingCard"});return(0,h.jsx)(L._z,{ghost:!0,content:fe,children:he})};E.default=U}}]);

//# sourceMappingURL=p__workspace__my-reaction__index.0df09684.async.js.map