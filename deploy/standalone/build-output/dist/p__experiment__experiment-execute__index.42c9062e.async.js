"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[2972],{52461:function(R,g,n){n.r(g),n.d(g,{default:function(){return L}});var z=n(97857),l=n.n(z),b=n(5574),y=n.n(b),C=n(54025),S=n(52595),d=n(43851),j=n(34369),E=n(11774),v=n(85673),D=n(93967),I=n.n(D),h=n(96486),T=n(67294),e=n(32884),V=n(66309),o=n(85893),W=[{title:"\u5B9E\u9A8C\u6D41\u7A0B\u540D\u79F0",dataIndex:"design_name",align:"left",width:180},{title:(0,e.oz)("pages.experiment.label.name"),dataIndex:"experiment_name",align:"left",width:180},{title:(0,e.oz)("pages.experiment.label.status"),dataIndex:"status",align:"center",width:150,render:function(t){var a={running:(0,e.oz)("component.notification.statusValue.running"),hold:(0,e.oz)("experiment-pending"),canceled:(0,e.oz)("pages.projectTable.statusLabel.cancelled"),completed:(0,e.oz)("component.notification.statusValue.success"),success:(0,e.oz)("app.general.message.success"),incident:(0,e.oz)("experiment-exception"),failed:(0,e.oz)("component.notification.statusValue.failed")};return t&&(0,o.jsx)(V.Z,{color:d.vm[t],children:a[t]})}},{title:(0,e.oz)("experiment-actual-start-time"),dataIndex:"start_time",align:"left",width:150,render:function(t){return t?(0,e.S9)(t):""}},{title:(0,e.oz)("experiment-actual-end-time"),dataIndex:"end_time",width:150,align:"left",render:function(t){return t?(0,e.S9)(t):""}},{title:(0,e.oz)("pages.experiment.label.operation"),align:"center",fixed:"right",width:120,render:function(t,a){var s=a.experiment_no;return(0,o.jsx)("a",{onClick:function(){return(0,e.y0)(s)},children:"\u5B9E\u9A8C\u8BE6\u60C5"})}}],Z={experimentExecute:"experimentExecute___qnrzL"},A=[{label:(0,e.oz)("pages.experiment.label.status"),ctype:"select",key:"status",enums:[{label:(0,e.oz)("component.notification.statusValue.running"),value:"running"},{label:(0,e.oz)("experiment-exception"),value:"incident"},{label:(0,e.oz)("experiment-pending"),value:"hold"},{label:(0,e.oz)("component.notification.statusValue.failed"),value:"failed"},{label:(0,e.oz)("component.notification.statusValue.success"),value:"completed"},{label:(0,e.oz)("app.general.message.success"),value:"success"}],placeholder:(0,e.oz)("input-tip"),XL:{col:5,labelWidth:7,wrapperWidth:17}},{label:(0,e.oz)("reaction-no"),ctype:"input",key:"rxn_no",placeholder:(0,e.oz)("select-tip"),XL:{col:7,labelWidth:6,wrapperWidth:17}},{label:(0,e.oz)("pages.experiment.label.name"),ctype:"input",key:"experiment_name",placeholder:(0,e.oz)("input-tip"),XL:{col:5,labelWidth:7,wrapperWidth:17}}];function L(){var r=(0,T.useState)(d.mw),t=y()(r,2),a=t[0],s=t[1],m=(0,j.Z)(a,d.AN),P=m.loading,w=m.listData,x=m.total,B={loading:P,bordered:!0,dataSource:w,pagination:{total:x,current:a.page_no,pageSize:a.page_size,showTotal:function(){return"\u5171".concat(x,"\u6761\u8BB0\u5F55")},showQuickJumper:!0,showSizeChanger:!0}};return(0,o.jsxs)(E._z,{breadcrumbRender:function(u){var c=u.breadcrumb,f=c==null?void 0:c.items;return(0,h.isArray)(f)&&!(0,h.isEmpty)(f)?(0,o.jsx)(v.Z,{children:f.map(function(i,F){return(0,o.jsx)(v.Z.Item,{onClick:function(N){F===0&&N.preventDefault()},href:i==null?void 0:i.linkPath,children:i.breadcrumbName},i==null?void 0:i.linkPath)})}):""},className:I()(Z.experimentExecute),children:[(0,o.jsx)(S.Z,{formData:A,onSubmit:function(u){return s(l()(l()(l()({},a),u),{},{pageNo:1}))},onReset:function(){return s(d.mw)}}),(0,o.jsx)(C.Z,l()(l()({},B),{},{columns:W,rowKey:"experiment_design_no",onChange:function(u,c){s(l()(l()({},a),{},{page_no:u,page_size:c}))}}))]})}}}]);

//# sourceMappingURL=p__experiment__experiment-execute__index.42c9062e.async.js.map