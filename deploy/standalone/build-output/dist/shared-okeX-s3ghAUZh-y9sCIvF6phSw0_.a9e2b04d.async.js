"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[75],{33188:function(z,h,e){e.d(h,{Z:function(){return a}});var C=e(36569),t=e(93967),p=e.n(t),u={anchor:"anchor___bISZp"},x=e(85893);function a(v){return(0,x.jsx)(C.Z,{onClick:function(b,S){return location.replace(S.href)},className:p()(u.anchor,v==null?void 0:v.wrapClassName),affix:!1,items:v==null?void 0:v.items})}},69260:function(z,h,e){e.d(h,{Z:function(){return i}});var C=e(5574),t=e.n(C),p=e(21987),u=e(15394),x=e(34528),a=e(93967),v=e.n(a),L=e(67294),b={procedureText:"procedureText___oDxRq",expandedIcon:"expandedIcon___SeifF"},S=e(85893);function i(m){var s=m.procedure,r=m.rows,c=s.text,D=(0,L.useState)(!1),W=t()(D,2),M=W[0],ee=W[1],Y=(0,S.jsx)(u.Z,{className:b.expandedIcon,onClick:function(te){te.stopPropagation(),ee(function(ne){return!ne})},children:M?"less":"more"});return(0,S.jsxs)("div",{className:v()(b.procedureText),children:[(0,S.jsx)(x.Z,{level:5,children:"Procedure"}),(0,S.jsxs)(p.Z,{ellipsis:M?!1:{rows:r||5,expandable:!0,symbol:Y},copyable:{text:c},children:[c||(s==null?void 0:s.experimentalProcedure)||"",M&&Y]})]})}},17322:function(z,h,e){e.d(h,{Z:function(){return x}});var C=e(93967),t=e.n(C),p={sectionTitle:"sectionTitle___KIteW",extraCom:"extraCom___ymouh"},u=e(85893);function x(a){return(0,u.jsxs)("div",{className:t()(p.sectionTitle,a==null?void 0:a.wrapClassName),id:a==null?void 0:a.anchorId,children:[(0,u.jsx)("h2",{children:a==null?void 0:a.word}),a!=null&&a.extra?(0,u.jsx)("div",{className:p.extraCom,children:a==null?void 0:a.extra}):null]})}},36569:function(z,h,e){e.d(h,{Z:function(){return $e}});var C=e(74902),t=e(67294),p=e(93967),u=e.n(p),x=e(66680),a=e(17423),v=e(66367),L=e(58375),b=e(30291),S=e(53124),i=e(35792),s=t.createContext(void 0),c=l=>{const{href:n,title:f,prefixCls:O,children:g,className:I,target:$,replace:P}=l,X=t.useContext(s),{registerLink:H,unregisterLink:V,scrollTo:E,onClick:F,activeLink:A,direction:Q}=X||{};t.useEffect(()=>(H==null||H(n),()=>{V==null||V(n)}),[n]);const G=B=>{F==null||F(B,{title:f,href:n}),E==null||E(n),P&&(B.preventDefault(),window.location.replace(n))},{getPrefixCls:J}=t.useContext(S.E_),T=J("anchor",O),q=A===n,w=u()(`${T}-link`,I,{[`${T}-link-active`]:q}),_=u()(`${T}-link-title`,{[`${T}-link-title-active`]:q});return t.createElement("div",{className:w},t.createElement("a",{className:_,href:n,title:typeof f=="string"?f:"",target:$,onClick:G},f),Q!=="horizontal"?g:null)},D=e(85982),W=e(14747),M=e(83559),ee=e(83262);const Y=l=>{const{componentCls:n,holderOffsetBlock:f,motionDurationSlow:O,lineWidthBold:g,colorPrimary:I,lineType:$,colorSplit:P,calc:X}=l;return{[`${n}-wrapper`]:{marginBlockStart:X(f).mul(-1).equal(),paddingBlockStart:f,[n]:Object.assign(Object.assign({},(0,W.Wf)(l)),{position:"relative",paddingInlineStart:g,[`${n}-link`]:{paddingBlock:l.linkPaddingBlock,paddingInline:`${(0,D.unit)(l.linkPaddingInlineStart)} 0`,"&-title":Object.assign(Object.assign({},W.vS),{position:"relative",display:"block",marginBlockEnd:l.anchorTitleBlock,color:l.colorText,transition:`all ${l.motionDurationSlow}`,"&:only-child":{marginBlockEnd:0}}),[`&-active > ${n}-link-title`]:{color:l.colorPrimary},[`${n}-link`]:{paddingBlock:l.anchorPaddingBlockSecondary}}}),[`&:not(${n}-wrapper-horizontal)`]:{[n]:{"&::before":{position:"absolute",insetInlineStart:0,top:0,height:"100%",borderInlineStart:`${(0,D.unit)(g)} ${$} ${P}`,content:'" "'},[`${n}-ink`]:{position:"absolute",insetInlineStart:0,display:"none",transform:"translateY(-50%)",transition:`top ${O} ease-in-out`,width:g,backgroundColor:I,[`&${n}-ink-visible`]:{display:"inline-block"}}}},[`${n}-fixed ${n}-ink ${n}-ink`]:{display:"none"}}}},de=l=>{const{componentCls:n,motionDurationSlow:f,lineWidthBold:O,colorPrimary:g}=l;return{[`${n}-wrapper-horizontal`]:{position:"relative","&::before":{position:"absolute",left:{_skip_check_:!0,value:0},right:{_skip_check_:!0,value:0},bottom:0,borderBottom:`${(0,D.unit)(l.lineWidth)} ${l.lineType} ${l.colorSplit}`,content:'" "'},[n]:{overflowX:"scroll",position:"relative",display:"flex",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"},[`${n}-link:first-of-type`]:{paddingInline:0},[`${n}-ink`]:{position:"absolute",bottom:0,transition:`left ${f} ease-in-out, width ${f} ease-in-out`,height:O,backgroundColor:g}}}}},te=l=>({linkPaddingBlock:l.paddingXXS,linkPaddingInlineStart:l.padding});var ne=(0,M.I$)("Anchor",l=>{const{fontSize:n,fontSizeLG:f,paddingXXS:O,calc:g}=l,I=(0,ee.mergeToken)(l,{holderOffsetBlock:O,anchorPaddingBlockSecondary:g(O).div(2).equal(),anchorTitleBlock:g(n).div(14).mul(3).equal(),anchorBallSize:g(f).div(2).equal()});return[Y(I),de(I)]},te);function Ce(){return window}function ue(l,n){if(!l.getClientRects().length)return 0;const f=l.getBoundingClientRect();return f.width||f.height?n===window?f.top-l.ownerDocument.documentElement.clientTop:f.top-n.getBoundingClientRect().top:f.top}const fe=/#([\S ]+)$/;var Se=l=>{var n;const{rootClassName:f,prefixCls:O,className:g,style:I,offsetTop:$,affix:P=!0,showInkInFixed:X=!1,children:H,items:V,direction:E="vertical",bounds:F,targetOffset:A,onClick:Q,onChange:G,getContainer:J,getCurrentAnchor:T,replace:q}=l,[w,_]=t.useState([]),[B,Te]=t.useState(null),oe=t.useRef(B),he=t.useRef(null),le=t.useRef(null),re=t.useRef(!1),{direction:ke,anchor:R,getTargetContainer:Oe,getPrefixCls:Ee}=t.useContext(S.E_),j=Ee("anchor",O),me=(0,i.Z)(j),[je,be,Pe]=ne(j,me),K=(n=J!=null?J:Oe)!==null&&n!==void 0?n:Ce,ie=JSON.stringify(w),Le=(0,x.Z)(o=>{w.includes(o)||_(d=>[].concat((0,C.Z)(d),[o]))}),Ie=(0,x.Z)(o=>{w.includes(o)&&_(d=>d.filter(k=>k!==o))}),Ae=()=>{var o;const d=(o=he.current)===null||o===void 0?void 0:o.querySelector(`.${j}-link-title-active`);if(d&&le.current){const{style:k}=le.current,y=E==="horizontal";k.top=y?"":`${d.offsetTop+d.clientHeight/2}px`,k.height=y?"":`${d.clientHeight}px`,k.left=y?`${d.offsetLeft}px`:"",k.width=y?`${d.clientWidth}px`:"",y&&(0,a.Z)(d,{scrollMode:"if-needed",block:"nearest"})}},Be=function(o){let d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,k=arguments.length>2&&arguments[2]!==void 0?arguments[2]:5;const y=[],ce=K();return o.forEach(Z=>{const N=fe.exec(Z==null?void 0:Z.toString());if(!N)return;const U=document.getElementById(N[1]);if(U){const ye=ue(U,ce);ye<=d+k&&y.push({link:Z,top:ye})}}),y.length?y.reduce((N,U)=>U.top>N.top?U:N).link:""},ae=(0,x.Z)(o=>{if(oe.current===o)return;const d=typeof T=="function"?T(o):o;Te(d),oe.current=d,G==null||G(o)}),se=t.useCallback(()=>{if(re.current)return;const o=Be(w,A!==void 0?A:$||0,F);ae(o)},[ie,A,$]),ge=t.useCallback(o=>{ae(o);const d=fe.exec(o);if(!d)return;const k=document.getElementById(d[1]);if(!k)return;const y=K(),ce=(0,v.Z)(y),Z=ue(k,y);let N=ce+Z;N-=A!==void 0?A:$||0,re.current=!0,(0,L.Z)(N,{getContainer:K,callback(){re.current=!1}})},[A,$]),Ne=u()(be,Pe,me,f,`${j}-wrapper`,{[`${j}-wrapper-horizontal`]:E==="horizontal",[`${j}-rtl`]:ke==="rtl"},g,R==null?void 0:R.className),Ze=u()(j,{[`${j}-fixed`]:!P&&!X}),we=u()(`${j}-ink`,{[`${j}-ink-visible`]:B}),Re=Object.assign(Object.assign({maxHeight:$?`calc(100vh - ${$}px)`:"100vh"},R==null?void 0:R.style),I),pe=o=>Array.isArray(o)?o.map(d=>t.createElement(c,Object.assign({replace:q},d,{key:d.key}),E==="vertical"&&pe(d.children))):null,xe=t.createElement("div",{ref:he,className:Ne,style:Re},t.createElement("div",{className:Ze},t.createElement("span",{className:we,ref:le}),"items"in l?pe(V):H));t.useEffect(()=>{const o=K();return se(),o==null||o.addEventListener("scroll",se),()=>{o==null||o.removeEventListener("scroll",se)}},[ie]),t.useEffect(()=>{typeof T=="function"&&ae(T(oe.current||""))},[T]),t.useEffect(()=>{Ae()},[E,T,ie,B]);const ze=t.useMemo(()=>({registerLink:Le,unregisterLink:Ie,scrollTo:ge,activeLink:B,onClick:Q,direction:E}),[B,Q,ge,E]),De=P&&typeof P=="object"?P:void 0;return je(t.createElement(s.Provider,{value:ze},P?t.createElement(b.Z,Object.assign({offsetTop:$,target:K},De),xe):xe))};const ve=Se;ve.Link=c;var $e=ve},15394:function(z,h,e){var C,t=e(64836).default,p=e(75263).default;C={value:!0},h.Z=void 0;var u=p(e(67294)),x=t(e(18475)),a=e(13594),v=t(e(28460)),L=function(i,m){var s={};for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&m.indexOf(r)<0&&(s[r]=i[r]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,r=Object.getOwnPropertySymbols(i);c<r.length;c++)m.indexOf(r[c])<0&&Object.prototype.propertyIsEnumerable.call(i,r[c])&&(s[r[c]]=i[r[c]]);return s};const b=(i,m)=>{var{ellipsis:s}=i,r=L(i,["ellipsis"]);const c=u.useMemo(()=>s&&typeof s=="object"?(0,x.default)(s,["expandable","rows"]):s,[s]);return u.createElement(v.default,Object.assign({ref:m},r,{ellipsis:c,component:"span"}))};var S=h.Z=u.forwardRef(b)},34528:function(z,h,e){var C,t=e(64836).default,p=e(75263).default;C={value:!0},h.Z=void 0;var u=p(e(67294)),x=e(13594),a=t(e(28460)),v=function(i,m){var s={};for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&m.indexOf(r)<0&&(s[r]=i[r]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var c=0,r=Object.getOwnPropertySymbols(i);c<r.length;c++)m.indexOf(r[c])<0&&Object.prototype.propertyIsEnumerable.call(i,r[c])&&(s[r[c]]=i[r[c]]);return s};const L=[1,2,3,4,5],b=u.forwardRef((i,m)=>{const{level:s=1}=i,r=v(i,["level"]),c=L.includes(s)?`h${s}`:"h1";return u.createElement(a.default,Object.assign({ref:m},r,{component:c}))});var S=h.Z=b}}]);

//# sourceMappingURL=shared-okeX-s3ghAUZh-y9sCIvF6phSw0_.a9e2b04d.async.js.map