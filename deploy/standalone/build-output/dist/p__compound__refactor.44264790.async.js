(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[5060],{95985:function(J,Y){"use strict";var o={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880.1 154H143.9c-24.5 0-39.8 26.7-27.5 48L349 597.4V838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V597.4L907.7 202c12.2-21.3-3.1-48-27.6-48zM603.4 798H420.6V642h182.9v156zm9.6-236.6l-9.5 16.6h-183l-9.5-16.6L212.7 226h598.6L613 561.4z"}}]},name:"filter",theme:"outlined"};Y.Z=o},19669:function(J,Y,o){"use strict";var j=o(1413),T=o(67294),S=o(95985),V=o(84089),X=function(ue,_){return T.createElement(V.Z,(0,j.Z)((0,j.Z)({},ue),{},{ref:_,icon:S.Z}))},D=T.forwardRef(X);Y.Z=D},56717:function(J,Y,o){"use strict";var j=o(1413),T=o(67294),S=o(93696),V=o(84089),X=function(ue,_){return T.createElement(V.Z,(0,j.Z)((0,j.Z)({},ue),{},{ref:_,icon:S.Z}))},D=T.forwardRef(X);Y.Z=D},86615:function(J,Y,o){"use strict";var j=o(1413),T=o(45987),S=o(22270),V=o(78045),X=o(67294),D=o(90789),ge=o(92755),ue=o(85893),_=["fieldProps","options","radioType","layout","proFieldProps","valueEnum"],b=X.forwardRef(function(Z,de){var H=Z.fieldProps,U=Z.options,q=Z.radioType,be=Z.layout,w=Z.proFieldProps,ve=Z.valueEnum,je=(0,T.Z)(Z,_);return(0,ue.jsx)(ge.Z,(0,j.Z)((0,j.Z)({valueType:q==="button"?"radioButton":"radio",ref:de,valueEnum:(0,S.h)(ve,void 0)},je),{},{fieldProps:(0,j.Z)({options:U,layout:be},H),proFieldProps:w,filedConfig:{customLightMode:!0}}))}),p=X.forwardRef(function(Z,de){var H=Z.fieldProps,U=Z.children;return(0,ue.jsx)(V.ZP,(0,j.Z)((0,j.Z)({},H),{},{ref:de,children:U}))}),ce=(0,D.G)(p,{valuePropName:"checked",ignoreWidth:!0}),ie=ce;ie.Group=b,ie.Button=V.ZP.Button,ie.displayName="ProFormComponent",Y.Z=ie},90637:function(J,Y,o){"use strict";o.d(Y,{Z:function(){return Z}});var j=o(15009),T=o.n(j),S=o(99289),V=o.n(S),X=o(5574),D=o.n(X),ge=o(32884),ue=o(87172),_=o(96486),b=function(){var de=V()(T()().mark(function H(){var U,q;return T()().wrap(function(w){for(;;)switch(w.prev=w.next){case 0:return w.next=2,(0,ue.query)("retro-preference-configs").paginate(1,1e3).get();case 2:if(U=w.sent,q=U.data,q!=null&&q.length){w.next=6;break}return w.abrupt("return",[]);case 6:return w.abrupt("return",(0,_.sortBy)(q,function(ve){return ve.order}));case 7:case"end":return w.stop()}},H)}));return function(){return de.apply(this,arguments)}}(),p=o(86615),ce=o(67294),ie=o(85893);function Z(){var de=(0,ce.useState)([]),H=D()(de,2),U=H[0],q=H[1],be=function(){var w=V()(T()().mark(function ve(){var je;return T()().wrap(function(me){for(;;)switch(me.prev=me.next){case 0:return me.next=2,b();case 2:je=me.sent,q(je);case 4:case"end":return me.stop()}},ve)}));return function(){return w.apply(this,arguments)}}();return(0,ce.useEffect)(function(){be()},[]),(0,ie.jsx)(ie.Fragment,{children:(0,ge.qt)(U)&&U.map(function(w,ve){return(0,ie.jsx)(p.Z.Group,{label:w==null?void 0:w.label,name:w==null?void 0:w.field,options:w.options},"".concat(ve,"-retroConfigs"))})})}},54453:function(J,Y,o){"use strict";o.d(Y,{Z:function(){return S}});var j=o(32884),T=o(85893);function S(V){var X={fte:(0,j.oz)("pages.projectTable.typeValue.fte"),ffs:(0,j.oz)("pages.projectTable.typeValue.ffs"),personal:(0,j.oz)("pages.projectTable.typeValue.personal"),designing:(0,j.oz)("molecules-status.designing"),synthesizing:(0,j.oz)("molecules-status.synthesizing"),created:(0,j.oz)("pages.projectTable.statusLabel.created"),started:(0,j.oz)("pages.projectTable.statusLabel.started"),finished:(0,j.oz)("component.notification.statusValue.success"),holding:(0,j.oz)("pages.projectTable.statusLabel.holding"),cancelled:(0,j.oz)("pages.projectTable.statusLabel.cancelled"),canceled:(0,j.oz)("pages.projectTable.statusLabel.cancelled")},D=V.word;return(0,T.jsx)(T.Fragment,{children:X["".concat(D)]||""})}},69776:function(J,Y,o){"use strict";var j=o(96486),T=o.n(j),S=o(63686),V=function(){var D=[{value:"created",label:(0,S.oz)("molecules-status.created")},{value:"designing",label:(0,S.oz)("molecules-status.designing")},{value:"synthesizing",label:(0,S.oz)("molecules-status.synthesizing")},{value:"finished",label:(0,S.oz)("component.notification.statusValue.success")},{value:"canceled",label:(0,S.oz)("pages.projectTable.statusLabel.cancelled")}],ge=[{value:"product",label:(0,S.oz)("product")},{value:"main_reactant",label:(0,S.oz)("main-reactant")},{value:"reactant",label:(0,S.oz)("reactant")},{value:"other_reagent",label:(0,S.oz)("other-reagent")}],ue=[{label:(0,S.oz)("same-key-material"),value:"start_material"},{label:(0,S.oz)("algorithm-cluster"),value:"cluster"},{label:(0,S.oz)("not-grouped"),value:"ungrouped"}],_=[{label:(0,S.oz)("algorithmic-score"),value:"score"},{label:(0,S.oz)("known-reaction-proportion"),value:"known_reaction_rate"},{label:(0,S.oz)("longest-chain-l"),value:"backbone_length"},{label:(0,S.oz)("route-length"),value:"min_n_main_tree_steps"}],b={createdAt:(0,S.oz)("creation-time"),updatedAt:(0,S.oz)("last-update-time"),no:(0,S.oz)("name")},p={target:(0,S.oz)("target-molecules"),building_block:(0,S.oz)("key-intermediate"),temp_block:(0,S.oz)("show-materials")},ce=(0,j.omit)(p,"temp_block"),ie={onlyOneLineEditorAlertMessage:(0,S.oz)("only-one-edit"),onlyAddOneLineAlertMessage:(0,S.oz)("only-one-added")},Z={total_cost:(0,S.oz)("total"),material_cost:(0,S.oz)("material-cost"),labor_cost:(0,S.oz)("labor-cost")},de={draft:(0,S.oz)("draft"),published:(0,S.oz)("in-use"),deleted:(0,S.oz)("deleted")},H={success:(0,S.oz)("pages.experiment.statusLabel.success"),fail:(0,S.oz)("pages.experiment.statusLabel.failed"),processing:(0,S.oz)("pages.experiment.statusLabel.running")},U={limited:(0,S.oz)("component.notification.statusValue.limited"),completed:(0,S.oz)("component.notification.statusValue.success"),running:(0,S.oz)("component.notification.statusValue.running"),pending:(0,S.oz)("component.notification.statusValue.pending"),failed:(0,S.oz)("component.notification.statusValue.failed")},q={working:(0,S.oz)("working"),holding:(0,S.oz)("hold"),error:(0,S.oz)("unavailable"),idle:(0,S.oz)("idle")};return{moleculeStatusOptions:D,reactionRoleOptions:ge,groupOptions:ue,proportionOptions:_,typeMap:p,sortStandard:b,typeMapForSelect:ce,editableConfig:ie,chargeDes:Z,materialManageStauts:de,aiGenerateStauts:U,aiAIInferenceStauts:H,robotStatus:q}};Y.Z=V},22969:function(J,Y,o){"use strict";o.r(Y),o.d(Y,{default:function(){return Mo}});var j=o(5574),T=o.n(j),S=o(49677),V=o.n(S),X=o(11774),D=o(70831),ge=o(72717),ue=o(93967),_=o.n(ue),b=o(67294),p=o(32884),ce=o(4393),ie=o(15009),Z=o.n(ie),de=o(99289),H=o.n(de),U=o(87172),q=o(77837),be=function(){var s=H()(Z()().mark(function t(e){var n,a,i,l,u,c;return Z()().wrap(function(v){for(;;)switch(v.prev=v.next){case 0:return v.next=2,(0,U.service)("project-compounds").selectManyByID([e]).populateWith("project_routes",["id"]).populateWith("compound",["id","smiles"]).populateDeep([{path:"retro_processes",fields:["id"],children:[{key:"retro_backbones",fields:["id"]}]}]).populateWith("default_route",["id"]).populateWith("project",["id"]).get();case 2:if(n=v.sent,a=n.data,i=n.error,!(!i&&a!==null&&a!==void 0&&a[0])){v.next=10;break}return c=a[0],c.project_routes_number=(l=c.project_routes)===null||l===void 0?void 0:l.length,c.retro_backbones_number=(u=c.retro_processes)===null||u===void 0?void 0:u.flatMap(function(m){return m.retro_backbones}).length,v.abrupt("return",c);case 10:throw new Error("Network response was not ok");case 11:case"end":return v.stop()}},t)}));return function(e){return s.apply(this,arguments)}}(),w=function(t){var e,n,a=typeof t!="number"?Number.parseInt(t||""):t,i=(0,q.a)({queryKey:["project-compound",a],queryFn:function(){return t?be(a):void 0},enabled:!isNaN(a)}),l=i.data,u=i.error,c=i.isLoading,h=i.refetch;if(isNaN(a))return{};var v=l==null||(e=l.project)===null||e===void 0?void 0:e.id;return{data:l,error:u,isLoading:c,refetch:h,projectId:v,defaultRouteId:l==null||(n=l.default_route)===null||n===void 0?void 0:n.id}},ve=o(82869),je=o(97857),R=o.n(je),me=o(73445),Re=o(782),ft={retroModel:"close",filterDrawer:!1},Pe=(0,me.Ue)((0,Re.XR)((0,Re.$e)(R()({},ft),function(s){return{set:function(e){return s(function(n){return R()(R()({},n),e)})},setRetroModel:function(e){return s(function(n){return R()(R()({},n),{},{retroModel:e})})},setFilterDrawer:function(e){return s(function(n){return R()(R()({},n),{},{filterDrawer:e})})},setCompoundId:function(e){return s(function(n){return R()(R()({},n),{},{compoundId:e})})}}}))),fe=o(43425),He=o(92783),Oe=o(66309),te=o(96074),Be=o(69484),qe=o(9783),Ye=o.n(qe),et={tabType:"aiGenerated",counts:{aiGenerated:0,myRoutes:0,reaction:0},retroLayout:"group",retroRouteFilters:{group:"start_material"}},xe=(0,me.Ue)((0,Re.XR)((0,Re.$e)(R()({},et),function(s){return{set:function(e){return s(function(n){return R()(R()({},n),e)})},setTabType:function(e){return s(function(n){return R()(R()({},n),{},{tabType:e})})},setCount:function(e,n){return s(function(a){var i=a.counts[e];return i!==n?R()(R()({},a),{},{counts:R()(R()({},a.counts),{},Ye()({},e,n))}):a})},setRetroLayout:function(e){return s(function(n){return R()(R()({},n),{},{retroLayout:e})})},setRetroRouteFilters:function(e){return s(function(n){return R()(R()({},n),{},{retroRouteFilters:e})})},setPagination:function(e,n){return s(function(a){return R()(R()({},a),{},{retroRouteFilters:R()(R()({},a.retroRouteFilters),{},{page:e,pageSize:n})})})},setPreference:function(e){return s(function(n){return R()(R()({},n),{},{retroRouteFilters:R()(R()({},n.retroRouteFilters),{},{preference:e,page:1})})})},setGroupSimilarId:function(e){return s(function(n){return R()(R()({},n),{},{retroRouteFilters:R()(R()({},n.retroRouteFilters),{},{groupSimilarId:e,page:1})})})}}}))),r=o(85893),pt=null,d=function(t){var e=t.compoundId,n=Pe(),a=n.setFilterDrawer,i=(0,ve.Li)(),l=i.setting,u=l===void 0?{}:l,c=u.retro,h=c===void 0?{}:c,v=h.display_feasibility_layout,m=(0,Be.Z)(e,!0),f=m.selected,x=f===void 0?{}:f,C=x.backbone_tree,P=x.status,N=xe(),E=N.retroLayout,M=N.retroRouteFilters,B=M.groupSimilarId,I=M.preference,$=N.setGroupSimilarId,F=N.setRetroLayout,G=(I==null?void 0:I.novelty_score)||(I==null?void 0:I.price_score)||(I==null?void 0:I.safety_score),se=["group"];C&&se.push("filter"),v&&se.push("feasibility");var L=se.map(function(ze){return{label:(0,p.oz)("retro-route-".concat(ze,"-layout")),value:ze}});(0,b.useEffect)(function(){!C&&E==="filter"&&F("group")},[C]);var ee=(0,r.jsx)(He.Z,{options:L,value:E,onChange:F}),pe=B!==void 0&&E==="group"&&P==="completed"?(0,r.jsx)(Oe.Z,{closable:!0,color:"pink",onClose:function(){return $()},children:"".concat((0,p.oz)("group")).concat(B+1)},"similarTag"):null,Ne=E==="group"&&P==="completed"?(0,r.jsx)(fe.Z,{onClick:function(){return a(!0)},color:G?"#0047BB":"#191919"}):null;return(0,r.jsxs)(r.Fragment,{children:[pe,Ne,pe||Ne?(0,r.jsx)(te.Z,{type:"vertical"}):null,ee]})},g=d,O=o(51042),y=o(28036),z=function(t){var e,n=t.compoundId,a=(0,D.useAccess)(),i=w(n),l=i.data,u=a==null||(e=a.authCodeList)===null||e===void 0?void 0:e.includes("compound.button.new-route"),c=(0,r.jsxs)(y.ZP,{type:"primary",block:!0,size:"small",onClick:function(){var v;return D.history.push("/projects/".concat(l==null||(v=l.project)===null||v===void 0?void 0:v.id,"/compound/").concat(l==null?void 0:l.id,"/create"))},hidden:!u,disabled:!(l!=null&&l.input_smiles),children:[(0,r.jsx)(O.Z,{}),(0,p.oz)("new-route")]});return n?c:null},W=z,A={root:"root____E7ly"},K=o(30042),re=function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:1,e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:10,n=(0,b.useState)({page:t,pageSize:e}),a=T()(n,2),i=a[0],l=a[1],u=function(m){l(function(f){return R()(R()({},f),{},{page:m})})},c=function(m){l(function(f){return R()(R()({},f),{},{pageSize:m})})},h=function(m){l(m)};return R()(R()({},i),{},{setPage:u,setPageSize:c,setPagination:h})},oe=re,le=o(74330),Se=o(72252),Q={cardRoot:"cardRoot___nf3iP",root:"root___GJ5DN",reactionWrapper:"reactionWrapper___n00cS",arrowWrapper:"arrowWrapper___MFx75",reactionBtns:"reactionBtns___UtXuu",reactionBtn:"reactionBtn___B59Z2",structureWrapper:"structureWrapper___Pid6p",structureInfo:"structureInfo___xgUUL",filterInfo:"filterInfo___uCsjQ"},he=o(51562),Le=o(4019),Me=o(99814),Ee=o(71181),ne=o(1413),$e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M878.3 392.1L631.9 145.7c-6.5-6.5-15-9.7-23.5-9.7s-17 3.2-23.5 9.7L423.8 306.9c-12.2-1.4-24.5-2-36.8-2-73.2 0-146.4 24.1-206.5 72.3a33.23 33.23 0 00-2.7 49.4l181.7 181.7-215.4 215.2a15.8 15.8 0 00-4.6 9.8l-3.4 37.2c-.9 9.4 6.6 17.4 15.9 17.4.5 0 1 0 1.5-.1l37.2-3.4c3.7-.3 7.2-2 9.8-4.6l215.4-215.4 181.7 181.7c6.5 6.5 15 9.7 23.5 9.7 9.7 0 19.3-4.2 25.9-12.4 56.3-70.3 79.7-158.3 70.2-243.4l161.1-161.1c12.9-12.8 12.9-33.8 0-46.8zM666.2 549.3l-24.5 24.5 3.8 34.4a259.92 259.92 0 01-30.4 153.9L262 408.8c12.9-7.1 26.3-13.1 40.3-17.9 27.2-9.4 55.7-14.1 84.7-14.1 9.6 0 19.3.5 28.9 1.6l34.4 3.8 24.5-24.5L608.5 224 800 415.5 666.2 549.3z"}}]},name:"pushpin",theme:"outlined"},ye=$e,k=o(84089),Ie=function(t,e){return b.createElement(k.Z,(0,ne.Z)((0,ne.Z)({},t),{},{ref:e,icon:ye}))},Te=b.forwardRef(Ie),tt=Te,We=o(50756),rt=function(t,e){return b.createElement(k.Z,(0,ne.Z)((0,ne.Z)({},t),{},{ref:e,icon:We.Z}))},nt=b.forwardRef(rt),ot=nt,Ve=b.createContext(void 0),at=function s(t){return t.child?s(t.child)+1:0},De=function s(t){var e,n=t.node,a=t.withWrapper,i=(0,Ee.m)(),l=i.copy,u=(0,b.useContext)(Ve)||{},c=u.hook,h=c===void 0?{}:c,v=h.select,m=h.unselect,f=h.getNode,x=h.isSelected,C=u.showFilter,P=n.value,N=n.child,E=n.rxn,M=n.path,B=M===void 0?[]:M,I=x==null?void 0:x(B),$=(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:Q.structureWrapper,children:[(0,r.jsx)(Me.default,{structure:P,className:Q.structure}),(0,r.jsxs)("div",{className:Q.structureInfo,children:["m",at(n)+1]}),C&&n.path&&n.path.length>1&&n.child&&(0,r.jsx)("div",{className:Q.filterInfo,onClick:function(){I?m==null||m(B):v==null||v(B)},children:(0,r.jsx)(y.ZP,{size:"small",color:I?"primary":"default",variant:I?"filled":"outlined",children:I?(0,r.jsx)(tt,{}):(0,r.jsxs)(r.Fragment,{children:[f==null||(e=f(B))===null||e===void 0?void 0:e.children.length,(0,r.jsx)(ot,{})]})})})]}),N&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{title:E,className:"".concat(Q.reactionWrapper," ").concat(Q.arrowWrapper),children:[(0,r.jsx)(Le.Z,{}),(0,r.jsx)("div",{className:Q.reactionBtns,children:(0,r.jsx)("div",{className:"".concat(Q.reactionCopyBtn," ").concat(Q.reactionBtn),onClick:function(){return l("".concat(N==null?void 0:N.value,">>").concat(P))},children:(0,r.jsx)(he.r,{})})})]}),(0,r.jsx)(s,{node:N})]})]});return a?(0,r.jsx)("div",{className:Q.root,children:$}):$},Ke=De,Kt=o(52197),Qt=function(t,e){return b.createElement(k.Z,(0,ne.Z)((0,ne.Z)({},t),{},{ref:e,icon:Kt.Z}))},kt=b.forwardRef(Qt),Jt=kt,Xt=o(75750),mt=o(31418),Ze=o(55241),Qe={root:"root___mJeMz",big:"big___KplLb",small:"small___tPT2D"},_t=function(t){var e=t.icon,n=t.text,a=t.onClick,i=t.disabled,l=t.style,u=t.disabledText,c=u===void 0?(0,p.oz)("temporary-route-tip"):u,h={type:"link",size:"small",icon:e,disabled:i,onClick:a,title:i?c:void 0,style:l};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Ze.Z,{content:n,className:Qe.small,children:(0,r.jsx)(y.ZP,R()({},h))}),(0,r.jsx)(y.ZP,R()(R()({},h),{},{className:Qe.big,children:n}))]})},ke=_t,qt=function(t){var e,n,a,i=t.route,l=t.tempRoute,u=t.reload,c=(0,D.useAccess)(),h=mt.Z.useApp(),v=h.message,m=(0,D.useModel)("@@initialState"),f=m.initialState,x=f==null||(e=f.userInfo)===null||e===void 0?void 0:e.id;if(!(c!=null&&(n=c.authCodeList)!==null&&n!==void 0&&n.includes("compound.button.collect")))return null;var C=i.collected_retro_backbones,P=C==null||(a=C.find(function(I){return I.user_id===String(x)}))===null||a===void 0?void 0:a.id,N=typeof P=="number",E=function(){var I=H()(Z()().mark(function $(){var F,G;return Z()().wrap(function(L){for(;;)switch(L.prev=L.next){case 0:if(!l){L.next=2;break}return L.abrupt("return");case 2:if(!N){L.next=8;break}return L.next=5,(0,U.service)("collected-retro-backbones").deleteOne(P);case 5:L.t0=L.sent,L.next=11;break;case 8:return L.next=10,(0,U.service)("collected-retro-backbones").create({user_id:String(x),retro_backbone:i.id});case 10:L.t0=L.sent;case 11:F=L.t0,G=F.error,G||(u(),v.success((0,p.oz)("operate-success")));case 14:case"end":return L.stop()}},$)}));return function(){return I.apply(this,arguments)}}(),M=(0,p.oz)(N?"unfavorite":"favorite"),B=N?(0,r.jsx)(Jt,{}):(0,r.jsx)(Xt.Z,{});return(0,r.jsx)(ke,{icon:B,text:M,disabled:l,onClick:E})},er=qt,tr=o(38545),rr=function(t){var e,n=t.route,a=t.tempRoute,i=(0,D.useAccess)(),l=(0,D.useModel)("commend"),u=l.getProfileInfo;if(!(i!=null&&(e=i.authCodeList)!==null&&e!==void 0&&e.includes("compound.button.feedback")))return null;var c=n.content_count?"(".concat(n.content_count,")"):"",h="".concat((0,p.oz)("comment")).concat(c);return(0,r.jsx)(ke,{icon:(0,r.jsx)(tr.Z,{}),text:h,disabled:a,onClick:function(){u({_commendSuject:n,collection_class:"project-route"})}})},gt=rr,ht=o(61487),nr=o(26363),or=o(27521),yt=o(45360),ar=function(t){var e=t.route,n=t.reload,a=(0,ht.f)(),i=a.fetch,l=Pe(),u=l.compoundId,c=w(u),h=c.defaultRouteId,v=c.refetch,m=(e==null?void 0:e.id)===h&&!!h,f=function(){var P=H()(Z()().mark(function N(){var E,M;return Z()().wrap(function(I){for(;;)switch(I.prev=I.next){case 0:if(!(!(e!=null&&e.id)||!u||m)){I.next=2;break}return I.abrupt("return");case 2:return I.next=4,i((0,U.service)("project-compounds").update(u,{default_route:e.id}));case 4:E=I.sent,M=E.data,M&&(yt.ZP.success((0,p.oz)("default-route-set")),v==null||v(),n());case 7:case"end":return I.stop()}},N)}));return function(){return P.apply(this,arguments)}}(),x=m?(0,r.jsx)(nr.Z,{}):(0,r.jsx)(or.Z,{}),C=(0,p.oz)(m?"default-route":"set-default-route");return(0,r.jsx)(ke,{icon:x,text:C,onClick:f})},sr=ar,st=o(15001),ir=o(85670),lr={canceled:[],confirmed:["canceled"],editing:["canceled"],finished:[]},ur=function(t){var e=t.route,n=e.status,a=e.id,i=t.reload,l=(0,ht.f)(),u=l.fetch,c=mt.Z.useApp(),h=c.message,v=lr[n]||[],m=function(){var f=H()(Z()().mark(function x(C){var P,N;return Z()().wrap(function(M){for(;;)switch(M.prev=M.next){case 0:if(a){M.next=2;break}return M.abrupt("return");case 2:return M.next=4,u((0,U.service)("project-routes").update(a,{status:C}));case 4:P=M.sent,N=P.data,N&&(h.success((0,p.oz)("success-update-status")),i());case 7:case"end":return M.stop()}},x)}));return function(C){return f.apply(this,arguments)}}();return v.length?(0,r.jsx)(ir.Z,{currentValue:n,avalibleValues:v,onSelect:m,valueRender:function(x){return(0,r.jsx)(st.Z,{labelPrefix:"pages.route.statusLabel",status:x})}}):(0,r.jsx)(st.Z,{labelPrefix:"pages.route.statusLabel",status:n})},cr=ur,dr=o(19632),jt=o.n(dr),xt=o(55932),vr=o(55287),fr="viewedRoute",pr=function(t){var e,n=t.route,a=(0,D.useAccess)(),i=Pe(),l=i.compoundId,u=w(l),c=u.projectId,h="backbone"in n?"retro":"project",v="".concat(fr,"-").concat(h),m=(0,xt.U2)(v),f=m==null?void 0:m.includes(n.id);if(!(a!=null&&(e=a.authCodeList)!==null&&e!==void 0&&e.includes("compound.button.view")))return null;var x=function(){(0,xt.t8)(v,[].concat(jt()(m||[]),[n.id])),"backbone"in n?D.history.push("/projects/".concat(c,"/compound/").concat(l,"/view-by-backbone/").concat(n.id)):D.history.push("/projects/".concat(c,"/compound/").concat(l,"/").concat(n.status==="editing"?"edit":"view","/").concat(n.id))},C=(0,p.oz)("pages.projectTable.actionLabel.viewDetail");return(0,r.jsx)(ke,{icon:(0,r.jsx)(vr.Z,{}),text:C,onClick:x,style:f?{color:"#a52ad2"}:{}})},St=pr,mr=function(t){var e=t.route,n=t.reload,a=t.tempRoute;return"backbone"in e?(0,r.jsxs)("div",{className:Qe.root,children:[(0,r.jsx)(St,{route:e}),(0,r.jsx)(er,{route:e,reload:n,tempRoute:a}),(0,r.jsx)(gt,{route:e,tempRoute:a})]}):(0,r.jsxs)("div",{className:Qe.root,children:[(0,r.jsx)(cr,{route:e,reload:n}),(0,r.jsx)(St,{route:e}),e.status==="confirmed"&&(0,r.jsx)(sr,{route:e,reload:n}),(0,r.jsx)(gt,{route:e})]})},gr=mr,Ct=o(15101),ae={root:"root___hDxfn",wrapper:"wrapper___nfnRa",icon:"icon___LmvPA"},hr=function(t){var e=t.route,n="backbone"in e?e.main_trees:[e.main_tree],a=n.map(Ct.WN).reduce(function(i,l){return l?i+1:i},0);return(0,r.jsxs)(Oe.Z,{className:ae.wrapper,color:"blue",children:[a>0?(0,p.oz)("has"):(0,p.oz)("No"),(0,p.Ig)()?" ":"",a>1?(0,p.oz)("multiple"):"",(0,p.Ig)()?" ":"",(0,p.oz)("branched-chain")]})},bt=hr,yr=function(t){var e=t.route.group_conditions,n=t.tempRoute,a=xe(),i=a.retroRouteFilters,l=a.setGroupSimilarId,u=e[i.group];return(0,r.jsx)("div",{className:ae.wrapper,children:(0,r.jsx)(Ze.Z,{content:(0,p.oz)("filter-routes-group"),children:(0,r.jsxs)(y.ZP,{size:"small",type:"primary",disabled:n,onClick:function(){return l(u)},children:[(0,p.oz)("new-group")," ",u+1]})})})},jr=yr,xr=function(t){var e=t.route.known_reaction_rate;return e===void 0?null:(0,r.jsxs)("div",{className:ae.wrapper,children:[(0,p.oz)("known-reaction-proportion")," ",e,"%"]})},Sr=xr,Cr=o(36688),br=function(t,e){return b.createElement(k.Z,(0,ne.Z)((0,ne.Z)({},t),{},{ref:e,icon:Cr.Z}))},Rr=b.forwardRef(br),Rt=Rr,Pr=function(t){var e=t.route.process_feasibility_score,n=e===void 0?1:e;return(0,r.jsxs)(Ze.Z,{className:ae.wrapper,content:(0,p.oz)("process-feasibility-tip"),children:[(0,p.oz)("process-feasibility"),(0,r.jsx)(Rt,{className:ae.icon}),":"," ",Math.round(n*100),"%"]})},Ir=Pr,Nr=function(t){var e=t.route,n="backbone"in e?e.backbone.length-1:(0,Ct.yn)(e.main_tree);return(0,r.jsxs)("div",{className:ae.wrapper,children:[(0,p.oz)("longest-chain-l"),n]})},Pt=Nr,zr=function(t){var e=t.route;return(0,r.jsx)(Oe.Z,{className:ae.wrapper,color:"#2db7f5",children:e.name})},Or=zr,Lr=function(t){var e=t.route;return(0,r.jsxs)("div",{className:ae.wrapper,children:[(0,p.oz)("route-id"),": ",e.id]})},It=Lr,Nt=o(56717),Mr=function(t){var e=t.route,n=e.score,a=n===void 0?100:n,i=e.originScore,l=i===void 0?1:i,u=e.price_score,c=u===void 0?1:u,h=e.novelty_score,v=h===void 0?1:h,m=e.safety_score,f=m===void 0?1:m,x=t.tempRoute;return(0,r.jsxs)("div",{className:ae.wrapper,children:[(0,p.oz)("algorithmic-score")," ",Math.round(a*100)/100,!x&&(0,r.jsx)(Ze.Z,{content:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("p",{children:[(0,p.oz)("route-novelty-score"),":",(0,p.T2)(v*100),"/100"]}),(0,r.jsxs)("p",{children:[(0,p.oz)("route-price-score"),":",(0,p.T2)(c*100),"/100"]}),(0,r.jsxs)("p",{children:[(0,p.oz)("route-safety-score"),":",(0,p.T2)(f*100),"/100"]}),(0,r.jsx)(te.Z,{style:{margin:"8px 0"}}),(0,r.jsxs)("p",{children:[(0,p.oz)("route-base-score"),":",(0,p.T2)(l),"/100"]})]}),children:(0,r.jsx)(Nt.Z,{className:ae.icon})})]})},Er=Mr,$r=function s(t){var e;return(e=t.children)!==null&&e!==void 0&&e.length?1+t.children.reduce(function(n,a){return n+s(a)},0):0},Tr=function(t){var e=t.route,n="backbone"in e?e.min_n_main_tree_steps||0:$r(e.main_tree);return(0,r.jsxs)(Ze.Z,{content:(0,p.oz)("multi-steps-tip"),className:ae.wrapper,children:[(0,p.oz)("route-length"),(0,r.jsx)(Rt,{className:ae.icon}),": ",n]})},zt=Tr,Dr=o(36223),Zr=o(27484),Je=o.n(Zr),Ar=function(t){var e=t.route,n=e.updatedAt||e.updated_at;return n?(0,r.jsxs)("div",{className:ae.wrapper,children:[(0,r.jsx)(Dr.Z,{}),(0,p.oz)("modified-time"),": ",(0,p.S9)(Je()(n))]}):null},Fr=Ar,wr=function(t){var e=t.route,n=t.tempRoute,a=xe(),i=a.retroLayout;return"backbone"in e?(0,r.jsxs)("div",{className:ae.root,children:[i==="group"&&(0,r.jsx)(jr,{route:e,tempRoute:n}),(0,r.jsx)(It,{route:e}),(0,r.jsx)(te.Z,{type:"vertical"}),(0,r.jsx)(zt,{route:e}),(0,r.jsx)(te.Z,{type:"vertical"}),(0,r.jsx)(Pt,{route:e}),(0,r.jsx)(te.Z,{type:"vertical"}),(0,r.jsx)(bt,{route:e}),i!=="filter"&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(te.Z,{type:"vertical"}),(0,r.jsx)(Er,{route:e,tempRoute:n})]}),(0,r.jsx)(te.Z,{type:"vertical"}),(0,r.jsx)(Sr,{route:e}),i==="feasibility"&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(te.Z,{type:"vertical"}),(0,r.jsx)(Ir,{route:e})]})]}):(0,r.jsxs)("div",{className:ae.root,children:[(0,r.jsx)(It,{route:e}),(0,r.jsx)(te.Z,{type:"vertical"}),(0,r.jsx)(Or,{route:e}),(0,r.jsx)(te.Z,{type:"vertical"}),(0,r.jsx)(zt,{route:e}),(0,r.jsx)(te.Z,{type:"vertical"}),(0,r.jsx)(Pt,{route:e}),(0,r.jsx)(te.Z,{type:"vertical"}),(0,r.jsx)(bt,{route:e}),(0,r.jsx)(te.Z,{type:"vertical"}),(0,r.jsx)(Fr,{route:e})]})},Br=wr,Wr=o(89257),Ur=o(6588),Gr=o(65160),Hr=o(40520),Ot=o(94860),Yr=function(t){switch(t){case void 0:case"default":return null;case"failed":return(0,r.jsx)(Hr.r,{});case"queueing":return(0,r.jsx)(Gr.r,{});case"generating":return(0,r.jsx)("img",{src:Wr});default:return(0,r.jsx)(Ur.r,{})}},Vr=function(t,e){switch(t){case void 0:case"default":return(0,p.oz)("no-routes-returned");case"failed":return(0,p.oz)("search-failed");case"queueing":return(0,p.oz)("wait-tip");case"generating":return(0,p.oz)("generating-tip");case"empty":return(0,r.jsxs)(r.Fragment,{children:[(0,p.oz)("no-AI-returned-create-I"),(0,r.jsxs)("a",{onClick:e,children:["\u3010",(0,p.oz)("gnerate-routes"),"\u3011"]}),(0,p.oz)("no-AI-returned-create-II")]});case"empty-with-filter":default:return(0,p.oz)("no-AI-returned")}},Kr=function(t,e){return(0,r.jsxs)("span",{children:[(0,p.oz)("no-routes-create-tip"),(0,r.jsxs)("a",{onClick:function(){return D.history.push("/projects/".concat(t,"/compound/").concat(e,"/create"))},children:["\u3010",(0,p.oz)("new-route"),"\u3011"]}),(0,p.oz)("add-routes")]})},Qr=function(t){return"backbone"in t?(0,Ot.m)(t.backbone):(0,Ot.A)(t.main_tree)},kr=function(t){var e=t.routes,n=t.refetch,a=t.tempRoute;return(0,r.jsx)("div",{className:Q.routesList,children:e.map(function(i){return(0,r.jsx)(ce.Z,{size:"small",className:Q.cardRoot,title:(0,r.jsx)(Br,{route:i,reload:n,tempRoute:a}),extra:(0,r.jsx)(gr,{route:i,reload:n,tempRoute:a}),children:(0,r.jsx)(Ke,{node:Qr(i),withWrapper:!0})},i.id)})})},Lt=kr,Jr=function(){var s=H()(Z()().mark(function t(e,n,a){var i,l,u,c;return Z()().wrap(function(v){for(;;)switch(v.prev=v.next){case 0:return v.next=2,(0,U.query)("project-routes?comment=true").filterDeep("project_compound.id","eq",e).sortBy([{field:"createdAt",order:"desc"},{field:"updatedAt",order:"desc"}]).paginate(n,a).get();case 2:if(i=v.sent,l=i.data,u=i.error,c=i.meta,!(!u&&l)){v.next=8;break}return v.abrupt("return",{data:l,pagination:c==null?void 0:c.pagination});case 8:throw new Error("Network response was not ok");case 9:case"end":return v.stop()}},t)}));return function(e,n,a){return s.apply(this,arguments)}}(),Xr=function(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:10,a=typeof t!="number"?Number.parseInt(t||""):t,i=(0,q.a)({queryKey:["project-route",a,e,n],queryFn:function(){return t?Jr(a,e,n):void 0},enabled:!isNaN(a),keepPreviousData:!0}),l=i.data,u=i.error,c=i.isLoading,h=i.isFetching,v=i.refetch;return isNaN(a)?{}:{data:l,error:u,isLoading:c,isFetching:h,refetch:v}},_r=function(t){var e=t.compoundId,n=w(e),a=n.projectId,i=oe(1,10),l=i.page,u=i.pageSize,c=i.setPagination,h=Xr(e,l,u),v=h.data,m=v===void 0?{}:v,f=m.data,x=m.pagination,C=h.isLoading,P=h.refetch;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(le.Z,{spinning:C||!1,children:!a||!e?null:f!=null&&f.length?(0,r.jsx)(Lt,{routes:f,refetch:function(){return P==null?void 0:P()}}):(0,r.jsx)(K.Z,{des:Kr(a,e)})}),(0,r.jsx)(Se.Z,{align:"end",total:(x==null?void 0:x.total)||0,hideOnSinglePage:!0,current:x==null?void 0:x.page,pageSize:x==null?void 0:x.pageSize,showSizeChanger:!1,onChange:function(E,M){return c({page:E,pageSize:M})}})]})},qr=_r,en=o(61227),tn=o.n(en),Mt=function s(t,e){var n=tn()(t),a=n[0],i=n.slice(1);if(!(!a||(e==null?void 0:e.smiles)!==a)){if(!i.length)return e;var l=e.children.find(function(u){return u.smiles===i[0]});return s(i,l)}},rn=function(t){var e=(0,b.useState)([]),n=T()(e,2),a=n[0],i=n[1];(0,b.useEffect)(function(){i(t!=null&&t.smiles?[t.smiles]:[])},[t]);var l=(0,b.useMemo)(function(){return Mt(a,t)},[a,t]),u=(0,b.useCallback)(function(f){f!=null&&f.length&&i(f)},[]),c=(0,b.useCallback)(function(f){f!=null&&f.length&&f.length>1&&i(f.slice(0,-1))},[]),h=(0,b.useCallback)(function(f){return f.length?Mt(f,t):void 0},[t]),v=(0,b.useCallback)(function(f){return f.every(function(x,C){return x===a[C]})},[a]),m=(0,b.useMemo)(function(){return(l==null?void 0:l.children.map(function(f){return f.maxScoreId}))||(t!=null&&t.maxScoreId?[t.maxScoreId]:[])},[l,t]);return{backboneIds:m,setTrace:i,getNode:h,isSelected:v,select:u,unselect:c}},Et=o(4505),it=o(96486),$t=["no","score","backbone","main_trees","group_conditions","group_info","known_reaction_rate","min_n_main_tree_steps","createdAt","updatedAt","safety_score","novelty_score","price_score","collected_retro_backbones","process_feasibility_score"],nn=function(){var s=H()(Z()().mark(function t(e){var n,a,i,l,u,c,h,v,m,f,x,C,P,N,E,M=arguments;return Z()().wrap(function(I){for(;;)switch(I.prev=I.next){case 0:return n=M.length>1&&M[1]!==void 0?M[1]:{},a=n.page,i=a===void 0?1:a,l=n.pageSize,u=l===void 0?10:l,c=n.group,h=c===void 0?"start_material":c,v=n.groupSimilarId,m=n.preference,f=(0,U.query)("retro-backbones?comment=true",{params:{group:h,preference:m}},$t).filterDeep("retro_process.id","eq",e).populateWith("collected_retro_backbones",["id","user_id"]).paginate(i,u),v!==void 0&&f.filterDeep("group_conditions.start_material","eq",v),I.next=6,f.get();case 6:if(x=I.sent,C=x.data,P=x.error,N=x.meta,!(!P&&C)){I.next=13;break}return E=C.map(function($){return R()(R()({},$),{},{collected:!(0,it.isEmpty)($==null?void 0:$.collected_retro_backbones),originScore:$.score||0,score:(0,Et.U)($,m||{})*100})}),I.abrupt("return",{data:E,pagination:N==null?void 0:N.pagination});case 13:throw new Error("Network response was not ok");case 14:case"end":return I.stop()}},t)}));return function(e){return s.apply(this,arguments)}}(),on=function(){var s=H()(Z()().mark(function t(e,n){var a,i,l,u,c;return Z()().wrap(function(v){for(;;)switch(v.prev=v.next){case 0:return v.next=2,(0,U.query)("retro-backbones?comment=true",void 0,$t).filterDeep("retro_process.id","eq",e).populateWith("collected_retro_backbones",["id","user_id"]).filterDeep("id","in",n).get();case 2:if(a=v.sent,i=a.data,l=a.error,u=a.meta,!(!l&&i)){v.next=9;break}return c=i.map(function(m){return R()(R()({},m),{},{collected:!(0,it.isEmpty)(m==null?void 0:m.collected_retro_backbones),originScore:m.score||0,score:(0,Et.U)(m,{safety_score:2,novelty_score:-2,price_score:2})*100})}),v.abrupt("return",{data:c,pagination:u==null?void 0:u.pagination});case 9:throw new Error("Network response was not ok");case 10:case"end":return v.stop()}},t)}));return function(e,n){return s.apply(this,arguments)}}(),an=function(t,e){var n=typeof t!="number"?Number.parseInt(t||""):t,a=(0,q.a)({queryKey:["retro-backbones",t,JSON.stringify(e)],queryFn:function(){return t&&e?nn(n,e):{}},enabled:!isNaN(n),keepPreviousData:!0}),i=a.data,l=a.error,u=a.isLoading,c=a.isFetching,h=a.refetch;return isNaN(n)?{}:{data:i,error:l,isLoading:u,isFetching:c,refetch:h}},sn=function(t,e){var n=typeof t!="number"?Number.parseInt(t||""):t,a=(0,q.a)({queryKey:["retro-backbones",t,e==null?void 0:e.join(",")],queryFn:function(){return t&&e!==null&&e!==void 0&&e.length?on(n,e):{}},enabled:!isNaN(n),keepPreviousData:!0}),i=a.data,l=a.error,u=a.isLoading,c=a.isFetching,h=a.refetch;return isNaN(n)?{}:{data:i,error:l,isLoading:u,isFetching:c,refetch:h}},ln=function(t,e){var n=e.backboneIds,a=e.filters,i=typeof t!="number"?Number.parseInt(t||""):t,l=an(t,a),u=sn(t,n);return isNaN(i)?{}:n?u:l},un=function(t,e){return t==="failed"||t==="canceled"?"failed":t==="pending"||t==="limited"?"queueing":t==="running"?"generating":e?"default":"empty"},cn={group:"start_material",preference:{plus_process_feasibility_score:!0,price_score:2,safety_score:2,novelty_score:-2}},dn=function(t){var e=t.compoundId,n=Pe(),a=n.setRetroModel,i=(0,Be.Z)(e,!0),l=i.selected,u=l||{},c=u.backbone_tree,h=u.id,v=u.status,m=xe(),f=m.retroRouteFilters,x=m.setPagination,C=m.retroLayout,P=rn(c),N=P.backboneIds,E=C==="feasibility"?cn:f,M=ln(h,{backboneIds:C==="filter"?N:void 0,filters:E}),B=M.data,I=B===void 0?{}:B,$=I.data,F=I.pagination,G=M.isLoading,se=M.refetch;(0,b.useEffect)(function(){se==null||se()},[c]);var L=C!=="filter",ee=C==="filter",pe=un(v,$==null?void 0:$.length);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(le.Z,{spinning:G||!1,children:$!=null&&$.length?(0,r.jsx)(Ve.Provider,{value:{hook:P,showFilter:ee},children:(0,r.jsx)(Lt,{routes:$,refetch:function(){return se==null?void 0:se()},tempRoute:v!=="completed"})}):(0,r.jsx)(K.Z,{image:Yr(pe),des:Vr(pe,function(){return a("init")})})}),L&&(0,r.jsx)(Se.Z,{total:(F==null?void 0:F.total)||0,align:"end",hideOnSinglePage:!0,current:(F==null?void 0:F.page)||f.page,pageSize:(F==null?void 0:F.pageSize)||f.pageSize,showSizeChanger:!1,onChange:function(ze,Xe){return x(ze,Xe)}})]})},vn=dn,fn={building_block:["aiGenerated"],target:["aiGenerated","myRoutes"],temp_block:["aiGenerated"]},pn=function(t){var e,n=t.compoundId,a=(0,D.useAccess)(),i=xe(),l=i.tabType,u=i.setTabType,c=i.counts,h=i.setCount,v=w(n),m=v.data;(0,b.useEffect)(function(){var C;h("myRoutes",(m==null||(C=m.project_routes)===null||C===void 0?void 0:C.length)||0)},[m==null||(e=m.project_routes)===null||e===void 0?void 0:e.length]);var f=fn[(m==null?void 0:m.type)||"target"].filter(function(C){var P;return a==null||(P=a.authCodeList)===null||P===void 0?void 0:P.includes("compound.tab.".concat(C))}).map(function(C){return{key:C,label:c[C]?"".concat((0,p.oz)(C)," (").concat(c[C],")"):(0,p.oz)(C)}}),x=l==="myRoutes"?(0,r.jsx)(W,{compoundId:n}):l==="aiGenerated"?(0,r.jsx)(g,{compoundId:n}):null;return(0,r.jsx)(ce.Z,{className:A.root,tabList:f,size:"small",activeTabKey:l,onTabChange:function(P){return u(P)},tabProps:{size:"small"},tabBarExtraContent:x,children:l==="myRoutes"?(0,r.jsx)(qr,{compoundId:n}):(0,r.jsx)(vn,{compoundId:n})})},mn=pn,gn=o(59652),hn=function(t){V()(t);var e=(0,D.useModel)("commend"),n=e.showLauncher,a=e.isOpen,i=e.sendMessage;return(0,r.jsx)(gn.Z,{onMessageWasSent:i,hiddenLauncher:n,isOpen:a})},yn=hn,jn=o(54453),xn=o(69776),Sn=o(71471),Cn=o(48054),bn=function(t){var e=t.moleculeId,n=(0,xn.Z)(),a=n.typeMap,i=(0,D.useModel)("compound"),l=i.resetFilterInfo,u=w(e),c=u.data,h=u.error,v=u.isLoading,m=function(x){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(Me.default,{structure:x.input_smiles,height:150}),(0,r.jsx)(Sn.Z.Text,{style:{width:(0,p.Ig)()?115:162},copyable:!0,ellipsis:{tooltip:x.no},children:x.no}),(0,r.jsxs)("div",{className:"display-flex targetMoleculeInfo",children:[x.type&&(0,r.jsx)(Oe.Z,{className:"targetMoleculeType",onClose:function(){return l()},children:a[x.type]},"targetMoleculeType"),(0,r.jsx)(Oe.Z,{className:"targetMoleculeStatus",onClose:function(){return l()},children:(0,r.jsx)(jn.Z,{word:x.status})},"targetMoleculeStatus")]})]})};return(0,r.jsx)(ce.Z,{size:"small",className:"target-card",bordered:!0,title:(0,p.oz)("target-molecules"),children:v?(0,r.jsx)(Cn.Z,{}):h||!c?(0,p.oz)("noticeIcon.empty"):m(c)})},Rn=bn,Pn=o(90637),In=o(34994),Nn=o(98138),zn=o(85265),On={progressTitle:"progressTitle___BkIBj",filterDrawer:"filterDrawer___LI65V"};function Ln(s){var t=s.open,e=s.onClose,n=s.onChange,a=Nn.Z.useForm(),i=T()(a,1),l=i[0],u=(0,ve.R1)(),c=u.setting,h=(0,b.useState)(!0),v=T()(h,2),m=v[0],f=v[1],x=(0,D.useModel)("compound"),C=x.retroParamsConfig,P=x.updateRetroParamsConfig;return(0,b.useEffect)(function(){if(c!=null&&c.retro&&!(0,it.isEmpty)(c==null?void 0:c.retro)){var N,E,M,B={safety_score:c==null||(N=c.retro)===null||N===void 0?void 0:N.safety_score,novelty_score:c==null||(E=c.retro)===null||E===void 0?void 0:E.novelty_score,price_score:c==null||(M=c.retro)===null||M===void 0?void 0:M.price_score};P(B),l.setFieldsValue(B),n==null||n(B)}},[c==null?void 0:c.retro]),(0,b.useEffect)(function(){f(t||!1)},[t]),(0,r.jsx)(zn.Z,{forceRender:!0,width:(0,p.Ig)()?408:508,title:(0,r.jsx)("div",{className:"flex-align-items-center",children:(0,p.oz)("route-sort-dimension-weight-setting")}),placement:"right",onClose:function(){f(!1),e()},open:m,className:On.filterDrawer,destroyOnClose:!0,children:(0,r.jsxs)("section",{children:[(0,r.jsx)("h1",{children:(0,p.oz)("demension-weight")}),(0,r.jsx)(te.Z,{}),(0,r.jsx)(In.A,{onValuesChange:function(E){P(R()(R()({},C),E)),n==null||n(R()(R()({},C),E))},form:l,submitter:!1,layout:"horizontal",labelCol:{span:4},wrapperCol:{span:20},children:(0,r.jsx)(Pn.Z,{})})]})})}var Mn=Object.defineProperty,Tt=Object.getOwnPropertySymbols,En=Object.prototype.hasOwnProperty,$n=Object.prototype.propertyIsEnumerable,Dt=(s,t,e)=>t in s?Mn(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e,Tn=(s,t)=>{for(var e in t||(t={}))En.call(t,e)&&Dt(s,e,t[e]);if(Tt)for(var e of Tt(t))$n.call(t,e)&&Dt(s,e,t[e]);return s};const Dn=s=>b.createElement("svg",Tn({width:16,height:16,xmlns:"http://www.w3.org/2000/svg"},s),b.createElement("path",{d:"M10.093 9.867h-2a.689.689 0 0 1-.473-.194l-1.133-1.14h-1.38a.669.669 0 0 1-.667-.666c0-.367.3-.667.667-.667h1.38L7.62 6.06a.66.66 0 0 1 .473-.193h2c.367 0 .667.3.667.666 0 .367-.3.667-.667.667h-1.72l-.666.667.666.666h1.72c.367 0 .667.3.667.667 0 .367-.3.667-.667.667ZM13.987 14.653a.683.683 0 0 1-.473-.193l-1.667-1.667a.664.664 0 1 1 .94-.94l1.666 1.667a.664.664 0 0 1-.473 1.133h.007Z"}),b.createElement("path",{d:"M7.847 14.347a6.504 6.504 0 0 1-6.5-6.5c0-3.587 2.92-6.5 6.5-6.5s6.5 2.913 6.5 6.5c0 3.586-2.914 6.5-6.5 6.5Zm0-11.667A5.175 5.175 0 0 0 2.68 7.847a5.175 5.175 0 0 0 5.167 5.166 5.175 5.175 0 0 0 5.166-5.166A5.175 5.175 0 0 0 7.847 2.68Z"}));var To="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEwLjA5MyA5Ljg2N2gtMmEuNjg5LjY4OSAwIDAgMS0uNDczLS4xOTRsLTEuMTMzLTEuMTRoLTEuMzhhLjY2OS42NjkgMCAwIDEtLjY2Ny0uNjY2YzAtLjM2Ny4zLS42NjcuNjY3LS42NjdoMS4zOEw3LjYyIDYuMDZhLjY2LjY2IDAgMCAxIC40NzMtLjE5M2gyYy4zNjcgMCAuNjY3LjMuNjY3LjY2NiAwIC4zNjctLjMuNjY3LS42NjcuNjY3aC0xLjcybC0uNjY2LjY2Ny42NjYuNjY2aDEuNzJjLjM2NyAwIC42NjcuMy42NjcuNjY3IDAgLjM2Ny0uMy42NjctLjY2Ny42NjdaTTEzLjk4NyAxNC42NTNhLjY4My42ODMgMCAwIDEtLjQ3My0uMTkzbC0xLjY2Ny0xLjY2N2EuNjY0LjY2NCAwIDEgMSAuOTQtLjk0bDEuNjY2IDEuNjY3YS42NjQuNjY0IDAgMCAxLS40NzMgMS4xMzNoLjAwN1oiLz48cGF0aCBkPSJNNy44NDcgMTQuMzQ3YTYuNTA0IDYuNTA0IDAgMCAxLTYuNS02LjVjMC0zLjU4NyAyLjkyLTYuNSA2LjUtNi41czYuNSAyLjkxMyA2LjUgNi41YzAgMy41ODYtMi45MTQgNi41LTYuNSA2LjVabTAtMTEuNjY3QTUuMTc1IDUuMTc1IDAgMCAwIDIuNjggNy44NDdhNS4xNzUgNS4xNzUgMCAwIDAgNS4xNjcgNS4xNjYgNS4xNzUgNS4xNzUgMCAwIDAgNS4xNjYtNS4xNjZBNS4xNzUgNS4xNzUgMCAwIDAgNy44NDcgMi42OFoiLz48L3N2Zz4=",Zn=function(t){var e,n=t.moleculeId,a=t.onRetro,i=(0,D.useAccess)(),l=w(n),u=l.data,c=l.isLoading,h=!c&&(u==null?void 0:u.id)&&!(0,p.u9)(void 0,u==null?void 0:u.status)&&(i==null||(e=i.authCodeList)===null||e===void 0?void 0:e.includes("compound.button.gnerate-routes"));return h?(0,r.jsx)("div",{className:"buttons-wrapper",children:(0,r.jsxs)(y.ZP,{type:"primary",block:!0,size:"middle",className:"action-button flex-center",onClick:function(){return a==null?void 0:a()},children:[(0,r.jsx)(Dn,{width:16,fill:"#fff"}),(0,p.oz)("gnerate-routes")]})}):null},An=Zn,Zt=o(85576),Fn=o(71230),At=o(15746),wn=function(t){var e=t.logs,n=(0,b.useState)(!1),a=T()(n,2),i=a[0],l=a[1];return(0,b.useEffect)(function(){e!=null&&e.length&&l(!0)},[e]),(0,r.jsx)(Zt.Z,{open:i,onCancel:function(){return l(!1)},afterOpenChange:function(c){return l(c)},title:(0,p.oz)("log"),footer:!1,children:e==null?void 0:e.map(function(u,c){return(0,r.jsxs)(Fn.Z,{children:[(0,r.jsx)(At.Z,{span:8,children:(0,p.S9)(u==null?void 0:u.event_time)}),(0,r.jsx)(At.Z,{span:16,children:u==null?void 0:u.event_msg})]},"".concat(u==null?void 0:u.event_time,"-").concat(c))})})},Bn=wn,Wn=o(52677),Un=o.n(Wn),Gn=o(26855),Hn=o(54324),Yn=function(t){var e=t.moleculeId,n=t.onSuccess,a=t.onCancel,i=t.open,l=yt.ZP.useMessage(),u=T()(l,2),c=u[0],h=u[1],v=Gn.ZP.useNotification(),m=T()(v,2),f=m[0],x=m[1],C=w(e),P=C.data,N=(0,D.useModel)("@@initialState"),E=N.initialState,M=E===void 0?{}:E,B=M.userInfo,I=B===void 0?void 0:B,$=(0,b.useState)(!1),F=T()($,2),G=F[0],se=F[1],L=(0,b.useState)(!1),ee=T()(L,2),pe=ee[0],Ne=ee[1],ze=(0,b.useState)(!1),Xe=T()(ze,2),Ht=Xe[0],Ue=Xe[1],Eo=(0,b.useState)(),Yt=T()(Eo,2),_e=Yt[0],Vt=Yt[1],lt=(0,b.useRef)(),$o=function(){var Ge=H()(Z()().mark(function Fe(){var ut,ct,dt,vt,we;return Z()().wrap(function(Ce){for(;;)switch(Ce.prev=Ce.next){case 0:return Ue(!0),Ce.next=3,(ut=lt.current)===null||ut===void 0?void 0:ut.call(lt).catch();case 3:if(ct=Ce.sent,ct){Ce.next=7;break}return Ue(!1),Ce.abrupt("return");case 7:return Ce.next=9,(0,U.service)("retro-processes").create({project_compound:P==null?void 0:P.id,creator_id:(I==null?void 0:I.username)||"",params:ct});case 9:dt=Ce.sent,vt=dt.data,we=dt.error,Ue(!1),!we&&vt?(c.success((0,p.oz)("search-been-created")),n==null||n(vt.retro_id)):we&&f.error({message:(0,p.oz)("route-generate-failed"),description:"".concat((0,p.oz)("error-detail")).concat(JSON.stringify(we==null?void 0:we.message))});case 14:case"end":return Ce.stop()}},Fe)}));return function(){return Ge.apply(this,arguments)}}();return(0,b.useEffect)(function(){i&&Un()(i)==="object"?(Ne(!0),Vt(i)):(Ne(!1),Vt(void 0)),Ue(!1),se(i!=="close")},[i]),P?(0,r.jsxs)(r.Fragment,{children:[h,x,(0,r.jsx)(Zt.Z,{open:G,footer:pe?!1:void 0,onCancel:function(){return a()},okButtonProps:{disabled:Ht||pe},okText:(0,p.oz)("submit"),confirmLoading:Ht,centered:!0,width:510,destroyOnClose:!0,onOk:$o,children:(0,r.jsx)(Hn.Z,{target:P==null?void 0:P.input_smiles,registerParamsGetter:function(Fe){return lt.current=Fe},viewOnly:_e,retroId:_e==null?void 0:_e.retroId,onEdit:function(){return Ne(!1)},onLoading:function(Fe){return Ue(Fe)}})})]}):null},Vn=Yn,Kn=o(19669),Qn=o(84567),kn=o(98423),Ft=o(98065),Jn=o(53124),Xn=o(83559),_n=o(83262);const wt=["wrap","nowrap","wrap-reverse"],Bt=["flex-start","flex-end","start","end","center","space-between","space-around","space-evenly","stretch","normal","left","right"],Wt=["center","start","end","flex-start","flex-end","self-start","self-end","baseline","normal","stretch"],qn=(s,t)=>{const e=t.wrap===!0?"wrap":t.wrap;return{[`${s}-wrap-${e}`]:e&&wt.includes(e)}},eo=(s,t)=>{const e={};return Wt.forEach(n=>{e[`${s}-align-${n}`]=t.align===n}),e[`${s}-align-stretch`]=!t.align&&!!t.vertical,e},to=(s,t)=>{const e={};return Bt.forEach(n=>{e[`${s}-justify-${n}`]=t.justify===n}),e};function ro(s,t){return _()(Object.assign(Object.assign(Object.assign({},qn(s,t)),eo(s,t)),to(s,t)))}var no=ro;const oo=s=>{const{componentCls:t}=s;return{[t]:{display:"flex","&-vertical":{flexDirection:"column"},"&-rtl":{direction:"rtl"},"&:empty":{display:"none"}}}},ao=s=>{const{componentCls:t}=s;return{[t]:{"&-gap-small":{gap:s.flexGapSM},"&-gap-middle":{gap:s.flexGap},"&-gap-large":{gap:s.flexGapLG}}}},so=s=>{const{componentCls:t}=s,e={};return wt.forEach(n=>{e[`${t}-wrap-${n}`]={flexWrap:n}}),e},io=s=>{const{componentCls:t}=s,e={};return Wt.forEach(n=>{e[`${t}-align-${n}`]={alignItems:n}}),e},lo=s=>{const{componentCls:t}=s,e={};return Bt.forEach(n=>{e[`${t}-justify-${n}`]={justifyContent:n}}),e},uo=()=>({});var co=(0,Xn.I$)("Flex",s=>{const{paddingXS:t,padding:e,paddingLG:n}=s,a=(0,_n.mergeToken)(s,{flexGapSM:t,flexGap:e,flexGapLG:n});return[oo(a),ao(a),so(a),io(a),lo(a)]},uo,{resetStyle:!1}),vo=function(s,t){var e={};for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&t.indexOf(n)<0&&(e[n]=s[n]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,n=Object.getOwnPropertySymbols(s);a<n.length;a++)t.indexOf(n[a])<0&&Object.prototype.propertyIsEnumerable.call(s,n[a])&&(e[n[a]]=s[n[a]]);return e},Ut=b.forwardRef((s,t)=>{const{prefixCls:e,rootClassName:n,className:a,style:i,flex:l,gap:u,children:c,vertical:h=!1,component:v="div"}=s,m=vo(s,["prefixCls","rootClassName","className","style","flex","gap","children","vertical","component"]),{flex:f,direction:x,getPrefixCls:C}=b.useContext(Jn.E_),P=C("flex",e),[N,E,M]=co(P),B=h!=null?h:f==null?void 0:f.vertical,I=_()(a,n,f==null?void 0:f.className,P,E,M,no(P,s),{[`${P}-rtl`]:x==="rtl",[`${P}-gap-${u}`]:(0,Ft.n)(u),[`${P}-vertical`]:B}),$=Object.assign(Object.assign({},f==null?void 0:f.style),i);return l&&($.flex=l),u&&!(0,Ft.n)(u)&&($.gap=u),N(b.createElement(v,Object.assign({ref:t,className:I,style:$},(0,kn.Z)(m,["justify","wrap","align"])),c))}),fo=o(43851),po={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656zM340 683v77c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-77c-10.1 3.3-20.8 5-32 5s-21.9-1.8-32-5zm64-198V264c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v221c10.1-3.3 20.8-5 32-5s21.9 1.8 32 5zm-64 198c10.1 3.3 20.8 5 32 5s21.9-1.8 32-5c41.8-13.5 72-52.7 72-99s-30.2-85.5-72-99c-10.1-3.3-20.8-5-32-5s-21.9 1.8-32 5c-41.8 13.5-72 52.7-72 99s30.2 85.5 72 99zm.1-115.7c.3-.6.7-1.2 1-1.8v-.1l1.2-1.8c.1-.2.2-.3.3-.5.3-.5.7-.9 1-1.4.1-.1.2-.3.3-.4.5-.6.9-1.1 1.4-1.6l.3-.3 1.2-1.2.4-.4c.5-.5 1-.9 1.6-1.4.6-.5 1.1-.9 1.7-1.3.2-.1.3-.2.5-.3.5-.3.9-.7 1.4-1 .1-.1.3-.2.4-.3.6-.4 1.2-.7 1.9-1.1.1-.1.3-.1.4-.2.5-.3 1-.5 1.6-.8l.6-.3c.7-.3 1.3-.6 2-.8.7-.3 1.4-.5 2.1-.7.2-.1.4-.1.6-.2.6-.2 1.1-.3 1.7-.4.2 0 .3-.1.5-.1.7-.2 1.5-.3 2.2-.4.2 0 .3 0 .5-.1.6-.1 1.2-.1 1.8-.2h.6c.8 0 1.5-.1 2.3-.1s1.5 0 2.3.1h.6c.6 0 1.2.1 1.8.2.2 0 .3 0 .5.1.7.1 1.5.2 2.2.4.2 0 .3.1.5.1.6.1 1.2.3 1.7.4.2.1.4.1.6.2.7.2 1.4.4 2.1.7.7.2 1.3.5 2 .8l.6.3c.5.2 1.1.5 1.6.8.1.1.3.1.4.2.6.3 1.3.7 1.9 1.1.1.1.3.2.4.3.5.3 1 .6 1.4 1 .2.1.3.2.5.3.6.4 1.2.9 1.7 1.3s1.1.9 1.6 1.4l.4.4 1.2 1.2.3.3c.5.5 1 1.1 1.4 1.6.1.1.2.3.3.4.4.4.7.9 1 1.4.1.2.2.3.3.5l1.2 1.8s0 .1.1.1a36.18 36.18 0 015.1 18.5c0 6-1.5 11.7-4.1 16.7-.3.6-.7 1.2-1 1.8 0 0 0 .1-.1.1l-1.2 1.8c-.1.2-.2.3-.3.5-.3.5-.7.9-1 1.4-.1.1-.2.3-.3.4-.5.6-.9 1.1-1.4 1.6l-.3.3-1.2 1.2-.4.4c-.5.5-1 .9-1.6 1.4-.6.5-1.1.9-1.7 1.3-.2.1-.3.2-.5.3-.5.3-.9.7-1.4 1-.1.1-.3.2-.4.3-.6.4-1.2.7-1.9 1.1-.1.1-.3.1-.4.2-.5.3-1 .5-1.6.8l-.6.3c-.7.3-1.3.6-2 .8-.7.3-1.4.5-2.1.7-.2.1-.4.1-.6.2-.6.2-1.1.3-1.7.4-.2 0-.3.1-.5.1-.7.2-1.5.3-2.2.4-.2 0-.3 0-.5.1-.6.1-1.2.1-1.8.2h-.6c-.8 0-1.5.1-2.3.1s-1.5 0-2.3-.1h-.6c-.6 0-1.2-.1-1.8-.2-.2 0-.3 0-.5-.1-.7-.1-1.5-.2-2.2-.4-.2 0-.3-.1-.5-.1-.6-.1-1.2-.3-1.7-.4-.2-.1-.4-.1-.6-.2-.7-.2-1.4-.4-2.1-.7-.7-.2-1.3-.5-2-.8l-.6-.3c-.5-.2-1.1-.5-1.6-.8-.1-.1-.3-.1-.4-.2-.6-.3-1.3-.7-1.9-1.1-.1-.1-.3-.2-.4-.3-.5-.3-1-.6-1.4-1-.2-.1-.3-.2-.5-.3-.6-.4-1.2-.9-1.7-1.3s-1.1-.9-1.6-1.4l-.4-.4-1.2-1.2-.3-.3c-.5-.5-1-1.1-1.4-1.6-.1-.1-.2-.3-.3-.4-.4-.4-.7-.9-1-1.4-.1-.2-.2-.3-.3-.5l-1.2-1.8v-.1c-.4-.6-.7-1.2-1-1.8-2.6-5-4.1-10.7-4.1-16.7s1.5-11.7 4.1-16.7zM620 539v221c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V539c-10.1 3.3-20.8 5-32 5s-21.9-1.8-32-5zm64-198v-77c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v77c10.1-3.3 20.8-5 32-5s21.9 1.8 32 5zm-64 198c10.1 3.3 20.8 5 32 5s21.9-1.8 32-5c41.8-13.5 72-52.7 72-99s-30.2-85.5-72-99c-10.1-3.3-20.8-5-32-5s-21.9 1.8-32 5c-41.8 13.5-72 52.7-72 99s30.2 85.5 72 99zm.1-115.7c.3-.6.7-1.2 1-1.8v-.1l1.2-1.8c.1-.2.2-.3.3-.5.3-.5.7-.9 1-1.4.1-.1.2-.3.3-.4.5-.6.9-1.1 1.4-1.6l.3-.3 1.2-1.2.4-.4c.5-.5 1-.9 1.6-1.4.6-.5 1.1-.9 1.7-1.3.2-.1.3-.2.5-.3.5-.3.9-.7 1.4-1 .1-.1.3-.2.4-.3.6-.4 1.2-.7 1.9-1.1.1-.1.3-.1.4-.2.5-.3 1-.5 1.6-.8l.6-.3c.7-.3 1.3-.6 2-.8.7-.3 1.4-.5 2.1-.7.2-.1.4-.1.6-.2.6-.2 1.1-.3 1.7-.4.2 0 .3-.1.5-.1.7-.2 1.5-.3 2.2-.4.2 0 .3 0 .5-.1.6-.1 1.2-.1 1.8-.2h.6c.8 0 1.5-.1 2.3-.1s1.5 0 2.3.1h.6c.6 0 1.2.1 1.8.2.2 0 .3 0 .5.1.7.1 1.5.2 2.2.4.2 0 .3.1.5.1.6.1 1.2.3 1.7.4.2.1.4.1.6.2.7.2 1.4.4 2.1.7.7.2 1.3.5 2 .8l.6.3c.5.2 1.1.5 1.6.8.1.1.3.1.4.2.6.3 1.3.7 1.9 1.1.1.1.3.2.4.3.5.3 1 .6 1.4 1 .2.1.3.2.5.3.6.4 1.2.9 1.7 1.3s1.1.9 1.6 1.4l.4.4 1.2 1.2.3.3c.5.5 1 1.1 1.4 1.6.1.1.2.3.3.4.4.4.7.9 1 1.4.1.2.2.3.3.5l1.2 1.8v.1a36.18 36.18 0 015.1 18.5c0 6-1.5 11.7-4.1 16.7-.3.6-.7 1.2-1 1.8v.1l-1.2 1.8c-.1.2-.2.3-.3.5-.3.5-.7.9-1 1.4-.1.1-.2.3-.3.4-.5.6-.9 1.1-1.4 1.6l-.3.3-1.2 1.2-.4.4c-.5.5-1 .9-1.6 1.4-.6.5-1.1.9-1.7 1.3-.2.1-.3.2-.5.3-.5.3-.9.7-1.4 1-.1.1-.3.2-.4.3-.6.4-1.2.7-1.9 1.1-.1.1-.3.1-.4.2-.5.3-1 .5-1.6.8l-.6.3c-.7.3-1.3.6-2 .8-.7.3-1.4.5-2.1.7-.2.1-.4.1-.6.2-.6.2-1.1.3-1.7.4-.2 0-.3.1-.5.1-.7.2-1.5.3-2.2.4-.2 0-.3 0-.5.1-.6.1-1.2.1-1.8.2h-.6c-.8 0-1.5.1-2.3.1s-1.5 0-2.3-.1h-.6c-.6 0-1.2-.1-1.8-.2-.2 0-.3 0-.5-.1-.7-.1-1.5-.2-2.2-.4-.2 0-.3-.1-.5-.1-.6-.1-1.2-.3-1.7-.4-.2-.1-.4-.1-.6-.2-.7-.2-1.4-.4-2.1-.7-.7-.2-1.3-.5-2-.8l-.6-.3c-.5-.2-1.1-.5-1.6-.8-.1-.1-.3-.1-.4-.2-.6-.3-1.3-.7-1.9-1.1-.1-.1-.3-.2-.4-.3-.5-.3-1-.6-1.4-1-.2-.1-.3-.2-.5-.3-.6-.4-1.2-.9-1.7-1.3s-1.1-.9-1.6-1.4l-.4-.4-1.2-1.2-.3-.3c-.5-.5-1-1.1-1.4-1.6-.1-.1-.2-.3-.3-.4-.4-.4-.7-.9-1-1.4-.1-.2-.2-.3-.3-.5l-1.2-1.8v-.1c-.4-.6-.7-1.2-1-1.8-2.6-5-4.1-10.7-4.1-16.7s1.5-11.7 4.1-16.7z"}}]},name:"control",theme:"outlined"},mo=po,go=function(t,e){return b.createElement(k.Z,(0,ne.Z)((0,ne.Z)({},t),{},{ref:e,icon:mo}))},ho=b.forwardRef(go),yo=ho,jo=o(40411),xo=o(28459),So=o(26412),Ae={historyCardsWrapper:"historyCardsWrapper___uFsOE",historyCard:"historyCard___Oxcvx",selected:"selected___ZAfae",statusTag:"statusTag___jt6hq",buttonWrapper:"buttonWrapper___ii4iq"},Gt=o(46172),Co={completed:"success",running:"processing",limited:"warning",pending:"warning",failed:"error"},bo=function(t,e){var n,a,i=function(f){return f?(0,p.S9)(f):""},l=[{key:"owner",label:(0,p.oz)("owner"),children:(n=t.creator_id)===null||n===void 0?void 0:n.split("@")[0]},{key:"status",label:(0,p.oz)("status"),children:(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(jo.Z,{size:"small",className:Ae.CardBadge,count:e,children:(0,r.jsx)(st.Z,{colorMap:Co,className:_()(Ae.statusTag),label:(0,p.oz)(fo.oK[t.status]),status:t.status})})})},{key:"route-count",label:(0,p.oz)("number-of-routes"),children:((a=t.retro_backbones)===null||a===void 0?void 0:a.length)||t.count||0},{key:"creation-time",label:(0,p.oz)("creation-time"),children:i(t.createdAt)}],u=t.status,c=t.predict_start_time,h=t.search_start_time,v=t.search_end_time;return["limited","pending"].includes(u)&&c&&l.push({key:"predict-start-time",label:(0,p.oz)("estimate-start"),children:i(Je()(c))}),u==="pending"&&l.push({key:"tasks-in-queue",label:(0,p.oz)("tasks-in-queue"),children:t.queue_count||0}),h&&l.push({key:"start-time",label:(0,p.oz)("pages.searchTable.updateForm.schedulingPeriod.timeLabel"),children:i(Je()(h))}),v&&l.push({key:"complete-time",label:(0,p.oz)("complete-time"),children:i(Je()(v))}),l.map(function(m){return R()({span:24},m)})},Ro=function(t){var e=t.retroProcess,n=t.newCount,a=t.onDisplayLogs,i=t.onDisplayParams,l=(0,Gt.A)(),u=l.selectedProcessId,c=l.setSelectedProcessId,h=l.clearOffset,v=e.retro_id===u;return(0,r.jsx)(xo.ZP,{theme:{components:{Descriptions:{itemPaddingBottom:4}}},children:(0,r.jsxs)(ce.Z,{size:"small",type:"inner",title:!1,hoverable:!0,className:_()(Ae.historyCard,Ye()({},Ae.selected,v)),onClick:function(){c(e.retro_id),h(e.compound_id,e.retro_id)},children:[(0,r.jsx)(So.Z,{items:bo(e,n),column:24}),(0,r.jsxs)("div",{className:Ae.buttonWrapper,children:[(0,r.jsx)(y.ZP,{size:"small",type:"text",shape:"circle",onClick:function(f){a==null||a(),f.stopPropagation()},children:(0,r.jsx)(Nt.Z,{})}),(0,r.jsx)(y.ZP,{size:"small",type:"text",shape:"circle",onClick:function(f){i==null||i(),f.stopPropagation()},children:(0,r.jsx)(yo,{})})]})]})})},Po=Ro,Io=function(t){var e=t.moleculeId,n=t.refetchRegister,a=t.onDisplayLogs,i=t.onDisplayParams,l=(0,b.useState)(!1),u=T()(l,2),c=u[0],h=u[1],v=(0,Gt.A)(),m=v.setSelectedProcessId,f=v.selectedProcessId,x=(0,Be.Z)(e,!0),C=x.data,P=x.error,N=x.isLoading,E=x.refetch,M=xe(),B=M.setCount,I=C==null?void 0:C.find(function(L){return L.retro_id===f}),$=c,F=function(ee){return c?ee.status==="completed":!0},G=C==null?void 0:C.filter(F);if((0,b.useEffect)(function(){var L=function(pe){E==null||E(),setTimeout(function(){pe&&m(pe)},300)};n==null||n(L)},[E]),(0,b.useEffect)(function(){var L,ee=G==null||(L=G[0])===null||L===void 0?void 0:L.retro_id;ee&&(!f||ee!==f)&&(G==null?void 0:G.findIndex(function(pe){return pe.retro_id===f}))===-1&&m(ee)},[G,f]),(0,b.useEffect)(function(){var L;f&&B("aiGenerated",(I==null||(L=I.retro_backbones)===null||L===void 0?void 0:L.length)||0)},[f,I]),P||!G)return null;var se=(0,r.jsx)(Ze.Z,{placement:"right",content:(0,r.jsx)(Qn.Z,{checked:c,onChange:function(ee){return h(ee.target.checked)},children:(0,p.oz)("pages.projectCompound.retroHistory.success-retro-only")}),children:(0,r.jsx)(y.ZP,{type:$?"link":"text",size:"small",children:(0,r.jsx)(Kn.Z,{})})});return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(Ut,{justify:"space-between",align:"center",wrap:!1,children:[(0,r.jsxs)("p",{children:[(0,p.oz)("pages.projectCompound.retroHistory.history"),N?(0,r.jsx)(le.Z,{}):"(".concat(G.length,")")]}),(0,r.jsx)("p",{children:se})]}),(0,r.jsx)("div",{className:Ae.historyCardsWrapper,children:(0,r.jsx)(Ut,{gap:8,vertical:!0,children:G.map(function(L){return(0,r.jsx)(Po,{retroProcess:L,newCount:L.offset,onDisplayLogs:function(){return a==null?void 0:a(jt()(L.search_log||[]))},onDisplayParams:function(){return i==null?void 0:i(R()(R()({},L.params||{}),{},{retroId:L.retro_id}))}},L.id)})})})]})},No=Io,zo=function(){var t=(0,b.useState)(),e=T()(t,2),n=e[0],a=e[1];return(0,b.useMemo)(function(){return{event:n,trigger:function(l){return a(l)}}},[n])},Oo=(0,b.createContext)({}),Lo=function(t){V()(t);var e=zo(),n=Pe(),a=n.setCompoundId,i=(0,D.useParams)(),l=i.compoundId,u=Pe(),c=u.retroModel,h=u.setRetroModel,v=u.filterDrawer,m=u.setFilterDrawer,f=(0,b.useState)(),x=T()(f,2),C=x[0],P=x[1],N=(0,b.useRef)(),E=xe(),M=E.tabType,B=E.setPreference,I=function(F){var G;N==null||(G=N.current)===null||G===void 0||G.call(N,F),h("close")};return(0,b.useEffect)(function(){return a(l)},[l]),(0,r.jsxs)(Oo.Provider,{value:e,children:[(0,r.jsx)(yn,{}),(0,r.jsx)(Ln,{open:v,onClose:function(){return m(!1)},onChange:function(F){return B(F)}}),(0,r.jsx)(X._z,{className:"target-molecule-detail-root",children:(0,r.jsxs)(ge.VY,{className:"compound-body",children:[(0,r.jsxs)("div",{className:_()("left-side",{none:M!=="aiGenerated"}),children:[(0,r.jsx)(Rn,{moleculeId:l}),(0,r.jsx)(An,{moleculeId:l,onRetro:function(){return h("init")}}),(0,r.jsx)(No,{moleculeId:l,refetchRegister:function(F){return N.current=F},onDisplayLogs:function(F){return P(F)},onDisplayParams:function(F){return h(F)}})]}),(0,r.jsx)(mn,{compoundId:l})]})}),(0,r.jsx)(Vn,{moleculeId:l,open:c,onSuccess:I,onCancel:function(){return h("close")}}),(0,r.jsx)(Bn,{logs:C})]})},Mo=Lo},26412:function(J,Y,o){"use strict";o.d(Y,{Z:function(){return pt}});var j=o(67294),T=o(93967),S=o.n(T),V=o(74443),X=o(53124),D=o(98675),ge=o(25378),_={xxl:3,xl:3,lg:3,md:3,sm:2,xs:1},p=j.createContext({}),ce=o(50344),ie=function(d,g){var O={};for(var y in d)Object.prototype.hasOwnProperty.call(d,y)&&g.indexOf(y)<0&&(O[y]=d[y]);if(d!=null&&typeof Object.getOwnPropertySymbols=="function")for(var z=0,y=Object.getOwnPropertySymbols(d);z<y.length;z++)g.indexOf(y[z])<0&&Object.prototype.propertyIsEnumerable.call(d,y[z])&&(O[y[z]]=d[y[z]]);return O};const Z=d=>(0,ce.Z)(d).map(g=>Object.assign(Object.assign({},g==null?void 0:g.props),{key:g.key}));function de(d,g,O){const y=j.useMemo(()=>g||Z(O),[g,O]);return j.useMemo(()=>y.map(W=>{var{span:A}=W,K=ie(W,["span"]);return A==="filled"?Object.assign(Object.assign({},K),{filled:!0}):Object.assign(Object.assign({},K),{span:typeof A=="number"?A:(0,V.m9)(d,A)})}),[y,d])}var H=function(d,g){var O={};for(var y in d)Object.prototype.hasOwnProperty.call(d,y)&&g.indexOf(y)<0&&(O[y]=d[y]);if(d!=null&&typeof Object.getOwnPropertySymbols=="function")for(var z=0,y=Object.getOwnPropertySymbols(d);z<y.length;z++)g.indexOf(y[z])<0&&Object.prototype.propertyIsEnumerable.call(d,y[z])&&(O[y[z]]=d[y[z]]);return O};function U(d,g){let O=[],y=[],z=!1,W=0;return d.filter(A=>A).forEach(A=>{const{filled:K}=A,re=H(A,["filled"]);if(K){y.push(re),O.push(y),y=[],W=0;return}const oe=g-W;W+=A.span||1,W>=g?(W>g?(z=!0,y.push(Object.assign(Object.assign({},re),{span:oe}))):y.push(re),O.push(y),y=[],W=0):y.push(re)}),y.length>0&&O.push(y),O=O.map(A=>{const K=A.reduce((re,oe)=>re+(oe.span||1),0);if(K<g){const re=A[A.length-1];return re.span=g-K+1,A}return A}),[O,z]}var be=(d,g)=>{const[O,y]=(0,j.useMemo)(()=>U(g,d),[g,d]);return O},ve=d=>{let{children:g}=d;return g};function je(d){return d!=null}var me=d=>{const{itemPrefixCls:g,component:O,span:y,className:z,style:W,labelStyle:A,contentStyle:K,bordered:re,label:oe,content:le,colon:Se,type:Q}=d,he=O;return re?j.createElement(he,{className:S()({[`${g}-item-label`]:Q==="label",[`${g}-item-content`]:Q==="content"},z),style:W,colSpan:y},je(oe)&&j.createElement("span",{style:A},oe),je(le)&&j.createElement("span",{style:K},le)):j.createElement(he,{className:S()(`${g}-item`,z),style:W,colSpan:y},j.createElement("div",{className:`${g}-item-container`},(oe||oe===0)&&j.createElement("span",{className:S()(`${g}-item-label`,{[`${g}-item-no-colon`]:!Se}),style:A},oe),(le||le===0)&&j.createElement("span",{className:S()(`${g}-item-content`),style:K},le)))};function Re(d,g,O){let{colon:y,prefixCls:z,bordered:W}=g,{component:A,type:K,showLabel:re,showContent:oe,labelStyle:le,contentStyle:Se}=O;return d.map((Q,he)=>{let{label:Le,children:Me,prefixCls:Ee=z,className:ne,style:$e,labelStyle:ye,contentStyle:k,span:Ie=1,key:Te}=Q;return typeof A=="string"?j.createElement(me,{key:`${K}-${Te||he}`,className:ne,style:$e,labelStyle:Object.assign(Object.assign({},le),ye),contentStyle:Object.assign(Object.assign({},Se),k),span:Ie,colon:y,component:A,itemPrefixCls:Ee,bordered:W,label:re?Le:null,content:oe?Me:null,type:K}):[j.createElement(me,{key:`label-${Te||he}`,className:ne,style:Object.assign(Object.assign(Object.assign({},le),$e),ye),span:1,colon:y,component:A[0],itemPrefixCls:Ee,bordered:W,label:Le,type:"label"}),j.createElement(me,{key:`content-${Te||he}`,className:ne,style:Object.assign(Object.assign(Object.assign({},Se),$e),k),span:Ie*2-1,component:A[1],itemPrefixCls:Ee,bordered:W,content:Me,type:"content"})]})}var Pe=d=>{const g=j.useContext(p),{prefixCls:O,vertical:y,row:z,index:W,bordered:A}=d;return y?j.createElement(j.Fragment,null,j.createElement("tr",{key:`label-${W}`,className:`${O}-row`},Re(z,d,Object.assign({component:"th",type:"label",showLabel:!0},g))),j.createElement("tr",{key:`content-${W}`,className:`${O}-row`},Re(z,d,Object.assign({component:"td",type:"content",showContent:!0},g)))):j.createElement("tr",{key:W,className:`${O}-row`},Re(z,d,Object.assign({component:A?["th","td"]:"td",type:"item",showLabel:!0,showContent:!0},g)))},fe=o(85982),He=o(14747),Oe=o(83559),te=o(83262);const Be=d=>{const{componentCls:g,labelBg:O}=d;return{[`&${g}-bordered`]:{[`> ${g}-view`]:{border:`${(0,fe.unit)(d.lineWidth)} ${d.lineType} ${d.colorSplit}`,"> table":{tableLayout:"auto"},[`${g}-row`]:{borderBottom:`${(0,fe.unit)(d.lineWidth)} ${d.lineType} ${d.colorSplit}`,"&:last-child":{borderBottom:"none"},[`> ${g}-item-label, > ${g}-item-content`]:{padding:`${(0,fe.unit)(d.padding)} ${(0,fe.unit)(d.paddingLG)}`,borderInlineEnd:`${(0,fe.unit)(d.lineWidth)} ${d.lineType} ${d.colorSplit}`,"&:last-child":{borderInlineEnd:"none"}},[`> ${g}-item-label`]:{color:d.colorTextSecondary,backgroundColor:O,"&::after":{display:"none"}}}},[`&${g}-middle`]:{[`${g}-row`]:{[`> ${g}-item-label, > ${g}-item-content`]:{padding:`${(0,fe.unit)(d.paddingSM)} ${(0,fe.unit)(d.paddingLG)}`}}},[`&${g}-small`]:{[`${g}-row`]:{[`> ${g}-item-label, > ${g}-item-content`]:{padding:`${(0,fe.unit)(d.paddingXS)} ${(0,fe.unit)(d.padding)}`}}}}}},qe=d=>{const{componentCls:g,extraColor:O,itemPaddingBottom:y,itemPaddingEnd:z,colonMarginRight:W,colonMarginLeft:A,titleMarginBottom:K}=d;return{[g]:Object.assign(Object.assign(Object.assign({},(0,He.Wf)(d)),Be(d)),{"&-rtl":{direction:"rtl"},[`${g}-header`]:{display:"flex",alignItems:"center",marginBottom:K},[`${g}-title`]:Object.assign(Object.assign({},He.vS),{flex:"auto",color:d.titleColor,fontWeight:d.fontWeightStrong,fontSize:d.fontSizeLG,lineHeight:d.lineHeightLG}),[`${g}-extra`]:{marginInlineStart:"auto",color:O,fontSize:d.fontSize},[`${g}-view`]:{width:"100%",borderRadius:d.borderRadiusLG,table:{width:"100%",tableLayout:"fixed",borderCollapse:"collapse"}},[`${g}-row`]:{"> th, > td":{paddingBottom:y,paddingInlineEnd:z},"> th:last-child, > td:last-child":{paddingInlineEnd:0},"&:last-child":{borderBottom:"none","> th, > td":{paddingBottom:0}}},[`${g}-item-label`]:{color:d.colorTextTertiary,fontWeight:"normal",fontSize:d.fontSize,lineHeight:d.lineHeight,textAlign:"start","&::after":{content:'":"',position:"relative",top:-.5,marginInline:`${(0,fe.unit)(A)} ${(0,fe.unit)(W)}`},[`&${g}-item-no-colon::after`]:{content:'""'}},[`${g}-item-no-label`]:{"&::after":{margin:0,content:'""'}},[`${g}-item-content`]:{display:"table-cell",flex:1,color:d.contentColor,fontSize:d.fontSize,lineHeight:d.lineHeight,wordBreak:"break-word",overflowWrap:"break-word"},[`${g}-item`]:{paddingBottom:0,verticalAlign:"top","&-container":{display:"flex",[`${g}-item-label`]:{display:"inline-flex",alignItems:"baseline"},[`${g}-item-content`]:{display:"inline-flex",alignItems:"baseline",minWidth:"1em"}}},"&-middle":{[`${g}-row`]:{"> th, > td":{paddingBottom:d.paddingSM}}},"&-small":{[`${g}-row`]:{"> th, > td":{paddingBottom:d.paddingXS}}}})}},Ye=d=>({labelBg:d.colorFillAlter,titleColor:d.colorText,titleMarginBottom:d.fontSizeSM*d.lineHeightSM,itemPaddingBottom:d.padding,itemPaddingEnd:d.padding,colonMarginRight:d.marginXS,colonMarginLeft:d.marginXXS/2,contentColor:d.colorText,extraColor:d.colorText});var et=(0,Oe.I$)("Descriptions",d=>{const g=(0,te.mergeToken)(d,{});return qe(g)},Ye),xe=function(d,g){var O={};for(var y in d)Object.prototype.hasOwnProperty.call(d,y)&&g.indexOf(y)<0&&(O[y]=d[y]);if(d!=null&&typeof Object.getOwnPropertySymbols=="function")for(var z=0,y=Object.getOwnPropertySymbols(d);z<y.length;z++)g.indexOf(y[z])<0&&Object.prototype.propertyIsEnumerable.call(d,y[z])&&(O[y[z]]=d[y[z]]);return O};const r=d=>{const{prefixCls:g,title:O,extra:y,column:z,colon:W=!0,bordered:A,layout:K,children:re,className:oe,rootClassName:le,style:Se,size:Q,labelStyle:he,contentStyle:Le,items:Me}=d,Ee=xe(d,["prefixCls","title","extra","column","colon","bordered","layout","children","className","rootClassName","style","size","labelStyle","contentStyle","items"]),{getPrefixCls:ne,direction:$e,descriptions:ye}=j.useContext(X.E_),k=ne("descriptions",g),Ie=(0,ge.Z)(),Te=j.useMemo(()=>{var De;return typeof z=="number"?z:(De=(0,V.m9)(Ie,Object.assign(Object.assign({},_),z)))!==null&&De!==void 0?De:3},[Ie,z]),tt=de(Ie,Me,re),We=(0,D.Z)(Q),rt=be(Te,tt),[nt,ot,Ve]=et(k),at=j.useMemo(()=>({labelStyle:he,contentStyle:Le}),[he,Le]);return nt(j.createElement(p.Provider,{value:at},j.createElement("div",Object.assign({className:S()(k,ye==null?void 0:ye.className,{[`${k}-${We}`]:We&&We!=="default",[`${k}-bordered`]:!!A,[`${k}-rtl`]:$e==="rtl"},oe,le,ot,Ve),style:Object.assign(Object.assign({},ye==null?void 0:ye.style),Se)},Ee),(O||y)&&j.createElement("div",{className:`${k}-header`},O&&j.createElement("div",{className:`${k}-title`},O),y&&j.createElement("div",{className:`${k}-extra`},y)),j.createElement("div",{className:`${k}-view`},j.createElement("table",null,j.createElement("tbody",null,rt.map((De,Ke)=>j.createElement(Pe,{key:Ke,index:Ke,colon:W,prefixCls:k,vertical:K==="vertical",bordered:A,row:De}))))))))};r.Item=ve;var pt=r},49677:function(J){function Y(o){if(o==null)throw new TypeError("Cannot destructure "+o)}J.exports=Y,J.exports.__esModule=!0,J.exports.default=J.exports},61227:function(J,Y,o){var j=o(82187),T=o(96936),S=o(96263),V=o(69094);function X(D){return j(D)||T(D)||S(D)||V()}J.exports=X,J.exports.__esModule=!0,J.exports.default=J.exports}}]);

//# sourceMappingURL=p__compound__refactor.44264790.async.js.map