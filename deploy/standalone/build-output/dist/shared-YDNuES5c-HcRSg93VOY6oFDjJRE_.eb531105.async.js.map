{"version": 3, "file": "shared-YDNuES5c-HcRSg93VOY6oFDjJRE_.eb531105.async.js", "mappings": "kuDACIA,EAAU,EACVC,EAAiB,IACjBC,EAAkB,IAClBC,EAAkB,IAClBC,EAAkB,IAClBC,EAAkB,EAClBC,EAAiB,EAEjBC,EAAe,CAAC,CAClB,MAAO,EACP,QAAS,GACX,EAAG,CACD,MAAO,EACP,QAAS,GACX,EAAG,CACD,MAAO,EACP,QAAS,EACX,EAAG,CACD,MAAO,EACP,QAAS,GACX,EAAG,CACD,MAAO,EACP,QAAS,GACX,EAAG,CACD,MAAO,EACP,QAAS,GACX,EAAG,CACD,MAAO,EACP,QAAS,EACX,EAAG,CACD,MAAO,EACP,QAAS,GACX,EAAG,CACD,MAAO,EACP,QAAS,GACX,EAAG,CACD,MAAO,EACP,QAAS,GACX,CAAC,EAGD,SAASC,EAAMC,EAAM,CACnB,IAAIC,EAAID,EAAK,EACXE,EAAIF,EAAK,EACTG,EAAIH,EAAK,EACPI,KAAM,MAASH,EAAGC,EAAGC,CAAC,EAC1B,MAAO,CACL,EAAGC,EAAI,EAAI,IACX,EAAGA,EAAI,EACP,EAAGA,EAAI,CACT,CACF,CAIA,SAASC,EAAMC,EAAO,CACpB,IAAIL,EAAIK,EAAM,EACZJ,EAAII,EAAM,EACVH,EAAIG,EAAM,EACZ,MAAO,IAAI,UAAO,MAASL,EAAGC,EAAGC,EAAG,EAAK,CAAC,CAC5C,CAKA,SAASI,EAAIC,EAAMC,EAAMC,EAAQ,CAC/B,IAAIC,EAAID,EAAS,IACbE,EAAM,CACR,GAAIH,EAAK,EAAID,EAAK,GAAKG,EAAIH,EAAK,EAChC,GAAIC,EAAK,EAAID,EAAK,GAAKG,EAAIH,EAAK,EAChC,GAAIC,EAAK,EAAID,EAAK,GAAKG,EAAIH,EAAK,CAClC,EACA,OAAOI,CACT,CACA,SAASC,EAAOT,EAAKU,EAAGC,EAAO,CAC7B,IAAIC,EAEJ,OAAI,KAAK,MAAMZ,EAAI,CAAC,GAAK,IAAM,KAAK,MAAMA,EAAI,CAAC,GAAK,IAClDY,EAAMD,EAAQ,KAAK,MAAMX,EAAI,CAAC,EAAIb,EAAUuB,EAAI,KAAK,MAAMV,EAAI,CAAC,EAAIb,EAAUuB,EAE9EE,EAAMD,EAAQ,KAAK,MAAMX,EAAI,CAAC,EAAIb,EAAUuB,EAAI,KAAK,MAAMV,EAAI,CAAC,EAAIb,EAAUuB,EAE5EE,EAAM,EACRA,GAAO,IACEA,GAAO,MAChBA,GAAO,KAEFA,CACT,CACA,SAASC,EAAcb,EAAKU,EAAGC,EAAO,CAEpC,GAAIX,EAAI,IAAM,GAAKA,EAAI,IAAM,EAC3B,OAAOA,EAAI,EAEb,IAAIc,EACJ,OAAIH,EACFG,EAAad,EAAI,EAAIZ,EAAiBsB,EAC7BA,IAAMjB,EACfqB,EAAad,EAAI,EAAIZ,EAErB0B,EAAad,EAAI,EAAIX,EAAkBqB,EAGrCI,EAAa,IACfA,EAAa,GAGXH,GAASD,IAAMlB,GAAmBsB,EAAa,KACjDA,EAAa,IAEXA,EAAa,MACfA,EAAa,KAER,OAAOA,EAAW,QAAQ,CAAC,CAAC,CACrC,CACA,SAASC,EAASf,EAAKU,EAAGC,EAAO,CAC/B,IAAIK,EACJ,OAAIL,EACFK,EAAQhB,EAAI,EAAIV,EAAkBoB,EAElCM,EAAQhB,EAAI,EAAIT,EAAkBmB,EAEhCM,EAAQ,IACVA,EAAQ,GAEH,OAAOA,EAAM,QAAQ,CAAC,CAAC,CAChC,CACe,SAASC,EAASC,EAAO,CAItC,QAHIC,EAAO,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAC,EAC5EC,EAAW,CAAC,EACZC,KAAS,MAAWH,CAAK,EACpBR,EAAIlB,EAAiBkB,EAAI,EAAGA,GAAK,EAAG,CAC3C,IAAIV,GAAML,EAAM0B,CAAM,EAClBC,GAAcrB,KAAM,MAAW,CACjC,EAAGQ,EAAOT,GAAKU,EAAG,EAAI,EACtB,EAAGG,EAAcb,GAAKU,EAAG,EAAI,EAC7B,EAAGK,EAASf,GAAKU,EAAG,EAAI,CAC1B,CAAC,CAAC,EACFU,EAAS,KAAKE,EAAW,CAC3B,CACAF,EAAS,KAAKnB,EAAMoB,CAAM,CAAC,EAC3B,QAASE,GAAK,EAAGA,IAAM9B,EAAgB8B,IAAM,EAAG,CAC9C,IAAIC,GAAO7B,EAAM0B,CAAM,EACnBI,GAAexB,KAAM,MAAW,CAClC,EAAGQ,EAAOe,GAAMD,EAAE,EAClB,EAAGV,EAAcW,GAAMD,EAAE,EACzB,EAAGR,EAASS,GAAMD,EAAE,CACtB,CAAC,CAAC,EACFH,EAAS,KAAKK,EAAY,CAC5B,CAGA,OAAIN,EAAK,QAAU,OACVzB,EAAa,IAAI,SAAUgC,GAAO,CACvC,IAAIC,GAAQD,GAAM,MAChBE,GAAUF,GAAM,QACdG,GAAkB5B,EAAME,KAAI,MAAWgB,EAAK,iBAAmB,SAAS,KAAG,MAAWC,EAASO,EAAK,CAAC,EAAGC,GAAU,GAAG,CAAC,EAC1H,OAAOC,EACT,CAAC,EAEIT,CACT,CChKO,IAAIU,EAAsB,CAC/B,IAAO,UACP,QAAW,UACX,OAAU,UACV,KAAQ,UACR,OAAU,UACV,KAAQ,UACR,MAAS,UACT,KAAQ,UACR,KAAQ,UACR,SAAY,UACZ,OAAU,UACV,QAAW,UACX,KAAQ,SACV,EACWC,EAAM,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EAC9HA,EAAI,QAAUA,EAAI,CAAC,EACZ,IAAIC,EAAU,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EAClIA,EAAQ,QAAUA,EAAQ,CAAC,EACpB,IAAIC,EAAS,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACjIA,EAAO,QAAUA,EAAO,CAAC,EAClB,IAAIC,EAAO,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EAC/HA,EAAK,QAAUA,EAAK,CAAC,EACd,IAAIC,GAAS,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACjIA,GAAO,QAAUA,GAAO,CAAC,EAClB,IAAIC,EAAO,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EAC/HA,EAAK,QAAUA,EAAK,CAAC,EACd,IAAIC,EAAQ,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EAChIA,EAAM,QAAUA,EAAM,CAAC,EAChB,IAAIC,GAAO,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EAC/HA,GAAK,QAAUA,GAAK,CAAC,EACd,IAAIC,GAAO,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EAC/HA,GAAK,QAAUA,GAAK,CAAC,EACd,IAAIC,GAAW,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACnIA,GAAS,QAAUA,GAAS,CAAC,EACtB,IAAIC,GAAS,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACjIA,GAAO,QAAUA,GAAO,CAAC,EAClB,IAAIC,GAAU,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EAClIA,GAAQ,QAAUA,GAAQ,CAAC,EACpB,IAAIC,GAAO,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EAC/HA,GAAK,QAAUA,GAAK,CAAC,EACd,IAAIC,GAAOD,GACPE,EAAiB,CAC1B,IAAKd,EACL,QAASC,EACT,OAAQC,EACR,KAAMC,EACN,OAAQC,GACR,KAAMC,EACN,MAAOC,EACP,KAAMC,GACN,KAAMC,GACN,SAAUC,GACV,OAAQC,GACR,QAASC,GACT,KAAMC,EACR,EACWG,EAAU,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EAClIA,EAAQ,QAAUA,EAAQ,CAAC,EACpB,IAAIC,EAAc,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACtIA,EAAY,QAAUA,EAAY,CAAC,EAC5B,IAAIC,EAAa,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACrIA,EAAW,QAAUA,EAAW,CAAC,EAC1B,IAAIC,EAAW,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACnIA,EAAS,QAAUA,EAAS,CAAC,EACtB,IAAIC,EAAa,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACrIA,EAAW,QAAUA,EAAW,CAAC,EAC1B,IAAIC,EAAW,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACnIA,EAAS,QAAUA,EAAS,CAAC,EACtB,IAAIC,EAAY,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACpIA,EAAU,QAAUA,EAAU,CAAC,EACxB,IAAIC,EAAW,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACnIA,EAAS,QAAUA,EAAS,CAAC,EACtB,IAAIC,EAAW,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACnIA,EAAS,QAAUA,EAAS,CAAC,EACtB,IAAIC,EAAe,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACvIA,EAAa,QAAUA,EAAa,CAAC,EAC9B,IAAIC,GAAa,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACrIA,GAAW,QAAUA,GAAW,CAAC,EAC1B,IAAIC,EAAc,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACtIA,EAAY,QAAUA,EAAY,CAAC,EAC5B,IAAIC,EAAW,CAAC,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,UAAW,SAAS,EACnIA,EAAS,QAAUA,EAAS,CAAC,EACtB,IAAIC,GAAqB,CAC9B,IAAKb,EACL,QAASC,EACT,OAAQC,EACR,KAAMC,EACN,OAAQC,EACR,KAAMC,EACN,MAAOC,EACP,KAAMC,EACN,KAAMC,EACN,SAAUC,EACV,OAAQC,GACR,QAASC,EACT,KAAMC,CACR,C,6SCjGWE,EAAuB,SAA8BC,EAAcC,EAAW,CACvF,IAAIC,EAAuBC,EAAuBC,EAAwBC,EAAuBC,EAC7FC,KAAoB,KAAc,CAAC,EAAGP,CAAY,EACtD,SAAO,QAAc,KAAc,CACjC,SAAU,mBAAmB,OAAOC,EAAU,iBAAkB,IAAI,EAAE,OAAOA,EAAU,cAAe,OAAO,EAC7G,qBAAsBA,EAAU,mBAChC,wBAAyBM,GAAsB,OAAyCL,EAAwBK,EAAkB,SAAW,MAAQL,IAA0B,OAAS,OAASA,EAAsB,wBACvN,2BAAyB,MAASD,EAAU,cAAe,GAAI,EAC/D,0BAA2BA,EAAU,aACvC,EAAGM,CAAiB,EAAG,CAAC,EAAG,CACzB,UAAQ,KAAc,CACpB,iBAAe,MAASN,EAAU,gBAAiB,EAAG,EACtD,uBAAqB,MAASA,EAAU,gBAAiB,EAAG,EAC5D,iBAAkBA,EAAU,UAC5B,wBAAsB,MAASA,EAAU,cAAe,GAAI,EAC5D,wBAAyB,cACzB,qBAAsBM,GAAsB,OAAyCJ,EAAwBI,EAAkB,UAAY,MAAQJ,IAA0B,OAAS,OAASA,EAAsB,iBAAmB,4BAA8BC,EAAyBG,EAAkB,UAAY,MAAQH,IAA2B,OAAS,OAASA,EAAuB,cAAgBH,EAAU,gBACna,yBAAuB,MAASA,EAAU,cAAe,GAAI,EAC7D,gCAA8B,MAASA,EAAU,cAAe,GAAI,EACpE,0BAA2BA,EAAU,kBACrC,mBAAoB,GACpB,cAAeA,EAAU,mBACzB,uBAAwBA,EAAU,kBAClC,mBAAoBA,EAAU,UAC9B,oBAAqBA,EAAU,SACjC,EAAGM,EAAkB,MAAM,EAC3B,SAAO,KAAc,CACnB,wBAAyB,EACzB,uBAAwB,EACxB,uBAAwBN,EAAU,gBAClC,8BAA+BA,EAAU,mBACzC,4BAA0B,MAASA,EAAU,cAAe,GAAI,EAChE,oBAAqB,cACrB,wBAAsB,MAASA,EAAU,cAAe,GAAI,EAC5D,wBAAsB,MAASA,EAAU,cAAe,GAAI,EAC5D,2BAAyB,MAASA,EAAU,cAAe,GAAI,EAC/D,uBAAwBA,EAAU,UAClC,yBAAuB,MAASA,EAAU,cAAe,GAAI,EAC7D,oBAAqBA,EAAU,UAC/B,cAAeA,EAAU,mBACzB,uBAAwBA,EAAU,kBAClC,mBAAoBA,EAAU,UAC9B,4BAA0B,MAASA,EAAU,cAAe,GAAI,CAClE,EAAGM,EAAkB,KAAK,EAC1B,iBAAe,KAAc,CAC3B,qBAAsB,cACtB,oCAAqCF,EAAwBE,EAAkB,iBAAmB,MAAQF,IAA0B,OAAS,OAASA,EAAsB,mCAAqC,GACjN,mCAAoCC,EAAyBC,EAAkB,iBAAmB,MAAQD,IAA2B,OAAS,OAASA,EAAuB,kCAAoC,GAClN,0BAA2BL,EAAU,eACvC,EAAGM,EAAkB,aAAa,CACpC,CAAC,CACH,E,sBCnDWC,EAAQ,UAAiB,CAElC,QADIC,EAAM,CAAC,EACFC,EAAO,UAAU,OAAQC,EAAO,IAAI,MAAMD,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,CAAI,EAAI,UAAUA,CAAI,EAK7B,QAHIC,EAAKF,EAAK,OACVG,EACAjE,EAAI,EACDA,EAAIgE,EAAIhE,GAAK,EAClB,IAAKiE,KAAOH,EAAK9D,CAAC,EACZ8D,EAAK9D,CAAC,EAAE,eAAeiE,CAAG,OACxB,KAAQL,EAAIK,CAAG,CAAC,IAAM,aAAY,KAAQH,EAAK9D,CAAC,EAAEiE,CAAG,CAAC,IAAM,UAAYL,EAAIK,CAAG,IAAM,QAAaL,EAAIK,CAAG,IAAM,MAAQ,CAAC,MAAM,QAAQL,EAAIK,CAAG,CAAC,GAAK,CAAC,MAAM,QAAQH,EAAK9D,CAAC,EAAEiE,CAAG,CAAC,EAChLL,EAAIK,CAAG,KAAI,QAAc,KAAc,CAAC,EAAGL,EAAIK,CAAG,CAAC,EAAGH,EAAK9D,CAAC,EAAEiE,CAAG,CAAC,EAElEL,EAAIK,CAAG,EAAIH,EAAK9D,CAAC,EAAEiE,CAAG,GAK9B,OAAOL,CACT,E,iCClBIM,EAAY,CAAC,SAAU,cAAc,EACvCC,EAAa,CAAC,SAAU,OAAO,EAkB7BC,GAAgB,SAAuBR,EAAK,CAC9C,IAAIS,EAAS,CAAC,EAMd,GALA,OAAO,KAAKT,GAAO,CAAC,CAAC,EAAE,QAAQ,SAAUK,EAAK,CACxCL,EAAIK,CAAG,IAAM,SACfI,EAAOJ,CAAG,EAAIL,EAAIK,CAAG,EAEzB,CAAC,EACG,SAAO,KAAKI,CAAM,EAAE,OAAS,GAGjC,OAAOA,CACT,EAQWC,EAAiB,UAA0B,CACpD,IAAIC,EAAuBC,EAC3B,MAAI,SAAOC,GAAY,gBAAkBF,EAAwB,gBAA0B,MAAQA,IAA0B,OAAS,OAASA,EAAsB,YAAY,KAAO,UAAYC,EAAyB,gBAA0B,MAAQA,IAA2B,OAAS,OAASA,EAAuB,YAAY,KAAO,OAIxV,EA2BIE,EAAgC,gBAAoB,CACtD,QAAM,QAAc,KAAc,CAAC,EAAG,IAAQ,EAAG,CAAC,EAAG,CACnD,OAAQ,SACV,CAAC,EACD,aAAc,CAAC,EACf,MAAO,aACP,OAAQ,GACR,KAAM,GACN,MAAO,cACT,CAAC,EACGC,GAAiBD,EAAiB,SAQlCE,GAAa,UAAsB,CACrC,IAAIC,KAAgB,MAAa,EAC/BC,EAAQD,EAAc,MACxB,sBAAU,UAAY,CACpB,OAAO,UAAY,CAGjBC,EAAM,MAAM,CACd,CAEF,EAAG,CAAC,CAAC,EACE,IACT,EAOIC,GAA0B,SAAiCC,EAAO,CACpE,IAAIC,EACAC,EAAWF,EAAM,SACnBG,EAAOH,EAAM,KACbI,EAAeJ,EAAM,aACrBK,EAAwBL,EAAM,eAC9BM,EAAiBD,IAA0B,OAAS,GAAQA,EAC5DE,EAAaP,EAAM,MACnBQ,EAAYR,EAAM,UAClBS,EAAOT,EAAM,KACXU,MAAc,cAAW,kBAAgC,EAC3DC,EAASD,GAAY,OACrBE,EAAeF,GAAY,aAC3BG,MAAa,KAAyBH,GAAaxB,CAAS,EAC1D4B,GAAgBb,EAAqB,KAAS,YAAc,MAAQA,IAAuB,OAAS,OAASA,EAAmB,KAAK,IAAQ,EAC7Ic,KAAa,cAAWrB,CAAgB,EAQxCsB,EAAmBR,EAAY,IAAI,OAAOA,CAAS,EAAI,IAAI,OAAOI,EAAa,EAAG,MAAM,EACxFK,EAAS,IAAML,EAAa,EAC5BM,EAAO,GAAG,OAAOF,CAAgB,EAIjCG,MAAsB,WAAQ,UAAY,CAC5C,OAAOjD,EAAqBqC,GAAc,CAAC,EAAGO,EAAa,OAAS,cAAY,CAClF,EAAG,CAACP,EAAYO,EAAa,KAAK,CAAC,EAC/BM,MAAkB,WAAQ,UAAY,CACxC,IAAIC,GACAC,GAAaX,GAAW,KAA4B,OAASA,EAAO,OACpE1B,MAAM,MAA2BqC,EAAU,EAE3CC,GAAed,GAAS,KAA0BA,EAAOa,MAAgBD,GAAmBN,EAAW,QAAU,MAAQM,KAAqB,OAAS,OAASA,GAAiB,UAAY,UAAY,KAAQpC,EAAG,EAAI8B,EAAW,MAAQ,KAAQ9B,EAAG,EAC1P,SAAO,QAAc,KAAc,CAAC,EAAG8B,CAAU,EAAG,CAAC,EAAG,CACtD,KAAMZ,GAAS,KAA0BA,EAAOY,EAAW,KAC3D,MAAOpC,EAAMoC,EAAW,MAAOD,EAAa,MAAO,CACjD,iBAAkBE,EAClB,OAAQC,EACR,QAASH,EAAa,MAAM,GAC5B,OAAQK,EACV,CAAC,EACD,KAAMI,IAAgB,IACxB,CAAC,CACH,EAAG,CAACZ,GAAW,KAA4B,OAASA,EAAO,OAAQI,EAAYZ,EAAMW,EAAa,MAAOA,EAAa,MAAM,GAAIE,EAAkBC,EAAQE,GAAqBV,CAAI,CAAC,EAChLe,MAAa,QAAc,KAAc,CAAC,EAAGJ,GAAgB,OAAS,CAAC,CAAC,EAAG,CAAC,EAAG,CACjF,iBAAkBJ,CACpB,CAAC,EACGS,MAAiB,iBAAcX,EAAa,MAAO,CAACA,EAAa,MAAOU,IAAe,KAAgCA,GAAa,CAAC,CAAC,EAAG,CACzI,KAAMN,EACN,SAAUM,EACZ,CAAC,EACDE,MAAkB,KAAeD,GAAgB,CAAC,EAClDE,GAAQD,GAAgB,CAAC,EACzBE,GAAeF,GAAgB,CAAC,EAC9BG,MAAS,WAAQ,UAAY,CAI/B,MAHI,IAAM,SAAW,IAGjBd,EAAW,SAAW,GAE5B,EAAG,CAACA,EAAW,OAAQf,EAAM,MAAM,CAAC,EAChC8B,MAAS,WAAQ,UAAY,CAM/B,OALI9B,EAAM,SAAW,IAGjBe,EAAW,SAAW,IAEtBzB,EAAe,IAAM,GAChB,GACEwB,EAAa,OACfA,EAAa,OAGbc,EAEX,EAAG,CAACA,GAAcb,EAAW,OAAQf,EAAM,MAAM,CAAC,KAClD,aAAU,UAAY,CACpB,YAAcW,GAAW,KAA4B,OAASA,EAAO,SAAW,OAAO,CACzF,EAAG,CAACA,GAAW,KAA4B,OAASA,EAAO,MAAM,CAAC,EAClE,IAAIoB,MAAc,WAAQ,UAAY,CACpC,SAAO,QAAc,KAAc,CAAC,EAAGlB,GAAW,KAAK,EAAG,CAAC,EAAG,CAC5D,OAAQiB,GACR,OAAQD,IAAUvC,EAAe,CACnC,CAAC,CACH,EAAG,CAACuB,GAAW,MAAOiB,GAAQD,GAAQvC,EAAe,CAAC,CAAC,EACnD0C,MAAwB,WAAQ,UAAY,CAC9C,SAAO,QAAc,KAAc,CAAC,EAAGZ,EAAe,EAAG,CAAC,EAAG,CAC3D,aAAchB,IAAiBgB,IAAoB,KAAqC,OAASA,GAAgB,cACjH,MAAOO,GACP,MAAOb,EAAa,MACpB,OAAQe,GACR,OAAQC,EACV,CAAC,CACH,EAAG,CAACV,GAAiBhB,EAAcuB,GAAOb,EAAa,MAAOe,GAAQC,EAAM,CAAC,EACzEG,MAAoB,WAAQ,UAAY,CAC1C,SAAoB,OAAK,QAAoB,QAAc,KAAc,CAAC,EAAGpB,EAAU,EAAG,CAAC,EAAG,CAC5F,MAAOkB,GACP,YAAuB,OAAKrC,EAAiB,SAAU,CACrD,MAAOsC,GACP,YAAuB,QAAM,WAAW,CACtC,SAAU,CAAC1B,MAA+B,OAAKV,GAAY,CAAC,CAAC,EAAGM,CAAQ,CAC1E,CAAC,CACH,CAAC,CACH,CAAC,CAAC,CAGJ,EAAG,CAACW,GAAYkB,GAAaC,GAAuB1B,EAAgBJ,CAAQ,CAAC,EAC7E,OAAKI,KACe,OAAK,KAAW,CAClC,MAAO,CACL,SAAU,UAAoB,CAC5B,OAAO,IAAI,GACb,CACF,EACA,SAAU2B,EACZ,CAAC,EAR2BA,EAS9B,EAOWC,GAAoB,SAA2BlC,EAAO,CAC/D,IAAImC,EAAWnC,EAAM,SACnBG,EAAOH,EAAM,KACb2B,EAAQ3B,EAAM,MACZe,KAAa,cAAWrB,CAAgB,EACxC0C,KAAe,cAAW,kBAAgC,EAC5DzB,EAASyB,EAAa,OACtBC,EAAQD,EAAa,MACrBtD,KAAO,KAAyBsD,EAAcjD,CAAU,EAGtDmD,EAAgBH,GAAYpB,EAAW,SAAW,QAAa,OAAO,KAAKf,CAAK,EAAE,KAAK,EAAE,KAAK,GAAG,IAAM,oBAC3G,GAAIsC,EAAe,SAAoB,OAAK,WAAW,CACrD,SAAUtC,EAAM,QAClB,CAAC,EACD,IAAIuC,GAAiB,UAA0B,CAC7C,IAAIC,GAASrC,GAAS,KAA0BA,EAAOY,EAAW,KAClE,OAAIyB,IAAU,CAAC,MAAM,QAAQH,GAAU,KAA2B,OAASA,EAAM,SAAS,EACjF,CAAC,KAAS,cAAeA,GAAU,KAA2B,OAASA,EAAM,SAAS,EAAE,OAAO,OAAO,EAE3GG,IAAU,MAAM,QAAQH,GAAU,KAA2B,OAASA,EAAM,SAAS,EAChF,CAAC,KAAS,aAAa,EAAE,UAAO,MAAoBA,GAAU,KAA2B,OAASA,EAAM,YAAc,CAAC,CAAC,CAAC,EAAE,OAAO,OAAO,EAE3IA,GAAU,KAA2B,OAASA,EAAM,SAC7D,EAEII,KAAiB,QAAc,KAAc,CAAC,EAAG3D,CAAI,EAAG,CAAC,EAAG,CAC9D,OAAQ6B,GAAU+B,IAClB,MAAOtD,MAAc,QAAc,KAAc,CAAC,EAAGiD,CAAK,EAAG,CAAC,EAAG,CAC/D,UAAWE,GAAe,CAC5B,CAAC,CAAC,CACJ,CAAC,EACD,SAAoB,OAAK,QAAoB,QAAc,KAAc,CAAC,EAAGE,CAAc,EAAG,CAAC,EAAG,CAChG,YAAuB,OAAK1C,MAAyB,QAAc,KAAc,CAAC,EAAGC,CAAK,EAAG,CAAC,EAAG,CAC/F,MAAO2B,CACT,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,EAUO,SAASgB,IAAU,CACxB,IAAIC,KAAe,cAAW,kBAAgC,EAC5DjC,EAASiC,EAAa,OACpBC,KAAe,cAAWnD,CAAgB,EAC5Ce,EAAOoC,EAAa,KACtB,OAAIpC,GAAQA,EAAK,SAAW,UACnBA,GAAQ,KAEbE,GAAW,MAA6BA,EAAO,QAC1C,QAAQ,MAA2BA,EAAO,MAAM,CAAC,GAAK,IAGjE,CACAjB,EAAiB,YAAc,cACxB,IAAIoD,GAAcpD,EACzB,GAAeA,C,0HC9Sf,EAAe,CACb,YAAa,IACb,KAAM,CACJ,YAAa,CACX,KAAM,uCACN,MAAO,qBACP,QAAS,iCACT,SAAU,gCACZ,CACF,EACA,UAAW,CACT,OAAQ,2BACR,MAAO,gEACP,OAAQ,iCACR,UAAW,iCACX,OAAQ,iCACR,iBAAkB,kFAClB,kBAAmB,uFACrB,EACA,MAAO,CACL,MAAO,qBACP,SAAU,2BACV,KAAM,0BACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,eACP,KAAM,gCACR,CACF,EACA,aAAc,CACZ,QAAS,6EACT,SAAU,6EACV,MAAO,4EACP,eAAgB,6EAChB,gBAAiB,6EACjB,aAAc,4EACd,MAAO,gEACP,cAAe,8FACf,cAAe,yDACf,WAAY,mFACZ,eAAgB,qIAChB,OAAQ,iCACR,QAAS,6CACT,eAAgB,6CAChB,cAAe,2BACf,cAAe,qBACf,aAAc,0BAChB,EACA,UAAW,CACT,KAAM,uCACN,KAAM,uCACN,OAAQ,0BACV,EACA,UAAW,CACT,WAAY,qEACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,2BACN,OAAQ,gEACR,OAAQ,qBACR,IAAK,2GACP,CACF,EACA,OAAQ,CACN,KAAM,iCACN,MAAO,oBACT,CACF,ECtEA,EAAe,CACb,YAAa,SACb,KAAM,CACJ,YAAa,CACX,KAAM,SACN,MAAO,UACP,QAAS,YACT,SAAU,UACZ,CACF,EACA,UAAW,CACT,OAAQ,SACR,MAAO,UACP,OAAQ,SACR,UAAW,WACX,OAAQ,gBACR,iBAAkB,qBAClB,kBAAmB,mBACrB,EACA,MAAO,CACL,MAAO,UACP,SAAU,cACV,KAAM,SACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,KACP,KAAM,UACR,CACF,EACA,aAAc,CACZ,QAAS,mBACT,SAAU,iBACV,MAAO,YACP,eAAgB,qBAChB,gBAAiB,mBACjB,aAAc,cACd,MAAO,YACP,cAAe,kBACf,cAAe,kBACf,WAAY,oBACZ,eAAgB,2BAChB,OAAQ,YACR,QAAS,WACT,eAAgB,cAChB,cAAe,QACf,cAAe,WACf,aAAc,UAChB,EACA,UAAW,CACT,KAAM,aACN,KAAM,WACN,OAAQ,WACV,EACA,UAAW,CACT,WAAY,QACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,UACN,OAAQ,gBACR,OAAQ,WACR,IAAK,0BACP,CACF,EACA,OAAQ,CACN,KAAM,QACN,MAAO,QACT,CACF,ECtEA,EAAe,CACb,YAAa,UACb,eAAgB,6BAChB,aAAc,mCACd,KAAM,CACJ,YAAa,CACX,KAAM,SACN,MAAO,UACP,QAAS,WACT,SAAU,cACZ,CACF,EACA,UAAW,CACT,OAAQ,QACR,MAAO,YACP,OAAQ,UACR,UAAW,oBACX,OAAQ,eACR,iBAAkB,oBAClB,kBAAmB,mBACrB,EACA,MAAO,CACL,MAAO,UACP,SAAU,aACV,KAAM,cACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,IACP,KAAM,cACR,CACF,EACA,aAAc,CACZ,QAAS,uBACT,SAAU,wBACV,MAAO,WACP,eAAgB,qBAChB,gBAAiB,sBACjB,aAAc,aACd,MAAO,YACP,cAAe,4BACf,cAAe,eACf,WAAY,oBACZ,eAAgB,gCAChB,OAAQ,UACR,QAAS,UACT,eAAgB,gBAChB,cAAe,qBACf,cAAe,kBACf,aAAc,cAChB,EACA,UAAW,CACT,KAAM,gBACN,KAAM,oBACN,OAAQ,eACV,EACA,UAAW,CACT,WAAY,sBACd,EACA,cAAe,CACb,kBAAmB,wCACnB,OAAQ,CACN,KAAM,cACN,OAAQ,cACR,OAAQ,UACR,IAAK,+BACP,CACF,EACA,OAAQ,CACN,KAAM,kBACN,MAAO,gBACT,CACF,ECzEA,EAAe,CACb,YAAa,SACb,KAAM,CACJ,YAAa,CACX,KAAM,OACN,MAAO,kBACP,QAAS,gBACT,SAAU,aACZ,CACF,EACA,UAAW,CACT,OAAQ,SACR,MAAO,kBACP,OAAQ,WACR,UAAW,aACX,OAAQ,gBACR,iBAAkB,iBAClB,kBAAmB,oBACrB,EACA,MAAO,CACL,MAAO,kBACP,SAAU,gBACV,KAAM,SACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,MACP,KAAM,cACR,CACF,EACA,aAAc,CACZ,QAAS,iBACT,SAAU,kBACV,MAAO,mBACP,eAAgB,gBAChB,gBAAiB,iBACjB,aAAc,gBACd,MAAO,kBACP,cAAe,oBACf,cAAe,gBACf,WAAY,WACZ,eAAgB,qBAChB,OAAQ,gBACR,QAAS,UACT,eAAgB,WAChB,cAAe,eACf,cAAe,SACf,aAAc,SAChB,EACA,UAAW,CACT,KAAM,SACN,KAAM,YACN,OAAQ,gBACV,EACA,UAAW,CACT,WAAY,UACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,SACN,OAAQ,YACR,OAAQ,aACR,IAAK,gCACP,CACF,EACA,OAAQ,CACN,KAAM,QACN,MAAO,cACT,CACF,ECtEA,EAAe,CACb,YAAa,OACb,KAAM,CACJ,YAAa,CACX,KAAM,OACN,MAAO,QACP,QAAS,UACT,SAAU,OACZ,CACF,EACA,UAAW,CACT,OAAQ,QACR,MAAO,QACP,OAAQ,SACR,UAAW,SACX,OAAQ,WACR,iBAAkB,eAClB,kBAAmB,eACrB,EACA,MAAO,CACL,MAAO,QACP,SAAU,WACV,KAAM,MACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,KACP,KAAM,OACR,CACF,EACA,aAAc,CACZ,QAAS,cACT,SAAU,eACV,MAAO,WACP,eAAgB,oBAChB,gBAAiB,qBACjB,aAAc,YACd,MAAO,QACP,cAAe,iBACf,cAAe,iBACf,WAAY,cACZ,eAAgB,mBAChB,OAAQ,UACR,QAAS,UACT,eAAgB,UAChB,cAAe,SACf,cAAe,SACf,aAAc,SAChB,EACA,UAAW,CACT,KAAM,OACN,KAAM,WACN,OAAQ,QACV,EACA,UAAW,CACT,WAAY,OACd,EACA,cAAe,CACb,kBAAmB,8BACnB,eAAgB,6BAChB,OAAQ,CACN,KAAM,OACN,OAAQ,SACR,OAAQ,SACR,IAAK,mBACP,CACF,EACA,OAAQ,CACN,KAAM,OACN,MAAO,OACT,CACF,ECxEA,EAAe,CACb,YAAa,IACb,eAAgB,mBAChB,aAAc,iBACd,KAAM,CACJ,YAAa,CACX,KAAM,OACN,MAAO,QACP,QAAS,UACT,SAAU,OACZ,CACF,EACA,UAAW,CACT,OAAQ,QACR,MAAO,QACP,OAAQ,SACR,UAAW,SACX,OAAQ,WACR,iBAAkB,eAClB,kBAAmB,eACrB,EACA,MAAO,CACL,MAAO,QACP,SAAU,WACV,KAAM,MACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,KACP,KAAM,OACR,CACF,EACA,aAAc,CACZ,QAAS,cACT,SAAU,eACV,MAAO,WACP,eAAgB,oBAChB,gBAAiB,qBACjB,aAAc,YACd,MAAO,QACP,cAAe,iBACf,cAAe,iBACf,WAAY,cACZ,eAAgB,mBAChB,OAAQ,UACR,QAAS,UACT,eAAgB,UAChB,cAAe,SACf,cAAe,SACf,aAAc,SAChB,EACA,UAAW,CACT,KAAM,OACN,KAAM,WACN,OAAQ,QACV,EACA,UAAW,CACT,WAAY,OACd,EACA,cAAe,CACb,kBAAmB,8BACnB,eAAgB,6BAChB,OAAQ,CACN,KAAM,OACN,OAAQ,SACR,OAAQ,SACR,IAAK,mBACP,CACF,EACA,OAAQ,CACN,KAAM,OACN,MAAO,OACT,CACF,EC1EA,EAAe,CACb,YAAa,SACb,KAAM,CACJ,YAAa,CACX,KAAM,SACN,MAAO,UACP,QAAS,YACT,SAAU,cACZ,CACF,EACA,UAAW,CACT,OAAQ,SACR,MAAO,UACP,OAAQ,SACR,UAAW,WACX,OAAQ,WACR,iBAAkB,gBAClB,kBAAmB,kBACrB,EACA,MAAO,CACL,MAAO,UACP,SAAU,eACV,KAAM,UACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,KACP,KAAM,cACR,CACF,EACA,aAAc,CACZ,QAAS,qBACT,SAAU,mBACV,MAAO,UACP,eAAgB,wBAChB,gBAAiB,sBACjB,aAAc,YACd,MAAO,YACP,cAAe,kBACf,cAAe,mBACf,WAAY,oBACZ,eAAgB,0BAChB,OAAQ,YACR,QAAS,WACT,eAAgB,cAChB,cAAe,QACf,cAAe,QACf,aAAc,UAChB,EACA,UAAW,CACT,KAAM,YACN,KAAM,WACN,OAAQ,WACV,EACA,UAAW,CACT,WAAY,QACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,UACN,OAAQ,YACR,OAAQ,SACR,IAAK,6BACP,CACF,EACA,OAAQ,CACN,KAAM,QACN,MAAO,QACT,CACF,ECtEA,EAAe,CACb,YAAa,iCACb,KAAM,CACJ,YAAa,CACX,KAAM,iCACN,MAAO,8CACP,QAAS,iCACT,SAAU,0BACZ,CACF,EACA,UAAW,CACT,OAAQ,iCACR,MAAO,mDACP,OAAQ,iCACR,UAAW,gEACX,OAAQ,0DACR,iBAAkB,oDAClB,kBAAmB,+DACrB,EACA,MAAO,CACL,MAAO,8CACP,SAAU,uCACV,KAAM,0BACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,eACP,KAAM,0BACR,CACF,EACA,aAAc,CACZ,QAAS,2DACT,SAAU,uEACV,MAAO,0DACP,eAAgB,wEAChB,gBAAiB,oFACjB,aAAc,iCACd,MAAO,mDACP,cAAe,oDACf,cAAe,6CACf,WAAY,oDACZ,eAAgB,mHAChB,OAAQ,oDACR,QAAS,iCACT,eAAgB,wCAChB,cAAe,2BACf,cAAe,iCACf,aAAc,0BAChB,EACA,UAAW,CACT,KAAM,2BACN,KAAM,2BACN,OAAQ,gCACV,EACA,UAAW,CACT,WAAY,0BACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,iCACN,OAAQ,qBACR,OAAQ,qBACR,IAAK,wHACP,CACF,EACA,OAAQ,CACN,KAAM,qBACN,MAAO,gCACT,CACF,ECtEA,EAAe,CACb,YAAa,SACb,KAAM,CACJ,YAAa,CACX,KAAM,OACN,MAAO,UACP,QAAS,YACT,SAAU,OACZ,CACF,EACA,UAAW,CACT,OAAQ,aACR,MAAO,mBACP,OAAQ,UACR,UAAW,WACX,OAAQ,aACR,iBAAkB,oBAClB,kBAAmB,4BACrB,EACA,MAAO,CACL,MAAO,mBACP,SAAU,oBACV,KAAM,MACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,MACP,KAAM,gBACR,CACF,EACA,aAAc,CACZ,QAAS,0BACT,SAAU,0BACV,MAAO,kBACP,eAAgB,oBAChB,gBAAiB,oBACjB,aAAc,cACd,MAAO,mBACP,cAAe,oBACf,cAAe,cACf,WAAY,iBACZ,eAAgB,yBAChB,OAAQ,aACR,QAAS,aACT,eAAgB,gBAChB,cAAe,SACf,cAAe,UACf,aAAc,UAChB,EACA,UAAW,CACT,KAAM,WACN,KAAM,mBACN,OAAQ,WACV,EACA,UAAW,CACT,WAAY,cACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,cACN,OAAQ,UACR,OAAQ,YACR,IAAK,iCACP,CACF,EACA,OAAQ,CACN,KAAM,SACN,MAAO,SACT,CACF,ECtEA,EAAe,CACb,YAAa,SACb,eAAgB,2DAChB,aAAc,iEACd,KAAM,CACJ,YAAa,CACX,KAAM,2BACN,MAAO,qBACP,QAAS,iCACT,SAAU,sCACZ,CACF,EACA,UAAW,CACT,OAAQ,iCACR,MAAO,iCACP,OAAQ,qBACR,UAAW,2BACX,OAAQ,2BACR,iBAAkB,8CAClB,kBAAmB,uCACrB,EACA,MAAO,CACL,MAAO,qBACP,SAAU,2BACV,KAAM,0BACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,2BACP,KAAM,sCACR,CACF,EACA,aAAc,CACZ,QAAS,0DACT,SAAU,0DACV,MAAO,8CACP,eAAgB,gEAChB,gBAAiB,gEACjB,aAAc,8CACd,MAAO,iCACP,cAAe,sEACf,cAAe,uCACf,WAAY,wCACZ,eAAgB,2DAChB,OAAQ,2BACR,QAAS,mDACT,eAAgB,0DAChB,cAAe,2BACf,cAAe,uCACf,aAAc,oBAChB,EACA,UAAW,CACT,KAAM,qBACN,KAAM,2BACN,OAAQ,0BACV,EACA,UAAW,CACT,WAAY,gCACd,EACA,cAAe,CACb,kBAAmB,mHACnB,OAAQ,CACN,KAAM,2BACN,OAAQ,iCACR,OAAQ,iCACR,IAAK,wFACP,CACF,EACA,OAAQ,CACN,KAAM,qBACN,MAAO,0BACT,CACF,ECzEA,EAAe,CACb,YAAa,KACb,KAAM,CACJ,YAAa,CACX,KAAM,YACN,MAAO,cACP,QAAS,UACT,SAAU,QACZ,CACF,EACA,UAAW,CACT,OAAQ,gBACR,MAAO,eACP,OAAQ,UACR,UAAW,cACX,OAAQ,QACR,iBAAkB,UAClB,kBAAmB,WACrB,EACA,MAAO,CACL,MAAO,cACP,SAAU,UACV,KAAM,QACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,KACP,KAAM,QACR,CACF,EACA,aAAc,CACZ,QAAS,sBACT,SAAU,qBACV,MAAO,sBACP,eAAgB,kBAChB,gBAAiB,iBACjB,aAAc,iBACd,MAAO,YACP,cAAe,iBACf,cAAe,WACf,WAAY,cACZ,eAAgB,8BAChB,OAAQ,sBACR,QAAS,gBACT,eAAgB,SAChB,cAAe,SACf,cAAe,UACf,aAAc,MAChB,EACA,UAAW,CACT,KAAM,gBACN,KAAM,YACN,OAAQ,MACV,EACA,UAAW,CACT,WAAY,SACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,SACN,OAAQ,WACR,OAAQ,cACR,IAAK,sBACP,CACF,EACA,OAAQ,CACN,KAAM,SACN,MAAO,SACT,CACF,ECtEA,EAAe,CACb,YAAa,KACb,KAAM,CACJ,YAAa,CACX,KAAM,QACN,MAAO,QACP,QAAS,aACT,SAAU,MACZ,CACF,EACA,UAAW,CACT,OAAQ,OACR,MAAO,aACP,OAAQ,QACR,UAAW,gBACX,OAAQ,eACR,iBAAkB,qBAClB,kBAAmB,OACrB,EACA,MAAO,CACL,MAAO,QACP,SAAU,UACV,KAAM,OACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,OACP,KAAM,OACR,CACF,EACA,aAAc,CACZ,QAAS,WACT,SAAU,YACV,MAAO,gBACP,eAAgB,YAChB,gBAAiB,aACjB,aAAc,cACd,MAAO,aACP,cAAe,iBACf,cAAe,aACf,WAAY,cACZ,eAAgB,qBAChB,OAAQ,aACR,QAAS,YACT,eAAgB,UAChB,cAAe,cACf,cAAe,SACf,aAAc,OAChB,EACA,UAAW,CACT,KAAM,cACN,KAAM,aACN,OAAQ,SACV,EACA,UAAW,CACT,WAAY,OACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,SACN,OAAQ,QACR,OAAQ,QACR,IAAK,sBACP,CACF,EACA,OAAQ,CACN,KAAM,OACN,MAAO,OACT,CACF,ECtEA,EAAe,CACb,YAAa,SACb,KAAM,CACJ,YAAa,CACX,KAAM,SACN,MAAO,UACP,QAAS,WACT,SAAU,UACZ,CACF,EACA,UAAW,CACT,OAAQ,SACR,MAAO,UACP,OAAQ,QACR,UAAW,UACX,OAAQ,UACR,iBAAkB,SAClB,kBAAmB,WACrB,EACA,MAAO,CACL,MAAO,UACP,SAAU,cACV,KAAM,UACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,KACP,KAAM,UACR,CACF,EACA,aAAc,CACZ,QAAS,mBACT,SAAU,iBACV,MAAO,uBACP,eAAgB,qBAChB,gBAAiB,mBACjB,aAAc,cACd,MAAO,aACP,cAAe,uBACf,cAAe,eACf,WAAY,6BACZ,eAAgB,qCAChB,OAAQ,WACR,QAAS,oBACT,eAAgB,cAChB,cAAe,SACf,cAAe,QACf,aAAc,UAChB,EACA,UAAW,CACT,KAAM,aACN,KAAM,aACN,OAAQ,SACV,EACA,UAAW,CACT,WAAY,QACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,QACN,OAAQ,UACR,OAAQ,SACR,IAAK,mBACP,CACF,EACA,OAAQ,CACN,KAAM,OACN,MAAO,QACT,CACF,ECtEA,EAAe,CACb,YAAa,OACb,KAAM,CACJ,YAAa,CACX,KAAM,eACN,MAAO,qBACP,QAAS,eACT,SAAU,0BACZ,CACF,EACA,UAAW,CACT,OAAQ,eACR,MAAO,2BACP,OAAQ,eACR,UAAW,eACX,OAAQ,eACR,iBAAkB,mDAClB,kBAAmB,kDACrB,EACA,MAAO,CACL,MAAO,qBACP,SAAU,2BACV,KAAM,0BACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,2BACP,MAAO,gBACP,KAAM,GACR,CACF,EACA,aAAc,CACZ,QAAS,2BACT,SAAU,2BACV,MAAO,iCACP,eAAgB,yDAChB,gBAAiB,yDACjB,aAAc,yDACd,MAAO,2BACP,cAAe,qBACf,cAAe,iCACf,WAAY,6CACZ,eAAgB,eAChB,OAAQ,eACR,QAAS,eACT,eAAgB,iCAChB,cAAe,SACf,cAAe,SACf,aAAc,QAChB,EACA,UAAW,CACT,KAAM,eACN,KAAM,eACN,OAAQ,cACV,EACA,UAAW,CACT,WAAY,0BACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,eACN,OAAQ,iCACR,OAAQ,eACR,IAAK,cACP,CACF,EACA,OAAQ,CACN,KAAM,eACN,MAAO,oBACT,CACF,ECtEA,EAAe,CACb,YAAa,SACb,KAAM,CACJ,YAAa,CACX,KAAM,qBACN,MAAO,qBACP,QAAS,eACT,SAAU,cACZ,CACF,EACA,UAAW,CACT,OAAQ,eACR,MAAO,qBACP,OAAQ,eACR,UAAW,eACX,OAAQ,eACR,iBAAkB,wCAClB,kBAAmB,uCACrB,EACA,MAAO,CACL,MAAO,eACP,SAAU,eACV,KAAM,QACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,WACP,KAAM,QACR,CACF,EACA,aAAc,CACZ,QAAS,kCACT,SAAU,wCACV,MAAO,sBACP,eAAgB,wCAChB,gBAAiB,8CACjB,aAAc,qBACd,MAAO,qBACP,cAAe,4BACf,cAAe,eACf,WAAY,4BACZ,eAAgB,yCAChB,OAAQ,4BACR,QAAS,eACT,eAAgB,eAChB,cAAe,4BACf,cAAe,4BACf,aAAc,2BAChB,EACA,UAAW,CACT,KAAM,eACN,KAAM,eACN,OAAQ,cACV,EACA,UAAW,CACT,WAAY,oBACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,eACN,OAAQ,eACR,OAAQ,eACR,IAAK,wCACP,CACF,EACA,OAAQ,CACN,KAAM,SACN,MAAO,qBACT,CACF,ECtEA,EAAe,CACb,YAAa,SACb,KAAM,CACJ,YAAa,CACX,KAAM,2BACN,MAAO,mDACP,QAAS,uFACT,SAAU,4CACZ,CACF,EACA,UAAW,CACT,OAAQ,2BACR,MAAO,mDACP,OAAQ,uCACR,UAAW,mDACX,OAAQ,uCACR,iBAAkB,mFAClB,kBAAmB,kFACrB,EACA,MAAO,CACL,MAAO,mDACP,SAAU,+DACV,KAAM,0BACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,2BACP,KAAM,oBACR,CACF,EACA,aAAc,CACZ,QAAS,yFACT,SAAU,qGACV,MAAO,yDACP,eAAgB,8FAChB,gBAAiB,0GACjB,aAAc,uFACd,MAAO,mDACP,cAAe,oGACf,cAAe,mDACf,WAAY,wFACZ,eAAgB,2GAChB,OAAQ,mDACR,QAAS,uCACT,eAAgB,uCAChB,cAAe,qBACf,cAAe,2BACf,aAAc,gCAChB,EACA,UAAW,CACT,KAAM,uCACN,KAAM,iCACN,OAAQ,4CACV,EACA,UAAW,CACT,WAAY,4CACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,mDACN,OAAQ,uCACR,OAAQ,uCACR,IAAK,mDACP,CACF,EACA,OAAQ,CACN,KAAM,2BACN,MAAO,0BACT,CACF,ECtEA,EAAe,CACb,YAAa,KACb,KAAM,CACJ,YAAa,CACX,KAAM,eACN,MAAO,QACP,QAAS,cACT,SAAU,MACZ,CACF,EACA,UAAW,CACT,OAAQ,OACR,MAAO,oBACP,OAAQ,SACR,UAAW,UACX,OAAQ,SACR,iBAAkB,aAClB,kBAAmB,YACrB,EACA,MAAO,CACL,MAAO,QACP,SAAU,UACV,KAAM,MACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,WACP,KAAM,MACR,CACF,EACA,aAAc,CACZ,QAAS,cACT,SAAU,eACV,MAAO,YACP,eAAgB,gBAChB,gBAAiB,iBACjB,aAAc,cACd,MAAO,oBACP,cAAe,QACf,cAAe,WACf,WAAY,cACZ,eAAgB,qBAChB,OAAQ,cACR,QAAS,UACT,eAAgB,QAChB,cAAe,QACf,cAAe,SACf,aAAc,OAChB,EACA,UAAW,CACT,KAAM,aACN,KAAM,aACN,OAAQ,SACV,EACA,UAAW,CACT,WAAY,WACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,SACN,OAAQ,cACR,OAAQ,eACR,IAAK,mBACP,CACF,EACA,OAAQ,CACN,KAAM,UACN,MAAO,OACT,CACF,ECtEA,EAAe,CACb,YAAa,UACb,KAAM,CACJ,YAAa,CACX,KAAM,cACN,MAAO,oBACP,QAAS,iBACT,SAAU,iBACZ,CACF,EACA,UAAW,CACT,OAAQ,SACR,MAAO,QACP,OAAQ,iBACR,UAAW,oBACX,OAAQ,mBACR,iBAAkB,yBAClB,kBAAmB,yBACrB,EACA,MAAO,CACL,MAAO,oBACP,SAAU,UACV,KAAM,MACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,IACP,KAAM,WACR,CACF,EACA,aAAc,CACZ,QAAS,oBACT,SAAU,qBACV,MAAO,UACP,eAAgB,0BAChB,gBAAiB,2BACjB,aAAc,oBACd,MAAO,QACP,cAAe,2BACf,cAAe,aACf,WAAY,mBACZ,eAAgB,2BAChB,OAAQ,oBACR,QAAS,cACT,eAAgB,WAChB,cAAe,UACf,cAAe,SACf,aAAc,YAChB,EACA,UAAW,CACT,KAAM,SACN,KAAM,YACN,OAAQ,gBACV,EACA,UAAW,CACT,WAAY,kBACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,eACN,OAAQ,SACR,OAAQ,mBACR,IAAK,0BACP,CACF,EACA,OAAQ,CACN,KAAM,gBACN,MAAO,cACT,CACF,ECtEA,EAAe,CACb,YAAa,KACb,KAAM,CACJ,YAAa,CACX,KAAM,OACN,MAAO,SACP,QAAS,YACT,SAAU,OACZ,CACF,EACA,UAAW,CACT,OAAQ,UACR,MAAO,SACP,OAAQ,YACR,UAAW,WACX,OAAQ,WACR,iBAAkB,mBAClB,kBAAmB,qBACrB,EACA,MAAO,CACL,MAAO,SACP,SAAU,iBACV,KAAM,SACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,KACP,KAAM,OACR,CACF,EACA,aAAc,CACZ,QAAS,sBACT,SAAU,qBACV,MAAO,YACP,eAAgB,uBAChB,gBAAiB,sBACjB,aAAc,gBACd,MAAO,SACP,cAAe,iBACf,cAAe,sBACf,WAAY,aACZ,eAAgB,qBAChB,OAAQ,YACR,QAAS,YACT,eAAgB,YAChB,cAAe,QACf,cAAe,WACf,aAAc,UAChB,EACA,UAAW,CACT,KAAM,aACN,KAAM,WACN,OAAQ,QACV,EACA,UAAW,CACT,WAAY,QACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,SACN,OAAQ,WACR,OAAQ,SACR,IAAK,8BACP,CACF,EACA,OAAQ,CACN,KAAM,QACN,MAAO,QACT,CACF,ECtEA,EAAe,CACb,YAAa,SACb,KAAM,CACJ,YAAa,CACX,KAAM,qBACN,MAAO,mDACP,QAAS,eACT,SAAU,4CACZ,CACF,EACA,UAAW,CACT,OAAQ,iCACR,MAAO,iCACP,OAAQ,yDACR,UAAW,+DACX,OAAQ,mDACR,iBAAkB,8FAClB,kBAAmB,mGACrB,EACA,MAAO,CACL,MAAO,mDACP,SAAU,6CACV,KAAM,wDACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,eACP,KAAM,wDACR,CACF,EACA,aAAc,CACZ,QAAS,wFACT,SAAU,8FACV,MAAO,yDACP,eAAgB,8FAChB,gBAAiB,oGACjB,aAAc,4EACd,MAAO,iCACP,cAAe,gHACf,cAAe,yDACf,WAAY,sEACZ,eAAgB,wKAChB,OAAQ,mDACR,QAAS,uCACT,eAAgB,sEAChB,cAAe,6CACf,cAAe,6CACf,aAAc,sCAChB,EACA,UAAW,CACT,KAAM,yDACN,KAAM,+DACN,OAAQ,wDACV,EACA,UAAW,CACT,WAAY,0BACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,yDACN,OAAQ,mDACR,OAAQ,6CACR,IAAK,0GACP,CACF,EACA,OAAQ,CACN,KAAM,2LACN,MAAO,oDACT,CACF,ECtEA,EAAe,CACb,YAAa,SACb,eAAgB,iCAChB,aAAc,6BACd,KAAM,CACJ,YAAa,CACX,KAAM,OACN,MAAO,qBACP,QAAS,gBACT,SAAU,cACZ,CACF,EACA,UAAW,CACT,OAAQ,gBACR,MAAO,iBACP,OAAQ,eACR,UAAW,gBACX,OAAQ,cACR,iBAAkB,qBAClB,kBAAmB,oBACrB,EACA,MAAO,CACL,MAAO,qBACP,SAAU,aACV,KAAM,cACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,IACP,KAAM,eACR,CACF,EACA,aAAc,CACZ,QAAS,6BACT,SAAU,yBACV,MAAO,cACP,eAAgB,2BAChB,gBAAiB,uBACjB,aAAc,gBACd,MAAO,iBACP,cAAe,0BACf,cAAe,aACf,WAAY,oBACZ,eAAgB,sCAChB,OAAQ,eACR,QAAS,UACT,eAAgB,gBAChB,cAAe,sBACf,cAAe,aACf,aAAc,cAChB,EACA,UAAW,CACT,KAAM,mBACN,KAAM,uBACN,OAAQ,eACV,EACA,UAAW,CACT,WAAY,sBACd,EACA,cAAe,CACb,kBAAmB,6CACnB,OAAQ,CACN,KAAM,mBACN,OAAQ,mBACR,OAAQ,oBACR,IAAK,8BACP,CACF,EACA,OAAQ,CACN,KAAM,eACN,MAAO,cACT,CACF,ECzEA,GAAe,CACb,YAAa,MACb,KAAM,CACJ,YAAa,CACX,KAAM,YACN,MAAO,cACP,QAAS,UACT,SAAU,QACZ,CACF,EACA,UAAW,CACT,OAAQ,eACR,MAAO,UACP,OAAQ,eACR,UAAW,eACX,OAAQ,QACR,iBAAkB,iBAClB,kBAAmB,kBACrB,EACA,MAAO,CACL,MAAO,cACP,SAAU,WACV,KAAM,QACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,KACP,KAAM,QACR,CACF,EACA,aAAc,CACZ,QAAS,mBACT,SAAU,oBACV,MAAO,qBACP,eAAgB,iBAChB,gBAAiB,kBACjB,aAAc,iBACd,MAAO,UACP,cAAe,gBACf,cAAe,mBACf,WAAY,YACZ,eAAgB,oBAChB,OAAQ,cACR,QAAS,gBACT,eAAgB,gBAChB,cAAe,YACf,cAAe,UACf,aAAc,WAChB,EACA,UAAW,CACT,KAAM,QACN,KAAM,QACN,OAAQ,QACV,EACA,UAAW,CACT,WAAY,YACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,eACN,OAAQ,eACR,OAAQ,cACR,IAAK,sBACP,CACF,EACA,OAAQ,CACN,KAAM,mDACN,MAAO,wDACT,CACF,ECtEA,EAAe,CACb,YAAa,SACb,eAAgB,qEAChB,aAAc,6FACd,KAAM,CACJ,YAAa,CACX,KAAM,6CACN,MAAO,uCACP,QAAS,uCACT,SAAU,sCACZ,CACF,EACA,UAAW,CACT,OAAQ,uCACR,MAAO,uCACP,OAAQ,qBACR,UAAW,2BACX,OAAQ,2BACR,iBAAkB,yDAClB,kBAAmB,wDACrB,EACA,MAAO,CACL,MAAO,uCACP,SAAU,yDACV,KAAM,sCACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,qBACP,KAAM,sCACR,CACF,EACA,aAAc,CACZ,QAAS,mGACT,SAAU,6FACV,MAAO,2EACP,eAAgB,iFAChB,gBAAiB,2EACjB,aAAc,mDACd,MAAO,uCACP,cAAe,uFACf,cAAe,+DACf,WAAY,uCACZ,eAAgB,2HAChB,OAAQ,uCACR,QAAS,qEACT,eAAgB,qEAChB,cAAe,2EACf,cAAe,2BACf,aAAc,kDAChB,EACA,UAAW,CACT,KAAM,iCACN,KAAM,mDACN,OAAQ,gCACV,EACA,UAAW,CACT,WAAY,oEACd,EACA,cAAe,CACb,kBAAmB,mMACnB,OAAQ,CACN,KAAM,uCACN,OAAQ,uCACR,OAAQ,eACR,IAAK,wGACP,CACF,EACA,OAAQ,CACN,KAAM,2BACN,MAAO,oBACT,CACF,ECzEA,EAAe,CACb,YAAa,SACb,KAAM,CACJ,YAAa,CACX,KAAM,aACN,MAAO,UACP,QAAS,SACT,SAAU,gBACZ,CACF,EACA,UAAW,CACT,OAAQ,WACR,MAAO,oBACP,OAAQ,YACR,UAAW,aACX,OAAQ,UACR,iBAAkB,2CAClB,kBAAmB,6CACrB,EACA,MAAO,CACL,MAAO,UACP,SAAU,YACV,KAAM,aACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,SACP,KAAM,aACR,CACF,EACA,aAAc,CACZ,QAAS,eACT,SAAU,oBACV,MAAO,0BACP,eAAgB,kBAChB,gBAAiB,uBACjB,aAAc,eACd,MAAO,oBACP,cAAe,6BACf,cAAe,UACf,WAAY,YACZ,eAAgB,2BAChB,OAAQ,SACR,QAAS,qBACT,eAAgB,kBAChB,cAAe,cACf,cAAe,OACf,aAAc,gBAChB,EACA,UAAW,CACT,KAAM,gBACN,KAAM,YACN,OAAQ,WACV,EACA,UAAW,CACT,WAAY,gBACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,SACN,OAAQ,YACR,OAAQ,MACR,IAAK,4BACP,CACF,EACA,OAAQ,CACN,KAAM,eACN,MAAO,UACT,CACF,ECtEA,GAAe,CACb,YAAa,SACb,eAAgB,kFAChB,aAAc,8FACd,KAAM,CACJ,YAAa,CACX,KAAM,eACN,MAAO,mDACP,QAAS,eACT,SAAU,4CACZ,CACF,EACA,UAAW,CACT,OAAQ,iCACR,MAAO,mDACP,OAAQ,+DACR,UAAW,+DACX,OAAQ,mDACR,iBAAkB,8FAClB,kBAAmB,6FACrB,EACA,MAAO,CACL,MAAO,mDACP,SAAU,uCACV,KAAM,wDACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,SACP,KAAM,wDACR,CACF,EACA,aAAc,CACZ,QAAS,wFACT,SAAU,8FACV,MAAO,+DACP,eAAgB,8FAChB,gBAAiB,oGACjB,aAAc,4EACd,MAAO,6CACP,cAAe,4HACf,cAAe,2EACf,WAAY,gHACZ,eAAgB,kKAChB,OAAQ,6CACR,QAAS,uCACT,eAAgB,8FAChB,cAAe,6CACf,cAAe,mDACf,aAAc,4CAChB,EACA,UAAW,CACT,KAAM,yDACN,KAAM,+DACN,OAAQ,wDACV,EACA,UAAW,CACT,WAAY,0BACd,EACA,cAAe,CACb,kBAAmB,2QACnB,OAAQ,CACN,KAAM,mDACN,OAAQ,yDACR,OAAQ,mDACR,IAAK,qEACP,CACF,EACA,OAAQ,CACN,KAAM,mDACN,MAAO,4CACT,CACF,ECzEA,GAAe,CACb,YAAa,MACb,KAAM,CACJ,YAAa,CACX,KAAM,OACN,MAAO,WACP,QAAS,KACT,SAAU,cACZ,CACF,EACA,UAAW,CACT,OAAQ,WACR,MAAO,gBACP,OAAQ,WACR,UAAW,eACX,OAAQ,eACR,iBAAkB,oBAClB,kBAAmB,kBACrB,EACA,MAAO,CACL,MAAO,WACP,SAAU,YACV,KAAM,YACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,MACP,KAAM,YACR,CACF,EACA,aAAc,CACZ,QAAS,oBACT,SAAU,yBACV,MAAO,6BACP,eAAgB,sBAChB,gBAAiB,2BACjB,aAAc,gBACd,MAAO,gBACP,cAAe,0BACf,cAAe,aACf,WAAY,oBACZ,eAAgB,+BAChB,OAAQ,YACR,QAAS,eACT,eAAgB,WAChB,cAAe,QACf,cAAe,gBACf,aAAc,QAChB,EACA,UAAW,CACT,KAAM,UACN,KAAM,UACN,OAAQ,UACV,EACA,UAAW,CACT,WAAY,QACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,UACN,OAAQ,eACR,OAAQ,iBACR,IAAK,yCACP,CACF,EACA,OAAQ,CACN,KAAM,SACN,MAAO,QACT,CACF,ECtEA,GAAe,CACb,YAAa,SACb,KAAM,CACJ,YAAa,CACX,KAAM,sBACN,MAAO,QACP,QAAS,mBACT,SAAU,UACZ,CACF,EACA,UAAW,CACT,OAAQ,mBACR,MAAO,kBACP,OAAQ,mBACR,UAAW,oBACX,OAAQ,eACR,iBAAkB,8BAClB,kBAAmB,uBACrB,EACA,MAAO,CACL,MAAO,SACP,SAAU,uBACV,KAAM,UACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,IACP,MAAO,UACP,KAAM,kBACR,CACF,EACA,aAAc,CACZ,QAAS,eACT,SAAU,iBACV,MAAO,eACP,eAAgB,iCAChB,gBAAiB,mCACjB,aAAc,mCACd,MAAO,kBACP,cAAe,8BACf,cAAe,mBACf,WAAY,+CACZ,eAAgB,wDAChB,OAAQ,kBACR,QAAS,2CACT,eAAgB,0BAChB,cAAe,0BACf,cAAe,gBACf,aAAc,WAChB,EACA,UAAW,CACT,KAAM,MACN,KAAM,kBACN,OAAQ,kBACV,EACA,UAAW,CACT,WAAY,0BACd,EACA,cAAe,CACb,OAAQ,CACN,KAAM,WACN,OAAQ,WACR,OAAQ,SACR,IAAK,4CACP,CACF,EACA,OAAQ,CACN,KAAM,UACN,MAAO,cACT,CACF,ECtEA,GAAe,CACb,YAAa,OACb,eAAgB,2BAChB,aAAc,2BACd,KAAM,CACJ,YAAa,CACX,KAAM,2BACN,MAAO,eACP,QAAS,eACT,SAAU,QACZ,CACF,EACA,UAAW,CACT,OAAQ,eACR,MAAO,eACP,OAAQ,eACR,UAAW,eACX,OAAQ,eACR,iBAAkB,qBAClB,kBAAmB,oBACrB,EACA,MAAO,CACL,MAAO,2BACP,SAAU,qBACV,KAAM,QACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,SACP,MAAO,sBACP,KAAM,QACR,CACF,EACA,aAAc,CACZ,QAAS,iCACT,SAAU,iCACV,MAAO,qBACP,eAAgB,iCAChB,gBAAiB,iCACjB,aAAc,qBACd,MAAO,eACP,cAAe,qBACf,cAAe,qBACf,WAAY,eACZ,eAAgB,2BAChB,OAAQ,eACR,QAAS,eACT,eAAgB,eAChB,cAAe,eACf,cAAe,eACf,aAAc,cAChB,EACA,UAAW,CACT,KAAM,qBACN,KAAM,qBACN,OAAQ,cACV,EACA,UAAW,CACT,WAAY,cACd,EACA,cAAe,CACb,kBAAmB,mDACnB,OAAQ,CACN,KAAM,eACN,OAAQ,eACR,OAAQ,eACR,IAAK,sCACP,CACF,EACA,OAAQ,CACN,KAAM,eACN,MAAO,cACT,CACF,ECzEA,GAAe,CACb,YAAa,MACb,eAAgB,2BAChB,aAAc,2BACd,KAAM,CACJ,YAAa,CACX,KAAM,2BACN,MAAO,eACP,QAAS,eACT,SAAU,QACZ,CACF,EACA,UAAW,CACT,OAAQ,eACR,MAAO,eACP,OAAQ,eACR,UAAW,eACX,OAAQ,eACR,iBAAkB,qBAClB,kBAAmB,oBACrB,EACA,MAAO,CACL,MAAO,2BACP,SAAU,qBACV,KAAM,QACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,SACP,MAAO,sBACP,KAAM,QACR,CACF,EACA,aAAc,CACZ,QAAS,iCACT,SAAU,iCACV,MAAO,qBACP,eAAgB,iCAChB,gBAAiB,iCACjB,aAAc,qBACd,MAAO,eACP,cAAe,qBACf,cAAe,qBACf,WAAY,eACZ,eAAgB,2BAChB,OAAQ,eACR,QAAS,eACT,eAAgB,eAChB,cAAe,eACf,cAAe,eACf,aAAc,cAChB,EACA,UAAW,CACT,KAAM,qBACN,KAAM,qBACN,OAAQ,cACV,EACA,UAAW,CACT,WAAY,cACd,EACA,cAAe,CACb,kBAAmB,mDACnB,OAAQ,CACN,KAAM,eACN,OAAQ,eACR,OAAQ,eACR,IAAK,sCACP,CACF,EACA,OAAQ,CACN,KAAM,eACN,MAAO,cACT,CACF,ECzEA,GAAe,CACb,YAAa,SACb,eAAgB,uBAChB,aAAc,qBACd,KAAM,CACJ,YAAa,CACX,KAAM,eACN,MAAO,SACP,QAAS,aACT,SAAU,MACZ,CACF,EACA,UAAW,CACT,OAAQ,SACR,MAAO,WACP,OAAQ,WACR,UAAW,YACX,OAAQ,YACR,iBAAkB,UAClB,kBAAmB,WACrB,EACA,MAAO,CACL,MAAO,qBACP,SAAU,eACV,KAAM,MACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,MACP,MAAO,eACP,KAAM,OACR,CACF,EACA,aAAc,CACZ,QAAS,uBACT,SAAU,uBACV,MAAO,kBACP,eAAgB,+BAChB,gBAAiB,gCACjB,aAAc,kBACd,MAAO,WACP,cAAe,gBACf,cAAe,oBACf,WAAY,kBACZ,eAAgB,0BAChB,OAAQ,aACR,QAAS,YACT,eAAgB,UAChB,cAAe,OACf,cAAe,YACf,aAAc,SAChB,EACA,UAAW,CACT,KAAM,gBACN,KAAM,cACN,OAAQ,UACV,EACA,UAAW,CACT,WAAY,UACd,EACA,cAAe,CACb,kBAAmB,4CACnB,OAAQ,CACN,KAAM,UACN,OAAQ,YACR,OAAQ,cACR,IAAK,qBACP,CACF,EACA,OAAQ,CACN,KAAM,SACN,MAAO,SACT,CACF,ECzEA,GAAe,CACb,YAAa,MACb,eAAgB,4BAChB,aAAc,6BACd,KAAM,CACJ,YAAa,CACX,KAAM,mBACN,MAAO,wBACP,QAAS,gBACT,SAAU,UACZ,CACF,EACA,UAAW,CACT,OAAQ,aACR,MAAO,iBACP,OAAQ,UACR,UAAW,UACX,OAAQ,eACR,iBAAkB,mBAClB,kBAAmB,gBACrB,EACA,MAAO,CACL,MAAO,8BACP,SAAU,WACV,KAAM,UACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,QACP,MAAO,iBACP,KAAM,UACR,CACF,EACA,aAAc,CACZ,QAAS,6BACT,SAAU,kCACV,MAAO,UACP,eAAgB,+BAChB,gBAAiB,6BACjB,aAAc,UACd,MAAO,iBACP,cAAe,uBACf,cAAe,sBACf,WAAY,gBACZ,eAAgB,8BAChB,OAAQ,oBACR,QAAS,YACT,eAAgB,SAChB,cAAe,OACf,cAAe,QACf,aAAc,SAChB,EACA,UAAW,CACT,KAAM,qBACN,KAAM,iBACN,OAAQ,SACV,EACA,UAAW,CACT,WAAY,eACd,EACA,cAAe,CACb,kBAAmB,0CACnB,OAAQ,CACN,KAAM,gBACN,OAAQ,gBACR,OAAQ,cACR,IAAK,wBACP,CACF,EACA,OAAQ,CACN,KAAM,WACN,MAAO,YACT,CACF,ECzEA,EAAe,CACb,YAAa,MACb,eAAgB,mBAChB,aAAc,oBACd,KAAM,CACJ,YAAa,CACX,KAAM,cACN,MAAO,QACP,QAAS,cACT,SAAU,QACZ,CACF,EACA,UAAW,CACT,OAAQ,SACR,MAAO,kBACP,OAAQ,SACR,UAAW,YACX,OAAQ,eACR,iBAAkB,mBAClB,kBAAmB,qBACrB,EACA,MAAO,CACL,MAAO,aACP,SAAU,OACV,KAAM,QACR,EACA,WAAY,CACV,MAAO,CACL,MAAO,UACP,MAAO,gBACP,KAAM,QACR,CACF,EACA,aAAc,CACZ,QAAS,0BACT,SAAU,wBACV,MAAO,eACP,eAAgB,0BAChB,gBAAiB,wBACjB,aAAc,eACd,MAAO,kBACP,cAAe,gBACf,cAAe,yBACf,WAAY,eACZ,eAAgB,uBAChB,OAAQ,WACR,QAAS,YACT,eAAgB,SAChB,cAAe,SACf,cAAe,SACf,aAAc,SAChB,EACA,UAAW,CACT,KAAM,gBACN,KAAM,wBACN,OAAQ,QACV,EACA,UAAW,CACT,WAAY,UACd,EACA,cAAe,CACb,kBAAmB,8CACnB,OAAQ,CACN,KAAM,QACN,OAAQ,SACR,OAAQ,SACR,IAAK,qBACP,CACF,EACA,OAAQ,CACN,KAAM,WACN,MAAO,UACT,CACF,EClCWqD,EAAa,SAAoBpC,GAAQqC,GAAW,CAC7D,MAAO,CACL,WAAY,SAAoBC,EAAIC,EAAgB,CAClD,IAAIC,KAAM,MAAIH,GAAWC,EAAG,QAAQ,aAAc,KAAK,EAAE,MAAM,GAAG,CAAC,GAAK,GACxE,GAAIE,EAAK,OAAOA,EAChB,IAAIC,EAAWzC,GAAO,QAAQ,IAAK,GAAG,EACtC,GAAIyC,IAAa,QACf,OAAOF,EAGT,IAAIzC,EAAO4C,GAAQ,OAAO,EAC1B,OAAO5C,EAAOA,EAAK,WAAWwC,EAAIC,CAAc,EAAIA,CACtD,EACA,OAAQvC,EACV,CACF,EACI2C,EAAWP,EAAW,QAAS,CAAI,EACnCQ,EAAWR,EAAW,QAAS,CAAI,EACnCS,EAAWT,EAAW,QAAS,EAAI,EACnCU,EAAWV,EAAW,QAAS,CAAI,EACnCW,EAAWX,EAAW,QAAS,CAAI,EACnCY,EAAWZ,EAAW,QAAS,EAAI,EACnCa,EAAWb,EAAW,QAAS,CAAI,EACnCc,EAAWd,EAAW,QAAS,CAAI,EACnCe,EAAWf,EAAW,QAAS,CAAI,EACnCgB,GAAWhB,EAAW,QAAS,CAAI,EACnCiB,EAAWjB,EAAW,QAAS,CAAI,EACnCkB,EAAWlB,EAAW,QAAS,EAAI,EACnCmB,GAAWnB,EAAW,QAAS,CAAI,EACnCoB,EAAWpB,EAAW,QAAS,EAAI,EACnCqB,EAAWrB,EAAW,QAAS,CAAI,EACnCsB,EAAWtB,EAAW,QAAS,CAAI,EACnCuB,EAAWvB,EAAW,QAAS,CAAI,EACnCwB,EAAWxB,EAAW,QAAS,CAAI,EACnCyB,GAAWzB,EAAW,QAAS,CAAI,EACnC0B,GAAW1B,EAAW,QAAS,CAAI,EACnC2B,GAAW3B,EAAW,QAAS,CAAI,EACnC4B,GAAW5B,EAAW,QAAS,CAAI,EACnC6B,GAAW7B,EAAW,MAAO,CAAI,EACjC8B,GAAW9B,EAAW,QAAS,CAAI,EACnC+B,GAAW/B,EAAW,QAAS,CAAI,EACnCgC,GAAWhC,EAAW,QAAS,CAAI,EACnCiC,GAAWjC,EAAW,QAAS,CAAI,EACnCkC,GAAWlC,EAAW,QAAS,EAAI,EACnCmC,GAAWnC,EAAW,QAAS,EAAI,EACnCoC,GAAWpC,EAAW,QAAS,EAAI,EACnCqC,GAAWrC,EAAW,QAAS,EAAI,EACnCsC,GAAWtC,EAAW,QAAS,CAAI,EACnCM,GAAU,CACZ,QAASC,EACT,QAASC,EACT,QAASC,EACT,QAASC,EACT,QAASC,EACT,QAASC,EACT,QAASC,EACT,QAASC,EACT,QAASC,EACT,QAASC,GACT,QAASC,EACT,QAASC,EACT,QAASC,GACT,QAASC,EACT,QAASC,EACT,QAASC,EACT,QAASC,EACT,QAASC,EACT,QAASC,GACT,QAASC,GACT,QAASC,GACT,QAASC,GACT,QAASC,GACT,QAASC,GACT,QAASC,GACT,QAASC,GACT,QAASC,GACT,QAASC,GACT,QAASC,GACT,QAASC,GACT,QAASC,GACT,QAASC,EACX,EACIC,GAAc,OAAO,KAAKjC,EAAO,EAO1BkC,GAA6B,SAAoCC,GAAW,CACrF,IAAIlE,IAAckE,IAAa,SAAS,kBAAkB,EAC1D,OAAOF,GAAY,KAAK,SAAUG,EAAS,CACzC,IAAIC,EAAeD,EAAQ,kBAAkB,EAC7C,OAAOC,EAAa,SAASpE,EAAU,CACzC,CAAC,CACH,C,2QCvHWqE,EAAW,SAAkBC,EAAWC,EAAO,CACxD,OAAO,IAAI,IAAUD,CAAS,EAAE,SAASC,CAAK,EAAE,YAAY,CAC9D,EASWC,EAAU,SAAiBF,EAAWG,EAAY,CAC3D,IAAIC,EAAW,IAAI,UAAUJ,CAAS,EACtC,OAAOI,EAAS,QAAQD,CAAU,EAAE,YAAY,CAClD,EACIE,EAAW,UAAoB,CACjC,OAAI,OAAO,KAAU,aAAe,CAAC,IAAc,EAC5C,GACT,EACWC,EAAWD,EAAS,EACpBE,EAAWD,EAAS,SACpBE,EAAiB,SAAwBzE,EAAO,CACzD,MAAO,CACL,UAAW,aACX,OAAQ,EACR,QAAS,EACT,MAAOA,EAAM,UACb,SAAUA,EAAM,SAChB,WAAYA,EAAM,WAClB,UAAW,MACb,CACF,EACW0E,EAAgB,SAAuB1E,EAAO,CACvD,MAAO,CAGL,MAAOA,EAAM,UACb,QAAS,OACT,OAAQ,UACR,WAAY,SAAS,OAAOA,EAAM,kBAAkB,EACpD,mBAAoB,CAClB,MAAOA,EAAM,cACf,EACA,WAAY,CACV,MAAOA,EAAM,eACf,CACF,CACF,EAQO,SAAS2E,EAASC,EAAeC,EAAS,CAC/C,IAAIC,EACA/F,KAAc,cAAW,IAAW,EACtCgG,EAAoBhG,EAAY,MAChCiB,EAAQ+E,IAAsB,OAAS,CAAC,EAAIA,EAC1CtE,MAAe,cAAW,IAAW,EACvCP,EAASO,GAAa,OACpBuE,EAAYR,EAAS,EACvB/H,GAAYuI,EAAU,MACtB7E,GAAS6E,EAAU,OACjB/D,MAAe,cAAW,IAAW,EACvCgE,GAAehE,GAAa,MAC1BC,MAAe,cAAW,kBAAgC,EAC5DjC,GAAeiC,GAAa,aAC5BgE,GAAMhE,GAAa,IAGrB,OAAKlB,EAAM,SACTA,KAAQ,KAAc,CAAC,EAAGvD,EAAS,GAErCuD,EAAM,kBAAoB8E,EAAwB9E,EAAM,oBAAsB,MAAQ8E,IAA0B,OAASA,EAAwB,IAAI,OAAO7F,GAAa,KAAK,CAAC,EAC/Ke,EAAM,OAAS,IAAI,OAAOf,GAAa,CAAC,EACjC,CACL,WAAS,oBAAiB,CACxB,MAAOgG,GACP,MAAOjF,EACP,KAAM,CAAC4E,CAAa,EACpB,MAAOM,IAAQ,KAAyB,OAASA,GAAI,KACvD,EAAG,UAAY,CACb,OAAOL,EAAQ7E,CAAK,CACtB,CAAC,EACD,OAAQE,EAASC,GAAS,EAC5B,CACF,C,oOCtGIgF,EAGOC,EAAe,CACxB,KAAM,UACN,OAAQ,UACR,KAAM,UACN,MAAO,UACP,QAAS,UACT,KAAM,UACN,IAAK,UACL,OAAQ,UACR,OAAQ,UACR,QAAS,UACT,SAAU,UACV,KAAM,UACN,KAAM,UACN,aAAc,UACd,aAAc,UACd,aAAc,UACd,WAAY,UACZ,UAAW,UACX,cAAe,OACf,YAAa,OACb,WAAY,wLACZ,SAAU,GACV,UAAW,EACX,SAAU,QACV,WAAY,GACZ,WAAY,EACZ,kBAAmB,oCACnB,oBAAqB,uCACrB,cAAe,sCACf,gBAAiB,uCACjB,kBAAmB,sCACnB,kBAAmB,uCACnB,mBAAoB,iCACpB,aAAc,EACd,SAAU,EACV,SAAU,EACV,eAAgB,GAChB,cAAe,GACf,WAAY,EACZ,gBAAiB,IACjB,aAAc,EACd,UAAW,GACX,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,UAAW,UACX,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,YAAa,UACb,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,UAAW,UACX,UAAW,UACX,UAAW,UACX,UAAW,UACX,UAAW,UACX,UAAW,UACX,UAAW,UACX,UAAW,UACX,UAAW,UACX,UAAW,UACX,WAAY,UACZ,YAAa,UACb,YAAa,UACb,YAAa,UACb,YAAa,UACb,YAAa,UACb,YAAa,UACb,YAAa,UACb,YAAa,UACb,YAAa,UACb,aAAc,UACd,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,UAAW,UACX,QAAS,UACT,QAAS,UACT,QAAS,UACT,QAAS,UACT,QAAS,UACT,QAAS,UACT,QAAS,UACT,QAAS,UACT,QAAS,UACT,SAAU,UACV,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,YAAa,UACb,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,WAAY,UACZ,YAAa,UACb,YAAa,UACb,YAAa,UACb,YAAa,UACb,YAAa,UACb,YAAa,UACb,YAAa,UACb,YAAa,UACb,YAAa,UACb,YAAa,UACb,aAAc,UACd,aAAc,UACd,aAAc,UACd,aAAc,UACd,aAAc,UACd,aAAc,UACd,aAAc,UACd,aAAc,UACd,aAAc,UACd,aAAc,UACd,cAAe,UACf,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,UAAW,UACX,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,SAAU,UACV,UAAW,UACX,UAAW,sBACX,mBAAoB,sBACpB,kBAAmB,sBACnB,oBAAqB,sBACrB,UAAW,sBACX,mBAAoB,sBACpB,kBAAmB,sBACnB,oBAAqB,sBACrB,cAAe,mBACf,iBAAkB,UAClB,gBAAiB,UACjB,iBAAkB,sBAClB,YAAa,UACb,qBAAsB,UACtB,eAAgB,UAChB,oBAAqB,UACrB,mBAAoB,UACpB,wBAAyB,UACzB,kBAAmB,UACnB,mBAAoB,UACpB,sBAAuB,UACvB,iBAAkB,UAClB,uBAAwB,UACxB,eAAgB,UAChB,oBAAqB,UACrB,mBAAoB,UACpB,wBAAyB,UACzB,kBAAmB,UACnB,mBAAoB,UACpB,sBAAuB,UACvB,iBAAkB,UAClB,uBAAwB,UACxB,aAAc,UACd,kBAAmB,UACnB,iBAAkB,UAClB,sBAAuB,UACvB,gBAAiB,UACjB,iBAAkB,UAClB,oBAAqB,UACrB,eAAgB,UAChB,qBAAsB,UACtB,eAAgB,UAChB,oBAAqB,UACrB,mBAAoB,UACpB,wBAAyB,UACzB,kBAAmB,UACnB,mBAAoB,UACpB,sBAAuB,UACvB,iBAAkB,UAClB,uBAAwB,UACxB,YAAa,UACb,iBAAkB,UAClB,gBAAiB,UACjB,qBAAsB,UACtB,eAAgB,UAChB,gBAAiB,UACjB,mBAAoB,UACpB,cAAe,UACf,oBAAqB,UACrB,YAAa,sBACb,WAAY,OACZ,QAAS,GACT,OAAQ,GACR,OAAQ,GACR,OAAQ,GACR,OAAQ,GACR,KAAM,GACN,OAAQ,GACR,OAAQ,EACR,QAAS,EACT,gBAAiB,GACjB,gBAAiB,GACjB,gBAAiB,GACjB,mBAAoB,OACpB,kBAAmB,OACnB,mBAAoB,OACpB,UAAW,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,EAAE,EAClD,YAAa,CAAC,mBAAoB,mBAAoB,IAAK,IAAK,mBAAoB,mBAAoB,mBAAoB,kBAAmB,mBAAoB,kBAAkB,EACrL,cAAe,EACf,eAAgB,EAChB,eAAgB,EAChB,eAAgB,EAChB,kBAAmB,EACnB,UAAW,UACX,eAAgB,UAChB,gBAAiB,UACjB,iBAAkB,sBAClB,sBAAuB,sBACvB,eAAgB,sBAChB,yBAA0B,sBAC1B,cAAe,UACf,WAAY,sBACZ,qBAAsB,sBACtB,kBAAmB,sBACnB,iBAAkB,sBAClB,eAAgB,sBAChB,qBAAsB,sBACtB,oBAAqB,OACrB,eAAgB,UAChB,iBAAkB,sBAClB,kBAAmB,sBACnB,UAAW,sBACX,eAAgB,sBAChB,kBAAmB,yBACnB,oBAAqB,yBACrB,WAAY,GACZ,WAAY,GACZ,WAAY,GACZ,iBAAkB,GAClB,iBAAkB,GAClB,iBAAkB,GAClB,iBAAkB,GAClB,iBAAkB,GAClB,aAAc,GACd,WAAY,mBACZ,aAAc,IACd,aAAc,mBACd,mBAAoB,mBACpB,mBAAoB,mBACpB,mBAAoB,mBACpB,mBAAoB,IACpB,mBAAoB,IACpB,oBAAqB,EACrB,uBAAwB,GACxB,mBAAoB,sBACpB,oBAAqB,UACrB,yBAA0B,UAC1B,4BAA6B,sBAC7B,kBAAmB,sBACnB,eAAgB,yBAChB,iBAAkB,IAClB,eAAgB,IAChB,eAAgB,OAChB,oBAAqB,OACrB,oBAAqB,OACrB,yBAA0B,GAC1B,2BAA4B,EAC5B,WAAY,EACZ,UAAW,EACX,UAAW,GACX,QAAS,GACT,UAAW,GACX,UAAW,GACX,UAAW,GACX,2BAA4B,GAC5B,yBAA0B,GAC1B,yBAA0B,GAC1B,uBAAwB,GACxB,2BAA4B,GAC5B,yBAA0B,EAC1B,UAAW,EACX,SAAU,EACV,SAAU,GACV,OAAQ,GACR,SAAU,GACV,SAAU,GACV,SAAU,GACV,UAAW,GACX,UAAW,qGACX,mBAAoB,yGACpB,SAAU,IACV,YAAa,IACb,YAAa,IACb,SAAU,IACV,YAAa,IACb,YAAa,IACb,SAAU,IACV,YAAa,IACb,YAAa,IACb,SAAU,IACV,YAAa,IACb,YAAa,IACb,SAAU,KACV,YAAa,KACb,YAAa,KACb,UAAW,KACX,aAAc,KACd,aAAc,KACd,sBAAuB,iCACvB,cAAe,wGACf,qBAAsB,4GACtB,oBAAqB,yGACrB,kBAAmB,yGACnB,oBAAqB,4GACrB,0BAA2B,4CAC3B,2BAA4B,6CAC5B,yBAA0B,4CAC1B,4BAA6B,6CAC7B,UAAW,UACX,QAAS,qCACX,EACWC,EAAW,SAAkBC,EAAK,CAI3C,QAHIC,EAAO,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,EAC3EC,EAAK,WAAaD,EACpBE,EAAK,WAAaF,EACXlM,EAAI,EAAGqM,EAAIrM,EAAIiM,EAAI,OAAQjM,IAClCqM,EAAKJ,EAAI,WAAWjM,CAAC,EACrBmM,EAAK,KAAK,KAAKA,EAAKE,EAAI,UAAU,EAClCD,EAAK,KAAK,KAAKA,EAAKC,EAAI,UAAU,EAEpC,SAAK,KAAK,KAAKF,EAAKA,IAAO,GAAI,UAAU,EAAI,KAAK,KAAKC,EAAKA,IAAO,GAAI,UAAU,EACjFA,EAAK,KAAK,KAAKA,EAAKA,IAAO,GAAI,UAAU,EAAI,KAAK,KAAKD,EAAKA,IAAO,GAAI,UAAU,EAC1E,YAAc,QAAUC,IAAOD,IAAO,EAC/C,EAGWG,KAAa,eAAY,SAAU3F,EAAO,CACnD,OAAOA,CACT,CAAC,EACUA,EAAQ,CACjB,MAAO2F,EACP,SAAO,QAAc,KAAc,CAAC,EAAGP,CAAY,EAAG,MAAU,MAAQ,MAAU,SAAWD,EAAwB,IAAM,oBAAsB,MAAQA,IAA0B,OAAS,OAASA,EAAsB,KAAK,IAAO,MAAU,MAAQ,MAAU,OAAS,OAAS,IAAM,WAAW,CAAC,EACvS,OAAQ,OAAO,OAAOE,EAAS,KAAK,UAAUD,CAAY,CAAC,CAAC,CAC9D,EACWZ,EAAW,UAAoB,CACxC,OAAOxE,CACT,C,iFCvYI4F,EAAS,6IAKTC,EAAa,SAAoBC,EAAG,CACtC,OAAOA,IAAM,KAAOA,IAAM,KAAOA,IAAM,GACzC,EAIIC,EAAW,SAAkBC,EAAG,CAClC,IAAI,EAAI,SAASA,EAAG,EAAE,EACtB,OAAO,MAAM,CAAC,EAAIA,EAAI,CACxB,EAKIC,EAAY,SAAmBC,EAAGxN,EAAG,CACvC,SAAO,KAAQwN,CAAC,OAAM,KAAQxN,CAAC,EAAI,CAAC,OAAOwN,CAAC,EAAG,OAAOxN,CAAC,CAAC,EAAI,CAACwN,EAAGxN,CAAC,CACnE,EAOIyN,EAAiB,SAAwBD,EAAGxN,EAAG,CACjD,GAAImN,EAAWK,CAAC,GAAKL,EAAWnN,CAAC,EAAG,MAAO,GAC3C,IAAI0N,EAAaH,EAAUF,EAASG,CAAC,EAAGH,EAASrN,CAAC,CAAC,EACjD2N,KAAc,KAAeD,EAAY,CAAC,EAC1CE,EAAKD,EAAY,CAAC,EAClBE,EAAKF,EAAY,CAAC,EACpB,OAAIC,EAAKC,EAAW,EAChBD,EAAKC,EAAW,GACb,CACT,EAMIC,EAAkB,SAAyBN,EAAGxN,EAAG,CACnD,QAASW,EAAI,EAAGA,EAAI,KAAK,IAAI6M,EAAE,OAAQxN,EAAE,MAAM,EAAGW,IAAK,CACrD,IAAIb,EAAI2N,EAAeD,EAAE7M,CAAC,GAAK,IAAKX,EAAEW,CAAC,GAAK,GAAG,EAC/C,GAAIb,IAAM,EAAG,OAAOA,CACtB,CACA,MAAO,EACT,EAKIiO,EAAmB,SAA0BC,EAAS,CACxD,IAAIC,EACAC,EAAQF,EAAQ,MAAMd,CAAM,EAChC,UAAU,OAA6Be,EAAeC,EAAM,SAAW,MAAQD,IAAiB,QAAUA,EAAa,KAAKC,CAAK,EAC1HA,CACT,EASWC,EAAkB,SAAyBC,EAAIC,EAAI,CAE5D,IAAIC,EAAKP,EAAiBK,CAAE,EACxBG,EAAKR,EAAiBM,CAAE,EAGxBG,EAAKF,EAAG,IAAI,EACZG,EAAKF,EAAG,IAAI,EAGZzO,EAAIgO,EAAgBQ,EAAIC,CAAE,EAC9B,OAAIzO,IAAM,EAAUA,EAChB0O,GAAMC,EACDD,EAAK,GAAK,EAEZ,CACT,C,wDCtFO,MAAME,EAAqBC,GAC3BA,EAGE,OAAOA,GAAc,WAAaA,EAAU,EAAIA,EAF9C,I,uECDI,SAASC,GAAiB,CACvC,KAAM,CAAC,CAAEC,CAAW,EAAI,aAAiBC,GAAKA,EAAI,EAAG,CAAC,EACtD,OAAOD,CACT,C,mICFO,MAAME,EAAkB,CAAC,MAAO,KAAM,KAAM,KAAM,KAAM,IAAI,EAC7DC,EAAmB1H,IAAU,CACjC,GAAI,eAAeA,EAAM,WAAW,MACpC,GAAI,eAAeA,EAAM,QAAQ,MACjC,GAAI,eAAeA,EAAM,QAAQ,MACjC,GAAI,eAAeA,EAAM,QAAQ,MACjC,GAAI,eAAeA,EAAM,QAAQ,MACjC,IAAK,eAAeA,EAAM,SAAS,KACrC,GAKM2H,EAAsB3H,GAAS,CACnC,MAAM4H,EAAiB5H,EACjB6H,EAAiB,CAAC,EAAE,OAAOJ,CAAe,EAAE,QAAQ,EAC1D,SAAe,QAAQ,CAACK,EAAYzO,IAAM,CACxC,MAAM0O,EAAkBD,EAAW,YAAY,EACzCE,EAAY,SAASD,CAAe,MACpCE,EAAS,SAASF,CAAe,GACvC,GAAI,EAAEH,EAAeI,CAAS,GAAKJ,EAAeK,CAAM,GACtD,MAAM,IAAI,MAAM,GAAGD,CAAS,KAAKC,CAAM,cAAcL,EAAeI,CAAS,CAAC,KAAKJ,EAAeK,CAAM,CAAC,GAAG,EAE9G,GAAI5O,EAAIwO,EAAe,OAAS,EAAG,CACjC,MAAMK,EAAY,SAASH,CAAe,MAC1C,GAAI,EAAEH,EAAeK,CAAM,GAAKL,EAAeM,CAAS,GACtD,MAAM,IAAI,MAAM,GAAGD,CAAM,KAAKC,CAAS,cAAcN,EAAeK,CAAM,CAAC,KAAKL,EAAeM,CAAS,CAAC,GAAG,EAG9G,MAAMC,EAAgB,SADSN,EAAexO,EAAI,CAAC,EAAE,YAAY,CACZ,MACrD,GAAI,EAAEuO,EAAeM,CAAS,GAAKN,EAAeO,CAAa,GAC7D,MAAM,IAAI,MAAM,GAAGD,CAAS,KAAKC,CAAa,cAAcP,EAAeM,CAAS,CAAC,KAAKN,EAAeO,CAAa,CAAC,GAAG,CAE9H,CACF,CAAC,EACMnI,CACT,EACe,SAASoI,GAAwB,CAC9C,KAAM,CAAC,CAAEpI,CAAK,KAAI,MAAS,EACrBqI,EAAgBX,EAAiBC,EAAoB3H,CAAK,CAAC,EAEjE,OAAO,UAAc,IAAM,CACzB,MAAMsI,EAAc,IAAI,IACxB,IAAIC,EAAS,GACTC,EAAU,CAAC,EACf,MAAO,CACL,cAAe,CAAC,EAChB,SAASC,EAAU,CACjB,SAAUA,EACVH,EAAY,QAAQI,GAAQA,EAAKF,CAAO,CAAC,EAClCF,EAAY,MAAQ,CAC7B,EACA,UAAUI,EAAM,CACd,OAAKJ,EAAY,MAAM,KAAK,SAAS,EACrCC,GAAU,EACVD,EAAY,IAAIC,EAAQG,CAAI,EAC5BA,EAAKF,CAAO,EACLD,CACT,EACA,YAAYI,EAAY,CACtBL,EAAY,OAAOK,CAAU,EACxBL,EAAY,MAAM,KAAK,WAAW,CACzC,EACA,YAAa,CACX,OAAO,KAAKD,CAAa,EAAE,QAAQJ,GAAU,CAC3C,MAAMW,EAAkBP,EAAcJ,CAAM,EACtCY,EAAU,KAAK,cAAcD,CAAe,EAClDC,GAAY,MAAsCA,EAAQ,IAAI,eAAeA,GAAY,KAA6B,OAASA,EAAQ,QAAQ,CACjJ,CAAC,EACDP,EAAY,MAAM,CACpB,EACA,UAAW,CACT,OAAO,KAAKD,CAAa,EAAE,QAAQJ,GAAU,CAC3C,MAAMW,EAAkBP,EAAcJ,CAAM,EACtCa,EAAWvQ,GAAQ,CACvB,GAAI,CACF,SACF,EAAIA,EACJ,KAAK,SAAS,OAAO,OAAO,OAAO,OAAO,CAAC,EAAGiQ,CAAO,EAAG,CACtD,CAACP,CAAM,EAAGc,CACZ,CAAC,CAAC,CACJ,EACMC,EAAM,OAAO,WAAWJ,CAAe,EAC7CI,EAAI,YAAYF,CAAQ,EACxB,KAAK,cAAcF,CAAe,EAAI,CACpC,MACA,UACF,EACAE,EAASE,CAAG,CACd,CAAC,CACH,EACA,eACF,CACF,EAAG,CAAChJ,CAAK,CAAC,CACZ,CACO,MAAMiJ,EAAc,CAACT,EAASU,IAAgB,CACnD,GAAIA,GAAe,OAAOA,GAAgB,SACxC,QAAS7P,EAAI,EAAGA,EAAIoO,EAAgB,OAAQpO,IAAK,CAC/C,MAAMyO,EAAaL,EAAgBpO,CAAC,EACpC,GAAImP,EAAQV,CAAU,GAAKoB,EAAYpB,CAAU,IAAM,OACrD,OAAOoB,EAAYpB,CAAU,CAEjC,CAEJ,C,uKCxGA,EADmC,gBAAoB,CAAC,CAAC,E,4CCEzD,MAAMqB,EAAenJ,GAAS,CAC5B,KAAM,CACJ,SACA,eACA,UACA,WACA,cACA,gBACA,kBACA,kBACA,eACA,iBACA,kBACA,eACA,iBACA,kBACA,YACA,UACF,EAAIA,EAEEoJ,EAAkB,CAACC,EAAMC,EAAUC,MAAY,CACnD,MAAOF,EACP,OAAQA,EACR,aAAc,MACd,CAAC,IAAIG,CAAY,SAAS,EAAG,CAC3B,aAAcD,EAChB,EACA,CAAC,IAAIC,CAAY,OAAO,EAAG,CACzB,WACA,CAAC,KAAKC,CAAO,EAAE,EAAG,CAChB,OAAQ,CACV,CACF,CACF,GACA,MAAO,CACL,CAACD,CAAY,EAAG,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,KAAG,MAAexJ,CAAK,CAAC,EAAG,CAClG,SAAU,WACV,QAAS,cACT,eAAgB,SAChB,WAAY,SACZ,SAAU,SACV,MAAO0J,EACP,WAAY,SACZ,UAAW,SACX,cAAe,SACf,WAAYC,EACZ,OAAQ,MAAG,QAAKC,CAAS,CAAC,IAAIC,CAAQ,eACtC,UAAW,CACT,WAAY,aACd,EACA,CAAC,GAAGvK,CAAM,YAAY,EAAG,CACvB,QAAS,OACX,CACF,CAAC,EAAG8J,EAAgBU,EAAeC,EAAcC,CAAY,CAAC,EAAG,CAC/D,OAAQ,OAAO,OAAO,CAAC,EAAGZ,EAAgBa,EAAiBC,EAAgBC,CAAc,CAAC,EAC1F,OAAQ,OAAO,OAAO,CAAC,EAAGf,EAAgBgB,EAAiBC,GAAgBC,EAAc,CAAC,EAC1F,QAAS,CACP,QAAS,QACT,MAAO,OACP,OAAQ,OACR,UAAW,OACb,CACF,CAAC,CACH,CACF,EACMC,EAAgBvK,GAAS,CAC7B,KAAM,CACJ,eACA,mBACA,mBACA,YACF,EAAIA,EACJ,MAAO,CACL,CAAC,GAAGwJ,CAAY,QAAQ,EAAG,CACzB,QAAS,cACT,CAACA,CAAY,EAAG,CACd,YAAagB,CACf,EACA,wBAAyB,CACvB,kBAAmBC,CACrB,CACF,EACA,CAAC,GAAGjB,CAAY,gBAAgB,EAAG,CACjC,CAAC,GAAGA,CAAY,MAAMA,CAAY,EAAE,EAAG,CACrC,kBAAmBkB,CACrB,CACF,CACF,CACF,EACaC,EAAwB3K,GAAS,CAC5C,KAAM,CACJ,gBACA,kBACA,kBACA,WACA,aACA,aACA,mBACA,WACA,YACA,eACF,EAAIA,EACJ,MAAO,CACL,cAAe4K,EACf,gBAAiBC,EACjB,gBAAiBC,EACjB,aAAc,KAAK,OAAOC,EAAaC,GAAc,CAAC,EACtD,eAAgBC,EAChB,eAAgB3B,EAChB,WAAY4B,EACZ,iBAAkB,CAACC,EACnB,iBAAkBC,CACpB,CACF,EACA,SAAe,MAAc,SAAUpL,GAAS,CAC9C,KAAM,CACJ,sBACA,sBACF,EAAIA,EACEqL,KAAc,cAAWrL,EAAO,CACpC,SAAUsL,EACV,YAAaC,CACf,CAAC,EACD,MAAO,CAACpC,EAAakC,CAAW,EAAGd,EAAcc,CAAW,CAAC,CAC/D,EAAGV,CAAqB,EC7HpBa,EAAgC,SAAU1F,EAAG2F,EAAG,CAClD,IAAIC,EAAI,CAAC,EACT,QAASxS,KAAK4M,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAG5M,CAAC,GAAKuS,EAAE,QAAQvS,CAAC,EAAI,IAAGwS,EAAExS,CAAC,EAAI4M,EAAE5M,CAAC,GAC/F,GAAI4M,GAAK,MAAQ,OAAO,OAAO,uBAA0B,WAAY,QAASzM,EAAI,EAAGH,EAAI,OAAO,sBAAsB4M,CAAC,EAAGzM,EAAIH,EAAE,OAAQG,IAClIoS,EAAE,QAAQvS,EAAEG,CAAC,CAAC,EAAI,GAAK,OAAO,UAAU,qBAAqB,KAAKyM,EAAG5M,EAAEG,CAAC,CAAC,IAAGqS,EAAExS,EAAEG,CAAC,CAAC,EAAIyM,EAAE5M,EAAEG,CAAC,CAAC,GAElG,OAAOqS,CACT,EAaA,MAAMC,EAAiB,CAACtN,EAAOuN,IAAQ,CACrC,KAAM,CAACC,EAAOC,CAAQ,EAAI,WAAe,CAAC,EACpC,CAACC,EAASC,CAAU,EAAI,WAAe,EAAK,EAC5C,CAACC,EAAYC,CAAa,EAAI,WAAe,EAAI,EACjDC,EAAgB,SAAa,IAAI,EACjCC,EAAoB,SAAa,IAAI,EACrCC,KAAsB,MAAWT,EAAKO,CAAa,EACnD,CACJ,gBACA,QACF,EAAI,aAAiB,IAAa,EAC5BG,EAAY,aAAiB,CAAa,EAC1CC,GAAgB,IAAM,CAC1B,GAAI,CAACH,EAAkB,SAAW,CAACD,EAAc,QAC/C,OAEF,MAAMK,EAAgBJ,EAAkB,QAAQ,YAC1CK,EAAYN,EAAc,QAAQ,YAExC,GAAIK,IAAkB,GAAKC,IAAc,EAAG,CAC1C,KAAM,CACJ,MAAM,CACR,EAAIpO,EACAqO,EAAM,EAAID,GACZX,EAASW,EAAYC,EAAM,EAAIF,GAAiBC,EAAYC,EAAM,GAAKF,EAAgB,CAAC,CAE5F,CACF,EACA,YAAgB,IAAM,CACpBR,EAAW,EAAI,CACjB,EAAG,CAAC,CAAC,EACL,YAAgB,IAAM,CACpBE,EAAc,EAAI,EAClBJ,EAAS,CAAC,CACZ,EAAG,CAACzN,EAAM,GAAG,CAAC,EACd,YAAgBkO,GAAe,CAAClO,EAAM,GAAG,CAAC,EAC1C,MAAMsO,EAAqB,IAAM,CAC/B,KAAM,CACJ,SACF,EAAItO,GACcuO,GAAY,KAA6B,OAASA,EAAQ,KAC1D,IAChBV,EAAc,EAAK,CAEvB,EACM,CACF,UAAWW,EACX,QACA,KAAMC,EACN,MACA,UACA,QACA,aACA,iBACA,OACA,aACA,YACA,cACF,EAAIzO,EACJ0O,GAASvB,EAAOnN,EAAO,CAAC,YAAa,QAAS,OAAQ,MAAO,SAAU,OAAQ,YAAa,gBAAiB,MAAO,YAAa,WAAY,aAAa,CAAC,EACvJgL,MAAO2D,KAAQC,GAAW,CAC9B,IAAIC,EAAIC,EACR,OAAQA,GAAMD,EAAKJ,GAAe,KAAgCA,EAAaR,GAAc,KAA+B,OAASA,EAAU,QAAU,MAAQY,IAAO,OAASA,EAAKD,KAAa,MAAQE,IAAO,OAASA,EAAK,SAClO,CAAC,EACKC,GAAiB,OAAO,KAAK,OAAO/D,IAAS,SAAWA,IAAQ,CAAC,EAAI,CAAC,CAAC,EAAE,KAAK/L,GAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAK,EAAE,SAASA,CAAG,CAAC,EACxIkL,MAAU6E,KAAcD,EAAc,EACtCE,GAAsB,UAAc,IAAM,CAC9C,GAAI,OAAOjE,IAAS,SAClB,MAAO,CAAC,EAEV,MAAMkE,EAAoB,KAAgB,KAAKtF,GAAUO,GAAQP,CAAM,CAAC,EAClEuF,EAAcnE,GAAKkE,CAAiB,EAC1C,OAAOC,EAAc,CACnB,MAAOA,EACP,OAAQA,EACR,SAAUA,IAAgBC,IAAQlP,IAAYiP,EAAc,EAAI,EAClE,EAAI,CAAC,CACP,EAAG,CAAChF,GAASa,EAAI,CAAC,EAKZxK,GAAYI,GAAa,SAAU4N,CAAkB,EACrDa,MAAUC,KAAa9O,EAAS,EAChC,CAAC+O,GAAYzN,GAAQ0N,EAAS,EAAI,EAAShP,GAAW6O,EAAO,EAC7DI,GAAU,IAAW,CACzB,CAAC,GAAGjP,EAAS,KAAK,EAAGwK,KAAS,QAC9B,CAAC,GAAGxK,EAAS,KAAK,EAAGwK,KAAS,OAChC,CAAC,EACK0E,GAA+B,iBAAqBC,CAAG,EACvDC,EAAcC,IAAU5B,GAAc,KAA+B,OAASA,EAAU,QAAU,SAClG6B,EAAc,IAAWtP,GAAWiP,GAASM,GAAW,KAA4B,OAASA,EAAO,UAAW,GAAGvP,EAAS,IAAIoP,CAAW,GAAI,CAClJ,CAAC,GAAGpP,EAAS,QAAQ,EAAGkP,IAAmBC,GAAO/B,EAClD,CAAC,GAAGpN,EAAS,OAAO,EAAG,CAAC,CAAC4O,EAC3B,EAAGI,GAAWH,GAASW,GAAWC,GAAenO,EAAM,EACjDoO,EAAY,OAAOlF,IAAS,SAAW,CAC3C,MAAOA,GACP,OAAQA,GACR,SAAUoE,GAAOpE,GAAO,EAAI,EAC9B,EAAI,CAAC,EACL,IAAImF,EACJ,GAAI,OAAOR,GAAQ,UAAY/B,EAC7BuC,EAAgC,gBAAoB,MAAO,CACzD,IAAKR,EACL,UAAWS,GACX,OAAQC,GACR,QAAS/B,EACT,IAAKgC,GACL,YAAaC,EACf,CAAC,UACQb,GACTS,EAAmBR,UACVP,GACTe,EAAmBf,WACV1B,GAAWF,IAAU,EAAG,CACjC,MAAMgD,EAAkB,SAAShD,CAAK,IAChCiD,EAAgB,CACpB,YAAaD,EACb,gBAAiBA,EACjB,UAAWA,CACb,EACAL,EAAgC,gBAAoB,UAAgB,CAClE,SAAUjC,EACZ,EAAgB,gBAAoB,OAAQ,CAC1C,UAAW,GAAG1N,EAAS,UACvB,IAAKuN,EACL,MAAO,OAAO,OAAO,CAAC,EAAG0C,CAAa,CACxC,EAAGvQ,EAAQ,CAAC,CACd,MACEiQ,EAAgC,gBAAoB,OAAQ,CAC1D,UAAW,GAAG3P,EAAS,UACvB,MAAO,CACL,QAAS,CACX,EACA,IAAKuN,CACP,EAAG7N,EAAQ,EAIb,cAAOwO,GAAO,QACd,OAAOA,GAAO,IACPa,GAAwB,gBAAoB,OAAQ,OAAO,OAAO,CAAC,EAAGb,GAAQ,CACnF,MAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC,EAAGwB,CAAS,EAAGjB,EAAmB,EAAGc,GAAW,KAA4B,OAASA,EAAO,KAAK,EAAGrB,GAAO,KAAK,EAChL,UAAWoB,EACX,IAAK9B,CACP,CAAC,EAAGmC,CAAgB,CAAC,CACvB,EAKA,MAJ4B,aAAiB7C,CAAc,E,mCC5J3D,MAAMoD,GAAwB1Q,GAAS,CACrC,KAAM,CACJ,OACA,OACF,EAAI,aAAiB,CAAa,EAC5B2Q,EAAqB,UAAc,KAAO,CAC9C,KAAM3Q,EAAM,MAAQgL,EACpB,MAAOhL,EAAM,OAAS6P,CACxB,GAAI,CAAC7P,EAAM,KAAMA,EAAM,MAAOgL,EAAM6E,CAAK,CAAC,EAC1C,OAAoB,gBAAoB,EAAc,SAAU,CAC9D,MAAOc,CACT,EAAG3Q,EAAM,QAAQ,CACnB,EA2EA,OA1EcA,GAAS,CACrB,IAAI6O,EAAIC,EAAI8B,EACZ,KAAM,CACJ,eACA,WACF,EAAI,aAAiB,IAAa,EAC5B,CACJ,UAAWpC,EACX,YACA,gBACA,QACA,WACA,YACA,OACA,QACA,uBACA,oBACA,WACA,KACF,EAAIxO,EAQEQ,EAAYI,EAAa,SAAU4N,CAAkB,EACrDqC,EAAiB,GAAGrQ,CAAS,SAC7B6O,MAAUC,KAAa9O,CAAS,EAChC,CAAC+O,GAAYzN,GAAQ0N,EAAS,EAAI,EAAShP,EAAW6O,EAAO,EAC7DyB,GAAM,IAAWD,EAAgB,CACrC,CAAC,GAAGA,CAAc,MAAM,EAAGE,IAAc,KAC3C,EAAGvB,GAAWH,GAASW,EAAWC,EAAenO,EAAM,EACjDkP,MAAoBC,KAAQ/Q,CAAQ,EAAE,IAAI,CAACgR,GAAOjV,QAAU,OAAaiV,GAAO,CACpF,IAAK,cAAcjV,EAAK,EAC1B,CAAC,CAAC,EACIkV,IAAcC,GAAQ,KAAyB,OAASA,EAAI,QAAUC,EACtEC,GAAgBN,GAAkB,OACxC,GAAIG,IAAcA,GAAaG,GAAe,CAC5C,MAAMC,GAAeP,GAAkB,MAAM,EAAGG,EAAU,EACpDK,GAAiBR,GAAkB,MAAMG,GAAYG,EAAa,EAClEG,IAAcL,GAAQ,KAAyB,OAASA,EAAI,QAAUM,GACtEC,KAAwB9C,EAAKuC,GAAQ,KAAyB,OAASA,EAAI,WAAa,MAAQvC,IAAO,OAAS,OAASA,EAAG,UAAY+C,GAAqB,QAC7JC,KAA0B/C,EAAKsC,GAAQ,KAAyB,OAASA,EAAI,WAAa,MAAQtC,IAAO,OAAS,OAASA,EAAG,YAAcgD,IAAuB,MACnKC,GAAa,OAAO,OAAO,OAAO,OAAO,CAC7C,QAASP,EACX,EAAGJ,GAAQ,KAAyB,OAASA,EAAI,OAAO,EAAG,CACzD,iBAAkB,IAAW,GAAGP,CAAc,YAAaD,EAAKQ,GAAQ,KAAyB,OAASA,EAAI,WAAa,MAAQR,IAAO,OAAS,OAASA,EAAG,gBAAgB,EAC/K,UAAWiB,GACX,QAASF,EACX,CAAC,EACD,UAAa,KAAkB,gBAAoB,KAAS,OAAO,OAAO,CACxE,IAAK,qBACL,qBAAsB,EACxB,EAAGI,EAAU,EAAgB,gBAAoB,EAAQ,CACvD,MAAON,EACT,EAAG,IAAIH,GAAgBH,EAAU,EAAE,CAAC,CAAC,EAC9B5B,GAAwB,gBAAoBmB,GAAuB,CACxE,MAAOb,EACP,KAAM7E,CACR,EAAgB,gBAAoB,MAAO,CACzC,UAAW8F,GACX,MAAOkB,CACT,EAAGT,EAAY,CAAC,CAAC,CACnB,CACA,OAAOhC,GAAwB,gBAAoBmB,GAAuB,CACxE,MAAOb,EACP,KAAM7E,CACR,EAAgB,gBAAoB,MAAO,CACzC,UAAW8F,GACX,MAAOkB,CACT,EAAGhB,EAAiB,CAAC,CAAC,CACxB,EC9FA,MAAM,GAAS,EACf,GAAO,MAAQ,GACf,OAAe,E,uECDf,SAAShC,GAAgB,CACvB,IAAIiD,EAAkB,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GAC1F,MAAMC,KAAa,UAAO,CAAC,CAAC,EACtBhJ,KAAc,KAAe,EAC7BiJ,KAAqB,MAAsB,EACjD,cAAgB,IAAM,CACpB,MAAMxQ,EAAQwQ,EAAmB,UAAUC,GAAkB,CAC3DF,EAAW,QAAUE,EACjBH,GACF/I,EAAY,CAEhB,CAAC,EACD,MAAO,IAAMiJ,EAAmB,YAAYxQ,CAAK,CACnD,EAAG,CAAC,CAAC,EACEuQ,EAAW,OACpB,CACA,IAAelD,C,wICpBX7B,EAAgC,SAAU1F,EAAG2F,EAAG,CAClD,IAAIC,EAAI,CAAC,EACT,QAASxS,KAAK4M,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAG5M,CAAC,GAAKuS,EAAE,QAAQvS,CAAC,EAAI,IAAGwS,EAAExS,CAAC,EAAI4M,EAAE5M,CAAC,GAC/F,GAAI4M,GAAK,MAAQ,OAAO,OAAO,uBAA0B,WAAY,QAASzM,EAAI,EAAGH,EAAI,OAAO,sBAAsB4M,CAAC,EAAGzM,EAAIH,EAAE,OAAQG,IAClIoS,EAAE,QAAQvS,EAAEG,CAAC,CAAC,EAAI,GAAK,OAAO,UAAU,qBAAqB,KAAKyM,EAAG5M,EAAEG,CAAC,CAAC,IAAGqS,EAAExS,EAAEG,CAAC,CAAC,EAAIyM,EAAE5M,EAAEG,CAAC,CAAC,GAElG,OAAOqS,CACT,EAOO,MAAMgF,EAAUnY,GAAQ,CAC7B,GAAI,CACF,QACA,UACA,WACF,EAAIA,EACJ,MAAI,CAACoY,GAAS,CAACC,EACN,KAEW,gBAAoB,WAAgB,KAAMD,GAAsB,gBAAoB,MAAO,CAC7G,UAAW,GAAG9R,CAAS,QACzB,EAAG8R,CAAK,EAAGC,GAAwB,gBAAoB,MAAO,CAC5D,UAAW,GAAG/R,CAAS,gBACzB,EAAG+R,CAAO,CAAC,CACb,EACaC,EAAexS,GAAS,CACnC,KAAM,CACJ,SACA,YACA,YACA,QACA,YAAY,MACZ,QACA,UACA,UACF,EAAIA,EACEyS,KAAY,KAAmBH,CAAK,EACpCI,KAAc,KAAmBH,CAAO,EACxCzB,GAAM,IAAWhP,EAAQtB,EAAW,GAAGA,CAAS,QAAS,GAAGA,CAAS,cAAcmS,CAAS,GAAI3C,CAAS,EAC/G,OAAoB,gBAAoB,MAAO,CAC7C,UAAWc,GACX,MAAOkB,CACT,EAAgB,gBAAoB,MAAO,CACzC,UAAW,GAAGxR,CAAS,QACzB,CAAC,EAAgB,gBAAoB,QAAO,OAAO,OAAO,CAAC,EAAGR,EAAO,CACnE,UAAW8B,EACX,UAAWtB,CACb,CAAC,EAAGN,GAAyB,gBAAoBmS,EAAS,CACxD,UAAW7R,EACX,MAAOiS,EACP,QAASC,CACX,CAAC,CAAC,CAAC,CACL,EACME,EAAY5S,GAAS,CACzB,KAAM,CACF,UAAWwO,EACX,WACF,EAAIxO,EACJ6S,EAAY1F,EAAOnN,EAAO,CAAC,YAAa,WAAW,CAAC,EAChD,CACJ,cACF,EAAI,aAAiB,IAAa,EAC5BQ,EAAYI,EAAa,UAAW4N,CAAkB,EACtD,CAACe,EAAYzN,EAAQ0N,CAAS,KAAI,KAAShP,CAAS,EAC1D,OAAO+O,EAAwB,gBAAoBiD,EAAc,OAAO,OAAO,CAAC,EAAGK,EAAW,CAC5F,UAAWrS,EACX,OAAQsB,EACR,UAAW,IAAWkO,EAAWR,CAAS,CAC5C,CAAC,CAAC,CAAC,CACL,EACA,KAAeoD,C,8JC1EXzF,EAAgC,SAAU1F,EAAG2F,EAAG,CAClD,IAAIC,EAAI,CAAC,EACT,QAASxS,KAAK4M,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAG5M,CAAC,GAAKuS,EAAE,QAAQvS,CAAC,EAAI,IAAGwS,EAAExS,CAAC,EAAI4M,EAAE5M,CAAC,GAC/F,GAAI4M,GAAK,MAAQ,OAAO,OAAO,uBAA0B,WAAY,QAASzM,EAAI,EAAGH,EAAI,OAAO,sBAAsB4M,CAAC,EAAGzM,EAAIH,EAAE,OAAQG,IAClIoS,EAAE,QAAQvS,EAAEG,CAAC,CAAC,EAAI,GAAK,OAAO,UAAU,qBAAqB,KAAKyM,EAAG5M,EAAEG,CAAC,CAAC,IAAGqS,EAAExS,EAAEG,CAAC,CAAC,EAAIyM,EAAE5M,EAAEG,CAAC,CAAC,GAElG,OAAOqS,CACT,EAmFA,MAAMyF,EAtE+B,aAAiB,CAAC9S,EAAOuN,IAAQ,CACpE,IAAIsB,EAAIC,EACR,KAAM,CACF,UAAWN,EACX,QACA,UACA,oBACA,YAAY,MACZ,UAAU,QACV,YACA,mBAAkB,GAClB,mBAAkB,GAClB,gBACA,gBAAe,CAAC,CAClB,EAAIxO,EACJ+S,GAAa5F,EAAOnN,EAAO,CAAC,YAAa,QAAS,UAAW,mBAAoB,YAAa,UAAW,WAAY,kBAAmB,kBAAmB,eAAgB,cAAc,CAAC,EACtL,CACJ,eACF,EAAI,aAAiB,IAAa,EAC5BQ,EAAYI,GAAa,UAAW4N,CAAkB,EACtD,CAACe,EAAYzN,EAAQ0N,CAAS,KAAI,KAAShP,CAAS,EACpDwS,EAAgBpS,GAAa,EAC7BqS,EAAa,IAAWC,GAAkBpR,EAAQ0N,CAAS,EAC3D,CAAC2D,EAAMC,CAAO,KAAI,KAAe,GAAO,CAC5C,OAAQvE,EAAK7O,EAAM,QAAU,MAAQ6O,IAAO,OAASA,EAAK7O,EAAM,QAChE,cAAe8O,EAAK9O,EAAM,eAAiB,MAAQ8O,IAAO,OAASA,EAAK9O,EAAM,cAChF,CAAC,EACKqT,EAAc,CAAC/X,EAAO8R,KAAM,CAChCgG,EAAQ9X,EAAO,EAAI,EACnBgY,IAAiB,MAA2CA,GAAahY,EAAO8R,EAAC,CACnF,EACMmG,EAAYnG,GAAK,CACjBA,EAAE,UAAY,IAAQ,KACxBiG,EAAY,GAAOjG,CAAC,CAExB,EACMoG,EAAuBlY,GAAS,CACpC+X,EAAY/X,CAAK,CACnB,EACMmX,MAAY,KAAmBH,CAAK,EACpCI,KAAc,KAAmBH,CAAO,EAC9C,OAAOhD,EAAwB,gBAAoB,IAAS,OAAO,OAAO,CACxE,UAAWoD,EACX,QAASc,EACT,gBAAiBC,GACjB,gBAAiBC,GACjB,aAAcC,EAChB,EAAGb,GAAY,CACb,UAAWvS,EACX,iBAAkByS,EAClB,IAAK1F,EACL,KAAM4F,EACN,aAAcK,EACd,QAASf,IAAaC,EAA4B,gBAAoB,KAAS,CAC7E,UAAWlS,EACX,MAAOiS,GACP,QAASC,CACX,CAAC,EAAK,KACN,kBAAgB,KAAkBM,EAAe,WAAYD,GAAW,cAAc,EACtF,sBAAuB,EACzB,CAAC,KAAG,MAAa7S,GAAU,CACzB,UAAWkN,GAAK,CACd,IAAIyB,GAAIC,EACS,iBAAqB5O,EAAQ,KAC3C4O,EAAK5O,IAAa,KAA8B,QAAU2O,GAAK3O,GAAS,OAAO,aAAe,MAAQ4O,IAAO,QAAkBA,EAAG,KAAKD,GAAIzB,CAAC,GAE/ImG,EAAUnG,CAAC,CACb,CACF,CAAC,CAAC,CAAC,CACL,CAAC,EAED0F,EAAQ,uCAAyC,KAIjD,IAAeA,C,wGC5Ff,MAAMhI,EAAenJ,GAAS,CAC5B,KAAM,CACJ,eACA,eACA,gBACA,mBACA,eACA,qBACA,mBACA,iBACA,cACA,oBACA,kBACA,aACA,oBACA,sBACA,eACF,EAAIA,EACJ,MAAO,CAAC,CACN,CAACwJ,CAAY,EAAG,OAAO,OAAO,OAAO,OAAO,CAAC,KAAG,MAAexJ,CAAK,CAAC,EAAG,CACtE,SAAU,WACV,IAAK,EAEL,KAAM,CACJ,aAAc,GACd,MAAO,CACT,EACA,OAAQkS,EACR,WAAY,SACZ,WAAY,SACZ,UAAW,QACX,OAAQ,OACR,WAAY,OAEZ,mBAAoB,iDACpB,gBAAiB,CAAC,6BAA8B,qBAAqB,EAAE,KAAK,GAAG,EAC/E,gCAAiCC,EACjC,MAAO,cACP,SAAU,QACV,QAAS,CACP,UAAW,KACb,EACA,WAAY,CACV,QAAS,MACX,EACA,CAAC,GAAG3I,CAAY,UAAU,EAAG,CAC3B,SAAU,UACZ,EACA,CAAC,GAAGA,CAAY,QAAQ,EAAG,CACzB,gBAAiB4I,GACjB,eAAgB,cAChB,aAAcjI,EACd,UAAWkI,EACX,QAASC,CACX,EACA,CAAC,GAAG9I,CAAY,QAAQ,EAAG,CACzB,SAAU+I,EACV,aAAcC,EACd,MAAOC,EACP,WAAYC,EACZ,aAAcC,EACd,QAASC,EACX,EACA,CAAC,GAAGpJ,CAAY,gBAAgB,EAAG,CACjC,MAAOqJ,EACP,QAASC,CACX,CACF,CAAC,CACH,KAEA,MAAc9S,EAAO,oCAAoC,EAEzD,CACE,CAAC,GAAGwJ,CAAY,OAAO,EAAG,CACxB,SAAU,WACV,SAAU,OACV,OAAQxJ,EAAM,eACd,QAAS,eACT,CAAC,GAAGwJ,CAAY,UAAU,EAAG,CAC3B,QAAS,cACX,CACF,CACF,CAAC,CACH,EACMuJ,EAAgB/S,GAAS,CAC7B,KAAM,CACJ,cACF,EAAIA,EACJ,MAAO,CACL,CAACwJ,CAAY,EAAG,IAAa,IAAIwJ,GAAY,CAC3C,MAAMC,EAAajT,EAAM,GAAGgT,CAAQ,GAAG,EACvC,MAAO,CACL,CAAC,IAAIxJ,CAAY,IAAIwJ,CAAQ,EAAE,EAAG,CAChC,gCAAiCC,EACjC,CAAC,GAAGzJ,CAAY,QAAQ,EAAG,CACzB,gBAAiByJ,CACnB,EACA,CAAC,GAAGzJ,CAAY,QAAQ,EAAG,CACzB,WAAY,aACd,CACF,CACF,CACF,CAAC,CACH,CACF,EACamB,EAAwB3K,GAAS,CAC5C,KAAM,CACJ,YACA,gBACA,aACA,UACA,YACA,kBACA,iBACA,WACA,WACA,aACA,WACF,EAAIA,EACEkT,GAAwBtI,EAAgBuI,EACxCC,EAA8BF,GAAwB,EACtDG,EAAiCH,GAAwB,EAAItJ,EAC7D0J,GAA2BC,EACjC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,CAC/C,cAAe,IACf,YAAaC,EAAkB,EACjC,KAAG,KAAcxT,CAAK,CAAC,KAAG,MAAoB,CAC5C,cAAemK,EACf,oBAAqB,EACvB,CAAC,CAAC,EAAG,CAEH,aAAcsJ,EAAY,EAAI,GAC9B,kBAAmBA,EAAY,EAAItI,EACnC,aAAcsI,EAAY,GAAGL,CAA2B,MAAME,EAAwB,MAAMD,CAA8B,KAAO,EACjI,kBAAmBI,EAAY,GAAG7J,CAAS,MAAMC,CAAQ,IAAI6J,CAAU,GAAK,OAC5E,oBAAqBD,EAAY,GAAGE,CAAS,MAAML,EAAwB,KAAO,CACpF,CAAC,CACH,EACA,OAAe,MAAc,UAAWtT,GAAS,CAC/C,KAAM,CACJ,kBACA,WACF,EAAIA,EACE4T,KAAe,cAAW5T,EAAO,CACrC,UAAWmS,EACX,aAAc0B,CAChB,CAAC,EACD,MAAO,CAAC1K,EAAayK,CAAY,EAAGb,EAAca,CAAY,KAAG,MAAeA,EAAc,UAAU,CAAC,CAC3G,EAAGjJ,EAAuB,CACxB,WAAY,GACZ,iBAAkB,CAAC,CAAC,QAAS,eAAe,EAAG,CAAC,WAAY,eAAe,CAAC,CAC9E,CAAC,C,sBC5JD,SAASmJ,EAAK7W,EAAK8W,EAAQ,CAIzB,QAFIC,EAAc,OAAO,OAAO,CAAC,EAAG/W,CAAG,EAE9B5D,EAAI,EAAGA,EAAI0a,EAAO,OAAQ1a,GAAK,EAAG,CACzC,IAAIiE,EAAMyW,EAAO1a,CAAC,EAClB,OAAO2a,EAAY1W,CAAG,CACxB,CAEA,OAAO0W,CACT,CAEA,IAAeF,C,wBCHF,IAAIrI,EAAE,EAAQ,KAAO,EAAE,SAASwI,EAAE/N,EAAExN,EAAE,CAAC,OAAOwN,IAAIxN,IAAQwN,IAAJ,GAAO,EAAEA,IAAI,EAAExN,IAAIwN,IAAIA,GAAGxN,IAAIA,CAAC,CAAC,IAAIwb,EAAe,OAAO,OAAO,IAA3B,WAA8B,OAAO,GAAGD,EAAEE,EAAE1I,EAAE,SAAS2I,EAAE3I,EAAE,UAAU4I,EAAE5I,EAAE,gBAAgBvS,EAAEuS,EAAE,cAAc,SAAS6I,EAAEpO,EAAExN,EAAE,CAAC,IAAI6b,EAAE7b,EAAE,EAAE8b,EAAEL,EAAE,CAAC,KAAK,CAAC,MAAMI,EAAE,YAAY7b,CAAC,CAAC,CAAC,EAAE,EAAE8b,EAAE,CAAC,EAAE,KAAK/b,EAAE+b,EAAE,CAAC,EAAE,SAAE,UAAU,CAAC,EAAE,MAAMD,EAAE,EAAE,YAAY7b,EAAEF,EAAE,CAAC,GAAGC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAACyN,EAAEqO,EAAE7b,CAAC,CAAC,EAAE0b,EAAE,UAAU,CAAC,SAAE,CAAC,GAAG3b,EAAE,CAAC,KAAK,CAAC,CAAC,EAASyN,EAAE,UAAU,CAAC1N,EAAE,CAAC,GAAGC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACyN,CAAC,CAAC,EAAEhN,EAAEqb,CAAC,EAASA,CAAC,CAClc,SAAS/b,EAAE0N,EAAE,CAAC,IAAIxN,EAAEwN,EAAE,YAAYA,EAAEA,EAAE,MAAM,GAAG,CAAC,IAAIqO,EAAE7b,EAAE,EAAE,MAAM,CAACwb,EAAEhO,EAAEqO,CAAC,CAAC,OAAOC,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,SAAS9I,EAAExF,EAAExN,EAAE,CAAC,OAAOA,EAAE,CAAC,CAAC,IAAI+b,EAAgB,OAAO,QAArB,aAA2C,OAAO,OAAO,UAA5B,aAAoD,OAAO,OAAO,SAAS,eAArC,YAAmD/I,EAAE4I,EAAEI,EAAQ,qBAA8BjJ,EAAE,uBAAX,OAAgCA,EAAE,qBAAqBgJ,C,wBCPxUE,EAAO,QAAU,EAAjB,M,sICAF,MAAMC,EAAO,IAAI,CAAC,EAKZC,EAA8BD,EAAK,EACnCE,EAAS,OACTC,EAAe/O,GAAIA,IAAM6O,EACzBG,EAAchP,GAAI,OAAOA,GAAK,WAC9BiP,EAAe,CAAC/O,EAAGxN,IAAK,SACnBwN,GACAxN,GAELwc,EAAiB1N,GAAIwN,EAAWxN,EAAE,IAAI,EAMtC2N,EAAQ,IAAI,QAElB,IAAIC,EAAU,EASd,MAAMC,EAAcC,GAAM,CACtB,MAAMC,EAAO,OAAOD,EACdE,EAAcF,GAAOA,EAAI,YACzBG,EAASD,GAAe,KAC9B,IAAIE,EACApb,EACJ,GAAIwa,EAAOQ,CAAG,IAAMA,GAAO,CAACG,GAAUD,GAAe,OAAQ,CAIzD,GADAE,EAASP,EAAM,IAAIG,CAAG,EAClBI,EAAQ,OAAOA,EAMnB,GAFAA,EAAS,EAAEN,EAAU,IACrBD,EAAM,IAAIG,EAAKI,CAAM,EACjBF,GAAe,MAAO,CAGtB,IADAE,EAAS,IACLpb,EAAQ,EAAGA,EAAQgb,EAAI,OAAQhb,IAC/Bob,GAAUL,EAAWC,EAAIhb,CAAK,CAAC,EAAI,IAEvC6a,EAAM,IAAIG,EAAKI,CAAM,CACzB,CACA,GAAIF,GAAeV,EAAQ,CAEvBY,EAAS,IACT,MAAMC,EAAOb,EAAO,KAAKQ,CAAG,EAAE,KAAK,EACnC,KAAM,CAACP,EAAYza,EAAQqb,EAAK,IAAI,CAAC,GAC5BZ,EAAYO,EAAIhb,CAAK,CAAC,IACvBob,GAAUpb,EAAQ,IAAM+a,EAAWC,EAAIhb,CAAK,CAAC,EAAI,KAGzD6a,EAAM,IAAIG,EAAKI,CAAM,CACzB,CACJ,MACIA,EAASD,EAASH,EAAI,OAAO,EAAIC,GAAQ,SAAWD,EAAI,SAAS,EAAIC,GAAQ,SAAW,KAAK,UAAUD,CAAG,EAAI,GAAKA,EAEvH,OAAOI,CACX,EAGME,EAAiB,IAAI,QAErBC,EAAc,CAAC,EACfC,EAAgB,CAAC,EACjBC,EAAgB,YAEhBC,EAAkB,OAAO,QAAUD,EACnCE,EAAoB,OAAO,UAAYF,EACvCG,EAA2B,IAAIF,GAAmB,OAAO,OAAO,uBAA4BD,EAC5FI,EAAoB,CAAChY,EAAOb,IAAM,CACpC,MAAM8Y,EAAQR,EAAe,IAAIzX,CAAK,EACtC,MAAO,CAEH,IAAI,CAAC4W,EAAYzX,CAAG,GAAKa,EAAM,IAAIb,CAAG,GAAKuY,EAE1CQ,GAAO,CACJ,GAAI,CAACtB,EAAYzX,CAAG,EAAG,CACnB,MAAMgZ,EAAOnY,EAAM,IAAIb,CAAG,EAGpBA,KAAOwY,IACTA,EAAcxY,CAAG,EAAIgZ,GAEzBF,EAAM,CAAC,EAAE9Y,EAAK2X,EAAaqB,EAAMD,CAAI,EAAGC,GAAQT,CAAW,CAC/D,CACJ,EAEAO,EAAM,CAAC,EAEP,IACQ,CAACrB,EAAYzX,CAAG,GAEZA,KAAOwY,EAAsBA,EAAcxY,CAAG,EAG/C,CAACyX,EAAYzX,CAAG,GAAKa,EAAM,IAAIb,CAAG,GAAKuY,CAEtD,CACJ,EASI,IAAIU,EAAS,GACjB,MAAMC,EAAW,IAAID,EAEf,CAACE,GAAeC,CAAc,EAAIV,GAAmB,OAAO,iBAAmB,CACjF,OAAO,iBAAiB,KAAK,MAAM,EACnC,OAAO,oBAAoB,KAAK,MAAM,CAC1C,EAAI,CACApB,EACAA,CACJ,EACM+B,EAAY,IAAI,CAClB,MAAMC,EAAkBX,GAAqB,SAAS,gBACtD,OAAOlB,EAAY6B,CAAe,GAAKA,IAAoB,QAC/D,EACMC,GAAaC,IAEXb,GACA,SAAS,iBAAiB,mBAAoBa,CAAQ,EAE1DL,GAAc,QAASK,CAAQ,EACxB,IAAI,CACHb,GACA,SAAS,oBAAoB,mBAAoBa,CAAQ,EAE7DJ,EAAe,QAASI,CAAQ,CACpC,GAEEC,GAAiBD,GAAW,CAE9B,MAAME,EAAW,IAAI,CACjBT,EAAS,GACTO,EAAS,CACb,EAEMG,EAAY,IAAI,CAClBV,EAAS,EACb,EACA,UAAc,SAAUS,CAAQ,EAChCP,GAAc,UAAWQ,CAAS,EAC3B,IAAI,CACPP,EAAe,SAAUM,CAAQ,EACjCN,EAAe,UAAWO,CAAS,CACvC,CACJ,EACMC,GAAS,CACX,WACA,WACJ,EACMC,GAAuB,CACzB,aACA,gBACJ,EAEMC,GAAkB,CAAC,EAAM,MACzBC,GAAY,CAACrB,GAAmB,SAAU,OAE1CsB,GAAO9C,GAAI0B,EAAyB,EAAI,OAAO,sBAAyB1B,CAAC,EAAI,WAAWA,EAAG,CAAC,EAI5F+C,EAA4BF,GAAY,YAAY,kBAEpDG,EAAsB,OAAO,WAAc,aAAe,UAAU,WAEpEC,EAAiB,CAACJ,IAAaG,IAAwB,CACzD,UACA,IACJ,EAAE,SAASA,EAAoB,aAAa,GAAKA,EAAoB,UAE/D,EAAala,GAAM,CACrB,GAAI0X,EAAW1X,CAAG,EACd,GAAI,CACAA,EAAMA,EAAI,CACd,OAASoa,EAAK,CAEVpa,EAAM,EACV,CAIJ,MAAMqa,EAAOra,EAEb,SAAM,OAAOA,GAAO,SAAWA,GAAO,MAAM,QAAQA,CAAG,EAAIA,EAAI,OAASA,GAAO+X,EAAW/X,CAAG,EAAI,GAC1F,CACHA,EACAqa,CACJ,CACJ,EAGA,IAAIC,EAAc,EAClB,MAAMC,EAAe,IAAI,EAAED,EAErBE,EAAc,EACdC,EAAkB,EAClBC,EAAe,EAGrB,IAAIC,EAAS,CACX,UAAW,KACX,uBAJ6B,EAK7B,YAAaH,EACb,aAAcE,EACd,gBAAiBD,CACnB,EAEA,SAAeG,MAAkBP,EAAM,iCACnC,KAAM,CAACxZ,EAAOf,EAAM+a,EAAOC,CAAK,EAAIT,EAG9BU,EAAUpD,EAAa,CACzB,cAAe,GACf,aAAc,EAClB,EAAG,OAAOmD,GAAU,UAAY,CAC5B,WAAYA,CAChB,EAAIA,GAAS,CAAC,CAAC,EACf,IAAIE,EAAgBD,EAAQ,cAC5B,MAAME,GAAwBF,EAAQ,gBACtC,IAAIG,EAAiBH,EAAQ,eAC7B,MAAMI,GAAmBC,IACd,OAAOH,IAA0B,WAAaA,GAAsBG,EAAK,EAAIH,KAA0B,GAE5GI,GAAeN,EAAQ,aAG7B,GAAIrD,EAAW5X,CAAI,EAAG,CAClB,MAAMwb,GAAYxb,EACZyb,GAAc,CAAC,EACfC,GAAK3a,EAAM,KAAK,EACtB,UAAWb,MAAOwb,GAEd,CAAC,iBAAiB,KAAKxb,EAAG,GAAKsb,GAAUza,EAAM,IAAIb,EAAG,EAAE,EAAE,GACtDub,GAAY,KAAKvb,EAAG,EAG5B,OAAO,QAAQ,IAAIub,GAAY,IAAIE,EAAW,CAAC,CACnD,CACA,OAAOA,GAAY3b,CAAI,EACvB,SAAe2b,GAAYC,GAAI,iCAE3B,KAAM,CAAC1b,EAAG,EAAI,EAAU0b,EAAE,EAC1B,GAAI,CAAC1b,GAAK,OACV,KAAM,CAAC2b,GAAKC,EAAG,EAAI/C,EAAkBhY,EAAOb,EAAG,EACzC,CAAC6b,GAAoBC,EAAUC,GAAOC,EAAO,EAAI1D,EAAe,IAAIzX,CAAK,EACzEob,GAAkB,IAAI,CACxB,MAAMC,GAAeL,GAAmB7b,EAAG,EAE3C,OADmB0X,EAAWqD,EAAQ,UAAU,EAAIA,EAAQ,WAAWY,GAAI,EAAE,KAAMD,EAAE,EAAIX,EAAQ,aAAe,MAI5G,OAAOgB,GAAM/b,EAAG,EAChB,OAAOgc,GAAQhc,EAAG,EACdkc,IAAgBA,GAAa,CAAC,GACvBA,GAAa,CAAC,EAAExB,CAAY,EAAE,KAAK,IAAIiB,GAAI,EAAE,IAAI,EAGzDA,GAAI,EAAE,IACjB,EAEA,GAAItB,EAAK,OAAS,EAEd,OAAO4B,GAAgB,EAE3B,IAAIE,GAAOtB,EACPO,GAEJ,MAAMgB,GAAmB7B,EAAa,EACtCuB,EAAS9b,EAAG,EAAI,CACZoc,GACA,CACJ,EACA,MAAMC,GAAoB,CAAC5E,EAAYyD,CAAc,EAC/CpC,GAAQ6C,GAAI,EAIZW,GAAgBxD,GAAM,KACtByD,GAAczD,GAAM,GACpB0D,GAAgB/E,EAAY8E,EAAW,EAAID,GAAgBC,GAUjE,GARIF,KACAnB,EAAiBxD,EAAWwD,CAAc,EAAIA,EAAesB,GAAeF,EAAa,EAAIpB,EAE7FU,GAAI,CACA,KAAMV,EACN,GAAIsB,EACR,CAAC,GAED9E,EAAWyE,EAAI,EAEf,GAAI,CACAA,GAAOA,GAAKK,EAAa,CAC7B,OAASpC,GAAK,CAEVgB,GAAQhB,EACZ,CAGJ,GAAI+B,IAAQvE,EAAcuE,EAAI,EAS1B,GANAA,GAAO,MAAMA,GAAK,MAAO/B,IAAM,CAC3BgB,GAAQhB,EACZ,CAAC,EAIGgC,KAAqBN,EAAS9b,EAAG,EAAE,CAAC,EAAG,CACvC,GAAIob,GAAO,MAAMA,GACjB,OAAOe,EACX,MAAWf,IAASiB,IAAqBlB,GAAgBC,EAAK,IAG1DJ,EAAgB,GAEhBY,GAAI,CACA,KAAMY,GACN,GAAIjF,CACR,CAAC,GAIT,GAAIyD,GACI,CAACI,GAED,GAAI1D,EAAWsD,CAAa,EAAG,CAC3B,MAAMyB,GAAqBzB,EAAcmB,GAAMK,EAAa,EAC5DZ,GAAI,CACA,KAAMa,GACN,MAAOlF,EACP,GAAIA,CACR,CAAC,CACL,MAEIqE,GAAI,CACA,QACA,MAAOrE,EACP,GAAIA,CACR,CAAC,EAeb,GAVAuE,EAAS9b,EAAG,EAAE,CAAC,EAAIua,EAAa,EAEhC,QAAQ,QAAQ0B,GAAgB,CAAC,EAAE,KAAK,IAAI,CAGxCL,GAAI,CACA,GAAIrE,CACR,CAAC,CACL,CAAC,EAEG6D,GAAO,CACP,GAAIC,GAAc,MAAMD,GACxB,MACJ,CACA,OAAOe,EACX,GACJ,GAEA,MAAMO,EAAoB,CAACR,EAAcjE,IAAO,CAC5C,UAAUjY,KAAOkc,EACTA,EAAalc,CAAG,EAAE,CAAC,GAAGkc,EAAalc,CAAG,EAAE,CAAC,EAAEiY,CAAI,CAE3D,EACM0E,EAAY,CAACC,EAAU7B,IAAU,CAMnC,GAAI,CAACzC,EAAe,IAAIsE,CAAQ,EAAG,CAC/B,MAAMpgB,EAAOmb,EAAakC,GAAsBkB,CAAO,EAGjDc,EAAqB,CAAC,EACtBgB,EAASjC,GAAe,KAAKrD,EAAWqF,CAAQ,EACtD,IAAIE,EAAUxF,EACd,MAAMyF,EAAgB,CAAC,EACjBC,GAAY,CAAChd,GAAKwZ,KAAW,CAC/B,MAAMyD,GAAOF,EAAc/c,EAAG,GAAK,CAAC,EACpC,SAAcA,EAAG,EAAIid,GACrBA,GAAK,KAAKzD,EAAQ,EACX,IAAIyD,GAAK,OAAOA,GAAK,QAAQzD,EAAQ,EAAG,CAAC,CACpD,EACM0D,EAAS,CAACld,GAAK3D,GAAO2c,KAAO,CAC/B4D,EAAS,IAAI5c,GAAK3D,EAAK,EACvB,MAAM4gB,GAAOF,EAAc/c,EAAG,EAC9B,GAAIid,GACA,UAAWE,MAAMF,GACbE,GAAG9gB,GAAO2c,EAAI,CAG1B,EACMoE,GAAe,IAAI,CACrB,GAAI,CAAC9E,EAAe,IAAIsE,CAAQ,IAE5BtE,EAAe,IAAIsE,EAAU,CACzBf,EACA,CAAC,EACD,CAAC,EACD,CAAC,EACDgB,EACAK,EACAF,EACJ,CAAC,EACG,CAACjD,IAAW,CAOZ,MAAMsD,GAAe7gB,EAAK,UAAU,WAAW,KAAK+a,EAAWmF,EAAkB,KAAKnF,EAAWsE,EAAoBrB,CAAW,CAAC,CAAC,EAC5H8C,GAAmB9gB,EAAK,cAAc,WAAW,KAAK+a,EAAWmF,EAAkB,KAAKnF,EAAWsE,EAAoBpB,CAAe,CAAC,CAAC,EAC9IqC,EAAU,IAAI,CACVO,IAAgBA,GAAa,EAC7BC,IAAoBA,GAAiB,EAIrChF,EAAe,OAAOsE,CAAQ,CAClC,CACJ,CAER,EACA,UAAa,EAMN,CACHA,EACAC,EACAO,GACAN,CACJ,CACJ,CACA,MAAO,CACHF,EACAtE,EAAe,IAAIsE,CAAQ,EAAE,CAAC,CAClC,CACJ,EAGMW,GAAe,CAACC,EAAGC,EAAIC,EAAQC,EAAYnhB,IAAO,CACpD,MAAMohB,EAAgBF,EAAO,gBACvBG,EAAoBrhB,EAAK,WAEzBshB,GAAU,CAAC,GAAG,KAAK,OAAO,EAAI,KAAQ,IAAMD,EAAoB,EAAIA,EAAoB,KAAOH,EAAO,mBACxG,CAACjG,EAAYmG,CAAa,GAAKC,EAAoBD,GAGvD,WAAWD,EAAYG,GAASthB,CAAI,CACxC,EACMuhB,EAAU,CAACxB,EAAayB,IAAUjG,EAAWwE,CAAW,GAAKxE,EAAWiG,CAAO,EAE/E,CAACnd,EAAOgc,CAAM,EAAIF,EAAU,IAAI,GAAK,EAErCsB,EAAgBtG,EAAa,CAE/B,cAAeL,EACf,UAAWA,EACX,QAASA,EACT,gBACA,YAAaA,EAEb,kBAAmB,GACnB,sBAAuB,GACvB,kBAAmB,GACnB,mBAAoB,GAEpB,mBAAoB6C,EAAiB,IAAQ,IAC7C,sBAAuB,EAAI,IAC3B,iBAAkB,EAAI,IACtB,eAAgBA,EAAiB,IAAO,IAExC,UACA,SAAU,IAAI,GACd,QACA,SACA,SAAU,CAAC,CACf,EACAP,EAAM,EAEAsE,EAAe,CAACtV,EAAGxN,IAAI,CAEzB,MAAMsN,EAAIiP,EAAa/O,EAAGxN,CAAC,EAE3B,GAAIA,EAAG,CACH,KAAM,CAAE,IAAK+iB,EAAI,SAAUC,CAAG,EAAIxV,EAC5B,CAAE,IAAKyV,EAAI,SAAUC,CAAG,EAAIljB,EAC9B+iB,GAAME,IACN3V,EAAE,IAAMyV,EAAG,OAAOE,CAAE,GAEpBD,GAAME,IACN5V,EAAE,SAAWiP,EAAayG,EAAIE,CAAE,EAExC,CACA,OAAO5V,CACX,EAEM6V,MAAmB,iBAAc,CAAC,CAAC,EACnCC,GAAazd,GAAQ,CACvB,KAAM,CAAE,OAAM,EAAIA,EACZ0d,KAAe,cAAWF,EAAgB,EAC1CG,EAAqBhH,EAAWrb,CAAK,EACrCqhB,KAAS,WAAQ,IAAIgB,EAAqBriB,EAAMoiB,CAAY,EAAIpiB,EAAO,CACzEqiB,EACAD,EACApiB,CACJ,CAAC,EAEKsiB,KAAiB,WAAQ,IAAID,EAAqBhB,EAASQ,EAAaO,EAAcf,CAAM,EAAG,CACjGgB,EACAD,EACAf,CACJ,CAAC,EAEKd,EAAWc,GAAUA,EAAO,SAE5BkB,MAAkB,UAAOrH,CAAS,EACpCqF,GAAY,CAACgC,GAAgB,UAC7BA,GAAgB,QAAUjC,EAAUC,EAAS+B,EAAe,OAAS9d,CAAK,EAAG6c,CAAM,GAEvF,MAAMmB,EAAeD,GAAgB,QAErC,OAAIC,IACAF,EAAe,MAAQE,EAAa,CAAC,EACrCF,EAAe,OAASE,EAAa,CAAC,GAG1C5E,EAA0B,IAAI,CAC1B,GAAI4E,EACA,SAAa,CAAC,GAAKA,EAAa,CAAC,EAAE,EAC5BA,EAAa,CAAC,CAE7B,EAAG,CAAC,CAAC,KACE,iBAAcN,GAAiB,SAAU5G,EAAa5W,EAAO,CAChE,MAAO4d,CACX,CAAC,CAAC,CACN,EAEMG,GAAkB,QAGlBC,GAAiBrG,GAAmB,OAAO,qBAC3CsG,GAAMD,GAAiB,OAAO,qBAAuB,CAAC,EACtDE,GAAgB,IAAI,CAClBF,KAEA,OAAO,uBAAyB,EAExC,EAEMG,GAAa7E,GACR3C,EAAW2C,EAAK,CAAC,CAAC,EAAI,CACzBA,EAAK,CAAC,EACNA,EAAK,CAAC,EACNA,EAAK,CAAC,GAAK,CAAC,CAChB,EAAI,CACAA,EAAK,CAAC,EACN,MACCA,EAAK,CAAC,IAAM,KAAOA,EAAK,CAAC,EAAIA,EAAK,CAAC,IAAM,CAAC,CAC/C,EAGE8E,GAAe,IACVxH,EAAasG,KAAe,cAAWM,EAAgB,CAAC,EAG7Da,GAAU,CAACC,EAAMC,IAAU,CAC7B,KAAM,CAACtf,EAAKuf,CAAK,EAAI,EAAUF,CAAI,EAC7B,CAAC,CAAE,CAAE,CAAErD,CAAO,EAAI1D,EAAe,IAAIzX,CAAK,EAEhD,GAAImb,EAAQhc,CAAG,EAAG,OAAOgc,EAAQhc,CAAG,EACpC,MAAMwf,EAAMF,EAAQC,CAAK,EACzB,SAAQvf,CAAG,EAAIwf,EACRA,CACX,EACMC,GAAcC,GAAa,CAACL,EAAMM,EAAUjC,IAenCgC,EAAWL,EAbFM,IAAa,IAAItF,IAAO,CACpC,KAAM,CAACra,CAAG,EAAI,EAAUqf,CAAI,EACtB,CAAC,CAAE,CAAE,CAAErD,EAAO,EAAI1D,EAAe,IAAIzX,CAAK,EAChD,GAAIb,EAAI,WAAW8e,EAAe,EAG9B,OAAOa,EAAS,GAAGtF,CAAI,EAE3B,MAAMmF,EAAMxD,GAAQhc,CAAG,EACvB,OAAIyX,EAAY+H,CAAG,EAAUG,EAAS,GAAGtF,CAAI,GAC7C,OAAO2B,GAAQhc,CAAG,EACXwf,EACX,GACiC9B,CAAM,EAGzCkC,GAAsBZ,GAAI,OAAOS,EAAU,EAI3CI,GAAYC,GACP,YAAuBzF,EAAM,CAEhC,MAAM0F,EAAiBZ,GAAa,EAE9B,CAACnf,EAAKmd,EAAI6C,CAAO,EAAId,GAAU7E,CAAI,EAEnCqD,GAASQ,EAAa6B,EAAgBC,CAAO,EAEnD,IAAIC,EAAOH,EACX,KAAM,CAAE,MAAI,EAAIpC,GACV+B,IAAcT,IAAO,CAAC,GAAG,OAAOY,EAAmB,EACzD,QAAQ7jB,GAAI0jB,GAAW,OAAQ1jB,MAC3BkkB,EAAOR,GAAW1jB,EAAC,EAAEkkB,CAAI,EAE7B,OAAOA,EAAKjgB,EAAKmd,GAAMO,GAAO,SAAW,KAAMA,EAAM,CACzD,EAKEwC,GAAoB,CAAClgB,EAAKmgB,EAAW3G,IAAW,CAClD,MAAM4G,EAAoBD,EAAUngB,CAAG,IAAMmgB,EAAUngB,CAAG,EAAI,CAAC,GAC/D,SAAkB,KAAKwZ,CAAQ,EACxB,IAAI,CACP,MAAMxc,EAAQojB,EAAkB,QAAQ5G,CAAQ,EAC5Cxc,GAAS,IAETojB,EAAkBpjB,CAAK,EAAIojB,EAAkBA,EAAkB,OAAS,CAAC,EACzEA,EAAkB,IAAI,EAE9B,CACJ,EAGMC,GAAiB,CAACC,EAAQb,IACrB,IAAIpF,IAAO,CACd,KAAM,CAACra,EAAKmd,EAAIO,CAAM,EAAIwB,GAAU7E,CAAI,EAClCkG,GAAQ7C,EAAO,KAAO,CAAC,GAAG,OAAO+B,CAAU,EACjD,OAAOa,EAAOtgB,EAAKmd,EAAI,SAChBO,GADgB,CAEnB,IAAK6C,CACT,EAAC,CACL,EAGJtB,GAAc,EC1pBd,MAAMuB,GAAsBxgB,GAAM,UAAUA,CAAG,EAAE,CAAC,EAG5C,GAAM,EAAa,MAASygB,GAAU,CACxC,GAAIA,EAAQ,SAAW,UACnB,MAAMA,EACH,GAAIA,EAAQ,SAAW,YAC1B,OAAOA,EAAQ,MACZ,MAAIA,EAAQ,SAAW,WACpBA,EAAQ,QAEdA,EAAQ,OAAS,UACjBA,EAAQ,KAAM/X,GAAI,CACd+X,EAAQ,OAAS,YACjBA,EAAQ,MAAQ/X,CACpB,EAAIyF,GAAI,CACJsS,EAAQ,OAAS,WACjBA,EAAQ,OAAStS,CACrB,CAAC,EACKsS,EAEd,GACMC,GAAc,CAChB,OAAQ,EACZ,EACMC,GAAgB,CAAC7gB,EAAMwf,EAAS5B,IAAS,CAC3C,KAAM,CAAE,QAAO,UAAS,WAAU,eAAc,qBAAmB,oBAAmB,mBAAiB,qBAAmB,sBAAoB,mBAAiB,EAAIA,EAC7J,CAAC7B,GAAoBC,GAAUC,GAAOC,EAAO,EAAI1D,EAAe,IAAIzX,CAAK,EAKzE,CAACb,EAAKuf,EAAK,EAAI,EAAUzf,CAAI,EAE7B8gB,MAAoB,UAAO,EAAK,EAGhCC,MAAe,UAAO,EAAK,EAE3BC,MAAS,UAAO9gB,CAAG,EACnB+gB,MAAa,UAAOzB,CAAO,EAC3B0B,MAAY,UAAOtD,CAAM,EACzBuD,GAAY,IAAID,GAAU,QAC1BE,GAAW,IAAID,GAAU,EAAE,UAAU,GAAKA,GAAU,EAAE,SAAS,EAC/D,CAACE,GAAUC,GAAUC,GAAgBC,EAAe,EAAIzI,EAAkBhY,EAAOb,CAAG,EACpFuhB,MAAoB,UAAO,CAAC,CAAC,EAAE,QAC/BC,GAAW/J,EAAYgK,CAAY,EAAI/D,EAAO,SAAS1d,CAAG,EAAIyhB,EAC9DC,GAAU,CAAC1I,GAAM2I,KAAU,CAC7B,UAAUnE,MAAK+D,GAAkB,CAC7B,MAAMnT,GAAIoP,GACV,GAAIpP,KAAM,QACN,GAAI,CAAC2P,EAAQ/E,GAAK5K,EAAC,EAAGuT,GAAQvT,EAAC,CAAC,IACxB,CAACqJ,EAAYuB,GAAK5K,EAAC,CAAC,GAGpB,CAAC2P,EAAQ6D,GAAcD,GAAQvT,EAAC,CAAC,GACjC,MAAO,WAIXuT,GAAQvT,EAAC,IAAM4K,GAAK5K,EAAC,EACrB,MAAO,EAGnB,CACA,MAAO,EACX,EACMyT,MAAc,WAAQ,IAAI,CAC5B,MAAMC,GACE,CAAC9hB,GACD,CAACsf,EAAgB,GAEhB7H,EAAYsK,EAAiB,EAE9Bd,GAAU,EAAE,SAAS,GACrBe,EAAiB,GAChBvK,EAAYwK,CAAiB,EAC3B,GADqCA,EAJAF,GAQ1CG,GAAoBpJ,IAAQ,CAE9B,MAAMqJ,GAAWxK,EAAamB,EAAK,EAEnC,OADA,OAAOqJ,GAAS,GACXL,GAGE,IACH,aAAc,GACd,UAAW,IACRK,IALIA,EAOf,EACMC,GAAajB,GAAS,EACtBkB,GAAcf,GAAgB,EAC9BgB,GAAiBJ,GAAiBE,EAAU,EAC5CG,GAAiBH,KAAeC,GAAcC,GAAiBJ,GAAiBG,EAAW,EAIjG,IAAIG,GAAoBF,GACxB,MAAO,CACH,IAAI,CACA,MAAMG,GAAcP,GAAiBf,GAAS,CAAC,EAE/C,OADsBO,GAAQe,GAAaD,EAAiB,GAYxDA,GAAkB,KAAOC,GAAY,KACrCD,GAAkB,UAAYC,GAAY,UAC1CD,GAAkB,aAAeC,GAAY,aAC7CD,GAAkB,MAAQC,GAAY,MAC/BD,KAEPA,GAAoBC,GACbA,GAEf,EACA,IAAIF,EACR,CAEJ,EAAG,CACC1hB,EACAb,CACJ,CAAC,EAEK0iB,MAAS,2BAAqB,eAAalJ,IAAW6H,GAAerhB,EAAK,CAAC2hB,GAAS3I,KAAO,CACpF0I,GAAQ1I,GAAM2I,EAAO,GAAGnI,GAAS,CAC1C,CAAC,EACL,CACI3Y,EACAb,CACJ,CAAC,EAAG6hB,GAAY,CAAC,EAAGA,GAAY,CAAC,CAAC,EAC5Bc,GAAiB,CAAC/B,GAAkB,QACpCgC,GAAiB/G,GAAmB7b,CAAG,GAAK6b,GAAmB7b,CAAG,EAAE,OAAS,EAC7EoiB,GAAaM,GAAO,KACpBvG,GAAO1E,EAAY2K,EAAU,EAAIZ,GAAWY,GAC5ChH,GAAQsH,GAAO,MAEfG,MAAe,UAAO1G,EAAI,EAC1ByF,GAAekB,GAAmBrL,EAAY2K,EAAU,EAAIS,GAAa,QAAUT,GAAajG,GAIhG4G,GAEEH,IAAkB,CAACnL,EAAY2D,EAAK,EAAU,GAE9CuH,IAAkB,CAAClL,EAAYsK,EAAiB,EAAUA,GAE1Dd,GAAU,EAAE,SAAS,EAAU,GAI/Be,EAAiBvK,EAAY0E,EAAI,EAAI,GAAQ8F,EAG1CxK,EAAY0E,EAAI,GAAK8F,EAI1Be,GAAyB,CAAC,EAAEhjB,GAAOsf,GAAWqD,IAAkBI,IAChEE,GAAexL,EAAYiL,GAAO,YAAY,EAAIM,GAAyBN,GAAO,aAClFQ,GAAYzL,EAAYiL,GAAO,SAAS,EAAIM,GAAyBN,GAAO,UAG5E/E,MAAa,eAAmBwF,IAAiB,yBACnD,MAAMC,GAAiBrC,GAAW,QAClC,GAAI,CAAC/gB,GAAO,CAACojB,IAAkBvC,GAAa,SAAWI,GAAU,EAAE,SAAS,EACxE,MAAO,GAEX,IAAIjD,GACAqF,GACAC,GAAU,GACd,MAAM9mB,GAAO2mB,IAAkB,CAAC,EAG1BI,GAAwB,CAACxH,GAAM/b,CAAG,GAAK,CAACxD,GAAK,OAW5CgnB,GAAoB,IACnB1J,GACO,CAAC+G,GAAa,SAAW7gB,IAAQ8gB,GAAO,SAAWF,GAAkB,QAEzE5gB,IAAQ8gB,GAAO,QAGpB2C,GAAa,CACf,aAAc,GACd,UAAW,EACf,EACMC,GAA8B,IAAI,CACpCtC,GAASqC,EAAU,CACvB,EACME,GAAe,IAAI,CAErB,MAAMC,GAAc7H,GAAM/b,CAAG,EACzB4jB,IAAeA,GAAY,CAAC,IAAMP,IAClC,OAAOtH,GAAM/b,CAAG,CAExB,EAEM6jB,GAAe,CACjB,aAAc,EAClB,EAGIpM,EAAY0J,GAAS,EAAE,IAAI,IAC3B0C,GAAa,UAAY,IAE7B,GAAI,CAgCA,GA/BIN,KACAnC,GAASyC,EAAY,EAGjBnG,EAAO,gBAAkBjG,EAAY0J,GAAS,EAAE,IAAI,GACpD,WAAW,IAAI,CACPmC,IAAWE,GAAkB,GAC7BvC,GAAU,EAAE,cAAcjhB,EAAK0d,CAAM,CAE7C,EAAGA,EAAO,cAAc,EAI5B3B,GAAM/b,CAAG,EAAI,CACTojB,GAAe7D,EAAK,EACpBhF,EAAa,CACjB,GAEJ,CAACyD,GAASqF,EAAO,EAAItH,GAAM/b,CAAG,EAC9Bge,GAAU,MAAMA,GACZuF,IAGA,WAAWI,GAAcjG,EAAO,gBAAgB,EAQhD,CAAC3B,GAAM/b,CAAG,GAAK+b,GAAM/b,CAAG,EAAE,CAAC,IAAMqjB,GACjC,OAAIE,IACIC,GAAkB,GAClBvC,GAAU,EAAE,YAAYjhB,CAAG,EAG5B,GAGXyjB,GAAW,MAAQlM,EAanB,MAAMuM,GAAehI,GAAS9b,CAAG,EACjC,GAAI,CAACyX,EAAYqM,EAAY,IAC5BT,IAAWS,GAAa,CAAC,GAC1BT,IAAWS,GAAa,CAAC,GACzBA,GAAa,CAAC,IAAM,GAChB,UAA4B,EACxBP,IACIC,GAAkB,GAClBvC,GAAU,EAAE,YAAYjhB,CAAG,EAG5B,GAIX,MAAM+jB,GAAY5C,GAAS,EAAE,KAG7BsC,GAAW,KAAO1F,EAAQgG,GAAW/F,EAAO,EAAI+F,GAAY/F,GAExDuF,IACIC,GAAkB,GAClBvC,GAAU,EAAE,UAAUjD,GAAShe,EAAK0d,CAAM,CAGtD,OAAStD,GAAK,CACVuJ,GAAa,EACb,MAAMK,GAAgB/C,GAAU,EAC1B,CAAE,qBAAmB,EAAI+C,GAE1BA,GAAc,SAAS,IAExBP,GAAW,MAAQrJ,GAGfmJ,IAAyBC,GAAkB,IAC3CQ,GAAc,QAAQ5J,GAAKpa,EAAKgkB,EAAa,GACzCC,KAAuB,IAAQvM,EAAWuM,EAAkB,GAAKA,GAAmB7J,EAAG,KACnF,CAAC6G,GAAU,EAAE,mBAAqB,CAACA,GAAU,EAAE,uBAAyBC,GAAS,IAIjF8C,GAAc,aAAa5J,GAAKpa,EAAKgkB,GAAgBlJ,IAAQ,CACzD,MAAMoB,GAAeL,GAAmB7b,CAAG,EACvCkc,IAAgBA,GAAa,CAAC,GAC9BA,GAAa,CAAC,EAAE,EAAiB,uBAAwBpB,EAAK,CAEtE,EAAG,CACC,YAAate,GAAK,YAAc,GAAK,EACrC,OAAQ,EACZ,CAAC,GAKrB,CAEA,UAAU,GAEVknB,GAA4B,EACrB,EACX,GAWA,CACI1jB,EACAa,CACJ,CAAC,EAGKqjB,MAAc,eACpB,IAAI7J,KACOO,GAAe/Z,EAAOigB,GAAO,QAAS,GAAGzG,EAAI,EAExD,CAAC,CAAC,EA2GF,GAzGAJ,EAA0B,IAAI,CAC1B8G,GAAW,QAAUzB,EACrB0B,GAAU,QAAUtD,EAGfjG,EAAY2K,EAAU,IACvBS,GAAa,QAAUT,GAE/B,CAAC,EAEDnI,EAA0B,IAAI,CAC1B,GAAI,CAACja,EAAK,OACV,MAAMmkB,GAAiBxG,GAAW,KAAKpG,EAAWmJ,EAAW,EAG7D,IAAI0D,GAAyB,EAmB7B,MAAMC,GAAcnE,GAAkBlgB,EAAK6b,GAlBtB,CAAC5D,GAAMzb,GAAO,CAAC,IAAI,CACpC,GAAIyb,IAAQ,EAAiB,YAAa,CACtC,MAAMqM,GAAM,KAAK,IAAI,EACjBrD,GAAU,EAAE,mBAAqBqD,GAAMF,IAA0BlD,GAAS,IAC1EkD,GAAyBE,GAAMrD,GAAU,EAAE,sBAC3CkD,GAAe,EAEvB,SAAWlM,IAAQ,EAAiB,gBAC5BgJ,GAAU,EAAE,uBAAyBC,GAAS,GAC9CiD,GAAe,MAEhB,IAAIlM,IAAQ,EAAiB,aAChC,OAAO0F,GAAW,EACf,GAAI1F,IAAQ,EAAiB,uBAChC,OAAO0F,GAAWnhB,EAAI,EAG9B,CAC2E,EAE3E,UAAa,QAAU,GACvBskB,GAAO,QAAU9gB,EACjB4gB,GAAkB,QAAU,GAE5BQ,GAAS,CACL,GAAI7B,EACR,CAAC,EAEGwD,KACItL,EAAY0E,EAAI,GAAKpC,GAErBoK,GAAe,EAIfnK,GAAImK,EAAc,GAGnB,IAAI,CAEPtD,GAAa,QAAU,GACvBwD,GAAY,CAChB,CACJ,EAAG,CACCrkB,CACJ,CAAC,EAEDia,EAA0B,IAAI,CAC1B,IAAIsK,GACJ,SAAStE,IAAO,CAGZ,MAAMuE,GAAW9M,EAAW+M,EAAe,EAAIA,GAAgBtD,GAAS,EAAE,IAAI,EAAIsD,GAI9ED,IAAYD,KAAU,KACtBA,GAAQ,WAAWG,GAASF,EAAQ,EAE5C,CACA,SAASE,IAAU,CAGX,CAACvD,GAAS,EAAE,QAAUwD,IAAqB1D,GAAU,EAAE,UAAU,KAAO2D,IAAsB3D,GAAU,EAAE,SAAS,GACnHtD,GAAW+C,EAAW,EAAE,KAAKT,EAAI,EAGjCA,GAAK,CAEb,CACA,UAAK,EACE,IAAI,CACHsE,KACA,aAAaA,EAAK,EAClBA,GAAQ,GAEhB,CACJ,EAAG,CACCE,GACAE,GACAC,GACA5kB,CACJ,CAAC,KAED,iBAAc4hB,EAAY,EAKtBI,GAAYvK,EAAY0E,EAAI,GAAKnc,EAAK,CAItC,GAAI,CAAC8Z,IAAmBC,GACpB,MAAM,IAAI,MAAM,uDAAuD,EAG3EgH,GAAW,QAAUzB,EACrB0B,GAAU,QAAUtD,EACpBmD,GAAa,QAAU,GACvB,MAAMrB,GAAMxD,GAAQhc,CAAG,EACvB,GAAI,CAACyX,EAAY+H,EAAG,EAAG,CACnB,MAAMiB,GAAUyD,GAAY1E,EAAG,EAC/B,GAAIiB,EAAO,CACf,CACA,GAAIhJ,EAAY2D,EAAK,EAAG,CACpB,MAAMqF,GAAU9C,GAAW+C,EAAW,EACjCjJ,EAAYmK,EAAY,IACzBnB,GAAQ,OAAS,YACjBA,GAAQ,MAAQ,IAEpB,GAAIA,EAAO,CACf,KACI,OAAMrF,EAEd,CACA,MAAO,CACH,OAAQ8I,GACR,IAAI,MAAQ,CACR,UAAkB,KAAO,GAClBtC,EACX,EACA,IAAI,OAAS,CACT,UAAkB,MAAQ,GACnBxG,EACX,EACA,IAAI,cAAgB,CAChB,UAAkB,aAAe,GAC1B6H,EACX,EACA,IAAI,WAAa,CACb,UAAkB,UAAY,GACvBC,EACX,CACJ,CACJ,EACM,GAAY1L,EAAO,eAAegH,GAAa,eAAgB,CACjE,MAAOP,CACX,CAAC,EAeSqC,GAAST,GAASc,EAAa,C", "sources": ["webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@ant-design/colors/es/generate.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@ant-design/colors/es/presets.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/typing/layoutToken.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/utils/merge.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/ar_EG.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/ca_ES.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/cs_CZ.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/de_DE.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/en_GB.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/en_US.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/es_ES.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/fa_IR.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/fr_FR.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/he_IL.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/hr_HR.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/id_ID.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/it_IT.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/ja_JP.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/ko_KR.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/mn_MN.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/ms_MY.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/pl_PL.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/pt_BR.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/ru_RU.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/sk_SK.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/sr_RS.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/th_TH.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/tr_TR.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/uk_UA.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/uz_UZ.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/vi_VN.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/zh_CN.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/zh_TW.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/nl_NL.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/ro_RO.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/locale/sv_SE.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/intl.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/useStyle/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-provider/es/useStyle/token.js", "webpack://labwise-web/./node_modules/@ant-design/pro-utils/es/compareVersions/index.js", "webpack://labwise-web/./node_modules/antd/es/_util/getRenderPropValue.js", "webpack://labwise-web/./node_modules/antd/es/_util/hooks/useForceUpdate.js", "webpack://labwise-web/./node_modules/antd/es/_util/responsiveObserver.js", "webpack://labwise-web/./node_modules/antd/es/avatar/AvatarContext.js", "webpack://labwise-web/./node_modules/antd/es/avatar/style/index.js", "webpack://labwise-web/./node_modules/antd/es/avatar/avatar.js", "webpack://labwise-web/./node_modules/antd/es/avatar/group.js", "webpack://labwise-web/./node_modules/antd/es/avatar/index.js", "webpack://labwise-web/./node_modules/antd/es/grid/hooks/useBreakpoint.js", "webpack://labwise-web/./node_modules/antd/es/popover/PurePanel.js", "webpack://labwise-web/./node_modules/antd/es/popover/index.js", "webpack://labwise-web/./node_modules/antd/es/popover/style/index.js", "webpack://labwise-web/./node_modules/omit.js/es/index.js", "webpack://labwise-web/./node_modules/swr/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.min.js", "webpack://labwise-web/./node_modules/swr/node_modules/use-sync-external-store/shim/index.js", "webpack://labwise-web/./node_modules/swr/dist/_internal/index.mjs", "webpack://labwise-web/./node_modules/swr/dist/core/index.mjs"], "sourcesContent": ["import { inputToRGB, rgbToHex, rgbToHsv } from '@ctrl/tinycolor';\nvar hueStep = 2; // 色相阶梯\nvar saturationStep = 0.16; // 饱和度阶梯，浅色部分\nvar saturationStep2 = 0.05; // 饱和度阶梯，深色部分\nvar brightnessStep1 = 0.05; // 亮度阶梯，浅色部分\nvar brightnessStep2 = 0.15; // 亮度阶梯，深色部分\nvar lightColorCount = 5; // 浅色数量，主色上\nvar darkColorCount = 4; // 深色数量，主色下\n// 暗色主题颜色映射关系表\nvar darkColorMap = [{\n  index: 7,\n  opacity: 0.15\n}, {\n  index: 6,\n  opacity: 0.25\n}, {\n  index: 5,\n  opacity: 0.3\n}, {\n  index: 5,\n  opacity: 0.45\n}, {\n  index: 5,\n  opacity: 0.65\n}, {\n  index: 5,\n  opacity: 0.85\n}, {\n  index: 4,\n  opacity: 0.9\n}, {\n  index: 3,\n  opacity: 0.95\n}, {\n  index: 2,\n  opacity: 0.97\n}, {\n  index: 1,\n  opacity: 0.98\n}];\n// Wrapper function ported from TinyColor.prototype.toHsv\n// Keep it here because of `hsv.h * 360`\nfunction toHsv(_ref) {\n  var r = _ref.r,\n    g = _ref.g,\n    b = _ref.b;\n  var hsv = rgbToHsv(r, g, b);\n  return {\n    h: hsv.h * 360,\n    s: hsv.s,\n    v: hsv.v\n  };\n}\n\n// Wrapper function ported from TinyColor.prototype.toHexString\n// Keep it here because of the prefix `#`\nfunction toHex(_ref2) {\n  var r = _ref2.r,\n    g = _ref2.g,\n    b = _ref2.b;\n  return \"#\".concat(rgbToHex(r, g, b, false));\n}\n\n// Wrapper function ported from TinyColor.prototype.mix, not treeshakable.\n// Amount in range [0, 1]\n// Assume color1 & color2 has no alpha, since the following src code did so.\nfunction mix(rgb1, rgb2, amount) {\n  var p = amount / 100;\n  var rgb = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b\n  };\n  return rgb;\n}\nfunction getHue(hsv, i, light) {\n  var hue;\n  // 根据色相不同，色相转向不同\n  if (Math.round(hsv.h) >= 60 && Math.round(hsv.h) <= 240) {\n    hue = light ? Math.round(hsv.h) - hueStep * i : Math.round(hsv.h) + hueStep * i;\n  } else {\n    hue = light ? Math.round(hsv.h) + hueStep * i : Math.round(hsv.h) - hueStep * i;\n  }\n  if (hue < 0) {\n    hue += 360;\n  } else if (hue >= 360) {\n    hue -= 360;\n  }\n  return hue;\n}\nfunction getSaturation(hsv, i, light) {\n  // grey color don't change saturation\n  if (hsv.h === 0 && hsv.s === 0) {\n    return hsv.s;\n  }\n  var saturation;\n  if (light) {\n    saturation = hsv.s - saturationStep * i;\n  } else if (i === darkColorCount) {\n    saturation = hsv.s + saturationStep;\n  } else {\n    saturation = hsv.s + saturationStep2 * i;\n  }\n  // 边界值修正\n  if (saturation > 1) {\n    saturation = 1;\n  }\n  // 第一格的 s 限制在 0.06-0.1 之间\n  if (light && i === lightColorCount && saturation > 0.1) {\n    saturation = 0.1;\n  }\n  if (saturation < 0.06) {\n    saturation = 0.06;\n  }\n  return Number(saturation.toFixed(2));\n}\nfunction getValue(hsv, i, light) {\n  var value;\n  if (light) {\n    value = hsv.v + brightnessStep1 * i;\n  } else {\n    value = hsv.v - brightnessStep2 * i;\n  }\n  if (value > 1) {\n    value = 1;\n  }\n  return Number(value.toFixed(2));\n}\nexport default function generate(color) {\n  var opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var patterns = [];\n  var pColor = inputToRGB(color);\n  for (var i = lightColorCount; i > 0; i -= 1) {\n    var hsv = toHsv(pColor);\n    var colorString = toHex(inputToRGB({\n      h: getHue(hsv, i, true),\n      s: getSaturation(hsv, i, true),\n      v: getValue(hsv, i, true)\n    }));\n    patterns.push(colorString);\n  }\n  patterns.push(toHex(pColor));\n  for (var _i = 1; _i <= darkColorCount; _i += 1) {\n    var _hsv = toHsv(pColor);\n    var _colorString = toHex(inputToRGB({\n      h: getHue(_hsv, _i),\n      s: getSaturation(_hsv, _i),\n      v: getValue(_hsv, _i)\n    }));\n    patterns.push(_colorString);\n  }\n\n  // dark theme patterns\n  if (opts.theme === 'dark') {\n    return darkColorMap.map(function (_ref3) {\n      var index = _ref3.index,\n        opacity = _ref3.opacity;\n      var darkColorString = toHex(mix(inputToRGB(opts.backgroundColor || '#141414'), inputToRGB(patterns[index]), opacity * 100));\n      return darkColorString;\n    });\n  }\n  return patterns;\n}", "// Generated by script. Do NOT modify!\n\nexport var presetPrimaryColors = {\n  \"red\": \"#F5222D\",\n  \"volcano\": \"#FA541C\",\n  \"orange\": \"#FA8C16\",\n  \"gold\": \"#FAAD14\",\n  \"yellow\": \"#FADB14\",\n  \"lime\": \"#A0D911\",\n  \"green\": \"#52C41A\",\n  \"cyan\": \"#13C2C2\",\n  \"blue\": \"#1677FF\",\n  \"geekblue\": \"#2F54EB\",\n  \"purple\": \"#722ED1\",\n  \"magenta\": \"#EB2F96\",\n  \"grey\": \"#666666\"\n};\nexport var red = [\"#fff1f0\", \"#ffccc7\", \"#ffa39e\", \"#ff7875\", \"#ff4d4f\", \"#f5222d\", \"#cf1322\", \"#a8071a\", \"#820014\", \"#5c0011\"];\nred.primary = red[5];\nexport var volcano = [\"#fff2e8\", \"#ffd8bf\", \"#ffbb96\", \"#ff9c6e\", \"#ff7a45\", \"#fa541c\", \"#d4380d\", \"#ad2102\", \"#871400\", \"#610b00\"];\nvolcano.primary = volcano[5];\nexport var orange = [\"#fff7e6\", \"#ffe7ba\", \"#ffd591\", \"#ffc069\", \"#ffa940\", \"#fa8c16\", \"#d46b08\", \"#ad4e00\", \"#873800\", \"#612500\"];\norange.primary = orange[5];\nexport var gold = [\"#fffbe6\", \"#fff1b8\", \"#ffe58f\", \"#ffd666\", \"#ffc53d\", \"#faad14\", \"#d48806\", \"#ad6800\", \"#874d00\", \"#613400\"];\ngold.primary = gold[5];\nexport var yellow = [\"#feffe6\", \"#ffffb8\", \"#fffb8f\", \"#fff566\", \"#ffec3d\", \"#fadb14\", \"#d4b106\", \"#ad8b00\", \"#876800\", \"#614700\"];\nyellow.primary = yellow[5];\nexport var lime = [\"#fcffe6\", \"#f4ffb8\", \"#eaff8f\", \"#d3f261\", \"#bae637\", \"#a0d911\", \"#7cb305\", \"#5b8c00\", \"#3f6600\", \"#254000\"];\nlime.primary = lime[5];\nexport var green = [\"#f6ffed\", \"#d9f7be\", \"#b7eb8f\", \"#95de64\", \"#73d13d\", \"#52c41a\", \"#389e0d\", \"#237804\", \"#135200\", \"#092b00\"];\ngreen.primary = green[5];\nexport var cyan = [\"#e6fffb\", \"#b5f5ec\", \"#87e8de\", \"#5cdbd3\", \"#36cfc9\", \"#13c2c2\", \"#08979c\", \"#006d75\", \"#00474f\", \"#002329\"];\ncyan.primary = cyan[5];\nexport var blue = [\"#e6f4ff\", \"#bae0ff\", \"#91caff\", \"#69b1ff\", \"#4096ff\", \"#1677ff\", \"#0958d9\", \"#003eb3\", \"#002c8c\", \"#001d66\"];\nblue.primary = blue[5];\nexport var geekblue = [\"#f0f5ff\", \"#d6e4ff\", \"#adc6ff\", \"#85a5ff\", \"#597ef7\", \"#2f54eb\", \"#1d39c4\", \"#10239e\", \"#061178\", \"#030852\"];\ngeekblue.primary = geekblue[5];\nexport var purple = [\"#f9f0ff\", \"#efdbff\", \"#d3adf7\", \"#b37feb\", \"#9254de\", \"#722ed1\", \"#531dab\", \"#391085\", \"#22075e\", \"#120338\"];\npurple.primary = purple[5];\nexport var magenta = [\"#fff0f6\", \"#ffd6e7\", \"#ffadd2\", \"#ff85c0\", \"#f759ab\", \"#eb2f96\", \"#c41d7f\", \"#9e1068\", \"#780650\", \"#520339\"];\nmagenta.primary = magenta[5];\nexport var grey = [\"#a6a6a6\", \"#999999\", \"#8c8c8c\", \"#808080\", \"#737373\", \"#666666\", \"#404040\", \"#1a1a1a\", \"#000000\", \"#000000\"];\ngrey.primary = grey[5];\nexport var gray = grey;\nexport var presetPalettes = {\n  red: red,\n  volcano: volcano,\n  orange: orange,\n  gold: gold,\n  yellow: yellow,\n  lime: lime,\n  green: green,\n  cyan: cyan,\n  blue: blue,\n  geekblue: geekblue,\n  purple: purple,\n  magenta: magenta,\n  grey: grey\n};\nexport var redDark = [\"#2a1215\", \"#431418\", \"#58181c\", \"#791a1f\", \"#a61d24\", \"#d32029\", \"#e84749\", \"#f37370\", \"#f89f9a\", \"#fac8c3\"];\nredDark.primary = redDark[5];\nexport var volcanoDark = [\"#2b1611\", \"#441d12\", \"#592716\", \"#7c3118\", \"#aa3e19\", \"#d84a1b\", \"#e87040\", \"#f3956a\", \"#f8b692\", \"#fad4bc\"];\nvolcanoDark.primary = volcanoDark[5];\nexport var orangeDark = [\"#2b1d11\", \"#442a11\", \"#593815\", \"#7c4a15\", \"#aa6215\", \"#d87a16\", \"#e89a3c\", \"#f3b765\", \"#f8cf8d\", \"#fae3b7\"];\norangeDark.primary = orangeDark[5];\nexport var goldDark = [\"#2b2111\", \"#443111\", \"#594214\", \"#7c5914\", \"#aa7714\", \"#d89614\", \"#e8b339\", \"#f3cc62\", \"#f8df8b\", \"#faedb5\"];\ngoldDark.primary = goldDark[5];\nexport var yellowDark = [\"#2b2611\", \"#443b11\", \"#595014\", \"#7c6e14\", \"#aa9514\", \"#d8bd14\", \"#e8d639\", \"#f3ea62\", \"#f8f48b\", \"#fafab5\"];\nyellowDark.primary = yellowDark[5];\nexport var limeDark = [\"#1f2611\", \"#2e3c10\", \"#3e4f13\", \"#536d13\", \"#6f9412\", \"#8bbb11\", \"#a9d134\", \"#c9e75d\", \"#e4f88b\", \"#f0fab5\"];\nlimeDark.primary = limeDark[5];\nexport var greenDark = [\"#162312\", \"#1d3712\", \"#274916\", \"#306317\", \"#3c8618\", \"#49aa19\", \"#6abe39\", \"#8fd460\", \"#b2e58b\", \"#d5f2bb\"];\ngreenDark.primary = greenDark[5];\nexport var cyanDark = [\"#112123\", \"#113536\", \"#144848\", \"#146262\", \"#138585\", \"#13a8a8\", \"#33bcb7\", \"#58d1c9\", \"#84e2d8\", \"#b2f1e8\"];\ncyanDark.primary = cyanDark[5];\nexport var blueDark = [\"#111a2c\", \"#112545\", \"#15325b\", \"#15417e\", \"#1554ad\", \"#1668dc\", \"#3c89e8\", \"#65a9f3\", \"#8dc5f8\", \"#b7dcfa\"];\nblueDark.primary = blueDark[5];\nexport var geekblueDark = [\"#131629\", \"#161d40\", \"#1c2755\", \"#203175\", \"#263ea0\", \"#2b4acb\", \"#5273e0\", \"#7f9ef3\", \"#a8c1f8\", \"#d2e0fa\"];\ngeekblueDark.primary = geekblueDark[5];\nexport var purpleDark = [\"#1a1325\", \"#24163a\", \"#301c4d\", \"#3e2069\", \"#51258f\", \"#642ab5\", \"#854eca\", \"#ab7ae0\", \"#cda8f0\", \"#ebd7fa\"];\npurpleDark.primary = purpleDark[5];\nexport var magentaDark = [\"#291321\", \"#40162f\", \"#551c3b\", \"#75204f\", \"#a02669\", \"#cb2b83\", \"#e0529c\", \"#f37fb7\", \"#f8a8cc\", \"#fad2e3\"];\nmagentaDark.primary = magentaDark[5];\nexport var greyDark = [\"#151515\", \"#1f1f1f\", \"#2d2d2d\", \"#393939\", \"#494949\", \"#5a5a5a\", \"#6a6a6a\", \"#7b7b7b\", \"#888888\", \"#969696\"];\ngreyDark.primary = greyDark[5];\nexport var presetDarkPalettes = {\n  red: redDark,\n  volcano: volcanoDark,\n  orange: orangeDark,\n  gold: goldDark,\n  yellow: yellowDark,\n  lime: limeDark,\n  green: greenDark,\n  cyan: cyanDark,\n  blue: blueDark,\n  geekblue: geekblueDark,\n  purple: purpleDark,\n  magenta: magentaDark,\n  grey: greyDark\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { setAlpha } from \"../useStyle\";\nexport var getLayoutDesignToken = function getLayoutDesignToken(designTokens, antdToken) {\n  var _finalDesignTokens$si, _finalDesignTokens$he, _finalDesignTokens$he2, _finalDesignTokens$pa, _finalDesignTokens$pa2;\n  var finalDesignTokens = _objectSpread({}, designTokens);\n  return _objectSpread(_objectSpread({\n    bgLayout: \"linear-gradient(\".concat(antdToken.colorBgContainer, \", \").concat(antdToken.colorBgLayout, \" 28%)\"),\n    colorTextAppListIcon: antdToken.colorTextSecondary,\n    appListIconHoverBgColor: finalDesignTokens === null || finalDesignTokens === void 0 || (_finalDesignTokens$si = finalDesignTokens.sider) === null || _finalDesignTokens$si === void 0 ? void 0 : _finalDesignTokens$si.colorBgMenuItemSelected,\n    colorBgAppListIconHover: setAlpha(antdToken.colorTextBase, 0.04),\n    colorTextAppListIconHover: antdToken.colorTextBase\n  }, finalDesignTokens), {}, {\n    header: _objectSpread({\n      colorBgHeader: setAlpha(antdToken.colorBgElevated, 0.6),\n      colorBgScrollHeader: setAlpha(antdToken.colorBgElevated, 0.8),\n      colorHeaderTitle: antdToken.colorText,\n      colorBgMenuItemHover: setAlpha(antdToken.colorTextBase, 0.03),\n      colorBgMenuItemSelected: 'transparent',\n      colorBgMenuElevated: (finalDesignTokens === null || finalDesignTokens === void 0 || (_finalDesignTokens$he = finalDesignTokens.header) === null || _finalDesignTokens$he === void 0 ? void 0 : _finalDesignTokens$he.colorBgHeader) !== 'rgba(255, 255, 255, 0.6)' ? (_finalDesignTokens$he2 = finalDesignTokens.header) === null || _finalDesignTokens$he2 === void 0 ? void 0 : _finalDesignTokens$he2.colorBgHeader : antdToken.colorBgElevated,\n      colorTextMenuSelected: setAlpha(antdToken.colorTextBase, 0.95),\n      colorBgRightActionsItemHover: setAlpha(antdToken.colorTextBase, 0.03),\n      colorTextRightActionsItem: antdToken.colorTextTertiary,\n      heightLayoutHeader: 56,\n      colorTextMenu: antdToken.colorTextSecondary,\n      colorTextMenuSecondary: antdToken.colorTextTertiary,\n      colorTextMenuTitle: antdToken.colorText,\n      colorTextMenuActive: antdToken.colorText\n    }, finalDesignTokens.header),\n    sider: _objectSpread({\n      paddingInlineLayoutMenu: 8,\n      paddingBlockLayoutMenu: 0,\n      colorBgCollapsedButton: antdToken.colorBgElevated,\n      colorTextCollapsedButtonHover: antdToken.colorTextSecondary,\n      colorTextCollapsedButton: setAlpha(antdToken.colorTextBase, 0.25),\n      colorMenuBackground: 'transparent',\n      colorMenuItemDivider: setAlpha(antdToken.colorTextBase, 0.06),\n      colorBgMenuItemHover: setAlpha(antdToken.colorTextBase, 0.03),\n      colorBgMenuItemSelected: setAlpha(antdToken.colorTextBase, 0.04),\n      colorTextMenuItemHover: antdToken.colorText,\n      colorTextMenuSelected: setAlpha(antdToken.colorTextBase, 0.95),\n      colorTextMenuActive: antdToken.colorText,\n      colorTextMenu: antdToken.colorTextSecondary,\n      colorTextMenuSecondary: antdToken.colorTextTertiary,\n      colorTextMenuTitle: antdToken.colorText,\n      colorTextSubMenuSelected: setAlpha(antdToken.colorTextBase, 0.95)\n    }, finalDesignTokens.sider),\n    pageContainer: _objectSpread({\n      colorBgPageContainer: 'transparent',\n      paddingInlinePageContainerContent: ((_finalDesignTokens$pa = finalDesignTokens.pageContainer) === null || _finalDesignTokens$pa === void 0 ? void 0 : _finalDesignTokens$pa.marginInlinePageContainerContent) || 40,\n      paddingBlockPageContainerContent: ((_finalDesignTokens$pa2 = finalDesignTokens.pageContainer) === null || _finalDesignTokens$pa2 === void 0 ? void 0 : _finalDesignTokens$pa2.marginBlockPageContainerContent) || 32,\n      colorBgPageContainerFixed: antdToken.colorBgElevated\n    }, finalDesignTokens.pageContainer)\n  });\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nexport var merge = function merge() {\n  var obj = {};\n  for (var _len = arguments.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {\n    rest[_key] = arguments[_key];\n  }\n  var il = rest.length;\n  var key;\n  var i = 0;\n  for (; i < il; i += 1) {\n    for (key in rest[i]) {\n      if (rest[i].hasOwnProperty(key)) {\n        if (_typeof(obj[key]) === 'object' && _typeof(rest[i][key]) === 'object' && obj[key] !== undefined && obj[key] !== null && !Array.isArray(obj[key]) && !Array.isArray(rest[i][key])) {\n          obj[key] = _objectSpread(_objectSpread({}, obj[key]), rest[i][key]);\n        } else {\n          obj[key] = rest[i][key];\n        }\n      }\n    }\n  }\n  return obj;\n};", "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar _excluded = [\"locale\", \"getPrefixCls\"],\n  _excluded2 = [\"locale\", \"theme\"];\nimport { useCacheToken } from '@ant-design/cssinjs';\nimport { ConfigProvider as AntdConfigProvider } from 'antd';\nimport zh_CN from \"antd/es/locale/zh_CN\";\nimport React, { useContext, useEffect, useMemo } from 'react';\nimport { SWRConfig, useSWRConfig } from 'swr';\nimport { findIntlKeyByAntdLocaleKey, intlMap, zhCNIntl } from \"./intl\";\nimport dayjs from 'dayjs';\nimport { getLayoutDesignToken } from \"./typing/layoutToken\";\nimport { proTheme } from \"./useStyle\";\nimport { defaultToken, emptyTheme } from \"./useStyle/token\";\nimport { merge } from \"./utils/merge\";\nimport 'dayjs/locale/zh-cn';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport * from \"./intl\";\nexport * from \"./useStyle\";\nvar omitUndefined = function omitUndefined(obj) {\n  var newObj = {};\n  Object.keys(obj || {}).forEach(function (key) {\n    if (obj[key] !== undefined) {\n      newObj[key] = obj[key];\n    }\n  });\n  if (Object.keys(newObj).length < 1) {\n    return undefined;\n  }\n  return newObj;\n};\n\n/**\n * 用于判断当前是否需要开启哈希（Hash）模式。\n * 首先也会判断当前是否处于测试环境中（通过 process.env.NODE_ENV === 'TEST' 判断），\n * 如果是，则返回 false。否则，直接返回 true 表示需要打开。\n * @returns\n */\nexport var isNeedOpenHash = function isNeedOpenHash() {\n  var _process$env$NODE_ENV, _process$env$NODE_ENV2;\n  if (typeof process !== 'undefined' && (((_process$env$NODE_ENV = process.env.NODE_ENV) === null || _process$env$NODE_ENV === void 0 ? void 0 : _process$env$NODE_ENV.toUpperCase()) === 'TEST' || ((_process$env$NODE_ENV2 = process.env.NODE_ENV) === null || _process$env$NODE_ENV2 === void 0 ? void 0 : _process$env$NODE_ENV2.toUpperCase()) === 'DEV')) {\n    return false;\n  }\n  return true;\n};\n\n/**\n * 用于配置 ValueEnum 的通用配置\n */\n\n/**\n * 支持 Map 和 Object\n *\n * @name ValueEnum 的类型\n */\n\n/**\n * 支持 Map 和 Object\n */\n\n/**\n * BaseProFieldFC 的类型设置\n */\n\n/** Render 第二个参数，里面包含了一些常用的参数 */\n\n/**\n * 自带的token 配置\n */\n\n/* Creating a context object with the default values. */\nvar ProConfigContext = /*#__PURE__*/React.createContext({\n  intl: _objectSpread(_objectSpread({}, zhCNIntl), {}, {\n    locale: 'default'\n  }),\n  valueTypeMap: {},\n  theme: emptyTheme,\n  hashed: true,\n  dark: false,\n  token: defaultToken\n});\nvar ConfigConsumer = ProConfigContext.Consumer;\n\n/**\n * 组件解除挂载后清空一下 cache\n * @date 2022-11-28\n * @returns null\n */\nexport { ConfigConsumer };\nvar CacheClean = function CacheClean() {\n  var _useSWRConfig = useSWRConfig(),\n    cache = _useSWRConfig.cache;\n  useEffect(function () {\n    return function () {\n      // is a map\n      // @ts-ignore\n      cache.clear();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return null;\n};\n\n/**\n * 用于配置 Pro 的组件,分装之后会简单一些\n * @param props\n * @returns\n */\nvar ConfigProviderContainer = function ConfigProviderContainer(props) {\n  var _proTheme$useToken;\n  var children = props.children,\n    dark = props.dark,\n    valueTypeMap = props.valueTypeMap,\n    _props$autoClearCache = props.autoClearCache,\n    autoClearCache = _props$autoClearCache === void 0 ? false : _props$autoClearCache,\n    propsToken = props.token,\n    prefixCls = props.prefixCls,\n    intl = props.intl;\n  var _useContext = useContext(AntdConfigProvider.ConfigContext),\n    locale = _useContext.locale,\n    getPrefixCls = _useContext.getPrefixCls,\n    restConfig = _objectWithoutProperties(_useContext, _excluded);\n  var tokenContext = (_proTheme$useToken = proTheme.useToken) === null || _proTheme$useToken === void 0 ? void 0 : _proTheme$useToken.call(proTheme);\n  var proProvide = useContext(ProConfigContext);\n\n  /**\n   * pro 的 类\n   * @type {string}\n   * @example .ant-pro\n   */\n\n  var proComponentsCls = prefixCls ? \".\".concat(prefixCls) : \".\".concat(getPrefixCls(), \"-pro\");\n  var antCls = '.' + getPrefixCls();\n  var salt = \"\".concat(proComponentsCls);\n  /**\n   * 合并一下token，不然导致嵌套 token 失效\n   */\n  var proLayoutTokenMerge = useMemo(function () {\n    return getLayoutDesignToken(propsToken || {}, tokenContext.token || defaultToken);\n  }, [propsToken, tokenContext.token]);\n  var proProvideValue = useMemo(function () {\n    var _proProvide$intl;\n    var localeName = locale === null || locale === void 0 ? void 0 : locale.locale;\n    var key = findIntlKeyByAntdLocaleKey(localeName);\n    // antd 的 key 存在的时候以 antd 的为主\n    var resolvedIntl = intl !== null && intl !== void 0 ? intl : localeName && ((_proProvide$intl = proProvide.intl) === null || _proProvide$intl === void 0 ? void 0 : _proProvide$intl.locale) === 'default' ? intlMap[key] : proProvide.intl || intlMap[key];\n    return _objectSpread(_objectSpread({}, proProvide), {}, {\n      dark: dark !== null && dark !== void 0 ? dark : proProvide.dark,\n      token: merge(proProvide.token, tokenContext.token, {\n        proComponentsCls: proComponentsCls,\n        antCls: antCls,\n        themeId: tokenContext.theme.id,\n        layout: proLayoutTokenMerge\n      }),\n      intl: resolvedIntl || zhCNIntl\n    });\n  }, [locale === null || locale === void 0 ? void 0 : locale.locale, proProvide, dark, tokenContext.token, tokenContext.theme.id, proComponentsCls, antCls, proLayoutTokenMerge, intl]);\n  var finalToken = _objectSpread(_objectSpread({}, proProvideValue.token || {}), {}, {\n    proComponentsCls: proComponentsCls\n  });\n  var _useCacheToken = useCacheToken(tokenContext.theme, [tokenContext.token, finalToken !== null && finalToken !== void 0 ? finalToken : {}], {\n      salt: salt,\n      override: finalToken\n    }),\n    _useCacheToken2 = _slicedToArray(_useCacheToken, 2),\n    token = _useCacheToken2[0],\n    nativeHashId = _useCacheToken2[1];\n  var hashed = useMemo(function () {\n    if (props.hashed === false) {\n      return false;\n    }\n    if (proProvide.hashed === false) return false;\n    return true;\n  }, [proProvide.hashed, props.hashed]);\n  var hashId = useMemo(function () {\n    if (props.hashed === false) {\n      return '';\n    }\n    if (proProvide.hashed === false) return '';\n    //Fix issue with hashId code\n    if (isNeedOpenHash() === false) {\n      return '';\n    } else if (tokenContext.hashId) {\n      return tokenContext.hashId;\n    } else {\n      // 生产环境或其他环境\n      return nativeHashId;\n    }\n  }, [nativeHashId, proProvide.hashed, props.hashed]);\n  useEffect(function () {\n    dayjs.locale((locale === null || locale === void 0 ? void 0 : locale.locale) || 'zh-cn');\n  }, [locale === null || locale === void 0 ? void 0 : locale.locale]);\n  var themeConfig = useMemo(function () {\n    return _objectSpread(_objectSpread({}, restConfig.theme), {}, {\n      hashId: hashId,\n      hashed: hashed && isNeedOpenHash()\n    });\n  }, [restConfig.theme, hashId, hashed, isNeedOpenHash()]);\n  var proConfigContextValue = useMemo(function () {\n    return _objectSpread(_objectSpread({}, proProvideValue), {}, {\n      valueTypeMap: valueTypeMap || (proProvideValue === null || proProvideValue === void 0 ? void 0 : proProvideValue.valueTypeMap),\n      token: token,\n      theme: tokenContext.theme,\n      hashed: hashed,\n      hashId: hashId\n    });\n  }, [proProvideValue, valueTypeMap, token, tokenContext.theme, hashed, hashId]);\n  var configProviderDom = useMemo(function () {\n    return /*#__PURE__*/_jsx(AntdConfigProvider, _objectSpread(_objectSpread({}, restConfig), {}, {\n      theme: themeConfig,\n      children: /*#__PURE__*/_jsx(ProConfigContext.Provider, {\n        value: proConfigContextValue,\n        children: /*#__PURE__*/_jsxs(_Fragment, {\n          children: [autoClearCache && /*#__PURE__*/_jsx(CacheClean, {}), children]\n        })\n      })\n    }));\n\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [restConfig, themeConfig, proConfigContextValue, autoClearCache, children]);\n  if (!autoClearCache) return configProviderDom;\n  return /*#__PURE__*/_jsx(SWRConfig, {\n    value: {\n      provider: function provider() {\n        return new Map();\n      }\n    },\n    children: configProviderDom\n  });\n};\n\n/**\n * 用于配置 Pro 的一些全局性的东西\n * @param props\n * @returns\n */\nexport var ProConfigProvider = function ProConfigProvider(props) {\n  var needDeps = props.needDeps,\n    dark = props.dark,\n    token = props.token;\n  var proProvide = useContext(ProConfigContext);\n  var _useContext2 = useContext(AntdConfigProvider.ConfigContext),\n    locale = _useContext2.locale,\n    theme = _useContext2.theme,\n    rest = _objectWithoutProperties(_useContext2, _excluded2);\n\n  // 是不是不需要渲染 provide\n  var isNullProvide = needDeps && proProvide.hashId !== undefined && Object.keys(props).sort().join('-') === 'children-needDeps';\n  if (isNullProvide) return /*#__PURE__*/_jsx(_Fragment, {\n    children: props.children\n  });\n  var mergeAlgorithm = function mergeAlgorithm() {\n    var isDark = dark !== null && dark !== void 0 ? dark : proProvide.dark;\n    if (isDark && !Array.isArray(theme === null || theme === void 0 ? void 0 : theme.algorithm)) {\n      return [proTheme.darkAlgorithm, theme === null || theme === void 0 ? void 0 : theme.algorithm].filter(Boolean);\n    }\n    if (isDark && Array.isArray(theme === null || theme === void 0 ? void 0 : theme.algorithm)) {\n      return [proTheme.darkAlgorithm].concat(_toConsumableArray((theme === null || theme === void 0 ? void 0 : theme.algorithm) || [])).filter(Boolean);\n    }\n    return theme === null || theme === void 0 ? void 0 : theme.algorithm;\n  };\n  // 自动注入 antd 的配置\n  var configProvider = _objectSpread(_objectSpread({}, rest), {}, {\n    locale: locale || zh_CN,\n    theme: omitUndefined(_objectSpread(_objectSpread({}, theme), {}, {\n      algorithm: mergeAlgorithm()\n    }))\n  });\n  return /*#__PURE__*/_jsx(AntdConfigProvider, _objectSpread(_objectSpread({}, configProvider), {}, {\n    children: /*#__PURE__*/_jsx(ConfigProviderContainer, _objectSpread(_objectSpread({}, props), {}, {\n      token: token\n    }))\n  }));\n};\n\n/**\n * It returns the intl object from the context if it exists, otherwise it returns the intl object for\n * 获取国际化的方法\n * @param locale\n * @param localeMap\n * the current locale\n * @returns The return value of the function is the intl object.\n */\nexport function useIntl() {\n  var _useContext3 = useContext(AntdConfigProvider.ConfigContext),\n    locale = _useContext3.locale;\n  var _useContext4 = useContext(ProConfigContext),\n    intl = _useContext4.intl;\n  if (intl && intl.locale !== 'default') {\n    return intl || zhCNIntl;\n  }\n  if (locale !== null && locale !== void 0 && locale.locale) {\n    return intlMap[findIntlKeyByAntdLocaleKey(locale.locale)] || zhCNIntl;\n  }\n  return zhCNIntl;\n}\nProConfigContext.displayName = 'ProProvider';\nexport var ProProvider = ProConfigContext;\nexport default ProConfigContext;", "export default {\n  moneySymbol: '$',\n  form: {\n    lightFilter: {\n      more: 'المزيد',\n      clear: 'نظف',\n      confirm: 'تأكيد',\n      itemUnit: 'عناصر'\n    }\n  },\n  tableForm: {\n    search: 'ابحث',\n    reset: 'إعادة تعيين',\n    submit: 'ارسال',\n    collapsed: 'مُقلص',\n    expand: 'مُوسع',\n    inputPlaceholder: 'الرجاء الإدخال',\n    selectPlaceholder: 'الرجاء الإختيار'\n  },\n  alert: {\n    clear: 'نظف',\n    selected: 'محدد',\n    item: 'عنصر'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'من',\n      item: 'عناصر'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'ثبت على اليسار',\n    rightPin: 'ثبت على اليمين',\n    noPin: 'الغاء التثبيت',\n    leftFixedTitle: 'لصق على اليسار',\n    rightFixedTitle: 'لصق على اليمين',\n    noFixedTitle: 'إلغاء الإلصاق',\n    reset: 'إعادة تعيين',\n    columnDisplay: 'الأعمدة المعروضة',\n    columnSetting: 'الإعدادات',\n    fullScreen: 'وضع كامل الشاشة',\n    exitFullScreen: 'الخروج من وضع كامل الشاشة',\n    reload: 'تحديث',\n    density: 'الكثافة',\n    densityDefault: 'افتراضي',\n    densityLarger: 'أكبر',\n    densityMiddle: 'وسط',\n    densitySmall: 'مدمج'\n  },\n  stepsForm: {\n    next: 'التالي',\n    prev: 'السابق',\n    submit: 'أنهى'\n  },\n  loginForm: {\n    submitText: 'تسجيل الدخول'\n  },\n  editableTable: {\n    action: {\n      save: 'أنقذ',\n      cancel: 'إلغاء الأمر',\n      delete: 'حذف',\n      add: 'إضافة صف من البيانات'\n    }\n  },\n  switch: {\n    open: 'مفتوح',\n    close: 'غلق'\n  }\n};", "export default {\n  moneySymbol: '€',\n  form: {\n    lightFilter: {\n      more: 'Més',\n      clear: 'Netejar',\n      confirm: 'Confirmar',\n      itemUnit: 'Elements'\n    }\n  },\n  tableForm: {\n    search: 'Cercar',\n    reset: 'Netejar',\n    submit: 'Enviar',\n    collapsed: 'Expandir',\n    expand: 'Col·lapsar',\n    inputPlaceholder: 'Introduïu valor',\n    selectPlaceholder: 'Seleccioneu valor'\n  },\n  alert: {\n    clear: 'Netejar',\n    selected: 'Seleccionat',\n    item: 'Article'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'de',\n      item: 'articles'\n    }\n  },\n  tableToolBar: {\n    leftPin: \"Pin a l'esquerra\",\n    rightPin: 'Pin a la dreta',\n    noPin: 'Sense Pin',\n    leftFixedTitle: \"Fixat a l'esquerra\",\n    rightFixedTitle: 'Fixat a la dreta',\n    noFixedTitle: 'Sense fixar',\n    reset: 'Reiniciar',\n    columnDisplay: 'Mostrar Columna',\n    columnSetting: 'Configuració',\n    fullScreen: 'Pantal<PERSON> Completa',\n    exitFullScreen: 'Sortir Pantalla Completa',\n    reload: 'Refrescar',\n    density: 'Densitat',\n    densityDefault: 'Per Defecte',\n    densityLarger: 'Llarg',\n    densityMiddle: 'Mitjà',\n    densitySmall: 'Compacte'\n  },\n  stepsForm: {\n    next: 'Següent',\n    prev: 'Anterior',\n    submit: 'Finalizar'\n  },\n  loginForm: {\n    submitText: 'Entrar'\n  },\n  editableTable: {\n    action: {\n      save: 'Guardar',\n      cancel: 'Cancel·lar',\n      delete: 'Eliminar',\n      add: 'afegir una fila de dades'\n    }\n  },\n  switch: {\n    open: 'obert',\n    close: 'tancat'\n  }\n};", "export default {\n  moneySymbol: 'K<PERSON>',\n  deleteThisLine: 'Smazat tento řádek',\n  copyThisLine: 'Kop<PERSON><PERSON>at tento řádek',\n  form: {\n    lightFilter: {\n      more: 'Víc',\n      clear: 'Vymazat',\n      confirm: 'Potvrdit',\n      itemUnit: '<PERSON><PERSON><PERSON>'\n    }\n  },\n  tableForm: {\n    search: '<PERSON>az',\n    reset: 'Resetovat',\n    submit: 'Odes<PERSON>',\n    collapsed: 'Zvětšit',\n    expand: 'Zmenšit',\n    inputPlaceholder: '<PERSON>adejte prosím',\n    selectPlaceholder: 'Vyberte prosím'\n  },\n  alert: {\n    clear: 'Vymazat',\n    selected: 'Vybraný',\n    item: 'Polo<PERSON><PERSON>'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'z',\n      item: 'polo<PERSON>ek'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Připnout doleva',\n    rightPin: 'Připnout doprava',\n    noPin: 'Odepnuto',\n    leftFixedTitle: 'Fixováno nalevo',\n    rightFixedTitle: 'Fixováno napravo',\n    noFixedTitle: 'Neopraveno',\n    reset: 'Resetovat',\n    columnDisplay: '<PERSON>obrazen<PERSON> sloupců',\n    columnSetting: 'Nastavení',\n    fullScreen: 'Celá obrazovka',\n    exitFullScreen: 'Ukončete celou obrazovku',\n    reload: 'Obnovit',\n    density: 'Hustota',\n    densityDefault: 'Výchozí',\n    densityLarger: 'Větší',\n    densityMiddle: 'Střední',\n    densitySmall: 'Kompaktní'\n  },\n  stepsForm: {\n    next: 'Další',\n    prev: 'Předchozí',\n    submit: 'Dokončit'\n  },\n  loginForm: {\n    submitText: 'Přihlásit se'\n  },\n  editableTable: {\n    onlyOneLineEditor: 'Upravit lze pouze jeden řádek',\n    action: {\n      save: 'Uložit',\n      cancel: 'Zrušit',\n      delete: 'Vymazat',\n      add: 'přidat řádek dat'\n    }\n  },\n  switch: {\n    open: 'otevřít',\n    close: 'zavřít'\n  }\n};", "export default {\n  moneySymbol: '€',\n  form: {\n    lightFilter: {\n      more: 'Mehr',\n      clear: '<PERSON>ur<PERSON>set<PERSON>',\n      confirm: 'Bestätigen',\n      itemUnit: 'Einträge'\n    }\n  },\n  tableForm: {\n    search: 'Suchen',\n    reset: '<PERSON>ur<PERSON>setzen',\n    submit: 'Absenden',\n    collapsed: '<PERSON><PERSON>ge mehr',\n    expand: '<PERSON><PERSON><PERSON> weniger',\n    inputPlaceholder: '<PERSON>te eingeben',\n    selectPlaceholder: 'Bitte auswählen'\n  },\n  alert: {\n    clear: 'Zur<PERSON>setzen',\n    selected: 'Ausgewählt',\n    item: 'Eintrag'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'von',\n      item: 'Einträgen'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Links anheften',\n    rightPin: 'Rechts anheften',\n    noPin: 'Nicht angeheftet',\n    leftFixedTitle: 'Links fixiert',\n    rightFixedTitle: 'Rechts fixiert',\n    noFixedTitle: 'Nicht fixiert',\n    reset: '<PERSON><PERSON><PERSON>setzen',\n    columnDisplay: 'Angezei<PERSON>e Reihen',\n    columnSetting: 'Einstellungen',\n    fullScreen: 'Vollbild',\n    exitFullScreen: 'Vollbild verlassen',\n    reload: 'Aktualisieren',\n    density: 'Abstand',\n    densityDefault: 'Standard',\n    densityLarger: 'Größer',\n    densityMiddle: 'Mittel',\n    densitySmall: 'Kompakt'\n  },\n  stepsForm: {\n    next: 'Weiter',\n    prev: 'Zurück',\n    submit: 'Abschließen'\n  },\n  loginForm: {\n    submitText: 'Anmelden'\n  },\n  editableTable: {\n    action: {\n      save: 'Retten',\n      cancel: 'Abbrechen',\n      delete: 'Löschen',\n      add: 'Hinzufügen einer Datenzeile'\n    }\n  },\n  switch: {\n    open: 'offen',\n    close: 'schließen'\n  }\n};", "export default {\n  moneySymbol: '£',\n  form: {\n    lightFilter: {\n      more: 'More',\n      clear: 'Clear',\n      confirm: 'Confirm',\n      itemUnit: 'Items'\n    }\n  },\n  tableForm: {\n    search: 'Query',\n    reset: 'Reset',\n    submit: 'Submit',\n    collapsed: 'Expand',\n    expand: 'Collapse',\n    inputPlaceholder: 'Please enter',\n    selectPlaceholder: 'Please select'\n  },\n  alert: {\n    clear: 'Clear',\n    selected: 'Selected',\n    item: 'Item'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'of',\n      item: 'items'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Pin to left',\n    rightPin: 'Pin to right',\n    noPin: 'Unpinned',\n    leftFixedTitle: 'Fixed to the left',\n    rightFixedTitle: 'Fixed to the right',\n    noFixedTitle: 'Not Fixed',\n    reset: 'Reset',\n    columnDisplay: 'Column Display',\n    columnSetting: 'Table Settings',\n    fullScreen: 'Full Screen',\n    exitFullScreen: 'Exit Full Screen',\n    reload: 'Refresh',\n    density: 'Density',\n    densityDefault: 'Default',\n    densityLarger: 'Larger',\n    densityMiddle: 'Middle',\n    densitySmall: 'Compact'\n  },\n  stepsForm: {\n    next: 'Next',\n    prev: 'Previous',\n    submit: 'Finish'\n  },\n  loginForm: {\n    submitText: 'Login'\n  },\n  editableTable: {\n    onlyOneLineEditor: 'Only one line can be edited',\n    onlyAddOneLine: 'Only one line can be added',\n    action: {\n      save: 'Save',\n      cancel: 'Cancel',\n      delete: 'Delete',\n      add: 'add a row of data'\n    }\n  },\n  switch: {\n    open: 'open',\n    close: 'close'\n  }\n};", "export default {\n  moneySymbol: '$',\n  deleteThisLine: 'Delete this line',\n  copyThisLine: 'Copy this line',\n  form: {\n    lightFilter: {\n      more: 'More',\n      clear: 'Clear',\n      confirm: 'Confirm',\n      itemUnit: 'Items'\n    }\n  },\n  tableForm: {\n    search: 'Query',\n    reset: 'Reset',\n    submit: 'Submit',\n    collapsed: 'Expand',\n    expand: 'Collapse',\n    inputPlaceholder: 'Please enter',\n    selectPlaceholder: 'Please select'\n  },\n  alert: {\n    clear: 'Clear',\n    selected: 'Selected',\n    item: 'Item'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'of',\n      item: 'items'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Pin to left',\n    rightPin: 'Pin to right',\n    noPin: 'Unpinned',\n    leftFixedTitle: 'Fixed to the left',\n    rightFixedTitle: 'Fixed to the right',\n    noFixedTitle: 'Not Fixed',\n    reset: 'Reset',\n    columnDisplay: 'Column Display',\n    columnSetting: 'Table Settings',\n    fullScreen: 'Full Screen',\n    exitFullScreen: 'Exit Full Screen',\n    reload: 'Refresh',\n    density: 'Density',\n    densityDefault: 'Default',\n    densityLarger: 'Larger',\n    densityMiddle: 'Middle',\n    densitySmall: 'Compact'\n  },\n  stepsForm: {\n    next: 'Next',\n    prev: 'Previous',\n    submit: 'Finish'\n  },\n  loginForm: {\n    submitText: 'Login'\n  },\n  editableTable: {\n    onlyOneLineEditor: 'Only one line can be edited',\n    onlyAddOneLine: 'Only one line can be added',\n    action: {\n      save: 'Save',\n      cancel: 'Cancel',\n      delete: 'Delete',\n      add: 'add a row of data'\n    }\n  },\n  switch: {\n    open: 'open',\n    close: 'close'\n  }\n};", "export default {\n  moneySymbol: '€',\n  form: {\n    lightFilter: {\n      more: 'Más',\n      clear: 'Limpiar',\n      confirm: 'Confirmar',\n      itemUnit: 'artí<PERSON><PERSON>'\n    }\n  },\n  tableForm: {\n    search: 'Buscar',\n    reset: 'Limpiar',\n    submit: 'Submit',\n    collapsed: 'Expandir',\n    expand: 'Colapsar',\n    inputPlaceholder: 'Ingrese valor',\n    selectPlaceholder: 'Seleccione valor'\n  },\n  alert: {\n    clear: 'Limpiar',\n    selected: 'Seleccionado',\n    item: 'Articulo'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'de',\n      item: 'artículos'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Pin a la izquierda',\n    rightPin: 'Pin a la derecha',\n    noPin: 'Sin Pin',\n    leftFixedTitle: 'Fijado a la izquierda',\n    rightFixedTitle: 'Fijado a la derecha',\n    noFixedTitle: 'Sin Fijar',\n    reset: 'Reiniciar',\n    columnDisplay: 'Mostrar Columna',\n    columnSetting: 'Configuración',\n    fullScreen: 'Pantalla Completa',\n    exitFullScreen: 'Salir Pantalla Completa',\n    reload: 'Refrescar',\n    density: 'Densidad',\n    densityDefault: 'Por Defecto',\n    densityLarger: 'Largo',\n    densityMiddle: 'Medio',\n    densitySmall: 'Compacto'\n  },\n  stepsForm: {\n    next: 'Siguiente',\n    prev: 'Anterior',\n    submit: 'Finalizar'\n  },\n  loginForm: {\n    submitText: 'Entrar'\n  },\n  editableTable: {\n    action: {\n      save: 'Guardar',\n      cancel: 'Descartar',\n      delete: 'Borrar',\n      add: 'añadir una fila de datos'\n    }\n  },\n  switch: {\n    open: 'abrir',\n    close: 'cerrar'\n  }\n};", "export default {\n  moneySymbol: 'تومان',\n  form: {\n    lightFilter: {\n      more: 'بیشتر',\n      clear: 'پاک کردن',\n      confirm: 'تایید',\n      itemUnit: 'مورد'\n    }\n  },\n  tableForm: {\n    search: 'جستجو',\n    reset: 'بازنشانی',\n    submit: 'تایید',\n    collapsed: 'نمایش بیشتر',\n    expand: 'نمایش کمتر',\n    inputPlaceholder: 'پیدا کنید',\n    selectPlaceholder: 'انتخاب کنید'\n  },\n  alert: {\n    clear: 'پاک سازی',\n    selected: 'انتخاب',\n    item: 'مورد'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'از',\n      item: 'مورد'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'سنجاق به چپ',\n    rightPin: 'سنجاق به راست',\n    noPin: 'سنجاق نشده',\n    leftFixedTitle: 'ثابت شده در چپ',\n    rightFixedTitle: 'ثابت شده در راست',\n    noFixedTitle: 'شناور',\n    reset: 'بازنشانی',\n    columnDisplay: 'نمایش همه',\n    columnSetting: 'تنظیمات',\n    fullScreen: 'تمام صفحه',\n    exitFullScreen: 'خروج از حالت تمام صفحه',\n    reload: 'تازه سازی',\n    density: 'تراکم',\n    densityDefault: 'پیش فرض',\n    densityLarger: 'بزرگ',\n    densityMiddle: 'متوسط',\n    densitySmall: 'کوچک'\n  },\n  stepsForm: {\n    next: 'بعدی',\n    prev: 'قبلی',\n    submit: 'اتمام'\n  },\n  loginForm: {\n    submitText: 'ورود'\n  },\n  editableTable: {\n    action: {\n      save: 'ذخیره',\n      cancel: 'لغو',\n      delete: 'حذف',\n      add: 'یک ردیف داده اضافه کنید'\n    }\n  },\n  switch: {\n    open: 'باز',\n    close: 'نزدیک'\n  }\n};", "export default {\n  moneySymbol: '€',\n  form: {\n    lightFilter: {\n      more: 'Plus',\n      clear: 'Effacer',\n      confirm: 'Confirmer',\n      itemUnit: 'Items'\n    }\n  },\n  tableForm: {\n    search: 'Rechercher',\n    reset: 'Réinitialiser',\n    submit: 'Envoyer',\n    collapsed: 'Agrandir',\n    expand: 'Réduire',\n    inputPlaceholder: 'Entrer une valeur',\n    selectPlaceholder: 'Sélectionner une valeur'\n  },\n  alert: {\n    clear: 'Réinitialiser',\n    selected: 'Sélectionné',\n    item: 'Item'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'sur',\n      item: 'éléments'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Épingler à gauche',\n    rightPin: 'Épingler à gauche',\n    noPin: 'Sans épingle',\n    leftFixedTitle: 'Fixer à gauche',\n    rightFixedTitle: 'Fixer à droite',\n    noFixedTitle: 'Non fixé',\n    reset: 'Réinitialiser',\n    columnDisplay: 'Affichage colonne',\n    columnSetting: 'Réglages',\n    fullScreen: 'Plein écran',\n    exitFullScreen: 'Quitter Plein écran',\n    reload: 'Rafraichir',\n    density: 'Densité',\n    densityDefault: 'Par défaut',\n    densityLarger: 'Larger',\n    densityMiddle: 'Moyenne',\n    densitySmall: 'Compacte'\n  },\n  stepsForm: {\n    next: 'Suivante',\n    prev: 'Précédente',\n    submit: 'Finaliser'\n  },\n  loginForm: {\n    submitText: 'Se connecter'\n  },\n  editableTable: {\n    action: {\n      save: 'Sauvegarder',\n      cancel: 'Annuler',\n      delete: 'Supprimer',\n      add: 'ajouter une ligne de données'\n    }\n  },\n  switch: {\n    open: 'ouvert',\n    close: 'près'\n  }\n};", "export default {\n  moneySymbol: '₪',\n  deleteThisLine: 'מחק שורה זו',\n  copyThisLine: 'העתק שורה זו',\n  form: {\n    lightFilter: {\n      more: 'יותר',\n      clear: 'נקה',\n      confirm: 'אישור',\n      itemUnit: 'פריטים'\n    }\n  },\n  tableForm: {\n    search: 'חיפוש',\n    reset: 'איפוס',\n    submit: 'שלח',\n    collapsed: 'הרחב',\n    expand: 'כווץ',\n    inputPlaceholder: 'אנא הכנס',\n    selectPlaceholder: 'אנא בחר'\n  },\n  alert: {\n    clear: 'נקה',\n    selected: 'נבחר',\n    item: 'פריט'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'מתוך',\n      item: 'פריטים'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'הצמד לשמאל',\n    rightPin: 'הצמד לימין',\n    noPin: 'לא מצורף',\n    leftFixedTitle: 'מוצמד לשמאל',\n    rightFixedTitle: 'מוצמד לימין',\n    noFixedTitle: 'לא מוצמד',\n    reset: 'איפוס',\n    columnDisplay: 'תצוגת עמודות',\n    columnSetting: 'הגדרות',\n    fullScreen: 'מסך מלא',\n    exitFullScreen: 'צא ממסך מלא',\n    reload: 'רענן',\n    density: 'רזולוציה',\n    densityDefault: 'ברירת מחדל',\n    densityLarger: 'גדול',\n    densityMiddle: 'בינוני',\n    densitySmall: 'קטן'\n  },\n  stepsForm: {\n    next: 'הבא',\n    prev: 'קודם',\n    submit: 'סיום'\n  },\n  loginForm: {\n    submitText: 'כניסה'\n  },\n  editableTable: {\n    onlyOneLineEditor: 'ניתן לערוך רק שורה אחת',\n    action: {\n      save: 'שמור',\n      cancel: 'ביטול',\n      delete: 'מחיקה',\n      add: 'הוסף שורת נתונים'\n    }\n  },\n  switch: {\n    open: 'פתח',\n    close: 'סגור'\n  }\n};", "export default {\n  moneySymbol: 'kn',\n  form: {\n    lightFilter: {\n      more: 'Vi<PERSON>e',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n      confirm: 'Potv<PERSON>',\n      itemUnit: '<PERSON>av<PERSON>'\n    }\n  },\n  tableForm: {\n    search: 'Pretra<PERSON><PERSON>',\n    reset: '<PERSON><PERSON><PERSON><PERSON>',\n    submit: 'Potvrdi',\n    collapsed: 'Ra<PERSON><PERSON>',\n    expand: 'Sku<PERSON>',\n    inputPlaceholder: 'Unesite',\n    selectPlaceholder: 'Odaberite'\n  },\n  alert: {\n    clear: 'O<PERSON><PERSON><PERSON>',\n    selected: 'Odaberi',\n    item: 'stavke'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'od',\n      item: 'stavke'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Prikači lijevo',\n    rightPin: '<PERSON><PERSON><PERSON>i desno',\n    noPin: 'Bez prikačenja',\n    leftFixedTitle: 'Fiksiraj lijevo',\n    rightFixedTitle: 'Fiksiraj desno',\n    noFixedTitle: 'Bez fiksiranja',\n    reset: 'Resetiraj',\n    columnDisplay: 'Prikaz stupaca',\n    columnSetting: '<PERSON>avke',\n    fullScreen: 'P<PERSON> zaslon',\n    exitFullScreen: 'Izađi iz punog zaslona',\n    reload: 'Ponovno učitaj',\n    density: 'Veličina',\n    densityDefault: 'Zadano',\n    densityLarger: 'Veliko',\n    densityMiddle: 'Srednje',\n    densitySmall: 'Malo'\n  },\n  stepsForm: {\n    next: 'Sljedeći',\n    prev: 'Prethodni',\n    submit: 'Kraj'\n  },\n  loginForm: {\n    submitText: 'Prijava'\n  },\n  editableTable: {\n    action: {\n      save: 'Spremi',\n      cancel: 'Odustani',\n      delete: 'Obriši',\n      add: 'dodajte red podataka'\n    }\n  },\n  switch: {\n    open: 'otvori',\n    close: 'zatvori'\n  }\n};", "export default {\n  moneySymbol: 'RP',\n  form: {\n    lightFilter: {\n      more: '<PERSON>bih',\n      clear: '<PERSON><PERSON>',\n      confirm: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n      itemUnit: 'Unit'\n    }\n  },\n  tableForm: {\n    search: 'Cari',\n    reset: 'Atur ulang',\n    submit: '<PERSON><PERSON>',\n    collapsed: 'Lebih sedikit',\n    expand: 'Lebih banyak',\n    inputPlaceholder: 'Masukkan pencarian',\n    selectPlaceholder: '<PERSON>lih'\n  },\n  alert: {\n    clear: 'Hapus',\n    selected: 'Dipilih',\n    item: 'Butir'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'Dari',\n      item: 'Butir'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Pin kiri',\n    rightPin: 'Pin kanan',\n    noPin: 'Tidak ada pin',\n    leftFixedTitle: 'Rata kiri',\n    rightFixedTitle: 'Rata kanan',\n    noFixedTitle: 'Tidak tetap',\n    reset: 'Atur ulang',\n    columnDisplay: '<PERSON><PERSON><PERSON> kolom',\n    columnSetting: 'Pengaturan',\n    fullScreen: '<PERSON>ar penuh',\n    exitFullScreen: 'Keluar layar penuh',\n    reload: 'Atur ulang',\n    density: 'Kerapatan',\n    densityDefault: 'Standar',\n    densityLarger: 'Lebih besar',\n    densityMiddle: 'Sedang',\n    densitySmall: 'Rapat'\n  },\n  stepsForm: {\n    next: 'Selanjutnya',\n    prev: 'Sebelumnya',\n    submit: 'Selesai'\n  },\n  loginForm: {\n    submitText: 'Login'\n  },\n  editableTable: {\n    action: {\n      save: 'simpan',\n      cancel: 'batal',\n      delete: 'hapus',\n      add: 'Tambahkan baris data'\n    }\n  },\n  switch: {\n    open: 'buka',\n    close: 'tutup'\n  }\n};", "export default {\n  moneySymbol: '€',\n  form: {\n    lightFilter: {\n      more: 'più',\n      clear: 'pulisci',\n      confirm: 'conferma',\n      itemUnit: 'elementi'\n    }\n  },\n  tableForm: {\n    search: 'Filtra',\n    reset: '<PERSON><PERSON><PERSON><PERSON>',\n    submit: 'Invia',\n    collapsed: '<PERSON>span<PERSON>',\n    expand: 'Contrai',\n    inputPlaceholder: 'Digita',\n    selectPlaceholder: 'Seleziona'\n  },\n  alert: {\n    clear: 'Rimuovi',\n    selected: 'Selezionati',\n    item: 'elementi'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'di',\n      item: 'elementi'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Fissa a sinistra',\n    rightPin: 'Fissa a destra',\n    noPin: 'Ripristina posizione',\n    leftFixedTitle: 'Fissato a sinistra',\n    rightFixedTitle: 'Fissato a destra',\n    noFixedTitle: 'Non fissato',\n    reset: 'Ripristina',\n    columnDisplay: 'Disposizione colonne',\n    columnSetting: 'Impostazioni',\n    fullScreen: 'Modalità schermo intero',\n    exitFullScreen: 'Esci da modalità schermo intero',\n    reload: 'Ricarica',\n    density: 'Grandezza tabella',\n    densityDefault: 'predefinito',\n    densityLarger: 'Grande',\n    densityMiddle: 'Media',\n    densitySmall: 'Compatta'\n  },\n  stepsForm: {\n    next: 'successivo',\n    prev: 'precedente',\n    submit: 'finisci'\n  },\n  loginForm: {\n    submitText: 'Accedi'\n  },\n  editableTable: {\n    action: {\n      save: 'salva',\n      cancel: 'annulla',\n      delete: 'Delete',\n      add: 'add a row of data'\n    }\n  },\n  switch: {\n    open: 'open',\n    close: 'chiudi'\n  }\n};", "export default {\n  moneySymbol: '¥',\n  form: {\n    lightFilter: {\n      more: '更に',\n      clear: 'クリア',\n      confirm: '確認',\n      itemUnit: 'アイテム'\n    }\n  },\n  tableForm: {\n    search: '検索',\n    reset: 'リセット',\n    submit: '送信',\n    collapsed: '拡大',\n    expand: '折畳',\n    inputPlaceholder: '入力してください',\n    selectPlaceholder: '選択してください'\n  },\n  alert: {\n    clear: 'クリア',\n    selected: '選択した',\n    item: 'アイテム'\n  },\n  pagination: {\n    total: {\n      range: 'レコード',\n      total: '/合計',\n      item: ' '\n    }\n  },\n  tableToolBar: {\n    leftPin: '左に固定',\n    rightPin: '右に固定',\n    noPin: 'キャンセル',\n    leftFixedTitle: '左に固定された項目',\n    rightFixedTitle: '右に固定された項目',\n    noFixedTitle: '固定されてない項目',\n    reset: 'リセット',\n    columnDisplay: '表示列',\n    columnSetting: '列表示設定',\n    fullScreen: 'フルスクリーン',\n    exitFullScreen: '終了',\n    reload: '更新',\n    density: '行高',\n    densityDefault: 'デフォルト',\n    densityLarger: '大',\n    densityMiddle: '中',\n    densitySmall: '小'\n  },\n  stepsForm: {\n    next: '次へ',\n    prev: '前へ',\n    submit: '送信'\n  },\n  loginForm: {\n    submitText: 'ログイン'\n  },\n  editableTable: {\n    action: {\n      save: '保存',\n      cancel: 'キャンセル',\n      delete: '削除',\n      add: '追加'\n    }\n  },\n  switch: {\n    open: '開く',\n    close: '閉じる'\n  }\n};", "export default {\n  moneySymbol: '₩',\n  form: {\n    lightFilter: {\n      more: '더보기',\n      clear: '초기화',\n      confirm: '확인',\n      itemUnit: '건수'\n    }\n  },\n  tableForm: {\n    search: '조회',\n    reset: '초기화',\n    submit: '제출',\n    collapsed: '확장',\n    expand: '닫기',\n    inputPlaceholder: '입력해 주세요',\n    selectPlaceholder: '선택해 주세요'\n  },\n  alert: {\n    clear: '취소',\n    selected: '선택',\n    item: '건'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: '/ 총',\n      item: '건'\n    }\n  },\n  tableToolBar: {\n    leftPin: '왼쪽으로 핀',\n    rightPin: '오른쪽으로 핀',\n    noPin: '핀 제거',\n    leftFixedTitle: '왼쪽으로 고정',\n    rightFixedTitle: '오른쪽으로 고정',\n    noFixedTitle: '비고정',\n    reset: '초기화',\n    columnDisplay: '컬럼 표시',\n    columnSetting: '설정',\n    fullScreen: '전체 화면',\n    exitFullScreen: '전체 화면 취소',\n    reload: '새로 고침',\n    density: '여백',\n    densityDefault: '기본',\n    densityLarger: '많은 여백',\n    densityMiddle: '중간 여백',\n    densitySmall: '좁은 여백'\n  },\n  stepsForm: {\n    next: '다음',\n    prev: '이전',\n    submit: '종료'\n  },\n  loginForm: {\n    submitText: '로그인'\n  },\n  editableTable: {\n    action: {\n      save: '저장',\n      cancel: '취소',\n      delete: '삭제',\n      add: '데이터 행 추가'\n    }\n  },\n  switch: {\n    open: '열',\n    close: '가까 운'\n  }\n};", "export default {\n  moneySymbol: '₮',\n  form: {\n    lightFilter: {\n      more: 'Илүү',\n      clear: 'Цэвэрлэх',\n      confirm: 'Баталгаажуулах',\n      itemUnit: 'Нэгжүүд'\n    }\n  },\n  tableForm: {\n    search: 'Хайх',\n    reset: 'Шинэчлэх',\n    submit: 'Илгээх',\n    collapsed: 'Өргөтгөх',\n    expand: 'Хураах',\n    inputPlaceholder: 'Утга оруулна уу',\n    selectPlaceholder: 'Утга сонгоно уу'\n  },\n  alert: {\n    clear: 'Цэвэрлэх',\n    selected: 'Сонгогдсон',\n    item: 'Нэгж'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'Нийт',\n      item: 'мөр'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Зүүн тийш бэхлэх',\n    rightPin: 'Баруун тийш бэхлэх',\n    noPin: 'Бэхлэхгүй',\n    leftFixedTitle: 'Зүүн зэрэгцүүлэх',\n    rightFixedTitle: 'Баруун зэрэгцүүлэх',\n    noFixedTitle: 'Зэрэгцүүлэхгүй',\n    reset: 'Шинэчлэх',\n    columnDisplay: 'Баганаар харуулах',\n    columnSetting: 'Тохиргоо',\n    fullScreen: 'Бүтэн дэлгэцээр',\n    exitFullScreen: 'Бүтэн дэлгэц цуцлах',\n    reload: 'Шинэчлэх',\n    density: 'Хэмжээ',\n    densityDefault: 'Хэвийн',\n    densityLarger: 'Том',\n    densityMiddle: 'Дунд',\n    densitySmall: 'Жижиг'\n  },\n  stepsForm: {\n    next: 'Дараах',\n    prev: 'Өмнөх',\n    submit: 'Дуусгах'\n  },\n  loginForm: {\n    submitText: 'Нэвтрэх'\n  },\n  editableTable: {\n    action: {\n      save: 'Хадгалах',\n      cancel: 'Цуцлах',\n      delete: 'Устгах',\n      add: 'Мөр нэмэх'\n    }\n  },\n  switch: {\n    open: 'Нээх',\n    close: 'Хаах'\n  }\n};", "export default {\n  moneySymbol: 'RM',\n  form: {\n    lightFilter: {\n      more: 'Lebih banyak',\n      clear: '<PERSON><PERSON>',\n      confirm: 'Men<PERSON><PERSON><PERSON>',\n      itemUnit: 'Item'\n    }\n  },\n  tableForm: {\n    search: 'Cari',\n    reset: 'Menetapkan semula',\n    submit: 'Hantar',\n    collapsed: 'Kembang',\n    expand: 'Kuncup',\n    inputPlaceholder: 'Sila masuk',\n    selectPlaceholder: 'Sila pilih'\n  },\n  alert: {\n    clear: 'Padam',\n    selected: 'Dipilih',\n    item: 'Item'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'daripada',\n      item: 'item'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Pin ke kiri',\n    rightPin: 'Pin ke kanan',\n    noPin: 'Tidak pin',\n    leftFixedTitle: 'Tetap ke kiri',\n    rightFixedTitle: 'Tetap ke kanan',\n    noFixedTitle: 'Tidak Tetap',\n    reset: 'Menetapkan semula',\n    columnDisplay: 'Lajur',\n    columnSetting: 'Settings',\n    fullScreen: 'Full Screen',\n    exitFullScreen: 'Keluar Full Screen',\n    reload: 'Muat Semula',\n    density: 'Densiti',\n    densityDefault: 'Biasa',\n    densityLarger: 'Besar',\n    densityMiddle: 'Tengah',\n    densitySmall: 'Kecil'\n  },\n  stepsForm: {\n    next: 'Seterusnya',\n    prev: 'Sebelumnya',\n    submit: 'Selesai'\n  },\n  loginForm: {\n    submitText: 'Log Masuk'\n  },\n  editableTable: {\n    action: {\n      save: 'Simpan',\n      cancel: 'Membatalkan',\n      delete: 'Menghapuskan',\n      add: 'tambah baris data'\n    }\n  },\n  switch: {\n    open: 'Terbuka',\n    close: 'Tutup'\n  }\n};", "export default {\n  moneySymbol: 'zł',\n  form: {\n    lightFilter: {\n      more: 'Wi<PERSON>cej',\n      clear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n      confirm: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n      itemUnit: '<PERSON><PERSON><PERSON><PERSON>'\n    }\n  },\n  tableForm: {\n    search: 'Szukaj',\n    reset: 'Reset',\n    submit: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    collapsed: '<PERSON><PERSON><PERSON> wiecej',\n    expand: '<PERSON><PERSON><PERSON> mniej',\n    inputPlaceholder: '<PERSON><PERSON><PERSON> podać',\n    selectPlaceholder: '<PERSON><PERSON><PERSON> wybrać'\n  },\n  alert: {\n    clear: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n    selected: 'Wybrane',\n    item: 'Wpis'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'z',\n      item: 'Wpisów'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Przypnij do lewej',\n    rightPin: 'Przypnij do prawej',\n    noPin: 'Odepnij',\n    leftFixedTitle: 'Przypięte do lewej',\n    rightFixedTitle: 'P<PERSON>ypi<PERSON>te do prawej',\n    noFixedTitle: 'Ni<PERSON><PERSON><PERSON><PERSON><PERSON>te',\n    reset: 'Reset',\n    columnDisplay: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wiersze',\n    columnSetting: 'Ustawi<PERSON>',\n    fullScreen: 'Pełen ekran',\n    exitFullScreen: 'Zamknij pełen ekran',\n    reload: 'Odśwież',\n    density: 'Odstęp',\n    densityDefault: 'Standard',\n    densityLarger: 'Wiekszy',\n    densityMiddle: 'Sredni',\n    densitySmall: 'Kompaktowy'\n  },\n  stepsForm: {\n    next: 'Weiter',\n    prev: 'Zurück',\n    submit: 'Abschließen'\n  },\n  loginForm: {\n    submitText: 'Zaloguj się'\n  },\n  editableTable: {\n    action: {\n      save: 'Zapisać',\n      cancel: 'Anuluj',\n      delete: 'Usunąć',\n      add: 'dodawanie wiersza danych'\n    }\n  },\n  switch: {\n    open: 'otwierać',\n    close: 'zamykać'\n  }\n};", "export default {\n  moneySymbol: 'R$',\n  form: {\n    lightFilter: {\n      more: 'Mai<PERSON>',\n      clear: '<PERSON><PERSON>',\n      confirm: 'Confirmar',\n      itemUnit: 'Itens'\n    }\n  },\n  tableForm: {\n    search: 'Filtrar',\n    reset: 'Limpar',\n    submit: 'Confirmar',\n    collapsed: 'Expandir',\n    expand: 'Colapsar',\n    inputPlaceholder: 'Por favor insira',\n    selectPlaceholder: 'Por favor selecione'\n  },\n  alert: {\n    clear: 'Limpar',\n    selected: 'Selecion<PERSON>(s)',\n    item: 'Item(s)'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'de',\n      item: 'itens'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Fixar à esquerda',\n    rightPin: 'Fixar à direita',\n    noPin: 'Desfixado',\n    leftFixedTitle: 'Fixado à esquerda',\n    rightFixedTitle: 'Fixado à direita',\n    noFixedTitle: 'Não fixado',\n    reset: 'Limpar',\n    columnDisplay: 'Mostrar Coluna',\n    columnSetting: 'Configurações',\n    fullScreen: 'Tela Cheia',\n    exitFullScreen: '<PERSON><PERSON> da Tela Cheia',\n    reload: 'Atualizar',\n    density: 'Densidade',\n    densityDefault: 'Padrão',\n    densityLarger: 'Largo',\n    densityMiddle: 'Médio',\n    densitySmall: 'Compacto'\n  },\n  stepsForm: {\n    next: 'Próximo',\n    prev: 'Anterior',\n    submit: 'Enviar'\n  },\n  loginForm: {\n    submitText: 'Entrar'\n  },\n  editableTable: {\n    action: {\n      save: 'Salvar',\n      cancel: 'Cancelar',\n      delete: 'Apagar',\n      add: 'adicionar uma linha de dados'\n    }\n  },\n  switch: {\n    open: 'abrir',\n    close: 'fechar'\n  }\n};", "export default {\n  moneySymbol: '₽',\n  form: {\n    lightFilter: {\n      more: 'Еще',\n      clear: 'Очистить',\n      confirm: 'ОК',\n      itemUnit: 'Позиции'\n    }\n  },\n  tableForm: {\n    search: 'Найти',\n    reset: 'Сброс',\n    submit: 'Отправить',\n    collapsed: 'Развернуть',\n    expand: 'Свернуть',\n    inputPlaceholder: 'Введите значение',\n    selectPlaceholder: 'Выберите значение'\n  },\n  alert: {\n    clear: 'Очистить',\n    selected: 'Выбрано',\n    item: 'элементов'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'из',\n      item: 'элементов'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Закрепить слева',\n    rightPin: 'Закрепить справа',\n    noPin: 'Открепить',\n    leftFixedTitle: 'Закреплено слева',\n    rightFixedTitle: 'Закреплено справа',\n    noFixedTitle: 'Не закреплено',\n    reset: 'Сброс',\n    columnDisplay: 'Отображение столбца',\n    columnSetting: 'Настройки',\n    fullScreen: 'Полный экран',\n    exitFullScreen: 'Выйти из полноэкранного режима',\n    reload: 'Обновить',\n    density: 'Размер',\n    densityDefault: 'По умолчанию',\n    densityLarger: 'Большой',\n    densityMiddle: 'Средний',\n    densitySmall: 'Сжатый'\n  },\n  stepsForm: {\n    next: 'Следующий',\n    prev: 'Предыдущий',\n    submit: 'Завершить'\n  },\n  loginForm: {\n    submitText: 'Вход'\n  },\n  editableTable: {\n    action: {\n      save: 'Сохранить',\n      cancel: 'Отменить',\n      delete: 'Удалить',\n      add: 'добавить ряд данных'\n    }\n  },\n  switch: {\n    open: 'Открытый чемпионат мира по теннису',\n    close: 'По адресу:'\n  }\n};", "export default {\n  moneySymbol: '€',\n  deleteThisLine: 'Odstrániť tento riadok',\n  copyThisLine: '<PERSON>kop<PERSON>rujte tento riadok',\n  form: {\n    lightFilter: {\n      more: 'Viac',\n      clear: 'Vy<PERSON>isti<PERSON>',\n      confirm: 'Potvrďte',\n      itemUnit: '<PERSON><PERSON><PERSON>'\n    }\n  },\n  tableForm: {\n    search: 'Vyhlada<PERSON>',\n    reset: 'Resetova<PERSON>',\n    submit: 'Odosla<PERSON>',\n    collapsed: 'Rozbaliť',\n    expand: 'Zbali<PERSON>',\n    inputPlaceholder: 'Prosím, zadajte',\n    selectPlaceholder: 'Prosím, vyberte'\n  },\n  alert: {\n    clear: 'Vyč<PERSON>i<PERSON>',\n    selected: 'Vybraný',\n    item: 'Položka'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'z',\n      item: 'položiek'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Pripnúť vľavo',\n    rightPin: 'Pripnúť vpravo',\n    noPin: 'Odopnuté',\n    leftFixedTitle: 'Fixovan<PERSON> na ľavo',\n    rightFixedTitle: 'Fixovan<PERSON> na pravo',\n    noFixedTitle: 'Nefixované',\n    reset: 'Resetova<PERSON>',\n    columnDisplay: 'Z<PERSON><PERSON>nie stĺpcov',\n    columnSetting: 'Nastavenia',\n    fullScreen: 'Celá obrazovka',\n    exitFullScreen: 'Ukončiť celú obrazovku',\n    reload: 'Obnoviť',\n    density: 'Hustota',\n    densityDefault: 'Predvolené',\n    densityLarger: 'Väčšie',\n    densityMiddle: 'Stredné',\n    densitySmall: 'Kompaktné'\n  },\n  stepsForm: {\n    next: 'Ďalšie',\n    prev: 'Predchádzajúce',\n    submit: 'Potvrdiť'\n  },\n  loginForm: {\n    submitText: 'Prihlásiť sa'\n  },\n  editableTable: {\n    onlyOneLineEditor: 'Upravovať možno iba jeden riadok',\n    action: {\n      save: 'Uložiť',\n      cancel: 'Zrušiť',\n      delete: 'Odstrániť',\n      add: 'pridať riadok údajov'\n    }\n  },\n  switch: {\n    open: 'otvoriť',\n    close: 'zavrieť'\n  }\n};", "export default {\n  moneySymbol: 'RSD',\n  form: {\n    lightFilter: {\n      more: 'Vi<PERSON>e',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n      confirm: 'Potv<PERSON>',\n      itemUnit: '<PERSON>av<PERSON>'\n    }\n  },\n  tableForm: {\n    search: '<PERSON><PERSON><PERSON><PERSON>',\n    reset: 'Resetuj',\n    submit: '<PERSON><PERSON><PERSON><PERSON>',\n    collapsed: '<PERSON><PERSON><PERSON>',\n    expand: 'Sku<PERSON>',\n    inputPlaceholder: 'Molimo unesite',\n    selectPlaceholder: 'Molimo odaberite'\n  },\n  alert: {\n    clear: '<PERSON><PERSON><PERSON><PERSON>',\n    selected: 'Odabra<PERSON>',\n    item: 'Stavka'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'od',\n      item: 'stavki'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Zakači levo',\n    rightPin: '<PERSON><PERSON><PERSON><PERSON> desno',\n    noPin: '<PERSON><PERSON> zaka<PERSON>',\n    leftFixedTitle: 'Fiksirano levo',\n    rightFixedTitle: 'Fiksirano desno',\n    noFixedTitle: '<PERSON><PERSON> fiksirano',\n    reset: 'Resetuj',\n    columnDisplay: '<PERSON><PERSON>z kolona',\n    columnSetting: '<PERSON><PERSON><PERSON>vanja',\n    fullScreen: 'Pun ekran',\n    exitFullScreen: 'Zatvori pun ekran',\n    reload: 'Osveži',\n    density: 'Veličina',\n    densityDefault: 'Podrazumevana',\n    densityLarger: 'Veća',\n    densityMiddle: 'Srednja',\n    densitySmall: 'Kompaktna'\n  },\n  stepsForm: {\n    next: 'Dalje',\n    prev: 'Nazad',\n    submit: 'Gotovo'\n  },\n  loginForm: {\n    submitText: 'Prijavi se'\n  },\n  editableTable: {\n    action: {\n      save: 'Sačuvaj',\n      cancel: 'Poništi',\n      delete: 'Obriši',\n      add: 'dodajte red podataka'\n    }\n  },\n  switch: {\n    open: 'Отворите',\n    close: 'Затворите'\n  }\n};", "export default {\n  moneySymbol: '฿',\n  deleteThisLine: 'ลบบรรทัดนี้',\n  copyThisLine: 'คัดลอกบรรทัดนี้',\n  form: {\n    lightFilter: {\n      more: 'มากกว่า',\n      clear: 'ชัดเจน',\n      confirm: 'ยืนยัน',\n      itemUnit: 'รายการ'\n    }\n  },\n  tableForm: {\n    search: 'สอบถาม',\n    reset: 'รีเซ็ต',\n    submit: 'ส่ง',\n    collapsed: 'ขยาย',\n    expand: 'ทรุด',\n    inputPlaceholder: 'กรุณาป้อน',\n    selectPlaceholder: 'โปรดเลือก'\n  },\n  alert: {\n    clear: 'ชัดเจน',\n    selected: 'เลือกแล้ว',\n    item: 'รายการ'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'ของ',\n      item: 'รายการ'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'ปักหมุดไปทางซ้าย',\n    rightPin: 'ปักหมุดไปทางขวา',\n    noPin: 'เลิกตรึงแล้ว',\n    leftFixedTitle: 'แก้ไขด้านซ้าย',\n    rightFixedTitle: 'แก้ไขด้านขวา',\n    noFixedTitle: 'ไม่คงที่',\n    reset: 'รีเซ็ต',\n    columnDisplay: 'การแสดงคอลัมน์',\n    columnSetting: 'การตั้งค่า',\n    fullScreen: 'เต็มจอ',\n    exitFullScreen: 'ออกจากโหมดเต็มหน้าจอ',\n    reload: 'รีเฟรช',\n    density: 'ความหนาแน่น',\n    densityDefault: 'ค่าเริ่มต้น',\n    densityLarger: 'ขนาดใหญ่ขึ้น',\n    densityMiddle: 'กลาง',\n    densitySmall: 'กะทัดรัด'\n  },\n  stepsForm: {\n    next: 'ถัดไป',\n    prev: 'ก่อนหน้า',\n    submit: 'เสร็จ'\n  },\n  loginForm: {\n    submitText: 'เข้าสู่ระบบ'\n  },\n  editableTable: {\n    onlyOneLineEditor: 'แก้ไขได้เพียงบรรทัดเดียวเท่านั้น',\n    action: {\n      save: 'บันทึก',\n      cancel: 'ยกเลิก',\n      delete: 'ลบ',\n      add: 'เพิ่มแถวของข้อมูล'\n    }\n  },\n  switch: {\n    open: 'เปิด',\n    close: 'ปิด'\n  }\n};", "export default {\n  moneySymbol: '₺',\n  form: {\n    lightFilter: {\n      more: '<PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON>',\n      confirm: '<PERSON><PERSON><PERSON>',\n      itemUnit: '<PERSON><PERSON><PERSON><PERSON>'\n    }\n  },\n  tableForm: {\n    search: '<PERSON>ltrel<PERSON>',\n    reset: '<PERSON><PERSON><PERSON><PERSON>rl<PERSON>',\n    submit: '<PERSON><PERSON><PERSON>',\n    collapsed: '<PERSON>ha fazla',\n    expand: '<PERSON>ha az',\n    inputPlaceholder: 'Filtrelemek için bir değer girin',\n    selectPlaceholder: 'Filtrelemek için bir değer seçin'\n  },\n  alert: {\n    clear: 'Temizle',\n    selected: '<PERSON><PERSON><PERSON>',\n    item: 'Öğe'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'Toplam',\n      item: 'Öğe'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Sola sabitle',\n    rightPin: 'Sağa sabitle',\n    noPin: 'Sabitlemeyi kaldır',\n    leftFixedTitle: 'Sola sabitlendi',\n    rightFixedTitle: 'Sağa sabitlendi',\n    noFixedTitle: 'Sabitlenmedi',\n    reset: '<PERSON><PERSON><PERSON>ırla',\n    columnDisplay: '<PERSON><PERSON> Görünümü',\n    columnSetting: 'Ayar<PERSON>',\n    fullScreen: '<PERSON>',\n    exitFullScreen: 'Tam <PERSON>krandan Çık',\n    reload: 'Yenile',\n    density: 'Kalınlık',\n    densityDefault: 'Varsayılan',\n    densityLarger: 'Büyük',\n    densityMiddle: 'Orta',\n    densitySmall: 'Küçük'\n  },\n  stepsForm: {\n    next: 'Sıradaki',\n    prev: 'Önceki',\n    submit: 'Gönder'\n  },\n  loginForm: {\n    submitText: 'Giriş Yap'\n  },\n  editableTable: {\n    action: {\n      save: 'Kaydet',\n      cancel: 'Vazgeç',\n      delete: 'Sil',\n      add: 'foegje in rige gegevens ta'\n    }\n  },\n  switch: {\n    open: 'açık',\n    close: 'kapatmak'\n  }\n};", "export default {\n  moneySymbol: '₴',\n  deleteThisLine: 'Видатили рядок',\n  copyThisLine: 'Скопіювати рядок',\n  form: {\n    lightFilter: {\n      more: 'Ще',\n      clear: 'Очистити',\n      confirm: 'Ок',\n      itemUnit: 'Позиції'\n    }\n  },\n  tableForm: {\n    search: 'Пошук',\n    reset: 'Очистити',\n    submit: 'Відправити',\n    collapsed: 'Розгорнути',\n    expand: 'Згорнути',\n    inputPlaceholder: 'Введіть значення',\n    selectPlaceholder: 'Оберіть значення'\n  },\n  alert: {\n    clear: 'Очистити',\n    selected: 'Обрано',\n    item: 'елементів'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'з',\n      item: 'елементів'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Закріпити зліва',\n    rightPin: 'Закріпити справа',\n    noPin: 'Відкріпити',\n    leftFixedTitle: 'Закріплено зліва',\n    rightFixedTitle: 'Закріплено справа',\n    noFixedTitle: 'Не закріплено',\n    reset: 'Скинути',\n    columnDisplay: 'Відображення стовпців',\n    columnSetting: 'Налаштування',\n    fullScreen: 'Повноекранний режим',\n    exitFullScreen: 'Вийти з повноекранного режиму',\n    reload: 'Оновити',\n    density: 'Розмір',\n    densityDefault: 'За замовчуванням',\n    densityLarger: 'Великий',\n    densityMiddle: 'Середній',\n    densitySmall: 'Стислий'\n  },\n  stepsForm: {\n    next: 'Наступний',\n    prev: 'Попередній',\n    submit: 'Завершити'\n  },\n  loginForm: {\n    submitText: 'Вхіх'\n  },\n  editableTable: {\n    onlyOneLineEditor: 'Тільки один рядок може бути редагований одночасно',\n    action: {\n      save: 'Зберегти',\n      cancel: 'Відмінити',\n      delete: 'Видалити',\n      add: 'додати рядок'\n    }\n  },\n  switch: {\n    open: 'Відкрито',\n    close: 'Закрито'\n  }\n};", "export default {\n  moneySymbol: 'UZS',\n  form: {\n    lightFilter: {\n      more: '<PERSON><PERSON>',\n      clear: 'Tozalash',\n      confirm: 'OK',\n      itemUnit: 'Pozitsiyalar'\n    }\n  },\n  tableForm: {\n    search: 'Qidirish',\n    reset: '<PERSON><PERSON><PERSON> tiklash',\n    submit: 'Yuborish',\n    collapsed: 'Yig‘ish',\n    expand: 'Kengaytirish',\n    inputPlaceholder: 'Qiymatni kiriting',\n    selectPlaceholder: 'Qiymatni tanlang'\n  },\n  alert: {\n    clear: 'Tozalash',\n    selected: 'Tanlangan',\n    item: 'elementlar'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'dan',\n      item: 'elementlar'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Chapga mahkamlash',\n    rightPin: 'O‘ngga mahkamlash',\n    noPin: 'Mahkamlashni olib tashlash',\n    leftFixedTitle: 'Chapga mahkamlangan',\n    rightFixedTitle: 'O‘ngga mahkamlangan',\n    noFixedTitle: '<PERSON>h<PERSON>mlashsiz',\n    reset: '<PERSON><PERSON>ta tiklash',\n    columnDisplay: '<PERSON><PERSON><PERSON> ko‘rsatish',\n    columnSetting: 'Sozlamalar',\n    fullScreen: 'To‘liq ekran',\n    exitFullScreen: 'To‘liq ekrandan chiqish',\n    reload: 'Yangilash',\n    density: 'O‘lcham',\n    densityDefault: 'Standart',\n    densityLarger: 'Katta',\n    densityMiddle: 'O‘rtacha',\n    densitySmall: 'Kichik'\n  },\n  stepsForm: {\n    next: 'Keyingi',\n    prev: 'Oldingi',\n    submit: 'Tugatish'\n  },\n  loginForm: {\n    submitText: 'Kirish'\n  },\n  editableTable: {\n    action: {\n      save: 'Saqlash',\n      cancel: 'Bekor qilish',\n      delete: 'O‘chirish',\n      add: 'maʼlumotlar qatorini qo‘shish'\n    }\n  },\n  switch: {\n    open: 'Ochish',\n    close: 'Yopish'\n  }\n};", "export default {\n  moneySymbol: '₫',\n  form: {\n    lightFilter: {\n      more: '<PERSON><PERSON><PERSON>u hơn',\n      clear: 'Trong',\n      confirm: '<PERSON><PERSON><PERSON> nhận',\n      itemUnit: '<PERSON>ụ<PERSON>'\n    }\n  },\n  tableForm: {\n    search: 'Tìm kiếm',\n    reset: '<PERSON><PERSON><PERSON> lại',\n    submit: 'Gửi đi',\n    collapsed: 'Mở rộng',\n    expand: 'Thu gọn',\n    inputPlaceholder: 'nhập dữ liệu',\n    selectPlaceholder: '<PERSON>ui lòng chọn'\n  },\n  alert: {\n    clear: 'Xóa',\n    selected: 'đã chọn',\n    item: 'mục'\n  },\n  pagination: {\n    total: {\n      range: ' ',\n      total: 'trên',\n      item: 'mặt hàng'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Ghim trái',\n    rightPin: 'Ghim phải',\n    noPin: 'Bỏ ghim',\n    leftFixedTitle: 'Cố định trái',\n    rightFixedTitle: '<PERSON><PERSON> định phải',\n    noFixedTitle: '<PERSON><PERSON><PERSON> cố định',\n    reset: '<PERSON><PERSON><PERSON> lại',\n    columnDisplay: 'Cột hiển thị',\n    columnSetting: '<PERSON><PERSON>u hình',\n    fullScreen: 'Chế độ toàn màn hình',\n    exitFullScreen: 'Thoát chế độ toàn màn hình',\n    reload: 'Làm mới',\n    density: 'Mật độ hiển thị',\n    densityDefault: 'Mặc định',\n    densityLarger: 'Mặc định',\n    densityMiddle: 'Trung bình',\n    densitySmall: 'Chật'\n  },\n  stepsForm: {\n    next: 'Sau',\n    prev: 'Trước',\n    submit: 'Kết thúc'\n  },\n  loginForm: {\n    submitText: 'Đăng nhập'\n  },\n  editableTable: {\n    action: {\n      save: 'Cứu',\n      cancel: 'Hủy',\n      delete: 'Xóa',\n      add: 'thêm một hàng dữ liệu'\n    }\n  },\n  switch: {\n    open: 'mở',\n    close: 'đóng'\n  }\n};", "export default {\n  moneySymbol: '¥',\n  deleteThisLine: '删除此项',\n  copyThisLine: '复制此项',\n  form: {\n    lightFilter: {\n      more: '更多筛选',\n      clear: '清除',\n      confirm: '确认',\n      itemUnit: '项'\n    }\n  },\n  tableForm: {\n    search: '查询',\n    reset: '重置',\n    submit: '提交',\n    collapsed: '展开',\n    expand: '收起',\n    inputPlaceholder: '请输入',\n    selectPlaceholder: '请选择'\n  },\n  alert: {\n    clear: '取消选择',\n    selected: '已选择',\n    item: '项'\n  },\n  pagination: {\n    total: {\n      range: '第',\n      total: '条/总共',\n      item: '条'\n    }\n  },\n  tableToolBar: {\n    leftPin: '固定在列首',\n    rightPin: '固定在列尾',\n    noPin: '不固定',\n    leftFixedTitle: '固定在左侧',\n    rightFixedTitle: '固定在右侧',\n    noFixedTitle: '不固定',\n    reset: '重置',\n    columnDisplay: '列展示',\n    columnSetting: '列设置',\n    fullScreen: '全屏',\n    exitFullScreen: '退出全屏',\n    reload: '刷新',\n    density: '密度',\n    densityDefault: '正常',\n    densityLarger: '宽松',\n    densityMiddle: '中等',\n    densitySmall: '紧凑'\n  },\n  stepsForm: {\n    next: '下一步',\n    prev: '上一步',\n    submit: '提交'\n  },\n  loginForm: {\n    submitText: '登录'\n  },\n  editableTable: {\n    onlyOneLineEditor: '只能同时编辑一行',\n    action: {\n      save: '保存',\n      cancel: '取消',\n      delete: '删除',\n      add: '添加一行数据'\n    }\n  },\n  switch: {\n    open: '打开',\n    close: '关闭'\n  }\n};", "export default {\n  moneySymbol: 'NT$',\n  deleteThisLine: '刪除此项',\n  copyThisLine: '複製此项',\n  form: {\n    lightFilter: {\n      more: '更多篩選',\n      clear: '清除',\n      confirm: '確認',\n      itemUnit: '項'\n    }\n  },\n  tableForm: {\n    search: '查詢',\n    reset: '重置',\n    submit: '提交',\n    collapsed: '展開',\n    expand: '收起',\n    inputPlaceholder: '請輸入',\n    selectPlaceholder: '請選擇'\n  },\n  alert: {\n    clear: '取消選擇',\n    selected: '已選擇',\n    item: '項'\n  },\n  pagination: {\n    total: {\n      range: '第',\n      total: '條/總共',\n      item: '條'\n    }\n  },\n  tableToolBar: {\n    leftPin: '固定到左邊',\n    rightPin: '固定到右邊',\n    noPin: '不固定',\n    leftFixedTitle: '固定在左側',\n    rightFixedTitle: '固定在右側',\n    noFixedTitle: '不固定',\n    reset: '重置',\n    columnDisplay: '列展示',\n    columnSetting: '列設置',\n    fullScreen: '全屏',\n    exitFullScreen: '退出全屏',\n    reload: '刷新',\n    density: '密度',\n    densityDefault: '正常',\n    densityLarger: '寬鬆',\n    densityMiddle: '中等',\n    densitySmall: '緊湊'\n  },\n  stepsForm: {\n    next: '下一步',\n    prev: '上一步',\n    submit: '完成'\n  },\n  loginForm: {\n    submitText: '登入'\n  },\n  editableTable: {\n    onlyOneLineEditor: '只能同時編輯一行',\n    action: {\n      save: '保存',\n      cancel: '取消',\n      delete: '刪除',\n      add: '新增一行資料'\n    }\n  },\n  switch: {\n    open: '打開',\n    close: '關閉'\n  }\n};", "export default {\n  moneySymbol: '€',\n  deleteThisLine: 'Ver<PERSON><PERSON>der deze regel',\n  copyThisLine: 'Kopieer deze regel',\n  form: {\n    lightFilter: {\n      more: 'Meer filters',\n      clear: 'Wissen',\n      confirm: 'Bevestigen',\n      itemUnit: 'item'\n    }\n  },\n  tableForm: {\n    search: 'Zoeken',\n    reset: 'Resetten',\n    submit: 'Indienen',\n    collapsed: 'Uitvouwen',\n    expand: 'Inklappen',\n    inputPlaceholder: 'Voer in',\n    selectPlaceholder: 'Selecteer'\n  },\n  alert: {\n    clear: 'Selectie annuleren',\n    selected: 'Geselecteerd',\n    item: 'item'\n  },\n  pagination: {\n    total: {\n      range: 'Van',\n      total: 'items/totaal',\n      item: 'items'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Vastzetten aan begin',\n    rightPin: 'Vastzetten aan einde',\n    noPin: 'Niet vastzetten',\n    leftFixedTitle: 'Vastzetten aan de linkerkant',\n    rightFixedTitle: 'Vastzetten aan de rechterkant',\n    noFixedTitle: 'Niet vastzetten',\n    reset: 'Resetten',\n    columnDisplay: 'Kolomweergave',\n    columnSetting: 'Kolominstellingen',\n    fullScreen: 'Volledig scherm',\n    exitFullScreen: 'Verlaat volledig scherm',\n    reload: 'Vernieuwen',\n    density: 'Dichtheid',\n    densityDefault: 'Normaal',\n    densityLarger: 'Ruim',\n    densityMiddle: 'Gemiddeld',\n    densitySmall: 'Compact'\n  },\n  stepsForm: {\n    next: 'Volgende stap',\n    prev: 'Vorige stap',\n    submit: 'Indienen'\n  },\n  loginForm: {\n    submitText: 'Inloggen'\n  },\n  editableTable: {\n    onlyOneLineEditor: 'Slechts één regel tegelijk bewerken',\n    action: {\n      save: 'Opslaan',\n      cancel: 'Annuleren',\n      delete: 'Verwijderen',\n      add: 'Een regel toevoegen'\n    }\n  },\n  switch: {\n    open: 'Openen',\n    close: 'Sluiten'\n  }\n};", "export default {\n  moneySymbol: 'RON',\n  deleteThisLine: 'Șterge acest rând',\n  copyThisLine: 'Copiază acest rând',\n  form: {\n    lightFilter: {\n      more: 'Mai multe filtre',\n      clear: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n      confirm: 'Confirmă',\n      itemUnit: 'elemente'\n    }\n  },\n  tableForm: {\n    search: 'Caut<PERSON>',\n    reset: 'Reseteaz<PERSON>',\n    submit: 'Trimite',\n    collapsed: 'Extinde',\n    expand: 'Restrânge',\n    inputPlaceholder: 'Introduceți',\n    selectPlaceholder: 'Selectați'\n  },\n  alert: {\n    clear: 'Anulează selecția',\n    selected: 'Selectat',\n    item: 'elemente'\n  },\n  pagination: {\n    total: {\n      range: 'De la',\n      total: 'elemente/total',\n      item: 'elemente'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Fixează la început',\n    rightPin: 'Fixează la sfârșit',\n    noPin: 'Nu fixa',\n    leftFixedTitle: 'Fixează în stânga',\n    rightFixedTitle: 'Fixează în dreapta',\n    noFixedTitle: 'Nu fixa',\n    reset: 'Reseteaz<PERSON>',\n    columnDisplay: 'Afișare coloane',\n    columnSetting: 'Set<PERSON><PERSON> coloane',\n    fullScreen: 'Ecran complet',\n    exitFullScreen: 'Ieși din ecran complet',\n    reload: 'Reîncarcă',\n    density: 'Densitate',\n    densityDefault: 'Normal',\n    densityLarger: 'Larg',\n    densityMiddle: 'Mediu',\n    densitySmall: 'Compact'\n  },\n  stepsForm: {\n    next: 'Pasul următor',\n    prev: 'Pasul anterior',\n    submit: 'Trimite'\n  },\n  loginForm: {\n    submitText: 'Autentificare'\n  },\n  editableTable: {\n    onlyOneLineEditor: 'Se poate edita doar un rând simultan',\n    action: {\n      save: 'Salvează',\n      cancel: 'Anulează',\n      delete: 'Șterge',\n      add: 'Adaugă un rând'\n    }\n  },\n  switch: {\n    open: 'Deschide',\n    close: 'Închide'\n  }\n};", "export default {\n  moneySymbol: 'SEK',\n  deleteThisLine: 'Radera denna rad',\n  copyThisLine: 'Kopiera denna rad',\n  form: {\n    lightFilter: {\n      more: 'Fler filter',\n      clear: 'Ren<PERSON>',\n      confirm: 'Bekräfta',\n      itemUnit: 'objekt'\n    }\n  },\n  tableForm: {\n    search: '<PERSON>ök',\n    reset: '<PERSON><PERSON>täll',\n    submit: '<PERSON>ck<PERSON>',\n    collapsed: 'Expandera',\n    expand: 'Fäll ihop',\n    inputPlaceholder: 'Vänligen ange',\n    selectPlaceholder: 'Vänligen välj'\n  },\n  alert: {\n    clear: 'Avbryt val',\n    selected: 'Vald',\n    item: 'objekt'\n  },\n  pagination: {\n    total: {\n      range: 'Från',\n      total: 'objekt/totalt',\n      item: 'objekt'\n    }\n  },\n  tableToolBar: {\n    leftPin: 'Fäst till vänster',\n    rightPin: 'Fäst till höger',\n    noPin: 'Inte fäst',\n    leftFixedTitle: 'Fäst till vänster',\n    rightFixedTitle: '<PERSON>äst till höger',\n    noFixedTitle: 'Inte fäst',\n    reset: '<PERSON><PERSON>täll',\n    columnDisplay: 'Kolumnvisning',\n    columnSetting: 'Kolumninställningar',\n    fullScreen: 'Fullskärm',\n    exitFullScreen: 'Avsluta fullskärm',\n    reload: 'Ladda om',\n    density: 'Täthet',\n    densityDefault: 'Normal',\n    densityLarger: 'Lös',\n    densityMiddle: 'Medium',\n    densitySmall: 'Kompakt'\n  },\n  stepsForm: {\n    next: 'Nästa steg',\n    prev: 'Föregående steg',\n    submit: 'Skicka'\n  },\n  loginForm: {\n    submitText: 'Logga in'\n  },\n  editableTable: {\n    onlyOneLineEditor: 'Endast en rad kan redigeras åt gången',\n    action: {\n      save: 'Spara',\n      cancel: 'Avbryt',\n      delete: 'Radera',\n      add: 'Lägg till en rad'\n    }\n  },\n  switch: {\n    open: 'Öppna',\n    close: 'Stäng'\n  }\n};", "import { get } from 'rc-util';\nimport arEG from \"./locale/ar_EG\";\nimport caES from \"./locale/ca_ES\";\nimport csCZ from \"./locale/cs_CZ\";\nimport deDE from \"./locale/de_DE\";\nimport enGB from \"./locale/en_GB\";\nimport enUS from \"./locale/en_US\";\nimport esES from \"./locale/es_ES\";\nimport faIR from \"./locale/fa_IR\";\nimport frFR from \"./locale/fr_FR\";\nimport heIL from \"./locale/he_IL\";\nimport hrHR from \"./locale/hr_HR\";\nimport idID from \"./locale/id_ID\";\nimport itIT from \"./locale/it_IT\";\nimport jaJP from \"./locale/ja_JP\";\nimport koKR from \"./locale/ko_KR\";\nimport mnMN from \"./locale/mn_MN\";\nimport msMY from \"./locale/ms_MY\";\nimport plPL from \"./locale/pl_PL\";\nimport ptBR from \"./locale/pt_BR\";\nimport ruRU from \"./locale/ru_RU\";\nimport skSK from \"./locale/sk_SK\";\nimport srRS from \"./locale/sr_RS\";\nimport thTH from \"./locale/th_TH\";\nimport trTR from \"./locale/tr_TR\";\nimport ukUA from \"./locale/uk_UA\";\nimport uzUZ from \"./locale/uz_UZ\";\nimport viVN from \"./locale/vi_VN\";\nimport zhCN from \"./locale/zh_CN\";\nimport zhTW from \"./locale/zh_TW\";\nimport nlNL from \"./locale/nl_NL\";\nimport roRO from \"./locale/ro_RO\";\nimport svSE from \"./locale/sv_SE\";\n/**\n * 创建一个国际化的操作函数\n *\n * @param locale\n * @param localeMap\n */\nexport var createIntl = function createIntl(locale, localeMap) {\n  return {\n    getMessage: function getMessage(id, defaultMessage) {\n      var msg = get(localeMap, id.replace(/\\[(\\d+)\\]/g, '.$1').split('.')) || '';\n      if (msg) return msg;\n      var localKey = locale.replace('_', '-');\n      if (localKey === 'zh-CN') {\n        return defaultMessage;\n      }\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      var intl = intlMap['zh-CN'];\n      return intl ? intl.getMessage(id, defaultMessage) : defaultMessage;\n    },\n    locale: locale\n  };\n};\nvar mnMNIntl = createIntl('mn_MN', mnMN);\nvar arEGIntl = createIntl('ar_EG', arEG);\nvar zhCNIntl = createIntl('zh_CN', zhCN);\nvar enUSIntl = createIntl('en_US', enUS);\nvar enGBIntl = createIntl('en_GB', enGB);\nvar viVNIntl = createIntl('vi_VN', viVN);\nvar itITIntl = createIntl('it_IT', itIT);\nvar jaJPIntl = createIntl('ja_JP', jaJP);\nvar esESIntl = createIntl('es_ES', esES);\nvar caESIntl = createIntl('ca_ES', caES);\nvar ruRUIntl = createIntl('ru_RU', ruRU);\nvar srRSIntl = createIntl('sr_RS', srRS);\nvar msMYIntl = createIntl('ms_MY', msMY);\nvar zhTWIntl = createIntl('zh_TW', zhTW);\nvar frFRIntl = createIntl('fr_FR', frFR);\nvar ptBRIntl = createIntl('pt_BR', ptBR);\nvar koKRIntl = createIntl('ko_KR', koKR);\nvar idIDIntl = createIntl('id_ID', idID);\nvar deDEIntl = createIntl('de_DE', deDE);\nvar faIRIntl = createIntl('fa_IR', faIR);\nvar trTRIntl = createIntl('tr_TR', trTR);\nvar plPLIntl = createIntl('pl_PL', plPL);\nvar hrHRIntl = createIntl('hr_', hrHR);\nvar thTHIntl = createIntl('th_TH', thTH);\nvar csCZIntl = createIntl('cs_cz', csCZ);\nvar skSKIntl = createIntl('sk_SK', skSK);\nvar heILIntl = createIntl('he_IL', heIL);\nvar ukUAIntl = createIntl('uk_UA', ukUA);\nvar uzUZIntl = createIntl('uz_UZ', uzUZ);\nvar nlNLIntl = createIntl('nl_NL', nlNL);\nvar roROIntl = createIntl('ro_RO', roRO);\nvar svSEIntl = createIntl('sv_SE', svSE);\nvar intlMap = {\n  'mn-MN': mnMNIntl,\n  'ar-EG': arEGIntl,\n  'zh-CN': zhCNIntl,\n  'en-US': enUSIntl,\n  'en-GB': enGBIntl,\n  'vi-VN': viVNIntl,\n  'it-IT': itITIntl,\n  'ja-JP': jaJPIntl,\n  'es-ES': esESIntl,\n  'ca-ES': caESIntl,\n  'ru-RU': ruRUIntl,\n  'sr-RS': srRSIntl,\n  'ms-MY': msMYIntl,\n  'zh-TW': zhTWIntl,\n  'fr-FR': frFRIntl,\n  'pt-BR': ptBRIntl,\n  'ko-KR': koKRIntl,\n  'id-ID': idIDIntl,\n  'de-DE': deDEIntl,\n  'fa-IR': faIRIntl,\n  'tr-TR': trTRIntl,\n  'pl-PL': plPLIntl,\n  'hr-HR': hrHRIntl,\n  'th-TH': thTHIntl,\n  'cs-CZ': csCZIntl,\n  'sk-SK': skSKIntl,\n  'he-IL': heILIntl,\n  'uk-UA': ukUAIntl,\n  'uz-UZ': uzUZIntl,\n  'nl-NL': nlNLIntl,\n  'ro-RO': roROIntl,\n  'sv-SE': svSEIntl\n};\nvar intlMapKeys = Object.keys(intlMap);\n\n/**\n * 根据 antd 的 key 来找到的 locale 插件的 key\n *\n * @param localeKey\n */\nexport var findIntlKeyByAntdLocaleKey = function findIntlKeyByAntdLocaleKey(localeKey) {\n  var localeName = (localeKey || 'zh-CN').toLocaleLowerCase();\n  return intlMapKeys.find(function (intlKey) {\n    var LowerCaseKey = intlKey.toLocaleLowerCase();\n    return LowerCaseKey.includes(localeName);\n  });\n};\nexport { arEGIntl, caESIntl, csCZIntl, deDEIntl, enGBIntl, enUSIntl, esESIntl, faIRIntl, frFRIntl, heILIntl, hrHRIntl, idIDIntl, intlMap, intlMapKeys, itITIntl, jaJPIntl, koKRIntl, mnMNIntl, msMYIntl, plPLIntl, ptBRIntl, ruRUIntl, skSKIntl, srRSIntl, thTHIntl, trTRIntl, ukUAIntl, uzUZIntl, viVNIntl, zhCNIntl, zhTWIntl, nlNLIntl, roROIntl, svSEIntl };", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useStyleRegister } from '@ant-design/cssinjs';\nimport { TinyColor } from '@ctrl/tinycolor';\nimport { ConfigProvider as AntdConfigProvider, theme } from 'antd';\nimport { useContext } from 'react';\nimport { ProProvider } from \"../index\";\nimport * as batToken from \"./token\";\n\n/**\n * 把一个颜色设置一下透明度\n * @example (#fff, 0.5) => rgba(255, 255, 255, 0.5)\n * @param baseColor {string}\n * @param alpha {0-1}\n * @returns rgba {string}\n */\nexport var setAlpha = function setAlpha(baseColor, alpha) {\n  return new TinyColor(baseColor).setAlpha(alpha).toRgbString();\n};\n\n/**\n * 把一个颜色修改一些明度\n * @example (#000, 50) => #808080\n * @param baseColor {string}\n * @param brightness {0-100}\n * @returns hexColor {string}\n */\nexport var lighten = function lighten(baseColor, brightness) {\n  var instance = new TinyColor(baseColor);\n  return instance.lighten(brightness).toHexString();\n};\nvar genTheme = function genTheme() {\n  if (typeof theme === 'undefined' || !theme) return batToken;\n  return theme;\n};\nexport var proTheme = genTheme();\nexport var useToken = proTheme.useToken;\nexport var resetComponent = function resetComponent(token) {\n  return {\n    boxSizing: 'border-box',\n    margin: 0,\n    padding: 0,\n    color: token.colorText,\n    fontSize: token.fontSize,\n    lineHeight: token.lineHeight,\n    listStyle: 'none'\n  };\n};\nexport var operationUnit = function operationUnit(token) {\n  return {\n    // FIXME: This use link but is a operation unit. Seems should be a colorPrimary.\n    // And Typography use this to generate link style which should not do this.\n    color: token.colorLink,\n    outline: 'none',\n    cursor: 'pointer',\n    transition: \"color \".concat(token.motionDurationSlow),\n    '&:focus, &:hover': {\n      color: token.colorLinkHover\n    },\n    '&:active': {\n      color: token.colorLinkActive\n    }\n  };\n};\n\n/**\n * 封装了一下 antd 的 useStyle，支持了一下antd@4\n * @param componentName {string} 组件的名字\n * @param styleFn {GenerateStyle} 生成样式的函数\n * @returns UseStyleResult\n */\nexport function useStyle(componentName, styleFn) {\n  var _token$proComponentsC;\n  var _useContext = useContext(ProProvider),\n    _useContext$token = _useContext.token,\n    token = _useContext$token === void 0 ? {} : _useContext$token;\n  var _useContext2 = useContext(ProProvider),\n    hashed = _useContext2.hashed;\n  var _useToken = useToken(),\n    antdToken = _useToken.token,\n    hashId = _useToken.hashId;\n  var _useContext3 = useContext(ProProvider),\n    provideTheme = _useContext3.theme;\n  var _useContext4 = useContext(AntdConfigProvider.ConfigContext),\n    getPrefixCls = _useContext4.getPrefixCls,\n    csp = _useContext4.csp;\n\n  // 如果不在 ProProvider 里面，就用 antd 的\n  if (!token.layout) {\n    token = _objectSpread({}, antdToken);\n  }\n  token.proComponentsCls = (_token$proComponentsC = token.proComponentsCls) !== null && _token$proComponentsC !== void 0 ? _token$proComponentsC : \".\".concat(getPrefixCls('pro'));\n  token.antCls = \".\".concat(getPrefixCls());\n  return {\n    wrapSSR: useStyleRegister({\n      theme: provideTheme,\n      token: token,\n      path: [componentName],\n      nonce: csp === null || csp === void 0 ? void 0 : csp.nonce\n    }, function () {\n      return styleFn(token);\n    }),\n    hashId: hashed ? hashId : ''\n  };\n}", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar _theme$defaultAlgorit;\nimport { createTheme } from '@ant-design/cssinjs';\nimport { theme } from 'antd';\nexport var defaultToken = {\n  blue: '#1677ff',\n  purple: '#722ED1',\n  cyan: '#13C2C2',\n  green: '#52C41A',\n  magenta: '#EB2F96',\n  pink: '#eb2f96',\n  red: '#F5222D',\n  orange: '#FA8C16',\n  yellow: '#FADB14',\n  volcano: '#FA541C',\n  geekblue: '#2F54EB',\n  gold: '#FAAD14',\n  lime: '#A0D911',\n  colorPrimary: '#1677ff',\n  colorSuccess: '#52c41a',\n  colorWarning: '#faad14',\n  colorError: '#ff7875',\n  colorInfo: '#1677ff',\n  colorTextBase: '#000',\n  colorBgBase: '#fff',\n  fontFamily: \"-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji'\",\n  fontSize: 14,\n  lineWidth: 1,\n  lineType: 'solid',\n  motionUnit: 0.1,\n  motionBase: 0,\n  motionEaseOutCirc: 'cubic-bezier(0.08, 0.82, 0.17, 1)',\n  motionEaseInOutCirc: 'cubic-bezier(0.78, 0.14, 0.15, 0.86)',\n  motionEaseOut: 'cubic-bezier(0.215, 0.61, 0.355, 1)',\n  motionEaseInOut: 'cubic-bezier(0.645, 0.045, 0.355, 1)',\n  motionEaseOutBack: 'cubic-bezier(0.12, 0.4, 0.29, 1.46)',\n  motionEaseInQuint: 'cubic-bezier(0.645, 0.045, 0.355, 1)',\n  motionEaseOutQuint: 'cubic-bezier(0.23, 1, 0.32, 1)',\n  borderRadius: 4,\n  sizeUnit: 4,\n  sizeStep: 4,\n  sizePopupArrow: 16,\n  controlHeight: 32,\n  zIndexBase: 0,\n  zIndexPopupBase: 1000,\n  opacityImage: 1,\n  wireframe: false,\n  'blue-1': '#e6f4ff',\n  'blue-2': '#bae0ff',\n  'blue-3': '#91caff',\n  'blue-4': '#69b1ff',\n  'blue-5': '#4096ff',\n  'blue-6': '#1677ff',\n  'blue-7': '#0958d9',\n  'blue-8': '#003eb3',\n  'blue-9': '#002c8c',\n  'blue-10': '#001d66',\n  'purple-1': '#f9f0ff',\n  'purple-2': '#efdbff',\n  'purple-3': '#d3adf7',\n  'purple-4': '#b37feb',\n  'purple-5': '#9254de',\n  'purple-6': '#722ed1',\n  'purple-7': '#531dab',\n  'purple-8': '#391085',\n  'purple-9': '#22075e',\n  'purple-10': '#120338',\n  'cyan-1': '#e6fffb',\n  'cyan-2': '#b5f5ec',\n  'cyan-3': '#87e8de',\n  'cyan-4': '#5cdbd3',\n  'cyan-5': '#36cfc9',\n  'cyan-6': '#13c2c2',\n  'cyan-7': '#08979c',\n  'cyan-8': '#006d75',\n  'cyan-9': '#00474f',\n  'cyan-10': '#002329',\n  'green-1': '#f6ffed',\n  'green-2': '#d9f7be',\n  'green-3': '#b7eb8f',\n  'green-4': '#95de64',\n  'green-5': '#73d13d',\n  'green-6': '#52c41a',\n  'green-7': '#389e0d',\n  'green-8': '#237804',\n  'green-9': '#135200',\n  'green-10': '#092b00',\n  'magenta-1': '#fff0f6',\n  'magenta-2': '#ffd6e7',\n  'magenta-3': '#ffadd2',\n  'magenta-4': '#ff85c0',\n  'magenta-5': '#f759ab',\n  'magenta-6': '#eb2f96',\n  'magenta-7': '#c41d7f',\n  'magenta-8': '#9e1068',\n  'magenta-9': '#780650',\n  'magenta-10': '#520339',\n  'pink-1': '#fff0f6',\n  'pink-2': '#ffd6e7',\n  'pink-3': '#ffadd2',\n  'pink-4': '#ff85c0',\n  'pink-5': '#f759ab',\n  'pink-6': '#eb2f96',\n  'pink-7': '#c41d7f',\n  'pink-8': '#9e1068',\n  'pink-9': '#780650',\n  'pink-10': '#520339',\n  'red-1': '#fff1f0',\n  'red-2': '#ffccc7',\n  'red-3': '#ffa39e',\n  'red-4': '#ff7875',\n  'red-5': '#ff4d4f',\n  'red-6': '#f5222d',\n  'red-7': '#cf1322',\n  'red-8': '#a8071a',\n  'red-9': '#820014',\n  'red-10': '#5c0011',\n  'orange-1': '#fff7e6',\n  'orange-2': '#ffe7ba',\n  'orange-3': '#ffd591',\n  'orange-4': '#ffc069',\n  'orange-5': '#ffa940',\n  'orange-6': '#fa8c16',\n  'orange-7': '#d46b08',\n  'orange-8': '#ad4e00',\n  'orange-9': '#873800',\n  'orange-10': '#612500',\n  'yellow-1': '#feffe6',\n  'yellow-2': '#ffffb8',\n  'yellow-3': '#fffb8f',\n  'yellow-4': '#fff566',\n  'yellow-5': '#ffec3d',\n  'yellow-6': '#fadb14',\n  'yellow-7': '#d4b106',\n  'yellow-8': '#ad8b00',\n  'yellow-9': '#876800',\n  'yellow-10': '#614700',\n  'volcano-1': '#fff2e8',\n  'volcano-2': '#ffd8bf',\n  'volcano-3': '#ffbb96',\n  'volcano-4': '#ff9c6e',\n  'volcano-5': '#ff7a45',\n  'volcano-6': '#fa541c',\n  'volcano-7': '#d4380d',\n  'volcano-8': '#ad2102',\n  'volcano-9': '#871400',\n  'volcano-10': '#610b00',\n  'geekblue-1': '#f0f5ff',\n  'geekblue-2': '#d6e4ff',\n  'geekblue-3': '#adc6ff',\n  'geekblue-4': '#85a5ff',\n  'geekblue-5': '#597ef7',\n  'geekblue-6': '#2f54eb',\n  'geekblue-7': '#1d39c4',\n  'geekblue-8': '#10239e',\n  'geekblue-9': '#061178',\n  'geekblue-10': '#030852',\n  'gold-1': '#fffbe6',\n  'gold-2': '#fff1b8',\n  'gold-3': '#ffe58f',\n  'gold-4': '#ffd666',\n  'gold-5': '#ffc53d',\n  'gold-6': '#faad14',\n  'gold-7': '#d48806',\n  'gold-8': '#ad6800',\n  'gold-9': '#874d00',\n  'gold-10': '#613400',\n  'lime-1': '#fcffe6',\n  'lime-2': '#f4ffb8',\n  'lime-3': '#eaff8f',\n  'lime-4': '#d3f261',\n  'lime-5': '#bae637',\n  'lime-6': '#a0d911',\n  'lime-7': '#7cb305',\n  'lime-8': '#5b8c00',\n  'lime-9': '#3f6600',\n  'lime-10': '#254000',\n  colorText: 'rgba(0, 0, 0, 0.88)',\n  colorTextSecondary: 'rgba(0, 0, 0, 0.65)',\n  colorTextTertiary: 'rgba(0, 0, 0, 0.45)',\n  colorTextQuaternary: 'rgba(0, 0, 0, 0.25)',\n  colorFill: 'rgba(0, 0, 0, 0.15)',\n  colorFillSecondary: 'rgba(0, 0, 0, 0.06)',\n  colorFillTertiary: 'rgba(0, 0, 0, 0.04)',\n  colorFillQuaternary: 'rgba(0, 0, 0, 0.02)',\n  colorBgLayout: 'hsl(220,23%,97%)',\n  colorBgContainer: '#ffffff',\n  colorBgElevated: '#ffffff',\n  colorBgSpotlight: 'rgba(0, 0, 0, 0.85)',\n  colorBorder: '#d9d9d9',\n  colorBorderSecondary: '#f0f0f0',\n  colorPrimaryBg: '#e6f4ff',\n  colorPrimaryBgHover: '#bae0ff',\n  colorPrimaryBorder: '#91caff',\n  colorPrimaryBorderHover: '#69b1ff',\n  colorPrimaryHover: '#4096ff',\n  colorPrimaryActive: '#0958d9',\n  colorPrimaryTextHover: '#4096ff',\n  colorPrimaryText: '#1677ff',\n  colorPrimaryTextActive: '#0958d9',\n  colorSuccessBg: '#f6ffed',\n  colorSuccessBgHover: '#d9f7be',\n  colorSuccessBorder: '#b7eb8f',\n  colorSuccessBorderHover: '#95de64',\n  colorSuccessHover: '#95de64',\n  colorSuccessActive: '#389e0d',\n  colorSuccessTextHover: '#73d13d',\n  colorSuccessText: '#52c41a',\n  colorSuccessTextActive: '#389e0d',\n  colorErrorBg: '#fff2f0',\n  colorErrorBgHover: '#fff1f0',\n  colorErrorBorder: '#ffccc7',\n  colorErrorBorderHover: '#ffa39e',\n  colorErrorHover: '#ffa39e',\n  colorErrorActive: '#d9363e',\n  colorErrorTextHover: '#ff7875',\n  colorErrorText: '#ff4d4f',\n  colorErrorTextActive: '#d9363e',\n  colorWarningBg: '#fffbe6',\n  colorWarningBgHover: '#fff1b8',\n  colorWarningBorder: '#ffe58f',\n  colorWarningBorderHover: '#ffd666',\n  colorWarningHover: '#ffd666',\n  colorWarningActive: '#d48806',\n  colorWarningTextHover: '#ffc53d',\n  colorWarningText: '#faad14',\n  colorWarningTextActive: '#d48806',\n  colorInfoBg: '#e6f4ff',\n  colorInfoBgHover: '#bae0ff',\n  colorInfoBorder: '#91caff',\n  colorInfoBorderHover: '#69b1ff',\n  colorInfoHover: '#69b1ff',\n  colorInfoActive: '#0958d9',\n  colorInfoTextHover: '#4096ff',\n  colorInfoText: '#1677ff',\n  colorInfoTextActive: '#0958d9',\n  colorBgMask: 'rgba(0, 0, 0, 0.45)',\n  colorWhite: '#fff',\n  sizeXXL: 48,\n  sizeXL: 32,\n  sizeLG: 24,\n  sizeMD: 20,\n  sizeMS: 16,\n  size: 16,\n  sizeSM: 12,\n  sizeXS: 8,\n  sizeXXS: 4,\n  controlHeightSM: 24,\n  controlHeightXS: 16,\n  controlHeightLG: 40,\n  motionDurationFast: '0.1s',\n  motionDurationMid: '0.2s',\n  motionDurationSlow: '0.3s',\n  fontSizes: [12, 14, 16, 20, 24, 30, 38, 46, 56, 68],\n  lineHeights: [1.6666666666666667, 1.5714285714285714, 1.5, 1.4, 1.3333333333333333, 1.2666666666666666, 1.2105263157894737, 1.173913043478261, 1.1428571428571428, 1.1176470588235294],\n  lineWidthBold: 2,\n  borderRadiusXS: 1,\n  borderRadiusSM: 4,\n  borderRadiusLG: 8,\n  borderRadiusOuter: 4,\n  colorLink: '#1677ff',\n  colorLinkHover: '#69b1ff',\n  colorLinkActive: '#0958d9',\n  colorFillContent: 'rgba(0, 0, 0, 0.06)',\n  colorFillContentHover: 'rgba(0, 0, 0, 0.15)',\n  colorFillAlter: 'rgba(0, 0, 0, 0.02)',\n  colorBgContainerDisabled: 'rgba(0, 0, 0, 0.04)',\n  colorBorderBg: '#ffffff',\n  colorSplit: 'rgba(5, 5, 5, 0.06)',\n  colorTextPlaceholder: 'rgba(0, 0, 0, 0.25)',\n  colorTextDisabled: 'rgba(0, 0, 0, 0.25)',\n  colorTextHeading: 'rgba(0, 0, 0, 0.88)',\n  colorTextLabel: 'rgba(0, 0, 0, 0.65)',\n  colorTextDescription: 'rgba(0, 0, 0, 0.45)',\n  colorTextLightSolid: '#fff',\n  colorHighlight: '#ff7875',\n  colorBgTextHover: 'rgba(0, 0, 0, 0.06)',\n  colorBgTextActive: 'rgba(0, 0, 0, 0.15)',\n  colorIcon: 'rgba(0, 0, 0, 0.45)',\n  colorIconHover: 'rgba(0, 0, 0, 0.88)',\n  colorErrorOutline: 'rgba(255, 38, 5, 0.06)',\n  colorWarningOutline: 'rgba(255, 215, 5, 0.1)',\n  fontSizeSM: 12,\n  fontSizeLG: 16,\n  fontSizeXL: 20,\n  fontSizeHeading1: 38,\n  fontSizeHeading2: 30,\n  fontSizeHeading3: 24,\n  fontSizeHeading4: 20,\n  fontSizeHeading5: 16,\n  fontSizeIcon: 12,\n  lineHeight: 1.5714285714285714,\n  lineHeightLG: 1.5,\n  lineHeightSM: 1.6666666666666667,\n  lineHeightHeading1: 1.2105263157894737,\n  lineHeightHeading2: 1.2666666666666666,\n  lineHeightHeading3: 1.3333333333333333,\n  lineHeightHeading4: 1.4,\n  lineHeightHeading5: 1.5,\n  controlOutlineWidth: 2,\n  controlInteractiveSize: 16,\n  controlItemBgHover: 'rgba(0, 0, 0, 0.04)',\n  controlItemBgActive: '#e6f4ff',\n  controlItemBgActiveHover: '#bae0ff',\n  controlItemBgActiveDisabled: 'rgba(0, 0, 0, 0.15)',\n  controlTmpOutline: 'rgba(0, 0, 0, 0.02)',\n  controlOutline: 'rgba(5, 145, 255, 0.1)',\n  fontWeightStrong: 600,\n  opacityLoading: 0.65,\n  linkDecoration: 'none',\n  linkHoverDecoration: 'none',\n  linkFocusDecoration: 'none',\n  controlPaddingHorizontal: 12,\n  controlPaddingHorizontalSM: 8,\n  paddingXXS: 4,\n  paddingXS: 8,\n  paddingSM: 12,\n  padding: 16,\n  paddingMD: 20,\n  paddingLG: 24,\n  paddingXL: 32,\n  paddingContentHorizontalLG: 24,\n  paddingContentVerticalLG: 16,\n  paddingContentHorizontal: 16,\n  paddingContentVertical: 12,\n  paddingContentHorizontalSM: 16,\n  paddingContentVerticalSM: 8,\n  marginXXS: 4,\n  marginXS: 8,\n  marginSM: 12,\n  margin: 16,\n  marginMD: 20,\n  marginLG: 24,\n  marginXL: 32,\n  marginXXL: 48,\n  boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.03),0 1px 6px -1px rgba(0, 0, 0, 0.02),0 2px 4px 0 rgba(0, 0, 0, 0.02)',\n  boxShadowSecondary: '0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05)',\n  screenXS: 480,\n  screenXSMin: 480,\n  screenXSMax: 479,\n  screenSM: 576,\n  screenSMMin: 576,\n  screenSMMax: 575,\n  screenMD: 768,\n  screenMDMin: 768,\n  screenMDMax: 767,\n  screenLG: 992,\n  screenLGMin: 992,\n  screenLGMax: 991,\n  screenXL: 1200,\n  screenXLMin: 1200,\n  screenXLMax: 1199,\n  screenXXL: 1600,\n  screenXXLMin: 1600,\n  screenXXLMax: 1599,\n  boxShadowPopoverArrow: '3px 3px 7px rgba(0, 0, 0, 0.1)',\n  boxShadowCard: '0 1px 2px -2px rgba(0, 0, 0, 0.16),0 3px 6px 0 rgba(0, 0, 0, 0.12),0 5px 12px 4px rgba(0, 0, 0, 0.09)',\n  boxShadowDrawerRight: '-6px 0 16px 0 rgba(0, 0, 0, 0.08),-3px 0 6px -4px rgba(0, 0, 0, 0.12),-9px 0 28px 8px rgba(0, 0, 0, 0.05)',\n  boxShadowDrawerLeft: '6px 0 16px 0 rgba(0, 0, 0, 0.08),3px 0 6px -4px rgba(0, 0, 0, 0.12),9px 0 28px 8px rgba(0, 0, 0, 0.05)',\n  boxShadowDrawerUp: '0 6px 16px 0 rgba(0, 0, 0, 0.08),0 3px 6px -4px rgba(0, 0, 0, 0.12),0 9px 28px 8px rgba(0, 0, 0, 0.05)',\n  boxShadowDrawerDown: '0 -6px 16px 0 rgba(0, 0, 0, 0.08),0 -3px 6px -4px rgba(0, 0, 0, 0.12),0 -9px 28px 8px rgba(0, 0, 0, 0.05)',\n  boxShadowTabsOverflowLeft: 'inset 10px 0 8px -8px rgba(0, 0, 0, 0.08)',\n  boxShadowTabsOverflowRight: 'inset -10px 0 8px -8px rgba(0, 0, 0, 0.08)',\n  boxShadowTabsOverflowTop: 'inset 0 10px 8px -8px rgba(0, 0, 0, 0.08)',\n  boxShadowTabsOverflowBottom: 'inset 0 -10px 8px -8px rgba(0, 0, 0, 0.08)',\n  _tokenKey: '19w80ff',\n  _hashId: 'css-dev-only-do-not-override-i2zu9q'\n};\nexport var hashCode = function hashCode(str) {\n  var seed = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n  var h1 = 0xdeadbeef ^ seed,\n    h2 = 0x41c6ce57 ^ seed;\n  for (var i = 0, ch; i < str.length; i++) {\n    ch = str.charCodeAt(i);\n    h1 = Math.imul(h1 ^ ch, 2654435761);\n    h2 = Math.imul(h2 ^ ch, 1597334677);\n  }\n  h1 = Math.imul(h1 ^ h1 >>> 16, 2246822507) ^ Math.imul(h2 ^ h2 >>> 13, 3266489909);\n  h2 = Math.imul(h2 ^ h2 >>> 16, 2246822507) ^ Math.imul(h1 ^ h1 >>> 13, 3266489909);\n  return 4294967296 * (2097151 & h2) + (h1 >>> 0);\n};\n\n// @ts-ignore\nexport var emptyTheme = createTheme(function (token) {\n  return token;\n});\nexport var token = {\n  theme: emptyTheme,\n  token: _objectSpread(_objectSpread({}, defaultToken), theme === null || theme === void 0 || (_theme$defaultAlgorit = theme.defaultAlgorithm) === null || _theme$defaultAlgorit === void 0 ? void 0 : _theme$defaultAlgorit.call(theme, theme === null || theme === void 0 ? void 0 : theme.defaultSeed)),\n  hashId: \"pro-\".concat(hashCode(JSON.stringify(defaultToken)))\n};\nexport var useToken = function useToken() {\n  return token;\n};", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar semver = /^[v^~<>=]*?(\\d+)(?:\\.([x*]|\\d+)(?:\\.([x*]|\\d+)(?:\\.([x*]|\\d+))?(?:-([\\da-z\\-]+(?:\\.[\\da-z\\-]+)*))?(?:\\+[\\da-z\\-]+(?:\\.[\\da-z\\-]+)*)?)?)?$/i;\n\n/**\n * @param  {string} s\n */\nvar isWildcard = function isWildcard(s) {\n  return s === '*' || s === 'x' || s === 'X';\n};\n/**\n * @param  {string} v\n */\nvar tryParse = function tryParse(v) {\n  var n = parseInt(v, 10);\n  return isNaN(n) ? v : n;\n};\n/**\n * @param  {string|number} a\n * @param  {string|number} b\n */\nvar forceType = function forceType(a, b) {\n  return _typeof(a) !== _typeof(b) ? [String(a), String(b)] : [a, b];\n};\n\n/**\n * @param  {string} a\n * @param  {string} b\n * @returns number\n */\nvar compareStrings = function compareStrings(a, b) {\n  if (isWildcard(a) || isWildcard(b)) return 0;\n  var _forceType = forceType(tryParse(a), tryParse(b)),\n    _forceType2 = _slicedToArray(_forceType, 2),\n    ap = _forceType2[0],\n    bp = _forceType2[1];\n  if (ap > bp) return 1;\n  if (ap < bp) return -1;\n  return 0;\n};\n/**\n * @param  {string|RegExpMatchArray} a\n * @param  {string|RegExpMatchArray} b\n * @returns number\n */\nvar compareSegments = function compareSegments(a, b) {\n  for (var i = 0; i < Math.max(a.length, b.length); i++) {\n    var r = compareStrings(a[i] || '0', b[i] || '0');\n    if (r !== 0) return r;\n  }\n  return 0;\n};\n/**\n * @param  {string} version\n * @returns RegExpMatchArray\n */\nvar validateAndParse = function validateAndParse(version) {\n  var _match$shift;\n  var match = version.match(semver);\n  match === null || match === void 0 || (_match$shift = match.shift) === null || _match$shift === void 0 || _match$shift.call(match);\n  return match;\n};\n\n/**\n * Compare [semver](https://semver.org/) version strings to find greater, equal or lesser.\n * This library supports the full semver specification, including comparing versions with different number of digits like `1.0.0`, `1.0`, `1`, and pre-release versions like `1.0.0-alpha`.\n * @param v1 - First version to compare\n * @param v2 - Second version to compare\n * @returns Numeric value compatible with the [Array.sort(fn) interface](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#Parameters).\n */\nexport var compareVersions = function compareVersions(v1, v2) {\n  // validate input and split into segments\n  var n1 = validateAndParse(v1);\n  var n2 = validateAndParse(v2);\n\n  // pop off the patch\n  var p1 = n1.pop();\n  var p2 = n2.pop();\n\n  // validate numbers\n  var r = compareSegments(n1, n2);\n  if (r !== 0) return r;\n  if (p1 || p2) {\n    return p1 ? -1 : 1;\n  }\n  return 0;\n};", "export const getRenderPropValue = propValue => {\n  if (!propValue) {\n    return null;\n  }\n  return typeof propValue === 'function' ? propValue() : propValue;\n};", "import * as React from 'react';\nexport default function useForceUpdate() {\n  const [, forceUpdate] = React.useReducer(x => x + 1, 0);\n  return forceUpdate;\n}", "import React from 'react';\nimport { useToken } from '../theme/internal';\nexport const responsiveArray = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];\nconst getResponsiveMap = token => ({\n  xs: `(max-width: ${token.screenXSMax}px)`,\n  sm: `(min-width: ${token.screenSM}px)`,\n  md: `(min-width: ${token.screenMD}px)`,\n  lg: `(min-width: ${token.screenLG}px)`,\n  xl: `(min-width: ${token.screenXL}px)`,\n  xxl: `(min-width: ${token.screenXXL}px)`\n});\n/**\n * Ensures that the breakpoints token are valid, in good order\n * For each breakpoint : screenMin <= screen <= screenMax and screenMax <= nextScreenMin\n */\nconst validateBreakpoints = token => {\n  const indexableToken = token;\n  const revBreakpoints = [].concat(responsiveArray).reverse();\n  revBreakpoints.forEach((breakpoint, i) => {\n    const breakpointUpper = breakpoint.toUpperCase();\n    const screenMin = `screen${breakpointUpper}Min`;\n    const screen = `screen${breakpointUpper}`;\n    if (!(indexableToken[screenMin] <= indexableToken[screen])) {\n      throw new Error(`${screenMin}<=${screen} fails : !(${indexableToken[screenMin]}<=${indexableToken[screen]})`);\n    }\n    if (i < revBreakpoints.length - 1) {\n      const screenMax = `screen${breakpointUpper}Max`;\n      if (!(indexableToken[screen] <= indexableToken[screenMax])) {\n        throw new Error(`${screen}<=${screenMax} fails : !(${indexableToken[screen]}<=${indexableToken[screenMax]})`);\n      }\n      const nextBreakpointUpperMin = revBreakpoints[i + 1].toUpperCase();\n      const nextScreenMin = `screen${nextBreakpointUpperMin}Min`;\n      if (!(indexableToken[screenMax] <= indexableToken[nextScreenMin])) {\n        throw new Error(`${screenMax}<=${nextScreenMin} fails : !(${indexableToken[screenMax]}<=${indexableToken[nextScreenMin]})`);\n      }\n    }\n  });\n  return token;\n};\nexport default function useResponsiveObserver() {\n  const [, token] = useToken();\n  const responsiveMap = getResponsiveMap(validateBreakpoints(token));\n  // To avoid repeat create instance, we add `useMemo` here.\n  return React.useMemo(() => {\n    const subscribers = new Map();\n    let subUid = -1;\n    let screens = {};\n    return {\n      matchHandlers: {},\n      dispatch(pointMap) {\n        screens = pointMap;\n        subscribers.forEach(func => func(screens));\n        return subscribers.size >= 1;\n      },\n      subscribe(func) {\n        if (!subscribers.size) this.register();\n        subUid += 1;\n        subscribers.set(subUid, func);\n        func(screens);\n        return subUid;\n      },\n      unsubscribe(paramToken) {\n        subscribers.delete(paramToken);\n        if (!subscribers.size) this.unregister();\n      },\n      unregister() {\n        Object.keys(responsiveMap).forEach(screen => {\n          const matchMediaQuery = responsiveMap[screen];\n          const handler = this.matchHandlers[matchMediaQuery];\n          handler === null || handler === void 0 ? void 0 : handler.mql.removeListener(handler === null || handler === void 0 ? void 0 : handler.listener);\n        });\n        subscribers.clear();\n      },\n      register() {\n        Object.keys(responsiveMap).forEach(screen => {\n          const matchMediaQuery = responsiveMap[screen];\n          const listener = _ref => {\n            let {\n              matches\n            } = _ref;\n            this.dispatch(Object.assign(Object.assign({}, screens), {\n              [screen]: matches\n            }));\n          };\n          const mql = window.matchMedia(matchMediaQuery);\n          mql.addListener(listener);\n          this.matchHandlers[matchMediaQuery] = {\n            mql,\n            listener\n          };\n          listener(mql);\n        });\n      },\n      responsiveMap\n    };\n  }, [token]);\n}\nexport const matchScreen = (screens, screenSizes) => {\n  if (screenSizes && typeof screenSizes === 'object') {\n    for (let i = 0; i < responsiveArray.length; i++) {\n      const breakpoint = responsiveArray[i];\n      if (screens[breakpoint] && screenSizes[breakpoint] !== undefined) {\n        return screenSizes[breakpoint];\n      }\n    }\n  }\n};", "import * as React from 'react';\nconst AvatarContext = /*#__PURE__*/React.createContext({});\nexport default AvatarContext;", "import { unit } from '@ant-design/cssinjs';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\nconst genBaseStyle = token => {\n  const {\n    antCls,\n    componentCls,\n    iconCls,\n    avatarBg,\n    avatarColor,\n    containerSize,\n    containerSizeLG,\n    containerSizeSM,\n    textFontSize,\n    textFontSizeLG,\n    textFontSizeSM,\n    borderRadius,\n    borderRadiusLG,\n    borderRadiusSM,\n    lineWidth,\n    lineType\n  } = token;\n  // Avatar size style\n  const avatarSizeStyle = (size, fontSize, radius) => ({\n    width: size,\n    height: size,\n    borderRadius: '50%',\n    [`&${componentCls}-square`]: {\n      borderRadius: radius\n    },\n    [`&${componentCls}-icon`]: {\n      fontSize,\n      [`> ${iconCls}`]: {\n        margin: 0\n      }\n    }\n  });\n  return {\n    [componentCls]: Object.assign(Object.assign(Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'relative',\n      display: 'inline-flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      overflow: 'hidden',\n      color: avatarColor,\n      whiteSpace: 'nowrap',\n      textAlign: 'center',\n      verticalAlign: 'middle',\n      background: avatarBg,\n      border: `${unit(lineWidth)} ${lineType} transparent`,\n      '&-image': {\n        background: 'transparent'\n      },\n      [`${antCls}-image-img`]: {\n        display: 'block'\n      }\n    }), avatarSizeStyle(containerSize, textFontSize, borderRadius)), {\n      '&-lg': Object.assign({}, avatarSizeStyle(containerSizeLG, textFontSizeLG, borderRadiusLG)),\n      '&-sm': Object.assign({}, avatarSizeStyle(containerSizeSM, textFontSizeSM, borderRadiusSM)),\n      '> img': {\n        display: 'block',\n        width: '100%',\n        height: '100%',\n        objectFit: 'cover'\n      }\n    })\n  };\n};\nconst genGroupStyle = token => {\n  const {\n    componentCls,\n    groupBorderColor,\n    groupOverlapping,\n    groupSpace\n  } = token;\n  return {\n    [`${componentCls}-group`]: {\n      display: 'inline-flex',\n      [componentCls]: {\n        borderColor: groupBorderColor\n      },\n      '> *:not(:first-child)': {\n        marginInlineStart: groupOverlapping\n      }\n    },\n    [`${componentCls}-group-popover`]: {\n      [`${componentCls} + ${componentCls}`]: {\n        marginInlineStart: groupSpace\n      }\n    }\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    controlHeight,\n    controlHeightLG,\n    controlHeightSM,\n    fontSize,\n    fontSizeLG,\n    fontSizeXL,\n    fontSizeHeading3,\n    marginXS,\n    marginXXS,\n    colorBorderBg\n  } = token;\n  return {\n    containerSize: controlHeight,\n    containerSizeLG: controlHeightLG,\n    containerSizeSM: controlHeightSM,\n    textFontSize: Math.round((fontSizeLG + fontSizeXL) / 2),\n    textFontSizeLG: fontSizeHeading3,\n    textFontSizeSM: fontSize,\n    groupSpace: marginXXS,\n    groupOverlapping: -marginXS,\n    groupBorderColor: colorBorderBg\n  };\n};\nexport default genStyleHooks('Avatar', token => {\n  const {\n    colorTextLightSolid,\n    colorTextPlaceholder\n  } = token;\n  const avatarToken = mergeToken(token, {\n    avatarBg: colorTextPlaceholder,\n    avatarColor: colorTextLightSolid\n  });\n  return [genBaseStyle(avatarToken), genGroupStyle(avatarToken)];\n}, prepareComponentToken);", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport { composeRef } from \"rc-util/es/ref\";\nimport { responsiveArray } from '../_util/responsiveObserver';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport useSize from '../config-provider/hooks/useSize';\nimport useBreakpoint from '../grid/hooks/useBreakpoint';\nimport AvatarContext from './AvatarContext';\nimport useStyle from './style';\nconst InternalAvatar = (props, ref) => {\n  const [scale, setScale] = React.useState(1);\n  const [mounted, setMounted] = React.useState(false);\n  const [isImgExist, setIsImgExist] = React.useState(true);\n  const avatarNodeRef = React.useRef(null);\n  const avatarChildrenRef = React.useRef(null);\n  const avatarNodeMergedRef = composeRef(ref, avatarNodeRef);\n  const {\n    getPrefixCls,\n    avatar\n  } = React.useContext(ConfigContext);\n  const avatarCtx = React.useContext(AvatarContext);\n  const setScaleParam = () => {\n    if (!avatarChildrenRef.current || !avatarNodeRef.current) {\n      return;\n    }\n    const childrenWidth = avatarChildrenRef.current.offsetWidth; // offsetWidth avoid affecting be transform scale\n    const nodeWidth = avatarNodeRef.current.offsetWidth;\n    // denominator is 0 is no meaning\n    if (childrenWidth !== 0 && nodeWidth !== 0) {\n      const {\n        gap = 4\n      } = props;\n      if (gap * 2 < nodeWidth) {\n        setScale(nodeWidth - gap * 2 < childrenWidth ? (nodeWidth - gap * 2) / childrenWidth : 1);\n      }\n    }\n  };\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n  React.useEffect(() => {\n    setIsImgExist(true);\n    setScale(1);\n  }, [props.src]);\n  React.useEffect(setScaleParam, [props.gap]);\n  const handleImgLoadError = () => {\n    const {\n      onError\n    } = props;\n    const errorFlag = onError === null || onError === void 0 ? void 0 : onError();\n    if (errorFlag !== false) {\n      setIsImgExist(false);\n    }\n  };\n  const {\n      prefixCls: customizePrefixCls,\n      shape,\n      size: customSize,\n      src,\n      srcSet,\n      icon,\n      className,\n      rootClassName,\n      alt,\n      draggable,\n      children,\n      crossOrigin\n    } = props,\n    others = __rest(props, [\"prefixCls\", \"shape\", \"size\", \"src\", \"srcSet\", \"icon\", \"className\", \"rootClassName\", \"alt\", \"draggable\", \"children\", \"crossOrigin\"]);\n  const size = useSize(ctxSize => {\n    var _a, _b;\n    return (_b = (_a = customSize !== null && customSize !== void 0 ? customSize : avatarCtx === null || avatarCtx === void 0 ? void 0 : avatarCtx.size) !== null && _a !== void 0 ? _a : ctxSize) !== null && _b !== void 0 ? _b : 'default';\n  });\n  const needResponsive = Object.keys(typeof size === 'object' ? size || {} : {}).some(key => ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'].includes(key));\n  const screens = useBreakpoint(needResponsive);\n  const responsiveSizeStyle = React.useMemo(() => {\n    if (typeof size !== 'object') {\n      return {};\n    }\n    const currentBreakpoint = responsiveArray.find(screen => screens[screen]);\n    const currentSize = size[currentBreakpoint];\n    return currentSize ? {\n      width: currentSize,\n      height: currentSize,\n      fontSize: currentSize && (icon || children) ? currentSize / 2 : 18\n    } : {};\n  }, [screens, size]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Avatar');\n    process.env.NODE_ENV !== \"production\" ? warning(!(typeof icon === 'string' && icon.length > 2), 'breaking', `\\`icon\\` is using ReactNode instead of string naming in v4. Please check \\`${icon}\\` at https://ant.design/components/icon`) : void 0;\n  }\n  const prefixCls = getPrefixCls('avatar', customizePrefixCls);\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const sizeCls = classNames({\n    [`${prefixCls}-lg`]: size === 'large',\n    [`${prefixCls}-sm`]: size === 'small'\n  });\n  const hasImageElement = /*#__PURE__*/React.isValidElement(src);\n  const mergedShape = shape || (avatarCtx === null || avatarCtx === void 0 ? void 0 : avatarCtx.shape) || 'circle';\n  const classString = classNames(prefixCls, sizeCls, avatar === null || avatar === void 0 ? void 0 : avatar.className, `${prefixCls}-${mergedShape}`, {\n    [`${prefixCls}-image`]: hasImageElement || src && isImgExist,\n    [`${prefixCls}-icon`]: !!icon\n  }, cssVarCls, rootCls, className, rootClassName, hashId);\n  const sizeStyle = typeof size === 'number' ? {\n    width: size,\n    height: size,\n    fontSize: icon ? size / 2 : 18\n  } : {};\n  let childrenToRender;\n  if (typeof src === 'string' && isImgExist) {\n    childrenToRender = /*#__PURE__*/React.createElement(\"img\", {\n      src: src,\n      draggable: draggable,\n      srcSet: srcSet,\n      onError: handleImgLoadError,\n      alt: alt,\n      crossOrigin: crossOrigin\n    });\n  } else if (hasImageElement) {\n    childrenToRender = src;\n  } else if (icon) {\n    childrenToRender = icon;\n  } else if (mounted || scale !== 1) {\n    const transformString = `scale(${scale})`;\n    const childrenStyle = {\n      msTransform: transformString,\n      WebkitTransform: transformString,\n      transform: transformString\n    };\n    childrenToRender = /*#__PURE__*/React.createElement(ResizeObserver, {\n      onResize: setScaleParam\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-string`,\n      ref: avatarChildrenRef,\n      style: Object.assign({}, childrenStyle)\n    }, children));\n  } else {\n    childrenToRender = /*#__PURE__*/React.createElement(\"span\", {\n      className: `${prefixCls}-string`,\n      style: {\n        opacity: 0\n      },\n      ref: avatarChildrenRef\n    }, children);\n  }\n  // The event is triggered twice from bubbling up the DOM tree.\n  // see https://codesandbox.io/s/kind-snow-9lidz\n  delete others.onError;\n  delete others.gap;\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"span\", Object.assign({}, others, {\n    style: Object.assign(Object.assign(Object.assign(Object.assign({}, sizeStyle), responsiveSizeStyle), avatar === null || avatar === void 0 ? void 0 : avatar.style), others.style),\n    className: classString,\n    ref: avatarNodeMergedRef\n  }), childrenToRender));\n};\nconst Avatar = /*#__PURE__*/React.forwardRef(InternalAvatar);\nif (process.env.NODE_ENV !== 'production') {\n  Avatar.displayName = 'Avatar';\n}\nexport default Avatar;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport toArray from \"rc-util/es/Children/toArray\";\nimport { cloneElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport { ConfigContext } from '../config-provider';\nimport useCSSVarCls from '../config-provider/hooks/useCSSVarCls';\nimport Popover from '../popover';\nimport Avatar from './avatar';\nimport AvatarContext from './AvatarContext';\nimport useStyle from './style';\nconst AvatarContextProvider = props => {\n  const {\n    size,\n    shape\n  } = React.useContext(AvatarContext);\n  const avatarContextValue = React.useMemo(() => ({\n    size: props.size || size,\n    shape: props.shape || shape\n  }), [props.size, props.shape, size, shape]);\n  return /*#__PURE__*/React.createElement(AvatarContext.Provider, {\n    value: avatarContextValue\n  }, props.children);\n};\nconst Group = props => {\n  var _a, _b, _c;\n  const {\n    getPrefixCls,\n    direction\n  } = React.useContext(ConfigContext);\n  const {\n    prefixCls: customizePrefixCls,\n    className,\n    rootClassName,\n    style,\n    maxCount,\n    maxStyle,\n    size,\n    shape,\n    maxPopoverPlacement,\n    maxPopoverTrigger,\n    children,\n    max\n  } = props;\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Avatar.Group');\n    warning.deprecated(!maxCount, 'maxCount', 'max={{ count: number }}');\n    warning.deprecated(!maxStyle, 'maxStyle', 'max={{ style: CSSProperties }}');\n    warning.deprecated(!maxPopoverPlacement, 'maxPopoverPlacement', 'max={{ popover: PopoverProps }}');\n    warning.deprecated(!maxPopoverTrigger, 'maxPopoverTrigger', 'max={{ popover: PopoverProps }}');\n  }\n  const prefixCls = getPrefixCls('avatar', customizePrefixCls);\n  const groupPrefixCls = `${prefixCls}-group`;\n  const rootCls = useCSSVarCls(prefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls, rootCls);\n  const cls = classNames(groupPrefixCls, {\n    [`${groupPrefixCls}-rtl`]: direction === 'rtl'\n  }, cssVarCls, rootCls, className, rootClassName, hashId);\n  const childrenWithProps = toArray(children).map((child, index) => cloneElement(child, {\n    key: `avatar-key-${index}`\n  }));\n  const mergeCount = (max === null || max === void 0 ? void 0 : max.count) || maxCount;\n  const numOfChildren = childrenWithProps.length;\n  if (mergeCount && mergeCount < numOfChildren) {\n    const childrenShow = childrenWithProps.slice(0, mergeCount);\n    const childrenHidden = childrenWithProps.slice(mergeCount, numOfChildren);\n    const mergeStyle = (max === null || max === void 0 ? void 0 : max.style) || maxStyle;\n    const mergePopoverTrigger = ((_a = max === null || max === void 0 ? void 0 : max.popover) === null || _a === void 0 ? void 0 : _a.trigger) || maxPopoverTrigger || 'hover';\n    const mergePopoverPlacement = ((_b = max === null || max === void 0 ? void 0 : max.popover) === null || _b === void 0 ? void 0 : _b.placement) || maxPopoverPlacement || 'top';\n    const mergeProps = Object.assign(Object.assign({\n      content: childrenHidden\n    }, max === null || max === void 0 ? void 0 : max.popover), {\n      overlayClassName: classNames(`${groupPrefixCls}-popover`, (_c = max === null || max === void 0 ? void 0 : max.popover) === null || _c === void 0 ? void 0 : _c.overlayClassName),\n      placement: mergePopoverPlacement,\n      trigger: mergePopoverTrigger\n    });\n    childrenShow.push(/*#__PURE__*/React.createElement(Popover, Object.assign({\n      key: \"avatar-popover-key\",\n      destroyTooltipOnHide: true\n    }, mergeProps), /*#__PURE__*/React.createElement(Avatar, {\n      style: mergeStyle\n    }, `+${numOfChildren - mergeCount}`)));\n    return wrapCSSVar(/*#__PURE__*/React.createElement(AvatarContextProvider, {\n      shape: shape,\n      size: size\n    }, /*#__PURE__*/React.createElement(\"div\", {\n      className: cls,\n      style: style\n    }, childrenShow)));\n  }\n  return wrapCSSVar(/*#__PURE__*/React.createElement(AvatarContextProvider, {\n    shape: shape,\n    size: size\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: cls,\n    style: style\n  }, childrenWithProps)));\n};\nexport default Group;", "\"use client\";\n\nimport InternalAvatar from './avatar';\nimport Group from './group';\nexport { Group };\nconst Avatar = InternalAvatar;\nAvatar.Group = Group;\nexport default Avatar;", "\"use client\";\n\nimport { useRef } from 'react';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport useForceUpdate from '../../_util/hooks/useForceUpdate';\nimport useResponsiveObserver from '../../_util/responsiveObserver';\nfunction useBreakpoint() {\n  let refreshOnChange = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;\n  const screensRef = useRef({});\n  const forceUpdate = useForceUpdate();\n  const responsiveObserver = useResponsiveObserver();\n  useLayoutEffect(() => {\n    const token = responsiveObserver.subscribe(supportScreens => {\n      screensRef.current = supportScreens;\n      if (refreshOnChange) {\n        forceUpdate();\n      }\n    });\n    return () => responsiveObserver.unsubscribe(token);\n  }, []);\n  return screensRef.current;\n}\nexport default useBreakpoint;", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { Popup } from 'rc-tooltip';\nimport { getRenderPropValue } from '../_util/getRenderPropValue';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nexport const Overlay = _ref => {\n  let {\n    title,\n    content,\n    prefixCls\n  } = _ref;\n  if (!title && !content) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(React.Fragment, null, title && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-title`\n  }, title), content && /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-inner-content`\n  }, content));\n};\nexport const RawPurePanel = props => {\n  const {\n    hashId,\n    prefixCls,\n    className,\n    style,\n    placement = 'top',\n    title,\n    content,\n    children\n  } = props;\n  const titleNode = getRenderPropValue(title);\n  const contentNode = getRenderPropValue(content);\n  const cls = classNames(hashId, prefixCls, `${prefixCls}-pure`, `${prefixCls}-placement-${placement}`, className);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: cls,\n    style: style\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${prefixCls}-arrow`\n  }), /*#__PURE__*/React.createElement(Popup, Object.assign({}, props, {\n    className: hashId,\n    prefixCls: prefixCls\n  }), children || /*#__PURE__*/React.createElement(Overlay, {\n    prefixCls: prefixCls,\n    title: titleNode,\n    content: contentNode\n  })));\n};\nconst PurePanel = props => {\n  const {\n      prefixCls: customizePrefixCls,\n      className\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"className\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('popover', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(RawPurePanel, Object.assign({}, restProps, {\n    prefixCls: prefixCls,\n    hashId: hashId,\n    className: classNames(className, cssVarCls)\n  })));\n};\nexport default PurePanel;", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport { getRenderPropValue } from '../_util/getRenderPropValue';\nimport { getTransitionName } from '../_util/motion';\nimport { cloneElement } from '../_util/reactNode';\nimport { ConfigContext } from '../config-provider';\nimport Tooltip from '../tooltip';\nimport PurePanel, { Overlay } from './PurePanel';\n// CSSINJS\nimport useStyle from './style';\nconst InternalPopover = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _a, _b;\n  const {\n      prefixCls: customizePrefixCls,\n      title,\n      content,\n      overlayClassName,\n      placement = 'top',\n      trigger = 'hover',\n      children,\n      mouseEnterDelay = 0.1,\n      mouseLeaveDelay = 0.1,\n      onOpenChange,\n      overlayStyle = {}\n    } = props,\n    otherProps = __rest(props, [\"prefixCls\", \"title\", \"content\", \"overlayClassName\", \"placement\", \"trigger\", \"children\", \"mouseEnterDelay\", \"mouseLeaveDelay\", \"onOpenChange\", \"overlayStyle\"]);\n  const {\n    getPrefixCls\n  } = React.useContext(ConfigContext);\n  const prefixCls = getPrefixCls('popover', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const rootPrefixCls = getPrefixCls();\n  const overlayCls = classNames(overlayClassName, hashId, cssVarCls);\n  const [open, setOpen] = useMergedState(false, {\n    value: (_a = props.open) !== null && _a !== void 0 ? _a : props.visible,\n    defaultValue: (_b = props.defaultOpen) !== null && _b !== void 0 ? _b : props.defaultVisible\n  });\n  const settingOpen = (value, e) => {\n    setOpen(value, true);\n    onOpenChange === null || onOpenChange === void 0 ? void 0 : onOpenChange(value, e);\n  };\n  const onKeyDown = e => {\n    if (e.keyCode === KeyCode.ESC) {\n      settingOpen(false, e);\n    }\n  };\n  const onInternalOpenChange = value => {\n    settingOpen(value);\n  };\n  const titleNode = getRenderPropValue(title);\n  const contentNode = getRenderPropValue(content);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(Tooltip, Object.assign({\n    placement: placement,\n    trigger: trigger,\n    mouseEnterDelay: mouseEnterDelay,\n    mouseLeaveDelay: mouseLeaveDelay,\n    overlayStyle: overlayStyle\n  }, otherProps, {\n    prefixCls: prefixCls,\n    overlayClassName: overlayCls,\n    ref: ref,\n    open: open,\n    onOpenChange: onInternalOpenChange,\n    overlay: titleNode || contentNode ? (/*#__PURE__*/React.createElement(Overlay, {\n      prefixCls: prefixCls,\n      title: titleNode,\n      content: contentNode\n    })) : null,\n    transitionName: getTransitionName(rootPrefixCls, 'zoom-big', otherProps.transitionName),\n    \"data-popover-inject\": true\n  }), cloneElement(children, {\n    onKeyDown: e => {\n      var _a, _b;\n      if (/*#__PURE__*/React.isValidElement(children)) {\n        (_b = children === null || children === void 0 ? void 0 : (_a = children.props).onKeyDown) === null || _b === void 0 ? void 0 : _b.call(_a, e);\n      }\n      onKeyDown(e);\n    }\n  })));\n});\nconst Popover = InternalPopover;\nPopover._InternalPanelDoNotUseOrYouWillBeFired = PurePanel;\nif (process.env.NODE_ENV !== 'production') {\n  Popover.displayName = 'Popover';\n}\nexport default Popover;", "import { resetComponent } from '../../style';\nimport { initZoomMotion } from '../../style/motion';\nimport getArrowStyle, { getArrowOffsetToken } from '../../style/placementArrow';\nimport { getArrowToken } from '../../style/roundedArrow';\nimport { genStyleHooks, mergeToken, PresetColors } from '../../theme/internal';\nconst genBaseStyle = token => {\n  const {\n    componentCls,\n    popoverColor,\n    titleMinWidth,\n    fontWeightStrong,\n    innerPadding,\n    boxShadowSecondary,\n    colorTextHeading,\n    borderRadiusLG,\n    zIndexPopup,\n    titleMarginBottom,\n    colorBgElevated,\n    popoverBg,\n    titleBorderBottom,\n    innerContentPadding,\n    titlePadding\n  } = token;\n  return [{\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      position: 'absolute',\n      top: 0,\n      // use `left` to fix https://github.com/ant-design/ant-design/issues/39195\n      left: {\n        _skip_check_: true,\n        value: 0\n      },\n      zIndex: zIndexPopup,\n      fontWeight: 'normal',\n      whiteSpace: 'normal',\n      textAlign: 'start',\n      cursor: 'auto',\n      userSelect: 'text',\n      // When use `autoArrow`, origin will follow the arrow position\n      '--valid-offset-x': 'var(--arrow-offset-horizontal, var(--arrow-x))',\n      transformOrigin: [`var(--valid-offset-x, 50%)`, `var(--arrow-y, 50%)`].join(' '),\n      '--antd-arrow-background-color': colorBgElevated,\n      width: 'max-content',\n      maxWidth: '100vw',\n      '&-rtl': {\n        direction: 'rtl'\n      },\n      '&-hidden': {\n        display: 'none'\n      },\n      [`${componentCls}-content`]: {\n        position: 'relative'\n      },\n      [`${componentCls}-inner`]: {\n        backgroundColor: popoverBg,\n        backgroundClip: 'padding-box',\n        borderRadius: borderRadiusLG,\n        boxShadow: boxShadowSecondary,\n        padding: innerPadding\n      },\n      [`${componentCls}-title`]: {\n        minWidth: titleMinWidth,\n        marginBottom: titleMarginBottom,\n        color: colorTextHeading,\n        fontWeight: fontWeightStrong,\n        borderBottom: titleBorderBottom,\n        padding: titlePadding\n      },\n      [`${componentCls}-inner-content`]: {\n        color: popoverColor,\n        padding: innerContentPadding\n      }\n    })\n  },\n  // Arrow Style\n  getArrowStyle(token, 'var(--antd-arrow-background-color)'),\n  // Pure Render\n  {\n    [`${componentCls}-pure`]: {\n      position: 'relative',\n      maxWidth: 'none',\n      margin: token.sizePopupArrow,\n      display: 'inline-block',\n      [`${componentCls}-content`]: {\n        display: 'inline-block'\n      }\n    }\n  }];\n};\nconst genColorStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [componentCls]: PresetColors.map(colorKey => {\n      const lightColor = token[`${colorKey}6`];\n      return {\n        [`&${componentCls}-${colorKey}`]: {\n          '--antd-arrow-background-color': lightColor,\n          [`${componentCls}-inner`]: {\n            backgroundColor: lightColor\n          },\n          [`${componentCls}-arrow`]: {\n            background: 'transparent'\n          }\n        }\n      };\n    })\n  };\n};\nexport const prepareComponentToken = token => {\n  const {\n    lineWidth,\n    controlHeight,\n    fontHeight,\n    padding,\n    wireframe,\n    zIndexPopupBase,\n    borderRadiusLG,\n    marginXS,\n    lineType,\n    colorSplit,\n    paddingSM\n  } = token;\n  const titlePaddingBlockDist = controlHeight - fontHeight;\n  const popoverTitlePaddingBlockTop = titlePaddingBlockDist / 2;\n  const popoverTitlePaddingBlockBottom = titlePaddingBlockDist / 2 - lineWidth;\n  const popoverPaddingHorizontal = padding;\n  return Object.assign(Object.assign(Object.assign({\n    titleMinWidth: 177,\n    zIndexPopup: zIndexPopupBase + 30\n  }, getArrowToken(token)), getArrowOffsetToken({\n    contentRadius: borderRadiusLG,\n    limitVerticalRadius: true\n  })), {\n    // internal\n    innerPadding: wireframe ? 0 : 12,\n    titleMarginBottom: wireframe ? 0 : marginXS,\n    titlePadding: wireframe ? `${popoverTitlePaddingBlockTop}px ${popoverPaddingHorizontal}px ${popoverTitlePaddingBlockBottom}px` : 0,\n    titleBorderBottom: wireframe ? `${lineWidth}px ${lineType} ${colorSplit}` : 'none',\n    innerContentPadding: wireframe ? `${paddingSM}px ${popoverPaddingHorizontal}px` : 0\n  });\n};\nexport default genStyleHooks('Popover', token => {\n  const {\n    colorBgElevated,\n    colorText\n  } = token;\n  const popoverToken = mergeToken(token, {\n    popoverBg: colorBgElevated,\n    popoverColor: colorText\n  });\n  return [genBaseStyle(popoverToken), genColorStyle(popoverToken), initZoomMotion(popoverToken, 'zoom-big')];\n}, prepareComponentToken, {\n  resetStyle: false,\n  deprecatedTokens: [['width', 'titleMinWidth'], ['minWidth', 'titleMinWidth']]\n});", "function omit(obj, fields) {\n  // eslint-disable-next-line prefer-object-spread\n  var shallowCopy = Object.assign({}, obj);\n\n  for (var i = 0; i < fields.length; i += 1) {\n    var key = fields[i];\n    delete shallowCopy[key];\n  }\n\n  return shallowCopy;\n}\n\nexport default omit;", "/**\n * @license React\n * use-sync-external-store-shim.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var e=require(\"react\");function h(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var k=\"function\"===typeof Object.is?Object.is:h,l=e.useState,m=e.useEffect,n=e.useLayoutEffect,p=e.useDebugValue;function q(a,b){var d=b(),f=l({inst:{value:d,getSnapshot:b}}),c=f[0].inst,g=f[1];n(function(){c.value=d;c.getSnapshot=b;r(c)&&g({inst:c})},[a,d,b]);m(function(){r(c)&&g({inst:c});return a(function(){r(c)&&g({inst:c})})},[a]);p(d);return d}\nfunction r(a){var b=a.getSnapshot;a=a.value;try{var d=b();return!k(a,d)}catch(f){return!0}}function t(a,b){return b()}var u=\"undefined\"===typeof window||\"undefined\"===typeof window.document||\"undefined\"===typeof window.document.createElement?t:q;exports.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:u;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.min.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "import React, { useEffect, useLayoutEffect, createContext, useContext, useMemo, useRef, createElement } from 'react';\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\nconst mergeObjects = (a, b)=>({\n        ...a,\n        ...b\n    });\nconst isPromiseLike = (x)=>isFunction(x.then);\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const constructor = arg && arg.constructor;\n    const isDate = constructor == Date;\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && constructor != RegExp) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (constructor == Array) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (constructor == OBJECT) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\n\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = 'undefined';\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = typeof window != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst hasRequestAnimationFrame = ()=>isWindowDefined && typeof window['requestAnimationFrame'] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key)=>{\n    const state = SWRGlobalState.get(cache);\n    return [\n        // Getter\n        ()=>!isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n        // Setter\n        (info)=>{\n            if (!isUndefined(key)) {\n                const prev = cache.get(key);\n                // Before writing to the store, we keep the value in the initial cache\n                // if it's not there yet.\n                if (!(key in INITIAL_CACHE)) {\n                    INITIAL_CACHE[key] = prev;\n                }\n                state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n            }\n        },\n        // Subscriber\n        state[6],\n        // Get server cache snapshot\n        ()=>{\n            if (!isUndefined(key)) {\n                // If the cache was updated on the client, we return the stored initial value.\n                if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n            }\n            // If we haven't done any client-side updates, we return the current value.\n            return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n        }\n    ];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */ let online = true;\nconst isOnline = ()=>online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [\n    window.addEventListener.bind(window),\n    window.removeEventListener.bind(window)\n] : [\n    noop,\n    noop\n];\nconst isVisible = ()=>{\n    const visibilityState = isDocumentDefined && document.visibilityState;\n    return isUndefined(visibilityState) || visibilityState !== 'hidden';\n};\nconst initFocus = (callback)=>{\n    // focus revalidate\n    if (isDocumentDefined) {\n        document.addEventListener('visibilitychange', callback);\n    }\n    onWindowEvent('focus', callback);\n    return ()=>{\n        if (isDocumentDefined) {\n            document.removeEventListener('visibilitychange', callback);\n        }\n        offWindowEvent('focus', callback);\n    };\n};\nconst initReconnect = (callback)=>{\n    // revalidate on reconnected\n    const onOnline = ()=>{\n        online = true;\n        callback();\n    };\n    // nothing to revalidate, just update the status\n    const onOffline = ()=>{\n        online = false;\n    };\n    onWindowEvent('online', onOnline);\n    onWindowEvent('offline', onOffline);\n    return ()=>{\n        offWindowEvent('online', onOnline);\n        offWindowEvent('offline', onOffline);\n    };\n};\nconst preset = {\n    isOnline,\n    isVisible\n};\nconst defaultConfigOptions = {\n    initFocus,\n    initReconnect\n};\n\nconst IS_REACT_LEGACY = !React.useId;\nconst IS_SERVER = !isWindowDefined || 'Deno' in window;\n// Polyfill requestAnimationFrame\nconst rAF = (f)=>hasRequestAnimationFrame() ? window['requestAnimationFrame'](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? useEffect : useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== 'undefined' && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && ([\n    'slow-2g',\n    '2g'\n].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = ()=>++__timestamp;\n\nconst FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\n\nvar events = {\n  __proto__: null,\n  ERROR_REVALIDATE_EVENT: ERROR_REVALIDATE_EVENT,\n  FOCUS_EVENT: FOCUS_EVENT,\n  MUTATE_EVENT: MUTATE_EVENT,\n  RECONNECT_EVENT: RECONNECT_EVENT\n};\n\nasync function internalMutate(...args) {\n    const [cache, _key, _data, _opts] = args;\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = mergeObjects({\n        populateCache: true,\n        throwOnError: true\n    }, typeof _opts === 'boolean' ? {\n        revalidate: _opts\n    } : _opts || {});\n    let populateCache = options.populateCache;\n    const rollbackOnErrorOption = options.rollbackOnError;\n    let optimisticData = options.optimisticData;\n    const rollbackOnError = (error)=>{\n        return typeof rollbackOnErrorOption === 'function' ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n    };\n    const throwOnError = options.throwOnError;\n    // If the second argument is a key filter, return the mutation results for all\n    // filtered keys.\n    if (isFunction(_key)) {\n        const keyFilter = _key;\n        const matchedKeys = [];\n        const it = cache.keys();\n        for (const key of it){\n            if (// Skip the special useSWRInfinite and useSWRSubscription keys.\n            !/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n                matchedKeys.push(key);\n            }\n        }\n        return Promise.all(matchedKeys.map(mutateByKey));\n    }\n    return mutateByKey(_key);\n    async function mutateByKey(_k) {\n        // Serialize key\n        const [key] = serialize(_k);\n        if (!key) return;\n        const [get, set] = createCacheHelper(cache, key);\n        const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n        const startRevalidate = ()=>{\n            const revalidators = EVENT_REVALIDATORS[key];\n            const revalidate = isFunction(options.revalidate) ? options.revalidate(get().data, _k) : options.revalidate !== false;\n            if (revalidate) {\n                // Invalidate the key by deleting the concurrent request markers so new\n                // requests will not be deduped.\n                delete FETCH[key];\n                delete PRELOAD[key];\n                if (revalidators && revalidators[0]) {\n                    return revalidators[0](MUTATE_EVENT).then(()=>get().data);\n                }\n            }\n            return get().data;\n        };\n        // If there is no new data provided, revalidate the key with current state.\n        if (args.length < 3) {\n            // Revalidate and broadcast state.\n            return startRevalidate();\n        }\n        let data = _data;\n        let error;\n        // Update global timestamps.\n        const beforeMutationTs = getTimestamp();\n        MUTATION[key] = [\n            beforeMutationTs,\n            0\n        ];\n        const hasOptimisticData = !isUndefined(optimisticData);\n        const state = get();\n        // `displayedData` is the current value on screen. It could be the optimistic value\n        // that is going to be overridden by a `committedData`, or get reverted back.\n        // `committedData` is the validated value that comes from a fetch or mutation.\n        const displayedData = state.data;\n        const currentData = state._c;\n        const committedData = isUndefined(currentData) ? displayedData : currentData;\n        // Do optimistic data update.\n        if (hasOptimisticData) {\n            optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n            // When we set optimistic data, backup the current committedData data in `_c`.\n            set({\n                data: optimisticData,\n                _c: committedData\n            });\n        }\n        if (isFunction(data)) {\n            // `data` is a function, call it passing current cache value.\n            try {\n                data = data(committedData);\n            } catch (err) {\n                // If it throws an error synchronously, we shouldn't update the cache.\n                error = err;\n            }\n        }\n        // `data` is a promise/thenable, resolve the final data first.\n        if (data && isPromiseLike(data)) {\n            // This means that the mutation is async, we need to check timestamps to\n            // avoid race conditions.\n            data = await data.catch((err)=>{\n                error = err;\n            });\n            // Check if other mutations have occurred since we've started this mutation.\n            // If there's a race we don't update cache or broadcast the change,\n            // just return the data.\n            if (beforeMutationTs !== MUTATION[key][0]) {\n                if (error) throw error;\n                return data;\n            } else if (error && hasOptimisticData && rollbackOnError(error)) {\n                // Rollback. Always populate the cache in this case but without\n                // transforming the data.\n                populateCache = true;\n                // Reset data to be the latest committed data, and clear the `_c` value.\n                set({\n                    data: committedData,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // If we should write back the cache after request.\n        if (populateCache) {\n            if (!error) {\n                // Transform the result into data.\n                if (isFunction(populateCache)) {\n                    const populateCachedData = populateCache(data, committedData);\n                    set({\n                        data: populateCachedData,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                } else {\n                    // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n                    set({\n                        data,\n                        error: UNDEFINED,\n                        _c: UNDEFINED\n                    });\n                }\n            }\n        }\n        // Reset the timestamp to mark the mutation has ended.\n        MUTATION[key][1] = getTimestamp();\n        // Update existing SWR Hooks' internal states:\n        Promise.resolve(startRevalidate()).then(()=>{\n            // The mutation and revalidation are ended, we can clear it since the data is\n            // not an optimistic value anymore.\n            set({\n                _c: UNDEFINED\n            });\n        });\n        // Throw error or return data\n        if (error) {\n            if (throwOnError) throw error;\n            return;\n        }\n        return data;\n    }\n}\n\nconst revalidateAllKeys = (revalidators, type)=>{\n    for(const key in revalidators){\n        if (revalidators[key][0]) revalidators[key][0](type);\n    }\n};\nconst initCache = (provider, options)=>{\n    // The global state for a specific provider will be used to deduplicate\n    // requests and store listeners. As well as a mutate function that is bound to\n    // the cache.\n    // The provider's global state might be already initialized. Let's try to get the\n    // global state associated with the provider first.\n    if (!SWRGlobalState.has(provider)) {\n        const opts = mergeObjects(defaultConfigOptions, options);\n        // If there's no global state bound to the provider, create a new one with the\n        // new mutate function.\n        const EVENT_REVALIDATORS = {};\n        const mutate = internalMutate.bind(UNDEFINED, provider);\n        let unmount = noop;\n        const subscriptions = {};\n        const subscribe = (key, callback)=>{\n            const subs = subscriptions[key] || [];\n            subscriptions[key] = subs;\n            subs.push(callback);\n            return ()=>subs.splice(subs.indexOf(callback), 1);\n        };\n        const setter = (key, value, prev)=>{\n            provider.set(key, value);\n            const subs = subscriptions[key];\n            if (subs) {\n                for (const fn of subs){\n                    fn(value, prev);\n                }\n            }\n        };\n        const initProvider = ()=>{\n            if (!SWRGlobalState.has(provider)) {\n                // Update the state if it's new, or if the provider has been extended.\n                SWRGlobalState.set(provider, [\n                    EVENT_REVALIDATORS,\n                    {},\n                    {},\n                    {},\n                    mutate,\n                    setter,\n                    subscribe\n                ]);\n                if (!IS_SERVER) {\n                    // When listening to the native events for auto revalidations,\n                    // we intentionally put a delay (setTimeout) here to make sure they are\n                    // fired after immediate JavaScript executions, which can be\n                    // React's state updates.\n                    // This avoids some unnecessary revalidations such as\n                    // https://github.com/vercel/swr/issues/1680.\n                    const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, FOCUS_EVENT)));\n                    const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, RECONNECT_EVENT)));\n                    unmount = ()=>{\n                        releaseFocus && releaseFocus();\n                        releaseReconnect && releaseReconnect();\n                        // When un-mounting, we need to remove the cache provider from the state\n                        // storage too because it's a side-effect. Otherwise, when re-mounting we\n                        // will not re-register those event listeners.\n                        SWRGlobalState.delete(provider);\n                    };\n                }\n            }\n        };\n        initProvider();\n        // This is a new provider, we need to initialize it and setup DOM events\n        // listeners for `focus` and `reconnect` actions.\n        // We might want to inject an extra layer on top of `provider` in the future,\n        // such as key serialization, auto GC, etc.\n        // For now, it's just a `Map` interface without any modifications.\n        return [\n            provider,\n            mutate,\n            initProvider,\n            unmount\n        ];\n    }\n    return [\n        provider,\n        SWRGlobalState.get(provider)[4]\n    ];\n};\n\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts)=>{\n    const maxRetryCount = config.errorRetryCount;\n    const currentRetryCount = opts.retryCount;\n    // Exponential backoff\n    const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n    if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n        return;\n    }\n    setTimeout(revalidate, timeout, opts);\n};\nconst compare = (currentData, newData)=>stableHash(currentData) == stableHash(newData);\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n    // events\n    onLoadingSlow: noop,\n    onSuccess: noop,\n    onError: noop,\n    onErrorRetry,\n    onDiscarded: noop,\n    // switches\n    revalidateOnFocus: true,\n    revalidateOnReconnect: true,\n    revalidateIfStale: true,\n    shouldRetryOnError: true,\n    // timeouts\n    errorRetryInterval: slowConnection ? 10000 : 5000,\n    focusThrottleInterval: 5 * 1000,\n    dedupingInterval: 2 * 1000,\n    loadingTimeout: slowConnection ? 5000 : 3000,\n    // providers\n    compare,\n    isPaused: ()=>false,\n    cache,\n    mutate,\n    fallback: {}\n}, // use web preset by default\npreset);\n\nconst mergeConfigs = (a, b)=>{\n    // Need to create a new object to avoid mutating the original here.\n    const v = mergeObjects(a, b);\n    // If two configs are provided, merge their `use` and `fallback` options.\n    if (b) {\n        const { use: u1, fallback: f1 } = a;\n        const { use: u2, fallback: f2 } = b;\n        if (u1 && u2) {\n            v.use = u1.concat(u2);\n        }\n        if (f1 && f2) {\n            v.fallback = mergeObjects(f1, f2);\n        }\n    }\n    return v;\n};\n\nconst SWRConfigContext = createContext({});\nconst SWRConfig = (props)=>{\n    const { value } = props;\n    const parentConfig = useContext(SWRConfigContext);\n    const isFunctionalConfig = isFunction(value);\n    const config = useMemo(()=>isFunctionalConfig ? value(parentConfig) : value, [\n        isFunctionalConfig,\n        parentConfig,\n        value\n    ]);\n    // Extend parent context values and middleware.\n    const extendedConfig = useMemo(()=>isFunctionalConfig ? config : mergeConfigs(parentConfig, config), [\n        isFunctionalConfig,\n        parentConfig,\n        config\n    ]);\n    // Should not use the inherited provider.\n    const provider = config && config.provider;\n    // initialize the cache only on first access.\n    const cacheContextRef = useRef(UNDEFINED);\n    if (provider && !cacheContextRef.current) {\n        cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n    }\n    const cacheContext = cacheContextRef.current;\n    // Override the cache if a new provider is given.\n    if (cacheContext) {\n        extendedConfig.cache = cacheContext[0];\n        extendedConfig.mutate = cacheContext[1];\n    }\n    // Unsubscribe events.\n    useIsomorphicLayoutEffect(()=>{\n        if (cacheContext) {\n            cacheContext[2] && cacheContext[2]();\n            return cacheContext[3];\n        }\n    }, []);\n    return createElement(SWRConfigContext.Provider, mergeObjects(props, {\n        value: extendedConfig\n    }));\n};\n\nconst INFINITE_PREFIX = '$inf$';\n\n// @ts-expect-error\nconst enableDevtools = isWindowDefined && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = ()=>{\n    if (enableDevtools) {\n        // @ts-expect-error\n        window.__SWR_DEVTOOLS_REACT__ = React;\n    }\n};\n\nconst normalize = (args)=>{\n    return isFunction(args[1]) ? [\n        args[0],\n        args[1],\n        args[2] || {}\n    ] : [\n        args[0],\n        null,\n        (args[1] === null ? args[2] : args[1]) || {}\n    ];\n};\n\nconst useSWRConfig = ()=>{\n    return mergeObjects(defaultConfig, useContext(SWRConfigContext));\n};\n\nconst preload = (key_, fetcher)=>{\n    const [key, fnArg] = serialize(key_);\n    const [, , , PRELOAD] = SWRGlobalState.get(cache);\n    // Prevent preload to be called multiple times before used.\n    if (PRELOAD[key]) return PRELOAD[key];\n    const req = fetcher(fnArg);\n    PRELOAD[key] = req;\n    return req;\n};\nconst middleware = (useSWRNext)=>(key_, fetcher_, config)=>{\n        // fetcher might be a sync function, so this should not be an async function\n        const fetcher = fetcher_ && ((...args)=>{\n            const [key] = serialize(key_);\n            const [, , , PRELOAD] = SWRGlobalState.get(cache);\n            if (key.startsWith(INFINITE_PREFIX)) {\n                // we want the infinite fetcher to be called.\n                // handling of the PRELOAD cache happens there.\n                return fetcher_(...args);\n            }\n            const req = PRELOAD[key];\n            if (isUndefined(req)) return fetcher_(...args);\n            delete PRELOAD[key];\n            return req;\n        });\n        return useSWRNext(key_, fetcher, config);\n    };\n\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = (hook)=>{\n    return function useSWRArgs(...args) {\n        // Get the default and inherited configuration.\n        const fallbackConfig = useSWRConfig();\n        // Normalize arguments.\n        const [key, fn, _config] = normalize(args);\n        // Merge configurations.\n        const config = mergeConfigs(fallbackConfig, _config);\n        // Apply middleware\n        let next = hook;\n        const { use } = config;\n        const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n        for(let i = middleware.length; i--;){\n            next = middleware[i](next);\n        }\n        return next(key, fn || config.fetcher || null, config);\n    };\n};\n\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback)=>{\n    const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n    keyedRevalidators.push(callback);\n    return ()=>{\n        const index = keyedRevalidators.indexOf(callback);\n        if (index >= 0) {\n            // O(1): faster than splice\n            keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n            keyedRevalidators.pop();\n        }\n    };\n};\n\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware)=>{\n    return (...args)=>{\n        const [key, fn, config] = normalize(args);\n        const uses = (config.use || []).concat(middleware);\n        return useSWR(key, fn, {\n            ...config,\n            use: uses\n        });\n    };\n};\n\nsetupDevTools();\n\nexport { INFINITE_PREFIX, IS_REACT_LEGACY, IS_SERVER, OBJECT, SWRConfig, SWRGlobalState, UNDEFINED, cache, compare, createCacheHelper, defaultConfig, defaultConfigOptions, getTimestamp, hasRequestAnimationFrame, initCache, internalMutate, isDocumentDefined, isFunction, isPromiseLike, isUndefined, isWindowDefined, mergeConfigs, mergeObjects, mutate, noop, normalize, preload, preset, rAF, events as revalidateEvents, serialize, slowConnection, stableHash, subscribeCallback, useIsomorphicLayoutEffect, useSWRConfig, withArgs, withMiddleware };\n", "import 'client-only';\nimport ReactExports, { useRef, useMemo, useCallback, useDebugValue } from 'react';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js';\nimport { serialize, OBJECT, SWRConfig as SWRConfig$1, defaultConfig, withArgs, SWRGlobalState, createCacheHelper, isUndefined, getTimestamp, UNDEFINED, isFunction, revalidateEvents, internalMutate, useIsomorphicLayoutEffect, subscribeCallback, IS_SERVER, rAF, IS_REACT_LEGACY, mergeObjects } from 'swr/_internal';\nexport { mutate, preload, useSWRConfig } from 'swr/_internal';\n\nconst unstable_serialize = (key)=>serialize(key)[0];\n\n/// <reference types=\"react/experimental\" />\nconst use = ReactExports.use || ((promise)=>{\n    if (promise.status === 'pending') {\n        throw promise;\n    } else if (promise.status === 'fulfilled') {\n        return promise.value;\n    } else if (promise.status === 'rejected') {\n        throw promise.reason;\n    } else {\n        promise.status = 'pending';\n        promise.then((v)=>{\n            promise.status = 'fulfilled';\n            promise.value = v;\n        }, (e)=>{\n            promise.status = 'rejected';\n            promise.reason = e;\n        });\n        throw promise;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache, compare, suspense, fallbackData, revalidateOnMount, revalidateIfStale, refreshInterval, refreshWhenHidden, refreshWhenOffline, keepPreviousData } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = serialize(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = useRef(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = useRef(false);\n    // Refs to keep the key and config.\n    const keyRef = useRef(key);\n    const fetcherRef = useRef(fetcher);\n    const configRef = useRef(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = createCacheHelper(cache, key);\n    const stateDependencies = useRef({}).current;\n    const fallback = isUndefined(fallbackData) ? config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === 'data') {\n                if (!compare(prev[t], current[t])) {\n                    if (!isUndefined(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = useMemo(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!isUndefined(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            if (!isUndefined(revalidateIfStale)) return revalidateIfStale;\n            return true;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = mergeObjects(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = useSyncExternalStore(useCallback((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = isUndefined(cachedData) ? fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = useRef(data);\n    const returnedData = keepPreviousData ? isUndefined(cachedData) ? laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !isUndefined(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !isUndefined(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return isUndefined(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return isUndefined(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = isUndefined(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = isUndefined(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = useCallback(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (IS_REACT_LEGACY) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if (isUndefined(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && isUndefined(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    getTimestamp()\n                ];\n            }\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = UNDEFINED;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!isUndefined(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || isFunction(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (!getConfig().revalidateOnFocus || !getConfig().revalidateOnReconnect || isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](revalidateEvents.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n    // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    const boundMutate = useCallback(// Use callback to make sure `keyRef.current` returns latest result every time\n    (...args)=>{\n        return internalMutate(cache, keyRef.current, ...args);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // The logic for updating refs.\n    useIsomorphicLayoutEffect(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!isUndefined(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    useIsomorphicLayoutEffect(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(UNDEFINED, WITH_DEDUPE);\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        let nextFocusRevalidatedAt = 0;\n        const onRevalidate = (type, opts = {})=>{\n            if (type == revalidateEvents.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == revalidateEvents.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == revalidateEvents.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == revalidateEvents.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = subscribeCallback(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            if (isUndefined(data) || IS_SERVER) {\n                // Revalidate immediately.\n                softRevalidate();\n            } else {\n                // Delay the revalidate if we have data to return so we won't block\n                // rendering.\n                rAF(softRevalidate);\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    useIsomorphicLayoutEffect(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = isFunction(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    useDebugValue(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense && isUndefined(data) && key) {\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any initial data. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!IS_REACT_LEGACY && IS_SERVER) {\n            throw new Error('Fallback data is required when using suspense in SSR.');\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        unmountedRef.current = false;\n        const req = PRELOAD[key];\n        if (!isUndefined(req)) {\n            const promise = boundMutate(req);\n            use(promise);\n        }\n        if (isUndefined(error)) {\n            const promise = revalidate(WITH_DEDUPE);\n            if (!isUndefined(returnedData)) {\n                promise.status = 'fulfilled';\n                promise.value = true;\n            }\n            use(promise);\n        } else {\n            throw error;\n        }\n    }\n    return {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n};\nconst SWRConfig = OBJECT.defineProperty(SWRConfig$1, 'defaultValue', {\n    value: defaultConfig\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error, isLoading } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (isLoading) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = withArgs(useSWRHandler);\n\nexport { SWRConfig, useSWR as default, unstable_serialize };\n"], "names": ["hueStep", "saturationStep", "saturationStep2", "brightnessStep1", "brightnessStep2", "lightColorCount", "darkColorCount", "darkColorMap", "toHsv", "_ref", "r", "g", "b", "hsv", "toHex", "_ref2", "mix", "rgb1", "rgb2", "amount", "p", "rgb", "getHue", "i", "light", "hue", "getSaturation", "saturation", "getValue", "value", "generate", "color", "opts", "patterns", "pColor", "colorString", "_i", "_hsv", "_colorString", "_ref3", "index", "opacity", "darkColorString", "presetPrimaryColors", "red", "volcano", "orange", "gold", "yellow", "lime", "green", "cyan", "blue", "geekblue", "purple", "magenta", "grey", "gray", "presetPalettes", "redDark", "volcanoDark", "orangeDark", "goldDark", "yellowDark", "limeDark", "greenDark", "cyanDark", "blueDark", "geekblueDark", "purpleDark", "magentaDark", "greyDark", "presetDarkPalettes", "getLayoutDesignToken", "designTokens", "antdToken", "_finalDesignTokens$si", "_finalDesignTokens$he", "_finalDesignTokens$he2", "_finalDesignTokens$pa", "_finalDesignTokens$pa2", "finalDesignTokens", "merge", "obj", "_len", "rest", "_key", "il", "key", "_excluded", "_excluded2", "omitUndefined", "newObj", "isNeedOpenHash", "_process$env$NODE_ENV", "_process$env$NODE_ENV2", "process", "ProConfigContext", "ConfigConsumer", "<PERSON><PERSON><PERSON><PERSON>", "_useSWRConfig", "cache", "ConfigProviderContainer", "props", "_proTheme$useToken", "children", "dark", "valueTypeMap", "_props$autoClearCache", "autoClearCache", "propsToken", "prefixCls", "intl", "_useContext", "locale", "getPrefixCls", "restConfig", "tokenContext", "proProvide", "proComponentsCls", "antCls", "salt", "proLayoutTokenMerge", "proProvideValue", "_proProvide$intl", "localeName", "resolvedIntl", "finalToken", "_useCacheToken", "_useCacheToken2", "token", "nativeHashId", "hashed", "hashId", "themeConfig", "proConfigContextValue", "configProviderDom", "ProConfigProvider", "needDeps", "_useContext2", "theme", "isNullProvide", "mergeAlgorithm", "isDark", "config<PERSON><PERSON><PERSON>", "zh_CN", "useIntl", "_useContext3", "_useContext4", "ProProvider", "createIntl", "localeMap", "id", "defaultMessage", "msg", "localKey", "intlMap", "mnMNIntl", "arEGIntl", "zhCNIntl", "enUSIntl", "enGBIntl", "viVNIntl", "itITIntl", "jaJPIntl", "esESIntl", "caESIntl", "ruRUIntl", "srRSIntl", "msMYIntl", "zhTWIntl", "frFRIntl", "ptBRIntl", "koKRIntl", "idIDIntl", "deDEIntl", "faIRIntl", "trTRIntl", "plPLIntl", "hrHRIntl", "thTHIntl", "csCZIntl", "skSKIntl", "heILIntl", "ukUAIntl", "uzUZIntl", "nlNLIntl", "roROIntl", "svSEIntl", "intlMapKeys", "findIntlKeyByAntdLocaleKey", "localeKey", "intlKey", "LowerCaseKey", "<PERSON><PERSON><PERSON><PERSON>", "baseColor", "alpha", "lighten", "brightness", "instance", "genTheme", "proTheme", "useToken", "resetComponent", "operationUnit", "useStyle", "componentName", "styleFn", "_token$proComponentsC", "_useContext$token", "_useToken", "provideTheme", "csp", "_theme$defaultAlgorit", "defaultToken", "hashCode", "str", "seed", "h1", "h2", "ch", "emptyTheme", "semver", "isWildcard", "s", "try<PERSON><PERSON><PERSON>", "v", "forceType", "a", "compareStrings", "_forceType", "_forceType2", "ap", "bp", "compareSegments", "validateAndParse", "version", "_match$shift", "match", "compareVersions", "v1", "v2", "n1", "n2", "p1", "p2", "getRenderPropValue", "propValue", "useForceUpdate", "forceUpdate", "x", "responsiveArray", "getResponsiveMap", "validateBreakpoints", "indexableToken", "revBreakpoints", "breakpoint", "breakpointUpper", "screenMin", "screen", "screenMax", "nextScreenMin", "useResponsiveObserver", "responsiveMap", "subscribers", "subUid", "screens", "pointMap", "func", "paramToken", "matchMediaQuery", "handler", "listener", "matches", "mql", "matchScreen", "screenSizes", "genBaseStyle", "avatarSizeStyle", "size", "fontSize", "radius", "componentCls", "iconCls", "avatarColor", "avatarBg", "lineWidth", "lineType", "containerSize", "textFontSize", "borderRadius", "containerSizeLG", "textFontSizeLG", "borderRadiusLG", "containerSizeSM", "textFontSizeSM", "borderRadiusSM", "genGroupStyle", "groupBorderColor", "groupOverlapping", "groupSpace", "prepareComponentToken", "controlHeight", "controlHeightLG", "controlHeightSM", "fontSizeLG", "fontSizeXL", "fontSizeHeading3", "marginXXS", "marginXS", "colorBorderBg", "avatarToken", "colorTextPlaceholder", "colorTextLightSolid", "__rest", "e", "t", "InternalAvatar", "ref", "scale", "setScale", "mounted", "setMounted", "isImgExist", "setIsImgExist", "avatarNodeRef", "avatar<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "avatarNodeMergedRef", "avatarCtx", "setScaleParam", "children<PERSON><PERSON>th", "nodeWidth", "gap", "handleImgLoadError", "onError", "customizePrefixCls", "customSize", "others", "useSize", "ctxSize", "_a", "_b", "needResponsive", "useBreakpoint", "responsiveSizeStyle", "currentBreakpoint", "currentSize", "icon", "rootCls", "useCSSVarCls", "wrapCSSVar", "cssVarCls", "sizeCls", "hasImageElement", "src", "mergedShape", "shape", "classString", "avatar", "className", "rootClassName", "sizeStyle", "children<PERSON><PERSON><PERSON><PERSON>", "draggable", "srcSet", "alt", "crossOrigin", "transformString", "childrenStyle", "AvatarContextProvider", "avatarContextValue", "_c", "groupPrefixCls", "cls", "direction", "childrenWithProps", "toArray", "child", "mergeCount", "max", "maxCount", "numOfChildren", "childrenShow", "childrenH<PERSON>den", "mergeStyle", "maxStyle", "mergePopoverTrigger", "maxPopoverTrigger", "mergePopoverPlacement", "maxPopoverPlacement", "mergeProps", "style", "refreshOnChange", "screensRef", "responsiveObserver", "supportScreens", "Overlay", "title", "content", "RawPurePanel", "titleNode", "contentNode", "placement", "PurePanel", "restProps", "Popover", "otherProps", "rootPrefixCls", "overlayCls", "overlayClassName", "open", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "onOpenChange", "onKeyDown", "onInternalOpenChange", "trigger", "mouseEnterDelay", "mouseLeaveDelay", "overlayStyle", "zIndexPopup", "colorBgElevated", "popoverBg", "boxShadowSecondary", "innerPadding", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "titleMarginBottom", "colorTextHeading", "fontWeightStrong", "titleBorderBottom", "titlePadding", "popoverColor", "innerContentPadding", "genColorStyle", "colorKey", "lightColor", "titlePaddingBlockDist", "fontHeight", "popoverTitlePaddingBlockTop", "popoverTitlePaddingBlockBottom", "popoverPaddingHorizontal", "padding", "zIndexPopupBase", "wireframe", "colorSplit", "paddingSM", "popoverToken", "colorText", "omit", "fields", "shallowCopy", "h", "k", "l", "m", "n", "q", "d", "f", "u", "exports", "module", "noop", "UNDEFINED", "OBJECT", "isUndefined", "isFunction", "mergeObjects", "isPromiseLike", "table", "counter", "stableHash", "arg", "type", "constructor", "isDate", "result", "keys", "SWRGlobalState", "EMPTY_CACHE", "INITIAL_CACHE", "STR_UNDEFINED", "isWindowDefined", "isDocumentDefined", "hasRequestAnimationFrame", "createCacheHelper", "state", "info", "prev", "online", "isOnline", "onWindowEvent", "offWindowEvent", "isVisible", "visibilityState", "initFocus", "callback", "initReconnect", "onOnline", "onOffline", "preset", "defaultConfigOptions", "IS_REACT_LEGACY", "IS_SERVER", "rAF", "useIsomorphicLayoutEffect", "navigatorConnection", "slowConnection", "err", "args", "__timestamp", "getTimestamp", "FOCUS_EVENT", "RECONNECT_EVENT", "MUTATE_EVENT", "events", "internalMutate", "_data", "_opts", "options", "populateCache", "rollbackOnErrorOption", "optimisticData", "rollbackOnError", "error", "throwOnError", "keyFilter", "<PERSON><PERSON><PERSON><PERSON>", "it", "mutateByKey", "_k", "get", "set", "EVENT_REVALIDATORS", "MUTATION", "FETCH", "PRELOAD", "startRevalidate", "revalidators", "data", "beforeMutationTs", "hasOptimisticData", "displayedData", "currentData", "committedData", "populateCachedData", "revalidateAllKeys", "initCache", "provider", "mutate", "unmount", "subscriptions", "subscribe", "subs", "setter", "fn", "initProvider", "releaseFocus", "releaseReconnect", "onErrorRetry", "_", "__", "config", "revalidate", "maxRetryCount", "currentRetryCount", "timeout", "compare", "newData", "defaultConfig", "mergeConfigs", "u1", "f1", "u2", "f2", "SWRConfigContext", "SWRConfig", "parentConfig", "isFunctionalConfig", "extendedConfig", "cacheContextRef", "cacheContext", "INFINITE_PREFIX", "enableDevtools", "use", "setupDevTools", "normalize", "useSWRConfig", "preload", "key_", "fetcher", "fnArg", "req", "middleware", "useSWRNext", "fetcher_", "BUILT_IN_MIDDLEWARE", "<PERSON><PERSON><PERSON><PERSON>", "hook", "fallbackConfig", "_config", "next", "subscribeCallback", "callbacks", "keyedRevalidators", "withMiddleware", "useSWR", "uses", "unstable_serialize", "promise", "WITH_DEDUPE", "useSWRHandler", "initialMountedRef", "unmountedRef", "keyRef", "fetcherRef", "configRef", "getConfig", "isActive", "getCache", "setCache", "subscribeCache", "getInitialCache", "stateDependencies", "fallback", "fallbackD<PERSON>", "isEqual", "current", "returnedData", "getSnapshot", "shouldStartRequest", "revalidateOnMount", "suspense", "revalidateIfStale", "getSelectedCache", "snapshot", "cachedData", "initialData", "clientSnapshot", "serverSnapshot", "memorizedSnapshot", "newSnapshot", "cached", "isInitialMount", "hasRevalidator", "laggyDataRef", "keepPreviousData", "shouldDoInitialRevalidation", "defaultValidatingState", "isValidating", "isLoading", "revalidateOpts", "current<PERSON><PERSON>cher", "startAt", "loading", "shouldStartNewRequest", "callback<PERSON><PERSON><PERSON><PERSON>", "finalState", "finishRequestAndUpdateState", "cleanupState", "requestInfo", "initialState", "mutationInfo", "cacheData", "currentConfig", "shouldRetryOnError", "boundMutate", "softRevalidate", "nextFocusRevalidatedAt", "unsubEvents", "now", "timer", "interval", "refreshInterval", "execute", "refreshWhenHidden", "refreshWhenOffline"], "sourceRoot": ""}