{"version": 3, "file": "p__experimental-procedure__report-details__index.0546ec9d.async.js", "mappings": "2MAGMA,KAAoBC,EAAAA,MAAK,kBAC7B,mEAAyCC,KAAK,SAACC,EAAQ,CAAF,MAAM,CACzD,QAASA,EAAM,OACjB,CAAC,CAAC,CAAC,CACL,EAEe,SAASC,EAAgBC,EAA+B,CACrE,SACEC,EAAAA,KAACC,EAAAA,SAAQ,CACPC,YACEF,EAAAA,KAAA,OAAAG,YACEH,EAAAA,KAACI,EAAAA,EAAQ,CAACC,OAAM,GAAE,CAAC,CAChB,EACNF,YAEDH,EAAAA,KAACN,EAAiBY,EAAAA,EAAA,GAAKP,CAAK,CAAG,CAAC,CACxB,CAEd,C,sKCfaQ,EAAyB,SACpCC,EACqC,CACrC,OAAQA,EAAM,CACZ,IAAK,UACH,MAAO,UACT,IAAK,SACH,MAAO,SACT,QACE,OAAOA,CACX,CACF,EAEaC,EAAkC,SAAHC,EAAA,KAExCC,EAAED,EAAFC,GAAEC,EAAAF,EACFG,uBAAAA,EAAsBD,IAAA,OAAG,GAAEA,EAAAE,EAAAJ,EAC3BK,MAAAA,EAAKD,IAAA,OAAG,GAAEA,EAAAE,EAAAN,EACVO,UAAAA,EAASD,IAAA,OAAG,GAAEA,EAAAE,EAAAR,EACdS,WAAAA,EAAUD,IAAA,OAAG,EAACA,EAAAE,EAAAV,EACdW,IAAAA,GAAGD,IAAA,OAAG,GAAEA,EAAAE,EAAAZ,EACRa,WAAAA,EAAUD,IAAA,OAAG,GAAEA,EACfE,GAAMd,EAANc,OAAMC,EAAAf,EACNgB,eAAAA,GAAcD,IAAA,OAAG,GAAEA,EAAAE,EAAAjB,EACnBkB,eAAAA,EAAcD,IAAA,OAAG,UAASA,EAAAE,EAAAnB,EAC1BoB,MAAAA,EAAKD,IAAA,OAAG,GAAEA,EAAAE,EAAArB,EACVsB,QAAAA,EAAOD,IAAA,OAAG,GAAEA,EAAAE,EAAAvB,EACZwB,KAAAA,EAAID,IAAA,OAAG,GAAEA,EAAAE,EAAAzB,EACT0B,UAAAA,EAASD,IAAA,OAAG,GAAEA,EAAAE,EAAA3B,EACd4B,eAAAA,EAAcD,IAAA,OAAG,GAAEA,EAAAE,EAAA7B,EACnB8B,UAAAA,EAASD,IAAA,OAAG,GAAEA,EAAAE,EAAA/B,EACdgC,YAAAA,EAAWD,IAAA,OAAG,GAAKA,EAErBE,EAAeC,UAAAC,OAAA,GAAAD,UAAA,KAAAE,OAAAF,UAAA,GAAG,GAAK,MACN,CACjBjC,GAAAA,EACAU,IAAAA,GACAsB,OAAAA,EACAI,KAAM9B,GAAa,GACnB+B,sBAAuBnC,GAA0B,GACjDa,eAAgBA,IAAkB,GAClCX,MAAOA,GAAS,GAChBkC,UAAW,CACTzC,KAAMD,EAAuBqB,CAAc,EAC3CE,MAAOA,GAAS,GAChBE,QAASA,GAAW,GACpBE,KAAMA,GAAQ,GACdgB,GAAIV,GAAa,GACjBF,eAAgBA,GAAkB,GAClCF,UAAWA,GAAa,EAC1B,EACAe,YAAa5B,GAAc,GAC3B6B,YAAa5B,IAAUsB,OACvB3B,WAAYA,GAAc,EAC1BkC,SAAUX,GAAe,EAC3B,CAAC,EAEYY,EAAyB,SACpC9C,EACiB,CACjB,OAAQA,EAAM,CACZ,IAAK,UACH,SAAOR,EAAAA,KAACuD,EAAAA,EAAG,CAACC,MAAM,QAAOrD,SAAC,SAAO,CAAK,EACxC,IAAK,SACH,SAAOH,EAAAA,KAACuD,EAAAA,EAAG,CAACC,MAAM,OAAMrD,SAAC,QAAM,CAAK,EACtC,QACE,SAAOH,EAAAA,KAACuD,EAAAA,EAAG,CAACC,MAAM,SAAQrD,SAAC,QAAM,CAAK,CAC1C,CACF,EAEasD,EAA6B,SACxCd,EACAe,EACiB,CACjB,OAAQf,EAAQ,CACd,IAAK,GACH,SACE3C,EAAAA,KAACuD,EAAAA,EAAG,CAACC,MAAM,QAAOrD,SACfuD,KACGC,EAAAA,IAAK,EAAC,wBAAAC,OACoBF,CAAI,WAAAE,OACxBF,EAAI,UAAAE,UAAIC,EAAAA,IAAQ,eAAe,CAAC,KACtCA,EAAAA,IAAQ,eAAe,CAAC,CACzB,EAGT,IAAK,GACH,SACE7D,EAAAA,KAACuD,EAAAA,EAAG,CAACC,MAAM,OAAMrD,SACduD,KACGC,EAAAA,IAAK,EAAC,iBAAAC,OACaF,CAAI,WAAAE,OACjBF,EAAI,UAAAE,UAAIC,EAAAA,IAAQ,WAAW,CAAC,KAClCA,EAAAA,IAAQ,WAAW,CAAC,CACrB,EAET,QACE,SAAO7D,EAAAA,KAAA8D,EAAAA,SAAA,EAAI,CACf,CACF,C,wYCxGA,EAAe,CAAC,YAAc,sBAAsB,UAAY,oBAAoB,eAAiB,yBAAyB,aAAe,uBAAuB,cAAgB,wBAAwB,QAAU,kBAAkB,UAAY,oBAAoB,kBAAoB,4BAA4B,cAAgB,wBAAwB,OAAS,iBAAiB,SAAW,mBAAmB,SAAW,mBAAmB,YAAc,sBAAsB,MAAQ,gBAAgB,aAAe,uBAAuB,UAAY,oBAAoB,SAAW,kBAAkB,E,WCyBtkB,SAASC,GAAgB,KAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EACtCC,KAAyBC,EAAAA,UAAS,gBAAgB,EAA1CC,EAAYF,EAAZE,aACRC,KAAqBC,EAAAA,WAAU,EAAvBC,EAAQF,EAARE,SACRC,KAAgDC,EAAAA,UAAyB,EAACC,GAAAC,EAAAA,EAAAH,EAAA,GAAnEI,EAAgBF,GAAA,GAAEG,EAAmBH,GAAA,GAC5CI,MAAsDL,EAAAA,UAAiB,CAAC,EAACM,EAAAJ,EAAAA,EAAAG,GAAA,GAAlEE,GAAmBD,EAAA,GAAEE,GAAsBF,EAAA,GAE5CG,GAA2B,eAAA5E,EAAA6E,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAC,GAAA,KAAAC,EAAA,OAAAH,EAAAA,EAAA,EAAAI,KAAA,SAAAC,EAAA,eAAAA,EAAAC,KAAAD,EAAAE,KAAA,QAAAF,OAAAA,EAAAE,KAAA,KAChBC,EAAAA,IAA4B,CAC5CC,YAAatB,CACf,CAAC,EAAC,OAFIgB,EAAGE,EAAAK,QAGLC,EAAAA,IAAoBR,CAAG,EAAES,IAC3BnB,EAAoBU,GAAG,YAAHA,EAAKU,IAAI,EAC9B,wBAAAR,EAAAS,KAAA,IAAAZ,CAAA,EACF,oBAPgC,QAAAhF,EAAA6F,MAAA,KAAA3D,SAAA,SASjC4D,EAAAA,WAAU,UAAM,KAAAC,EACdpB,GACEL,GAAgB,OAAAyB,EAAhBzB,EAAkB0B,0BAAsB,MAAAD,IAAA,cAAxCA,EAA0CE,qBAC5C,CACF,EAAG,CAAC3B,GAAgB,OAAAhB,EAAhBgB,EAAkB0B,0BAAsB,MAAA1C,IAAA,cAAxCA,EAA0C2C,qBAAqB,CAAC,KAEpEH,EAAAA,WAAU,UAAM,CACdlB,GAA4B,CAC9B,EAAG,CAAC,CAAC,EAEL,IAAMsB,GAA0C,CAC9C,CACE9E,SAAO+B,EAAAA,IAAQ,YAAY,EAC3BgD,MAAO,IACPC,UAAW,SACXC,MAAO,OACPC,OAAQ,SAACC,EAAgB,CAAF,OACrBA,KACEjH,EAAAA,KAACF,EAAAA,EAAe,CAACoH,UAAWD,EAAQE,UAAU,YAAY,CAAE,EAE5D,EACD,CACL,EACA,CACErF,SAAO+B,EAAAA,IAAQ,YAAY,EAC3BgD,MAAO,GACPC,UAAW,YACb,EACA,CACEhF,SAAO+B,EAAAA,IAAQ,eAAe,EAC9BgD,MAAO,GACPC,UAAW,eACXE,OAAQ,SAACI,EAAGC,EAAQ,CAAF,IAAAC,EAAA,OAChBD,GAAM,OAAAC,EAAND,EAAQE,gBAAY,MAAAD,IAAA,QAApBA,EAAsBE,iBAAgB3D,EAAAA,IAAQ,KAAK,KAAIA,EAAAA,IAAQ,IAAI,CAAC,CACxE,EACA,CACE/B,SAAO+B,EAAAA,IAAQ,cAAc,EAC7BgD,MAAO,GACPC,UAAW,eACXE,OAAQ,SAACI,EAAGC,EAAQ,CAAF,IAAAI,EAAA,OAChBJ,GAAM,OAAAI,EAANJ,EAAQE,gBAAY,MAAAE,IAAA,QAApBA,EAAsBC,iBAAgB7D,EAAAA,IAAQ,KAAK,KAAIA,EAAAA,IAAQ,IAAI,CAAC,CACxE,EACA,CACE/B,SAAO+B,EAAAA,IAAQ,MAAM,EACrBgD,MAAO,GACPC,UAAW,OACXa,UAAW,CACTC,SAAU,CAAE7E,QAAMc,EAAAA,IAAQ,2BAA2B,CAAE,EACvDgE,QAAS,CAAE9E,QAAMc,EAAAA,IAAQ,SAAS,CAAE,EACpCiE,MAAO,CAAE/E,QAAMc,EAAAA,IAAQ,oBAAoB,CAAE,EAC7C,aAAc,CAAEd,QAAMc,EAAAA,IAAQ,YAAY,CAAE,CAC9C,CACF,EACA,CACE/B,SAAO+B,EAAAA,IAAQ,gBAAgB,EAC/BgD,MAAO,GACPC,UAAW,kBACXE,OAAQ,SAACI,EAAGC,EAAQ,CAAF,OAChBA,GAAM,MAANA,EAAQU,gBAAgB,GAAK,KACzBC,EAAAA,IAAoBX,GAAM,YAANA,EAAQU,gBAAgB,GAAK,CAAC,EAClD,GAAG,CACX,EACA,CACEjG,SAAO+B,EAAAA,IAAQ,gBAAgB,EAC/BgD,MAAO,GACPC,UAAW,kBACXE,OAAQ,SAACI,EAAGC,EAAQ,CAAF,OAChBA,GAAM,MAANA,EAAQU,gBAAgB,GAAK,KACzBC,EAAAA,IAAoBX,GAAM,YAANA,EAAQU,gBAAgB,GAAK,CAAC,EAClD,GAAG,CACX,CAAC,EAGGE,GAAW,SAACC,EAAqB,CAAF,OACnC7C,GAAuB6C,EAAEC,OAAOC,KAAK,CAAC,EAElCC,EAAY,CAChB,YAAUxE,EAAAA,IAAQ,UAAU,EAC5ByE,oBAAkBzE,EAAAA,IAAQ,uBAAuB,EACjD0E,aAAW1E,EAAAA,IAAQ,mBAAmB,CACxC,EACM2E,GAAW,SAACvF,EAA+B,CAC/C,SACEwF,EAAAA,MAAA,OAAKtB,UAAWuB,EAAOF,SAASrI,SAAA,IAC9BsI,EAAAA,MAAA,OAAKtB,UAAWuB,EAAOC,YAAYxI,SAAA,IACjCH,EAAAA,KAAA,QAAMmH,UAAWuB,EAAOE,SAASzI,YAAE0D,EAAAA,IAAQ,UAAU,CAAC,CAAO,KAC7D7D,EAAAA,KAACF,EAAAA,EAAe,CACdoH,UAAWjE,GAAS,YAATA,EAAW5B,IACtB8F,UAAWuB,EAAOxB,SAAU,CAC7B,CAAC,EACC,KACLlH,EAAAA,KAAA,OAAKmH,UAAWuB,EAAOG,KAAM,CAAE,KAC/BJ,EAAAA,MAAA,OAAKtB,UAAWuB,EAAOI,aAAa3I,SAAA,EACjC8C,GAAS,YAATA,EAAWhC,eACVjB,EAAAA,KAAC+I,EAAAA,EAAa,CACZC,KAAM,EACN/H,UAAW,CAAE8B,KAAME,GAAS,YAATA,EAAWhC,SAAU,CAAE,CAC3C,KAEHwH,EAAAA,MAAA,OAAAtI,SAAA,IACEH,EAAAA,KAACuD,GAAAA,EAAG,CAACC,MAAM,OAAMrD,YACdI,EAAAA,IAAuB0C,GAAS,YAATA,EAAWrB,cAAc,CAAC,CAC/C,EACJqB,GAAS,YAATA,EAAWX,cAAc,EACvB,CAAC,EACH,CAAC,EACH,CAET,EAEM2G,GAAa,eAAAC,EAAA3D,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAA0D,GAAA,KAAAC,EAAAC,EAAAC,EAAA3D,GAAA,OAAAH,EAAAA,EAAA,EAAAI,KAAA,SAAA2D,EAAA,eAAAA,EAAAzD,KAAAyD,EAAAxD,KAAA,QAChBsD,OAAAA,EACFrE,GAAgB,OAAAoE,EAAhBpE,EAAkB0B,0BAAsB,MAAA0C,IAAA,cAAxCA,EAA0CI,oBACxCpE,EAAmB,EAEnBkE,EACF,IAACG,EAAAA,OAAMJ,GAAc,YAAdA,EAAgBpG,SAAS,GAAK,IAACyG,EAAAA,SAAQL,GAAc,YAAdA,EAAgBpG,SAAS,EAAC,GAAAW,OACjEyE,EAAUgB,EAAeM,WAAW,CAAC,EAAA/F,OACtCyF,GAAc,YAAdA,EAAgBO,eAAe,EAEjCvB,EAAUgB,EAAeM,WAAW,EAACJ,EAAAxD,KAAA,KACzB8D,EAAAA,IAAwB,CACxCxD,KAAM,CACJ1B,SAAAA,EACA+B,uBAAsBpG,EAAAA,EAAAA,EAAAA,EAAA,GACjB0E,GAAgB,YAAhBA,EAAkB0B,sBAAsB,MAC3CC,sBAAuBvB,EAAmB,EAE9C,CACF,CAAC,EAAC,OARO,GAAHO,GAAG4D,EAAArD,QASJC,EAAAA,IAAoBR,EAAG,EAAES,GAAI,CAAFmD,EAAAxD,KAAA,eAAAwD,EAAAO,OAAA,iBAChCC,EAAAA,GAAQC,QAAQ,2BAADpG,OAAQ0F,CAAU,CAAE,EACnChE,GAA4B,EAAC,wBAAAiE,EAAAjD,KAAA,IAAA6C,CAAA,EAC9B,oBAvBkB,QAAAD,EAAA3C,MAAA,KAAA3D,SAAA,MAyBnB,SACE5C,EAAAA,KAACiK,EAAAA,GAAa,CAAA9J,YACZsI,EAAAA,MAAA,OAAKtB,UAAWuB,EAAOwB,cAAc/J,SAAA,IACnCsI,EAAAA,MAAA,OACEtB,UAAWgD,EAAAA,EAAGzB,EAAO0B,QAAOC,EAAAA,EAAAA,EAAAA,EAAAA,EAAAA,EAAAA,EAAAA,EAAA,GACzB3B,EAAO,YAAiB,EAAClE,GAAY,MAAZA,EAAc8F,gBAAe,EACtD5B,EAAO,UAAelE,GAAY,YAAZA,EAAc8F,eAAe,EACnD5B,EAAO,eACN,EAAClE,GAAY,MAAZA,EAAc8F,qBAAmB3G,EAAAA,IAAK,CAAC,EACzC+E,EAAO,cAAkBlE,GAAY,YAAZA,EAAc8F,qBAAmB3G,EAAAA,IAAK,CAAC,CAClE,EAAExD,SAAA,IAEHH,EAAAA,KAACuK,EAAAA,EAAY,CAACC,QAAM3G,EAAAA,IAAQ,QAAQ,EAAG4G,SAAS,QAAQ,CAAE,KAC1DzK,EAAAA,KAAA,OACEmH,UAAWgD,EAAAA,EAAGzB,EAAOgC,UAASL,EAAAA,EAAAA,EAAAA,EAAA,GAC3B3B,EAAO,YAAiB,EAAClE,GAAY,MAAZA,EAAc8F,gBAAe,EACtD5B,EAAO,UAAelE,GAAY,YAAZA,EAAc8F,eAAe,CACrD,EAAEnK,YAEHH,EAAAA,KAAA,UACE2K,IAAG,GAAA/G,UACDgH,EAAAA,IAAa,EAAEC,QAAO,wCAAAjH,OACee,EAAQ,OAAO,CACvD,CAAC,CACC,KACL3E,EAAAA,KAACuK,EAAAA,EAAY,CACXC,QAAM3G,EAAAA,IAAQ,qBAAqB,EACnC4G,SAAS,qBACTK,cAAc,MAAM,CACrB,KACD9K,EAAAA,KAAC+K,EAAAA,EAAQ,CACPC,OAAO,SACPC,OAAQ,CAAEC,EAAG,GAAI,EACjBC,OAAQ,GACRC,QAASxE,GACTyE,WAAY,GACZC,QAAS,GACTC,WAAYvG,GAAgB,OAAAf,EAAhBe,EAAkB0B,0BAAsB,MAAAzC,IAAA,cAAxCA,EAA0CuH,UAAW,CAClE,KACDxL,EAAAA,KAACuK,EAAAA,EAAY,CACXC,QAAM3G,EAAAA,IAAQ,cAAc,EAC5BiH,cAAc,OACdL,SAAS,aAAa,CACvB,KACA5G,EAAAA,IAAQmB,GAAgB,OAAAd,EAAhBc,EAAkB0B,0BAAsB,MAAAxC,IAAA,cAAxCA,EAA0CuH,UAAU,KAC7DzL,EAAAA,KAACuK,EAAAA,EAAY,CACXE,SAAS,iBACTD,QAAM3G,EAAAA,IAAQ,iBAAiB,EAC/BiH,cAAc,MAAM,CACrB,KACD9K,EAAAA,KAAA,OAAKmH,UAAWuB,EAAOgD,kBAAkBvL,YACtC0D,EAAAA,IAAQ,qBAAqB,CAAC,CAC5B,KACL7D,EAAAA,KAAA,OAAAG,YACEH,EAAAA,KAAC2L,EAAAA,GAAAA,MAAW,CAAC1D,SAAUA,GAAUG,MAAOhD,GAAoBjF,YAC1DH,EAAAA,KAAC4L,GAAAA,EAAK,CAACC,UAAU,WAAU1L,YACxB2L,EAAAA,IACC9G,GAAgB,OAAAb,EAAhBa,EAAkB0B,0BAAsB,MAAAvC,IAAA,cAAxCA,EAA0CqF,mBAC5C,EACIxE,GAAgB,OAAAZ,EAAhBY,EAAkB0B,0BAAsB,MAAAtC,IAAA,SAAAA,EAAxCA,EAA0CoF,uBAAmB,MAAApF,IAAA,cAA7DA,EAA+D2H,IAC7D,SAACC,EAAMC,EAAe,CAAF,SAClBjM,EAAAA,KAAC2L,EAAAA,GAAK,CAAavD,MAAO6D,EAAM9L,SAC7B,IAACsJ,EAAAA,OAAMuC,GAAI,YAAJA,EAAM/I,SAAS,GACvB,IAACyG,EAAAA,SAAQsC,GAAI,YAAJA,EAAM/I,SAAS,KACtBwF,EAAAA,MAACyD,EAAAA,EAAO,CACNC,UAAU,QACV/B,QAAS5B,GAASwD,GAAI,YAAJA,EAAM/I,SAAS,EAAE9C,SAAA,CAElCkI,EAAU2D,EAAKrC,WAAW,EAAG,IAC7BqC,GAAI,YAAJA,EAAMpC,eAAe,EACf,EAETvB,EAAU2D,EAAKrC,WAAW,CAC3B,EAZSsC,CAaL,CAAC,CAEZ,EACA,EAAE,CACD,CAAC,CACG,CAAC,CACX,KACLjM,EAAAA,KAACoM,GAAAA,GAAM,CACL5L,KAAK,UACL2G,UAAWuB,EAAO2D,cAClBC,QAASrD,GACTsD,UACEvH,GAAgB,OAAAX,EAAhBW,EAAkB0B,0BAAsB,MAAArC,IAAA,cAAxCA,EACIsC,yBAA0B,EAC/BxG,YAEA0D,EAAAA,IAAQ,gCAAgC,CAAC,CACpC,CAAC,EACN,KACL7D,EAAAA,KAAA,OAAKwM,MAAO,CAAEC,QAAM9I,EAAAA,IAAK,EAAI,YAAc,WAAY,EAAExD,YACvDH,EAAAA,KAAC0M,EAAAA,EAAY,CACX5B,iBAAenH,EAAAA,IAAK,EAAI+E,EAAOiE,SAAWjE,EAAOkE,OACjDC,MAAO,CACL,CACEC,IAAK,SACLC,KAAM,UACNjL,SAAO+B,EAAAA,IAAQ,QAAQ,CACzB,EACA,CACEiJ,IAAK,qBACLC,KAAM,sBACNjL,SAAO+B,EAAAA,IAAQ,qBAAqB,CACtC,EACA,CACEiJ,IAAK,cACLC,KAAM,eACNjL,SAAO+B,EAAAA,IAAQ,cAAc,CAC/B,EACA,CACEiJ,IAAK,kBACLC,KAAM,kBACNjL,SAAO+B,EAAAA,IAAQ,iBAAiB,CAClC,CAAC,CACD,CACH,CAAC,CACC,CAAC,EACH,CAAC,CACO,CAEnB,C,qNCtSA,MAAMmJ,EAAeC,GAAS,CAC5B,KAAM,CACJ,WAAAC,EACA,UAAAC,EACA,qBAAAC,EACA,aAAAC,EACA,KAAAC,CACF,EAAIL,EACEM,EAAgBD,EAAKF,CAAoB,EAAE,IAAID,CAAS,EAAE,MAAM,EAChEK,EAAmBF,EAAKJ,CAAU,EAAE,IAAIC,CAAS,EAAE,MAAM,EAC/D,MAAO,CAEL,CAACE,CAAY,EAAG,OAAO,OAAO,OAAO,OAAO,CAAC,KAAG,MAAeJ,CAAK,CAAC,EAAG,CACtE,QAAS,eACT,OAAQ,OAER,gBAAiBA,EAAM,SACvB,cAAAM,EACA,SAAUN,EAAM,YAChB,WAAYA,EAAM,cAClB,WAAY,SACZ,WAAYA,EAAM,UAClB,OAAQ,MAAG,QAAKA,EAAM,SAAS,CAAC,IAAIA,EAAM,QAAQ,IAAIA,EAAM,WAAW,GACvE,aAAcA,EAAM,eACpB,QAAS,EACT,WAAY,OAAOA,EAAM,iBAAiB,GAC1C,UAAW,QACX,SAAU,WAEV,CAAC,IAAII,CAAY,MAAM,EAAG,CACxB,UAAW,KACb,EACA,gBAAiB,CACf,MAAOJ,EAAM,YACf,EACA,CAAC,GAAGI,CAAY,aAAa,EAAG,CAC9B,kBAAmBG,EACnB,SAAUP,EAAM,YAChB,MAAOA,EAAM,qBACb,OAAQ,UACR,WAAY,OAAOA,EAAM,iBAAiB,GAC1C,UAAW,CACT,MAAOA,EAAM,gBACf,CACF,EACA,CAAC,IAAII,CAAY,YAAY,EAAG,CAC9B,YAAa,cACb,CAAC,kBAAkBJ,EAAM,OAAO,WAAWA,EAAM,OAAO,cAAc,EAAG,CACvE,MAAOA,EAAM,mBACf,CACF,EACA,cAAe,CACb,gBAAiB,cACjB,YAAa,cACb,OAAQ,UACR,CAAC,SAASI,CAAY,2BAA2B,EAAG,CAClD,MAAOJ,EAAM,aACb,gBAAiBA,EAAM,kBACzB,EACA,sBAAuB,CACrB,MAAOA,EAAM,mBACf,EACA,YAAa,CACX,gBAAiBA,EAAM,aACvB,UAAW,CACT,gBAAiBA,EAAM,iBACzB,CACF,EACA,WAAY,CACV,gBAAiBA,EAAM,kBACzB,CACF,EACA,WAAY,CACV,QAAS,MACX,EAEA,CAAC,KAAKA,EAAM,OAAO,qBAAqBA,EAAM,OAAO,EAAE,EAAG,CACxD,kBAAmBM,CACrB,CACF,CAAC,EACD,CAAC,GAAGF,CAAY,aAAa,EAAG,CAC9B,YAAa,cACb,WAAYJ,EAAM,eACpB,CACF,CACF,EAEaQ,EAAeR,GAAS,CACnC,KAAM,CACJ,UAAAE,EACA,aAAAO,EACA,KAAAJ,CACF,EAAIL,EACEU,EAAcV,EAAM,WAU1B,SATiB,cAAWA,EAAO,CACjC,YAAAU,EACA,iBAAe,QAAKL,EAAKL,EAAM,YAAY,EAAE,IAAIU,CAAW,EAAE,MAAM,CAAC,EACrE,YAAaL,EAAKI,CAAY,EAAE,IAAIJ,EAAKH,CAAS,EAAE,IAAI,CAAC,CAAC,EAAE,MAAM,EAElE,qBAAsB,EAEtB,gBAAiBF,EAAM,SACzB,CAAC,CAEH,EACaW,EAAwBX,IAAU,CAC7C,UAAW,IAAI,IAAUA,EAAM,mBAAmB,EAAE,aAAaA,EAAM,gBAAgB,EAAE,YAAY,EACrG,aAAcA,EAAM,SACtB,GACA,SAAe,MAAc,MAAOA,GAAS,CAC3C,MAAMY,EAAWJ,EAAaR,CAAK,EACnC,OAAOD,EAAaa,CAAQ,CAC9B,EAAGD,CAAqB,ECnHpBE,EAAgC,SAAUC,EAAG7F,EAAG,CAClD,IAAI8F,EAAI,CAAC,EACT,QAASC,KAAKF,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGE,CAAC,GAAK/F,EAAE,QAAQ+F,CAAC,EAAI,IAAGD,EAAEC,CAAC,EAAIF,EAAEE,CAAC,GAC/F,GAAIF,GAAK,MAAQ,OAAO,OAAO,uBAA0B,WAAY,QAASG,EAAI,EAAGD,EAAI,OAAO,sBAAsBF,CAAC,EAAGG,EAAID,EAAE,OAAQC,IAClIhG,EAAE,QAAQ+F,EAAEC,CAAC,CAAC,EAAI,GAAK,OAAO,UAAU,qBAAqB,KAAKH,EAAGE,EAAEC,CAAC,CAAC,IAAGF,EAAEC,EAAEC,CAAC,CAAC,EAAIH,EAAEE,EAAEC,CAAC,CAAC,GAElG,OAAOF,CACT,EAoCA,EA/BkC,aAAiB,CAACjO,EAAOoO,IAAQ,CACjE,KAAM,CACF,UAAWC,EACX,MAAA5B,EACA,UAAArF,EACA,QAAAkH,EACA,SAAApG,EACA,QAAAqE,CACF,EAAIvM,EACJuO,EAAYR,EAAO/N,EAAO,CAAC,YAAa,QAAS,YAAa,UAAW,WAAY,SAAS,CAAC,EAC3F,CACJ,aAAAwO,EACA,IAAAC,CACF,EAAI,aAAiB,IAAa,EAC5BC,EAAcvG,GAAK,CACvBD,GAAa,MAAuCA,EAAS,CAACoG,CAAO,EACrE/B,GAAY,MAAsCA,EAAQpE,CAAC,CAC7D,EACMwG,EAAYH,EAAa,MAAOH,CAAkB,EAElD,CAACO,GAAYC,EAAQC,CAAS,EAAI,EAASH,CAAS,EACpDI,GAAM,IAAWJ,EAAW,GAAGA,CAAS,aAAc,CAC1D,CAAC,GAAGA,CAAS,oBAAoB,EAAGL,CACtC,EAAGG,GAAQ,KAAyB,OAASA,EAAI,UAAWrH,EAAWyH,EAAQC,CAAS,EACxF,OAAOF,GAAwB,gBAAoB,OAAQ,OAAO,OAAO,CAAC,EAAGL,EAAW,CACtF,IAAKH,EACL,MAAO,OAAO,OAAO,OAAO,OAAO,CAAC,EAAG3B,CAAK,EAAGgC,GAAQ,KAAyB,OAASA,EAAI,KAAK,EAClG,UAAWM,GACX,QAASL,CACX,CAAC,CAAC,CAAC,CACL,CAAC,E,WCxCD,MAAMM,GAAiB9B,MAAS+B,EAAA,GAAe/B,EAAO,CAACgC,EAAUvO,IAAS,CACxE,GAAI,CACF,UAAAwO,EACA,iBAAAC,EACA,WAAAC,EACA,UAAAC,CACF,EAAI3O,EACJ,MAAO,CACL,CAAC,GAAGuM,EAAM,YAAY,GAAGA,EAAM,YAAY,IAAIgC,CAAQ,EAAE,EAAG,CAC1D,MAAOC,EACP,WAAYE,EACZ,YAAaD,EAEb,YAAa,CACX,MAAOlC,EAAM,oBACb,WAAYoC,EACZ,YAAaA,CACf,EACA,CAAC,IAAIpC,EAAM,YAAY,aAAa,EAAG,CACrC,YAAa,aACf,CACF,CACF,CACF,CAAC,EAED,SAAe,MAAqB,CAAC,MAAO,QAAQ,EAAGA,GAAS,CAC9D,MAAMY,EAAWJ,EAAaR,CAAK,EACnC,OAAO8B,GAAelB,CAAQ,CAChC,EAAGD,CAAqB,EChCT,SAAS0B,GAAWC,EAAK,CACtC,OAAI,OAAOA,GAAQ,SACVA,EAEGA,EAAI,OAAO,CAAC,EAAE,YAAY,EAAIA,EAAI,MAAM,CAAC,CAEvD,CCHA,MAAMC,EAAoB,CAACvC,EAAOwC,EAAQC,IAAoB,CAC5D,MAAMC,EAA6BL,GAAWI,CAAe,EAC7D,MAAO,CACL,CAAC,GAAGzC,EAAM,YAAY,GAAGA,EAAM,YAAY,IAAIwC,CAAM,EAAE,EAAG,CACxD,MAAOxC,EAAM,QAAQyC,CAAe,EAAE,EACtC,WAAYzC,EAAM,QAAQ0C,CAA0B,IAAI,EACxD,YAAa1C,EAAM,QAAQ0C,CAA0B,QAAQ,EAC7D,CAAC,IAAI1C,EAAM,YAAY,aAAa,EAAG,CACrC,YAAa,aACf,CACF,CACF,CACF,EAEA,SAAe,MAAqB,CAAC,MAAO,QAAQ,EAAGA,GAAS,CAC9D,MAAMY,EAAWJ,EAAaR,CAAK,EACnC,MAAO,CAACuC,EAAkB3B,EAAU,UAAW,SAAS,EAAG2B,EAAkB3B,EAAU,aAAc,MAAM,EAAG2B,EAAkB3B,EAAU,QAAS,OAAO,EAAG2B,EAAkB3B,EAAU,UAAW,SAAS,CAAC,CAChN,EAAGD,CAAqB,EClBpB,EAAgC,SAAUG,EAAG7F,EAAG,CAClD,IAAI8F,EAAI,CAAC,EACT,QAASC,KAAKF,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGE,CAAC,GAAK/F,EAAE,QAAQ+F,CAAC,EAAI,IAAGD,EAAEC,CAAC,EAAIF,EAAEE,CAAC,GAC/F,GAAIF,GAAK,MAAQ,OAAO,OAAO,uBAA0B,WAAY,QAASG,EAAI,EAAGD,EAAI,OAAO,sBAAsBF,CAAC,EAAGG,EAAID,EAAE,OAAQC,IAClIhG,EAAE,QAAQ+F,EAAEC,CAAC,CAAC,EAAI,GAAK,OAAO,UAAU,qBAAqB,KAAKH,EAAGE,EAAEC,CAAC,CAAC,IAAGF,EAAEC,EAAEC,CAAC,CAAC,EAAIH,EAAEE,EAAEC,CAAC,CAAC,GAElG,OAAOF,CACT,EAwGA,MAAMzK,EA1F2B,aAAiB,CAACqM,EAAUzB,IAAQ,CACnE,KAAM,CACF,UAAWC,EACX,UAAAjH,EACA,cAAA0I,EACA,MAAArD,EACA,SAAArM,EACA,KAAA2P,EACA,MAAAtM,EACA,QAAAuM,EACA,SAAAC,EAAW,GACX,QAASC,CACX,EAAIL,EACJ7P,EAAQ,EAAO6P,EAAU,CAAC,YAAa,YAAa,gBAAiB,QAAS,WAAY,OAAQ,QAAS,UAAW,WAAY,SAAS,CAAC,EACxI,CACJ,aAAArB,GACA,UAAA1C,EACA,IAAKqE,CACP,EAAI,aAAiB,IAAa,EAC5B,CAACC,GAASC,CAAU,EAAI,WAAe,EAAI,EAC3CC,MAAWC,EAAA,GAAKvQ,EAAO,CAAC,YAAa,UAAU,CAAC,EAMtD,YAAgB,IAAM,CAChBkQ,IAAsB,QACxBG,EAAWH,CAAiB,CAEhC,EAAG,CAACA,CAAiB,CAAC,EACtB,MAAMM,MAAW,MAAc/M,CAAK,EAC9BgN,MAAW,MAAoBhN,CAAK,EACpCiN,GAAkBF,IAAYC,GAC9BE,GAAW,OAAO,OAAO,OAAO,OAAO,CAC3C,gBAAiBlN,GAAS,CAACiN,GAAkBjN,EAAQ,MACvD,EAAG0M,GAAe,KAAgC,OAASA,EAAW,KAAK,EAAG1D,CAAK,EAC7EkC,EAAYH,GAAa,MAAOH,CAAkB,EAClD,CAACO,GAAYC,GAAQC,CAAS,EAAI,EAASH,CAAS,EAEpDiC,EAAe,IAAWjC,EAAWwB,GAAe,KAAgC,OAASA,EAAW,UAAW,CACvH,CAAC,GAAGxB,CAAS,IAAIlL,CAAK,EAAE,EAAGiN,GAC3B,CAAC,GAAG/B,CAAS,YAAY,EAAGlL,GAAS,CAACiN,GACtC,CAAC,GAAG/B,CAAS,SAAS,EAAG,CAACyB,GAC1B,CAAC,GAAGzB,CAAS,MAAM,EAAG7C,IAAc,MACpC,CAAC,GAAG6C,CAAS,aAAa,EAAG,CAACsB,CAChC,EAAG7I,EAAW0I,EAAejB,GAAQC,CAAS,EACxC+B,EAAmB1I,IAAK,CAC5BA,GAAE,gBAAgB,EAClB6H,GAAY,MAAsCA,EAAQ7H,EAAC,EACvD,CAAAA,GAAE,kBAGNkI,EAAW,EAAK,CAClB,EACM,CAAC,CAAES,CAAe,KAAIC,EAAA,MAAY,KAAalB,CAAQ,KAAG,KAAaM,CAAU,EAAG,CACxF,SAAU,GACV,gBAAiBa,IAAY,CAC3B,MAAMC,GAA2B,gBAAoB,OAAQ,CAC3D,UAAW,GAAGtC,CAAS,cACvB,QAASkC,CACX,EAAGG,EAAQ,EACX,SAAO,MAAeA,GAAUC,GAAaC,IAAgB,CAC3D,QAAS/I,IAAK,CACZ,IAAIgJ,IACHA,GAAKD,GAAgB,KAAiC,OAASA,EAAY,WAAa,MAAQC,KAAO,QAAkBA,GAAG,KAAKD,EAAa/I,EAAC,EAChJ0I,EAAiB1I,EAAC,CACpB,EACA,UAAW,IAAW+I,GAAgB,KAAiC,OAASA,EAAY,UAAW,GAAGvC,CAAS,aAAa,CAClI,EAAE,CACJ,CACF,CAAC,EACKyC,EAAa,OAAOpR,EAAM,SAAY,YAAcI,GAAYA,EAAS,OAAS,IAClF4Q,GAAWjB,GAAQ,KACnBsB,GAAOL,GAAyB,gBAAoB,WAAgB,KAAMA,GAAU5Q,GAAyB,gBAAoB,OAAQ,KAAMA,CAAQ,CAAC,EAAKA,EAC7JkR,EAAuB,gBAAoB,OAAQ,OAAO,OAAO,CAAC,EAAGhB,GAAU,CACnF,IAAKlC,EACL,UAAWwC,EACX,MAAOD,EACT,CAAC,EAAGU,GAAMP,EAAiBN,IAAyB,gBAAoB,EAAW,CACjF,IAAK,SACL,UAAW7B,CACb,CAAC,EAAG8B,IAAyB,gBAAoB,EAAW,CAC1D,IAAK,SACL,UAAW9B,CACb,CAAC,CAAC,EACF,OAAOC,GAAWwC,EAA0B,gBAAoB,IAAM,CACpE,UAAW,KACb,EAAGE,CAAO,EAAIA,CAAO,CACvB,CAAC,EAKD9N,EAAI,aAAe,EACnB,MAAeA,C", "sources": ["webpack://labwise-web/./src/components/LazySmileDrawer/index.tsx", "webpack://labwise-web/./src/components/ReactionTabs/ReactionLibTab/util.tsx", "webpack://labwise-web/./src/pages/experimental-procedure/report-details/index.less?fba4", "webpack://labwise-web/./src/pages/experimental-procedure/report-details/index.tsx", "webpack://labwise-web/./node_modules/antd/es/tag/style/index.js", "webpack://labwise-web/./node_modules/antd/es/tag/CheckableTag.js", "webpack://labwise-web/./node_modules/antd/es/tag/style/presetCmp.js", "webpack://labwise-web/./node_modules/antd/es/_util/capitalize.js", "webpack://labwise-web/./node_modules/antd/es/tag/style/statusCmp.js", "webpack://labwise-web/./node_modules/antd/es/tag/index.js"], "sourcesContent": ["import { Skeleton } from 'antd'\nimport { Suspense, lazy } from 'react'\nimport { MoleculeStructureProps } from '../MoleculeStructure'\nconst MoleculeStructure = lazy(() =>\n  import('@/components/MoleculeStructure').then((module) => ({\n    default: module.default\n  }))\n)\n\nexport default function LazySmileDrawer(props: MoleculeStructureProps) {\n  return (\n    <Suspense\n      fallback={\n        <div>\n          <Skeleton active />\n        </div>\n      }\n    >\n      <MoleculeStructure {...props} />\n    </Suspense>\n  )\n}\n", "import { ProcedureRxnMatchResult } from '@/services/brain'\nimport { AiProcedure } from '@/types/Procedure'\nimport { getWord, isEN } from '@/utils'\nimport { Tag } from 'antd'\nimport { ReactElement } from 'react'\n\nexport const transformReferenceType = (\n  type: ProcedureRxnMatchResult['reference_type']\n): AiProcedure['reference']['type'] => {\n  switch (type) {\n    case 'JOURNAL':\n      return 'journal'\n    case 'Patent':\n      return 'patent'\n    default:\n      return type\n  }\n}\n\nexport const transformProcedureMatchResponse = (\n  {\n    id,\n    experimental_procedure = '',\n    query = '',\n    procedure = '',\n    similarity = 0,\n    rxn = '',\n    rxn_yields = '',\n    yields,\n    transformation = '',\n    reference_type = 'JOURNAL',\n    title = '',\n    authors = '',\n    date = '',\n    assignees = '',\n    reference_text = '',\n    patent_id = '',\n    is_scalable = false\n  }: ProcedureRxnMatchResult,\n  isSame: boolean = false\n): AiProcedure => ({\n  id,\n  rxn,\n  isSame,\n  text: procedure || '',\n  experimentalProcedure: experimental_procedure || '',\n  transformation: transformation || '',\n  query: query || '',\n  reference: {\n    type: transformReferenceType(reference_type),\n    title: title || '',\n    authors: authors || '',\n    date: date || '',\n    no: patent_id || '',\n    reference_text: reference_text || '',\n    assignees: assignees || ''\n  },\n  yieldString: rxn_yields || '',\n  yieldNumber: yields || undefined,\n  similarity: similarity || 0,\n  scalable: is_scalable || false\n})\n\nexport const getTagForReferenceType = (\n  type: AiProcedure['reference']['type']\n): ReactElement => {\n  switch (type) {\n    case 'journal':\n      return <Tag color=\"green\">Journal</Tag>\n    case 'patent':\n      return <Tag color=\"blue\">Patent</Tag>\n    default:\n      return <Tag color=\"orange\">Custom</Tag>\n  }\n}\n\nexport const getTagForSameOrSimilerType = (\n  isSame: boolean,\n  name?: string\n): ReactElement => {\n  switch (isSame) {\n    case true:\n      return (\n        <Tag color=\"green\">\n          {name\n            ? isEN()\n              ? `The Same Reaction as ${name}`\n              : `与${name}是${getWord('same-reaction')}`\n            : getWord('same-reaction')}\n        </Tag>\n      )\n\n    case false:\n      return (\n        <Tag color=\"blue\">\n          {name\n            ? isEN()\n              ? `Reference for ${name}`\n              : `与${name}是${getWord('reference')}`\n            : getWord('reference')}\n        </Tag>\n      )\n    default:\n      return <></>\n  }\n}\n", "// extracted by mini-css-extract-plugin\nexport default {\"unfoldWidth\":\"unfoldWidth___UrE3s\",\"foldWidth\":\"foldWidth___TQy9x\",\"unfoldWidth_EN\":\"unfoldWidth_EN___TUgZ7\",\"foldWidth_EN\":\"foldWidth_EN___k4QHt\",\"reportDetails\":\"reportDetails___aLpJq\",\"content\":\"content___kmMeX\",\"pdfReview\":\"pdfReview___Z_w7R\",\"actionProposalTip\":\"actionProposalTip___a7pXe\",\"confirmButton\":\"confirmButton___Y_L0s\",\"anchor\":\"anchor___I9qMj\",\"anchorEN\":\"anchorEN___lYu1l\",\"stepInfo\":\"stepInfo___alskd\",\"reactionDes\":\"reactionDes___u09Cr\",\"space\":\"space___AIYte\",\"procedureDes\":\"procedureDes___zfAqU\",\"structure\":\"structure___YCgaH\",\"desTitle\":\"desTitle___xWzRC\"};", "import CustomAnchor from '@/components/Anchor'\nimport LazySmileDrawer from '@/components/LazySmileDrawer'\nimport ProcedureText from '@/components/ProcedureText'\nimport { transformReferenceType } from '@/components/ReactionTabs/ReactionLibTab/util'\nimport {\n  apiExperimentAnalysisReport,\n  apiUploadAnalysisReport,\n  parseResponseResult\n} from '@/services'\nimport { ReportReference, ReportResponse } from '@/types/models'\nimport {\n  convertToPercentage,\n  getEnvConfig,\n  getWord,\n  isEN,\n  isValidArray\n} from '@/utils'\nimport { PageContainer, ProColumns, ProTable } from '@ant-design/pro-components'\nimport { useModel, useParams } from '@umijs/max'\nimport type { RadioChangeEvent } from 'antd'\nimport { Button, Popover, Radio, Space, Tag, message } from 'antd'\nimport cs from 'classnames'\nimport { isEmpty, isNil } from 'lodash'\nimport { useEffect, useState } from 'react'\nimport SectionTitle from '../../../components/SectionTitle/index'\nimport styles from './index.less'\nexport default function ReportDetails() {\n  const { initialState } = useModel('@@initialState')\n  const { check_no } = useParams()\n  const [reportDetailData, setreportDetailData] = useState<ReportResponse>()\n  const [selectedActionIndex, setSelectedActionIndex] = useState<number>(0)\n\n  const getExperimentAnalysisReport = async () => {\n    const res = await apiExperimentAnalysisReport({\n      routeParams: check_no\n    })\n    if (parseResponseResult(res).ok) {\n      setreportDetailData(res?.data)\n    }\n  }\n\n  useEffect(() => {\n    setSelectedActionIndex(\n      reportDetailData?.report_analysis_result?.selected_action_index\n    )\n  }, [reportDetailData?.report_analysis_result?.selected_action_index])\n\n  useEffect(() => {\n    getExperimentAnalysisReport()\n  }, [])\n\n  const reportDetailsColumns: ProColumns<any>[] = [\n    {\n      title: getWord('structural'),\n      width: 160,\n      dataIndex: 'smiles',\n      fixed: 'left',\n      render: (smiles: string) =>\n        smiles ? (\n          <LazySmileDrawer structure={smiles} className=\"smilesItem\" />\n        ) : (\n          ''\n        )\n    },\n    {\n      title: getWord('exact-mass'),\n      width: 96,\n      dataIndex: 'exact_mass'\n    },\n    {\n      title: getWord('ultra-spectra'),\n      width: 96,\n      dataIndex: 'detected_map',\n      render: (_, record) =>\n        record?.detected_map?.chromatograph ? getWord('yes') : getWord('no')\n    },\n    {\n      title: getWord('mass-spectra'),\n      width: 96,\n      dataIndex: 'detected_map',\n      render: (_, record) =>\n        record?.detected_map?.mass_spectrum ? getWord('yes') : getWord('no')\n    },\n    {\n      title: getWord('role'),\n      width: 96,\n      dataIndex: 'role',\n      valueEnum: {\n        material: { text: getWord('menu.list.material-manage') },\n        product: { text: getWord('product') },\n        other: { text: getWord('other-and-impurity') },\n        'by-product': { text: getWord('by-product') }\n      }\n    },\n    {\n      title: getWord('proportion-254'),\n      width: 96,\n      dataIndex: 'peak_area_ratio',\n      render: (_, record) =>\n        record?.peak_area_ratio['254']\n          ? convertToPercentage(record?.peak_area_ratio['254'])\n          : '-'\n    },\n    {\n      title: getWord('proportion-214'),\n      width: 96,\n      dataIndex: 'peak_area_ratio',\n      render: (_, record) =>\n        record?.peak_area_ratio['214']\n          ? convertToPercentage(record?.peak_area_ratio['214'])\n          : '-'\n    }\n  ]\n\n  const onChange = (e: RadioChangeEvent) =>\n    setSelectedActionIndex(e.target.value)\n\n  const stepsEnum = {\n    continue: getWord('continue'),\n    change_condition: getWord('try-another-procedure'),\n    terminate: getWord('cancel-experiment')\n  }\n  const stepInfo = (reference: ReportReference) => {\n    return (\n      <div className={styles.stepInfo}>\n        <div className={styles.reactionDes}>\n          <span className={styles.desTitle}>{getWord('reaction')}</span>\n          <LazySmileDrawer\n            structure={reference?.rxn}\n            className={styles.structure}\n          />\n        </div>\n        <div className={styles.space} />\n        <div className={styles.procedureDes}>\n          {reference?.procedure && (\n            <ProcedureText\n              rows={6}\n              procedure={{ text: reference?.procedure }}\n            />\n          )}\n          <div>\n            <Tag color=\"blue\">\n              {transformReferenceType(reference?.reference_type)}\n            </Tag>\n            {reference?.reference_text}\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  const confrimReport = async () => {\n    let curOperateItem =\n      reportDetailData?.report_analysis_result?.recommended_actions[\n        selectedActionIndex\n      ]\n    let successMsg =\n      !isNil(curOperateItem?.reference) && !isEmpty(curOperateItem?.reference)\n        ? `${stepsEnum[curOperateItem.action_type]}${\n            curOperateItem?.condition_index\n          }`\n        : stepsEnum[curOperateItem.action_type]\n    const res = await apiUploadAnalysisReport({\n      data: {\n        check_no,\n        report_analysis_result: {\n          ...reportDetailData?.report_analysis_result,\n          selected_action_index: selectedActionIndex\n        }\n      }\n    })\n    if (!parseResponseResult(res).ok) return\n    message.success(`机器人会${successMsg}`)\n    getExperimentAnalysisReport()\n  }\n\n  return (\n    <PageContainer>\n      <div className={styles.reportDetails}>\n        <div\n          className={cs(styles.content, {\n            [styles['unfoldWidth']]: !initialState?.isMenuCollapsed,\n            [styles['foldWidth']]: initialState?.isMenuCollapsed,\n            [styles['unfoldWidth_EN']]:\n              !initialState?.isMenuCollapsed && isEN(),\n            [styles['foldWidth_EN']]: initialState?.isMenuCollapsed && isEN()\n          })}\n        >\n          <SectionTitle word={getWord('report')} anchorId=\"report\" />\n          <div\n            className={cs(styles.pdfReview, {\n              [styles['unfoldWidth']]: !initialState?.isMenuCollapsed,\n              [styles['foldWidth']]: initialState?.isMenuCollapsed\n            })}\n          >\n            <iframe\n              src={`${\n                getEnvConfig().apiBase\n              }/api/run/experiment/analysis/report/${check_no}.pdf`}\n            />\n          </div>\n          <SectionTitle\n            word={getWord('substance-inference')}\n            anchorId=\"substanceInference\"\n            wrapClassName=\"mt20\"\n          />\n          <ProTable\n            rowKey=\"smiles\"\n            scroll={{ x: 650 }}\n            search={false}\n            columns={reportDetailsColumns}\n            pagination={false}\n            options={false}\n            dataSource={reportDetailData?.report_analysis_result?.substances}\n          />\n          <SectionTitle\n            word={getWord('ai-inference')}\n            wrapClassName=\"mt20\"\n            anchorId=\"AIInference\"\n          />\n          {getWord(reportDetailData?.report_analysis_result?.conclusion)}\n          <SectionTitle\n            anchorId=\"actionProposal\"\n            word={getWord('action-proposal')}\n            wrapClassName=\"mt20\"\n          />\n          <div className={styles.actionProposalTip}>\n            {getWord('action-proposal-tip')}\n          </div>\n          <div>\n            <Radio.Group onChange={onChange} value={selectedActionIndex}>\n              <Space direction=\"vertical\">\n                {isValidArray(\n                  reportDetailData?.report_analysis_result?.recommended_actions\n                )\n                  ? reportDetailData?.report_analysis_result?.recommended_actions?.map(\n                      (item, index: number) => (\n                        <Radio key={index} value={index}>\n                          {!isNil(item?.reference) &&\n                          !isEmpty(item?.reference) ? (\n                            <Popover\n                              placement=\"right\"\n                              content={stepInfo(item?.reference)}\n                            >\n                              {stepsEnum[item.action_type]}{' '}\n                              {item?.condition_index}\n                            </Popover>\n                          ) : (\n                            stepsEnum[item.action_type]\n                          )}\n                        </Radio>\n                      )\n                    )\n                  : ''}\n              </Space>\n            </Radio.Group>\n          </div>\n          <Button\n            type=\"primary\"\n            className={styles.confirmButton}\n            onClick={confrimReport}\n            disabled={\n              reportDetailData?.report_analysis_result\n                ?.selected_action_index !== 0\n            }\n          >\n            {getWord('pages.route.edit.label.confirm')}\n          </Button>\n        </div>\n        <div style={{ flex: isEN() ? '0 0 170px' : '0 0 120px' }}>\n          <CustomAnchor\n            wrapClassName={isEN() ? styles.anchorEN : styles.anchor}\n            items={[\n              {\n                key: 'report',\n                href: '#report',\n                title: getWord('report')\n              },\n              {\n                key: 'substanceInference',\n                href: '#substanceInference',\n                title: getWord('substance-inference')\n              },\n              {\n                key: 'AIInference',\n                href: '#AIInference',\n                title: getWord('ai-inference')\n              },\n              {\n                key: 'action-proposal',\n                href: '#actionProposal',\n                title: getWord('action-proposal')\n              }\n            ]}\n          />\n        </div>\n      </div>\n    </PageContainer>\n  )\n}\n", "import { unit } from '@ant-design/cssinjs';\nimport { TinyColor } from '@ctrl/tinycolor';\nimport { resetComponent } from '../../style';\nimport { genStyleHooks, mergeToken } from '../../theme/internal';\n// ============================== Styles ==============================\nconst genBaseStyle = token => {\n  const {\n    paddingXXS,\n    lineWidth,\n    tagPaddingHorizontal,\n    componentCls,\n    calc\n  } = token;\n  const paddingInline = calc(tagPaddingHorizontal).sub(lineWidth).equal();\n  const iconMarginInline = calc(paddingXXS).sub(lineWidth).equal();\n  return {\n    // Result\n    [componentCls]: Object.assign(Object.assign({}, resetComponent(token)), {\n      display: 'inline-block',\n      height: 'auto',\n      // https://github.com/ant-design/ant-design/pull/47504\n      marginInlineEnd: token.marginXS,\n      paddingInline,\n      fontSize: token.tagFontSize,\n      lineHeight: token.tagLineHeight,\n      whiteSpace: 'nowrap',\n      background: token.defaultBg,\n      border: `${unit(token.lineWidth)} ${token.lineType} ${token.colorBorder}`,\n      borderRadius: token.borderRadiusSM,\n      opacity: 1,\n      transition: `all ${token.motionDurationMid}`,\n      textAlign: 'start',\n      position: 'relative',\n      // RTL\n      [`&${componentCls}-rtl`]: {\n        direction: 'rtl'\n      },\n      '&, a, a:hover': {\n        color: token.defaultColor\n      },\n      [`${componentCls}-close-icon`]: {\n        marginInlineStart: iconMarginInline,\n        fontSize: token.tagIconSize,\n        color: token.colorTextDescription,\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationMid}`,\n        '&:hover': {\n          color: token.colorTextHeading\n        }\n      },\n      [`&${componentCls}-has-color`]: {\n        borderColor: 'transparent',\n        [`&, a, a:hover, ${token.iconCls}-close, ${token.iconCls}-close:hover`]: {\n          color: token.colorTextLightSolid\n        }\n      },\n      '&-checkable': {\n        backgroundColor: 'transparent',\n        borderColor: 'transparent',\n        cursor: 'pointer',\n        [`&:not(${componentCls}-checkable-checked):hover`]: {\n          color: token.colorPrimary,\n          backgroundColor: token.colorFillSecondary\n        },\n        '&:active, &-checked': {\n          color: token.colorTextLightSolid\n        },\n        '&-checked': {\n          backgroundColor: token.colorPrimary,\n          '&:hover': {\n            backgroundColor: token.colorPrimaryHover\n          }\n        },\n        '&:active': {\n          backgroundColor: token.colorPrimaryActive\n        }\n      },\n      '&-hidden': {\n        display: 'none'\n      },\n      // To ensure that a space will be placed between character and `Icon`.\n      [`> ${token.iconCls} + span, > span + ${token.iconCls}`]: {\n        marginInlineStart: paddingInline\n      }\n    }),\n    [`${componentCls}-borderless`]: {\n      borderColor: 'transparent',\n      background: token.tagBorderlessBg\n    }\n  };\n};\n// ============================== Export ==============================\nexport const prepareToken = token => {\n  const {\n    lineWidth,\n    fontSizeIcon,\n    calc\n  } = token;\n  const tagFontSize = token.fontSizeSM;\n  const tagToken = mergeToken(token, {\n    tagFontSize,\n    tagLineHeight: unit(calc(token.lineHeightSM).mul(tagFontSize).equal()),\n    tagIconSize: calc(fontSizeIcon).sub(calc(lineWidth).mul(2)).equal(),\n    // Tag icon is much smaller\n    tagPaddingHorizontal: 8,\n    // Fixed padding.\n    tagBorderlessBg: token.defaultBg\n  });\n  return tagToken;\n};\nexport const prepareComponentToken = token => ({\n  defaultBg: new TinyColor(token.colorFillQuaternary).onBackground(token.colorBgContainer).toHexString(),\n  defaultColor: token.colorText\n});\nexport default genStyleHooks('Tag', token => {\n  const tagToken = prepareToken(token);\n  return genBaseStyle(tagToken);\n}, prepareComponentToken);", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { ConfigContext } from '../config-provider';\nimport useStyle from './style';\nconst CheckableTag = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      style,\n      className,\n      checked,\n      onChange,\n      onClick\n    } = props,\n    restProps = __rest(props, [\"prefixCls\", \"style\", \"className\", \"checked\", \"onChange\", \"onClick\"]);\n  const {\n    getPrefixCls,\n    tag\n  } = React.useContext(ConfigContext);\n  const handleClick = e => {\n    onChange === null || onChange === void 0 ? void 0 : onChange(!checked);\n    onClick === null || onClick === void 0 ? void 0 : onClick(e);\n  };\n  const prefixCls = getPrefixCls('tag', customizePrefixCls);\n  // Style\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  const cls = classNames(prefixCls, `${prefixCls}-checkable`, {\n    [`${prefixCls}-checkable-checked`]: checked\n  }, tag === null || tag === void 0 ? void 0 : tag.className, className, hashId, cssVarCls);\n  return wrapCSSVar(/*#__PURE__*/React.createElement(\"span\", Object.assign({}, restProps, {\n    ref: ref,\n    style: Object.assign(Object.assign({}, style), tag === null || tag === void 0 ? void 0 : tag.style),\n    className: cls,\n    onClick: handleClick\n  })));\n});\nexport default CheckableTag;", "// Style as status component\nimport { prepareComponentToken, prepareToken } from '.';\nimport { genPresetColor, genSubStyleComponent } from '../../theme/internal';\n// ============================== Preset ==============================\nconst genPresetStyle = token => genPresetColor(token, (colorKey, _ref) => {\n  let {\n    textColor,\n    lightBorderColor,\n    lightColor,\n    darkColor\n  } = _ref;\n  return {\n    [`${token.componentCls}${token.componentCls}-${colorKey}`]: {\n      color: textColor,\n      background: lightColor,\n      borderColor: lightBorderColor,\n      // Inverse color\n      '&-inverse': {\n        color: token.colorTextLightSolid,\n        background: darkColor,\n        borderColor: darkColor\n      },\n      [`&${token.componentCls}-borderless`]: {\n        borderColor: 'transparent'\n      }\n    }\n  };\n});\n// ============================== Export ==============================\nexport default genSubStyleComponent(['Tag', 'preset'], token => {\n  const tagToken = prepareToken(token);\n  return genPresetStyle(tagToken);\n}, prepareComponentToken);", "export default function capitalize(str) {\n  if (typeof str !== 'string') {\n    return str;\n  }\n  const ret = str.charAt(0).toUpperCase() + str.slice(1);\n  return ret;\n}", "import { prepareComponentToken, prepareToken } from '.';\nimport capitalize from '../../_util/capitalize';\nimport { genSubStyleComponent } from '../../theme/internal';\nconst genTagStatusStyle = (token, status, cssVariableType) => {\n  const capitalizedCssVariableType = capitalize(cssVariableType);\n  return {\n    [`${token.componentCls}${token.componentCls}-${status}`]: {\n      color: token[`color${cssVariableType}`],\n      background: token[`color${capitalizedCssVariableType}Bg`],\n      borderColor: token[`color${capitalizedCssVariableType}Border`],\n      [`&${token.componentCls}-borderless`]: {\n        borderColor: 'transparent'\n      }\n    }\n  };\n};\n// ============================== Export ==============================\nexport default genSubStyleComponent(['Tag', 'status'], token => {\n  const tagToken = prepareToken(token);\n  return [genTagStatusStyle(tagToken, 'success', 'Success'), genTagStatusStyle(tagToken, 'processing', 'Info'), genTagStatusStyle(tagToken, 'error', 'Error'), genTagStatusStyle(tagToken, 'warning', 'Warning')];\n}, prepareComponentToken);", "\"use client\";\n\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport omit from \"rc-util/es/omit\";\nimport { isPresetColor, isPresetStatusColor } from '../_util/colors';\nimport useClosable, { pickClosable } from '../_util/hooks/useClosable';\nimport { replaceElement } from '../_util/reactNode';\nimport { devUseWarning } from '../_util/warning';\nimport Wave from '../_util/wave';\nimport { ConfigContext } from '../config-provider';\nimport CheckableTag from './CheckableTag';\nimport useStyle from './style';\nimport PresetCmp from './style/presetCmp';\nimport StatusCmp from './style/statusCmp';\nconst InternalTag = /*#__PURE__*/React.forwardRef((tagProps, ref) => {\n  const {\n      prefixCls: customizePrefixCls,\n      className,\n      rootClassName,\n      style,\n      children,\n      icon,\n      color,\n      onClose,\n      bordered = true,\n      visible: deprecatedVisible\n    } = tagProps,\n    props = __rest(tagProps, [\"prefixCls\", \"className\", \"rootClassName\", \"style\", \"children\", \"icon\", \"color\", \"onClose\", \"bordered\", \"visible\"]);\n  const {\n    getPrefixCls,\n    direction,\n    tag: tagContext\n  } = React.useContext(ConfigContext);\n  const [visible, setVisible] = React.useState(true);\n  const domProps = omit(props, ['closeIcon', 'closable']);\n  // Warning for deprecated usage\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = devUseWarning('Tag');\n    warning.deprecated(!('visible' in tagProps), 'visible', 'visible && <Tag />');\n  }\n  React.useEffect(() => {\n    if (deprecatedVisible !== undefined) {\n      setVisible(deprecatedVisible);\n    }\n  }, [deprecatedVisible]);\n  const isPreset = isPresetColor(color);\n  const isStatus = isPresetStatusColor(color);\n  const isInternalColor = isPreset || isStatus;\n  const tagStyle = Object.assign(Object.assign({\n    backgroundColor: color && !isInternalColor ? color : undefined\n  }, tagContext === null || tagContext === void 0 ? void 0 : tagContext.style), style);\n  const prefixCls = getPrefixCls('tag', customizePrefixCls);\n  const [wrapCSSVar, hashId, cssVarCls] = useStyle(prefixCls);\n  // Style\n  const tagClassName = classNames(prefixCls, tagContext === null || tagContext === void 0 ? void 0 : tagContext.className, {\n    [`${prefixCls}-${color}`]: isInternalColor,\n    [`${prefixCls}-has-color`]: color && !isInternalColor,\n    [`${prefixCls}-hidden`]: !visible,\n    [`${prefixCls}-rtl`]: direction === 'rtl',\n    [`${prefixCls}-borderless`]: !bordered\n  }, className, rootClassName, hashId, cssVarCls);\n  const handleCloseClick = e => {\n    e.stopPropagation();\n    onClose === null || onClose === void 0 ? void 0 : onClose(e);\n    if (e.defaultPrevented) {\n      return;\n    }\n    setVisible(false);\n  };\n  const [, mergedCloseIcon] = useClosable(pickClosable(tagProps), pickClosable(tagContext), {\n    closable: false,\n    closeIconRender: iconNode => {\n      const replacement = /*#__PURE__*/React.createElement(\"span\", {\n        className: `${prefixCls}-close-icon`,\n        onClick: handleCloseClick\n      }, iconNode);\n      return replaceElement(iconNode, replacement, originProps => ({\n        onClick: e => {\n          var _a;\n          (_a = originProps === null || originProps === void 0 ? void 0 : originProps.onClick) === null || _a === void 0 ? void 0 : _a.call(originProps, e);\n          handleCloseClick(e);\n        },\n        className: classNames(originProps === null || originProps === void 0 ? void 0 : originProps.className, `${prefixCls}-close-icon`)\n      }));\n    }\n  });\n  const isNeedWave = typeof props.onClick === 'function' || children && children.type === 'a';\n  const iconNode = icon || null;\n  const kids = iconNode ? (/*#__PURE__*/React.createElement(React.Fragment, null, iconNode, children && /*#__PURE__*/React.createElement(\"span\", null, children))) : children;\n  const tagNode = /*#__PURE__*/React.createElement(\"span\", Object.assign({}, domProps, {\n    ref: ref,\n    className: tagClassName,\n    style: tagStyle\n  }), kids, mergedCloseIcon, isPreset && /*#__PURE__*/React.createElement(PresetCmp, {\n    key: \"preset\",\n    prefixCls: prefixCls\n  }), isStatus && /*#__PURE__*/React.createElement(StatusCmp, {\n    key: \"status\",\n    prefixCls: prefixCls\n  }));\n  return wrapCSSVar(isNeedWave ? /*#__PURE__*/React.createElement(Wave, {\n    component: \"Tag\"\n  }, tagNode) : tagNode);\n});\nconst Tag = InternalTag;\nif (process.env.NODE_ENV !== 'production') {\n  Tag.displayName = 'Tag';\n}\nTag.CheckableTag = CheckableTag;\nexport default Tag;"], "names": ["MoleculeStructure", "lazy", "then", "module", "LazySmileDrawer", "props", "_jsx", "Suspense", "fallback", "children", "Skeleton", "active", "_objectSpread", "transformReferenceType", "type", "transformProcedureMatchResponse", "_ref", "id", "_ref$experimental_pro", "experimental_procedure", "_ref$query", "query", "_ref$procedure", "procedure", "_ref$similarity", "similarity", "_ref$rxn", "rxn", "_ref$rxn_yields", "rxn_yields", "yields", "_ref$transformation", "transformation", "_ref$reference_type", "reference_type", "_ref$title", "title", "_ref$authors", "authors", "_ref$date", "date", "_ref$assignees", "assignees", "_ref$reference_text", "reference_text", "_ref$patent_id", "patent_id", "_ref$is_scalable", "is_scalable", "isSame", "arguments", "length", "undefined", "text", "experimentalProcedure", "reference", "no", "yieldString", "yieldNumber", "scalable", "getTagForReferenceType", "Tag", "color", "getTagForSameOrSimilerType", "name", "isEN", "concat", "getWord", "_Fragment", "ReportDetails", "_reportDetailData$rep2", "_reportDetailData$rep4", "_reportDetailData$rep5", "_reportDetailData$rep6", "_reportDetailData$rep7", "_reportDetailData$rep8", "_useModel", "useModel", "initialState", "_useParams", "useParams", "check_no", "_useState", "useState", "_useState2", "_slicedToArray", "reportDetailData", "setreportDetailData", "_useState3", "_useState4", "selectedActionIndex", "setSelectedActionIndex", "getExperimentAnalysisReport", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_context", "prev", "next", "apiExperimentAnalysisReport", "routeParams", "sent", "parseResponseResult", "ok", "data", "stop", "apply", "useEffect", "_reportDetailData$rep", "report_analysis_result", "selected_action_index", "reportDetailsColumns", "width", "dataIndex", "fixed", "render", "smiles", "structure", "className", "_", "record", "_record$detected_map", "detected_map", "chromatograph", "_record$detected_map2", "mass_spectrum", "valueEnum", "material", "product", "other", "peak_area_ratio", "convertToPercentage", "onChange", "e", "target", "value", "stepsEnum", "change_condition", "terminate", "stepInfo", "_jsxs", "styles", "reactionDes", "des<PERSON>itle", "space", "procedureDes", "ProcedureText", "rows", "confrimReport", "_ref2", "_callee2", "_reportDetailData$rep3", "curOperateItem", "successMsg", "_context2", "recommended_actions", "isNil", "isEmpty", "action_type", "condition_index", "apiUploadAnalysisReport", "abrupt", "message", "success", "<PERSON><PERSON><PERSON><PERSON>", "reportDetails", "cs", "content", "_defineProperty", "isMenuCollapsed", "SectionTitle", "word", "anchorId", "pdfReview", "src", "getEnvConfig", "apiBase", "wrapClassName", "ProTable", "<PERSON><PERSON><PERSON>", "scroll", "x", "search", "columns", "pagination", "options", "dataSource", "substances", "conclusion", "actionProposalTip", "Radio", "Space", "direction", "isValidArray", "map", "item", "index", "Popover", "placement", "<PERSON><PERSON>", "confirmButton", "onClick", "disabled", "style", "flex", "CustomAnchor", "anchorEN", "anchor", "items", "key", "href", "genBaseStyle", "token", "paddingXXS", "lineWidth", "tagPaddingHorizontal", "componentCls", "calc", "paddingInline", "iconMarginInline", "prepareToken", "fontSizeIcon", "tagFontSize", "prepareComponentToken", "tagToken", "__rest", "s", "t", "p", "i", "ref", "customizePrefixCls", "checked", "restProps", "getPrefixCls", "tag", "handleClick", "prefixCls", "wrapCSSVar", "hashId", "cssVarCls", "cls", "genPresetStyle", "genPresetColor", "colorKey", "textColor", "lightBorderColor", "lightColor", "darkColor", "capitalize", "str", "genTagStatusStyle", "status", "cssVariableType", "capitalizedCssVariableType", "tagProps", "rootClassName", "icon", "onClose", "bordered", "deprecatedVisible", "tagContext", "visible", "setVisible", "domProps", "omit", "isPreset", "isStatus", "isInternalColor", "tagStyle", "tagClassName", "handleCloseClick", "mergedCloseIcon", "useClosable", "iconNode", "replacement", "originProps", "_a", "isNeedWave", "kids", "tagNode"], "sourceRoot": ""}