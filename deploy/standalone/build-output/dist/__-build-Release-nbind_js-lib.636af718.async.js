(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[2180],{18113:function(module,exports,__webpack_require__){var process=__webpack_require__(34155),Buffer=__webpack_require__(48764).Buffer,__WEBPACK_AMD_DEFINE_ARRAY__,__WEBPACK_AMD_DEFINE_RESULT__;(function(h,p){__WEBPACK_AMD_DEFINE_ARRAY__=[],__WEBPACK_AMD_DEFINE_RESULT__=function(){return p}.apply(exports,__WEBPACK_AMD_DEFINE_ARRAY__),__WEBPACK_AMD_DEFINE_RESULT__!==void 0&&(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)})(this,function(Module,cb){typeof Module=="function"&&(cb=Module,Module={}),Module.onRuntimeInitialized=function(h,p){return function(){h&&h.apply(this,arguments);try{Module.ccall("nbind_init")}catch(y){p(y);return}p(null,{bind:Module._nbind_value,reflect:Module.NBind.reflect,queryType:Module.NBind.queryType,toggleLightGC:Module.toggleLightGC,lib:Module})}}(Module.onRuntimeInitialized,cb);var Module;Module||(Module=(typeof Module!="undefined"?Module:null)||{});var moduleOverrides={};for(var key in Module)Module.hasOwnProperty(key)&&(moduleOverrides[key]=Module[key]);var ENVIRONMENT_IS_WEB=!1,ENVIRONMENT_IS_WORKER=!1,ENVIRONMENT_IS_NODE=!1,ENVIRONMENT_IS_SHELL=!1;if(Module.ENVIRONMENT)if(Module.ENVIRONMENT==="WEB")ENVIRONMENT_IS_WEB=!0;else if(Module.ENVIRONMENT==="WORKER")ENVIRONMENT_IS_WORKER=!0;else if(Module.ENVIRONMENT==="NODE")ENVIRONMENT_IS_NODE=!0;else if(Module.ENVIRONMENT==="SHELL")ENVIRONMENT_IS_SHELL=!0;else throw new Error("The provided Module['ENVIRONMENT'] value is not valid. It must be one of: WEB|WORKER|NODE|SHELL.");else ENVIRONMENT_IS_WEB=typeof window=="object",ENVIRONMENT_IS_WORKER=typeof importScripts=="function",ENVIRONMENT_IS_NODE=typeof process=="object"&&!0&&!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_WORKER,ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;if(ENVIRONMENT_IS_NODE){Module.print||(Module.print=console.log),Module.printErr||(Module.printErr=console.warn);var nodeFS,nodePath;Module.read=function(p,y){nodeFS||(nodeFS={}("")),nodePath||(nodePath={}("")),p=nodePath.normalize(p);var w=nodeFS.readFileSync(p);return y?w:w.toString()},Module.readBinary=function(p){var y=Module.read(p,!0);return y.buffer||(y=new Uint8Array(y)),assert(y.buffer),y},Module.load=function(p){globalEval(read(p))},Module.thisProgram||(process.argv.length>1?Module.thisProgram=process.argv[1].replace(/\\/g,"/"):Module.thisProgram="unknown-program"),Module.arguments=process.argv.slice(2),module.exports=Module,process.on("uncaughtException",function(h){if(!(h instanceof ExitStatus))throw h}),Module.inspect=function(){return"[Emscripten Module object]"}}else if(ENVIRONMENT_IS_SHELL)Module.print||(Module.print=print),typeof printErr!="undefined"&&(Module.printErr=printErr),typeof read!="undefined"?Module.read=read:Module.read=function(){throw"no read() available"},Module.readBinary=function(p){if(typeof readbuffer=="function")return new Uint8Array(readbuffer(p));var y=read(p,"binary");return assert(typeof y=="object"),y},typeof scriptArgs!="undefined"?Module.arguments=scriptArgs:typeof arguments!="undefined"&&(Module.arguments=arguments),typeof quit=="function"&&(Module.quit=function(h,p){quit(h)});else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(Module.read=function(p){var y=new XMLHttpRequest;return y.open("GET",p,!1),y.send(null),y.responseText},ENVIRONMENT_IS_WORKER&&(Module.readBinary=function(p){var y=new XMLHttpRequest;return y.open("GET",p,!1),y.responseType="arraybuffer",y.send(null),new Uint8Array(y.response)}),Module.readAsync=function(p,y,w){var T=new XMLHttpRequest;T.open("GET",p,!0),T.responseType="arraybuffer",T.onload=function(){T.status==200||T.status==0&&T.response?y(T.response):w()},T.onerror=w,T.send(null)},typeof arguments!="undefined"&&(Module.arguments=arguments),typeof console!="undefined")Module.print||(Module.print=function(p){console.log(p)}),Module.printErr||(Module.printErr=function(p){console.warn(p)});else{var TRY_USE_DUMP=!1;Module.print||(Module.print=TRY_USE_DUMP&&typeof dump!="undefined"?function(h){dump(h)}:function(h){})}ENVIRONMENT_IS_WORKER&&(Module.load=importScripts),typeof Module.setWindowTitle=="undefined"&&(Module.setWindowTitle=function(h){document.title=h})}else throw"Unknown runtime environment. Where are we?";function globalEval(h){eval.call(null,h)}!Module.load&&Module.read&&(Module.load=function(p){globalEval(Module.read(p))}),Module.print||(Module.print=function(){}),Module.printErr||(Module.printErr=Module.print),Module.arguments||(Module.arguments=[]),Module.thisProgram||(Module.thisProgram="./this.program"),Module.quit||(Module.quit=function(h,p){throw p}),Module.print=Module.print,Module.printErr=Module.printErr,Module.preRun=[],Module.postRun=[];for(var key in moduleOverrides)moduleOverrides.hasOwnProperty(key)&&(Module[key]=moduleOverrides[key]);moduleOverrides=void 0;var Runtime={setTempRet0:function(h){return tempRet0=h,h},getTempRet0:function(){return tempRet0},stackSave:function(){return STACKTOP},stackRestore:function(h){STACKTOP=h},getNativeTypeSize:function(h){switch(h){case"i1":case"i8":return 1;case"i16":return 2;case"i32":return 4;case"i64":return 8;case"float":return 4;case"double":return 8;default:{if(h[h.length-1]==="*")return Runtime.QUANTUM_SIZE;if(h[0]==="i"){var p=parseInt(h.substr(1));return assert(p%8===0),p/8}else return 0}}},getNativeFieldSize:function(h){return Math.max(Runtime.getNativeTypeSize(h),Runtime.QUANTUM_SIZE)},STACK_ALIGN:16,prepVararg:function(h,p){return p==="double"||p==="i64"?h&7&&(assert((h&7)===4),h+=4):assert((h&3)===0),h},getAlignSize:function(h,p,y){return!y&&(h=="i64"||h=="double")?8:h?Math.min(p||(h?Runtime.getNativeFieldSize(h):0),Runtime.QUANTUM_SIZE):Math.min(p,8)},dynCall:function(h,p,y){return y&&y.length?Module["dynCall_"+h].apply(null,[p].concat(y)):Module["dynCall_"+h].call(null,p)},functionPointers:[],addFunction:function(h){for(var p=0;p<Runtime.functionPointers.length;p++)if(!Runtime.functionPointers[p])return Runtime.functionPointers[p]=h,2*(1+p);throw"Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS."},removeFunction:function(h){Runtime.functionPointers[(h-2)/2]=null},warnOnce:function(h){Runtime.warnOnce.shown||(Runtime.warnOnce.shown={}),Runtime.warnOnce.shown[h]||(Runtime.warnOnce.shown[h]=1,Module.printErr(h))},funcWrappers:{},getFuncWrapper:function(h,p){if(h){assert(p),Runtime.funcWrappers[p]||(Runtime.funcWrappers[p]={});var y=Runtime.funcWrappers[p];return y[h]||(p.length===1?y[h]=function(){return Runtime.dynCall(p,h)}:p.length===2?y[h]=function(T){return Runtime.dynCall(p,h,[T])}:y[h]=function(){return Runtime.dynCall(p,h,Array.prototype.slice.call(arguments))}),y[h]}},getCompilerSetting:function(h){throw"You must build with -s RETAIN_COMPILER_SETTINGS=1 for Runtime.getCompilerSetting or emscripten_get_compiler_setting to work"},stackAlloc:function(h){var p=STACKTOP;return STACKTOP=STACKTOP+h|0,STACKTOP=STACKTOP+15&-16,p},staticAlloc:function(h){var p=STATICTOP;return STATICTOP=STATICTOP+h|0,STATICTOP=STATICTOP+15&-16,p},dynamicAlloc:function(h){var p=HEAP32[DYNAMICTOP_PTR>>2],y=(p+h+15|0)&-16;if(HEAP32[DYNAMICTOP_PTR>>2]=y,y>=TOTAL_MEMORY){var w=enlargeMemory();if(!w)return HEAP32[DYNAMICTOP_PTR>>2]=p,0}return p},alignMemory:function(h,p){var y=h=Math.ceil(h/(p||16))*(p||16);return y},makeBigInt:function(h,p,y){var w=y?+(h>>>0)+ +(p>>>0)*4294967296:+(h>>>0)+ +(p|0)*4294967296;return w},GLOBAL_BASE:8,QUANTUM_SIZE:4,__dummy__:0};Module.Runtime=Runtime;var ABORT=0,EXITSTATUS=0;function assert(h,p){h||abort("Assertion failed: "+p)}function getCFunc(ident){var func=Module["_"+ident];if(!func)try{func=eval("_"+ident)}catch(h){}return assert(func,"Cannot call unknown function "+ident+" (perhaps LLVM optimizations or closure removed it?)"),func}var cwrap,ccall;(function(){var JSfuncs={stackSave:function(){Runtime.stackSave()},stackRestore:function(){Runtime.stackRestore()},arrayToC:function(h){var p=Runtime.stackAlloc(h.length);return writeArrayToMemory(h,p),p},stringToC:function(h){var p=0;if(h!=null&&h!==0){var y=(h.length<<2)+1;p=Runtime.stackAlloc(y),stringToUTF8(h,p,y)}return p}},toC={string:JSfuncs.stringToC,array:JSfuncs.arrayToC};ccall=function(p,y,w,T,e){var B=getCFunc(p),N=[],F=0;if(T)for(var k=0;k<T.length;k++){var O=toC[w[k]];O?(F===0&&(F=Runtime.stackSave()),N[k]=O(T[k])):N[k]=T[k]}var H=B.apply(null,N);if(y==="string"&&(H=Pointer_stringify(H)),F!==0){if(e&&e.async){EmterpreterAsync.asyncFinalizers.push(function(){Runtime.stackRestore(F)});return}Runtime.stackRestore(F)}return H};var sourceRegex=/^function\s*[a-zA-Z$_0-9]*\s*\(([^)]*)\)\s*{\s*([^*]*?)[\s;]*(?:return\s*(.*?)[;\s]*)?}$/;function parseJSFunc(h){var p=h.toString().match(sourceRegex).slice(1);return{arguments:p[0],body:p[1],returnValue:p[2]}}var JSsource=null;function ensureJSsource(){if(!JSsource){JSsource={};for(var h in JSfuncs)JSfuncs.hasOwnProperty(h)&&(JSsource[h]=parseJSFunc(JSfuncs[h]))}}cwrap=function cwrap(ident,returnType,argTypes){argTypes=argTypes||[];var cfunc=getCFunc(ident),numericArgs=argTypes.every(function(h){return h==="number"}),numericRet=returnType!=="string";if(numericRet&&numericArgs)return cfunc;var argNames=argTypes.map(function(h,p){return"$"+p}),funcstr="(function("+argNames.join(",")+") {",nargs=argTypes.length;if(!numericArgs){ensureJSsource(),funcstr+="var stack = "+JSsource.stackSave.body+";";for(var i=0;i<nargs;i++){var arg=argNames[i],type=argTypes[i];if(type!=="number"){var convertCode=JSsource[type+"ToC"];funcstr+="var "+convertCode.arguments+" = "+arg+";",funcstr+=convertCode.body+";",funcstr+=arg+"=("+convertCode.returnValue+");"}}}var cfuncname=parseJSFunc(function(){return cfunc}).returnValue;if(funcstr+="var ret = "+cfuncname+"("+argNames.join(",")+");",!numericRet){var strgfy=parseJSFunc(function(){return Pointer_stringify}).returnValue;funcstr+="ret = "+strgfy+"(ret);"}return numericArgs||(ensureJSsource(),funcstr+=JSsource.stackRestore.body.replace("()","(stack)")+";"),funcstr+="return ret})",eval(funcstr)}})(),Module.ccall=ccall,Module.cwrap=cwrap;function setValue(h,p,y,w){switch(y=y||"i8",y.charAt(y.length-1)==="*"&&(y="i32"),y){case"i1":HEAP8[h>>0]=p;break;case"i8":HEAP8[h>>0]=p;break;case"i16":HEAP16[h>>1]=p;break;case"i32":HEAP32[h>>2]=p;break;case"i64":tempI64=[p>>>0,(tempDouble=p,+Math_abs(tempDouble)>=1?tempDouble>0?(Math_min(+Math_floor(tempDouble/4294967296),4294967295)|0)>>>0:~~+Math_ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[h>>2]=tempI64[0],HEAP32[h+4>>2]=tempI64[1];break;case"float":HEAPF32[h>>2]=p;break;case"double":HEAPF64[h>>3]=p;break;default:abort("invalid type for setValue: "+y)}}Module.setValue=setValue;function getValue(h,p,y){switch(p=p||"i8",p.charAt(p.length-1)==="*"&&(p="i32"),p){case"i1":return HEAP8[h>>0];case"i8":return HEAP8[h>>0];case"i16":return HEAP16[h>>1];case"i32":return HEAP32[h>>2];case"i64":return HEAP32[h>>2];case"float":return HEAPF32[h>>2];case"double":return HEAPF64[h>>3];default:abort("invalid type for setValue: "+p)}return null}Module.getValue=getValue;var ALLOC_NORMAL=0,ALLOC_STACK=1,ALLOC_STATIC=2,ALLOC_DYNAMIC=3,ALLOC_NONE=4;Module.ALLOC_NORMAL=ALLOC_NORMAL,Module.ALLOC_STACK=ALLOC_STACK,Module.ALLOC_STATIC=ALLOC_STATIC,Module.ALLOC_DYNAMIC=ALLOC_DYNAMIC,Module.ALLOC_NONE=ALLOC_NONE;function allocate(h,p,y,w){var T,e;typeof h=="number"?(T=!0,e=h):(T=!1,e=h.length);var B=typeof p=="string"?p:null,N;if(y==ALLOC_NONE?N=w:N=[typeof _malloc=="function"?_malloc:Runtime.staticAlloc,Runtime.stackAlloc,Runtime.staticAlloc,Runtime.dynamicAlloc][y===void 0?ALLOC_STATIC:y](Math.max(e,B?1:p.length)),T){var w=N,F;for(assert((N&3)==0),F=N+(e&-4);w<F;w+=4)HEAP32[w>>2]=0;for(F=N+e;w<F;)HEAP8[w++>>0]=0;return N}if(B==="i8")return h.subarray||h.slice?HEAPU8.set(h,N):HEAPU8.set(new Uint8Array(h),N),N;for(var k=0,O,H,D;k<e;){var i0=h[k];if(typeof i0=="function"&&(i0=Runtime.getFunctionIndex(i0)),O=B||p[k],O===0){k++;continue}O=="i64"&&(O="i32"),setValue(N+k,i0,O),D!==O&&(H=Runtime.getNativeTypeSize(O),D=O),k+=H}return N}Module.allocate=allocate;function getMemory(h){return staticSealed?runtimeInitialized?_malloc(h):Runtime.dynamicAlloc(h):Runtime.staticAlloc(h)}Module.getMemory=getMemory;function Pointer_stringify(h,p){if(p===0||!h)return"";for(var y=0,w,T=0;w=HEAPU8[h+T>>0],y|=w,!(w==0&&!p||(T++,p&&T==p)););p||(p=T);var e="";if(y<128){for(var B=1024,N;p>0;)N=String.fromCharCode.apply(String,HEAPU8.subarray(h,h+Math.min(p,B))),e=e?e+N:N,h+=B,p-=B;return e}return Module.UTF8ToString(h)}Module.Pointer_stringify=Pointer_stringify;function AsciiToString(h){for(var p="";;){var y=HEAP8[h++>>0];if(!y)return p;p+=String.fromCharCode(y)}}Module.AsciiToString=AsciiToString;function stringToAscii(h,p){return writeAsciiToMemory(h,p,!1)}Module.stringToAscii=stringToAscii;var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf8"):void 0;function UTF8ArrayToString(h,p){for(var y=p;h[y];)++y;if(y-p>16&&h.subarray&&UTF8Decoder)return UTF8Decoder.decode(h.subarray(p,y));for(var w,T,e,B,N,F,k="";;){if(w=h[p++],!w)return k;if(!(w&128)){k+=String.fromCharCode(w);continue}if(T=h[p++]&63,(w&224)==192){k+=String.fromCharCode((w&31)<<6|T);continue}if(e=h[p++]&63,(w&240)==224?w=(w&15)<<12|T<<6|e:(B=h[p++]&63,(w&248)==240?w=(w&7)<<18|T<<12|e<<6|B:(N=h[p++]&63,(w&252)==248?w=(w&3)<<24|T<<18|e<<12|B<<6|N:(F=h[p++]&63,w=(w&1)<<30|T<<24|e<<18|B<<12|N<<6|F))),w<65536)k+=String.fromCharCode(w);else{var O=w-65536;k+=String.fromCharCode(55296|O>>10,56320|O&1023)}}}Module.UTF8ArrayToString=UTF8ArrayToString;function UTF8ToString(h){return UTF8ArrayToString(HEAPU8,h)}Module.UTF8ToString=UTF8ToString;function stringToUTF8Array(h,p,y,w){if(!(w>0))return 0;for(var T=y,e=y+w-1,B=0;B<h.length;++B){var N=h.charCodeAt(B);if(N>=55296&&N<=57343&&(N=65536+((N&1023)<<10)|h.charCodeAt(++B)&1023),N<=127){if(y>=e)break;p[y++]=N}else if(N<=2047){if(y+1>=e)break;p[y++]=192|N>>6,p[y++]=128|N&63}else if(N<=65535){if(y+2>=e)break;p[y++]=224|N>>12,p[y++]=128|N>>6&63,p[y++]=128|N&63}else if(N<=2097151){if(y+3>=e)break;p[y++]=240|N>>18,p[y++]=128|N>>12&63,p[y++]=128|N>>6&63,p[y++]=128|N&63}else if(N<=67108863){if(y+4>=e)break;p[y++]=248|N>>24,p[y++]=128|N>>18&63,p[y++]=128|N>>12&63,p[y++]=128|N>>6&63,p[y++]=128|N&63}else{if(y+5>=e)break;p[y++]=252|N>>30,p[y++]=128|N>>24&63,p[y++]=128|N>>18&63,p[y++]=128|N>>12&63,p[y++]=128|N>>6&63,p[y++]=128|N&63}}return p[y]=0,y-T}Module.stringToUTF8Array=stringToUTF8Array;function stringToUTF8(h,p,y){return stringToUTF8Array(h,HEAPU8,p,y)}Module.stringToUTF8=stringToUTF8;function lengthBytesUTF8(h){for(var p=0,y=0;y<h.length;++y){var w=h.charCodeAt(y);w>=55296&&w<=57343&&(w=65536+((w&1023)<<10)|h.charCodeAt(++y)&1023),w<=127?++p:w<=2047?p+=2:w<=65535?p+=3:w<=2097151?p+=4:w<=67108863?p+=5:p+=6}return p}Module.lengthBytesUTF8=lengthBytesUTF8;var UTF16Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf-16le"):void 0;function demangle(h){var p=Module.___cxa_demangle||Module.__cxa_demangle;if(p){try{var y=h.substr(1),w=lengthBytesUTF8(y)+1,T=_malloc(w);stringToUTF8(y,T,w);var e=_malloc(4),B=p(T,0,0,e);if(getValue(e,"i32")===0&&B)return Pointer_stringify(B)}catch(N){}finally{T&&_free(T),e&&_free(e),B&&_free(B)}return h}return Runtime.warnOnce("warning: build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling"),h}function demangleAll(h){var p=/__Z[\w\d_]+/g;return h.replace(p,function(y){var w=demangle(y);return y===w?y:y+" ["+w+"]"})}function jsStackTrace(){var h=new Error;if(!h.stack){try{throw new Error(0)}catch(p){h=p}if(!h.stack)return"(no stack trace available)"}return h.stack.toString()}function stackTrace(){var h=jsStackTrace();return Module.extraStackTrace&&(h+=`
`+Module.extraStackTrace()),demangleAll(h)}Module.stackTrace=stackTrace;var HEAP,buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferViews(){Module.HEAP8=HEAP8=new Int8Array(buffer),Module.HEAP16=HEAP16=new Int16Array(buffer),Module.HEAP32=HEAP32=new Int32Array(buffer),Module.HEAPU8=HEAPU8=new Uint8Array(buffer),Module.HEAPU16=HEAPU16=new Uint16Array(buffer),Module.HEAPU32=HEAPU32=new Uint32Array(buffer),Module.HEAPF32=HEAPF32=new Float32Array(buffer),Module.HEAPF64=HEAPF64=new Float64Array(buffer)}var STATIC_BASE,STATICTOP,staticSealed,STACK_BASE,STACKTOP,STACK_MAX,DYNAMIC_BASE,DYNAMICTOP_PTR;STATIC_BASE=STATICTOP=STACK_BASE=STACKTOP=STACK_MAX=DYNAMIC_BASE=DYNAMICTOP_PTR=0,staticSealed=!1;function abortOnCannotGrowMemory(){abort("Cannot enlarge memory arrays. Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+TOTAL_MEMORY+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime but prevents some optimizations, (3) set Module.TOTAL_MEMORY to a higher value before the program runs, or (4) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")}function enlargeMemory(){abortOnCannotGrowMemory()}var TOTAL_STACK=Module.TOTAL_STACK||5242880,TOTAL_MEMORY=Module.TOTAL_MEMORY||134217728;TOTAL_MEMORY<TOTAL_STACK&&Module.printErr("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+TOTAL_MEMORY+"! (TOTAL_STACK="+TOTAL_STACK+")"),Module.buffer?buffer=Module.buffer:buffer=new ArrayBuffer(TOTAL_MEMORY),updateGlobalBufferViews();function getTotalMemory(){return TOTAL_MEMORY}if(HEAP32[0]=1668509029,HEAP16[1]=25459,HEAPU8[2]!==115||HEAPU8[3]!==99)throw"Runtime error: expected the system to be little-endian!";Module.HEAP=HEAP,Module.buffer=buffer,Module.HEAP8=HEAP8,Module.HEAP16=HEAP16,Module.HEAP32=HEAP32,Module.HEAPU8=HEAPU8,Module.HEAPU16=HEAPU16,Module.HEAPU32=HEAPU32,Module.HEAPF32=HEAPF32,Module.HEAPF64=HEAPF64;function callRuntimeCallbacks(h){for(;h.length>0;){var p=h.shift();if(typeof p=="function"){p();continue}var y=p.func;typeof y=="number"?p.arg===void 0?Module.dynCall_v(y):Module.dynCall_vi(y,p.arg):y(p.arg===void 0?null:p.arg)}}var __ATPRERUN__=[],__ATINIT__=[],__ATMAIN__=[],__ATEXIT__=[],__ATPOSTRUN__=[],runtimeInitialized=!1,runtimeExited=!1;function preRun(){if(Module.preRun)for(typeof Module.preRun=="function"&&(Module.preRun=[Module.preRun]);Module.preRun.length;)addOnPreRun(Module.preRun.shift());callRuntimeCallbacks(__ATPRERUN__)}function ensureInitRuntime(){runtimeInitialized||(runtimeInitialized=!0,callRuntimeCallbacks(__ATINIT__))}function preMain(){callRuntimeCallbacks(__ATMAIN__)}function exitRuntime(){callRuntimeCallbacks(__ATEXIT__),runtimeExited=!0}function postRun(){if(Module.postRun)for(typeof Module.postRun=="function"&&(Module.postRun=[Module.postRun]);Module.postRun.length;)addOnPostRun(Module.postRun.shift());callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(h){__ATPRERUN__.unshift(h)}Module.addOnPreRun=addOnPreRun;function addOnInit(h){__ATINIT__.unshift(h)}Module.addOnInit=addOnInit;function addOnPreMain(h){__ATMAIN__.unshift(h)}Module.addOnPreMain=addOnPreMain;function addOnExit(h){__ATEXIT__.unshift(h)}Module.addOnExit=addOnExit;function addOnPostRun(h){__ATPOSTRUN__.unshift(h)}Module.addOnPostRun=addOnPostRun;function intArrayFromString(h,p,y){var w=y>0?y:lengthBytesUTF8(h)+1,T=new Array(w),e=stringToUTF8Array(h,T,0,T.length);return p&&(T.length=e),T}Module.intArrayFromString=intArrayFromString;function intArrayToString(h){for(var p=[],y=0;y<h.length;y++){var w=h[y];w>255&&(w&=255),p.push(String.fromCharCode(w))}return p.join("")}Module.intArrayToString=intArrayToString;function writeStringToMemory(h,p,y){Runtime.warnOnce("writeStringToMemory is deprecated and should not be called! Use stringToUTF8() instead!");var w,T;y&&(T=p+lengthBytesUTF8(h),w=HEAP8[T]),stringToUTF8(h,p,1/0),y&&(HEAP8[T]=w)}Module.writeStringToMemory=writeStringToMemory;function writeArrayToMemory(h,p){HEAP8.set(h,p)}Module.writeArrayToMemory=writeArrayToMemory;function writeAsciiToMemory(h,p,y){for(var w=0;w<h.length;++w)HEAP8[p++>>0]=h.charCodeAt(w);y||(HEAP8[p>>0]=0)}if(Module.writeAsciiToMemory=writeAsciiToMemory,(!Math.imul||Math.imul(4294967295,5)!==-5)&&(Math.imul=function h(p,y){var w=p>>>16,T=p&65535,e=y>>>16,B=y&65535;return T*B+(w*B+T*e<<16)|0}),Math.imul=Math.imul,!Math.fround){var froundBuffer=new Float32Array(1);Math.fround=function(h){return froundBuffer[0]=h,froundBuffer[0]}}Math.fround=Math.fround,Math.clz32||(Math.clz32=function(h){h=h>>>0;for(var p=0;p<32;p++)if(h&1<<31-p)return p;return 32}),Math.clz32=Math.clz32,Math.trunc||(Math.trunc=function(h){return h<0?Math.ceil(h):Math.floor(h)}),Math.trunc=Math.trunc;var Math_abs=Math.abs,Math_cos=Math.cos,Math_sin=Math.sin,Math_tan=Math.tan,Math_acos=Math.acos,Math_asin=Math.asin,Math_atan=Math.atan,Math_atan2=Math.atan2,Math_exp=Math.exp,Math_log=Math.log,Math_sqrt=Math.sqrt,Math_ceil=Math.ceil,Math_floor=Math.floor,Math_pow=Math.pow,Math_imul=Math.imul,Math_fround=Math.fround,Math_round=Math.round,Math_min=Math.min,Math_clz32=Math.clz32,Math_trunc=Math.trunc,runDependencies=0,runDependencyWatcher=null,dependenciesFulfilled=null;function getUniqueRunDependency(h){return h}function addRunDependency(h){runDependencies++,Module.monitorRunDependencies&&Module.monitorRunDependencies(runDependencies)}Module.addRunDependency=addRunDependency;function removeRunDependency(h){if(runDependencies--,Module.monitorRunDependencies&&Module.monitorRunDependencies(runDependencies),runDependencies==0&&(runDependencyWatcher!==null&&(clearInterval(runDependencyWatcher),runDependencyWatcher=null),dependenciesFulfilled)){var p=dependenciesFulfilled;dependenciesFulfilled=null,p()}}Module.removeRunDependency=removeRunDependency,Module.preloadedImages={},Module.preloadedAudios={};var ASM_CONSTS=[function(h,p,y,w,T,e,B,N){return _nbind.callbackSignatureList[h].apply(this,arguments)}];function _emscripten_asm_const_iiiiiiii(h,p,y,w,T,e,B,N){return ASM_CONSTS[h](p,y,w,T,e,B,N)}function _emscripten_asm_const_iiiii(h,p,y,w,T){return ASM_CONSTS[h](p,y,w,T)}function _emscripten_asm_const_iiidddddd(h,p,y,w,T,e,B,N,F){return ASM_CONSTS[h](p,y,w,T,e,B,N,F)}function _emscripten_asm_const_iiididi(h,p,y,w,T,e,B){return ASM_CONSTS[h](p,y,w,T,e,B)}function _emscripten_asm_const_iiii(h,p,y,w){return ASM_CONSTS[h](p,y,w)}function _emscripten_asm_const_iiiid(h,p,y,w,T){return ASM_CONSTS[h](p,y,w,T)}function _emscripten_asm_const_iiiiii(h,p,y,w,T,e){return ASM_CONSTS[h](p,y,w,T,e)}STATIC_BASE=Runtime.GLOBAL_BASE,STATICTOP=STATIC_BASE+12800,__ATINIT__.push({func:function(){__GLOBAL__sub_I_Yoga_cpp()}},{func:function(){__GLOBAL__sub_I_nbind_cc()}},{func:function(){__GLOBAL__sub_I_common_cc()}},{func:function(){__GLOBAL__sub_I_Binding_cc()}}),allocate([0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,192,127,0,0,192,127,0,0,192,127,0,0,192,127,3,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,3,0,0,0,0,0,192,127,3,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,192,127,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,192,127,0,0,192,127,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,192,127,0,0,0,0,0,0,0,0,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,192,127,0,0,192,127,0,0,0,0,0,0,0,0,255,255,255,255,255,255,255,255,0,0,128,191,0,0,128,191,0,0,192,127,0,0,0,0,0,0,0,0,0,0,128,63,1,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,3,0,0,0,1,0,0,0,2,0,0,0,0,0,0,0,190,12,0,0,200,12,0,0,208,12,0,0,216,12,0,0,230,12,0,0,242,12,0,0,1,0,0,0,3,0,0,0,0,0,0,0,2,0,0,0,0,0,192,127,3,0,0,0,180,45,0,0,181,45,0,0,182,45,0,0,181,45,0,0,182,45,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,3,0,0,0,1,0,0,0,4,0,0,0,183,45,0,0,181,45,0,0,181,45,0,0,181,45,0,0,181,45,0,0,181,45,0,0,181,45,0,0,184,45,0,0,185,45,0,0,181,45,0,0,181,45,0,0,182,45,0,0,186,45,0,0,185,45,0,0,148,4,0,0,3,0,0,0,187,45,0,0,164,4,0,0,188,45,0,0,2,0,0,0,189,45,0,0,164,4,0,0,188,45,0,0,185,45,0,0,164,4,0,0,185,45,0,0,164,4,0,0,188,45,0,0,181,45,0,0,182,45,0,0,181,45,0,0,0,0,0,0,0,0,0,0,1,0,0,0,5,0,0,0,6,0,0,0,1,0,0,0,7,0,0,0,183,45,0,0,182,45,0,0,181,45,0,0,190,45,0,0,190,45,0,0,182,45,0,0,182,45,0,0,185,45,0,0,181,45,0,0,185,45,0,0,182,45,0,0,181,45,0,0,185,45,0,0,182,45,0,0,185,45,0,0,48,5,0,0,3,0,0,0,56,5,0,0,1,0,0,0,189,45,0,0,185,45,0,0,164,4,0,0,76,5,0,0,2,0,0,0,191,45,0,0,186,45,0,0,182,45,0,0,185,45,0,0,192,45,0,0,185,45,0,0,182,45,0,0,186,45,0,0,185,45,0,0,76,5,0,0,76,5,0,0,136,5,0,0,182,45,0,0,181,45,0,0,2,0,0,0,190,45,0,0,136,5,0,0,56,19,0,0,156,5,0,0,2,0,0,0,184,45,0,0,0,0,0,0,0,0,0,0,1,0,0,0,8,0,0,0,9,0,0,0,1,0,0,0,10,0,0,0,204,5,0,0,181,45,0,0,181,45,0,0,2,0,0,0,180,45,0,0,204,5,0,0,2,0,0,0,195,45,0,0,236,5,0,0,97,19,0,0,198,45,0,0,211,45,0,0,212,45,0,0,213,45,0,0,214,45,0,0,215,45,0,0,188,45,0,0,182,45,0,0,216,45,0,0,217,45,0,0,218,45,0,0,219,45,0,0,192,45,0,0,181,45,0,0,0,0,0,0,185,45,0,0,110,19,0,0,186,45,0,0,115,19,0,0,221,45,0,0,120,19,0,0,148,4,0,0,132,19,0,0,96,6,0,0,145,19,0,0,222,45,0,0,164,19,0,0,223,45,0,0,173,19,0,0,0,0,0,0,3,0,0,0,104,6,0,0,1,0,0,0,187,45,0,0,0,0,0,0,0,0,0,0,1,0,0,0,11,0,0,0,12,0,0,0,1,0,0,0,13,0,0,0,185,45,0,0,224,45,0,0,164,6,0,0,188,45,0,0,172,6,0,0,180,6,0,0,2,0,0,0,188,6,0,0,7,0,0,0,224,45,0,0,7,0,0,0,164,6,0,0,1,0,0,0,213,45,0,0,185,45,0,0,224,45,0,0,172,6,0,0,185,45,0,0,224,45,0,0,164,6,0,0,185,45,0,0,224,45,0,0,211,45,0,0,211,45,0,0,222,45,0,0,211,45,0,0,224,45,0,0,222,45,0,0,211,45,0,0,224,45,0,0,172,6,0,0,222,45,0,0,211,45,0,0,224,45,0,0,188,45,0,0,222,45,0,0,211,45,0,0,40,7,0,0,188,45,0,0,2,0,0,0,224,45,0,0,185,45,0,0,188,45,0,0,188,45,0,0,188,45,0,0,188,45,0,0,222,45,0,0,224,45,0,0,148,4,0,0,185,45,0,0,148,4,0,0,148,4,0,0,148,4,0,0,148,4,0,0,148,4,0,0,185,45,0,0,164,6,0,0,148,4,0,0,0,0,0,0,0,0,0,0,1,0,0,0,14,0,0,0,15,0,0,0,1,0,0,0,16,0,0,0,148,7,0,0,2,0,0,0,225,45,0,0,183,45,0,0,188,45,0,0,168,7,0,0,5,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,234,45,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,148,45,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,28,9,0,0,5,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,3,0,0,0,2,0,0,0,242,45,0,0,0,4,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,10,255,255,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,67,111,117,108,100,32,110,111,116,32,97,108,108,111,99,97,116,101,32,109,101,109,111,114,121,32,102,111,114,32,110,111,100,101,0,67,97,110,110,111,116,32,114,101,115,101,116,32,97,32,110,111,100,101,32,119,104,105,99,104,32,115,116,105,108,108,32,104,97,115,32,99,104,105,108,100,114,101,110,32,97,116,116,97,99,104,101,100,0,67,97,110,110,111,116,32,114,101,115,101,116,32,97,32,110,111,100,101,32,115,116,105,108,108,32,97,116,116,97,99,104,101,100,32,116,111,32,97,32,112,97,114,101,110,116,0,67,111,117,108,100,32,110,111,116,32,97,108,108,111,99,97,116,101,32,109,101,109,111,114,121,32,102,111,114,32,99,111,110,102,105,103,0,67,97,110,110,111,116,32,115,101,116,32,109,101,97,115,117,114,101,32,102,117,110,99,116,105,111,110,58,32,78,111,100,101,115,32,119,105,116,104,32,109,101,97,115,117,114,101,32,102,117,110,99,116,105,111,110,115,32,99,97,110,110,111,116,32,104,97,118,101,32,99,104,105,108,100,114,101,110,46,0,67,104,105,108,100,32,97,108,114,101,97,100,121,32,104,97,115,32,97,32,112,97,114,101,110,116,44,32,105,116,32,109,117,115,116,32,98,101,32,114,101,109,111,118,101,100,32,102,105,114,115,116,46,0,67,97,110,110,111,116,32,97,100,100,32,99,104,105,108,100,58,32,78,111,100,101,115,32,119,105,116,104,32,109,101,97,115,117,114,101,32,102,117,110,99,116,105,111,110,115,32,99,97,110,110,111,116,32,104,97,118,101,32,99,104,105,108,100,114,101,110,46,0,79,110,108,121,32,108,101,97,102,32,110,111,100,101,115,32,119,105,116,104,32,99,117,115,116,111,109,32,109,101,97,115,117,114,101,32,102,117,110,99,116,105,111,110,115,115,104,111,117,108,100,32,109,97,110,117,97,108,108,121,32,109,97,114,107,32,116,104,101,109,115,101,108,118,101,115,32,97,115,32,100,105,114,116,121,0,67,97,110,110,111,116,32,103,101,116,32,108,97,121,111,117,116,32,112,114,111,112,101,114,116,105,101,115,32,111,102,32,109,117,108,116,105,45,101,100,103,101,32,115,104,111,114,116,104,97,110,100,115,0,37,115,37,100,46,123,91,115,107,105,112,112,101,100,93,32,0,119,109,58,32,37,115,44,32,104,109,58,32,37,115,44,32,97,119,58,32,37,102,32,97,104,58,32,37,102,32,61,62,32,100,58,32,40,37,102,44,32,37,102,41,32,37,115,10,0,37,115,37,100,46,123,37,115,0,42,0,119,109,58,32,37,115,44,32,104,109,58,32,37,115,44,32,97,119,58,32,37,102,32,97,104,58,32,37,102,32,37,115,10,0,37,115,37,100,46,125,37,115,0,119,109,58,32,37,115,44,32,104,109,58,32,37,115,44,32,100,58,32,40,37,102,44,32,37,102,41,32,37,115,10,0,79,117,116,32,111,102,32,99,97,99,104,101,32,101,110,116,114,105,101,115,33,10,0,83,99,97,108,101,32,102,97,99,116,111,114,32,115,104,111,117,108,100,32,110,111,116,32,98,101,32,108,101,115,115,32,116,104,97,110,32,122,101,114,111,0,105,110,105,116,105,97,108,0,37,115,10,0,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,32,0,85,78,68,69,70,73,78,69,68,0,69,88,65,67,84,76,89,0,65,84,95,77,79,83,84,0,76,65,89,95,85,78,68,69,70,73,78,69,68,0,76,65,89,95,69,88,65,67,84,76,89,0,76,65,89,95,65,84,95,77,79,83,84,0,97,118,97,105,108,97,98,108,101,87,105,100,116,104,32,105,115,32,105,110,100,101,102,105,110,105,116,101,32,115,111,32,119,105,100,116,104,77,101,97,115,117,114,101,77,111,100,101,32,109,117,115,116,32,98,101,32,89,71,77,101,97,115,117,114,101,77,111,100,101,85,110,100,101,102,105,110,101,100,0,97,118,97,105,108,97,98,108,101,72,101,105,103,104,116,32,105,115,32,105,110,100,101,102,105,110,105,116,101,32,115,111,32,104,101,105,103,104,116,77,101,97,115,117,114,101,77,111,100,101,32,109,117,115,116,32,98,101,32,89,71,77,101,97,115,117,114,101,77,111,100,101,85,110,100,101,102,105,110,101,100,0,102,108,101,120,0,115,116,114,101,116,99,104,0,109,117,108,116,105,108,105,110,101,45,115,116,114,101,116,99,104,0,69,120,112,101,99,116,101,100,32,110,111,100,101,32,116,111,32,104,97,118,101,32,99,117,115,116,111,109,32,109,101,97,115,117,114,101,32,102,117,110,99,116,105,111,110,0,109,101,97,115,117,114,101,0,69,120,112,101,99,116,32,99,117,115,116,111,109,32,98,97,115,101,108,105,110,101,32,102,117,110,99,116,105,111,110,32,116,111,32,110,111,116,32,114,101,116,117,114,110,32,78,97,78,0,97,98,115,45,109,101,97,115,117,114,101,0,97,98,115,45,108,97,121,111,117,116,0,78,111,100,101,0,99,114,101,97,116,101,68,101,102,97,117,108,116,0,99,114,101,97,116,101,87,105,116,104,67,111,110,102,105,103,0,100,101,115,116,114,111,121,0,114,101,115,101,116,0,99,111,112,121,83,116,121,108,101,0,115,101,116,80,111,115,105,116,105,111,110,84,121,112,101,0,115,101,116,80,111,115,105,116,105,111,110,0,115,101,116,80,111,115,105,116,105,111,110,80,101,114,99,101,110,116,0,115,101,116,65,108,105,103,110,67,111,110,116,101,110,116,0,115,101,116,65,108,105,103,110,73,116,101,109,115,0,115,101,116,65,108,105,103,110,83,101,108,102,0,115,101,116,70,108,101,120,68,105,114,101,99,116,105,111,110,0,115,101,116,70,108,101,120,87,114,97,112,0,115,101,116,74,117,115,116,105,102,121,67,111,110,116,101,110,116,0,115,101,116,77,97,114,103,105,110,0,115,101,116,77,97,114,103,105,110,80,101,114,99,101,110,116,0,115,101,116,77,97,114,103,105,110,65,117,116,111,0,115,101,116,79,118,101,114,102,108,111,119,0,115,101,116,68,105,115,112,108,97,121,0,115,101,116,70,108,101,120,0,115,101,116,70,108,101,120,66,97,115,105,115,0,115,101,116,70,108,101,120,66,97,115,105,115,80,101,114,99,101,110,116,0,115,101,116,70,108,101,120,71,114,111,119,0,115,101,116,70,108,101,120,83,104,114,105,110,107,0,115,101,116,87,105,100,116,104,0,115,101,116,87,105,100,116,104,80,101,114,99,101,110,116,0,115,101,116,87,105,100,116,104,65,117,116,111,0,115,101,116,72,101,105,103,104,116,0,115,101,116,72,101,105,103,104,116,80,101,114,99,101,110,116,0,115,101,116,72,101,105,103,104,116,65,117,116,111,0,115,101,116,77,105,110,87,105,100,116,104,0,115,101,116,77,105,110,87,105,100,116,104,80,101,114,99,101,110,116,0,115,101,116,77,105,110,72,101,105,103,104,116,0,115,101,116,77,105,110,72,101,105,103,104,116,80,101,114,99,101,110,116,0,115,101,116,77,97,120,87,105,100,116,104,0,115,101,116,77,97,120,87,105,100,116,104,80,101,114,99,101,110,116,0,115,101,116,77,97,120,72,101,105,103,104,116,0,115,101,116,77,97,120,72,101,105,103,104,116,80,101,114,99,101,110,116,0,115,101,116,65,115,112,101,99,116,82,97,116,105,111,0,115,101,116,66,111,114,100,101,114,0,115,101,116,80,97,100,100,105,110,103,0,115,101,116,80,97,100,100,105,110,103,80,101,114,99,101,110,116,0,103,101,116,80,111,115,105,116,105,111,110,84,121,112,101,0,103,101,116,80,111,115,105,116,105,111,110,0,103,101,116,65,108,105,103,110,67,111,110,116,101,110,116,0,103,101,116,65,108,105,103,110,73,116,101,109,115,0,103,101,116,65,108,105,103,110,83,101,108,102,0,103,101,116,70,108,101,120,68,105,114,101,99,116,105,111,110,0,103,101,116,70,108,101,120,87,114,97,112,0,103,101,116,74,117,115,116,105,102,121,67,111,110,116,101,110,116,0,103,101,116,77,97,114,103,105,110,0,103,101,116,70,108,101,120,66,97,115,105,115,0,103,101,116,70,108,101,120,71,114,111,119,0,103,101,116,70,108,101,120,83,104,114,105,110,107,0,103,101,116,87,105,100,116,104,0,103,101,116,72,101,105,103,104,116,0,103,101,116,77,105,110,87,105,100,116,104,0,103,101,116,77,105,110,72,101,105,103,104,116,0,103,101,116,77,97,120,87,105,100,116,104,0,103,101,116,77,97,120,72,101,105,103,104,116,0,103,101,116,65,115,112,101,99,116,82,97,116,105,111,0,103,101,116,66,111,114,100,101,114,0,103,101,116,79,118,101,114,102,108,111,119,0,103,101,116,68,105,115,112,108,97,121,0,103,101,116,80,97,100,100,105,110,103,0,105,110,115,101,114,116,67,104,105,108,100,0,114,101,109,111,118,101,67,104,105,108,100,0,103,101,116,67,104,105,108,100,67,111,117,110,116,0,103,101,116,80,97,114,101,110,116,0,103,101,116,67,104,105,108,100,0,115,101,116,77,101,97,115,117,114,101,70,117,110,99,0,117,110,115,101,116,77,101,97,115,117,114,101,70,117,110,99,0,109,97,114,107,68,105,114,116,121,0,105,115,68,105,114,116,121,0,99,97,108,99,117,108,97,116,101,76,97,121,111,117,116,0,103,101,116,67,111,109,112,117,116,101,100,76,101,102,116,0,103,101,116,67,111,109,112,117,116,101,100,82,105,103,104,116,0,103,101,116,67,111,109,112,117,116,101,100,84,111,112,0,103,101,116,67,111,109,112,117,116,101,100,66,111,116,116,111,109,0,103,101,116,67,111,109,112,117,116,101,100,87,105,100,116,104,0,103,101,116,67,111,109,112,117,116,101,100,72,101,105,103,104,116,0,103,101,116,67,111,109,112,117,116,101,100,76,97,121,111,117,116,0,103,101,116,67,111,109,112,117,116,101,100,77,97,114,103,105,110,0,103,101,116,67,111,109,112,117,116,101,100,66,111,114,100,101,114,0,103,101,116,67,111,109,112,117,116,101,100,80,97,100,100,105,110,103,0,67,111,110,102,105,103,0,99,114,101,97,116,101,0,115,101,116,69,120,112,101,114,105,109,101,110,116,97,108,70,101,97,116,117,114,101,69,110,97,98,108,101,100,0,115,101,116,80,111,105,110,116,83,99,97,108,101,70,97,99,116,111,114,0,105,115,69,120,112,101,114,105,109,101,110,116,97,108,70,101,97,116,117,114,101,69,110,97,98,108,101,100,0,86,97,108,117,101,0,76,97,121,111,117,116,0,83,105,122,101,0,103,101,116,73,110,115,116,97,110,99,101,67,111,117,110,116,0,73,110,116,54,52,0,1,1,1,2,2,4,4,4,4,8,8,4,8,118,111,105,100,0,98,111,111,108,0,115,116,100,58,58,115,116,114,105,110,103,0,99,98,70,117,110,99,116,105,111,110,32,38,0,99,111,110,115,116,32,99,98,70,117,110,99,116,105,111,110,32,38,0,69,120,116,101,114,110,97,108,0,66,117,102,102,101,114,0,78,66,105,110,100,73,68,0,78,66,105,110,100,0,98,105,110,100,95,118,97,108,117,101,0,114,101,102,108,101,99,116,0,113,117,101,114,121,84,121,112,101,0,108,97,108,108,111,99,0,108,114,101,115,101,116,0,123,114,101,116,117,114,110,40,95,110,98,105,110,100,46,99,97,108,108,98,97,99,107,83,105,103,110,97,116,117,114,101,76,105,115,116,91,36,48,93,46,97,112,112,108,121,40,116,104,105,115,44,97,114,103,117,109,101,110,116,115,41,41,59,125,0,95,110,98,105,110,100,95,110,101,119,0,17,0,10,0,17,17,17,0,0,0,0,5,0,0,0,0,0,0,9,0,0,0,0,11,0,0,0,0,0,0,0,0,17,0,15,10,17,17,17,3,10,7,0,1,19,9,11,11,0,0,9,6,11,0,0,11,0,6,17,0,0,0,17,17,17,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,11,0,0,0,0,0,0,0,0,17,0,10,10,17,17,17,0,10,0,0,2,0,9,11,0,0,0,9,0,11,0,0,11,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,12,0,0,0,0,9,12,0,0,0,0,0,12,0,0,12,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,14,0,0,0,0,0,0,0,0,0,0,0,13,0,0,0,4,13,0,0,0,0,9,14,0,0,0,0,0,14,0,0,14,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16,0,0,0,0,0,0,0,0,0,0,0,15,0,0,0,0,15,0,0,0,0,9,16,0,0,0,0,0,16,0,0,16,0,0,18,0,0,0,18,18,18,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,18,0,0,0,18,18,18,0,0,0,0,0,0,9,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,11,0,0,0,0,0,0,0,0,0,0,0,10,0,0,0,0,10,0,0,0,0,9,11,0,0,0,0,0,11,0,0,11,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,0,0,0,0,0,0,0,12,0,0,0,0,12,0,0,0,0,9,12,0,0,0,0,0,12,0,0,12,0,0,45,43,32,32,32,48,88,48,120,0,40,110,117,108,108,41,0,45,48,88,43,48,88,32,48,88,45,48,120,43,48,120,32,48,120,0,105,110,102,0,73,78,70,0,110,97,110,0,78,65,78,0,48,49,50,51,52,53,54,55,56,57,65,66,67,68,69,70,46,0,84,33,34,25,13,1,2,3,17,75,28,12,16,4,11,29,18,30,39,104,110,111,112,113,98,32,5,6,15,19,20,21,26,8,22,7,40,36,23,24,9,10,14,27,31,37,35,131,130,125,38,42,43,60,61,62,63,67,71,74,77,88,89,90,91,92,93,94,95,96,97,99,100,101,102,103,105,106,107,108,114,115,116,121,122,123,124,0,73,108,108,101,103,97,108,32,98,121,116,101,32,115,101,113,117,101,110,99,101,0,68,111,109,97,105,110,32,101,114,114,111,114,0,82,101,115,117,108,116,32,110,111,116,32,114,101,112,114,101,115,101,110,116,97,98,108,101,0,78,111,116,32,97,32,116,116,121,0,80,101,114,109,105,115,115,105,111,110,32,100,101,110,105,101,100,0,79,112,101,114,97,116,105,111,110,32,110,111,116,32,112,101,114,109,105,116,116,101,100,0,78,111,32,115,117,99,104,32,102,105,108,101,32,111,114,32,100,105,114,101,99,116,111,114,121,0,78,111,32,115,117,99,104,32,112,114,111,99,101,115,115,0,70,105,108,101,32,101,120,105,115,116,115,0,86,97,108,117,101,32,116,111,111,32,108,97,114,103,101,32,102,111,114,32,100,97,116,97,32,116,121,112,101,0,78,111,32,115,112,97,99,101,32,108,101,102,116,32,111,110,32,100,101,118,105,99,101,0,79,117,116,32,111,102,32,109,101,109,111,114,121,0,82,101,115,111,117,114,99,101,32,98,117,115,121,0,73,110,116,101,114,114,117,112,116,101,100,32,115,121,115,116,101,109,32,99,97,108,108,0,82,101,115,111,117,114,99,101,32,116,101,109,112,111,114,97,114,105,108,121,32,117,110,97,118,97,105,108,97,98,108,101,0,73,110,118,97,108,105,100,32,115,101,101,107,0,67,114,111,115,115,45,100,101,118,105,99,101,32,108,105,110,107,0,82,101,97,100,45,111,110,108,121,32,102,105,108,101,32,115,121,115,116,101,109,0,68,105,114,101,99,116,111,114,121,32,110,111,116,32,101,109,112,116,121,0,67,111,110,110,101,99,116,105,111,110,32,114,101,115,101,116,32,98,121,32,112,101,101,114,0,79,112,101,114,97,116,105,111,110,32,116,105,109,101,100,32,111,117,116,0,67,111,110,110,101,99,116,105,111,110,32,114,101,102,117,115,101,100,0,72,111,115,116,32,105,115,32,100,111,119,110,0,72,111,115,116,32,105,115,32,117,110,114,101,97,99,104,97,98,108,101,0,65,100,100,114,101,115,115,32,105,110,32,117,115,101,0,66,114,111,107,101,110,32,112,105,112,101,0,73,47,79,32,101,114,114,111,114,0,78,111,32,115,117,99,104,32,100,101,118,105,99,101,32,111,114,32,97,100,100,114,101,115,115,0,66,108,111,99,107,32,100,101,118,105,99,101,32,114,101,113,117,105,114,101,100,0,78,111,32,115,117,99,104,32,100,101,118,105,99,101,0,78,111,116,32,97,32,100,105,114,101,99,116,111,114,121,0,73,115,32,97,32,100,105,114,101,99,116,111,114,121,0,84,101,120,116,32,102,105,108,101,32,98,117,115,121,0,69,120,101,99,32,102,111,114,109,97,116,32,101,114,114,111,114,0,73,110,118,97,108,105,100,32,97,114,103,117,109,101,110,116,0,65,114,103,117,109,101,110,116,32,108,105,115,116,32,116,111,111,32,108,111,110,103,0,83,121,109,98,111,108,105,99,32,108,105,110,107,32,108,111,111,112,0,70,105,108,101,110,97,109,101,32,116,111,111,32,108,111,110,103,0,84,111,111,32,109,97,110,121,32,111,112,101,110,32,102,105,108,101,115,32,105,110,32,115,121,115,116,101,109,0,78,111,32,102,105,108,101,32,100,101,115,99,114,105,112,116,111,114,115,32,97,118,97,105,108,97,98,108,101,0,66,97,100,32,102,105,108,101,32,100,101,115,99,114,105,112,116,111,114,0,78,111,32,99,104,105,108,100,32,112,114,111,99,101,115,115,0,66,97,100,32,97,100,100,114,101,115,115,0,70,105,108,101,32,116,111,111,32,108,97,114,103,101,0,84,111,111,32,109,97,110,121,32,108,105,110,107,115,0,78,111,32,108,111,99,107,115,32,97,118,97,105,108,97,98,108,101,0,82,101,115,111,117,114,99,101,32,100,101,97,100,108,111,99,107,32,119,111,117,108,100,32,111,99,99,117,114,0,83,116,97,116,101,32,110,111,116,32,114,101,99,111,118,101,114,97,98,108,101,0,80,114,101,118,105,111,117,115,32,111,119,110,101,114,32,100,105,101,100,0,79,112,101,114,97,116,105,111,110,32,99,97,110,99,101,108,101,100,0,70,117,110,99,116,105,111,110,32,110,111,116,32,105,109,112,108,101,109,101,110,116,101,100,0,78,111,32,109,101,115,115,97,103,101,32,111,102,32,100,101,115,105,114,101,100,32,116,121,112,101,0,73,100,101,110,116,105,102,105,101,114,32,114,101,109,111,118,101,100,0,68,101,118,105,99,101,32,110,111,116,32,97,32,115,116,114,101,97,109,0,78,111,32,100,97,116,97,32,97,118,97,105,108,97,98,108,101,0,68,101,118,105,99,101,32,116,105,109,101,111,117,116,0,79,117,116,32,111,102,32,115,116,114,101,97,109,115,32,114,101,115,111,117,114,99,101,115,0,76,105,110,107,32,104,97,115,32,98,101,101,110,32,115,101,118,101,114,101,100,0,80,114,111,116,111,99,111,108,32,101,114,114,111,114,0,66,97,100,32,109,101,115,115,97,103,101,0,70,105,108,101,32,100,101,115,99,114,105,112,116,111,114,32,105,110,32,98,97,100,32,115,116,97,116,101,0,78,111,116,32,97,32,115,111,99,107,101,116,0,68,101,115,116,105,110,97,116,105,111,110,32,97,100,100,114,101,115,115,32,114,101,113,117,105,114,101,100,0,77,101,115,115,97,103,101,32,116,111,111,32,108,97,114,103,101,0,80,114,111,116,111,99,111,108,32,119,114,111,110,103,32,116,121,112,101,32,102,111,114,32,115,111,99,107,101,116,0,80,114,111,116,111,99,111,108,32,110,111,116,32,97,118,97,105,108,97,98,108,101,0,80,114,111,116,111,99,111,108,32,110,111,116,32,115,117,112,112,111,114,116,101,100,0,83,111,99,107,101,116,32,116,121,112,101,32,110,111,116,32,115,117,112,112,111,114,116,101,100,0,78,111,116,32,115,117,112,112,111,114,116,101,100,0,80,114,111,116,111,99,111,108,32,102,97,109,105,108,121,32,110,111,116,32,115,117,112,112,111,114,116,101,100,0,65,100,100,114,101,115,115,32,102,97,109,105,108,121,32,110,111,116,32,115,117,112,112,111,114,116,101,100,32,98,121,32,112,114,111,116,111,99,111,108,0,65,100,100,114,101,115,115,32,110,111,116,32,97,118,97,105,108,97,98,108,101,0,78,101,116,119,111,114,107,32,105,115,32,100,111,119,110,0,78,101,116,119,111,114,107,32,117,110,114,101,97,99,104,97,98,108,101,0,67,111,110,110,101,99,116,105,111,110,32,114,101,115,101,116,32,98,121,32,110,101,116,119,111,114,107,0,67,111,110,110,101,99,116,105,111,110,32,97,98,111,114,116,101,100,0,78,111,32,98,117,102,102,101,114,32,115,112,97,99,101,32,97,118,97,105,108,97,98,108,101,0,83,111,99,107,101,116,32,105,115,32,99,111,110,110,101,99,116,101,100,0,83,111,99,107,101,116,32,110,111,116,32,99,111,110,110,101,99,116,101,100,0,67,97,110,110,111,116,32,115,101,110,100,32,97,102,116,101,114,32,115,111,99,107,101,116,32,115,104,117,116,100,111,119,110,0,79,112,101,114,97,116,105,111,110,32,97,108,114,101,97,100,121,32,105,110,32,112,114,111,103,114,101,115,115,0,79,112,101,114,97,116,105,111,110,32,105,110,32,112,114,111,103,114,101,115,115,0,83,116,97,108,101,32,102,105,108,101,32,104,97,110,100,108,101,0,82,101,109,111,116,101,32,73,47,79,32,101,114,114,111,114,0,81,117,111,116,97,32,101,120,99,101,101,100,101,100,0,78,111,32,109,101,100,105,117,109,32,102,111,117,110,100,0,87,114,111,110,103,32,109,101,100,105,117,109,32,116,121,112,101,0,78,111,32,101,114,114,111,114,32,105,110,102,111,114,109,97,116,105,111,110,0,0],"i8",ALLOC_NONE,Runtime.GLOBAL_BASE);var tempDoublePtr=STATICTOP;STATICTOP+=16;function _atexit(h,p){__ATEXIT__.unshift({func:h,arg:p})}function ___cxa_atexit(){return _atexit.apply(null,arguments)}function _abort(){Module.abort()}function __ZN8facebook4yoga14YGNodeToStringEPNSt3__212basic_stringIcNS1_11char_traitsIcEENS1_9allocatorIcEEEEP6YGNode14YGPrintOptionsj(){Module.printErr("missing function: _ZN8facebook4yoga14YGNodeToStringEPNSt3__212basic_stringIcNS1_11char_traitsIcEENS1_9allocatorIcEEEEP6YGNode14YGPrintOptionsj"),abort(-1)}function __decorate(h,p,y,w){var T=arguments.length,e=T<3?p:w===null?w=Object.getOwnPropertyDescriptor(p,y):w,B;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")e=Reflect.decorate(h,p,y,w);else for(var N=h.length-1;N>=0;N--)(B=h[N])&&(e=(T<3?B(e):T>3?B(p,y,e):B(p,y))||e);return T>3&&e&&Object.defineProperty(p,y,e),e}function _defineHidden(h){return function(p,y){Object.defineProperty(p,y,{configurable:!1,enumerable:!1,value:h,writable:!0})}}var _nbind={};function __nbind_free_external(h){_nbind.externalList[h].dereference(h)}function __nbind_reference_external(h){_nbind.externalList[h].reference()}function _llvm_stackrestore(h){var p=_llvm_stacksave,y=p.LLVM_SAVEDSTACKS[h];p.LLVM_SAVEDSTACKS.splice(h,1),Runtime.stackRestore(y)}function __nbind_register_pool(h,p,y,w){_nbind.Pool.pageSize=h,_nbind.Pool.usedPtr=p/4,_nbind.Pool.rootPtr=y,_nbind.Pool.pagePtr=w/4,HEAP32[p/4]=16909060,HEAP8[p]==1&&(_nbind.bigEndian=!0),HEAP32[p/4]=0,_nbind.makeTypeKindTbl=(e={},e[1024]=_nbind.PrimitiveType,e[64]=_nbind.Int64Type,e[2048]=_nbind.BindClass,e[3072]=_nbind.BindClassPtr,e[4096]=_nbind.SharedClassPtr,e[5120]=_nbind.ArrayType,e[6144]=_nbind.ArrayType,e[7168]=_nbind.CStringType,e[9216]=_nbind.CallbackType,e[10240]=_nbind.BindType,e),_nbind.makeTypeNameTbl={Buffer:_nbind.BufferType,External:_nbind.ExternalType,Int64:_nbind.Int64Type,_nbind_new:_nbind.CreateValueType,bool:_nbind.BooleanType,"cbFunction &":_nbind.CallbackType,"const cbFunction &":_nbind.CallbackType,"const std::string &":_nbind.StringType,"std::string":_nbind.StringType},Module.toggleLightGC=_nbind.toggleLightGC,_nbind.callUpcast=Module.dynCall_ii;var T=_nbind.makeType(_nbind.constructType,{flags:2048,id:0,name:""});T.proto=Module,_nbind.BindClass.list.push(T);var e}function _emscripten_set_main_loop_timing(h,p){if(Browser.mainLoop.timingMode=h,Browser.mainLoop.timingValue=p,!Browser.mainLoop.func)return 1;if(h==0)Browser.mainLoop.scheduler=function(){var B=Math.max(0,Browser.mainLoop.tickStartTime+p-_emscripten_get_now())|0;setTimeout(Browser.mainLoop.runner,B)},Browser.mainLoop.method="timeout";else if(h==1)Browser.mainLoop.scheduler=function(){Browser.requestAnimationFrame(Browser.mainLoop.runner)},Browser.mainLoop.method="rAF";else if(h==2){if(!window.setImmediate){let e=function(B){B.source===window&&B.data===w&&(B.stopPropagation(),y.shift()())};var T=e,y=[],w="setimmediate";window.addEventListener("message",e,!0),window.setImmediate=function(N){y.push(N),ENVIRONMENT_IS_WORKER?(Module.setImmediates===void 0&&(Module.setImmediates=[]),Module.setImmediates.push(N),window.postMessage({target:w})):window.postMessage(w,"*")}}Browser.mainLoop.scheduler=function(){window.setImmediate(Browser.mainLoop.runner)},Browser.mainLoop.method="immediate"}return 0}function _emscripten_get_now(){abort()}function _emscripten_set_main_loop(h,p,y,w,T){Module.noExitRuntime=!0,assert(!Browser.mainLoop.func,"emscripten_set_main_loop: there can only be one main loop function at once: call emscripten_cancel_main_loop to cancel the previous one before setting a new one with different parameters."),Browser.mainLoop.func=h,Browser.mainLoop.arg=w;var e;typeof w!="undefined"?e=function(){Module.dynCall_vi(h,w)}:e=function(){Module.dynCall_v(h)};var B=Browser.mainLoop.currentlyRunningMainloop;if(Browser.mainLoop.runner=function(){if(!ABORT){if(Browser.mainLoop.queue.length>0){var F=Date.now(),k=Browser.mainLoop.queue.shift();if(k.func(k.arg),Browser.mainLoop.remainingBlockers){var O=Browser.mainLoop.remainingBlockers,H=O%1==0?O-1:Math.floor(O);k.counted?Browser.mainLoop.remainingBlockers=H:(H=H+.5,Browser.mainLoop.remainingBlockers=(8*O+H)/9)}if(console.log('main loop blocker "'+k.name+'" took '+(Date.now()-F)+" ms"),Browser.mainLoop.updateStatus(),B<Browser.mainLoop.currentlyRunningMainloop)return;setTimeout(Browser.mainLoop.runner,0);return}if(!(B<Browser.mainLoop.currentlyRunningMainloop)){if(Browser.mainLoop.currentFrameNumber=Browser.mainLoop.currentFrameNumber+1|0,Browser.mainLoop.timingMode==1&&Browser.mainLoop.timingValue>1&&Browser.mainLoop.currentFrameNumber%Browser.mainLoop.timingValue!=0){Browser.mainLoop.scheduler();return}else Browser.mainLoop.timingMode==0&&(Browser.mainLoop.tickStartTime=_emscripten_get_now());Browser.mainLoop.method==="timeout"&&Module.ctx&&(Module.printErr("Looks like you are rendering without using requestAnimationFrame for the main loop. You should use 0 for the frame rate in emscripten_set_main_loop in order to use requestAnimationFrame, as that can greatly improve your frame rates!"),Browser.mainLoop.method=""),Browser.mainLoop.runIter(e),!(B<Browser.mainLoop.currentlyRunningMainloop)&&(typeof SDL=="object"&&SDL.audio&&SDL.audio.queueNewAudioData&&SDL.audio.queueNewAudioData(),Browser.mainLoop.scheduler())}}},T||(p&&p>0?_emscripten_set_main_loop_timing(0,1e3/p):_emscripten_set_main_loop_timing(1,1),Browser.mainLoop.scheduler()),y)throw"SimulateInfiniteLoop"}var Browser={mainLoop:{scheduler:null,method:"",currentlyRunningMainloop:0,func:null,arg:0,timingMode:0,timingValue:0,currentFrameNumber:0,queue:[],pause:function(){Browser.mainLoop.scheduler=null,Browser.mainLoop.currentlyRunningMainloop++},resume:function(){Browser.mainLoop.currentlyRunningMainloop++;var h=Browser.mainLoop.timingMode,p=Browser.mainLoop.timingValue,y=Browser.mainLoop.func;Browser.mainLoop.func=null,_emscripten_set_main_loop(y,0,!1,Browser.mainLoop.arg,!0),_emscripten_set_main_loop_timing(h,p),Browser.mainLoop.scheduler()},updateStatus:function(){if(Module.setStatus){var h=Module.statusMessage||"Please wait...",p=Browser.mainLoop.remainingBlockers,y=Browser.mainLoop.expectedBlockers;p?p<y?Module.setStatus(h+" ("+(y-p)+"/"+y+")"):Module.setStatus(h):Module.setStatus("")}},runIter:function(h){if(!ABORT){if(Module.preMainLoop){var p=Module.preMainLoop();if(p===!1)return}try{h()}catch(y){if(y instanceof ExitStatus)return;throw y&&typeof y=="object"&&y.stack&&Module.printErr("exception thrown: "+[y,y.stack]),y}Module.postMainLoop&&Module.postMainLoop()}}},isFullscreen:!1,pointerLock:!1,moduleContextCreatedCallbacks:[],workers:[],init:function(){if(Module.preloadPlugins||(Module.preloadPlugins=[]),Browser.initted)return;Browser.initted=!0;try{new Blob,Browser.hasBlobConstructor=!0}catch(T){Browser.hasBlobConstructor=!1,console.log("warning: no blob constructor, cannot create blobs with mimetypes")}Browser.BlobBuilder=typeof MozBlobBuilder!="undefined"?MozBlobBuilder:typeof WebKitBlobBuilder!="undefined"?WebKitBlobBuilder:Browser.hasBlobConstructor?null:console.log("warning: no BlobBuilder"),Browser.URLObject=typeof window!="undefined"?window.URL?window.URL:window.webkitURL:void 0,!Module.noImageDecoding&&typeof Browser.URLObject=="undefined"&&(console.log("warning: Browser does not support creating object URLs. Built-in browser image decoding will not be available."),Module.noImageDecoding=!0);var h={};h.canHandle=function(e){return!Module.noImageDecoding&&/\.(jpg|jpeg|png|bmp)$/i.test(e)},h.handle=function(e,B,N,F){var k=null;if(Browser.hasBlobConstructor)try{k=new Blob([e],{type:Browser.getMimetype(B)}),k.size!==e.length&&(k=new Blob([new Uint8Array(e).buffer],{type:Browser.getMimetype(B)}))}catch(i0){Runtime.warnOnce("Blob constructor present but fails: "+i0+"; falling back to blob builder")}if(!k){var O=new Browser.BlobBuilder;O.append(new Uint8Array(e).buffer),k=O.getBlob()}var H=Browser.URLObject.createObjectURL(k),D=new Image;D.onload=function(){assert(D.complete,"Image "+B+" could not be decoded");var s=document.createElement("canvas");s.width=D.width,s.height=D.height;var g0=s.getContext("2d");g0.drawImage(D,0,0),Module.preloadedImages[B]=s,Browser.URLObject.revokeObjectURL(H),N&&N(e)},D.onerror=function(s){console.log("Image "+H+" could not be decoded"),F&&F()},D.src=H},Module.preloadPlugins.push(h);var p={};p.canHandle=function(e){return!Module.noAudioDecoding&&e.substr(-4)in{".ogg":1,".wav":1,".mp3":1}},p.handle=function(e,B,N,F){var k=!1;function O(g0){k||(k=!0,Module.preloadedAudios[B]=g0,N&&N(e))}function H(){k||(k=!0,Module.preloadedAudios[B]=new Audio,F&&F())}if(Browser.hasBlobConstructor){try{var D=new Blob([e],{type:Browser.getMimetype(B)})}catch(g0){return H()}var i0=Browser.URLObject.createObjectURL(D),s=new Audio;s.addEventListener("canplaythrough",function(){O(s)},!1),s.onerror=function(L0){if(k)return;console.log("warning: browser could not fully decode audio "+B+", trying slower base64 approach");function V(S0){for(var $0="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",M2="=",I2="",y0=0,G0=0,Q0=0;Q0<S0.length;Q0++)for(y0=y0<<8|S0[Q0],G0+=8;G0>=6;){var en=y0>>G0-6&63;G0-=6,I2+=$0[en]}return G0==2?(I2+=$0[(y0&3)<<4],I2+=M2+M2):G0==4&&(I2+=$0[(y0&15)<<2],I2+=M2),I2}s.src="data:audio/x-"+B.substr(-3)+";base64,"+V(e),O(s)},s.src=i0,Browser.safeSetTimeout(function(){O(s)},1e4)}else return H()},Module.preloadPlugins.push(p);function y(){Browser.pointerLock=document.pointerLockElement===Module.canvas||document.mozPointerLockElement===Module.canvas||document.webkitPointerLockElement===Module.canvas||document.msPointerLockElement===Module.canvas}var w=Module.canvas;w&&(w.requestPointerLock=w.requestPointerLock||w.mozRequestPointerLock||w.webkitRequestPointerLock||w.msRequestPointerLock||function(){},w.exitPointerLock=document.exitPointerLock||document.mozExitPointerLock||document.webkitExitPointerLock||document.msExitPointerLock||function(){},w.exitPointerLock=w.exitPointerLock.bind(document),document.addEventListener("pointerlockchange",y,!1),document.addEventListener("mozpointerlockchange",y,!1),document.addEventListener("webkitpointerlockchange",y,!1),document.addEventListener("mspointerlockchange",y,!1),Module.elementPointerLock&&w.addEventListener("click",function(T){!Browser.pointerLock&&Module.canvas.requestPointerLock&&(Module.canvas.requestPointerLock(),T.preventDefault())},!1))},createContext:function(h,p,y,w){if(p&&Module.ctx&&h==Module.canvas)return Module.ctx;var T,e;if(p){var B={antialias:!1,alpha:!1};if(w)for(var N in w)B[N]=w[N];e=GL.createContext(h,B),e&&(T=GL.getContext(e).GLctx)}else T=h.getContext("2d");return T?(y&&(p||assert(typeof GLctx=="undefined","cannot set in module if GLctx is used, but we are a non-GL context that would replace it"),Module.ctx=T,p&&GL.makeContextCurrent(e),Module.useWebGL=p,Browser.moduleContextCreatedCallbacks.forEach(function(F){F()}),Browser.init()),T):null},destroyContext:function(h,p,y){},fullscreenHandlersInstalled:!1,lockPointer:void 0,resizeCanvas:void 0,requestFullscreen:function(h,p,y){Browser.lockPointer=h,Browser.resizeCanvas=p,Browser.vrDevice=y,typeof Browser.lockPointer=="undefined"&&(Browser.lockPointer=!0),typeof Browser.resizeCanvas=="undefined"&&(Browser.resizeCanvas=!1),typeof Browser.vrDevice=="undefined"&&(Browser.vrDevice=null);var w=Module.canvas;function T(){Browser.isFullscreen=!1;var B=w.parentNode;(document.fullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||document.webkitFullscreenElement||document.webkitCurrentFullScreenElement)===B?(w.exitFullscreen=document.exitFullscreen||document.cancelFullScreen||document.mozCancelFullScreen||document.msExitFullscreen||document.webkitCancelFullScreen||function(){},w.exitFullscreen=w.exitFullscreen.bind(document),Browser.lockPointer&&w.requestPointerLock(),Browser.isFullscreen=!0,Browser.resizeCanvas&&Browser.setFullscreenCanvasSize()):(B.parentNode.insertBefore(w,B),B.parentNode.removeChild(B),Browser.resizeCanvas&&Browser.setWindowedCanvasSize()),Module.onFullScreen&&Module.onFullScreen(Browser.isFullscreen),Module.onFullscreen&&Module.onFullscreen(Browser.isFullscreen),Browser.updateCanvasDimensions(w)}Browser.fullscreenHandlersInstalled||(Browser.fullscreenHandlersInstalled=!0,document.addEventListener("fullscreenchange",T,!1),document.addEventListener("mozfullscreenchange",T,!1),document.addEventListener("webkitfullscreenchange",T,!1),document.addEventListener("MSFullscreenChange",T,!1));var e=document.createElement("div");w.parentNode.insertBefore(e,w),e.appendChild(w),e.requestFullscreen=e.requestFullscreen||e.mozRequestFullScreen||e.msRequestFullscreen||(e.webkitRequestFullscreen?function(){e.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT)}:null)||(e.webkitRequestFullScreen?function(){e.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT)}:null),y?e.requestFullscreen({vrDisplay:y}):e.requestFullscreen()},requestFullScreen:function(h,p,y){return Module.printErr("Browser.requestFullScreen() is deprecated. Please call Browser.requestFullscreen instead."),Browser.requestFullScreen=function(w,T,e){return Browser.requestFullscreen(w,T,e)},Browser.requestFullscreen(h,p,y)},nextRAF:0,fakeRequestAnimationFrame:function(h){var p=Date.now();if(Browser.nextRAF===0)Browser.nextRAF=p+1e3/60;else for(;p+2>=Browser.nextRAF;)Browser.nextRAF+=1e3/60;var y=Math.max(Browser.nextRAF-p,0);setTimeout(h,y)},requestAnimationFrame:function h(p){typeof window=="undefined"?Browser.fakeRequestAnimationFrame(p):(window.requestAnimationFrame||(window.requestAnimationFrame=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame||window.oRequestAnimationFrame||Browser.fakeRequestAnimationFrame),window.requestAnimationFrame(p))},safeCallback:function(h){return function(){if(!ABORT)return h.apply(null,arguments)}},allowAsyncCallbacks:!0,queuedAsyncCallbacks:[],pauseAsyncCallbacks:function(){Browser.allowAsyncCallbacks=!1},resumeAsyncCallbacks:function(){if(Browser.allowAsyncCallbacks=!0,Browser.queuedAsyncCallbacks.length>0){var h=Browser.queuedAsyncCallbacks;Browser.queuedAsyncCallbacks=[],h.forEach(function(p){p()})}},safeRequestAnimationFrame:function(h){return Browser.requestAnimationFrame(function(){ABORT||(Browser.allowAsyncCallbacks?h():Browser.queuedAsyncCallbacks.push(h))})},safeSetTimeout:function(h,p){return Module.noExitRuntime=!0,setTimeout(function(){ABORT||(Browser.allowAsyncCallbacks?h():Browser.queuedAsyncCallbacks.push(h))},p)},safeSetInterval:function(h,p){return Module.noExitRuntime=!0,setInterval(function(){ABORT||Browser.allowAsyncCallbacks&&h()},p)},getMimetype:function(h){return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",bmp:"image/bmp",ogg:"audio/ogg",wav:"audio/wav",mp3:"audio/mpeg"}[h.substr(h.lastIndexOf(".")+1)]},getUserMedia:function(h){window.getUserMedia||(window.getUserMedia=navigator.getUserMedia||navigator.mozGetUserMedia),window.getUserMedia(h)},getMovementX:function(h){return h.movementX||h.mozMovementX||h.webkitMovementX||0},getMovementY:function(h){return h.movementY||h.mozMovementY||h.webkitMovementY||0},getMouseWheelDelta:function(h){var p=0;switch(h.type){case"DOMMouseScroll":p=h.detail;break;case"mousewheel":p=h.wheelDelta;break;case"wheel":p=h.deltaY;break;default:throw"unrecognized mouse wheel event: "+h.type}return p},mouseX:0,mouseY:0,mouseMovementX:0,mouseMovementY:0,touches:{},lastTouches:{},calculateMouseEvent:function(h){if(Browser.pointerLock)h.type!="mousemove"&&"mozMovementX"in h?Browser.mouseMovementX=Browser.mouseMovementY=0:(Browser.mouseMovementX=Browser.getMovementX(h),Browser.mouseMovementY=Browser.getMovementY(h)),typeof SDL!="undefined"?(Browser.mouseX=SDL.mouseX+Browser.mouseMovementX,Browser.mouseY=SDL.mouseY+Browser.mouseMovementY):(Browser.mouseX+=Browser.mouseMovementX,Browser.mouseY+=Browser.mouseMovementY);else{var p=Module.canvas.getBoundingClientRect(),y=Module.canvas.width,w=Module.canvas.height,T=typeof window.scrollX!="undefined"?window.scrollX:window.pageXOffset,e=typeof window.scrollY!="undefined"?window.scrollY:window.pageYOffset;if(h.type==="touchstart"||h.type==="touchend"||h.type==="touchmove"){var B=h.touch;if(B===void 0)return;var N=B.pageX-(T+p.left),F=B.pageY-(e+p.top);N=N*(y/p.width),F=F*(w/p.height);var k={x:N,y:F};if(h.type==="touchstart")Browser.lastTouches[B.identifier]=k,Browser.touches[B.identifier]=k;else if(h.type==="touchend"||h.type==="touchmove"){var O=Browser.touches[B.identifier];O||(O=k),Browser.lastTouches[B.identifier]=O,Browser.touches[B.identifier]=k}return}var H=h.pageX-(T+p.left),D=h.pageY-(e+p.top);H=H*(y/p.width),D=D*(w/p.height),Browser.mouseMovementX=H-Browser.mouseX,Browser.mouseMovementY=D-Browser.mouseY,Browser.mouseX=H,Browser.mouseY=D}},asyncLoad:function(h,p,y,w){var T=w?"":"al "+h;Module.readAsync(h,function(e){assert(e,'Loading data file "'+h+'" failed (no arrayBuffer).'),p(new Uint8Array(e)),T&&removeRunDependency(T)},function(e){if(y)y();else throw'Loading data file "'+h+'" failed.'}),T&&addRunDependency(T)},resizeListeners:[],updateResizeListeners:function(){var h=Module.canvas;Browser.resizeListeners.forEach(function(p){p(h.width,h.height)})},setCanvasSize:function(h,p,y){var w=Module.canvas;Browser.updateCanvasDimensions(w,h,p),y||Browser.updateResizeListeners()},windowedWidth:0,windowedHeight:0,setFullscreenCanvasSize:function(){if(typeof SDL!="undefined"){var h=HEAPU32[SDL.screen+Runtime.QUANTUM_SIZE*0>>2];h=h|8388608,HEAP32[SDL.screen+Runtime.QUANTUM_SIZE*0>>2]=h}Browser.updateResizeListeners()},setWindowedCanvasSize:function(){if(typeof SDL!="undefined"){var h=HEAPU32[SDL.screen+Runtime.QUANTUM_SIZE*0>>2];h=h&-8388609,HEAP32[SDL.screen+Runtime.QUANTUM_SIZE*0>>2]=h}Browser.updateResizeListeners()},updateCanvasDimensions:function(h,p,y){p&&y?(h.widthNative=p,h.heightNative=y):(p=h.widthNative,y=h.heightNative);var w=p,T=y;if(Module.forcedAspectRatio&&Module.forcedAspectRatio>0&&(w/T<Module.forcedAspectRatio?w=Math.round(T*Module.forcedAspectRatio):T=Math.round(w/Module.forcedAspectRatio)),(document.fullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||document.webkitFullscreenElement||document.webkitCurrentFullScreenElement)===h.parentNode&&typeof screen!="undefined"){var e=Math.min(screen.width/w,screen.height/T);w=Math.round(w*e),T=Math.round(T*e)}Browser.resizeCanvas?(h.width!=w&&(h.width=w),h.height!=T&&(h.height=T),typeof h.style!="undefined"&&(h.style.removeProperty("width"),h.style.removeProperty("height"))):(h.width!=p&&(h.width=p),h.height!=y&&(h.height=y),typeof h.style!="undefined"&&(w!=p||T!=y?(h.style.setProperty("width",w+"px","important"),h.style.setProperty("height",T+"px","important")):(h.style.removeProperty("width"),h.style.removeProperty("height"))))},wgetRequests:{},nextWgetRequestHandle:0,getNextWgetRequestHandle:function(){var h=Browser.nextWgetRequestHandle;return Browser.nextWgetRequestHandle++,h}},SYSCALLS={varargs:0,get:function(h){SYSCALLS.varargs+=4;var p=HEAP32[SYSCALLS.varargs-4>>2];return p},getStr:function(){var h=Pointer_stringify(SYSCALLS.get());return h},get64:function(){var h=SYSCALLS.get(),p=SYSCALLS.get();return h>=0?assert(p===0):assert(p===-1),h},getZero:function(){assert(SYSCALLS.get()===0)}};function ___syscall6(h,p){SYSCALLS.varargs=p;try{var y=SYSCALLS.getStreamFromFD();return FS.close(y),0}catch(w){return(typeof FS=="undefined"||!(w instanceof FS.ErrnoError))&&abort(w),-w.errno}}function ___syscall54(h,p){SYSCALLS.varargs=p;try{return 0}catch(y){return(typeof FS=="undefined"||!(y instanceof FS.ErrnoError))&&abort(y),-y.errno}}function _typeModule(h){var p=[[0,1,"X"],[1,1,"const X"],[128,1,"X *"],[256,1,"X &"],[384,1,"X &&"],[512,1,"std::shared_ptr<X>"],[640,1,"std::unique_ptr<X>"],[5120,1,"std::vector<X>"],[6144,2,"std::array<X, Y>"],[9216,-1,"std::function<X (Y)>"]];function y(F,k,O,H,D,i0){if(k==1){var s=H&896;(s==128||s==256||s==384)&&(F="X const")}var g0;return i0?g0=O.replace("X",F).replace("Y",D):g0=F.replace("X",O).replace("Y",D),g0.replace(/([*&]) (?=[*&])/g,"$1")}function w(F,k,O,H,D){throw new Error(F+" type "+O.replace("X",k+"?")+(H?" with flag "+H:"")+" in "+D)}function T(F,k,O,H,D,i0,s,g0){i0===void 0&&(i0="X"),g0===void 0&&(g0=1);var L0=O(F);if(L0)return L0;var V=H(F),S0=V.placeholderFlag,$0=p[S0];s&&$0&&(i0=y(s[2],s[0],i0,$0[0],"?",!0));var M2;S0==0&&(M2="Unbound"),S0>=10&&(M2="Corrupt"),g0>20&&(M2="Deeply nested"),M2&&w(M2,F,i0,S0,D||"?");var I2=V.paramList[0],y0=T(I2,k,O,H,D,i0,$0,g0+1),G0,Q0={flags:$0[0],id:F,name:"",paramList:[y0]},en=[],hn="?";switch(V.placeholderFlag){case 1:G0=y0.spec;break;case 2:if((y0.flags&15360)==1024&&y0.spec.ptrSize==1){Q0.flags=7168;break}case 3:case 6:case 5:G0=y0.spec,y0.flags&15360;break;case 8:hn=""+V.paramList[1],Q0.paramList.push(V.paramList[1]);break;case 9:for(var dn=0,xn=V.paramList[1];dn<xn.length;dn++){var v0=xn[dn],Dn=T(v0,k,O,H,D,i0,$0,g0+1);en.push(Dn.name),Q0.paramList.push(Dn)}hn=en.join(", ");break;default:break}if(Q0.name=y($0[2],$0[0],y0.name,y0.flags,hn),G0){for(var H1=0,Hn=Object.keys(G0);H1<Hn.length;H1++){var mn=Hn[H1];Q0[mn]=Q0[mn]||G0[mn]}Q0.flags|=G0.flags}return e(k,Q0)}function e(F,k){var O=k.flags,H=O&896,D=O&15360;return!k.name&&D==1024&&(k.ptrSize==1?k.name=(O&16?"":(O&8?"un":"")+"signed ")+"char":k.name=(O&8?"u":"")+(O&32?"float":"int")+(k.ptrSize*8+"_t")),k.ptrSize==8&&!(O&32)&&(D=64),D==2048&&(H==512||H==640?D=4096:H&&(D=3072)),F(D,k)}var B=function(){function F(k){this.id=k.id,this.name=k.name,this.flags=k.flags,this.spec=k}return F.prototype.toString=function(){return this.name},F}(),N={Type:B,getComplexType:T,makeType:e,structureList:p};return h.output=N,h.output||N}function __nbind_register_type(h,p){var y=_nbind.readAsciiString(p),w={flags:10240,id:h,name:y};_nbind.makeType(_nbind.constructType,w)}function __nbind_register_callback_signature(h,p){var y=_nbind.readTypeIdList(h,p),w=_nbind.callbackSignatureList.length;return _nbind.callbackSignatureList[w]=_nbind.makeJSCaller(y),w}function __extends(h,p){for(var y in p)p.hasOwnProperty(y)&&(h[y]=p[y]);function w(){this.constructor=h}w.prototype=p.prototype,h.prototype=new w}function __nbind_register_class(h,p,y,w,T,e,B){var N=_nbind.readAsciiString(B),F=_nbind.readPolicyList(p),k=HEAPU32.subarray(h/4,h/4+2),O={flags:2048|(F.Value?2:0),id:k[0],name:N},H=_nbind.makeType(_nbind.constructType,O);H.ptrType=_nbind.getComplexType(k[1],_nbind.constructType,_nbind.getType,_nbind.queryType),H.destroy=_nbind.makeMethodCaller(H.ptrType,{boundID:O.id,flags:0,name:"destroy",num:0,ptr:e,title:H.name+".free",typeList:["void","uint32_t","uint32_t"]}),T&&(H.superIdList=Array.prototype.slice.call(HEAPU32.subarray(y/4,y/4+T)),H.upcastList=Array.prototype.slice.call(HEAPU32.subarray(w/4,w/4+T))),Module[H.name]=H.makeBound(F),_nbind.BindClass.list.push(H)}function _removeAccessorPrefix(h){var p=/^[Gg]et_?([A-Z]?([A-Z]?))/;return h.replace(p,function(y,w,T){return T?w:w.toLowerCase()})}function __nbind_register_function(h,p,y,w,T,e,B,N,F,k){var O=_nbind.getType(h),H=_nbind.readPolicyList(p),D=_nbind.readTypeIdList(y,w),i0;if(B==5)i0=[{direct:T,name:"__nbindConstructor",ptr:0,title:O.name+" constructor",typeList:["uint32_t"].concat(D.slice(1))},{direct:e,name:"__nbindValueConstructor",ptr:0,title:O.name+" value constructor",typeList:["void","uint32_t"].concat(D.slice(1))}];else{var s=_nbind.readAsciiString(N),g0=(O.name&&O.name+".")+s;(B==3||B==4)&&(s=_removeAccessorPrefix(s)),i0=[{boundID:h,direct:e,name:s,ptr:T,title:g0,typeList:D}]}for(var L0=0,V=i0;L0<V.length;L0++){var S0=V[L0];S0.signatureType=B,S0.policyTbl=H,S0.num=F,S0.flags=k,O.addMethod(S0)}}function _nbind_value(h,p){_nbind.typeNameTbl[h]||_nbind.throwError("Unknown value type "+h),Module.NBind.bind_value(h,p),_defineHidden(_nbind.typeNameTbl[h].proto.prototype.__nbindValueConstructor)(p.prototype,"__nbindValueConstructor")}Module._nbind_value=_nbind_value;function __nbind_get_value_object(h,p){var y=_nbind.popValue(h);if(!y.fromJS)throw new Error("Object "+y+" has no fromJS function");y.fromJS(function(){y.__nbindValueConstructor.apply(this,Array.prototype.concat.apply([p],arguments))})}function _emscripten_memcpy_big(h,p,y){return HEAPU8.set(HEAPU8.subarray(p,p+y),h),h}function __nbind_register_primitive(h,p,y){var w={flags:1024|y,id:h,ptrSize:p};_nbind.makeType(_nbind.constructType,w)}var cttz_i8=allocate([8,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,6,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,7,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,6,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,5,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0,4,0,1,0,2,0,1,0,3,0,1,0,2,0,1,0],"i8",ALLOC_STATIC);function ___setErrNo(h){return Module.___errno_location&&(HEAP32[Module.___errno_location()>>2]=h),h}function _llvm_stacksave(){var h=_llvm_stacksave;return h.LLVM_SAVEDSTACKS||(h.LLVM_SAVEDSTACKS=[]),h.LLVM_SAVEDSTACKS.push(Runtime.stackSave()),h.LLVM_SAVEDSTACKS.length-1}function ___syscall140(h,p){SYSCALLS.varargs=p;try{var y=SYSCALLS.getStreamFromFD(),w=SYSCALLS.get(),T=SYSCALLS.get(),e=SYSCALLS.get(),B=SYSCALLS.get(),N=T;return FS.llseek(y,N,B),HEAP32[e>>2]=y.position,y.getdents&&N===0&&B===0&&(y.getdents=null),0}catch(F){return(typeof FS=="undefined"||!(F instanceof FS.ErrnoError))&&abort(F),-F.errno}}function ___syscall146(h,p){SYSCALLS.varargs=p;try{var y=SYSCALLS.get(),w=SYSCALLS.get(),T=SYSCALLS.get(),e=0;___syscall146.buffer||(___syscall146.buffers=[null,[],[]],___syscall146.printChar=function(O,H){var D=___syscall146.buffers[O];assert(D),H===0||H===10?((O===1?Module.print:Module.printErr)(UTF8ArrayToString(D,0)),D.length=0):D.push(H)});for(var B=0;B<T;B++){for(var N=HEAP32[w+B*8>>2],F=HEAP32[w+(B*8+4)>>2],k=0;k<F;k++)___syscall146.printChar(y,HEAPU8[N+k]);e+=F}return e}catch(O){return(typeof FS=="undefined"||!(O instanceof FS.ErrnoError))&&abort(O),-O.errno}}function __nbind_finish(){for(var h=0,p=_nbind.BindClass.list;h<p.length;h++){var y=p[h];y.finish()}}var ___dso_handle=STATICTOP;STATICTOP+=16,function(_nbind){var typeIdTbl={};_nbind.typeNameTbl={};var Pool=function(){function h(){}return h.lalloc=function(p){p=p+7&-8;var y=HEAPU32[h.usedPtr];if(p>h.pageSize/2||p>h.pageSize-y){var w=_nbind.typeNameTbl.NBind.proto;return w.lalloc(p)}else return HEAPU32[h.usedPtr]=y+p,h.rootPtr+y},h.lreset=function(p,y){var w=HEAPU32[h.pagePtr];if(w){var T=_nbind.typeNameTbl.NBind.proto;T.lreset(p,y)}else HEAPU32[h.usedPtr]=p},h}();_nbind.Pool=Pool;function constructType(h,p){var y=h==10240?_nbind.makeTypeNameTbl[p.name]||_nbind.BindType:_nbind.makeTypeKindTbl[h],w=new y(p);return typeIdTbl[p.id]=w,_nbind.typeNameTbl[p.name]=w,w}_nbind.constructType=constructType;function getType(h){return typeIdTbl[h]}_nbind.getType=getType;function queryType(h){var p=HEAPU8[h],y=_nbind.structureList[p][1];h/=4,y<0&&(++h,y=HEAPU32[h]+1);var w=Array.prototype.slice.call(HEAPU32.subarray(h+1,h+1+y));return p==9&&(w=[w[0],w.slice(1)]),{paramList:w,placeholderFlag:p}}_nbind.queryType=queryType;function getTypes(h,p){return h.map(function(y){return typeof y=="number"?_nbind.getComplexType(y,constructType,getType,queryType,p):_nbind.typeNameTbl[y]})}_nbind.getTypes=getTypes;function readTypeIdList(h,p){return Array.prototype.slice.call(HEAPU32,h/4,h/4+p)}_nbind.readTypeIdList=readTypeIdList;function readAsciiString(h){for(var p=h;HEAPU8[p++];);return String.fromCharCode.apply("",HEAPU8.subarray(h,p-1))}_nbind.readAsciiString=readAsciiString;function readPolicyList(h){var p={};if(h)for(;;){var y=HEAPU32[h/4];if(!y)break;p[readAsciiString(y)]=!0,h+=4}return p}_nbind.readPolicyList=readPolicyList;function getDynCall(h,p){var y={float32_t:"d",float64_t:"d",int64_t:"d",uint64_t:"d",void:"v"},w=h.map(function(e){return y[e.name]||"i"}).join(""),T=Module["dynCall_"+w];if(!T)throw new Error("dynCall_"+w+" not found for "+p+"("+h.map(function(e){return e.name}).join(", ")+")");return T}_nbind.getDynCall=getDynCall;function addMethod(h,p,y,w){var T=h[p];h.hasOwnProperty(p)&&T?((T.arity||T.arity===0)&&(T=_nbind.makeOverloader(T,T.arity),h[p]=T),T.addMethod(y,w)):(y.arity=w,h[p]=y)}_nbind.addMethod=addMethod;function throwError(h){throw new Error(h)}_nbind.throwError=throwError,_nbind.bigEndian=!1,_a=_typeModule(_typeModule),_nbind.Type=_a.Type,_nbind.makeType=_a.makeType,_nbind.getComplexType=_a.getComplexType,_nbind.structureList=_a.structureList;var BindType=function(h){__extends(p,h);function p(){var y=h!==null&&h.apply(this,arguments)||this;return y.heap=HEAPU32,y.ptrSize=4,y}return p.prototype.needsWireRead=function(y){return!!this.wireRead||!!this.makeWireRead},p.prototype.needsWireWrite=function(y){return!!this.wireWrite||!!this.makeWireWrite},p}(_nbind.Type);_nbind.BindType=BindType;var PrimitiveType=function(h){__extends(p,h);function p(y){var w=h.call(this,y)||this,T=y.flags&32?{32:HEAPF32,64:HEAPF64}:y.flags&8?{8:HEAPU8,16:HEAPU16,32:HEAPU32}:{8:HEAP8,16:HEAP16,32:HEAP32};return w.heap=T[y.ptrSize*8],w.ptrSize=y.ptrSize,w}return p.prototype.needsWireWrite=function(y){return!!y&&!!y.Strict},p.prototype.makeWireWrite=function(y,w){return w&&w.Strict&&function(T){if(typeof T=="number")return T;throw new Error("Type mismatch")}},p}(BindType);_nbind.PrimitiveType=PrimitiveType;function pushCString(h,p){if(h==null){if(p&&p.Nullable)return 0;throw new Error("Type mismatch")}if(p&&p.Strict){if(typeof h!="string")throw new Error("Type mismatch")}else h=h.toString();var y=Module.lengthBytesUTF8(h)+1,w=_nbind.Pool.lalloc(y);return Module.stringToUTF8Array(h,HEAPU8,w,y),w}_nbind.pushCString=pushCString;function popCString(h){return h===0?null:Module.Pointer_stringify(h)}_nbind.popCString=popCString;var CStringType=function(h){__extends(p,h);function p(){var y=h!==null&&h.apply(this,arguments)||this;return y.wireRead=popCString,y.wireWrite=pushCString,y.readResources=[_nbind.resources.pool],y.writeResources=[_nbind.resources.pool],y}return p.prototype.makeWireWrite=function(y,w){return function(T){return pushCString(T,w)}},p}(BindType);_nbind.CStringType=CStringType;var BooleanType=function(h){__extends(p,h);function p(){var y=h!==null&&h.apply(this,arguments)||this;return y.wireRead=function(w){return!!w},y}return p.prototype.needsWireWrite=function(y){return!!y&&!!y.Strict},p.prototype.makeWireRead=function(y){return"!!("+y+")"},p.prototype.makeWireWrite=function(y,w){return w&&w.Strict&&function(T){if(typeof T=="boolean")return T;throw new Error("Type mismatch")}||y},p}(BindType);_nbind.BooleanType=BooleanType;var Wrapper=function(){function h(){}return h.prototype.persist=function(){this.__nbindState|=1},h}();_nbind.Wrapper=Wrapper;function makeBound(h,p){var y=function(w){__extends(T,w);function T(e,B,N,F){var k=w.call(this)||this;if(!(k instanceof T))return new(Function.prototype.bind.apply(T,Array.prototype.concat.apply([null],arguments)));var O=B,H=N,D=F;if(e!==_nbind.ptrMarker){var i0=k.__nbindConstructor.apply(k,arguments);O=4608,D=HEAPU32[i0/4],H=HEAPU32[i0/4+1]}var s={configurable:!0,enumerable:!1,value:null,writable:!1},g0={__nbindFlags:O,__nbindPtr:H};D&&(g0.__nbindShared=D,_nbind.mark(k));for(var L0=0,V=Object.keys(g0);L0<V.length;L0++){var S0=V[L0];s.value=g0[S0],Object.defineProperty(k,S0,s)}return _defineHidden(0)(k,"__nbindState"),k}return T.prototype.free=function(){p.destroy.call(this,this.__nbindShared,this.__nbindFlags),this.__nbindState|=2,disableMember(this,"__nbindShared"),disableMember(this,"__nbindPtr")},T}(Wrapper);return __decorate([_defineHidden()],y.prototype,"__nbindConstructor",void 0),__decorate([_defineHidden()],y.prototype,"__nbindValueConstructor",void 0),__decorate([_defineHidden(h)],y.prototype,"__nbindPolicies",void 0),y}_nbind.makeBound=makeBound;function disableMember(h,p){function y(){throw new Error("Accessing deleted object")}Object.defineProperty(h,p,{configurable:!1,enumerable:!1,get:y,set:y})}_nbind.ptrMarker={};var BindClass=function(h){__extends(p,h);function p(y){var w=h.call(this,y)||this;return w.wireRead=function(T){return _nbind.popValue(T,w.ptrType)},w.wireWrite=function(T){return pushPointer(T,w.ptrType,!0)},w.pendingSuperCount=0,w.ready=!1,w.methodTbl={},y.paramList?(w.classType=y.paramList[0].classType,w.proto=w.classType.proto):w.classType=w,w}return p.prototype.makeBound=function(y){var w=_nbind.makeBound(y,this);return this.proto=w,this.ptrType.proto=w,w},p.prototype.addMethod=function(y){var w=this.methodTbl[y.name]||[];w.push(y),this.methodTbl[y.name]=w},p.prototype.registerMethods=function(y,w){for(var T,e=0,B=Object.keys(y.methodTbl);e<B.length;e++)for(var N=B[e],F=y.methodTbl[N],k=0,O=F;k<O.length;k++){var H=O[k],D=void 0,i0=void 0;if(D=this.proto.prototype,!(w&&H.signatureType!=1))switch(H.signatureType){case 1:D=this.proto;case 5:i0=_nbind.makeCaller(H),_nbind.addMethod(D,H.name,i0,H.typeList.length-1);break;case 4:T=_nbind.makeMethodCaller(y.ptrType,H);break;case 3:Object.defineProperty(D,H.name,{configurable:!0,enumerable:!1,get:_nbind.makeMethodCaller(y.ptrType,H),set:T});break;case 2:i0=_nbind.makeMethodCaller(y.ptrType,H),_nbind.addMethod(D,H.name,i0,H.typeList.length-1);break;default:break}}},p.prototype.registerSuperMethods=function(y,w,T){if(!T[y.name]){T[y.name]=!0;for(var e=0,B,N=0,F=y.superIdList||[];N<F.length;N++){var k=F[N],O=_nbind.getType(k);e++<w||w<0?B=-1:B=0,this.registerSuperMethods(O,B,T)}this.registerMethods(y,w<0)}},p.prototype.finish=function(){if(this.ready)return this;this.ready=!0,this.superList=(this.superIdList||[]).map(function(T){return _nbind.getType(T).finish()});var y=this.proto;if(this.superList.length){var w=function(){this.constructor=y};w.prototype=this.superList[0].proto.prototype,y.prototype=new w}return y!=Module&&(y.prototype.__nbindType=this),this.registerSuperMethods(this,1,{}),this},p.prototype.upcastStep=function(y,w){if(y==this)return w;for(var T=0;T<this.superList.length;++T){var e=this.superList[T].upcastStep(y,_nbind.callUpcast(this.upcastList[T],w));if(e)return e}return 0},p}(_nbind.BindType);BindClass.list=[],_nbind.BindClass=BindClass;function popPointer(h,p){return h?new p.proto(_nbind.ptrMarker,p.flags,h):null}_nbind.popPointer=popPointer;function pushPointer(h,p,y){if(!(h instanceof _nbind.Wrapper)){if(y)return _nbind.pushValue(h);throw new Error("Type mismatch")}var w=h.__nbindPtr,T=h.__nbindType.classType,e=p.classType;if(h instanceof p.proto)for(;T!=e;)w=_nbind.callUpcast(T.upcastList[0],w),T=T.superList[0];else if(w=T.upcastStep(e,w),!w)throw new Error("Type mismatch");return w}_nbind.pushPointer=pushPointer;function pushMutablePointer(h,p){var y=pushPointer(h,p);if(h.__nbindFlags&1)throw new Error("Passing a const value as a non-const argument");return y}var BindClassPtr=function(h){__extends(p,h);function p(y){var w=h.call(this,y)||this;w.classType=y.paramList[0].classType,w.proto=w.classType.proto;var T=y.flags&1,e=(w.flags&896)==256&&y.flags&2,B=T?pushPointer:pushMutablePointer,N=e?_nbind.popValue:popPointer;return w.makeWireWrite=function(F,k){return k.Nullable?function(O){return O?B(O,w):0}:function(O){return B(O,w)}},w.wireRead=function(F){return N(F,w)},w.wireWrite=function(F){return B(F,w)},w}return p}(_nbind.BindType);_nbind.BindClassPtr=BindClassPtr;function popShared(h,p){var y=HEAPU32[h/4],w=HEAPU32[h/4+1];return w?new p.proto(_nbind.ptrMarker,p.flags,w,y):null}_nbind.popShared=popShared;function pushShared(h,p){if(!(h instanceof p.proto))throw new Error("Type mismatch");return h.__nbindShared}function pushMutableShared(h,p){if(!(h instanceof p.proto))throw new Error("Type mismatch");if(h.__nbindFlags&1)throw new Error("Passing a const value as a non-const argument");return h.__nbindShared}var SharedClassPtr=function(h){__extends(p,h);function p(y){var w=h.call(this,y)||this;w.readResources=[_nbind.resources.pool],w.classType=y.paramList[0].classType,w.proto=w.classType.proto;var T=y.flags&1,e=T?pushShared:pushMutableShared;return w.wireRead=function(B){return popShared(B,w)},w.wireWrite=function(B){return e(B,w)},w}return p}(_nbind.BindType);_nbind.SharedClassPtr=SharedClassPtr,_nbind.externalList=[0];var firstFreeExternal=0,External=function(){function h(p){this.refCount=1,this.data=p}return h.prototype.register=function(){var p=firstFreeExternal;return p?firstFreeExternal=_nbind.externalList[p]:p=_nbind.externalList.length,_nbind.externalList[p]=this,p},h.prototype.reference=function(){++this.refCount},h.prototype.dereference=function(p){--this.refCount==0&&(this.free&&this.free(),_nbind.externalList[p]=firstFreeExternal,firstFreeExternal=p)},h}();_nbind.External=External;function popExternal(h){var p=_nbind.externalList[h];return p.dereference(h),p.data}function pushExternal(h){var p=new External(h);return p.reference(),p.register()}var ExternalType=function(h){__extends(p,h);function p(){var y=h!==null&&h.apply(this,arguments)||this;return y.wireRead=popExternal,y.wireWrite=pushExternal,y}return p}(_nbind.BindType);_nbind.ExternalType=ExternalType,_nbind.callbackSignatureList=[];var CallbackType=function(h){__extends(p,h);function p(){var y=h!==null&&h.apply(this,arguments)||this;return y.wireWrite=function(w){return typeof w!="function"&&_nbind.throwError("Type mismatch"),new _nbind.External(w).register()},y}return p}(_nbind.BindType);_nbind.CallbackType=CallbackType,_nbind.valueList=[0];var firstFreeValue=0;function pushValue(h){var p=firstFreeValue;return p?firstFreeValue=_nbind.valueList[p]:p=_nbind.valueList.length,_nbind.valueList[p]=h,p*2+1}_nbind.pushValue=pushValue;function popValue(h,p){if(h||_nbind.throwError("Value type JavaScript class is missing or not registered"),h&1){h>>=1;var y=_nbind.valueList[h];return _nbind.valueList[h]=firstFreeValue,firstFreeValue=h,y}else{if(p)return _nbind.popShared(h,p);throw new Error("Invalid value slot "+h)}}_nbind.popValue=popValue;var valueBase=18446744073709552e3;function push64(h){return typeof h=="number"?h:pushValue(h)*4096+valueBase}function pop64(h){return h<valueBase?h:popValue((h-valueBase)/4096)}var CreateValueType=function(h){__extends(p,h);function p(){return h!==null&&h.apply(this,arguments)||this}return p.prototype.makeWireWrite=function(y){return"(_nbind.pushValue(new "+y+"))"},p}(_nbind.BindType);_nbind.CreateValueType=CreateValueType;var Int64Type=function(h){__extends(p,h);function p(){var y=h!==null&&h.apply(this,arguments)||this;return y.wireWrite=push64,y.wireRead=pop64,y}return p}(_nbind.BindType);_nbind.Int64Type=Int64Type;function pushArray(h,p){if(!h)return 0;var y=h.length;if((p.size||p.size===0)&&y<p.size)throw new Error("Type mismatch");var w=p.memberType.ptrSize,T=_nbind.Pool.lalloc(4+y*w);HEAPU32[T/4]=y;var e=p.memberType.heap,B=(T+4)/w,N=p.memberType.wireWrite,F=0;if(N)for(;F<y;)e[B++]=N(h[F++]);else for(;F<y;)e[B++]=h[F++];return T}_nbind.pushArray=pushArray;function popArray(h,p){if(h===0)return null;var y=HEAPU32[h/4],w=new Array(y),T=p.memberType.heap;h=(h+4)/p.memberType.ptrSize;var e=p.memberType.wireRead,B=0;if(e)for(;B<y;)w[B++]=e(T[h++]);else for(;B<y;)w[B++]=T[h++];return w}_nbind.popArray=popArray;var ArrayType=function(h){__extends(p,h);function p(y){var w=h.call(this,y)||this;return w.wireRead=function(T){return popArray(T,w)},w.wireWrite=function(T){return pushArray(T,w)},w.readResources=[_nbind.resources.pool],w.writeResources=[_nbind.resources.pool],w.memberType=y.paramList[0],y.paramList[1]&&(w.size=y.paramList[1]),w}return p}(_nbind.BindType);_nbind.ArrayType=ArrayType;function pushString(h,p){if(h==null)if(p&&p.Nullable)h="";else throw new Error("Type mismatch");if(p&&p.Strict){if(typeof h!="string")throw new Error("Type mismatch")}else h=h.toString();var y=Module.lengthBytesUTF8(h),w=_nbind.Pool.lalloc(4+y+1);return HEAPU32[w/4]=y,Module.stringToUTF8Array(h,HEAPU8,w+4,y+1),w}_nbind.pushString=pushString;function popString(h){if(h===0)return null;var p=HEAPU32[h/4];return Module.Pointer_stringify(h+4,p)}_nbind.popString=popString;var StringType=function(h){__extends(p,h);function p(){var y=h!==null&&h.apply(this,arguments)||this;return y.wireRead=popString,y.wireWrite=pushString,y.readResources=[_nbind.resources.pool],y.writeResources=[_nbind.resources.pool],y}return p.prototype.makeWireWrite=function(y,w){return function(T){return pushString(T,w)}},p}(_nbind.BindType);_nbind.StringType=StringType;function makeArgList(h){return Array.apply(null,Array(h)).map(function(p,y){return"a"+(y+1)})}function anyNeedsWireWrite(h,p){return h.reduce(function(y,w){return y||w.needsWireWrite(p)},!1)}function anyNeedsWireRead(h,p){return h.reduce(function(y,w){return y||!!w.needsWireRead(p)},!1)}function makeWireRead(h,p,y,w){var T=h.length;return y.makeWireRead?y.makeWireRead(w,h,T):y.wireRead?(h[T]=y.wireRead,"(convertParamList["+T+"]("+w+"))"):w}function makeWireWrite(h,p,y,w){var T,e=h.length;return y.makeWireWrite?T=y.makeWireWrite(w,p,h,e):T=y.wireWrite,T?typeof T=="string"?T:(h[e]=T,"(convertParamList["+e+"]("+w+"))"):w}function buildCallerFunction(dynCall,ptrType,ptr,num,policyTbl,needsWireWrite,prefix,returnType,argTypeList,mask,err){var argList=makeArgList(argTypeList.length),convertParamList=[],callExpression=makeWireRead(convertParamList,policyTbl,returnType,"dynCall("+[prefix].concat(argList.map(function(h,p){return makeWireWrite(convertParamList,policyTbl,argTypeList[p],h)})).join(",")+")"),resourceSet=_nbind.listResources([returnType],argTypeList),sourceCode="function("+argList.join(",")+"){"+(mask?"this.__nbindFlags&mask&&err();":"")+resourceSet.makeOpen()+"var r="+callExpression+";"+resourceSet.makeClose()+"return r;}";return eval("("+sourceCode+")")}function buildJSCallerFunction(returnType,argTypeList){var argList=makeArgList(argTypeList.length),convertParamList=[],callExpression=makeWireWrite(convertParamList,null,returnType,"_nbind.externalList[num].data("+argList.map(function(h,p){return makeWireRead(convertParamList,null,argTypeList[p],h)}).join(",")+")"),resourceSet=_nbind.listResources(argTypeList,[returnType]);resourceSet.remove(_nbind.resources.pool);var sourceCode="function("+["dummy","num"].concat(argList).join(",")+"){"+resourceSet.makeOpen()+"var r="+callExpression+";"+resourceSet.makeClose()+"return r;}";return eval("("+sourceCode+")")}_nbind.buildJSCallerFunction=buildJSCallerFunction;function makeJSCaller(h){var p=h.length-1,y=_nbind.getTypes(h,"callback"),w=y[0],T=y.slice(1),e=anyNeedsWireRead(T,null),B=w.needsWireWrite(null);if(!B&&!e)switch(p){case 0:return function(N,F){return _nbind.externalList[F].data()};case 1:return function(N,F,k){return _nbind.externalList[F].data(k)};case 2:return function(N,F,k,O){return _nbind.externalList[F].data(k,O)};case 3:return function(N,F,k,O,H){return _nbind.externalList[F].data(k,O,H)};default:break}return buildJSCallerFunction(w,T)}_nbind.makeJSCaller=makeJSCaller;function makeMethodCaller(h,p){var y=p.typeList.length-1,w=p.typeList.slice(0);w.splice(1,0,"uint32_t",p.boundID);var T=_nbind.getTypes(w,p.title),e=T[0],B=T.slice(3),N=e.needsWireRead(p.policyTbl),F=anyNeedsWireWrite(B,p.policyTbl),k=p.ptr,O=p.num,H=_nbind.getDynCall(T,p.title),D=~p.flags&1;function i0(){throw new Error("Calling a non-const method on a const object")}if(!N&&!F)switch(y){case 0:return function(){return this.__nbindFlags&D?i0():H(k,O,_nbind.pushPointer(this,h))};case 1:return function(s){return this.__nbindFlags&D?i0():H(k,O,_nbind.pushPointer(this,h),s)};case 2:return function(s,g0){return this.__nbindFlags&D?i0():H(k,O,_nbind.pushPointer(this,h),s,g0)};case 3:return function(s,g0,L0){return this.__nbindFlags&D?i0():H(k,O,_nbind.pushPointer(this,h),s,g0,L0)};default:break}return buildCallerFunction(H,h,k,O,p.policyTbl,F,"ptr,num,pushPointer(this,ptrType)",e,B,D,i0)}_nbind.makeMethodCaller=makeMethodCaller;function makeCaller(h){var p=h.typeList.length-1,y=_nbind.getTypes(h.typeList,h.title),w=y[0],T=y.slice(1),e=w.needsWireRead(h.policyTbl),B=anyNeedsWireWrite(T,h.policyTbl),N=h.direct,F=h.ptr;if(h.direct&&!e&&!B){var k=_nbind.getDynCall(y,h.title);switch(p){case 0:return function(){return k(N)};case 1:return function(i0){return k(N,i0)};case 2:return function(i0,s){return k(N,i0,s)};case 3:return function(i0,s,g0){return k(N,i0,s,g0)};default:break}F=0}var O;if(F){var H=h.typeList.slice(0);H.splice(1,0,"uint32_t"),y=_nbind.getTypes(H,h.title),O="ptr,num"}else F=N,O="ptr";var D=_nbind.getDynCall(y,h.title);return buildCallerFunction(D,null,F,h.num,h.policyTbl,B,O,w,T)}_nbind.makeCaller=makeCaller;function makeOverloader(h,p){var y=[];function w(){return y[arguments.length].apply(this,arguments)}return w.addMethod=function(T,e){y[e]=T},w.addMethod(h,p),w}_nbind.makeOverloader=makeOverloader;var Resource=function(){function h(p,y){var w=this;this.makeOpen=function(){return Object.keys(w.openTbl).join("")},this.makeClose=function(){return Object.keys(w.closeTbl).join("")},this.openTbl={},this.closeTbl={},p&&(this.openTbl[p]=!0),y&&(this.closeTbl[y]=!0)}return h.prototype.add=function(p){for(var y=0,w=Object.keys(p.openTbl);y<w.length;y++){var T=w[y];this.openTbl[T]=!0}for(var e=0,B=Object.keys(p.closeTbl);e<B.length;e++){var T=B[e];this.closeTbl[T]=!0}},h.prototype.remove=function(p){for(var y=0,w=Object.keys(p.openTbl);y<w.length;y++){var T=w[y];delete this.openTbl[T]}for(var e=0,B=Object.keys(p.closeTbl);e<B.length;e++){var T=B[e];delete this.closeTbl[T]}},h}();_nbind.Resource=Resource;function listResources(h,p){for(var y=new Resource,w=0,T=h;w<T.length;w++)for(var e=T[w],B=0,N=e.readResources||[];B<N.length;B++){var F=N[B];y.add(F)}for(var k=0,O=p;k<O.length;k++)for(var e=O[k],H=0,D=e.writeResources||[];H<D.length;H++){var F=D[H];y.add(F)}return y}_nbind.listResources=listResources,_nbind.resources={pool:new Resource("var used=HEAPU32[_nbind.Pool.usedPtr],page=HEAPU32[_nbind.Pool.pagePtr];","_nbind.Pool.lreset(used,page);")};var ExternalBuffer=function(h){__extends(p,h);function p(y,w){var T=h.call(this,y)||this;return T.ptr=w,T}return p.prototype.free=function(){_free(this.ptr)},p}(_nbind.External);function getBuffer(h){return h instanceof ArrayBuffer?new Uint8Array(h):h instanceof DataView?new Uint8Array(h.buffer,h.byteOffset,h.byteLength):h}function pushBuffer(h,p){if(h==null&&p&&p.Nullable&&(h=[]),typeof h!="object")throw new Error("Type mismatch");var y=h,w=y.byteLength||y.length;if(!w&&w!==0&&y.byteLength!==0)throw new Error("Type mismatch");var T=_nbind.Pool.lalloc(8),e=_malloc(w),B=T/4;return HEAPU32[B++]=w,HEAPU32[B++]=e,HEAPU32[B++]=new ExternalBuffer(h,e).register(),HEAPU8.set(getBuffer(h),e),T}var BufferType=function(h){__extends(p,h);function p(){var y=h!==null&&h.apply(this,arguments)||this;return y.wireWrite=pushBuffer,y.readResources=[_nbind.resources.pool],y.writeResources=[_nbind.resources.pool],y}return p.prototype.makeWireWrite=function(y,w){return function(T){return pushBuffer(T,w)}},p}(_nbind.BindType);_nbind.BufferType=BufferType;function commitBuffer(h,p,y){var w=_nbind.externalList[h].data,T=Buffer;if(typeof Buffer!="function"&&(T=function(){}),!(w instanceof Array)){var e=HEAPU8.subarray(p,p+y);if(w instanceof T){var B=void 0;typeof Buffer.from=="function"&&Buffer.from.length>=3?B=Buffer.from(e):B=new Buffer(e),B.copy(w)}else getBuffer(w).set(e)}}_nbind.commitBuffer=commitBuffer;var dirtyList=[],gcTimer=0;function sweep(){for(var h=0,p=dirtyList;h<p.length;h++){var y=p[h];y.__nbindState&3||y.free()}dirtyList=[],gcTimer=0}_nbind.mark=function(h){};function toggleLightGC(h){h?_nbind.mark=function(p){dirtyList.push(p),gcTimer||(gcTimer=setTimeout(sweep,0))}:_nbind.mark=function(p){}}_nbind.toggleLightGC=toggleLightGC}(_nbind),Module.requestFullScreen=function h(p,y,w){Module.printErr("Module.requestFullScreen is deprecated. Please call Module.requestFullscreen instead."),Module.requestFullScreen=Module.requestFullscreen,Browser.requestFullScreen(p,y,w)},Module.requestFullscreen=function h(p,y,w){Browser.requestFullscreen(p,y,w)},Module.requestAnimationFrame=function h(p){Browser.requestAnimationFrame(p)},Module.setCanvasSize=function h(p,y,w){Browser.setCanvasSize(p,y,w)},Module.pauseMainLoop=function h(){Browser.mainLoop.pause()},Module.resumeMainLoop=function h(){Browser.mainLoop.resume()},Module.getUserMedia=function h(){Browser.getUserMedia()},Module.createContext=function h(p,y,w,T){return Browser.createContext(p,y,w,T)},ENVIRONMENT_IS_NODE?_emscripten_get_now=function(){var p=process.hrtime();return p[0]*1e3+p[1]/1e6}:typeof dateNow!="undefined"?_emscripten_get_now=dateNow:typeof self=="object"&&self.performance&&typeof self.performance.now=="function"?_emscripten_get_now=function(){return self.performance.now()}:typeof performance=="object"&&typeof performance.now=="function"?_emscripten_get_now=function(){return performance.now()}:_emscripten_get_now=Date.now,__ATEXIT__.push(function(){var h=Module._fflush;h&&h(0);var p=___syscall146.printChar;if(p){var y=___syscall146.buffers;y[1].length&&p(1,10),y[2].length&&p(2,10)}}),DYNAMICTOP_PTR=allocate(1,"i32",ALLOC_STATIC),STACK_BASE=STACKTOP=Runtime.alignMemory(STATICTOP),STACK_MAX=STACK_BASE+TOTAL_STACK,DYNAMIC_BASE=Runtime.alignMemory(STACK_MAX),HEAP32[DYNAMICTOP_PTR>>2]=DYNAMIC_BASE,staticSealed=!0;function invoke_viiiii(h,p,y,w,T,e){try{Module.dynCall_viiiii(h,p,y,w,T,e)}catch(B){if(typeof B!="number"&&B!=="longjmp")throw B;Module.setThrew(1,0)}}function invoke_vif(h,p,y){try{Module.dynCall_vif(h,p,y)}catch(w){if(typeof w!="number"&&w!=="longjmp")throw w;Module.setThrew(1,0)}}function invoke_vid(h,p,y){try{Module.dynCall_vid(h,p,y)}catch(w){if(typeof w!="number"&&w!=="longjmp")throw w;Module.setThrew(1,0)}}function invoke_fiff(h,p,y,w){try{return Module.dynCall_fiff(h,p,y,w)}catch(T){if(typeof T!="number"&&T!=="longjmp")throw T;Module.setThrew(1,0)}}function invoke_vi(h,p){try{Module.dynCall_vi(h,p)}catch(y){if(typeof y!="number"&&y!=="longjmp")throw y;Module.setThrew(1,0)}}function invoke_vii(h,p,y){try{Module.dynCall_vii(h,p,y)}catch(w){if(typeof w!="number"&&w!=="longjmp")throw w;Module.setThrew(1,0)}}function invoke_ii(h,p){try{return Module.dynCall_ii(h,p)}catch(y){if(typeof y!="number"&&y!=="longjmp")throw y;Module.setThrew(1,0)}}function invoke_viddi(h,p,y,w,T){try{Module.dynCall_viddi(h,p,y,w,T)}catch(e){if(typeof e!="number"&&e!=="longjmp")throw e;Module.setThrew(1,0)}}function invoke_vidd(h,p,y,w){try{Module.dynCall_vidd(h,p,y,w)}catch(T){if(typeof T!="number"&&T!=="longjmp")throw T;Module.setThrew(1,0)}}function invoke_iiii(h,p,y,w){try{return Module.dynCall_iiii(h,p,y,w)}catch(T){if(typeof T!="number"&&T!=="longjmp")throw T;Module.setThrew(1,0)}}function invoke_diii(h,p,y,w){try{return Module.dynCall_diii(h,p,y,w)}catch(T){if(typeof T!="number"&&T!=="longjmp")throw T;Module.setThrew(1,0)}}function invoke_di(h,p){try{return Module.dynCall_di(h,p)}catch(y){if(typeof y!="number"&&y!=="longjmp")throw y;Module.setThrew(1,0)}}function invoke_iid(h,p,y){try{return Module.dynCall_iid(h,p,y)}catch(w){if(typeof w!="number"&&w!=="longjmp")throw w;Module.setThrew(1,0)}}function invoke_iii(h,p,y){try{return Module.dynCall_iii(h,p,y)}catch(w){if(typeof w!="number"&&w!=="longjmp")throw w;Module.setThrew(1,0)}}function invoke_viiddi(h,p,y,w,T,e){try{Module.dynCall_viiddi(h,p,y,w,T,e)}catch(B){if(typeof B!="number"&&B!=="longjmp")throw B;Module.setThrew(1,0)}}function invoke_viiiiii(h,p,y,w,T,e,B){try{Module.dynCall_viiiiii(h,p,y,w,T,e,B)}catch(N){if(typeof N!="number"&&N!=="longjmp")throw N;Module.setThrew(1,0)}}function invoke_dii(h,p,y){try{return Module.dynCall_dii(h,p,y)}catch(w){if(typeof w!="number"&&w!=="longjmp")throw w;Module.setThrew(1,0)}}function invoke_i(h){try{return Module.dynCall_i(h)}catch(p){if(typeof p!="number"&&p!=="longjmp")throw p;Module.setThrew(1,0)}}function invoke_iiiiii(h,p,y,w,T,e){try{return Module.dynCall_iiiiii(h,p,y,w,T,e)}catch(B){if(typeof B!="number"&&B!=="longjmp")throw B;Module.setThrew(1,0)}}function invoke_viiid(h,p,y,w,T){try{Module.dynCall_viiid(h,p,y,w,T)}catch(e){if(typeof e!="number"&&e!=="longjmp")throw e;Module.setThrew(1,0)}}function invoke_viififi(h,p,y,w,T,e,B){try{Module.dynCall_viififi(h,p,y,w,T,e,B)}catch(N){if(typeof N!="number"&&N!=="longjmp")throw N;Module.setThrew(1,0)}}function invoke_viii(h,p,y,w){try{Module.dynCall_viii(h,p,y,w)}catch(T){if(typeof T!="number"&&T!=="longjmp")throw T;Module.setThrew(1,0)}}function invoke_v(h){try{Module.dynCall_v(h)}catch(p){if(typeof p!="number"&&p!=="longjmp")throw p;Module.setThrew(1,0)}}function invoke_viid(h,p,y,w){try{Module.dynCall_viid(h,p,y,w)}catch(T){if(typeof T!="number"&&T!=="longjmp")throw T;Module.setThrew(1,0)}}function invoke_idd(h,p,y){try{return Module.dynCall_idd(h,p,y)}catch(w){if(typeof w!="number"&&w!=="longjmp")throw w;Module.setThrew(1,0)}}function invoke_viiii(h,p,y,w,T){try{Module.dynCall_viiii(h,p,y,w,T)}catch(e){if(typeof e!="number"&&e!=="longjmp")throw e;Module.setThrew(1,0)}}Module.asmGlobalArg={Math,Int8Array,Int16Array,Int32Array,Uint8Array,Uint16Array,Uint32Array,Float32Array,Float64Array,NaN:NaN,Infinity:1/0},Module.asmLibraryArg={abort,assert,enlargeMemory,getTotalMemory,abortOnCannotGrowMemory,invoke_viiiii,invoke_vif,invoke_vid,invoke_fiff,invoke_vi,invoke_vii,invoke_ii,invoke_viddi,invoke_vidd,invoke_iiii,invoke_diii,invoke_di,invoke_iid,invoke_iii,invoke_viiddi,invoke_viiiiii,invoke_dii,invoke_i,invoke_iiiiii,invoke_viiid,invoke_viififi,invoke_viii,invoke_v,invoke_viid,invoke_idd,invoke_viiii,_emscripten_asm_const_iiiii,_emscripten_asm_const_iiidddddd,_emscripten_asm_const_iiiid,__nbind_reference_external,_emscripten_asm_const_iiiiiiii,_removeAccessorPrefix,_typeModule,__nbind_register_pool,__decorate,_llvm_stackrestore,___cxa_atexit,__extends,__nbind_get_value_object,__ZN8facebook4yoga14YGNodeToStringEPNSt3__212basic_stringIcNS1_11char_traitsIcEENS1_9allocatorIcEEEEP6YGNode14YGPrintOptionsj,_emscripten_set_main_loop_timing,__nbind_register_primitive,__nbind_register_type,_emscripten_memcpy_big,__nbind_register_function,___setErrNo,__nbind_register_class,__nbind_finish,_abort,_nbind_value,_llvm_stacksave,___syscall54,_defineHidden,_emscripten_set_main_loop,_emscripten_get_now,__nbind_register_callback_signature,_emscripten_asm_const_iiiiii,__nbind_free_external,_emscripten_asm_const_iiii,_emscripten_asm_const_iiididi,___syscall6,_atexit,___syscall140,___syscall146,DYNAMICTOP_PTR,tempDoublePtr,ABORT,STACKTOP,STACK_MAX,cttz_i8,___dso_handle};var asm=function(h,p,y){var w=new h.Int8Array(y),T=new h.Int16Array(y),e=new h.Int32Array(y),B=new h.Uint8Array(y),N=new h.Uint16Array(y),F=new h.Uint32Array(y),k=new h.Float32Array(y),O=new h.Float64Array(y),H=p.DYNAMICTOP_PTR|0,D=p.tempDoublePtr|0,i0=p.ABORT|0,s=p.STACKTOP|0,g0=p.STACK_MAX|0,L0=p.cttz_i8|0,V=p.___dso_handle|0,S0=0,$0=0,M2=0,I2=0,y0=h.NaN,G0=h.Infinity,Q0=0,en=0,hn=0,dn=0,xn=0,v0=0,Dn=h.Math.floor,H1=h.Math.abs,Hn=h.Math.sqrt,mn=h.Math.pow,Mk=h.Math.cos,Tk=h.Math.sin,Ak=h.Math.tan,Ck=h.Math.acos,Sk=h.Math.asin,Ek=h.Math.atan,Lk=h.Math.atan2,Bk=h.Math.exp,Rk=h.Math.log,Pk=h.Math.ceil,sr=h.Math.imul,Ok=h.Math.min,Nk=h.Math.max,U1=h.Math.clz32,_=h.Math.fround,T0=p.abort,jk=p.assert,Ru=p.enlargeMemory,Pu=p.getTotalMemory,Ou=p.abortOnCannotGrowMemory,Ik=p.invoke_viiiii,Fk=p.invoke_vif,xk=p.invoke_vid,Dk=p.invoke_fiff,Hk=p.invoke_vi,Uk=p.invoke_vii,qk=p.invoke_ii,zk=p.invoke_viddi,Wk=p.invoke_vidd,Yk=p.invoke_iiii,Vk=p.invoke_diii,Gk=p.invoke_di,Kk=p.invoke_iid,Xk=p.invoke_iii,Jk=p.invoke_viiddi,Zk=p.invoke_viiiiii,$k=p.invoke_dii,Qk=p.invoke_i,ak=p.invoke_iiiiii,bk=p.invoke_viiid,nM=p.invoke_viififi,eM=p.invoke_viii,rM=p.invoke_v,iM=p.invoke_viid,tM=p.invoke_idd,uM=p.invoke_viiii,Un=p._emscripten_asm_const_iiiii,Nu=p._emscripten_asm_const_iiidddddd,ju=p._emscripten_asm_const_iiiid,Iu=p.__nbind_reference_external,Fu=p._emscripten_asm_const_iiiiiiii,oM=p._removeAccessorPrefix,fM=p._typeModule,xu=p.__nbind_register_pool,lM=p.__decorate,Du=p._llvm_stackrestore,$=p.___cxa_atexit,cM=p.__extends,vr=p.__nbind_get_value_object,Hu=p.__ZN8facebook4yoga14YGNodeToStringEPNSt3__212basic_stringIcNS1_11char_traitsIcEENS1_9allocatorIcEEEEP6YGNode14YGPrintOptionsj,sM=p._emscripten_set_main_loop_timing,Uu=p.__nbind_register_primitive,_r=p.__nbind_register_type,qu=p._emscripten_memcpy_big,pn=p.__nbind_register_function,hr=p.___setErrNo,zu=p.__nbind_register_class,Wu=p.__nbind_finish,u0=p._abort,vM=p._nbind_value,Yu=p._llvm_stacksave,Vu=p.___syscall54,_M=p._defineHidden,hM=p._emscripten_set_main_loop,dM=p._emscripten_get_now,Gu=p.__nbind_register_callback_signature,qn=p._emscripten_asm_const_iiiiii,dr=p.__nbind_free_external,mr=p._emscripten_asm_const_iiii,Ku=p._emscripten_asm_const_iiididi,Xu=p.___syscall6,mM=p._atexit,Ju=p.___syscall140,pr=p.___syscall146,pM=_(0);const x=_(0);function Zu(n){n=n|0;var r=0;return r=s,s=s+n|0,s=s+15&-16,r|0}function $u(){return s|0}function Qu(n){n=n|0,s=n}function au(n,r){n=n|0,r=r|0,s=n,g0=r}function bu(n,r){n=n|0,r=r|0,S0||(S0=n,$0=r)}function no(n){n=n|0,v0=n}function eo(){return v0|0}function ro(){var n=0,r=0;Q(8104,8,400)|0,Q(8504,408,540)|0,n=9044,r=n+44|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));w[9088]=0,w[9089]=1,e[2273]=0,e[2274]=948,e[2275]=948,$(17,8104,V|0)|0}function io(n){n=n|0,po(n+948|0)}function W(n){return n=_(n),((af(n)|0)&2147483647)>>>0>2139095040|0}function K0(n,r,t){n=n|0,r=r|0,t=t|0;n:do if(e[n+(r<<3)+4>>2]|0)n=n+(r<<3)|0;else{if((r|2|0)==3&&e[n+60>>2]|0){n=n+56|0;break}switch(r|0){case 0:case 2:case 4:case 5:{if(e[n+52>>2]|0){n=n+48|0;break n}break}default:}if(e[n+68>>2]|0){n=n+64|0;break}else{n=(r|1|0)==5?948:t;break}}while(!1);return n|0}function wr(n){n=n|0;var r=0;return r=Bn(1e3)|0,zn(n,(r|0)!=0,2456),e[2276]=(e[2276]|0)+1,Q(r|0,8104,1e3)|0,w[n+2>>0]|0&&(e[r+4>>2]=2,e[r+12>>2]=4),e[r+976>>2]=n,r|0}function zn(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0;o=s,s=s+16|0,u=o,r||(e[u>>2]=t,Uf(n,5,3197,u)),s=o}function to(){return wr(956)|0}function gr(n){n=n|0;var r=0;return r=q(1e3)|0,uo(r,n),zn(e[n+976>>2]|0,1,2456),e[2276]=(e[2276]|0)+1,e[r+944>>2]=0,r|0}function uo(n,r){n=n|0,r=r|0;var t=0;Q(n|0,r|0,948)|0,zf(n+948|0,r+948|0),t=n+960|0,n=r+960|0,r=t+40|0;do e[t>>2]=e[n>>2],t=t+4|0,n=n+4|0;while((t|0)<(r|0))}function oo(n){n=n|0;var r=0,t=0,u=0,o=0;if(r=n+944|0,t=e[r>>2]|0,t|0&&(yr(t+948|0,n)|0,e[r>>2]=0),t=F2(n)|0,t|0){r=0;do e[(x2(n,r)|0)+944>>2]=0,r=r+1|0;while((r|0)!=(t|0))}t=n+948|0,u=e[t>>2]|0,o=n+952|0,r=e[o>>2]|0,(r|0)!=(u|0)&&(e[o>>2]=r+(~((r+-4-u|0)>>>2)<<2)),kr(t),Rn(n),e[2276]=(e[2276]|0)+-1}function yr(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0;u=e[n>>2]|0,c=n+4|0,t=e[c>>2]|0,f=t;n:do if((u|0)==(t|0))o=u,l=4;else for(n=u;;){if((e[n>>2]|0)==(r|0)){o=n,l=4;break n}if(n=n+4|0,(n|0)==(t|0)){n=0;break}}while(!1);return(l|0)==4&&((o|0)!=(t|0)?(u=o+4|0,n=f-u|0,r=n>>2,r&&(cn(o|0,u|0,n|0)|0,t=e[c>>2]|0),n=o+(r<<2)|0,(t|0)==(n|0)||(e[c>>2]=t+(~((t+-4-n|0)>>>2)<<2)),n=1):n=0),n|0}function F2(n){return n=n|0,(e[n+952>>2]|0)-(e[n+948>>2]|0)>>2|0}function x2(n,r){n=n|0,r=r|0;var t=0;return t=e[n+948>>2]|0,(e[n+952>>2]|0)-t>>2>>>0>r>>>0?n=e[t+(r<<2)>>2]|0:n=0,n|0}function kr(n){n=n|0;var r=0,t=0,u=0,o=0;u=s,s=s+32|0,r=u,o=e[n>>2]|0,t=(e[n+4>>2]|0)-o|0,((e[n+8>>2]|0)-o|0)>>>0>t>>>0&&(o=t>>2,Qn(r,o,o,n+8|0),bf(n,r),an(r)),s=u}function fo(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0;m=F2(n)|0;do if(m|0){if((e[(x2(n,0)|0)+944>>2]|0)==(n|0)){if(!(yr(n+948|0,r)|0))break;Q(r+400|0,8504,540)|0,e[r+944>>2]=0,o0(n);break}l=e[(e[n+976>>2]|0)+12>>2]|0,c=n+948|0,v=(l|0)==0,t=0,f=0;do u=e[(e[c>>2]|0)+(f<<2)>>2]|0,(u|0)==(r|0)?o0(n):(o=gr(u)|0,e[(e[c>>2]|0)+(t<<2)>>2]=o,e[o+944>>2]=n,v||cr[l&15](u,o,n,t),t=t+1|0),f=f+1|0;while((f|0)!=(m|0));if(t>>>0<m>>>0){v=n+948|0,c=n+952|0,l=t,t=e[c>>2]|0;do f=(e[v>>2]|0)+(l<<2)|0,u=f+4|0,o=t-u|0,r=o>>2,r&&(cn(f|0,u|0,o|0)|0,t=e[c>>2]|0),o=t,u=f+(r<<2)|0,(o|0)!=(u|0)&&(t=o+(~((o+-4-u|0)>>>2)<<2)|0,e[c>>2]=t),l=l+1|0;while((l|0)!=(m|0))}}while(!1)}function lo(n){n=n|0;var r=0,t=0,u=0,o=0;a0(n,(F2(n)|0)==0,2491),a0(n,(e[n+944>>2]|0)==0,2545),r=n+948|0,t=e[r>>2]|0,u=n+952|0,o=e[u>>2]|0,(o|0)!=(t|0)&&(e[u>>2]=o+(~((o+-4-t|0)>>>2)<<2)),kr(r),r=n+976|0,t=e[r>>2]|0,Q(n|0,8104,1e3)|0,w[t+2>>0]|0&&(e[n+4>>2]=2,e[n+12>>2]=4),e[r>>2]=t}function a0(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0;o=s,s=s+16|0,u=o,r||(e[u>>2]=t,A2(n,5,3197,u)),s=o}function co(){return e[2276]|0}function so(){var n=0;return n=Bn(20)|0,vo((n|0)!=0,2592),e[2277]=(e[2277]|0)+1,e[n>>2]=e[239],e[n+4>>2]=e[240],e[n+8>>2]=e[241],e[n+12>>2]=e[242],e[n+16>>2]=e[243],n|0}function vo(n,r){n=n|0,r=r|0;var t=0,u=0;u=s,s=s+16|0,t=u,n||(e[t>>2]=r,A2(0,5,3197,t)),s=u}function _o(n){n=n|0,Rn(n),e[2277]=(e[2277]|0)+-1}function Mr(n,r){n=n|0,r=r|0;var t=0;r?(a0(n,(F2(n)|0)==0,2629),t=1):(t=0,r=0),e[n+964>>2]=r,e[n+988>>2]=t}function ho(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;u=s,s=s+16|0,f=u+8|0,o=u+4|0,l=u,e[o>>2]=r,a0(n,(e[r+944>>2]|0)==0,2709),a0(n,(e[n+964>>2]|0)==0,2763),Wn(n),r=n+948|0,e[l>>2]=(e[r>>2]|0)+(t<<2),e[f>>2]=e[l>>2],mo(r,f,o)|0,e[(e[o>>2]|0)+944>>2]=n,o0(n),s=u}function Wn(n){n=n|0;var r=0,t=0,u=0,o=0,f=0,l=0,c=0;if(t=F2(n)|0,t|0&&(e[(x2(n,0)|0)+944>>2]|0)!=(n|0)){u=e[(e[n+976>>2]|0)+12>>2]|0,o=n+948|0,f=(u|0)==0,r=0;do l=e[(e[o>>2]|0)+(r<<2)>>2]|0,c=gr(l)|0,e[(e[o>>2]|0)+(r<<2)>>2]=c,e[c+944>>2]=n,f||cr[u&15](l,c,n,r),r=r+1|0;while((r|0)!=(t|0))}}function mo(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0,L=0,C=0,A=0,P=0,R=0;P=s,s=s+64|0,g=P+52|0,c=P+48|0,M=P+28|0,L=P+24|0,C=P+20|0,A=P,u=e[n>>2]|0,f=u,r=u+((e[r>>2]|0)-f>>2<<2)|0,u=n+4|0,o=e[u>>2]|0,l=n+8|0;do if(o>>>0<(e[l>>2]|0)>>>0){if((r|0)==(o|0)){e[r>>2]=e[t>>2],e[u>>2]=(e[u>>2]|0)+4;break}n4(n,r,o,r+4|0),r>>>0<=t>>>0&&(t=(e[u>>2]|0)>>>0>t>>>0?t+4|0:t),e[r>>2]=e[t>>2]}else{u=(o-f>>2)+1|0,o=Dr(n)|0,o>>>0<u>>>0&&c0(n),d=e[n>>2]|0,m=(e[l>>2]|0)-d|0,f=m>>1,Qn(A,m>>2>>>0<o>>>1>>>0?f>>>0<u>>>0?u:f:o,r-d>>2,n+8|0),d=A+8|0,u=e[d>>2]|0,f=A+12|0,m=e[f>>2]|0,l=m,v=u;do if((u|0)==(m|0)){if(m=A+4|0,u=e[m>>2]|0,R=e[A>>2]|0,o=R,u>>>0<=R>>>0){u=l-o>>1,u=u|0?u:1,Qn(M,u,u>>>2,e[A+16>>2]|0),e[L>>2]=e[m>>2],e[C>>2]=e[d>>2],e[c>>2]=e[L>>2],e[g>>2]=e[C>>2],r4(M,c,g),u=e[A>>2]|0,e[A>>2]=e[M>>2],e[M>>2]=u,u=M+4|0,R=e[m>>2]|0,e[m>>2]=e[u>>2],e[u>>2]=R,u=M+8|0,R=e[d>>2]|0,e[d>>2]=e[u>>2],e[u>>2]=R,u=M+12|0,R=e[f>>2]|0,e[f>>2]=e[u>>2],e[u>>2]=R,an(M),u=e[d>>2]|0;break}f=u,l=((f-o>>2)+1|0)/-2|0,c=u+(l<<2)|0,o=v-f|0,f=o>>2,f&&(cn(c|0,u|0,o|0)|0,u=e[m>>2]|0),R=c+(f<<2)|0,e[d>>2]=R,e[m>>2]=u+(l<<2),u=R}while(!1);e[u>>2]=e[t>>2],e[d>>2]=(e[d>>2]|0)+4,r=e4(n,A,r)|0,an(A)}while(!1);return s=P,r|0}function o0(n){n=n|0;var r=0;do{if(r=n+984|0,w[r>>0]|0)break;w[r>>0]=1,k[n+504>>2]=_(y0),n=e[n+944>>2]|0}while(n|0)}function po(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-4-u|0)>>>2)<<2)),I(t))}function wo(n){return n=n|0,e[n+944>>2]|0}function go(n){n=n|0,a0(n,(e[n+964>>2]|0)!=0,2832),o0(n)}function yo(n){return n=n|0,(w[n+984>>0]|0)!=0|0}function ko(n,r){n=n|0,r=r|0,ny(n,r,400)|0&&(Q(n|0,r|0,400)|0,o0(n))}function Mo(n){n=n|0;var r=x;return r=_(k[n+44>>2]),n=W(r)|0,_(n?_(0):r)}function To(n){n=n|0;var r=x;return r=_(k[n+48>>2]),W(r)|0&&(r=w[(e[n+976>>2]|0)+2>>0]|0?_(1):_(0)),_(r)}function Ao(n,r){n=n|0,r=r|0,e[n+980>>2]=r}function Tr(n){return n=n|0,e[n+980>>2]|0}function Co(n,r){n=n|0,r=r|0;var t=0;t=n+4|0,(e[t>>2]|0)!=(r|0)&&(e[t>>2]=r,o0(n))}function So(n){return n=n|0,e[n+4>>2]|0}function Eo(n,r){n=n|0,r=r|0;var t=0;t=n+8|0,(e[t>>2]|0)!=(r|0)&&(e[t>>2]=r,o0(n))}function Lo(n){return n=n|0,e[n+8>>2]|0}function Bo(n,r){n=n|0,r=r|0;var t=0;t=n+12|0,(e[t>>2]|0)!=(r|0)&&(e[t>>2]=r,o0(n))}function Ro(n){return n=n|0,e[n+12>>2]|0}function Po(n,r){n=n|0,r=r|0;var t=0;t=n+16|0,(e[t>>2]|0)!=(r|0)&&(e[t>>2]=r,o0(n))}function Oo(n){return n=n|0,e[n+16>>2]|0}function No(n,r){n=n|0,r=r|0;var t=0;t=n+20|0,(e[t>>2]|0)!=(r|0)&&(e[t>>2]=r,o0(n))}function jo(n){return n=n|0,e[n+20>>2]|0}function Io(n,r){n=n|0,r=r|0;var t=0;t=n+24|0,(e[t>>2]|0)!=(r|0)&&(e[t>>2]=r,o0(n))}function Fo(n){return n=n|0,e[n+24>>2]|0}function xo(n,r){n=n|0,r=r|0;var t=0;t=n+28|0,(e[t>>2]|0)!=(r|0)&&(e[t>>2]=r,o0(n))}function Do(n){return n=n|0,e[n+28>>2]|0}function Ho(n,r){n=n|0,r=r|0;var t=0;t=n+32|0,(e[t>>2]|0)!=(r|0)&&(e[t>>2]=r,o0(n))}function Uo(n){return n=n|0,e[n+32>>2]|0}function qo(n,r){n=n|0,r=r|0;var t=0;t=n+36|0,(e[t>>2]|0)!=(r|0)&&(e[t>>2]=r,o0(n))}function zo(n){return n=n|0,e[n+36>>2]|0}function Wo(n,r){n=n|0,r=_(r);var t=0;t=n+40|0,_(k[t>>2])!=r&&(k[t>>2]=r,o0(n))}function Yo(n,r){n=n|0,r=_(r);var t=0;t=n+44|0,_(k[t>>2])!=r&&(k[t>>2]=r,o0(n))}function Vo(n,r){n=n|0,r=_(r);var t=0;t=n+48|0,_(k[t>>2])!=r&&(k[t>>2]=r,o0(n))}function Go(n,r){n=n|0,r=_(r);var t=0,u=0,o=0,f=0;f=W(r)|0,t=(f^1)&1,u=n+52|0,o=n+56|0,f|_(k[u>>2])==r&&(e[o>>2]|0)==(t|0)||(k[u>>2]=r,e[o>>2]=t,o0(n))}function Ko(n,r){n=n|0,r=_(r);var t=0,u=0;u=n+52|0,t=n+56|0,_(k[u>>2])==r&&(e[t>>2]|0)==2||(k[u>>2]=r,u=W(r)|0,e[t>>2]=u?3:2,o0(n))}function Xo(n,r){n=n|0,r=r|0;var t=0,u=0;u=r+52|0,t=e[u+4>>2]|0,r=n,e[r>>2]=e[u>>2],e[r+4>>2]=t}function Jo(n,r,t){n=n|0,r=r|0,t=_(t);var u=0,o=0,f=0;f=W(t)|0,u=(f^1)&1,o=n+132+(r<<3)|0,r=n+132+(r<<3)+4|0,f|_(k[o>>2])==t&&(e[r>>2]|0)==(u|0)||(k[o>>2]=t,e[r>>2]=u,o0(n))}function Zo(n,r,t){n=n|0,r=r|0,t=_(t);var u=0,o=0,f=0;f=W(t)|0,u=f?0:2,o=n+132+(r<<3)|0,r=n+132+(r<<3)+4|0,f|_(k[o>>2])==t&&(e[r>>2]|0)==(u|0)||(k[o>>2]=t,e[r>>2]=u,o0(n))}function $o(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=r+132+(t<<3)|0,r=e[u+4>>2]|0,t=n,e[t>>2]=e[u>>2],e[t+4>>2]=r}function Qo(n,r,t){n=n|0,r=r|0,t=_(t);var u=0,o=0,f=0;f=W(t)|0,u=(f^1)&1,o=n+60+(r<<3)|0,r=n+60+(r<<3)+4|0,f|_(k[o>>2])==t&&(e[r>>2]|0)==(u|0)||(k[o>>2]=t,e[r>>2]=u,o0(n))}function ao(n,r,t){n=n|0,r=r|0,t=_(t);var u=0,o=0,f=0;f=W(t)|0,u=f?0:2,o=n+60+(r<<3)|0,r=n+60+(r<<3)+4|0,f|_(k[o>>2])==t&&(e[r>>2]|0)==(u|0)||(k[o>>2]=t,e[r>>2]=u,o0(n))}function bo(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=r+60+(t<<3)|0,r=e[u+4>>2]|0,t=n,e[t>>2]=e[u>>2],e[t+4>>2]=r}function nf(n,r){n=n|0,r=r|0;var t=0;t=n+60+(r<<3)+4|0,(e[t>>2]|0)!=3&&(k[n+60+(r<<3)>>2]=_(y0),e[t>>2]=3,o0(n))}function ef(n,r,t){n=n|0,r=r|0,t=_(t);var u=0,o=0,f=0;f=W(t)|0,u=(f^1)&1,o=n+204+(r<<3)|0,r=n+204+(r<<3)+4|0,f|_(k[o>>2])==t&&(e[r>>2]|0)==(u|0)||(k[o>>2]=t,e[r>>2]=u,o0(n))}function rf(n,r,t){n=n|0,r=r|0,t=_(t);var u=0,o=0,f=0;f=W(t)|0,u=f?0:2,o=n+204+(r<<3)|0,r=n+204+(r<<3)+4|0,f|_(k[o>>2])==t&&(e[r>>2]|0)==(u|0)||(k[o>>2]=t,e[r>>2]=u,o0(n))}function tf(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=r+204+(t<<3)|0,r=e[u+4>>2]|0,t=n,e[t>>2]=e[u>>2],e[t+4>>2]=r}function uf(n,r,t){n=n|0,r=r|0,t=_(t);var u=0,o=0,f=0;f=W(t)|0,u=(f^1)&1,o=n+276+(r<<3)|0,r=n+276+(r<<3)+4|0,f|_(k[o>>2])==t&&(e[r>>2]|0)==(u|0)||(k[o>>2]=t,e[r>>2]=u,o0(n))}function of(n,r){return n=n|0,r=r|0,_(k[n+276+(r<<3)>>2])}function ff(n,r){n=n|0,r=_(r);var t=0,u=0,o=0,f=0;f=W(r)|0,t=(f^1)&1,u=n+348|0,o=n+352|0,f|_(k[u>>2])==r&&(e[o>>2]|0)==(t|0)||(k[u>>2]=r,e[o>>2]=t,o0(n))}function lf(n,r){n=n|0,r=_(r);var t=0,u=0;u=n+348|0,t=n+352|0,_(k[u>>2])==r&&(e[t>>2]|0)==2||(k[u>>2]=r,u=W(r)|0,e[t>>2]=u?3:2,o0(n))}function cf(n){n=n|0;var r=0;r=n+352|0,(e[r>>2]|0)!=3&&(k[n+348>>2]=_(y0),e[r>>2]=3,o0(n))}function sf(n,r){n=n|0,r=r|0;var t=0,u=0;u=r+348|0,t=e[u+4>>2]|0,r=n,e[r>>2]=e[u>>2],e[r+4>>2]=t}function vf(n,r){n=n|0,r=_(r);var t=0,u=0,o=0,f=0;f=W(r)|0,t=(f^1)&1,u=n+356|0,o=n+360|0,f|_(k[u>>2])==r&&(e[o>>2]|0)==(t|0)||(k[u>>2]=r,e[o>>2]=t,o0(n))}function _f(n,r){n=n|0,r=_(r);var t=0,u=0;u=n+356|0,t=n+360|0,_(k[u>>2])==r&&(e[t>>2]|0)==2||(k[u>>2]=r,u=W(r)|0,e[t>>2]=u?3:2,o0(n))}function hf(n){n=n|0;var r=0;r=n+360|0,(e[r>>2]|0)!=3&&(k[n+356>>2]=_(y0),e[r>>2]=3,o0(n))}function df(n,r){n=n|0,r=r|0;var t=0,u=0;u=r+356|0,t=e[u+4>>2]|0,r=n,e[r>>2]=e[u>>2],e[r+4>>2]=t}function mf(n,r){n=n|0,r=_(r);var t=0,u=0,o=0,f=0;f=W(r)|0,t=(f^1)&1,u=n+364|0,o=n+368|0,f|_(k[u>>2])==r&&(e[o>>2]|0)==(t|0)||(k[u>>2]=r,e[o>>2]=t,o0(n))}function pf(n,r){n=n|0,r=_(r);var t=0,u=0,o=0,f=0;f=W(r)|0,t=f?0:2,u=n+364|0,o=n+368|0,f|_(k[u>>2])==r&&(e[o>>2]|0)==(t|0)||(k[u>>2]=r,e[o>>2]=t,o0(n))}function wf(n,r){n=n|0,r=r|0;var t=0,u=0;u=r+364|0,t=e[u+4>>2]|0,r=n,e[r>>2]=e[u>>2],e[r+4>>2]=t}function gf(n,r){n=n|0,r=_(r);var t=0,u=0,o=0,f=0;f=W(r)|0,t=(f^1)&1,u=n+372|0,o=n+376|0,f|_(k[u>>2])==r&&(e[o>>2]|0)==(t|0)||(k[u>>2]=r,e[o>>2]=t,o0(n))}function yf(n,r){n=n|0,r=_(r);var t=0,u=0,o=0,f=0;f=W(r)|0,t=f?0:2,u=n+372|0,o=n+376|0,f|_(k[u>>2])==r&&(e[o>>2]|0)==(t|0)||(k[u>>2]=r,e[o>>2]=t,o0(n))}function kf(n,r){n=n|0,r=r|0;var t=0,u=0;u=r+372|0,t=e[u+4>>2]|0,r=n,e[r>>2]=e[u>>2],e[r+4>>2]=t}function Mf(n,r){n=n|0,r=_(r);var t=0,u=0,o=0,f=0;f=W(r)|0,t=(f^1)&1,u=n+380|0,o=n+384|0,f|_(k[u>>2])==r&&(e[o>>2]|0)==(t|0)||(k[u>>2]=r,e[o>>2]=t,o0(n))}function Tf(n,r){n=n|0,r=_(r);var t=0,u=0,o=0,f=0;f=W(r)|0,t=f?0:2,u=n+380|0,o=n+384|0,f|_(k[u>>2])==r&&(e[o>>2]|0)==(t|0)||(k[u>>2]=r,e[o>>2]=t,o0(n))}function Af(n,r){n=n|0,r=r|0;var t=0,u=0;u=r+380|0,t=e[u+4>>2]|0,r=n,e[r>>2]=e[u>>2],e[r+4>>2]=t}function Cf(n,r){n=n|0,r=_(r);var t=0,u=0,o=0,f=0;f=W(r)|0,t=(f^1)&1,u=n+388|0,o=n+392|0,f|_(k[u>>2])==r&&(e[o>>2]|0)==(t|0)||(k[u>>2]=r,e[o>>2]=t,o0(n))}function Sf(n,r){n=n|0,r=_(r);var t=0,u=0,o=0,f=0;f=W(r)|0,t=f?0:2,u=n+388|0,o=n+392|0,f|_(k[u>>2])==r&&(e[o>>2]|0)==(t|0)||(k[u>>2]=r,e[o>>2]=t,o0(n))}function Ef(n,r){n=n|0,r=r|0;var t=0,u=0;u=r+388|0,t=e[u+4>>2]|0,r=n,e[r>>2]=e[u>>2],e[r+4>>2]=t}function Lf(n,r){n=n|0,r=_(r);var t=0;t=n+396|0,_(k[t>>2])!=r&&(k[t>>2]=r,o0(n))}function Bf(n){return n=n|0,_(k[n+396>>2])}function Ar(n){return n=n|0,_(k[n+400>>2])}function Cr(n){return n=n|0,_(k[n+404>>2])}function Sr(n){return n=n|0,_(k[n+408>>2])}function Er(n){return n=n|0,_(k[n+412>>2])}function Lr(n){return n=n|0,_(k[n+416>>2])}function Br(n){return n=n|0,_(k[n+420>>2])}function Rf(n,r){switch(n=n|0,r=r|0,a0(n,(r|0)<6,2918),r|0){case 0:{r=(e[n+496>>2]|0)==2?5:4;break}case 2:{r=(e[n+496>>2]|0)==2?4:5;break}default:}return _(k[n+424+(r<<2)>>2])}function Pf(n,r){switch(n=n|0,r=r|0,a0(n,(r|0)<6,2918),r|0){case 0:{r=(e[n+496>>2]|0)==2?5:4;break}case 2:{r=(e[n+496>>2]|0)==2?4:5;break}default:}return _(k[n+448+(r<<2)>>2])}function Of(n,r){switch(n=n|0,r=r|0,a0(n,(r|0)<6,2918),r|0){case 0:{r=(e[n+496>>2]|0)==2?5:4;break}case 2:{r=(e[n+496>>2]|0)==2?4:5;break}default:}return _(k[n+472+(r<<2)>>2])}function Nf(n,r){n=n|0,r=r|0;var t=0,u=x;return t=e[n+4>>2]|0,(t|0)==(e[r+4>>2]|0)?t?(u=_(k[n>>2]),n=_(H1(_(u-_(k[r>>2]))))<_(999999974e-13)):n=1:n=0,n|0}function B0(n,r){n=_(n),r=_(r);var t=0;return W(n)|0?t=W(r)|0:t=_(H1(_(n-r)))<_(999999974e-13),t|0}function jf(n,r){n=n|0,r=r|0,If(n,r)}function If(n,r){n=n|0,r=r|0;var t=0,u=0;t=s,s=s+16|0,u=t+4|0,e[u>>2]=0,e[u+4>>2]=0,e[u+8>>2]=0,Hu(u|0,n|0,r|0,0),A2(n,3,(w[u+11>>0]|0)<0?e[u>>2]|0:u,t),Ay(u),s=t}function T2(n,r,t,u){n=_(n),r=_(r),t=t|0,u=u|0;var o=x;n=_(n*r),o=_(ir(n,_(1)));do if(B0(o,_(0))|0)n=_(n-o);else{if(n=_(n-o),B0(o,_(1))|0){n=_(n+_(1));break}if(t){n=_(n+_(1));break}u||(o>_(.5)?o=_(1):(u=B0(o,_(.5))|0,o=_(u?1:0)),n=_(n+o))}while(!1);return _(n/r)}function Rr(n,r,t,u,o,f,l,c,v,m,d,g,M){n=n|0,r=_(r),t=t|0,u=_(u),o=o|0,f=_(f),l=l|0,c=_(c),v=_(v),m=_(m),d=_(d),g=_(g),M=M|0;var L=0,C=x,A=x,P=x,R=x,j=x,S=x;return v<_(0)|m<_(0)?M=0:(M|0&&(C=_(k[M+4>>2]),C!=_(0))?(P=_(T2(r,C,0,0)),R=_(T2(u,C,0,0)),A=_(T2(f,C,0,0)),C=_(T2(c,C,0,0))):(A=f,P=r,C=c,R=u),(o|0)==(n|0)?L=B0(A,P)|0:L=0,(l|0)==(t|0)?M=B0(C,R)|0:M=0,!L&&(j=_(r-d),!(Pr(n,j,v)|0))&&!(Or(n,j,o,v)|0)?L=Nr(n,j,o,f,v)|0:L=1,!M&&(S=_(u-g),!(Pr(t,S,m)|0))&&!(Or(t,S,l,m)|0)?M=Nr(t,S,l,c,m)|0:M=1,M=L&M),M|0}function Pr(n,r,t){return n=n|0,r=_(r),t=_(t),(n|0)==1?n=B0(r,t)|0:n=0,n|0}function Or(n,r,t,u){return n=n|0,r=_(r),t=t|0,u=_(u),(n|0)==2&(t|0)==0?r>=u?n=1:n=B0(r,u)|0:n=0,n|0}function Nr(n,r,t,u,o){return n=n|0,r=_(r),t=t|0,u=_(u),o=_(o),(n|0)==2&(t|0)==2&u>r?o<=r?n=1:n=B0(r,o)|0:n=0,n|0}function T1(n,r,t,u,o,f,l,c,v,m,d){n=n|0,r=_(r),t=_(t),u=u|0,o=o|0,f=f|0,l=_(l),c=_(c),v=v|0,m=m|0,d=d|0;var g=0,M=0,L=0,C=0,A=x,P=x,R=0,j=0,S=0,E=0,U=0,t0=0,K=0,Y=0,a=0,r0=0,G=0,m0=x,H0=x,U0=x,q0=0,Z0=0;G=s,s=s+160|0,Y=G+152|0,K=G+120|0,t0=G+104|0,S=G+72|0,C=G+56|0,U=G+8|0,j=G,E=(e[2279]|0)+1|0,e[2279]=E,a=n+984|0,w[a>>0]|0&&(e[n+512>>2]|0)!=(e[2278]|0)?R=4:(e[n+516>>2]|0)==(u|0)?r0=0:R=4,(R|0)==4&&(e[n+520>>2]=0,e[n+924>>2]=-1,e[n+928>>2]=-1,k[n+932>>2]=_(-1),k[n+936>>2]=_(-1),r0=1);n:do if(e[n+964>>2]|0)if(A=_(p0(n,2,l)),P=_(p0(n,0,l)),g=n+916|0,U0=_(k[g>>2]),H0=_(k[n+920>>2]),m0=_(k[n+932>>2]),Rr(o,r,f,t,e[n+924>>2]|0,U0,e[n+928>>2]|0,H0,m0,_(k[n+936>>2]),A,P,d)|0)R=22;else if(L=e[n+520>>2]|0,!L)R=21;else for(M=0;;){if(g=n+524+(M*24|0)|0,m0=_(k[g>>2]),H0=_(k[n+524+(M*24|0)+4>>2]),U0=_(k[n+524+(M*24|0)+16>>2]),Rr(o,r,f,t,e[n+524+(M*24|0)+8>>2]|0,m0,e[n+524+(M*24|0)+12>>2]|0,H0,U0,_(k[n+524+(M*24|0)+20>>2]),A,P,d)|0){R=22;break n}if(M=M+1|0,M>>>0>=L>>>0){R=21;break}}else{if(v){if(g=n+916|0,!(B0(_(k[g>>2]),r)|0)){R=21;break}if(!(B0(_(k[n+920>>2]),t)|0)){R=21;break}if((e[n+924>>2]|0)!=(o|0)){R=21;break}g=(e[n+928>>2]|0)==(f|0)?g:0,R=22;break}if(L=e[n+520>>2]|0,!L)R=21;else for(M=0;;){if(g=n+524+(M*24|0)|0,B0(_(k[g>>2]),r)|0&&B0(_(k[n+524+(M*24|0)+4>>2]),t)|0&&(e[n+524+(M*24|0)+8>>2]|0)==(o|0)&&(e[n+524+(M*24|0)+12>>2]|0)==(f|0)){R=22;break n}if(M=M+1|0,M>>>0>=L>>>0){R=21;break}}}while(!1);do if((R|0)==21)w[11697]|0?(g=0,R=28):(g=0,R=31);else if((R|0)==22){if(M=(w[11697]|0)!=0,!((g|0)!=0&(r0^1)))if(M){R=28;break}else{R=31;break}C=g+16|0,e[n+908>>2]=e[C>>2],L=g+20|0,e[n+912>>2]=e[L>>2],(w[11698]|0)==0|M^1||(e[j>>2]=Yn(E)|0,e[j+4>>2]=E,A2(n,4,2972,j),M=e[n+972>>2]|0,M|0&&K2[M&127](n),o=q1(o,v)|0,f=q1(f,v)|0,Z0=+_(k[C>>2]),q0=+_(k[L>>2]),e[U>>2]=o,e[U+4>>2]=f,O[U+8>>3]=+r,O[U+16>>3]=+t,O[U+24>>3]=Z0,O[U+32>>3]=q0,e[U+40>>2]=m,A2(n,4,2989,U))}while(!1);return(R|0)==28&&(M=Yn(E)|0,e[C>>2]=M,e[C+4>>2]=E,e[C+8>>2]=r0?3047:11699,A2(n,4,3038,C),M=e[n+972>>2]|0,M|0&&K2[M&127](n),U=q1(o,v)|0,R=q1(f,v)|0,e[S>>2]=U,e[S+4>>2]=R,O[S+8>>3]=+r,O[S+16>>3]=+t,e[S+24>>2]=m,A2(n,4,3049,S),R=31),(R|0)==31&&(Ff(n,r,t,u,o,f,l,c,v,d),w[11697]|0&&(M=e[2279]|0,U=Yn(M)|0,e[t0>>2]=U,e[t0+4>>2]=M,e[t0+8>>2]=r0?3047:11699,A2(n,4,3083,t0),M=e[n+972>>2]|0,M|0&&K2[M&127](n),U=q1(o,v)|0,t0=q1(f,v)|0,q0=+_(k[n+908>>2]),Z0=+_(k[n+912>>2]),e[K>>2]=U,e[K+4>>2]=t0,O[K+8>>3]=q0,O[K+16>>3]=Z0,e[K+24>>2]=m,A2(n,4,3092,K)),e[n+516>>2]=u,g||(M=n+520|0,g=e[M>>2]|0,(g|0)==16&&(w[11697]|0&&A2(n,4,3124,Y),e[M>>2]=0,g=0),v?g=n+916|0:(e[M>>2]=g+1,g=n+524+(g*24|0)|0),k[g>>2]=r,k[g+4>>2]=t,e[g+8>>2]=o,e[g+12>>2]=f,e[g+16>>2]=e[n+908>>2],e[g+20>>2]=e[n+912>>2],g=0)),v&&(e[n+416>>2]=e[n+908>>2],e[n+420>>2]=e[n+912>>2],w[n+985>>0]=1,w[a>>0]=0),e[2279]=(e[2279]|0)+-1,e[n+512>>2]=e[2278],s=G,r0|(g|0)==0|0}function p0(n,r,t){n=n|0,r=r|0,t=_(t);var u=x;return u=_(t2(n,r,t)),_(u+_(D2(n,r,t)))}function A2(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0;f=s,s=s+16|0,o=f,e[o>>2]=u,n?u=e[n+976>>2]|0:u=0,xr(u,n,r,t,o),s=f}function Yn(n){return n=n|0,(n>>>0>60?3201:3201+(60-n)|0)|0}function q1(n,r){n=n|0,r=r|0;var t=0,u=0,o=0;return o=s,s=s+32|0,t=o+12|0,u=o,e[t>>2]=e[254],e[t+4>>2]=e[255],e[t+8>>2]=e[256],e[u>>2]=e[257],e[u+4>>2]=e[258],e[u+8>>2]=e[259],(n|0)>2?n=11699:n=e[(r?u:t)+(n<<2)>>2]|0,s=o,n|0}function Ff(n,r,t,u,o,f,l,c,v,m){n=n|0,r=_(r),t=_(t),u=u|0,o=o|0,f=f|0,l=_(l),c=_(c),v=v|0,m=m|0;var d=0,g=0,M=0,L=0,C=x,A=x,P=x,R=x,j=x,S=x,E=x,U=0,t0=0,K=0,Y=x,a=x,r0=0,G=x,m0=0,H0=0,U0=0,q0=0,Z0=0,h1=0,d1=0,f2=0,m1=0,R2=0,P2=0,p1=0,w1=0,g1=0,s0=0,l2=0,y1=0,p2=0,k1=x,M1=x,O2=x,N2=x,w2=x,j0=0,i2=0,V0=0,c2=0,J2=0,Z2=x,j2=x,$2=x,Q2=x,I0=x,O0=x,s2=0,w0=x,a2=x,z0=x,g2=x,W0=x,y2=x,b2=0,n1=0,k2=x,F0=x,v2=0,e1=0,r1=0,i1=0,b=x,M0=0,N0=0,Y0=0,x0=0,n0=0,X=0,_2=0,z=x,t1=0,A0=0;_2=s,s=s+16|0,j0=_2+12|0,i2=_2+8|0,V0=_2+4|0,c2=_2,a0(n,(o|0)==0|(W(r)|0)^1,3326),a0(n,(f|0)==0|(W(t)|0)^1,3406),N0=Ur(n,u)|0,e[n+496>>2]=N0,n0=A1(2,N0)|0,X=A1(0,N0)|0,k[n+440>>2]=_(t2(n,n0,l)),k[n+444>>2]=_(D2(n,n0,l)),k[n+428>>2]=_(t2(n,X,l)),k[n+436>>2]=_(D2(n,X,l)),k[n+464>>2]=_(H2(n,n0)),k[n+468>>2]=_(C1(n,n0)),k[n+452>>2]=_(H2(n,X)),k[n+460>>2]=_(C1(n,X)),k[n+488>>2]=_(Vn(n,n0,l)),k[n+492>>2]=_(Gn(n,n0,l)),k[n+476>>2]=_(Vn(n,X,l)),k[n+484>>2]=_(Gn(n,X,l));do if(e[n+964>>2]|0)Vf(n,r,t,o,f,l,c);else{if(Y0=n+948|0,x0=(e[n+952>>2]|0)-(e[Y0>>2]|0)>>2,!x0){Gf(n,r,t,o,f,l,c);break}if(!v&&Kf(n,r,t,o,f,l,c)|0)break;Wn(n),l2=n+508|0,w[l2>>0]=0,n0=A1(e[n+4>>2]|0,N0)|0,X=Kn(n0,N0)|0,M0=D0(n0)|0,y1=e[n+8>>2]|0,e1=n+28|0,p2=(e[e1>>2]|0)!=0,W0=M0?l:c,k2=M0?c:l,k1=_(Xn(n,n0,l)),M1=_(qr(n,n0,l)),C=_(Xn(n,X,l)),y2=_(d2(n,n0,l)),F0=_(d2(n,X,l)),K=M0?o:f,v2=M0?f:o,b=M0?y2:F0,j=M0?F0:y2,g2=_(p0(n,2,l)),R=_(p0(n,0,l)),A=_(_(_0(n+364|0,l))-b),P=_(_(_0(n+380|0,l))-b),S=_(_(_0(n+372|0,c))-j),E=_(_(_0(n+388|0,c))-j),O2=M0?A:S,N2=M0?P:E,g2=_(r-g2),r=_(g2-b),W(r)|0?b=r:b=_(k0(_(F1(r,P)),A)),a2=_(t-R),r=_(a2-j),W(r)|0?z0=r:z0=_(k0(_(F1(r,E)),S)),A=M0?b:z0,w0=M0?z0:b;n:do if((K|0)==1)for(u=0,g=0;;){if(d=x2(n,g)|0,!u)_(z1(d))>_(0)&&_(rn(d))>_(0)?u=d:u=0;else if(zr(d)|0){L=0;break n}if(g=g+1|0,g>>>0>=x0>>>0){L=u;break}}else L=0;while(!1);U=L+500|0,t0=L+504|0,u=0,d=0,r=_(0),M=0;do{if(g=e[(e[Y0>>2]|0)+(M<<2)>>2]|0,(e[g+36>>2]|0)==1)Wr(g),w[g+985>>0]=1,w[g+984>>0]=0;else{jr(g),v&&Ir(g,Ur(g,N0)|0,A,w0,b);do if((e[g+24>>2]|0)!=1)if((g|0)==(L|0)){e[U>>2]=e[2278],k[t0>>2]=_(0);break}else{Xf(n,g,b,o,z0,b,z0,f,N0,m);break}else d|0&&(e[d+960>>2]=g),e[g+960>>2]=0,d=g,u=u|0?u:g;while(!1);O0=_(k[g+504>>2]),r=_(r+_(O0+_(p0(g,n0,b))))}M=M+1|0}while((M|0)!=(x0|0));for(U0=r>A,s2=p2&((K|0)==2&U0)?1:K,m0=(v2|0)==1,Z0=m0&(v^1),h1=(s2|0)==1,d1=(s2|0)==2,f2=976+(n0<<2)|0,m1=(v2|2|0)==2,g1=m0&(p2^1),R2=1040+(X<<2)|0,P2=1040+(n0<<2)|0,p1=976+(X<<2)|0,w1=(v2|0)!=1,U0=p2&((K|0)!=0&U0),H0=n+976|0,m0=m0^1,r=A,r0=0,q0=0,O0=_(0),w2=_(0);;){n:do if(r0>>>0<x0>>>0)for(t0=e[Y0>>2]|0,M=0,E=_(0),S=_(0),P=_(0),A=_(0),g=0,d=0,L=r0;;){if(U=e[t0+(L<<2)>>2]|0,(e[U+36>>2]|0)!=1&&(e[U+940>>2]=q0,(e[U+24>>2]|0)!=1)){if(R=_(p0(U,n0,b)),s0=e[f2>>2]|0,t=_(_0(U+380+(s0<<3)|0,W0)),j=_(k[U+504>>2]),t=_(F1(t,j)),t=_(k0(_(_0(U+364+(s0<<3)|0,W0)),t)),p2&(M|0)!=0&_(R+_(S+t))>r){f=M,R=E,K=L;break n}R=_(R+t),t=_(S+R),R=_(E+R),zr(U)|0&&(P=_(P+_(z1(U))),A=_(A-_(j*_(rn(U))))),d|0&&(e[d+960>>2]=U),e[U+960>>2]=0,M=M+1|0,d=U,g=g|0?g:U}else R=E,t=S;if(L=L+1|0,L>>>0<x0>>>0)E=R,S=t;else{f=M,K=L;break}}else f=0,R=_(0),P=_(0),A=_(0),g=0,K=r0;while(!1);s0=P>_(0)&P<_(1),Y=s0?_(1):P,s0=A>_(0)&A<_(1),E=s0?_(1):A;do if(h1)s0=51;else if(R<O2&((W(O2)|0)^1))r=O2,s0=51;else if(R>N2&((W(N2)|0)^1))r=N2,s0=51;else if(w[(e[H0>>2]|0)+3>>0]|0)s0=51;else{if(Y!=_(0)&&_(z1(n))!=_(0)){s0=53;break}r=R,s0=53}while(!1);if((s0|0)==51&&(s0=0,W(r)|0?s0=53:(a=_(r-R),G=r)),(s0|0)==53&&(s0=0,R<_(0)?(a=_(-R),G=r):(a=_(0),G=r)),!Z0&&(J2=(g|0)==0,!J2)){M=e[f2>>2]|0,L=a<_(0),j=_(a/E),U=a>_(0),S=_(a/Y),P=_(0),R=_(0),r=_(0),d=g;do t=_(_0(d+380+(M<<3)|0,W0)),A=_(_0(d+364+(M<<3)|0,W0)),A=_(F1(t,_(k0(A,_(k[d+504>>2]))))),L?(t=_(A*_(rn(d))),t!=_(-0)&&(z=_(A-_(j*t)),Z2=_(C0(d,n0,z,G,b)),z!=Z2)&&(P=_(P-_(Z2-A)),r=_(r+t))):U&&(j2=_(z1(d)),j2!=_(0))&&(z=_(A+_(S*j2)),$2=_(C0(d,n0,z,G,b)),z!=$2)&&(P=_(P-_($2-A)),R=_(R-j2)),d=e[d+960>>2]|0;while(d|0);if(r=_(E+r),A=_(a+P),J2)r=_(0);else{j=_(Y+R),L=e[f2>>2]|0,U=A<_(0),t0=r==_(0),S=_(A/r),M=A>_(0),j=_(A/j),r=_(0);do{z=_(_0(g+380+(L<<3)|0,W0)),P=_(_0(g+364+(L<<3)|0,W0)),P=_(F1(z,_(k0(P,_(k[g+504>>2]))))),U?(z=_(P*_(rn(g))),A=_(-z),z!=_(-0)?(z=_(S*A),A=_(C0(g,n0,_(P+(t0?A:z)),G,b))):A=P):M&&(Q2=_(z1(g)),Q2!=_(0))?A=_(C0(g,n0,_(P+_(j*Q2)),G,b)):A=P,r=_(r-_(A-P)),R=_(p0(g,n0,b)),t=_(p0(g,X,b)),A=_(A+R),k[i2>>2]=A,e[c2>>2]=1,P=_(k[g+396>>2]);n:do if(W(P)|0){d=W(w0)|0;do if(!d){if(U0|(h2(g,X,w0)|0|m0)||(U2(n,g)|0)!=4||(e[(S1(g,X)|0)+4>>2]|0)==3||(e[(E1(g,X)|0)+4>>2]|0)==3)break;k[j0>>2]=w0,e[V0>>2]=1;break n}while(!1);if(h2(g,X,w0)|0){d=e[g+992+(e[p1>>2]<<2)>>2]|0,z=_(t+_(_0(d,w0))),k[j0>>2]=z,d=w1&(e[d+4>>2]|0)==2,e[V0>>2]=((W(z)|0|d)^1)&1;break}else{k[j0>>2]=w0,e[V0>>2]=d?0:2;break}}else z=_(A-R),Y=_(z/P),z=_(P*z),e[V0>>2]=1,k[j0>>2]=_(t+(M0?Y:z));while(!1);W1(g,n0,G,b,c2,i2),W1(g,X,w0,b,V0,j0);do if(!(h2(g,X,w0)|0)&&(U2(n,g)|0)==4){if((e[(S1(g,X)|0)+4>>2]|0)==3){d=0;break}d=(e[(E1(g,X)|0)+4>>2]|0)!=3}else d=0;while(!1);z=_(k[i2>>2]),Y=_(k[j0>>2]),t1=e[c2>>2]|0,A0=e[V0>>2]|0,T1(g,M0?z:Y,M0?Y:z,N0,M0?t1:A0,M0?A0:t1,b,z0,v&(d^1),3488,m)|0,w[l2>>0]=w[l2>>0]|w[g+508>>0],g=e[g+960>>2]|0}while(g|0)}}else r=_(0);if(r=_(a+r),A0=r<_(0)&1,w[l2>>0]=A0|B[l2>>0],d1&r>_(0)?(d=e[f2>>2]|0,e[n+364+(d<<3)+4>>2]|0&&(I0=_(_0(n+364+(d<<3)|0,W0)),I0>=_(0))?A=_(k0(_(0),_(I0-_(G-r)))):A=_(0)):A=r,U=r0>>>0<K>>>0,U){L=e[Y0>>2]|0,M=r0,d=0;do g=e[L+(M<<2)>>2]|0,e[g+24>>2]|0||(d=((e[(S1(g,n0)|0)+4>>2]|0)==3&1)+d|0,d=d+((e[(E1(g,n0)|0)+4>>2]|0)==3&1)|0),M=M+1|0;while((M|0)!=(K|0));d?(R=_(0),t=_(0)):s0=101}else s0=101;n:do if((s0|0)==101)switch(s0=0,y1|0){case 1:{d=0,R=_(A*_(.5)),t=_(0);break n}case 2:{d=0,R=A,t=_(0);break n}case 3:{if(f>>>0<=1){d=0,R=_(0),t=_(0);break n}t=_((f+-1|0)>>>0),d=0,R=_(0),t=_(_(k0(A,_(0)))/t);break n}case 5:{t=_(A/_((f+1|0)>>>0)),d=0,R=t;break n}case 4:{t=_(A/_(f>>>0)),d=0,R=_(t*_(.5));break n}default:{d=0,R=_(0),t=_(0);break n}}while(!1);if(r=_(k1+R),U){P=_(A/_(d|0)),M=e[Y0>>2]|0,g=r0,A=_(0);do{d=e[M+(g<<2)>>2]|0;n:do if((e[d+36>>2]|0)!=1){switch(e[d+24>>2]|0){case 1:{if(C2(d,n0)|0){if(!v)break n;z=_(Y1(d,n0,G)),z=_(z+_(H2(n,n0))),z=_(z+_(t2(d,n0,b))),k[d+400+(e[P2>>2]<<2)>>2]=z;break n}break}case 0:if(A0=(e[(S1(d,n0)|0)+4>>2]|0)==3,z=_(P+r),r=A0?z:r,v&&(A0=d+400+(e[P2>>2]<<2)|0,k[A0>>2]=_(r+_(k[A0>>2]))),A0=(e[(E1(d,n0)|0)+4>>2]|0)==3,z=_(P+r),r=A0?z:r,Z0){z=_(t+_(p0(d,n0,b))),A=w0,r=_(r+_(z+_(k[d+504>>2])));break n}else{r=_(r+_(t+_(Jn(d,n0,b)))),A=_(k0(A,_(Jn(d,X,b))));break n}default:}v&&(z=_(R+_(H2(n,n0))),A0=d+400+(e[P2>>2]<<2)|0,k[A0>>2]=_(z+_(k[A0>>2])))}while(!1);g=g+1|0}while((g|0)!=(K|0))}else A=_(0);if(t=_(M1+r),m1?R=_(_(C0(n,X,_(F0+A),k2,l))-F0):R=w0,P=_(_(C0(n,X,_(F0+(g1?w0:A)),k2,l))-F0),U&v){g=r0;do{M=e[(e[Y0>>2]|0)+(g<<2)>>2]|0;do if((e[M+36>>2]|0)!=1){if((e[M+24>>2]|0)==1){if(C2(M,X)|0){if(z=_(Y1(M,X,w0)),z=_(z+_(H2(n,X))),z=_(z+_(t2(M,X,b))),d=e[R2>>2]|0,k[M+400+(d<<2)>>2]=z,!(W(z)|0))break}else d=e[R2>>2]|0;z=_(H2(n,X)),k[M+400+(d<<2)>>2]=_(z+_(t2(M,X,b)));break}d=U2(n,M)|0;do if((d|0)==4){if((e[(S1(M,X)|0)+4>>2]|0)==3){s0=139;break}if((e[(E1(M,X)|0)+4>>2]|0)==3){s0=139;break}if(h2(M,X,w0)|0){r=C;break}t1=e[M+908+(e[f2>>2]<<2)>>2]|0,e[j0>>2]=t1,r=_(k[M+396>>2]),A0=W(r)|0,A=(e[D>>2]=t1,_(k[D>>2])),A0?r=P:(a=_(p0(M,X,b)),z=_(A/r),r=_(r*A),r=_(a+(M0?z:r))),k[i2>>2]=r,k[j0>>2]=_(_(p0(M,n0,b))+A),e[V0>>2]=1,e[c2>>2]=1,W1(M,n0,G,b,V0,j0),W1(M,X,w0,b,c2,i2),r=_(k[j0>>2]),a=_(k[i2>>2]),z=M0?r:a,r=M0?a:r,A0=((W(z)|0)^1)&1,T1(M,z,r,N0,A0,((W(r)|0)^1)&1,b,z0,1,3493,m)|0,r=C}else s0=139;while(!1);n:do if((s0|0)==139){s0=0,r=_(R-_(Jn(M,X,b)));do if((e[(S1(M,X)|0)+4>>2]|0)==3){if((e[(E1(M,X)|0)+4>>2]|0)!=3)break;r=_(C+_(k0(_(0),_(r*_(.5)))));break n}while(!1);if((e[(E1(M,X)|0)+4>>2]|0)==3){r=C;break}if((e[(S1(M,X)|0)+4>>2]|0)==3){r=_(C+_(k0(_(0),r)));break}switch(d|0){case 1:{r=C;break n}case 2:{r=_(C+_(r*_(.5)));break n}default:{r=_(C+r);break n}}}while(!1);z=_(O0+r),A0=M+400+(e[R2>>2]<<2)|0,k[A0>>2]=_(z+_(k[A0>>2]))}while(!1);g=g+1|0}while((g|0)!=(K|0))}if(O0=_(O0+P),w2=_(k0(w2,t)),f=q0+1|0,K>>>0>=x0>>>0)break;r=G,r0=K,q0=f}do if(v){if(d=f>>>0>1,!d&&!(Jf(n)|0))break;if(!(W(w0)|0)){r=_(w0-O0);n:do switch(e[n+12>>2]|0){case 3:{C=_(C+r),S=_(0);break}case 2:{C=_(C+_(r*_(.5))),S=_(0);break}case 4:{w0>O0?S=_(r/_(f>>>0)):S=_(0);break}case 7:if(w0>O0){C=_(C+_(r/_(f<<1>>>0))),S=_(r/_(f>>>0)),S=d?S:_(0);break n}else{C=_(C+_(r*_(.5))),S=_(0);break n}case 6:{S=_(r/_(q0>>>0)),S=w0>O0&d?S:_(0);break}default:S=_(0)}while(!1);if(f|0)for(U=1040+(X<<2)|0,t0=976+(X<<2)|0,L=0,g=0;;){n:do if(g>>>0<x0>>>0)for(A=_(0),P=_(0),r=_(0),M=g;;){d=e[(e[Y0>>2]|0)+(M<<2)>>2]|0;do if((e[d+36>>2]|0)!=1&&!(e[d+24>>2]|0)){if((e[d+940>>2]|0)!=(L|0))break n;if(Zf(d,X)|0&&(z=_(k[d+908+(e[t0>>2]<<2)>>2]),r=_(k0(r,_(z+_(p0(d,X,b)))))),(U2(n,d)|0)!=5)break;I0=_(Zn(d)),I0=_(I0+_(t2(d,0,b))),z=_(k[d+912>>2]),z=_(_(z+_(p0(d,0,b)))-I0),I0=_(k0(P,I0)),z=_(k0(A,z)),A=z,P=I0,r=_(k0(r,_(I0+z)))}while(!1);if(d=M+1|0,d>>>0<x0>>>0)M=d;else{M=d;break}}else P=_(0),r=_(0),M=g;while(!1);if(j=_(S+r),t=C,C=_(C+j),g>>>0<M>>>0){R=_(t+P),d=g;do{g=e[(e[Y0>>2]|0)+(d<<2)>>2]|0;n:do if((e[g+36>>2]|0)!=1&&!(e[g+24>>2]|0))switch(U2(n,g)|0){case 1:{z=_(t+_(t2(g,X,b))),k[g+400+(e[U>>2]<<2)>>2]=z;break n}case 3:{z=_(_(C-_(D2(g,X,b)))-_(k[g+908+(e[t0>>2]<<2)>>2])),k[g+400+(e[U>>2]<<2)>>2]=z;break n}case 2:{z=_(t+_(_(j-_(k[g+908+(e[t0>>2]<<2)>>2]))*_(.5))),k[g+400+(e[U>>2]<<2)>>2]=z;break n}case 4:{if(z=_(t+_(t2(g,X,b))),k[g+400+(e[U>>2]<<2)>>2]=z,h2(g,X,w0)|0||(M0?(A=_(k[g+908>>2]),r=_(A+_(p0(g,n0,b))),P=j):(P=_(k[g+912>>2]),P=_(P+_(p0(g,X,b))),r=j,A=_(k[g+908>>2])),B0(r,A)|0&&B0(P,_(k[g+912>>2]))|0))break n;T1(g,r,P,N0,1,1,b,z0,1,3501,m)|0;break n}case 5:{k[g+404>>2]=_(_(R-_(Zn(g)))+_(Y1(g,0,w0)));break n}default:break n}while(!1);d=d+1|0}while((d|0)!=(M|0))}if(L=L+1|0,(L|0)==(f|0))break;g=M}}}while(!1);if(k[n+908>>2]=_(C0(n,2,g2,l,l)),k[n+912>>2]=_(C0(n,0,a2,c,l)),s2|0&&(b2=e[n+32>>2]|0,n1=(s2|0)==2,!(n1&(b2|0)!=2))?n1&(b2|0)==2&&(r=_(y2+G),r=_(k0(_(F1(r,_($n(n,n0,w2,W0)))),y2)),s0=198):(r=_(C0(n,n0,w2,W0,l)),s0=198),(s0|0)==198&&(k[n+908+(e[976+(n0<<2)>>2]<<2)>>2]=r),v2|0&&(r1=e[n+32>>2]|0,i1=(v2|0)==2,!(i1&(r1|0)!=2))?i1&(r1|0)==2&&(r=_(F0+w0),r=_(k0(_(F1(r,_($n(n,X,_(F0+O0),k2)))),F0)),s0=204):(r=_(C0(n,X,_(F0+O0),k2,l)),s0=204),(s0|0)==204&&(k[n+908+(e[976+(X<<2)>>2]<<2)>>2]=r),v){if((e[e1>>2]|0)==2){g=976+(X<<2)|0,M=1040+(X<<2)|0,d=0;do L=x2(n,d)|0,e[L+24>>2]|0||(t1=e[g>>2]|0,z=_(k[n+908+(t1<<2)>>2]),A0=L+400+(e[M>>2]<<2)|0,z=_(z-_(k[A0>>2])),k[A0>>2]=_(z-_(k[L+908+(t1<<2)>>2]))),d=d+1|0;while((d|0)!=(x0|0))}if(u|0){d=M0?s2:o;do $f(n,u,b,d,z0,N0,m),u=e[u+960>>2]|0;while(u|0)}if(d=(n0|2|0)==3,g=(X|2|0)==3,d|g){u=0;do M=e[(e[Y0>>2]|0)+(u<<2)>>2]|0,(e[M+36>>2]|0)!=1&&(d&&Yr(n,M,n0),g&&Yr(n,M,X)),u=u+1|0;while((u|0)!=(x0|0))}}}while(!1);s=_2}function xf(n,r){n=n|0,r=_(r);var t=0;zn(n,r>=_(0),3147),t=r==_(0),k[n+4>>2]=t?_(0):r}function Df(n,r,t,u){n=n|0,r=_(r),t=_(t),u=u|0;var o=x,f=x,l=0,c=0,v=0;e[2278]=(e[2278]|0)+1,jr(n),h2(n,2,r)|0?(o=_(_0(e[n+992>>2]|0,r)),v=1,o=_(o+_(p0(n,2,r)))):(o=_(_0(n+380|0,r)),o>=_(0)?v=2:(v=((W(r)|0)^1)&1,o=r)),h2(n,0,t)|0?(f=_(_0(e[n+996>>2]|0,t)),c=1,f=_(f+_(p0(n,0,r)))):(f=_(_0(n+388|0,t)),f>=_(0)?c=2:(c=((W(t)|0)^1)&1,f=t)),l=n+976|0,T1(n,o,f,u,v,c,r,t,1,3189,e[l>>2]|0)|0&&(Ir(n,e[n+496>>2]|0,r,t,r),Fr(n,_(k[(e[l>>2]|0)+4>>2]),_(0),_(0)),w[11696]|0)&&jf(n,7)}function jr(n){n=n|0;var r=0,t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0;c=s,s=s+32|0,l=c+24|0,f=c+16|0,u=c+8|0,o=c,t=0;do r=n+380+(t<<3)|0,e[n+380+(t<<3)+4>>2]|0&&(v=r,m=e[v+4>>2]|0,d=u,e[d>>2]=e[v>>2],e[d+4>>2]=m,d=n+364+(t<<3)|0,m=e[d+4>>2]|0,v=o,e[v>>2]=e[d>>2],e[v+4>>2]=m,e[f>>2]=e[u>>2],e[f+4>>2]=e[u+4>>2],e[l>>2]=e[o>>2],e[l+4>>2]=e[o+4>>2],Nf(f,l)|0)||(r=n+348+(t<<3)|0),e[n+992+(t<<2)>>2]=r,t=t+1|0;while((t|0)!=2);s=c}function h2(n,r,t){n=n|0,r=r|0,t=_(t);var u=0;switch(n=e[n+992+(e[976+(r<<2)>>2]<<2)>>2]|0,e[n+4>>2]|0){case 0:case 3:{n=0;break}case 1:{_(k[n>>2])<_(0)?n=0:u=5;break}case 2:{_(k[n>>2])<_(0)?n=0:n=(W(t)|0)^1;break}default:u=5}return(u|0)==5&&(n=1),n|0}function _0(n,r){switch(n=n|0,r=_(r),e[n+4>>2]|0){case 2:{r=_(_(_(k[n>>2])*r)/_(100));break}case 1:{r=_(k[n>>2]);break}default:r=_(y0)}return _(r)}function Ir(n,r,t,u,o){n=n|0,r=r|0,t=_(t),u=_(u),o=_(o);var f=0,l=x;r=e[n+944>>2]|0?r:1,f=A1(e[n+4>>2]|0,r)|0,r=Kn(f,r)|0,t=_(Gr(n,f,t)),u=_(Gr(n,r,u)),l=_(t+_(t2(n,f,o))),k[n+400+(e[1040+(f<<2)>>2]<<2)>>2]=l,t=_(t+_(D2(n,f,o))),k[n+400+(e[1e3+(f<<2)>>2]<<2)>>2]=t,t=_(u+_(t2(n,r,o))),k[n+400+(e[1040+(r<<2)>>2]<<2)>>2]=t,o=_(u+_(D2(n,r,o))),k[n+400+(e[1e3+(r<<2)>>2]<<2)>>2]=o}function Fr(n,r,t,u){n=n|0,r=_(r),t=_(t),u=_(u);var o=0,f=0,l=x,c=x,v=0,m=0,d=x,g=0,M=x,L=x,C=x,A=x;if(r!=_(0)&&(o=n+400|0,A=_(k[o>>2]),f=n+404|0,C=_(k[f>>2]),g=n+416|0,L=_(k[g>>2]),m=n+420|0,l=_(k[m>>2]),M=_(A+t),d=_(C+u),u=_(M+L),c=_(d+l),v=(e[n+988>>2]|0)==1,k[o>>2]=_(T2(A,r,0,v)),k[f>>2]=_(T2(C,r,0,v)),t=_(ir(_(L*r),_(1))),B0(t,_(0))|0?f=0:f=(B0(t,_(1))|0)^1,t=_(ir(_(l*r),_(1))),B0(t,_(0))|0?o=0:o=(B0(t,_(1))|0)^1,A=_(T2(u,r,v&f,v&(f^1))),k[g>>2]=_(A-_(T2(M,r,0,v))),A=_(T2(c,r,v&o,v&(o^1))),k[m>>2]=_(A-_(T2(d,r,0,v))),f=(e[n+952>>2]|0)-(e[n+948>>2]|0)>>2,f|0)){o=0;do Fr(x2(n,o)|0,r,M,d),o=o+1|0;while((o|0)!=(f|0))}}function Hf(n,r,t,u,o){switch(n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,t|0){case 5:case 0:{n=tu(e[489]|0,u,o)|0;break}default:n=yy(u,o)|0}return n|0}function Uf(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0;o=s,s=s+16|0,f=o,e[f>>2]=u,xr(n,0,r,t,f),s=o}function xr(n,r,t,u,o){if(n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,n=n|0?n:956,Su[e[n+8>>2]&1](n,r,t,u,o)|0,(t|0)==5)u0();else return}function qf(n,r,t){n=n|0,r=r|0,t=t|0,w[n+r>>0]=t&1}function zf(n,r){n=n|0,r=r|0;var t=0,u=0;e[n>>2]=0,e[n+4>>2]=0,e[n+8>>2]=0,t=r+4|0,u=(e[t>>2]|0)-(e[r>>2]|0)>>2,u|0&&(Wf(n,u),Yf(n,e[r>>2]|0,e[t>>2]|0,u))}function Wf(n,r){n=n|0,r=r|0;var t=0;if((Dr(n)|0)>>>0<r>>>0&&c0(n),r>>>0>1073741823)u0();else{t=q(r<<2)|0,e[n+4>>2]=t,e[n>>2]=t,e[n+8>>2]=t+(r<<2);return}}function Yf(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0,u=n+4|0,n=t-r|0,(n|0)>0&&(Q(e[u>>2]|0,r|0,n|0)|0,e[u>>2]=(e[u>>2]|0)+(n>>>2<<2))}function Dr(n){return n=n|0,1073741823}function t2(n,r,t){return n=n|0,r=r|0,t=_(t),D0(r)|0&&e[n+96>>2]|0?n=n+92|0:n=K0(n+60|0,e[1040+(r<<2)>>2]|0,992)|0,_(Hr(n,t))}function D2(n,r,t){return n=n|0,r=r|0,t=_(t),D0(r)|0&&e[n+104>>2]|0?n=n+100|0:n=K0(n+60|0,e[1e3+(r<<2)>>2]|0,992)|0,_(Hr(n,t))}function D0(n){return n=n|0,(n|1|0)==3|0}function Hr(n,r){return n=n|0,r=_(r),(e[n+4>>2]|0)==3?r=_(0):r=_(_0(n,r)),_(r)}function Ur(n,r){return n=n|0,r=r|0,n=e[n>>2]|0,(n|0?n:(r|0)>1?r:1)|0}function A1(n,r){n=n|0,r=r|0;var t=0;n:do if((r|0)==2){switch(n|0){case 2:{n=3;break n}case 3:break;default:{t=4;break n}}n=2}else t=4;while(!1);return n|0}function H2(n,r){n=n|0,r=r|0;var t=x;return D0(r)|0&&e[n+312>>2]|0&&(t=_(k[n+308>>2]),t>=_(0))||(t=_(k0(_(k[(K0(n+276|0,e[1040+(r<<2)>>2]|0,992)|0)>>2]),_(0)))),_(t)}function C1(n,r){n=n|0,r=r|0;var t=x;return D0(r)|0&&e[n+320>>2]|0&&(t=_(k[n+316>>2]),t>=_(0))||(t=_(k0(_(k[(K0(n+276|0,e[1e3+(r<<2)>>2]|0,992)|0)>>2]),_(0)))),_(t)}function Vn(n,r,t){n=n|0,r=r|0,t=_(t);var u=x;return D0(r)|0&&e[n+240>>2]|0&&(u=_(_0(n+236|0,t)),u>=_(0))||(u=_(k0(_(_0(K0(n+204|0,e[1040+(r<<2)>>2]|0,992)|0,t)),_(0)))),_(u)}function Gn(n,r,t){n=n|0,r=r|0,t=_(t);var u=x;return D0(r)|0&&e[n+248>>2]|0&&(u=_(_0(n+244|0,t)),u>=_(0))||(u=_(k0(_(_0(K0(n+204|0,e[1e3+(r<<2)>>2]|0,992)|0,t)),_(0)))),_(u)}function Vf(n,r,t,u,o,f,l){n=n|0,r=_(r),t=_(t),u=u|0,o=o|0,f=_(f),l=_(l);var c=x,v=x,m=x,d=x,g=x,M=x,L=0,C=0,A=0;A=s,s=s+16|0,L=A,C=n+964|0,a0(n,(e[C>>2]|0)!=0,3519),c=_(d2(n,2,r)),v=_(d2(n,0,r)),m=_(p0(n,2,r)),d=_(p0(n,0,r)),W(r)|0?g=r:g=_(k0(_(0),_(_(r-m)-c))),W(t)|0?M=t:M=_(k0(_(0),_(_(t-d)-v))),(u|0)==1&(o|0)==1?(k[n+908>>2]=_(C0(n,2,_(r-m),f,f)),r=_(C0(n,0,_(t-d),l,f))):(Eu[e[C>>2]&1](L,n,g,u,M,o),g=_(c+_(k[L>>2])),M=_(r-m),k[n+908>>2]=_(C0(n,2,(u|2|0)==2?g:M,f,f)),M=_(v+_(k[L+4>>2])),r=_(t-d),r=_(C0(n,0,(o|2|0)==2?M:r,l,f))),k[n+912>>2]=r,s=A}function Gf(n,r,t,u,o,f,l){n=n|0,r=_(r),t=_(t),u=u|0,o=o|0,f=_(f),l=_(l);var c=x,v=x,m=x,d=x;m=_(d2(n,2,f)),c=_(d2(n,0,f)),d=_(p0(n,2,f)),v=_(p0(n,0,f)),r=_(r-d),k[n+908>>2]=_(C0(n,2,(u|2|0)==2?m:r,f,f)),t=_(t-v),k[n+912>>2]=_(C0(n,0,(o|2|0)==2?c:t,l,f))}function Kf(n,r,t,u,o,f,l){n=n|0,r=_(r),t=_(t),u=u|0,o=o|0,f=_(f),l=_(l);var c=0,v=x,m=x;return c=(u|0)==2,!(r<=_(0)&c)&&!(t<=_(0)&(o|0)==2)&&!((u|0)==1&(o|0)==1)?n=0:(v=_(p0(n,0,f)),m=_(p0(n,2,f)),c=r<_(0)&c|(W(r)|0),r=_(r-m),k[n+908>>2]=_(C0(n,2,c?_(0):r,f,f)),r=_(t-v),c=t<_(0)&(o|0)==2|(W(t)|0),k[n+912>>2]=_(C0(n,0,c?_(0):r,l,f)),n=1),n|0}function Kn(n,r){return n=n|0,r=r|0,wn(n)|0?n=A1(2,r)|0:n=0,n|0}function Xn(n,r,t){return n=n|0,r=r|0,t=_(t),t=_(Vn(n,r,t)),_(t+_(H2(n,r)))}function qr(n,r,t){return n=n|0,r=r|0,t=_(t),t=_(Gn(n,r,t)),_(t+_(C1(n,r)))}function d2(n,r,t){n=n|0,r=r|0,t=_(t);var u=x;return u=_(Xn(n,r,t)),_(u+_(qr(n,r,t)))}function zr(n){return n=n|0,e[n+24>>2]|0?n=0:_(z1(n))!=_(0)?n=1:n=_(rn(n))!=_(0),n|0}function z1(n){n=n|0;var r=x;if(e[n+944>>2]|0){if(r=_(k[n+44>>2]),W(r)|0)return r=_(k[n+40>>2]),n=r>_(0)&((W(r)|0)^1),_(n?r:_(0))}else r=_(0);return _(r)}function rn(n){n=n|0;var r=x,t=0,u=x;do if(e[n+944>>2]|0){if(r=_(k[n+48>>2]),W(r)|0){if(t=w[(e[n+976>>2]|0)+2>>0]|0,!(t<<24>>24)&&(u=_(k[n+40>>2]),u<_(0)&((W(u)|0)^1))){r=_(-u);break}r=t<<24>>24?_(1):_(0)}}else r=_(0);while(!1);return _(r)}function Wr(n){n=n|0;var r=0,t=0;if(a1(n+400|0,0,540)|0,w[n+985>>0]=1,Wn(n),t=F2(n)|0,t|0){r=n+948|0,n=0;do Wr(e[(e[r>>2]|0)+(n<<2)>>2]|0),n=n+1|0;while((n|0)!=(t|0))}}function Xf(n,r,t,u,o,f,l,c,v,m){n=n|0,r=r|0,t=_(t),u=u|0,o=_(o),f=_(f),l=_(l),c=c|0,v=v|0,m=m|0;var d=0,g=x,M=0,L=0,C=x,A=x,P=0,R=x,j=0,S=x,E=0,U=0,t0=0,K=0,Y=0,a=0,r0=0,G=0,m0=0,H0=0;m0=s,s=s+16|0,t0=m0+12|0,K=m0+8|0,Y=m0+4|0,a=m0,G=A1(e[n+4>>2]|0,v)|0,E=D0(G)|0,g=_(_0(Qf(r)|0,E?f:l)),U=h2(r,2,f)|0,r0=h2(r,0,l)|0;do if(!(W(g)|0)&&!(W(E?t:o)|0)){if(d=r+504|0,!(W(_(k[d>>2]))|0)&&(!(Vr(e[r+976>>2]|0,0)|0)||(e[r+500>>2]|0)==(e[2278]|0)))break;k[d>>2]=_(k0(g,_(d2(r,G,f))))}else M=7;while(!1);do if((M|0)==7){if(j=E^1,!(j|U^1)){l=_(_0(e[r+992>>2]|0,f)),k[r+504>>2]=_(k0(l,_(d2(r,2,f))));break}if(!(E|r0^1)){l=_(_0(e[r+996>>2]|0,l)),k[r+504>>2]=_(k0(l,_(d2(r,0,f))));break}k[t0>>2]=_(y0),k[K>>2]=_(y0),e[Y>>2]=0,e[a>>2]=0,R=_(p0(r,2,f)),S=_(p0(r,0,f)),U?(C=_(R+_(_0(e[r+992>>2]|0,f))),k[t0>>2]=C,e[Y>>2]=1,L=1):(L=0,C=_(y0)),r0?(g=_(S+_(_0(e[r+996>>2]|0,l))),k[K>>2]=g,e[a>>2]=1,d=1):(d=0,g=_(y0)),M=e[n+32>>2]|0,E&(M|0)==2?M=2:W(C)|0&&!(W(t)|0)&&(k[t0>>2]=t,e[Y>>2]=2,L=2,C=t),!((M|0)==2&j)&&W(g)|0&&!(W(o)|0)&&(k[K>>2]=o,e[a>>2]=2,d=2,g=o),A=_(k[r+396>>2]),P=W(A)|0;do if(P)M=L;else{if((L|0)==1&j){k[K>>2]=_(_(C-R)/A),e[a>>2]=1,d=1,M=1;break}E&(d|0)==1?(k[t0>>2]=_(A*_(g-S)),e[Y>>2]=1,d=1,M=1):M=L}while(!1);H0=W(t)|0,L=(U2(n,r)|0)!=4,!(E|U|((u|0)!=1|H0)|(L|(M|0)==1))&&(k[t0>>2]=t,e[Y>>2]=1,!P)&&(k[K>>2]=_(_(t-R)/A),e[a>>2]=1,d=1),!(r0|j|((c|0)!=1|(W(o)|0))|(L|(d|0)==1))&&(k[K>>2]=o,e[a>>2]=1,!P)&&(k[t0>>2]=_(A*_(o-S)),e[Y>>2]=1),W1(r,2,f,f,Y,t0),W1(r,0,l,f,a,K),t=_(k[t0>>2]),o=_(k[K>>2]),T1(r,t,o,v,e[Y>>2]|0,e[a>>2]|0,f,l,0,3565,m)|0,l=_(k[r+908+(e[976+(G<<2)>>2]<<2)>>2]),k[r+504>>2]=_(k0(l,_(d2(r,G,f))))}while(!1);e[r+500>>2]=e[2278],s=m0}function C0(n,r,t,u,o){return n=n|0,r=r|0,t=_(t),u=_(u),o=_(o),u=_($n(n,r,t,u)),_(k0(u,_(d2(n,r,o))))}function U2(n,r){return n=n|0,r=r|0,r=r+20|0,r=e[(e[r>>2]|0?r:n+16|0)>>2]|0,(r|0)==5&&wn(e[n+4>>2]|0)|0&&(r=1),r|0}function S1(n,r){return n=n|0,r=r|0,D0(r)|0&&e[n+96>>2]|0?r=4:r=e[1040+(r<<2)>>2]|0,n+60+(r<<3)|0}function E1(n,r){return n=n|0,r=r|0,D0(r)|0&&e[n+104>>2]|0?r=5:r=e[1e3+(r<<2)>>2]|0,n+60+(r<<3)|0}function W1(n,r,t,u,o,f){switch(n=n|0,r=r|0,t=_(t),u=_(u),o=o|0,f=f|0,t=_(_0(n+380+(e[976+(r<<2)>>2]<<3)|0,t)),t=_(t+_(p0(n,r,u))),e[o>>2]|0){case 2:case 1:{o=W(t)|0,u=_(k[f>>2]),k[f>>2]=o|u<t?u:t;break}case 0:{W(t)|0||(e[o>>2]=2,k[f>>2]=t);break}default:}}function C2(n,r){return n=n|0,r=r|0,n=n+132|0,D0(r)|0&&e[(K0(n,4,948)|0)+4>>2]|0?n=1:n=(e[(K0(n,e[1040+(r<<2)>>2]|0,948)|0)+4>>2]|0)!=0,n|0}function Y1(n,r,t){n=n|0,r=r|0,t=_(t);var u=0,o=0;return n=n+132|0,D0(r)|0&&(u=K0(n,4,948)|0,(e[u+4>>2]|0)!=0)?o=4:(u=K0(n,e[1040+(r<<2)>>2]|0,948)|0,e[u+4>>2]|0?o=4:t=_(0)),(o|0)==4&&(t=_(_0(u,t))),_(t)}function Jn(n,r,t){n=n|0,r=r|0,t=_(t);var u=x;return u=_(k[n+908+(e[976+(r<<2)>>2]<<2)>>2]),u=_(u+_(t2(n,r,t))),_(u+_(D2(n,r,t)))}function Jf(n){n=n|0;var r=0,t=0,u=0;n:do if(wn(e[n+4>>2]|0)|0)r=0;else if((e[n+16>>2]|0)!=5)if(t=F2(n)|0,!t)r=0;else for(r=0;;){if(u=x2(n,r)|0,!(e[u+24>>2]|0)&&(e[u+20>>2]|0)==5){r=1;break n}if(r=r+1|0,r>>>0>=t>>>0){r=0;break}}else r=1;while(!1);return r|0}function Zf(n,r){n=n|0,r=r|0;var t=x;return t=_(k[n+908+(e[976+(r<<2)>>2]<<2)>>2]),t>=_(0)&((W(t)|0)^1)|0}function Zn(n){n=n|0;var r=x,t=0,u=0,o=0,f=0,l=0,c=0,v=x;if(t=e[n+968>>2]|0,t)v=_(k[n+908>>2]),r=_(k[n+912>>2]),r=_(Mu[t&0](n,v,r)),a0(n,(W(r)|0)^1,3573);else{f=F2(n)|0;do if(f|0){for(t=0,o=0;;){if(u=x2(n,o)|0,e[u+940>>2]|0){l=8;break}if((e[u+24>>2]|0)!=1)if(c=(U2(n,u)|0)==5,c){t=u;break}else t=t|0?t:u;if(o=o+1|0,o>>>0>=f>>>0){l=8;break}}if((l|0)==8&&!t)break;return r=_(Zn(t)),_(r+_(k[t+404>>2]))}while(!1);r=_(k[n+912>>2])}return _(r)}function $n(n,r,t,u){n=n|0,r=r|0,t=_(t),u=_(u);var o=x,f=0;return wn(r)|0?(r=1,f=3):D0(r)|0?(r=0,f=3):(u=_(y0),o=_(y0)),(f|0)==3&&(o=_(_0(n+364+(r<<3)|0,u)),u=_(_0(n+380+(r<<3)|0,u))),f=u<t&(u>=_(0)&((W(u)|0)^1)),t=f?u:t,f=o>=_(0)&((W(o)|0)^1)&t<o,_(f?o:t)}function $f(n,r,t,u,o,f,l){n=n|0,r=r|0,t=_(t),u=u|0,o=_(o),f=f|0,l=l|0;var c=x,v=x,m=0,d=0,g=x,M=x,L=x,C=0,A=0,P=0,R=0,j=x,S=0;P=A1(e[n+4>>2]|0,f)|0,C=Kn(P,f)|0,A=D0(P)|0,g=_(p0(r,2,t)),M=_(p0(r,0,t)),h2(r,2,t)|0?c=_(g+_(_0(e[r+992>>2]|0,t))):C2(r,2)|0&&gn(r,2)|0?(c=_(k[n+908>>2]),v=_(H2(n,2)),v=_(c-_(v+_(C1(n,2)))),c=_(Y1(r,2,t)),c=_(C0(r,2,_(v-_(c+_(tn(r,2,t)))),t,t))):c=_(y0),h2(r,0,o)|0?v=_(M+_(_0(e[r+996>>2]|0,o))):C2(r,0)|0&&gn(r,0)|0?(v=_(k[n+912>>2]),j=_(H2(n,0)),j=_(v-_(j+_(C1(n,0)))),v=_(Y1(r,0,o)),v=_(C0(r,0,_(j-_(v+_(tn(r,0,o)))),o,t))):v=_(y0),m=W(c)|0,d=W(v)|0;do if(m^d&&(L=_(k[r+396>>2]),!(W(L)|0)))if(m){c=_(g+_(_(v-M)*L));break}else{j=_(M+_(_(c-g)/L)),v=d?j:v;break}while(!1);d=W(c)|0,m=W(v)|0,d|m&&(S=(d^1)&1,u=t>_(0)&((u|0)!=0&d),c=A?c:u?t:c,T1(r,c,v,f,A?S:u?2:S,d&(m^1)&1,c,v,0,3623,l)|0,c=_(k[r+908>>2]),c=_(c+_(p0(r,2,t))),v=_(k[r+912>>2]),v=_(v+_(p0(r,0,t)))),T1(r,c,v,f,1,1,c,v,1,3635,l)|0,gn(r,P)|0&&!(C2(r,P)|0)?(S=e[976+(P<<2)>>2]|0,j=_(k[n+908+(S<<2)>>2]),j=_(j-_(k[r+908+(S<<2)>>2])),j=_(j-_(C1(n,P))),j=_(j-_(D2(r,P,t))),j=_(j-_(tn(r,P,A?t:o))),k[r+400+(e[1040+(P<<2)>>2]<<2)>>2]=j):R=21;do if((R|0)==21){if(!(C2(r,P)|0)&&(e[n+8>>2]|0)==1){S=e[976+(P<<2)>>2]|0,j=_(k[n+908+(S<<2)>>2]),j=_(_(j-_(k[r+908+(S<<2)>>2]))*_(.5)),k[r+400+(e[1040+(P<<2)>>2]<<2)>>2]=j;break}!(C2(r,P)|0)&&(e[n+8>>2]|0)==2&&(S=e[976+(P<<2)>>2]|0,j=_(k[n+908+(S<<2)>>2]),j=_(j-_(k[r+908+(S<<2)>>2])),k[r+400+(e[1040+(P<<2)>>2]<<2)>>2]=j)}while(!1);gn(r,C)|0&&!(C2(r,C)|0)?(S=e[976+(C<<2)>>2]|0,j=_(k[n+908+(S<<2)>>2]),j=_(j-_(k[r+908+(S<<2)>>2])),j=_(j-_(C1(n,C))),j=_(j-_(D2(r,C,t))),j=_(j-_(tn(r,C,A?o:t))),k[r+400+(e[1040+(C<<2)>>2]<<2)>>2]=j):R=30;do if((R|0)==30&&!(C2(r,C)|0)){if((U2(n,r)|0)==2){S=e[976+(C<<2)>>2]|0,j=_(k[n+908+(S<<2)>>2]),j=_(_(j-_(k[r+908+(S<<2)>>2]))*_(.5)),k[r+400+(e[1040+(C<<2)>>2]<<2)>>2]=j;break}S=(U2(n,r)|0)==3,S^(e[n+28>>2]|0)==2&&(S=e[976+(C<<2)>>2]|0,j=_(k[n+908+(S<<2)>>2]),j=_(j-_(k[r+908+(S<<2)>>2])),k[r+400+(e[1040+(C<<2)>>2]<<2)>>2]=j)}while(!1)}function Yr(n,r,t){n=n|0,r=r|0,t=t|0;var u=x,o=0;o=e[976+(t<<2)>>2]|0,u=_(k[r+908+(o<<2)>>2]),u=_(_(k[n+908+(o<<2)>>2])-u),u=_(u-_(k[r+400+(e[1040+(t<<2)>>2]<<2)>>2])),k[r+400+(e[1e3+(t<<2)>>2]<<2)>>2]=u}function wn(n){return n=n|0,(n|1|0)==1|0}function Qf(n){n=n|0;var r=x;switch(e[n+56>>2]|0){case 0:case 3:{r=_(k[n+40>>2]),r>_(0)&((W(r)|0)^1)?n=w[(e[n+976>>2]|0)+2>>0]|0?1056:992:n=1056;break}default:n=n+52|0}return n|0}function Vr(n,r){return n=n|0,r=r|0,(w[n+r>>0]|0)!=0|0}function gn(n,r){return n=n|0,r=r|0,n=n+132|0,D0(r)|0&&e[(K0(n,5,948)|0)+4>>2]|0?n=1:n=(e[(K0(n,e[1e3+(r<<2)>>2]|0,948)|0)+4>>2]|0)!=0,n|0}function tn(n,r,t){n=n|0,r=r|0,t=_(t);var u=0,o=0;return n=n+132|0,D0(r)|0&&(u=K0(n,5,948)|0,(e[u+4>>2]|0)!=0)?o=4:(u=K0(n,e[1e3+(r<<2)>>2]|0,948)|0,e[u+4>>2]|0?o=4:t=_(0)),(o|0)==4&&(t=_(_0(u,t))),_(t)}function Gr(n,r,t){return n=n|0,r=r|0,t=_(t),C2(n,r)|0?t=_(Y1(n,r,t)):t=_(-_(tn(n,r,t))),_(t)}function af(n){return n=_(n),k[D>>2]=n,e[D>>2]|0|0}function Qn(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>1073741823)u0();else{o=q(r<<2)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t<<2)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r<<2)}function bf(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(0-(o>>2)<<2)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function an(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~((u+-4-r|0)>>>2)<<2)),n=e[n>>2]|0,n|0&&I(n)}function n4(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0;if(l=n+4|0,c=e[l>>2]|0,o=c-u|0,f=o>>2,n=r+(f<<2)|0,n>>>0<t>>>0){u=c;do e[u>>2]=e[n>>2],n=n+4|0,u=(e[l>>2]|0)+4|0,e[l>>2]=u;while(n>>>0<t>>>0)}f|0&&cn(c+(0-f<<2)|0,r|0,o|0)|0}function e4(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0;return c=r+4|0,v=e[c>>2]|0,o=e[n>>2]|0,l=t,f=l-o|0,u=v+(0-(f>>2)<<2)|0,e[c>>2]=u,(f|0)>0&&Q(u|0,o|0,f|0)|0,o=n+4|0,f=r+8|0,u=(e[o>>2]|0)-l|0,(u|0)>0&&(Q(e[f>>2]|0,t|0,u|0)|0,e[f>>2]=(e[f>>2]|0)+(u>>>2<<2)),l=e[n>>2]|0,e[n>>2]=e[c>>2],e[c>>2]=l,l=e[o>>2]|0,e[o>>2]=e[f>>2],e[f>>2]=l,l=n+8|0,t=r+12|0,n=e[l>>2]|0,e[l>>2]=e[t>>2],e[t>>2]=n,e[r>>2]=e[c>>2],v|0}function r4(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;if(l=e[r>>2]|0,f=e[t>>2]|0,(l|0)!=(f|0)){o=n+8|0,t=((f+-4-l|0)>>>2)+1|0,n=l,u=e[o>>2]|0;do e[u>>2]=e[n>>2],u=(e[o>>2]|0)+4|0,e[o>>2]=u,n=n+4|0;while((n|0)!=(f|0));e[r>>2]=l+(t<<2)}}function i4(){ro()}function t4(){var n=0;return n=q(4)|0,u4(n),n|0}function u4(n){n=n|0,e[n>>2]=so()|0}function o4(n){n=n|0,n|0&&(Kr(n),I(n))}function Kr(n){n=n|0,_o(e[n>>2]|0)}function f4(n,r,t){n=n|0,r=r|0,t=t|0,qf(e[n>>2]|0,r,t)}function l4(n,r){n=n|0,r=_(r),xf(e[n>>2]|0,r)}function c4(n,r){return n=n|0,r=r|0,Vr(e[n>>2]|0,r)|0}function s4(){var n=0;return n=q(8)|0,Xr(n,0),n|0}function Xr(n,r){n=n|0,r=r|0,r?r=wr(e[r>>2]|0)|0:r=to()|0,e[n>>2]=r,e[n+4>>2]=0,Ao(r,n)}function v4(n){n=n|0;var r=0;return r=q(8)|0,Xr(r,n),r|0}function _4(n){n=n|0,n|0&&(Jr(n),I(n))}function Jr(n){n=n|0;var r=0;oo(e[n>>2]|0),r=n+4|0,n=e[r>>2]|0,e[r>>2]=0,n|0&&(V1(n),I(n))}function V1(n){n=n|0,G1(n)}function G1(n){n=n|0,n=e[n>>2]|0,n|0&&dr(n|0)}function Zr(n){return n=n|0,Tr(n)|0}function h4(n){n=n|0;var r=0,t=0;t=n+4|0,r=e[t>>2]|0,e[t>>2]=0,r|0&&(V1(r),I(r)),lo(e[n>>2]|0)}function d4(n,r){n=n|0,r=r|0,ko(e[n>>2]|0,e[r>>2]|0)}function m4(n,r){n=n|0,r=r|0,Io(e[n>>2]|0,r)}function p4(n,r,t){n=n|0,r=r|0,t=+t,Jo(e[n>>2]|0,r,_(t))}function w4(n,r,t){n=n|0,r=r|0,t=+t,Zo(e[n>>2]|0,r,_(t))}function g4(n,r){n=n|0,r=r|0,Bo(e[n>>2]|0,r)}function y4(n,r){n=n|0,r=r|0,Po(e[n>>2]|0,r)}function k4(n,r){n=n|0,r=r|0,No(e[n>>2]|0,r)}function M4(n,r){n=n|0,r=r|0,Co(e[n>>2]|0,r)}function T4(n,r){n=n|0,r=r|0,xo(e[n>>2]|0,r)}function A4(n,r){n=n|0,r=r|0,Eo(e[n>>2]|0,r)}function C4(n,r,t){n=n|0,r=r|0,t=+t,Qo(e[n>>2]|0,r,_(t))}function S4(n,r,t){n=n|0,r=r|0,t=+t,ao(e[n>>2]|0,r,_(t))}function E4(n,r){n=n|0,r=r|0,nf(e[n>>2]|0,r)}function L4(n,r){n=n|0,r=r|0,Ho(e[n>>2]|0,r)}function B4(n,r){n=n|0,r=r|0,qo(e[n>>2]|0,r)}function R4(n,r){n=n|0,r=+r,Wo(e[n>>2]|0,_(r))}function P4(n,r){n=n|0,r=+r,Go(e[n>>2]|0,_(r))}function O4(n,r){n=n|0,r=+r,Ko(e[n>>2]|0,_(r))}function N4(n,r){n=n|0,r=+r,Yo(e[n>>2]|0,_(r))}function j4(n,r){n=n|0,r=+r,Vo(e[n>>2]|0,_(r))}function I4(n,r){n=n|0,r=+r,ff(e[n>>2]|0,_(r))}function F4(n,r){n=n|0,r=+r,lf(e[n>>2]|0,_(r))}function x4(n){n=n|0,cf(e[n>>2]|0)}function D4(n,r){n=n|0,r=+r,vf(e[n>>2]|0,_(r))}function H4(n,r){n=n|0,r=+r,_f(e[n>>2]|0,_(r))}function U4(n){n=n|0,hf(e[n>>2]|0)}function q4(n,r){n=n|0,r=+r,mf(e[n>>2]|0,_(r))}function z4(n,r){n=n|0,r=+r,pf(e[n>>2]|0,_(r))}function W4(n,r){n=n|0,r=+r,gf(e[n>>2]|0,_(r))}function Y4(n,r){n=n|0,r=+r,yf(e[n>>2]|0,_(r))}function V4(n,r){n=n|0,r=+r,Mf(e[n>>2]|0,_(r))}function G4(n,r){n=n|0,r=+r,Tf(e[n>>2]|0,_(r))}function K4(n,r){n=n|0,r=+r,Cf(e[n>>2]|0,_(r))}function X4(n,r){n=n|0,r=+r,Sf(e[n>>2]|0,_(r))}function J4(n,r){n=n|0,r=+r,Lf(e[n>>2]|0,_(r))}function Z4(n,r,t){n=n|0,r=r|0,t=+t,uf(e[n>>2]|0,r,_(t))}function $4(n,r,t){n=n|0,r=r|0,t=+t,ef(e[n>>2]|0,r,_(t))}function Q4(n,r,t){n=n|0,r=r|0,t=+t,rf(e[n>>2]|0,r,_(t))}function a4(n){return n=n|0,Fo(e[n>>2]|0)|0}function b4(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0;u=s,s=s+16|0,o=u,$o(o,e[r>>2]|0,t),S2(n,o),s=u}function S2(n,r){n=n|0,r=r|0,bn(n,e[r+4>>2]|0,+_(k[r>>2]))}function bn(n,r,t){n=n|0,r=r|0,t=+t,e[n>>2]=r,O[n+8>>3]=t}function nl(n){return n=n|0,Ro(e[n>>2]|0)|0}function el(n){return n=n|0,Oo(e[n>>2]|0)|0}function rl(n){return n=n|0,jo(e[n>>2]|0)|0}function il(n){return n=n|0,So(e[n>>2]|0)|0}function tl(n){return n=n|0,Do(e[n>>2]|0)|0}function ul(n){return n=n|0,Lo(e[n>>2]|0)|0}function ol(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0;u=s,s=s+16|0,o=u,bo(o,e[r>>2]|0,t),S2(n,o),s=u}function fl(n){return n=n|0,Uo(e[n>>2]|0)|0}function ll(n){return n=n|0,zo(e[n>>2]|0)|0}function cl(n,r){n=n|0,r=r|0;var t=0,u=0;t=s,s=s+16|0,u=t,Xo(u,e[r>>2]|0),S2(n,u),s=t}function sl(n){return n=n|0,+ +_(Mo(e[n>>2]|0))}function vl(n){return n=n|0,+ +_(To(e[n>>2]|0))}function _l(n,r){n=n|0,r=r|0;var t=0,u=0;t=s,s=s+16|0,u=t,sf(u,e[r>>2]|0),S2(n,u),s=t}function hl(n,r){n=n|0,r=r|0;var t=0,u=0;t=s,s=s+16|0,u=t,df(u,e[r>>2]|0),S2(n,u),s=t}function dl(n,r){n=n|0,r=r|0;var t=0,u=0;t=s,s=s+16|0,u=t,wf(u,e[r>>2]|0),S2(n,u),s=t}function ml(n,r){n=n|0,r=r|0;var t=0,u=0;t=s,s=s+16|0,u=t,kf(u,e[r>>2]|0),S2(n,u),s=t}function pl(n,r){n=n|0,r=r|0;var t=0,u=0;t=s,s=s+16|0,u=t,Af(u,e[r>>2]|0),S2(n,u),s=t}function wl(n,r){n=n|0,r=r|0;var t=0,u=0;t=s,s=s+16|0,u=t,Ef(u,e[r>>2]|0),S2(n,u),s=t}function gl(n){return n=n|0,+ +_(Bf(e[n>>2]|0))}function yl(n,r){return n=n|0,r=r|0,+ +_(of(e[n>>2]|0,r))}function kl(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0;u=s,s=s+16|0,o=u,tf(o,e[r>>2]|0,t),S2(n,o),s=u}function Ml(n,r,t){n=n|0,r=r|0,t=t|0,ho(e[n>>2]|0,e[r>>2]|0,t)}function Tl(n,r){n=n|0,r=r|0,fo(e[n>>2]|0,e[r>>2]|0)}function Al(n){return n=n|0,F2(e[n>>2]|0)|0}function Cl(n){return n=n|0,n=wo(e[n>>2]|0)|0,n?n=Zr(n)|0:n=0,n|0}function Sl(n,r){return n=n|0,r=r|0,n=x2(e[n>>2]|0,r)|0,n?n=Zr(n)|0:n=0,n|0}function El(n,r){n=n|0,r=r|0;var t=0,u=0;u=q(4)|0,$r(u,r),t=n+4|0,r=e[t>>2]|0,e[t>>2]=u,r|0&&(V1(r),I(r)),Mr(e[n>>2]|0,1)}function $r(n,r){n=n|0,r=r|0,Hl(n,r)}function Ll(n,r,t,u,o,f){n=n|0,r=r|0,t=_(t),u=u|0,o=_(o),f=f|0;var l=0,c=0;l=s,s=s+16|0,c=l,Bl(c,Tr(r)|0,+t,u,+o,f),k[n>>2]=_(+O[c>>3]),k[n+4>>2]=_(+O[c+8>>3]),s=l}function Bl(n,r,t,u,o,f){n=n|0,r=r|0,t=+t,u=u|0,o=+o,f=f|0;var l=0,c=0,v=0,m=0,d=0;l=s,s=s+32|0,d=l+8|0,m=l+20|0,v=l,c=l+16|0,O[d>>3]=t,e[m>>2]=u,O[v>>3]=o,e[c>>2]=f,Rl(n,e[r+4>>2]|0,d,m,v,c),s=l}function Rl(n,r,t,u,o,f){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0;var l=0,c=0;l=s,s=s+16|0,c=l,n2(c),r=X0(r)|0,Pl(n,r,+O[t>>3],e[u>>2]|0,+O[o>>3],e[f>>2]|0),e2(c),s=l}function X0(n){return n=n|0,e[n>>2]|0}function Pl(n,r,t,u,o,f){n=n|0,r=r|0,t=+t,u=u|0,o=+o,f=f|0;var l=0;l=u2(Ol()|0)|0,t=+q2(t),u=ne(u)|0,o=+q2(o),Nl(n,Ku(0,l|0,r|0,+t,u|0,+o,ne(f)|0)|0)}function Ol(){var n=0;return w[7608]|0||(xl(9120),n=7608,e[n>>2]=1,e[n+4>>2]=0),9120}function u2(n){return n=n|0,e[n+8>>2]|0}function q2(n){return n=+n,+ +ee(n)}function ne(n){return n=n|0,ar(n)|0}function Nl(n,r){n=n|0,r=r|0;var t=0,u=0,o=0;o=s,s=s+32|0,t=o,u=r,u&1?(jl(t,0),vr(u|0,t|0)|0,Il(n,t),Fl(t)):(e[n>>2]=e[r>>2],e[n+4>>2]=e[r+4>>2],e[n+8>>2]=e[r+8>>2],e[n+12>>2]=e[r+12>>2]),s=o}function jl(n,r){n=n|0,r=r|0,Qr(n,r),e[n+8>>2]=0,w[n+24>>0]=0}function Il(n,r){n=n|0,r=r|0,r=r+8|0,e[n>>2]=e[r>>2],e[n+4>>2]=e[r+4>>2],e[n+8>>2]=e[r+8>>2],e[n+12>>2]=e[r+12>>2]}function Fl(n){n=n|0,w[n+24>>0]=0}function Qr(n,r){n=n|0,r=r|0,e[n>>2]=r}function ar(n){return n=n|0,n|0}function ee(n){return n=+n,+n}function xl(n){n=n|0,o2(n,Dl()|0,4)}function Dl(){return 1064}function o2(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,e[n+4>>2]=t,e[n+8>>2]=Gu(r|0,t+1|0)|0}function Hl(n,r){n=n|0,r=r|0,r=e[r>>2]|0,e[n>>2]=r,Iu(r|0)}function Ul(n){n=n|0;var r=0,t=0;t=n+4|0,r=e[t>>2]|0,e[t>>2]=0,r|0&&(V1(r),I(r)),Mr(e[n>>2]|0,0)}function ql(n){n=n|0,go(e[n>>2]|0)}function zl(n){return n=n|0,yo(e[n>>2]|0)|0}function Wl(n,r,t,u){n=n|0,r=+r,t=+t,u=u|0,Df(e[n>>2]|0,_(r),_(t),u)}function Yl(n){return n=n|0,+ +_(Ar(e[n>>2]|0))}function Vl(n){return n=n|0,+ +_(Sr(e[n>>2]|0))}function Gl(n){return n=n|0,+ +_(Cr(e[n>>2]|0))}function Kl(n){return n=n|0,+ +_(Er(e[n>>2]|0))}function Xl(n){return n=n|0,+ +_(Lr(e[n>>2]|0))}function Jl(n){return n=n|0,+ +_(Br(e[n>>2]|0))}function Zl(n,r){n=n|0,r=r|0,O[n>>3]=+_(Ar(e[r>>2]|0)),O[n+8>>3]=+_(Sr(e[r>>2]|0)),O[n+16>>3]=+_(Cr(e[r>>2]|0)),O[n+24>>3]=+_(Er(e[r>>2]|0)),O[n+32>>3]=+_(Lr(e[r>>2]|0)),O[n+40>>3]=+_(Br(e[r>>2]|0))}function $l(n,r){return n=n|0,r=r|0,+ +_(Rf(e[n>>2]|0,r))}function Ql(n,r){return n=n|0,r=r|0,+ +_(Pf(e[n>>2]|0,r))}function al(n,r){return n=n|0,r=r|0,+ +_(Of(e[n>>2]|0,r))}function bl(){return co()|0}function nc(){ec(),rc(),ic(),tc(),uc(),oc()}function ec(){ud(11713,4938,1)}function rc(){Ch(10448)}function ic(){uh(10408)}function tc(){B7(10324)}function uc(){x9(10096)}function oc(){fc(9132)}function fc(n){n=n|0;var r=0,t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0,L=0,C=0,A=0,P=0,R=0,j=0,S=0,E=0,U=0,t0=0,K=0,Y=0,a=0,r0=0,G=0,m0=0,H0=0,U0=0,q0=0,Z0=0,h1=0,d1=0,f2=0,m1=0,R2=0,P2=0,p1=0,w1=0,g1=0,s0=0,l2=0,y1=0,p2=0,k1=0,M1=0,O2=0,N2=0,w2=0,j0=0,i2=0,V0=0,c2=0,J2=0,Z2=0,j2=0,$2=0,Q2=0,I0=0,O0=0,s2=0,w0=0,a2=0,z0=0,g2=0,W0=0,y2=0,b2=0,n1=0,k2=0,F0=0,v2=0,e1=0,r1=0,i1=0,b=0,M0=0,N0=0,Y0=0,x0=0,n0=0,X=0,_2=0;r=s,s=s+672|0,t=r+656|0,_2=r+648|0,X=r+640|0,n0=r+632|0,x0=r+624|0,Y0=r+616|0,N0=r+608|0,M0=r+600|0,b=r+592|0,i1=r+584|0,r1=r+576|0,e1=r+568|0,v2=r+560|0,F0=r+552|0,k2=r+544|0,n1=r+536|0,b2=r+528|0,y2=r+520|0,W0=r+512|0,g2=r+504|0,z0=r+496|0,a2=r+488|0,w0=r+480|0,s2=r+472|0,O0=r+464|0,I0=r+456|0,Q2=r+448|0,$2=r+440|0,j2=r+432|0,Z2=r+424|0,J2=r+416|0,c2=r+408|0,V0=r+400|0,i2=r+392|0,j0=r+384|0,w2=r+376|0,N2=r+368|0,O2=r+360|0,M1=r+352|0,k1=r+344|0,p2=r+336|0,y1=r+328|0,l2=r+320|0,s0=r+312|0,g1=r+304|0,w1=r+296|0,p1=r+288|0,P2=r+280|0,R2=r+272|0,m1=r+264|0,f2=r+256|0,d1=r+248|0,h1=r+240|0,Z0=r+232|0,q0=r+224|0,U0=r+216|0,H0=r+208|0,m0=r+200|0,G=r+192|0,r0=r+184|0,a=r+176|0,Y=r+168|0,K=r+160|0,t0=r+152|0,U=r+144|0,E=r+136|0,S=r+128|0,j=r+120|0,R=r+112|0,P=r+104|0,A=r+96|0,C=r+88|0,L=r+80|0,M=r+72|0,g=r+64|0,d=r+56|0,m=r+48|0,v=r+40|0,c=r+32|0,l=r+24|0,f=r+16|0,o=r+8|0,u=r,lc(n,3646),cc(n,3651,2)|0,sc(n,3665,2)|0,vc(n,3682,18)|0,e[_2>>2]=19,e[_2+4>>2]=0,e[t>>2]=e[_2>>2],e[t+4>>2]=e[_2+4>>2],un(n,3690,t)|0,e[X>>2]=1,e[X+4>>2]=0,e[t>>2]=e[X>>2],e[t+4>>2]=e[X+4>>2],_c(n,3696,t)|0,e[n0>>2]=2,e[n0+4>>2]=0,e[t>>2]=e[n0>>2],e[t+4>>2]=e[n0+4>>2],E2(n,3706,t)|0,e[x0>>2]=1,e[x0+4>>2]=0,e[t>>2]=e[x0>>2],e[t+4>>2]=e[x0+4>>2],L1(n,3722,t)|0,e[Y0>>2]=2,e[Y0+4>>2]=0,e[t>>2]=e[Y0>>2],e[t+4>>2]=e[Y0+4>>2],L1(n,3734,t)|0,e[N0>>2]=3,e[N0+4>>2]=0,e[t>>2]=e[N0>>2],e[t+4>>2]=e[N0+4>>2],E2(n,3753,t)|0,e[M0>>2]=4,e[M0+4>>2]=0,e[t>>2]=e[M0>>2],e[t+4>>2]=e[M0+4>>2],E2(n,3769,t)|0,e[b>>2]=5,e[b+4>>2]=0,e[t>>2]=e[b>>2],e[t+4>>2]=e[b+4>>2],E2(n,3783,t)|0,e[i1>>2]=6,e[i1+4>>2]=0,e[t>>2]=e[i1>>2],e[t+4>>2]=e[i1+4>>2],E2(n,3796,t)|0,e[r1>>2]=7,e[r1+4>>2]=0,e[t>>2]=e[r1>>2],e[t+4>>2]=e[r1+4>>2],E2(n,3813,t)|0,e[e1>>2]=8,e[e1+4>>2]=0,e[t>>2]=e[e1>>2],e[t+4>>2]=e[e1+4>>2],E2(n,3825,t)|0,e[v2>>2]=3,e[v2+4>>2]=0,e[t>>2]=e[v2>>2],e[t+4>>2]=e[v2+4>>2],L1(n,3843,t)|0,e[F0>>2]=4,e[F0+4>>2]=0,e[t>>2]=e[F0>>2],e[t+4>>2]=e[F0+4>>2],L1(n,3853,t)|0,e[k2>>2]=9,e[k2+4>>2]=0,e[t>>2]=e[k2>>2],e[t+4>>2]=e[k2+4>>2],E2(n,3870,t)|0,e[n1>>2]=10,e[n1+4>>2]=0,e[t>>2]=e[n1>>2],e[t+4>>2]=e[n1+4>>2],E2(n,3884,t)|0,e[b2>>2]=11,e[b2+4>>2]=0,e[t>>2]=e[b2>>2],e[t+4>>2]=e[b2+4>>2],E2(n,3896,t)|0,e[y2>>2]=1,e[y2+4>>2]=0,e[t>>2]=e[y2>>2],e[t+4>>2]=e[y2+4>>2],R0(n,3907,t)|0,e[W0>>2]=2,e[W0+4>>2]=0,e[t>>2]=e[W0>>2],e[t+4>>2]=e[W0+4>>2],R0(n,3915,t)|0,e[g2>>2]=3,e[g2+4>>2]=0,e[t>>2]=e[g2>>2],e[t+4>>2]=e[g2+4>>2],R0(n,3928,t)|0,e[z0>>2]=4,e[z0+4>>2]=0,e[t>>2]=e[z0>>2],e[t+4>>2]=e[z0+4>>2],R0(n,3948,t)|0,e[a2>>2]=5,e[a2+4>>2]=0,e[t>>2]=e[a2>>2],e[t+4>>2]=e[a2+4>>2],R0(n,3960,t)|0,e[w0>>2]=6,e[w0+4>>2]=0,e[t>>2]=e[w0>>2],e[t+4>>2]=e[w0+4>>2],R0(n,3974,t)|0,e[s2>>2]=7,e[s2+4>>2]=0,e[t>>2]=e[s2>>2],e[t+4>>2]=e[s2+4>>2],R0(n,3983,t)|0,e[O0>>2]=20,e[O0+4>>2]=0,e[t>>2]=e[O0>>2],e[t+4>>2]=e[O0+4>>2],un(n,3999,t)|0,e[I0>>2]=8,e[I0+4>>2]=0,e[t>>2]=e[I0>>2],e[t+4>>2]=e[I0+4>>2],R0(n,4012,t)|0,e[Q2>>2]=9,e[Q2+4>>2]=0,e[t>>2]=e[Q2>>2],e[t+4>>2]=e[Q2+4>>2],R0(n,4022,t)|0,e[$2>>2]=21,e[$2+4>>2]=0,e[t>>2]=e[$2>>2],e[t+4>>2]=e[$2+4>>2],un(n,4039,t)|0,e[j2>>2]=10,e[j2+4>>2]=0,e[t>>2]=e[j2>>2],e[t+4>>2]=e[j2+4>>2],R0(n,4053,t)|0,e[Z2>>2]=11,e[Z2+4>>2]=0,e[t>>2]=e[Z2>>2],e[t+4>>2]=e[Z2+4>>2],R0(n,4065,t)|0,e[J2>>2]=12,e[J2+4>>2]=0,e[t>>2]=e[J2>>2],e[t+4>>2]=e[J2+4>>2],R0(n,4084,t)|0,e[c2>>2]=13,e[c2+4>>2]=0,e[t>>2]=e[c2>>2],e[t+4>>2]=e[c2+4>>2],R0(n,4097,t)|0,e[V0>>2]=14,e[V0+4>>2]=0,e[t>>2]=e[V0>>2],e[t+4>>2]=e[V0+4>>2],R0(n,4117,t)|0,e[i2>>2]=15,e[i2+4>>2]=0,e[t>>2]=e[i2>>2],e[t+4>>2]=e[i2+4>>2],R0(n,4129,t)|0,e[j0>>2]=16,e[j0+4>>2]=0,e[t>>2]=e[j0>>2],e[t+4>>2]=e[j0+4>>2],R0(n,4148,t)|0,e[w2>>2]=17,e[w2+4>>2]=0,e[t>>2]=e[w2>>2],e[t+4>>2]=e[w2+4>>2],R0(n,4161,t)|0,e[N2>>2]=18,e[N2+4>>2]=0,e[t>>2]=e[N2>>2],e[t+4>>2]=e[N2+4>>2],R0(n,4181,t)|0,e[O2>>2]=5,e[O2+4>>2]=0,e[t>>2]=e[O2>>2],e[t+4>>2]=e[O2+4>>2],L1(n,4196,t)|0,e[M1>>2]=6,e[M1+4>>2]=0,e[t>>2]=e[M1>>2],e[t+4>>2]=e[M1+4>>2],L1(n,4206,t)|0,e[k1>>2]=7,e[k1+4>>2]=0,e[t>>2]=e[k1>>2],e[t+4>>2]=e[k1+4>>2],L1(n,4217,t)|0,e[p2>>2]=3,e[p2+4>>2]=0,e[t>>2]=e[p2>>2],e[t+4>>2]=e[p2+4>>2],z2(n,4235,t)|0,e[y1>>2]=1,e[y1+4>>2]=0,e[t>>2]=e[y1>>2],e[t+4>>2]=e[y1+4>>2],re(n,4251,t)|0,e[l2>>2]=4,e[l2+4>>2]=0,e[t>>2]=e[l2>>2],e[t+4>>2]=e[l2+4>>2],z2(n,4263,t)|0,e[s0>>2]=5,e[s0+4>>2]=0,e[t>>2]=e[s0>>2],e[t+4>>2]=e[s0+4>>2],z2(n,4279,t)|0,e[g1>>2]=6,e[g1+4>>2]=0,e[t>>2]=e[g1>>2],e[t+4>>2]=e[g1+4>>2],z2(n,4293,t)|0,e[w1>>2]=7,e[w1+4>>2]=0,e[t>>2]=e[w1>>2],e[t+4>>2]=e[w1+4>>2],z2(n,4306,t)|0,e[p1>>2]=8,e[p1+4>>2]=0,e[t>>2]=e[p1>>2],e[t+4>>2]=e[p1+4>>2],z2(n,4323,t)|0,e[P2>>2]=9,e[P2+4>>2]=0,e[t>>2]=e[P2>>2],e[t+4>>2]=e[P2+4>>2],z2(n,4335,t)|0,e[R2>>2]=2,e[R2+4>>2]=0,e[t>>2]=e[R2>>2],e[t+4>>2]=e[R2+4>>2],re(n,4353,t)|0,e[m1>>2]=12,e[m1+4>>2]=0,e[t>>2]=e[m1>>2],e[t+4>>2]=e[m1+4>>2],B1(n,4363,t)|0,e[f2>>2]=1,e[f2+4>>2]=0,e[t>>2]=e[f2>>2],e[t+4>>2]=e[f2+4>>2],W2(n,4376,t)|0,e[d1>>2]=2,e[d1+4>>2]=0,e[t>>2]=e[d1>>2],e[t+4>>2]=e[d1+4>>2],W2(n,4388,t)|0,e[h1>>2]=13,e[h1+4>>2]=0,e[t>>2]=e[h1>>2],e[t+4>>2]=e[h1+4>>2],B1(n,4402,t)|0,e[Z0>>2]=14,e[Z0+4>>2]=0,e[t>>2]=e[Z0>>2],e[t+4>>2]=e[Z0+4>>2],B1(n,4411,t)|0,e[q0>>2]=15,e[q0+4>>2]=0,e[t>>2]=e[q0>>2],e[t+4>>2]=e[q0+4>>2],B1(n,4421,t)|0,e[U0>>2]=16,e[U0+4>>2]=0,e[t>>2]=e[U0>>2],e[t+4>>2]=e[U0+4>>2],B1(n,4433,t)|0,e[H0>>2]=17,e[H0+4>>2]=0,e[t>>2]=e[H0>>2],e[t+4>>2]=e[H0+4>>2],B1(n,4446,t)|0,e[m0>>2]=18,e[m0+4>>2]=0,e[t>>2]=e[m0>>2],e[t+4>>2]=e[m0+4>>2],B1(n,4458,t)|0,e[G>>2]=3,e[G+4>>2]=0,e[t>>2]=e[G>>2],e[t+4>>2]=e[G+4>>2],W2(n,4471,t)|0,e[r0>>2]=1,e[r0+4>>2]=0,e[t>>2]=e[r0>>2],e[t+4>>2]=e[r0+4>>2],yn(n,4486,t)|0,e[a>>2]=10,e[a+4>>2]=0,e[t>>2]=e[a>>2],e[t+4>>2]=e[a+4>>2],z2(n,4496,t)|0,e[Y>>2]=11,e[Y+4>>2]=0,e[t>>2]=e[Y>>2],e[t+4>>2]=e[Y+4>>2],z2(n,4508,t)|0,e[K>>2]=3,e[K+4>>2]=0,e[t>>2]=e[K>>2],e[t+4>>2]=e[K+4>>2],re(n,4519,t)|0,e[t0>>2]=4,e[t0+4>>2]=0,e[t>>2]=e[t0>>2],e[t+4>>2]=e[t0+4>>2],hc(n,4530,t)|0,e[U>>2]=19,e[U+4>>2]=0,e[t>>2]=e[U>>2],e[t+4>>2]=e[U+4>>2],dc(n,4542,t)|0,e[E>>2]=12,e[E+4>>2]=0,e[t>>2]=e[E>>2],e[t+4>>2]=e[E+4>>2],mc(n,4554,t)|0,e[S>>2]=13,e[S+4>>2]=0,e[t>>2]=e[S>>2],e[t+4>>2]=e[S+4>>2],pc(n,4568,t)|0,e[j>>2]=2,e[j+4>>2]=0,e[t>>2]=e[j>>2],e[t+4>>2]=e[j+4>>2],wc(n,4578,t)|0,e[R>>2]=20,e[R+4>>2]=0,e[t>>2]=e[R>>2],e[t+4>>2]=e[R+4>>2],gc(n,4587,t)|0,e[P>>2]=22,e[P+4>>2]=0,e[t>>2]=e[P>>2],e[t+4>>2]=e[P+4>>2],un(n,4602,t)|0,e[A>>2]=23,e[A+4>>2]=0,e[t>>2]=e[A>>2],e[t+4>>2]=e[A+4>>2],un(n,4619,t)|0,e[C>>2]=14,e[C+4>>2]=0,e[t>>2]=e[C>>2],e[t+4>>2]=e[C+4>>2],yc(n,4629,t)|0,e[L>>2]=1,e[L+4>>2]=0,e[t>>2]=e[L>>2],e[t+4>>2]=e[L+4>>2],kc(n,4637,t)|0,e[M>>2]=4,e[M+4>>2]=0,e[t>>2]=e[M>>2],e[t+4>>2]=e[M+4>>2],W2(n,4653,t)|0,e[g>>2]=5,e[g+4>>2]=0,e[t>>2]=e[g>>2],e[t+4>>2]=e[g+4>>2],W2(n,4669,t)|0,e[d>>2]=6,e[d+4>>2]=0,e[t>>2]=e[d>>2],e[t+4>>2]=e[d+4>>2],W2(n,4686,t)|0,e[m>>2]=7,e[m+4>>2]=0,e[t>>2]=e[m>>2],e[t+4>>2]=e[m+4>>2],W2(n,4701,t)|0,e[v>>2]=8,e[v+4>>2]=0,e[t>>2]=e[v>>2],e[t+4>>2]=e[v+4>>2],W2(n,4719,t)|0,e[c>>2]=9,e[c+4>>2]=0,e[t>>2]=e[c>>2],e[t+4>>2]=e[c+4>>2],W2(n,4736,t)|0,e[l>>2]=21,e[l+4>>2]=0,e[t>>2]=e[l>>2],e[t+4>>2]=e[l+4>>2],Mc(n,4754,t)|0,e[f>>2]=2,e[f+4>>2]=0,e[t>>2]=e[f>>2],e[t+4>>2]=e[f+4>>2],yn(n,4772,t)|0,e[o>>2]=3,e[o+4>>2]=0,e[t>>2]=e[o>>2],e[t+4>>2]=e[o+4>>2],yn(n,4790,t)|0,e[u>>2]=4,e[u+4>>2]=0,e[t>>2]=e[u>>2],e[t+4>>2]=e[u+4>>2],yn(n,4808,t)|0,s=r}function lc(n,r){n=n|0,r=r|0;var t=0;t=L9()|0,e[n>>2]=t,B9(t,r),s1(e[n>>2]|0)}function cc(n,r,t){return n=n|0,r=r|0,t=t|0,h9(n,h0(r)|0,t,0),n|0}function sc(n,r,t){return n=n|0,r=r|0,t=t|0,Q_(n,h0(r)|0,t,0),n|0}function vc(n,r,t){return n=n|0,r=r|0,t=t|0,D_(n,h0(r)|0,t,0),n|0}function un(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],M_(n,r,o),s=u,n|0}function _c(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],r_(n,r,o),s=u,n|0}function E2(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],qv(n,r,o),s=u,n|0}function L1(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],Cv(n,r,o),s=u,n|0}function R0(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],lv(n,r,o),s=u,n|0}function z2(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],K6(n,r,o),s=u,n|0}function re(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],P6(n,r,o),s=u,n|0}function B1(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],r6(n,r,o),s=u,n|0}function W2(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],q3(n,r,o),s=u,n|0}function yn(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],C3(n,r,o),s=u,n|0}function hc(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],l3(n,r,o),s=u,n|0}function dc(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],K8(n,r,o),s=u,n|0}function mc(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],O8(n,r,o),s=u,n|0}function pc(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],p8(n,r,o),s=u,n|0}function wc(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],bs(n,r,o),s=u,n|0}function gc(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],xs(n,r,o),s=u,n|0}function yc(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],ks(n,r,o),s=u,n|0}function kc(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],rs(n,r,o),s=u,n|0}function Mc(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],Tc(n,r,o),s=u,n|0}function Tc(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],Ac(n,t,o,1),s=u}function h0(n){return n=n|0,n|0}function Ac(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=ie()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=Cc(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,Sc(f,u)|0,u),s=o}function ie(){var n=0,r=0;if(w[7616]|0||(ei(9136),$(24,9136,V|0)|0,r=7616,e[r>>2]=1,e[r+4>>2]=0),!(e0(9136)|0)){n=9136,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));ei(9136)}return 9136}function Cc(n){return n=n|0,0}function Sc(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=ie()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],ni(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(Bc(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function d0(n,r,t,u,o,f){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0;var l=0,c=0,v=0,m=0,d=0,g=0,M=0,L=0;l=s,s=s+32|0,M=l+24|0,g=l+20|0,v=l+16|0,d=l+12|0,m=l+8|0,c=l+4|0,L=l,e[g>>2]=r,e[v>>2]=t,e[d>>2]=u,e[m>>2]=o,e[c>>2]=f,f=n+28|0,e[L>>2]=e[f>>2],e[M>>2]=e[L>>2],Ec(n+24|0,M,g,d,m,v,c)|0,e[f>>2]=e[e[f>>2]>>2],s=l}function Ec(n,r,t,u,o,f,l){return n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0,l=l|0,n=Lc(r)|0,r=q(24)|0,br(r+4|0,e[t>>2]|0,e[u>>2]|0,e[o>>2]|0,e[f>>2]|0,e[l>>2]|0),e[r>>2]=e[n>>2],e[n>>2]=r,r|0}function Lc(n){return n=n|0,e[n>>2]|0}function br(n,r,t,u,o,f){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0,e[n>>2]=r,e[n+4>>2]=t,e[n+8>>2]=u,e[n+12>>2]=o,e[n+16>>2]=f}function J(n,r){return n=n|0,r=r|0,r|n|0}function ni(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function Bc(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=Rc(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,Pc(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],ni(f,u,t),e[v>>2]=(e[v>>2]|0)+12,Oc(n,c),Nc(c),s=m;return}}function Rc(n){return n=n|0,357913941}function Pc(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function Oc(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function Nc(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function ei(n){n=n|0,Fc(n)}function jc(n){n=n|0,Ic(n+24|0)}function e0(n){return n=n|0,e[n>>2]|0}function Ic(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function Fc(n){n=n|0;var r=0;r=f0()|0,l0(n,2,3,r,xc()|0,0),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function f0(){return 9228}function xc(){return 1140}function Dc(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0;return t=s,s=s+16|0,u=t+8|0,o=t,f=Hc(n)|0,n=e[f+4>>2]|0,e[o>>2]=e[f>>2],e[o+4>>2]=n,e[u>>2]=e[o>>2],e[u+4>>2]=e[o+4>>2],r=Uc(r,u)|0,s=t,r|0}function l0(n,r,t,u,o,f){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0,e[n>>2]=r,e[n+4>>2]=t,e[n+8>>2]=u,e[n+12>>2]=o,e[n+16>>2]=f}function Hc(n){return n=n|0,(e[(ie()|0)+24>>2]|0)+(n*12|0)|0}function Uc(n,r){n=n|0,r=r|0;var t=0,u=0,o=0;return o=s,s=s+48|0,u=o,t=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(t=e[(e[n>>2]|0)+t>>2]|0),X2[t&31](u,n),u=qc(u)|0,s=o,u|0}function qc(n){n=n|0;var r=0,t=0,u=0,o=0;return o=s,s=s+32|0,r=o+12|0,t=o,u=te(ri()|0)|0,u?(ue(r,u),oe(t,r),zc(n,t),n=fe(r)|0):n=Wc(n)|0,s=o,n|0}function ri(){var n=0;return w[7632]|0||(ac(9184),$(25,9184,V|0)|0,n=7632,e[n>>2]=1,e[n+4>>2]=0),9184}function te(n){return n=n|0,e[n+36>>2]|0}function ue(n,r){n=n|0,r=r|0,e[n>>2]=r,e[n+4>>2]=n,e[n+8>>2]=0}function oe(n,r){n=n|0,r=r|0,e[n>>2]=e[r>>2],e[n+4>>2]=e[r+4>>2],e[n+8>>2]=0}function zc(n,r){n=n|0,r=r|0,Kc(r,n,n+8|0,n+16|0,n+24|0,n+32|0,n+40|0)|0}function fe(n){return n=n|0,e[(e[n+4>>2]|0)+8>>2]|0}function Wc(n){n=n|0;var r=0,t=0,u=0,o=0,f=0,l=0,c=0,v=0;v=s,s=s+16|0,t=v+4|0,u=v,o=b0(8)|0,f=o,l=q(48)|0,c=l,r=c+48|0;do e[c>>2]=e[n>>2],c=c+4|0,n=n+4|0;while((c|0)<(r|0));return r=f+4|0,e[r>>2]=l,c=q(8)|0,l=e[r>>2]|0,e[u>>2]=0,e[t>>2]=e[u>>2],ii(c,l,t),e[o>>2]=c,s=v,f|0}function ii(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,t=q(16)|0,e[t+4>>2]=0,e[t+8>>2]=0,e[t>>2]=1092,e[t+12>>2]=r,e[n+4>>2]=t}function Yc(n){n=n|0,Q1(n),I(n)}function Vc(n){n=n|0,n=e[n+12>>2]|0,n|0&&I(n)}function Gc(n){n=n|0,I(n)}function Kc(n,r,t,u,o,f,l){return n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0,l=l|0,f=Xc(e[n>>2]|0,r,t,u,o,f,l)|0,l=n+4|0,e[(e[l>>2]|0)+8>>2]=f,e[(e[l>>2]|0)+8>>2]|0}function Xc(n,r,t,u,o,f,l){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0,l=l|0;var c=0,v=0;return c=s,s=s+16|0,v=c,n2(v),n=X0(n)|0,l=Jc(n,+O[r>>3],+O[t>>3],+O[u>>3],+O[o>>3],+O[f>>3],+O[l>>3])|0,e2(v),s=c,l|0}function Jc(n,r,t,u,o,f,l){n=n|0,r=+r,t=+t,u=+u,o=+o,f=+f,l=+l;var c=0;return c=u2(Zc()|0)|0,r=+q2(r),t=+q2(t),u=+q2(u),o=+q2(o),f=+q2(f),Nu(0,c|0,n|0,+r,+t,+u,+o,+f,+ +q2(l))|0}function Zc(){var n=0;return w[7624]|0||($c(9172),n=7624,e[n>>2]=1,e[n+4>>2]=0),9172}function $c(n){n=n|0,o2(n,Qc()|0,6)}function Qc(){return 1112}function ac(n){n=n|0,u1(n)}function bc(n){n=n|0,ti(n+24|0),ui(n+16|0)}function ti(n){n=n|0,es(n)}function ui(n){n=n|0,ns(n)}function ns(n){n=n|0;var r=0,t=0;if(r=e[n>>2]|0,r|0)do t=r,r=e[r>>2]|0,I(t);while(r|0);e[n>>2]=0}function es(n){n=n|0;var r=0,t=0;if(r=e[n>>2]|0,r|0)do t=r,r=e[r>>2]|0,I(t);while(r|0);e[n>>2]=0}function u1(n){n=n|0;var r=0;e[n+16>>2]=0,e[n+20>>2]=0,r=n+24|0,e[r>>2]=0,e[n+28>>2]=r,e[n+36>>2]=0,w[n+40>>0]=0,w[n+41>>0]=0}function rs(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],is(n,t,o,0),s=u}function is(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=le()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=ts(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,us(f,u)|0,u),s=o}function le(){var n=0,r=0;if(w[7640]|0||(fi(9232),$(26,9232,V|0)|0,r=7640,e[r>>2]=1,e[r+4>>2]=0),!(e0(9232)|0)){n=9232,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));fi(9232)}return 9232}function ts(n){return n=n|0,0}function us(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=le()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],oi(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(os(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function oi(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function os(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=fs(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,ls(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],oi(f,u,t),e[v>>2]=(e[v>>2]|0)+12,cs(n,c),ss(c),s=m;return}}function fs(n){return n=n|0,357913941}function ls(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function cs(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function ss(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function fi(n){n=n|0,hs(n)}function vs(n){n=n|0,_s(n+24|0)}function _s(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function hs(n){n=n|0;var r=0;r=f0()|0,l0(n,2,1,r,ds()|0,3),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function ds(){return 1144}function ms(n,r,t,u,o){n=n|0,r=r|0,t=+t,u=+u,o=o|0;var f=0,l=0,c=0,v=0;f=s,s=s+16|0,l=f+8|0,c=f,v=ps(n)|0,n=e[v+4>>2]|0,e[c>>2]=e[v>>2],e[c+4>>2]=n,e[l>>2]=e[c>>2],e[l+4>>2]=e[c+4>>2],ws(r,l,t,u,o),s=f}function ps(n){return n=n|0,(e[(le()|0)+24>>2]|0)+(n*12|0)|0}function ws(n,r,t,u,o){n=n|0,r=r|0,t=+t,u=+u,o=o|0;var f=0,l=0,c=0,v=0,m=0;m=s,s=s+16|0,l=m+2|0,c=m+1|0,v=m,f=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(f=e[(e[n>>2]|0)+f>>2]|0),L2(l,t),t=+B2(l,t),L2(c,u),u=+B2(c,u),Y2(v,o),v=V2(v,o)|0,Tu[f&1](n,t,u,v),s=m}function L2(n,r){n=n|0,r=+r}function B2(n,r){return n=n|0,r=+r,+ +ys(r)}function Y2(n,r){n=n|0,r=r|0}function V2(n,r){return n=n|0,r=r|0,gs(r)|0}function gs(n){return n=n|0,n|0}function ys(n){return n=+n,+n}function ks(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],Ms(n,t,o,1),s=u}function Ms(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=ce()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=Ts(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,As(f,u)|0,u),s=o}function ce(){var n=0,r=0;if(w[7648]|0||(ci(9268),$(27,9268,V|0)|0,r=7648,e[r>>2]=1,e[r+4>>2]=0),!(e0(9268)|0)){n=9268,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));ci(9268)}return 9268}function Ts(n){return n=n|0,0}function As(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=ce()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],li(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(Cs(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function li(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function Cs(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=Ss(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,Es(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],li(f,u,t),e[v>>2]=(e[v>>2]|0)+12,Ls(n,c),Bs(c),s=m;return}}function Ss(n){return n=n|0,357913941}function Es(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function Ls(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function Bs(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function ci(n){n=n|0,Os(n)}function Rs(n){n=n|0,Ps(n+24|0)}function Ps(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function Os(n){n=n|0;var r=0;r=f0()|0,l0(n,2,4,r,Ns()|0,0),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function Ns(){return 1160}function js(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0;return t=s,s=s+16|0,u=t+8|0,o=t,f=Is(n)|0,n=e[f+4>>2]|0,e[o>>2]=e[f>>2],e[o+4>>2]=n,e[u>>2]=e[o>>2],e[u+4>>2]=e[o+4>>2],r=Fs(r,u)|0,s=t,r|0}function Is(n){return n=n|0,(e[(ce()|0)+24>>2]|0)+(n*12|0)|0}function Fs(n,r){n=n|0,r=r|0;var t=0;return t=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(t=e[(e[n>>2]|0)+t>>2]|0),si(D1[t&31](n)|0)|0}function si(n){return n=n|0,n&1|0}function xs(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],Ds(n,t,o,0),s=u}function Ds(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=se()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=Hs(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,Us(f,u)|0,u),s=o}function se(){var n=0,r=0;if(w[7656]|0||(_i(9304),$(28,9304,V|0)|0,r=7656,e[r>>2]=1,e[r+4>>2]=0),!(e0(9304)|0)){n=9304,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));_i(9304)}return 9304}function Hs(n){return n=n|0,0}function Us(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=se()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],vi(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(qs(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function vi(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function qs(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=zs(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,Ws(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],vi(f,u,t),e[v>>2]=(e[v>>2]|0)+12,Ys(n,c),Vs(c),s=m;return}}function zs(n){return n=n|0,357913941}function Ws(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function Ys(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function Vs(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function _i(n){n=n|0,Xs(n)}function Gs(n){n=n|0,Ks(n+24|0)}function Ks(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function Xs(n){n=n|0;var r=0;r=f0()|0,l0(n,2,5,r,Js()|0,1),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function Js(){return 1164}function Zs(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;u=s,s=s+16|0,o=u+8|0,f=u,l=$s(n)|0,n=e[l+4>>2]|0,e[f>>2]=e[l>>2],e[f+4>>2]=n,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],Qs(r,o,t),s=u}function $s(n){return n=n|0,(e[(se()|0)+24>>2]|0)+(n*12|0)|0}function Qs(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0;f=s,s=s+16|0,o=f,u=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(u=e[(e[n>>2]|0)+u>>2]|0),o1(o,t),t=f1(o,t)|0,X2[u&31](n,t),l1(o),s=f}function o1(n,r){n=n|0,r=r|0,as(n,r)}function f1(n,r){return n=n|0,r=r|0,n|0}function l1(n){n=n|0,V1(n)}function as(n,r){n=n|0,r=r|0,ve(n,r)}function ve(n,r){n=n|0,r=r|0,e[n>>2]=r}function bs(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],n8(n,t,o,0),s=u}function n8(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=_e()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=e8(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,r8(f,u)|0,u),s=o}function _e(){var n=0,r=0;if(w[7664]|0||(di(9340),$(29,9340,V|0)|0,r=7664,e[r>>2]=1,e[r+4>>2]=0),!(e0(9340)|0)){n=9340,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));di(9340)}return 9340}function e8(n){return n=n|0,0}function r8(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=_e()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],hi(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(i8(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function hi(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function i8(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=t8(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,u8(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],hi(f,u,t),e[v>>2]=(e[v>>2]|0)+12,o8(n,c),f8(c),s=m;return}}function t8(n){return n=n|0,357913941}function u8(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function o8(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function f8(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function di(n){n=n|0,s8(n)}function l8(n){n=n|0,c8(n+24|0)}function c8(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function s8(n){n=n|0;var r=0;r=f0()|0,l0(n,2,4,r,v8()|0,1),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function v8(){return 1180}function _8(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=h8(n)|0,n=e[l+4>>2]|0,e[f>>2]=e[l>>2],e[f+4>>2]=n,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],t=d8(r,o,t)|0,s=u,t|0}function h8(n){return n=n|0,(e[(_e()|0)+24>>2]|0)+(n*12|0)|0}function d8(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0;return f=s,s=s+16|0,o=f,u=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(u=e[(e[n>>2]|0)+u>>2]|0),R1(o,t),o=P1(o,t)|0,o=kn(lr[u&15](n,o)|0)|0,s=f,o|0}function R1(n,r){n=n|0,r=r|0}function P1(n,r){return n=n|0,r=r|0,m8(r)|0}function kn(n){return n=n|0,n|0}function m8(n){return n=n|0,n|0}function p8(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],w8(n,t,o,0),s=u}function w8(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=he()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=g8(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,y8(f,u)|0,u),s=o}function he(){var n=0,r=0;if(w[7672]|0||(pi(9376),$(30,9376,V|0)|0,r=7672,e[r>>2]=1,e[r+4>>2]=0),!(e0(9376)|0)){n=9376,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));pi(9376)}return 9376}function g8(n){return n=n|0,0}function y8(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=he()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],mi(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(k8(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function mi(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function k8(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=M8(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,T8(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],mi(f,u,t),e[v>>2]=(e[v>>2]|0)+12,A8(n,c),C8(c),s=m;return}}function M8(n){return n=n|0,357913941}function T8(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function A8(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function C8(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function pi(n){n=n|0,L8(n)}function S8(n){n=n|0,E8(n+24|0)}function E8(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function L8(n){n=n|0;var r=0;r=f0()|0,l0(n,2,5,r,wi()|0,0),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function wi(){return 1196}function B8(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0;return t=s,s=s+16|0,u=t+8|0,o=t,f=R8(n)|0,n=e[f+4>>2]|0,e[o>>2]=e[f>>2],e[o+4>>2]=n,e[u>>2]=e[o>>2],e[u+4>>2]=e[o+4>>2],r=P8(r,u)|0,s=t,r|0}function R8(n){return n=n|0,(e[(he()|0)+24>>2]|0)+(n*12|0)|0}function P8(n,r){n=n|0,r=r|0;var t=0;return t=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(t=e[(e[n>>2]|0)+t>>2]|0),kn(D1[t&31](n)|0)|0}function O8(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],N8(n,t,o,1),s=u}function N8(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=de()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=j8(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,I8(f,u)|0,u),s=o}function de(){var n=0,r=0;if(w[7680]|0||(yi(9412),$(31,9412,V|0)|0,r=7680,e[r>>2]=1,e[r+4>>2]=0),!(e0(9412)|0)){n=9412,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));yi(9412)}return 9412}function j8(n){return n=n|0,0}function I8(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=de()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],gi(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(F8(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function gi(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function F8(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=x8(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,D8(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],gi(f,u,t),e[v>>2]=(e[v>>2]|0)+12,H8(n,c),U8(c),s=m;return}}function x8(n){return n=n|0,357913941}function D8(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function H8(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function U8(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function yi(n){n=n|0,W8(n)}function q8(n){n=n|0,z8(n+24|0)}function z8(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function W8(n){n=n|0;var r=0;r=f0()|0,l0(n,2,6,r,ki()|0,0),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function ki(){return 1200}function Y8(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0;return t=s,s=s+16|0,u=t+8|0,o=t,f=V8(n)|0,n=e[f+4>>2]|0,e[o>>2]=e[f>>2],e[o+4>>2]=n,e[u>>2]=e[o>>2],e[u+4>>2]=e[o+4>>2],r=G8(r,u)|0,s=t,r|0}function V8(n){return n=n|0,(e[(de()|0)+24>>2]|0)+(n*12|0)|0}function G8(n,r){n=n|0,r=r|0;var t=0;return t=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(t=e[(e[n>>2]|0)+t>>2]|0),Mn(D1[t&31](n)|0)|0}function Mn(n){return n=n|0,n|0}function K8(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],X8(n,t,o,0),s=u}function X8(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=me()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=J8(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,Z8(f,u)|0,u),s=o}function me(){var n=0,r=0;if(w[7688]|0||(Ti(9448),$(32,9448,V|0)|0,r=7688,e[r>>2]=1,e[r+4>>2]=0),!(e0(9448)|0)){n=9448,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Ti(9448)}return 9448}function J8(n){return n=n|0,0}function Z8(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=me()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],Mi(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):($8(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function Mi(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function $8(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=Q8(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,a8(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],Mi(f,u,t),e[v>>2]=(e[v>>2]|0)+12,b8(n,c),n3(c),s=m;return}}function Q8(n){return n=n|0,357913941}function a8(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function b8(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function n3(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function Ti(n){n=n|0,i3(n)}function e3(n){n=n|0,r3(n+24|0)}function r3(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function i3(n){n=n|0;var r=0;r=f0()|0,l0(n,2,6,r,Ai()|0,1),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function Ai(){return 1204}function t3(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;u=s,s=s+16|0,o=u+8|0,f=u,l=u3(n)|0,n=e[l+4>>2]|0,e[f>>2]=e[l>>2],e[f+4>>2]=n,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],o3(r,o,t),s=u}function u3(n){return n=n|0,(e[(me()|0)+24>>2]|0)+(n*12|0)|0}function o3(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0;f=s,s=s+16|0,o=f,u=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(u=e[(e[n>>2]|0)+u>>2]|0),pe(o,t),o=we(o,t)|0,X2[u&31](n,o),s=f}function pe(n,r){n=n|0,r=r|0}function we(n,r){return n=n|0,r=r|0,f3(r)|0}function f3(n){return n=n|0,n|0}function l3(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],c3(n,t,o,0),s=u}function c3(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=ge()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=s3(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,v3(f,u)|0,u),s=o}function ge(){var n=0,r=0;if(w[7696]|0||(Si(9484),$(33,9484,V|0)|0,r=7696,e[r>>2]=1,e[r+4>>2]=0),!(e0(9484)|0)){n=9484,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Si(9484)}return 9484}function s3(n){return n=n|0,0}function v3(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=ge()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],Ci(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(_3(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function Ci(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function _3(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=h3(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,d3(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],Ci(f,u,t),e[v>>2]=(e[v>>2]|0)+12,m3(n,c),p3(c),s=m;return}}function h3(n){return n=n|0,357913941}function d3(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function m3(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function p3(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function Si(n){n=n|0,y3(n)}function w3(n){n=n|0,g3(n+24|0)}function g3(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function y3(n){n=n|0;var r=0;r=f0()|0,l0(n,2,1,r,k3()|0,2),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function k3(){return 1212}function M3(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0;o=s,s=s+16|0,f=o+8|0,l=o,c=T3(n)|0,n=e[c+4>>2]|0,e[l>>2]=e[c>>2],e[l+4>>2]=n,e[f>>2]=e[l>>2],e[f+4>>2]=e[l+4>>2],A3(r,f,t,u),s=o}function T3(n){return n=n|0,(e[(ge()|0)+24>>2]|0)+(n*12|0)|0}function A3(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0;c=s,s=s+16|0,f=c+1|0,l=c,o=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(o=e[(e[n>>2]|0)+o>>2]|0),pe(f,t),f=we(f,t)|0,R1(l,u),l=P1(l,u)|0,_n[o&15](n,f,l),s=c}function C3(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],S3(n,t,o,1),s=u}function S3(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=ye()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=E3(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,L3(f,u)|0,u),s=o}function ye(){var n=0,r=0;if(w[7704]|0||(Li(9520),$(34,9520,V|0)|0,r=7704,e[r>>2]=1,e[r+4>>2]=0),!(e0(9520)|0)){n=9520,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Li(9520)}return 9520}function E3(n){return n=n|0,0}function L3(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=ye()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],Ei(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(B3(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function Ei(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function B3(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=R3(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,P3(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],Ei(f,u,t),e[v>>2]=(e[v>>2]|0)+12,O3(n,c),N3(c),s=m;return}}function R3(n){return n=n|0,357913941}function P3(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function O3(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function N3(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function Li(n){n=n|0,F3(n)}function j3(n){n=n|0,I3(n+24|0)}function I3(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function F3(n){n=n|0;var r=0;r=f0()|0,l0(n,2,1,r,x3()|0,1),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function x3(){return 1224}function D3(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;return o=s,s=s+16|0,f=o+8|0,l=o,c=H3(n)|0,n=e[c+4>>2]|0,e[l>>2]=e[c>>2],e[l+4>>2]=n,e[f>>2]=e[l>>2],e[f+4>>2]=e[l+4>>2],u=+U3(r,f,t),s=o,+u}function H3(n){return n=n|0,(e[(ye()|0)+24>>2]|0)+(n*12|0)|0}function U3(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return f=s,s=s+16|0,o=f,u=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(u=e[(e[n>>2]|0)+u>>2]|0),Y2(o,t),o=V2(o,t)|0,l=+ee(+Cu[u&7](n,o)),s=f,+l}function q3(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],z3(n,t,o,1),s=u}function z3(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=ke()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=W3(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,Y3(f,u)|0,u),s=o}function ke(){var n=0,r=0;if(w[7712]|0||(Ri(9556),$(35,9556,V|0)|0,r=7712,e[r>>2]=1,e[r+4>>2]=0),!(e0(9556)|0)){n=9556,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Ri(9556)}return 9556}function W3(n){return n=n|0,0}function Y3(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=ke()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],Bi(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(V3(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function Bi(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function V3(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=G3(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,K3(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],Bi(f,u,t),e[v>>2]=(e[v>>2]|0)+12,X3(n,c),J3(c),s=m;return}}function G3(n){return n=n|0,357913941}function K3(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function X3(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function J3(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function Ri(n){n=n|0,Q3(n)}function Z3(n){n=n|0,$3(n+24|0)}function $3(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function Q3(n){n=n|0;var r=0;r=f0()|0,l0(n,2,5,r,a3()|0,0),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function a3(){return 1232}function b3(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=n6(n)|0,n=e[l+4>>2]|0,e[f>>2]=e[l>>2],e[f+4>>2]=n,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],t=+e6(r,o),s=u,+t}function n6(n){return n=n|0,(e[(ke()|0)+24>>2]|0)+(n*12|0)|0}function e6(n,r){n=n|0,r=r|0;var t=0;return t=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(t=e[(e[n>>2]|0)+t>>2]|0),+ +ee(+Au[t&15](n))}function r6(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],i6(n,t,o,1),s=u}function i6(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=Me()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=t6(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,u6(f,u)|0,u),s=o}function Me(){var n=0,r=0;if(w[7720]|0||(Oi(9592),$(36,9592,V|0)|0,r=7720,e[r>>2]=1,e[r+4>>2]=0),!(e0(9592)|0)){n=9592,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Oi(9592)}return 9592}function t6(n){return n=n|0,0}function u6(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=Me()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],Pi(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(o6(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function Pi(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function o6(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=f6(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,l6(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],Pi(f,u,t),e[v>>2]=(e[v>>2]|0)+12,c6(n,c),s6(c),s=m;return}}function f6(n){return n=n|0,357913941}function l6(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function c6(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function s6(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function Oi(n){n=n|0,h6(n)}function v6(n){n=n|0,_6(n+24|0)}function _6(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function h6(n){n=n|0;var r=0;r=f0()|0,l0(n,2,7,r,d6()|0,0),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function d6(){return 1276}function m6(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0;return t=s,s=s+16|0,u=t+8|0,o=t,f=p6(n)|0,n=e[f+4>>2]|0,e[o>>2]=e[f>>2],e[o+4>>2]=n,e[u>>2]=e[o>>2],e[u+4>>2]=e[o+4>>2],r=w6(r,u)|0,s=t,r|0}function p6(n){return n=n|0,(e[(Me()|0)+24>>2]|0)+(n*12|0)|0}function w6(n,r){n=n|0,r=r|0;var t=0,u=0,o=0;return o=s,s=s+16|0,u=o,t=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(t=e[(e[n>>2]|0)+t>>2]|0),X2[t&31](u,n),u=Ni(u)|0,s=o,u|0}function Ni(n){n=n|0;var r=0,t=0,u=0,o=0;return o=s,s=s+32|0,r=o+12|0,t=o,u=te(ji()|0)|0,u?(ue(r,u),oe(t,r),g6(n,t),n=fe(r)|0):n=y6(n)|0,s=o,n|0}function ji(){var n=0;return w[7736]|0||(R6(9640),$(25,9640,V|0)|0,n=7736,e[n>>2]=1,e[n+4>>2]=0),9640}function g6(n,r){n=n|0,r=r|0,A6(r,n,n+8|0)|0}function y6(n){n=n|0;var r=0,t=0,u=0,o=0,f=0,l=0,c=0;return t=s,s=s+16|0,o=t+4|0,l=t,u=b0(8)|0,r=u,c=q(16)|0,e[c>>2]=e[n>>2],e[c+4>>2]=e[n+4>>2],e[c+8>>2]=e[n+8>>2],e[c+12>>2]=e[n+12>>2],f=r+4|0,e[f>>2]=c,n=q(8)|0,f=e[f>>2]|0,e[l>>2]=0,e[o>>2]=e[l>>2],Te(n,f,o),e[u>>2]=n,s=t,r|0}function Te(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,t=q(16)|0,e[t+4>>2]=0,e[t+8>>2]=0,e[t>>2]=1244,e[t+12>>2]=r,e[n+4>>2]=t}function k6(n){n=n|0,Q1(n),I(n)}function M6(n){n=n|0,n=e[n+12>>2]|0,n|0&&I(n)}function T6(n){n=n|0,I(n)}function A6(n,r,t){return n=n|0,r=r|0,t=t|0,r=C6(e[n>>2]|0,r,t)|0,t=n+4|0,e[(e[t>>2]|0)+8>>2]=r,e[(e[t>>2]|0)+8>>2]|0}function C6(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0;return u=s,s=s+16|0,o=u,n2(o),n=X0(n)|0,t=S6(n,e[r>>2]|0,+O[t>>3])|0,e2(o),s=u,t|0}function S6(n,r,t){n=n|0,r=r|0,t=+t;var u=0;return u=u2(E6()|0)|0,r=ne(r)|0,ju(0,u|0,n|0,r|0,+ +q2(t))|0}function E6(){var n=0;return w[7728]|0||(L6(9628),n=7728,e[n>>2]=1,e[n+4>>2]=0),9628}function L6(n){n=n|0,o2(n,B6()|0,2)}function B6(){return 1264}function R6(n){n=n|0,u1(n)}function P6(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],O6(n,t,o,1),s=u}function O6(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=Ae()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=N6(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,j6(f,u)|0,u),s=o}function Ae(){var n=0,r=0;if(w[7744]|0||(Fi(9684),$(37,9684,V|0)|0,r=7744,e[r>>2]=1,e[r+4>>2]=0),!(e0(9684)|0)){n=9684,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Fi(9684)}return 9684}function N6(n){return n=n|0,0}function j6(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=Ae()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],Ii(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(I6(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function Ii(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function I6(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=F6(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,x6(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],Ii(f,u,t),e[v>>2]=(e[v>>2]|0)+12,D6(n,c),H6(c),s=m;return}}function F6(n){return n=n|0,357913941}function x6(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function D6(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function H6(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function Fi(n){n=n|0,z6(n)}function U6(n){n=n|0,q6(n+24|0)}function q6(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function z6(n){n=n|0;var r=0;r=f0()|0,l0(n,2,5,r,W6()|0,1),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function W6(){return 1280}function Y6(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=V6(n)|0,n=e[l+4>>2]|0,e[f>>2]=e[l>>2],e[f+4>>2]=n,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],t=G6(r,o,t)|0,s=u,t|0}function V6(n){return n=n|0,(e[(Ae()|0)+24>>2]|0)+(n*12|0)|0}function G6(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return l=s,s=s+32|0,o=l,f=l+16|0,u=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(u=e[(e[n>>2]|0)+u>>2]|0),Y2(f,t),f=V2(f,t)|0,_n[u&15](o,n,f),f=Ni(o)|0,s=l,f|0}function K6(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],X6(n,t,o,1),s=u}function X6(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=Ce()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=J6(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,Z6(f,u)|0,u),s=o}function Ce(){var n=0,r=0;if(w[7752]|0||(Di(9720),$(38,9720,V|0)|0,r=7752,e[r>>2]=1,e[r+4>>2]=0),!(e0(9720)|0)){n=9720,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Di(9720)}return 9720}function J6(n){return n=n|0,0}function Z6(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=Ce()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],xi(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):($6(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function xi(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function $6(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=Q6(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,a6(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],xi(f,u,t),e[v>>2]=(e[v>>2]|0)+12,b6(n,c),nv(c),s=m;return}}function Q6(n){return n=n|0,357913941}function a6(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function b6(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function nv(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function Di(n){n=n|0,iv(n)}function ev(n){n=n|0,rv(n+24|0)}function rv(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function iv(n){n=n|0;var r=0;r=f0()|0,l0(n,2,8,r,tv()|0,0),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function tv(){return 1288}function uv(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0;return t=s,s=s+16|0,u=t+8|0,o=t,f=ov(n)|0,n=e[f+4>>2]|0,e[o>>2]=e[f>>2],e[o+4>>2]=n,e[u>>2]=e[o>>2],e[u+4>>2]=e[o+4>>2],r=fv(r,u)|0,s=t,r|0}function ov(n){return n=n|0,(e[(Ce()|0)+24>>2]|0)+(n*12|0)|0}function fv(n,r){n=n|0,r=r|0;var t=0;return t=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(t=e[(e[n>>2]|0)+t>>2]|0),ar(D1[t&31](n)|0)|0}function lv(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],cv(n,t,o,0),s=u}function cv(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=Se()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=sv(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,vv(f,u)|0,u),s=o}function Se(){var n=0,r=0;if(w[7760]|0||(Ui(9756),$(39,9756,V|0)|0,r=7760,e[r>>2]=1,e[r+4>>2]=0),!(e0(9756)|0)){n=9756,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Ui(9756)}return 9756}function sv(n){return n=n|0,0}function vv(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=Se()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],Hi(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(_v(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function Hi(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function _v(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=hv(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,dv(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],Hi(f,u,t),e[v>>2]=(e[v>>2]|0)+12,mv(n,c),pv(c),s=m;return}}function hv(n){return n=n|0,357913941}function dv(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function mv(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function pv(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function Ui(n){n=n|0,yv(n)}function wv(n){n=n|0,gv(n+24|0)}function gv(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function yv(n){n=n|0;var r=0;r=f0()|0,l0(n,2,8,r,kv()|0,1),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function kv(){return 1292}function Mv(n,r,t){n=n|0,r=r|0,t=+t;var u=0,o=0,f=0,l=0;u=s,s=s+16|0,o=u+8|0,f=u,l=Tv(n)|0,n=e[l+4>>2]|0,e[f>>2]=e[l>>2],e[f+4>>2]=n,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],Av(r,o,t),s=u}function Tv(n){return n=n|0,(e[(Se()|0)+24>>2]|0)+(n*12|0)|0}function Av(n,r,t){n=n|0,r=r|0,t=+t;var u=0,o=0,f=0;f=s,s=s+16|0,o=f,u=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(u=e[(e[n>>2]|0)+u>>2]|0),L2(o,t),t=+B2(o,t),ku[u&31](n,t),s=f}function Cv(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],Sv(n,t,o,0),s=u}function Sv(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=Ee()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=Ev(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,Lv(f,u)|0,u),s=o}function Ee(){var n=0,r=0;if(w[7768]|0||(zi(9792),$(40,9792,V|0)|0,r=7768,e[r>>2]=1,e[r+4>>2]=0),!(e0(9792)|0)){n=9792,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));zi(9792)}return 9792}function Ev(n){return n=n|0,0}function Lv(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=Ee()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],qi(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(Bv(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function qi(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function Bv(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=Rv(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,Pv(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],qi(f,u,t),e[v>>2]=(e[v>>2]|0)+12,Ov(n,c),Nv(c),s=m;return}}function Rv(n){return n=n|0,357913941}function Pv(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function Ov(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function Nv(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function zi(n){n=n|0,Fv(n)}function jv(n){n=n|0,Iv(n+24|0)}function Iv(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function Fv(n){n=n|0;var r=0;r=f0()|0,l0(n,2,1,r,xv()|0,2),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function xv(){return 1300}function Dv(n,r,t,u){n=n|0,r=r|0,t=t|0,u=+u;var o=0,f=0,l=0,c=0;o=s,s=s+16|0,f=o+8|0,l=o,c=Hv(n)|0,n=e[c+4>>2]|0,e[l>>2]=e[c>>2],e[l+4>>2]=n,e[f>>2]=e[l>>2],e[f+4>>2]=e[l+4>>2],Uv(r,f,t,u),s=o}function Hv(n){return n=n|0,(e[(Ee()|0)+24>>2]|0)+(n*12|0)|0}function Uv(n,r,t,u){n=n|0,r=r|0,t=t|0,u=+u;var o=0,f=0,l=0,c=0;c=s,s=s+16|0,f=c+1|0,l=c,o=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(o=e[(e[n>>2]|0)+o>>2]|0),Y2(f,t),f=V2(f,t)|0,L2(l,u),u=+B2(l,u),Bu[o&15](n,f,u),s=c}function qv(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],zv(n,t,o,0),s=u}function zv(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=Le()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=Wv(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,Yv(f,u)|0,u),s=o}function Le(){var n=0,r=0;if(w[7776]|0||(Yi(9828),$(41,9828,V|0)|0,r=7776,e[r>>2]=1,e[r+4>>2]=0),!(e0(9828)|0)){n=9828,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Yi(9828)}return 9828}function Wv(n){return n=n|0,0}function Yv(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=Le()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],Wi(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(Vv(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function Wi(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function Vv(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=Gv(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,Kv(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],Wi(f,u,t),e[v>>2]=(e[v>>2]|0)+12,Xv(n,c),Jv(c),s=m;return}}function Gv(n){return n=n|0,357913941}function Kv(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function Xv(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function Jv(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function Yi(n){n=n|0,Qv(n)}function Zv(n){n=n|0,$v(n+24|0)}function $v(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function Qv(n){n=n|0;var r=0;r=f0()|0,l0(n,2,7,r,av()|0,1),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function av(){return 1312}function bv(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;u=s,s=s+16|0,o=u+8|0,f=u,l=n_(n)|0,n=e[l+4>>2]|0,e[f>>2]=e[l>>2],e[f+4>>2]=n,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],e_(r,o,t),s=u}function n_(n){return n=n|0,(e[(Le()|0)+24>>2]|0)+(n*12|0)|0}function e_(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0;f=s,s=s+16|0,o=f,u=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(u=e[(e[n>>2]|0)+u>>2]|0),Y2(o,t),o=V2(o,t)|0,X2[u&31](n,o),s=f}function r_(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],i_(n,t,o,0),s=u}function i_(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=Be()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=t_(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,u_(f,u)|0,u),s=o}function Be(){var n=0,r=0;if(w[7784]|0||(Gi(9864),$(42,9864,V|0)|0,r=7784,e[r>>2]=1,e[r+4>>2]=0),!(e0(9864)|0)){n=9864,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Gi(9864)}return 9864}function t_(n){return n=n|0,0}function u_(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=Be()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],Vi(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(o_(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function Vi(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function o_(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=f_(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,l_(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],Vi(f,u,t),e[v>>2]=(e[v>>2]|0)+12,c_(n,c),s_(c),s=m;return}}function f_(n){return n=n|0,357913941}function l_(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function c_(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function s_(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function Gi(n){n=n|0,h_(n)}function v_(n){n=n|0,__(n+24|0)}function __(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function h_(n){n=n|0;var r=0;r=f0()|0,l0(n,2,8,r,d_()|0,1),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function d_(){return 1320}function m_(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;u=s,s=s+16|0,o=u+8|0,f=u,l=p_(n)|0,n=e[l+4>>2]|0,e[f>>2]=e[l>>2],e[f+4>>2]=n,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],w_(r,o,t),s=u}function p_(n){return n=n|0,(e[(Be()|0)+24>>2]|0)+(n*12|0)|0}function w_(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0;f=s,s=s+16|0,o=f,u=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(u=e[(e[n>>2]|0)+u>>2]|0),g_(o,t),o=y_(o,t)|0,X2[u&31](n,o),s=f}function g_(n,r){n=n|0,r=r|0}function y_(n,r){return n=n|0,r=r|0,k_(r)|0}function k_(n){return n=n|0,n|0}function M_(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],T_(n,t,o,0),s=u}function T_(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=Re()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=A_(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,C_(f,u)|0,u),s=o}function Re(){var n=0,r=0;if(w[7792]|0||(Xi(9900),$(43,9900,V|0)|0,r=7792,e[r>>2]=1,e[r+4>>2]=0),!(e0(9900)|0)){n=9900,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Xi(9900)}return 9900}function A_(n){return n=n|0,0}function C_(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=Re()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],Ki(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(S_(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function Ki(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function S_(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=E_(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,L_(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],Ki(f,u,t),e[v>>2]=(e[v>>2]|0)+12,B_(n,c),R_(c),s=m;return}}function E_(n){return n=n|0,357913941}function L_(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function B_(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function R_(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function Xi(n){n=n|0,N_(n)}function P_(n){n=n|0,O_(n+24|0)}function O_(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function N_(n){n=n|0;var r=0;r=f0()|0,l0(n,2,22,r,j_()|0,0),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function j_(){return 1344}function I_(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0;t=s,s=s+16|0,u=t+8|0,o=t,f=F_(n)|0,n=e[f+4>>2]|0,e[o>>2]=e[f>>2],e[o+4>>2]=n,e[u>>2]=e[o>>2],e[u+4>>2]=e[o+4>>2],x_(r,u),s=t}function F_(n){return n=n|0,(e[(Re()|0)+24>>2]|0)+(n*12|0)|0}function x_(n,r){n=n|0,r=r|0;var t=0;t=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(t=e[(e[n>>2]|0)+t>>2]|0),K2[t&127](n)}function D_(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0;f=e[n>>2]|0,o=Pe()|0,n=H_(t)|0,d0(f,r,o,n,U_(t,u)|0,u)}function Pe(){var n=0,r=0;if(w[7800]|0||(Zi(9936),$(44,9936,V|0)|0,r=7800,e[r>>2]=1,e[r+4>>2]=0),!(e0(9936)|0)){n=9936,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Zi(9936)}return 9936}function H_(n){return n=n|0,n|0}function U_(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0;return c=s,s=s+16|0,o=c,f=c+4|0,e[o>>2]=n,v=Pe()|0,l=v+24|0,r=J(r,4)|0,e[f>>2]=r,t=v+28|0,u=e[t>>2]|0,u>>>0<(e[v+32>>2]|0)>>>0?(Ji(u,n,r),r=(e[t>>2]|0)+8|0,e[t>>2]=r):(q_(l,o,f),r=e[t>>2]|0),s=c,(r-(e[l>>2]|0)>>3)+-1|0}function Ji(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,e[n+4>>2]=t}function q_(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0;if(c=s,s=s+32|0,o=c,f=n+4|0,l=((e[f>>2]|0)-(e[n>>2]|0)>>3)+1|0,u=z_(n)|0,u>>>0<l>>>0)c0(n);else{v=e[n>>2]|0,d=(e[n+8>>2]|0)-v|0,m=d>>2,W_(o,d>>3>>>0<u>>>1>>>0?m>>>0<l>>>0?l:m:u,(e[f>>2]|0)-v>>3,n+8|0),l=o+8|0,Ji(e[l>>2]|0,e[r>>2]|0,e[t>>2]|0),e[l>>2]=(e[l>>2]|0)+8,Y_(n,o),V_(o),s=c;return}}function z_(n){return n=n|0,536870911}function W_(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>536870911)u0();else{o=q(r<<3)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t<<3)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r<<3)}function Y_(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(0-(o>>3)<<3)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function V_(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~((u+-8-r|0)>>>3)<<3)),n=e[n>>2]|0,n|0&&I(n)}function Zi(n){n=n|0,X_(n)}function G_(n){n=n|0,K_(n+24|0)}function K_(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function X_(n){n=n|0;var r=0;r=f0()|0,l0(n,1,23,r,Ai()|0,1),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function J_(n,r){n=n|0,r=r|0,$_(e[(Z_(n)|0)>>2]|0,r)}function Z_(n){return n=n|0,(e[(Pe()|0)+24>>2]|0)+(n<<3)|0}function $_(n,r){n=n|0,r=r|0;var t=0,u=0;t=s,s=s+16|0,u=t,pe(u,r),r=we(u,r)|0,K2[n&127](r),s=t}function Q_(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0;f=e[n>>2]|0,o=Oe()|0,n=a_(t)|0,d0(f,r,o,n,b_(t,u)|0,u)}function Oe(){var n=0,r=0;if(w[7808]|0||(Qi(9972),$(45,9972,V|0)|0,r=7808,e[r>>2]=1,e[r+4>>2]=0),!(e0(9972)|0)){n=9972,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Qi(9972)}return 9972}function a_(n){return n=n|0,n|0}function b_(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0;return c=s,s=s+16|0,o=c,f=c+4|0,e[o>>2]=n,v=Oe()|0,l=v+24|0,r=J(r,4)|0,e[f>>2]=r,t=v+28|0,u=e[t>>2]|0,u>>>0<(e[v+32>>2]|0)>>>0?($i(u,n,r),r=(e[t>>2]|0)+8|0,e[t>>2]=r):(n9(l,o,f),r=e[t>>2]|0),s=c,(r-(e[l>>2]|0)>>3)+-1|0}function $i(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,e[n+4>>2]=t}function n9(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0;if(c=s,s=s+32|0,o=c,f=n+4|0,l=((e[f>>2]|0)-(e[n>>2]|0)>>3)+1|0,u=e9(n)|0,u>>>0<l>>>0)c0(n);else{v=e[n>>2]|0,d=(e[n+8>>2]|0)-v|0,m=d>>2,r9(o,d>>3>>>0<u>>>1>>>0?m>>>0<l>>>0?l:m:u,(e[f>>2]|0)-v>>3,n+8|0),l=o+8|0,$i(e[l>>2]|0,e[r>>2]|0,e[t>>2]|0),e[l>>2]=(e[l>>2]|0)+8,i9(n,o),t9(o),s=c;return}}function e9(n){return n=n|0,536870911}function r9(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>536870911)u0();else{o=q(r<<3)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t<<3)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r<<3)}function i9(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(0-(o>>3)<<3)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function t9(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~((u+-8-r|0)>>>3)<<3)),n=e[n>>2]|0,n|0&&I(n)}function Qi(n){n=n|0,f9(n)}function u9(n){n=n|0,o9(n+24|0)}function o9(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function f9(n){n=n|0;var r=0;r=f0()|0,l0(n,1,9,r,l9()|0,1),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function l9(){return 1348}function c9(n,r){return n=n|0,r=r|0,v9(e[(s9(n)|0)>>2]|0,r)|0}function s9(n){return n=n|0,(e[(Oe()|0)+24>>2]|0)+(n<<3)|0}function v9(n,r){n=n|0,r=r|0;var t=0,u=0;return t=s,s=s+16|0,u=t,ai(u,r),r=bi(u,r)|0,r=kn(D1[n&31](r)|0)|0,s=t,r|0}function ai(n,r){n=n|0,r=r|0}function bi(n,r){return n=n|0,r=r|0,_9(r)|0}function _9(n){return n=n|0,n|0}function h9(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0;f=e[n>>2]|0,o=Ne()|0,n=d9(t)|0,d0(f,r,o,n,m9(t,u)|0,u)}function Ne(){var n=0,r=0;if(w[7816]|0||(et(10008),$(46,10008,V|0)|0,r=7816,e[r>>2]=1,e[r+4>>2]=0),!(e0(10008)|0)){n=10008,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));et(10008)}return 10008}function d9(n){return n=n|0,n|0}function m9(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0;return c=s,s=s+16|0,o=c,f=c+4|0,e[o>>2]=n,v=Ne()|0,l=v+24|0,r=J(r,4)|0,e[f>>2]=r,t=v+28|0,u=e[t>>2]|0,u>>>0<(e[v+32>>2]|0)>>>0?(nt(u,n,r),r=(e[t>>2]|0)+8|0,e[t>>2]=r):(p9(l,o,f),r=e[t>>2]|0),s=c,(r-(e[l>>2]|0)>>3)+-1|0}function nt(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,e[n+4>>2]=t}function p9(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0;if(c=s,s=s+32|0,o=c,f=n+4|0,l=((e[f>>2]|0)-(e[n>>2]|0)>>3)+1|0,u=w9(n)|0,u>>>0<l>>>0)c0(n);else{v=e[n>>2]|0,d=(e[n+8>>2]|0)-v|0,m=d>>2,g9(o,d>>3>>>0<u>>>1>>>0?m>>>0<l>>>0?l:m:u,(e[f>>2]|0)-v>>3,n+8|0),l=o+8|0,nt(e[l>>2]|0,e[r>>2]|0,e[t>>2]|0),e[l>>2]=(e[l>>2]|0)+8,y9(n,o),k9(o),s=c;return}}function w9(n){return n=n|0,536870911}function g9(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>536870911)u0();else{o=q(r<<3)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t<<3)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r<<3)}function y9(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(0-(o>>3)<<3)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function k9(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~((u+-8-r|0)>>>3)<<3)),n=e[n>>2]|0,n|0&&I(n)}function et(n){n=n|0,A9(n)}function M9(n){n=n|0,T9(n+24|0)}function T9(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function A9(n){n=n|0;var r=0;r=f0()|0,l0(n,1,15,r,wi()|0,0),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function C9(n){return n=n|0,E9(e[(S9(n)|0)>>2]|0)|0}function S9(n){return n=n|0,(e[(Ne()|0)+24>>2]|0)+(n<<3)|0}function E9(n){return n=n|0,kn(Fn[n&7]()|0)|0}function L9(){var n=0;return w[7832]|0||(F9(10052),$(25,10052,V|0)|0,n=7832,e[n>>2]=1,e[n+4>>2]=0),10052}function B9(n,r){n=n|0,r=r|0,e[n>>2]=R9()|0,e[n+4>>2]=P9()|0,e[n+12>>2]=r,e[n+8>>2]=O9()|0,e[n+32>>2]=2}function R9(){return 11709}function P9(){return 1188}function O9(){return Tn()|0}function N9(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0,(c1(u,896)|0)==512?t|0&&(j9(t),I(t)):r|0&&(Jr(r),I(r))}function c1(n,r){return n=n|0,r=r|0,r&n|0}function j9(n){n=n|0,n=e[n+4>>2]|0,n|0&&v1(n)}function Tn(){var n=0;return w[7824]|0||(e[2511]=I9()|0,e[2512]=0,n=7824,e[n>>2]=1,e[n+4>>2]=0),10044}function I9(){return 0}function F9(n){n=n|0,u1(n)}function x9(n){n=n|0;var r=0,t=0,u=0,o=0,f=0;r=s,s=s+32|0,t=r+24|0,f=r+16|0,o=r+8|0,u=r,D9(n,4827),H9(n,4834,3)|0,U9(n,3682,47)|0,e[f>>2]=9,e[f+4>>2]=0,e[t>>2]=e[f>>2],e[t+4>>2]=e[f+4>>2],q9(n,4841,t)|0,e[o>>2]=1,e[o+4>>2]=0,e[t>>2]=e[o>>2],e[t+4>>2]=e[o+4>>2],z9(n,4871,t)|0,e[u>>2]=10,e[u+4>>2]=0,e[t>>2]=e[u>>2],e[t+4>>2]=e[u+4>>2],W9(n,4891,t)|0,s=r}function D9(n,r){n=n|0,r=r|0;var t=0;t=k7()|0,e[n>>2]=t,M7(t,r),s1(e[n>>2]|0)}function H9(n,r,t){return n=n|0,r=r|0,t=t|0,t7(n,h0(r)|0,t,0),n|0}function U9(n,r,t){return n=n|0,r=r|0,t=t|0,Y5(n,h0(r)|0,t,0),n|0}function q9(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],C5(n,r,o),s=u,n|0}function z9(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],u5(n,r,o),s=u,n|0}function W9(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=e[t+4>>2]|0,e[f>>2]=e[t>>2],e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],Y9(n,r,o),s=u,n|0}function Y9(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],V9(n,t,o,1),s=u}function V9(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=je()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=G9(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,K9(f,u)|0,u),s=o}function je(){var n=0,r=0;if(w[7840]|0||(it(10100),$(48,10100,V|0)|0,r=7840,e[r>>2]=1,e[r+4>>2]=0),!(e0(10100)|0)){n=10100,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));it(10100)}return 10100}function G9(n){return n=n|0,0}function K9(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=je()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],rt(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(X9(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function rt(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function X9(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=J9(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,Z9(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],rt(f,u,t),e[v>>2]=(e[v>>2]|0)+12,$9(n,c),Q9(c),s=m;return}}function J9(n){return n=n|0,357913941}function Z9(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function $9(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function Q9(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function it(n){n=n|0,n5(n)}function a9(n){n=n|0,b9(n+24|0)}function b9(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function n5(n){n=n|0;var r=0;r=f0()|0,l0(n,2,6,r,e5()|0,1),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function e5(){return 1364}function r5(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;return u=s,s=s+16|0,o=u+8|0,f=u,l=i5(n)|0,n=e[l+4>>2]|0,e[f>>2]=e[l>>2],e[f+4>>2]=n,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],t=t5(r,o,t)|0,s=u,t|0}function i5(n){return n=n|0,(e[(je()|0)+24>>2]|0)+(n*12|0)|0}function t5(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0;return f=s,s=s+16|0,o=f,u=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(u=e[(e[n>>2]|0)+u>>2]|0),Y2(o,t),o=V2(o,t)|0,o=si(lr[u&15](n,o)|0)|0,s=f,o|0}function u5(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],o5(n,t,o,0),s=u}function o5(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=Ie()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=f5(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,l5(f,u)|0,u),s=o}function Ie(){var n=0,r=0;if(w[7848]|0||(ut(10136),$(49,10136,V|0)|0,r=7848,e[r>>2]=1,e[r+4>>2]=0),!(e0(10136)|0)){n=10136,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));ut(10136)}return 10136}function f5(n){return n=n|0,0}function l5(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=Ie()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],tt(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(c5(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function tt(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function c5(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=s5(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,v5(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],tt(f,u,t),e[v>>2]=(e[v>>2]|0)+12,_5(n,c),h5(c),s=m;return}}function s5(n){return n=n|0,357913941}function v5(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function _5(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function h5(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function ut(n){n=n|0,p5(n)}function d5(n){n=n|0,m5(n+24|0)}function m5(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function p5(n){n=n|0;var r=0;r=f0()|0,l0(n,2,9,r,w5()|0,1),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function w5(){return 1372}function g5(n,r,t){n=n|0,r=r|0,t=+t;var u=0,o=0,f=0,l=0;u=s,s=s+16|0,o=u+8|0,f=u,l=y5(n)|0,n=e[l+4>>2]|0,e[f>>2]=e[l>>2],e[f+4>>2]=n,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],k5(r,o,t),s=u}function y5(n){return n=n|0,(e[(Ie()|0)+24>>2]|0)+(n*12|0)|0}function k5(n,r,t){n=n|0,r=r|0,t=+t;var u=0,o=0,f=0,l=x;f=s,s=s+16|0,o=f,u=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(u=e[(e[n>>2]|0)+u>>2]|0),M5(o,t),l=_(T5(o,t)),yu[u&1](n,l),s=f}function M5(n,r){n=n|0,r=+r}function T5(n,r){return n=n|0,r=+r,_(A5(r))}function A5(n){return n=+n,_(n)}function C5(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,o=u+8|0,f=u,c=e[t>>2]|0,l=e[t+4>>2]|0,t=h0(r)|0,e[f>>2]=c,e[f+4>>2]=l,e[o>>2]=e[f>>2],e[o+4>>2]=e[f+4>>2],S5(n,t,o,0),s=u}function S5(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0,v=0,m=0,d=0;o=s,s=s+32|0,f=o+16|0,d=o+8|0,c=o,m=e[t>>2]|0,v=e[t+4>>2]|0,l=e[n>>2]|0,n=Fe()|0,e[d>>2]=m,e[d+4>>2]=v,e[f>>2]=e[d>>2],e[f+4>>2]=e[d+4>>2],t=E5(f)|0,e[c>>2]=m,e[c+4>>2]=v,e[f>>2]=e[c>>2],e[f+4>>2]=e[c+4>>2],d0(l,r,n,t,L5(f,u)|0,u),s=o}function Fe(){var n=0,r=0;if(w[7856]|0||(ft(10172),$(50,10172,V|0)|0,r=7856,e[r>>2]=1,e[r+4>>2]=0),!(e0(10172)|0)){n=10172,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));ft(10172)}return 10172}function E5(n){return n=n|0,0}function L5(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0;return d=s,s=s+32|0,o=d+24|0,l=d+16|0,c=d,v=d+8|0,f=e[n>>2]|0,u=e[n+4>>2]|0,e[c>>2]=f,e[c+4>>2]=u,g=Fe()|0,m=g+24|0,n=J(r,4)|0,e[v>>2]=n,r=g+28|0,t=e[r>>2]|0,t>>>0<(e[g+32>>2]|0)>>>0?(e[l>>2]=f,e[l+4>>2]=u,e[o>>2]=e[l>>2],e[o+4>>2]=e[l+4>>2],ot(t,o,n),n=(e[r>>2]|0)+12|0,e[r>>2]=n):(B5(m,c,v),n=e[r>>2]|0),s=d,((n-(e[m>>2]|0)|0)/12|0)+-1|0}function ot(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=e[r+4>>2]|0,e[n>>2]=e[r>>2],e[n+4>>2]=u,e[n+8>>2]=t}function B5(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;if(m=s,s=s+48|0,u=m+32|0,l=m+24|0,c=m,v=n+4|0,o=(((e[v>>2]|0)-(e[n>>2]|0)|0)/12|0)+1|0,f=R5(n)|0,f>>>0<o>>>0)c0(n);else{d=e[n>>2]|0,M=((e[n+8>>2]|0)-d|0)/12|0,g=M<<1,P5(c,M>>>0<f>>>1>>>0?g>>>0<o>>>0?o:g:f,((e[v>>2]|0)-d|0)/12|0,n+8|0),v=c+8|0,f=e[v>>2]|0,o=e[r+4>>2]|0,t=e[t>>2]|0,e[l>>2]=e[r>>2],e[l+4>>2]=o,e[u>>2]=e[l>>2],e[u+4>>2]=e[l+4>>2],ot(f,u,t),e[v>>2]=(e[v>>2]|0)+12,O5(n,c),N5(c),s=m;return}}function R5(n){return n=n|0,357913941}function P5(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>357913941)u0();else{o=q(r*12|0)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t*12|0)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r*12|0)}function O5(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(((o|0)/-12|0)*12|0)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function N5(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~(((u+-12-r|0)>>>0)/12|0)*12|0)),n=e[n>>2]|0,n|0&&I(n)}function ft(n){n=n|0,F5(n)}function j5(n){n=n|0,I5(n+24|0)}function I5(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~(((r+-12-u|0)>>>0)/12|0)*12|0)),I(t))}function F5(n){n=n|0;var r=0;r=f0()|0,l0(n,2,3,r,x5()|0,2),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function x5(){return 1380}function D5(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0;o=s,s=s+16|0,f=o+8|0,l=o,c=H5(n)|0,n=e[c+4>>2]|0,e[l>>2]=e[c>>2],e[l+4>>2]=n,e[f>>2]=e[l>>2],e[f+4>>2]=e[l+4>>2],U5(r,f,t,u),s=o}function H5(n){return n=n|0,(e[(Fe()|0)+24>>2]|0)+(n*12|0)|0}function U5(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0;c=s,s=s+16|0,f=c+1|0,l=c,o=e[r>>2]|0,r=e[r+4>>2]|0,n=n+(r>>1)|0,r&1&&(o=e[(e[n>>2]|0)+o>>2]|0),Y2(f,t),f=V2(f,t)|0,q5(l,u),l=z5(l,u)|0,_n[o&15](n,f,l),s=c}function q5(n,r){n=n|0,r=r|0}function z5(n,r){return n=n|0,r=r|0,W5(r)|0}function W5(n){return n=n|0,(n|0)!=0|0}function Y5(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0;f=e[n>>2]|0,o=xe()|0,n=V5(t)|0,d0(f,r,o,n,G5(t,u)|0,u)}function xe(){var n=0,r=0;if(w[7864]|0||(ct(10208),$(51,10208,V|0)|0,r=7864,e[r>>2]=1,e[r+4>>2]=0),!(e0(10208)|0)){n=10208,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));ct(10208)}return 10208}function V5(n){return n=n|0,n|0}function G5(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0;return c=s,s=s+16|0,o=c,f=c+4|0,e[o>>2]=n,v=xe()|0,l=v+24|0,r=J(r,4)|0,e[f>>2]=r,t=v+28|0,u=e[t>>2]|0,u>>>0<(e[v+32>>2]|0)>>>0?(lt(u,n,r),r=(e[t>>2]|0)+8|0,e[t>>2]=r):(K5(l,o,f),r=e[t>>2]|0),s=c,(r-(e[l>>2]|0)>>3)+-1|0}function lt(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,e[n+4>>2]=t}function K5(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0;if(c=s,s=s+32|0,o=c,f=n+4|0,l=((e[f>>2]|0)-(e[n>>2]|0)>>3)+1|0,u=X5(n)|0,u>>>0<l>>>0)c0(n);else{v=e[n>>2]|0,d=(e[n+8>>2]|0)-v|0,m=d>>2,J5(o,d>>3>>>0<u>>>1>>>0?m>>>0<l>>>0?l:m:u,(e[f>>2]|0)-v>>3,n+8|0),l=o+8|0,lt(e[l>>2]|0,e[r>>2]|0,e[t>>2]|0),e[l>>2]=(e[l>>2]|0)+8,Z5(n,o),$5(o),s=c;return}}function X5(n){return n=n|0,536870911}function J5(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>536870911)u0();else{o=q(r<<3)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t<<3)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r<<3)}function Z5(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(0-(o>>3)<<3)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function $5(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~((u+-8-r|0)>>>3)<<3)),n=e[n>>2]|0,n|0&&I(n)}function ct(n){n=n|0,b5(n)}function Q5(n){n=n|0,a5(n+24|0)}function a5(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function b5(n){n=n|0;var r=0;r=f0()|0,l0(n,1,24,r,n7()|0,1),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function n7(){return 1392}function e7(n,r){n=n|0,r=r|0,i7(e[(r7(n)|0)>>2]|0,r)}function r7(n){return n=n|0,(e[(xe()|0)+24>>2]|0)+(n<<3)|0}function i7(n,r){n=n|0,r=r|0;var t=0,u=0;t=s,s=s+16|0,u=t,ai(u,r),r=bi(u,r)|0,K2[n&127](r),s=t}function t7(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0;f=e[n>>2]|0,o=De()|0,n=u7(t)|0,d0(f,r,o,n,o7(t,u)|0,u)}function De(){var n=0,r=0;if(w[7872]|0||(vt(10244),$(52,10244,V|0)|0,r=7872,e[r>>2]=1,e[r+4>>2]=0),!(e0(10244)|0)){n=10244,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));vt(10244)}return 10244}function u7(n){return n=n|0,n|0}function o7(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0;return c=s,s=s+16|0,o=c,f=c+4|0,e[o>>2]=n,v=De()|0,l=v+24|0,r=J(r,4)|0,e[f>>2]=r,t=v+28|0,u=e[t>>2]|0,u>>>0<(e[v+32>>2]|0)>>>0?(st(u,n,r),r=(e[t>>2]|0)+8|0,e[t>>2]=r):(f7(l,o,f),r=e[t>>2]|0),s=c,(r-(e[l>>2]|0)>>3)+-1|0}function st(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,e[n+4>>2]=t}function f7(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0;if(c=s,s=s+32|0,o=c,f=n+4|0,l=((e[f>>2]|0)-(e[n>>2]|0)>>3)+1|0,u=l7(n)|0,u>>>0<l>>>0)c0(n);else{v=e[n>>2]|0,d=(e[n+8>>2]|0)-v|0,m=d>>2,c7(o,d>>3>>>0<u>>>1>>>0?m>>>0<l>>>0?l:m:u,(e[f>>2]|0)-v>>3,n+8|0),l=o+8|0,st(e[l>>2]|0,e[r>>2]|0,e[t>>2]|0),e[l>>2]=(e[l>>2]|0)+8,s7(n,o),v7(o),s=c;return}}function l7(n){return n=n|0,536870911}function c7(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>536870911)u0();else{o=q(r<<3)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t<<3)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r<<3)}function s7(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(0-(o>>3)<<3)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function v7(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~((u+-8-r|0)>>>3)<<3)),n=e[n>>2]|0,n|0&&I(n)}function vt(n){n=n|0,d7(n)}function _7(n){n=n|0,h7(n+24|0)}function h7(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function d7(n){n=n|0;var r=0;r=f0()|0,l0(n,1,16,r,m7()|0,0),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function m7(){return 1400}function p7(n){return n=n|0,g7(e[(w7(n)|0)>>2]|0)|0}function w7(n){return n=n|0,(e[(De()|0)+24>>2]|0)+(n<<3)|0}function g7(n){return n=n|0,y7(Fn[n&7]()|0)|0}function y7(n){return n=n|0,n|0}function k7(){var n=0;return w[7880]|0||(L7(10280),$(25,10280,V|0)|0,n=7880,e[n>>2]=1,e[n+4>>2]=0),10280}function M7(n,r){n=n|0,r=r|0,e[n>>2]=T7()|0,e[n+4>>2]=A7()|0,e[n+12>>2]=r,e[n+8>>2]=C7()|0,e[n+32>>2]=4}function T7(){return 11711}function A7(){return 1356}function C7(){return Tn()|0}function S7(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0,(c1(u,896)|0)==512?t|0&&(E7(t),I(t)):r|0&&(Kr(r),I(r))}function E7(n){n=n|0,n=e[n+4>>2]|0,n|0&&v1(n)}function L7(n){n=n|0,u1(n)}function B7(n){n=n|0,R7(n,4920),P7(n)|0,O7(n)|0}function R7(n,r){n=n|0,r=r|0;var t=0;t=ji()|0,e[n>>2]=t,a7(t,r),s1(e[n>>2]|0)}function P7(n){n=n|0;var r=0;return r=e[n>>2]|0,O1(r,W7()|0),n|0}function O7(n){n=n|0;var r=0;return r=e[n>>2]|0,O1(r,N7()|0),n|0}function N7(){var n=0;return w[7888]|0||(_t(10328),$(53,10328,V|0)|0,n=7888,e[n>>2]=1,e[n+4>>2]=0),e0(10328)|0||_t(10328),10328}function O1(n,r){n=n|0,r=r|0,d0(n,0,r,0,0,0)}function _t(n){n=n|0,F7(n),N1(n,10)}function j7(n){n=n|0,I7(n+24|0)}function I7(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function F7(n){n=n|0;var r=0;r=f0()|0,l0(n,5,1,r,U7()|0,2),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function x7(n,r,t){n=n|0,r=r|0,t=+t,D7(n,r,t)}function N1(n,r){n=n|0,r=r|0,e[n+20>>2]=r}function D7(n,r,t){n=n|0,r=r|0,t=+t;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+16|0,f=u+8|0,c=u+13|0,o=u,l=u+12|0,Y2(c,r),e[f>>2]=V2(c,r)|0,L2(l,t),O[o>>3]=+B2(l,t),H7(n,f,o),s=u}function H7(n,r,t){n=n|0,r=r|0,t=t|0,bn(n+8|0,e[r>>2]|0,+O[t>>3]),w[n+24>>0]=1}function U7(){return 1404}function q7(n,r){return n=n|0,r=+r,z7(n,r)|0}function z7(n,r){n=n|0,r=+r;var t=0,u=0,o=0,f=0,l=0,c=0,v=0;return u=s,s=s+16|0,f=u+4|0,l=u+8|0,c=u,o=b0(8)|0,t=o,v=q(16)|0,Y2(f,n),n=V2(f,n)|0,L2(l,r),bn(v,n,+B2(l,r)),l=t+4|0,e[l>>2]=v,n=q(8)|0,l=e[l>>2]|0,e[c>>2]=0,e[f>>2]=e[c>>2],Te(n,l,f),e[o>>2]=n,s=u,t|0}function W7(){var n=0;return w[7896]|0||(ht(10364),$(54,10364,V|0)|0,n=7896,e[n>>2]=1,e[n+4>>2]=0),e0(10364)|0||ht(10364),10364}function ht(n){n=n|0,G7(n),N1(n,55)}function Y7(n){n=n|0,V7(n+24|0)}function V7(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function G7(n){n=n|0;var r=0;r=f0()|0,l0(n,5,4,r,Z7()|0,0),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function K7(n){n=n|0,X7(n)}function X7(n){n=n|0,J7(n)}function J7(n){n=n|0,dt(n+8|0),w[n+24>>0]=1}function dt(n){n=n|0,e[n>>2]=0,O[n+8>>3]=0}function Z7(){return 1424}function $7(){return Q7()|0}function Q7(){var n=0,r=0,t=0,u=0,o=0,f=0,l=0;return r=s,s=s+16|0,o=r+4|0,l=r,t=b0(8)|0,n=t,u=q(16)|0,dt(u),f=n+4|0,e[f>>2]=u,u=q(8)|0,f=e[f>>2]|0,e[l>>2]=0,e[o>>2]=e[l>>2],Te(u,f,o),e[t>>2]=u,s=r,n|0}function a7(n,r){n=n|0,r=r|0,e[n>>2]=b7()|0,e[n+4>>2]=nh()|0,e[n+12>>2]=r,e[n+8>>2]=eh()|0,e[n+32>>2]=5}function b7(){return 11710}function nh(){return 1416}function eh(){return An()|0}function rh(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0,(c1(u,896)|0)==512?t|0&&(ih(t),I(t)):r|0&&I(r)}function ih(n){n=n|0,n=e[n+4>>2]|0,n|0&&v1(n)}function An(){var n=0;return w[7904]|0||(e[2600]=th()|0,e[2601]=0,n=7904,e[n>>2]=1,e[n+4>>2]=0),10400}function th(){return e[357]|0}function uh(n){n=n|0,oh(n,4926),fh(n)|0}function oh(n,r){n=n|0,r=r|0;var t=0;t=ri()|0,e[n>>2]=t,gh(t,r),s1(e[n>>2]|0)}function fh(n){n=n|0;var r=0;return r=e[n>>2]|0,O1(r,lh()|0),n|0}function lh(){var n=0;return w[7912]|0||(mt(10412),$(56,10412,V|0)|0,n=7912,e[n>>2]=1,e[n+4>>2]=0),e0(10412)|0||mt(10412),10412}function mt(n){n=n|0,vh(n),N1(n,57)}function ch(n){n=n|0,sh(n+24|0)}function sh(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function vh(n){n=n|0;var r=0;r=f0()|0,l0(n,5,5,r,mh()|0,0),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function _h(n){n=n|0,hh(n)}function hh(n){n=n|0,dh(n)}function dh(n){n=n|0;var r=0,t=0;r=n+8|0,t=r+48|0;do e[r>>2]=0,r=r+4|0;while((r|0)<(t|0));w[n+56>>0]=1}function mh(){return 1432}function ph(){return wh()|0}function wh(){var n=0,r=0,t=0,u=0,o=0,f=0,l=0,c=0;l=s,s=s+16|0,n=l+4|0,r=l,t=b0(8)|0,u=t,o=q(48)|0,f=o,c=f+48|0;do e[f>>2]=0,f=f+4|0;while((f|0)<(c|0));return f=u+4|0,e[f>>2]=o,c=q(8)|0,f=e[f>>2]|0,e[r>>2]=0,e[n>>2]=e[r>>2],ii(c,f,n),e[t>>2]=c,s=l,u|0}function gh(n,r){n=n|0,r=r|0,e[n>>2]=yh()|0,e[n+4>>2]=kh()|0,e[n+12>>2]=r,e[n+8>>2]=Mh()|0,e[n+32>>2]=6}function yh(){return 11704}function kh(){return 1436}function Mh(){return An()|0}function Th(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0,(c1(u,896)|0)==512?t|0&&(Ah(t),I(t)):r|0&&I(r)}function Ah(n){n=n|0,n=e[n+4>>2]|0,n|0&&v1(n)}function Ch(n){n=n|0,Sh(n,4933),Eh(n)|0,Lh(n)|0}function Sh(n,r){n=n|0,r=r|0;var t=0;t=Qh()|0,e[n>>2]=t,ah(t,r),s1(e[n>>2]|0)}function Eh(n){n=n|0;var r=0;return r=e[n>>2]|0,O1(r,zh()|0),n|0}function Lh(n){n=n|0;var r=0;return r=e[n>>2]|0,O1(r,Bh()|0),n|0}function Bh(){var n=0;return w[7920]|0||(pt(10452),$(58,10452,V|0)|0,n=7920,e[n>>2]=1,e[n+4>>2]=0),e0(10452)|0||pt(10452),10452}function pt(n){n=n|0,Oh(n),N1(n,1)}function Rh(n){n=n|0,Ph(n+24|0)}function Ph(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function Oh(n){n=n|0;var r=0;r=f0()|0,l0(n,5,1,r,Fh()|0,2),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function Nh(n,r,t){n=n|0,r=+r,t=+t,jh(n,r,t)}function jh(n,r,t){n=n|0,r=+r,t=+t;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+32|0,f=u+8|0,c=u+17|0,o=u,l=u+16|0,L2(c,r),O[f>>3]=+B2(c,r),L2(l,t),O[o>>3]=+B2(l,t),Ih(n,f,o),s=u}function Ih(n,r,t){n=n|0,r=r|0,t=t|0,wt(n+8|0,+O[r>>3],+O[t>>3]),w[n+24>>0]=1}function wt(n,r,t){n=n|0,r=+r,t=+t,O[n>>3]=r,O[n+8>>3]=t}function Fh(){return 1472}function xh(n,r){return n=+n,r=+r,Dh(n,r)|0}function Dh(n,r){n=+n,r=+r;var t=0,u=0,o=0,f=0,l=0,c=0,v=0;return u=s,s=s+16|0,l=u+4|0,c=u+8|0,v=u,o=b0(8)|0,t=o,f=q(16)|0,L2(l,n),n=+B2(l,n),L2(c,r),wt(f,n,+B2(c,r)),c=t+4|0,e[c>>2]=f,f=q(8)|0,c=e[c>>2]|0,e[v>>2]=0,e[l>>2]=e[v>>2],gt(f,c,l),e[o>>2]=f,s=u,t|0}function gt(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,t=q(16)|0,e[t+4>>2]=0,e[t+8>>2]=0,e[t>>2]=1452,e[t+12>>2]=r,e[n+4>>2]=t}function Hh(n){n=n|0,Q1(n),I(n)}function Uh(n){n=n|0,n=e[n+12>>2]|0,n|0&&I(n)}function qh(n){n=n|0,I(n)}function zh(){var n=0;return w[7928]|0||(yt(10488),$(59,10488,V|0)|0,n=7928,e[n>>2]=1,e[n+4>>2]=0),e0(10488)|0||yt(10488),10488}function yt(n){n=n|0,Vh(n),N1(n,60)}function Wh(n){n=n|0,Yh(n+24|0)}function Yh(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function Vh(n){n=n|0;var r=0;r=f0()|0,l0(n,5,6,r,Jh()|0,0),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function Gh(n){n=n|0,Kh(n)}function Kh(n){n=n|0,Xh(n)}function Xh(n){n=n|0,kt(n+8|0),w[n+24>>0]=1}function kt(n){n=n|0,e[n>>2]=0,e[n+4>>2]=0,e[n+8>>2]=0,e[n+12>>2]=0}function Jh(){return 1492}function Zh(){return $h()|0}function $h(){var n=0,r=0,t=0,u=0,o=0,f=0,l=0;return r=s,s=s+16|0,o=r+4|0,l=r,t=b0(8)|0,n=t,u=q(16)|0,kt(u),f=n+4|0,e[f>>2]=u,u=q(8)|0,f=e[f>>2]|0,e[l>>2]=0,e[o>>2]=e[l>>2],gt(u,f,o),e[t>>2]=u,s=r,n|0}function Qh(){var n=0;return w[7936]|0||(td(10524),$(25,10524,V|0)|0,n=7936,e[n>>2]=1,e[n+4>>2]=0),10524}function ah(n,r){n=n|0,r=r|0,e[n>>2]=bh()|0,e[n+4>>2]=nd()|0,e[n+12>>2]=r,e[n+8>>2]=ed()|0,e[n+32>>2]=7}function bh(){return 11700}function nd(){return 1484}function ed(){return An()|0}function rd(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0,(c1(u,896)|0)==512?t|0&&(id(t),I(t)):r|0&&I(r)}function id(n){n=n|0,n=e[n+4>>2]|0,n|0&&v1(n)}function td(n){n=n|0,u1(n)}function ud(n,r,t){n=n|0,r=r|0,t=t|0,n=h0(r)|0,r=od(t)|0,t=fd(t,0)|0,Dd(n,r,t,He()|0,0)}function od(n){return n=n|0,n|0}function fd(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0;return c=s,s=s+16|0,o=c,f=c+4|0,e[o>>2]=n,v=He()|0,l=v+24|0,r=J(r,4)|0,e[f>>2]=r,t=v+28|0,u=e[t>>2]|0,u>>>0<(e[v+32>>2]|0)>>>0?(Tt(u,n,r),r=(e[t>>2]|0)+8|0,e[t>>2]=r):(dd(l,o,f),r=e[t>>2]|0),s=c,(r-(e[l>>2]|0)>>3)+-1|0}function He(){var n=0,r=0;if(w[7944]|0||(Mt(10568),$(61,10568,V|0)|0,r=7944,e[r>>2]=1,e[r+4>>2]=0),!(e0(10568)|0)){n=10568,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Mt(10568)}return 10568}function Mt(n){n=n|0,sd(n)}function ld(n){n=n|0,cd(n+24|0)}function cd(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function sd(n){n=n|0;var r=0;r=f0()|0,l0(n,1,17,r,ki()|0,0),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function vd(n){return n=n|0,hd(e[(_d(n)|0)>>2]|0)|0}function _d(n){return n=n|0,(e[(He()|0)+24>>2]|0)+(n<<3)|0}function hd(n){return n=n|0,Mn(Fn[n&7]()|0)|0}function Tt(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,e[n+4>>2]=t}function dd(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0;if(c=s,s=s+32|0,o=c,f=n+4|0,l=((e[f>>2]|0)-(e[n>>2]|0)>>3)+1|0,u=md(n)|0,u>>>0<l>>>0)c0(n);else{v=e[n>>2]|0,d=(e[n+8>>2]|0)-v|0,m=d>>2,pd(o,d>>3>>>0<u>>>1>>>0?m>>>0<l>>>0?l:m:u,(e[f>>2]|0)-v>>3,n+8|0),l=o+8|0,Tt(e[l>>2]|0,e[r>>2]|0,e[t>>2]|0),e[l>>2]=(e[l>>2]|0)+8,wd(n,o),gd(o),s=c;return}}function md(n){return n=n|0,536870911}function pd(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>536870911)u0();else{o=q(r<<3)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t<<3)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r<<3)}function wd(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(0-(o>>3)<<3)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function gd(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~((u+-8-r|0)>>>3)<<3)),n=e[n>>2]|0,n|0&&I(n)}function yd(){kd()}function kd(){Md(10604)}function Md(n){n=n|0,Td(n,4955)}function Td(n,r){n=n|0,r=r|0;var t=0;t=Ad()|0,e[n>>2]=t,Cd(t,r),s1(e[n>>2]|0)}function Ad(){var n=0;return w[7952]|0||(jd(10612),$(25,10612,V|0)|0,n=7952,e[n>>2]=1,e[n+4>>2]=0),10612}function Cd(n,r){n=n|0,r=r|0,e[n>>2]=Bd()|0,e[n+4>>2]=Rd()|0,e[n+12>>2]=r,e[n+8>>2]=Pd()|0,e[n+32>>2]=8}function s1(n){n=n|0;var r=0,t=0;r=s,s=s+16|0,t=r,K1()|0,e[t>>2]=n,Sd(10608,t),s=r}function K1(){return w[11714]|0||(e[2652]=0,$(62,10608,V|0)|0,w[11714]=1),10608}function Sd(n,r){n=n|0,r=r|0;var t=0;t=q(8)|0,e[t+4>>2]=e[r>>2],e[t>>2]=e[n>>2],e[n>>2]=t}function Ed(n){n=n|0,Ld(n)}function Ld(n){n=n|0;var r=0,t=0;if(r=e[n>>2]|0,r|0)do t=r,r=e[r>>2]|0,I(t);while(r|0);e[n>>2]=0}function Bd(){return 11715}function Rd(){return 1496}function Pd(){return Tn()|0}function Od(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0,(c1(u,896)|0)==512?t|0&&(Nd(t),I(t)):r|0&&I(r)}function Nd(n){n=n|0,n=e[n+4>>2]|0,n|0&&v1(n)}function jd(n){n=n|0,u1(n)}function Id(n,r){n=n|0,r=r|0;var t=0,u=0;K1()|0,t=e[2652]|0;n:do if(t|0){for(;u=e[t+4>>2]|0,!(u|0&&!(iu(Ue(u)|0,n)|0));)if(t=e[t>>2]|0,!t)break n;Fd(u,r)}while(!1)}function Ue(n){return n=n|0,e[n+12>>2]|0}function Fd(n,r){n=n|0,r=r|0;var t=0;n=n+36|0,t=e[n>>2]|0,t|0&&(V1(t),I(t)),t=q(4)|0,$r(t,r),e[n>>2]=t}function qe(){return w[11716]|0||(e[2664]=0,$(63,10656,V|0)|0,w[11716]=1),10656}function At(){var n=0;return w[11717]|0?n=e[2665]|0:(xd(),e[2665]=1504,w[11717]=1,n=1504),n|0}function xd(){w[11740]|0||(w[11718]=J(J(8,0)|0,0)|0,w[11719]=J(J(0,0)|0,0)|0,w[11720]=J(J(0,16)|0,0)|0,w[11721]=J(J(8,0)|0,0)|0,w[11722]=J(J(0,0)|0,0)|0,w[11723]=J(J(8,0)|0,0)|0,w[11724]=J(J(0,0)|0,0)|0,w[11725]=J(J(8,0)|0,0)|0,w[11726]=J(J(0,0)|0,0)|0,w[11727]=J(J(8,0)|0,0)|0,w[11728]=J(J(0,0)|0,0)|0,w[11729]=J(J(0,0)|0,32)|0,w[11730]=J(J(0,0)|0,32)|0,w[11740]=1)}function Ct(){return 1572}function Dd(n,r,t,u,o){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0;var f=0,l=0,c=0,v=0,m=0,d=0;f=s,s=s+32|0,d=f+16|0,m=f+12|0,v=f+8|0,c=f+4|0,l=f,e[d>>2]=n,e[m>>2]=r,e[v>>2]=t,e[c>>2]=u,e[l>>2]=o,qe()|0,Hd(10656,d,m,v,c,l),s=f}function Hd(n,r,t,u,o,f){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0;var l=0;l=q(24)|0,br(l+4|0,e[r>>2]|0,e[t>>2]|0,e[u>>2]|0,e[o>>2]|0,e[f>>2]|0),e[l>>2]=e[n>>2],e[n>>2]=l}function St(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0,L=0,C=0,A=0,P=0,R=0,j=0;if(j=s,s=s+32|0,C=j+20|0,A=j+8|0,P=j+4|0,R=j,r=e[r>>2]|0,r|0){L=C+4|0,v=C+8|0,m=A+4|0,d=A+8|0,g=A+8|0,M=C+8|0;do{if(l=r+4|0,c=ze(l)|0,c|0){if(o=on(c)|0,e[C>>2]=0,e[L>>2]=0,e[v>>2]=0,u=(fn(c)|0)+1|0,Ud(C,u),u|0)for(;u=u+-1|0,m2(A,e[o>>2]|0),f=e[L>>2]|0,f>>>0<(e[M>>2]|0)>>>0?(e[f>>2]=e[A>>2],e[L>>2]=(e[L>>2]|0)+4):We(C,A),u;)o=o+4|0;u=ln(c)|0,e[A>>2]=0,e[m>>2]=0,e[d>>2]=0;n:do if(e[u>>2]|0)for(o=0,f=0;;){if((o|0)==(f|0)?qd(A,u):(e[o>>2]=e[u>>2],e[m>>2]=(e[m>>2]|0)+4),u=u+4|0,!(e[u>>2]|0))break n;o=e[m>>2]|0,f=e[g>>2]|0}while(!1);e[P>>2]=Cn(l)|0,e[R>>2]=e0(c)|0,zd(t,n,P,R,C,A),Ye(A),G2(C)}r=e[r>>2]|0}while(r|0)}s=j}function ze(n){return n=n|0,e[n+12>>2]|0}function on(n){return n=n|0,e[n+12>>2]|0}function fn(n){return n=n|0,e[n+16>>2]|0}function Ud(n,r){n=n|0,r=r|0;var t=0,u=0,o=0;o=s,s=s+32|0,t=o,u=e[n>>2]|0,(e[n+8>>2]|0)-u>>2>>>0<r>>>0&&(jt(t,r,(e[n+4>>2]|0)-u>>2,n+8|0),It(n,t),Ft(t)),s=o}function We(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0;if(l=s,s=s+32|0,t=l,u=n+4|0,o=((e[u>>2]|0)-(e[n>>2]|0)>>2)+1|0,f=Nt(n)|0,f>>>0<o>>>0)c0(n);else{c=e[n>>2]|0,m=(e[n+8>>2]|0)-c|0,v=m>>1,jt(t,m>>2>>>0<f>>>1>>>0?v>>>0<o>>>0?o:v:f,(e[u>>2]|0)-c>>2,n+8|0),f=t+8|0,e[e[f>>2]>>2]=e[r>>2],e[f>>2]=(e[f>>2]|0)+4,It(n,t),Ft(t),s=l;return}}function ln(n){return n=n|0,e[n+8>>2]|0}function qd(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0;if(l=s,s=s+32|0,t=l,u=n+4|0,o=((e[u>>2]|0)-(e[n>>2]|0)>>2)+1|0,f=Ot(n)|0,f>>>0<o>>>0)c0(n);else{c=e[n>>2]|0,m=(e[n+8>>2]|0)-c|0,v=m>>1,om(t,m>>2>>>0<f>>>1>>>0?v>>>0<o>>>0?o:v:f,(e[u>>2]|0)-c>>2,n+8|0),f=t+8|0,e[e[f>>2]>>2]=e[r>>2],e[f>>2]=(e[f>>2]|0)+4,fm(n,t),lm(t),s=l;return}}function Cn(n){return n=n|0,e[n>>2]|0}function zd(n,r,t,u,o,f){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0,Wd(n,r,t,u,o,f)}function Ye(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-4-u|0)>>>2)<<2)),I(t))}function G2(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-4-u|0)>>>2)<<2)),I(t))}function Wd(n,r,t,u,o,f){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0;var l=0,c=0,v=0,m=0,d=0,g=0;l=s,s=s+48|0,d=l+40|0,c=l+32|0,g=l+24|0,v=l+12|0,m=l,n2(c),n=X0(n)|0,e[g>>2]=e[r>>2],t=e[t>>2]|0,u=e[u>>2]|0,Ve(v,o),Yd(m,f),e[d>>2]=e[g>>2],Vd(n,d,t,u,v,m),Ye(m),G2(v),e2(c),s=l}function Ve(n,r){n=n|0,r=r|0;var t=0,u=0;e[n>>2]=0,e[n+4>>2]=0,e[n+8>>2]=0,t=r+4|0,u=(e[t>>2]|0)-(e[r>>2]|0)>>2,u|0&&(tm(n,u),um(n,e[r>>2]|0,e[t>>2]|0,u))}function Yd(n,r){n=n|0,r=r|0;var t=0,u=0;e[n>>2]=0,e[n+4>>2]=0,e[n+8>>2]=0,t=r+4|0,u=(e[t>>2]|0)-(e[r>>2]|0)>>2,u|0&&(rm(n,u),im(n,e[r>>2]|0,e[t>>2]|0,u))}function Vd(n,r,t,u,o,f){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0;var l=0,c=0,v=0,m=0,d=0,g=0;l=s,s=s+32|0,d=l+28|0,g=l+24|0,c=l+12|0,v=l,m=u2(Gd()|0)|0,e[g>>2]=e[r>>2],e[d>>2]=e[g>>2],r=j1(d)|0,t=Et(t)|0,u=Ge(u)|0,e[c>>2]=e[o>>2],d=o+4|0,e[c+4>>2]=e[d>>2],g=o+8|0,e[c+8>>2]=e[g>>2],e[g>>2]=0,e[d>>2]=0,e[o>>2]=0,o=Ke(c)|0,e[v>>2]=e[f>>2],d=f+4|0,e[v+4>>2]=e[d>>2],g=f+8|0,e[v+8>>2]=e[g>>2],e[g>>2]=0,e[d>>2]=0,e[f>>2]=0,Fu(0,m|0,n|0,r|0,t|0,u|0,o|0,Kd(v)|0)|0,Ye(v),G2(c),s=l}function Gd(){var n=0;return w[7968]|0||(nm(10708),n=7968,e[n>>2]=1,e[n+4>>2]=0),10708}function j1(n){return n=n|0,Bt(n)|0}function Et(n){return n=n|0,Lt(n)|0}function Ge(n){return n=n|0,Mn(n)|0}function Ke(n){return n=n|0,Jd(n)|0}function Kd(n){return n=n|0,Xd(n)|0}function Xd(n){n=n|0;var r=0,t=0,u=0;if(u=(e[n+4>>2]|0)-(e[n>>2]|0)|0,t=u>>2,u=b0(u+4|0)|0,e[u>>2]=t,t|0){r=0;do e[u+4+(r<<2)>>2]=Lt(e[(e[n>>2]|0)+(r<<2)>>2]|0)|0,r=r+1|0;while((r|0)!=(t|0))}return u|0}function Lt(n){return n=n|0,n|0}function Jd(n){n=n|0;var r=0,t=0,u=0;if(u=(e[n+4>>2]|0)-(e[n>>2]|0)|0,t=u>>2,u=b0(u+4|0)|0,e[u>>2]=t,t|0){r=0;do e[u+4+(r<<2)>>2]=Bt((e[n>>2]|0)+(r<<2)|0)|0,r=r+1|0;while((r|0)!=(t|0))}return u|0}function Bt(n){n=n|0;var r=0,t=0,u=0,o=0;return o=s,s=s+32|0,r=o+12|0,t=o,u=te(Rt()|0)|0,u?(ue(r,u),oe(t,r),Pg(n,t),n=fe(r)|0):n=Zd(n)|0,s=o,n|0}function Rt(){var n=0;return w[7960]|0||(bd(10664),$(25,10664,V|0)|0,n=7960,e[n>>2]=1,e[n+4>>2]=0),10664}function Zd(n){n=n|0;var r=0,t=0,u=0,o=0,f=0,l=0,c=0;return t=s,s=s+16|0,o=t+4|0,l=t,u=b0(8)|0,r=u,c=q(4)|0,e[c>>2]=e[n>>2],f=r+4|0,e[f>>2]=c,n=q(8)|0,f=e[f>>2]|0,e[l>>2]=0,e[o>>2]=e[l>>2],Pt(n,f,o),e[u>>2]=n,s=t,r|0}function Pt(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,t=q(16)|0,e[t+4>>2]=0,e[t+8>>2]=0,e[t>>2]=1656,e[t+12>>2]=r,e[n+4>>2]=t}function $d(n){n=n|0,Q1(n),I(n)}function Qd(n){n=n|0,n=e[n+12>>2]|0,n|0&&I(n)}function ad(n){n=n|0,I(n)}function bd(n){n=n|0,u1(n)}function nm(n){n=n|0,o2(n,em()|0,5)}function em(){return 1676}function rm(n,r){n=n|0,r=r|0;var t=0;if((Ot(n)|0)>>>0<r>>>0&&c0(n),r>>>0>1073741823)u0();else{t=q(r<<2)|0,e[n+4>>2]=t,e[n>>2]=t,e[n+8>>2]=t+(r<<2);return}}function im(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0,u=n+4|0,n=t-r|0,(n|0)>0&&(Q(e[u>>2]|0,r|0,n|0)|0,e[u>>2]=(e[u>>2]|0)+(n>>>2<<2))}function Ot(n){return n=n|0,1073741823}function tm(n,r){n=n|0,r=r|0;var t=0;if((Nt(n)|0)>>>0<r>>>0&&c0(n),r>>>0>1073741823)u0();else{t=q(r<<2)|0,e[n+4>>2]=t,e[n>>2]=t,e[n+8>>2]=t+(r<<2);return}}function um(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0,u=n+4|0,n=t-r|0,(n|0)>0&&(Q(e[u>>2]|0,r|0,n|0)|0,e[u>>2]=(e[u>>2]|0)+(n>>>2<<2))}function Nt(n){return n=n|0,1073741823}function om(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>1073741823)u0();else{o=q(r<<2)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t<<2)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r<<2)}function fm(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(0-(o>>2)<<2)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function lm(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~((u+-4-r|0)>>>2)<<2)),n=e[n>>2]|0,n|0&&I(n)}function jt(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>1073741823)u0();else{o=q(r<<2)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t<<2)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r<<2)}function It(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(0-(o>>2)<<2)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function Ft(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~((u+-4-r|0)>>>2)<<2)),n=e[n>>2]|0,n|0&&I(n)}function cm(n,r,t,u,o){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0;var f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0,L=0,C=0,A=0;if(A=s,s=s+32|0,d=A+20|0,g=A+12|0,m=A+16|0,M=A+4|0,L=A,C=A+8|0,c=At()|0,f=e[c>>2]|0,l=e[f>>2]|0,l|0)for(v=e[c+8>>2]|0,c=e[c+4>>2]|0;m2(d,l),sm(n,d,c,v),f=f+4|0,l=e[f>>2]|0,l;)v=v+1|0,c=c+1|0;if(f=Ct()|0,l=e[f>>2]|0,l|0)do m2(d,l),e[g>>2]=e[f+4>>2],vm(r,d,g),f=f+8|0,l=e[f>>2]|0;while(l|0);if(f=e[(K1()|0)>>2]|0,f|0)do r=e[f+4>>2]|0,m2(d,e[(X1(r)|0)>>2]|0),e[g>>2]=Ue(r)|0,_m(t,d,g),f=e[f>>2]|0;while(f|0);if(m2(m,0),f=qe()|0,e[d>>2]=e[m>>2],St(d,f,o),f=e[(K1()|0)>>2]|0,f|0){n=d+4|0,r=d+8|0,t=d+8|0;do{if(v=e[f+4>>2]|0,m2(g,e[(X1(v)|0)>>2]|0),hm(M,xt(v)|0),l=e[M>>2]|0,l|0){e[d>>2]=0,e[n>>2]=0,e[r>>2]=0;do m2(L,e[(X1(e[l+4>>2]|0)|0)>>2]|0),c=e[n>>2]|0,c>>>0<(e[t>>2]|0)>>>0?(e[c>>2]=e[L>>2],e[n>>2]=(e[n>>2]|0)+4):We(d,L),l=e[l>>2]|0;while(l|0);dm(u,g,d),G2(d)}e[C>>2]=e[g>>2],m=Dt(v)|0,e[d>>2]=e[C>>2],St(d,m,o),ui(M),f=e[f>>2]|0}while(f|0)}s=A}function sm(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0,Em(n,r,t,u)}function vm(n,r,t){n=n|0,r=r|0,t=t|0,Sm(n,r,t)}function X1(n){return n=n|0,n|0}function _m(n,r,t){n=n|0,r=r|0,t=t|0,Mm(n,r,t)}function xt(n){return n=n|0,n+16|0}function hm(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0;if(f=s,s=s+16|0,o=f+8|0,t=f,e[n>>2]=0,u=e[r>>2]|0,e[o>>2]=u,e[t>>2]=n,t=km(t)|0,u|0){if(u=q(12)|0,l=(Ht(o)|0)+4|0,n=e[l+4>>2]|0,r=u+4|0,e[r>>2]=e[l>>2],e[r+4>>2]=n,r=e[e[o>>2]>>2]|0,e[o>>2]=r,!r)n=u;else for(r=u;n=q(12)|0,v=(Ht(o)|0)+4|0,c=e[v+4>>2]|0,l=n+4|0,e[l>>2]=e[v>>2],e[l+4>>2]=c,e[r>>2]=n,l=e[e[o>>2]>>2]|0,e[o>>2]=l,l;)r=n;e[n>>2]=e[t>>2],e[t>>2]=u}s=f}function dm(n,r,t){n=n|0,r=r|0,t=t|0,mm(n,r,t)}function Dt(n){return n=n|0,n+24|0}function mm(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+32|0,l=u+24|0,o=u+16|0,c=u+12|0,f=u,n2(o),n=X0(n)|0,e[c>>2]=e[r>>2],Ve(f,t),e[l>>2]=e[c>>2],pm(n,l,f),G2(f),e2(o),s=u}function pm(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=s,s=s+32|0,l=u+16|0,c=u+12|0,o=u,f=u2(wm()|0)|0,e[c>>2]=e[r>>2],e[l>>2]=e[c>>2],r=j1(l)|0,e[o>>2]=e[t>>2],l=t+4|0,e[o+4>>2]=e[l>>2],c=t+8|0,e[o+8>>2]=e[c>>2],e[c>>2]=0,e[l>>2]=0,e[t>>2]=0,Un(0,f|0,n|0,r|0,Ke(o)|0)|0,G2(o),s=u}function wm(){var n=0;return w[7976]|0||(gm(10720),n=7976,e[n>>2]=1,e[n+4>>2]=0),10720}function gm(n){n=n|0,o2(n,ym()|0,2)}function ym(){return 1732}function km(n){return n=n|0,e[n>>2]|0}function Ht(n){return n=n|0,e[n>>2]|0}function Mm(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;u=s,s=s+32|0,f=u+16|0,o=u+8|0,l=u,n2(o),n=X0(n)|0,e[l>>2]=e[r>>2],t=e[t>>2]|0,e[f>>2]=e[l>>2],Ut(n,f,t),e2(o),s=u}function Ut(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;u=s,s=s+16|0,f=u+4|0,l=u,o=u2(Tm()|0)|0,e[l>>2]=e[r>>2],e[f>>2]=e[l>>2],r=j1(f)|0,Un(0,o|0,n|0,r|0,Et(t)|0)|0,s=u}function Tm(){var n=0;return w[7984]|0||(Am(10732),n=7984,e[n>>2]=1,e[n+4>>2]=0),10732}function Am(n){n=n|0,o2(n,Cm()|0,2)}function Cm(){return 1744}function Sm(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;u=s,s=s+32|0,f=u+16|0,o=u+8|0,l=u,n2(o),n=X0(n)|0,e[l>>2]=e[r>>2],t=e[t>>2]|0,e[f>>2]=e[l>>2],Ut(n,f,t),e2(o),s=u}function Em(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0;o=s,s=s+32|0,l=o+16|0,f=o+8|0,c=o,n2(f),n=X0(n)|0,e[c>>2]=e[r>>2],t=w[t>>0]|0,u=w[u>>0]|0,e[l>>2]=e[c>>2],Lm(n,l,t,u),e2(f),s=o}function Lm(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0;o=s,s=s+16|0,l=o+4|0,c=o,f=u2(Bm()|0)|0,e[c>>2]=e[r>>2],e[l>>2]=e[c>>2],r=j1(l)|0,t=J1(t)|0,qn(0,f|0,n|0,r|0,t|0,J1(u)|0)|0,s=o}function Bm(){var n=0;return w[7992]|0||(Pm(10744),n=7992,e[n>>2]=1,e[n+4>>2]=0),10744}function J1(n){return n=n|0,Rm(n)|0}function Rm(n){return n=n|0,n&255|0}function Pm(n){n=n|0,o2(n,Om()|0,3)}function Om(){return 1756}function Nm(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;switch(M=s,s=s+32|0,c=M+8|0,v=M+4|0,m=M+20|0,d=M,ve(n,0),u=Rg(r)|0,e[c>>2]=0,g=c+4|0,e[g>>2]=0,e[c+8>>2]=0,u<<24>>24){case 0:{w[m>>0]=0,jm(v,t,m),Sn(n,v)|0,G1(v);break}case 8:{g=ae(r)|0,w[m>>0]=8,m2(d,e[g+4>>2]|0),Im(v,t,m,d,g+8|0),Sn(n,v)|0,G1(v);break}case 9:{if(f=ae(r)|0,r=e[f+4>>2]|0,r|0)for(l=c+8|0,o=f+12|0;r=r+-1|0,m2(v,e[o>>2]|0),u=e[g>>2]|0,u>>>0<(e[l>>2]|0)>>>0?(e[u>>2]=e[v>>2],e[g>>2]=(e[g>>2]|0)+4):We(c,v),r;)o=o+4|0;w[m>>0]=9,m2(d,e[f+8>>2]|0),Fm(v,t,m,d,c),Sn(n,v)|0,G1(v);break}default:g=ae(r)|0,w[m>>0]=u,m2(d,e[g+4>>2]|0),xm(v,t,m,d),Sn(n,v)|0,G1(v)}G2(c),s=M}function jm(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0;u=s,s=s+16|0,o=u,n2(o),r=X0(r)|0,Zm(n,r,w[t>>0]|0),e2(o),s=u}function Sn(n,r){n=n|0,r=r|0;var t=0;return t=e[n>>2]|0,t|0&&dr(t|0),e[n>>2]=e[r>>2],e[r>>2]=0,n|0}function Im(n,r,t,u,o){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0;var f=0,l=0,c=0,v=0;f=s,s=s+32|0,c=f+16|0,l=f+8|0,v=f,n2(l),r=X0(r)|0,t=w[t>>0]|0,e[v>>2]=e[u>>2],o=e[o>>2]|0,e[c>>2]=e[v>>2],Gm(n,r,t,c,o),e2(l),s=f}function Fm(n,r,t,u,o){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0;var f=0,l=0,c=0,v=0,m=0;f=s,s=s+32|0,v=f+24|0,l=f+16|0,m=f+12|0,c=f,n2(l),r=X0(r)|0,t=w[t>>0]|0,e[m>>2]=e[u>>2],Ve(c,o),e[v>>2]=e[m>>2],zm(n,r,t,v,c),G2(c),e2(l),s=f}function xm(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0;o=s,s=s+32|0,l=o+16|0,f=o+8|0,c=o,n2(f),r=X0(r)|0,t=w[t>>0]|0,e[c>>2]=e[u>>2],e[l>>2]=e[c>>2],Dm(n,r,t,l),e2(f),s=o}function Dm(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0,l=0,c=0;o=s,s=s+16|0,f=o+4|0,c=o,l=u2(Hm()|0)|0,t=J1(t)|0,e[c>>2]=e[u>>2],e[f>>2]=e[c>>2],En(n,Un(0,l|0,r|0,t|0,j1(f)|0)|0),s=o}function Hm(){var n=0;return w[8e3]|0||(Um(10756),n=8e3,e[n>>2]=1,e[n+4>>2]=0),10756}function En(n,r){n=n|0,r=r|0,ve(n,r)}function Um(n){n=n|0,o2(n,qm()|0,2)}function qm(){return 1772}function zm(n,r,t,u,o){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0;var f=0,l=0,c=0,v=0,m=0;f=s,s=s+32|0,v=f+16|0,m=f+12|0,l=f,c=u2(Wm()|0)|0,t=J1(t)|0,e[m>>2]=e[u>>2],e[v>>2]=e[m>>2],u=j1(v)|0,e[l>>2]=e[o>>2],v=o+4|0,e[l+4>>2]=e[v>>2],m=o+8|0,e[l+8>>2]=e[m>>2],e[m>>2]=0,e[v>>2]=0,e[o>>2]=0,En(n,qn(0,c|0,r|0,t|0,u|0,Ke(l)|0)|0),G2(l),s=f}function Wm(){var n=0;return w[8008]|0||(Ym(10768),n=8008,e[n>>2]=1,e[n+4>>2]=0),10768}function Ym(n){n=n|0,o2(n,Vm()|0,3)}function Vm(){return 1784}function Gm(n,r,t,u,o){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0;var f=0,l=0,c=0,v=0;f=s,s=s+16|0,c=f+4|0,v=f,l=u2(Km()|0)|0,t=J1(t)|0,e[v>>2]=e[u>>2],e[c>>2]=e[v>>2],u=j1(c)|0,En(n,qn(0,l|0,r|0,t|0,u|0,Ge(o)|0)|0),s=f}function Km(){var n=0;return w[8016]|0||(Xm(10780),n=8016,e[n>>2]=1,e[n+4>>2]=0),10780}function Xm(n){n=n|0,o2(n,Jm()|0,3)}function Jm(){return 1800}function Zm(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;u=u2($m()|0)|0,En(n,mr(0,u|0,r|0,J1(t)|0)|0)}function $m(){var n=0;return w[8024]|0||(Qm(10792),n=8024,e[n>>2]=1,e[n+4>>2]=0),10792}function Qm(n){n=n|0,o2(n,am()|0,1)}function am(){return 1816}function bm(){np(),ep(),rp()}function np(){e[2702]=hu(65536)|0}function ep(){Tp(10856)}function rp(){ip(10816)}function ip(n){n=n|0,tp(n,5044),up(n)|0}function tp(n,r){n=n|0,r=r|0;var t=0;t=Rt()|0,e[n>>2]=t,pp(t,r),s1(e[n>>2]|0)}function up(n){n=n|0;var r=0;return r=e[n>>2]|0,O1(r,op()|0),n|0}function op(){var n=0;return w[8032]|0||(qt(10820),$(64,10820,V|0)|0,n=8032,e[n>>2]=1,e[n+4>>2]=0),e0(10820)|0||qt(10820),10820}function qt(n){n=n|0,cp(n),N1(n,25)}function fp(n){n=n|0,lp(n+24|0)}function lp(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function cp(n){n=n|0;var r=0;r=f0()|0,l0(n,5,18,r,hp()|0,1),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function sp(n,r){n=n|0,r=r|0,vp(n,r)}function vp(n,r){n=n|0,r=r|0;var t=0,u=0,o=0;t=s,s=s+16|0,u=t,o=t+4|0,R1(o,r),e[u>>2]=P1(o,r)|0,_p(n,u),s=t}function _p(n,r){n=n|0,r=r|0,zt(n+4|0,e[r>>2]|0),w[n+8>>0]=1}function zt(n,r){n=n|0,r=r|0,e[n>>2]=r}function hp(){return 1824}function dp(n){return n=n|0,mp(n)|0}function mp(n){n=n|0;var r=0,t=0,u=0,o=0,f=0,l=0,c=0;return t=s,s=s+16|0,o=t+4|0,l=t,u=b0(8)|0,r=u,c=q(4)|0,R1(o,n),zt(c,P1(o,n)|0),f=r+4|0,e[f>>2]=c,n=q(8)|0,f=e[f>>2]|0,e[l>>2]=0,e[o>>2]=e[l>>2],Pt(n,f,o),e[u>>2]=n,s=t,r|0}function b0(n){n=n|0;var r=0,t=0;return n=n+7&-8,n>>>0<=32768&&(r=e[2701]|0,n>>>0<=(65536-r|0)>>>0)?(t=(e[2702]|0)+r|0,e[2701]=r+n,n=t):(n=hu(n+8|0)|0,e[n>>2]=e[2703],e[2703]=n,n=n+8|0),n|0}function pp(n,r){n=n|0,r=r|0,e[n>>2]=wp()|0,e[n+4>>2]=gp()|0,e[n+12>>2]=r,e[n+8>>2]=yp()|0,e[n+32>>2]=9}function wp(){return 11744}function gp(){return 1832}function yp(){return An()|0}function kp(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0,(c1(u,896)|0)==512?t|0&&(Mp(t),I(t)):r|0&&I(r)}function Mp(n){n=n|0,n=e[n+4>>2]|0,n|0&&v1(n)}function Tp(n){n=n|0,Ap(n,5052),Cp(n)|0,Sp(n,5058,26)|0,Ep(n,5069,1)|0,Lp(n,5077,10)|0,Bp(n,5087,19)|0,Rp(n,5094,27)|0}function Ap(n,r){n=n|0,r=r|0;var t=0;t=Mg()|0,e[n>>2]=t,Tg(t,r),s1(e[n>>2]|0)}function Cp(n){n=n|0;var r=0;return r=e[n>>2]|0,O1(r,fg()|0),n|0}function Sp(n,r,t){return n=n|0,r=r|0,t=t|0,Yw(n,h0(r)|0,t,0),n|0}function Ep(n,r,t){return n=n|0,r=r|0,t=t|0,Bw(n,h0(r)|0,t,0),n|0}function Lp(n,r,t){return n=n|0,r=r|0,t=t|0,ow(n,h0(r)|0,t,0),n|0}function Bp(n,r,t){return n=n|0,r=r|0,t=t|0,Gp(n,h0(r)|0,t,0),n|0}function Wt(n,r){n=n|0,r=r|0;var t=0,u=0;n:for(;;){for(t=e[2703]|0;;){if((t|0)==(r|0))break n;if(u=e[t>>2]|0,e[2703]=u,!t)t=u;else break}I(t)}e[2701]=n}function Rp(n,r,t){return n=n|0,r=r|0,t=t|0,Pp(n,h0(r)|0,t,0),n|0}function Pp(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0;f=e[n>>2]|0,o=Xe()|0,n=Op(t)|0,d0(f,r,o,n,Np(t,u)|0,u)}function Xe(){var n=0,r=0;if(w[8040]|0||(Vt(10860),$(65,10860,V|0)|0,r=8040,e[r>>2]=1,e[r+4>>2]=0),!(e0(10860)|0)){n=10860,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Vt(10860)}return 10860}function Op(n){return n=n|0,n|0}function Np(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0;return c=s,s=s+16|0,o=c,f=c+4|0,e[o>>2]=n,v=Xe()|0,l=v+24|0,r=J(r,4)|0,e[f>>2]=r,t=v+28|0,u=e[t>>2]|0,u>>>0<(e[v+32>>2]|0)>>>0?(Yt(u,n,r),r=(e[t>>2]|0)+8|0,e[t>>2]=r):(jp(l,o,f),r=e[t>>2]|0),s=c,(r-(e[l>>2]|0)>>3)+-1|0}function Yt(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,e[n+4>>2]=t}function jp(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0;if(c=s,s=s+32|0,o=c,f=n+4|0,l=((e[f>>2]|0)-(e[n>>2]|0)>>3)+1|0,u=Ip(n)|0,u>>>0<l>>>0)c0(n);else{v=e[n>>2]|0,d=(e[n+8>>2]|0)-v|0,m=d>>2,Fp(o,d>>3>>>0<u>>>1>>>0?m>>>0<l>>>0?l:m:u,(e[f>>2]|0)-v>>3,n+8|0),l=o+8|0,Yt(e[l>>2]|0,e[r>>2]|0,e[t>>2]|0),e[l>>2]=(e[l>>2]|0)+8,xp(n,o),Dp(o),s=c;return}}function Ip(n){return n=n|0,536870911}function Fp(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>536870911)u0();else{o=q(r<<3)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t<<3)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r<<3)}function xp(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(0-(o>>3)<<3)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function Dp(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~((u+-8-r|0)>>>3)<<3)),n=e[n>>2]|0,n|0&&I(n)}function Vt(n){n=n|0,qp(n)}function Hp(n){n=n|0,Up(n+24|0)}function Up(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function qp(n){n=n|0;var r=0;r=f0()|0,l0(n,1,11,r,zp()|0,2),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function zp(){return 1840}function Wp(n,r,t){n=n|0,r=r|0,t=t|0,Vp(e[(Yp(n)|0)>>2]|0,r,t)}function Yp(n){return n=n|0,(e[(Xe()|0)+24>>2]|0)+(n<<3)|0}function Vp(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0;u=s,s=s+16|0,f=u+1|0,o=u,R1(f,r),r=P1(f,r)|0,R1(o,t),t=P1(o,t)|0,X2[n&31](r,t),s=u}function Gp(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0;f=e[n>>2]|0,o=Je()|0,n=Kp(t)|0,d0(f,r,o,n,Xp(t,u)|0,u)}function Je(){var n=0,r=0;if(w[8048]|0||(Kt(10896),$(66,10896,V|0)|0,r=8048,e[r>>2]=1,e[r+4>>2]=0),!(e0(10896)|0)){n=10896,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Kt(10896)}return 10896}function Kp(n){return n=n|0,n|0}function Xp(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0;return c=s,s=s+16|0,o=c,f=c+4|0,e[o>>2]=n,v=Je()|0,l=v+24|0,r=J(r,4)|0,e[f>>2]=r,t=v+28|0,u=e[t>>2]|0,u>>>0<(e[v+32>>2]|0)>>>0?(Gt(u,n,r),r=(e[t>>2]|0)+8|0,e[t>>2]=r):(Jp(l,o,f),r=e[t>>2]|0),s=c,(r-(e[l>>2]|0)>>3)+-1|0}function Gt(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,e[n+4>>2]=t}function Jp(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0;if(c=s,s=s+32|0,o=c,f=n+4|0,l=((e[f>>2]|0)-(e[n>>2]|0)>>3)+1|0,u=Zp(n)|0,u>>>0<l>>>0)c0(n);else{v=e[n>>2]|0,d=(e[n+8>>2]|0)-v|0,m=d>>2,$p(o,d>>3>>>0<u>>>1>>>0?m>>>0<l>>>0?l:m:u,(e[f>>2]|0)-v>>3,n+8|0),l=o+8|0,Gt(e[l>>2]|0,e[r>>2]|0,e[t>>2]|0),e[l>>2]=(e[l>>2]|0)+8,Qp(n,o),ap(o),s=c;return}}function Zp(n){return n=n|0,536870911}function $p(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>536870911)u0();else{o=q(r<<3)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t<<3)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r<<3)}function Qp(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(0-(o>>3)<<3)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function ap(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~((u+-8-r|0)>>>3)<<3)),n=e[n>>2]|0,n|0&&I(n)}function Kt(n){n=n|0,ew(n)}function bp(n){n=n|0,nw(n+24|0)}function nw(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function ew(n){n=n|0;var r=0;r=f0()|0,l0(n,1,11,r,rw()|0,1),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function rw(){return 1852}function iw(n,r){return n=n|0,r=r|0,uw(e[(tw(n)|0)>>2]|0,r)|0}function tw(n){return n=n|0,(e[(Je()|0)+24>>2]|0)+(n<<3)|0}function uw(n,r){n=n|0,r=r|0;var t=0,u=0;return t=s,s=s+16|0,u=t,R1(u,r),r=P1(u,r)|0,r=Mn(D1[n&31](r)|0)|0,s=t,r|0}function ow(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0;f=e[n>>2]|0,o=Ze()|0,n=fw(t)|0,d0(f,r,o,n,lw(t,u)|0,u)}function Ze(){var n=0,r=0;if(w[8056]|0||(Jt(10932),$(67,10932,V|0)|0,r=8056,e[r>>2]=1,e[r+4>>2]=0),!(e0(10932)|0)){n=10932,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));Jt(10932)}return 10932}function fw(n){return n=n|0,n|0}function lw(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0;return c=s,s=s+16|0,o=c,f=c+4|0,e[o>>2]=n,v=Ze()|0,l=v+24|0,r=J(r,4)|0,e[f>>2]=r,t=v+28|0,u=e[t>>2]|0,u>>>0<(e[v+32>>2]|0)>>>0?(Xt(u,n,r),r=(e[t>>2]|0)+8|0,e[t>>2]=r):(cw(l,o,f),r=e[t>>2]|0),s=c,(r-(e[l>>2]|0)>>3)+-1|0}function Xt(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,e[n+4>>2]=t}function cw(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0;if(c=s,s=s+32|0,o=c,f=n+4|0,l=((e[f>>2]|0)-(e[n>>2]|0)>>3)+1|0,u=sw(n)|0,u>>>0<l>>>0)c0(n);else{v=e[n>>2]|0,d=(e[n+8>>2]|0)-v|0,m=d>>2,vw(o,d>>3>>>0<u>>>1>>>0?m>>>0<l>>>0?l:m:u,(e[f>>2]|0)-v>>3,n+8|0),l=o+8|0,Xt(e[l>>2]|0,e[r>>2]|0,e[t>>2]|0),e[l>>2]=(e[l>>2]|0)+8,_w(n,o),hw(o),s=c;return}}function sw(n){return n=n|0,536870911}function vw(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>536870911)u0();else{o=q(r<<3)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t<<3)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r<<3)}function _w(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(0-(o>>3)<<3)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function hw(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~((u+-8-r|0)>>>3)<<3)),n=e[n>>2]|0,n|0&&I(n)}function Jt(n){n=n|0,pw(n)}function dw(n){n=n|0,mw(n+24|0)}function mw(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function pw(n){n=n|0;var r=0;r=f0()|0,l0(n,1,7,r,ww()|0,2),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function ww(){return 1860}function gw(n,r,t){return n=n|0,r=r|0,t=t|0,kw(e[(yw(n)|0)>>2]|0,r,t)|0}function yw(n){return n=n|0,(e[(Ze()|0)+24>>2]|0)+(n<<3)|0}function kw(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0;return u=s,s=s+32|0,l=u+12|0,f=u+8|0,c=u,v=u+16|0,o=u+4|0,Mw(v,r),Tw(c,v,r),o1(o,t),t=f1(o,t)|0,e[l>>2]=e[c>>2],_n[n&15](f,l,t),t=Aw(f)|0,G1(f),l1(o),s=u,t|0}function Mw(n,r){n=n|0,r=r|0}function Tw(n,r,t){n=n|0,r=r|0,t=t|0,Cw(n,t)}function Aw(n){return n=n|0,X0(n)|0}function Cw(n,r){n=n|0,r=r|0;var t=0,u=0,o=0;o=s,s=s+16|0,t=o,u=r,u&1?(Sw(t,0),vr(u|0,t|0)|0,Ew(n,t),Lw(t)):e[n>>2]=e[r>>2],s=o}function Sw(n,r){n=n|0,r=r|0,Qr(n,r),e[n+4>>2]=0,w[n+8>>0]=0}function Ew(n,r){n=n|0,r=r|0,e[n>>2]=e[r+4>>2]}function Lw(n){n=n|0,w[n+8>>0]=0}function Bw(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0;f=e[n>>2]|0,o=$e()|0,n=Rw(t)|0,d0(f,r,o,n,Pw(t,u)|0,u)}function $e(){var n=0,r=0;if(w[8064]|0||($t(10968),$(68,10968,V|0)|0,r=8064,e[r>>2]=1,e[r+4>>2]=0),!(e0(10968)|0)){n=10968,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));$t(10968)}return 10968}function Rw(n){return n=n|0,n|0}function Pw(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0;return c=s,s=s+16|0,o=c,f=c+4|0,e[o>>2]=n,v=$e()|0,l=v+24|0,r=J(r,4)|0,e[f>>2]=r,t=v+28|0,u=e[t>>2]|0,u>>>0<(e[v+32>>2]|0)>>>0?(Zt(u,n,r),r=(e[t>>2]|0)+8|0,e[t>>2]=r):(Ow(l,o,f),r=e[t>>2]|0),s=c,(r-(e[l>>2]|0)>>3)+-1|0}function Zt(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,e[n+4>>2]=t}function Ow(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0;if(c=s,s=s+32|0,o=c,f=n+4|0,l=((e[f>>2]|0)-(e[n>>2]|0)>>3)+1|0,u=Nw(n)|0,u>>>0<l>>>0)c0(n);else{v=e[n>>2]|0,d=(e[n+8>>2]|0)-v|0,m=d>>2,jw(o,d>>3>>>0<u>>>1>>>0?m>>>0<l>>>0?l:m:u,(e[f>>2]|0)-v>>3,n+8|0),l=o+8|0,Zt(e[l>>2]|0,e[r>>2]|0,e[t>>2]|0),e[l>>2]=(e[l>>2]|0)+8,Iw(n,o),Fw(o),s=c;return}}function Nw(n){return n=n|0,536870911}function jw(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>536870911)u0();else{o=q(r<<3)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t<<3)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r<<3)}function Iw(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(0-(o>>3)<<3)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function Fw(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~((u+-8-r|0)>>>3)<<3)),n=e[n>>2]|0,n|0&&I(n)}function $t(n){n=n|0,Hw(n)}function xw(n){n=n|0,Dw(n+24|0)}function Dw(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function Hw(n){n=n|0;var r=0;r=f0()|0,l0(n,1,1,r,Uw()|0,5),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function Uw(){return 1872}function qw(n,r,t,u,o,f){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0,Ww(e[(zw(n)|0)>>2]|0,r,t,u,o,f)}function zw(n){return n=n|0,(e[($e()|0)+24>>2]|0)+(n<<3)|0}function Ww(n,r,t,u,o,f){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0;var l=0,c=0,v=0,m=0,d=0,g=0;l=s,s=s+32|0,c=l+16|0,v=l+12|0,m=l+8|0,d=l+4|0,g=l,o1(c,r),r=f1(c,r)|0,o1(v,t),t=f1(v,t)|0,o1(m,u),u=f1(m,u)|0,o1(d,o),o=f1(d,o)|0,o1(g,f),f=f1(g,f)|0,gu[n&1](r,t,u,o,f),l1(g),l1(d),l1(m),l1(v),l1(c),s=l}function Yw(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0;f=e[n>>2]|0,o=Qe()|0,n=Vw(t)|0,d0(f,r,o,n,Gw(t,u)|0,u)}function Qe(){var n=0,r=0;if(w[8072]|0||(at(11004),$(69,11004,V|0)|0,r=8072,e[r>>2]=1,e[r+4>>2]=0),!(e0(11004)|0)){n=11004,r=n+36|0;do e[n>>2]=0,n=n+4|0;while((n|0)<(r|0));at(11004)}return 11004}function Vw(n){return n=n|0,n|0}function Gw(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0,c=0,v=0;return c=s,s=s+16|0,o=c,f=c+4|0,e[o>>2]=n,v=Qe()|0,l=v+24|0,r=J(r,4)|0,e[f>>2]=r,t=v+28|0,u=e[t>>2]|0,u>>>0<(e[v+32>>2]|0)>>>0?(Qt(u,n,r),r=(e[t>>2]|0)+8|0,e[t>>2]=r):(Kw(l,o,f),r=e[t>>2]|0),s=c,(r-(e[l>>2]|0)>>3)+-1|0}function Qt(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,e[n+4>>2]=t}function Kw(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0;if(c=s,s=s+32|0,o=c,f=n+4|0,l=((e[f>>2]|0)-(e[n>>2]|0)>>3)+1|0,u=Xw(n)|0,u>>>0<l>>>0)c0(n);else{v=e[n>>2]|0,d=(e[n+8>>2]|0)-v|0,m=d>>2,Jw(o,d>>3>>>0<u>>>1>>>0?m>>>0<l>>>0?l:m:u,(e[f>>2]|0)-v>>3,n+8|0),l=o+8|0,Qt(e[l>>2]|0,e[r>>2]|0,e[t>>2]|0),e[l>>2]=(e[l>>2]|0)+8,Zw(n,o),$w(o),s=c;return}}function Xw(n){return n=n|0,536870911}function Jw(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0;e[n+12>>2]=0,e[n+16>>2]=u;do if(r)if(r>>>0>536870911)u0();else{o=q(r<<3)|0;break}else o=0;while(!1);e[n>>2]=o,u=o+(t<<3)|0,e[n+8>>2]=u,e[n+4>>2]=u,e[n+12>>2]=o+(r<<3)}function Zw(n,r){n=n|0,r=r|0;var t=0,u=0,o=0,f=0,l=0;u=e[n>>2]|0,l=n+4|0,f=r+4|0,o=(e[l>>2]|0)-u|0,t=(e[f>>2]|0)+(0-(o>>3)<<3)|0,e[f>>2]=t,(o|0)>0?(Q(t|0,u|0,o|0)|0,u=f,t=e[f>>2]|0):u=f,f=e[n>>2]|0,e[n>>2]=t,e[u>>2]=f,f=r+8|0,o=e[l>>2]|0,e[l>>2]=e[f>>2],e[f>>2]=o,f=n+8|0,l=r+12|0,n=e[f>>2]|0,e[f>>2]=e[l>>2],e[l>>2]=n,e[r>>2]=e[u>>2]}function $w(n){n=n|0;var r=0,t=0,u=0;r=e[n+4>>2]|0,t=n+8|0,u=e[t>>2]|0,(u|0)!=(r|0)&&(e[t>>2]=u+(~((u+-8-r|0)>>>3)<<3)),n=e[n>>2]|0,n|0&&I(n)}function at(n){n=n|0,bw(n)}function Qw(n){n=n|0,aw(n+24|0)}function aw(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function bw(n){n=n|0;var r=0;r=f0()|0,l0(n,1,12,r,ng()|0,2),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function ng(){return 1896}function eg(n,r,t){n=n|0,r=r|0,t=t|0,ig(e[(rg(n)|0)>>2]|0,r,t)}function rg(n){return n=n|0,(e[(Qe()|0)+24>>2]|0)+(n<<3)|0}function ig(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0;u=s,s=s+16|0,f=u+4|0,o=u,tg(f,r),r=ug(f,r)|0,o1(o,t),t=f1(o,t)|0,X2[n&31](r,t),l1(o),s=u}function tg(n,r){n=n|0,r=r|0}function ug(n,r){return n=n|0,r=r|0,og(r)|0}function og(n){return n=n|0,n|0}function fg(){var n=0;return w[8080]|0||(bt(11040),$(70,11040,V|0)|0,n=8080,e[n>>2]=1,e[n+4>>2]=0),e0(11040)|0||bt(11040),11040}function bt(n){n=n|0,sg(n),N1(n,71)}function lg(n){n=n|0,cg(n+24|0)}function cg(n){n=n|0;var r=0,t=0,u=0;t=e[n>>2]|0,u=t,t|0&&(n=n+4|0,r=e[n>>2]|0,(r|0)!=(t|0)&&(e[n>>2]=r+(~((r+-8-u|0)>>>3)<<3)),I(t))}function sg(n){n=n|0;var r=0;r=f0()|0,l0(n,5,7,r,dg()|0,0),e[n+24>>2]=0,e[n+28>>2]=0,e[n+32>>2]=0}function vg(n){n=n|0,_g(n)}function _g(n){n=n|0,hg(n)}function hg(n){n=n|0,w[n+8>>0]=1}function dg(){return 1936}function mg(){return pg()|0}function pg(){var n=0,r=0,t=0,u=0,o=0,f=0,l=0;return r=s,s=s+16|0,o=r+4|0,l=r,t=b0(8)|0,n=t,f=n+4|0,e[f>>2]=q(1)|0,u=q(8)|0,f=e[f>>2]|0,e[l>>2]=0,e[o>>2]=e[l>>2],wg(u,f,o),e[t>>2]=u,s=r,n|0}function wg(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]=r,t=q(16)|0,e[t+4>>2]=0,e[t+8>>2]=0,e[t>>2]=1916,e[t+12>>2]=r,e[n+4>>2]=t}function gg(n){n=n|0,Q1(n),I(n)}function yg(n){n=n|0,n=e[n+12>>2]|0,n|0&&I(n)}function kg(n){n=n|0,I(n)}function Mg(){var n=0;return w[8088]|0||(Bg(11076),$(25,11076,V|0)|0,n=8088,e[n>>2]=1,e[n+4>>2]=0),11076}function Tg(n,r){n=n|0,r=r|0,e[n>>2]=Ag()|0,e[n+4>>2]=Cg()|0,e[n+12>>2]=r,e[n+8>>2]=Sg()|0,e[n+32>>2]=10}function Ag(){return 11745}function Cg(){return 1940}function Sg(){return Tn()|0}function Eg(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0,(c1(u,896)|0)==512?t|0&&(Lg(t),I(t)):r|0&&I(r)}function Lg(n){n=n|0,n=e[n+4>>2]|0,n|0&&v1(n)}function Bg(n){n=n|0,u1(n)}function m2(n,r){n=n|0,r=r|0,e[n>>2]=r}function ae(n){return n=n|0,e[n>>2]|0}function Rg(n){return n=n|0,w[e[n>>2]>>0]|0}function Pg(n,r){n=n|0,r=r|0;var t=0,u=0;t=s,s=s+16|0,u=t,e[u>>2]=e[n>>2],Og(r,u)|0,s=t}function Og(n,r){n=n|0,r=r|0;var t=0;return t=Ng(e[n>>2]|0,r)|0,r=n+4|0,e[(e[r>>2]|0)+8>>2]=t,e[(e[r>>2]|0)+8>>2]|0}function Ng(n,r){n=n|0,r=r|0;var t=0,u=0;return t=s,s=s+16|0,u=t,n2(u),n=X0(n)|0,r=jg(n,e[r>>2]|0)|0,e2(u),s=t,r|0}function n2(n){n=n|0,e[n>>2]=e[2701],e[n+4>>2]=e[2703]}function jg(n,r){n=n|0,r=r|0;var t=0;return t=u2(Ig()|0)|0,mr(0,t|0,n|0,Ge(r)|0)|0}function e2(n){n=n|0,Wt(e[n>>2]|0,e[n+4>>2]|0)}function Ig(){var n=0;return w[8096]|0||(Fg(11120),n=8096,e[n>>2]=1,e[n+4>>2]=0),11120}function Fg(n){n=n|0,o2(n,xg()|0,1)}function xg(){return 1948}function Dg(){Hg()}function Hg(){var n=0,r=0,t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0,L=0,C=0,A=0;if(C=s,s=s+16|0,d=C+4|0,g=C,xu(65536,10804,e[2702]|0,10812),t=At()|0,r=e[t>>2]|0,n=e[r>>2]|0,n|0)for(u=e[t+8>>2]|0,t=e[t+4>>2]|0;Uu(n|0,B[t>>0]|0|0,w[u>>0]|0),r=r+4|0,n=e[r>>2]|0,n;)u=u+1|0,t=t+1|0;if(n=Ct()|0,r=e[n>>2]|0,r|0)do _r(r|0,e[n+4>>2]|0),n=n+8|0,r=e[n>>2]|0;while(r|0);_r(Ug()|0,5167),m=K1()|0,n=e[m>>2]|0;n:do if(n|0){do qg(e[n+4>>2]|0),n=e[n>>2]|0;while(n|0);if(n=e[m>>2]|0,n|0){v=m;do{for(;o=n,n=e[n>>2]|0,o=e[o+4>>2]|0,!!(zg(o)|0);)if(e[g>>2]=v,e[d>>2]=e[g>>2],Wg(m,d)|0,!n)break n;if(Yg(o),v=e[v>>2]|0,r=nu(o)|0,f=Yu()|0,l=s,s=s+((1*(r<<2)|0)+15&-16)|0,c=s,s=s+((1*(r<<2)|0)+15&-16)|0,r=e[(xt(o)|0)>>2]|0,r|0)for(t=l,u=c;e[t>>2]=e[(X1(e[r+4>>2]|0)|0)>>2],e[u>>2]=e[r+8>>2],r=e[r>>2]|0,r;)t=t+4|0,u=u+4|0;A=X1(o)|0,r=Vg(o)|0,t=nu(o)|0,u=Gg(o)|0,zu(A|0,r|0,l|0,c|0,t|0,u|0,Ue(o)|0),Du(f|0)}while(n|0)}}while(!1);if(n=e[(qe()|0)>>2]|0,n|0)do A=n+4|0,m=ze(A)|0,o=ln(m)|0,f=on(m)|0,l=(fn(m)|0)+1|0,c=Ln(m)|0,v=eu(A)|0,m=e0(m)|0,d=Cn(A)|0,g=be(A)|0,pn(0,o|0,f|0,l|0,c|0,v|0,m|0,d|0,g|0,nr(A)|0),n=e[n>>2]|0;while(n|0);n=e[(K1()|0)>>2]|0;n:do if(n|0){e:for(;;){if(r=e[n+4>>2]|0,r|0&&(M=e[(X1(r)|0)>>2]|0,L=e[(Dt(r)|0)>>2]|0,L|0)){t=L;do{r=t+4|0,u=ze(r)|0;r:do if(u|0)switch(e0(u)|0){case 0:break e;case 4:case 3:case 2:{c=ln(u)|0,v=on(u)|0,m=(fn(u)|0)+1|0,d=Ln(u)|0,g=e0(u)|0,A=Cn(r)|0,pn(M|0,c|0,v|0,m|0,d|0,0,g|0,A|0,be(r)|0,nr(r)|0);break r}case 1:{l=ln(u)|0,c=on(u)|0,v=(fn(u)|0)+1|0,m=Ln(u)|0,d=eu(r)|0,g=e0(u)|0,A=Cn(r)|0,pn(M|0,l|0,c|0,v|0,m|0,d|0,g|0,A|0,be(r)|0,nr(r)|0);break r}case 5:{m=ln(u)|0,d=on(u)|0,g=(fn(u)|0)+1|0,A=Ln(u)|0,pn(M|0,m|0,d|0,g|0,A|0,Kg(u)|0,e0(u)|0,0,0,0);break r}default:break r}while(!1);t=e[t>>2]|0}while(t|0)}if(n=e[n>>2]|0,!n)break n}u0()}while(!1);Wu(),s=C}function Ug(){return 11703}function qg(n){n=n|0,w[n+40>>0]=0}function zg(n){return n=n|0,(w[n+40>>0]|0)!=0|0}function Wg(n,r){return n=n|0,r=r|0,r=Xg(r)|0,n=e[r>>2]|0,e[r>>2]=e[n>>2],I(n),e[r>>2]|0}function Yg(n){n=n|0,w[n+40>>0]=1}function nu(n){return n=n|0,e[n+20>>2]|0}function Vg(n){return n=n|0,e[n+8>>2]|0}function Gg(n){return n=n|0,e[n+32>>2]|0}function Ln(n){return n=n|0,e[n+4>>2]|0}function eu(n){return n=n|0,e[n+4>>2]|0}function be(n){return n=n|0,e[n+8>>2]|0}function nr(n){return n=n|0,e[n+16>>2]|0}function Kg(n){return n=n|0,e[n+20>>2]|0}function Xg(n){return n=n|0,e[n>>2]|0}function Bn(n){n=n|0;var r=0,t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0,L=0,C=0,A=0,P=0,R=0,j=0,S=0,E=0,U=0;U=s,s=s+16|0,M=U;do if(n>>>0<245){if(m=n>>>0<11?16:n+11&-8,n=m>>>3,g=e[2783]|0,t=g>>>n,t&3|0)return r=(t&1^1)+n|0,n=11172+(r<<1<<2)|0,t=n+8|0,u=e[t>>2]|0,o=u+8|0,f=e[o>>2]|0,(n|0)==(f|0)?e[2783]=g&~(1<<r):(e[f+12>>2]=n,e[t>>2]=f),E=r<<3,e[u+4>>2]=E|3,E=u+E+4|0,e[E>>2]=e[E>>2]|1,E=o,s=U,E|0;if(d=e[2785]|0,m>>>0>d>>>0){if(t|0)return r=2<<n,r=t<<n&(r|0-r),r=(r&0-r)+-1|0,l=r>>>12&16,r=r>>>l,t=r>>>5&8,r=r>>>t,o=r>>>2&4,r=r>>>o,n=r>>>1&2,r=r>>>n,u=r>>>1&1,u=(t|l|o|n|u)+(r>>>u)|0,r=11172+(u<<1<<2)|0,n=r+8|0,o=e[n>>2]|0,l=o+8|0,t=e[l>>2]|0,(r|0)==(t|0)?(n=g&~(1<<u),e[2783]=n):(e[t+12>>2]=r,e[n>>2]=t,n=g),f=(u<<3)-m|0,e[o+4>>2]=m|3,u=o+m|0,e[u+4>>2]=f|1,e[u+f>>2]=f,d|0&&(o=e[2788]|0,r=d>>>3,t=11172+(r<<1<<2)|0,r=1<<r,n&r?(n=t+8|0,r=e[n>>2]|0):(e[2783]=n|r,r=t,n=t+8|0),e[n>>2]=o,e[r+12>>2]=o,e[o+8>>2]=r,e[o+12>>2]=t),e[2785]=f,e[2788]=u,E=l,s=U,E|0;if(c=e[2784]|0,c){if(t=(c&0-c)+-1|0,l=t>>>12&16,t=t>>>l,f=t>>>5&8,t=t>>>f,v=t>>>2&4,t=t>>>v,u=t>>>1&2,t=t>>>u,n=t>>>1&1,n=e[11436+((f|l|v|u|n)+(t>>>n)<<2)>>2]|0,t=(e[n+4>>2]&-8)-m|0,u=e[n+16+(((e[n+16>>2]|0)==0&1)<<2)>>2]|0,!u)v=n,f=t;else{do l=(e[u+4>>2]&-8)-m|0,v=l>>>0<t>>>0,t=v?l:t,n=v?u:n,u=e[u+16+(((e[u+16>>2]|0)==0&1)<<2)>>2]|0;while(u|0);v=n,f=t}if(l=v+m|0,v>>>0<l>>>0){o=e[v+24>>2]|0,r=e[v+12>>2]|0;do if((r|0)==(v|0)){if(n=v+20|0,r=e[n>>2]|0,!r&&(n=v+16|0,r=e[n>>2]|0,!r)){t=0;break}for(;;){if(t=r+20|0,u=e[t>>2]|0,u|0){r=u,n=t;continue}if(t=r+16|0,u=e[t>>2]|0,u)r=u,n=t;else break}e[n>>2]=0,t=r}else t=e[v+8>>2]|0,e[t+12>>2]=r,e[r+8>>2]=t,t=r;while(!1);do if(o|0){if(r=e[v+28>>2]|0,n=11436+(r<<2)|0,(v|0)==(e[n>>2]|0)){if(e[n>>2]=t,!t){e[2784]=c&~(1<<r);break}}else if(e[o+16+(((e[o+16>>2]|0)!=(v|0)&1)<<2)>>2]=t,!t)break;e[t+24>>2]=o,r=e[v+16>>2]|0,r|0&&(e[t+16>>2]=r,e[r+24>>2]=t),r=e[v+20>>2]|0,r|0&&(e[t+20>>2]=r,e[r+24>>2]=t)}while(!1);return f>>>0<16?(E=f+m|0,e[v+4>>2]=E|3,E=v+E+4|0,e[E>>2]=e[E>>2]|1):(e[v+4>>2]=m|3,e[l+4>>2]=f|1,e[l+f>>2]=f,d|0&&(u=e[2788]|0,r=d>>>3,t=11172+(r<<1<<2)|0,r=1<<r,g&r?(n=t+8|0,r=e[n>>2]|0):(e[2783]=g|r,r=t,n=t+8|0),e[n>>2]=u,e[r+12>>2]=u,e[u+8>>2]=r,e[u+12>>2]=t),e[2785]=f,e[2788]=l),E=v+8|0,s=U,E|0}else g=m}else g=m}else g=m}else if(n>>>0<=4294967231)if(n=n+11|0,m=n&-8,v=e[2784]|0,v){u=0-m|0,n=n>>>8,n?m>>>0>16777215?c=31:(g=(n+1048320|0)>>>16&8,S=n<<g,d=(S+520192|0)>>>16&4,S=S<<d,c=(S+245760|0)>>>16&2,c=14-(d|g|c)+(S<<c>>>15)|0,c=m>>>(c+7|0)&1|c<<1):c=0,t=e[11436+(c<<2)>>2]|0;n:do if(!t)t=0,n=0,S=57;else for(n=0,l=m<<((c|0)==31?0:25-(c>>>1)|0),f=0;;){if(o=(e[t+4>>2]&-8)-m|0,o>>>0<u>>>0)if(o)n=t,u=o;else{n=t,u=0,o=t,S=61;break n}if(o=e[t+20>>2]|0,t=e[t+16+(l>>>31<<2)>>2]|0,f=(o|0)==0|(o|0)==(t|0)?f:o,o=(t|0)==0,o){t=f,S=57;break}else l=l<<((o^1)&1)}while(!1);if((S|0)==57){if((t|0)==0&(n|0)==0){if(n=2<<c,n=v&(n|0-n),!n){g=m;break}g=(n&0-n)+-1|0,l=g>>>12&16,g=g>>>l,f=g>>>5&8,g=g>>>f,c=g>>>2&4,g=g>>>c,d=g>>>1&2,g=g>>>d,t=g>>>1&1,n=0,t=e[11436+((f|l|c|d|t)+(g>>>t)<<2)>>2]|0}t?(o=t,S=61):(c=n,l=u)}if((S|0)==61)for(;;)if(S=0,t=(e[o+4>>2]&-8)-m|0,g=t>>>0<u>>>0,t=g?t:u,n=g?o:n,o=e[o+16+(((e[o+16>>2]|0)==0&1)<<2)>>2]|0,o)u=t,S=61;else{c=n,l=t;break}if(c|0&&l>>>0<((e[2785]|0)-m|0)>>>0){if(f=c+m|0,c>>>0>=f>>>0)return E=0,s=U,E|0;o=e[c+24>>2]|0,r=e[c+12>>2]|0;do if((r|0)==(c|0)){if(n=c+20|0,r=e[n>>2]|0,!r&&(n=c+16|0,r=e[n>>2]|0,!r)){r=0;break}for(;;){if(t=r+20|0,u=e[t>>2]|0,u|0){r=u,n=t;continue}if(t=r+16|0,u=e[t>>2]|0,u)r=u,n=t;else break}e[n>>2]=0}else E=e[c+8>>2]|0,e[E+12>>2]=r,e[r+8>>2]=E;while(!1);do if(o){if(n=e[c+28>>2]|0,t=11436+(n<<2)|0,(c|0)==(e[t>>2]|0)){if(e[t>>2]=r,!r){u=v&~(1<<n),e[2784]=u;break}}else if(e[o+16+(((e[o+16>>2]|0)!=(c|0)&1)<<2)>>2]=r,!r){u=v;break}e[r+24>>2]=o,n=e[c+16>>2]|0,n|0&&(e[r+16>>2]=n,e[n+24>>2]=r),n=e[c+20>>2]|0,n&&(e[r+20>>2]=n,e[n+24>>2]=r),u=v}else u=v;while(!1);do if(l>>>0>=16){if(e[c+4>>2]=m|3,e[f+4>>2]=l|1,e[f+l>>2]=l,r=l>>>3,l>>>0<256){t=11172+(r<<1<<2)|0,n=e[2783]|0,r=1<<r,n&r?(n=t+8|0,r=e[n>>2]|0):(e[2783]=n|r,r=t,n=t+8|0),e[n>>2]=f,e[r+12>>2]=f,e[f+8>>2]=r,e[f+12>>2]=t;break}if(r=l>>>8,r?l>>>0>16777215?r=31:(S=(r+1048320|0)>>>16&8,E=r<<S,j=(E+520192|0)>>>16&4,E=E<<j,r=(E+245760|0)>>>16&2,r=14-(j|S|r)+(E<<r>>>15)|0,r=l>>>(r+7|0)&1|r<<1):r=0,t=11436+(r<<2)|0,e[f+28>>2]=r,n=f+16|0,e[n+4>>2]=0,e[n>>2]=0,n=1<<r,!(u&n)){e[2784]=u|n,e[t>>2]=f,e[f+24>>2]=t,e[f+12>>2]=f,e[f+8>>2]=f;break}for(n=l<<((r|0)==31?0:25-(r>>>1)|0),t=e[t>>2]|0;;){if((e[t+4>>2]&-8|0)==(l|0)){S=97;break}if(u=t+16+(n>>>31<<2)|0,r=e[u>>2]|0,r)n=n<<1,t=r;else{S=96;break}}if((S|0)==96){e[u>>2]=f,e[f+24>>2]=t,e[f+12>>2]=f,e[f+8>>2]=f;break}else if((S|0)==97){S=t+8|0,E=e[S>>2]|0,e[E+12>>2]=f,e[S>>2]=f,e[f+8>>2]=E,e[f+12>>2]=t,e[f+24>>2]=0;break}}else E=l+m|0,e[c+4>>2]=E|3,E=c+E+4|0,e[E>>2]=e[E>>2]|1;while(!1);return E=c+8|0,s=U,E|0}else g=m}else g=m;else g=-1;while(!1);if(t=e[2785]|0,t>>>0>=g>>>0)return r=t-g|0,n=e[2788]|0,r>>>0>15?(E=n+g|0,e[2788]=E,e[2785]=r,e[E+4>>2]=r|1,e[E+r>>2]=r,e[n+4>>2]=g|3):(e[2785]=0,e[2788]=0,e[n+4>>2]=t|3,E=n+t+4|0,e[E>>2]=e[E>>2]|1),E=n+8|0,s=U,E|0;if(l=e[2786]|0,l>>>0>g>>>0)return j=l-g|0,e[2786]=j,E=e[2789]|0,S=E+g|0,e[2789]=S,e[S+4>>2]=j|1,e[E+4>>2]=g|3,E=E+8|0,s=U,E|0;if(e[2901]|0?n=e[2903]|0:(e[2903]=4096,e[2902]=4096,e[2904]=-1,e[2905]=-1,e[2906]=0,e[2894]=0,n=M&-16^1431655768,e[M>>2]=n,e[2901]=n,n=4096),c=g+48|0,v=g+47|0,f=n+v|0,o=0-n|0,m=f&o,m>>>0<=g>>>0||(n=e[2893]|0,n|0&&(d=e[2891]|0,M=d+m|0,M>>>0<=d>>>0|M>>>0>n>>>0)))return E=0,s=U,E|0;n:do if(e[2894]&4)r=0,S=133;else{t=e[2789]|0;e:do if(t){for(u=11580;n=e[u>>2]|0,!(n>>>0<=t>>>0&&(A=u+4|0,(n+(e[A>>2]|0)|0)>>>0>t>>>0));)if(n=e[u+8>>2]|0,n)u=n;else{S=118;break e}if(r=f-l&o,r>>>0<2147483647)if(n=_1(r|0)|0,(n|0)==((e[u>>2]|0)+(e[A>>2]|0)|0)){if((n|0)!=-1){l=r,f=n,S=135;break n}}else u=n,S=126;else r=0}else S=118;while(!1);do if((S|0)==118)if(t=_1(0)|0,(t|0)!=-1&&(r=t,L=e[2902]|0,C=L+-1|0,r=(C&r|0?(C+r&0-L)-r|0:0)+m|0,L=e[2891]|0,C=r+L|0,r>>>0>g>>>0&r>>>0<2147483647)){if(A=e[2893]|0,A|0&&C>>>0<=L>>>0|C>>>0>A>>>0){r=0;break}if(n=_1(r|0)|0,(n|0)==(t|0)){l=r,f=t,S=135;break n}else u=n,S=126}else r=0;while(!1);do if((S|0)==126){if(t=0-r|0,!(c>>>0>r>>>0&(r>>>0<2147483647&(u|0)!=-1)))if((u|0)==-1){r=0;break}else{l=r,f=u,S=135;break n}if(n=e[2903]|0,n=v-r+n&0-n,n>>>0>=2147483647){l=r,f=u,S=135;break n}if((_1(n|0)|0)==-1){_1(t|0)|0,r=0;break}else{l=n+r|0,f=u,S=135;break n}}while(!1);e[2894]=e[2894]|4,S=133}while(!1);if((S|0)==133&&m>>>0<2147483647&&(j=_1(m|0)|0,A=_1(0)|0,P=A-j|0,R=P>>>0>(g+40|0)>>>0,!((j|0)==-1|R^1|j>>>0<A>>>0&((j|0)!=-1&(A|0)!=-1)^1))&&(l=R?P:r,f=j,S=135),(S|0)==135){r=(e[2891]|0)+l|0,e[2891]=r,r>>>0>(e[2892]|0)>>>0&&(e[2892]=r),v=e[2789]|0;do if(v){for(r=11580;;){if(n=e[r>>2]|0,t=r+4|0,u=e[t>>2]|0,(f|0)==(n+u|0)){S=145;break}if(o=e[r+8>>2]|0,o)r=o;else break}if((S|0)==145&&!(e[r+12>>2]&8|0)&&v>>>0<f>>>0&v>>>0>=n>>>0){e[t>>2]=u+l,E=v+8|0,E=E&7|0?0-E&7:0,S=v+E|0,E=(e[2786]|0)+(l-E)|0,e[2789]=S,e[2786]=E,e[S+4>>2]=E|1,e[S+E+4>>2]=40,e[2790]=e[2905];break}for(f>>>0<(e[2787]|0)>>>0&&(e[2787]=f),t=f+l|0,r=11580;;){if((e[r>>2]|0)==(t|0)){S=153;break}if(n=e[r+8>>2]|0,n)r=n;else break}if((S|0)==153&&!(e[r+12>>2]&8|0)){e[r>>2]=f,d=r+4|0,e[d>>2]=(e[d>>2]|0)+l,d=f+8|0,d=f+(d&7|0?0-d&7:0)|0,r=t+8|0,r=t+(r&7|0?0-r&7:0)|0,m=d+g|0,c=r-d-g|0,e[d+4>>2]=g|3;do if((r|0)!=(v|0)){if((r|0)==(e[2788]|0)){E=(e[2785]|0)+c|0,e[2785]=E,e[2788]=m,e[m+4>>2]=E|1,e[m+E>>2]=E;break}if(n=e[r+4>>2]|0,(n&3|0)==1){l=n&-8,u=n>>>3;n:do if(n>>>0<256)if(n=e[r+8>>2]|0,t=e[r+12>>2]|0,(t|0)==(n|0)){e[2783]=e[2783]&~(1<<u);break}else{e[n+12>>2]=t,e[t+8>>2]=n;break}else{f=e[r+24>>2]|0,n=e[r+12>>2]|0;do if((n|0)==(r|0)){if(u=r+16|0,t=u+4|0,n=e[t>>2]|0,!n)if(n=e[u>>2]|0,n)t=u;else{n=0;break}for(;;){if(u=n+20|0,o=e[u>>2]|0,o|0){n=o,t=u;continue}if(u=n+16|0,o=e[u>>2]|0,o)n=o,t=u;else break}e[t>>2]=0}else E=e[r+8>>2]|0,e[E+12>>2]=n,e[n+8>>2]=E;while(!1);if(!f)break;t=e[r+28>>2]|0,u=11436+(t<<2)|0;do if((r|0)!=(e[u>>2]|0)){if(e[f+16+(((e[f+16>>2]|0)!=(r|0)&1)<<2)>>2]=n,!n)break n}else{if(e[u>>2]=n,n|0)break;e[2784]=e[2784]&~(1<<t);break n}while(!1);if(e[n+24>>2]=f,t=r+16|0,u=e[t>>2]|0,u|0&&(e[n+16>>2]=u,e[u+24>>2]=n),t=e[t+4>>2]|0,!t)break;e[n+20>>2]=t,e[t+24>>2]=n}while(!1);r=r+l|0,o=l+c|0}else o=c;if(r=r+4|0,e[r>>2]=e[r>>2]&-2,e[m+4>>2]=o|1,e[m+o>>2]=o,r=o>>>3,o>>>0<256){t=11172+(r<<1<<2)|0,n=e[2783]|0,r=1<<r,n&r?(n=t+8|0,r=e[n>>2]|0):(e[2783]=n|r,r=t,n=t+8|0),e[n>>2]=m,e[r+12>>2]=m,e[m+8>>2]=r,e[m+12>>2]=t;break}r=o>>>8;do if(!r)r=0;else{if(o>>>0>16777215){r=31;break}S=(r+1048320|0)>>>16&8,E=r<<S,j=(E+520192|0)>>>16&4,E=E<<j,r=(E+245760|0)>>>16&2,r=14-(j|S|r)+(E<<r>>>15)|0,r=o>>>(r+7|0)&1|r<<1}while(!1);if(u=11436+(r<<2)|0,e[m+28>>2]=r,n=m+16|0,e[n+4>>2]=0,e[n>>2]=0,n=e[2784]|0,t=1<<r,!(n&t)){e[2784]=n|t,e[u>>2]=m,e[m+24>>2]=u,e[m+12>>2]=m,e[m+8>>2]=m;break}for(n=o<<((r|0)==31?0:25-(r>>>1)|0),t=e[u>>2]|0;;){if((e[t+4>>2]&-8|0)==(o|0)){S=194;break}if(u=t+16+(n>>>31<<2)|0,r=e[u>>2]|0,r)n=n<<1,t=r;else{S=193;break}}if((S|0)==193){e[u>>2]=m,e[m+24>>2]=t,e[m+12>>2]=m,e[m+8>>2]=m;break}else if((S|0)==194){S=t+8|0,E=e[S>>2]|0,e[E+12>>2]=m,e[S>>2]=m,e[m+8>>2]=E,e[m+12>>2]=t,e[m+24>>2]=0;break}}else E=(e[2786]|0)+c|0,e[2786]=E,e[2789]=m,e[m+4>>2]=E|1;while(!1);return E=d+8|0,s=U,E|0}for(r=11580;n=e[r>>2]|0,!(n>>>0<=v>>>0&&(E=n+(e[r+4>>2]|0)|0,E>>>0>v>>>0));)r=e[r+8>>2]|0;o=E+-47|0,n=o+8|0,n=o+(n&7|0?0-n&7:0)|0,o=v+16|0,n=n>>>0<o>>>0?v:n,r=n+8|0,t=f+8|0,t=t&7|0?0-t&7:0,S=f+t|0,t=l+-40-t|0,e[2789]=S,e[2786]=t,e[S+4>>2]=t|1,e[S+t+4>>2]=40,e[2790]=e[2905],t=n+4|0,e[t>>2]=27,e[r>>2]=e[2895],e[r+4>>2]=e[2896],e[r+8>>2]=e[2897],e[r+12>>2]=e[2898],e[2895]=f,e[2896]=l,e[2898]=0,e[2897]=r,r=n+24|0;do S=r,r=r+4|0,e[r>>2]=7;while((S+8|0)>>>0<E>>>0);if((n|0)!=(v|0)){if(f=n-v|0,e[t>>2]=e[t>>2]&-2,e[v+4>>2]=f|1,e[n>>2]=f,r=f>>>3,f>>>0<256){t=11172+(r<<1<<2)|0,n=e[2783]|0,r=1<<r,n&r?(n=t+8|0,r=e[n>>2]|0):(e[2783]=n|r,r=t,n=t+8|0),e[n>>2]=v,e[r+12>>2]=v,e[v+8>>2]=r,e[v+12>>2]=t;break}if(r=f>>>8,r?f>>>0>16777215?t=31:(S=(r+1048320|0)>>>16&8,E=r<<S,j=(E+520192|0)>>>16&4,E=E<<j,t=(E+245760|0)>>>16&2,t=14-(j|S|t)+(E<<t>>>15)|0,t=f>>>(t+7|0)&1|t<<1):t=0,u=11436+(t<<2)|0,e[v+28>>2]=t,e[v+20>>2]=0,e[o>>2]=0,r=e[2784]|0,n=1<<t,!(r&n)){e[2784]=r|n,e[u>>2]=v,e[v+24>>2]=u,e[v+12>>2]=v,e[v+8>>2]=v;break}for(n=f<<((t|0)==31?0:25-(t>>>1)|0),t=e[u>>2]|0;;){if((e[t+4>>2]&-8|0)==(f|0)){S=216;break}if(u=t+16+(n>>>31<<2)|0,r=e[u>>2]|0,r)n=n<<1,t=r;else{S=215;break}}if((S|0)==215){e[u>>2]=v,e[v+24>>2]=t,e[v+12>>2]=v,e[v+8>>2]=v;break}else if((S|0)==216){S=t+8|0,E=e[S>>2]|0,e[E+12>>2]=v,e[S>>2]=v,e[v+8>>2]=E,e[v+12>>2]=t,e[v+24>>2]=0;break}}}else{E=e[2787]|0,(E|0)==0|f>>>0<E>>>0&&(e[2787]=f),e[2895]=f,e[2896]=l,e[2898]=0,e[2792]=e[2901],e[2791]=-1,r=0;do E=11172+(r<<1<<2)|0,e[E+12>>2]=E,e[E+8>>2]=E,r=r+1|0;while((r|0)!=32);E=f+8|0,E=E&7|0?0-E&7:0,S=f+E|0,E=l+-40-E|0,e[2789]=S,e[2786]=E,e[S+4>>2]=E|1,e[S+E+4>>2]=40,e[2790]=e[2905]}while(!1);if(r=e[2786]|0,r>>>0>g>>>0)return j=r-g|0,e[2786]=j,E=e[2789]|0,S=E+g|0,e[2789]=S,e[S+4>>2]=j|1,e[E+4>>2]=g|3,E=E+8|0,s=U,E|0}return e[(Z1()|0)>>2]=12,E=0,s=U,E|0}function Rn(n){n=n|0;var r=0,t=0,u=0,o=0,f=0,l=0,c=0,v=0;if(n){t=n+-8|0,o=e[2787]|0,n=e[n+-4>>2]|0,r=n&-8,v=t+r|0;do if(n&1)c=t,l=t;else{if(u=e[t>>2]|0,!(n&3)||(l=t+(0-u)|0,f=u+r|0,l>>>0<o>>>0))return;if((l|0)==(e[2788]|0)){if(n=v+4|0,r=e[n>>2]|0,(r&3|0)!=3){c=l,r=f;break}e[2785]=f,e[n>>2]=r&-2,e[l+4>>2]=f|1,e[l+f>>2]=f;return}if(t=u>>>3,u>>>0<256)if(n=e[l+8>>2]|0,r=e[l+12>>2]|0,(r|0)==(n|0)){e[2783]=e[2783]&~(1<<t),c=l,r=f;break}else{e[n+12>>2]=r,e[r+8>>2]=n,c=l,r=f;break}o=e[l+24>>2]|0,n=e[l+12>>2]|0;do if((n|0)==(l|0)){if(t=l+16|0,r=t+4|0,n=e[r>>2]|0,!n)if(n=e[t>>2]|0,n)r=t;else{n=0;break}for(;;){if(t=n+20|0,u=e[t>>2]|0,u|0){n=u,r=t;continue}if(t=n+16|0,u=e[t>>2]|0,u)n=u,r=t;else break}e[r>>2]=0}else c=e[l+8>>2]|0,e[c+12>>2]=n,e[n+8>>2]=c;while(!1);if(o){if(r=e[l+28>>2]|0,t=11436+(r<<2)|0,(l|0)==(e[t>>2]|0)){if(e[t>>2]=n,!n){e[2784]=e[2784]&~(1<<r),c=l,r=f;break}}else if(e[o+16+(((e[o+16>>2]|0)!=(l|0)&1)<<2)>>2]=n,!n){c=l,r=f;break}e[n+24>>2]=o,r=l+16|0,t=e[r>>2]|0,t|0&&(e[n+16>>2]=t,e[t+24>>2]=n),r=e[r+4>>2]|0,r?(e[n+20>>2]=r,e[r+24>>2]=n,c=l,r=f):(c=l,r=f)}else c=l,r=f}while(!1);if(!(l>>>0>=v>>>0)&&(n=v+4|0,u=e[n>>2]|0,!!(u&1))){if(u&2)e[n>>2]=u&-2,e[c+4>>2]=r|1,e[l+r>>2]=r,o=r;else{if(n=e[2788]|0,(v|0)==(e[2789]|0)){if(v=(e[2786]|0)+r|0,e[2786]=v,e[2789]=c,e[c+4>>2]=v|1,(c|0)!=(n|0))return;e[2788]=0,e[2785]=0;return}if((v|0)==(n|0)){v=(e[2785]|0)+r|0,e[2785]=v,e[2788]=l,e[c+4>>2]=v|1,e[l+v>>2]=v;return}o=(u&-8)+r|0,t=u>>>3;do if(u>>>0<256)if(r=e[v+8>>2]|0,n=e[v+12>>2]|0,(n|0)==(r|0)){e[2783]=e[2783]&~(1<<t);break}else{e[r+12>>2]=n,e[n+8>>2]=r;break}else{f=e[v+24>>2]|0,n=e[v+12>>2]|0;do if((n|0)==(v|0)){if(t=v+16|0,r=t+4|0,n=e[r>>2]|0,!n)if(n=e[t>>2]|0,n)r=t;else{t=0;break}for(;;){if(t=n+20|0,u=e[t>>2]|0,u|0){n=u,r=t;continue}if(t=n+16|0,u=e[t>>2]|0,u)n=u,r=t;else break}e[r>>2]=0,t=n}else t=e[v+8>>2]|0,e[t+12>>2]=n,e[n+8>>2]=t,t=n;while(!1);if(f|0){if(n=e[v+28>>2]|0,r=11436+(n<<2)|0,(v|0)==(e[r>>2]|0)){if(e[r>>2]=t,!t){e[2784]=e[2784]&~(1<<n);break}}else if(e[f+16+(((e[f+16>>2]|0)!=(v|0)&1)<<2)>>2]=t,!t)break;e[t+24>>2]=f,n=v+16|0,r=e[n>>2]|0,r|0&&(e[t+16>>2]=r,e[r+24>>2]=t),n=e[n+4>>2]|0,n|0&&(e[t+20>>2]=n,e[n+24>>2]=t)}}while(!1);if(e[c+4>>2]=o|1,e[l+o>>2]=o,(c|0)==(e[2788]|0)){e[2785]=o;return}}if(n=o>>>3,o>>>0<256){t=11172+(n<<1<<2)|0,r=e[2783]|0,n=1<<n,r&n?(r=t+8|0,n=e[r>>2]|0):(e[2783]=r|n,n=t,r=t+8|0),e[r>>2]=c,e[n+12>>2]=c,e[c+8>>2]=n,e[c+12>>2]=t;return}n=o>>>8,n?o>>>0>16777215?n=31:(l=(n+1048320|0)>>>16&8,v=n<<l,f=(v+520192|0)>>>16&4,v=v<<f,n=(v+245760|0)>>>16&2,n=14-(f|l|n)+(v<<n>>>15)|0,n=o>>>(n+7|0)&1|n<<1):n=0,u=11436+(n<<2)|0,e[c+28>>2]=n,e[c+20>>2]=0,e[c+16>>2]=0,r=e[2784]|0,t=1<<n;do if(r&t){for(r=o<<((n|0)==31?0:25-(n>>>1)|0),t=e[u>>2]|0;;){if((e[t+4>>2]&-8|0)==(o|0)){n=73;break}if(u=t+16+(r>>>31<<2)|0,n=e[u>>2]|0,n)r=r<<1,t=n;else{n=72;break}}if((n|0)==72){e[u>>2]=c,e[c+24>>2]=t,e[c+12>>2]=c,e[c+8>>2]=c;break}else if((n|0)==73){l=t+8|0,v=e[l>>2]|0,e[v+12>>2]=c,e[l>>2]=c,e[c+8>>2]=v,e[c+12>>2]=t,e[c+24>>2]=0;break}}else e[2784]=r|t,e[u>>2]=c,e[c+24>>2]=u,e[c+12>>2]=c,e[c+8>>2]=c;while(!1);if(v=(e[2791]|0)+-1|0,e[2791]=v,!v)n=11588;else return;for(;n=e[n>>2]|0,n;)n=n+8|0;e[2791]=-1}}}function Jg(){return 11628}function Zg(n){n=n|0;var r=0,t=0;return r=s,s=s+16|0,t=r,e[t>>2]=ag(e[n+60>>2]|0)|0,n=Pn(Xu(6,t|0)|0)|0,s=r,n|0}function ru(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0,L=0;g=s,s=s+48|0,m=g+16|0,f=g,o=g+32|0,c=n+28|0,u=e[c>>2]|0,e[o>>2]=u,v=n+20|0,u=(e[v>>2]|0)-u|0,e[o+4>>2]=u,e[o+8>>2]=r,e[o+12>>2]=t,u=u+t|0,l=n+60|0,e[f>>2]=e[l>>2],e[f+4>>2]=o,e[f+8>>2]=2,f=Pn(pr(146,f|0)|0)|0;n:do if((u|0)!=(f|0)){for(r=2;!((f|0)<0);)if(u=u-f|0,L=e[o+4>>2]|0,M=f>>>0>L>>>0,o=M?o+8|0:o,r=(M<<31>>31)+r|0,L=f-(M?L:0)|0,e[o>>2]=(e[o>>2]|0)+L,M=o+4|0,e[M>>2]=(e[M>>2]|0)-L,e[m>>2]=e[l>>2],e[m+4>>2]=o,e[m+8>>2]=r,f=Pn(pr(146,m|0)|0)|0,(u|0)==(f|0)){d=3;break n}e[n+16>>2]=0,e[c>>2]=0,e[v>>2]=0,e[n>>2]=e[n>>2]|32,(r|0)==2?t=0:t=t-(e[o+4>>2]|0)|0}else d=3;while(!1);return(d|0)==3&&(L=e[n+44>>2]|0,e[n+16>>2]=L+(e[n+48>>2]|0),e[c>>2]=L,e[v>>2]=L),s=g,t|0}function $g(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0;return o=s,s=s+32|0,f=o,u=o+20|0,e[f>>2]=e[n+60>>2],e[f+4>>2]=0,e[f+8>>2]=r,e[f+12>>2]=u,e[f+16>>2]=t,(Pn(Ju(140,f|0)|0)|0)<0?(e[u>>2]=-1,n=-1):n=e[u>>2]|0,s=o,n|0}function Pn(n){return n=n|0,n>>>0>4294963200&&(e[(Z1()|0)>>2]=0-n,n=-1),n|0}function Z1(){return(Qg()|0)+64|0}function Qg(){return er()|0}function er(){return 2084}function ag(n){return n=n|0,n|0}function bg(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0;return o=s,s=s+32|0,u=o,e[n+36>>2]=1,!(e[n>>2]&64|0)&&(e[u>>2]=e[n+60>>2],e[u+4>>2]=21523,e[u+8>>2]=o+16,Vu(54,u|0)|0)&&(w[n+75>>0]=-1),u=ru(n,r,t)|0,s=o,u|0}function iu(n,r){n=n|0,r=r|0;var t=0,u=0;if(t=w[n>>0]|0,u=w[r>>0]|0,!(t<<24>>24)||t<<24>>24!=u<<24>>24)n=u;else{do n=n+1|0,r=r+1|0,t=w[n>>0]|0,u=w[r>>0]|0;while(!(!(t<<24>>24)||t<<24>>24!=u<<24>>24));n=u}return(t&255)-(n&255)|0}function ny(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0;n:do if(!t)n=0;else{for(;u=w[n>>0]|0,o=w[r>>0]|0,u<<24>>24==o<<24>>24;)if(t=t+-1|0,t)n=n+1|0,r=r+1|0;else{n=0;break n}n=(u&255)-(o&255)|0}while(!1);return n|0}function tu(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0,L=0,C=0,A=0;A=s,s=s+224|0,d=A+120|0,g=A+80|0,L=A,C=A+136|0,u=g,o=u+40|0;do e[u>>2]=0,u=u+4|0;while((u|0)<(o|0));return e[d>>2]=e[t>>2],(rr(0,r,d,L,g)|0)<0?t=-1:((e[n+76>>2]|0)>-1?M=ey(n)|0:M=0,t=e[n>>2]|0,m=t&32,(w[n+74>>0]|0)<1&&(e[n>>2]=t&-33),u=n+48|0,e[u>>2]|0?t=rr(n,r,d,L,g)|0:(o=n+44|0,f=e[o>>2]|0,e[o>>2]=C,l=n+28|0,e[l>>2]=C,c=n+20|0,e[c>>2]=C,e[u>>2]=80,v=n+16|0,e[v>>2]=C+80,t=rr(n,r,d,L,g)|0,f&&(In[e[n+36>>2]&7](n,0,0)|0,t=e[c>>2]|0?t:-1,e[o>>2]=f,e[u>>2]=0,e[v>>2]=0,e[l>>2]=0,e[c>>2]=0)),u=e[n>>2]|0,e[n>>2]=u|m,M|0&&ry(n),t=u&32|0?-1:t),s=A,t|0}function rr(n,r,t,u,o){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0;var f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0,L=0,C=0,A=0,P=0,R=0,j=0,S=0,E=0,U=0,t0=0,K=0,Y=0,a=0,r0=0,G=0;G=s,s=s+64|0,K=G+16|0,Y=G,U=G+24|0,a=G+8|0,r0=G+20|0,e[K>>2]=r,j=(n|0)!=0,S=U+40|0,E=S,U=U+39|0,t0=a+4|0,l=0,f=0,d=0;n:for(;;){do if((f|0)>-1)if((l|0)>(2147483647-f|0)){e[(Z1()|0)>>2]=75,f=-1;break}else{f=l+f|0;break}while(!1);if(l=w[r>>0]|0,l<<24>>24)c=r;else{R=87;break}e:for(;;){switch(l<<24>>24){case 37:{l=c,R=9;break e}case 0:{l=c;break e}default:}P=c+1|0,e[K>>2]=P,l=w[P>>0]|0,c=P}e:do if((R|0)==9)for(;;){if(R=0,(w[c+1>>0]|0)!=37)break e;if(l=l+1|0,c=c+2|0,e[K>>2]=c,(w[c>>0]|0)==37)R=9;else break}while(!1);if(l=l-r|0,j&&E0(n,r,l),l|0){r=c;continue}v=c+1|0,l=(w[v>>0]|0)+-48|0,l>>>0<10?(P=(w[c+2>>0]|0)==36,A=P?l:-1,d=P?1:d,v=P?c+3|0:v):A=-1,e[K>>2]=v,l=w[v>>0]|0,c=(l<<24>>24)+-32|0;e:do if(c>>>0<32)for(m=0,g=l;;){if(l=1<<c,!(l&75913)){l=g;break e}if(m=l|m,v=v+1|0,e[K>>2]=v,l=w[v>>0]|0,c=(l<<24>>24)+-32|0,c>>>0>=32)break;g=l}else m=0;while(!1);if(l<<24>>24==42){if(c=v+1|0,l=(w[c>>0]|0)+-48|0,l>>>0<10&&(w[v+2>>0]|0)==36)e[o+(l<<2)>>2]=10,l=e[u+((w[c>>0]|0)+-48<<3)>>2]|0,d=1,v=v+3|0;else{if(d|0){f=-1;break}j?(d=(e[t>>2]|0)+3&-4,l=e[d>>2]|0,e[t>>2]=d+4,d=0,v=c):(l=0,d=0,v=c)}e[K>>2]=v,P=(l|0)<0,l=P?0-l|0:l,m=P?m|8192:m}else{if(l=uu(K)|0,(l|0)<0){f=-1;break}v=e[K>>2]|0}do if((w[v>>0]|0)==46){if((w[v+1>>0]|0)!=42){e[K>>2]=v+1,c=uu(K)|0,v=e[K>>2]|0;break}if(g=v+2|0,c=(w[g>>0]|0)+-48|0,c>>>0<10&&(w[v+3>>0]|0)==36){e[o+(c<<2)>>2]=10,c=e[u+((w[g>>0]|0)+-48<<3)>>2]|0,v=v+4|0,e[K>>2]=v;break}if(d|0){f=-1;break n}j?(P=(e[t>>2]|0)+3&-4,c=e[P>>2]|0,e[t>>2]=P+4):c=0,e[K>>2]=g,v=g}else c=-1;while(!1);for(C=0;;){if(((w[v>>0]|0)+-65|0)>>>0>57){f=-1;break n}if(P=v+1|0,e[K>>2]=P,g=w[(w[v>>0]|0)+-65+(5178+(C*58|0))>>0]|0,M=g&255,(M+-1|0)>>>0<8)C=M,v=P;else break}if(!(g<<24>>24)){f=-1;break}L=(A|0)>-1;do if(g<<24>>24==19)if(L){f=-1;break n}else R=49;else{if(L){e[o+(A<<2)>>2]=M,L=u+(A<<3)|0,A=e[L+4>>2]|0,R=Y,e[R>>2]=e[L>>2],e[R+4>>2]=A,R=49;break}if(!j){f=0;break n}ou(Y,M,t)}while(!1);if((R|0)==49&&(R=0,!j)){l=0,r=P;continue}v=w[v>>0]|0,v=(C|0)!=0&(v&15|0)==3?v&-33:v,L=m&-65537,A=m&8192|0?L:m;e:do switch(v|0){case 110:switch((C&255)<<24>>24){case 0:{e[e[Y>>2]>>2]=f,l=0,r=P;continue n}case 1:{e[e[Y>>2]>>2]=f,l=0,r=P;continue n}case 2:{l=e[Y>>2]|0,e[l>>2]=f,e[l+4>>2]=((f|0)<0)<<31>>31,l=0,r=P;continue n}case 3:{T[e[Y>>2]>>1]=f,l=0,r=P;continue n}case 4:{w[e[Y>>2]>>0]=f,l=0,r=P;continue n}case 6:{e[e[Y>>2]>>2]=f,l=0,r=P;continue n}case 7:{l=e[Y>>2]|0,e[l>>2]=f,e[l+4>>2]=((f|0)<0)<<31>>31,l=0,r=P;continue n}default:{l=0,r=P;continue n}}case 112:{v=120,c=c>>>0>8?c:8,r=A|8,R=61;break}case 88:case 120:{r=A,R=61;break}case 111:{v=Y,r=e[v>>2]|0,v=e[v+4>>2]|0,M=ty(r,v,S)|0,L=E-M|0,m=0,g=5642,c=(A&8|0)==0|(c|0)>(L|0)?c:L+1|0,L=A,R=67;break}case 105:case 100:if(v=Y,r=e[v>>2]|0,v=e[v+4>>2]|0,(v|0)<0){r=On(0,0,r|0,v|0)|0,v=v0,m=Y,e[m>>2]=r,e[m+4>>2]=v,m=1,g=5642,R=66;break e}else{m=(A&2049|0)!=0&1,g=A&2048|0?5643:A&1|0?5644:5642,R=66;break e}case 117:{v=Y,m=0,g=5642,r=e[v>>2]|0,v=e[v+4>>2]|0,R=66;break}case 99:{w[U>>0]=e[Y>>2],r=U,m=0,g=5642,M=S,v=1,c=L;break}case 109:{v=uy(e[(Z1()|0)>>2]|0)|0,R=71;break}case 115:{v=e[Y>>2]|0,v=v|0?v:5652,R=71;break}case 67:{e[a>>2]=e[Y>>2],e[t0>>2]=0,e[Y>>2]=a,M=-1,v=a,R=75;break}case 83:{r=e[Y>>2]|0,c?(M=c,v=r,R=75):(P0(n,32,l,0,A),r=0,R=84);break}case 65:case 71:case 70:case 69:case 97:case 103:case 102:case 101:{l=fy(n,+O[Y>>3],l,c,A,v)|0,r=P;continue n}default:m=0,g=5642,M=S,v=c,c=A}while(!1);e:do if((R|0)==61)A=Y,C=e[A>>2]|0,A=e[A+4>>2]|0,M=iy(C,A,S,v&32)|0,g=(r&8|0)==0|(C|0)==0&(A|0)==0,m=g?0:2,g=g?5642:5642+(v>>4)|0,L=r,r=C,v=A,R=67;else if((R|0)==66)M=$1(r,v,S)|0,L=A,R=67;else if((R|0)==71)R=0,A=oy(v,0,c)|0,C=(A|0)==0,r=v,m=0,g=5642,M=C?v+c|0:A,v=C?c:A-v|0,c=L;else if((R|0)==75){for(R=0,g=v,r=0,c=0;m=e[g>>2]|0,!(!m||(c=fu(r0,m)|0,(c|0)<0|c>>>0>(M-r|0)>>>0));)if(r=c+r|0,M>>>0>r>>>0)g=g+4|0;else break;if((c|0)<0){f=-1;break n}if(P0(n,32,l,r,A),!r)r=0,R=84;else for(m=0;;){if(c=e[v>>2]|0,!c){R=84;break e}if(c=fu(r0,c)|0,m=c+m|0,(m|0)>(r|0)){R=84;break e}if(E0(n,r0,c),m>>>0>=r>>>0){R=84;break}else v=v+4|0}}while(!1);if((R|0)==67)R=0,v=(r|0)!=0|(v|0)!=0,A=(c|0)!=0|v,v=((v^1)&1)+(E-M)|0,r=A?M:S,M=S,v=A?(c|0)>(v|0)?c:v:c,c=(c|0)>-1?L&-65537:L;else if((R|0)==84){R=0,P0(n,32,l,r,A^8192),l=(l|0)>(r|0)?l:r,r=P;continue}C=M-r|0,L=(v|0)<(C|0)?C:v,A=L+m|0,l=(l|0)<(A|0)?A:l,P0(n,32,l,A,c),E0(n,g,m),P0(n,48,l,A,c^65536),P0(n,48,L,C,0),E0(n,r,C),P0(n,32,l,A,c^8192),r=P}n:do if((R|0)==87&&!n)if(!d)f=0;else{for(f=1;r=e[o+(f<<2)>>2]|0,!!r;)if(ou(u+(f<<3)|0,r,t),f=f+1|0,(f|0)>=10){f=1;break n}for(;;){if(e[o+(f<<2)>>2]|0){f=-1;break n}if(f=f+1|0,(f|0)>=10){f=1;break}}}while(!1);return s=G,f|0}function ey(n){return n=n|0,0}function ry(n){n=n|0}function E0(n,r,t){n=n|0,r=r|0,t=t|0,e[n>>2]&32||py(r,t,n)|0}function uu(n){n=n|0;var r=0,t=0,u=0;if(t=e[n>>2]|0,u=(w[t>>0]|0)+-48|0,u>>>0<10){r=0;do r=u+(r*10|0)|0,t=t+1|0,e[n>>2]=t,u=(w[t>>0]|0)+-48|0;while(u>>>0<10)}else r=0;return r|0}function ou(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0;n:do if(r>>>0<=20)do switch(r|0){case 9:{u=(e[t>>2]|0)+3&-4,r=e[u>>2]|0,e[t>>2]=u+4,e[n>>2]=r;break n}case 10:{u=(e[t>>2]|0)+3&-4,r=e[u>>2]|0,e[t>>2]=u+4,u=n,e[u>>2]=r,e[u+4>>2]=((r|0)<0)<<31>>31;break n}case 11:{u=(e[t>>2]|0)+3&-4,r=e[u>>2]|0,e[t>>2]=u+4,u=n,e[u>>2]=r,e[u+4>>2]=0;break n}case 12:{u=(e[t>>2]|0)+7&-8,r=u,o=e[r>>2]|0,r=e[r+4>>2]|0,e[t>>2]=u+8,u=n,e[u>>2]=o,e[u+4>>2]=r;break n}case 13:{o=(e[t>>2]|0)+3&-4,u=e[o>>2]|0,e[t>>2]=o+4,u=(u&65535)<<16>>16,o=n,e[o>>2]=u,e[o+4>>2]=((u|0)<0)<<31>>31;break n}case 14:{o=(e[t>>2]|0)+3&-4,u=e[o>>2]|0,e[t>>2]=o+4,o=n,e[o>>2]=u&65535,e[o+4>>2]=0;break n}case 15:{o=(e[t>>2]|0)+3&-4,u=e[o>>2]|0,e[t>>2]=o+4,u=(u&255)<<24>>24,o=n,e[o>>2]=u,e[o+4>>2]=((u|0)<0)<<31>>31;break n}case 16:{o=(e[t>>2]|0)+3&-4,u=e[o>>2]|0,e[t>>2]=o+4,o=n,e[o>>2]=u&255,e[o+4>>2]=0;break n}case 17:{o=(e[t>>2]|0)+7&-8,f=+O[o>>3],e[t>>2]=o+8,O[n>>3]=f;break n}case 18:{o=(e[t>>2]|0)+7&-8,f=+O[o>>3],e[t>>2]=o+8,O[n>>3]=f;break n}default:break n}while(!1);while(!1)}function iy(n,r,t,u){if(n=n|0,r=r|0,t=t|0,u=u|0,!((n|0)==0&(r|0)==0))do t=t+-1|0,w[t>>0]=B[5694+(n&15)>>0]|0|u,n=Nn(n|0,r|0,4)|0,r=v0;while(!((n|0)==0&(r|0)==0));return t|0}function ty(n,r,t){if(n=n|0,r=r|0,t=t|0,!((n|0)==0&(r|0)==0))do t=t+-1|0,w[t>>0]=n&7|48,n=Nn(n|0,r|0,3)|0,r=v0;while(!((n|0)==0&(r|0)==0));return t|0}function $1(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;if(r>>>0>0|(r|0)==0&n>>>0>4294967295){for(;u=or(n|0,r|0,10,0)|0,t=t+-1|0,w[t>>0]=u&255|48,u=n,n=ur(n|0,r|0,10,0)|0,r>>>0>9|(r|0)==9&u>>>0>4294967295;)r=v0;r=n}else r=n;if(r)for(;t=t+-1|0,w[t>>0]=(r>>>0)%10|0|48,!(r>>>0<10);)r=(r>>>0)/10|0;return t|0}function uy(n){return n=n|0,_y(n,e[(vy()|0)+188>>2]|0)|0}function oy(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;f=r&255,u=(t|0)!=0;n:do if(u&(n&3|0)!=0)for(o=r&255;;){if((w[n>>0]|0)==o<<24>>24){l=6;break n}if(n=n+1|0,t=t+-1|0,u=(t|0)!=0,!(u&(n&3|0)!=0)){l=5;break}}else l=5;while(!1);(l|0)==5&&(u?l=6:t=0);n:do if((l|0)==6&&(o=r&255,(w[n>>0]|0)!=o<<24>>24)){u=sr(f,16843009)|0;e:do if(t>>>0>3){for(;f=e[n>>2]^u,!((f&-2139062144^-2139062144)&f+-16843009|0);)if(n=n+4|0,t=t+-4|0,t>>>0<=3){l=11;break e}}else l=11;while(!1);if((l|0)==11&&!t){t=0;break}for(;;){if((w[n>>0]|0)==o<<24>>24)break n;if(n=n+1|0,t=t+-1|0,!t){t=0;break}}}while(!1);return(t|0?n:0)|0}function P0(n,r,t,u,o){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0;var f=0,l=0;if(l=s,s=s+256|0,f=l,(t|0)>(u|0)&(o&73728|0)==0){if(o=t-u|0,a1(f|0,r|0,(o>>>0<256?o:256)|0)|0,o>>>0>255){r=t-u|0;do E0(n,f,256),o=o+-256|0;while(o>>>0>255);o=r&255}E0(n,f,o)}s=l}function fu(n,r){return n=n|0,r=r|0,n?n=cy(n,r,0)|0:n=0,n|0}function fy(n,r,t,u,o,f){n=n|0,r=+r,t=t|0,u=u|0,o=o|0,f=f|0;var l=0,c=0,v=0,m=0,d=0,g=0,M=0,L=0,C=0,A=0,P=0,R=0,j=0,S=0,E=0,U=0,t0=0,K=0,Y=0,a=0,r0=0,G=0,m0=0;m0=s,s=s+560|0,v=m0+8|0,P=m0,G=m0+524|0,r0=G,m=m0+512|0,e[P>>2]=0,a=m+12|0,lu(r)|0,(v0|0)<0?(r=-r,K=1,t0=5659):(K=(o&2049|0)!=0&1,t0=o&2048|0?5662:o&1|0?5665:5660),lu(r)|0,Y=v0&2146435072;do if(Y>>>0<2146435072|(Y|0)==2146435072&!1){if(L=+ly(r,P)*2,l=L!=0,l&&(e[P>>2]=(e[P>>2]|0)+-1),j=f|32,(j|0)==97){C=f&32,M=C|0?t0+9|0:t0,g=K|2,l=12-u|0;do if(u>>>0>11|(l|0)==0)r=L;else{r=8;do l=l+-1|0,r=r*16;while(l|0);if((w[M>>0]|0)==45){r=-(r+(-L-r));break}else{r=L+r-r;break}}while(!1);c=e[P>>2]|0,l=(c|0)<0?0-c|0:c,l=$1(l,((l|0)<0)<<31>>31,a)|0,(l|0)==(a|0)&&(l=m+11|0,w[l>>0]=48),w[l+-1>>0]=(c>>31&2)+43,d=l+-2|0,w[d>>0]=f+15,m=(u|0)<1,v=(o&8|0)==0,l=G;do Y=~~r,c=l+1|0,w[l>>0]=B[5694+Y>>0]|C,r=(r-+(Y|0))*16,(c-r0|0)==1&&!(v&(m&r==0))?(w[c>>0]=46,l=l+2|0):l=c;while(r!=0);Y=l-r0|0,r0=a-d|0,a=(u|0)!=0&(Y+-2|0)<(u|0)?u+2|0:Y,l=r0+g+a|0,P0(n,32,t,l,o),E0(n,M,g),P0(n,48,t,l,o^65536),E0(n,G,Y),P0(n,48,a-Y|0,0,0),E0(n,d,r0),P0(n,32,t,l,o^8192);break}c=(u|0)<0?6:u,l?(l=(e[P>>2]|0)+-28|0,e[P>>2]=l,r=L*268435456):(r=L,l=e[P>>2]|0),Y=(l|0)<0?v:v+288|0,v=Y;do E=~~r>>>0,e[v>>2]=E,v=v+4|0,r=(r-+(E>>>0))*1e9;while(r!=0);if((l|0)>0)for(m=Y,g=v;;){if(d=(l|0)<29?l:29,l=g+-4|0,l>>>0>=m>>>0){v=0;do S=du(e[l>>2]|0,0,d|0)|0,S=tr(S|0,v0|0,v|0,0)|0,E=v0,R=or(S|0,E|0,1e9,0)|0,e[l>>2]=R,v=ur(S|0,E|0,1e9,0)|0,l=l+-4|0;while(l>>>0>=m>>>0);v&&(m=m+-4|0,e[m>>2]=v)}for(v=g;!(v>>>0<=m>>>0);)if(l=v+-4|0,!(e[l>>2]|0))v=l;else break;if(l=(e[P>>2]|0)-d|0,e[P>>2]=l,(l|0)>0)g=v;else break}else m=Y;if((l|0)<0){u=((c+25|0)/9|0)+1|0,A=(j|0)==102;do{if(C=0-l|0,C=(C|0)<9?C:9,m>>>0<v>>>0){d=(1<<C)+-1|0,g=1e9>>>C,M=0,l=m;do E=e[l>>2]|0,e[l>>2]=(E>>>C)+M,M=sr(E&d,g)|0,l=l+4|0;while(l>>>0<v>>>0);l=e[m>>2]|0?m:m+4|0,M?(e[v>>2]=M,m=l,l=v+4|0):(m=l,l=v)}else m=e[m>>2]|0?m:m+4|0,l=v;v=A?Y:m,v=(l-v>>2|0)>(u|0)?v+(u<<2)|0:l,l=(e[P>>2]|0)+C|0,e[P>>2]=l}while((l|0)<0);l=m,u=v}else l=m,u=v;if(E=Y,l>>>0<u>>>0){if(v=(E-l>>2)*9|0,d=e[l>>2]|0,d>>>0>=10){m=10;do m=m*10|0,v=v+1|0;while(d>>>0>=m>>>0)}}else v=0;if(A=(j|0)==103,R=(c|0)!=0,m=c-((j|0)!=102?v:0)+((R&A)<<31>>31)|0,(m|0)<(((u-E>>2)*9|0)+-9|0)){if(m=m+9216|0,C=Y+4+(((m|0)/9|0)+-1024<<2)|0,m=((m|0)%9|0)+1|0,(m|0)<9){d=10;do d=d*10|0,m=m+1|0;while((m|0)!=9)}else d=10;if(g=e[C>>2]|0,M=(g>>>0)%(d>>>0)|0,m=(C+4|0)==(u|0),m&(M|0)==0)m=C;else if(L=((g>>>0)/(d>>>0)|0)&1|0?9007199254740994:9007199254740992,S=(d|0)/2|0,r=M>>>0<S>>>0?.5:m&(M|0)==(S|0)?1:1.5,K&&(S=(w[t0>>0]|0)==45,r=S?-r:r,L=S?-L:L),m=g-M|0,e[C>>2]=m,L+r!=L){if(S=m+d|0,e[C>>2]=S,S>>>0>999999999)for(v=C;m=v+-4|0,e[v>>2]=0,m>>>0<l>>>0&&(l=l+-4|0,e[l>>2]=0),S=(e[m>>2]|0)+1|0,e[m>>2]=S,S>>>0>999999999;)v=m;else m=C;if(v=(E-l>>2)*9|0,g=e[l>>2]|0,g>>>0>=10){d=10;do d=d*10|0,v=v+1|0;while(g>>>0>=d>>>0)}}else m=C;m=m+4|0,m=u>>>0>m>>>0?m:u,S=l}else m=u,S=l;for(j=m;;){if(j>>>0<=S>>>0){P=0;break}if(l=j+-4|0,!(e[l>>2]|0))j=l;else{P=1;break}}u=0-v|0;do if(A)if(l=((R^1)&1)+c|0,(l|0)>(v|0)&(v|0)>-5?(d=f+-1|0,c=l+-1-v|0):(d=f+-2|0,c=l+-1|0),l=o&8,l)C=l;else{if(P&&(U=e[j+-4>>2]|0,(U|0)!=0))if((U>>>0)%10|0)m=0;else{m=0,l=10;do l=l*10|0,m=m+1|0;while(!((U>>>0)%(l>>>0)|0|0))}else m=9;if(l=((j-E>>2)*9|0)+-9|0,(d|32|0)==102){C=l-m|0,C=(C|0)>0?C:0,c=(c|0)<(C|0)?c:C,C=0;break}else{C=l+v-m|0,C=(C|0)>0?C:0,c=(c|0)<(C|0)?c:C,C=0;break}}else d=f,C=o&8;while(!1);if(A=c|C,g=(A|0)!=0&1,M=(d|32|0)==102,M)R=0,l=(v|0)>0?v:0;else{if(l=(v|0)<0?u:v,l=$1(l,((l|0)<0)<<31>>31,a)|0,m=a,(m-l|0)<2)do l=l+-1|0,w[l>>0]=48;while((m-l|0)<2);w[l+-1>>0]=(v>>31&2)+43,l=l+-2|0,w[l>>0]=d,R=l,l=m-l|0}if(l=K+1+c+g+l|0,P0(n,32,t,l,o),E0(n,t0,K),P0(n,48,t,l,o^65536),M){d=S>>>0>Y>>>0?Y:S,C=G+9|0,g=C,M=G+8|0,m=d;do{if(v=$1(e[m>>2]|0,0,C)|0,(m|0)==(d|0))(v|0)==(C|0)&&(w[M>>0]=48,v=M);else if(v>>>0>G>>>0){a1(G|0,48,v-r0|0)|0;do v=v+-1|0;while(v>>>0>G>>>0)}E0(n,v,g-v|0),m=m+4|0}while(m>>>0<=Y>>>0);if(A|0&&E0(n,5710,1),m>>>0<j>>>0&(c|0)>0)for(;;){if(v=$1(e[m>>2]|0,0,C)|0,v>>>0>G>>>0){a1(G|0,48,v-r0|0)|0;do v=v+-1|0;while(v>>>0>G>>>0)}if(E0(n,v,(c|0)<9?c:9),m=m+4|0,v=c+-9|0,m>>>0<j>>>0&(c|0)>9)c=v;else{c=v;break}}P0(n,48,c+9|0,9,0)}else{if(A=P?j:S+4|0,(c|0)>-1){P=G+9|0,C=(C|0)==0,u=P,g=0-r0|0,M=G+8|0,d=S;do{v=$1(e[d>>2]|0,0,P)|0,(v|0)==(P|0)&&(w[M>>0]=48,v=M);do if((d|0)==(S|0)){if(m=v+1|0,E0(n,v,1),C&(c|0)<1){v=m;break}E0(n,5710,1),v=m}else{if(v>>>0<=G>>>0)break;a1(G|0,48,v+g|0)|0;do v=v+-1|0;while(v>>>0>G>>>0)}while(!1);r0=u-v|0,E0(n,v,(c|0)>(r0|0)?r0:c),c=c-r0|0,d=d+4|0}while(d>>>0<A>>>0&(c|0)>-1)}P0(n,48,c+18|0,18,0),E0(n,R,a-R|0)}P0(n,32,t,l,o^8192)}else G=(f&32|0)!=0,l=K+3|0,P0(n,32,t,l,o&-65537),E0(n,t0,K),E0(n,r!=r|!1?G?5686:5690:G?5678:5682,3),P0(n,32,t,l,o^8192);while(!1);return s=m0,((l|0)<(t|0)?t:l)|0}function lu(n){n=+n;var r=0;return O[D>>3]=n,r=e[D>>2]|0,v0=e[D+4>>2]|0,r|0}function ly(n,r){return n=+n,r=r|0,+ +cu(n,r)}function cu(n,r){n=+n,r=r|0;var t=0,u=0,o=0;switch(O[D>>3]=n,t=e[D>>2]|0,u=e[D+4>>2]|0,o=Nn(t|0,u|0,52)|0,o&2047){case 0:{n!=0?(n=+cu(n*18446744073709552e3,r),t=(e[r>>2]|0)+-64|0):t=0,e[r>>2]=t;break}case 2047:break;default:e[r>>2]=(o&2047)+-1022,e[D>>2]=t,e[D+4>>2]=u&-2146435073|1071644672,n=+O[D>>3]}return+n}function cy(n,r,t){n=n|0,r=r|0,t=t|0;do if(n){if(r>>>0<128){w[n>>0]=r,n=1;break}if(!(e[e[(sy()|0)+188>>2]>>2]|0))if((r&-128|0)==57216){w[n>>0]=r,n=1;break}else{e[(Z1()|0)>>2]=84,n=-1;break}if(r>>>0<2048){w[n>>0]=r>>>6|192,w[n+1>>0]=r&63|128,n=2;break}if(r>>>0<55296|(r&-8192|0)==57344){w[n>>0]=r>>>12|224,w[n+1>>0]=r>>>6&63|128,w[n+2>>0]=r&63|128,n=3;break}if((r+-65536|0)>>>0<1048576){w[n>>0]=r>>>18|240,w[n+1>>0]=r>>>12&63|128,w[n+2>>0]=r>>>6&63|128,w[n+3>>0]=r&63|128,n=4;break}else{e[(Z1()|0)>>2]=84,n=-1;break}}else n=1;while(!1);return n|0}function sy(){return er()|0}function vy(){return er()|0}function _y(n,r){n=n|0,r=r|0;var t=0,u=0;for(u=0;;){if((B[5712+u>>0]|0)==(n|0)){n=2;break}if(t=u+1|0,(t|0)==87){t=5800,u=87,n=5;break}else u=t}if((n|0)==2&&(u?(t=5800,n=5):t=5800),(n|0)==5)for(;;){do n=t,t=t+1|0;while(w[n>>0]|0);if(u=u+-1|0,u)n=5;else break}return hy(t,e[r+20>>2]|0)|0}function hy(n,r){return n=n|0,r=r|0,dy(n,r)|0}function dy(n,r){return n=n|0,r=r|0,r?r=my(e[r>>2]|0,e[r+4>>2]|0,n)|0:r=0,(r|0?r:n)|0}function my(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0;M=(e[n>>2]|0)+1794895138|0,f=I1(e[n+8>>2]|0,M)|0,u=I1(e[n+12>>2]|0,M)|0,o=I1(e[n+16>>2]|0,M)|0;n:do if(f>>>0<r>>>2>>>0&&(g=r-(f<<2)|0,u>>>0<g>>>0&o>>>0<g>>>0)&&!((o|u)&3|0)){for(g=u>>>2,d=o>>>2,m=0;;){if(c=f>>>1,v=m+c|0,l=v<<1,o=l+g|0,u=I1(e[n+(o<<2)>>2]|0,M)|0,o=I1(e[n+(o+1<<2)>>2]|0,M)|0,!(o>>>0<r>>>0&u>>>0<(r-o|0)>>>0)){u=0;break n}if(w[n+(o+u)>>0]|0){u=0;break n}if(u=iu(t,n+o|0)|0,!u)break;if(u=(u|0)<0,(f|0)==1){u=0;break n}else m=u?m:v,f=u?c:f-c|0}u=l+d|0,o=I1(e[n+(u<<2)>>2]|0,M)|0,u=I1(e[n+(u+1<<2)>>2]|0,M)|0,u>>>0<r>>>0&o>>>0<(r-u|0)>>>0?u=w[n+(u+o)>>0]|0?0:n+u|0:u=0}else u=0;while(!1);return u|0}function I1(n,r){n=n|0,r=r|0;var t=0;return t=wu(n|0)|0,(r|0?t:n)|0}function py(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0,c=0;u=t+16|0,o=e[u>>2]|0,o?f=5:wy(t)|0?u=0:(o=e[u>>2]|0,f=5);n:do if((f|0)==5){if(c=t+20|0,l=e[c>>2]|0,u=l,(o-l|0)>>>0<r>>>0){u=In[e[t+36>>2]&7](t,n,r)|0;break}e:do if((w[t+75>>0]|0)>-1){for(l=r;;){if(!l){f=0,o=n;break e}if(o=l+-1|0,(w[n+o>>0]|0)==10)break;l=o}if(u=In[e[t+36>>2]&7](t,n,l)|0,u>>>0<l>>>0)break n;f=l,o=n+l|0,r=r-l|0,u=e[c>>2]|0}else f=0,o=n;while(!1);Q(u|0,o|0,r|0)|0,e[c>>2]=(e[c>>2]|0)+r,u=f+r|0}while(!1);return u|0}function wy(n){n=n|0;var r=0,t=0;return r=n+74|0,t=w[r>>0]|0,w[r>>0]=t+255|t,r=e[n>>2]|0,r&8?(e[n>>2]=r|32,n=-1):(e[n+8>>2]=0,e[n+4>>2]=0,t=e[n+44>>2]|0,e[n+28>>2]=t,e[n+20>>2]=t,e[n+16>>2]=t+(e[n+48>>2]|0),n=0),n|0}function k0(n,r){n=_(n),r=_(r);var t=0,u=0;t=su(n)|0;do if((t&2147483647)>>>0<=2139095040){if(u=su(r)|0,(u&2147483647)>>>0<=2139095040)if((u^t|0)<0){n=(t|0)<0?r:n;break}else{n=n<r?r:n;break}}else n=r;while(!1);return _(n)}function su(n){return n=_(n),k[D>>2]=n,e[D>>2]|0|0}function F1(n,r){n=_(n),r=_(r);var t=0,u=0;t=vu(n)|0;do if((t&2147483647)>>>0<=2139095040){if(u=vu(r)|0,(u&2147483647)>>>0<=2139095040)if((u^t|0)<0){n=(t|0)<0?n:r;break}else{n=n<r?n:r;break}}else n=r;while(!1);return _(n)}function vu(n){return n=_(n),k[D>>2]=n,e[D>>2]|0|0}function ir(n,r){n=_(n),r=_(r);var t=0,u=0,o=0,f=0,l=0,c=0,v=0,m=0;f=(k[D>>2]=n,e[D>>2]|0),c=(k[D>>2]=r,e[D>>2]|0),t=f>>>23&255,l=c>>>23&255,v=f&-2147483648,o=c<<1;n:do if(o|0&&!((t|0)==255|((gy(r)|0)&2147483647)>>>0>2139095040)){if(u=f<<1,u>>>0<=o>>>0)return r=_(n*_(0)),_((u|0)==(o|0)?r:n);if(t)u=f&8388607|8388608;else{if(t=f<<9,(t|0)>-1){u=t,t=0;do t=t+-1|0,u=u<<1;while((u|0)>-1)}else t=0;u=f<<1-t}if(l)c=c&8388607|8388608;else{if(f=c<<9,(f|0)>-1){o=0;do o=o+-1|0,f=f<<1;while((f|0)>-1)}else o=0;l=o,c=c<<1-o}o=u-c|0,f=(o|0)>-1;e:do if((t|0)>(l|0)){for(;;){if(f)if(o)u=o;else break;if(u=u<<1,t=t+-1|0,o=u-c|0,f=(o|0)>-1,(t|0)<=(l|0))break e}r=_(n*_(0));break n}while(!1);if(f)if(o)u=o;else{r=_(n*_(0));break}if(u>>>0<8388608)do u=u<<1,t=t+-1|0;while(u>>>0<8388608);(t|0)>0?t=u+-8388608|t<<23:t=u>>>(1-t|0),r=(e[D>>2]=t|v,_(k[D>>2]))}else m=3;while(!1);return(m|0)==3&&(r=_(n*r),r=_(r/r)),_(r)}function gy(n){return n=_(n),k[D>>2]=n,e[D>>2]|0|0}function yy(n,r){return n=n|0,r=r|0,tu(e[582]|0,n,r)|0}function c0(n){n=n|0,u0()}function Q1(n){n=n|0}function ky(n,r){return n=n|0,r=r|0,0}function My(n){return n=n|0,(_u(n+4|0)|0)==-1?(K2[e[(e[n>>2]|0)+8>>2]&127](n),n=1):n=0,n|0}function _u(n){n=n|0;var r=0;return r=e[n>>2]|0,e[n>>2]=r+-1,r+-1|0}function v1(n){n=n|0,My(n)|0&&Ty(n)}function Ty(n){n=n|0;var r=0;r=n+8|0,e[r>>2]|0&&(_u(r)|0)!=-1||K2[e[(e[n>>2]|0)+16>>2]&127](n)}function q(n){n=n|0;var r=0;for(r=n|0?n:1;n=Bn(r)|0,!(n|0);){if(n=Cy()|0,!n){n=0;break}Lu[n&0]()}return n|0}function hu(n){return n=n|0,q(n)|0}function I(n){n=n|0,Rn(n)}function Ay(n){n=n|0,(w[n+11>>0]|0)<0&&I(e[n>>2]|0)}function Cy(){var n=0;return n=e[2923]|0,e[2923]=n+0,n|0}function Sy(){}function On(n,r,t,u){return n=n|0,r=r|0,t=t|0,u=u|0,u=r-u-(t>>>0>n>>>0|0)>>>0,v0=u,n-t>>>0|0|0}function tr(n,r,t,u){return n=n|0,r=r|0,t=t|0,u=u|0,t=n+t>>>0,v0=r+u+(t>>>0<n>>>0|0)>>>0,t|0|0}function a1(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0,l=0;if(f=n+t|0,r=r&255,(t|0)>=67){for(;n&3;)w[n>>0]=r,n=n+1|0;for(u=f&-4|0,o=u-64|0,l=r|r<<8|r<<16|r<<24;(n|0)<=(o|0);)e[n>>2]=l,e[n+4>>2]=l,e[n+8>>2]=l,e[n+12>>2]=l,e[n+16>>2]=l,e[n+20>>2]=l,e[n+24>>2]=l,e[n+28>>2]=l,e[n+32>>2]=l,e[n+36>>2]=l,e[n+40>>2]=l,e[n+44>>2]=l,e[n+48>>2]=l,e[n+52>>2]=l,e[n+56>>2]=l,e[n+60>>2]=l,n=n+64|0;for(;(n|0)<(u|0);)e[n>>2]=l,n=n+4|0}for(;(n|0)<(f|0);)w[n>>0]=r,n=n+1|0;return f-t|0}function du(n,r,t){return n=n|0,r=r|0,t=t|0,(t|0)<32?(v0=r<<t|(n&(1<<t)-1<<32-t)>>>32-t,n<<t):(v0=n<<t-32,0)}function Nn(n,r,t){return n=n|0,r=r|0,t=t|0,(t|0)<32?(v0=r>>>t,n>>>t|(r&(1<<t)-1)<<32-t):(v0=0,r>>>t-32|0)}function Q(n,r,t){n=n|0,r=r|0,t=t|0;var u=0,o=0,f=0;if((t|0)>=8192)return qu(n|0,r|0,t|0)|0;if(f=n|0,o=n+t|0,(n&3)==(r&3)){for(;n&3;){if(!t)return f|0;w[n>>0]=w[r>>0]|0,n=n+1|0,r=r+1|0,t=t-1|0}for(t=o&-4|0,u=t-64|0;(n|0)<=(u|0);)e[n>>2]=e[r>>2],e[n+4>>2]=e[r+4>>2],e[n+8>>2]=e[r+8>>2],e[n+12>>2]=e[r+12>>2],e[n+16>>2]=e[r+16>>2],e[n+20>>2]=e[r+20>>2],e[n+24>>2]=e[r+24>>2],e[n+28>>2]=e[r+28>>2],e[n+32>>2]=e[r+32>>2],e[n+36>>2]=e[r+36>>2],e[n+40>>2]=e[r+40>>2],e[n+44>>2]=e[r+44>>2],e[n+48>>2]=e[r+48>>2],e[n+52>>2]=e[r+52>>2],e[n+56>>2]=e[r+56>>2],e[n+60>>2]=e[r+60>>2],n=n+64|0,r=r+64|0;for(;(n|0)<(t|0);)e[n>>2]=e[r>>2],n=n+4|0,r=r+4|0}else for(t=o-4|0;(n|0)<(t|0);)w[n>>0]=w[r>>0]|0,w[n+1>>0]=w[r+1>>0]|0,w[n+2>>0]=w[r+2>>0]|0,w[n+3>>0]=w[r+3>>0]|0,n=n+4|0,r=r+4|0;for(;(n|0)<(o|0);)w[n>>0]=w[r>>0]|0,n=n+1|0,r=r+1|0;return f|0}function mu(n){n=n|0;var r=0;return r=w[L0+(n&255)>>0]|0,(r|0)<8?r|0:(r=w[L0+(n>>8&255)>>0]|0,(r|0)<8?r+8|0:(r=w[L0+(n>>16&255)>>0]|0,(r|0)<8?r+16|0:(w[L0+(n>>>24)>>0]|0)+24|0))}function pu(n,r,t,u,o){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0;var f=0,l=0,c=0,v=0,m=0,d=0,g=0,M=0,L=0,C=0;if(d=n,v=r,m=v,l=t,M=u,c=M,!m)return f=(o|0)!=0,c?f?(e[o>>2]=n|0,e[o+4>>2]=r&0,M=0,o=0,v0=M,o|0):(M=0,o=0,v0=M,o|0):(f&&(e[o>>2]=(d>>>0)%(l>>>0),e[o+4>>2]=0),M=0,o=(d>>>0)/(l>>>0)>>>0,v0=M,o|0);f=(c|0)==0;do if(l){if(!f){if(f=(U1(c|0)|0)-(U1(m|0)|0)|0,f>>>0<=31){g=f+1|0,c=31-f|0,r=f-31>>31,l=g,n=d>>>(g>>>0)&r|m<<c,r=m>>>(g>>>0)&r,f=0,c=d<<c;break}return o?(e[o>>2]=n|0,e[o+4>>2]=v|r&0,M=0,o=0,v0=M,o|0):(M=0,o=0,v0=M,o|0)}if(f=l-1|0,f&l|0){c=(U1(l|0)|0)+33-(U1(m|0)|0)|0,C=64-c|0,g=32-c|0,v=g>>31,L=c-32|0,r=L>>31,l=c,n=g-1>>31&m>>>(L>>>0)|(m<<g|d>>>(c>>>0))&r,r=r&m>>>(c>>>0),f=d<<C&v,c=(m<<C|d>>>(L>>>0))&v|d<<g&c-33>>31;break}return o|0&&(e[o>>2]=f&d,e[o+4>>2]=0),(l|0)==1?(L=v|r&0,C=n|0|0,v0=L,C|0):(C=mu(l|0)|0,L=m>>>(C>>>0)|0,C=m<<32-C|d>>>(C>>>0)|0,v0=L,C|0)}else{if(f)return o|0&&(e[o>>2]=(m>>>0)%(l>>>0),e[o+4>>2]=0),L=0,C=(m>>>0)/(l>>>0)>>>0,v0=L,C|0;if(!d)return o|0&&(e[o>>2]=0,e[o+4>>2]=(m>>>0)%(c>>>0)),L=0,C=(m>>>0)/(c>>>0)>>>0,v0=L,C|0;if(f=c-1|0,!(f&c))return o|0&&(e[o>>2]=n|0,e[o+4>>2]=f&m|r&0),L=0,C=m>>>((mu(c|0)|0)>>>0),v0=L,C|0;if(f=(U1(c|0)|0)-(U1(m|0)|0)|0,f>>>0<=30){r=f+1|0,c=31-f|0,l=r,n=m<<c|d>>>(r>>>0),r=m>>>(r>>>0),f=0,c=d<<c;break}return o?(e[o>>2]=n|0,e[o+4>>2]=v|r&0,L=0,C=0,v0=L,C|0):(L=0,C=0,v0=L,C|0)}while(!1);if(!l)m=c,v=0,c=0;else{g=t|0|0,d=M|u&0,m=tr(g|0,d|0,-1,-1)|0,t=v0,v=c,c=0;do u=v,v=f>>>31|v<<1,f=c|f<<1,u=n<<1|u>>>31|0,M=n>>>31|r<<1|0,On(m|0,t|0,u|0,M|0)|0,C=v0,L=C>>31|((C|0)<0?-1:0)<<1,c=L&1,n=On(u|0,M|0,L&g|0,(((C|0)<0?-1:0)>>31|((C|0)<0?-1:0)<<1)&d|0)|0,r=v0,l=l-1|0;while(l|0);m=v,v=0}return l=0,o|0&&(e[o>>2]=n,e[o+4>>2]=r),L=(f|0)>>>31|(m|l)<<1|(l<<1|f>>>31)&0|v,C=(f<<1|0)&-2|c,v0=L,C|0}function ur(n,r,t,u){return n=n|0,r=r|0,t=t|0,u=u|0,pu(n,r,t,u,0)|0}function _1(n){n=n|0;var r=0,t=0;return t=n+15&-16|0,r=e[H>>2]|0,n=r+t|0,(t|0)>0&(n|0)<(r|0)|(n|0)<0?(Ou()|0,hr(12),-1):(e[H>>2]=n,(n|0)>(Pu()|0)&&!(Ru()|0)?(e[H>>2]=r,hr(12),-1):r|0)}function cn(n,r,t){n=n|0,r=r|0,t=t|0;var u=0;if((r|0)<(n|0)&(n|0)<(r+t|0)){for(u=n,r=r+t|0,n=n+t|0;(t|0)>0;)n=n-1|0,r=r-1|0,t=t-1|0,w[n>>0]=w[r>>0]|0;n=u}else Q(n,r,t)|0;return n|0}function or(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0;var o=0,f=0;return f=s,s=s+16|0,o=f|0,pu(n,r,t,u,o)|0,s=f,v0=e[o+4>>2]|0,e[o>>2]|0|0}function wu(n){return n=n|0,(n&255)<<24|(n>>8&255)<<16|(n>>16&255)<<8|n>>>24|0}function Ey(n,r,t,u,o,f){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0,gu[n&1](r|0,t|0,u|0,o|0,f|0)}function Ly(n,r,t){n=n|0,r=r|0,t=_(t),yu[n&1](r|0,_(t))}function By(n,r,t){n=n|0,r=r|0,t=+t,ku[n&31](r|0,+t)}function Ry(n,r,t,u){return n=n|0,r=r|0,t=_(t),u=_(u),_(Mu[n&0](r|0,_(t),_(u)))}function Py(n,r){n=n|0,r=r|0,K2[n&127](r|0)}function Oy(n,r,t){n=n|0,r=r|0,t=t|0,X2[n&31](r|0,t|0)}function Ny(n,r){return n=n|0,r=r|0,D1[n&31](r|0)|0}function jy(n,r,t,u,o){n=n|0,r=r|0,t=+t,u=+u,o=o|0,Tu[n&1](r|0,+t,+u,o|0)}function Iy(n,r,t,u){n=n|0,r=r|0,t=+t,u=+u,dk[n&1](r|0,+t,+u)}function Fy(n,r,t,u){return n=n|0,r=r|0,t=t|0,u=u|0,In[n&7](r|0,t|0,u|0)|0}function xy(n,r,t,u){return n=n|0,r=r|0,t=t|0,u=u|0,+mk[n&1](r|0,t|0,u|0)}function Dy(n,r){return n=n|0,r=r|0,+Au[n&15](r|0)}function Hy(n,r,t){return n=n|0,r=r|0,t=+t,pk[n&1](r|0,+t)|0}function Uy(n,r,t){return n=n|0,r=r|0,t=t|0,lr[n&15](r|0,t|0)|0}function qy(n,r,t,u,o,f){n=n|0,r=r|0,t=t|0,u=+u,o=+o,f=f|0,wk[n&1](r|0,t|0,+u,+o,f|0)}function zy(n,r,t,u,o,f,l){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0,l=l|0,gk[n&1](r|0,t|0,u|0,o|0,f|0,l|0)}function Wy(n,r,t){return n=n|0,r=r|0,t=t|0,+Cu[n&7](r|0,t|0)}function Yy(n){return n=n|0,Fn[n&7]()|0}function Vy(n,r,t,u,o,f){return n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0,Su[n&1](r|0,t|0,u|0,o|0,f|0)|0}function Gy(n,r,t,u,o){n=n|0,r=r|0,t=t|0,u=u|0,o=+o,yk[n&1](r|0,t|0,u|0,+o)}function Ky(n,r,t,u,o,f,l){n=n|0,r=r|0,t=t|0,u=_(u),o=o|0,f=_(f),l=l|0,Eu[n&1](r|0,t|0,_(u),o|0,_(f),l|0)}function Xy(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0,_n[n&15](r|0,t|0,u|0)}function Jy(n){n=n|0,Lu[n&0]()}function Zy(n,r,t,u){n=n|0,r=r|0,t=t|0,u=+u,Bu[n&15](r|0,t|0,+u)}function $y(n,r,t){return n=n|0,r=+r,t=+t,kk[n&1](+r,+t)|0}function Qy(n,r,t,u,o){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,cr[n&15](r|0,t|0,u|0,o|0)}function ay(n,r,t,u,o){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,T0(0)}function by(n,r){n=n|0,r=_(r),T0(1)}function J0(n,r){n=n|0,r=+r,T0(2)}function nk(n,r,t){return n=n|0,r=_(r),t=_(t),T0(3),x}function Z(n){n=n|0,T0(4)}function sn(n,r){n=n|0,r=r|0,T0(5)}function r2(n){return n=n|0,T0(6),0}function ek(n,r,t,u){n=n|0,r=+r,t=+t,u=u|0,T0(7)}function rk(n,r,t){n=n|0,r=+r,t=+t,T0(8)}function ik(n,r,t){return n=n|0,r=r|0,t=t|0,T0(9),0}function tk(n,r,t){return n=n|0,r=r|0,t=t|0,T0(10),0}function x1(n){return n=n|0,T0(11),0}function uk(n,r){return n=n|0,r=+r,T0(12),0}function vn(n,r){return n=n|0,r=r|0,T0(13),0}function ok(n,r,t,u,o){n=n|0,r=r|0,t=+t,u=+u,o=o|0,T0(14)}function fk(n,r,t,u,o,f){n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,f=f|0,T0(15)}function fr(n,r){return n=n|0,r=r|0,T0(16),0}function lk(){return T0(17),0}function ck(n,r,t,u,o){return n=n|0,r=r|0,t=t|0,u=u|0,o=o|0,T0(18),0}function sk(n,r,t,u){n=n|0,r=r|0,t=t|0,u=+u,T0(19)}function vk(n,r,t,u,o,f){n=n|0,r=r|0,t=_(t),u=u|0,o=_(o),f=f|0,T0(20)}function jn(n,r,t){n=n|0,r=r|0,t=t|0,T0(21)}function _k(){T0(22)}function b1(n,r,t){n=n|0,r=r|0,t=+t,T0(23)}function hk(n,r){return n=+n,r=+r,T0(24),0}function nn(n,r,t,u){n=n|0,r=r|0,t=t|0,u=u|0,T0(25)}var gu=[ay,cm],yu=[by,l4],ku=[J0,R4,P4,O4,N4,j4,I4,F4,D4,H4,q4,z4,W4,Y4,V4,G4,K4,X4,J4,J0,J0,J0,J0,J0,J0,J0,J0,J0,J0,J0,J0,J0],Mu=[nk],K2=[Z,Q1,Yc,Vc,Gc,k6,M6,T6,Hh,Uh,qh,$d,Qd,ad,gg,yg,kg,io,_4,h4,x4,U4,Ul,ql,jc,bc,vs,Rs,Gs,l8,S8,q8,e3,w3,j3,Z3,v6,U6,ev,wv,jv,Zv,v_,P_,G_,u9,M9,o4,a9,d5,j5,Q5,_7,j7,Y7,K7,ch,_h,Rh,Wh,Gh,ld,Ed,ti,fp,Hp,bp,dw,xw,Qw,lg,vg,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z,Z],X2=[sn,d4,m4,g4,y4,k4,M4,T4,A4,E4,L4,B4,cl,_l,hl,dl,ml,pl,wl,Tl,El,Zl,I_,J_,e7,sp,Id,Wt,sn,sn,sn,sn],D1=[r2,Zg,v4,a4,nl,el,rl,il,tl,ul,fl,ll,Al,Cl,zl,C9,p7,vd,dp,b0,r2,r2,r2,r2,r2,r2,r2,r2,r2,r2,r2,r2],Tu=[ek,Wl],dk=[rk,Nh],In=[ik,ru,$g,bg,_8,Y6,r5,gw],mk=[tk,D3],Au=[x1,sl,vl,gl,Yl,Vl,Gl,Kl,Xl,Jl,x1,x1,x1,x1,x1,x1],pk=[uk,q7],lr=[vn,ky,Sl,Dc,js,B8,Y8,m6,uv,c9,c4,iw,vn,vn,vn,vn],wk=[ok,ms],gk=[fk,qw],Cu=[fr,yl,$l,Ql,al,b3,fr,fr],Fn=[lk,bl,s4,t4,$7,ph,Zh,mg],Su=[ck,Hf],yk=[sk,Dv],Eu=[vk,Ll],_n=[jn,b4,ol,kl,Ml,Zs,t3,bv,m_,f4,Nm,Wp,eg,jn,jn,jn],Lu=[_k],Bu=[b1,p4,w4,C4,S4,Z4,$4,Q4,Mv,g5,x7,b1,b1,b1,b1,b1],kk=[hk,xh],cr=[nn,M3,N9,D5,S7,rh,Th,rd,Od,kp,Eg,nn,nn,nn,nn,nn];return{_llvm_bswap_i32:wu,dynCall_idd:$y,dynCall_i:Yy,_i64Subtract:On,___udivdi3:ur,dynCall_vif:Ly,setThrew:bu,dynCall_viii:Xy,_bitshift64Lshr:Nn,_bitshift64Shl:du,dynCall_vi:Py,dynCall_viiddi:qy,dynCall_diii:xy,dynCall_iii:Uy,_memset:a1,_sbrk:_1,_memcpy:Q,__GLOBAL__sub_I_Yoga_cpp:i4,dynCall_vii:Oy,___uremdi3:or,dynCall_vid:By,stackAlloc:Zu,_nbind_init:Dg,getTempRet0:eo,dynCall_di:Dy,dynCall_iid:Hy,setTempRet0:no,_i64Add:tr,dynCall_fiff:Ry,dynCall_iiii:Fy,_emscripten_get_global_libc:Jg,dynCall_viid:Zy,dynCall_viiid:Gy,dynCall_viififi:Ky,dynCall_ii:Ny,__GLOBAL__sub_I_Binding_cc:bm,dynCall_viiii:Qy,dynCall_iiiiii:Vy,stackSave:$u,dynCall_viiiii:Ey,__GLOBAL__sub_I_nbind_cc:nc,dynCall_vidd:Iy,_free:Rn,runPostSets:Sy,dynCall_viiiiii:zy,establishStackSpace:au,_memmove:cn,stackRestore:Qu,_malloc:Bn,__GLOBAL__sub_I_common_cc:yd,dynCall_viddi:jy,dynCall_dii:Wy,dynCall_v:Jy}}(Module.asmGlobalArg,Module.asmLibraryArg,buffer),_llvm_bswap_i32=Module._llvm_bswap_i32=asm._llvm_bswap_i32,getTempRet0=Module.getTempRet0=asm.getTempRet0,___udivdi3=Module.___udivdi3=asm.___udivdi3,setThrew=Module.setThrew=asm.setThrew,_bitshift64Lshr=Module._bitshift64Lshr=asm._bitshift64Lshr,_bitshift64Shl=Module._bitshift64Shl=asm._bitshift64Shl,_memset=Module._memset=asm._memset,_sbrk=Module._sbrk=asm._sbrk,_memcpy=Module._memcpy=asm._memcpy,stackAlloc=Module.stackAlloc=asm.stackAlloc,___uremdi3=Module.___uremdi3=asm.___uremdi3,_nbind_init=Module._nbind_init=asm._nbind_init,_i64Subtract=Module._i64Subtract=asm._i64Subtract,setTempRet0=Module.setTempRet0=asm.setTempRet0,_i64Add=Module._i64Add=asm._i64Add,_emscripten_get_global_libc=Module._emscripten_get_global_libc=asm._emscripten_get_global_libc,__GLOBAL__sub_I_Yoga_cpp=Module.__GLOBAL__sub_I_Yoga_cpp=asm.__GLOBAL__sub_I_Yoga_cpp,__GLOBAL__sub_I_Binding_cc=Module.__GLOBAL__sub_I_Binding_cc=asm.__GLOBAL__sub_I_Binding_cc,stackSave=Module.stackSave=asm.stackSave,__GLOBAL__sub_I_nbind_cc=Module.__GLOBAL__sub_I_nbind_cc=asm.__GLOBAL__sub_I_nbind_cc,_free=Module._free=asm._free,runPostSets=Module.runPostSets=asm.runPostSets,establishStackSpace=Module.establishStackSpace=asm.establishStackSpace,_memmove=Module._memmove=asm._memmove,stackRestore=Module.stackRestore=asm.stackRestore,_malloc=Module._malloc=asm._malloc,__GLOBAL__sub_I_common_cc=Module.__GLOBAL__sub_I_common_cc=asm.__GLOBAL__sub_I_common_cc,dynCall_viiiii=Module.dynCall_viiiii=asm.dynCall_viiiii,dynCall_vif=Module.dynCall_vif=asm.dynCall_vif,dynCall_vid=Module.dynCall_vid=asm.dynCall_vid,dynCall_fiff=Module.dynCall_fiff=asm.dynCall_fiff,dynCall_vi=Module.dynCall_vi=asm.dynCall_vi,dynCall_vii=Module.dynCall_vii=asm.dynCall_vii,dynCall_ii=Module.dynCall_ii=asm.dynCall_ii,dynCall_viddi=Module.dynCall_viddi=asm.dynCall_viddi,dynCall_vidd=Module.dynCall_vidd=asm.dynCall_vidd,dynCall_iiii=Module.dynCall_iiii=asm.dynCall_iiii,dynCall_diii=Module.dynCall_diii=asm.dynCall_diii,dynCall_di=Module.dynCall_di=asm.dynCall_di,dynCall_iid=Module.dynCall_iid=asm.dynCall_iid,dynCall_iii=Module.dynCall_iii=asm.dynCall_iii,dynCall_viiddi=Module.dynCall_viiddi=asm.dynCall_viiddi,dynCall_viiiiii=Module.dynCall_viiiiii=asm.dynCall_viiiiii,dynCall_dii=Module.dynCall_dii=asm.dynCall_dii,dynCall_i=Module.dynCall_i=asm.dynCall_i,dynCall_iiiiii=Module.dynCall_iiiiii=asm.dynCall_iiiiii,dynCall_viiid=Module.dynCall_viiid=asm.dynCall_viiid,dynCall_viififi=Module.dynCall_viififi=asm.dynCall_viififi,dynCall_viii=Module.dynCall_viii=asm.dynCall_viii,dynCall_v=Module.dynCall_v=asm.dynCall_v,dynCall_viid=Module.dynCall_viid=asm.dynCall_viid,dynCall_idd=Module.dynCall_idd=asm.dynCall_idd,dynCall_viiii=Module.dynCall_viiii=asm.dynCall_viiii;Runtime.stackAlloc=Module.stackAlloc,Runtime.stackSave=Module.stackSave,Runtime.stackRestore=Module.stackRestore,Runtime.establishStackSpace=Module.establishStackSpace,Runtime.setTempRet0=Module.setTempRet0,Runtime.getTempRet0=Module.getTempRet0,Module.asm=asm;function ExitStatus(h){this.name="ExitStatus",this.message="Program terminated with exit("+h+")",this.status=h}ExitStatus.prototype=new Error,ExitStatus.prototype.constructor=ExitStatus;var initialStackTop,preloadStartTime=null,calledMain=!1;dependenciesFulfilled=function h(){Module.calledRun||run(),Module.calledRun||(dependenciesFulfilled=h)},Module.callMain=Module.callMain=function h(p){p=p||[],ensureInitRuntime();var y=p.length+1;function w(){for(var F=0;F<3;F++)T.push(0)}var T=[allocate(intArrayFromString(Module.thisProgram),"i8",ALLOC_NORMAL)];w();for(var e=0;e<y-1;e=e+1)T.push(allocate(intArrayFromString(p[e]),"i8",ALLOC_NORMAL)),w();T.push(0),T=allocate(T,"i32",ALLOC_NORMAL);try{var B=Module._main(y,T,0);exit(B,!0)}catch(F){if(F instanceof ExitStatus)return;if(F=="SimulateInfiniteLoop"){Module.noExitRuntime=!0;return}else{var N=F;F&&typeof F=="object"&&F.stack&&(N=[F,F.stack]),Module.printErr("exception thrown: "+N),Module.quit(1,F)}}finally{calledMain=!0}};function run(h){if(h=h||Module.arguments,preloadStartTime===null&&(preloadStartTime=Date.now()),runDependencies>0||(preRun(),runDependencies>0)||Module.calledRun)return;function p(){Module.calledRun||(Module.calledRun=!0,!ABORT&&(ensureInitRuntime(),preMain(),Module.onRuntimeInitialized&&Module.onRuntimeInitialized(),Module._main&&shouldRunNow&&Module.callMain(h),postRun()))}Module.setStatus?(Module.setStatus("Running..."),setTimeout(function(){setTimeout(function(){Module.setStatus("")},1),p()},1)):p()}Module.run=Module.run=run;function exit(h,p){p&&Module.noExitRuntime||(Module.noExitRuntime||(ABORT=!0,EXITSTATUS=h,STACKTOP=initialStackTop,exitRuntime(),Module.onExit&&Module.onExit(h)),ENVIRONMENT_IS_NODE&&process.exit(h),Module.quit(h,new ExitStatus(h)))}Module.exit=Module.exit=exit;var abortDecorators=[];function abort(h){Module.onAbort&&Module.onAbort(h),h!==void 0?(Module.print(h),Module.printErr(h),h=JSON.stringify(h)):h="",ABORT=!0,EXITSTATUS=1;var p=`
If this abort() is unexpected, build with -s ASSERTIONS=1 which can give more information.`,y="abort("+h+") at "+stackTrace()+p;throw abortDecorators&&abortDecorators.forEach(function(w){y=w(y,h)}),y}if(Module.abort=Module.abort=abort,Module.preInit)for(typeof Module.preInit=="function"&&(Module.preInit=[Module.preInit]);Module.preInit.length>0;)Module.preInit.pop()();var shouldRunNow=!0;Module.noInitialRun&&(shouldRunNow=!1),run()})}}]);

//# sourceMappingURL=__-build-Release-nbind_js-lib.636af718.async.js.map