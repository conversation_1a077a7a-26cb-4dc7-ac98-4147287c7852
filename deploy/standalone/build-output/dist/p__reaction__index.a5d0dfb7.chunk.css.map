{"version": 3, "file": "p__reaction__index.a5d0dfb7.chunk.css", "mappings": "AAAA,CAAC,gCAAgC,CAAjC,0BAEY,IAAZ,CAFA,CAAC,gCAAgC,CAAjC,6BAMM,SAAU,SACV,QAAS,IADf,CANA,CAAC,gCAAgC,CAAjC,gCAUM,iBAAkB,IADxB,CATA,CAAC,gCAAgC,CAAjC,iDAciB,IAAI,IAFrB,CAZA,CAAC,gCAAgC,CAAjC,6CAkBM,WAAY,EACZ,YAAa,IAHnB,CAhBA,CAAC,gCAAgC,CAAjC,2DAuBM,QAAS,IAJf,CAnBA,CAAC,gCAAgC,CAAjC,8BA0BM,YAAa,IAJnB,CAtBA,CAAC,gCAAgC,CAAjC,uBA+BY,KAAK,IANjB,CAzBA,CAAC,gCAAgC,CAAjC,+BAiCM,QAAS,IALf,CAMM,CAlCL,gCAkCK,CAlCN,gBAkCM,CAlCN,aAkCM,QACE,SAAU,SACV,KAAM,EACN,MAAO,IACP,OAAQ,KACR,WAAY,IACZ,iBAAkB,QAxC1B,cAyCuB,IACf,QAAS,EAJjB,CAtCA,CAAC,gCAAgC,CAAjC,gCA8CM,YAAa,IALnB,CAzCA,CAAC,gCAAgC,CAAjC,WAmDI,uBAAuB,KAP3B,CA5CA,CAAC,gCAAgC,CAAjC,8EAsDQ,MAAO,KAAK,IAAI,uBAAuB,EAAE,KAPjD,CA/CA,CAAC,gCAAgC,CAAjC,8EAAC,gCAAgC,CAAjC,6EAyDQ,mBAAoB,IAAI,sBAPhC,CAlDA,CAAC,gCAAgC,CAAjC,gFA+DQ,MAAO,KAAK,KAAK,EAAE,IAAI,uBAAuB,EAAE,MAChD,mBAAoB,KAAK,IAAI,uBAAuB,EAAE,IAP9D,CAzDA,CAAC,gCAAgC,CAAjC,sCAoEM,YAAa,IARnB,CAWM,CAvEL,gCAuEK,CAvEN,WAuEM,yDACE,QAAS,KACT,sBAAuB,IAAI,GATnC,CAhEA,CAAC,gCAAgC,CAAjC,kDAgFM,YAAa,MAbnB,CAnEA,CAAC,gCAAgC,CAAjC,YAmFM,aAAc,KACd,iBAAkB,WAbxB,CAvEA,CAAC,gCAAgC,CAAjC,+BAuFM,YAAa,IAbnB,CA1EA,CAAC,gCAAgC,CAAjC,8BA8FQ,MAAO,YACP,UAAW,MA/FnB,QAgGiB,EAAE,IACX,WAAY,OACZ,cAAe,IAAI,MAlG3B,cAmGuB,CAjBvB,CClFA,CAAC,oBAAD,CDAA,cCGM,QAAS,KACT,gBAAiB,aADvB,CCHA,CAAC,eACC,QAAS,KACT,UAAW,EACX,UAAW,OACX,gBAAiB,MACnB,CACE,CAND,cAMC,6BAEI,OAAQ,YACR,WAAY,uBACZ,WAAY,cAAlB,CAVA,CAAC,eAAe,CAMd,IASE,QAAS,KACT,YAAa,OACb,gBAAiB,MAFrB,CAfA,CAAC,eAAe,CAMd,IANF,IAmBM,OAAQ,KFnBd,QEoBe,IACT,WAAY,QACZ,WAAY,KADlB,CArBA,CAAC,eAAe,CAAhB,KA2BI,WAAY,OACZ,MAAO,KACP,OAAQ,KACR,OAAQ,IAHZ,CC3BA,CAAC,wBACC,SAAU,SACV,QAAS,KACT,KAAM,EAAE,EAAE,KACV,gBAAiB,OACjB,MAAO,KACP,OAAQ,KACR,OAAQ,IACV,CACE,CATD,uBASC,wBACE,QAAS,IACb,CAXA,CAAC,wBAAwB,CAAzB,IAcI,MAAO,eACP,OAAQ,cAAZ,CAfA,CAAC,wBAAwB,CAAzB,iBAmBI,QAAS,KACT,gBAAiB,OACjB,MAAO,IADX,CApBA,CAAC,wBAAwB,CAAzB,qCAuBM,MAAO,KACP,OAAQ,IAAd,CAxBA,CAAC,wBAAwB,CASvB,gBAoBE,SAAU,SACV,IAAK,EACL,MAAO,EACP,QAAS,EACT,QAAS,KACT,IAAK,IHlCT,QGmCa,GAFb,CAjCA,CAAC,wBAAwB,CASvB,gBATF,gBAqCM,SAAU,SACV,QAAS,KACT,MAAO,KACP,OAAQ,KACR,OAAQ,OADd,CAxCA,CAAC,wBAAwB,CASvB,gBATF,yBA4CQ,KAAM,OADd,CAGM,CA9CL,wBA8CK,CArCJ,gBAqCI,CA9CN,cA8CM,gBAEI,KAAM,OAFhB,CA9CA,CAAC,wBAAwB,CASvB,gBATF,2CAsDQ,OAAQ,OALhB,CAOM,CAxDL,wBAwDK,CA/CJ,gBA+CI,CAxDN,qBAwDM,2BAEI,OAAQ,OANlB,CAaA,CAAC,OACC,SAAU,SACV,IAAK,IACL,KAAM,GAXR,CAcA,CANC,MAMM,OACL,OAAQ,iBAZV,CC5DA,CAAC,yBAAD,4BAEI,cAAe,IAAnB,CAFA,CAAC,yBAAD,oCAOQ,cAAe,GAFvB,CALA,CAAC,yBAAD,gDAUQ,eAAgB,GAFxB,CARA,CAAC,yBAAD,6EAYU,QAAS,KJZnB,QIamB,EACM,KADN,GAAnB,CAbA,CAAC,yBAAD,+EAiBU,MAAO,IADjB,CAhBA,CAAC,yBAAD,qD,CAAC,yB,CAAD,Y,gCA2BQ,MAAO,KACP,OAAQ,IAPhB,CASM,CA9BL,yBA8BK,CA9BN,WA8BM,gBA9BN,uCA+BQ,MAAO,WAPf,CAxBA,CAAC,yBAAD,iGAoCQ,QAAS,cATjB,CA3BA,CAAC,yBAAD,yBAuCQ,QAAS,KACT,eAAgB,OAChB,YAAa,OACb,gBAAiB,OACjB,MAAO,KACP,OAAQ,IAThB,CAWM,CA9CL,yBA8CK,CA9CN,WA8CM,6BAEI,QAAS,IAVnB,CAtCA,CAAC,yBAAD,sBAuDI,YAAa,IAdjB,CCzCA,CAAC,qBACC,SAAU,SACV,QAAS,KACT,MAAO,IACT,CAJA,CAAC,qBAAD,kBAKI,SAAU,SACV,IAAK,EACL,MAAO,CAEX,CACA,CAVC,qBAUD,GACE,SAAU,SACV,OAAQ,KACR,aAAc,KACd,YAAa,IACb,UAAW,KACX,YAAa,KACb,WAAY,IACd,CACA,CAnBC,qBAmBD,UACE,SAAU,SACV,IAAK,IACL,KAAM,EACN,MAAO,IACP,OAAQ,KACR,WAAY,MACZ,iBAAkB,QL1BpB,cK6BiB,IACf,QAAS,EACX,CC9BA,CAAC,kBACC,MAAO,KACP,UAAW,MACX,OAAQ,KACR,cAAe,KACf,OAAQ,IAAI,MAAM,OAWpB,CCjBA,CAAC,qBAAqB,CAAtB,QAEI,MAAO,QACP,WAAY,gBAAgB,IAAhB,CAAsB,OAAtB,CAA+B,QAAQ,CAAnD,sCAEA,OAAQ,IAAI,MAAM,OADtB,CAJA,CAAC,qBAAqB,QAAtB,iCASM,YAAa,WAFnB,CAPA,CAAC,qBAAqB,QAAtB,2BAYM,QAAS,IAFf,CAVA,CAAC,qBAAqB,CAAtB,kBAgBI,MAAO,KACP,OAAQ,IAHZ,CAII,CAlBH,qBAkBG,CAlBJ,iBAkBI,aAlBH,qBAkBG,CAlBJ,iBAkBI,oBACE,OAAQ,OAFd,CAjBA,CAAC,qBAAqB,CAAtB,kCAyBM,QAAS,KACT,eAAgB,IP1BtB,QO2Be,EAAE,GAFjB,CAzBA,CAAC,qBAAqB,CAAtB,sDA6BQ,YAAa,IADrB,CA5BA,CAAC,qBAAqB,CAAtB,iCAiCM,aAAc,IACd,UAAW,KACX,YAAa,IAFnB,CAOA,CAAC,qBAAqB,QAAtB,CAxCA,kBPAA,QO2Ce,IAAI,aAPnB,CAYA,CAAC,oBAAoB,QAArB,mBPhDA,QOsDe,KAAK,IAAI,aAfxB,CASA,CAAC,oBAAoB,QAArB,qCAIQ,aAAc,IAVtB,CAiBA,CAAC,UACC,OAAQ,IAfV,CAcA,CAAC,UAAU,CAAX,YAGI,SAAU,SACV,IAAK,IACL,MAAO,KACP,OAAQ,IAdZ,CClDA,CAAC,qBAAqB,CAAtB,2BAEI,QAAS,IAUb,CANA,CJPA,YIOa,CJPb,uC,CAAA,Y,gCIUI,MAAO,KACP,OAAQ,IAOZ,CALE,CJbF,WIaE,CJiBI,cIjBJ,CJbF,uCIcI,MAAO,WAOX,CAdA,CJPA,YIOa,CJPb,6BIOA,CJPA,yBIOA,EJPA,0BImBI,QAAS,cAKb,CAjBA,CJPA,YIOa,CJPb,WIsBI,QAAS,KACT,eAAgB,OAChB,YAAa,OACb,gBAAiB,OACjB,MAAO,KACP,OAAQ,IAKZ,CAHE,CJ7BF,WI6BE,CJiBI,gBIjBJ,CJiBI,WIfA,QAAS,IAIf,CACA,CAAC,eACC,MAAO,KAAK,MAAZ,kBACF,CAIA,CAAC,iBACC,MAAO,KAAK,MAAZ,kBAFF,CAxCA,CAAC,6BAAD,oCAEI,QAAS,IAUb,CANA,CAAC,oBAAD,gD,CAAC,oB,wCAGG,MAAO,KACP,OAAQ,IAOZ,CALE,CAND,mBAMC,wBANF,+CAOI,MAAO,WAOX,CAdA,CAAC,oBAAD,4GAYI,QAAS,cAKb,CAjBA,CAAC,oBAAD,oBAeI,QAAS,KACT,eAAgB,OAChB,YAAa,OACb,gBAAiB,OACjB,MAAO,KACP,OAAQ,IAKZ,CAHE,CAtBD,mBAsBC,6CAEI,QAAS,IAIf,CACA,CAAC,uBACC,MAAO,KAAK,MAAZ,kBACF,CAIA,CAAC,yBACC,MAAO,KAAK,MAAZ,kBAFF,CCzCA,CAAC,6BAA6B,CAA9B,gBAEI,YAAa,IAAjB,CAFA,CAAC,6BAA6B,CAA9B,gBAKI,QAAS,KACT,gBAAiB,MAArB,CANA,CAAC,6BAA6B,CAA9B,iBTAA,QSQM,OAAQ,IACd,CAIA,CAAC,iBAAiB,CLblB,YKaA,CLbA,uC,CKaC,iB,CLbD,Y,gCKiBM,MAAO,KACP,OAAQ,IAJd,CAMI,CAPH,iBAOG,CLpBJ,WKoBI,CLUE,cKVF,CLpBJ,uCKqBM,MAAO,WAJb,CAJA,CAAC,iBAAiB,CLblB,YKaA,CLbA,6BKaA,CLbA,yBKaA,ELbA,0BK0BM,QAAS,cANf,CAPA,CAAC,iBAAiB,CLblB,YKaA,CLbA,WK6BM,QAAS,KACT,eAAgB,OAChB,YAAa,OACb,gBAAiB,OACjB,MAAO,KACP,OAAQ,IANd,CAQI,CAvBH,iBAuBG,CLpCJ,WKoCI,CLUE,gBKVF,CLUE,WKRE,QAAS,IAPjB,CAaA,CAAC,iBACC,MAAO,KAXT,CClCA,CAAC,kBACC,MAAO,KACP,OAAQ,QACR,MAAO,UACP,WAAY,IACd,CCLA,CAAC,aXAD,OWCU,KAAK,EXDf,cWEiB,IACjB,CAHA,CAAC,aAAa,CAAd,MAII,QAAS,IAEb,CANA,CAAC,aAAa,CAAd,oBAMM,QAAS,KACT,IAAK,KACL,MAAO,IAGb,CAXA,CAAC,aAAa,CAAd,wBAUQ,QAAS,KACT,YAAa,MAIrB,CAfA,CAAC,aAAa,CAAd,ORSE,gBQTF,CAAC,aAAa,CAAd,qBAeM,YAAa,IAGnB,CAlBA,CAAC,aAAa,CAAd,kBXAA,OWsBY,IAAI,CAEhB,CAEA,CAAC,2BX1BD,OW2BU,IAAI,EX3Bd,cW4BiB,GAAjB,CAFA,CAAC,2BAA2B,CAA5B,uBAII,aAAc,GAClB,CALA,CAAC,2BAA2B,CA1B5B,MAiCI,QAAS,IACb,CARA,CAAC,2BAA2B,CA1B5B,MA0BA,eX1BA,OWmCc,IAEd,CAXA,CAAC,2BAA2B,CAA5B,iBAeI,CAfH,2BAeG,CAfJ,gBAeI,OAFA,iBAAkB,QAClB,aAAc,OAClB,CAfA,CAAC,2BAA2B,CAA5B,UAuBI,CAvBH,2BAuBG,CAvBJ,SAuBI,OAFA,iBAAkB,QAClB,aAAc,OAClB,CAvBA,CAAC,2BAA2B,CAA5B,cP1BA,uC,CO0BC,2B,CAAD,a,gCA+BM,MAAO,KACP,OAAQ,IAAd,CAhCA,CAAC,2BAA2B,CAA5B,cP1BA,0BO6DM,QAAS,cAAf,CAEI,CArCH,2BAqCG,CArCJ,YAqCI,CPjBE,gBOiBF,CPjBE,WOmBE,QAAS,IADjB,CAtCA,CAAC,2BAA2B,CAA5B,eA4CI,MAAO,KACP,OAAQ,IAHZ,CCpEA,CAAC,qBAAD,KAEI,cAAe,IAAnB,CAFA,CAAC,sBAAD,gBAKI,MAAO,KAAX,CCLA,CAAC,eACC,SAAU,MACV,MAAO,KACP,OAAQ,KACR,QAAS,GACT,QAAS,KACT,eAAgB,OAChB,gBAAiB,cACjB,WAAY,WACZ,MAAO,MACP,OAAQ,KAAK,KAAK,EAAE,OACpB,WAAY,MACZ,YAAa,cAAgB,CAAE,SAAS,CAAE,KAAK,CAAE,WACjD,WAAY,Kbbd,caciB,KACf,WAAY,EAAI,IAAI,KAAK,IAAI,UAC7B,WAAY,IAAK,WACnB,CAEA,CAnBC,cAmBc,CAAC,OACd,OAAQ,KACR,WAAY,OACZ,QAAS,CACX,CAEA,CAAC,gBACC,OAAQ,Ib1BV,Qa2BW,KAAK,EACd,WAAY,KACZ,iBAAkB,KAClB,gBAAiB,IACnB,CAEA,CAAC,eACC,WAAY,KACd,CACA,CAAC,iBACC,WAAY,IACd,CAEA,OAAO,CAAC,SAAS,EAAE,OACjB,CAzCD,eA0CG,MAAO,EACP,OAAQ,EACR,MAAO,KACP,OAAQ,KACR,WAAY,Kb9ChB,ca+CmB,CACjB,CACA,CAjDD,eAkDG,WAAY,IAAK,WACnB,CACA,CApDD,cAoDgB,CAjCD,OAkCZ,OAAQ,CACV,CACF,CAEA,CAAC,eAEC,QAAS,KACT,YAAa,OACb,WAAY,Kb7Dd,Oa8DU,EAAE,IACZ,CAEA,CAAC,WACC,QAAS,KACT,YAAa,OACb,gBAAiB,OACjB,MAAO,KACP,OAAQ,KACR,aAAc,KACd,WAAY,QbxEd,cayEiB,IACjB,CACA,CAVC,UAUW,CAAE,IACZ,MAAO,KACP,OAAQ,IACV,CAEA,CAAC,ObhFD,OaiFU,EAAE,EAAE,KAAK,IAYnB,CAbA,CAAC,OAEC,CAAC,QACC,UAAW,KACX,YAAa,MACb,OAAQ,OACV,CANF,CAAC,OAOC,CALC,OAKQ,CAAE,IACT,SAAU,SACV,IAAK,KACL,MAAO,KACP,OAAQ,IACV,CAGF,CAAC,IACC,cAAe,GACjB,CCjGA,CAAC,gBACC,SAAU,KACV,MAAO,KACP,WAAY,KAAK,KAAK,EAAE,MACxB,WAAY,WdJd,QcKW,IACX,CAEA,CAAC,0BACC,QAAS,KACT,eAAgB,IAChB,UAAW,IACb,CAEA,CAAC,gCACC,UAAW,KACX,MAAO,QACP,YAAa,IACb,UAAW,KdlBb,OcmBU,IACR,eAAgB,GAClB,CAEA,CAAC,uBdvBD,OcwBU,IACR,MAAO,KACP,YAAa,KACb,WAAY,OACZ,OAAQ,QACR,eAAgB,OAChB,UAAW,KACX,WAAY,UAAU,KAAK,SAC3B,iBAAkB,IACpB,CAEA,CAZC,sBAYsB,OACrB,UAAW,MAAM,IACnB,CCrCA,CAAC,UACC,SAAU,SACV,QAAS,KACT,WAAY,WACZ,WAAY,KfJd,QeKW,KACT,MAAO,KACP,WAAY,QACZ,uBAAwB,IACxB,wBAAyB,IACzB,WAAY,EAAE,IAAI,IAAI,KACxB,CAEA,CAAC,eACC,WAAY,OACZ,MAAO,KACP,OAAQ,KfhBV,QeiBW,KfjBX,cekBiB,GACjB,CAEA,CAAC,qBACC,KAAM,EAAN,EACA,WAAY,OfvBd,QewBW,KfxBX,ceyBiB,IACf,YAAa,IACf,CAEA,CAAC,wBACC,WAAY,OACZ,WAAY,WACZ,MAAO,KACP,OAAQ,KACR,aAAc,KflChB,cemCiB,IACf,OAAQ,OACV,CAEA,CAVC,uBAUuB,OACtB,WAAY,OACd,CAEA,CAdC,wBAcwB,IACvB,WAAY,WACZ,MAAO,KACP,OAAQ,Kf9CV,Qe+CW,IACX,CAEA,OAAO,CAAC,SAAS,EAAE,OACjB,CAnDD,UfAD,ceoDmB,CACjB,CACF,CCtDA,CAAC,YACC,SAAU,MACV,OAAQ,KACR,KAAM,KACN,QAAS,WACT,MAAO,KACP,OAAQ,KACR,iBAAkB,QAClB,kBAAmB,UACnB,oBAAqB,OhBTvB,cgBUiB,IACf,WAAY,KACZ,WAAY,WAAW,IAAK,WAC9B,CAEA,CAfC,WAeW,QACV,SAAU,SACV,QAAS,MACT,MAAO,KACP,OAAQ,KhBnBV,cgBoBiB,IACf,WAAY,WAAW,IAAK,YAC5B,QAAS,EACX,CAEA,CAzBC,YAyBY,CAAC,a,CAzBb,Y,gBA2BC,SAAU,MACV,OAAQ,KACR,KAAM,KACN,MAAO,KACP,OAAQ,KACR,WAAY,QAAQ,IAAM,WAAW,CAAE,UAAU,IAAM,WACzD,CAEA,CAnCC,YAmCY,C,eACX,MAAO,KACP,OAAQ,KhBrCV,QgBsCW,IACT,WAAY,QAAQ,IAAM,WAAW,CAAE,UAAU,IAAM,WACzD,CAEA,CA1CC,YA0CY,CAjBC,aAkBZ,WAAY,WhB3Cd,QgB4CW,KACT,QAAS,CACX,CAEA,CAAC,OACC,OAAQ,OACV,CAEA,CApDC,WAoDW,CAJX,OAImB,CA3BN,aA4BZ,UAAW,OAAO,QAClB,QAAS,CACX,CAEA,CAzDC,WAyDW,CATX,OASmB,C,eAClB,UAAW,OAAO,QAClB,QAAS,CACX,CAEA,CA9DC,WA8DW,CAdX,MAckB,QACjB,WAAY,EAAI,EAAI,MAAM,MAAM,SAClC,CAEA,CAlEC,WAkEW,OACV,WAAY,EAAE,EAAI,KAAK,MAAM,KAC/B,CAEA,CAAC,sBACC,SAAU,SACV,IAAK,KACL,KAAM,KACN,QAAS,KACT,eAAgB,OAChB,gBAAiB,OACjB,MAAO,KACP,OAAQ,KhB9EV,OgB+EU,KACR,MAAO,KACP,YAAa,IACb,UAAW,KACX,WAAY,OACZ,WAAY,QhBpFd,cgBqFiB,IACf,WAAY,KAAK,IAAI,IAAI,SAC3B,CCvFA,CAAC,WACC,QAAS,KACT,MAAO,MjBFT,OiBGU,KACR,eAAgB,IAClB,CAEA,CAAC,oBACC,QAAS,KACT,MAAO,KACP,cAAe,IACjB,CAEA,CANC,mBAMmB,CAAC,KACnB,gBAAiB,QACnB,CAEA,CAVC,mBAUmB,CAJC,KAIK,CAAC,mBACzB,QAAS,IACX,CAEA,CAJ2B,mBAKzB,WAAY,OACZ,UAAW,KACX,WAAY,KACZ,aAAc,KACd,iBAAkB,sIAClB,kBAAmB,UACnB,oBAAqB,OACrB,gBAAiB,KjB7BnB,ciB8BiB,GACjB,CAEA,CAAC,iBACC,SAAU,SACV,MAAO,KAAK,KAAK,EAAE,MjBnCrB,QiBoCW,KAAK,KACd,YAAa,IACb,UAAW,KACX,YAAa,IACb,YAAa,SACb,UAAW,WjBzCb,ciB0CiB,IACf,uBAAwB,oBAwB1B,CAlCA,CAAC,iBAWC,CAAC,IAAK,CAAE,IACN,SAAU,SACV,MAAO,IACP,OAAQ,IACR,MAAO,KACP,OAAQ,IACV,CAjBF,CAAC,iBAkBC,CAAC,KACC,SAAU,SACV,MAAO,IACP,OAAQ,MACR,MAAO,YAEP,MAAO,QAEP,UAAW,IACb,CA3BF,CAAC,iBA4BC,CAVC,IAUK,CAAE,CAAC,UACP,aAAc,IACd,MAAO,KACP,YAAa,IACb,UAAW,IACb,CAGF,CA9DC,mBA8DmB,CAxDC,KAwDK,CApCzB,iBAqCC,UAAW,KAAK,KAAK,EAAE,OACvB,MAAO,KACP,UAAW,WACX,iBAAkB,OACpB,CAEA,CArEC,mBAqEmB,CAAC,SAAS,CA3C7B,iBA4CC,aAAc,KACd,MAAO,QACP,iBAAkB,OACpB,CAEA,CAAC,kBACC,UAAW,IACb,CAEA,CAAC,iBACC,QAAS,KjBvFX,QiBwFW,KAAK,KACd,YAAa,IACb,UAAW,KACX,YAAa,IACb,gBAAiB,KACjB,WAAY,KACZ,OAAQ,MAAM,IAAI,QjB9FpB,ciB+FiB,IACf,OAAQ,OACV,CAEA,CAbC,iBAaiB,EjBnGlB,OiBoGU,EAAI,EAAI,EAAI,KACpB,MAAO,SACT,CAEA,CAlBC,iBAkBiB,CAAC,wBAAwB,OAAO,KAChD,KAAM,SACR,CAEA,OAAO,CAAC,SAAS,EAAE,OACjB,CA7GD,WA8GG,MAAO,GACT,CACF,CChHA,CAAC,gBACC,SAAU,SACV,MAAO,KACT,CAEA,CAAC,4BACC,SAAU,SACV,OAAQ,KACR,MAAO,MACP,MAAO,MACP,WAAY,MACZ,OAAQ,MACR,WAAY,EAAI,IAAI,KAAK,IAAI,UAC7B,WAAY,KlBbd,ckBciB,KACf,QAAS,KACT,WAAY,IAAK,YACjB,QAAS,ElBjBX,QkBkBW,EAAI,IAAI,IACjB,WAAY,UACd,CAEA,CAjBC,2BAiB2B,CLHZ,OKId,QAAS,EACT,WAAY,OACZ,OAAQ,IACV,CAEA,CAvBC,2BAuB2B,OAC1B,QAAS,GACT,MAAO,KACP,OAAQ,KACR,WAAY,KACZ,SAAU,SACV,QAAS,GACT,OAAQ,KACR,MAAO,KACP,UAAW,OAAO,OlBrCpB,ckBsCiB,GACjB,CAEA,CAAC,wBACC,MAAO,MACP,WAAY,WlB3Cd,OkB4CU,KACR,QAAS,MACT,aAAc,IAAI,IAAI,IAAI,IAC1B,MAAO,QACP,aAAc,KACd,OAAQ,KACR,UAAW,KACX,iBAAkB,8DAClB,gBAAiB,KAAK,KACtB,kBAAmB,UACnB,oBAAqB,EAAE,KACvB,QAAS,IACX,CAEA,CAjBC,uBAiBuB,cACtB,MAAO,OACT,CC5DA,CAAC,cACC,SAAU,SACV,OAAQ,EACR,QAAS,KACT,WAAY,MnBJd,OmBKU,EACR,iBAAkB,QAClB,2BAA4B,KAC5B,0BAA2B,KAC3B,WAAY,iBAAiB,IAAK,IAAI,CAAE,WAAW,IAAK,IAC1D,CAEA,CAAC,oBACC,OAAQ,EACR,WAAY,WACZ,MAAO,MACP,WAAY,MnBhBd,QmBiBW,KACT,SAAU,OACV,WAAY,OACZ,WAAY,KACZ,MAAO,QACP,YAAa,IACb,UAAW,KACX,YAAa,KACb,YAAa,SACb,UAAW,WACX,OAAQ,KACR,0BAA2B,KAC3B,QAAS,KACT,OAAQ,KACR,uBAAwB,WAC1B,CAEA,CAtBC,mBAsBmB,MAAM,QACxB,QAAS,MACT,MAAO,UACP,QAAS,KACT,QAAS,KAAK,YAChB,CAEA,CAAC,uBACC,SAAU,SACV,MAAO,KACP,QAAS,KACT,gBAAiB,SACjB,MAAO,MACP,OAAQ,IACV,CAEA,CAAC,sBACC,QAAS,KACT,eAAgB,OAChB,gBAAiB,OACjB,MAAO,KACP,OAAQ,IACV,CAEA,CAAC,aACC,QAAS,KACT,eAAgB,OAChB,gBAAiB,OACjB,MAAO,cAKT,CATA,CAAC,aAKC,IACE,MAAO,eACP,OAAQ,IACV,CAGF,CAnBC,sBAmBsB,O,CAXtB,aAaC,OAAQ,OACV,CAEA,CAjCC,uBAiCuB,KAAK,CAAC,WAC5B,QAAS,IACX,CAEA,CAAC,8BACC,QAAS,KACT,eAAgB,MAClB,CAEA,CAnFC,aAmFa,CAAC,OACb,iBAAkB,KAClB,WAAY,KACZ,WAAY,EAAI,KAAK,KAAS,SAChC,CAEA,CFemB,yB,0BEbjB,WAAY,OACZ,MAAO,KACP,OAAQ,KACR,QAAS,KACT,OAAQ,OACV,CAEA,CAxCC,aAwCa,K,CFMK,yB,oCEHjB,KAAM,SACR,CAEA,CA9CC,YA8CY,OAAO,K,CFAD,wB,iDEGjB,KAAM,OACR,CAEA,CAAC,kC,oEAGC,QAAS,KACT,eAAgB,OAChB,gBAAiB,OnBnHnB,OmBoHU,EnBpHV,QmBqHW,IACT,WAAY,KACZ,OAAQ,IACV,CAEA,C,mEAEE,eAAgB,GAClB,CAEA,CAjBC,iCAiBiC,OAChC,QAAS,IACX,CAEA,CAAC,0BACC,WAAY,OACZ,OAAQ,KACR,OAAQ,OACV,CAEA,CANC,0BAM0B,K,CAN1B,0B,OAQC,KAAM,SACR,CAEA,CAhCC,iCAgCiC,OAAO,CAXxC,0BAWmE,K,CAhCnE,iC,QAqBA,0B,QAAA,yB,CAhDc,O,MAgDd,yB,CAhDc,O,QAgDd,yB,aAAA,yB,cAiBC,KAAM,OACR,CZrJA,CAAC,6BAAD,iBAEI,MAAO,QACP,WAAY,gBAAgB,IAAhB,CAAsB,OAAtB,CAA+B,QAAQ,CAAnD,sCAEA,OAAQ,IAAI,MAAM,OADtB,CAJA,CAAC,6BAAD,iCASM,YAAa,WAFnB,CAPA,CAAC,6BAAD,2BAYM,QAAS,IAFf,CAVA,CAAC,6BAAD,2BAgBI,MAAO,KACP,OAAQ,IAHZ,CAII,CAlBH,6BAkBG,CAlBJ,yBAkBI,qBAlBH,6BAkBG,CAlBJ,yBAkBI,oCACE,OAAQ,OAFd,CAjBA,CAAC,6BAAD,mDAyBM,QAAS,KACT,eAAgB,IP1BtB,QO2Be,EAAE,GAFjB,CAzBA,CAAC,6BAAD,+EA6BQ,YAAa,IADrB,CA5BA,CAAC,6BAAD,kDAiCM,aAAc,IACd,UAAW,KACX,YAAa,IAFnB,CAOA,CAAC,6BAAD,CAxCA,kBPAA,QO2Ce,IAAI,aAPnB,CAYA,CAAC,4BAAD,mBPhDA,QOsDe,KAAK,IAAI,aAfxB,CASA,CAAC,4BAAD,qCAIQ,aAAc,IAVtB,CAiBA,CAAC,kBACC,OAAQ,IAfV,CAcA,CAAC,kBAAD,qBAGI,SAAU,SACV,IAAK,IACL,MAAO,KACP,OAAQ,IAdZ,CanDA,CAAC,qBAAqB,CAAtB,mBAEI,QAAS,IAAb,CAFA,CAAC,qBAAqB,CAAtB,yBAKI,WAAY,QAAhB,CALA,CAAC,qBAAqB,ChBAtB,YgBAA,ChBAA,uC,CgBAC,qB,ChBAD,Y,gCgBUM,MAAO,KACP,OAAQ,IADd,CAGI,CAbH,qBAaG,ChBbJ,WgBaI,ChBiBE,cgBjBF,ChBbJ,uCgBcM,MAAO,WADb,CAbA,CAAC,qBAAqB,ChBAtB,YgBAA,ChBAA,6BgBAA,ChBAA,yBgBAA,EhBAA,0BgBmBM,QAAS,cAHf,CAhBA,CAAC,qBAAqB,ChBAtB,YgBAA,ChBAA,WgBsBM,QAAS,KACT,eAAgB,OAChB,YAAa,OACb,gBAAiB,OACjB,MAAO,KACP,OAAQ,IAHd,CAKI,CA7BH,qBA6BG,ChB7BJ,WgB6BI,ChBiBE,gBgBjBF,ChBiBE,WgBfE,QAAS,IAJjB,CA3BA,CAAC,qBAAqB,CAAtB,yEhBAA,cgBsCQ,cAAe,CARvB,CA9BA,CAAC,qBAAqB,CAAtB,yEhBAA,OgBAA,ChBAA,sBgByCQ,OAAQ,EACR,QAAS,CARjB,CClCA,CAAC,mBAAmB,CAApB,uDrBAA,QqBGe,IAAI,CADnB,CAFA,CAAC,mBAAmB,CAApB,kIAMU,QAAS,IADnB", "sources": ["webpack://labwise-web/./src/pages/experimental-procedure/conclusion/index.less", "webpack://labwise-web/./src/pages/experimental-procedure/conclusion/sections/AnalysisRecord/index.less", "webpack://labwise-web/./src/components/MoleculeStructure/RemoteImg/index.less", "webpack://labwise-web/./src/components/MoleculeStructure/index.less", "webpack://labwise-web/./src/components/MaterialsTable/index.less", "webpack://labwise-web/./src/components/SectionTitle/index.less", "webpack://labwise-web/./src/components/ReagentList/index.less", "webpack://labwise-web/./src/components/ReactionTabs/index.less", "webpack://labwise-web/./src/components/ReactionTabs/ExperimentListTab/index.less", "webpack://labwise-web/./src/components/ReactionTabs/MyReactionDesignTab/index.less", "webpack://labwise-web/./src/components/StatusTip/index.less", "webpack://labwise-web/./src/components/ReactionTabs/ReactionLibTab/index.less", "webpack://labwise-web/./src/components/ReactionTabs/ReactionLibTab/Filter/index.less", "webpack://labwise-web/./src/components/Launcher/styles/chat-window.css", "webpack://labwise-web/./src/components/Launcher/styles/emojiPicker.css", "webpack://labwise-web/./src/components/Launcher/styles/header.css", "webpack://labwise-web/./src/components/Launcher/styles/launcher.css", "webpack://labwise-web/./src/components/Launcher/styles/message.css", "webpack://labwise-web/./src/components/Launcher/styles/popup-window.css", "webpack://labwise-web/./src/components/Launcher/styles/user-input.css", "webpack://labwise-web/./src/pages/reaction/component/index.less", "webpack://labwise-web/./src/pages/reaction/index.less"], "sourcesContent": [".experiment-conclusion-page-root {\n  .basic-info-wrapper {\n    margin: 12px;\n  }\n  .detail-title-wrapper {\n    > div > div {\n      position: relative;\n      display: flex;\n    }\n    .ant-affix {\n      background-color: white;\n    }\n    .anchor {\n      .ant-anchor {\n        padding: 8px 12px;\n      }\n    }\n    .action-buttons-wrapper {\n      margin-top: 0;\n      margin-left: auto;\n    }\n\n    .ant-anchor-wrapper-horizontal::before {\n      content: none;\n    }\n    .divider {\n      margin-left: 12px;\n    }\n  }\n\n  .section-wrapper {\n    margin: 36px 12px;\n    .section-title {\n      display: flex;\n      &::before {\n        position: absolute;\n        left: 0;\n        width: 4px;\n        height: 24px;\n        margin-top: 2px;\n        background-color: #027aff;\n        border-radius: 4px;\n        content: '';\n      }\n    }\n    .section-action {\n      margin-left: auto;\n    }\n  }\n\n  #operations {\n    --timeline-left-width: 150px;\n    .ant-timeline-item.ant-timeline-item-left {\n      .ant-timeline-item-label {\n        width: calc(var(--timeline-left-width) - 12px);\n      }\n      .ant-timeline-item-tail {\n        inset-inline-start: var(--timeline-left-width);\n      }\n      .ant-timeline-item-head {\n        inset-inline-start: var(--timeline-left-width);\n      }\n      .ant-timeline-item-content {\n        width: calc(100% - var(--timeline-left-width) - 28px);\n        inset-inline-start: calc(var(--timeline-left-width) - 4px);\n      }\n    }\n    .exception-actions-wrapper {\n      margin-left: auto;\n    }\n    .exception-view-wrapper {\n      &.full-width .ant-descriptions-row {\n        display: grid;\n        grid-template-columns: 1fr 1fr;\n      }\n    }\n  }\n\n  #result {\n    .note-wrapper .ant-descriptions-item-label {\n      align-items: center;\n    }\n    form {\n      margin-right: 12px;\n      background-color: transparent;\n    }\n    .result-actions-wrapper {\n      margin-left: auto;\n    }\n  }\n\n  #announce {\n    .announce-text {\n      input {\n        width: fit-content;\n        min-width: 120px;\n        padding: 0 4px;\n        text-align: center;\n        border-bottom: 1px solid;\n        border-radius: 0;\n      }\n    }\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", ".analysisTab {\n  :global {\n    .section-title {\n      display: flex;\n      justify-content: space-between;\n    }\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", ".images-wrapper {\n  display: flex;\n  flex-grow: 0;\n  flex-wrap: nowrap;\n  justify-content: center;\n\n  &.fit-content-height {\n    .img img {\n      height: fit-content;\n      max-height: -webkit-fill-available;\n      max-height: fill-available;\n    }\n  }\n\n  .img {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    img {\n      height: 100%;\n      padding: 1px;\n      object-fit: contain;\n      background: #ffffff00;\n    }\n  }\n\n  .icon {\n    align-self: center;\n    width: 24px;\n    height: 24px;\n    stroke: #000000;\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", ".molecule-structure-root {\n  position: relative;\n  display: flex;\n  flex: 0 0 auto;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  cursor: auto;\n\n  &:hover .buttons-wrapper {\n    display: flex;\n  }\n\n  .svg {\n    width: 100% !important;\n    height: 100% !important;\n  }\n\n  .skeleton-wrapper {\n    display: flex;\n    justify-content: center;\n    width: 100%;\n    .ant-skeleton-image {\n      width: 100%;\n      height: 100%;\n    }\n  }\n\n  .buttons-wrapper {\n    position: absolute;\n    top: 0;\n    right: 0;\n    z-index: 1;\n    display: none;\n    gap: 2px;\n    padding: 4px;\n    .button-wrapper {\n      position: relative;\n      display: flex;\n      width: 16px;\n      height: 16px;\n      cursor: pointer;\n\n      svg > path {\n        fill: #bfbfbf;\n      }\n      &:hover {\n        svg > path {\n          fill: #1a90ff;\n        }\n      }\n    }\n    .expand-button-wrapper {\n      svg > path:last-child {\n        stroke: #bfbfbf;\n      }\n      &:hover {\n        svg > path:last-child {\n          stroke: #1a90ff;\n        }\n      }\n    }\n  }\n}\n\n.expand {\n  position: absolute;\n  top: 5px;\n  left: 5px;\n}\n\n.expand:hover {\n  cursor: pointer !important;\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", ".filter-form-root {\n  .expand-btn-wrapper {\n    margin-bottom: 12px;\n  }\n  .confirm-col {\n    :global {\n      .ant-form-item {\n        margin-bottom: 4px;\n      }\n      .ant-row.ant-form-item-row {\n        flex-direction: row;\n        .ant-col.ant-form-item-label {\n          display: flex;\n          padding: 0;\n          padding-right: 16px;\n        }\n        .ant-col.ant-form-item-control {\n          width: auto;\n        }\n      }\n    }\n  }\n\n  :global {\n    .smiles-list {\n      .ant-upload-list-picture-card-container,\n      .ant-upload-select-picture-card {\n        width: 60px;\n        height: 60px;\n      }\n      &.reaction-list .ant-upload-list-picture-card-container {\n        width: fit-content;\n      }\n      .ant-upload-list-picture-card\n        .ant-upload-list-item-file\n        + .ant-upload-list-item-name {\n        display: none !important;\n      }\n      .add-button {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        width: 100%;\n        height: 100%;\n      }\n      &.hide-upload-btn {\n        .ant-upload {\n          display: none;\n        }\n      }\n    }\n  }\n\n  .re-retro-btn {\n    margin-left: auto;\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", ".sectionTitle {\n  position: relative;\n  display: flex;\n  width: 100%;\n  .extraCom {\n    position: absolute;\n    top: 0px;\n    right: 0px;\n  }\n}\n.sectionTitle h2 {\n  position: relative;\n  height: 40px;\n  padding-left: 10px;\n  font-weight: 500;\n  font-size: 20px;\n  line-height: 40px;\n  text-align: left;\n}\n.sectionTitle h2:before {\n  position: absolute;\n  top: 50%;\n  left: 0;\n  width: 4px;\n  height: 20px;\n  margin-top: -10px;\n  background-color: #6691d6;\n  -webkit-border-radius: 3px;\n  -moz-border-radius: 3px;\n  border-radius: 3px;\n  content: '';\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", "@import '@/style/variables.less';\n.structure {\n  width: 100%;\n  max-width: 480px;\n  height: auto;\n  margin-bottom: 22px;\n  border: 1px solid @border-color-base;\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", ".retroReactionRowRoot {\n  .riskTag {\n    color: #b41500;\n    background: linear-gradient(0deg, #f7e8e5, #f7e8e5),\n      linear-gradient(0deg, #e8b9b2, #e8b9b2);\n    border: 1px solid #e8b9b2;\n  }\n  :global {\n    .ant-pro-card .ant-pro-card-body {\n      padding-top: 0 !important; // for override importent style\n    }\n    .ant-pro-checkcard-content {\n      display: none;\n    }\n  }\n  .retroReactionRoot {\n    width: 100%;\n    cursor: auto;\n    &.selectable {\n      cursor: pointer;\n      .smiles {\n        cursor: pointer;\n      }\n    }\n    .actionsWrapper {\n      display: flex;\n      flex-direction: row;\n      padding: 0 6px;\n      .rightActionWrapper {\n        margin-left: auto;\n      }\n    }\n    .sourceWrapper {\n      padding-left: 4px;\n      font-size: 12px;\n      line-height: 14px;\n    }\n  }\n}\n\n.retroReactionTabRoot {\n  :global {\n    .ant-pro-card-body {\n      padding: 8px 4px !important;\n    }\n  }\n}\n\n.materialsPriceTable {\n  :global {\n    .ant-modal-content {\n      .ant-modal-header {\n        padding-left: 15px;\n      }\n      padding: 20px 5px 2px !important;\n    }\n  }\n}\n\n.viwePrice {\n  height: 24px;\n  .warningIcon {\n    position: relative;\n    top: 4px;\n    right: -6px;\n    height: 24px;\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", "@import '@/style/variables.less';\n.experiment-list-root {\n  .ant-pro-table-list-toolbar {\n    display: none;\n  }\n}\n\n.smiles-list {\n  .ant-upload-list-picture-card-container,\n  .ant-upload-select-picture-card {\n    width: 60px;\n    height: 60px;\n  }\n  &.reaction-list .ant-upload-list-picture-card-container {\n    width: fit-content;\n  }\n  .ant-upload-list-picture-card\n    .ant-upload-list-item-file\n    + .ant-upload-list-item-name {\n    display: none !important; // FIXME\n  }\n  .add-button {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    width: 100%;\n    height: 100%;\n  }\n  &.hide-upload-btn {\n    .ant-upload {\n      display: none;\n    }\n  }\n}\n\n.workbench_fold {\n  width: calc(\n    100vw - @fold-menu-width - @workbench-fold-summary-width - 50px\n  ) !important;\n}\n\n.workbench_unFold {\n  width: calc(\n    100vw - @siderWidth - @workbench-unfold-summary-width - 50px\n  ) !important;\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", ".my-reaction-design-card-root {\n  .actions-wrapper {\n    margin-left: auto;\n  }\n  .divider-wrapper {\n    display: flex;\n    justify-content: center;\n    .divider {\n      height: 100%;\n    }\n  }\n}\n\n.create-form-root {\n  .smiles-list {\n    .ant-upload-list-picture-card-container,\n    .ant-upload-select-picture-card {\n      width: 60px;\n      height: 60px;\n    }\n    &.reaction-list .ant-upload-list-picture-card-container {\n      width: fit-content;\n    }\n    .ant-upload-list-picture-card\n      .ant-upload-list-item-file\n      + .ant-upload-list-item-name {\n      display: none !important; // FIXME\n    }\n    .add-button {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      width: 100%;\n      height: 100%;\n    }\n    &.hide-upload-btn {\n      .ant-upload {\n        display: none;\n      }\n    }\n  }\n}\n\n.procedurePopover {\n  width: 350px;\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", ".statusTip {\n  width: 100%;\n  height: inherit;\n  color: rgba(0, 0, 0, 0.52);\n  background: #fff;\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", ".ai-card-root {\n  margin: 12px 0;\n  border-radius: 12px;\n  .title {\n    display: flex;\n    .info-wrapper {\n      display: flex;\n      gap: 20px;\n      width: 100%;\n      div {\n        display: flex;\n        align-items: center;\n      }\n    }\n    .buttons-wrapper {\n      margin-left: auto;\n    }\n    .reference-btn {\n      margin-left: auto;\n    }\n  }\n  .procedure-wrapper {\n    margin: 8px 0;\n  }\n}\n\n.procedure-detail-card-root {\n  margin: 8px 0;\n  border-radius: 8px;\n  .reference-type-wrapper {\n    padding-left: 8px;\n  }\n  .title {\n    display: flex;\n    .yield-wrapper {\n      margin: auto;\n    }\n  }\n  .cancel-apply-btn {\n    background-color: #52c41a;\n    border-color: #52c41a;\n    &:hover {\n      background-color: #52c41a;\n      border-color: #52c41a;\n    }\n  }\n  .apply-btn {\n    background-color: #faad14;\n    border-color: #faad14;\n    &:hover {\n      background-color: #faad14;\n      border-color: #faad14;\n    }\n  }\n  .smiles-input {\n    .ant-upload-list-picture-card-container,\n    .ant-upload-select-picture-card {\n      width: 60px;\n      height: 60px;\n    }\n    .ant-upload-list-item-name {\n      display: none !important; // FIXME\n    }\n    &.hide-upload-btn {\n      .ant-upload {\n        display: none;\n      }\n    }\n  }\n  .smiles-display {\n    width: 60px;\n    height: 60px;\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", ".fieldsWrapper {\n  > div {\n    margin-bottom: 10px;\n  }\n  .slider {\n    width: 200px;\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", ".sc-chat-window {\n  position: fixed;\n  right: 25px;\n  bottom: 38px; /* 100px */\n  z-index: 10;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  box-sizing: border-box;\n  width: 370px;\n  height: calc(100% - 120px);\n  max-height: 590px;\n  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;\n  background: white;\n  border-radius: 10px;\n  box-shadow: 0px 7px 40px 2px rgba(148, 149, 150, 0.3);\n  transition: 0.3s ease-in-out;\n}\n\n.sc-chat-window.closed {\n  bottom: 90px;\n  visibility: hidden;\n  opacity: 0;\n}\n\n.sc-message-list {\n  height: 80%;\n  padding: 20px 0px;\n  overflow-y: auto;\n  background-color: white;\n  background-size: 100%;\n}\n\n.sc-message--me {\n  text-align: right;\n}\n.sc-message--them {\n  text-align: left;\n}\n\n@media (max-width: 450px) {\n  .sc-chat-window {\n    right: 0px;\n    bottom: 0px;\n    width: 100%;\n    height: 100%;\n    max-height: 100%;\n    border-radius: 0px;\n  }\n  .sc-chat-window {\n    transition: 0.1s ease-in-out;\n  }\n  .sc-chat-window.closed {\n    bottom: 0px;\n  }\n}\n\n.chat-shorthand {\n  display: flex;\n  display: flex;\n  align-items: center;\n  min-height: 65px;\n  margin: 0 35px;\n}\n\n.chat-robot {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 30px;\n  height: 30px;\n  margin-right: 15px;\n  background: #4e8cff;\n  border-radius: 15px;\n}\n.chat-robot > img {\n  width: 20px;\n  height: auto;\n}\n\n.option {\n  margin: 0 0 10px 35px;\n  .ant-tag {\n    font-size: 16px;\n    line-height: unset;\n    cursor: pointer;\n  }\n  .ant-tag > img {\n    position: relative;\n    top: -1px;\n    width: 14px;\n    height: auto;\n  }\n}\n\n.tag {\n  margin-bottom: 5px;\n}\n", ".sc-emoji-picker {\n  overflow: auto;\n  width: 100%;\n  max-height: calc(100% - 40px);\n  box-sizing: border-box;\n  padding: 15px;\n}\n\n.sc-emoji-picker--category {\n  display: flex;\n  flex-direction: row;\n  flex-wrap: wrap;\n}\n\n.sc-emoji-picker--category-title {\n  min-width: 100%;\n  color: #b8c3ca;\n  font-weight: 200;\n  font-size: 13px;\n  margin: 5px;\n  letter-spacing: 1px;\n}\n\n.sc-emoji-picker--emoji {\n  margin: 5px;\n  width: 30px;\n  line-height: 30px;\n  text-align: center;\n  cursor: pointer;\n  vertical-align: middle;\n  font-size: 28px;\n  transition: transform 60ms ease-out,-webkit-transform 60ms ease-out;\n  transition-delay: 60ms;\n}\n\n.sc-emoji-picker--emoji:hover {\n  transform: scale(1.4);\n}", ".sc-header {\n  position: relative;\n  display: flex;\n  box-sizing: border-box;\n  min-height: 75px;\n  padding: 10px;\n  color: white;\n  background: #4e8cff;\n  border-top-left-radius: 9px;\n  border-top-right-radius: 9px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);\n}\n\n.sc-header--img {\n  align-self: center;\n  width: 54px;\n  height: 54px;\n  padding: 10px;\n  border-radius: 50%;\n}\n\n.sc-header--team-name {\n  flex: 1;\n  align-self: center;\n  padding: 10px;\n  border-radius: 5px;\n  user-select: none;\n}\n\n.sc-header--close-button {\n  align-self: center;\n  box-sizing: border-box;\n  width: 40px;\n  height: 40px;\n  margin-right: 10px;\n  border-radius: 5px;\n  cursor: pointer;\n}\n\n.sc-header--close-button:hover {\n  background: #4882ed;\n}\n\n.sc-header--close-button img {\n  box-sizing: border-box;\n  width: 100%;\n  height: 100%;\n  padding: 13px;\n}\n\n@media (max-width: 450px) {\n  .sc-header {\n    border-radius: 0px;\n  }\n}\n", ".sc-launcher {\n  position: fixed;\n  bottom: 90px;\n  left: 10px;\n  z-index: 2147483647;\n  width: 60px;\n  height: 60px;\n  background-color: #4e8cff;\n  background-repeat: no-repeat;\n  background-position: center;\n  border-radius: 50%;\n  box-shadow: none;\n  transition: box-shadow 0.2s ease-in-out;\n}\n\n.sc-launcher:before {\n  position: relative;\n  display: block;\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  transition: box-shadow 0.2s ease-in-out;\n  content: '';\n}\n\n.sc-launcher .sc-open-icon,\n.sc-launcher .sc-closed-icon {\n  position: fixed;\n  bottom: 90px;\n  left: 10px;\n  width: 60px;\n  height: 60px;\n  transition: opacity 100ms ease-in-out, transform 100ms ease-in-out;\n}\n\n.sc-launcher .sc-closed-icon {\n  width: 60px;\n  height: 60px;\n  padding: 8px;\n  transition: opacity 100ms ease-in-out, transform 100ms ease-in-out;\n}\n\n.sc-launcher .sc-open-icon {\n  box-sizing: border-box;\n  padding: 20px;\n  opacity: 0;\n}\n\n.opened {\n  cursor: pointer;\n}\n\n.sc-launcher.opened .sc-open-icon {\n  transform: rotate(-90deg);\n  opacity: 1;\n}\n\n.sc-launcher.opened .sc-closed-icon {\n  transform: rotate(-90deg);\n  opacity: 0;\n}\n\n.sc-launcher.opened:before {\n  box-shadow: 0px 0px 400px 250px rgba(148, 149, 150, 0.2);\n}\n\n.sc-launcher:hover {\n  box-shadow: 0 0px 27px 1.5px rgba(0, 0, 0, 0.2);\n}\n\n.sc-new-messages-count {\n  position: absolute;\n  top: -3px;\n  left: 41px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 22px;\n  height: 22px;\n  margin: auto;\n  color: white;\n  font-weight: 500;\n  font-size: 12px;\n  text-align: center;\n  background: #ff4646;\n  border-radius: 50%;\n  box-shadow: -1px 1px 2px rgba(0, 0, 0, 0.3);\n}\n", ".sc-message {\n  display: flex;\n  width: 300px;\n  margin: auto;\n  padding-bottom: 10px;\n}\n\n.sc-message--content {\n  display: flex;\n  width: 100%;\n  margin-bottom: 12px;\n}\n\n.sc-message--content.sent {\n  justify-content: flex-end;\n}\n\n.sc-message--content.sent .sc-message--avatar {\n  display: none;\n}\n\n.sc-message--avatar {\n  align-self: center;\n  min-width: 30px;\n  min-height: 30px;\n  margin-right: 15px;\n  background-image: url(https://d13yacurqjgara.cloudfront.net/assets/avatar-default-aa2eab7684294781f93bc99ad394a0eb3249c5768c21390163c9f55ea8ef83a4.gif);\n  background-repeat: no-repeat;\n  background-position: center;\n  background-size: 100%;\n  border-radius: 50%;\n}\n\n.sc-message--text {\n  position: relative;\n  width: calc(100% - 90px);\n  padding: 17px 20px;\n  font-weight: 300;\n  font-size: 14px;\n  line-height: 1.4;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  border-radius: 6px;\n  -webkit-font-smoothing: subpixel-antialiased;\n  .mood > img {\n    position: absolute;\n    right: 5px;\n    bottom: 8px;\n    width: 15px;\n    height: 15px;\n  }\n  .time {\n    position: absolute;\n    right: 5px;\n    bottom: -16px;\n    width: max-content;\n    /* color: #4e8cff; */\n    color: #595859;\n    /* color: #7d7d7f; */\n    font-size: 10px;\n  }\n  .time > .commentor {\n    margin-right: 6px;\n    color: black;\n    font-weight: bold;\n    font-size: 12px;\n  }\n}\n\n.sc-message--content.sent .sc-message--text {\n  max-width: calc(100% - 120px);\n  color: white;\n  word-wrap: break-word;\n  background-color: #4e8cff;\n}\n\n.sc-message--content.received .sc-message--text {\n  margin-right: 40px;\n  color: #263238;\n  background-color: #f4f7f9;\n}\n\n.sc-message--emoji {\n  font-size: 40px;\n}\n\n.sc-message--file {\n  display: flex;\n  padding: 15px 20px;\n  font-weight: 300;\n  font-size: 14px;\n  line-height: 1.4;\n  text-decoration: none;\n  background: white;\n  border: solid 1px #cccdd1;\n  border-radius: 5px;\n  cursor: pointer;\n}\n\n.sc-message--file p {\n  margin: 0px 0px 0px 10px;\n  color: rgba(86, 88, 103, 0.6);\n}\n\n.sc-message--file .sc-user-input--file-icon:hover path {\n  fill: rgba(86, 88, 103, 0.3);\n}\n\n@media (max-width: 450px) {\n  .sc-message {\n    width: 80%;\n  }\n}\n", ".sc-popup-window {\n  position: relative;\n  width: 150px;\n}\n\n.sc-popup-window--cointainer {\n  position: absolute;\n  bottom: 20px;\n  right: 100px;\n  width: 330px;\n  max-height: 260px;\n  height: 260px;\n  box-shadow: 0px 7px 40px 2px rgba(148, 149, 150, 0.3);\n  background: white;\n  border-radius: 10px;\n  outline: none;\n  transition: 0.2s ease-in-out;\n  z-index: 1;\n  padding: 0px 5px 5px 5px;\n  box-sizing: border-box;\n}\n\n.sc-popup-window--cointainer.closed {\n  opacity: 0;\n  visibility: hidden;\n  bottom: 14px;\n}\n\n.sc-popup-window--cointainer:after {\n  content: \"\";\n  width: 14px;\n  height: 14px;\n  background: white;\n  position: absolute;\n  z-index: -1;\n  bottom: -6px;\n  right: 28px;\n  transform: rotate(45deg);\n  border-radius: 2px;\n}\n\n.sc-popup-window--search {\n  width: 290px;\n  box-sizing: border-box;\n  margin: auto;\n  display: block;\n  border-width: 0px 0px 1px 0px;\n  color: #565867;\n  padding-left: 25px;\n  height: 40px;\n  font-size: 14px;\n  background-image: url(https://js.intercomcdn.com/images/<EMAIL>);\n  background-size: 16px 16px;\n  background-repeat: no-repeat;\n  background-position: 0 12px;\n  outline: none;\n}\n\n.sc-popup-window--search::placeholder {\n  color: #C1C7CD;\n}", ".sc-user-input {\n  position: relative;\n  bottom: 0;\n  display: flex;\n  max-height: 150px;\n  margin: 0px;\n  background-color: #f4f7f9;\n  border-bottom-right-radius: 10px;\n  border-bottom-left-radius: 10px;\n  transition: background-color 0.2s ease, box-shadow 0.2s ease;\n}\n\n.sc-user-input--text {\n  bottom: 0;\n  box-sizing: border-box;\n  width: 300px;\n  max-height: 200px;\n  padding: 18px;\n  overflow: scroll;\n  overflow-x: hidden;\n  overflow-y: auto;\n  color: #565867;\n  font-weight: 400;\n  font-size: 15px;\n  line-height: 1.33;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n  border: none;\n  border-bottom-left-radius: 10px;\n  outline: none;\n  resize: none;\n  -webkit-font-smoothing: antialiased;\n}\n\n.sc-user-input--text:empty:before {\n  display: block; /* For Firefox */\n  color: rgba(86, 88, 103, 0.3);\n  outline: none;\n  content: attr(placeholder);\n}\n\n.sc-user-input--buttons {\n  position: absolute;\n  right: 10px;\n  display: flex;\n  justify-content: flex-end;\n  width: 100px;\n  height: 100%;\n}\n\n.sc-user-input--button {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 30px;\n  height: 55px;\n}\n\n.actionButton {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 20px !important;\n  svg {\n    width: 20px !important;\n    height: auto;\n  }\n}\n\n.sc-user-input--button button,\n.actionButton {\n  cursor: pointer;\n}\n\n.sc-user-input--buttons input[type='file'] {\n  display: none;\n}\n\n.sc-user-input--picker-wrapper {\n  display: flex;\n  flex-direction: column;\n}\n\n.sc-user-input.active {\n  background-color: white;\n  box-shadow: none;\n  box-shadow: 0px -5px 20px 0px rgba(150, 165, 190, 0.2);\n}\n\n.sc-user-input--file-icon,\n.sc-user-input--send-icon {\n  align-self: center;\n  width: 20px;\n  height: 20px;\n  outline: none;\n  cursor: pointer;\n}\n\n.actionButton path,\n.sc-user-input--file-icon path,\n.sc-user-input--send-icon path {\n  fill: rgba(86, 88, 103, 0.3);\n}\n\n.actionButton:hover path,\n.sc-user-input--file-icon:hover path,\n.sc-user-input--send-icon:hover path {\n  fill: rgba(86, 88, 103, 1);\n}\n\n.sc-user-input--emoji-icon-wrapper,\n.sc-user-input--send-icon-wrapper,\n.sc-user-input--file-icon-wrapper {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  margin: 0px;\n  padding: 2px;\n  background: none;\n  border: none;\n}\n\n.sc-user-input--send-icon-wrapper,\n.sc-user-input--file-icon-wrapper {\n  flex-direction: row;\n}\n\n.sc-user-input--emoji-icon-wrapper:focus {\n  outline: none;\n}\n\n.sc-user-input--emoji-icon {\n  align-self: center;\n  height: 18px;\n  cursor: pointer;\n}\n\n.sc-user-input--emoji-icon path,\n.sc-user-input--emoji-icon circle {\n  fill: rgba(86, 88, 103, 0.3);\n}\n\n.sc-user-input--emoji-icon-wrapper:focus .sc-user-input--emoji-icon path,\n.sc-user-input--emoji-icon-wrapper:focus .sc-user-input--emoji-icon circle,\n.sc-user-input--emoji-icon.active path,\n.sc-user-input--emoji-icon.active circle,\n.sc-user-input--emoji-icon:hover path,\n.sc-user-input--emoji-icon:hover circle {\n  fill: rgba(86, 88, 103, 1);\n}\n", ".reaction-dialog-root {\n  .product-equivalent {\n    display: none;\n  }\n  .ant-pro-form-list-action {\n    align-self: baseline;\n  }\n  .smiles-list {\n    .ant-upload-list-picture-card-container,\n    .ant-upload-select-picture-card {\n      width: 60px;\n      height: 60px;\n    }\n    &.reaction-list .ant-upload-list-picture-card-container {\n      width: fit-content;\n    }\n    .ant-upload-list-picture-card\n      .ant-upload-list-item-file\n      + .ant-upload-list-item-name {\n      display: none !important; // FIXME\n    }\n    .add-button {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      width: 100%;\n      height: 100%;\n    }\n    &.hide-upload-btn {\n      .ant-upload {\n        display: none;\n      }\n    }\n  }\n  .ant-form-item-control-input-content {\n    .ant-pro-form-list-item:first-child {\n      .ant-form-item {\n        margin-bottom: 0;\n      }\n      .ant-col.ant-form-item-control {\n        height: 0;\n        opacity: 0;\n      }\n    }\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;", ".reaction-page-root {\n  .route-table-root {\n    .ant-pro-table-list-toolbar-container {\n      padding: 8px 0;\n      .ant-pro-table-list-toolbar-right {\n        .ant-pro-table-list-toolbar-setting-item {\n          display: none;\n        }\n      }\n    }\n  }\n}\n\n@blue-base: #1890ff;@blue-1: #e6f7ff;@blue-2: #bae7ff;@blue-3: #91d5ff;@blue-4: #69c0ff;@blue-5: #40a9ff;@blue-6: #1890ff;@blue-7: #096dd9;@blue-8: #0050b3;@blue-9: #003a8c;@blue-10: #002766;@purple-base: #722ed1;@purple-1: #f9f0ff;@purple-2: #efdbff;@purple-3: #d3adf7;@purple-4: #b37feb;@purple-5: #9254de;@purple-6: #722ed1;@purple-7: #531dab;@purple-8: #391085;@purple-9: #22075e;@purple-10: #120338;@cyan-base: #13c2c2;@cyan-1: #e6fffb;@cyan-2: #b5f5ec;@cyan-3: #87e8de;@cyan-4: #5cdbd3;@cyan-5: #36cfc9;@cyan-6: #13c2c2;@cyan-7: #08979c;@cyan-8: #006d75;@cyan-9: #00474f;@cyan-10: #002329;@green-base: #52c41a;@green-1: #f6ffed;@green-2: #d9f7be;@green-3: #b7eb8f;@green-4: #95de64;@green-5: #73d13d;@green-6: #52c41a;@green-7: #389e0d;@green-8: #237804;@green-9: #135200;@green-10: #092b00;@magenta-base: #eb2f96;@magenta-1: #fff0f6;@magenta-2: #ffd6e7;@magenta-3: #ffadd2;@magenta-4: #ff85c0;@magenta-5: #f759ab;@magenta-6: #eb2f96;@magenta-7: #c41d7f;@magenta-8: #9e1068;@magenta-9: #780650;@magenta-10: #520339;@pink-base: #eb2f96;@pink-1: #fff0f6;@pink-2: #ffd6e7;@pink-3: #ffadd2;@pink-4: #ff85c0;@pink-5: #f759ab;@pink-6: #eb2f96;@pink-7: #c41d7f;@pink-8: #9e1068;@pink-9: #780650;@pink-10: #520339;@red-base: #f5222d;@red-1: #fff1f0;@red-2: #ffccc7;@red-3: #ffa39e;@red-4: #ff7875;@red-5: #ff4d4f;@red-6: #f5222d;@red-7: #cf1322;@red-8: #a8071a;@red-9: #820014;@red-10: #5c0011;@orange-base: #fa8c16;@orange-1: #fff7e6;@orange-2: #ffe7ba;@orange-3: #ffd591;@orange-4: #ffc069;@orange-5: #ffa940;@orange-6: #fa8c16;@orange-7: #d46b08;@orange-8: #ad4e00;@orange-9: #873800;@orange-10: #612500;@yellow-base: #fadb14;@yellow-1: #feffe6;@yellow-2: #ffffb8;@yellow-3: #fffb8f;@yellow-4: #fff566;@yellow-5: #ffec3d;@yellow-6: #fadb14;@yellow-7: #d4b106;@yellow-8: #ad8b00;@yellow-9: #876800;@yellow-10: #614700;@volcano-base: #fa541c;@volcano-1: #fff2e8;@volcano-2: #ffd8bf;@volcano-3: #ffbb96;@volcano-4: #ff9c6e;@volcano-5: #ff7a45;@volcano-6: #fa541c;@volcano-7: #d4380d;@volcano-8: #ad2102;@volcano-9: #871400;@volcano-10: #610b00;@geekblue-base: #2f54eb;@geekblue-1: #f0f5ff;@geekblue-2: #d6e4ff;@geekblue-3: #adc6ff;@geekblue-4: #85a5ff;@geekblue-5: #597ef7;@geekblue-6: #2f54eb;@geekblue-7: #1d39c4;@geekblue-8: #10239e;@geekblue-9: #061178;@geekblue-10: #030852;@lime-base: #a0d911;@lime-1: #fcffe6;@lime-2: #f4ffb8;@lime-3: #eaff8f;@lime-4: #d3f261;@lime-5: #bae637;@lime-6: #a0d911;@lime-7: #7cb305;@lime-8: #5b8c00;@lime-9: #3f6600;@lime-10: #254000;@gold-base: #faad14;@gold-1: #fffbe6;@gold-2: #fff1b8;@gold-3: #ffe58f;@gold-4: #ffd666;@gold-5: #ffc53d;@gold-6: #faad14;@gold-7: #d48806;@gold-8: #ad6800;@gold-9: #874d00;@gold-10: #613400;@preset-colors: pink, magenta, red, volcano, orange, yellow, gold, cyan, lime, green, blue, geekblue,;@theme: default;@ant-prefix: ant;@html-selector: html;@primary-color: #1890ff;@primary-color-hover: #40a9ff;@primary-color-active: #096dd9;@primary-color-outline: rgba(24, 144, 255, 0.2);@processing-color: #1890ff;@info-color: #1890ff;@info-color-deprecated-bg: #e6f7ff;@info-color-deprecated-border: #91d5ff;@success-color: #52c41a;@success-color-hover: #73d13d;@success-color-active: #389e0d;@success-color-outline: rgba(82, 196, 26, 0.2);@success-color-deprecated-bg: #f6ffed;@success-color-deprecated-border: #b7eb8f;@warning-color: #faad14;@warning-color-hover: #ffc53d;@warning-color-active: #d48806;@warning-color-outline: rgba(250, 173, 20, 0.2);@warning-color-deprecated-bg: #fffbe6;@warning-color-deprecated-border: #ffe58f;@error-color: #ff4d4f;@error-color-hover: #ff7875;@error-color-active: #d9363e;@error-color-outline: rgba(255, 77, 79, 0.2);@error-color-deprecated-bg: #fff2f0;@error-color-deprecated-border: #ffccc7;@highlight-color: #ff4d4f;@normal-color: #d9d9d9;@white: #fff;@black: #000;@primary-1: #e6f7ff;@primary-2: #bae7ff;@primary-3: #91d5ff;@primary-4: #69c0ff;@primary-5: #40a9ff;@primary-6: #1890ff;@primary-7: #096dd9;@primary-8: #0050b3;@primary-9: #003a8c;@primary-10: #002766;@component-background: #fff;@popover-background: #fff;@popover-customize-border-color: #f0f0f0;@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@code-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;@text-color: rgba(0, 0, 0, 0.85);@text-color-secondary: rgba(0, 0, 0, 0.45);@text-color-inverse: #fff;@icon-color: inherit;@icon-color-hover: rgba(0, 0, 0, 0.75);@heading-color: rgba(0, 0, 0, 0.85);@text-color-dark: rgba(255, 255, 255, 0.85);@text-color-secondary-dark: rgba(255, 255, 255, 0.65);@text-selection-bg: #1890ff;@font-variant-base: tabular-nums;@font-feature-settings-base: tnum;@font-size-base: 14px;@font-size-lg: 16px;@font-size-sm: 12px;@heading-1-size: 38px;@heading-2-size: 30px;@heading-3-size: 24px;@heading-4-size: 20px;@heading-5-size: 16px;@line-height-base: 1.5715;@border-radius-base: 2px;@border-radius-sm: 2px;@control-border-radius: 2px;@arrow-border-radius: 2px;@padding-lg: 24px;@padding-md: 16px;@padding-sm: 12px;@padding-xs: 8px;@padding-xss: 4px;@control-padding-horizontal: 12px;@control-padding-horizontal-sm: 8px;@margin-lg: 24px;@margin-md: 16px;@margin-sm: 12px;@margin-xs: 8px;@margin-xss: 4px;@height-base: 32px;@height-lg: 40px;@height-sm: 24px;@item-active-bg: #e6f7ff;@item-hover-bg: #f5f5f5;@iconfont-css-prefix: anticon;@link-color: #1890ff;@link-hover-color: #40a9ff;@link-active-color: #096dd9;@link-decoration: none;@link-hover-decoration: none;@link-focus-decoration: none;@link-focus-outline: 0;@ease-base-out: cubic-bezier(0.7, 0.3, 0.1, 1);@ease-base-in: cubic-bezier(0.9, 0, 0.3, 0.7);@ease-out: cubic-bezier(0.215, 0.61, 0.355, 1);@ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);@ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);@ease-out-back: cubic-bezier(0.12, 0.4, 0.29, 1.46);@ease-in-back: cubic-bezier(0.71, -0.46, 0.88, 0.6);@ease-in-out-back: cubic-bezier(0.71, -0.46, 0.29, 1.46);@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);@ease-in-circ: cubic-bezier(0.6, 0.04, 0.98, 0.34);@ease-in-out-circ: cubic-bezier(0.78, 0.14, 0.15, 0.86);@ease-out-quint: cubic-bezier(0.23, 1, 0.32, 1);@ease-in-quint: cubic-bezier(0.755, 0.05, 0.855, 0.06);@ease-in-out-quint: cubic-bezier(0.86, 0, 0.07, 1);@border-color-base: #d9d9d9;@border-color-split: #f0f0f0;@border-color-inverse: #fff;@border-width-base: 1px;@border-style-base: solid;@outline-blur-size: 0;@outline-width: 2px;@outline-color: #1890ff;@outline-fade: 20%;@background-color-light: #fafafa;@background-color-base: #f5f5f5;@disabled-color: rgba(0, 0, 0, 0.25);@disabled-bg: #f5f5f5;@disabled-active-bg: #e6e6e6;@disabled-color-dark: rgba(255, 255, 255, 0.35);@shadow-color: rgba(0, 0, 0, 0.15);@shadow-color-inverse: #fff;@box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@shadow-1-up: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-down: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-left: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-1-right: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);@shadow-2: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);@btn-font-weight: 400;@btn-border-radius-base: 2px;@btn-border-radius-sm: 2px;@btn-border-width: 1px;@btn-border-style: solid;@btn-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);@btn-primary-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);@btn-text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);@btn-primary-color: #fff;@btn-primary-bg: #1890ff;@btn-default-color: rgba(0, 0, 0, 0.85);@btn-default-bg: #fff;@btn-default-border: #d9d9d9;@btn-danger-color: #fff;@btn-danger-bg: #ff4d4f;@btn-danger-border: #ff4d4f;@btn-disable-color: rgba(0, 0, 0, 0.25);@btn-disable-bg: #f5f5f5;@btn-disable-border: #d9d9d9;@btn-default-ghost-color: #fff;@btn-default-ghost-bg: transparent;@btn-default-ghost-border: #fff;@btn-font-size-lg: 16px;@btn-font-size-sm: 14px;@btn-padding-horizontal-base: 15px;@btn-padding-horizontal-lg: 15px;@btn-padding-horizontal-sm: 7px;@btn-height-base: 32px;@btn-height-lg: 40px;@btn-height-sm: 24px;@btn-line-height: 1.5715;@btn-circle-size: 32px;@btn-circle-size-lg: 40px;@btn-circle-size-sm: 24px;@btn-square-size: 32px;@btn-square-size-lg: 40px;@btn-square-size-sm: 24px;@btn-square-only-icon-size: 16px;@btn-square-only-icon-size-sm: 14px;@btn-square-only-icon-size-lg: 18px;@btn-group-border: #40a9ff;@btn-link-hover-bg: transparent;@btn-text-hover-bg: rgba(0, 0, 0, 0.018);@checkbox-size: 16px;@checkbox-color: #1890ff;@checkbox-check-color: #fff;@checkbox-check-bg: #fff;@checkbox-border-width: 1px;@checkbox-border-radius: 2px;@checkbox-group-item-margin-right: 8px;@descriptions-bg: #fafafa;@descriptions-title-margin-bottom: 20px;@descriptions-default-padding: 16px 24px;@descriptions-middle-padding: 12px 24px;@descriptions-small-padding: 8px 16px;@descriptions-item-padding-bottom: 16px;@descriptions-item-trailing-colon: true;@descriptions-item-label-colon-margin-right: 8px;@descriptions-item-label-colon-margin-left: 2px;@descriptions-extra-color: rgba(0, 0, 0, 0.85);@divider-text-padding: 1em;@divider-orientation-margin: 5%;@divider-color: rgba(0, 0, 0, 0.06);@divider-vertical-gutter: 8px;@dropdown-selected-color: #1890ff;@dropdown-menu-submenu-disabled-bg: #fff;@dropdown-selected-bg: #e6f7ff;@empty-font-size: 14px;@radio-size: 16px;@radio-top: 0.2em;@radio-border-width: 1px;@radio-dot-size: 8px;@radio-dot-color: #1890ff;@radio-dot-disabled-color: rgba(0, 0, 0, 0.2);@radio-solid-checked-color: #fff;@radio-button-bg: #fff;@radio-button-checked-bg: #fff;@radio-button-color: rgba(0, 0, 0, 0.85);@radio-button-hover-color: #40a9ff;@radio-button-active-color: #096dd9;@radio-button-padding-horizontal: 15px;@radio-disabled-button-checked-bg: #e6e6e6;@radio-disabled-button-checked-color: rgba(0, 0, 0, 0.25);@radio-wrapper-margin-right: 8px;@screen-xs: 480px;@screen-xs-min: 480px;@screen-sm: 576px;@screen-sm-min: 576px;@screen-md: 768px;@screen-md-min: 768px;@screen-lg: 992px;@screen-lg-min: 992px;@screen-xl: 1200px;@screen-xl-min: 1200px;@screen-xxl: 1600px;@screen-xxl-min: 1600px;@screen-xs-max: 575px;@screen-sm-max: 767px;@screen-md-max: 991px;@screen-lg-max: 1199px;@screen-xl-max: 1599px;@grid-columns: 24;@layout-header-background: #001529;@layout-header-height: 64px;@layout-header-padding: 0 50px;@layout-header-color: rgba(0, 0, 0, 0.85);@layout-footer-padding: 24px 50px;@layout-footer-background: #f0f2f5;@layout-sider-background: #001529;@layout-trigger-height: 48px;@layout-trigger-background: #002140;@layout-trigger-color: #fff;@layout-zero-trigger-width: 36px;@layout-zero-trigger-height: 42px;@layout-sider-background-light: #fff;@layout-trigger-background-light: #fff;@layout-trigger-color-light: rgba(0, 0, 0, 0.85);@zindex-badge: auto;@zindex-table-fixed: 2;@zindex-affix: 10;@zindex-back-top: 10;@zindex-picker-panel: 10;@zindex-popup-close: 10;@zindex-modal: 1000;@zindex-modal-mask: 1000;@zindex-message: 1010;@zindex-notification: 1010;@zindex-popover: 1030;@zindex-dropdown: 1050;@zindex-picker: 1050;@zindex-popoconfirm: 1060;@zindex-tooltip: 1070;@zindex-image: 1080;@animation-duration-slow: 0.3s;@animation-duration-base: 0.2s;@animation-duration-fast: 0.1s;@collapse-panel-border-radius: 2px;@dropdown-menu-bg: #fff;@dropdown-vertical-padding: 5px;@dropdown-edge-child-vertical-padding: 4px;@dropdown-font-size: 14px;@dropdown-line-height: 22px;@label-required-color: #ff4d4f;@label-color: rgba(0, 0, 0, 0.85);@form-warning-input-bg: #fff;@form-item-margin-bottom: 24px;@form-item-trailing-colon: true;@form-vertical-label-padding: 0 0 8px;@form-vertical-label-margin: 0;@form-item-label-font-size: 14px;@form-item-label-height: 32px;@form-item-label-colon-margin-right: 8px;@form-item-label-colon-margin-left: 2px;@form-error-input-bg: #fff;@input-height-base: 32px;@input-height-lg: 40px;@input-height-sm: 24px;@input-padding-horizontal: 11px;@input-padding-horizontal-base: 11px;@input-padding-horizontal-sm: 7px;@input-padding-horizontal-lg: 11px;@input-padding-vertical-base: 4px;@input-padding-vertical-sm: 0px;@input-padding-vertical-lg: 6.5px;@input-placeholder-color: #bfbfbf;@input-color: rgba(0, 0, 0, 0.85);@input-icon-color: rgba(0, 0, 0, 0.85);@input-border-color: #d9d9d9;@input-bg: #fff;@input-number-hover-border-color: #40a9ff;@input-number-handler-active-bg: #f4f4f4;@input-number-handler-hover-bg: #40a9ff;@input-number-handler-bg: #fff;@input-number-handler-border-color: #d9d9d9;@input-addon-bg: #fafafa;@input-hover-border-color: #40a9ff;@input-disabled-bg: #f5f5f5;@input-outline-offset: 0 0;@input-icon-hover-color: rgba(0, 0, 0, 0.85);@input-disabled-color: rgba(0, 0, 0, 0.25);@mentions-dropdown-bg: #fff;@mentions-dropdown-menu-item-hover-bg: #fff;@select-border-color: #d9d9d9;@select-item-selected-color: rgba(0, 0, 0, 0.85);@select-item-selected-font-weight: 600;@select-dropdown-bg: #fff;@select-item-selected-bg: #e6f7ff;@select-item-active-bg: #f5f5f5;@select-dropdown-vertical-padding: 5px;@select-dropdown-font-size: 14px;@select-dropdown-line-height: 22px;@select-dropdown-height: 32px;@select-background: #fff;@select-clear-background: #fff;@select-selection-item-bg: #f5f5f5;@select-selection-item-border-color: #f0f0f0;@select-single-item-height-lg: 40px;@select-multiple-item-height: 24px;@select-multiple-item-height-lg: 32px;@select-multiple-item-spacing-half: 2px;@select-multiple-disabled-background: #f5f5f5;@select-multiple-item-disabled-color: #bfbfbf;@select-multiple-item-disabled-border-color: #d9d9d9;@cascader-bg: #fff;@cascader-item-selected-bg: #e6f7ff;@cascader-menu-bg: #fff;@cascader-menu-border-color-split: #f0f0f0;@cascader-dropdown-vertical-padding: 5px;@cascader-dropdown-edge-child-vertical-padding: 4px;@cascader-dropdown-font-size: 14px;@cascader-dropdown-line-height: 22px;@anchor-bg: transparent;@anchor-border-color: #f0f0f0;@anchor-link-top: 4px;@anchor-link-left: 16px;@anchor-link-padding: 4px 0 4px 16px;@tooltip-max-width: 250px;@tooltip-color: #fff;@tooltip-bg: rgba(0, 0, 0, 0.75);@tooltip-arrow-width: 11.3137085px;@tooltip-distance: 14.3137085px;@tooltip-arrow-color: rgba(0, 0, 0, 0.75);@tooltip-border-radius: 2px;@popover-bg: #fff;@popover-color: rgba(0, 0, 0, 0.85);@popover-min-width: 177px;@popover-min-height: 32px;@popover-arrow-width: 11.3137085px;@popover-arrow-color: #fff;@popover-arrow-outer-color: #fff;@popover-distance: 15.3137085px;@popover-padding-horizontal: 16px;@modal-header-padding-vertical: 16px;@modal-header-padding-horizontal: 24px;@modal-header-bg: #fff;@modal-header-padding: 16px 24px;@modal-header-border-width: 1px;@modal-header-border-style: solid;@modal-header-title-line-height: 22px;@modal-header-title-font-size: 16px;@modal-header-border-color-split: #f0f0f0;@modal-header-close-size: 54px;@modal-content-bg: #fff;@modal-heading-color: rgba(0, 0, 0, 0.85);@modal-close-color: rgba(0, 0, 0, 0.45);@modal-footer-bg: transparent;@modal-footer-border-color-split: #f0f0f0;@modal-footer-border-style: solid;@modal-footer-padding-vertical: 10px;@modal-footer-padding-horizontal: 16px;@modal-footer-border-width: 1px;@modal-mask-bg: rgba(0, 0, 0, 0.45);@modal-confirm-title-font-size: 16px;@modal-border-radius: 2px;@progress-default-color: #1890ff;@progress-remaining-color: #f5f5f5;@progress-info-text-color: rgba(0, 0, 0, 0.85);@progress-radius: 100px;@progress-steps-item-bg: #f3f3f3;@progress-text-font-size: 1em;@progress-text-color: rgba(0, 0, 0, 0.85);@progress-circle-text-font-size: 1em;@menu-inline-toplevel-item-height: 40px;@menu-item-height: 40px;@menu-item-group-height: 1.5715;@menu-collapsed-width: 80px;@menu-bg: #fff;@menu-popup-bg: #fff;@menu-item-color: rgba(0, 0, 0, 0.85);@menu-inline-submenu-bg: #fafafa;@menu-highlight-color: #1890ff;@menu-highlight-danger-color: #ff4d4f;@menu-item-active-bg: #e6f7ff;@menu-item-active-danger-bg: #fff1f0;@menu-item-active-border-width: 3px;@menu-item-group-title-color: rgba(0, 0, 0, 0.45);@menu-item-vertical-margin: 4px;@menu-item-font-size: 14px;@menu-item-boundary-margin: 8px;@menu-item-padding-horizontal: 20px;@menu-item-padding: 0 20px;@menu-horizontal-line-height: 46px;@menu-icon-margin-right: 10px;@menu-icon-size: 14px;@menu-icon-size-lg: 16px;@menu-item-group-title-font-size: 14px;@menu-dark-color: rgba(255, 255, 255, 0.65);@menu-dark-danger-color: #ff4d4f;@menu-dark-bg: #001529;@menu-dark-arrow-color: #fff;@menu-dark-inline-submenu-bg: #000c17;@menu-dark-highlight-color: #fff;@menu-dark-item-active-bg: #1890ff;@menu-dark-item-active-danger-bg: #ff4d4f;@menu-dark-selected-item-icon-color: #fff;@menu-dark-selected-item-text-color: #fff;@menu-dark-item-hover-bg: transparent;@spin-dot-size-sm: 14px;@spin-dot-size: 20px;@spin-dot-size-lg: 32px;@table-bg: #fff;@table-header-bg: #fafafa;@table-header-color: rgba(0, 0, 0, 0.85);@table-header-sort-bg: #f5f5f5;@table-row-hover-bg: #fafafa;@table-selected-row-color: inherit;@table-selected-row-bg: #e6f7ff;@table-selected-row-hover-bg: #dcf4ff;@table-expanded-row-bg: #fbfbfb;@table-padding-vertical: 16px;@table-padding-horizontal: 16px;@table-padding-vertical-md: 12px;@table-padding-horizontal-md: 8px;@table-padding-vertical-sm: 8px;@table-padding-horizontal-sm: 8px;@table-border-color: #f0f0f0;@table-border-radius-base: 2px;@table-footer-bg: #fafafa;@table-footer-color: rgba(0, 0, 0, 0.85);@table-header-bg-sm: #fafafa;@table-font-size: 14px;@table-font-size-md: 14px;@table-font-size-sm: 14px;@table-header-cell-split-color: rgba(0, 0, 0, 0.06);@table-header-sort-active-bg: rgba(0, 0, 0, 0.04);@table-fixed-header-sort-active-bg: #f5f5f5;@table-header-filter-active-bg: rgba(0, 0, 0, 0.04);@table-filter-btns-bg: inherit;@table-filter-dropdown-bg: #fff;@table-expand-icon-bg: #fff;@table-selection-column-width: 32px;@table-sticky-scroll-bar-bg: rgba(0, 0, 0, 0.35);@table-sticky-scroll-bar-radius: 4px;@tag-border-radius: 2px;@tag-default-bg: #fafafa;@tag-default-color: rgba(0, 0, 0, 0.85);@tag-font-size: 12px;@tag-line-height: 20px;@picker-bg: #fff;@picker-basic-cell-hover-color: #f5f5f5;@picker-basic-cell-active-with-range-color: #e6f7ff;@picker-basic-cell-hover-with-range-color: #cbe6ff;@picker-basic-cell-disabled-bg: rgba(0, 0, 0, 0.04);@picker-border-color: #f0f0f0;@picker-date-hover-range-border-color: #7ec1ff;@picker-date-hover-range-color: #cbe6ff;@picker-time-panel-column-width: 56px;@picker-time-panel-column-height: 224px;@picker-time-panel-cell-height: 28px;@picker-panel-cell-height: 24px;@picker-panel-cell-width: 36px;@picker-text-height: 40px;@picker-panel-without-time-cell-height: 66px;@calendar-bg: #fff;@calendar-input-bg: #fff;@calendar-border-color: #fff;@calendar-item-active-bg: #e6f7ff;@calendar-column-active-bg: rgba(230, 247, 255, 0.2);@calendar-full-bg: #fff;@calendar-full-panel-bg: #fff;@carousel-dot-width: 16px;@carousel-dot-height: 3px;@carousel-dot-active-width: 24px;@badge-height: 20px;@badge-height-sm: 14px;@badge-dot-size: 6px;@badge-font-size: 12px;@badge-font-size-sm: 12px;@badge-font-weight: normal;@badge-status-size: 6px;@badge-text-color: #fff;@badge-color: #ff4d4f;@rate-star-color: #fadb14;@rate-star-bg: #f0f0f0;@rate-star-size: 20px;@rate-star-hover-scale: scale(1.1);@card-head-color: rgba(0, 0, 0, 0.85);@card-head-background: transparent;@card-head-font-size: 16px;@card-head-font-size-sm: 14px;@card-head-padding: 16px;@card-head-padding-sm: 8px;@card-head-height: 48px;@card-head-height-sm: 36px;@card-inner-head-padding: 12px;@card-padding-base: 24px;@card-padding-base-sm: 12px;@card-actions-background: #fff;@card-actions-li-margin: 12px 0;@card-skeleton-bg: #cfd8dc;@card-background: #fff;@card-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09);@card-radius: 2px;@card-head-tabs-margin-bottom: -17px;@card-head-extra-color: rgba(0, 0, 0, 0.85);@comment-bg: inherit;@comment-padding-base: 16px 0;@comment-nest-indent: 44px;@comment-font-size-base: 14px;@comment-font-size-sm: 12px;@comment-author-name-color: rgba(0, 0, 0, 0.45);@comment-author-time-color: #ccc;@comment-action-color: rgba(0, 0, 0, 0.45);@comment-action-hover-color: #595959;@comment-actions-margin-bottom: inherit;@comment-actions-margin-top: 12px;@comment-content-detail-p-margin-bottom: inherit;@tabs-card-head-background: #fafafa;@tabs-card-height: 40px;@tabs-card-active-color: #1890ff;@tabs-card-horizontal-padding: 8px 16px;@tabs-card-horizontal-padding-sm: 6px 16px;@tabs-card-horizontal-padding-lg: 7px 16px 6px;@tabs-title-font-size: 14px;@tabs-title-font-size-lg: 16px;@tabs-title-font-size-sm: 14px;@tabs-ink-bar-color: #1890ff;@tabs-bar-margin: 0 0 16px 0;@tabs-horizontal-gutter: 32px;@tabs-horizontal-margin: 0 0 0 32px;@tabs-horizontal-margin-rtl: 0 0 0 32px;@tabs-horizontal-padding: 12px 0;@tabs-horizontal-padding-lg: 16px 0;@tabs-horizontal-padding-sm: 8px 0;@tabs-vertical-padding: 8px 24px;@tabs-vertical-margin: 16px 0 0 0;@tabs-scrolling-size: 32px;@tabs-highlight-color: #1890ff;@tabs-hover-color: #40a9ff;@tabs-active-color: #096dd9;@tabs-card-gutter: 2px;@tabs-card-tab-active-border-top: 2px solid transparent;@back-top-color: #fff;@back-top-bg: rgba(0, 0, 0, 0.45);@back-top-hover-bg: rgba(0, 0, 0, 0.85);@avatar-size-base: 32px;@avatar-size-lg: 40px;@avatar-size-sm: 24px;@avatar-font-size-base: 18px;@avatar-font-size-lg: 24px;@avatar-font-size-sm: 14px;@avatar-bg: #ccc;@avatar-color: #fff;@avatar-border-radius: 2px;@avatar-group-overlapping: -8px;@avatar-group-space: 3px;@avatar-group-border-color: #fff;@switch-height: 22px;@switch-sm-height: 16px;@switch-min-width: 44px;@switch-sm-min-width: 28px;@switch-disabled-opacity: 0.4;@switch-color: #1890ff;@switch-bg: #fff;@switch-shadow-color: rgba(0, 35, 11, 0.2);@switch-padding: 2px;@switch-inner-margin-min: 7px;@switch-inner-margin-max: 25px;@switch-sm-inner-margin-min: 5px;@switch-sm-inner-margin-max: 18px;@pagination-item-bg: #fff;@pagination-item-size: 32px;@pagination-item-size-sm: 24px;@pagination-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@pagination-font-weight-active: 500;@pagination-item-bg-active: #fff;@pagination-item-link-bg: #fff;@pagination-item-disabled-color-active: rgba(0, 0, 0, 0.25);@pagination-item-disabled-bg-active: #e6e6e6;@pagination-item-input-bg: #fff;@pagination-mini-options-size-changer-top: 0px;@page-header-padding: 24px;@page-header-padding-vertical: 16px;@page-header-padding-breadcrumb: 12px;@page-header-content-padding-vertical: 12px;@page-header-back-color: #000;@page-header-ghost-bg: inherit;@page-header-heading-title: 20px;@page-header-heading-sub-title: 14px;@page-header-tabs-tab-font-size: 16px;@breadcrumb-base-color: rgba(0, 0, 0, 0.45);@breadcrumb-last-item-color: rgba(0, 0, 0, 0.85);@breadcrumb-font-size: 14px;@breadcrumb-icon-font-size: 14px;@breadcrumb-link-color: rgba(0, 0, 0, 0.45);@breadcrumb-link-color-hover: rgba(0, 0, 0, 0.85);@breadcrumb-separator-color: rgba(0, 0, 0, 0.45);@breadcrumb-separator-margin: 0 8px;@slider-margin: 10px 6px 10px;@slider-rail-background-color: #f5f5f5;@slider-rail-background-color-hover: #e1e1e1;@slider-track-background-color: #91d5ff;@slider-track-background-color-hover: #69c0ff;@slider-handle-border-width: 2px;@slider-handle-background-color: #fff;@slider-handle-color: #91d5ff;@slider-handle-color-hover: #69c0ff;@slider-handle-color-focus: #46a6ff;@slider-handle-color-focus-shadow: rgba(24, 144, 255, 0.12);@slider-handle-color-tooltip-open: #1890ff;@slider-handle-size: 14px;@slider-handle-margin-top: -5px;@slider-handle-shadow: 0;@slider-dot-border-color: #f0f0f0;@slider-dot-border-color-active: #8cc8ff;@slider-disabled-color: rgba(0, 0, 0, 0.25);@slider-disabled-background-color: #fff;@tree-bg: #fff;@tree-title-height: 24px;@tree-child-padding: 18px;@tree-directory-selected-color: #fff;@tree-directory-selected-bg: #1890ff;@tree-node-hover-bg: #f5f5f5;@tree-node-selected-bg: #bae7ff;@collapse-header-padding: 12px 16px;@collapse-header-padding-extra: 40px;@collapse-header-bg: #fafafa;@collapse-content-padding: 16px;@collapse-content-bg: #fff;@collapse-header-arrow-left: 16px;@skeleton-color: rgba(190, 190, 190, 0.2);@skeleton-to-color: rgba(129, 129, 129, 0.24);@skeleton-paragraph-margin-top: 28px;@skeleton-paragraph-li-margin-top: 16px;@skeleton-paragraph-li-height: 16px;@skeleton-title-height: 16px;@skeleton-title-paragraph-margin-top: 24px;@transfer-header-height: 40px;@transfer-item-height: 32px;@transfer-disabled-bg: #f5f5f5;@transfer-list-height: 200px;@transfer-item-hover-bg: #f5f5f5;@transfer-item-selected-hover-bg: #dcf4ff;@transfer-item-padding-vertical: 6px;@transfer-list-search-icon-top: 12px;@message-notice-content-padding: 10px 16px;@message-notice-content-bg: #fff;@wave-animation-width: 6px;@alert-success-border-color: #b7eb8f;@alert-success-bg-color: #f6ffed;@alert-success-icon-color: #52c41a;@alert-info-border-color: #91d5ff;@alert-info-bg-color: #e6f7ff;@alert-info-icon-color: #1890ff;@alert-warning-border-color: #ffe58f;@alert-warning-bg-color: #fffbe6;@alert-warning-icon-color: #faad14;@alert-error-border-color: #ffccc7;@alert-error-bg-color: #fff2f0;@alert-error-icon-color: #ff4d4f;@alert-message-color: rgba(0, 0, 0, 0.85);@alert-text-color: rgba(0, 0, 0, 0.85);@alert-close-color: rgba(0, 0, 0, 0.45);@alert-close-hover-color: rgba(0, 0, 0, 0.75);@alert-no-icon-padding-vertical: 8px;@alert-with-description-no-icon-padding-vertical: 15px;@alert-with-description-padding-vertical: 15px;@alert-with-description-padding: 15px 15px 15px 24px;@alert-icon-top: 12.0005px;@alert-with-description-icon-size: 24px;@list-header-background: transparent;@list-footer-background: transparent;@list-empty-text-padding: 16px;@list-item-padding: 12px 0;@list-item-padding-sm: 8px 16px;@list-item-padding-lg: 16px 24px;@list-item-meta-margin-bottom: 16px;@list-item-meta-avatar-margin-right: 16px;@list-item-meta-title-margin-bottom: 12px;@list-customize-card-bg: #fff;@list-item-meta-description-font-size: 14px;@statistic-title-font-size: 14px;@statistic-content-font-size: 24px;@statistic-unit-font-size: 24px;@statistic-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';@drawer-header-padding: 16px 24px;@drawer-bg: #fff;@drawer-footer-padding-vertical: 10px;@drawer-footer-padding-horizontal: 16px;@drawer-header-close-size: 56px;@drawer-title-font-size: 16px;@drawer-title-line-height: 22px;@timeline-width: 2px;@timeline-color: #f0f0f0;@timeline-dot-border-width: 2px;@timeline-dot-color: #1890ff;@timeline-dot-bg: #fff;@timeline-item-padding-bottom: 20px;@typography-title-font-weight: 600;@typography-title-margin-top: 1.2em;@typography-title-margin-bottom: 0.5em;@upload-actions-color: rgba(0, 0, 0, 0.45);@process-tail-color: #f0f0f0;@steps-nav-arrow-color: rgba(0, 0, 0, 0.25);@steps-background: #fff;@steps-icon-size: 32px;@steps-icon-custom-size: 32px;@steps-icon-custom-top: 0px;@steps-icon-custom-font-size: 24px;@steps-icon-top: -0.5px;@steps-icon-font-size: 16px;@steps-icon-margin: 0 8px 0 0;@steps-title-line-height: 32px;@steps-small-icon-size: 24px;@steps-small-icon-margin: 0 8px 0 0;@steps-dot-size: 8px;@steps-dot-top: 2px;@steps-current-dot-size: 10px;@steps-description-max-width: 140px;@steps-nav-content-max-width: auto;@steps-vertical-icon-width: 16px;@steps-vertical-tail-width: 16px;@steps-vertical-tail-width-sm: 12px;@notification-bg: #fff;@notification-padding-vertical: 16px;@notification-padding-horizontal: 24px;@result-title-font-size: 24px;@result-subtitle-font-size: 14px;@result-icon-font-size: 72px;@result-extra-margin: 24px 0 0 0;@image-size-base: 48px;@image-font-size-base: 24px;@image-bg: #f5f5f5;@image-color: #fff;@image-mask-font-size: 16px;@image-preview-operation-size: 18px;@image-preview-operation-color: rgba(255, 255, 255, 0.85);@image-preview-operation-disabled-color: rgba(255, 255, 255, 0.25);@segmented-bg: rgba(0, 0, 0, 0.04);@segmented-hover-bg: rgba(0, 0, 0, 0.06);@segmented-selected-bg: #fff;@segmented-label-color: rgba(0, 0, 0, 0.65);@segmented-label-hover-color: #262626;@root-entry-name: variable;"], "names": [], "sourceRoot": ""}