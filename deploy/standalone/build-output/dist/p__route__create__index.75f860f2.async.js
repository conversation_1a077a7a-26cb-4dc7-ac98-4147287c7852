"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[6997],{90159:function(g,m,e){e.r(m);var M=e(15009),i=e.n(M),O=e(99289),f=e.n(O),C=e(5574),j=e.n(C),b=e(87172),n=e(70831),h=e(31418),p=e(67294),A=e(57632),I=e(13330),a=e(85893),T=function(){var u,t,R=(0,n.useParams)(),c=R.compoundId,W=c===void 0?"":c,r=Number.parseInt(W),B=(0,p.useState)(),E=j()(B,2),_=E[0],K=E[1],L=h.Z.useApp(),U=L.notification,y=function(){var v=f()(i()().mark(function P(D){var d,l,s;return i()().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return o.next=2,(0,b.service)("project-compounds/".concat(D)).select(["id","status"]).populateWith("compound",["smiles"]).populateWith("project",["id"]).get();case 2:d=o.sent,l=d.data,s=d.error,!l||s?(U.error({message:(s==null?void 0:s.message)||"\u5206\u5B50\u672A\u627E\u5230"}),n.history.push("/404")):K(l);case 6:case"end":return o.stop()}},P)}));return function(D){return v.apply(this,arguments)}}();return(0,p.useEffect)(function(){y(r)},[r]),_!=null&&(u=_.compound)!==null&&u!==void 0&&u.smiles?(0,a.jsx)(I.default,{mainTree:{id:"".concat((0,A.Z)()),value:_.compound.smiles},compoundId:r,projectId:(t=_.project)===null||t===void 0?void 0:t.id,onCancel:function(){return n.history.back()}}):(0,a.jsx)(a.Fragment,{})};m.default=T}}]);

//# sourceMappingURL=p__route__create__index.75f860f2.async.js.map