{"version": 3, "file": "p__reaction__index.0672e644.async.js", "mappings": "uXACA,GAAe,CAAC,E,WCSD,SAASA,IAAkB,CACxC,IAAAC,KAAuDC,EAAAA,UAAS,YAAY,EAApEC,EAAqBF,EAArBE,sBAAuBC,EAAmBH,EAAnBG,oBAC/BC,KAAwCC,EAAAA,iBAAgB,EAACC,EAAAC,EAAAA,EAAAH,EAAA,GAAlDI,EAAYF,EAAA,GAAEG,EAAeH,EAAA,GACpCI,KAAuBC,EAAAA,WAAU,EAAzBC,EAAUF,EAAVE,WACRC,SAAAA,EAAAA,WAAU,UAAM,CACdX,EAAsB,CAAEY,oBAAqBF,CAAW,CAAC,CAC3D,EAAG,CAAC,CAAC,KAGHG,EAAAA,KAAA,OAAKC,UAAWC,GAAAA,EAAGC,GAAOC,YAAY,EAAEC,YACtCL,EAAAA,KAACM,EAAAA,EAAc,CACbC,cAAe,GACfC,aAAcpB,EACdqB,aAAc,SAACC,EAAW,CACxBvB,EAAqBwB,EAAAA,EAAAA,EAAAA,EAAC,CAAC,EAAID,CAAM,MAAEX,oBAAqBF,CAAU,EAAE,EACpE,IAAIe,EAAMnB,EAAaoB,IAAI,KAAK,EAC5BD,GACFlB,EACE,CACEkB,IAAAA,CACF,EACA,CAAEE,QAAS,EAAK,CAClB,CAEJ,CAAE,CACH,CAAC,CACC,CAET,CAEO,IAAMC,GAA2B,SACtCC,EACAC,EACAC,EACAC,EAAkC,OAC9B,CACJC,IAAK,gBACLC,SACErB,EAAAA,KAACsB,GAAAA,EAAa,CACZC,SAAOC,EAAAA,IAAQ,+CAA+C,EAC9DC,UAASC,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAC,GAAA,KAAAC,EAAA,OAAAH,EAAAA,EAAA,EAAAI,KAAA,SAAAC,EAAA,eAAAA,EAAAC,KAAAD,EAAAE,KAAA,WACJhB,EAAmB,CAAFc,EAAAE,KAAA,eAAAF,EAAAG,OAAA,iBAAAH,OAAAA,EAAAE,KAAA,KAEdE,EAAAA,IAAyB,CAC7BC,KAAM,CACJtC,oBAAqBmB,CACvB,CACF,CAAC,EAAC,OALuC,GAArCY,EAAqCE,EAAAM,KAAA,IAMvCC,EAAAA,IAAoBT,CAAG,EAAEU,GAAI,CAAFR,EAAAE,KAAA,eAAAF,EAAAG,OAAA,SAASL,EAAIO,KAAKI,MAAM,0BAAAT,EAAAU,KAAA,IAAAb,CAAA,EACxD,GACDc,aAAcxB,CAAY,CAC3B,EAEHd,SAAUY,GAAaC,KAAoBlB,EAAAA,KAAChB,GAAe,EAAE,EAAI,IACnE,CAAC,E,oFC9CW4D,GAAS,SAATA,EAAS,CAATA,OAAAA,EAAS,cAATA,EAAS,YAATA,EAAS,gBAATA,CAAS,MCuRTC,GAAQ,SAARA,EAAQ,CAARA,OAAAA,EAAQ,QAARA,EAAQ,QAARA,EAAQ,QAARA,CAAQ,MAwaRC,GAAY,SAAZA,EAAY,CAAZA,OAAAA,EAAY,oBAAZA,EAAY,sBAAZA,EAAY,gBAAZA,EAAY,kBAAZA,EAAY,kBAAZA,EAAY,kBAAZA,CAAY,MA+BZC,GAAY,SAAZA,EAAY,CAAZA,OAAAA,EAAY,oBAAZA,EAAY,sBAAZA,EAAY,kBAAZA,EAAY,oBAAZA,CAAY,MA6BZC,GAAe,SAAfA,EAAe,CAAfA,OAAAA,EAAe,oBAAfA,EAAe,kBAAfA,EAAe,sBAAfA,EAAe,oBAAfA,EAAe,4BAAfA,CAAe,MAQfC,GAAU,SAAVA,EAAU,CAAVA,OAAAA,EAAU,+BAAVA,EAAU,gBAAVA,EAAU,uBAAVA,CAAU,MAmLVC,GAAY,SAAZA,EAAY,CAAZA,OAAAA,EAAY,sBAAZA,EAAY,kBAAZA,EAAY,oBAAZA,EAAY,kBAAZA,EAAY,kBAAZA,CAAY,MAQZC,GAAU,SAAVA,EAAU,CAAVA,OAAAA,EAAU,UAAVA,EAAU,UAAVA,EAAU,oBAAVA,CAAU,MAmdVC,GAAS,SAATA,EAAS,CAATA,OAAAA,EAAS,cAATA,EAAS,aAATA,CAAS,MAKTC,GAAY,SAAZA,EAAY,CAAZA,OAAAA,EAAY,cAAZA,EAAY,kBAAZA,EAAY,YAAZA,EAAY,kBAAZA,CAAY,MCv4CZC,GAAW,SAAXA,EAAW,CAAXA,OAAAA,EAAW,YAAXA,EAAW,UAAXA,EAAW,UAAXA,CAAW,MAWXC,GAAW,SAAXA,EAAW,CAAXA,OAAAA,EAAW,oBAAXA,EAAW,oBAAXA,EAAW,oBAAXA,EAAW,YAAXA,CAAW,MAYXC,GAAS,SAATA,EAAS,CAATA,OAAAA,EAAS,MAATA,EAAS,MAATA,CAAS,M,wBCtCTC,GAAY,SAAZA,EAAY,CAAZA,OAAAA,EAAY,oBAAZA,EAAY,kBAAZA,EAAY,sBAAZA,EAAY,sBAAZA,CAAY,MCAZC,GAAgB,SAAhBA,EAAgB,CAAhBA,OAAAA,EAAgB,kBAAhBA,EAAgB,cAAhBA,EAAgB,kBAAhBA,EAAgB,oBAAhBA,EAAgB,YAAhBA,EAAgB,gBAAhBA,EAAgB,sBAAhBA,EAAgB,kBAAhBA,CAAgB,MCiEhBC,GAAU,SAAVA,EAAU,CAAVA,OAAAA,EAAU,wBAAVA,EAAU,gBAAVA,EAAU,kBAAVA,CAAU,MAMVC,GAAY,SAAZA,EAAY,CAAZA,OAAAA,EAAY,MAAZA,EAAY,QAAZA,EAAY,UAAZA,CAAY,MCvEZC,GAAc,SAAdA,EAAc,CAAdA,OAAAA,EAAc,sBAAdA,EAAc,cAAdA,EAAc,gBAAdA,CAAc,MCsDdC,GAAU,SAAVA,EAAU,CAAVA,OAAAA,EAAU,oBAAVA,EAAU,sBAAVA,EAAU,kBAAVA,EAAU,YAAVA,CAAU,MC2OVjB,GAAQ,SAARA,EAAQ,CAARA,OAAAA,EAAQ,QAARA,EAAQ,QAARA,EAAQ,QAARA,CAAQ,MAwaRC,GAAY,SAAZA,EAAY,CAAZA,OAAAA,EAAY,oBAAZA,EAAY,sBAAZA,EAAY,gBAAZA,EAAY,kBAAZA,EAAY,kBAAZA,EAAY,kBAAZA,CAAY,MA+BZC,GAAY,SAAZA,EAAY,CAAZA,OAAAA,EAAY,oBAAZA,EAAY,sBAAZA,EAAY,kBAAZA,EAAY,oBAAZA,CAAY,MA6BZC,GAAe,SAAfA,EAAe,CAAfA,OAAAA,EAAe,oBAAfA,EAAe,kBAAfA,EAAe,sBAAfA,EAAe,oBAAfA,EAAe,4BAAfA,CAAe,MAQfC,GAAU,SAAVA,EAAU,CAAVA,OAAAA,EAAU,+BAAVA,EAAU,gBAAVA,EAAU,uBAAVA,CAAU,MAmLVC,GAAY,SAAZA,EAAY,CAAZA,OAAAA,EAAY,sBAAZA,EAAY,kBAAZA,EAAY,oBAAZA,EAAY,kBAAZA,EAAY,kBAAZA,CAAY,MAQZC,GAAU,SAAVA,EAAU,CAAVA,OAAAA,EAAU,UAAVA,EAAU,UAAVA,EAAU,oBAAVA,CAAU,MAmdVC,GAAS,SAATA,EAAS,CAATA,OAAAA,EAAS,cAATA,EAAS,aAATA,CAAS,MAKTC,GAAY,SAAZA,EAAY,CAAZA,OAAAA,EAAY,cAAZA,EAAY,kBAAZA,EAAY,YAAZA,EAAY,kBAAZA,CAAY,MCh6CZU,GAAI,SAAJA,EAAI,CAAJA,OAAAA,EAAI,6BAAJA,EAAI,oBAAJA,CAAI,MCAJC,GAAM,SAANA,EAAM,CAANA,OAAAA,EAAM,oBAANA,EAAM,kBAANA,EAAM,oBAANA,EAAM,kBAANA,EAAM,oBAANA,CAAM,MCANC,GAAI,SAAJA,EAAI,CAAJA,OAAAA,EAAI,MAAJA,EAAI,QAAJA,CAAI,M,6ICiBVC,GAAwD,SAAHC,EAOrD,KAAAC,EAAAC,EAAAC,EANJC,EAASJ,EAATI,UACAC,EAAML,EAANK,OACAtD,EAAiBiD,EAAjBjD,kBACQuD,EAAUN,EAAlBO,OACUC,EAAYR,EAAtBS,SACAC,EAASV,EAATU,UAEMC,KAASC,EAAAA,WAAU,EACjBC,EAAmBT,EAAnBS,OAAQC,EAAWV,EAAXU,OAChBC,EAAkBC,GAAAA,EAAIC,OAAO,EAArBC,EAAKH,EAALG,MACR1F,KAAsCC,EAAAA,WAA0B,EAApDqB,EAAStB,EAAb2F,GAAezF,EAAUF,EAAVE,WACjB6E,EAAS,UAAH,QAASD,GAAU,YAAVA,EAAaF,CAAS,CAAC,EAEtCK,EAAW,UAAM,CAMvB,EACMW,EAAsB,eAAAC,EAAA9D,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAA6D,GAAA,QAAA9D,EAAAA,EAAA,EAAAI,KAAA,SAAA2D,EAAA,eAAAA,EAAAzD,KAAAyD,EAAAxD,KAAA,QAC7BmD,EAAMM,QAAQ,CACZpE,MAAO,+DACPqE,KAAM,UAAF,KAAAC,EAAAnE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAC,GAAA,QAAAF,EAAAA,EAAA,EAAAI,KAAA,SAAAC,EAAA,eAAAA,EAAAC,KAAAD,EAAAE,KAAA,QAAAF,OAAAA,EAAAE,KAAA,KACE4D,EAAAA,IAA2B,CAC/BzD,KAAM,CAAEiD,GAAId,EAAOc,GAAIS,OAAQtC,GAAauC,QAAS,CACvD,CAAC,EAAC,OACFnB,GAAS,MAATA,EAAY,QAAQ,EAAC,wBAAA7C,EAAAU,KAAA,IAAAb,CAAA,EACtB,YAAA+D,GAAA,QAAAC,EAAAI,MAAA,KAAAC,SAAA,SAAAN,CAAA,GACH,CAAC,EAAC,wBAAAF,EAAAhD,KAAA,IAAA+C,CAAA,EACH,oBAV2B,QAAAD,EAAAS,MAAA,KAAAC,SAAA,MAYtBC,EAAa,SAAHC,EAMV,KALJC,EAAOD,EAAPC,QACAC,EAASF,EAATE,UAKA,SACEtG,EAAAA,KAACuG,GAAAA,EAAO,CACNC,aAAc,CACZC,MAAO,MACT,EACAH,UAAWA,GAAa,MACxBD,QAASA,EACTK,MAAO,GAAMrG,YAEbL,EAAAA,KAAC2G,GAAAA,EAAWC,KAAI,CAAAvG,SAAC,WAAS,CAAiB,CAAC,CACrC,CAEb,EAEA,SACEL,EAAAA,KAAC6G,GAAAA,EAAI,CACHC,MAAO,CAAEC,OAAQ,OAAQ,EACzBC,KAAK,QACL/G,UAAU,+BAA8BI,YAExC4G,EAAAA,MAACC,EAAAA,EAAG,CAAA7G,SAAA,IACF4G,EAAAA,MAACE,EAAAA,EAAG,CAACC,GAAI,GAAIC,GAAI,GAAGhH,SAAA,IAClB4G,EAAAA,MAACC,EAAAA,EAAG,CAAA7G,SAAA,IACFL,EAAAA,KAAC2G,GAAAA,EAAWW,MAAK,CAACC,MAAO,EAAElH,YAAEmB,EAAAA,IAAQ,UAAU,CAAC,CAAmB,KACnExB,EAAAA,KAACwH,GAAAA,EAAK,CAACvH,UAAU,kBAAkB+G,KAAK,QAAO3G,UAC5CyE,GAAM,OAAAV,EAANU,EAAQ2C,gBAAY,MAAArD,IAAA,cAApBA,EAAsBsD,SACrB,gDACF,OACE1H,EAAAA,KAAC2H,GAAAA,GAAM,CAACC,KAAK,OAAOC,QAASnD,EAAOrE,YACjCmB,EAAAA,IAAQ,gBAAgB,CAAC,CACpB,CACT,CAII,CAAC,EACL,KACLxB,EAAAA,KAACkH,EAAAA,EAAG,CAAA7G,YACF4G,EAAAA,MAACO,GAAAA,EAAK,CAAAnH,SAAA,IACJ4G,EAAAA,MAACN,GAAAA,EAAWmB,KAAI,CAAAzH,SAAA,IAAEmB,EAAAA,IAAQ,OAAO,EAAE,IAAE,EAAiB,KACtDxB,EAAAA,KAAC2G,GAAAA,EAAWmB,KAAI,CAACF,KAAK,YAAWvH,YAC9B0H,GAAAA,OAAM/C,CAAM,EAAI,IAAM,GAAHgD,OAAMhD,EAAM,IAAG,CACpB,CAAC,EACb,CAAC,CACL,KACLhF,EAAAA,KAACkH,EAAAA,EAAG,CAAA7G,YACFL,EAAAA,KAACiI,GAAAA,QAAiB,CAACC,UAAWjD,CAAO,CAAE,CAAC,CACrC,KACLjF,EAAAA,KAACkH,EAAAA,EAAG,CAAA7G,YACFL,EAAAA,KAACmG,EAAU,CAACG,UAAU,UAAUD,QAAS9B,EAAUA,SAAU,CAAE,CAAC,CAC7D,CAAC,EACH,KACLvE,EAAAA,KAACmH,EAAAA,EAAG,CAACC,GAAI,EAAGC,GAAI,EAAGpH,UAAU,kBAAiBI,YAC5CL,EAAAA,KAACmI,GAAAA,EAAO,CAACP,KAAK,WAAW3H,UAAU,SAAS,CAAE,CAAC,CAC5C,KACLgH,EAAAA,MAACE,EAAAA,EAAG,CAACC,GAAI,GAAIC,GAAI,GAAGhH,SAAA,IAClB4G,EAAAA,MAACmB,GAAAA,GAAe,CACd7G,SAAOC,EAAAA,IAAQ,mBAAmB,EAClC6G,OAAQ,EACRC,WAAY9D,EACZ+D,SACEtB,EAAAA,MAACO,GAAAA,EAAK,CAAAnH,SAAA,CACHyE,GAAM,OAAAT,EAANS,EAAQ2C,gBAAY,MAAApD,IAAA,QAApBA,EAAsBqD,SACrB,yCACF,KACE1H,EAAAA,KAAC2H,GAAAA,GAAM,CACLC,KAAK,OACLC,QAAS,UAAM,CACbW,OAAOC,KAAK,aAADT,OACI/G,EAAS,cAAA+G,OAAanI,EAAU,mCAAAmI,UAAkCU,EAAAA,IAC7EC,KAAKC,UAAUpE,EAAOc,EAAE,CAC1B,EAAC,gBACD,QACF,CACF,EAAEjF,YAEDmB,EAAAA,IAAQ,2CAA2C,CAAC,CAC/C,EAER,GAEDgD,EAAOuB,SAAWtC,GAAaoF,cAC9B7I,EAAAA,KAAC8I,GAAAA,EAAmB,CAClBC,cAAexE,EAAUyE,eACzB/H,aAAWgI,EAAAA,IAAMzE,EAAO0E,UAAU,EAClChI,kBAAmBA,EACnBiI,oBAAqB3E,EAAO4E,qBAC5BC,UAAW,kBAAMxE,GAAS,YAATA,EAAY,QAAQ,CAAC,CAAC,CACxC,GAEFC,GAAM,OAAAR,EAANQ,EAAQ2C,gBAAY,MAAAnD,IAAA,cAApBA,EAAsBoD,SACrB,2CACF,IACElD,EAAOuB,SAAWtC,GAAauC,aAC7BhG,EAAAA,KAACsJ,GAAAA,EAAiB,CAChB1B,KAAK,OACLC,QAAStC,EAAuBlF,YAE/BmB,EAAAA,IAAQ,yCAAyC,CAAC,CAClC,CACpB,EACE,EACRnB,SAAA,IAEDL,EAAAA,KAACoI,GAAAA,GAAgBmB,KAAI,CACnBlI,SAAOG,EAAAA,IAAQ,6CAA6C,EAC5DgI,UAAU,MAAM,CACjB,KACDxJ,EAAAA,KAACoI,GAAAA,GAAgBmB,KAAI,CACnBlI,SAAOG,EAAAA,IAAQ,0BAA0B,EACzCgI,UAAU,SACVC,UAAW,CACTC,SAAU,CACRC,QACE3J,EAAAA,KAAC4J,GAAAA,EAAG,CAACC,MAAM,UAASxJ,YACjBmB,EAAAA,IAAQ,6CAA6C,CAAC,CACpD,CAET,EACAsI,QAAS,CACPH,QACE3J,EAAAA,KAAC4J,GAAAA,EAAG,CAACC,MAAM,UAASxJ,YACjBmB,EAAAA,IAAQ,4CAA4C,CAAC,CACnD,CAET,EACAuI,UAAW,CACTJ,QACE3J,EAAAA,KAAC4J,GAAAA,EAAG,CAACC,MAAM,UAASxJ,YACjBmB,EAAAA,IAAQ,8CAA8C,CAAC,CACrD,CAET,EACAwI,UAAW,CACTL,QACE3J,EAAAA,KAAC4J,GAAAA,EAAG,CAACC,MAAM,UAASxJ,YACjBmB,EAAAA,IAAQ,8CAA8C,CAAC,CACrD,CAET,CACF,CAAE,CACH,KACDxB,EAAAA,KAACoI,GAAAA,GAAgBmB,KAAI,CACnBlI,SAAOG,EAAAA,IAAQ,SAAS,EACxBgI,UAAU,SAAS,CACpB,KACDxJ,EAAAA,KAACoI,GAAAA,GAAgBmB,KAAI,CACnBlI,SAAOG,EAAAA,IAAQ,oBAAoB,EACnCgI,UAAU,gBACVS,UAAU,UAAU,CACrB,KACDjK,EAAAA,KAACoI,GAAAA,GAAgBmB,KAAI,CAAClI,SAAOG,EAAAA,IAAQ,qBAAqB,CAAE,CAAE,KAC9DxB,EAAAA,KAACoI,GAAAA,GAAgBmB,KAAI,CAAClI,SAAOG,EAAAA,IAAQ,gBAAgB,CAAE,CAAE,CAAC,EAC3C,KACjBxB,EAAAA,KAACmG,EAAU,CACTE,QACG7B,EAAO0F,gBACP1F,EAAO0F,cACT,CACF,CAAC,EACC,CAAC,EACH,CAAC,CACF,CAEV,EAEA,GAAehG,GClNTiG,GAA0D,SAAHhG,EAKvD,KAJJiG,EAAQjG,EAARiG,SACAC,EAAelG,EAAfkG,gBACAC,EAAYnG,EAAZmG,aACAC,EAAQpG,EAARoG,SAEAC,KAA8BC,EAAAA,UAA2B,CAAC,CAAC,EAACC,EAAAlL,EAAAA,EAAAgL,EAAA,GAArDG,EAAOD,EAAA,GAAEE,EAAUF,EAAA,GAC1BG,KAAgCJ,EAAAA,UAAmB,CAAEK,KAAM,EAAGC,SAAU,EAAG,CAAC,EAACC,EAAAxL,EAAAA,EAAAqL,EAAA,GAAtEI,EAAQD,EAAA,GAAEE,EAAWF,EAAA,GAC5BG,KAA8BV,EAAAA,UAAkB,EAAK,EAACW,EAAA5L,EAAAA,EAAA2L,EAAA,GAA/CE,EAAOD,EAAA,GAAEE,EAAUF,EAAA,GAEpBG,EAAa,eAAA/F,EAAA9D,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAC,EACpBuI,EACAC,EAAiC,KAAAmB,EAAAC,EAAAd,EAAA,OAAAhJ,EAAAA,EAAA,EAAAI,KAAA,SAAAC,EAAA,eAAAA,EAAAC,KAAAD,EAAAE,KAAA,QAAAF,OAAAA,EAAAE,KAAA,KAENwJ,GAAAA,IACzBtB,EACAC,CACF,EAAC,OAHKmB,OAAAA,EAAYxJ,EAAAM,KAAAN,EAAA2J,GAIGC,GAAAA,QAAO5J,EAAAE,KAAA,KAAO2J,GAAAA,IAAgBL,CAAY,EAAC,OAAAxJ,OAAAA,EAAA8J,GAAA9J,EAAAM,KAA1DmJ,KAAYzJ,EAAA2J,IAAA3J,EAAA8J,GAAgD,IAAI,EAAA9J,EAAAE,KAAA,MAChD6J,GAAAA,IAAaP,CAAY,EAAC,QAA1Cb,EAAO3I,EAAAM,KACbsI,EACED,EAAQqB,IAAI,SAACxH,EAAQ,CAAF,IAAAyH,EAAA,MAAM,CACvBzH,OAAAA,EACAD,YAAW0H,EAAAR,EAAajH,EAAO0H,MAAM,KAAC,MAAAD,IAAA,cAA3BA,EAA8B,CAAC,IAAK,CAAC,CAClD,CAAC,CAAC,CACJ,EAAC,yBAAAjK,EAAAU,KAAA,IAAAb,CAAA,EACF,mBAhBkBsK,EAAAC,EAAA,QAAA5G,EAAAS,MAAA,KAAAC,SAAA,SAkBnBpG,EAAAA,WAAU,UAAM,CACV,CAACsK,GAAY,CAACC,IAClBiB,EAAW,EAAI,EACfC,EAAcnB,EAAUC,CAAe,EAAEgC,KAAK,kBAAMf,EAAW,EAAK,CAAC,GACvE,EAAG,CAAClB,EAAUC,CAAe,CAAC,EAE9B,IAAMiC,EAAkBhC,EACpBK,EACAA,EAAQ4B,OAAO,SAACC,EAAG,CAAF,OAAKA,EAAEhI,OAAOuB,SAAW,UAAU,GAExD,OAAK4E,KAIH3K,EAAAA,KAACyM,GAAAA,GAAO,CACNC,MAAK,GACLrB,QAASA,EACTsB,WAAY,CACVC,QAAS3B,EAASH,KAClBC,SAAUE,EAASF,SACnB8B,OAAQ,GACRC,MAAOR,EAAgB7J,OACvBsK,SAAU,SAACjC,EAAMC,EAAa,CAC5BG,EAAY,CAAEJ,KAAAA,EAAMC,SAAAA,CAAS,CAAC,CAChC,CACF,EACAiC,WAAY,SAAA5G,EAAA,KAAG7B,EAAS6B,EAAT7B,UAAWC,EAAM4B,EAAN5B,OAAM,OAAO+F,GAAQ,YAARA,EAAWhG,EAAWC,CAAM,CAAC,EACpEyI,KAAM,CAAE5E,OAAQ,CAAE,EAClBC,WAAYgE,CAAgB,CAC7B,KAlBMtM,EAAAA,KAACkN,GAAAA,GAAW,CAACtF,KAAK,MAAM,CAAE,CAoBrC,EAEA,GAAeuC,KAEFgD,GAA+B,SAC1C/C,EACAC,EAAiC,KACjCE,EAGoBrE,UAAAzD,OAAA,GAAAyD,UAAA,KAAAkH,OAAAlH,UAAA,GAAG,SAAC3B,EAAWC,EAAQ,CAAF,SACvCxE,EAAAA,KAACkE,GAAkB,CACjBK,UAAWA,EACXC,OAAQA,EACRtD,kBAAmBmJ,GAAe,YAAfA,EAAiB/E,EAAG,CACxC,CAAC,EAEJgF,EAAsBpE,UAAAzD,OAAA,EAAAyD,UAAA,GAAAkH,OAAA,MAClB,CACJhM,IAAK,qBACLC,SACErB,EAAAA,KAACsB,GAAAA,EAAa,CACZC,SAAOC,EAAAA,IAAQ,0BAA0B,EACzCC,UAASC,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAA6D,GAAA,QAAA9D,EAAAA,EAAA,EAAAI,KAAA,SAAA2D,EAAA,eAAAA,EAAAzD,KAAAyD,EAAAxD,KAAA,aACL,CAACkI,GAAY,CAACC,GAAe,CAAA3E,EAAAxD,KAAA,eAAAwD,EAAAvD,OAAA,SAASiL,MAAS,SAAA1H,OAAAA,EAAAxD,KAAA,KACtCmL,GAAAA,IAAiBjD,EAAUC,EAAiBC,CAAY,EAAC,cAAA5E,EAAAvD,OAAA,SAAAuD,EAAApD,IAAA,0BAAAoD,EAAAhD,KAAA,IAAA+C,CAAA,EACvE,EAAC,CACH,EAEHpF,SACE+J,GAAYC,KACVrK,EAAAA,KAACmK,GAAmB,CAClBC,SAAUA,EACVC,gBAAiBA,EACjBE,SAAUA,EACVD,aAAcA,CAAa,CAC5B,EACC,IACR,CAAC,E,sHCvGKgD,GAAU,SACdrM,EACApB,EACA0N,EACAC,EAAmB,OACgB,CACnC,CACEvD,UAAW,OACX1I,SAAOC,EAAAA,IAAQ,eAAe,EAC9BgI,UAAW,gBACXiE,OAAQ,SAACC,EAAGC,EAAS,CACnB,SACE3N,EAAAA,KAAA,KACE6H,QAAS,UAAM,CACb2F,GAAM,MAANA,EAAS,EACTI,EAAAA,QAAQC,KAAK,aAAD7F,OACG/G,EAAS,cAAA+G,OAAanI,EAAU,uCAAAmI,UAAsCU,EAAAA,IACjFC,KAAKC,UAAU+E,EAAKG,aAAa,CACnC,CAAC,EAAA9F,OACC,CAAC,YAAa,QAAQ,EAAEN,SAASiG,EAAKI,iBAAiB,EACnD,cACA,EAAE,CAEV,CACF,EAAE1N,SAEDsN,EAAKG,aAAa,CAClB,CAEP,CACF,EACA,CACE7D,UAAW,SACX1I,SAAOC,EAAAA,IAAQ,uCAAuC,EACtDgI,UAAW,mBACXC,UAAW8D,EAAQS,OAA8B,SAACC,EAAKC,EAAQ,CAC7D,OAAIA,EAAIC,OAAS,EAAED,EAAIC,SAASF,KAC9BA,EAAIC,EAAIC,KAAK,EAAI,CAAExE,KAAMuE,EAAI7M,KAAM,GAE9B4M,CACT,EAAG,CAAC,CAAC,CACP,EACA,CACEhE,UAAW,WACX1I,SAAOC,EAAAA,IAAQ,YAAY,EAC3BgI,UAAW,uBACb,EACA,CACES,UAAW,SACX1I,SAAOC,EAAAA,IAAQ,YAAY,EAC3BgI,UAAW,oBACXiE,OAAQ,SAACC,EAAGU,EAAW,CACrB,SACEpO,EAAAA,KAACqO,GAAAA,EAAY,CACXtI,OAAQqI,EAAOL,kBACfO,YAAY,8BAA8B,CAC3C,CAEL,CACF,EACA,CACErE,UAAW,OACX1I,SAAOC,EAAAA,IAAQ,OAAO,EACtBgI,UAAW,mBACX+E,WAAY,SAACb,EAAGU,EAAQ,CAAF,SAAApG,OAAQoG,EAAOI,iBAAiBC,QAAQ,CAAC,EAAC,KAClE,CAAC,CACF,EAQKC,GAAsE,SAAHvK,EAMnE,KAAAC,EALJnD,EAASkD,EAATlD,UACApB,EAAUsE,EAAVtE,WACA0E,EAASJ,EAATI,UACAoK,EAAMxK,EAANwK,OACAC,EAAWzK,EAAXyK,YAEApE,KAAgCC,EAAAA,UAAkB,EAAK,EAACC,EAAAlL,EAAAA,EAAAgL,EAAA,GAAjDqE,EAAQnE,EAAA,GAAEoE,EAAWpE,EAAA,GAC5BqE,KAAoBC,GAAAA,GAAkB/N,CAAS,EAAvCsM,EAAOwB,EAAPxB,QACRrI,EAAkBC,GAAAA,EAAIC,OAAO,EAArBC,EAAKH,EAALG,MACFP,KAASC,EAAAA,WAAU,EAEvB4E,EAeEpF,EAfFoF,KACAsF,EAcE1K,EAdF0K,IACAC,EAaE3K,EAbF2K,WACAC,EAYE5K,EAZF4K,sBACAC,EAWE7K,EAXF6K,YAAWC,EAWT9K,EAVF+K,UACE1H,EAAIyH,EAAJzH,KACA2H,EAAIF,EAAJE,KACAhO,EAAK8N,EAAL9N,MACAiO,EAAOH,EAAPG,QACAC,EAAIJ,EAAJI,KACAC,EAAEL,EAAFK,GACAC,EAASN,EAATM,UACAzF,EAAcmF,EAAdnF,eAIE0F,KACJ5P,EAAAA,KAAC8H,EAAAA,EAAI,CACHhB,MAAO,CAAE+C,MAAO,UAAWgG,OAAQ,SAAU,EAC7ChI,QAAS,SAACiI,GAAM,CACdA,GAAEC,gBAAgB,EAClBjB,EAAY,SAACkB,GAAK,CAAF,MAAK,CAACA,EAAG,EAC3B,EAAE3P,SAEDwO,EAAW,OAAS,MAAM,CACvB,EAGR,SACE5H,EAAAA,MAACJ,GAAAA,EAAI,CAACG,KAAK,QAAQ/G,UAAU,eAAcI,SAAA,IACzC4G,EAAAA,MAACC,EAAAA,EAAG,CAAA7G,SAAA,IACFL,EAAAA,KAACmH,EAAAA,EAAG,CAAC8I,KAAM,GAAG5P,YACZ4G,EAAAA,MAACO,GAAAA,EAAK,CAACvH,UAAU,QAAQ+G,KAAK,QAAO3G,SAAA,IACnC4G,EAAAA,MAAA,OAAA5G,SAAA,IACEL,EAAAA,KAAC8H,EAAAA,EAAI,CAACF,KAAK,YAAWvH,YAAEmB,EAAAA,IAAQ,YAAY,CAAC,CAAO,EAAC,OACrDxB,EAAAA,KAAC8H,EAAAA,EAAI,CAAChB,MAAO,CAAE+C,MAAO,UAAWqG,YAAa,CAAE,EAAE7P,SAC/C6O,CAAU,CACP,CAAC,EACJ,KACLjI,EAAAA,MAAA,OAAA5G,SAAA,IACEL,EAAAA,KAAC8H,EAAAA,EAAI,CAACF,KAAK,YAAWvH,YAAEmB,EAAAA,IAAQ,OAAO,CAAC,CAAO,EAAC,OAChDxB,EAAAA,KAAC8H,EAAAA,EAAI,CAAChB,MAAO,CAAE+C,MAAO,UAAWqG,YAAa,CAAE,EAAE7P,SAC/C+O,CAAW,CACR,CAAC,EACJ,EACJ,CAAC,CAAC7K,EAAU4L,aACXlJ,EAAAA,MAAAmJ,EAAAA,SAAA,CAAA/P,SAAA,IACEL,EAAAA,KAACwH,GAAAA,EAAK,EAAE,KACRxH,EAAAA,KAAC4J,GAAAA,EAAG,CAACC,MAAM,OAAMxJ,YAAEmB,EAAAA,IAAQ,oBAAoB,CAAC,CAAM,CAAC,EACvD,EAEHmN,IAAWvB,UAAYiD,GAAAA,IAA2B1B,CAAM,EAAI,IAAI,EAC5D,CAAC,CACL,KACL3O,EAAAA,KAACmH,EAAAA,EAAG,CAACL,MAAO,CAAEwJ,WAAY,MAAO,EAAEjQ,YACjCL,EAAAA,KAACwH,GAAAA,EAAK,CAAAnH,UACHyE,GAAM,OAAAV,EAANU,EAAQ2C,gBAAY,MAAArD,IAAA,cAApBA,EAAsBsD,SACrB,6CACF,IACEkH,MACE5O,EAAAA,KAAC2H,GAAAA,GAAM,CACLC,KAAK,UACLZ,KAAK,QACLa,QAAS,kBAAM+G,EAAYrK,CAAS,CAAC,EAAClE,YAErCmB,EAAAA,IAAQ,mCAAmC,CAAC,CACvC,CACT,CACE,CAAC,CACL,CAAC,EACH,KACLyF,EAAAA,MAACC,EAAAA,EAAG,CAACqJ,OAAQ,CAAElJ,GAAI,EAAG,EAAEhH,SAAA,IACtBL,EAAAA,KAACmH,EAAAA,EAAG,CAACE,GAAI,EAAEhH,YACTL,EAAAA,KAACiI,GAAAA,QAAiB,CAACC,UAAW+G,CAAI,CAAE,CAAC,CAClC,KACLhI,EAAAA,MAACE,EAAAA,EAAG,CAACE,GAAI,GAAGhH,SAAA,IACVL,EAAAA,KAAC8H,EAAAA,EAAI,CAACF,KAAK,YAAY4I,OAAM,GAAAnQ,SAAC,WAE9B,CAAM,KACN4G,EAAAA,MAACwJ,GAAAA,EAAS,CACRC,SACE7B,EACI,GACA,CAAE8B,KAAM,EAAGC,WAAY,GAAMC,OAAQjB,CAAW,EAEtDkB,SAAU,CAAEnH,KAAAA,CAAK,EAAEtJ,SAAA,CAElBsJ,GAAQwF,GAAyB,GACjCN,GAAYe,CAAU,EACd,EACVjB,MACC1H,EAAAA,MAACa,EAAAA,EAAI,CAACF,KAAK,YAAWvH,SAAA,IACnBmB,EAAAA,IAAQ,QAAQ,EAAE,YACnBxB,EAAAA,KAACsJ,GAAAA,EAAiB,CAChB1B,KAAK,OACLC,QAAOnG,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAC,IAAA,KAAAC,GAAAiP,GAAA,OAAApP,EAAAA,EAAA,EAAAI,KAAA,SAAAC,GAAA,eAAAA,GAAAC,KAAAD,GAAAE,KAAA,QAAAF,OAAAA,GAAAE,KAAA,KACW8O,EAAAA,IAA8B,CAC9CC,YAAa,GAAFjJ,OAAKzD,EAAUe,EAAE,CAC9B,CAAC,EAAC,OAFIxD,GAAGE,GAAAM,QAGLC,EAAAA,IAAoBT,EAAG,EAAEU,KACrBuO,GAAM1L,EAAM6L,KAAK,CACrB3P,SAAOC,EAAAA,IAAQ,gBAAgB,EAC/BiF,MAAO,IACPJ,WACErG,EAAAA,KAACmR,GAAAA,EAAQ,CACPxE,WAAY,GACZyE,QAAS,CAAEC,SAAU,CAAC,EAAGC,OAAQ,EAAM,EACvCA,OAAQ,GACRhE,QAASA,GAAQrM,EAAWpB,EAAY0N,EAAS,kBAC/CwD,GAAIQ,QAAQ,CAAC,CACf,EACAjJ,WAAYxG,GAAIO,KAAKmP,kBAAmB,CACzC,CAEL,CAAC,GACF,wBAAAxP,GAAAU,KAAA,IAAAb,EAAA,EACF,GAACxB,YAEDmB,EAAAA,IAAQ,oBAAoB,CAAC,CACb,CAAC,EAChB,EAGP,CAACmN,MACA1H,EAAAA,MAAA,OAAKhH,UAAU,oBAAmBI,SAAA,IAChC4G,EAAAA,MAACK,GAAAA,EAAK,CAACC,MAAO,EAAElH,SAAA,CAAC,gBAAWoR,GAAAA,IAAuB7J,CAAI,CAAC,EAAQ,EAC/DA,IAAS,WAAa8H,GAAMC,OAC3B1I,EAAAA,MAACa,EAAAA,EAAI,CACHhB,MAAO,CACL4K,UAAW,QACXC,QAAS,QACTlL,MAAO,MACT,EAAEpG,SAAA,CAEDqP,MACCzI,EAAAA,MAAAmJ,EAAAA,SAAA,CAAA/P,SAAA,CAAE,iBACWL,EAAAA,KAAC8H,EAAAA,EAAI,CAACgJ,SAAQ,GAAAzQ,SAAEqP,CAAE,CAAO,CAAC,EACrC,EAEHC,MAAa1I,EAAAA,MAACa,EAAAA,EAAI,CAAAzH,SAAA,CAAC,iBAAesP,CAAS,EAAO,KACnD3P,EAAAA,KAAA,OAAK,CAAC,EACF,EAEPuP,MACCtI,EAAAA,MAAAmJ,EAAAA,SAAA,CAAA/P,SAAA,IACEL,EAAAA,KAAC8H,EAAAA,EAAI,CAACF,KAAK,YAAYkJ,SAAQ,GAAAzQ,SAC5BkP,KAAOvP,EAAAA,KAAC4G,GAAAA,EAAI,CAACgL,KAAMrC,EAAKlP,SAAEkB,CAAK,CAAO,EAAIA,CAAK,CAC5C,KACNvB,EAAAA,KAAA,OAAK,CAAC,EACN,EAEHkK,MACCjD,EAAAA,MAAAmJ,EAAAA,SAAA,CAAA/P,SAAA,IACEL,EAAAA,KAAC8H,EAAAA,EAAI,CAACF,KAAK,YAAYkJ,SAAQ,GAAAzQ,SAC5B6J,CAAc,CACX,KACNlK,EAAAA,KAAA,OAAK,CAAC,EACN,KAEJA,EAAAA,KAAC8H,EAAAA,EAAI,CACH+J,OAAM,GACN/K,MAAO,CAAE4K,UAAW,QAASC,QAAS,QAASlL,MAAO,MAAO,EAAEpG,SAE9DmP,CAAO,CACJ,EACLC,MACCzP,EAAAA,KAAC8H,EAAAA,EAAI,CACHhB,MAAO,CACL4K,UAAW,QACXC,QAAS,QACTlL,MAAO,MACT,EAAEpG,SAEDoP,CAAI,CACD,CACP,EACE,CACN,EACE,CAAC,EACH,CAAC,EACF,CAEV,EAEA,GAAef,G,wBCtSFoD,GAAkB,SAC7BC,EAEG,KADHC,EAAoB9L,UAAAzD,OAAA,GAAAyD,UAAA,KAAAkH,OAAAlH,UAAA,GAAG,EAEvBvG,KAAuCC,EAAAA,WAAU,EAACqS,EAAAtS,EAAzCoS,CAAS,EAAGG,EAAQD,IAAA,OAAG,GAAEA,EAC5BE,EAAWC,OAAOC,SAASH,CAAQ,EACnCI,EAAQF,OAAOG,MAAMJ,CAAQ,EAAIH,EAAeG,EACtD,OAAOG,CACT,E,wECuBME,GAA2B,SAAHrO,EAAW,KAAAsO,EAAArO,EAAAsO,GAAAA,EAAAvO,CAAA,EACvC,IAAMW,KAASC,EAAAA,WAAU,EACnBO,EAAKwM,GAAgB,YAAY,EACvCzS,KAAwCC,EAAAA,iBAAgB,EAACC,EAAAC,EAAAA,EAAAH,EAAA,GAAlDI,EAAYF,EAAA,GAAEG,EAAeH,EAAA,GACpCiL,KAAsBC,EAAAA,UAAiBhL,EAAaoB,IAAI,KAAK,CAAW,EAAC6J,EAAAlL,EAAAA,EAAAgL,EAAA,GAAlE5J,EAAG8J,EAAA,GAAEiI,EAAMjI,EAAA,GAClBkI,KAA2BC,GAAAA,GAAc,EAAjCC,EAAKF,EAALE,MAAOzH,EAAOuH,EAAPvH,QACfR,KAA8CJ,EAAAA,UAA0B,EAACO,EAAAxL,EAAAA,EAAAqL,EAAA,GAAlER,EAAeW,EAAA,GAAE+H,EAAkB/H,EAAA,GAC1CG,KAAgCV,EAAAA,UAAmB,EAACW,EAAA5L,EAAAA,EAAA2L,EAAA,GAA7Cf,EAAQgB,EAAA,GAAE4H,EAAW5H,EAAA,GACtB6H,KAAMC,EAAAA,QAAuB,EACnCjU,KAAyBC,EAAAA,UAAS,gBAAgB,EAA1CiU,EAAYlU,EAAZkU,aACRC,KAAgD3I,EAAAA,UAAoB,EAAC4I,EAAA7T,EAAAA,EAAA4T,EAAA,GAA9DE,EAAgBD,EAAA,GAAEE,EAAmBF,EAAA,GAC5CG,MAA4B/I,EAAAA,UAC1B,QACF,EAACgJ,GAAAjU,EAAAA,EAAAgU,GAAA,GAFME,GAAMD,GAAA,GAAEE,GAASF,GAAA,GAGxBG,MAAwBnJ,EAAAA,UAAkB,EAAK,EAACoJ,GAAArU,EAAAA,EAAAoU,GAAA,GAAzCnL,GAAIoL,GAAA,GAAEC,GAAOD,GAAA,GACpBE,MAAoDtJ,EAAAA,UAAkB,EAAK,EAACuJ,GAAAxU,EAAAA,EAAAuU,GAAA,GAArEE,GAAkBD,GAAA,GAAEE,GAAqBF,GAAA,GAChDG,MAAsC1J,EAAAA,UAA+B,CAAC,CAAC,EAAC2J,GAAA5U,EAAAA,EAAA2U,GAAA,GAAjEhT,GAAWiT,GAAA,GAAEC,GAAcD,GAAA,GAE5BE,GAAoB,eAAA9O,EAAA9D,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAC,EAAOyD,EAAY,CAAF,IAAAiP,EAAAlS,EAAA,OAAAV,EAAAA,EAAA,EAAAI,KAAA,SAAAC,EAAE,CAAF,cAAAA,EAAAC,KAAAD,EAAAE,KAAE,CAAF,OAAAF,OAAAA,EAAAE,KAAA,EACrB4Q,KACrB0B,GAAAA,SAAyB,mBAAmB,EACzCC,eAAe,CAACnP,CAAE,CAAC,EACnBoP,aAAa,iBAAkB,CAAC,YAAa,SAAU,MAAM,CAAC,EAC9DA,aAAa,UAAW,CAAC,IAAI,CAAC,EAC9B7T,IAAI,CACT,EAAC,OAAA0T,OAAAA,EAAAvS,EAAAM,KANOD,EAAIkS,EAAJlS,KAAIL,EAAAG,OAAA,SAOLE,CAAI,0BAAAL,EAAAU,KAAA,IAAAb,CAAA,EACZ,mBATyBsK,EAAA,QAAA3G,EAAAS,MAAA,KAAAC,SAAA,MAUpByO,GAAc,eAAAvO,EAAA1E,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAA6D,EAAOH,EAAY,CAAF,OAAA3D,EAAAA,EAAA,EAAAI,KAAA,SAAA2D,EAAE,CAAF,cAAAA,EAAAzD,KAAAyD,EAAAxD,KAAE,CAAF,OAAAwD,OAAAA,EAAAxD,KAAA,EAChCoS,GAAqBhP,CAAE,EAAE+G,KAAK,SAAChK,EAAS,CACxCA,GAAI,MAAJA,EAAMI,SACRsQ,EAAmB1Q,EAAK,CAAC,CAAC,EAC1B2Q,KAAY4B,GAAAA,IAAmBvS,EAAK,CAAC,EAAE+H,QAAQ,CAAC,EAEpD,CAAC,EAAC,wBAAA1E,EAAAhD,KAAA,IAAA+C,CAAA,EACH,mBAPmB2G,EAAA,QAAAhG,EAAAH,MAAA,KAAAC,SAAA,MAQd0I,GAAW,eAAAiG,EAAAnT,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAkT,EAAOvQ,EAAsB,KAAAwQ,EAAA,OAAApT,EAAAA,EAAA,EAAAI,KAAA,SAAAiT,EAAA,eAAAA,EAAA/S,KAAA+S,EAAA9S,KAAA,QAC/CqR,KACE0B,GAAAA,IAAuB1Q,EAAW,GAAFyD,QAAKmL,GAAY,OAAA4B,EAAZ5B,EAAc+B,YAAQ,MAAAH,IAAA,cAAtBA,EAAwBzP,KAAM,EAAE,CAAE,CACzE,EACAqO,GAAU,WAAW,EACrBG,GAAQ,EAAI,EAAC,wBAAAkB,EAAAtS,KAAA,IAAAoS,CAAA,EACd,mBANgBK,EAAA,QAAAN,EAAA5O,MAAA,KAAAC,SAAA,MAOXkP,GAAQ,eAAAC,EAAA3T,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAA0T,GAAA,KAAAC,EAAA,OAAA5T,EAAAA,EAAA,EAAAI,KAAA,SAAAyT,EAAA,eAAAA,EAAAvT,KAAAuT,EAAAtT,KAAA,QACfqR,EAAoB,CAClBtO,QAAQoF,GAAe,YAAfA,EAAiBD,WAAY,GACrC7F,UAAW,GACXyE,eAAgB,CAAC,EACjByM,eAAgBC,GAAAA,GAChBC,WAAY,GAAF3N,QAAKmL,GAAY,OAAAoC,EAAZpC,EAAc+B,YAAQ,MAAAK,IAAA,cAAtBA,EAAwBjQ,KAAM,EAAE,CACjD,CAAC,EACDqO,GAAU,QAAQ,EAClBG,GAAQ,EAAI,EAAC,wBAAA0B,EAAA9S,KAAA,IAAA4S,CAAA,EACd,oBAVa,QAAAD,EAAApP,MAAA,KAAAC,SAAA,MAWRxB,GAAM,eAAAkR,EAAAlU,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAiU,EAAOtR,EAAoB,QAAA5C,EAAAA,EAAA,EAAAI,KAAA,SAAA+T,EAAA,eAAAA,EAAA7T,KAAA6T,EAAA5T,KAAA,QACxCqR,EAAoBhP,CAAS,EAC7BoP,GAAU,MAAM,EAChBG,GAAQ,EAAI,EAAC,wBAAAgC,EAAApT,KAAA,IAAAmT,CAAA,EACd,mBAJWE,EAAA,QAAAH,EAAA3P,MAAA,KAAAC,SAAA,MAKNtB,GAAQ,eAAAoR,EAAAtU,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAqU,EAAO1R,EAAoB,KAAA2R,EAAAC,EAAAC,EAAAC,EAAA,OAAA1U,EAAAA,EAAA,EAAAI,KAAA,SAAAuU,GAAA,eAAAA,GAAArU,KAAAqU,GAAApU,KAAA,aACtC,CAACmI,GAAmB,CAAC9F,EAAUe,IAAE,CAAAgR,GAAApU,KAAA,eAAAoU,GAAAnU,OAAA,iBAC/B+T,OAAAA,EAAe,IAAIK,IAAYlM,EAAgBmM,oBAAoB,EACzEN,EAAY,OAAQ3R,EAAUe,EAAE,EAC1B6Q,EAAa,IAAII,IAAYlM,EAAgBoM,kBAAkB,EACrEN,EAAWO,IAAInS,EAAUe,EAAE,EAACgR,GAAApU,KAAA,KAEesS,GAAAA,SACzC,mBACF,EAAEmC,OAAOtM,EAAgB/E,GAAI,CAC3BkR,qBAAsBI,MAAMC,KAAKX,EAAaY,OAAO,CAAC,EACtDL,mBAAoBG,MAAMC,KAAKV,EAAWW,OAAO,CAAC,CACpD,CAAC,EAAC,OAAAV,EAAAE,GAAAhU,KALY+T,EAAkBD,EAAxB/T,KAMJgU,GACF1B,GAAerP,CAAE,EAClB,yBAAAgR,GAAA5T,KAAA,IAAAuT,CAAA,EACF,mBAhBac,EAAA,QAAAf,EAAA/P,MAAA,KAAAC,SAAA,SAkBdpG,EAAAA,WAAU,UAAM,CACd6U,GAAerP,CAAE,CACnB,EAAG,CAACA,CAAE,CAAC,EAEP,IAAM0R,GAAsB,CAC1B5V,IAAK,eACLC,SAAOrB,EAAAA,KAAAoQ,EAAAA,SAAA,CAAA/P,YAAGmB,EAAAA,IAAQ,cAAc,CAAC,CAAG,EACpCnB,SAAU+J,KACRpK,EAAAA,KAACiX,GAAAA,EAAc,CACb7M,SAAUA,EACVG,SAAU,SAAChG,EAAWoK,EAAQ,CAAF,IAAAuI,EAAA,SAC1BlX,EAAAA,KAAC0O,GAAyB,CACxBnK,UAAWA,EACXoK,OAAQA,EACRC,YAAaA,GACb3N,WAAWoJ,GAAe,OAAA6M,EAAf7M,EAAiB8M,WAAO,MAAAD,IAAA,cAAxBA,EAA0B5R,KAAM,EAC3CzF,WAAYyF,CAAG,CAChB,CAAC,EAEJ8R,WAAYxW,IAAQ,eAAiBqS,EAAIrG,QAAUQ,MAAU,CAC9D,EACC,IACN,EACMiK,MAA2BC,EAAAA,SAC/B,kBACEnK,GACE/C,EACAC,EACA,SAACkN,EAAG/K,EAAG,CAAF,SACHxM,EAAAA,KAACkE,GAAkB,CAEjBK,UAAWgT,EACX/S,OAAQgI,EACRtL,kBAAmBoE,EACnBZ,OAAQA,GACRE,SAAUA,GACVC,UAAW,SAAC6O,EAAW,CACrBiB,GAAerP,CAAE,EACjB+O,GAAe,CAAC,CAAC,EACbX,IAAW,UAAUf,EAAO,eAAe,CACjD,CAAE,EAVGnG,EAAElH,EAWR,CAAC,EAEJ2O,EACF,CAAC,EACH,CAAC7J,EAAUC,EAAiB4J,EAAkB,CAChD,EACMuD,MAAyBF,EAAAA,SAC7B,eAAAG,EAAA,SACEC,GAAAA,GACErN,GAAe,OAAAoN,EAAfpN,EAAiB8M,WAAO,MAAAM,IAAA,cAAxBA,EAA0BnS,GAC1B+E,GAAe,YAAfA,EAAiB/E,GACjBnE,EACF,CAAC,EACH,CAACkJ,EAAiBlJ,EAAW,CAC/B,EAEMwW,MAAuBL,EAAAA,SAC3B,eAAAM,EAAA,OACE7W,GACEsJ,GAAe,YAAfA,EAAiBrJ,aACjBqJ,GAAe,OAAAuN,EAAfvN,EAAiB8M,WAAO,MAAAS,IAAA,cAAxBA,EAA0BtS,GAC1B+E,GAAe,YAAfA,EAAiB/E,GACjBnE,EACF,CAAC,EACH,CAACkJ,EAAiBlJ,EAAW,CAC/B,EAEM0W,MAAwBP,EAAAA,SAC5B,oBAAMQ,GAAAA,GAA0B1N,CAAQ,CAAC,EACzC,CAACA,CAAQ,CACX,EAEM2N,MAAOT,EAAAA,SAAQ,UAAM,KAAAU,EACnBD,EAAO,CAAC,EACRtQ,EAAe3C,GAAM,YAANA,EAAQ2C,aAC7B,OAAIA,EAAaC,SAAS,iCAAiC,GACzDqQ,EAAKlK,KAAKmJ,EAAc,EACtBvP,EAAaC,SAAS,sCAAsC,GAC9DqQ,EAAKlK,KAAKwJ,EAAmB,EAC3B5P,EAAaC,SAAS,8BAA8B,GACtDqQ,EAAKlK,KAAK2J,EAAiB,EACzB/P,EAAaC,SAAS,mCAAmC,GAC3DqQ,EAAKlK,KAAKgK,EAAgB,EACxBpQ,EAAaC,SAAS,oCAAoC,GAC5DqQ,EAAKlK,KAAK8J,EAAe,EACtB/W,GAAK+R,GAAMqF,EAACD,EAAK,CAAC,KAAC,MAAAC,IAAA,cAAPA,EAAS5W,GAAG,EACtB2W,CACT,EAAG,CACDjT,GAAM,YAANA,EAAQ2C,aACRuP,GACAK,GACAG,GACAK,EAAgB,CACjB,KAED/X,EAAAA,WAAU,UAAM,CACd,IAAImY,EAAsBxY,EAAaoB,IAAI,cAAc,EACrDoX,EACFvY,EACE,CAAEkB,IAAAA,EAAKI,aAAciX,CAAoB,EACzC,CAAEnX,QAAS,EAAK,CAClB,EAEApB,EAAgB,CAAEkB,IAAAA,CAAI,EAAG,CAAEE,QAAS,EAAK,CAAC,CAE9C,EAAG,CAACF,CAAG,CAAC,EAER,IAAMsX,MACJlY,EAAAA,KAAAoQ,EAAAA,SAAA,CAAA/P,YACE4G,EAAAA,MAACC,EAAAA,EAAG,CAAA7G,SAAA,IACF4G,EAAAA,MAACE,EAAAA,EAAG,CAACE,GAAI,EAAG8Q,GAAI,EAAE9X,SAAA,IACfmB,EAAAA,IAAQ,aAAa,EAAE,KAAG8D,EAC1B+E,GAAe,MAAfA,EAAiBD,YAChBpK,EAAAA,KAACoY,GAAAA,EAAe,CACdlQ,UAAWmC,GAAe,YAAfA,EAAiBD,SAC5BiO,mBAAkB,GAClBC,cAAa,GACd,EAED,0BACD,EACE,KACLtY,EAAAA,KAACmH,EAAAA,EAAG,CAACE,GAAI,GAAI8Q,GAAI,GAAG9X,YAClBL,EAAAA,KAACmR,GAAAA,EAAQ,CACPlR,UAAU,mBACVyM,MAAK,GACL1F,KAAK,QACLsK,OAAQ,GACR3E,WAAY,CACV4L,gBAAiB,EACjBC,gBAAiB,GACjB3L,OAAQ,EACV,EACAS,QAAS,CACP,CACE/L,SAAOC,EAAAA,IAAQ,2BAA2B,EAC1CgI,UAAW,OACXiE,OAAQ,SAACgL,EAAKC,EAAO,CAAF,SACjB1Y,EAAAA,KAAC2H,GAAAA,GAAM,CACLC,KAAK,OACLC,QAAS,UAAM,CACbW,OAAOC,KAAK,eAADT,OAAgB0Q,EAAMpT,EAAE,EAAI,QAAQ,CACjD,EAAEjF,SAEDoY,CAAG,CACE,CAAC,CAEb,EACA,CACElX,SAAOC,EAAAA,IAAQ,6BAA6B,EAC5CgI,UAAW,SACXC,UAAW,CACTkP,QAAS,CACPhP,QAAMnI,EAAAA,IAAQ,oCAAoC,CACpD,EACAoX,UAAW,CACTjP,QAAMnI,EAAAA,IAAQ,sCAAsC,CACtD,EACAqX,SAAU,CACRlP,QAAMnI,EAAAA,IAAQ,4CAA4C,CAC5D,EACAkI,SAAU,CACRC,QAAMnI,EAAAA,IAAQ,qCAAqC,CACrD,CACF,CACF,EACA,CACED,SAAOC,EAAAA,IAAQ,+BAA+B,EAC9CiM,OAAQ,SAACC,EAACoL,EAAoB,KAAhBC,EAASD,EAATC,UACZ,GAAI,CAAC3O,GAAY,CAAC2O,EAAW,MAAO,IACpC,IAAMzT,KAAK0T,GAAAA,IAAgB5O,EAAU2O,CAAS,EACxCE,KAAYC,GAAAA,IAAaH,CAAS,EAClCI,EAAS7T,GAAM2T,EAAUpY,IAAIyE,CAAE,EACrC,OAAI6T,KACKC,GAAAA,IAAkBD,EAAO,CAAC,EAAGA,EAAO,CAAC,CAAC,EAExC,GACT,CACF,CAAC,EAEHE,eAAa7X,EAAAA,IAAQ,eAAe,EACpC8X,WAAY,GACZhR,WAAY+B,GAAe,OAAAoI,EAAfpI,EAAiBkP,kBAAc,MAAA9G,IAAA,cAA/BA,EAAiClG,OAC3C,SAACiN,EAAG,CAAF,OAAKA,EAAEzT,SAAW,WAAW,CACjC,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,CACN,EAGJ,SACE/F,EAAAA,KAACyZ,GAAAA,GAAa,CACZxZ,UAAU,qBACVoG,QAAS6R,GACT7M,QAASA,EACTqO,YAAa,GACbC,QAAS5B,GACT6B,SAAU,CAAEC,uBAAwB,EAAK,EACzCC,YAAa,SAAClZ,EAAK,CAAF,OAAK+R,EAAO/R,CAAG,CAAC,EACjCmZ,aAAcnZ,EACdoZ,sBACE/S,EAAAA,MAACO,GAAAA,EAAK,CAAAnH,SAAA,CACHO,IAAQ,yBACPqG,EAAAA,MAAAmJ,EAAAA,SAAA,CAAA/P,SAAA,EACGyE,GAAM,OAAAV,EAANU,EAAQ2C,gBAAY,MAAArD,IAAA,cAApBA,EAAsBsD,SACrB,gDACF,OACE1H,EAAAA,KAAC2H,GAAAA,GAAM,CAACC,KAAK,UAAUZ,KAAK,SAASa,QAASuN,GAAS/U,YACpDmB,EAAAA,IAAQ,iBAAiB,CAAC,CACrB,KAEVxB,EAAAA,KAACia,GAAAA,EAAM,CACLC,mBAAiB1Y,EAAAA,IAAQ,kBAAkB,EAC3C2Y,qBAAmB3Y,EAAAA,IAAQ,kBAAkB,EAC7C4Y,QAASnG,GACTlH,SAAU,SAACqN,EAAS,CAAF,OAAKlG,GAAsBkG,CAAO,CAAC,CAAC,CACvD,CAAC,EACF,KAEJpa,EAAAA,KAAA,OAAKiT,IAAK,SAACnD,EAAG,CAAF,OAAMmD,EAAIrG,QAAUkD,GAAK1C,MAAS,CAAE,CAAE,CAAC,EAC9C,EACR/M,SAEAgK,MACCrK,EAAAA,KAACqa,GAAAA,EAAgB,CACfhQ,gBAAiBA,EACjB9F,UAAW+O,EACX7K,KAAMA,GACN6R,KAAM5G,GACN6G,aAAc,SAAC9R,EAAS,CACtBqL,GAAQrL,CAAI,EACPA,GAAM8K,EAAoBnG,MAAS,CAC1C,EACAoN,UAAS,eAAAC,EAAA/Y,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAA8Y,EAAOnD,EAAG,CAAF,OAAA5V,EAAAA,EAAA,EAAAI,KAAA,SAAA4Y,EAAE,CAAF,cAAAA,EAAA1Y,KAAA0Y,EAAAzY,KAAE,CAAF,OACjB4R,OAAAA,GAAQ,EAAK,EAAC6G,EAAAzY,KAAA,EACRyS,GAAe4C,EAAEjS,EAAE,EAAC,OACtBoO,KAAW,aACbf,EAAO,oBAAoB,EAC5B,wBAAAgI,EAAAjY,KAAA,IAAAgY,CAAA,EACF,mBAAAE,EAAA,QAAAH,EAAAxU,MAAA,KAAAC,SAAA,KAAC,CACH,CACF,CACY,CAEnB,EAEA,GAAesM,E,mFCnWXqI,EAAyB,iBACzBC,GAA0B,iBAC9B,GAA6C,CAC3C,MAAO,EACT,EACAC,EAAQ,EAAU,OAClB,IAAIC,EAAQF,GAAwB,EAAQ,KAAO,CAAC,EAChDG,GAAQJ,EAAuB,EAAQ,KAAkB,CAAC,EAC1DK,EAAW,EAAQ,KAAkB,EACrCC,GAAQN,EAAuB,EAAQ,KAAQ,CAAC,EAChDO,GAAsC,SAAUC,EAAGvL,EAAG,CACxD,IAAIwL,EAAI,CAAC,EACT,QAAS/D,KAAK8D,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAG9D,CAAC,GAAKzH,EAAE,QAAQyH,CAAC,EAAI,IAAG+D,EAAE/D,CAAC,EAAI8D,EAAE9D,CAAC,GAC/F,GAAI8D,GAAK,MAAQ,OAAO,OAAO,uBAA0B,WAAY,QAASE,EAAI,EAAGhE,EAAI,OAAO,sBAAsB8D,CAAC,EAAGE,EAAIhE,EAAE,OAAQgE,IAClIzL,EAAE,QAAQyH,EAAEgE,CAAC,CAAC,EAAI,GAAK,OAAO,UAAU,qBAAqB,KAAKF,EAAG9D,EAAEgE,CAAC,CAAC,IAAGD,EAAE/D,EAAEgE,CAAC,CAAC,EAAIF,EAAE9D,EAAEgE,CAAC,CAAC,GAElG,OAAOD,CACT,EACA,MAAMxT,GAAO,CAAC0T,EAAIvI,IAAQ,CACxB,GAAI,CACA,SAAAvC,CACF,EAAI8K,EACJC,EAAYL,GAAOI,EAAI,CAAC,UAAU,CAAC,EACrC,MAAME,EAAiBV,EAAM,QAAQ,IAC/BtK,GAAY,OAAOA,GAAa,YACvBuK,GAAM,SAASvK,EAAU,CAAC,aAAc,MAAM,CAAC,EAErDA,EACN,CAACA,CAAQ,CAAC,EAKb,OAAoBsK,EAAM,cAAcG,GAAM,QAAS,OAAO,OAAO,CACnE,IAAKlI,CACP,EAAGwI,EAAW,CACZ,SAAUC,EACV,UAAW,MACb,CAAC,CAAC,CACJ,EACA,IAAIC,GAAWZ,EAAQ,EAAuBC,EAAM,WAAWlT,EAAI,C,6CCxC/D+S,EAAyB,iBACzBC,GAA0B,iBAC9B,GAA6C,CAC3C,MAAO,EACT,EACAC,EAAQ,EAAU,OAClB,IAAIC,EAAQF,GAAwB,EAAQ,KAAO,CAAC,EAChDI,GAAW,EAAQ,KAAkB,EACrCC,EAAQN,EAAuB,EAAQ,KAAQ,CAAC,EAChDO,GAAsC,SAAUC,EAAGvL,EAAG,CACxD,IAAIwL,EAAI,CAAC,EACT,QAAS/D,KAAK8D,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAG9D,CAAC,GAAKzH,EAAE,QAAQyH,CAAC,EAAI,IAAG+D,EAAE/D,CAAC,EAAI8D,EAAE9D,CAAC,GAC/F,GAAI8D,GAAK,MAAQ,OAAO,OAAO,uBAA0B,WAAY,QAASE,EAAI,EAAGhE,EAAI,OAAO,sBAAsB8D,CAAC,EAAGE,EAAIhE,EAAE,OAAQgE,IAClIzL,EAAE,QAAQyH,EAAEgE,CAAC,CAAC,EAAI,GAAK,OAAO,UAAU,qBAAqB,KAAKF,EAAG9D,EAAEgE,CAAC,CAAC,IAAGD,EAAE/D,EAAEgE,CAAC,CAAC,EAAIF,EAAE9D,EAAEgE,CAAC,CAAC,GAElG,OAAOD,CACT,EACA,MAAMM,GAAiB,CAAC,EAAG,EAAG,EAAG,EAAG,CAAC,EAC/BtU,GAAqB0T,EAAM,WAAW,CAACa,EAAO5I,IAAQ,CAC1D,KAAM,CACF,MAAA1L,EAAQ,CACV,EAAIsU,EACJJ,EAAYL,GAAOS,EAAO,CAAC,OAAO,CAAC,EAK/BC,EAAYF,GAAe,SAASrU,CAAK,EAAI,IAAIA,CAAK,GAAK,KACjE,OAAoByT,EAAM,cAAcG,EAAM,QAAS,OAAO,OAAO,CACnE,IAAKlI,CACP,EAAGwI,EAAW,CACZ,UAAWK,CACb,CAAC,CAAC,CACJ,CAAC,EACD,IAAIH,GAAWZ,EAAQ,EAAUzT,E", "sources": ["webpack://labwise-web/./src/components/ReactionTabs/DetectRecord/index.less", "webpack://labwise-web/./src/components/ReactionTabs/DetectRecord/index.tsx", "webpack://labwise-web/./src/types/models/add-method.ts", "webpack://labwise-web/./src/types/models/cameras.ts", "webpack://labwise-web/./src/types/models/check-record.ts", "webpack://labwise-web/./src/types/models/design-status.ts", "webpack://labwise-web/./src/types/models/experiment-status.ts", "webpack://labwise-web/./src/types/models/message-readers.ts", "webpack://labwise-web/./src/types/models/operator-status.ts", "webpack://labwise-web/./src/types/models/robot-tasks.ts", "webpack://labwise-web/./src/types/models/robots.ts", "webpack://labwise-web/./src/types/models/role.ts", "webpack://labwise-web/./src/types/models/status.ts", "webpack://labwise-web/./src/types/models/unit.ts", "webpack://labwise-web/./src/components/ReactionTabs/MyReactionDesignTab/ReactionDesignCard.tsx", "webpack://labwise-web/./src/components/ReactionTabs/MyReactionDesignTab/index.tsx", "webpack://labwise-web/./src/components/ReactionTabs/ReactionLibTab/ProcedureCardInDetailPage.tsx", "webpack://labwise-web/./src/hooks/usePathNumParam.ts", "webpack://labwise-web/./src/pages/reaction/index.tsx", "webpack://labwise-web/./node_modules/antd/lib/typography/Text.js", "webpack://labwise-web/./node_modules/antd/lib/typography/Title.js"], "sourcesContent": ["// extracted by mini-css-extract-plugin\nexport default {};", "import AnalysisRecord from '@/pages/experimental-procedure/conclusion/sections/AnalysisRecord'\nimport { apiSearchExperimentCheck, parseResponseResult } from '@/services'\nimport { OperationCheckResponse } from '@/types/models/check-record'\nimport { getWord } from '@/utils'\nimport { useModel, useParams, useSearchParams } from '@umijs/max'\nimport cs from 'classnames'\nimport { useEffect } from 'react'\nimport TabWithNumber from '../TabWithNumber'\nimport styles from './index.less'\n\nexport default function DetectRecordTab() {\n  const { searchExperimentCheck, experimentCheckList } = useModel('experiment')\n  const [searchParams, setSearchParams] = useSearchParams()\n  const { reactionId } = useParams()\n  useEffect(() => {\n    searchExperimentCheck({ project_reaction_id: reactionId })\n  }, [])\n\n  return (\n    <div className={cs(styles.detectRecord)}>\n      <AnalysisRecord\n        isAnalysisTab={true}\n        analysisData={experimentCheckList}\n        requestEvent={(params) => {\n          searchExperimentCheck({ ...params, project_reaction_id: reactionId })\n          let tab = searchParams.get('tab')\n          if (tab) {\n            setSearchParams(\n              {\n                tab\n              },\n              { replace: true }\n            )\n          }\n        }}\n      />\n    </div>\n  )\n}\n\nexport const getDetectRecordTabConfig = (\n  experimentNo: number,\n  projectId?: number,\n  projectReactionId?: number,\n  updateEvent?: Record<never, never>\n) => ({\n  key: 'detect-record',\n  label: (\n    <TabWithNumber\n      title={getWord('pages.experiment.label.operation.detectRecord')}\n      getNumber={async () => {\n        if (!projectReactionId) return\n        const res: { data: OperationCheckResponse } =\n          await apiSearchExperimentCheck({\n            data: {\n              project_reaction_id: projectReactionId\n            }\n          })\n        if (parseResponseResult(res).ok) return res.data.length\n      }}\n      refetchEvent={updateEvent}\n    />\n  ),\n  children: projectId && projectReactionId ? <DetectRecordTab /> : null\n})\n", "/* tslint:disable */\n/* eslint-disable */\n/**\n * labwise run api\n * lab agent of devices\n *\n * OpenAPI spec version: 0.0.1\n * \n *\n * NOTE: This class is auto generated by the swagger code generator program.\n * https://github.com/swagger-api/swagger-codegen.git\n * Do not edit the class manually.\n */\n/**\n * 加量方式，正常，滴加，批量加等\n * @export\n * @enum {string}\n */\nexport enum AddMethod {\n    Batch = 'batch',\n    Drip = 'drip',\n    Normal = 'normal'\n}\n\n", "/**\n * CameraListResponse\n */\nexport interface CamerasResponse {\n  data?: CameraListResponseDataItem[]\n  meta?: Meta\n  [property: string]: any\n}\n\n/**\n * CameraListResponseDataItem\n */\nexport interface CameraListResponseDataItem {\n  attributes?: Camera\n  id?: number\n  [property: string]: any\n}\n\n/**\n * Camera\n */\nexport interface Camera {\n  createdAt?: Date\n  createdBy?: PurpleCreatedBy\n  lab?: PurpleLab\n  name?: string\n  publishedAt?: Date\n  robot?: FluffyRobot\n  updatedAt?: Date\n  updatedBy?: UpdatedBy11\n  video_live_url?: string\n  videos?: FluffyVideos\n  [property: string]: any\n  usage: string\n}\n\nexport interface PurpleCreatedBy {\n  data?: PurpleData\n  [property: string]: any\n}\n\nexport interface PurpleData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleLab {\n  data?: FluffyData\n  [property: string]: any\n}\n\nexport interface FluffyData {\n  attributes?: PurpleAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleAttributes {\n  cameras?: PurpleCameras\n  createdAt?: Date\n  createdBy?: FluffyCreatedBy\n  lab_no?: string\n  location?: string\n  owner?: Owner\n  publishedAt?: Date\n  robot_agent_url?: string\n  robots?: Robots\n  site?: string\n  updatedAt?: Date\n  updatedBy?: UpdatedBy10\n  [property: string]: any\n}\n\nexport interface PurpleCameras {\n  data?: PurpleDatum[]\n  [property: string]: any\n}\n\nexport interface PurpleDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyCreatedBy {\n  data?: TentacledData\n  [property: string]: any\n}\n\nexport interface TentacledData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface Owner {\n  data?: OwnerData\n  [property: string]: any\n}\n\nexport interface OwnerData {\n  attributes?: FluffyAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyAttributes {\n  blocked?: boolean\n  confirmationToken?: string\n  confirmed?: boolean\n  createdAt?: Date\n  createdBy?: TentacledCreatedBy\n  email?: string\n  manager_id?: string\n  personal_project?: PersonalProject\n  provider?: string\n  resetPasswordToken?: string\n  role?: FluffyRole\n  updatedAt?: Date\n  updatedBy?: UpdatedBy3\n  username?: string\n  [property: string]: any\n}\n\nexport interface TentacledCreatedBy {\n  data?: StickyData\n  [property: string]: any\n}\n\nexport interface StickyData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PersonalProject {\n  data?: PersonalProjectData\n  [property: string]: any\n}\n\nexport interface PersonalProjectData {\n  attributes?: TentacledAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface TentacledAttributes {\n  createdAt?: Date\n  createdBy?: StickyCreatedBy\n  customer?: string\n  delivery_date?: Date\n  end_datetime?: Date\n  name?: string\n  no?: string\n  personal_owner?: PersonalOwner\n  project_compounds?: ProjectCompounds\n  project_members?: ProjectMembers\n  project_status_audits?: ProjectStatusAudits\n  start_datetime?: Date\n  status?: StickyStatus\n  type?: FluffyType\n  updatedAt?: Date\n  updatedBy?: BraggadociousUpdatedBy\n  [property: string]: any\n}\n\nexport interface StickyCreatedBy {\n  data?: IndigoData\n  [property: string]: any\n}\n\nexport interface IndigoData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PersonalOwner {\n  data?: PersonalOwnerData\n  [property: string]: any\n}\n\nexport interface PersonalOwnerData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface ProjectCompounds {\n  data?: ProjectCompoundsDatum[]\n  [property: string]: any\n}\n\nexport interface ProjectCompoundsDatum {\n  attributes?: StickyAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface StickyAttributes {\n  compound?: Compound\n  createdAt?: Date\n  createdBy?: IndecentCreatedBy\n  default_route?: DefaultRoute\n  director_id?: string\n  expanding_route?: ExpandingRoute\n  input_smiles?: string\n  no?: string\n  priority?: Priority\n  project?: PurpleProject\n  project_compound_status_audits?: ProjectCompoundStatusAudits\n  project_routes?: PurpleProjectRoutes\n  retro_processes?: FluffyRetroProcesses\n  searching?: boolean\n  status?: TentacledStatus\n  type?: PurpleType\n  updatedAt?: Date\n  updatedBy?: CunningUpdatedBy\n  [property: string]: any\n}\n\nexport interface Compound {\n  data?: CompoundData\n  [property: string]: any\n}\n\nexport interface CompoundData {\n  attributes?: IndigoAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface IndigoAttributes {\n  createdAt?: Date\n  createdBy?: IndigoCreatedBy\n  smiles?: string\n  updatedAt?: Date\n  updatedBy?: PurpleUpdatedBy\n  [property: string]: any\n}\n\nexport interface IndigoCreatedBy {\n  data?: IndecentData\n  [property: string]: any\n}\n\nexport interface IndecentData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleUpdatedBy {\n  data?: HilariousData\n  [property: string]: any\n}\n\nexport interface HilariousData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface IndecentCreatedBy {\n  data?: AmbitiousData\n  [property: string]: any\n}\n\nexport interface AmbitiousData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface DefaultRoute {\n  data?: DefaultRouteData\n  [property: string]: any\n}\n\nexport interface DefaultRouteData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface ExpandingRoute {\n  data?: ExpandingRouteData\n  [property: string]: any\n}\n\nexport interface ExpandingRouteData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport enum Priority {\n  P0 = 'P0',\n  P1 = 'P1',\n  P2 = 'P2'\n}\n\nexport interface PurpleProject {\n  data?: CunningData\n  [property: string]: any\n}\n\nexport interface CunningData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface ProjectCompoundStatusAudits {\n  data?: ProjectCompoundStatusAuditsDatum[]\n  [property: string]: any\n}\n\nexport interface ProjectCompoundStatusAuditsDatum {\n  attributes?: IndecentAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface IndecentAttributes {\n  createdAt?: Date\n  createdBy?: HilariousCreatedBy\n  from_status?: string\n  note?: string\n  project_compound?: PurpleProjectCompound\n  to_status?: string\n  updatedAt?: Date\n  updatedBy?: FluffyUpdatedBy\n  user_id?: string\n  [property: string]: any\n}\n\nexport interface HilariousCreatedBy {\n  data?: MagentaData\n  [property: string]: any\n}\n\nexport interface MagentaData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleProjectCompound {\n  data?: FriskyData\n  [property: string]: any\n}\n\nexport interface FriskyData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyUpdatedBy {\n  data?: MischievousData\n  [property: string]: any\n}\n\nexport interface MischievousData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleProjectRoutes {\n  data?: FluffyDatum[]\n  [property: string]: any\n}\n\nexport interface FluffyDatum {\n  attributes?: HilariousAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface HilariousAttributes {\n  createdAt?: Date\n  createdBy?: AmbitiousCreatedBy\n  expanding_material?: ExpandingMaterial\n  main_tree?: any\n  name?: string\n  project_compound?: FluffyProjectCompound\n  project_reactions?: ProjectReactions\n  retro_backbone?: PurpleRetroBackbone\n  status?: FluffyStatus\n  updatedAt?: Date\n  updatedBy?: AmbitiousUpdatedBy\n  [property: string]: any\n}\n\nexport interface AmbitiousCreatedBy {\n  data?: BraggadociousData\n  [property: string]: any\n}\n\nexport interface BraggadociousData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface ExpandingMaterial {\n  data?: ExpandingMaterialData\n  [property: string]: any\n}\n\nexport interface ExpandingMaterialData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyProjectCompound {\n  data?: Data1\n  [property: string]: any\n}\n\nexport interface Data1 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface ProjectReactions {\n  data?: ProjectReactionsDatum[]\n  [property: string]: any\n}\n\nexport interface ProjectReactionsDatum {\n  attributes?: AmbitiousAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface AmbitiousAttributes {\n  createdAt?: Date\n  createdBy?: CunningCreatedBy\n  effective_procedures?: any\n  history_procedures?: any\n  project?: FluffyProject\n  project_routes?: FluffyProjectRoutes\n  reaction?: string\n  updatedAt?: Date\n  updatedBy?: TentacledUpdatedBy\n  [property: string]: any\n}\n\nexport interface CunningCreatedBy {\n  data?: Data2\n  [property: string]: any\n}\n\nexport interface Data2 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyProject {\n  data?: Data3\n  [property: string]: any\n}\n\nexport interface Data3 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyProjectRoutes {\n  data?: TentacledDatum[]\n  [property: string]: any\n}\n\nexport interface TentacledDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface TentacledUpdatedBy {\n  data?: Data4\n  [property: string]: any\n}\n\nexport interface Data4 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleRetroBackbone {\n  data?: Data5\n  [property: string]: any\n}\n\nexport interface Data5 {\n  attributes?: CunningAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface CunningAttributes {\n  backbone?: any\n  collected_retro_backbones?: CollectedRetroBackbones\n  createdAt?: Date\n  createdBy?: FriskyCreatedBy\n  full_trees?: any\n  group_conditions?: any\n  group_info?: any\n  known_reaction_rate?: number\n  main_trees?: any\n  min_n_main_tree_steps?: number\n  no?: string\n  novelty_score?: number\n  price_score?: number\n  retro_process?: RetroProcess\n  safety_score?: number\n  score?: number\n  updatedAt?: Date\n  updatedBy?: HilariousUpdatedBy\n  [property: string]: any\n}\n\nexport interface CollectedRetroBackbones {\n  data?: CollectedRetroBackbonesDatum[]\n  [property: string]: any\n}\n\nexport interface CollectedRetroBackbonesDatum {\n  attributes?: MagentaAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface MagentaAttributes {\n  createdAt?: Date\n  createdBy?: MagentaCreatedBy\n  retro_backbone?: FluffyRetroBackbone\n  updatedAt?: Date\n  updatedBy?: StickyUpdatedBy\n  user_id?: string\n  [property: string]: any\n}\n\nexport interface MagentaCreatedBy {\n  data?: Data6\n  [property: string]: any\n}\n\nexport interface Data6 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyRetroBackbone {\n  data?: Data7\n  [property: string]: any\n}\n\nexport interface Data7 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface StickyUpdatedBy {\n  data?: Data8\n  [property: string]: any\n}\n\nexport interface Data8 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FriskyCreatedBy {\n  data?: Data9\n  [property: string]: any\n}\n\nexport interface Data9 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface RetroProcess {\n  data?: RetroProcessData\n  [property: string]: any\n}\n\nexport interface RetroProcessData {\n  attributes?: FriskyAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface FriskyAttributes {\n  createdAt?: Date\n  createdBy?: MischievousCreatedBy\n  creator_id?: string\n  params?: any\n  predict_start_time?: Date\n  project_compound?: TentacledProjectCompound\n  queue_count?: number\n  response?: any\n  retro_backbones?: RetroBackbones\n  retro_id?: string\n  retro_reactions?: RetroReactions\n  search_end_time?: Date\n  search_log?: any\n  search_start_time?: Date\n  status?: PurpleStatus\n  updatedAt?: Date\n  updatedBy?: IndecentUpdatedBy\n  [property: string]: any\n}\n\nexport interface MischievousCreatedBy {\n  data?: Data10\n  [property: string]: any\n}\n\nexport interface Data10 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface TentacledProjectCompound {\n  data?: Data11\n  [property: string]: any\n}\n\nexport interface Data11 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface RetroBackbones {\n  data?: RetroBackbonesDatum[]\n  [property: string]: any\n}\n\nexport interface RetroBackbonesDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface RetroReactions {\n  data?: RetroReactionsDatum[]\n  [property: string]: any\n}\n\nexport interface RetroReactionsDatum {\n  attributes?: MischievousAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface MischievousAttributes {\n  createdAt?: Date\n  createdBy?: BraggadociousCreatedBy\n  hash?: string\n  is_dangerous?: boolean\n  is_known_reaction?: boolean\n  is_selective_risk?: boolean\n  product?: string\n  reactants?: any\n  reliability_score?: number\n  retro_processes?: PurpleRetroProcesses\n  updatedAt?: Date\n  updatedBy?: IndigoUpdatedBy\n  [property: string]: any\n}\n\nexport interface BraggadociousCreatedBy {\n  data?: Data12\n  [property: string]: any\n}\n\nexport interface Data12 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleRetroProcesses {\n  data?: StickyDatum[]\n  [property: string]: any\n}\n\nexport interface StickyDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface IndigoUpdatedBy {\n  data?: Data13\n  [property: string]: any\n}\n\nexport interface Data13 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport enum PurpleStatus {\n  Canceled = 'canceled',\n  Completed = 'completed',\n  Failed = 'failed',\n  Limited = 'limited',\n  Pending = 'pending',\n  Running = 'running'\n}\n\nexport interface IndecentUpdatedBy {\n  data?: Data14\n  [property: string]: any\n}\n\nexport interface Data14 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface HilariousUpdatedBy {\n  data?: Data15\n  [property: string]: any\n}\n\nexport interface Data15 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport enum FluffyStatus {\n  Canceled = 'canceled',\n  Confirmed = 'confirmed',\n  Editing = 'editing',\n  Finished = 'finished'\n}\n\nexport interface AmbitiousUpdatedBy {\n  data?: Data16\n  [property: string]: any\n}\n\nexport interface Data16 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyRetroProcesses {\n  data?: IndigoDatum[]\n  [property: string]: any\n}\n\nexport interface IndigoDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport enum TentacledStatus {\n  Canceled = 'canceled',\n  Created = 'created',\n  Designing = 'designing',\n  Finished = 'finished',\n  Synthesizing = 'synthesizing'\n}\n\nexport enum PurpleType {\n  BuildingBlock = 'building_block',\n  Target = 'target',\n  TempBlock = 'temp_block'\n}\n\nexport interface CunningUpdatedBy {\n  data?: Data17\n  [property: string]: any\n}\n\nexport interface Data17 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface ProjectMembers {\n  data?: ProjectMembersDatum[]\n  [property: string]: any\n}\n\nexport interface ProjectMembersDatum {\n  attributes?: BraggadociousAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface BraggadociousAttributes {\n  createdAt?: Date\n  createdBy?: CreatedBy1\n  project?: TentacledProject\n  role?: PurpleRole\n  updatedAt?: Date\n  updatedBy?: FriskyUpdatedBy\n  user_id?: string\n  [property: string]: any\n}\n\nexport interface CreatedBy1 {\n  data?: Data18\n  [property: string]: any\n}\n\nexport interface Data18 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface TentacledProject {\n  data?: Data19\n  [property: string]: any\n}\n\nexport interface Data19 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleRole {\n  data?: Data20\n  [property: string]: any\n}\n\nexport interface Data20 {\n  attributes?: Attributes1\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes1 {\n  code?: string\n  createdAt?: Date\n  createdBy?: CreatedBy2\n  locale?: string\n  localizations?: Localizations\n  name_zh?: string\n  updatedAt?: Date\n  updatedBy?: MagentaUpdatedBy\n  [property: string]: any\n}\n\nexport interface CreatedBy2 {\n  data?: Data21\n  [property: string]: any\n}\n\nexport interface Data21 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface Localizations {\n  data?: any[]\n  [property: string]: any\n}\n\nexport interface MagentaUpdatedBy {\n  data?: Data22\n  [property: string]: any\n}\n\nexport interface Data22 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FriskyUpdatedBy {\n  data?: Data23\n  [property: string]: any\n}\n\nexport interface Data23 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface ProjectStatusAudits {\n  data?: ProjectStatusAuditsDatum[]\n  [property: string]: any\n}\n\nexport interface ProjectStatusAuditsDatum {\n  attributes?: Attributes2\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes2 {\n  createdAt?: Date\n  createdBy?: CreatedBy3\n  from_status?: string\n  note?: string\n  project?: StickyProject\n  to_status?: string\n  updatedAt?: Date\n  updatedBy?: MischievousUpdatedBy\n  user_id?: string\n  [property: string]: any\n}\n\nexport interface CreatedBy3 {\n  data?: Data24\n  [property: string]: any\n}\n\nexport interface Data24 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface StickyProject {\n  data?: Data25\n  [property: string]: any\n}\n\nexport interface Data25 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface MischievousUpdatedBy {\n  data?: Data26\n  [property: string]: any\n}\n\nexport interface Data26 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport enum StickyStatus {\n  Cancelled = 'cancelled',\n  Created = 'created',\n  Finished = 'finished',\n  Holding = 'holding',\n  Started = 'started'\n}\n\nexport enum FluffyType {\n  Ffs = 'ffs',\n  Fte = 'fte',\n  Personal = 'personal'\n}\n\nexport interface BraggadociousUpdatedBy {\n  data?: Data27\n  [property: string]: any\n}\n\nexport interface Data27 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyRole {\n  data?: Data28\n  [property: string]: any\n}\n\nexport interface Data28 {\n  attributes?: Attributes3\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes3 {\n  createdAt?: Date\n  createdBy?: CreatedBy4\n  description?: string\n  name?: string\n  permissions?: PurplePermissions\n  type?: string\n  updatedAt?: Date\n  updatedBy?: UpdatedBy2\n  users?: PurpleUsers\n  [property: string]: any\n}\n\nexport interface CreatedBy4 {\n  data?: Data29\n  [property: string]: any\n}\n\nexport interface Data29 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurplePermissions {\n  data?: IndecentDatum[]\n  [property: string]: any\n}\n\nexport interface IndecentDatum {\n  attributes?: Attributes4\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes4 {\n  action?: string\n  createdAt?: Date\n  createdBy?: CreatedBy5\n  role?: TentacledRole\n  updatedAt?: Date\n  updatedBy?: UpdatedBy1\n  [property: string]: any\n}\n\nexport interface CreatedBy5 {\n  data?: Data30\n  [property: string]: any\n}\n\nexport interface Data30 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface TentacledRole {\n  data?: Data31\n  [property: string]: any\n}\n\nexport interface Data31 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy1 {\n  data?: Data32\n  [property: string]: any\n}\n\nexport interface Data32 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy2 {\n  data?: Data33\n  [property: string]: any\n}\n\nexport interface Data33 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleUsers {\n  data?: HilariousDatum[]\n  [property: string]: any\n}\n\nexport interface HilariousDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy3 {\n  data?: Data34\n  [property: string]: any\n}\n\nexport interface Data34 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface Robots {\n  data?: RobotsDatum[]\n  [property: string]: any\n}\n\nexport interface RobotsDatum {\n  attributes?: Attributes5\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes5 {\n  cameras?: FluffyCameras\n  createdAt?: Date\n  createdBy?: CreatedBy11\n  lab?: TentacledLab\n  name?: string\n  publishedAt?: Date\n  robot_type?: RobotType\n  status?: IndigoStatus\n  updatedAt?: Date\n  updatedBy?: UpdatedBy9\n  [property: string]: any\n}\n\nexport interface FluffyCameras {\n  data?: AmbitiousDatum[]\n  [property: string]: any\n}\n\nexport interface AmbitiousDatum {\n  attributes?: Attributes6\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes6 {\n  createdAt?: Date\n  createdBy?: CreatedBy6\n  lab?: FluffyLab\n  name?: string\n  publishedAt?: Date\n  robot?: PurpleRobot\n  updatedAt?: Date\n  updatedBy?: UpdatedBy4\n  usage?: string\n  video_live_url?: string\n  videos?: PurpleVideos\n  [property: string]: any\n}\n\nexport interface CreatedBy6 {\n  data?: Data35\n  [property: string]: any\n}\n\nexport interface Data35 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyLab {\n  data?: Data36\n  [property: string]: any\n}\n\nexport interface Data36 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleRobot {\n  data?: Data37\n  [property: string]: any\n}\n\nexport interface Data37 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy4 {\n  data?: Data38\n  [property: string]: any\n}\n\nexport interface Data38 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleVideos {\n  data?: CunningDatum[]\n  [property: string]: any\n}\n\nexport interface CunningDatum {\n  attributes?: Attributes7\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes7 {\n  camera?: CameraObject\n  createdAt?: Date\n  createdBy?: CreatedBy7\n  name?: string\n  publishedAt?: Date\n  task_no?: string\n  updatedAt?: Date\n  updatedBy?: UpdatedBy8\n  video_url?: string\n  [property: string]: any\n}\n\nexport interface CameraObject {\n  data?: CameraData\n  [property: string]: any\n}\n\nexport interface CameraData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface CreatedBy7 {\n  data?: Data39\n  [property: string]: any\n}\n\nexport interface Data39 {\n  attributes?: Attributes8\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes8 {\n  blocked?: boolean\n  createdAt?: Date\n  createdBy?: CreatedBy8\n  email?: string\n  firstname?: string\n  isActive?: boolean\n  lastname?: string\n  preferedLanguage?: string\n  registrationToken?: string\n  resetPasswordToken?: string\n  roles?: Roles\n  updatedAt?: Date\n  updatedBy?: UpdatedBy7\n  username?: string\n  [property: string]: any\n}\n\nexport interface CreatedBy8 {\n  data?: Data40\n  [property: string]: any\n}\n\nexport interface Data40 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface Roles {\n  data?: RolesDatum[]\n  [property: string]: any\n}\n\nexport interface RolesDatum {\n  attributes?: Attributes9\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes9 {\n  code?: string\n  createdAt?: Date\n  createdBy?: CreatedBy9\n  description?: string\n  name?: string\n  permissions?: FluffyPermissions\n  updatedAt?: Date\n  updatedBy?: UpdatedBy6\n  users?: FluffyUsers\n  [property: string]: any\n}\n\nexport interface CreatedBy9 {\n  data?: Data41\n  [property: string]: any\n}\n\nexport interface Data41 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyPermissions {\n  data?: MagentaDatum[]\n  [property: string]: any\n}\n\nexport interface MagentaDatum {\n  attributes?: Attributes10\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes10 {\n  action?: string\n  conditions?: any\n  createdAt?: Date\n  createdBy?: CreatedBy10\n  properties?: any\n  role?: StickyRole\n  subject?: string\n  updatedAt?: Date\n  updatedBy?: UpdatedBy5\n  [property: string]: any\n}\n\nexport interface CreatedBy10 {\n  data?: Data42\n  [property: string]: any\n}\n\nexport interface Data42 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface StickyRole {\n  data?: Data43\n  [property: string]: any\n}\n\nexport interface Data43 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy5 {\n  data?: Data44\n  [property: string]: any\n}\n\nexport interface Data44 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy6 {\n  data?: Data45\n  [property: string]: any\n}\n\nexport interface Data45 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyUsers {\n  data?: FriskyDatum[]\n  [property: string]: any\n}\n\nexport interface FriskyDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy7 {\n  data?: Data46\n  [property: string]: any\n}\n\nexport interface Data46 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy8 {\n  data?: Data47\n  [property: string]: any\n}\n\nexport interface Data47 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface CreatedBy11 {\n  data?: Data48\n  [property: string]: any\n}\n\nexport interface Data48 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface TentacledLab {\n  data?: Data49\n  [property: string]: any\n}\n\nexport interface Data49 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport enum RobotType {\n  Aloha = 'aloha',\n  Sd01 = 'sd-01'\n}\n\nexport enum IndigoStatus {\n  Error = 'error',\n  Holding = 'holding',\n  Idle = 'idle',\n  Working = 'working'\n}\n\nexport interface UpdatedBy9 {\n  data?: Data50\n  [property: string]: any\n}\n\nexport interface Data50 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy10 {\n  data?: Data51\n  [property: string]: any\n}\n\nexport interface Data51 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyRobot {\n  data?: Data52\n  [property: string]: any\n}\n\nexport interface Data52 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy11 {\n  data?: Data53\n  [property: string]: any\n}\n\nexport interface Data53 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyVideos {\n  data?: MischievousDatum[]\n  [property: string]: any\n}\n\nexport interface MischievousDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface Meta {\n  pagination?: Pagination\n  [property: string]: any\n}\n\nexport interface Pagination {\n  page?: number\n  pageCount?: number\n  pageSize?: number\n  total?: number\n  [property: string]: any\n}\n", "export interface OperationCheckRequest {\n  /**\n   * value can be TLC, LCMS\n   */\n  checkMethod?: CheckMethod\n  /**\n   * Checkno\n   */\n  checkNo?: string\n  /**\n   * 中控的状态\n   */\n  checkStatus?: CheckStatus\n  /**\n   * 检测类型，检测类型\n   */\n  checkType?: CheckType\n  /**\n   * Experimentno\n   */\n  experimentNo?: string\n  /**\n   * Operator\n   */\n  operator?: string\n  [property: string]: any\n}\n\n/**\n * value can be TLC, LCMS\n *\n * CheckMethod，An enumeration.\n */\nexport enum CheckMethod {\n  Lcms = 'LCMS',\n  Nmr = 'NMR',\n  Tlc = 'TLC'\n}\n\n/**\n * 中控的状态\n *\n * CheckStatus，An enumeration.\n */\nexport enum CheckStatus {\n  Canceled = 'canceled',\n  Checking = 'checking',\n  Finished = 'finished',\n  Todo = 'todo'\n}\n\n/**\n * 检测类型，检测类型\n *\n * CheckType，An enumeration.\n */\nexport enum CheckType {\n  F = 'F',\n  M = 'M'\n}\n\n/**\n * Response Search Experiment Checks Api Experiment Check Search Post\n *\n * OperationCheckVo\n */\nexport interface OperationCheckResponse {\n  /**\n   * value can be TLC, LCMS\n   */\n  checkMethod?: CheckMethod\n  /**\n   * Checkno\n   */\n  checkNo?: string\n  /**\n   * 中控的状态，running, completed\n   */\n  checkStatus?: CheckStatus\n  /**\n   * 检测类型，检测类型\n   */\n  checkType?: CheckType\n  /**\n   * 完成时间\n   */\n  endTime?: Date\n  /**\n   * Experimentno\n   */\n  experimentNo?: string\n  /**\n   * Operator\n   */\n  operator?: string\n  /**\n   * 发起时间\n   */\n  startTime?: Date\n  [property: string]: any\n}\n", "/* tslint:disable */\n/* eslint-disable */\n/**\n * labwise run api\n * lab agent of devices\n *\n * OpenAPI spec version: 0.0.1\n *\n *\n * NOTE: This class is auto generated by the swagger code generator program.\n * https://github.com/swagger-api/swagger-codegen.git\n * Do not edit the class manually.\n */\n/**\n * An enumeration.\n * @export\n * @enum {string}\n */\nexport enum DesignStatus {\n  Canceled = 'canceled',\n  Created = 'created',\n  Published = 'published',\n  Validated = 'validated'\n}\n", "/* tslint:disable */\n/* eslint-disable */\n/**\n * labwise run api\n * lab agent of devices\n *\n * OpenAPI spec version: 0.0.1\n *\n *\n * NOTE: This class is auto generated by the swagger code generator program.\n * https://github.com/swagger-api/swagger-codegen.git\n * Do not edit the class manually.\n */\n/**\n * An enumeration.\n * @export\n * @enum {string}\n */\nexport enum ExperimentStatus {\n  Created = 'created',\n  Ready = 'ready',\n  Running = 'running',\n  Incident = 'incident',\n  Hold = 'hold',\n  Failed = 'failed',\n  Completed = 'completed',\n  Success = 'success'\n}\n", "/**\n * MessageReaderListResponse\n */\nexport interface MessageReadersResponse {\n  data?: MessageReaderListResponseDataItem[]\n  meta?: Meta\n  [property: string]: any\n}\n\n/**\n * MessageReaderListResponseDataItem\n */\nexport interface MessageReaderListResponseDataItem {\n  attributes?: MessageReader\n  id?: number\n  [property: string]: any\n}\n\n/**\n * MessageReader\n */\nexport interface MessageReader {\n  createdAt?: Date\n  createdBy?: PurpleCreatedBy\n  publishedAt?: Date\n  readed?: boolean\n  system_message?: PurpleSystemMessage\n  updatedAt?: Date\n  updatedBy?: IndecentUpdatedBy\n  user_id?: string\n  [property: string]: any\n}\n\nexport interface PurpleCreatedBy {\n  data?: PurpleData\n  [property: string]: any\n}\n\nexport interface PurpleData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleSystemMessage {\n  data?: FluffyData\n  [property: string]: any\n}\n\nexport interface FluffyData {\n  attributes?: PurpleAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleAttributes {\n  createdAt?: Date\n  createdBy?: MessageReadersFluffyCreatedBy\n  event_data?: any\n  event_level?: EventLevel\n  event_type?: string\n  experiment_no?: string\n  message?: string\n  message_level?: MessageLevel\n  project_no?: string\n  publishedAt?: Date\n  read_items?: ReadItems\n  updatedAt?: Date\n  updatedBy?: IndigoUpdatedBy\n  [property: string]: any\n}\n\nexport interface MessageReadersFluffyCreatedBy {\n  data?: TentacledData\n  [property: string]: any\n}\n\nexport interface TentacledData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport enum EventLevel {\n  Experiment = 'experiment',\n  Global = 'global',\n  Project = 'project'\n}\n\nexport enum MessageLevel {\n  I = 'I',\n  Ii = 'II',\n  Iii = 'III'\n}\n\nexport interface ReadItems {\n  data?: ReadItemsDatum[]\n  [property: string]: any\n}\n\nexport interface ReadItemsDatum {\n  attributes?: MessageReadersFluffyAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface MessageReadersFluffyAttributes {\n  createdAt?: Date\n  createdBy?: TentacledCreatedBy\n  publishedAt?: Date\n  readed?: boolean\n  system_message?: FluffySystemMessage\n  updatedAt?: Date\n  updatedBy?: StickyUpdatedBy\n  user_id?: string\n  [property: string]: any\n}\n\nexport interface TentacledCreatedBy {\n  data?: StickyData\n  [property: string]: any\n}\n\nexport interface StickyData {\n  attributes?: TentacledAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface TentacledAttributes {\n  blocked?: boolean\n  createdAt?: Date\n  createdBy?: StickyCreatedBy\n  email?: string\n  firstname?: string\n  isActive?: boolean\n  lastname?: string\n  preferedLanguage?: string\n  registrationToken?: string\n  resetPasswordToken?: string\n  roles?: Roles\n  updatedAt?: Date\n  updatedBy?: TentacledUpdatedBy\n  username?: string\n  [property: string]: any\n}\n\nexport interface StickyCreatedBy {\n  data?: IndigoData\n  [property: string]: any\n}\n\nexport interface IndigoData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface Roles {\n  data?: RolesDatum[]\n  [property: string]: any\n}\n\nexport interface RolesDatum {\n  attributes?: StickyAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface StickyAttributes {\n  code?: string\n  createdAt?: Date\n  createdBy?: IndigoCreatedBy\n  description?: string\n  name?: string\n  permissions?: Permissions\n  updatedAt?: Date\n  updatedBy?: FluffyUpdatedBy\n  users?: Users\n  [property: string]: any\n}\n\nexport interface IndigoCreatedBy {\n  data?: IndecentData\n  [property: string]: any\n}\n\nexport interface IndecentData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface Permissions {\n  data?: PermissionsDatum[]\n  [property: string]: any\n}\n\nexport interface PermissionsDatum {\n  attributes?: IndigoAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface IndigoAttributes {\n  action?: string\n  conditions?: any\n  createdAt?: Date\n  createdBy?: IndecentCreatedBy\n  properties?: any\n  role?: Role\n  subject?: string\n  updatedAt?: Date\n  updatedBy?: PurpleUpdatedBy\n  [property: string]: any\n}\n\nexport interface IndecentCreatedBy {\n  data?: HilariousData\n  [property: string]: any\n}\n\nexport interface HilariousData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface Role {\n  data?: RoleData\n  [property: string]: any\n}\n\nexport interface RoleData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleUpdatedBy {\n  data?: AmbitiousData\n  [property: string]: any\n}\n\nexport interface AmbitiousData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyUpdatedBy {\n  data?: CunningData\n  [property: string]: any\n}\n\nexport interface CunningData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface Users {\n  data?: UsersDatum[]\n  [property: string]: any\n}\n\nexport interface UsersDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface TentacledUpdatedBy {\n  data?: MagentaData\n  [property: string]: any\n}\n\nexport interface MagentaData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffySystemMessage {\n  data?: FriskyData\n  [property: string]: any\n}\n\nexport interface FriskyData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface StickyUpdatedBy {\n  data?: MischievousData\n  [property: string]: any\n}\n\nexport interface MischievousData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface IndecentUpdatedBy {\n  data?: MessageReadersData1\n  [property: string]: any\n}\n\nexport interface MessageReadersData1 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface Meta {\n  pagination?: Pagination\n  [property: string]: any\n}\n\nexport interface Pagination {\n  page?: number\n  pageCount?: number\n  pageSize?: number\n  total?: number\n  [property: string]: any\n}\n", "/* tslint:disable */\n/* eslint-disable */\n/**\n * labwise run api\n * lab agent of devices\n *\n * OpenAPI spec version: 0.0.1\n * \n *\n * NOTE: This class is auto generated by the swagger code generator program.\n * https://github.com/swagger-api/swagger-codegen.git\n * Do not edit the class manually.\n */\n/**\n * An enumeration.\n * @export\n * @enum {string}\n */\nexport enum OperatorStatus {\n    AVAILABLE = 'AVAILABLE',\n    LEAVE = 'LEAVE',\n    VACATE = 'VACATE'\n}\n\n", "/**\n * Response Get Task By Operator Api Robot Tasks  Robot Name  Get\n *\n * RobotTask\n */\nexport interface RobotTasksResponse {\n  /**\n   * 任务的结束时间\n   */\n  endTime?: Date\n  /**\n   * 实验编号\n   */\n  experimentNo: string\n  /**\n   * 操作详情\n   */\n  operations?: RobotOperation[]\n  /**\n   * 操作者\n   */\n  operator: string\n  /**\n   * 任务的开始时间\n   */\n  startTime?: Date\n  /**\n   * 任务状态\n   */\n  status: Status | 'completed' | 'running' | 'working'\n  /**\n   * 任务名称\n   */\n  taskName: string\n  /**\n   * 任务编号\n   */\n  taskNo: string\n  [property: string]: any\n\n  project_no: string\n  project_reaction_id: string\n}\n\n/**\n * RobotOperation\n */\nexport interface RobotOperation {\n  /**\n   * 操作描述\n   */\n  operation_description: string\n  /**\n   * 结束时间\n   */\n  operation_end_time?: Date\n  /**\n   * 操作名称\n   */\n  operation_name: string\n  /**\n   * 开始时间\n   */\n  operation_start_time?: Date\n  [property: string]: any\n}\n\n/**\n * 任务状态\n *\n * TaskStatusEnum，An enumeration.\n */\nexport enum TaskStatus {\n  Canceled = 'canceled',\n  Completed = 'completed',\n  Running = 'running',\n  Todo = 'todo'\n}\n", "/**\n * RobotResponse\n */\nexport interface Response {\n  data?: RobotResponseDataObject\n  meta?: { [key: string]: any }\n  [property: string]: any\n}\n\n/**\n * RobotResponseDataObject\n */\nexport interface RobotResponseDataObject {\n  attributes?: Robot\n  id?: number\n  [property: string]: any\n}\n\n/**\n * Robot\n */\nexport interface Robot {\n  cameras?: PurpleCameras\n  createdAt?: Date\n  createdBy?: PurpleCreatedBy\n  lab?: PurpleLab\n  name: string\n  publishedAt?: Date\n  robot_type: RobotType\n  status: IndigoStatus\n  updatedAt?: Date\n  updatedBy?: UpdatedBy11\n  [property: string]: any\n}\n\nexport interface PurpleCameras {\n  data?: PurpleDatum[]\n  [property: string]: any\n}\n\nexport interface PurpleDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleCreatedBy {\n  data?: PurpleData\n  [property: string]: any\n}\n\nexport interface PurpleData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleLab {\n  data?: FluffyData\n  [property: string]: any\n}\n\nexport interface FluffyData {\n  attributes?: PurpleAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleAttributes {\n  cameras?: FluffyCameras\n  createdAt?: Date\n  createdBy?: FluffyCreatedBy\n  lab_no?: string\n  location?: string\n  owner?: Owner\n  publishedAt?: Date\n  robot_agent_url?: string\n  robots?: Robots\n  site?: string\n  updatedAt?: Date\n  updatedBy?: UpdatedBy10\n  [property: string]: any\n}\n\nexport interface FluffyCameras {\n  data?: FluffyDatum[]\n  [property: string]: any\n}\n\nexport interface FluffyDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyCreatedBy {\n  data?: TentacledData\n  [property: string]: any\n}\n\nexport interface TentacledData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface Owner {\n  data?: OwnerData\n  [property: string]: any\n}\n\nexport interface OwnerData {\n  attributes?: FluffyAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyAttributes {\n  blocked?: boolean\n  confirmationToken?: string\n  confirmed?: boolean\n  createdAt?: Date\n  createdBy?: TentacledCreatedBy\n  email?: string\n  manager_id?: string\n  personal_project?: PersonalProject\n  provider?: string\n  resetPasswordToken?: string\n  role?: FluffyRole\n  updatedAt?: Date\n  updatedBy?: UpdatedBy3\n  username?: string\n  [property: string]: any\n}\n\nexport interface TentacledCreatedBy {\n  data?: StickyData\n  [property: string]: any\n}\n\nexport interface StickyData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PersonalProject {\n  data?: PersonalProjectData\n  [property: string]: any\n}\n\nexport interface PersonalProjectData {\n  attributes?: TentacledAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface TentacledAttributes {\n  createdAt?: Date\n  createdBy?: StickyCreatedBy\n  customer?: string\n  delivery_date?: Date\n  end_datetime?: Date\n  name?: string\n  no?: string\n  personal_owner?: PersonalOwner\n  project_compounds?: ProjectCompounds\n  project_members?: ProjectMembers\n  project_status_audits?: ProjectStatusAudits\n  start_datetime?: Date\n  status?: StickyStatus\n  type?: FluffyType\n  updatedAt?: Date\n  updatedBy?: BraggadociousUpdatedBy\n  [property: string]: any\n}\n\nexport interface StickyCreatedBy {\n  data?: IndigoData\n  [property: string]: any\n}\n\nexport interface IndigoData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PersonalOwner {\n  data?: PersonalOwnerData\n  [property: string]: any\n}\n\nexport interface PersonalOwnerData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface ProjectCompounds {\n  data?: ProjectCompoundsDatum[]\n  [property: string]: any\n}\n\nexport interface ProjectCompoundsDatum {\n  attributes?: StickyAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface StickyAttributes {\n  compound?: Compound\n  createdAt?: Date\n  createdBy?: IndecentCreatedBy\n  default_route?: DefaultRoute\n  director_id?: string\n  expanding_route?: ExpandingRoute\n  input_smiles?: string\n  no?: string\n  priority?: Priority\n  project?: PurpleProject\n  project_compound_status_audits?: ProjectCompoundStatusAudits\n  project_routes?: PurpleProjectRoutes\n  retro_processes?: FluffyRetroProcesses\n  searching?: boolean\n  status?: TentacledStatus\n  type?: PurpleType\n  updatedAt?: Date\n  updatedBy?: CunningUpdatedBy\n  [property: string]: any\n}\n\nexport interface Compound {\n  data?: CompoundData\n  [property: string]: any\n}\n\nexport interface CompoundData {\n  attributes?: IndigoAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface IndigoAttributes {\n  createdAt?: Date\n  createdBy?: IndigoCreatedBy\n  smiles?: string\n  updatedAt?: Date\n  updatedBy?: PurpleUpdatedBy\n  [property: string]: any\n}\n\nexport interface IndigoCreatedBy {\n  data?: IndecentData\n  [property: string]: any\n}\n\nexport interface IndecentData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleUpdatedBy {\n  data?: HilariousData\n  [property: string]: any\n}\n\nexport interface HilariousData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface IndecentCreatedBy {\n  data?: AmbitiousData\n  [property: string]: any\n}\n\nexport interface AmbitiousData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface DefaultRoute {\n  data?: DefaultRouteData\n  [property: string]: any\n}\n\nexport interface DefaultRouteData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface ExpandingRoute {\n  data?: ExpandingRouteData\n  [property: string]: any\n}\n\nexport interface ExpandingRouteData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport enum Priority {\n  P0 = 'P0',\n  P1 = 'P1',\n  P2 = 'P2'\n}\n\nexport interface PurpleProject {\n  data?: CunningData\n  [property: string]: any\n}\n\nexport interface CunningData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface ProjectCompoundStatusAudits {\n  data?: ProjectCompoundStatusAuditsDatum[]\n  [property: string]: any\n}\n\nexport interface ProjectCompoundStatusAuditsDatum {\n  attributes?: IndecentAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface IndecentAttributes {\n  createdAt?: Date\n  createdBy?: HilariousCreatedBy\n  from_status?: string\n  note?: string\n  project_compound?: PurpleProjectCompound\n  to_status?: string\n  updatedAt?: Date\n  updatedBy?: FluffyUpdatedBy\n  user_id?: string\n  [property: string]: any\n}\n\nexport interface HilariousCreatedBy {\n  data?: MagentaData\n  [property: string]: any\n}\n\nexport interface MagentaData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleProjectCompound {\n  data?: FriskyData\n  [property: string]: any\n}\n\nexport interface FriskyData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyUpdatedBy {\n  data?: MischievousData\n  [property: string]: any\n}\n\nexport interface MischievousData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleProjectRoutes {\n  data?: TentacledDatum[]\n  [property: string]: any\n}\n\nexport interface TentacledDatum {\n  attributes?: HilariousAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface HilariousAttributes {\n  createdAt?: Date\n  createdBy?: AmbitiousCreatedBy\n  expanding_material?: ExpandingMaterial\n  main_tree?: any\n  name?: string\n  project_compound?: FluffyProjectCompound\n  project_reactions?: ProjectReactions\n  retro_backbone?: PurpleRetroBackbone\n  status?: FluffyStatus\n  updatedAt?: Date\n  updatedBy?: AmbitiousUpdatedBy\n  [property: string]: any\n}\n\nexport interface AmbitiousCreatedBy {\n  data?: BraggadociousData\n  [property: string]: any\n}\n\nexport interface BraggadociousData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface ExpandingMaterial {\n  data?: ExpandingMaterialData\n  [property: string]: any\n}\n\nexport interface ExpandingMaterialData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyProjectCompound {\n  data?: Data1\n  [property: string]: any\n}\n\nexport interface Data1 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface ProjectReactions {\n  data?: ProjectReactionsDatum[]\n  [property: string]: any\n}\n\nexport interface ProjectReactionsDatum {\n  attributes?: AmbitiousAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface AmbitiousAttributes {\n  createdAt?: Date\n  createdBy?: CunningCreatedBy\n  effective_procedures?: any\n  history_procedures?: any\n  project?: FluffyProject\n  project_routes?: FluffyProjectRoutes\n  reaction?: string\n  updatedAt?: Date\n  updatedBy?: TentacledUpdatedBy\n  [property: string]: any\n}\n\nexport interface CunningCreatedBy {\n  data?: Data2\n  [property: string]: any\n}\n\nexport interface Data2 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyProject {\n  data?: Data3\n  [property: string]: any\n}\n\nexport interface Data3 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyProjectRoutes {\n  data?: StickyDatum[]\n  [property: string]: any\n}\n\nexport interface StickyDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface TentacledUpdatedBy {\n  data?: Data4\n  [property: string]: any\n}\n\nexport interface Data4 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleRetroBackbone {\n  data?: Data5\n  [property: string]: any\n}\n\nexport interface Data5 {\n  attributes?: CunningAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface CunningAttributes {\n  backbone?: any\n  collected_retro_backbones?: CollectedRetroBackbones\n  createdAt?: Date\n  createdBy?: FriskyCreatedBy\n  full_trees?: any\n  group_conditions?: any\n  group_info?: any\n  known_reaction_rate?: number\n  main_trees?: any\n  min_n_main_tree_steps?: number\n  no?: string\n  novelty_score?: number\n  price_score?: number\n  retro_process?: RetroProcess\n  safety_score?: number\n  score?: number\n  updatedAt?: Date\n  updatedBy?: HilariousUpdatedBy\n  [property: string]: any\n}\n\nexport interface CollectedRetroBackbones {\n  data?: CollectedRetroBackbonesDatum[]\n  [property: string]: any\n}\n\nexport interface CollectedRetroBackbonesDatum {\n  attributes?: MagentaAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface MagentaAttributes {\n  createdAt?: Date\n  createdBy?: MagentaCreatedBy\n  retro_backbone?: FluffyRetroBackbone\n  updatedAt?: Date\n  updatedBy?: StickyUpdatedBy\n  user_id?: string\n  [property: string]: any\n}\n\nexport interface MagentaCreatedBy {\n  data?: Data6\n  [property: string]: any\n}\n\nexport interface Data6 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyRetroBackbone {\n  data?: Data7\n  [property: string]: any\n}\n\nexport interface Data7 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface StickyUpdatedBy {\n  data?: Data8\n  [property: string]: any\n}\n\nexport interface Data8 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FriskyCreatedBy {\n  data?: Data9\n  [property: string]: any\n}\n\nexport interface Data9 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface RetroProcess {\n  data?: RetroProcessData\n  [property: string]: any\n}\n\nexport interface RetroProcessData {\n  attributes?: FriskyAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface FriskyAttributes {\n  createdAt?: Date\n  createdBy?: MischievousCreatedBy\n  creator_id?: string\n  params?: any\n  predict_start_time?: Date\n  project_compound?: TentacledProjectCompound\n  queue_count?: number\n  response?: any\n  retro_backbones?: RetroBackbones\n  retro_id?: string\n  retro_reactions?: RetroReactions\n  search_end_time?: Date\n  search_log?: any\n  search_start_time?: Date\n  status?: PurpleStatus\n  updatedAt?: Date\n  updatedBy?: IndecentUpdatedBy\n  [property: string]: any\n}\n\nexport interface MischievousCreatedBy {\n  data?: Data10\n  [property: string]: any\n}\n\nexport interface Data10 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface TentacledProjectCompound {\n  data?: Data11\n  [property: string]: any\n}\n\nexport interface Data11 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface RetroBackbones {\n  data?: RetroBackbonesDatum[]\n  [property: string]: any\n}\n\nexport interface RetroBackbonesDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface RetroReactions {\n  data?: RetroReactionsDatum[]\n  [property: string]: any\n}\n\nexport interface RetroReactionsDatum {\n  attributes?: MischievousAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface MischievousAttributes {\n  createdAt?: Date\n  createdBy?: BraggadociousCreatedBy\n  hash?: string\n  is_dangerous?: boolean\n  is_known_reaction?: boolean\n  is_selective_risk?: boolean\n  product?: string\n  reactants?: any\n  reliability_score?: number\n  retro_processes?: PurpleRetroProcesses\n  updatedAt?: Date\n  updatedBy?: IndigoUpdatedBy\n  [property: string]: any\n}\n\nexport interface BraggadociousCreatedBy {\n  data?: Data12\n  [property: string]: any\n}\n\nexport interface Data12 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleRetroProcesses {\n  data?: IndigoDatum[]\n  [property: string]: any\n}\n\nexport interface IndigoDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface IndigoUpdatedBy {\n  data?: Data13\n  [property: string]: any\n}\n\nexport interface Data13 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport enum PurpleStatus {\n  Canceled = 'canceled',\n  Completed = 'completed',\n  Failed = 'failed',\n  Limited = 'limited',\n  Pending = 'pending',\n  Running = 'running'\n}\n\nexport interface IndecentUpdatedBy {\n  data?: Data14\n  [property: string]: any\n}\n\nexport interface Data14 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface HilariousUpdatedBy {\n  data?: Data15\n  [property: string]: any\n}\n\nexport interface Data15 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport enum FluffyStatus {\n  Canceled = 'canceled',\n  Confirmed = 'confirmed',\n  Editing = 'editing',\n  Finished = 'finished'\n}\n\nexport interface AmbitiousUpdatedBy {\n  data?: Data16\n  [property: string]: any\n}\n\nexport interface Data16 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyRetroProcesses {\n  data?: IndecentDatum[]\n  [property: string]: any\n}\n\nexport interface IndecentDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport enum TentacledStatus {\n  Canceled = 'canceled',\n  Created = 'created',\n  Designing = 'designing',\n  Finished = 'finished',\n  Synthesizing = 'synthesizing'\n}\n\nexport enum PurpleType {\n  BuildingBlock = 'building_block',\n  Target = 'target',\n  TempBlock = 'temp_block'\n}\n\nexport interface CunningUpdatedBy {\n  data?: Data17\n  [property: string]: any\n}\n\nexport interface Data17 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface ProjectMembers {\n  data?: ProjectMembersDatum[]\n  [property: string]: any\n}\n\nexport interface ProjectMembersDatum {\n  attributes?: BraggadociousAttributes\n  id?: number\n  [property: string]: any\n}\n\nexport interface BraggadociousAttributes {\n  createdAt?: Date\n  createdBy?: CreatedBy1\n  project?: TentacledProject\n  role?: PurpleRole\n  updatedAt?: Date\n  updatedBy?: FriskyUpdatedBy\n  user_id?: string\n  [property: string]: any\n}\n\nexport interface CreatedBy1 {\n  data?: Data18\n  [property: string]: any\n}\n\nexport interface Data18 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface TentacledProject {\n  data?: Data19\n  [property: string]: any\n}\n\nexport interface Data19 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleRole {\n  data?: Data20\n  [property: string]: any\n}\n\nexport interface Data20 {\n  attributes?: Attributes1\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes1 {\n  code?: string\n  createdAt?: Date\n  createdBy?: CreatedBy2\n  locale?: string\n  localizations?: Localizations\n  name_zh?: string\n  updatedAt?: Date\n  updatedBy?: MagentaUpdatedBy\n  [property: string]: any\n}\n\nexport interface CreatedBy2 {\n  data?: Data21\n  [property: string]: any\n}\n\nexport interface Data21 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface Localizations {\n  data?: any[]\n  [property: string]: any\n}\n\nexport interface MagentaUpdatedBy {\n  data?: Data22\n  [property: string]: any\n}\n\nexport interface Data22 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FriskyUpdatedBy {\n  data?: Data23\n  [property: string]: any\n}\n\nexport interface Data23 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface ProjectStatusAudits {\n  data?: ProjectStatusAuditsDatum[]\n  [property: string]: any\n}\n\nexport interface ProjectStatusAuditsDatum {\n  attributes?: Attributes2\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes2 {\n  createdAt?: Date\n  createdBy?: CreatedBy3\n  from_status?: string\n  note?: string\n  project?: StickyProject\n  to_status?: string\n  updatedAt?: Date\n  updatedBy?: MischievousUpdatedBy\n  user_id?: string\n  [property: string]: any\n}\n\nexport interface CreatedBy3 {\n  data?: Data24\n  [property: string]: any\n}\n\nexport interface Data24 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface StickyProject {\n  data?: Data25\n  [property: string]: any\n}\n\nexport interface Data25 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface MischievousUpdatedBy {\n  data?: Data26\n  [property: string]: any\n}\n\nexport interface Data26 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport enum StickyStatus {\n  Cancelled = 'cancelled',\n  Created = 'created',\n  Finished = 'finished',\n  Holding = 'holding',\n  Started = 'started'\n}\n\nexport enum FluffyType {\n  Ffs = 'ffs',\n  Fte = 'fte',\n  Personal = 'personal'\n}\n\nexport interface BraggadociousUpdatedBy {\n  data?: Data27\n  [property: string]: any\n}\n\nexport interface Data27 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyRole {\n  data?: Data28\n  [property: string]: any\n}\n\nexport interface Data28 {\n  attributes?: Attributes3\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes3 {\n  createdAt?: Date\n  createdBy?: CreatedBy4\n  description?: string\n  name?: string\n  permissions?: PurplePermissions\n  type?: string\n  updatedAt?: Date\n  updatedBy?: UpdatedBy2\n  users?: PurpleUsers\n  [property: string]: any\n}\n\nexport interface CreatedBy4 {\n  data?: Data29\n  [property: string]: any\n}\n\nexport interface Data29 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurplePermissions {\n  data?: HilariousDatum[]\n  [property: string]: any\n}\n\nexport interface HilariousDatum {\n  attributes?: Attributes4\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes4 {\n  action?: string\n  createdAt?: Date\n  createdBy?: CreatedBy5\n  role?: TentacledRole\n  updatedAt?: Date\n  updatedBy?: UpdatedBy1\n  [property: string]: any\n}\n\nexport interface CreatedBy5 {\n  data?: Data30\n  [property: string]: any\n}\n\nexport interface Data30 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface TentacledRole {\n  data?: Data31\n  [property: string]: any\n}\n\nexport interface Data31 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy1 {\n  data?: Data32\n  [property: string]: any\n}\n\nexport interface Data32 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy2 {\n  data?: Data33\n  [property: string]: any\n}\n\nexport interface Data33 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface PurpleUsers {\n  data?: AmbitiousDatum[]\n  [property: string]: any\n}\n\nexport interface AmbitiousDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy3 {\n  data?: Data34\n  [property: string]: any\n}\n\nexport interface Data34 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface Robots {\n  data?: RobotsDatum[]\n  [property: string]: any\n}\n\nexport interface RobotsDatum {\n  attributes?: Attributes5\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes5 {\n  cameras?: TentacledCameras\n  createdAt?: Date\n  createdBy?: CreatedBy11\n  lab?: TentacledLab\n  name?: string\n  publishedAt?: Date\n  robot_type?: RobotType\n  status?: IndigoStatus\n  updatedAt?: Date\n  updatedBy?: UpdatedBy9\n  [property: string]: any\n}\n\nexport interface TentacledCameras {\n  data?: CunningDatum[]\n  [property: string]: any\n}\n\nexport interface CunningDatum {\n  attributes?: Attributes6\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes6 {\n  createdAt?: Date\n  createdBy?: CreatedBy6\n  lab?: FluffyLab\n  name?: string\n  publishedAt?: Date\n  robot?: RobotObject\n  updatedAt?: Date\n  updatedBy?: UpdatedBy4\n  usage?: string\n  video_live_url?: string\n  videos?: Videos\n  [property: string]: any\n}\n\nexport interface CreatedBy6 {\n  data?: Data35\n  [property: string]: any\n}\n\nexport interface Data35 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyLab {\n  data?: Data36\n  [property: string]: any\n}\n\nexport interface Data36 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface RobotObject {\n  data?: RobotData\n  [property: string]: any\n}\n\nexport interface RobotData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy4 {\n  data?: Data37\n  [property: string]: any\n}\n\nexport interface Data37 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface Videos {\n  data?: VideosDatum[]\n  [property: string]: any\n}\n\nexport interface VideosDatum {\n  attributes?: Attributes7\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes7 {\n  camera?: Camera\n  createdAt?: Date\n  createdBy?: CreatedBy7\n  name?: string\n  publishedAt?: Date\n  task_no?: string\n  updatedAt?: Date\n  updatedBy?: UpdatedBy8\n  video_url?: string\n  [property: string]: any\n}\n\nexport interface Camera {\n  data?: CameraData\n  [property: string]: any\n}\n\nexport interface CameraData {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface CreatedBy7 {\n  data?: Data38\n  [property: string]: any\n}\n\nexport interface Data38 {\n  attributes?: Attributes8\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes8 {\n  blocked?: boolean\n  createdAt?: Date\n  createdBy?: CreatedBy8\n  email?: string\n  firstname?: string\n  isActive?: boolean\n  lastname?: string\n  preferedLanguage?: string\n  registrationToken?: string\n  resetPasswordToken?: string\n  roles?: Roles\n  updatedAt?: Date\n  updatedBy?: UpdatedBy7\n  username?: string\n  [property: string]: any\n}\n\nexport interface CreatedBy8 {\n  data?: Data39\n  [property: string]: any\n}\n\nexport interface Data39 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface Roles {\n  data?: RolesDatum[]\n  [property: string]: any\n}\n\nexport interface RolesDatum {\n  attributes?: Attributes9\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes9 {\n  code?: string\n  createdAt?: Date\n  createdBy?: CreatedBy9\n  description?: string\n  name?: string\n  permissions?: FluffyPermissions\n  updatedAt?: Date\n  updatedBy?: UpdatedBy6\n  users?: FluffyUsers\n  [property: string]: any\n}\n\nexport interface CreatedBy9 {\n  data?: Data40\n  [property: string]: any\n}\n\nexport interface Data40 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyPermissions {\n  data?: MagentaDatum[]\n  [property: string]: any\n}\n\nexport interface MagentaDatum {\n  attributes?: Attributes10\n  id?: number\n  [property: string]: any\n}\n\nexport interface Attributes10 {\n  action?: string\n  conditions?: any\n  createdAt?: Date\n  createdBy?: CreatedBy10\n  properties?: any\n  role?: StickyRole\n  subject?: string\n  updatedAt?: Date\n  updatedBy?: UpdatedBy5\n  [property: string]: any\n}\n\nexport interface CreatedBy10 {\n  data?: Data41\n  [property: string]: any\n}\n\nexport interface Data41 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface StickyRole {\n  data?: Data42\n  [property: string]: any\n}\n\nexport interface Data42 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy5 {\n  data?: Data43\n  [property: string]: any\n}\n\nexport interface Data43 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy6 {\n  data?: Data44\n  [property: string]: any\n}\n\nexport interface Data44 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface FluffyUsers {\n  data?: FriskyDatum[]\n  [property: string]: any\n}\n\nexport interface FriskyDatum {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy7 {\n  data?: Data45\n  [property: string]: any\n}\n\nexport interface Data45 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy8 {\n  data?: Data46\n  [property: string]: any\n}\n\nexport interface Data46 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface CreatedBy11 {\n  data?: Data47\n  [property: string]: any\n}\n\nexport interface Data47 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface TentacledLab {\n  data?: Data48\n  [property: string]: any\n}\n\nexport interface Data48 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport enum RobotType {\n  Aloha = 'aloha',\n  Sd01 = 'sd-01'\n}\n\nexport enum IndigoStatus {\n  Error = 'error',\n  Holding = 'holding',\n  Idle = 'idle',\n  Working = 'working'\n}\n\nexport interface UpdatedBy9 {\n  data?: Data49\n  [property: string]: any\n}\n\nexport interface Data49 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy10 {\n  data?: Data50\n  [property: string]: any\n}\n\nexport interface Data50 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n\nexport interface UpdatedBy11 {\n  data?: Data51\n  [property: string]: any\n}\n\nexport interface Data51 {\n  attributes?: { [key: string]: any }\n  id?: number\n  [property: string]: any\n}\n", "/* tslint:disable */\n/* eslint-disable */\n/**\n * labwise run api\n * lab agent of devices\n *\n * OpenAPI spec version: 0.0.1\n * \n *\n * NOTE: This class is auto generated by the swagger code generator program.\n * https://github.com/swagger-api/swagger-codegen.git\n * Do not edit the class manually.\n */\n/**\n * 角色，主反应物，副反应物，溶剂，催化剂等等\n * @export\n * @enum {string}\n */\nexport enum Role {\n    MainReactant = 'main_reactant',\n    Reactant = 'reactant'\n}\n\n", "/* tslint:disable */\n/* eslint-disable */\n/**\n * labwise run api\n * lab agent of devices\n *\n * OpenAPI spec version: 0.0.1\n * \n *\n * NOTE: This class is auto generated by the swagger code generator program.\n * https://github.com/swagger-api/swagger-codegen.git\n * Do not edit the class manually.\n */\n/**\n * An enumeration.\n * @export\n * @enum {string}\n */\nexport enum Status {\n    Canceled = 'canceled',\n    Created = 'created',\n    Prepared = 'prepared',\n    Running = 'running',\n    Finished = 'finished'\n}\n\n", "/* tslint:disable */\n/* eslint-disable */\n/**\n * labwise run api\n * lab agent of devices\n *\n * OpenAPI spec version: 0.0.1\n * \n *\n * NOTE: This class is auto generated by the swagger code generator program.\n * https://github.com/swagger-api/swagger-codegen.git\n * Do not edit the class manually.\n */\n/**\n * 具体量单位\n * @export\n * @enum {string}\n */\nexport enum Unit {\n    G = 'g',\n    Ml = 'ml'\n}\n\n", "import ButtonWithLoading from '@/components/ButtonWithLoading'\nimport MoleculeStructure from '@/components/MoleculeStructure'\nimport { apiUpdateExperimentDesigns } from '@/services'\nimport { Procedure } from '@/services/brain'\nimport { DesignStatus, ExperimentDesignModel } from '@/types/models'\nimport { encodeString, getWord, toInt } from '@/utils'\nimport { ProDescriptions } from '@ant-design/pro-components'\nimport {\n  App,\n  Button,\n  Card,\n  Col,\n  Divider,\n  Popover,\n  Row,\n  Space,\n  Tag,\n  Typography\n} from 'antd'\nimport { TooltipPlacement } from 'antd/es/tooltip'\nimport { isNil } from 'lodash'\nimport React from 'react'\nimport { useAccess, useParams } from 'umi'\nimport EditExperimentModal from '../EditExperimentModal'\nimport './index.less'\n\nexport interface ReactionDesignCardProps {\n  procedure: Procedure\n  design: ExperimentDesignModel\n  projectReactionId?: number\n  onEdit?: (procedure: Procedure) => void\n  onDelete?: (procedure: Procedure) => void\n  onUpdated?: (action?: 'cancel' | 'create') => void\n}\n\nconst ReactionDesignCard: React.FC<ReactionDesignCardProps> = ({\n  procedure,\n  design,\n  projectReactionId,\n  onEdit: propOnEdit,\n  onDelete: propOnDelete,\n  onUpdated\n}) => {\n  const access = useAccess()\n  const { yields, smiles } = procedure\n  const { modal } = App.useApp()\n  const { id: projectId, reactionId } = useParams<{ id: string }>()\n  const onEdit = () => propOnEdit?.(procedure)\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const onDelete = () => {\n    return\n    modal.confirm({\n      title: `确认删除该反应？`,\n      onOk: () => propOnDelete?.(procedure)\n    })\n  }\n  const onCancelExperimentPlan = async () => {\n    modal.confirm({\n      title: `确认取消该实验设计？`,\n      onOk: async () => {\n        await apiUpdateExperimentDesigns({\n          data: { id: design.id, status: DesignStatus.Canceled }\n        })\n        onUpdated?.('cancel')\n      }\n    })\n  }\n\n  const PopoverCom = ({\n    content,\n    placement\n  }: {\n    content: string\n    placement?: TooltipPlacement\n  }) => {\n    return (\n      <Popover\n        overlayStyle={{\n          width: '30vw'\n        }}\n        placement={placement || 'top'}\n        content={content}\n        arrow={false}\n      >\n        <Typography.Link>Procedure</Typography.Link>\n      </Popover>\n    )\n  }\n\n  return (\n    <Card\n      style={{ margin: '4px 0' }}\n      size=\"small\"\n      className=\"my-reaction-design-card-root\"\n    >\n      <Row>\n        <Col sm={24} md={11}>\n          <Row>\n            <Typography.Title level={5}>{getWord('reaction')}</Typography.Title>\n            <Space className=\"actions-wrapper\" size=\"small\">\n              {access?.authCodeList?.includes(\n                'reaction-detail.myReactionDesign.editMolecules'\n              ) && (\n                <Button type=\"link\" onClick={onEdit}>\n                  {getWord('edit-molecules')}\n                </Button>\n              )}\n              {/* <Button type=\"link\" onClick={onDelete}>\n                删除\n              </Button> */}\n            </Space>\n          </Row>\n          <Row>\n            <Space>\n              <Typography.Text>{getWord('yield')}: </Typography.Text>\n              <Typography.Text type=\"secondary\">\n                {isNil(yields) ? '-' : `${yields}%`}\n              </Typography.Text>\n            </Space>\n          </Row>\n          <Row>\n            <MoleculeStructure structure={smiles} />\n          </Row>\n          <Row>\n            <PopoverCom placement=\"topLeft\" content={procedure.procedure} />\n          </Row>\n        </Col>\n        <Col sm={0} md={1} className=\"divider-wrapper\">\n          <Divider type=\"vertical\" className=\"divider\" />\n        </Col>\n        <Col sm={24} md={11}>\n          <ProDescriptions<ExperimentDesignModel>\n            title={getWord('experiment-design')}\n            column={1}\n            dataSource={design}\n            extra={\n              <Space>\n                {access?.authCodeList?.includes(\n                  'projects_reaction_experimentDesign_view'\n                ) ? (\n                  <Button\n                    type=\"link\"\n                    onClick={() => {\n                      window.open(\n                        `/projects/${projectId}/reaction/${reactionId}/experimental-procedure/detail/${encodeString(\n                          JSON.stringify(design.id)\n                        )}?type=editor`,\n                        '_blank'\n                      )\n                    }}\n                  >\n                    {getWord('pages.projectTable.actionLabel.viewDetail')}\n                  </Button>\n                ) : (\n                  ''\n                )}\n                {design.status === DesignStatus.Published && (\n                  <EditExperimentModal\n                    materialTable={procedure.material_table}\n                    projectId={toInt(design.project_no)}\n                    projectReactionId={projectReactionId}\n                    experiementDesignNo={design.experiment_design_no}\n                    onSuccess={() => onUpdated?.('create')}\n                  />\n                )}\n                {access?.authCodeList?.includes(\n                  'projects_reaction_experimentDesign_cancel'\n                ) &&\n                  design.status !== DesignStatus.Canceled && (\n                    <ButtonWithLoading\n                      type=\"link\"\n                      onClick={onCancelExperimentPlan}\n                    >\n                      {getWord('pages.experiment.label.operation.cancel')}\n                    </ButtonWithLoading>\n                  )}\n              </Space>\n            }\n          >\n            <ProDescriptions.Item\n              label={getWord('pages.experiment.label.experimentDesignName')}\n              dataIndex=\"name\"\n            />\n            <ProDescriptions.Item\n              label={getWord('experiment-design-status')}\n              dataIndex=\"status\"\n              valueEnum={{\n                canceled: {\n                  text: (\n                    <Tag color=\"#979797\">\n                      {getWord('pages.experimentDesign.statusLabel.canceled')}\n                    </Tag>\n                  )\n                },\n                created: {\n                  text: (\n                    <Tag color=\"#FAAD14\">\n                      {getWord('pages.experimentDesign.statusLabel.created')}\n                    </Tag>\n                  )\n                },\n                published: {\n                  text: (\n                    <Tag color=\"#1890FF\">\n                      {getWord('pages.experimentDesign.statusLabel.published')}\n                    </Tag>\n                  )\n                },\n                validated: {\n                  text: (\n                    <Tag color=\"#FAAD14\">\n                      {getWord('pages.experimentDesign.statusLabel.validated')}\n                    </Tag>\n                  )\n                }\n              }}\n            />\n            <ProDescriptions.Item\n              label={getWord('creator')}\n              dataIndex=\"creator\"\n            />\n            <ProDescriptions.Item\n              label={getWord('last-modified-time')}\n              dataIndex=\"modified_date\"\n              valueType=\"dateTime\"\n            />\n            <ProDescriptions.Item label={getWord('reaction-parameters')} />\n            <ProDescriptions.Item label={getWord('experiment-log')} />\n          </ProDescriptions>\n          <PopoverCom\n            content={\n              (design.reference_text as string) ||\n              (design.reference_text as string)\n            }\n          />\n        </Col>\n      </Row>\n    </Card>\n  )\n}\n\nexport default ReactionDesignCard\n", "import { Reaction } from '@/pages/route/util'\nimport { Paginate, Procedure, ProjectReaction } from '@/services/brain'\nimport { ExperimentDesignModel } from '@/types/models'\nimport { getWord } from '@/utils'\nimport { ProList, ProSkeleton } from '@ant-design/pro-components'\nimport { groupBy } from 'lodash'\nimport React, { useEffect, useState } from 'react'\nimport TabWithNumber from '../TabWithNumber'\nimport {\n  fetchDesignTotal,\n  fetchDesigns,\n  fetchEffectiveProcedureIds,\n  fetchProcedures\n} from '../util'\nimport ReactionDesignCard from './ReactionDesignCard'\n\nexport interface ReactionDesign {\n  procedure: Procedure\n  design: ExperimentDesignModel\n}\n\nexport interface MyReactionDesignTabProps {\n  reaction?: Reaction\n  projectReaction?: ProjectReaction\n  showCanceled?: boolean\n  renderer?: (\n    procedure: Procedure,\n    experiment: ExperimentDesignModel\n  ) => React.ReactNode\n}\n\nconst MyReactionDesignTab: React.FC<MyReactionDesignTabProps> = ({\n  reaction,\n  projectReaction,\n  showCanceled,\n  renderer\n}) => {\n  const [designs, setDesigns] = useState<ReactionDesign[]>([])\n  const [paginate, setPaginate] = useState<Paginate>({ page: 1, pageSize: 10 })\n  const [loading, setLoading] = useState<boolean>(false)\n\n  const updateDesigns = async (\n    reaction?: Reaction,\n    projectReaction?: ProjectReaction\n  ) => {\n    const procedureIds = await fetchEffectiveProcedureIds(\n      reaction,\n      projectReaction\n    )\n    const procedureMap = groupBy(await fetchProcedures(procedureIds), 'id')\n    const designs = await fetchDesigns(procedureIds)\n    setDesigns(\n      designs.map((design) => ({\n        design,\n        procedure: procedureMap[design.rxn_no]?.[0] || {}\n      }))\n    )\n  }\n\n  useEffect(() => {\n    if (!reaction || !projectReaction) return\n    setLoading(true)\n    updateDesigns(reaction, projectReaction).then(() => setLoading(false))\n  }, [reaction, projectReaction])\n\n  const filteredDesigns = showCanceled\n    ? designs\n    : designs.filter((d) => d.design.status !== 'canceled')\n\n  if (!designs) {\n    return <ProSkeleton type=\"list\" />\n  }\n  return (\n    <ProList<ReactionDesign>\n      ghost\n      loading={loading}\n      pagination={{\n        current: paginate.page,\n        pageSize: paginate.pageSize,\n        simple: true,\n        total: filteredDesigns.length,\n        onChange: (page, pageSize) => {\n          setPaginate({ page, pageSize })\n        }\n      }}\n      renderItem={({ procedure, design }) => renderer?.(procedure, design)}\n      grid={{ column: 1 }}\n      dataSource={filteredDesigns}\n    />\n  )\n}\n\nexport default MyReactionDesignTab\n\nexport const getMyReactionDesignTabConfig = (\n  reaction?: Reaction,\n  projectReaction?: ProjectReaction,\n  renderer: (\n    procedure: Procedure,\n    design: ExperimentDesignModel\n  ) => React.ReactNode = (procedure, design) => (\n    <ReactionDesignCard\n      procedure={procedure}\n      design={design}\n      projectReactionId={projectReaction?.id}\n    />\n  ),\n  showCanceled?: boolean\n) => ({\n  key: 'my-reaction-design',\n  label: (\n    <TabWithNumber\n      title={getWord('my-reaction-experimental')}\n      getNumber={async () => {\n        if (!reaction || !projectReaction) return undefined\n        return await fetchDesignTotal(reaction, projectReaction, showCanceled)\n      }}\n    />\n  ),\n  children:\n    reaction && projectReaction ? (\n      <MyReactionDesignTab\n        reaction={reaction}\n        projectReaction={projectReaction}\n        renderer={renderer}\n        showCanceled={showCanceled}\n      />\n    ) : null\n})\n", "import ButtonWithLoading from '@/components/ButtonWithLoading'\nimport MoleculeStructure from '@/components/MoleculeStructure'\nimport StatusRender from '@/components/StatusRender'\nimport { useProjectMembers } from '@/hooks/useProjectMembers'\nimport { apiGetExperimentDesignResults, parseResponseResult } from '@/services'\nimport { ExperimentResult } from '@/services/experiment-design/index.d'\nimport { AiProcedure } from '@/types/Procedure'\nimport { IOption } from '@/types/common'\nimport { encodeString, getWord } from '@/utils'\nimport {\n  ProColumns,\n  ProSchemaValueEnumObj,\n  ProTable\n} from '@ant-design/pro-components'\nimport { history, useAccess } from '@umijs/max'\nimport { App, Button, Card, Col, Row, Space, Tag } from 'antd'\nimport Link from 'antd/lib/typography/Link'\nimport Paragraph from 'antd/lib/typography/Paragraph'\nimport Text from 'antd/lib/typography/Text'\nimport Title from 'antd/lib/typography/Title'\nimport React, { useState } from 'react'\nimport { AiProcedureCardProps } from './AiProcedureCard'\nimport './index.less'\nimport { getTagForReferenceType, getTagForSameOrSimilerType } from './util'\n\nconst columns = (\n  projectId: number,\n  reactionId: number,\n  members: IOption[],\n  onJump?: () => void\n): ProColumns<ExperimentResult>[] => [\n  {\n    valueType: 'text',\n    title: getWord('experiment-ID'),\n    dataIndex: 'experiment_no',\n    render: (_, item) => {\n      return (\n        <a\n          onClick={() => {\n            onJump?.()\n            history.push(\n              `/projects/${projectId}/reaction/${reactionId}/experimental-procedure/conclusion/${encodeString(\n                JSON.stringify(item.experiment_no)\n              )}${\n                ['completed', 'failed'].includes(item.experiment_result)\n                  ? '#conclusion'\n                  : ''\n              }`\n            )\n          }}\n        >\n          {item.experiment_no}\n        </a>\n      )\n    }\n  },\n  {\n    valueType: 'select',\n    title: getWord('pages.experiment.label.personInCharge'),\n    dataIndex: 'experiment_owner',\n    valueEnum: members.reduce<ProSchemaValueEnumObj>((acc, cur) => {\n      if (cur.value && !(cur.value in acc)) {\n        acc[cur.value] = { text: cur.label }\n      }\n      return acc\n    }, {})\n  },\n  {\n    valueType: 'dateTime',\n    title: getWord('start-time'),\n    dataIndex: 'experiment_start_time'\n  },\n  {\n    valueType: 'select',\n    title: getWord('conclusion'),\n    dataIndex: 'experiment_result',\n    render: (_, result) => {\n      return (\n        <StatusRender\n          status={result.experiment_result}\n          labelPrefix=\"pages.experiment.statusLabel\"\n        />\n      )\n    }\n  },\n  {\n    valueType: 'text',\n    title: getWord('yield'),\n    dataIndex: 'experiment_yield',\n    renderText: (_, result) => `${result.experiment_yield.toFixed(0)}%`\n  }\n]\n\nexport interface ProcedureCardInDetailPageProps extends AiProcedureCardProps {\n  onReference?: (procedure: AiProcedure) => void\n  projectId: number\n  reactionId: number\n}\n\nconst ProcedureCardInDetailPage: React.FC<ProcedureCardInDetailPageProps> = ({\n  projectId,\n  reactionId,\n  procedure,\n  isSame,\n  onReference\n}) => {\n  const [expanded, setExpanded] = useState<boolean>(false)\n  const { members } = useProjectMembers(projectId)\n  const { modal } = App.useApp()\n  const access = useAccess()\n  const {\n    text,\n    rxn,\n    similarity,\n    experimentalProcedure,\n    yieldString,\n    reference: {\n      type,\n      link,\n      title,\n      authors,\n      date,\n      no,\n      assignees,\n      reference_text\n    }\n  } = procedure\n\n  const expandText = (\n    <Text\n      style={{ color: '#1890ff', cursor: 'pointer' }}\n      onClick={(e) => {\n        e.stopPropagation()\n        setExpanded((pre) => !pre)\n      }}\n    >\n      {expanded ? 'less' : 'more'}\n    </Text>\n  )\n\n  return (\n    <Card size=\"small\" className=\"ai-card-root\">\n      <Row>\n        <Col span={12}>\n          <Space className=\"title\" size=\"large\">\n            <div>\n              <Text type=\"secondary\">{getWord('similarity')}</Text>:\n              <Text style={{ color: '#027AFF', paddingLeft: 8 }}>\n                {similarity}\n              </Text>\n            </div>\n            <div>\n              <Text type=\"secondary\">{getWord('yield')}</Text>:\n              <Text style={{ color: '#027AFF', paddingLeft: 8 }}>\n                {yieldString}\n              </Text>\n            </div>\n            {!!procedure.scalable && (\n              <>\n                <Space />\n                <Tag color=\"lime\">{getWord('procedure.scalable')}</Tag>\n              </>\n            )}\n            {isSame !== undefined ? getTagForSameOrSimilerType(isSame) : null}\n          </Space>\n        </Col>\n        <Col style={{ marginLeft: 'auto' }}>\n          <Space>\n            {access?.authCodeList?.includes(\n              'reaction-detail.reactionLib.addToMyReaction'\n            ) &&\n              onReference && (\n                <Button\n                  type=\"primary\"\n                  size=\"small\"\n                  onClick={() => onReference(procedure)}\n                >\n                  {getWord('pages.route.label.addToMyReaction')}\n                </Button>\n              )}\n          </Space>\n        </Col>\n      </Row>\n      <Row gutter={{ md: 16 }}>\n        <Col md={8}>\n          <MoleculeStructure structure={rxn} />\n        </Col>\n        <Col md={16}>\n          <Text type=\"secondary\" strong>\n            Procedure\n          </Text>\n          <Paragraph\n            ellipsis={\n              expanded\n                ? false\n                : { rows: 5, expandable: true, symbol: expandText }\n            }\n            copyable={{ text }}\n          >\n            {text || experimentalProcedure || ''}\n            {expanded && expandText}\n          </Paragraph>\n          {isSame && (\n            <Text type=\"secondary\">\n              {getWord('source')}：\n              <ButtonWithLoading\n                type=\"link\"\n                onClick={async () => {\n                  const res = await apiGetExperimentDesignResults({\n                    routeParams: `${procedure.id}`\n                  })\n                  if (parseResponseResult(res).ok) {\n                    const ins = modal.info({\n                      title: getWord('experiment-log'),\n                      width: 800,\n                      content: (\n                        <ProTable<ExperimentResult>\n                          pagination={false}\n                          toolbar={{ settings: [], search: false }}\n                          search={false}\n                          columns={columns(projectId, reactionId, members, () =>\n                            ins.destroy()\n                          )}\n                          dataSource={res.data.experiment_results}\n                        />\n                      )\n                    })\n                  }\n                }}\n              >\n                {getWord('experiment-history')}\n              </ButtonWithLoading>\n            </Text>\n          )}\n\n          {!isSame && (\n            <div className=\"reference-wrapper\">\n              <Title level={5}>Reference {getTagForReferenceType(type)}</Title>\n              {type === 'patent' && (no || assignees) && (\n                <Text\n                  style={{\n                    textAlign: 'right',\n                    display: 'block',\n                    width: '100%'\n                  }}\n                >\n                  {no && (\n                    <>\n                      Patent No: <Text copyable>{no}</Text>\n                    </>\n                  )}\n                  {assignees && <Text>, assigned by {assignees}</Text>}\n                  <br />\n                </Text>\n              )}\n              {link && (\n                <>\n                  <Text type=\"secondary\" copyable>\n                    {link ? <Link href={link}>{title}</Link> : title}\n                  </Text>\n                  <br />\n                </>\n              )}\n              {reference_text && (\n                <>\n                  <Text type=\"secondary\" copyable>\n                    {reference_text}\n                  </Text>\n                  <br />\n                </>\n              )}\n              <Text\n                italic\n                style={{ textAlign: 'right', display: 'block', width: '100%' }}\n              >\n                {authors}\n              </Text>\n              {date && (\n                <Text\n                  style={{\n                    textAlign: 'right',\n                    display: 'block',\n                    width: '100%'\n                  }}\n                >\n                  {date}\n                </Text>\n              )}\n            </div>\n          )}\n        </Col>\n      </Row>\n    </Card>\n  )\n}\n\nexport default ProcedureCardInDetailPage\n", "import { useParams } from '@umijs/max'\n\nexport const usePathNumParam = (\n  paramName: string,\n  defaultValue: number = 0\n) => {\n  const { [paramName]: paramStr = '' } = useParams()\n  const paramNum = Number.parseInt(paramStr)\n  const param = Number.isNaN(paramNum) ? defaultValue : paramNum\n  return param\n}\n", "import LazySmileDrawer from '@/components/LazySmileDrawer'\nimport { getDetectRecordTabConfig } from '@/components/ReactionTabs/DetectRecord'\nimport { getExperimentListTabConfig } from '@/components/ReactionTabs/ExperimentListTab'\nimport { getMyReactionDesignTabConfig } from '@/components/ReactionTabs/MyReactionDesignTab'\nimport ReactionDesignCard from '@/components/ReactionTabs/MyReactionDesignTab/ReactionDesignCard'\nimport ReactionLibTab from '@/components/ReactionTabs/ReactionLibTab'\nimport ProcedureCardInDetailPage from '@/components/ReactionTabs/ReactionLibTab/ProcedureCardInDetailPage'\nimport { getRetroReactionTabConfig } from '@/components/ReactionTabs/RetroReactionTab'\nimport { useBrainFetch } from '@/hooks/useBrainFetch'\nimport { usePathNumParam } from '@/hooks/usePathNumParam'\nimport { Procedure, ProjectReaction, service } from '@/services/brain'\nimport { AiProcedure } from '@/types/Procedure'\nimport { getWord } from '@/utils'\nimport { PageContainer, ProTable } from '@ant-design/pro-components'\nimport { useModel, useSearchParams } from '@umijs/max'\nimport { Button, Col, Row, Space, Switch, TabsProps } from 'antd'\nimport React, { useEffect, useMemo, useRef, useState } from 'react'\nimport { useAccess } from 'umi'\nimport {\n  Reaction,\n  getDeepthMap,\n  getIdOfReaction,\n  getNameOfReaction\n} from '../route/util'\nimport MyReactionDialog from './component/MyReactionDialog'\nimport './index.less'\nimport {\n  aiProcedureToProcedure,\n  defaultReferenceType,\n  getReactionFromRxn\n} from './util'\n\ntype Tab = Exclude<TabsProps['items'], undefined>[number]\nconst ReactionDetail: React.FC = ({}) => {\n  const access = useAccess()\n  const id = usePathNumParam('reactionId')\n  const [searchParams, setSearchParams] = useSearchParams()\n  const [tab, setTab] = useState<string>(searchParams.get('tab') as string)\n  const { fetch, loading } = useBrainFetch()\n  const [projectReaction, setProjectReaction] = useState<ProjectReaction>()\n  const [reaction, setReaction] = useState<Reaction>()\n  const ref = useRef<HTMLDivElement>()\n  const { initialState } = useModel('@@initialState')\n  const [editingProcedure, setEditingProcedure] = useState<Procedure>()\n  const [action, setAction] = useState<'create' | 'edit' | 'reference'>(\n    'create'\n  )\n  const [open, setOpen] = useState<boolean>(false)\n  const [showCanceledDesign, setShowCanceledDesign] = useState<boolean>(false)\n  const [updateEvent, setUpdateEvent] = useState<Record<never, never>>({})\n\n  const fetchProjectReaction = async (id: number) => {\n    const { data } = await fetch(\n      service<ProjectReaction>('project-reactions')\n        .selectManyByID([id])\n        .populateWith('project_routes', ['main_tree', 'status', 'name'])\n        .populateWith('project', ['id'])\n        .get()\n    )\n    return data\n  }\n  const updateReaction = async (id: number) => {\n    await fetchProjectReaction(id).then((data) => {\n      if (data?.length) {\n        setProjectReaction(data[0])\n        setReaction(getReactionFromRxn(data[0].reaction))\n      }\n    })\n  }\n  const onReference = async (procedure: AiProcedure): Promise<void> => {\n    setEditingProcedure(\n      aiProcedureToProcedure(procedure, `${initialState?.userInfo?.id || -1}`)\n    )\n    setAction('reference')\n    setOpen(true)\n  }\n  const onCreate = async (): Promise<void> => {\n    setEditingProcedure({\n      smiles: projectReaction?.reaction || '',\n      procedure: '',\n      material_table: [],\n      reference_type: defaultReferenceType,\n      created_by: `${initialState?.userInfo?.id || -1}`\n    })\n    setAction('create')\n    setOpen(true)\n  }\n  const onEdit = async (procedure: Procedure): Promise<void> => {\n    setEditingProcedure(procedure)\n    setAction('edit')\n    setOpen(true)\n  }\n  const onDelete = async (procedure: Procedure): Promise<void> => {\n    if (!projectReaction || !procedure.id) return\n    const newEffective = new Set<number>(projectReaction.effective_procedures)\n    newEffective.delete(procedure.id)\n    const newHistory = new Set<number>(projectReaction.history_procedures)\n    newHistory.add(procedure.id)\n\n    const { data: newProjectReaction } = await service<ProjectReaction>(\n      'project-reactions'\n    ).update(projectReaction.id, {\n      effective_procedures: Array.from(newEffective.values()),\n      history_procedures: Array.from(newHistory.values())\n    })\n    if (newProjectReaction) {\n      updateReaction(id)\n    }\n  }\n\n  useEffect(() => {\n    updateReaction(id)\n  }, [id])\n\n  const reactionLibTab: Tab = {\n    key: 'reaction-lib',\n    label: <>{getWord('reaction-lib')}</>,\n    children: reaction ? (\n      <ReactionLibTab\n        reaction={reaction}\n        renderer={(procedure, isSame) => (\n          <ProcedureCardInDetailPage\n            procedure={procedure}\n            isSame={isSame}\n            onReference={onReference}\n            projectId={projectReaction?.project?.id || 0}\n            reactionId={id}\n          />\n        )}\n        actionSlot={tab === 'reaction-lib' ? ref.current : undefined}\n      />\n    ) : null\n  }\n  const myReactionDesignTab: Tab = useMemo(\n    () =>\n      getMyReactionDesignTabConfig(\n        reaction,\n        projectReaction,\n        (p, d) => (\n          <ReactionDesignCard\n            key={d.id}\n            procedure={p}\n            design={d}\n            projectReactionId={id}\n            onEdit={onEdit}\n            onDelete={onDelete}\n            onUpdated={(action) => {\n              updateReaction(id)\n              setUpdateEvent({})\n              if (action === 'create') setTab('my-experiment')\n            }}\n          />\n        ),\n        showCanceledDesign\n      ),\n    [reaction, projectReaction, showCanceledDesign]\n  )\n  const experimentListTab: Tab = useMemo(\n    () =>\n      getExperimentListTabConfig(\n        projectReaction?.project?.id,\n        projectReaction?.id,\n        updateEvent\n      ),\n    [projectReaction, updateEvent]\n  )\n\n  const detectRecordTab: Tab = useMemo(\n    () =>\n      getDetectRecordTabConfig(\n        projectReaction?.experimentNo,\n        projectReaction?.project?.id,\n        projectReaction?.id,\n        updateEvent\n      ),\n    [projectReaction, updateEvent]\n  )\n\n  const retroReactionTab: Tab = useMemo(\n    () => getRetroReactionTabConfig(reaction),\n    [reaction]\n  )\n\n  const tabs = useMemo(() => {\n    const tabs = []\n    const authCodeList = access?.authCodeList as string[]\n    if (authCodeList.includes('reaction-detail.tab.reactionLib'))\n      tabs.push(reactionLibTab)\n    if (authCodeList.includes('reaction-detail.tab.myReactionDesign'))\n      tabs.push(myReactionDesignTab)\n    if (authCodeList.includes('projects_reaction_experiment'))\n      tabs.push(experimentListTab)\n    if (authCodeList.includes('reaction-detail.tab.retroReaction'))\n      tabs.push(retroReactionTab)\n    if (authCodeList.includes('reaction-detail.tab.analysisRecord'))\n      tabs.push(detectRecordTab)\n    if (!tab) setTab(tabs[0]?.key)\n    return tabs as Tab[]\n  }, [\n    access?.authCodeList,\n    reactionLibTab,\n    myReactionDesignTab,\n    experimentListTab,\n    retroReactionTab\n  ])\n\n  useEffect(() => {\n    let defaultExperimentNo = searchParams.get('experimentNo')\n    if (defaultExperimentNo) {\n      setSearchParams(\n        { tab, experimentNo: defaultExperimentNo },\n        { replace: true }\n      )\n    } else {\n      setSearchParams({ tab }, { replace: true })\n    }\n  }, [tab])\n\n  const reactionContent = (\n    <>\n      <Row>\n        <Col md={8} lg={6}>\n          {getWord('reaction-no')}: {id}\n          {projectReaction?.reaction ? (\n            <LazySmileDrawer\n              structure={projectReaction?.reaction}\n              clickToCheckDetail\n              dbClickToCopy\n            />\n          ) : (\n            '无反应式'\n          )}\n        </Col>\n        <Col md={16} lg={18}>\n          <ProTable\n            className=\"route-table-root\"\n            ghost\n            size=\"small\"\n            search={false}\n            pagination={{\n              defaultPageSize: 5,\n              showSizeChanger: false,\n              simple: true\n            }}\n            columns={[\n              {\n                title: getWord('pages.reaction.label.name'),\n                dataIndex: 'name',\n                render: (dom, route) => (\n                  <Button\n                    type=\"link\"\n                    onClick={() => {\n                      window.open(`/route/view/${route.id}`, '_blank')\n                    }}\n                  >\n                    {dom}\n                  </Button>\n                )\n              },\n              {\n                title: getWord('pages.reaction.label.status'),\n                dataIndex: 'status',\n                valueEnum: {\n                  editing: {\n                    text: getWord('pages.reaction.statusLabel.editing')\n                  },\n                  confirmed: {\n                    text: getWord('pages.reaction.statusLabel.confirmed')\n                  },\n                  finished: {\n                    text: getWord('component.notification.statusValue.success')\n                  },\n                  canceled: {\n                    text: getWord('pages.reaction.statusLabel.canceled')\n                  }\n                }\n              },\n              {\n                title: getWord('pages.reaction.label.stepName'),\n                render: (_, { main_tree }) => {\n                  if (!reaction || !main_tree) return '-'\n                  const id = getIdOfReaction(reaction, main_tree)\n                  const deepthMap = getDeepthMap(main_tree)\n                  const deepth = id && deepthMap.get(id)\n                  if (deepth) {\n                    return getNameOfReaction(deepth[0], deepth[1])\n                  }\n                  return '-'\n                }\n              }\n            ]}\n            headerTitle={getWord('routes-citing')}\n            showHeader={true}\n            dataSource={projectReaction?.project_routes?.filter(\n              (r) => r.status === 'confirmed'\n            )}\n          />\n        </Col>\n      </Row>\n    </>\n  )\n\n  return (\n    <PageContainer\n      className=\"reaction-page-root\"\n      content={reactionContent}\n      loading={loading}\n      fixedHeader={false}\n      tabList={tabs}\n      tabProps={{ destroyInactiveTabPane: true }}\n      onTabChange={(tab) => setTab(tab)}\n      tabActiveKey={tab}\n      tabBarExtraContent={\n        <Space>\n          {tab === 'my-reaction-design' && (\n            <>\n              {access?.authCodeList?.includes(\n                'reaction-detail.myReactionDesign.newMyReaction'\n              ) && (\n                <Button type=\"primary\" size=\"middle\" onClick={onCreate}>\n                  {getWord('new-my-reaction')}\n                </Button>\n              )}\n              <Switch\n                checkedChildren={getWord('all-my-reactions')}\n                unCheckedChildren={getWord('all-my-reactions')}\n                checked={showCanceledDesign}\n                onChange={(checked) => setShowCanceledDesign(checked)}\n              />\n            </>\n          )}\n          <div ref={(e) => (ref.current = e || undefined)} />\n        </Space>\n      }\n    >\n      {projectReaction && (\n        <MyReactionDialog\n          projectReaction={projectReaction}\n          procedure={editingProcedure}\n          open={open}\n          mode={action}\n          onOpenChange={(open) => {\n            setOpen(open)\n            if (!open) setEditingProcedure(undefined)\n          }}\n          onCreated={async (p) => {\n            setOpen(false)\n            await updateReaction(p.id)\n            if (action === 'reference') {\n              setTab('my-reaction-design')\n            }\n          }}\n        />\n      )}\n    </PageContainer>\n  )\n}\n\nexport default ReactionDetail\n", "\"use strict\";\n\"use client\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _omit = _interopRequireDefault(require(\"rc-util/lib/omit\"));\nvar _warning = require(\"../_util/warning\");\nvar _Base = _interopRequireDefault(require(\"./Base\"));\nvar __rest = void 0 && (void 0).__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nconst Text = (_a, ref) => {\n  var {\n      ellipsis\n    } = _a,\n    restProps = __rest(_a, [\"ellipsis\"]);\n  const mergedEllipsis = React.useMemo(() => {\n    if (ellipsis && typeof ellipsis === 'object') {\n      return (0, _omit.default)(ellipsis, ['expandable', 'rows']);\n    }\n    return ellipsis;\n  }, [ellipsis]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = (0, _warning.devUseWarning)('Typography.Text');\n    process.env.NODE_ENV !== \"production\" ? warning(typeof ellipsis !== 'object' || !ellipsis || !('expandable' in ellipsis) && !('rows' in ellipsis), 'usage', '`ellipsis` do not support `expandable` or `rows` props.') : void 0;\n  }\n  return /*#__PURE__*/React.createElement(_Base.default, Object.assign({\n    ref: ref\n  }, restProps, {\n    ellipsis: mergedEllipsis,\n    component: \"span\"\n  }));\n};\nvar _default = exports.default = /*#__PURE__*/React.forwardRef(Text);", "\"use strict\";\n\"use client\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _warning = require(\"../_util/warning\");\nvar _Base = _interopRequireDefault(require(\"./Base\"));\nvar __rest = void 0 && (void 0).__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nconst TITLE_ELE_LIST = [1, 2, 3, 4, 5];\nconst Title = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n      level = 1\n    } = props,\n    restProps = __rest(props, [\"level\"]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = (0, _warning.devUseWarning)('Typography.Title');\n    process.env.NODE_ENV !== \"production\" ? warning(TITLE_ELE_LIST.includes(level), 'usage', 'Title only accept `1 | 2 | 3 | 4 | 5` as `level` value. And `5` need 4.6.0+ version.') : void 0;\n  }\n  const component = TITLE_ELE_LIST.includes(level) ? `h${level}` : `h1`;\n  return /*#__PURE__*/React.createElement(_Base.default, Object.assign({\n    ref: ref\n  }, restProps, {\n    component: component\n  }));\n});\nvar _default = exports.default = Title;"], "names": ["DetectRecordTab", "_useModel", "useModel", "searchExperimentCheck", "experimentCheckList", "_useSearchParams", "useSearchParams", "_useSearchParams2", "_slicedToArray", "searchParams", "setSearchParams", "_useParams", "useParams", "reactionId", "useEffect", "project_reaction_id", "_jsx", "className", "cs", "styles", "detectRecord", "children", "AnalysisRecord", "isAnalysisTab", "analysisData", "requestEvent", "params", "_objectSpread", "tab", "get", "replace", "getDetectRecordTabConfig", "experimentNo", "projectId", "projectReactionId", "updateEvent", "key", "label", "TabWithNumber", "title", "getWord", "getNumber", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "res", "wrap", "_context", "prev", "next", "abrupt", "apiSearchExperimentCheck", "data", "sent", "parseResponseResult", "ok", "length", "stop", "refetchEvent", "AddMethod", "Priority", "PurpleStatus", "<PERSON><PERSON>ffyStat<PERSON>", "Tentacled<PERSON><PERSON>us", "PurpleType", "StickyStatus", "FluffyType", "RobotType", "IndigoStatus", "CheckMethod", "CheckStatus", "CheckType", "DesignStatus", "ExperimentStatus", "EventLevel", "MessageLevel", "OperatorStatus", "TaskStatus", "Role", "Status", "Unit", "ReactionDesignCard", "_ref", "_access$authCodeList", "_access$authCodeList2", "_access$authCodeList3", "procedure", "design", "propOnEdit", "onEdit", "propOnDelete", "onDelete", "onUpdated", "access", "useAccess", "yields", "smiles", "_App$useApp", "App", "useApp", "modal", "id", "onCancelExperimentPlan", "_ref2", "_callee2", "_context2", "confirm", "onOk", "_onOk", "apiUpdateExperimentDesigns", "status", "Canceled", "apply", "arguments", "PopoverCom", "_ref3", "content", "placement", "Popover", "overlayStyle", "width", "arrow", "Typography", "Link", "Card", "style", "margin", "size", "_jsxs", "Row", "Col", "sm", "md", "Title", "level", "Space", "authCodeList", "includes", "<PERSON><PERSON>", "type", "onClick", "Text", "isNil", "concat", "MoleculeStructure", "structure", "Divider", "ProDescriptions", "column", "dataSource", "extra", "window", "open", "encodeString", "JSON", "stringify", "Published", "EditExperimentModal", "materialTable", "material_table", "toInt", "project_no", "experiementDesignNo", "experiment_design_no", "onSuccess", "ButtonWithLoading", "<PERSON><PERSON>", "dataIndex", "valueEnum", "canceled", "text", "Tag", "color", "created", "published", "validated", "valueType", "reference_text", "MyReactionDesignTab", "reaction", "projectReaction", "showCanceled", "renderer", "_useState", "useState", "_useState2", "designs", "setDesigns", "_useState3", "page", "pageSize", "_useState4", "paginate", "setPaginate", "_useState5", "_useState6", "loading", "setLoading", "updateDesigns", "procedureIds", "procedureMap", "fetchEffectiveProcedureIds", "t0", "groupBy", "fetchProcedures", "t1", "fetchDesigns", "map", "_procedureMap$design$", "rxn_no", "_x", "_x2", "then", "filteredDesigns", "filter", "d", "ProList", "ghost", "pagination", "current", "simple", "total", "onChange", "renderItem", "grid", "ProSkeleton", "getMyReactionDesignTabConfig", "undefined", "fetchDesignTotal", "columns", "members", "onJump", "render", "_", "item", "history", "push", "experiment_no", "experiment_result", "reduce", "acc", "cur", "value", "result", "StatusRender", "labelPrefix", "renderText", "experiment_yield", "toFixed", "ProcedureCardInDetailPage", "isSame", "onReference", "expanded", "setExpanded", "_useProjectMembers", "useProjectMembers", "rxn", "similarity", "experimentalProcedure", "yieldString", "_procedure$reference", "reference", "link", "authors", "date", "no", "assignees", "expandText", "cursor", "e", "stopPropagation", "pre", "span", "paddingLeft", "scalable", "_Fragment", "getTagForSameOrSimilerType", "marginLeft", "gutter", "strong", "Paragraph", "ellipsis", "rows", "expandable", "symbol", "copyable", "ins", "apiGetExperimentDesignResults", "routeParams", "info", "ProTable", "toolbar", "settings", "search", "destroy", "experiment_results", "getTagForReferenceType", "textAlign", "display", "href", "italic", "usePathNumParam", "paramName", "defaultValue", "_useParams$paramName", "paramStr", "paramNum", "Number", "parseInt", "param", "isNaN", "ReactionDetail", "_projectReaction$proj4", "_objectDestructuringEmpty", "setTab", "_useBrainFetch", "useBrainFetch", "fetch", "setProjectReaction", "setReaction", "ref", "useRef", "initialState", "_useState7", "_useState8", "editingProcedure", "setEditingProcedure", "_useState9", "_useState10", "action", "setAction", "_useState11", "_useState12", "<PERSON><PERSON><PERSON>", "_useState13", "_useState14", "showCanceledDesign", "setShowCanceledDesign", "_useState15", "_useState16", "setUpdateEvent", "fetchProjectReaction", "_yield$fetch", "service", "selectManyByID", "populateWith", "updateReaction", "getReactionFromRxn", "_ref4", "_callee3", "_initialState$userInf", "_context3", "aiProcedureToProcedure", "userInfo", "_x3", "onCreate", "_ref5", "_callee4", "_initialState$userInf2", "_context4", "reference_type", "defaultReferenceType", "created_by", "_ref6", "_callee5", "_context5", "_x4", "_ref7", "_callee6", "newEffective", "newHistory", "_yield$service$update", "newProjectReaction", "_context6", "Set", "effective_procedures", "history_procedures", "add", "update", "Array", "from", "values", "_x5", "reactionLibTab", "ReactionLibTab", "_projectReaction$proj", "project", "actionSlot", "myReactionDesignTab", "useMemo", "p", "experimentListTab", "_projectReaction$proj2", "getExperimentListTabConfig", "detectRecordTab", "_projectReaction$proj3", "retroReactionTab", "getRetroReactionTabConfig", "tabs", "_tabs$", "defaultExperimentNo", "reactionContent", "lg", "LazySmileDrawer", "clickToCheckDetail", "dbClickToCopy", "defaultPageSize", "showSizeChanger", "dom", "route", "editing", "confirmed", "finished", "_ref8", "main_tree", "getIdOfReaction", "deepthMap", "getDeepthMap", "deepth", "getNameOfReaction", "headerTitle", "showHeader", "project_routes", "r", "<PERSON><PERSON><PERSON><PERSON>", "fixedHeader", "tabList", "tabProps", "destroyInactiveTabPane", "onTabChange", "tabActiveKey", "tabBarExtraContent", "Switch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "checked", "MyReactionDialog", "mode", "onOpenChange", "onCreated", "_ref10", "_callee7", "_context7", "_x6", "_interopRequireDefault", "_interopRequireWildcard", "exports", "React", "_omit", "_warning", "_Base", "__rest", "s", "t", "i", "_a", "restProps", "mergedEllipsis", "_default", "TITLE_ELE_LIST", "props", "component"], "sourceRoot": ""}