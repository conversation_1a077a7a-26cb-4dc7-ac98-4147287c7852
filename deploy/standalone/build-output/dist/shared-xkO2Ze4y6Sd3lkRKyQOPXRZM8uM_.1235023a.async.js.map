{"version": 3, "file": "shared-xkO2Ze4y6Sd3lkRKyQOPXRZM8uM_.1235023a.async.js", "mappings": "iKAMIA,EAAiB,SAAwBC,EAAOC,EAAK,CACvD,OAAoB,gBAAoB,OAAU,QAAc,KAAc,CAAC,EAAGD,CAAK,EAAG,CAAC,EAAG,CAC5F,IAAKC,EACL,KAAM,GACR,CAAC,CAAC,CACJ,EACIC,EAAuB,aAAiBH,CAAc,EAI1D,IAAeG,C,wECVXC,EAAkB,SAAyBH,EAAOC,EAAK,CACzD,OAAoB,gBAAoB,OAAU,QAAc,KAAc,CAAC,EAAGD,CAAK,EAAG,CAAC,EAAG,CAC5F,IAAKC,EACL,KAAM,GACR,CAAC,CAAC,CACJ,EACIC,EAAuB,aAAiBC,CAAe,EAI3D,IAAeD,C,mFCdXE,EAAY,CAAC,aAAc,oBAAqB,kBAAmB,eAAe,EAQlFC,EAA6B,aAAiB,SAAUC,EAAML,EAAK,CACrE,IAAIM,EAAaD,EAAK,WACpBE,EAAoBF,EAAK,kBACzBG,EAAkBH,EAAK,gBACvBI,EAAgBJ,EAAK,cACrBK,KAAO,KAAyBL,EAAMF,CAAS,EACjD,SAAoB,OAAK,OAAU,KAAc,CAC/C,UAAW,SACX,cAAY,KAAc,CACxB,kBAAmBI,EACnB,gBAAiBC,CACnB,EAAGF,CAAU,EACb,IAAKN,EACL,cAAe,UACf,cAAeS,EACf,YAAa,CACX,cAAe,UACf,YAAa,GACb,gBAAiB,EACnB,CACF,EAAGC,CAAI,CAAC,CACV,CAAC,EACD,IAAeN,C,wLCxBJO,EAAO,SAAcN,EAAM,CACpC,IAAIO,EAAUP,EAAK,QACnB,SAAoB,OAAK,MAAO,CAC9B,MAAO,CACL,QAASO,GAAW,QACtB,EACA,YAAuB,OAAK,IAAS,CACnC,MAAO,CACL,OAAQ,CACV,CACF,CAAC,CACH,CAAC,CACH,EACWC,EAAoB,CAC7B,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,IAAK,CACP,EACIC,EAAoB,SAA2BC,EAAO,CACxD,IAAIC,EAAOD,EAAM,KACfE,EAASF,EAAM,OACbG,KAAa,WAAQ,UAAY,CACnC,MAAO,CACL,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,IAAK,EACP,CACF,EAAG,CAAC,CAAC,EACDC,KAAMC,EAAA,GAAc,GAAKF,EACzBG,GAAU,OAAO,KAAKF,CAAG,EAAE,OAAO,SAAUG,EAAK,CACnD,OAAOH,EAAIG,CAAG,IAAM,EACtB,CAAC,EAAE,CAAC,GAAK,KACLC,EAAYP,IAAS,OAAYH,EAAkBQ,EAAO,GAAK,EAAIL,EACnEQ,EAAa,SAAoBC,EAAO,CAC1C,OAAIA,IAAU,EACL,EAELF,EAAY,EACP,GAEF,EACT,EACA,SAAoB,OAAK,IAAM,CAC7B,SAAU,GACV,MAAO,CACL,eAAgB,EAClB,EACA,YAAuB,OAAK,MAAO,CACjC,MAAO,CACL,MAAO,OACP,eAAgB,gBAChB,QAAS,MACX,EACA,SAAU,IAAI,MAAMA,CAAS,EAAE,KAAK,IAAI,EAAE,IAAI,SAAUG,EAAGD,EAAO,CAChE,SAAoB,QAAM,MAAO,CAC/B,MAAO,CACL,kBAAmBF,EAAY,GAAKE,IAAU,EAAI,6BAA+B,OACjF,mBAAoBD,EAAWC,CAAK,EACpC,KAAM,EACN,gBAAiBA,IAAU,EAAI,GAAK,CACtC,EACA,SAAU,IAAc,OAAK,IAAU,CACrC,OAAQR,EACR,UAAW,GACX,MAAO,CACL,MAAO,IACP,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,KAAgB,OAAK,IAAS,OAAQ,CACrC,OAAQA,EACR,MAAO,CACL,OAAQ,EACV,CACF,CAAC,CAAC,CACJ,EAAGQ,CAAK,CACV,CAAC,CACH,CAAC,CACH,CAAC,CACH,EAGWE,EAAmB,SAA0BC,EAAO,CAC7D,IAAIX,EAASW,EAAM,OACnB,SAAoB,QAAM,WAAW,CACnC,SAAU,IAAc,OAAK,IAAM,CACjC,SAAU,GAGV,MAAO,CACL,aAAc,CAChB,EACA,OAAQ,CACN,KAAM,CACJ,QAAS,EACX,CACF,EACA,YAAuB,QAAM,MAAO,CAClC,MAAO,CACL,MAAO,OACP,QAAS,OACT,WAAY,SACZ,eAAgB,eAClB,EACA,SAAU,IAAc,OAAK,MAAO,CAClC,MAAO,CACL,SAAU,OACV,KAAM,CACR,EACA,YAAuB,OAAK,IAAU,CACpC,OAAQX,EACR,MAAO,CACL,MAAO,IACP,MAAO,CACL,iBAAkB,CACpB,CACF,EACA,UAAW,CACT,KAAM,EACN,MAAO,CACL,OAAQ,CACV,CACF,CACF,CAAC,CACH,CAAC,KAAgB,OAAK,IAAS,OAAQ,CACrC,OAAQA,EACR,KAAM,QACN,MAAO,CACL,MAAO,IACP,iBAAkB,EACpB,CACF,CAAC,CAAC,CACJ,CAAC,CACH,CAAC,KAAgB,OAAKN,EAAM,CAAC,CAAC,CAAC,CACjC,CAAC,CACH,EAGWkB,EAAe,SAAsBC,EAAO,CACrD,IAAId,EAAOc,EAAM,KACfC,EAAeD,EAAM,OACrBb,EAASc,IAAiB,OAAS,GAAOA,EAC1CC,EAAeF,EAAM,aACvB,SAAoB,QAAM,IAAM,CAC9B,SAAU,GACV,OAAQ,CACN,KAAM,CACJ,QAAS,CACX,CACF,EACA,SAAU,CAAC,IAAI,MAAMd,CAAI,EAAE,KAAK,IAAI,EAAE,IAAI,SAAUU,GAAGD,EAAO,CAC5D,SAGE,OAAKE,EAAkB,CACrB,OAAQ,CAAC,CAACV,CACZ,EAAGQ,CAAK,CAEZ,CAAC,EAAGO,IAAiB,OAAsB,OAAK,IAAM,CACpD,SAAU,GACV,MAAO,CACL,qBAAsB,EACtB,oBAAqB,CACvB,EACA,OAAQ,CACN,KAAM,CACJ,QAAS,OACT,WAAY,SACZ,eAAgB,QAClB,CACF,EACA,YAAuB,OAAK,IAAS,OAAQ,CAC3C,MAAO,CACL,MAAO,GACT,EACA,OAAQf,EACR,KAAM,OACR,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CACH,EAOWgB,EAAqB,SAA4BC,EAAO,CACjE,IAAIjB,EAASiB,EAAM,OACnB,SAAoB,QAAM,MAAO,CAC/B,MAAO,CACL,eAAgB,EAClB,EACA,SAAU,IAAc,OAAK,IAAU,CACrC,UAAW,GACX,MAAO,CACL,MAAO,GACT,CACF,CAAC,KAAgB,OAAK,IAAS,OAAQ,CACrC,OAAQjB,EACR,KAAM,OACR,CAAC,CAAC,CACJ,CAAC,CACH,EAMWkB,EAAsB,SAA6BC,EAAO,CACnE,IAAInB,EAASmB,EAAM,OACnB,SAAoB,OAAK,IAAM,CAC7B,SAAU,GACV,MAAO,CACL,wBAAyB,EACzB,uBAAwB,CAC1B,EACA,OAAQ,CACN,KAAM,CACJ,gBAAiB,CACnB,CACF,EACA,YAAuB,QAAM,IAAO,CAClC,MAAO,CACL,MAAO,OACP,eAAgB,eAClB,EACA,SAAU,IAAc,OAAK,IAAS,OAAQ,CAC5C,OAAQnB,EACR,MAAO,CACL,MAAO,GACT,EACA,KAAM,OACR,CAAC,KAAgB,QAAM,IAAO,CAC5B,SAAU,IAAc,OAAK,IAAS,OAAQ,CAC5C,OAAQA,EACR,KAAM,QACN,MAAO,CACL,MAAO,GACT,CACF,CAAC,KAAgB,OAAK,IAAS,OAAQ,CACrC,OAAQA,EACR,KAAM,QACN,MAAO,CACL,MAAO,EACT,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,CACH,CAAC,CACH,EACIoB,EAAmB,SAA0BC,EAAO,CACtD,IAAIC,EAAeD,EAAM,OACvBrB,EAASsB,IAAiB,OAAS,GAAOA,EAC1CC,EAAYF,EAAM,UAClBN,EAAeM,EAAM,aACrBG,GAAUH,EAAM,QAChBI,EAAaJ,EAAM,WACnBK,EAAaL,EAAM,KACnBM,EAAOD,IAAe,OAAS,EAAIA,EACrC,SAAoB,QAAM,MAAO,CAC/B,MAAO,CACL,MAAO,MACT,EACA,SAAU,CAACD,IAAe,OAAsB,OAAKT,EAAoB,CACvE,OAAQhB,CACV,CAAC,EAAGuB,IAAc,OAAsB,OAAK1B,EAAmB,CAC9D,KAAM0B,EACN,OAAQvB,CACV,CAAC,GAAIwB,KAAY,IAASG,IAAS,QAAuB,QAAM,IAAM,CACpE,SAAU,GACV,OAAQ,CACN,KAAM,CACJ,QAAS,CACX,CACF,EACA,SAAU,CAACH,KAAY,OAAsB,OAAKN,EAAqB,CACrE,OAAQlB,CACV,CAAC,EAAG2B,IAAS,OAAsB,OAAKf,EAAc,CACpD,KAAMe,EACN,OAAQ3B,EACR,aAAce,CAChB,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,CACH,EACA,EAAeK,ECtSX,EAAoB,CACtB,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,GAAI,EACJ,IAAK,CACP,EACIQ,GAAgC,SAAuCxC,EAAM,CAC/E,IAAIY,EAASZ,EAAK,OAClB,SAAoB,QAAM,MAAO,CAC/B,MAAO,CACL,iBAAkB,EACpB,EACA,SAAU,IAAc,OAAK,IAAS,OAAQ,CAC5C,OAAQY,EACR,KAAM,QACN,MAAO,CACL,MAAO,IACP,eAAgB,EAClB,CACF,CAAC,KAAgB,QAAM,MAAO,CAC5B,MAAO,CACL,MAAO,OACP,eAAgB,gBAChB,QAAS,MACX,EACA,SAAU,IAAc,QAAM,MAAO,CACnC,MAAO,CACL,KAAM,EACN,gBAAiB,GACjB,SAAU,GACZ,EACA,SAAU,IAAc,OAAK,IAAU,CACrC,OAAQA,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,KAAgB,OAAK,IAAU,CAC9B,OAAQA,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,KAAgB,OAAK,IAAU,CAC9B,OAAQA,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,CAAC,CACJ,CAAC,KAAgB,OAAK,MAAO,CAC3B,MAAO,CACL,KAAM,EACN,WAAY,SACZ,eAAgB,QAClB,EACA,YAAuB,QAAM,MAAO,CAClC,MAAO,CACL,SAAU,IACV,OAAQ,MACV,EACA,SAAU,IAAc,OAAK,IAAU,CACrC,OAAQA,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,KAAgB,OAAK,IAAU,CAC9B,OAAQA,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,CACH,EACI6B,EAA2B,SAAkC/B,EAAO,CACtE,IAAIC,EAAOD,EAAM,KACfE,EAASF,EAAM,OACbG,KAAa,WAAQ,UAAY,CACnC,MAAO,CACL,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,IAAK,EACP,CACF,EAAG,CAAC,CAAC,EACDC,KAAMC,EAAA,GAAc,GAAKF,EACzBG,GAAU,OAAO,KAAKF,CAAG,EAAE,OAAO,SAAUG,EAAK,CACnD,OAAOH,EAAIG,CAAG,IAAM,EACtB,CAAC,EAAE,CAAC,GAAK,KACLC,EAAYP,IAAS,OAAY,EAAkBK,EAAO,GAAK,EAAIL,EACvE,SAAoB,OAAK,MAAO,CAC9B,MAAO,CACL,MAAO,OACP,eAAgB,gBAChB,QAAS,MACX,EACA,SAAU,IAAI,MAAMO,CAAS,EAAE,KAAK,IAAI,EAAE,IAAI,SAAUG,EAAGD,EAAO,CAChE,SAAoB,QAAM,MAAO,CAC/B,MAAO,CACL,KAAM,EACN,mBAAoBA,IAAU,EAAI,EAAI,GACtC,iBAAkBA,IAAUF,EAAY,EAAI,EAAI,EAClD,EACA,SAAU,IAAc,OAAK,IAAU,CACrC,OAAQN,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,KAAgB,OAAK,IAAU,CAC9B,OAAQA,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,KAAgB,OAAK,IAAU,CAC9B,OAAQA,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,iBAAkB,CACpB,CACF,CACF,CAAC,CAAC,CACJ,EAAGQ,CAAK,CACV,CAAC,CACH,CAAC,CACH,EAOWsB,EAAoB,SAA2BnB,EAAO,CAC/D,IAAIX,EAASW,EAAM,OACjBoB,EAAepB,EAAM,OACrBqB,EAASD,IAAiB,OAAS,GAAQA,EACzC9B,KAAa,WAAQ,UAAY,CACnC,MAAO,CACL,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,GAAI,GACJ,IAAK,EACP,CACF,EAAG,CAAC,CAAC,EACDC,MAAMC,EAAA,GAAc,GAAKF,EACzBG,EAAU,OAAO,KAAKF,EAAG,EAAE,OAAO,SAAUG,EAAK,CACnD,OAAOH,GAAIG,CAAG,IAAM,EACtB,CAAC,EAAE,CAAC,GAAK,KACLC,EAAY,EAAkBF,CAAO,GAAK,EAC9C,SAAoB,QAAM,WAAW,CACnC,SAAU,IAAc,QAAM,MAAO,CACnC,MAAO,CACL,QAAS,OACT,WAAY4B,EAAS,mBAAqB,OAC1C,QAAS,UACX,EACA,SAAU,CAAC,IAAI,MAAM1B,CAAS,EAAE,KAAK,IAAI,EAAE,IAAI,SAAUG,EAAGD,EAAO,CACjE,SAAoB,OAAK,MAAO,CAC9B,MAAO,CACL,KAAM,EACN,mBAAoBwB,GAAUxB,IAAU,EAAI,EAAI,GAChD,iBAAkB,EACpB,EACA,YAAuB,OAAK,IAAU,CACpC,OAAQR,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,OAAQ,EACR,OAAQ,GACR,MAAOgC,EAAS,OAAS,MAC3B,CACF,CACF,CAAC,CACH,EAAGxB,CAAK,CACV,CAAC,KAAgB,OAAK,MAAO,CAC3B,MAAO,CACL,KAAM,EACN,mBAAoB,EACtB,EACA,YAAuB,OAAK,IAAU,CACpC,OAAQR,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,OAAQ,EACR,OAAQ,GACR,MAAOgC,EAAS,OAAS,MAC3B,CACF,CACF,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,KAAgB,OAAKtC,EAAM,CAC1B,QAAS,SACX,CAAC,CAAC,CACJ,CAAC,CACH,EAOWuC,EAAgB,SAAuBpB,EAAO,CACvD,IAAIb,EAASa,EAAM,OACjBqB,EAAarB,EAAM,KACnBd,EAAOmC,IAAe,OAAS,EAAIA,EACrC,SAAoB,QAAM,IAAM,CAC9B,SAAU,GACV,SAAU,IAAc,OAAK,IAAS,OAAQ,CAC5C,OAAQlC,EACR,KAAM,QACN,MAAO,CACL,MAAO,IACP,eAAgB,EAClB,CACF,CAAC,KAAgB,OAAK8B,EAAmB,CACvC,OAAQ,GACR,OAAQ9B,CACV,CAAC,EAAG,IAAI,MAAMD,CAAI,EAAE,KAAK,IAAI,EAAE,IAAI,SAAUU,EAAGD,GAAO,CACrD,SAGE,OAAKsB,EAAmB,CACtB,OAAQ9B,CACV,EAAGQ,EAAK,CAEZ,CAAC,KAAgB,OAAK,MAAO,CAC3B,MAAO,CACL,QAAS,OACT,eAAgB,WAChB,kBAAmB,EACrB,EACA,YAAuB,OAAK,IAAU,CACpC,OAAQR,EACR,UAAW,GACX,MAAO,CACL,MAAO,CACL,OAAQ,EACR,OAAQ,GACR,MAAO,QACP,SAAU,OACZ,CACF,CACF,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CACH,EACWmC,EAAuB,SAA8BlB,EAAO,CACrE,IAAIjB,EAASiB,EAAM,OACnB,SAAoB,QAAM,IAAM,CAC9B,SAAU,GACV,MAAO,CACL,qBAAsB,EACtB,oBAAqB,CACvB,EACA,SAAU,IAAc,OAAK,IAAS,OAAQ,CAC5C,OAAQjB,EACR,KAAM,QACN,MAAO,CACL,MAAO,IACP,eAAgB,EAClB,CACF,CAAC,KAAgB,OAAK6B,EAA0B,CAC9C,OAAQ7B,CACV,CAAC,KAAgB,OAAK4B,GAA+B,CACnD,OAAQ5B,CACV,CAAC,CAAC,CACJ,CAAC,CACH,EACIoC,EAA2B,SAAkCjB,EAAO,CACtE,IAAIkB,EAAelB,EAAM,OACvBnB,EAASqC,IAAiB,OAAS,GAAOA,EAC1CZ,EAAaN,EAAM,WACnBQ,EAAOR,EAAM,KACf,SAAoB,QAAM,MAAO,CAC/B,MAAO,CACL,MAAO,MACT,EACA,SAAU,CAACM,IAAe,OAAsB,OAAKT,EAAoB,CACvE,OAAQhB,CACV,CAAC,KAAgB,OAAKmC,EAAsB,CAC1C,OAAQnC,CACV,CAAC,EAAG2B,IAAS,OAAsB,OAAKjC,EAAM,CAAC,CAAC,EAAGiC,IAAS,OAAsB,OAAKM,EAAe,CACpG,OAAQjC,EACR,KAAM2B,CACR,CAAC,CAAC,CACJ,CAAC,CACH,EACA,EAAeS,EC9TXE,EAAqB,SAA4BlD,EAAM,CACzD,IAAImD,EAAcnD,EAAK,OACrBY,EAASuC,IAAgB,OAAS,GAAOA,EACzCd,EAAarC,EAAK,WACpB,SAAoB,QAAM,MAAO,CAC/B,MAAO,CACL,MAAO,MACT,EACA,SAAU,CAACqC,IAAe,OAAsB,OAAKT,EAAoB,CACvE,OAAQhB,CACV,CAAC,KAAgB,OAAK,IAAM,CAC1B,YAAuB,QAAM,MAAO,CAClC,MAAO,CACL,QAAS,OACT,eAAgB,SAChB,WAAY,SACZ,cAAe,SACf,QAAS,GACX,EACA,SAAU,IAAc,OAAK,IAAS,OAAQ,CAC5C,KAAM,GACN,MAAO,CACL,eAAgB,EAClB,CACF,CAAC,KAAgB,OAAK,IAAS,OAAQ,CACrC,OAAQA,EACR,MAAO,CACL,MAAO,IACP,eAAgB,CAClB,CACF,CAAC,KAAgB,OAAK,IAAS,OAAQ,CACrC,OAAQA,EACR,MAAO,CACL,MAAO,GACT,EACA,KAAM,OACR,CAAC,KAAgB,QAAM,IAAO,CAC5B,MAAO,CACL,iBAAkB,EACpB,EACA,SAAU,IAAc,OAAK,IAAS,OAAQ,CAC5C,OAAQA,EACR,MAAO,CACL,MAAO,GACT,CACF,CAAC,KAAgB,OAAK,IAAS,OAAQ,CACrC,OAAQA,EACR,MAAO,CACL,MAAO,GACT,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CACH,EACA,GAAesC,EC3DXpD,GAAY,CAAC,MAAM,EAOnBsD,EAAc,SAAqBpD,EAAM,CAC3C,IAAIqD,EAAYrD,EAAK,KACnBsD,EAAOD,IAAc,OAAS,OAASA,EACvChD,KAAO,KAAyBL,EAAMF,EAAS,EACjD,OAAIwD,IAAS,YACS,OAAKC,MAAoB,KAAc,CAAC,EAAGlD,CAAI,CAAC,EAElEiD,IAAS,kBACS,OAAKE,KAA0B,KAAc,CAAC,EAAGnD,CAAI,CAAC,KAExD,OAAKoD,KAAkB,KAAc,CAAC,EAAGpD,CAAI,CAAC,CACpE,EAEA,GAAe+C,C,+KCnBTM,EAA2C,SAAH1D,EAA8B,KAAxB2D,EAAO3D,EAAP2D,QAAYjE,EAAKkE,EAAAA,EAAA5D,EAAAF,CAAA,EACnE+D,MAA8BC,EAAAA,UAAkB,EAAK,EAACC,EAAAC,EAAAA,EAAAH,GAAA,GAA/CI,EAAOF,EAAA,GAAEG,EAAUH,EAAA,GACpBI,EAAsC,eAAAzD,EAAA0D,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAC,EAAOC,EAAO,CAAF,IAAAC,GAAA,OAAAJ,EAAAA,EAAA,EAAAK,KAAA,SAAAC,EAAE,CAAF,cAAAA,EAAAC,KAAAD,EAAAE,KAAE,CAAF,OACzDX,OAAAA,EAAW,EAAI,EAACS,EAAAC,KAAA,EAAAD,EAAAE,KAAA,EAEOlB,GAAO,YAAPA,EACnBa,CACF,EAAC,OAFKC,OAAAA,GAAME,EAAAG,KAGZZ,EAAW,EAAK,EAACS,EAAAI,OAAA,SACVN,EAAM,SAAAE,OAAAA,EAAAC,KAAA,EAAAD,EAAAK,GAAAL,EAAA,SAEbT,EAAW,EAAK,EAACS,EAAAI,OAAA,SACV,EAAE,2BAAAJ,EAAAM,KAAA,IAAAV,EAAA,cAEZ,mBAZ2CW,EAAA,QAAAxE,EAAAyE,MAAA,KAAAC,SAAA,MAc5C,SAAOC,EAAAA,KAACC,EAAAA,GAAMC,EAAAA,EAAAA,EAAAA,EAAA,CAACtB,QAASA,CAAQ,EAAKvE,CAAK,MAAEiE,QAASQ,CAAe,EAAE,CACxE,EAEA,IAAeT,C,kHCnBT8B,KAAoBC,EAAAA,MAAK,kBAC7B,mEAAyCC,KAAK,SAACC,EAAQ,CAAF,MAAM,CACzD,QAASA,EAAM,OACjB,CAAC,CAAC,CAAC,CACL,EAEe,SAASC,EAAgBlG,EAA+B,CACrE,SACE2F,EAAAA,KAACQ,EAAAA,SAAQ,CACPC,YACET,EAAAA,KAAA,OAAAU,YACEV,EAAAA,KAACW,EAAAA,EAAQ,CAACpF,OAAM,GAAE,CAAC,CAChB,EACNmF,YAEDV,EAAAA,KAACG,EAAiBD,EAAAA,EAAA,GAAK7F,CAAK,CAAG,CAAC,CACxB,CAEd,C,8NCAMuG,EAAkD,SAAHjG,EAK/C,KAAAkG,EAJJC,EAASnG,EAATmG,UACAC,EAAIpG,EAAJoG,KACAC,EAAMrG,EAANqG,OACAC,EAAWtG,EAAXsG,YAEMC,KAASC,EAAAA,WAAU,EACzB3C,MAAgCC,EAAAA,UAAkB,EAAK,EAACC,GAAAC,EAAAA,EAAAH,GAAA,GAAjD4C,EAAQ1C,GAAA,GAAE2C,GAAW3C,GAAA,GAE1B4C,EAMER,EANFQ,KACAC,EAKET,EALFS,UACAC,EAIEV,EAJFU,IACAC,EAGEX,EAHFW,WACAC,EAEEZ,EAFFY,sBACAC,EACEb,EADFa,YAEM1D,GACNsD,EADMtD,KAAM2D,EACZL,EADYK,KAAMC,EAClBN,EADkBM,MAAOC,EACzBP,EADyBO,QAASC,EAClCR,EADkCQ,KAAMC,GACxCT,EADwCS,GAAIC,GAC5CV,EAD4CU,UAAWC,GACvDX,EADuDW,eAGnDC,KACJnC,EAAAA,KAACoC,EAAAA,EAAI,CACHC,MAAO,CAAEC,MAAO,UAAWC,OAAQ,SAAU,EAC7CjE,QAAS,SAACkE,EAAM,CACdA,EAAEC,gBAAgB,EAClBpB,GAAY,SAACqB,EAAK,CAAF,MAAK,CAACA,CAAG,EAC3B,EAAEhC,SAEDU,EAAW,OAAS,MAAM,CACvB,EAGR,SACEuB,EAAAA,MAACC,EAAAA,EAAI,CAACtH,KAAK,QAAQuH,UAAU,eAAcnC,SAAA,IACzCV,EAAAA,KAAA,OAAK6C,UAAU,QAAOnC,YACpBiC,EAAAA,MAAA,OAAKE,UAAU,eAAcnC,SAAA,IAC3BiC,EAAAA,MAAA,OAAAjC,SAAA,IACEV,EAAAA,KAACoC,EAAAA,EAAI,CAACnE,KAAK,YAAWyC,YAAEoC,EAAAA,IAAQ,YAAY,CAAC,CAAO,EAAC,OACrD9C,EAAAA,KAACoC,EAAAA,EAAI,CAACC,MAAO,CAAEC,MAAO,UAAWS,YAAa,CAAE,EAAErC,SAC/Ce,CAAU,CACP,CAAC,EACJ,KACLzB,EAAAA,KAACgD,EAAAA,EAAK,EAAE,KACRL,EAAAA,MAAA,OAAAjC,SAAA,IACEV,EAAAA,KAACoC,EAAAA,EAAI,CAACnE,KAAK,YAAWyC,YAAEoC,EAAAA,IAAQ,OAAO,CAAC,CAAO,EAAC,OAChD9C,EAAAA,KAACoC,EAAAA,EAAI,CAACC,MAAO,CAAEC,MAAO,UAAWS,YAAa,CAAE,EAAErC,SAC/CiB,CAAW,CACR,CAAC,EACJ,KACLgB,EAAAA,MAAA,OAAKE,UAAU,kBAAiBnC,SAAA,CAC7B,CAAC,CAACI,EAAUmC,aACXN,EAAAA,MAAAO,EAAAA,SAAA,CAAAxC,SAAA,IACEV,EAAAA,KAACgD,EAAAA,EAAK,EAAE,KACRhD,EAAAA,KAACmD,EAAAA,EAAG,CAACb,MAAM,OAAM5B,YAAEoC,EAAAA,IAAQ,oBAAoB,CAAC,CAAM,CAAC,EACvD,EAEH9B,IAAWoC,UACVT,EAAAA,MAAAO,EAAAA,SAAA,CAAAxC,SAAA,IACEV,EAAAA,KAACgD,EAAAA,EAAK,EAAE,KACPK,EAAAA,IAA2BrC,EAAQD,CAAI,CAAC,EACzC,EACA,MACHG,GAAM,OAAAL,EAANK,EAAQoC,gBAAY,MAAAzC,IAAA,cAApBA,EAAsB0C,SACrB,gDACF,IACEtC,MACEjB,EAAAA,KAACC,EAAAA,GAAM,CACLuD,QAAMxD,EAAAA,KAACyD,EAAAA,EAAY,EAAE,EACrBnI,KAAK,QACLgD,QAAS,kBAAM2C,EAAYH,CAAS,CAAC,EAACJ,YAErCoC,EAAAA,IAAQ,mCAAmC,CAAC,CACvC,CACT,EACA,CAAC,EACH,CAAC,CACH,KACL9C,EAAAA,KAACgD,EAAAA,EAAK,EAAE,KACRhD,EAAAA,KAAA,OAAAU,YACEV,EAAAA,KAACG,EAAAA,QAAiB,CAACuD,UAAWlC,CAAI,CAAE,CAAC,CAClC,KACLmB,EAAAA,MAAA,OAAKE,UAAU,oBAAmBnC,SAAA,IAChCV,EAAAA,KAAC2D,EAAAA,EAAK,CAACC,MAAO,EAAElD,SAAC,WAAS,CAAO,KACjCiC,EAAAA,MAACkB,EAAAA,EAAS,CACRC,SACE1C,EACI,GACA,CACE2C,KAAM,EACNC,WAAY,GACZC,OAAQ9B,CACV,EAEN+B,SAAU,CAAE5C,KAAAA,CAAK,EAAEZ,SAAA,CAElBY,GAAQI,GAAyB,GACjCN,GAAYe,CAAU,EACd,CAAC,EACT,KACLQ,EAAAA,MAAA,OAAKE,UAAU,oBAAmBnC,SAAA,IAChCiC,EAAAA,MAACgB,EAAAA,EAAK,CAACC,MAAO,EAAElD,SAAA,CAAC,gBAAWyD,EAAAA,IAAuBlG,EAAI,CAAC,EAAQ,EAC/DA,KAAS,WAAa+D,IAAMC,QAC3BU,EAAAA,MAACP,EAAAA,EAAI,CAACC,MAAO,CAAE+B,UAAW,QAASC,QAAS,QAASC,MAAO,MAAO,EAAE5D,SAAA,CAClEsB,OACCW,EAAAA,MAAAO,EAAAA,SAAA,CAAAxC,SAAA,CAAE,iBACWV,EAAAA,KAACoC,EAAAA,EAAI,CAAC8B,SAAQ,GAAAxD,SAAEsB,EAAE,CAAO,CAAC,EACrC,EAEHC,OAAaU,EAAAA,MAACP,EAAAA,EAAI,CAAA1B,SAAA,CAAC,iBAAeuB,EAAS,EAAO,KACnDjC,EAAAA,KAAA,OAAK,CAAC,EACF,EAEP4B,MACCe,EAAAA,MAAAO,EAAAA,SAAA,CAAAxC,SAAA,IACEV,EAAAA,KAACoC,EAAAA,EAAI,CAACnE,KAAK,YAAYiG,SAAQ,GAAAxD,SAC5BkB,KAAO5B,EAAAA,KAACuE,EAAAA,EAAI,CAACC,KAAM5C,EAAKlB,SAAEmB,CAAK,CAAO,EAAIA,CAAK,CAC5C,KACN7B,EAAAA,KAAA,OAAK,CAAC,EACN,EAEHkC,OACCS,EAAAA,MAAAO,EAAAA,SAAA,CAAAxC,SAAA,IACEV,EAAAA,KAACoC,EAAAA,EAAI,CAACnE,KAAK,YAAYiG,SAAQ,GAAAxD,SAC5BwB,EAAc,CACX,KACNlC,EAAAA,KAAA,OAAK,CAAC,EACN,KAEJA,EAAAA,KAACoC,EAAAA,EAAI,CACHqC,OAAM,GACNpC,MAAO,CAAE+B,UAAW,QAASC,QAAS,QAASC,MAAO,MAAO,EAAE5D,SAE9DoB,CAAO,CACJ,EACLC,MACC/B,EAAAA,KAACoC,EAAAA,EAAI,CAACC,MAAO,CAAE+B,UAAW,QAASC,QAAS,QAASC,MAAO,MAAO,EAAE5D,SAClEqB,CAAI,CACD,CACP,EACE,CAAC,EACF,CAEV,EAEA,IAAenB,C,wKClKXnG,EAAY,CAAC,aAAc,eAAe,EAM1CiK,EAAY,gBAOZC,EAAmC,aAAiB,SAAUhK,EAAML,EAAK,CAC3E,IAAIM,EAAaD,EAAK,WACpBI,EAAgBJ,EAAK,cACrBK,MAAO,KAAyBL,EAAMF,CAAS,EAC7CmK,MAAU,cAAWC,EAAA,CAAY,EACrC,SAAoB,OAAK,OAAU,KAAc,CAC/C,IAAKvK,EACL,cAAY,KAAc,CACxB,kBAAmBsK,GAAQ,iBAC7B,EAAGhK,CAAU,EACb,UAAW8J,EACX,cAAe3J,EACf,YAAa,CACX,UAAW2J,EACX,gBAAiB,GACjB,0BAA2B,SAAmCI,GAAO,CACnE,SAAOC,EAAA,GAAmBD,IAAQlK,GAAe,KAAgC,OAASA,EAAW,SAAW,MAAM,CACxH,CACF,CACF,EAAGI,EAAI,CAAC,CACV,CAAC,EACD,EAAe2J,EClCX,EAAY,CAAC,aAAc,gBAAiB,MAAO,MAAO,OAAQ,QAAS,WAAY,OAAO,EAS9FK,EAA6B,aAAiB,SAAUrK,EAAML,EAAK,CACrE,IAAIM,EAAaD,EAAK,WACpBI,EAAgBJ,EAAK,cACrBsK,GAAMtK,EAAK,IACXuK,GAAMvK,EAAK,IACXwK,EAAOxK,EAAK,KACZyK,GAAQzK,EAAK,MACb0K,EAAW1K,EAAK,SAChB2K,EAAQ3K,EAAK,MACbK,KAAO,KAAyBL,EAAM,CAAS,EACjD,SAAoB,OAAK,OAAU,KAAc,CAC/C,UAAW,SACX,cAAY,QAAc,KAAc,CAAC,EAAGC,CAAU,EAAG,CAAC,EAAG,CAC3D,IAAKqK,GACL,IAAKC,GACL,KAAMC,EACN,MAAOC,GACP,SAAUC,EACV,MAAOC,EACP,MAAO1K,GAAe,KAAgC,OAASA,EAAW,KAC5E,CAAC,EACD,IAAKN,EACL,cAAeS,EACf,YAAa,CACX,YAAa,EACf,CACF,EAAGC,CAAI,CAAC,CACV,CAAC,EACD,EAAegK,E,WCtCf,EAAe,CAAC,cAAgB,wBAAwB,OAAS,gBAAgB,ECQ3EO,GAAkB,KAClBJ,EAAO,GACPK,EAAmB,UAAM,CAC7B,SACE7C,EAAAA,MAAA,OAAKE,UAAW4C,EAAOC,cAAchF,SAAA,IACnCV,EAAAA,KAAC2F,EAA0B,CACzB5E,KAAK,YACL6E,SAAO9C,EAAAA,IAAQ,6BAA6B,EAC5ClI,WAAY,CACViL,WAAY,CAAC,GAAM,EAAI,EACvBC,YAAa,IACXhD,EAAAA,IAAQ,mCAAmC,KAC3CA,EAAAA,IAAQ,mCAAmC,CAAC,CAEhD,CAAE,CACH,KACD9C,EAAAA,KAACgF,EAAa,CACZjE,KAAK,eACLuE,MAAK,GACLJ,IAAKK,GACLN,IAAK,EACLG,MAAOW,OAAOC,YACZC,EAAAA,EAAIC,MAAMC,KAAKC,MAAMb,GAAkBJ,CAAI,CAAC,CAAC,EAAEkB,IAAI,SAACrK,EAAGsK,EAAG,CAAF,MAAK,CAC3DA,EAAInB,EAAI,GAAAoB,OACLD,EAAInB,CAAI,EACZ,EACH,EACAS,SAAO9C,EAAAA,IAAQ,gCAAgC,EAC/ClI,WAAY,CAAE0K,MAAO,GAAMzC,UAAW4C,EAAOe,MAAO,CAAE,CACvD,KACDxG,EAAAA,KAACtF,EAAAA,EAAa,CACZqG,KAAK,iBACL6E,SAAO9C,EAAAA,IAAQ,qCAAqC,CAAE,CACvD,KACD9C,EAAAA,KAACtF,EAAAA,EAAa,CACZqG,KAAK,WACL6E,SAAO9C,EAAAA,IAAQ,2BAA2B,CAAE,CAC7C,KACD9C,EAAAA,KAACtF,EAAAA,EAAa,CAACqG,KAAK,OAAO6E,SAAO9C,EAAAA,IAAQ,eAAe,CAAE,CAAE,KAC7D9C,EAAAA,KAACtF,EAAAA,EAAa,CAACqG,KAAK,UAAU6E,SAAO9C,EAAAA,IAAQ,WAAW,CAAE,CAAE,CAAC,EAC1D,CAET,EAEA,EAAe0C,C,2LCvCFiB,EAAc,SAACC,EAAiD,KAAAC,EAAAC,EACnEC,EACNH,EADMG,UAAWC,EACjBJ,EADiBI,aAAcC,GAC/BL,EAD+BK,eAAgB9D,EAC/CyD,EAD+CzD,SAAU+D,EACzDN,EADyDM,KAAMC,EAC/DP,EAD+DO,QAEjE,MAAO,CACLC,SAAUL,GAAS,OAAAF,EAATE,EAAY,CAAC,KAAC,MAAAF,IAAA,cAAdA,EAAgBQ,KAAK,EAC/BC,SAAUP,GAAS,OAAAD,EAATC,EAAY,CAAC,KAAC,MAAAD,IAAA,cAAdA,EAAgBO,KAAK,EAC/BE,kBAAmBP,GAAY,YAAZA,EAAe,CAAC,EACnCQ,kBAAmBR,GAAY,YAAZA,EAAe,CAAC,EACnCS,oBAAqBR,GACrBS,cAAevE,EACf+D,KAAAA,EACAC,QAAAA,CACF,CACF,EAEaQ,EAAc,SAACf,EAAiD,CAC3E,IACEQ,EAQER,EARFQ,SACAE,EAOEV,EAPFU,SACAC,EAMEX,EANFW,kBACAC,EAKEZ,EALFY,kBACAC,GAIEb,EAJFa,oBACAC,EAGEd,EAHFc,cACAR,EAEEN,EAFFM,KACAC,EACEP,EADFO,QAEF,MAAO,CACLJ,UAAW,CAACK,EAAUE,CAAQ,EAAEf,IAAI,SAACqB,EAAG,CAAF,OACpCA,IAAMtE,OAAYA,OAAYuE,EAAAA,EAAM,GAADpB,OAAImB,EAAC,SAAQ,CAAC,CACnD,EACAZ,aAAc,CAACO,EAAmBC,CAAiB,EACnDP,eAAgBQ,GAChBtE,SAAUuE,EACVR,KAAAA,EACAC,QAAAA,CACF,CACF,EAEaW,EAAgB,SAACC,EAA2B,CACvD,IAAAC,EAAeC,EAAAA,EAAKC,QAA6B,EAACC,EAAAtJ,EAAAA,EAAAmJ,EAAA,GAA3CI,EAAID,EAAA,GACXzJ,KAAoCC,EAAAA,UAA0B,EAACC,GAAAC,EAAAA,EAAAH,EAAA,GAAxD2J,EAAUzJ,GAAA,GAAE0J,EAAa1J,GAAA,MAEhC2J,EAAAA,WAAU,UAAM,CACd,IAAMC,EAASb,EAAYI,GAAQ,CAAC,CAAC,EACrCK,EAAKK,eAAeD,CAAM,EAC1BF,EAAcE,CAAM,CACtB,EAAG,CAACJ,EAAML,CAAI,CAAC,EAEf,IAAMW,EAAe,SAACC,EAAmD,CACvE,OAAA1J,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAO,SAAAC,GAAA,KAAAwH,EAAA,OAAA1H,EAAAA,EAAA,EAAAK,KAAA,SAAAC,GAAA,eAAAA,GAAAC,KAAAD,GAAAE,KAAA,QACCkH,OAAAA,EAASwB,EAAKQ,eAAe,EACnCD,GAAU,MAAVA,EAAahC,EAAYC,CAAM,CAAC,EAChC0B,EAAc1B,CAAM,EAACpH,GAAAI,OAAA,SACd,EAAI,0BAAAJ,GAAAM,KAAA,IAAAV,CAAA,EACZ,EACH,EAEA,MAAO,CACLgJ,KAAAA,EACAM,aAAAA,EACAG,MAAO,kBAAMT,EAAKK,eAAeJ,GAAc,CAAC,CAAC,CAAC,EAClDS,QAASV,EAAKW,gBAAgB,CAChC,CACF,C,gHC7EIC,EAAY,OAAO,eACnBC,EAAsB,OAAO,sBAC7BC,EAAe,OAAO,UAAU,eAChCC,EAAe,OAAO,UAAU,qBAChCC,EAAkB,CAACC,EAAKvN,EAAKkJ,IAAUlJ,KAAOuN,EAAML,EAAUK,EAAKvN,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAkJ,CAAM,CAAC,EAAIqE,EAAIvN,CAAG,EAAIkJ,EACtJsE,EAAiB,CAACC,EAAGC,IAAM,CAC7B,QAASC,KAAQD,IAAMA,EAAI,CAAC,GACtBN,EAAa,KAAKM,EAAGC,CAAI,GAC3BL,EAAgBG,EAAGE,EAAMD,EAAEC,CAAI,CAAC,EACpC,GAAIR,EACF,QAASQ,KAAQR,EAAoBO,CAAC,EAChCL,EAAa,KAAKK,EAAGC,CAAI,GAC3BL,EAAgBG,EAAGE,EAAMD,EAAEC,CAAI,CAAC,EAEtC,OAAOF,CACT,EAEA,MAAMG,EAAanP,GAA0B,gBAAoB,MAAO+O,EAAe,CAAE,MAAO,IAAK,OAAQ,IAAK,KAAM,OAAQ,MAAO,4BAA6B,EAAG/O,CAAK,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,2JAA4J,OAAQ,OAAQ,YAAa,GAAK,cAAe,QAAS,eAAgB,QAAS,gBAAiB,KAAM,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,2IAA4I,KAAM,SAAU,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,kIAAmI,KAAM,SAAU,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,6MAA8M,KAAM,OAAQ,OAAQ,OAAQ,YAAa,GAAK,cAAe,QAAS,eAAgB,OAAQ,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,4IAA6I,KAAM,OAAQ,OAAQ,OAAQ,YAAa,GAAK,cAAe,QAAS,eAAgB,OAAQ,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,yBAA0B,OAAQ,OAAQ,YAAa,GAAK,cAAe,QAAS,eAAgB,OAAQ,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,8KAA+K,KAAM,OAAQ,OAAQ,OAAQ,YAAa,GAAK,cAAe,QAAS,eAAgB,OAAQ,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,kMAAmM,KAAM,UAAW,OAAQ,OAAQ,YAAa,GAAK,cAAe,QAAS,eAAgB,OAAQ,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,2BAA4B,OAAQ,OAAQ,YAAa,GAAK,cAAe,QAAS,eAAgB,OAAQ,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,uYAAwY,KAAM,SAAU,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,6DAA8D,OAAQ,OAAQ,YAAa,GAAK,cAAe,QAAS,eAAgB,OAAQ,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,0OAA2O,KAAM,MAAO,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,yHAA0H,KAAM,MAAO,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,qEAAsE,KAAM,MAAO,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,0HAA2H,KAAM,MAAO,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,kKAAmK,KAAM,MAAO,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,sIAAuI,OAAQ,OAAQ,YAAa,GAAK,cAAe,QAAS,eAAgB,OAAQ,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,mJAAoJ,KAAM,SAAU,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,uIAAwI,OAAQ,OAAQ,YAAa,GAAK,cAAe,QAAS,eAAgB,OAAQ,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,sIAAuI,KAAM,MAAO,CAAC,CAAC,EAE7kK,MAAe,q8K,gJCLToP,EAA0C,SAAH9O,EAGvC,KAFJ8N,EAAU9N,EAAV8N,WACAiB,EAAa/O,EAAb+O,cAEAC,KAAsC/B,EAAAA,IAAc8B,CAAa,EAAzDxB,GAAIyB,EAAJzB,KAAMM,EAAYmB,EAAZnB,aAAcG,GAAKgB,EAALhB,MAE5B,SACE3I,EAAAA,KAAC4J,EAAAA,EAAS,CACR/H,SAAOiB,EAAAA,IAAQ,wBAAwB,EACvCwB,MAAO,IACPhJ,KAAK,QACLuO,WAAS7J,EAAAA,KAACC,EAAAA,GAAM,CAAC3E,KAAK,QAAQkI,QAAMxD,EAAAA,KAAC5F,EAAAA,EAAc,EAAE,EAAG6D,KAAK,MAAM,CAAE,EACrEiK,KAAMA,GACN4B,SAAUtB,EAAaC,CAAU,EACjCsB,WAAY,CAAEC,SAAUrB,EAAM,EAAEjI,YAEhCV,EAAAA,KAACwF,EAAAA,EAAM,EAAE,CAAC,CACD,CAEf,EAEA,GAAeiE,E,uHCjBFQ,EAAe,GAEtBC,EAAS,eAAAvP,EAAAoE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAC,EAChBiL,EACAC,EACAC,EAAyB,KAAAC,GAAAC,EAAAC,GAAAC,GAAApP,GAAAqP,EAAAC,EAAA,OAAA3L,EAAAA,EAAA,EAAAK,KAAA,SAAAC,EAAA,eAAAA,EAAAC,KAAAD,EAAAE,KAAA,WAEpB2K,EAASS,UAAUC,OAAQ,CAAFvL,EAAAE,KAAA,eAAAF,EAAAI,OAAA,SAAS,CAAC,CAAC,SAG7B,GAH6BJ,EAAAK,GAGvCyK,EAAU9K,EAAAK,GAAA,CAAAL,EAAAE,KAAA,SAAAF,OAAAA,EAAAE,KAAA,KACHsL,EAAAA,IAAuBX,CAAQ,EAAC,UAAA7K,EAAAyL,GAAAT,GAAAhL,EAAAG,KAAAH,EAAA0L,GAAA1L,EAAAyL,KAAA,KAAAzL,EAAA0L,GAAA,CAAA1L,EAAAE,KAAA,SAAAF,EAAA0L,GAAAV,KAAA,mBAAAhL,EAAA0L,GAAA,CAAA1L,EAAAE,KAAA,SAAAF,EAAA2L,GAAA,OAAA3L,EAAAE,KAAA,iBAAAF,EAAA2L,GAAvCX,GAA0CjE,IAAI,SAAC6E,GAAG,CAAF,SAAKC,EAAAA,IAAmBD,EAAC,CAAC,GAAC,QAAA5L,EAAAK,GAAAL,EAAA2L,GAAA,QAFvEV,OAAAA,EAAIjL,EAAAK,GAGJ6K,MAAaW,EAAAA,IAAmBhB,CAAQ,EAAC7K,EAAAE,KAAA,MAE7B4L,EAAAA,SAAQ,sBAAuB,CAC/CC,OAAQ,OACRC,cAAe,GACfC,KAAIrL,EAAAA,EAAA,CACFsK,WAAAA,GACAD,KAAMA,GAAI,MAAJA,EAAMM,OAASN,EAAO,CAACC,EAAU,EACvCgB,aAAcvB,CAAY,EACvBI,CAAO,CAEd,CAAC,EACEoB,OAAO,EACPC,IAAI,EAAC,QAXC,GAAHjB,GAAGnL,EAAAG,KAAA,CAaLgL,GAAK,CAAFnL,EAAAE,KAAA,SAAAnE,OAAAA,GAEHoP,GADMC,EAAkBrP,GAAlBqP,mBAAoBC,EAAetP,GAAfsP,gBAAerL,EAAAI,OAAA,YAAA6G,OAAAN,EAAAA,EAItC0E,EAAgBtE,IAAI,SAACsF,GAAG,CAAF,SAAKC,EAAAA,IAAgCD,GAAG,EAAI,CAAC,EAAC,EAAA1F,EAAAA,EACpEyE,EAAmBrE,IAAI,SAACsF,GAAG,CAAF,SAC1BC,EAAAA,IAAgCD,GAAG,EAAK,CAAC,CAC3C,CAAC,kBAGC,IAAIE,MAAM,6BAA6B,EAAC,yBAAAvM,EAAAM,KAAA,IAAAV,CAAA,EAC/C,mBArCcW,EAAAiM,EAAAC,EAAA,QAAApR,EAAAmF,MAAA,KAAAC,SAAA,MAuCFiM,EAAe,SAC1B7B,EACAI,EACAF,EACG,CACH,IAAA4B,KAA4CC,GAAAA,GAAS,CACnDC,SAAU,CAAC,sBAAuBhC,EAAUI,EAAMF,CAAO,EACzD+B,QAAS,kBAAOjC,EAAWD,EAAUC,EAAUI,EAAMF,CAAO,EAAI,CAAC,CAAC,EAClEgC,QAAS,CAAC,CAAClC,CACb,CAAC,EAJOoB,GAAIU,EAAJV,KAAMe,EAAKL,EAALK,MAAOC,GAASN,EAATM,UAAWC,GAAOP,EAAPO,QAM1BC,GAAalB,IAAI,YAAJA,GAAMmB,OAAO,SAACf,EAAG,CAAF,OAC/BA,EAAE3K,OAASqJ,GAAO,YAAPA,EAASrD,KAAOqD,GAAO,YAAPA,EAASpD,WAAa,EAAoB,CACxE,EAEA,MAAO,CAAEwF,WAAAA,GAAYE,MAAOF,IAAU,YAAVA,GAAY5B,OAAQyB,MAAAA,EAAOC,UAAAA,GAAWC,QAAAA,EAAQ,CAC5E,EC5DMI,GACJ,SAACrC,EAAwC,CAAF,OACvC,SAACzJ,EAAwBE,EAAkB,CAAF,IAAA6L,EAAA,SAErC7M,EAAAA,KAACY,GAAAA,EAAe,CACdE,UAAWA,EACXE,OAAQA,EACRD,KAAMwJ,GAAI,OAAAsC,EAAJtC,EAAMuC,KAAK,SAAC5B,GAAG,CAAF,OAAKA,GAAE1J,MAAQV,EAAUiM,KAAK,MAAC,MAAAF,IAAA,cAA5CA,EAA8C9L,IAAK,CAC1D,CAAC,CACH,EASCiM,GAAgD,SAAHrS,EAK7C,KAJJwP,EAAQxP,EAARwP,SACA8C,EAAUtS,EAAVsS,WACA1C,EAAI5P,EAAJ4P,KAAI2C,GAAAvS,EACJwS,SAAAA,EAAQD,KAAA,OAAGN,GAAcrC,CAAI,EAAC2C,GAE9BE,MAA6CC,EAAAA,IAAoB,EAACC,GAAAF,GAA1DG,QAAOC,GAAAF,KAAA,OAAuB,CAAC,EAACA,GAAAG,EAAAD,GAArB1M,UAAAA,EAAS2M,IAAA,OAAG,CAAC,EAACA,EACjCjP,KAA0CC,EAAAA,UAA0B,EAACC,EAAAC,EAAAA,EAAAH,EAAA,GAA9DkP,GAAahP,EAAA,GAAEiP,GAAgBjP,EAAA,GAChC2L,GAAOnK,EAAAA,EAAAA,EAAAA,EAAA,GAAQY,CAAS,EAAK4M,EAAa,EAChDE,EAAkC5B,EAChC7B,EACAI,GAAI,YAAJA,EAAMlE,IAAI,SAAC6E,GAAG,CAAF,OAAKA,GAAE1J,GAAG,GACtB6I,EACF,EAJQkC,EAASqB,EAATrB,UAAWE,EAAUmB,EAAVnB,WAMnBoB,MAAgCpP,EAAAA,UAAmB,CAAEqP,KAAM,EAAGC,SAAU,EAAG,CAAC,EAACC,GAAArP,EAAAA,EAAAkP,GAAA,GAAtEI,GAAQD,GAAA,GAAEE,GAAWF,GAAA,GAE5B,SACErL,EAAAA,MAACwL,EAAAA,GAAc,CACbC,YAAa,oBACXpO,EAAAA,KAACqO,EAAAA,EAAS,CAACC,SAAOtO,EAAAA,KAACuO,EAAU,EAAE,EAAGC,OAAK1L,EAAAA,IAAQ,kBAAkB,CAAE,CAAE,CAAC,EACtEpC,SAAA,IAEFV,EAAAA,KAACyO,EAAAA,GAAO,CACNC,MAAK,GACL9P,QAAS2N,EACToC,WACElC,GAAU,MAAVA,EAAY5B,OACR,CACE+D,QAASX,GAASH,KAClBC,SAAUE,GAASF,SACnBc,OAAQ,GACRlC,MAAOxG,KAAKlB,KAAIwH,GAAU,YAAVA,EAAY5B,SAAU,EAAGZ,CAAY,EACrD6E,SAAU,SAAChB,GAAMC,GAAU,CAAF,OAAKG,GAAY,CAAEJ,KAAAA,GAAMC,SAAAA,EAAS,CAAC,CAAC,CAC/D,EACA,GAENgB,WAAY,SAACC,GAAM,CAAF,OAAK7B,EAAS6B,GAAMA,GAAKhO,MAAM,CAAC,EACjDiO,KAAM,CAAEC,OAAQ,CAAE,EAClBC,WAAY1C,CAAW,CACxB,EACAQ,GACCmC,EAAAA,gBACEpP,EAAAA,KAACyJ,GAAW,CAAChB,WAAYkF,GAAkBjE,cAAeW,EAAQ,CAAE,EACpE4C,CACF,CAAC,EACW,CAEpB,EAEA,GAAeD,E,sKC7EFqC,EAAyB,SACpCpR,EACqC,CACrC,OAAQA,EAAM,CACZ,IAAK,UACH,MAAO,UACT,IAAK,SACH,MAAO,SACT,QACE,OAAOA,CACX,CACF,EAEa2N,EAAkC,SAAHjR,EAAA,KAExC2U,EAAE3U,EAAF2U,GAAEC,EAAA5U,EACF6U,uBAAAA,EAAsBD,IAAA,OAAG,GAAEA,EAAAE,EAAA9U,EAC3BoS,MAAAA,EAAK0C,IAAA,OAAG,GAAEA,EAAAC,EAAA/U,EACVmG,UAAAA,EAAS4O,IAAA,OAAG,GAAEA,EAAAC,EAAAhV,EACd8G,WAAAA,EAAUkO,IAAA,OAAG,EAACA,EAAAC,EAAAjV,EACd6G,IAAAA,GAAGoO,IAAA,OAAG,GAAEA,EAAAC,EAAAlV,EACRmV,WAAAA,EAAUD,IAAA,OAAG,GAAEA,EACfE,EAAMpV,EAANoV,OAAMC,EAAArV,EACNsV,eAAAA,EAAcD,IAAA,OAAG,GAAEA,EAAAE,EAAAvV,EACnBwV,eAAAA,EAAcD,IAAA,OAAG,UAASA,EAAAE,GAAAzV,EAC1BkH,MAAAA,GAAKuO,KAAA,OAAG,GAAEA,GAAAC,EAAA1V,EACVmH,QAAAA,GAAOuO,IAAA,OAAG,GAAEA,EAAAC,EAAA3V,EACZoH,KAAAA,EAAIuO,IAAA,OAAG,GAAEA,EAAAC,EAAA5V,EACTsH,UAAAA,EAASsO,IAAA,OAAG,GAAEA,EAAAC,EAAA7V,EACduH,eAAAA,EAAcsO,IAAA,OAAG,GAAEA,EAAAC,GAAA9V,EACnB+V,UAAAA,EAASD,KAAA,OAAG,GAAEA,GAAAE,EAAAhW,EACdiW,YAAAA,EAAWD,IAAA,OAAG,GAAKA,EAErB3P,EAAejB,UAAA8K,OAAA,GAAA9K,UAAA,KAAAqD,OAAArD,UAAA,GAAG,GAAK,MACN,CACjBuP,GAAAA,EACA9N,IAAAA,GACAR,OAAAA,EACAM,KAAMR,GAAa,GACnBY,sBAAuB8N,GAA0B,GACjDS,eAAgBA,GAAkB,GAClClD,MAAOA,GAAS,GAChBxL,UAAW,CACTtD,KAAMoR,EAAuBc,CAAc,EAC3CtO,MAAOA,IAAS,GAChBC,QAASA,IAAW,GACpBC,KAAMA,GAAQ,GACdC,GAAI0O,GAAa,GACjBxO,eAAgBA,GAAkB,GAClCD,UAAWA,GAAa,EAC1B,EACAN,YAAamO,GAAc,GAC3Be,YAAad,GAAU3M,OACvB3B,WAAYA,GAAc,EAC1BwB,SAAU2N,GAAe,EAC3B,CAAC,EAEYzM,EAAyB,SACpClG,EACiB,CACjB,OAAQA,EAAM,CACZ,IAAK,UACH,SAAO+B,EAAAA,KAACmD,EAAAA,EAAG,CAACb,MAAM,QAAO5B,SAAC,SAAO,CAAK,EACxC,IAAK,SACH,SAAOV,EAAAA,KAACmD,EAAAA,EAAG,CAACb,MAAM,OAAM5B,SAAC,QAAM,CAAK,EACtC,QACE,SAAOV,EAAAA,KAACmD,EAAAA,EAAG,CAACb,MAAM,SAAQ5B,SAAC,QAAM,CAAK,CAC1C,CACF,EAEa2C,EAA6B,SACxCrC,EACAD,EACiB,CACjB,OAAQC,EAAQ,CACd,IAAK,GACH,SACEhB,EAAAA,KAACmD,EAAAA,EAAG,CAACb,MAAM,QAAO5B,SACfK,KACG+P,EAAAA,IAAK,EAAC,wBAAAvK,OACoBxF,CAAI,WAAAwF,OACxBxF,EAAI,UAAAwF,UAAIzD,EAAAA,IAAQ,eAAe,CAAC,KACtCA,EAAAA,IAAQ,eAAe,CAAC,CACzB,EAGT,IAAK,GACH,SACE9C,EAAAA,KAACmD,EAAAA,EAAG,CAACb,MAAM,OAAM5B,SACdK,KACG+P,EAAAA,IAAK,EAAC,iBAAAvK,OACaxF,CAAI,WAAAwF,OACjBxF,EAAI,UAAAwF,UAAIzD,EAAAA,IAAQ,WAAW,CAAC,KAClCA,EAAAA,IAAQ,WAAW,CAAC,CACrB,EAET,QACE,SAAO9C,EAAAA,KAAAkD,EAAAA,SAAA,EAAI,CACf,CACF,C,uNCzGI4F,EAAY,OAAO,eACnBC,EAAsB,OAAO,sBAC7BC,EAAe,OAAO,UAAU,eAChCC,EAAe,OAAO,UAAU,qBAChCC,EAAkB,CAACC,EAAKvN,EAAKkJ,IAAUlJ,KAAOuN,EAAML,EAAUK,EAAKvN,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAAkJ,CAAM,CAAC,EAAIqE,EAAIvN,CAAG,EAAIkJ,EACtJsE,EAAiB,CAACC,EAAGC,IAAM,CAC7B,QAASC,KAAQD,IAAMA,EAAI,CAAC,GACtBN,EAAa,KAAKM,EAAGC,CAAI,GAC3BL,EAAgBG,EAAGE,EAAMD,EAAEC,CAAI,CAAC,EACpC,GAAIR,EACF,QAASQ,KAAQR,EAAoBO,CAAC,EAChCL,EAAa,KAAKK,EAAGC,CAAI,GAC3BL,EAAgBG,EAAGE,EAAMD,EAAEC,CAAI,CAAC,EAEtC,OAAOF,CACT,EAEA,MAAM0H,GAAc1W,GAA0B,gBAAoB,MAAO+O,EAAe,CAAE,KAAM,OAAQ,MAAO,4BAA6B,EAAG/O,CAAK,EAAmB,gBAAoB,IAAK,CAAE,SAAU,sBAAuB,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,iBAAkB,OAAQ,UAAW,cAAe,QAAS,eAAgB,OAAQ,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,2CAA4C,KAAM,SAAU,CAAC,EAAmB,gBAAoB,SAAU,CAAE,GAAI,EAAG,GAAI,EAAG,EAAG,IAAK,OAAQ,SAAU,CAAC,CAAC,EAAmB,gBAAoB,OAAQ,KAAsB,gBAAoB,WAAY,CAAE,GAAI,gBAAiB,EAAmB,gBAAoB,OAAQ,CAAE,KAAM,OAAQ,EAAG,eAAgB,CAAC,CAAC,CAAC,CAAC,EAE7wB,MAAe,ygB,iSCRA,SAAS2W,EAAmB3W,EAA2B,CACpE,IAAA4W,KAAgCC,EAAAA,UAAS,UAAU,EAA3CC,EAAmBF,EAAnBE,oBACFC,EAAc,SAACC,EAAoB,CACvC,GAAI,CAACF,EAAqB,MAAO,GACjC,IAAIG,EAAY,GAChBvL,cAAOwL,KAAKJ,CAAmB,EAAE9K,IAAI,SAACzK,EAAgB,CACpD,IAAI4V,GAAsBL,EAAoBvV,CAAG,EACjD,OAAI4V,IAAaA,GAAUjO,SAAS8N,CAAO,IAAGC,EAAY1V,GACnD,IACT,CAAC,KACMkH,EAAAA,IAAQwO,CAAS,CAC1B,EAEMG,GAAU,SAACC,EAAiB9P,EAAiB,CACjD,IAAI+P,EAAiB,CAAC,EAACC,GAAAC,GAAAA,EACJH,CAAK,EAAAI,GAAA,IAAxB,IAAAF,GAAAG,EAAA,IAAAD,GAAAF,GAAAI,EAAA,GAAAC,MAA0B,KAAfC,GAAIJ,GAAAhN,MACTqN,GAAMf,EAAYc,EAAI,EACtBC,IAAKR,EAAKS,KAAKD,EAAG,CACxB,CAAC,OAAAE,GAAA,CAAAT,GAAApP,EAAA6P,EAAA,UAAAT,GAAAU,EAAA,EACD,SAAIC,EAAAA,SAAQZ,CAAI,EAAU,IACnB/P,KAAO5B,EAAAA,KAAA,KAAGwE,KAAM5C,EAAKlB,SAAEiR,EAAKa,KAAK,QAAG,CAAC,CAAI,EAAIb,EAAKa,KAAK,QAAG,CACnE,EAEMC,GAAW,CACf,CACE5Q,SAAOiB,EAAAA,IAAQ,YAAY,EAC3B4P,UAAW,mBACXC,OAAQ,SAACC,EAAgB,CAAF,OACrBA,KACE5S,EAAAA,KAACO,GAAAA,EAAe,CAACmD,UAAWkP,EAAQ/P,UAAU,YAAY,CAAE,EAE5D,EACD,CACL,EACA,CACEhB,MAAO,MACP6Q,UAAW,SACXC,OAAQ,SAACrR,EAAc,CAAF,OACnBA,GAAQA,IAAS,OACftB,EAAAA,KAAC6D,EAAAA,EAAS,CAACC,SAAU,GAAOI,SAAQ,GAAC7B,MAAO,CAAEwQ,UAAW,MAAO,EAAEnS,SAC/DY,CAAI,CACI,EAEX,GACD,CACL,EACA,CACEO,SAAOiB,EAAAA,IAAQ,YAAY,EAC3B4P,UAAW,iBACXC,OAAQ,SAACrR,EAAcwR,EAAsB,CAAF,OACzCxR,KACIwP,EAAAA,IAAK,EAAC,cAAAvK,OACKjF,EAAKyR,QAAQ,CAAC,EAAC,KAAAxM,OACtBuM,GAAM,MAANA,EAAQE,aAAazP,SAAS,GAAG,EAAI,IAAM,IAAI,WAAAgD,OAE7CjF,EAAKyR,QAAQ,CAAC,EAAC,KAAAxM,OACjBuM,GAAM,MAANA,EAAQE,aAAazP,SAAS,GAAG,EAAI,IAAM,KAAI,UAEnD,GAAG,CACX,EACA,CACE1B,SAAOiB,EAAAA,IAAQ,QAAQ,EACvB4P,UAAW,aACXC,OAAQ,SAACM,EAAeH,EAAsB,CAAF,SAC1CI,EAAAA,IAAaJ,GAAM,YAANA,EAAQK,UAAU,GAC/BL,IAAM,MAANA,IAAM,QAANA,EAAQK,WAAW,CAAC,GACpBL,IAAM,MAANA,IAAM,QAANA,EAAQK,WAAW,CAAC,EAAC,GAAA5M,OACduM,GAAM,YAANA,EAAQK,WAAW,CAAC,CAAC,EAAA5M,OAAGuM,GAAM,YAANA,EAAQE,aAAY,KAAAzM,OAAIuM,GAAM,YAANA,EAAQK,WAAW,CAAC,CAAC,EAAA5M,OAAGuM,GAAM,YAANA,EAAQE,YAAY,EAC/F,GAAG,CACX,EACA,CACEnR,SAAOiB,EAAAA,IAAQ,OAAO,EACtB4P,UAAW,QACXpO,MAAO,IACPqO,OAAQ,SAACM,EAAeH,EAAsB,CAAF,SACzCP,EAAAA,SAAQO,GAAM,YAANA,EAAQpB,KAAK,EAElB,IADAD,GAAQqB,GAAM,YAANA,EAAQpB,MAAmBoB,GAAM,YAANA,EAAQM,mBAAmB,CAC3D,CACX,EACA,CACEvR,SAAOiB,EAAAA,IAAQ,UAAU,EACzB4P,UAAW,SACXC,OAAQ,SAACrR,EAAcwR,EAAsB,CAAF,OACzCA,GAAM,MAANA,EAAQO,eACNrT,EAAAA,KAAA,KAAG1B,QAAS,kBAAMgV,OAAOC,KAAKT,GAAM,YAANA,EAAQO,WAAW,CAAC,EAAC3S,SAAEY,CAAI,CAAI,EAE7DA,CACD,CACL,EACA,CACEO,SAAOiB,EAAAA,IAAQ,SAAS,EACxB4P,UAAW,eACXC,OAAQ,SAACM,EAAeH,EAAsB,CAAF,IAAAU,EAAA,OAC1CV,GAAM,OAAAU,EAANV,EAAQW,gBAAY,MAAAD,IAAA,cAApBA,EAAsBE,OAAO,CACjC,CAAC,EAEH,SACE1T,EAAAA,KAAC2T,GAAAA,EAAK,CACJC,QAASnB,GACTtD,WAAY9U,GAAK,YAALA,EAAOwZ,eACnBlF,WAAY,EAAM,CACnB,CAEL,C,eCjHA,EAAe,CAAC,qBAAuB,+BAA+B,QAAU,kBAAkB,kBAAoB,4BAA4B,WAAa,qBAAqB,OAAS,iBAAiB,eAAiB,yBAAyB,mBAAqB,6BAA6B,cAAgB,wBAAwB,qBAAuB,+BAA+B,oBAAsB,8BAA8B,UAAY,oBAAoB,YAAc,qBAAqB,EC2BzfmF,GAAoB,SAACxN,EAAS,UAAAC,UAC/BzD,EAAAA,IAAQ,UAAU,CAAC,EAAAyD,OAAGD,EAAI,CAAC,GAE1ByN,EAAmB,SACvB5J,EACA6J,EACY,KAAAC,EACZ,QAAOA,EAAA9J,EAAS+J,mBAAe,MAAAD,IAAA,cAAxBA,EAA0BE,UAAU,SAACxI,GAAG,CAAF,OAAKA,GAAE2D,KAAO0E,CAAS,MAAM,EAC5E,EAeMI,GAAoD,SAAHzZ,EAQjD,KAAA0Z,EAPJlK,EAAQxP,EAARwP,SACAmK,GAAS3Z,EAAT2Z,UACAC,GAAiB5Z,EAAjB4Z,kBACAC,GAAQ7Z,EAAR6Z,SACAC,EAAkB9Z,EAAlB8Z,mBACAC,EAAc/Z,EAAd+Z,eACAC,EAAkBha,EAAlBga,mBAEA1D,MASIC,EAAAA,UAAS,SAAS,EARpB0D,GAAc3D,GAAd2D,eACAC,GAAmB5D,GAAnB4D,oBACAC,GAAY7D,GAAZ6D,aACAC,GAAW9D,GAAX8D,YACAC,GAAM/D,GAAN+D,OACAC,GAAchE,GAAdgE,eACAC,GAAcjE,GAAdiE,eACAC,GAAMlE,GAANkE,OAEFC,MAAmClE,EAAAA,UAAS,UAAU,EAA9CmE,GAAsBD,GAAtBC,uBACFnU,MAASC,EAAAA,WAAU,EACzB3C,MAA4CC,EAAAA,UAA0B,CAAC,CAAC,EAACC,GAAAC,EAAAA,EAAAH,GAAA,GAAlE8W,GAAc5W,GAAA,GAAE6W,GAAiB7W,GAAA,GACxC8W,MAA2BC,EAAAA,GAAc,EAAjCC,GAAKF,GAALE,MAAO9W,GAAO4W,GAAP5W,QACfiP,MAAsCpP,EAAAA,UAAS,EAAK,EAACuP,GAAArP,EAAAA,EAAAkP,GAAA,GAA9C8H,GAAW3H,GAAA,GAAE4H,GAAc5H,GAAA,GAClC6H,MAA4CpX,EAAAA,UAAyB,CAAC,CAAC,EAACqX,GAAAnX,EAAAA,EAAAkX,GAAA,GAAjEhC,EAAciC,GAAA,GAAEC,GAAiBD,GAAA,GAClCE,GAAa,eAAA3a,GAAA0D,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAC,IAAA,KAAA+W,EAAAC,GAAA3K,GAAA,OAAAvM,EAAAA,EAAA,EAAAK,KAAA,SAAAC,GAAA,eAAAA,GAAAC,KAAAD,GAAAE,KAAA,WACf2K,GAAQ,OAAA8L,EAAR9L,EAAUS,aAAS,MAAAqL,IAAA,QAAnBA,EAAqBpL,OAAQ,CAAFvL,GAAAE,KAAA,eAAAF,GAAAI,OAAA,iBAAAJ,OAAAA,GAAAE,KAAA,EACTkW,MACrBS,EAAAA,uBACE,oCACA,CAAE9K,OAAQ,OAAQE,KAAM,CAAElB,QAAOnK,EAAAA,EAAAA,EAAAA,EAAA,GAAOiK,CAAQ,MAAEmK,UAAAA,EAAS,EAAG,CAAE,CAClE,EACG8B,aAAa,kBAAmB,CAAC,IAAI,CAAC,EACtCC,OAAO,CAAC,CAAEC,MAAO,YAAaC,MAAO,MAAO,CAAC,CAAC,EAC9CtI,SAAS,EAAG,GAAI,EAChBvC,IAAI,CACT,EAAC,OAAAwK,GAAA5W,GAAAG,KATO8L,GAAI2K,GAAJ3K,KAUJA,KACFgK,GAAkBhK,EAAI,EACtBiJ,IAAQ,MAARA,GAAWjJ,GAAKlF,IAAI,SAAC6E,GAAG5E,GAAG,CAAF,MAAM,CAAE9E,IAAK0J,GAAGnK,KAAM+S,GAAkBxN,EAAC,CAAE,CAAC,CAAC,CAAC,GACxE,wBAAAhH,GAAAM,KAAA,IAAAV,EAAA,EACF,oBAhBkB,QAAA7D,GAAAyE,MAAA,KAAAC,SAAA,MAiBnB7D,GAAyByY,GAAsB,CAAC,EAAxC6B,GAAYta,GAAZsa,aAkBR,SAhBAC,GAAAA,IAAqB,UAAM,CACzBT,GAAc,EACdnB,GAAoB,gBAAgB,CACtC,EAAG,CAAC1K,CAAQ,CAAC,KAEb9B,EAAAA,WAAU,UAAM,CACdgN,GAAuB,CACzB,EAAG,CAAC,CAAC,KAELhN,EAAAA,WAAU,UAAM,CACV2M,KACFgB,GAAc,EACdf,GAAe,EAEnB,EAAG,CAACD,EAAM,CAAC,EAENM,MAIH3S,EAAAA,MAAAO,EAAAA,SAAA,CAAAxC,SAAA,IACEV,EAAAA,KAAC0W,GAAAA,EAAK,CACJ7U,SAAOiB,EAAAA,IAAQ,gBAAgB,EAC/ByQ,KAAMoC,GACN9S,UAAW4C,EAAOkR,oBAClB3M,SAAU,kBAAM4L,GAAe,EAAK,CAAC,EACrCgB,OAAQ,GACRC,SAAQ,GACRvS,MAAO,IAAI5D,YAEXV,EAAAA,KAACgR,EAAkB,CAAC6C,eAAgBA,CAAe,CAAE,CAAC,CACjD,KACP7T,EAAAA,KAACyO,EAAAA,GAAO,CACNC,MAAK,GACL9P,QAASA,IAAW,EAACuL,GAAQ,OAAAkK,EAARlK,EAAUS,aAAS,MAAAyJ,IAAA,QAAnBA,EAAqBxJ,QAC1ChI,UAAW4C,EAAOqR,qBAClBnI,WAAY,GACZoI,aAActR,EAAOuR,qBACrBC,aACEzC,GACI,CACE1F,SAAU,SAACoI,GAAW,CACpB,IAAIC,EAAWD,GAAO7Q,IAAI,SAACC,GAAG,CAAF,MAAM,CAChC9E,IAAK8T,GAAehP,EAAC,EACrBvF,KAAM+S,GAAkBxN,EAAW,CACrC,CAAC,CAAC,EACE4Q,GAAOrM,SAAW,GAAK4J,IACzB0C,EAAW7B,GAAejP,IAAI,SAAC6E,GAAG5E,GAAG,CAAF,MAAM,CACvC9E,IAAK0J,GACLnK,KAAM+S,GAAkBxN,EAAC,CAC3B,CAAC,CAAC,GAEJkO,GAAS2C,CAAQ,CACnB,CACF,EACA/T,OAENgU,MAAO,CACLC,QAAS,CACP1E,OAAQ,SAAC3W,GAAGsb,EAAQvb,GAAU,KAAA8E,GAAA0W,GAAAC,GACtBjc,GACJ,CAAC,CAACib,OACFrL,EAAAA,IAAmBmM,EAAQ,EAAI,OAC7BnM,EAAAA,IAAmBqL,GAAc,EAAI,EACzC,SACE7T,EAAAA,MAAA,OACEE,UAAW4U,GAAAA,EAAWhS,GAAM,YAANA,EAAQiS,kBAAiBC,EAAAA,EAAA,GAC5ClS,EAAOmS,WAAa,CAAC,CAACpD,EAAQ,CAChC,EAAE9T,SAAA,CAEFgU,MACC1U,EAAAA,KAAC6X,EAAAA,EAAWzV,KAAI,CACdnE,KAAK,YACL4E,UAAW4C,GAAM,YAANA,EAAQqS,cAAcpX,SAEhCqT,EAAiBuD,EAAQ5C,CAAc,KACpC5R,EAAAA,IAAQ,kBAAkB,KAC1BA,EAAAA,IAAQ,mBAAmB,CAAC,CACjB,KAEnBH,EAAAA,MAAA,OAAKE,UAAW4C,GAAM,YAANA,EAAQsS,eAAerX,SAAA,IACrCiC,EAAAA,MAACK,EAAAA,EAAK,CAACH,UAAW4C,GAAM,YAANA,EAAQuS,kBAAkBtX,SAAA,IAC1CiC,EAAAA,MAAA,OAAAjC,SAAA,IACGoC,EAAAA,IAAQ,UAAU,EAClB/G,GAAQ,CAAC,EACP,EACJ,OAAOub,GAAM,YAANA,EAAQW,oBAAsB,YACpCtV,EAAAA,MAAA,QAAAjC,SAAA,IACGoC,EAAAA,IAAQ,sBAAsB,EAAE,YAChCoV,EAAAA,OAAMZ,EAAOW,kBAAmB,CAAC,CAAC,EAC/B,EAEN,IAEDX,GAAM,YAANA,EAAQa,kBACPnY,EAAAA,KAACmD,EAAAA,EAAG,CAACb,MAAM,QAAQO,UAAW4C,EAAO2S,QAAQ1X,YAC1CoC,EAAAA,IAAQ,iBAAiB,CAAC,CACxB,EAENwU,EAAOe,sBACNrY,EAAAA,KAACmD,EAAAA,EAAG,CAACb,MAAM,SAAQ5B,YAAEoC,EAAAA,IAAQ,UAAU,CAAC,CAAM,EAE/CwU,EAAOgB,sBACNtY,EAAAA,KAACmD,EAAAA,EAAG,CAACb,MAAM,QAAO5B,YAAEoC,EAAAA,IAAQ,kBAAkB,CAAC,CAAM,CACtD,EACI,KACPH,EAAAA,MAACK,EAAAA,EAAK,CACJH,UAAW4C,GAAM,YAANA,EAAQ8S,mBACnBja,QAAS,SAACkE,GAAG,CAAF,OAAKA,GAAEC,gBAAgB,CAAC,EAAC/B,SAAA,CAEnCQ,IAAM,OAAAL,GAANK,GAAQoC,gBAAY,MAAAzC,KAAA,QAApBA,GAAsB0C,SACrB,wCACF,KACEvD,EAAAA,KAAC3B,EAAAA,EAAiB,CAChBwE,UAAW4C,GAAM,YAANA,EAAQ+S,cACnBva,KAAK,OACLK,QAAS,kBACPsW,GAAe,CACb6D,eAAgB,CACdnJ,GAAIgI,GAAM,YAANA,EAAQhI,EACd,EACAoJ,iBAAkB,iBAClBC,iBAAkBzD,EACpB,CAAC,CAAC,EAEJ5Z,KAAK,QAAOoF,SAEX4W,GAAM,MAANA,EAAQsB,gBAAiBtB,GAAM,YAANA,EAAQsB,eAAgB,EAAC,GAAArS,UAC5CzD,EAAAA,IAAQ,SAAS,EAAC,UAAAyD,OACnB+Q,GAAM,YAANA,EAAQsB,cAAa,aAEvB9V,EAAAA,IAAQ,SAAS,CAAC,CACL,EAEnB,GAED5B,IAAM,OAAAqW,GAANrW,GAAQoC,gBAAY,MAAAiU,KAAA,QAApBA,GAAsBhU,SACrB,6CACF,GAAKgR,MACHvU,EAAAA,KAAC3B,EAAAA,EAAiB,CAChB/C,KAAK,QACL2C,KAAK,OACL4a,SAAUtd,GACVsH,UAAW4C,GAAM,YAANA,EAAQqT,iBACnBxa,QAAS,kBACPiW,MAAkBpJ,EAAAA,IAAmBmM,CAAM,CAAC,CAAC,EAC9C5W,YAEAoC,EAAAA,IAAQvH,GAAS,iBAAmB,cAAc,CAAC,CACnC,EACjB,QACJoH,EAAAA,MAAA,OACEE,UAAWkW,GAAAA,EACTtT,EAAOuT,UACP,yBACF,EAAEtY,SAAA,EAED4W,GAAM,YAANA,EAAQ2B,sBACPjZ,EAAAA,KAACkZ,EAAAA,EAAO,CAAC7B,WAASvU,EAAAA,IAAQ,yBAAyB,EAAEpC,YACnDV,EAAAA,KAACmZ,GAAW,CACV7U,MAAO,GACPzB,UAAW4C,EAAO2T,WAAY,CAC/B,CAAC,CACK,EAEVlY,IAAM,OAAAsW,GAANtW,GAAQoC,gBAAY,MAAAkU,KAAA,QAApBA,GAAsBjU,SACrB,+CACF,KACEvD,EAAAA,KAAC3B,EAAAA,EAAiB,CAChBC,QAAOS,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAoa,IAAA,KAAAC,GAAA/N,GAAAe,GAAA9P,GAAA+c,GAAAC,GAAApa,GAAA,OAAAJ,EAAAA,EAAA,EAAAK,KAAA,SAAAoa,GAAA,eAAAA,GAAAla,KAAAka,GAAAja,KAAA,QAAAia,OAAAA,GAAAja,KAAA,KACuBuN,EAAAA,OAC5B,wBACA,CACE1B,OAAQ,OACRE,KAAM,CACJA,KAAM,CACJX,UAAW0M,GAAM,YAANA,EAAQ1M,SACrB,CACF,CACF,CACF,EACGwL,aAAa,eAAgB,CAC5B,OACA,UACA,aAAa,CACd,EACAA,aAAa,gBAAiB,CAAC,MAAM,CAAC,EACtC1K,IAAI,EAAC,OAjBW,GAiBX4N,GAAAG,GAAAha,KAjBA8L,GAAI+N,GAAJ/N,KAAMe,GAAKgN,GAALhN,MAAK,EAkBfA,IAAK,MAALA,GAAOoN,SAAO,CAAAD,GAAAja,KAAA,eAAAia,GAAA/Z,OAAA,SACTga,EAAAA,GAAQpN,MAAMA,IAAK,YAALA,GAAOoN,OAAO,CAAC,SAAAld,GAEpC+O,GADMgO,GAAM/c,GAAN+c,OAAQC,GAAKhd,GAALgd,MAEVpa,GAAS,CAAC,EAAJmH,OAAAN,EAAAA,EACPuT,EAAK,EAAAvT,EAAAA,EACLsT,GAAOlT,IAAI,SAACsT,GAAG,CAAF,MAAM,CACpBC,iBAAkBD,EACpB,CAAC,CAAC,CAAC,GAGLva,GAAOya,KAAK,SAACxQ,GAAGC,GAAG,CAAF,OACfD,GAAEuQ,iBAAiBE,cACjBxQ,GAAEsQ,gBACJ,CAAC,CACH,EACA7D,GAAkB3W,EAAM,KACnBmT,EAAAA,SAAQhH,EAAI,GAAGqK,GAAe,EAAI,EAAC,yBAAA6D,GAAA7Z,KAAA,IAAAyZ,EAAA,EACzC,GACD/d,KAAK,QACL2C,KAAK,OAAMyC,YAEVoC,EAAAA,IAAQ,gBAAgB,CAAC,CACT,EAEnB,EACD,EACE,CAAC,EACD,CAAC,EACL,KACL9C,EAAAA,KAAA,OAAK6C,UAAW4C,GAAM,YAANA,EAAQsU,gBAAgBrZ,YACtCV,EAAAA,KAACG,EAAAA,QAAiB,CAChB0C,UAAW4C,GAAM,YAANA,EAAQmN,OACnBlP,aAAWyH,EAAAA,IAAmBmM,CAAM,CAAE,CACvC,CAAC,CACC,KACLtX,EAAAA,KAAA,OAAK1B,QAAS,SAACkE,GAAG,CAAF,OAAKA,GAAEC,gBAAgB,CAAC,EAAC/B,YACvCV,EAAAA,KAACga,EAAAA,EAAQ,CACPC,YAAY,WACZC,iBAAkBnF,GAClBoF,eAAgBrF,GAChBK,OAAQA,EAAO,CAChB,CAAC,CACC,CAAC,EACH,CAET,CACF,CACF,EACAlG,KAAM,CAAEC,OAAQ,CAAE,EAClBC,WACEuF,EACIY,GAAeuE,KAAK,SAACxQ,GAAGC,GAAM,CAC5B,IAAM8Q,EAAKrG,EAAiB1K,GAAGqL,CAAc,EACvC2F,GAAKtG,EAAiBzK,GAAGoL,CAAc,EAC7C,OAAI0F,IAAOC,GACLhR,GAAEiR,YAAchR,GAAEgR,UAAkBjR,GAAEiG,GAAKhG,GAAEgG,IACzCjG,GAAEiR,WAAa,MAAQhR,GAAEgR,WAAa,IAAM,GAAK,EAEpDF,EAAK,GAAK,CACnB,CAAC,EACD9E,EACL,CACF,CAAC,EACF,KA3OKtV,EAAAA,KAACjC,GAAAA,GAAW,CAACE,KAAK,MAAM,CAAE,CA6OrC,EAEA,GAAemW,GAEFmG,GAA4B,SACvCpQ,EACAoK,EAAwC,OACpC,CACJ3Y,IAAK,iBACLgK,SACE5F,EAAAA,KAACwa,EAAAA,EAAa,CACZ3Y,SAAOiB,EAAAA,IAAQ,oBAAoB,EACnC2X,UAAS1b,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAyb,GAAA,KAAAC,GAAAC,GAAAtO,GAAA,OAAAtN,EAAAA,EAAA,EAAAK,KAAA,SAAAwb,EAAA,eAAAA,EAAAtb,KAAAsb,EAAArb,KAAA,aACL,CAAC2K,GAAY,CAACA,EAASS,UAAUC,QAAM,CAAAgQ,EAAArb,KAAA,eAAAqb,EAAAnb,OAAA,iBAAAmb,OAAAA,EAAArb,KAAA,KACb2W,EAAAA,uBAC5B,uBACA,CAAE9K,OAAQ,OAAQE,KAAM,CAAElB,QAASF,CAAS,CAAE,EAC9C,CAAC,IAAI,CACP,EACG8D,SAAS,EAAG,CAAC,EACbvC,IAAI,EAAC,OANW,GAMXiP,GAAAE,EAAApb,KANAmb,GAAID,GAAJC,KAAMtO,GAAKqO,GAALrO,MAAK,EAOfA,IAAS,EAACsO,IAAI,MAAJA,GAAMjM,aAAU,CAAAkM,EAAArb,KAAA,eAAAqb,EAAAnb,OAAA,SAAS0D,MAAS,gBAAAyX,EAAAnb,OAAA,SACzCkb,GAAKjM,WAAWhC,KAAK,2BAAAkO,EAAAjb,KAAA,IAAA8a,CAAA,EAC7B,EAAC,CACH,EAEHha,SAAUyJ,KACRnK,EAAAA,KAACoU,GAAgB,CACfjK,SAAUA,EACVoK,kBAAmBA,CAAkB,CACtC,EACC,IACN,CAAC,C,qGCnXKiG,EAA8C,SAAH7f,EAI3C,KAHJkH,EAAKlH,EAALkH,MACA4Y,EAAS9f,EAAT8f,UACAK,EAAYngB,EAAZmgB,aAEAtc,KAA8BC,EAAAA,UAAkB,EAAK,EAACC,EAAAC,EAAAA,EAAAH,EAAA,GAA/CI,EAAOF,EAAA,GAAEG,EAAUH,EAAA,GAC1BmP,KAA4BpP,EAAAA,UAAwB,EAACuP,EAAArP,EAAAA,EAAAkP,EAAA,GAA9CkN,GAAM/M,EAAA,GAAEgN,EAAShN,EAAA,GACxB3F,SAAAA,EAAAA,WAAU,UAAM,CACd,IAAI4S,EAAU,GACd,GAAKR,EACLS,eAAQC,QAAQV,EAAU,CAAC,EACxBpa,KAAK,SAAC2R,EAAG,CAAF,MAAK,CAACiJ,GAAWD,KAAUI,EAAAA,OAAMpJ,CAAC,EAAI5O,OAAY4O,CAAC,CAAC,GAAC,QACpD,iBAAM,CAACiJ,GAAWpc,EAAW,EAAK,CAAC,GAEvC,UAAM,CACXoc,EAAU,EACZ,CACF,EAAG,CAACR,EAAWK,CAAY,CAAC,KAG1BnY,EAAAA,MAAAO,EAAAA,SAAA,CAAAxC,SAAA,CACGmB,EACAjD,KAAUoB,EAAAA,KAACxF,EAAAA,EAAe,EAAE,KAAI4gB,EAAAA,OAAML,EAAM,EAAI,GAAK,IAAHxU,OAAOwU,GAAM,IAAG,EACnE,CAEN,EAEA,IAAeP,C,qOCnCFa,EAAgB,SAC3BhV,EACAuE,EACiC,CACjC,IAAM0Q,EAASvV,OAAOwV,QAAQlV,CAAG,EAAEmV,OACjC,SAACC,GAAG9gB,GAAa,KAAAU,GAAAsD,EAAAA,EAAAhE,GAAA,GAAV+gB,EAACrgB,GAAA,GAAEsgB,EAACtgB,GAAA,GACT,OAAIsgB,IAAM,iBAAmB,CAAC/Q,EAAUrH,SAASmY,CAAC,EAAGD,GAAIC,CAAC,EAAI,WACzDD,GAAIC,CAAC,EAAIC,EACPF,EACT,EACA,CAAC,CACH,EACA,OAAK1V,OAAOW,OAAO4U,CAAM,EAAE/X,SAAS,eAAe,IACjD+X,EAAO1Q,EAAU,CAAC,CAAC,EAAI,iBAElB0Q,CACT,E,gLCfaM,EAAe,eAAAjhB,EAAAoE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAC,EAC7B2c,EACAC,EAAwB,KAAAC,GAAAC,GAAAC,GAAA,OAAAjd,EAAAA,EAAA,EAAAK,KAAA,SAAAC,EAAA,eAAAA,EAAAC,KAAAD,EAAAE,KAAA,QAAAF,OAAAA,EAAAE,KAAA,KAE8BuN,EAAAA,OACpD,4BACA,CACE1B,OAAQ,OACRE,KAAM,CACJzK,UAAWgb,EAAchb,UACzBU,IAAKsa,EAAcI,iBACnBxL,UAAWoL,EAAcK,OAAO5a,UAAUS,GAC1CH,MAAOia,EAAcK,OAAO5a,UAAUM,MACtCua,OAAQN,EAAcxM,IAAM,EAC9B,EACAhE,cAAe,EACjB,CACF,EACGI,IAAI,EAAC,MACC,EAAC,OAfkC,GAAtCqQ,GAAsCzc,EAAAG,KAAA,EAiBxC,CAACsc,IAAiB,CAACA,GAAcM,UAAQ,CAAA/c,EAAAE,KAAA,QAC3C8c,eAAQC,IAAI,qDAAqD,EAACjd,EAAAI,OAAA,SAC3Dmc,CAAc,SAGmD,GAApEG,GAAYD,GAAcM,SAAS3P,OAAO,SAAClK,EAAG,CAAF,OAAKA,EAAEga,OAAS,OAAO,GAEpER,GAAUnR,OAAQ,CAAFvL,EAAAE,KAAA,eAAAF,EAAAI,OAAA,SACZmc,CAAc,SAGjBI,OAAAA,GAA+B,CAAC,EAEtCD,GAAUS,QAAQ,SAACzK,EAAM,CACvB,IAAMD,EAAIC,EAAE0K,sBAAwB1K,EAAE2K,kBACtC,GAAI,CAAC5K,EAAG,CACNuK,QAAQM,KAAK,kBAADrW,OAAmByL,EAAEjR,KAAI,+BAA8B,EACnE,MACF,CACA,IAAM6R,EAASb,EAAE8K,MAAM,GAAG,EAAEhD,KAAK,EAAErH,KAAK,GAAG,EAE3CyJ,GAAY7J,KAAK,CACfQ,OAAAA,EACA4J,KAAM,gBACNxa,GAAI,GACJ8a,WAAY,EACZC,KAAM,IACR,CAAC,CACH,CAAC,EAACzd,EAAAI,OAAA,YAAA6G,OAAAN,EAAAA,EAES4V,CAAc,EAAKI,EAAW,4BAAA3c,EAAAM,KAAA,IAAAV,CAAA,EAC1C,mBApD2BW,EAAAiM,EAAA,QAAAnR,EAAAmF,MAAA,KAAAC,SAAA,M,WC4CtBid,EAAkB,eAAAriB,EAAAoE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAC,EAAOsC,EAAa8S,EAAmB,CAAF,IAAA/I,GAAA,OAAAvM,EAAAA,EAAA,EAAAK,KAAA,SAAAC,GAAE,CAAF,cAAAA,GAAAC,KAAAD,GAAAE,KAAE,CAAF,OAAAF,OAAAA,GAAAE,KAAA,KAC3CuN,EAAAA,OAAuB,iCAAkC,CAC1E1B,OAAQ,OACRE,KAAM,CAAEpB,SAAU3I,EAAK8S,UAAAA,CAAU,EACjChJ,cAAe,EACjB,CAAC,EAAEI,IAAI,EAAC,OAJFH,OAAAA,GAAIjM,GAAAG,KAAAH,GAAAI,OAAA,SAMF6L,IAAI,YAAJA,GAAwC,CAAC,CAAC,0BAAAjM,GAAAM,KAAA,IAAAV,CAAA,EACnD,mBARuBW,EAAAiM,EAAA,QAAAnR,EAAAmF,MAAA,KAAAC,SAAA,MAUlBkd,EAAoD,SAAH5hB,EAQjD,KAPJ6hB,EAAe7hB,EAAf6hB,gBACWpB,EAAazgB,EAAxByF,UACAyS,GAAIlY,EAAJkY,KACA4J,GAAI9hB,EAAJ8hB,KACAC,GAAY/hB,EAAZ+hB,aACAC,EAAShiB,EAATgiB,UACA/I,EAASjZ,EAATiZ,UAEAgJ,KAAgCC,EAAAA,GAAW,EAAnCC,EAAmBF,EAAnBE,oBACRhf,KAAkCC,EAAAA,UAChCqd,CACF,EAACpd,GAAAC,EAAAA,EAAAH,EAAA,GAFMsC,EAASpC,GAAA,GAAE+e,GAAY/e,GAAA,GAG9Bgf,GAAyBC,EAAAA,EAAIC,OAAO,EAA5BC,GAAYH,GAAZG,aAER/V,EAAeC,GAAAA,EAAKC,QAAmB,EAACC,EAAAtJ,EAAAA,EAAAmJ,EAAA,GAAjCI,EAAID,EAAA,GACXuN,KAA8BC,EAAAA,GAAcrS,OAAW,EAAK,EAA7C0a,GAAUtI,EAAjBE,MACRqI,MAAiCtI,EAAAA,GAAcrS,OAAW,EAAK,EAAhD4a,GAAaD,GAApBrI,MACRuI,KAA+BC,GAAAA,IAAmBhB,EAAgB/S,QAAQ,EAAlES,EAASqT,EAATrT,UAAWuT,EAAOF,EAAPE,QACnBlN,MAAyBC,EAAAA,UAAS,gBAAgB,EAA1CkN,GAAYnN,GAAZmN,aACRvQ,MAA8BpP,EAAAA,UAAkB,EAAK,EAACuP,GAAArP,EAAAA,EAAAkP,GAAA,GAA/CjP,GAAOoP,GAAA,GAAEnP,GAAUmP,GAAA,GAEpBqQ,GAAe,eAAAniB,GAAA6C,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAoa,GAAOvY,GAAsB,CAAF,IAAAwd,GAAAC,GAAAC,GAAAC,GAAAlT,GAAAmT,GAAAC,GAAArW,GAAAsW,GAAAC,GAAAC,GAAAC,GAAAC,GAAA,OAAAhgB,EAAAA,EAAA,EAAAK,KAAA,SAAAoa,EAAE,CAAF,cAAAA,EAAAla,KAAAka,EAAAja,KAAE,CAAF,OAC3Cgf,OAAAA,MAAoBS,GAAAA,IACxBne,GAAS,GAAAyF,QACN6X,IAAY,OAAAE,GAAZF,GAAcc,YAAQ,MAAAZ,KAAA,cAAtBA,GAAwBhP,KAAM,EAAE,CACrC,EAACmK,EAAAja,KAAA,EACsBwe,MACrBjR,EAAAA,OAAc,mBAAoB,CAChCzB,cAAe,GACfD,OAAQ,OACRE,KAAM,CAAEzK,UAAW0d,GAAmBW,WAAY,EAAM,CAC1D,CAAC,EAAEzT,IAAI,CACT,EAAC,OANW,GAMX+S,GAAAhF,EAAAha,KANO8L,GAAIkT,GAAJlT,KAOHA,GAAM,CAAFkO,EAAAja,KAAA,eAAAia,EAAA/Z,OAAA,iBAG2D,GADhEgf,GAAe,IAAIU,IAAYlC,EAAgBmC,oBAAoB,EACnEV,GAAa,IAAIS,IAAYlC,EAAgBoC,kBAAkB,EAAC,CAChEhL,EAAW,CAAFmF,EAAAja,KAAA,SAAAia,OAAAA,EAAAja,KAAA,GACUwd,EACnBE,EAAgB/S,SAChBmK,CACF,EAAC,QAHKhM,GAAMmR,EAAAha,KAIZif,GAAe,IAAIU,IAAI9W,GAAO+W,oBAAoB,EAClDV,GAAa,IAAIS,IAAI9W,GAAOgX,kBAAkB,EAAC,QAEjDZ,OAAAA,GAAaa,IAAIhU,EAAyB,EACpCqT,GAAiB9C,GAAa,YAAbA,EAAexM,GAClCsP,KACFF,GAAY,OAAQE,EAAc,EAClCD,GAAWY,IAAIX,EAAc,GAC9BnF,EAAAja,KAAA,MAE0C4L,EAAAA,SACzC,mBACF,EAAEoU,OAAOtC,EAAgB5N,GAAI,CAC3B+P,qBAAsBnZ,MAAMuZ,KAAKf,GAAahY,OAAO,CAAC,EACtD4Y,mBAAoBpZ,MAAMuZ,KAAKd,GAAWjY,OAAO,CAAC,CACpD,CAAC,EAAC,QAAAmY,OAAAA,GAAApF,EAAAha,KALYqf,GAAkBD,GAAxBtT,KAAIkO,EAAAja,KAAA,MAQHuN,EAAAA,OAAM,iCAAkC,CAC7C1B,OAAQ,OACRE,KAAM,CACJ/J,OAAKke,EAAAA,IAAwB5e,GAAU+a,cAAc,EACrDta,UAAWua,GAAa,YAAbA,EAAeI,iBAC1Bpb,UAAWA,GAAUA,SACvB,EACAwK,cAAe,EACjB,CAAC,EACEI,IAAI,EAAC,MACC,EAAC,WAAA+N,EAAAzO,GAAAuT,GAAA9E,EAAAha,KAAAga,EAAA9Z,GAAA8Z,EAAAzO,KAAA,KAAAyO,EAAA9Z,GAAE,CAAF8Z,EAAAja,KAAA,SAAAia,EAAA9Z,GAAA4e,KAAA,mBAAA9E,EAAA9Z,GAAE,CAAF8Z,EAAAja,KAAA,SAAAia,EAAA1O,GAAA,OAAA0O,EAAAja,KAAA,iBAAAia,EAAA1O,GAXawT,GAYtBoB,YAAW,QAZRZ,OAAAA,GAAgBtF,EAAA1O,GAAA0O,EAAAla,KAAG,GAAHka,EAAAja,KAAG,MAejBogB,EAAAA,IAA2B,CAC/BrU,KAAM,CACJsU,OAAQtU,GACR/J,IAAKgd,GAAkB5L,OACvBkN,YAAUd,GAAE9B,EAAgB6C,WAAO,MAAAf,KAAA,cAAvBA,GAAyB1P,GACrCpN,eAAgBsc,GAAkB1d,UAClCie,iBAAkBA,IAAoB,CAAC,EACvCiB,UAAWxB,GAAkB3C,cAC/B,CACF,CAAC,EAAC,QAAApC,EAAAja,KAAA,iBAAAia,EAAAla,KAAA,GAAAka,EAAAxO,GAAAwO,EAAA,UAEF6C,QAAQhQ,MAAM,wCAAD/F,OAAAkT,EAAAxO,EAAA,CAA4C,EAAC,QAExD6T,KACFzB,GAAS,MAATA,EAAYyB,EAAkB,GAC/B,yBAAArF,EAAA7Z,KAAA,IAAAyZ,GAAA,gBACF,mBArEoBtN,GAAA,QAAA7P,GAAA4D,MAAA,KAAAC,SAAA,MAuEfkgB,GAAS,eAAA7jB,GAAA2C,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAyb,GAAOlZ,GAAW,KAAA0e,GAAAC,GAAA5U,GAAA6U,GAAAvE,GAAAwE,GAAAC,GAAAvgB,UAAA,OAAAf,EAAAA,EAAA,EAAAK,KAAA,SAAAwb,GAAA,eAAAA,GAAAtb,KAAAsb,GAAArb,KAAA,QAAE0gB,OAAAA,GAAoBI,GAAAzV,OAAA,GAAAyV,GAAA,KAAAld,OAAAkd,GAAA,GAAG,GAAKzF,GAAArb,KAAA,EACzCse,MACrB/Q,EAAAA,OAA6B,iBAAkB,CAC7CzB,cAAe,GACfD,OAAQ,OACRE,KAAM,CAAEgV,gBAAiB/e,EAAI,CAC/B,CAAC,EAAEkK,IAAI,CACT,EAAC,OANW,GAMXyU,GAAAtF,GAAApb,KANO8L,GAAI4U,GAAJ5U,KAQHA,GAAM,CAAFsP,GAAArb,KAAA,eAAAqb,GAAAnb,OAAA,iBAIR,GAHK0gB,GAAU/E,EACb9P,GAA0CiR,QAC3C0B,GAAAA,IAAmBhB,EAAgB/S,QAAQ,EAAES,SAC/C,EACIwV,GAAQjC,CAAO,IAAM,UAAS,CAAAtD,GAAArb,KAAA,SAChCqe,OAAAA,GAAavR,MAAM,CACjBoN,WAAS5W,EAAAA,IAAQ,uCAAuC,CAC1D,CAAC,EAAC+X,GAAAnb,OAAA,sBAGAkL,EAAU4V,KAAK,SAAC7E,GAAG,CAAF,MAAK,CAACyE,GAAQzE,EAAC,CAAC,GAAG,CAAFd,GAAArb,KAAA,SACpCqe,OAAAA,GAAavR,MAAM,CACjBoN,WAAS5W,EAAAA,IAAQ,wCAAwC,CAC3D,CAAC,EAAC+X,GAAAnb,OAAA,kBAQH,GAJKmc,MAAiB4E,GAAAA,IACrBjf,GACA4e,GACAtf,GAAS,YAATA,EAAW+a,cACb,EAAC,EAEG,CAACqE,IAAe,EAACpE,GAAa,MAAbA,EAAehb,YAAS,CAAA+Z,GAAArb,KAAA,SAC3Cie,OAAAA,GAAa,SAAC/a,GAAK,CAAF,OACfA,GAAGxC,EAAAA,EAAAA,EAAAA,EAAA,GACMwC,EAAG,MAAEkQ,OAAQpR,GAAKqa,eAAAA,GAAgB6E,YAAa,EAAE,GACtDtd,MAAS,CACf,EAACyX,GAAAnb,OAAA,kBAAAmb,OAAAA,GAAArb,KAAA,GAIsBoc,EAAgBC,GAAgBC,CAAa,EAAC,QAAjEuE,GAAUxF,GAAApb,KAEhBge,GAAa,SAAC/a,GAAK,CAAF,OACfA,GAAGxC,EAAAA,EAAAA,EAAAA,EAAA,GACMwC,EAAG,MAAEkQ,OAAQpR,GAAKqa,eAAgBwE,GAAYK,YAAa,EAAE,GAClEtd,MAAS,CACf,EAAC,yBAAAyX,GAAAjb,KAAA,IAAA8a,EAAA,EACF,mBAjDciG,GAAA,QAAAvkB,GAAA0D,MAAA,KAAAC,SAAA,MAmDfsI,SAAAA,EAAAA,WAAU,UAAM,CACd,GAAIvH,EAAW,KAAA8f,GACT,EAAC9f,GAAS,OAAA8f,GAAT9f,EAAW+a,kBAAc,MAAA+E,KAAA,QAAzBA,GAA2B/V,SAAU/J,EAAU8R,QAClD/T,GAAW,EAAI,EACfohB,GAAUnf,EAAU8R,OAAQ,EAAI,EAAC,QAAS,kBAAM/T,GAAW,EAAK,CAAC,IAEjEqJ,EAAKK,eAAezH,CAAS,CAEjC,CACF,EAAG,CAACA,CAAS,CAAC,KAEduH,EAAAA,WAAU,UAAM,CACdoV,GAAa3B,CAAa,CAC5B,EAAG,CAACA,CAAa,CAAC,KAGhBnZ,EAAAA,MAACiH,EAAAA,EAAS,CACR/G,UAAU,uBACVhB,SAAOiB,EAAAA,IAAQ,wBAADyD,OAAyB4W,GAAI,WAAU,EACrDjV,KAAMA,EACN5D,MAAO,IACPuc,oBAAmB,GACnBtN,KAAMA,GACNsF,SAAUja,GACVwe,aAAcA,GACdrT,WAAY,CAAE+W,eAAgB,GAAMjK,SAAU,EAAK,EACnDkK,cAAe,IACfjX,SAAQ,eAAAtN,GAAAuC,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAA+hB,GAAOta,GAAQ,CAAF,OAAA1H,EAAAA,EAAA,EAAAK,KAAA,SAAA4hB,GAAE,CAAF,cAAAA,GAAA1hB,KAAA0hB,GAAAzhB,KAAE,CAAF,OAAAyhB,OAAAA,GAAAzhB,KAAA,EACf6e,GAAgB3X,EAAM,EAAC,cAAAua,GAAAvhB,OAAA,SACtB,EAAI,0BAAAuhB,GAAArhB,KAAA,IAAAohB,EAAA,EACZ,mBAAAE,GAAA,QAAA1kB,GAAAsD,MAAA,KAAAC,SAAA,MAACW,SAAA,IAEFV,EAAAA,KAACmhB,EAAAA,EAAY,CACX7c,MAAM,KACNvD,KAAK,SACL6E,SAAO9C,EAAAA,IAAQ,OAAO,EACtBmC,IAAK,EACLC,IAAK,IACLtK,WAAY,CAAEwmB,UAAW,EAAGC,WAAY,GAAI,CAAE,CAC/C,KACDrhB,EAAAA,KAACshB,GAAAA,EAAW,CACVvgB,KAAK,cACL6E,SAAO9C,EAAAA,IAAQ,iDAAiD,EAChElI,WAAY,CAAEyH,MAAO,CAAEiC,MAAO,GAAI,CAAE,EACpC+c,cACErhB,EAAAA,KAAAkD,EAAAA,SAAA,CAAAxC,YACEV,EAAAA,KAACC,GAAAA,GAAM,CACLhC,KAAK,OACLK,QAAS,UAAM,CACb2hB,GAAU/X,EAAKqZ,cAAc,aAAa,CAAC,CAC7C,EAAE7gB,YAEDoC,EAAAA,IAAQ,gCAAgC,CAAC,CACpC,CAAC,CACT,CACH,CACF,KACD9C,EAAAA,KAACwhB,EAAAA,EAAW,CACVzgB,KAAK,iBACL6E,SAAO9C,EAAAA,IAAQ,gBAAgB,EAC/B2e,mBAAoB,CAClBC,qBAAmB5e,EAAAA,IAAQ,mBAAmB,CAChD,EACA6e,cAAe,GACfC,aAAc,SAACtL,GAAOta,GAAG6lB,GAAqB,CAC5C,IAAM7S,GAAO9G,EAAKqZ,cAAc,gBAAgB,EAC9CjL,GAAMvV,IAAI,EAEZ,OAAIiO,GAAK4D,SAAWuL,GAAWvT,EAAUrH,SAASyL,GAAK4D,MAAM,EACpD,CAAC,EAEHiP,EACT,EAAEnhB,SAED,SAAC4V,GAAU,CACV,IAAMtH,GAAO9G,EAAKqZ,cAAc,gBAAgB,EAC9CjL,GAAMvV,IAAI,EAEN8X,GACJ7J,GAAK4D,SAAWuL,GAAWvT,EAAUrH,SAASyL,GAAK4D,MAAM,EAC3D,SACEjQ,EAAAA,MAACmf,EAAAA,GAAY,CAAAphB,SAAA,IACXV,EAAAA,KAAC+hB,EAAAA,EAAa,CACZlJ,SAAUA,GACV9X,KAAK,OACLuD,MAAO,IACPsB,SAAO9C,EAAAA,IAAQ,MAAM,EACrBkf,QAASxE,EAAoB9Q,OAC3B,SAACxB,GAAG,CAAF,OAAKA,GAAEpG,QAAU,SAAS,CAC9B,EACAmd,SAAQ,GACRC,MAAO,CAAC,CAAED,SAAU,EAAK,CAAC,CAAE,CAC7B,KACDjiB,EAAAA,KAACmiB,EAAAA,EAAQC,KAAI,CACXvf,UAAU,mBACV9B,KAAM,SACN6E,SAAO9C,EAAAA,IAAQ,YAAY,EAC3Bmf,SAAQ,GACRC,MAAO,CAAC,CAAED,SAAU,EAAK,CAAC,EAAEvhB,YAE5BV,EAAAA,KAACqiB,EAAAA,EAAW,CAACxJ,SAAUA,GAAUyJ,SAAU,EAAM,CAAE,CAAC,CACxC,KACdtiB,EAAAA,KAACmhB,EAAAA,EAAY,CACXpgB,KAAK,aACL6E,SAAO9C,EAAAA,IAAQ,KAAK,EACpBmf,SAAQ,GACRC,MAAO,CACL,CAAED,SAAU,EAAK,EACjB,CACEM,QAAS,sCACT7I,WAAS5W,EAAAA,IAAQ,mBAAmB,CACtC,CAAC,CACD,CACH,KACD9C,EAAAA,KAAC+hB,EAAAA,EAAa,CACZhhB,KAAK,OACL6E,SAAO9C,EAAAA,IAAQ,MAAM,EACrBkf,QAASQ,EAAAA,GACTP,SAAQ,GACRC,MAAO,CAAC,CAAED,SAAU,EAAK,CAAC,CAAE,CAC7B,CAAC,GAvCc,OAwCJ,CAElB,CAAC,CACU,KACbjiB,EAAAA,KAACyiB,EAAAA,EAAe,CACd5J,SAAU,GACV9X,KAAK,YACL6E,MAAM,YACNqc,SAAU,GACVrnB,WAAY,CACV8nB,SAAU,CAAEC,QAAS,EAAGC,QAAS,CAAE,CACrC,CAAE,CACH,CAAC,EACO,CAEf,EAEA,GAAe3F,C,kDChVX4F,EAAyB,iBACzBC,EAA0B,iBAC9B,EAA6C,CAC3C,MAAO,EACT,EACAC,EAAQ,EAAU,OAClB,IAAIC,EAAQF,EAAwB,EAAQ,KAAO,CAAC,EAChDG,EAAW,EAAQ,KAAkB,EACrCC,EAAQL,EAAuB,EAAQ,KAAQ,CAAC,EAChDM,EAAsC,SAAUpR,EAAGvP,EAAG,CACxD,IAAI4gB,EAAI,CAAC,EACT,QAASzX,KAAKoG,EAAO,OAAO,UAAU,eAAe,KAAKA,EAAGpG,CAAC,GAAKnJ,EAAE,QAAQmJ,CAAC,EAAI,IAAGyX,EAAEzX,CAAC,EAAIoG,EAAEpG,CAAC,GAC/F,GAAIoG,GAAK,MAAQ,OAAO,OAAO,uBAA0B,WAAY,QAASzL,EAAI,EAAGqF,EAAI,OAAO,sBAAsBoG,CAAC,EAAGzL,EAAIqF,EAAE,OAAQrF,IAClI9D,EAAE,QAAQmJ,EAAErF,CAAC,CAAC,EAAI,GAAK,OAAO,UAAU,qBAAqB,KAAKyL,EAAGpG,EAAErF,CAAC,CAAC,IAAG8c,EAAEzX,EAAErF,CAAC,CAAC,EAAIyL,EAAEpG,EAAErF,CAAC,CAAC,GAElG,OAAO8c,CACT,EACA,MAAM7e,EAAoBye,EAAM,WAAW,CAACK,EAAI/oB,IAAQ,CACtD,GAAI,CACA,SAAAwJ,EACA,IAAAwf,CACF,EAAID,EACJE,EAAYJ,EAAOE,EAAI,CAAC,WAAY,KAAK,CAAC,EAK5C,MAAMG,EAAc,OAAO,OAAO,OAAO,OAAO,CAAC,EAAGD,CAAS,EAAG,CAC9D,IAAKD,IAAQ,QAAaC,EAAU,SAAW,SAAW,sBAAwBD,CACpF,CAAC,EAED,cAAOE,EAAY,SACCR,EAAM,cAAcE,EAAM,QAAS,OAAO,OAAO,CAAC,EAAGM,EAAa,CACpF,IAAKlpB,EACL,SAAU,CAAC,CAACwJ,EACZ,UAAW,GACb,CAAC,CAAC,CACJ,CAAC,EACD,IAAI2f,EAAWV,EAAQ,EAAUxe,C", "sources": ["webpack://labwise-web/./node_modules/@ant-design/icons/es/icons/FilterOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/pro-form/es/components/Switch/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-skeleton/es/components/List/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-skeleton/es/components/Descriptions/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-skeleton/es/components/Result/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-skeleton/es/index.js", "webpack://labwise-web/./src/components/ButtonWithLoading/index.tsx", "webpack://labwise-web/./src/components/LazySmileDrawer/index.tsx", "webpack://labwise-web/./src/components/ReactionTabs/ReactionLibTab/AiProcedureCard.tsx", "webpack://labwise-web/./node_modules/@ant-design/pro-form/es/components/DateYearRangePicker/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-form/es/components/Slider/index.js", "webpack://labwise-web/./src/components/ReactionTabs/ReactionLibTab/Filter/index.less?7b19", "webpack://labwise-web/./src/components/ReactionTabs/ReactionLibTab/Filter/Fields.tsx", "webpack://labwise-web/./src/components/ReactionTabs/ReactionLibTab/Filter/useFilterForm.ts", "webpack://labwise-web/./src/assets/svgs/nodata.svg", "webpack://labwise-web/./src/components/ReactionTabs/ReactionLibTab/Filter/Modal.tsx", "webpack://labwise-web/./src/components/ReactionTabs/ReactionLibTab/useProcedure.ts", "webpack://labwise-web/./src/components/ReactionTabs/ReactionLibTab/index.tsx", "webpack://labwise-web/./src/components/ReactionTabs/ReactionLibTab/util.tsx", "webpack://labwise-web/./src/assets/svgs/warning.svg", "webpack://labwise-web/./src/components/ReactionTabs/MaterialPriceTable/index.tsx", "webpack://labwise-web/./src/components/ReactionTabs/index.less?a292", "webpack://labwise-web/./src/components/ReactionTabs/RetroReactionTab.tsx", "webpack://labwise-web/./src/components/ReactionTabs/TabWithNumber.tsx", "webpack://labwise-web/./src/utils/reactionRoles.ts", "webpack://labwise-web/./src/pages/reaction/component/util.ts", "webpack://labwise-web/./src/pages/reaction/component/MyReactionDialog.tsx", "webpack://labwise-web/./node_modules/antd/lib/typography/Link.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport FilterOutlinedSvg from \"@ant-design/icons-svg/es/asn/FilterOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar FilterOutlined = function FilterOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: FilterOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(FilterOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FilterOutlined';\n}\nexport default RefIcon;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport LoadingOutlinedSvg from \"@ant-design/icons-svg/es/asn/LoadingOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar LoadingOutlined = function LoadingOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: LoadingOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(LoadingOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'LoadingOutlined';\n}\nexport default RefIcon;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"fieldProps\", \"unCheckedChildren\", \"checkedChildren\", \"proFieldProps\"];\nimport React from 'react';\nimport ProField from \"../Field\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * @zh-cn 单选 Switch\n * @en-us Single Choice Switch\n */\nvar ProFormSwitch = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var fieldProps = _ref.fieldProps,\n    unCheckedChildren = _ref.unCheckedChildren,\n    checkedChildren = _ref.checkedChildren,\n    proFieldProps = _ref.proFieldProps,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/_jsx(ProField, _objectSpread({\n    valueType: \"switch\",\n    fieldProps: _objectSpread({\n      unCheckedChildren: unCheckedChildren,\n      checkedChildren: checkedChildren\n    }, fieldProps),\n    ref: ref,\n    valuePropName: \"checked\",\n    proFieldProps: proFieldProps,\n    filedConfig: {\n      valuePropName: 'checked',\n      ignoreWidth: true,\n      customLightMode: true\n    }\n  }, rest));\n});\nexport default ProFormSwitch;", "import { Card, Divider, Skeleton, Space } from 'antd';\nimport useBreakpoint from 'antd/es/grid/hooks/useBreakpoint';\nimport React, { useMemo } from 'react';\n\n/** 一条分割线 */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nexport var Line = function Line(_ref) {\n  var padding = _ref.padding;\n  return /*#__PURE__*/_jsx(\"div\", {\n    style: {\n      padding: padding || '0 24px'\n    },\n    children: /*#__PURE__*/_jsx(Divider, {\n      style: {\n        margin: 0\n      }\n    })\n  });\n};\nexport var MediaQueryKeyEnum = {\n  xs: 2,\n  sm: 2,\n  md: 4,\n  lg: 4,\n  xl: 6,\n  xxl: 6\n};\nvar StatisticSkeleton = function StatisticSkeleton(_ref2) {\n  var size = _ref2.size,\n    active = _ref2.active;\n  var defaultCol = useMemo(function () {\n    return {\n      lg: true,\n      md: true,\n      sm: false,\n      xl: false,\n      xs: false,\n      xxl: false\n    };\n  }, []);\n  var col = useBreakpoint() || defaultCol;\n  var colSize = Object.keys(col).filter(function (key) {\n    return col[key] === true;\n  })[0] || 'md';\n  var arraySize = size === undefined ? MediaQueryKeyEnum[colSize] || 6 : size;\n  var firstWidth = function firstWidth(index) {\n    if (index === 0) {\n      return 0;\n    }\n    if (arraySize > 2) {\n      return 42;\n    }\n    return 16;\n  };\n  return /*#__PURE__*/_jsx(Card, {\n    bordered: false,\n    style: {\n      marginBlockEnd: 16\n    },\n    children: /*#__PURE__*/_jsx(\"div\", {\n      style: {\n        width: '100%',\n        justifyContent: 'space-between',\n        display: 'flex'\n      },\n      children: new Array(arraySize).fill(null).map(function (_, index) {\n        return /*#__PURE__*/_jsxs(\"div\", {\n          style: {\n            borderInlineStart: arraySize > 2 && index === 1 ? '1px solid rgba(0,0,0,0.06)' : undefined,\n            paddingInlineStart: firstWidth(index),\n            flex: 1,\n            marginInlineEnd: index === 0 ? 16 : 0\n          },\n          children: [/*#__PURE__*/_jsx(Skeleton, {\n            active: active,\n            paragraph: false,\n            title: {\n              width: 100,\n              style: {\n                marginBlockStart: 0\n              }\n            }\n          }), /*#__PURE__*/_jsx(Skeleton.Button, {\n            active: active,\n            style: {\n              height: 48\n            }\n          })]\n        }, index);\n      })\n    })\n  });\n};\n\n/** 列表子项目骨架屏 */\nexport var ListSkeletonItem = function ListSkeletonItem(_ref3) {\n  var active = _ref3.active;\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [/*#__PURE__*/_jsx(Card, {\n      bordered: false\n      // eslint-disable-next-line react/no-array-index-key\n      ,\n      style: {\n        borderRadius: 0\n      },\n      styles: {\n        body: {\n          padding: 24\n        }\n      },\n      children: /*#__PURE__*/_jsxs(\"div\", {\n        style: {\n          width: '100%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsx(\"div\", {\n          style: {\n            maxWidth: '100%',\n            flex: 1\n          },\n          children: /*#__PURE__*/_jsx(Skeleton, {\n            active: active,\n            title: {\n              width: 100,\n              style: {\n                marginBlockStart: 0\n              }\n            },\n            paragraph: {\n              rows: 1,\n              style: {\n                margin: 0\n              }\n            }\n          })\n        }), /*#__PURE__*/_jsx(Skeleton.Button, {\n          active: active,\n          size: \"small\",\n          style: {\n            width: 165,\n            marginBlockStart: 12\n          }\n        })]\n      })\n    }), /*#__PURE__*/_jsx(Line, {})]\n  });\n};\n\n/** 列表骨架屏 */\nexport var ListSkeleton = function ListSkeleton(_ref4) {\n  var size = _ref4.size,\n    _ref4$active = _ref4.active,\n    active = _ref4$active === void 0 ? true : _ref4$active,\n    actionButton = _ref4.actionButton;\n  return /*#__PURE__*/_jsxs(Card, {\n    bordered: false,\n    styles: {\n      body: {\n        padding: 0\n      }\n    },\n    children: [new Array(size).fill(null).map(function (_, index) {\n      return (\n        /*#__PURE__*/\n        // eslint-disable-next-line react/no-array-index-key\n        _jsx(ListSkeletonItem, {\n          active: !!active\n        }, index)\n      );\n    }), actionButton !== false && /*#__PURE__*/_jsx(Card, {\n      bordered: false,\n      style: {\n        borderStartEndRadius: 0,\n        borderTopLeftRadius: 0\n      },\n      styles: {\n        body: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        }\n      },\n      children: /*#__PURE__*/_jsx(Skeleton.Button, {\n        style: {\n          width: 102\n        },\n        active: active,\n        size: \"small\"\n      })\n    })]\n  });\n};\n\n/**\n * 面包屑的 骨架屏\n *\n * @param param0\n */\nexport var PageHeaderSkeleton = function PageHeaderSkeleton(_ref5) {\n  var active = _ref5.active;\n  return /*#__PURE__*/_jsxs(\"div\", {\n    style: {\n      marginBlockEnd: 16\n    },\n    children: [/*#__PURE__*/_jsx(Skeleton, {\n      paragraph: false,\n      title: {\n        width: 185\n      }\n    }), /*#__PURE__*/_jsx(Skeleton.Button, {\n      active: active,\n      size: \"small\"\n    })]\n  });\n};\n/**\n * 列表操作栏的骨架屏\n *\n * @param param0\n */\nexport var ListToolbarSkeleton = function ListToolbarSkeleton(_ref6) {\n  var active = _ref6.active;\n  return /*#__PURE__*/_jsx(Card, {\n    bordered: false,\n    style: {\n      borderBottomRightRadius: 0,\n      borderBottomLeftRadius: 0\n    },\n    styles: {\n      body: {\n        paddingBlockEnd: 8\n      }\n    },\n    children: /*#__PURE__*/_jsxs(Space, {\n      style: {\n        width: '100%',\n        justifyContent: 'space-between'\n      },\n      children: [/*#__PURE__*/_jsx(Skeleton.Button, {\n        active: active,\n        style: {\n          width: 200\n        },\n        size: \"small\"\n      }), /*#__PURE__*/_jsxs(Space, {\n        children: [/*#__PURE__*/_jsx(Skeleton.Button, {\n          active: active,\n          size: \"small\",\n          style: {\n            width: 120\n          }\n        }), /*#__PURE__*/_jsx(Skeleton.Button, {\n          active: active,\n          size: \"small\",\n          style: {\n            width: 80\n          }\n        })]\n      })]\n    })\n  });\n};\nvar ListPageSkeleton = function ListPageSkeleton(_ref7) {\n  var _ref7$active = _ref7.active,\n    active = _ref7$active === void 0 ? true : _ref7$active,\n    statistic = _ref7.statistic,\n    actionButton = _ref7.actionButton,\n    toolbar = _ref7.toolbar,\n    pageHeader = _ref7.pageHeader,\n    _ref7$list = _ref7.list,\n    list = _ref7$list === void 0 ? 5 : _ref7$list;\n  return /*#__PURE__*/_jsxs(\"div\", {\n    style: {\n      width: '100%'\n    },\n    children: [pageHeader !== false && /*#__PURE__*/_jsx(PageHeaderSkeleton, {\n      active: active\n    }), statistic !== false && /*#__PURE__*/_jsx(StatisticSkeleton, {\n      size: statistic,\n      active: active\n    }), (toolbar !== false || list !== false) && /*#__PURE__*/_jsxs(Card, {\n      bordered: false,\n      styles: {\n        body: {\n          padding: 0\n        }\n      },\n      children: [toolbar !== false && /*#__PURE__*/_jsx(ListToolbarSkeleton, {\n        active: active\n      }), list !== false && /*#__PURE__*/_jsx(ListSkeleton, {\n        size: list,\n        active: active,\n        actionButton: actionButton\n      })]\n    })]\n  });\n};\nexport default ListPageSkeleton;", "import { Card, Skeleton } from 'antd';\nimport useBreakpoint from 'antd/es/grid/hooks/useBreakpoint';\nimport React, { useMemo } from 'react';\nimport { Line, PageHeaderSkeleton } from \"../List\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nvar MediaQueryKeyEnum = {\n  xs: 1,\n  sm: 2,\n  md: 3,\n  lg: 3,\n  xl: 3,\n  xxl: 4\n};\nvar DescriptionsLargeItemSkeleton = function DescriptionsLargeItemSkeleton(_ref) {\n  var active = _ref.active;\n  return /*#__PURE__*/_jsxs(\"div\", {\n    style: {\n      marginBlockStart: 32\n    },\n    children: [/*#__PURE__*/_jsx(Skeleton.Button, {\n      active: active,\n      size: \"small\",\n      style: {\n        width: 100,\n        marginBlockEnd: 16\n      }\n    }), /*#__PURE__*/_jsxs(\"div\", {\n      style: {\n        width: '100%',\n        justifyContent: 'space-between',\n        display: 'flex'\n      },\n      children: [/*#__PURE__*/_jsxs(\"div\", {\n        style: {\n          flex: 1,\n          marginInlineEnd: 24,\n          maxWidth: 300\n        },\n        children: [/*#__PURE__*/_jsx(Skeleton, {\n          active: active,\n          paragraph: false,\n          title: {\n            style: {\n              marginBlockStart: 0\n            }\n          }\n        }), /*#__PURE__*/_jsx(Skeleton, {\n          active: active,\n          paragraph: false,\n          title: {\n            style: {\n              marginBlockStart: 8\n            }\n          }\n        }), /*#__PURE__*/_jsx(Skeleton, {\n          active: active,\n          paragraph: false,\n          title: {\n            style: {\n              marginBlockStart: 8\n            }\n          }\n        })]\n      }), /*#__PURE__*/_jsx(\"div\", {\n        style: {\n          flex: 1,\n          alignItems: 'center',\n          justifyContent: 'center'\n        },\n        children: /*#__PURE__*/_jsxs(\"div\", {\n          style: {\n            maxWidth: 300,\n            margin: 'auto'\n          },\n          children: [/*#__PURE__*/_jsx(Skeleton, {\n            active: active,\n            paragraph: false,\n            title: {\n              style: {\n                marginBlockStart: 0\n              }\n            }\n          }), /*#__PURE__*/_jsx(Skeleton, {\n            active: active,\n            paragraph: false,\n            title: {\n              style: {\n                marginBlockStart: 8\n              }\n            }\n          })]\n        })\n      })]\n    })]\n  });\n};\nvar DescriptionsItemSkeleton = function DescriptionsItemSkeleton(_ref2) {\n  var size = _ref2.size,\n    active = _ref2.active;\n  var defaultCol = useMemo(function () {\n    return {\n      lg: true,\n      md: true,\n      sm: false,\n      xl: false,\n      xs: false,\n      xxl: false\n    };\n  }, []);\n  var col = useBreakpoint() || defaultCol;\n  var colSize = Object.keys(col).filter(function (key) {\n    return col[key] === true;\n  })[0] || 'md';\n  var arraySize = size === undefined ? MediaQueryKeyEnum[colSize] || 3 : size;\n  return /*#__PURE__*/_jsx(\"div\", {\n    style: {\n      width: '100%',\n      justifyContent: 'space-between',\n      display: 'flex'\n    },\n    children: new Array(arraySize).fill(null).map(function (_, index) {\n      return /*#__PURE__*/_jsxs(\"div\", {\n        style: {\n          flex: 1,\n          paddingInlineStart: index === 0 ? 0 : 24,\n          paddingInlineEnd: index === arraySize - 1 ? 0 : 24\n        },\n        children: [/*#__PURE__*/_jsx(Skeleton, {\n          active: active,\n          paragraph: false,\n          title: {\n            style: {\n              marginBlockStart: 0\n            }\n          }\n        }), /*#__PURE__*/_jsx(Skeleton, {\n          active: active,\n          paragraph: false,\n          title: {\n            style: {\n              marginBlockStart: 8\n            }\n          }\n        }), /*#__PURE__*/_jsx(Skeleton, {\n          active: active,\n          paragraph: false,\n          title: {\n            style: {\n              marginBlockStart: 8\n            }\n          }\n        })]\n      }, index);\n    })\n  });\n};\n\n/**\n * Table 的子项目骨架屏\n *\n * @param param0\n */\nexport var TableItemSkeleton = function TableItemSkeleton(_ref3) {\n  var active = _ref3.active,\n    _ref3$header = _ref3.header,\n    header = _ref3$header === void 0 ? false : _ref3$header;\n  var defaultCol = useMemo(function () {\n    return {\n      lg: true,\n      md: true,\n      sm: false,\n      xl: false,\n      xs: false,\n      xxl: false\n    };\n  }, []);\n  var col = useBreakpoint() || defaultCol;\n  var colSize = Object.keys(col).filter(function (key) {\n    return col[key] === true;\n  })[0] || 'md';\n  var arraySize = MediaQueryKeyEnum[colSize] || 3;\n  return /*#__PURE__*/_jsxs(_Fragment, {\n    children: [/*#__PURE__*/_jsxs(\"div\", {\n      style: {\n        display: 'flex',\n        background: header ? 'rgba(0,0,0,0.02)' : 'none',\n        padding: '24px 8px'\n      },\n      children: [new Array(arraySize).fill(null).map(function (_, index) {\n        return /*#__PURE__*/_jsx(\"div\", {\n          style: {\n            flex: 1,\n            paddingInlineStart: header && index === 0 ? 0 : 20,\n            paddingInlineEnd: 32\n          },\n          children: /*#__PURE__*/_jsx(Skeleton, {\n            active: active,\n            paragraph: false,\n            title: {\n              style: {\n                margin: 0,\n                height: 24,\n                width: header ? '75px' : '100%'\n              }\n            }\n          })\n        }, index);\n      }), /*#__PURE__*/_jsx(\"div\", {\n        style: {\n          flex: 3,\n          paddingInlineStart: 32\n        },\n        children: /*#__PURE__*/_jsx(Skeleton, {\n          active: active,\n          paragraph: false,\n          title: {\n            style: {\n              margin: 0,\n              height: 24,\n              width: header ? '75px' : '100%'\n            }\n          }\n        })\n      })]\n    }), /*#__PURE__*/_jsx(Line, {\n      padding: \"0px 0px\"\n    })]\n  });\n};\n\n/**\n * Table 骨架屏\n *\n * @param param0\n */\nexport var TableSkeleton = function TableSkeleton(_ref4) {\n  var active = _ref4.active,\n    _ref4$size = _ref4.size,\n    size = _ref4$size === void 0 ? 4 : _ref4$size;\n  return /*#__PURE__*/_jsxs(Card, {\n    bordered: false,\n    children: [/*#__PURE__*/_jsx(Skeleton.Button, {\n      active: active,\n      size: \"small\",\n      style: {\n        width: 100,\n        marginBlockEnd: 16\n      }\n    }), /*#__PURE__*/_jsx(TableItemSkeleton, {\n      header: true,\n      active: active\n    }), new Array(size).fill(null).map(function (_, index) {\n      return (\n        /*#__PURE__*/\n        // eslint-disable-next-line react/no-array-index-key\n        _jsx(TableItemSkeleton, {\n          active: active\n        }, index)\n      );\n    }), /*#__PURE__*/_jsx(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        paddingBlockStart: 16\n      },\n      children: /*#__PURE__*/_jsx(Skeleton, {\n        active: active,\n        paragraph: false,\n        title: {\n          style: {\n            margin: 0,\n            height: 32,\n            float: 'right',\n            maxWidth: '630px'\n          }\n        }\n      })\n    })]\n  });\n};\nexport var DescriptionsSkeleton = function DescriptionsSkeleton(_ref5) {\n  var active = _ref5.active;\n  return /*#__PURE__*/_jsxs(Card, {\n    bordered: false,\n    style: {\n      borderStartEndRadius: 0,\n      borderTopLeftRadius: 0\n    },\n    children: [/*#__PURE__*/_jsx(Skeleton.Button, {\n      active: active,\n      size: \"small\",\n      style: {\n        width: 100,\n        marginBlockEnd: 16\n      }\n    }), /*#__PURE__*/_jsx(DescriptionsItemSkeleton, {\n      active: active\n    }), /*#__PURE__*/_jsx(DescriptionsLargeItemSkeleton, {\n      active: active\n    })]\n  });\n};\nvar DescriptionsPageSkeleton = function DescriptionsPageSkeleton(_ref6) {\n  var _ref6$active = _ref6.active,\n    active = _ref6$active === void 0 ? true : _ref6$active,\n    pageHeader = _ref6.pageHeader,\n    list = _ref6.list;\n  return /*#__PURE__*/_jsxs(\"div\", {\n    style: {\n      width: '100%'\n    },\n    children: [pageHeader !== false && /*#__PURE__*/_jsx(PageHeaderSkeleton, {\n      active: active\n    }), /*#__PURE__*/_jsx(DescriptionsSkeleton, {\n      active: active\n    }), list !== false && /*#__PURE__*/_jsx(Line, {}), list !== false && /*#__PURE__*/_jsx(TableSkeleton, {\n      active: active,\n      size: list\n    })]\n  });\n};\nexport default DescriptionsPageSkeleton;", "import { Card, Skeleton, Space } from 'antd';\nimport React from 'react';\nimport { PageHeaderSkeleton } from \"../List\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nvar ResultPageSkeleton = function ResultPageSkeleton(_ref) {\n  var _ref$active = _ref.active,\n    active = _ref$active === void 0 ? true : _ref$active,\n    pageHeader = _ref.pageHeader;\n  return /*#__PURE__*/_jsxs(\"div\", {\n    style: {\n      width: '100%'\n    },\n    children: [pageHeader !== false && /*#__PURE__*/_jsx(PageHeaderSkeleton, {\n      active: active\n    }), /*#__PURE__*/_jsx(Card, {\n      children: /*#__PURE__*/_jsxs(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          flexDirection: 'column',\n          padding: 128\n        },\n        children: [/*#__PURE__*/_jsx(Skeleton.Avatar, {\n          size: 64,\n          style: {\n            marginBlockEnd: 32\n          }\n        }), /*#__PURE__*/_jsx(Skeleton.Button, {\n          active: active,\n          style: {\n            width: 214,\n            marginBlockEnd: 8\n          }\n        }), /*#__PURE__*/_jsx(Skeleton.Button, {\n          active: active,\n          style: {\n            width: 328\n          },\n          size: \"small\"\n        }), /*#__PURE__*/_jsxs(Space, {\n          style: {\n            marginBlockStart: 24\n          },\n          children: [/*#__PURE__*/_jsx(Skeleton.Button, {\n            active: active,\n            style: {\n              width: 116\n            }\n          }), /*#__PURE__*/_jsx(Skeleton.Button, {\n            active: active,\n            style: {\n              width: 116\n            }\n          })]\n        })]\n      })\n    })]\n  });\n};\nexport default ResultPageSkeleton;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"type\"];\nimport \"antd/es/skeleton/style\";\nimport React from 'react';\nimport DescriptionsPageSkeleton, { DescriptionsSkeleton, TableItemSkeleton, TableSkeleton } from \"./components/Descriptions\";\nimport ListPageSkeleton, { ListSkeleton, ListSkeletonItem, ListToolbarSkeleton, PageHeaderSkeleton } from \"./components/List\";\nimport ResultPageSkeleton from \"./components/Result\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar ProSkeleton = function ProSkeleton(_ref) {\n  var _ref$type = _ref.type,\n    type = _ref$type === void 0 ? 'list' : _ref$type,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  if (type === 'result') {\n    return /*#__PURE__*/_jsx(ResultPageSkeleton, _objectSpread({}, rest));\n  }\n  if (type === 'descriptions') {\n    return /*#__PURE__*/_jsx(DescriptionsPageSkeleton, _objectSpread({}, rest));\n  }\n  return /*#__PURE__*/_jsx(ListPageSkeleton, _objectSpread({}, rest));\n};\nexport { DescriptionsSkeleton, ListPageSkeleton, ListSkeleton, ListSkeletonItem, ListToolbarSkeleton, PageHeaderSkeleton, ProSkeleton, TableItemSkeleton, TableSkeleton };\nexport default ProSkeleton;", "import { Button, ButtonProps } from 'antd'\nimport React, { useState } from 'react'\n\nconst ButtonWithLoading: React.FC<ButtonProps> = ({ onClick, ...props }) => {\n  const [loading, setLoading] = useState<boolean>(false)\n  const onClickHandler: ButtonProps['onClick'] = async (event) => {\n    setLoading(true)\n    try {\n      const result = await onClick?.(\n        event as React.MouseEvent<HTMLButtonElement, MouseEvent>\n      )\n      setLoading(false)\n      return result\n    } catch {\n      setLoading(false)\n      return ''\n    }\n  }\n\n  return <Button loading={loading} {...props} onClick={onClickHandler} />\n}\n\nexport default ButtonWithLoading\n", "import { Skeleton } from 'antd'\nimport { Suspense, lazy } from 'react'\nimport { MoleculeStructureProps } from '../MoleculeStructure'\nconst MoleculeStructure = lazy(() =>\n  import('@/components/MoleculeStructure').then((module) => ({\n    default: module.default\n  }))\n)\n\nexport default function LazySmileDrawer(props: MoleculeStructureProps) {\n  return (\n    <Suspense\n      fallback={\n        <div>\n          <Skeleton active />\n        </div>\n      }\n    >\n      <MoleculeStructure {...props} />\n    </Suspense>\n  )\n}\n", "import MoleculeStructure from '@/components/MoleculeStructure'\nimport { AiProcedure } from '@/types/Procedure'\nimport { getWord } from '@/utils'\nimport { PlusOutlined } from '@ant-design/icons'\nimport { useAccess } from '@umijs/max'\nimport { Button, Card, Space, Tag } from 'antd'\nimport Link from 'antd/lib/typography/Link'\nimport Paragraph from 'antd/lib/typography/Paragraph'\nimport Text from 'antd/lib/typography/Text'\nimport Title from 'antd/lib/typography/Title'\nimport React, { useState } from 'react'\nimport './index.less'\nimport { getTagForReferenceType, getTagForSameOrSimilerType } from './util'\n\nexport interface AiProcedureCardProps {\n  procedure: AiProcedure\n  name?: string\n  isSame?: boolean\n  onReference?: (procedure: AiProcedure) => void\n}\n\nconst AiProcedureCard: React.FC<AiProcedureCardProps> = ({\n  procedure,\n  name,\n  isSame,\n  onReference\n}) => {\n  const access = useAccess()\n  const [expanded, setExpanded] = useState<boolean>(false)\n  const {\n    text,\n    reference,\n    rxn,\n    similarity,\n    experimentalProcedure,\n    yieldString\n  } = procedure\n  const { type, link, title, authors, date, no, assignees, reference_text } =\n    reference\n\n  const expandText = (\n    <Text\n      style={{ color: '#1890ff', cursor: 'pointer' }}\n      onClick={(e) => {\n        e.stopPropagation()\n        setExpanded((pre) => !pre)\n      }}\n    >\n      {expanded ? 'less' : 'more'}\n    </Text>\n  )\n\n  return (\n    <Card size=\"small\" className=\"ai-card-root\">\n      <div className=\"title\">\n        <div className=\"info-wrapper\">\n          <div>\n            <Text type=\"secondary\">{getWord('similarity')}</Text>:\n            <Text style={{ color: '#027AFF', paddingLeft: 8 }}>\n              {similarity}\n            </Text>\n          </div>\n          <Space />\n          <div>\n            <Text type=\"secondary\">{getWord('yield')}</Text>:\n            <Text style={{ color: '#027AFF', paddingLeft: 8 }}>\n              {yieldString}\n            </Text>\n          </div>\n          <div className=\"buttons-wrapper\">\n            {!!procedure.scalable && (\n              <>\n                <Space />\n                <Tag color=\"lime\">{getWord('procedure.scalable')}</Tag>\n              </>\n            )}\n            {isSame !== undefined ? (\n              <>\n                <Space />\n                {getTagForSameOrSimilerType(isSame, name)}\n              </>\n            ) : null}\n            {access?.authCodeList?.includes(\n              'view-by-backbone.tab.recommend.addToMyReaction'\n            ) &&\n              onReference && (\n                <Button\n                  icon={<PlusOutlined />}\n                  size=\"small\"\n                  onClick={() => onReference(procedure)}\n                >\n                  {getWord('pages.route.label.addToMyReaction')}\n                </Button>\n              )}\n          </div>\n        </div>\n      </div>\n      <Space />\n      <div>\n        <MoleculeStructure structure={rxn} />\n      </div>\n      <div className=\"procedure-wrapper\">\n        <Title level={5}>Procedure</Title>\n        <Paragraph\n          ellipsis={\n            expanded\n              ? false\n              : {\n                  rows: 5,\n                  expandable: true,\n                  symbol: expandText\n                }\n          }\n          copyable={{ text }}\n        >\n          {text || experimentalProcedure || ''}\n          {expanded && expandText}\n        </Paragraph>\n      </div>\n      <div className=\"reference-wrapper\">\n        <Title level={5}>Reference {getTagForReferenceType(type)}</Title>\n        {type === 'patent' && (no || assignees) && (\n          <Text style={{ textAlign: 'right', display: 'block', width: '100%' }}>\n            {no && (\n              <>\n                Patent No: <Text copyable>{no}</Text>\n              </>\n            )}\n            {assignees && <Text>, assigned by {assignees}</Text>}\n            <br />\n          </Text>\n        )}\n        {link && (\n          <>\n            <Text type=\"secondary\" copyable>\n              {link ? <Link href={link}>{title}</Link> : title}\n            </Text>\n            <br />\n          </>\n        )}\n        {reference_text && (\n          <>\n            <Text type=\"secondary\" copyable>\n              {reference_text}\n            </Text>\n            <br />\n          </>\n        )}\n        <Text\n          italic\n          style={{ textAlign: 'right', display: 'block', width: '100%' }}\n        >\n          {authors}\n        </Text>\n        {date && (\n          <Text style={{ textAlign: 'right', display: 'block', width: '100%' }}>\n            {date}\n          </Text>\n        )}\n      </div>\n    </Card>\n  )\n}\n\nexport default AiProcedureCard\n", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"fieldProps\", \"proFieldProps\"];\nimport { dateArrayFormatter } from '@ant-design/pro-utils';\nimport React, { useContext } from 'react';\nimport FieldContext from \"../../FieldContext\";\nimport ProField from \"../Field\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar valueType = 'dateYearRange';\n\n/**\n * 季度份区间选择组件\n *\n * @param\n */\nvar DateYearRangePicker = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var fieldProps = _ref.fieldProps,\n    proFieldProps = _ref.proFieldProps,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var context = useContext(FieldContext);\n  return /*#__PURE__*/_jsx(ProField, _objectSpread({\n    ref: ref,\n    fieldProps: _objectSpread({\n      getPopupContainer: context.getPopupContainer\n    }, fieldProps),\n    valueType: valueType,\n    proFieldProps: proFieldProps,\n    filedConfig: {\n      valueType: valueType,\n      customLightMode: true,\n      lightFilterLabelFormatter: function lightFilterLabelFormatter(value) {\n        return dateArrayFormatter(value, (fieldProps === null || fieldProps === void 0 ? void 0 : fieldProps.format) || 'YYYY');\n      }\n    }\n  }, rest));\n});\nexport default DateYearRangePicker;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"fieldProps\", \"proFieldProps\", \"min\", \"max\", \"step\", \"marks\", \"vertical\", \"range\"];\nimport React from 'react';\nimport ProField from \"../Field\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * 文本选择组件\n *\n * @param\n */\nvar ProFormSlider = /*#__PURE__*/React.forwardRef(function (_ref, ref) {\n  var fieldProps = _ref.fieldProps,\n    proFieldProps = _ref.proFieldProps,\n    min = _ref.min,\n    max = _ref.max,\n    step = _ref.step,\n    marks = _ref.marks,\n    vertical = _ref.vertical,\n    range = _ref.range,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/_jsx(ProField, _objectSpread({\n    valueType: \"slider\",\n    fieldProps: _objectSpread(_objectSpread({}, fieldProps), {}, {\n      min: min,\n      max: max,\n      step: step,\n      marks: marks,\n      vertical: vertical,\n      range: range,\n      style: fieldProps === null || fieldProps === void 0 ? void 0 : fieldProps.style\n    }),\n    ref: ref,\n    proFieldProps: proFieldProps,\n    filedConfig: {\n      ignoreWidth: true\n    }\n  }, rest));\n});\nexport default ProFormSlider;", "// extracted by mini-css-extract-plugin\nexport default {\"fieldsWrapper\":\"fieldsWrapper___YYoyM\",\"slider\":\"slider___JccVt\"};", "import { getWord } from '@/utils'\nimport {\n  ProFormDateYearRangePicker,\n  ProFormSlider,\n  ProFormSwitch\n} from '@ant-design/pro-components'\nimport React from 'react'\nimport styles from './index.less'\n\nconst maxImpactFactor = 50.5\nconst step = 10\nconst Fields: React.FC = () => {\n  return (\n    <div className={styles.fieldsWrapper}>\n      <ProFormDateYearRangePicker\n        name=\"yearRange\"\n        label={getWord('procedure-filter.year-range')}\n        fieldProps={{\n          allowEmpty: [true, true],\n          placeholder: [\n            getWord('procedure-filter.year-range-empty'),\n            getWord('procedure-filter.year-range-empty')\n          ]\n        }}\n      />\n      <ProFormSlider\n        name=\"impactFactor\"\n        range\n        max={maxImpactFactor}\n        min={0}\n        marks={Object.fromEntries(\n          [...Array(Math.floor(maxImpactFactor / step))].map((_, i) => [\n            i * step,\n            `${i * step}`\n          ])\n        )}\n        label={getWord('procedure-filter.impact-factor')}\n        fieldProps={{ range: true, className: styles.slider }}\n      />\n      <ProFormSwitch\n        name=\"validProcedure\"\n        label={getWord('procedure-filter.has-procedure-only')}\n      />\n      <ProFormSwitch\n        name=\"scalable\"\n        label={getWord('procedure-filter.scalable')}\n      />\n      <ProFormSwitch name=\"same\" label={getWord('same-reaction')} />\n      <ProFormSwitch name=\"similar\" label={getWord('reference')} />\n    </div>\n  )\n}\n\nexport default Fields\n", "import { Form } from 'antd'\nimport dayjs, { Dayjs } from 'dayjs'\nimport { useEffect, useState } from 'react'\nimport { ProcedureFilter } from '../useProcedure'\n\nexport interface FilterFieldsForForm {\n  yearRange?: (Dayjs | undefined)[]\n  impactFactor?: (number | undefined)[]\n  validProcedure?: boolean\n  scalable?: boolean\n  same?: boolean\n  similar?: boolean\n}\n\nexport const frontToBack = (values: FilterFieldsForForm): ProcedureFilter => {\n  const { yearRange, impactFactor, validProcedure, scalable, same, similar } =\n    values\n  return {\n    year_min: yearRange?.[0]?.year(),\n    year_max: yearRange?.[1]?.year(),\n    impact_factor_min: impactFactor?.[0],\n    impact_factor_max: impactFactor?.[1],\n    has_valid_procedure: validProcedure,\n    need_scalable: scalable,\n    same,\n    similar\n  }\n}\n\nexport const backToFront = (values: ProcedureFilter): FilterFieldsForForm => {\n  const {\n    year_min,\n    year_max,\n    impact_factor_min,\n    impact_factor_max,\n    has_valid_procedure,\n    need_scalable,\n    same,\n    similar\n  } = values\n  return {\n    yearRange: [year_min, year_max].map((y) =>\n      y === undefined ? undefined : dayjs(`${y}-01-01`)\n    ),\n    impactFactor: [impact_factor_min, impact_factor_max],\n    validProcedure: has_valid_procedure,\n    scalable: need_scalable,\n    same,\n    similar\n  }\n}\n\nexport const useFilterForm = (init?: ProcedureFilter) => {\n  const [form] = Form.useForm<FilterFieldsForForm>()\n  const [latestInit, setLatestInit] = useState<ProcedureFilter>()\n\n  useEffect(() => {\n    const latest = backToFront(init || {})\n    form.setFieldsValue(latest)\n    setLatestInit(latest)\n  }, [form, init])\n\n  const handleFinish = (onFinished?: (values: ProcedureFilter) => void) => {\n    return async () => {\n      const values = form.getFieldsValue()\n      onFinished?.(frontToBack(values))\n      setLatestInit(values)\n      return true\n    }\n  }\n\n  return {\n    form,\n    handleFinish,\n    reset: () => form.setFieldsValue(latestInit || {}),\n    touched: form.isFieldsTouched()\n  }\n}\n", "var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nimport * as React from \"react\";\nconst SvgNodata = (props) => /* @__PURE__ */ React.createElement(\"svg\", __spreadValues({ width: 160, height: 160, fill: \"none\", xmlns: \"http://www.w3.org/2000/svg\" }, props), /* @__PURE__ */ React.createElement(\"path\", { d: \"M113.5 19.833c-13.083 5.284-49.467 19.517-51.533 14.934-5.317-11.784 8.716-8.967 14.233-5.1.7.5 2.383 1.95 2.25 3.45-.383 4.816-2.567 6.583-31.583 17.25\", stroke: \"#333\", strokeWidth: 0.5, strokeLinecap: \"round\", strokeLinejoin: \"round\", strokeDasharray: \"2 2\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M32.2 65.333c-3.117 6.217-10.667 9.784-10.667 9.784s.884-6.217.45-10.667c-.3-2.967 2.584-7.117 5.784-7.117 3.2 0 5.866 5.15 4.45 8H32.2Z\", fill: \"#6691D6\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M25.083 68c.717-6 3.4-12.583 9.5-15.017.434-.183.917.034 1.1.467a.843.843 0 0 1-.466 1.083c-5.8 1.667-8.934 7.85-10.134 13.467Z\", fill: \"#CCDAF1\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M113.667 95.533c0 17.617-14.284 31.9-31.9 31.9-17.617 0-31.9-14.283-31.9-31.9 0-17.616 14.283-31.9 31.9-31.9 3.95 0 7.75.717 11.233 2.034a31.825 31.825 0 0 1 12.8 8.883 31.801 31.801 0 0 1 7.867 20.983Z\", fill: \"#fff\", stroke: \"#000\", strokeWidth: 0.5, strokeLinecap: \"round\", strokeLinejoin: \"round\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"m109.967 68.433-4.184 6.117a31.824 31.824 0 0 0-12.8-8.883l4.184-5.684v-.016c2.266.133 8.766 1.183 12.7 8.333.033.05.05.083.066.133h.034Z\", fill: \"#fff\", stroke: \"#000\", strokeWidth: 0.5, strokeLinecap: \"round\", strokeLinejoin: \"round\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"m110.033 68.333-.066.1\", stroke: \"#000\", strokeWidth: 0.5, strokeLinecap: \"round\", strokeLinejoin: \"round\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"m116.883 61.533-3.55 5.2c-3.916-9.266-12.583-10.35-16.416-10.35l3.466-5.65a3.27 3.27 0 0 1 2.617-1.55c9.367-.45 13.017 5.95 14.267 9.184.4 1.05.266 2.233-.367 3.166h-.017Z\", fill: \"#fff\", stroke: \"#000\", strokeWidth: 0.5, strokeLinecap: \"round\", strokeLinejoin: \"round\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"m113.333 66.733-.133.184a3.282 3.282 0 0 1-3.167 1.4c-.05 0-.1 0-.15-.017-3.933-7.15-10.433-8.2-12.7-8.333h-.05c-1.033-.95-1.083-2.1-.3-3.467l.067-.1c3.85 0 12.5 1.083 16.417 10.35l.016-.017Z\", fill: \"#E0DEDE\", stroke: \"#000\", strokeWidth: 0.5, strokeLinecap: \"round\", strokeLinejoin: \"round\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"m97.183 59.983-.033-.033\", stroke: \"#000\", strokeWidth: 0.5, strokeLinecap: \"round\", strokeLinejoin: \"round\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M73.537 70.034c1.622-.575 2.8-1.427 2.632-1.904-.17-.477-1.622-.399-3.244.175-1.623.575-2.801 1.427-2.632 1.904.168.478 1.62.4 3.243-.175ZM55.35 83.667c.683-1.984 3.017-7.467 8.633-10.917.267-.167.584-.267.884-.283.5-.034 1.216.033 1.533.65.25.483.083 1.083-.35 1.416-1.117.867-4 3.5-7.7 9.95a1.674 1.674 0 0 1-.55.584c-.367.25-.983.533-1.583.366-.734-.216-1.117-1.05-.85-1.783l-.017.017Z\", fill: \"#9ACDF7\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M108.717 52.683s4.15 1.15 5.983 6.684M108.017 68.35l-2.3 3\", stroke: \"#000\", strokeWidth: 0.5, strokeLinecap: \"round\", strokeLinejoin: \"round\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M96.617 87.133a.233.233 0 1 0 0-.466.233.233 0 0 0 0 .466ZM101 81.833a.233.233 0 1 0 0-.466.233.233 0 0 0 0 .466ZM106.517 84.133a.233.233 0 1 0 0-.466.234.234 0 1 0 0 .466ZM103.633 89.433a.117.117 0 1 0 0-.233.117.117 0 0 0 0 .233Z\", fill: \"#fff\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M78.767 98.067c0 3.366-2.734 6.1-6.1 6.1a6.103 6.103 0 0 1-6.1-6.1c0-3.367 2.75-6 6.116-6 3.367 0 6.084 2.616 6.084 6Z\", fill: \"#000\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M75.35 101.95a1.267 1.267 0 1 0 0-2.533 1.267 1.267 0 0 0 0 2.533Z\", fill: \"#fff\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M85.45 98.067c0 3.366 2.733 6.1 6.1 6.1 3.367 0 6.1-2.734 6.1-6.1 0-3.367-2.8-6-6.167-6-3.366 0-6.05 2.616-6.05 6h.017Z\", fill: \"#000\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M88.9 101.95a1.267 1.267 0 1 0 0-2.533 1.267 1.267 0 0 0 0 2.533ZM74.8 98.6a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5ZM89.3 98.6a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z\", fill: \"#fff\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M81 111.167c.35-.317.8-.517 1.317-.517.533 0 1.016.217 1.366.567M87.817 87.8s4.516 3.8 7.766 1.933M75.6 87.8s-4.517 3.8-7.767 1.933\", stroke: \"#000\", strokeWidth: 0.5, strokeLinecap: \"round\", strokeLinejoin: \"round\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M80.833 144.167c6.444 0 11.667-1.493 11.667-3.334s-5.223-3.333-11.667-3.333c-6.443 0-11.666 1.492-11.666 3.333 0 1.841 5.223 3.334 11.666 3.334Z\", fill: \"#DBDBDB\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M30.733 64.75c-3.116 6.217-10.666 9.783-10.666 9.783s.883-6.216.45-10.666c-.3-2.967 2.583-7.117 5.783-7.117s5.867 5.15 4.45 8h-.017Z\", stroke: \"#333\", strokeWidth: 0.5, strokeLinecap: \"round\", strokeLinejoin: \"round\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M23.633 67.417c.717-6 3.4-12.584 9.5-15.017.434-.183.917.033 1.1.467a.844.844 0 0 1-.466 1.083c-5.8 1.667-8.934 7.85-10.134 13.467Z\", fill: \"#333\" }));\nexport { SvgNodata as ReactComponent };\nexport default \"data:image/svg+xml;base64,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\";\n", "import { getWord } from '@/utils'\nimport { FilterOutlined } from '@ant-design/icons'\nimport { ModalForm } from '@ant-design/pro-components'\nimport { Button } from 'antd'\nimport React from 'react'\nimport { ProcedureFilter } from '../useProcedure'\nimport Fields from './Fields'\nimport { FilterFieldsForForm, useFilterForm } from './useFilterForm'\n\ninterface FilterModalProps {\n  initialFilter?: ProcedureFilter\n  onFinished?: (values: ProcedureFilter) => void\n}\n\nconst FilterModal: React.FC<FilterModalProps> = ({\n  onFinished,\n  initialFilter\n}) => {\n  const { form, handleFinish, reset } = useFilterForm(initialFilter)\n\n  return (\n    <ModalForm<FilterFieldsForForm>\n      title={getWord('procedure-filter.title')}\n      width={400}\n      size=\"small\"\n      trigger={<Button size=\"small\" icon={<FilterOutlined />} type=\"text\" />}\n      form={form}\n      onFinish={handleFinish(onFinished)}\n      modalProps={{ onCancel: reset }}\n    >\n      <Fields />\n    </ModalForm>\n  )\n}\n\nexport default FilterModal\n", "import { fetchAllRetroReactions } from '@/pages/reaction/util'\nimport { getRxnFromReaction, Reaction } from '@/pages/route/util'\nimport { ProcedureRxnMatchResponse, service } from '@/services/brain'\nimport { AiProcedure } from '@/types/Procedure'\nimport { useQuery } from '@tanstack/react-query'\nimport { transformProcedureMatchResponse } from './util'\n\nexport interface ProcedureFilter {\n  has_valid_procedure?: boolean\n  same?: boolean\n  similar?: boolean\n  need_scalable?: boolean // true表示必须适合工艺放大，缺省为false\n  year_min?: number // int, 表示过滤最早年限(包含), null表示不限制, 缺省为null,\n  year_max?: number // int, 表示过滤最晚年限(包含), null表示不限制, 缺省为null,\n  impact_factor_min?: number // float, 缺省为null, 表示不限制\n  impact_factor_max?: number // float, 缺省为null，表示不限制\n}\n\nexport const defaultTotal = 30\n\nconst fetchInfo = async (\n  reaction: Reaction,\n  searchRxns?: string[],\n  filters?: ProcedureFilter\n): Promise<AiProcedure[]> => {\n  if (!reaction.reactants.length) return []\n\n  const rxns =\n    searchRxns ||\n    (await fetchAllRetroReactions(reaction))?.map((r) => getRxnFromReaction(r))\n  const simple_rxn = getRxnFromReaction(reaction)\n\n  const res = await service('procedure/rxn-match', {\n    method: 'post',\n    normalizeData: false,\n    data: {\n      simple_rxn,\n      rxns: rxns?.length ? rxns : [simple_rxn],\n      similar_topk: defaultTotal,\n      ...filters\n    }\n  })\n    .select()\n    .get()\n\n  if (res) {\n    const { similar_procedures, same_procedures } =\n      res as unknown as ProcedureRxnMatchResponse\n\n    return [\n      ...same_procedures.map((p) => transformProcedureMatchResponse(p, true)),\n      ...similar_procedures.map((p) =>\n        transformProcedureMatchResponse(p, false)\n      )\n    ]\n  }\n  throw new Error('Network response was not ok')\n}\n\nexport const useProcedure = (\n  reaction?: Reaction,\n  rxns?: string[],\n  filters?: ProcedureFilter\n) => {\n  const { data, error, isLoading, refetch } = useQuery({\n    queryKey: ['procedure/rxn-match', reaction, rxns, filters],\n    queryFn: () => (reaction ? fetchInfo(reaction, rxns, filters) : []),\n    enabled: !!reaction\n  })\n\n  const procedures = data?.filter((p) =>\n    (p.isSame ? filters?.same : filters?.similar) === false ? false : true\n  )\n\n  return { procedures, total: procedures?.length, error, isLoading, refetch }\n}\n", "import { ReactComponent as EmptyImage } from '@/assets/svgs/nodata.svg'\nimport StatusTip from '@/components/StatusTip'\nimport { useUserSettingQuery } from '@/hooks/useUserSetting'\nimport { Reaction } from '@/pages/route/util'\nimport { Paginate } from '@/services/brain'\nimport { AiProcedure } from '@/types/Procedure'\nimport { getWord } from '@/utils'\nimport { ProList } from '@ant-design/pro-components'\nimport { ConfigProvider } from 'antd'\nimport React, { useState } from 'react'\nimport ReactDOM from 'react-dom'\nimport AiProcedureCard from './AiProcedureCard'\nimport FilterModal from './Filter/Modal'\nimport { defaultTotal, ProcedureFilter, useProcedure } from './useProcedure'\n\nconst defaultRender =\n  (rxns?: { rxn: string; name: string }[]) =>\n  (procedure: AiProcedure, isSame?: boolean) =>\n    (\n      <AiProcedureCard\n        procedure={procedure}\n        isSame={isSame}\n        name={rxns?.find((r) => r.rxn === procedure.query)?.name}\n      />\n    )\n\nexport interface ReactionLibTabProps {\n  reaction?: Reaction\n  rxns?: { rxn: string; name: string }[]\n  renderer?: (procedure: AiProcedure, isSame?: boolean) => React.ReactNode\n  actionSlot?: HTMLElement\n}\n\nconst ReactionLibTab: React.FC<ReactionLibTabProps> = ({\n  reaction,\n  actionSlot,\n  rxns,\n  renderer = defaultRender(rxns)\n}) => {\n  const { setting: { procedure = {} } = {} } = useUserSettingQuery()\n  const [filtersUpdate, setFiltersUpdate] = useState<ProcedureFilter>()\n  const filters = { ...procedure, ...filtersUpdate }\n  const { isLoading, procedures } = useProcedure(\n    reaction,\n    rxns?.map((r) => r.rxn),\n    filters\n  )\n\n  const [paginate, setPaginate] = useState<Paginate>({ page: 1, pageSize: 10 })\n\n  return (\n    <ConfigProvider\n      renderEmpty={() => (\n        <StatusTip image={<EmptyImage />} des={getWord('noticeIcon.empty')} />\n      )}\n    >\n      <ProList\n        ghost\n        loading={isLoading}\n        pagination={\n          procedures?.length\n            ? {\n                current: paginate.page,\n                pageSize: paginate.pageSize,\n                simple: true,\n                total: Math.min(procedures?.length || 0, defaultTotal),\n                onChange: (page, pageSize) => setPaginate({ page, pageSize })\n              }\n            : false\n        }\n        renderItem={(item) => renderer(item, item.isSame)}\n        grid={{ column: 1 }}\n        dataSource={procedures}\n      />\n      {actionSlot &&\n        ReactDOM.createPortal(\n          <FilterModal onFinished={setFiltersUpdate} initialFilter={filters} />,\n          actionSlot\n        )}\n    </ConfigProvider>\n  )\n}\n\nexport default ReactionLibTab\n", "import { ProcedureRxnMatchResult } from '@/services/brain'\nimport { AiProcedure } from '@/types/Procedure'\nimport { getWord, isEN } from '@/utils'\nimport { Tag } from 'antd'\nimport { ReactElement } from 'react'\n\nexport const transformReferenceType = (\n  type: ProcedureRxnMatchResult['reference_type']\n): AiProcedure['reference']['type'] => {\n  switch (type) {\n    case 'JOURNAL':\n      return 'journal'\n    case 'Patent':\n      return 'patent'\n    default:\n      return type\n  }\n}\n\nexport const transformProcedureMatchResponse = (\n  {\n    id,\n    experimental_procedure = '',\n    query = '',\n    procedure = '',\n    similarity = 0,\n    rxn = '',\n    rxn_yields = '',\n    yields,\n    transformation = '',\n    reference_type = 'JOURNAL',\n    title = '',\n    authors = '',\n    date = '',\n    assignees = '',\n    reference_text = '',\n    patent_id = '',\n    is_scalable = false\n  }: ProcedureRxnMatchResult,\n  isSame: boolean = false\n): AiProcedure => ({\n  id,\n  rxn,\n  isSame,\n  text: procedure || '',\n  experimentalProcedure: experimental_procedure || '',\n  transformation: transformation || '',\n  query: query || '',\n  reference: {\n    type: transformReferenceType(reference_type),\n    title: title || '',\n    authors: authors || '',\n    date: date || '',\n    no: patent_id || '',\n    reference_text: reference_text || '',\n    assignees: assignees || ''\n  },\n  yieldString: rxn_yields || '',\n  yieldNumber: yields || undefined,\n  similarity: similarity || 0,\n  scalable: is_scalable || false\n})\n\nexport const getTagForReferenceType = (\n  type: AiProcedure['reference']['type']\n): ReactElement => {\n  switch (type) {\n    case 'journal':\n      return <Tag color=\"green\">Journal</Tag>\n    case 'patent':\n      return <Tag color=\"blue\">Patent</Tag>\n    default:\n      return <Tag color=\"orange\">Custom</Tag>\n  }\n}\n\nexport const getTagForSameOrSimilerType = (\n  isSame: boolean,\n  name?: string\n): ReactElement => {\n  switch (isSame) {\n    case true:\n      return (\n        <Tag color=\"green\">\n          {name\n            ? isEN()\n              ? `The Same Reaction as ${name}`\n              : `与${name}是${getWord('same-reaction')}`\n            : getWord('same-reaction')}\n        </Tag>\n      )\n\n    case false:\n      return (\n        <Tag color=\"blue\">\n          {name\n            ? isEN()\n              ? `Reference for ${name}`\n              : `与${name}是${getWord('reference')}`\n            : getWord('reference')}\n        </Tag>\n      )\n    default:\n      return <></>\n  }\n}\n", "var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nimport * as React from \"react\";\nconst SvgWarning = (props) => /* @__PURE__ */ React.createElement(\"svg\", __spreadValues({ fill: \"none\", xmlns: \"http://www.w3.org/2000/svg\" }, props), /* @__PURE__ */ React.createElement(\"g\", { clipPath: \"url(#warning_svg__a)\" }, /* @__PURE__ */ React.createElement(\"path\", { d: \"M7.978 5v4.068\", stroke: \"#FAAD14\", strokeLinecap: \"round\", strokeLinejoin: \"round\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M8 11.527a.5.5 0 1 0 0-1 .5.5 0 0 0 0 1Z\", fill: \"#FAAD14\" }), /* @__PURE__ */ React.createElement(\"circle\", { cx: 8, cy: 8, r: 6.5, stroke: \"#FAAD14\" })), /* @__PURE__ */ React.createElement(\"defs\", null, /* @__PURE__ */ React.createElement(\"clipPath\", { id: \"warning_svg__a\" }, /* @__PURE__ */ React.createElement(\"path\", { fill: \"#fff\", d: \"M0 0h16v16H0z\" }))));\nexport { SvgWarning as ReactComponent };\nexport default \"data:image/svg+xml;base64,PHN2ZyBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxnIGNsaXAtcGF0aD0idXJsKCNhKSI+PHBhdGggZD0iTTcuOTc4IDV2NC4wNjgiIHN0cm9rZT0iI0ZBQUQxNCIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PHBhdGggZD0iTTggMTEuNTI3YS41LjUgMCAxIDAgMC0xIC41LjUgMCAwIDAgMCAxWiIgZmlsbD0iI0ZBQUQxNCIvPjxjaXJjbGUgY3g9IjgiIGN5PSI4IiByPSI2LjUiIHN0cm9rZT0iI0ZBQUQxNCIvPjwvZz48ZGVmcz48Y2xpcFBhdGggaWQ9ImEiPjxwYXRoIGZpbGw9IiNmZmYiIGQ9Ik0wIDBoMTZ2MTZIMHoiLz48L2NsaXBQYXRoPjwvZGVmcz48L3N2Zz4=\";\n", "import LazySmileDrawer from '@/components/LazySmileDrawer'\nimport type { MaterialItem } from '@/services/brain'\nimport { getWord, isEN, isValidArray } from '@/utils'\nimport { useModel } from '@umijs/max'\nimport { Table } from 'antd'\nimport Paragraph from 'antd/lib/typography/Paragraph'\nimport { isEmpty } from 'lodash'\n\ninterface MaterialTableProps {\n  materialsPrice: MaterialItem[]\n}\nexport default function MaterialPriceTable(props: MaterialTableProps) {\n  const { materialCodeOptions } = useModel('compound')\n  const getTheLabel = (newCode: string) => {\n    if (!materialCodeOptions) return ''\n    let targetTag = ''\n    Object.keys(materialCodeOptions).map((key: string) => {\n      let curOption: string[] = materialCodeOptions[key]\n      if (curOption && curOption.includes(newCode)) targetTag = key\n      return null\n    })\n    return getWord(targetTag)\n  }\n\n  const getTags = (codes: string[], link: string) => {\n    let tags: string[] = []\n    for (const code of codes) {\n      let tag = getTheLabel(code)\n      if (tag) tags.push(tag)\n    }\n    if (isEmpty(tags)) return '-'\n    return link ? <a href={link}>{tags.join('、')}</a> : tags.join('、')\n  }\n\n  const coloumns = [\n    {\n      title: getWord('structural'),\n      dataIndex: 'canonical_smiles',\n      render: (smiles: string) =>\n        smiles ? (\n          <LazySmileDrawer structure={smiles} className=\"smilesItem\" />\n        ) : (\n          ''\n        )\n    },\n    {\n      title: 'CAS',\n      dataIndex: 'cas_no',\n      render: (text: string) =>\n        text && text !== '-' ? (\n          <Paragraph ellipsis={false} copyable style={{ marginTop: '15px' }}>\n            {text}\n          </Paragraph>\n        ) : (\n          '-'\n        )\n    },\n    {\n      title: getWord('unit-price'),\n      dataIndex: 'min_unit_price',\n      render: (text: number, record: MaterialItem) =>\n        text\n          ? isEN()\n            ? `From ￥${text.toFixed(2)}/${\n                record?.unified_unit.includes('g') ? 'g' : 'ml'\n              }`\n            : `￥${text.toFixed(2)}/${\n                record?.unified_unit.includes('g') ? 'g' : 'ml'\n              }起`\n          : '-'\n    },\n    {\n      title: getWord('amount'),\n      dataIndex: 'unit_range',\n      render: (_text: string, record: MaterialItem) =>\n        isValidArray(record?.unit_range) &&\n        record?.unit_range[0] &&\n        record?.unit_range[1]\n          ? `${record?.unit_range[0]}${record?.unified_unit}-${record?.unit_range[1]}${record?.unified_unit}`\n          : '-'\n    },\n    {\n      title: getWord('label'),\n      dataIndex: 'codes',\n      width: 110,\n      render: (_text: string, record: MaterialItem) =>\n        !isEmpty(record?.codes)\n          ? getTags(record?.codes as string[], record?.pubchem_safety_link)\n          : '-'\n    },\n    {\n      title: getWord('supplier'),\n      dataIndex: 'source',\n      render: (text: string, record: MaterialItem) =>\n        record?.source_link ? (\n          <a onClick={() => window.open(record?.source_link)}>{text}</a>\n        ) : (\n          text\n        )\n    },\n    {\n      title: getWord('version'),\n      dataIndex: 'material_lib',\n      render: (_text: string, record: MaterialItem) =>\n        record?.material_lib?.version\n    }\n  ]\n  return (\n    <Table\n      columns={coloumns}\n      dataSource={props?.materialsPrice}\n      pagination={false}\n    />\n  )\n}\n", "// extracted by mini-css-extract-plugin\nexport default {\"retroReactionRowRoot\":\"retroReactionRowRoot___jKhNK\",\"riskTag\":\"riskTag___iqJK6\",\"retroReactionRoot\":\"retroReactionRoot___UV23y\",\"selectable\":\"selectable___NPtwN\",\"smiles\":\"smiles___InELl\",\"actionsWrapper\":\"actionsWrapper___G4nFE\",\"rightActionWrapper\":\"rightActionWrapper___wZJji\",\"sourceWrapper\":\"sourceWrapper___IiGJg\",\"retroReactionTabRoot\":\"retroReactionTabRoot___iBFo5\",\"materialsPriceTable\":\"materialsPriceTable___fmkpq\",\"viwePrice\":\"viwePrice___ZS0HL\",\"warningIcon\":\"warningIcon___PrHbA\"};", "import { ReactComponent as WarningIcon } from '@/assets/svgs/warning.svg'\nimport Launcher from '@/components/Launcher'\nimport MoleculeStructure from '@/components/MoleculeStructure'\nimport { useBrainFetch } from '@/hooks/useBrainFetch'\nimport { Reaction, getRxnFromReaction } from '@/pages/route/util'\nimport {\n  MaterialItem,\n  MaterialItemSearchResponse,\n  RetroReaction,\n  query,\n  queryWithDefaultOrder\n} from '@/services/brain'\nimport { getWord } from '@/utils'\nimport {\n  ProList,\n  ProSkeleton,\n  useDeepCompareEffect\n} from '@ant-design/pro-components'\nimport { Modal, Popover, Space, Tag, Typography, message } from 'antd'\nimport { default as classNames, default as cs } from 'classnames'\nimport { isEmpty, round } from 'lodash'\nimport React, { useEffect, useState } from 'react'\nimport { useAccess, useModel } from 'umi'\nimport ButtonWithLoading from '../ButtonWithLoading'\nimport MaterialPriceTable from './MaterialPriceTable'\nimport TabWithNumber from './TabWithNumber'\nimport styles from './index.less'\n\nconst getNameOfReaction = (i: number): string =>\n  `${getWord('reaction')}${i + 1}`\n\nconst isCurrentProcess = (\n  reaction: RetroReaction,\n  processId: number\n): boolean => {\n  return reaction.retro_processes?.findIndex((p) => p.id === processId) !== -1\n}\n\nexport interface RetroReactionTabProps {\n  reaction?: Reaction\n  projectId?: number\n  onSelectProcedure?: (p?: string) => void\n  onSelect?: (reactions: { rxn: RetroReaction; name: string }[]) => void\n  selectAllWhenEmpty?: boolean\n  retroProcessId?: number\n  fullReactionConfig?: {\n    fullReaction?: Reaction\n    onSelectFullReaction?: (r: Reaction) => void\n  }\n}\n\nconst RetroReactionTab: React.FC<RetroReactionTabProps> = ({\n  reaction,\n  projectId,\n  onSelectProcedure,\n  onSelect,\n  selectAllWhenEmpty,\n  retroProcessId,\n  fullReactionConfig\n}) => {\n  const {\n    getProfileInfo,\n    getCommonExpression,\n    showLauncher,\n    sendMessage,\n    reload,\n    finishedReload,\n    reactionStepNo,\n    isOpen\n  } = useModel('commend')\n  const { getMaterialCodeOptions } = useModel('compound')\n  const access = useAccess()\n  const [retroReactions, setRetroReactions] = useState<RetroReaction[]>([])\n  const { fetch, loading } = useBrainFetch()\n  const [isModalOpen, setIsModalOpen] = useState(false)\n  const [materialsPrice, setMaterialsPrice] = useState<MaterialItem[]>([])\n  const fetchReaction = async () => {\n    if (!reaction?.reactants?.length) return\n    const { data } = await fetch(\n      queryWithDefaultOrder<RetroReaction>(\n        'retro-reactions/list?comment=true',\n        { method: 'post', data: { filters: { ...reaction, projectId } } }\n      )\n        .populateWith('retro_processes', ['id'])\n        .sortBy([{ field: 'updatedAt', order: 'desc' }])\n        .paginate(1, 1000)\n        .get()\n    )\n    if (data) {\n      setRetroReactions(data)\n      onSelect?.(data.map((r, i) => ({ rxn: r, name: getNameOfReaction(i) })))\n    }\n  }\n  const { fullReaction } = fullReactionConfig || {}\n\n  useDeepCompareEffect(() => {\n    fetchReaction()\n    getCommonExpression('retro-reaction')\n  }, [reaction])\n\n  useEffect(() => {\n    getMaterialCodeOptions()\n  }, [])\n\n  useEffect(() => {\n    if (reload) {\n      fetchReaction()\n      finishedReload()\n    }\n  }, [reload])\n\n  if (!retroReactions) {\n    return <ProSkeleton type=\"list\" />\n  }\n  return (\n    <>\n      <Modal\n        title={getWord('material-sheet')}\n        open={isModalOpen}\n        className={styles.materialsPriceTable}\n        onCancel={() => setIsModalOpen(false)}\n        footer={false}\n        centered\n        width={920}\n      >\n        <MaterialPriceTable materialsPrice={materialsPrice} />\n      </Modal>\n      <ProList\n        ghost\n        loading={loading || !reaction?.reactants?.length}\n        className={styles.retroReactionTabRoot}\n        pagination={false}\n        rowClassName={styles.retroReactionRowRoot}\n        rowSelection={\n          onSelect\n            ? {\n                onChange: (indexs) => {\n                  let selected = indexs.map((i) => ({\n                    rxn: retroReactions[i as number],\n                    name: getNameOfReaction(i as number)\n                  }))\n                  if (indexs.length === 0 && selectAllWhenEmpty) {\n                    selected = retroReactions.map((r, i) => ({\n                      rxn: r,\n                      name: getNameOfReaction(i)\n                    }))\n                  }\n                  onSelect(selected)\n                }\n              }\n            : undefined\n        }\n        metas={{\n          content: {\n            render: (_, entity, index) => {\n              const active =\n                !!fullReaction &&\n                getRxnFromReaction(entity, true) ===\n                  getRxnFromReaction(fullReaction, true)\n              return (\n                <div\n                  className={classNames(styles?.retroReactionRoot, {\n                    [styles.selectable]: !!onSelect\n                  })}\n                >\n                  {retroProcessId && (\n                    <Typography.Text\n                      type=\"secondary\"\n                      className={styles?.sourceWrapper}\n                    >\n                      {isCurrentProcess(entity, retroProcessId)\n                        ? getWord('from-this-search')\n                        : getWord('from-other-search')}\n                    </Typography.Text>\n                  )}\n                  <div className={styles?.actionsWrapper}>\n                    <Space className={styles?.leftActionWrapper}>\n                      <div>\n                        {getWord('reaction')}\n                        {index + 1}\n                      </div>\n                      {typeof entity?.reliability_score === 'number' ? (\n                        <span>\n                          {getWord('reaction-reliability')}：\n                          {round(entity.reliability_score, 2)}\n                        </span>\n                      ) : (\n                        ''\n                      )}\n                      {entity?.is_dangerous && (\n                        <Tag color=\"error\" className={styles.riskTag}>\n                          {getWord('danger-reaction')}\n                        </Tag>\n                      )}\n                      {entity.is_known_reaction && (\n                        <Tag color=\"purple\">{getWord('reported')}</Tag>\n                      )}\n                      {entity.is_selective_risk && (\n                        <Tag color=\"error\">{getWord('regioselectivity')}</Tag>\n                      )}\n                    </Space>\n                    <Space\n                      className={styles?.rightActionWrapper}\n                      onClick={(e) => e.stopPropagation()}\n                    >\n                      {access?.authCodeList?.includes(\n                        'view-by-backbone.tab.recommend.comment'\n                      ) ? (\n                        <ButtonWithLoading\n                          className={styles?.commendButton}\n                          type=\"link\"\n                          onClick={() =>\n                            getProfileInfo({\n                              _commendSuject: {\n                                id: entity?.id\n                              },\n                              collection_class: 'retro-reaction',\n                              reaction_step_no: reactionStepNo\n                            })\n                          }\n                          size=\"small\"\n                        >\n                          {entity?.content_count && entity?.content_count > 0\n                            ? `${getWord('comment')}（${\n                                entity?.content_count\n                              }）`\n                            : getWord('comment')}\n                        </ButtonWithLoading>\n                      ) : (\n                        ''\n                      )}\n                      {access?.authCodeList?.includes(\n                        'view-by-backbone.tab.recommend.add-to-route'\n                      ) && onSelectProcedure ? (\n                        <ButtonWithLoading\n                          size=\"small\"\n                          type=\"link\"\n                          disabled={active}\n                          className={styles?.addToRouteButton}\n                          onClick={() =>\n                            onSelectProcedure(getRxnFromReaction(entity))\n                          }\n                        >\n                          {getWord(active ? 'added-to-route' : 'add-to-route')}\n                        </ButtonWithLoading>\n                      ) : null}\n                      <div\n                        className={cs(\n                          styles.viwePrice,\n                          'flex-align-items-center'\n                        )}\n                      >\n                        {entity?.material_warning && (\n                          <Popover content={getWord('materials-not-available')}>\n                            <WarningIcon\n                              width={18}\n                              className={styles.warningIcon}\n                            />\n                          </Popover>\n                        )}\n                        {access?.authCodeList?.includes(\n                          'view-by-backbone.tab.recommend.view-materials'\n                        ) ? (\n                          <ButtonWithLoading\n                            onClick={async () => {\n                              const { data, error } = await query<MaterialItem>(\n                                'material-items/search',\n                                {\n                                  method: 'POST',\n                                  data: {\n                                    data: {\n                                      reactants: entity?.reactants\n                                    }\n                                  }\n                                }\n                              )\n                                .populateWith('material_lib', [\n                                  'name',\n                                  'version',\n                                  'description'\n                                ])\n                                .populateWith('material_tags', ['name'])\n                                .get()\n                              if (error?.message)\n                                return message.error(error?.message)\n                              const { missed, found } =\n                                data as unknown as MaterialItemSearchResponse\n                              const result = [\n                                ...found,\n                                ...missed.map((m) => ({\n                                  canonical_smiles: m\n                                }))\n                              ]\n                              // Note 原料表一样的分子放在一起展示\n                              result.sort((a, b) =>\n                                a.canonical_smiles.localeCompare(\n                                  b.canonical_smiles\n                                )\n                              )\n                              setMaterialsPrice(result)\n                              if (!isEmpty(data)) setIsModalOpen(true)\n                            }}\n                            size=\"small\"\n                            type=\"link\"\n                          >\n                            {getWord('view-materials')}\n                          </ButtonWithLoading>\n                        ) : (\n                          ''\n                        )}\n                      </div>\n                    </Space>\n                  </div>\n                  <div className={styles?.compoundWrapper}>\n                    <MoleculeStructure\n                      className={styles?.smiles}\n                      structure={getRxnFromReaction(entity)}\n                    />\n                  </div>\n                  <div onClick={(e) => e.stopPropagation()}>\n                    <Launcher\n                      commendType=\"reaction\"\n                      onMessageWasSent={sendMessage}\n                      hiddenLauncher={showLauncher}\n                      isOpen={isOpen}\n                    />\n                  </div>\n                </div>\n              )\n            }\n          }\n        }}\n        grid={{ column: 1 }}\n        dataSource={\n          retroProcessId\n            ? retroReactions.sort((a, b) => {\n                const ac = isCurrentProcess(a, retroProcessId)\n                const bc = isCurrentProcess(b, retroProcessId)\n                if (ac === bc) {\n                  if (a.updatedAt === b.updatedAt) return a.id - b.id\n                  return (a.updatedAt || '') >= (b.updatedAt || '') ? -1 : 1\n                }\n                return ac ? -1 : 1\n              })\n            : retroReactions\n        }\n      />\n    </>\n  )\n}\n\nexport default RetroReactionTab\n\nexport const getRetroReactionTabConfig = (\n  reaction?: Reaction,\n  onSelectProcedure?: (p?: string) => void\n) => ({\n  key: 'retro-reaction',\n  label: (\n    <TabWithNumber\n      title={getWord('generated-reaction')}\n      getNumber={async () => {\n        if (!reaction || !reaction.reactants.length) return\n        const { meta, error } = await queryWithDefaultOrder<RetroReaction>(\n          'retro-reactions/list',\n          { method: 'post', data: { filters: reaction } },\n          ['id']\n        )\n          .paginate(1, 1)\n          .get()\n        if (error || !meta?.pagination) return undefined\n        return meta.pagination.total\n      }}\n    />\n  ),\n  children: reaction ? (\n    <RetroReactionTab\n      reaction={reaction}\n      onSelectProcedure={onSelectProcedure}\n    />\n  ) : null\n})\n", "import { LoadingOutlined } from '@ant-design/icons'\nimport { isNil } from 'lodash'\nimport React, { useEffect, useState } from 'react'\n\nexport interface TabWithNumberProps {\n  title: string\n  getNumber?: () => Promise<number | void> | number | void\n  refetchEvent?: Record<never, never>\n}\n\nconst TabWithNumber: React.FC<TabWithNumberProps> = ({\n  title,\n  getNumber,\n  refetchEvent\n}) => {\n  const [loading, setLoading] = useState<boolean>(false)\n  const [number, setNumber] = useState<number | void>()\n  useEffect(() => {\n    let unmount = false\n    if (!getNumber) return\n    Promise.resolve(getNumber())\n      .then((n) => !unmount && setNumber(isNil(n) ? undefined : n))\n      .finally(() => !unmount && setLoading(false))\n\n    return () => {\n      unmount = true\n    }\n  }, [getNumber, refetchEvent])\n\n  return (\n    <>\n      {title}\n      {loading ? <LoadingOutlined /> : isNil(number) ? '' : `(${number})`}\n    </>\n  )\n}\n\nexport default TabWithNumber\n", "import { ReactionRole } from '@/services/brain'\n\nexport const updateRoleMap = (\n  map: Record<string, ReactionRole>,\n  reactants: string[]\n): Record<string, ReactionRole> => {\n  const newMap = Object.entries(map).reduce<Record<string, ReactionRole>>(\n    (acc, [k, v]) => {\n      if (v === 'main_reactant' && !reactants.includes(k)) acc[k] = 'reactant'\n      else acc[k] = v\n      return acc\n    },\n    {}\n  )\n  if (!Object.values(newMap).includes('main_reactant')) {\n    newMap[reactants[0]] = 'main_reactant'\n  }\n  return newMap\n}\n", "import { MaterialTable, Procedure, query } from '@/services/brain'\nimport { ProcedureToTextResponse } from '@/services/brain/types/procedureToText'\n\nexport const addOtherToTable = async (\n  material_table: MaterialTable[],\n  propProcedure: Procedure\n): Promise<MaterialTable[]> => {\n  const procedureText: ProcedureToTextResponse = (await query(\n    'procedure_parse/from-text',\n    {\n      method: 'post',\n      data: {\n        procedure: propProcedure.procedure,\n        rxn: propProcedure.reference_smiles,\n        patent_id: propProcedure.origin.reference.no,\n        title: propProcedure.origin.reference.title,\n        rxn_id: propProcedure.id || -1\n      },\n      normalizeData: false\n    }\n  )\n    .get()\n    .catch()) as unknown as ProcedureToTextResponse\n\n  if (!procedureText || !procedureText.entities) {\n    console.log(`No procedureText, skip add others to material table`)\n    return material_table\n  }\n\n  const allOthers = procedureText.entities.filter((e) => e.role === 'other')\n\n  if (!allOthers.length) {\n    return material_table\n  }\n\n  const newMaterial: MaterialTable[] = []\n\n  allOthers.forEach((n) => {\n    const s = n.canonicalized_smiles || n.normalized_smiles\n    if (!s) {\n      console.warn(`not smiles for ${n.name}, skip add to material table`)\n      return\n    }\n    const smiles = s.split('.').sort().join('.')\n\n    newMaterial.push({\n      smiles,\n      role: 'other_reagent',\n      no: '',\n      equivalent: 1,\n      unit: 'eq'\n    })\n  })\n\n  return [...material_table, ...newMaterial]\n}\n", "import SmilesInput from '@/components/SmilesInput'\nimport { reactionUnitOptions } from '@/constants'\nimport { useBrainFetch } from '@/hooks/useBrainFetch'\nimport useOptions from '@/hooks/useOptions'\nimport { getRxnFromMaterialTable } from '@/pages/route/util'\nimport { apiCreateExperimentDesigns } from '@/services'\nimport {\n  MaterialTable,\n  Procedure,\n  ProcedureRoleResponse,\n  ProjectReaction,\n  query,\n  service\n} from '@/services/brain'\nimport { getWord } from '@/utils'\nimport { updateRoleMap } from '@/utils/reactionRoles'\nimport {\n  ModalForm,\n  ProForm,\n  ProFormDigit,\n  ProFormGroup,\n  ProFormList,\n  ProFormSelect,\n  ProFormText,\n  ProFormTextArea\n} from '@ant-design/pro-components'\nimport { useModel } from '@umijs/max'\nimport { App, Button, Form } from 'antd'\nimport React, { useEffect, useState } from 'react'\nimport {\n  formatProcedureForCreate,\n  getReactionFromRxn,\n  updateMaterialTable\n} from '../util'\nimport './index.less'\nimport { addOtherToTable } from './util'\n\nexport interface MyReactionDialogProps {\n  projectReaction: ProjectReaction\n  procedure?: Procedure\n  mode?: 'create' | 'edit' | 'reference'\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n  onCreated?: (projectReaction: ProjectReaction) => void\n  projectId?: number\n}\n\nconst getProjectReaction = async (rxn: string, projectId: number) => {\n  const data = await query<ProjectReaction>('project-reaction/get-or-create', {\n    method: 'post',\n    data: { reaction: rxn, projectId },\n    normalizeData: false\n  }).get()\n\n  return (data as unknown as ProjectReaction[])?.[0]\n}\n\nconst MyReactionDialog: React.FC<MyReactionDialogProps> = ({\n  projectReaction,\n  procedure: propProcedure,\n  open,\n  mode,\n  onOpenChange,\n  onCreated,\n  projectId\n}) => {\n  const { reactionRoleOptions } = useOptions()\n  const [procedure, setProcedure] = useState<Procedure | undefined>(\n    propProcedure\n  )\n  const { notification } = App.useApp()\n\n  const [form] = Form.useForm<Procedure>()\n  const { fetch: fetchRoles } = useBrainFetch(undefined, false)\n  const { fetch: createProcess } = useBrainFetch(undefined, false)\n  const { reactants, product } = getReactionFromRxn(projectReaction.reaction)\n  const { initialState } = useModel('@@initialState')\n  const [loading, setLoading] = useState<boolean>(false)\n\n  const createProcedure = async (procedure: Procedure) => {\n    const formatedProcedure = formatProcedureForCreate(\n      procedure,\n      `${initialState?.userInfo?.id || -1}`\n    )\n    const { data } = await createProcess(\n      query<number>('procedure/create', {\n        normalizeData: false,\n        method: 'post',\n        data: { procedure: formatedProcedure, searchable: false }\n      }).get()\n    )\n    if (!data) return\n\n    let newEffective = new Set<number>(projectReaction.effective_procedures)\n    let newHistory = new Set<number>(projectReaction.history_procedures)\n    if (projectId) {\n      const latest = await getProjectReaction(\n        projectReaction.reaction,\n        projectId\n      )\n      newEffective = new Set(latest.effective_procedures)\n      newHistory = new Set(latest.history_procedures)\n    }\n    newEffective.add(data as unknown as number)\n    const oldProcedureId = propProcedure?.id\n    if (oldProcedureId) {\n      newEffective.delete(oldProcedureId)\n      newHistory.add(oldProcedureId)\n    }\n\n    const { data: newProjectReaction } = await service<ProjectReaction>(\n      'project-reactions'\n    ).update(projectReaction.id, {\n      effective_procedures: Array.from(newEffective.values()),\n      history_procedures: Array.from(newHistory.values())\n    })\n\n    const material_mapping = (\n      (await query('procedure/material_replacement', {\n        method: 'post',\n        data: {\n          rxn: getRxnFromMaterialTable(procedure.material_table),\n          reference: propProcedure?.reference_smiles,\n          procedure: procedure.procedure\n        },\n        normalizeData: false\n      })\n        .get()\n        .catch()) as unknown as { ReactantMap: any }\n    )?.ReactantMap\n\n    try {\n      await apiCreateExperimentDesigns({\n        data: {\n          rxn_no: data,\n          rxn: formatedProcedure.smiles,\n          project_no: projectReaction.project?.id,\n          reference_text: formatedProcedure.procedure,\n          material_mapping: material_mapping || {},\n          materials: formatedProcedure.material_table\n        }\n      })\n    } catch (e) {\n      console.error(`create experiment design failed with ${e}`)\n    }\n    if (newProjectReaction) {\n      onCreated?.(newProjectReaction)\n    }\n  }\n\n  const updateRxn = async (rxn: string, addCatalyst: boolean = false) => {\n    const { data } = await fetchRoles(\n      query<ProcedureRoleResponse>('procedure/role', {\n        normalizeData: false,\n        method: 'post',\n        data: { reaction_smiles: rxn }\n      }).get()\n    )\n\n    if (!data) return\n    const roleMap = updateRoleMap(\n      (data as unknown as ProcedureRoleResponse).role,\n      getReactionFromRxn(projectReaction.reaction).reactants\n    )\n    if (roleMap[product] !== 'product') {\n      notification.error({\n        message: getWord('pages.reaction.label.warn.productDiff')\n      })\n      return\n    }\n    if (reactants.some((v) => !roleMap[v])) {\n      notification.error({\n        message: getWord('pages.reaction.label.warn.materialDiff')\n      })\n      return\n    }\n\n    const material_table = updateMaterialTable(\n      rxn,\n      roleMap,\n      procedure?.material_table\n    )\n\n    if (!addCatalyst || !propProcedure?.procedure) {\n      setProcedure((pre) =>\n        pre\n          ? { ...pre, smiles: rxn, material_table, temp_smiles: '' }\n          : undefined\n      )\n      return\n    }\n\n    const addedTable = await addOtherToTable(material_table, propProcedure)\n\n    setProcedure((pre) =>\n      pre\n        ? { ...pre, smiles: rxn, material_table: addedTable, temp_smiles: '' }\n        : undefined\n    )\n  }\n\n  useEffect(() => {\n    if (procedure) {\n      if (!procedure?.material_table?.length && procedure.smiles) {\n        setLoading(true)\n        updateRxn(procedure.smiles, true).finally(() => setLoading(false))\n      } else {\n        form.setFieldsValue(procedure)\n      }\n    }\n  }, [procedure])\n\n  useEffect(() => {\n    setProcedure(propProcedure)\n  }, [propProcedure])\n\n  return (\n    <ModalForm<Procedure>\n      className=\"reaction-dialog-root\"\n      title={getWord(`pages.reaction.label.${mode}Reaction`)}\n      form={form}\n      width={1000}\n      autoFocusFirstInput\n      open={open}\n      disabled={loading}\n      onOpenChange={onOpenChange}\n      modalProps={{ destroyOnClose: true, centered: true }}\n      submitTimeout={2000}\n      onFinish={async (values) => {\n        await createProcedure(values)\n        return true\n      }}\n    >\n      <ProFormDigit\n        width=\"md\"\n        name=\"yields\"\n        label={getWord('yield')}\n        min={0}\n        max={100}\n        fieldProps={{ precision: 0, addonAfter: '%' }}\n      />\n      <ProFormText\n        name=\"temp_smiles\"\n        label={getWord('pages.reaction.label.createMaterialTableFromRxn')}\n        fieldProps={{ style: { width: 900 } }}\n        addonAfter={\n          <>\n            <Button\n              type=\"link\"\n              onClick={() => {\n                updateRxn(form.getFieldValue('temp_smiles'))\n              }}\n            >\n              {getWord('pages.route.edit.label.confirm')}\n            </Button>\n          </>\n        }\n      />\n      <ProFormList\n        name=\"material_table\"\n        label={getWord('material-sheet')}\n        creatorButtonProps={{\n          creatorButtonText: getWord('add-raw-materials')\n        }}\n        copyIconProps={false}\n        actionRender={(field, _, defaultActionDom) => {\n          const item = form.getFieldValue('material_table')[\n            field.name\n          ] as MaterialTable\n          if (item.smiles === product || reactants.includes(item.smiles)) {\n            return []\n          }\n          return defaultActionDom\n        }}\n      >\n        {(field) => {\n          const item = form.getFieldValue('material_table')[\n            field.name\n          ] as MaterialTable\n          const disabled =\n            item.smiles === product || reactants.includes(item.smiles)\n          return (\n            <ProFormGroup key=\"group\">\n              <ProFormSelect\n                disabled={disabled}\n                name=\"role\"\n                width={150}\n                label={getWord('role')}\n                options={reactionRoleOptions.filter(\n                  (r) => r.value !== 'product'\n                )}\n                required\n                rules={[{ required: true }]}\n              />\n              <ProForm.Item\n                className=\"filter-form-root\"\n                name={'smiles'}\n                label={getWord('structural')}\n                required\n                rules={[{ required: true }]}\n              >\n                <SmilesInput disabled={disabled} multiple={false} />\n              </ProForm.Item>\n              <ProFormDigit\n                name=\"equivalent\"\n                label={getWord('EWR')}\n                required\n                rules={[\n                  { required: true },\n                  {\n                    pattern: /^(?!0*(\\.0{1,2})?$)\\d+(\\.\\d{1,2})?$/,\n                    message: getWord('enter-two-decimal')\n                  }\n                ]}\n              />\n              <ProFormSelect\n                name=\"unit\"\n                label={getWord('unit')}\n                options={reactionUnitOptions}\n                required\n                rules={[{ required: true }]}\n              />\n            </ProFormGroup>\n          )\n        }}\n      </ProFormList>\n      <ProFormTextArea\n        disabled={false}\n        name=\"procedure\"\n        label=\"Procedure\"\n        required={false}\n        fieldProps={{\n          autoSize: { minRows: 5, maxRows: 8 }\n        }}\n      />\n    </ModalForm>\n  )\n}\n\nexport default MyReactionDialog\n", "\"use strict\";\n\"use client\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _warning = require(\"../_util/warning\");\nvar _Base = _interopRequireDefault(require(\"./Base\"));\nvar __rest = void 0 && (void 0).__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nconst Link = /*#__PURE__*/React.forwardRef((_a, ref) => {\n  var {\n      ellipsis,\n      rel\n    } = _a,\n    restProps = __rest(_a, [\"ellipsis\", \"rel\"]);\n  if (process.env.NODE_ENV !== 'production') {\n    const warning = (0, _warning.devUseWarning)('Typography.Link');\n    process.env.NODE_ENV !== \"production\" ? warning(typeof ellipsis !== 'object', 'usage', '`ellipsis` only supports boolean value.') : void 0;\n  }\n  const mergedProps = Object.assign(Object.assign({}, restProps), {\n    rel: rel === undefined && restProps.target === '_blank' ? 'noopener noreferrer' : rel\n  });\n  // @ts-expect-error: https://github.com/ant-design/ant-design/issues/26622\n  delete mergedProps.navigate;\n  return /*#__PURE__*/React.createElement(_Base.default, Object.assign({}, mergedProps, {\n    ref: ref,\n    ellipsis: !!ellipsis,\n    component: \"a\"\n  }));\n});\nvar _default = exports.default = Link;"], "names": ["FilterOutlined", "props", "ref", "RefIcon", "LoadingOutlined", "_excluded", "ProFormSwitch", "_ref", "fieldProps", "unChecked<PERSON><PERSON><PERSON>n", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proFieldProps", "rest", "Line", "padding", "MediaQueryKeyEnum", "StatisticSkeleton", "_ref2", "size", "active", "defaultCol", "col", "useBreakpoint", "colSize", "key", "arraySize", "firstWidth", "index", "_", "ListSkeletonItem", "_ref3", "ListSkeleton", "_ref4", "_ref4$active", "actionButton", "PageHeaderSkeleton", "_ref5", "ListToolbarSkeleton", "_ref6", "ListPageSkeleton", "_ref7", "_ref7$active", "statistic", "toolbar", "pageHeader", "_ref7$list", "list", "DescriptionsLargeItemSkeleton", "DescriptionsItemSkeleton", "TableItemSkeleton", "_ref3$header", "header", "TableSkeleton", "_ref4$size", "DescriptionsSkeleton", "DescriptionsPageSkeleton", "_ref6$active", "ResultPageSkeleton", "_ref$active", "ProSkeleton", "_ref$type", "type", "Result", "Descriptions", "List", "ButtonWithLoading", "onClick", "_objectWithoutProperties", "_useState", "useState", "_useState2", "_slicedToArray", "loading", "setLoading", "onClickHandler", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "event", "result", "wrap", "_context", "prev", "next", "sent", "abrupt", "t0", "stop", "_x", "apply", "arguments", "_jsx", "<PERSON><PERSON>", "_objectSpread", "MoleculeStructure", "lazy", "then", "module", "LazySmileDrawer", "Suspense", "fallback", "children", "Skeleton", "AiProcedureCard", "_access$authCodeList", "procedure", "name", "isSame", "onReference", "access", "useAccess", "expanded", "setExpanded", "text", "reference", "rxn", "similarity", "experimentalProcedure", "yieldString", "link", "title", "authors", "date", "no", "assignees", "reference_text", "expandText", "Text", "style", "color", "cursor", "e", "stopPropagation", "pre", "_jsxs", "Card", "className", "getWord", "paddingLeft", "Space", "scalable", "_Fragment", "Tag", "undefined", "getTagForSameOrSimilerType", "authCodeList", "includes", "icon", "PlusOutlined", "structure", "Title", "level", "Paragraph", "ellipsis", "rows", "expandable", "symbol", "copyable", "getTagForReferenceType", "textAlign", "display", "width", "Link", "href", "italic", "valueType", "DateYearRangePicker", "context", "FieldContext", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ProFormSlider", "min", "max", "step", "marks", "vertical", "range", "maxImpactFactor", "Fields", "styles", "fieldsWrapper", "ProFormDateYearRangePicker", "label", "allowEmpty", "placeholder", "Object", "fromEntries", "_toConsumableArray", "Array", "Math", "floor", "map", "i", "concat", "slider", "frontToBack", "values", "_yearRange$", "_yearRange$2", "year<PERSON><PERSON><PERSON>", "impactFactor", "validProcedure", "same", "similar", "year_min", "year", "year_max", "impact_factor_min", "impact_factor_max", "has_valid_procedure", "need_scalable", "backToFront", "y", "dayjs", "useFilterForm", "init", "_Form$useForm", "Form", "useForm", "_Form$useForm2", "form", "latestInit", "setLatestInit", "useEffect", "latest", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleFinish", "onFinished", "getFieldsValue", "reset", "touched", "isFieldsTouched", "__defProp", "__getOwnPropSymbols", "__hasOwnProp", "__propIsEnum", "__defNormalProp", "obj", "__spreadValues", "a", "b", "prop", "SvgNodata", "FilterModal", "initialFilter", "_useFilterForm", "ModalForm", "trigger", "onFinish", "modalProps", "onCancel", "defaultTotal", "fetchInfo", "reaction", "searchRxns", "filters", "_yield$fetchAllRetroR", "rxns", "simple_rxn", "res", "similar_procedures", "same_procedures", "reactants", "length", "fetchAllRetroReactions", "t2", "t1", "t3", "r", "getRxnFromReaction", "service", "method", "normalizeData", "data", "similar_topk", "select", "get", "p", "transformProcedureMatchResponse", "Error", "_x2", "_x3", "useProcedure", "_useQuery", "useQuery", "query<PERSON><PERSON>", "queryFn", "enabled", "error", "isLoading", "refetch", "procedures", "filter", "total", "defaultRender", "_rxns$find", "find", "query", "ReactionLibTab", "actionSlot", "_ref$renderer", "renderer", "_useUserSettingQuery", "useUserSettingQuery", "_useUserSettingQuery$", "setting", "_useUserSettingQuery$2", "_useUserSettingQuery$3", "filtersUpdate", "setFiltersUpdate", "_useProcedure", "_useState3", "page", "pageSize", "_useState4", "paginate", "setPaginate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "renderEmpty", "StatusTip", "image", "EmptyImage", "des", "ProList", "ghost", "pagination", "current", "simple", "onChange", "renderItem", "item", "grid", "column", "dataSource", "ReactDOM", "transformReferenceType", "id", "_ref$experimental_pro", "experimental_procedure", "_ref$query", "_ref$procedure", "_ref$similarity", "_ref$rxn", "_ref$rxn_yields", "rxn_yields", "yields", "_ref$transformation", "transformation", "_ref$reference_type", "reference_type", "_ref$title", "_ref$authors", "_ref$date", "_ref$assignees", "_ref$reference_text", "_ref$patent_id", "patent_id", "_ref$is_scalable", "is_scalable", "yieldNumber", "isEN", "SvgWarning", "MaterialPriceTable", "_useModel", "useModel", "materialCodeOptions", "getThe<PERSON><PERSON><PERSON>", "newCode", "targetTag", "keys", "curOption", "getTags", "codes", "tags", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "code", "tag", "push", "err", "f", "isEmpty", "join", "co<PERSON><PERSON><PERSON>", "dataIndex", "render", "smiles", "marginTop", "record", "toFixed", "unified_unit", "_text", "isValidArray", "unit_range", "pubchem_safety_link", "source_link", "window", "open", "_record$material_lib", "material_lib", "version", "Table", "columns", "materialsPrice", "getNameOfReaction", "isCurrentProcess", "processId", "_reaction$retro_proce", "retro_processes", "findIndex", "RetroReactionTab", "_reaction$reactants2", "projectId", "onSelectProcedure", "onSelect", "selectAllWhenEmpty", "retroProcessId", "fullReactionConfig", "getProfileInfo", "getCommonExpression", "showLauncher", "sendMessage", "reload", "finishedReload", "reactionStepNo", "isOpen", "_useModel2", "getMaterialCodeOptions", "retroReactions", "setRetroReactions", "_useBrainFetch", "useBrainFetch", "fetch", "isModalOpen", "setIsModalOpen", "_useState5", "_useState6", "setMaterialsPrice", "fetchReaction", "_reaction$reactants", "_yield$fetch", "queryWithDefaultOrder", "populateWith", "sortBy", "field", "order", "fullReaction", "useDeepCompareEffect", "Modal", "materialsPriceTable", "footer", "centered", "retroReactionTabRoot", "rowClassName", "retroReactionRowRoot", "rowSelection", "indexs", "selected", "metas", "content", "entity", "_access$authCodeList2", "_access$authCodeList3", "classNames", "retroReactionRoot", "_defineProperty", "selectable", "Typography", "sourceWrapper", "actionsWrapper", "leftActionWrapper", "reliability_score", "round", "is_dangerous", "riskTag", "is_known_reaction", "is_selective_risk", "rightActionWrapper", "commend<PERSON><PERSON><PERSON>", "_commendSuject", "collection_class", "reaction_step_no", "content_count", "disabled", "addToRouteButton", "cs", "viwePrice", "material_warning", "Popover", "WarningIcon", "warningIcon", "_callee2", "_yield$query$populate", "missed", "found", "_context2", "message", "m", "canonical_smiles", "sort", "localeCompare", "compoundWrapper", "Launcher", "commendType", "onMessageWasSent", "hiddenLauncher", "ac", "bc", "updatedAt", "getRetroReactionTabConfig", "TabWithNumber", "getNumber", "_callee3", "_yield$queryWithDefau", "meta", "_context3", "refetchEvent", "number", "setNumber", "unmount", "Promise", "resolve", "isNil", "updateRoleMap", "newMap", "entries", "reduce", "acc", "k", "v", "addOtherToTable", "material_table", "propProcedure", "procedureText", "allOthers", "newMaterial", "reference_smiles", "origin", "rxn_id", "entities", "console", "log", "role", "for<PERSON>ach", "canonicalized_smiles", "normalized_smiles", "warn", "split", "equivalent", "unit", "getProjectReaction", "MyReactionDialog", "projectReaction", "mode", "onOpenChange", "onCreated", "_useOptions", "useOptions", "reactionRoleOptions", "setProcedure", "_App$useApp", "App", "useApp", "notification", "fetchRoles", "_useBrainFetch2", "createProcess", "_getReactionFromRxn", "getReactionFromRxn", "product", "initialState", "createProcedure", "_initialState$userInf", "_yield$query$get$catc", "formatedProcedure", "_yield$createProcess", "newEffective", "newHistory", "oldProcedureId", "_yield$service$update", "newProjectReaction", "material_mapping", "_projectReaction$proj", "formatProcedureForCreate", "userInfo", "searchable", "Set", "effective_procedures", "history_procedures", "add", "update", "from", "getRxnFromMaterialTable", "ReactantMap", "apiCreateExperimentDesigns", "rxn_no", "project_no", "project", "materials", "updateRxn", "addCatalyst", "_yield$fetchRoles", "roleMap", "addedTable", "_args3", "reaction_smiles", "some", "updateMaterialTable", "temp_smiles", "_x4", "_procedure$material_t", "autoFocusFirstInput", "destroyOnClose", "submitTimeout", "_callee4", "_context4", "_x5", "ProFormDigit", "precision", "addonAfter", "ProFormText", "getFieldValue", "ProFormList", "creatorButtonProps", "creatorButtonText", "copyIconProps", "actionRender", "defaultActionDom", "ProFormGroup", "ProFormSelect", "options", "required", "rules", "ProForm", "<PERSON><PERSON>", "SmilesInput", "multiple", "pattern", "reactionUnitOptions", "ProFormTextArea", "autoSize", "minRows", "maxRows", "_interopRequireDefault", "_interopRequireWildcard", "exports", "React", "_warning", "_Base", "__rest", "t", "_a", "rel", "restProps", "mergedProps", "_default"], "sourceRoot": ""}