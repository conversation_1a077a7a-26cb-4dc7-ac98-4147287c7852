{"version": 3, "file": "p__workspace__my-experiment__index.2b5459c9.async.js", "mappings": "gKAMIA,EAAkB,SAAyBC,EAAOC,EAAK,CACzD,OAAoB,gBAAoB,OAAU,QAAc,KAAc,CAAC,EAAGD,CAAK,EAAG,CAAC,EAAG,CAC5F,IAAKC,EACL,KAAM,GACR,CAAC,CAAC,CACJ,EACIC,EAAuB,aAAiBH,CAAe,EAI3D,IAAeG,C,iHCbTC,KAAoBC,EAAAA,MAAK,kBAC7B,mEAAyCC,KAAK,SAACC,EAAQ,CAAF,MAAM,CACzD,QAASA,EAAM,OACjB,CAAC,CAAC,CAAC,CACL,EAEe,SAASC,EAAgBP,EAA+B,CACrE,SACEQ,EAAAA,KAACC,EAAAA,SAAQ,CACPC,YACEF,EAAAA,KAAA,OAAAG,YACEH,EAAAA,KAACI,EAAAA,EAAQ,CAACC,OAAM,GAAE,CAAC,CAChB,EACNF,YAEDH,EAAAA,KAACL,EAAiBW,EAAAA,EAAA,GAAKd,CAAK,CAAG,CAAC,CACxB,CAEd,C,oGCXMe,EAA8C,SAAHC,EAI3C,KAHJC,EAAKD,EAALC,MACAC,EAASF,EAATE,UACAC,EAAYH,EAAZG,aAEAC,KAA8BC,EAAAA,UAAkB,EAAK,EAACC,EAAAC,EAAAA,EAAAH,EAAA,GAA/CI,EAAOF,EAAA,GAAEG,EAAUH,EAAA,GAC1BI,KAA4BL,EAAAA,UAAwB,EAACM,EAAAJ,EAAAA,EAAAG,EAAA,GAA9CE,EAAMD,EAAA,GAAEE,EAASF,EAAA,GACxBG,SAAAA,EAAAA,WAAU,UAAM,CACd,IAAIC,EAAU,GACd,GAAKb,EACLc,eAAQC,QAAQf,EAAU,CAAC,EACxBb,KAAK,SAAC6B,EAAG,CAAF,MAAK,CAACH,GAAWF,KAAUM,EAAAA,OAAMD,CAAC,EAAIE,OAAYF,CAAC,CAAC,GAAC,QACpD,iBAAM,CAACH,GAAWN,EAAW,EAAK,CAAC,GAEvC,UAAM,CACXM,EAAU,EACZ,CACF,EAAG,CAACb,EAAWC,CAAY,CAAC,KAG1BkB,EAAAA,MAAAC,EAAAA,SAAA,CAAA3B,SAAA,CACGM,EACAO,KAAUhB,EAAAA,KAACT,EAAAA,EAAe,EAAE,KAAIoC,EAAAA,OAAMP,CAAM,EAAI,GAAK,IAAHW,OAAOX,EAAM,IAAG,EACnE,CAEN,EAEA,IAAeb,C,0FChCTyB,EAA6B,UAAM,CACvC,IAAAC,KACEC,EAAAA,UAAS,gBAAgB,EAACC,EAAAF,EADpBG,aAAYC,EAAAF,IAAA,OAA6B,CAAC,EAACA,EAAAG,EAAAD,EAA3BE,SAAAA,EAAQD,IAAA,OAAGV,OAASU,EAEtCE,EAASD,GAAQ,YAARA,EAAUE,GACzB,OAAKD,KAEHxC,EAAAA,KAAC0C,EAAAA,GAAa,CAAAvC,YACZH,EAAAA,KAAC2C,EAAAA,EAAiB,CAACC,QAASJ,CAAO,CAAE,CAAC,CACzB,EAJG,IAMtB,EAEA,UAAeR,C", "sources": ["webpack://labwise-web/./node_modules/@ant-design/icons/es/icons/LoadingOutlined.js", "webpack://labwise-web/./src/components/LazySmileDrawer/index.tsx", "webpack://labwise-web/./src/components/ReactionTabs/TabWithNumber.tsx", "webpack://labwise-web/./src/pages/workspace/my-experiment/index.tsx"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport LoadingOutlinedSvg from \"@ant-design/icons-svg/es/asn/LoadingOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar LoadingOutlined = function LoadingOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: LoadingOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(LoadingOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'LoadingOutlined';\n}\nexport default RefIcon;", "import { Skeleton } from 'antd'\nimport { Suspense, lazy } from 'react'\nimport { MoleculeStructureProps } from '../MoleculeStructure'\nconst MoleculeStructure = lazy(() =>\n  import('@/components/MoleculeStructure').then((module) => ({\n    default: module.default\n  }))\n)\n\nexport default function LazySmileDrawer(props: MoleculeStructureProps) {\n  return (\n    <Suspense\n      fallback={\n        <div>\n          <Skeleton active />\n        </div>\n      }\n    >\n      <MoleculeStructure {...props} />\n    </Suspense>\n  )\n}\n", "import { LoadingOutlined } from '@ant-design/icons'\nimport { isNil } from 'lodash'\nimport React, { useEffect, useState } from 'react'\n\nexport interface TabWithNumberProps {\n  title: string\n  getNumber?: () => Promise<number | void> | number | void\n  refetchEvent?: Record<never, never>\n}\n\nconst TabWithNumber: React.FC<TabWithNumberProps> = ({\n  title,\n  getNumber,\n  refetchEvent\n}) => {\n  const [loading, setLoading] = useState<boolean>(false)\n  const [number, setNumber] = useState<number | void>()\n  useEffect(() => {\n    let unmount = false\n    if (!getNumber) return\n    Promise.resolve(getNumber())\n      .then((n) => !unmount && setNumber(isNil(n) ? undefined : n))\n      .finally(() => !unmount && setLoading(false))\n\n    return () => {\n      unmount = true\n    }\n  }, [getNumber, refetchEvent])\n\n  return (\n    <>\n      {title}\n      {loading ? <LoadingOutlined /> : isNil(number) ? '' : `(${number})`}\n    </>\n  )\n}\n\nexport default TabWithNumber\n", "import ExperimentListTab from '@/components/ReactionTabs/ExperimentListTab'\nimport { PageContainer } from '@ant-design/pro-components'\nimport { useModel } from '@umijs/max'\nimport React from 'react'\n\nconst MyExperimentPage: React.FC = () => {\n  const { initialState: { userInfo = undefined } = {} } =\n    useModel('@@initialState')\n  const userId = userInfo?.id\n  if (!userId) return null\n  return (\n    <PageContainer>\n      <ExperimentListTab ownerId={userId} />\n    </PageContainer>\n  )\n}\n\nexport default MyExperimentPage\n"], "names": ["LoadingOutlined", "props", "ref", "RefIcon", "MoleculeStructure", "lazy", "then", "module", "LazySmileDrawer", "_jsx", "Suspense", "fallback", "children", "Skeleton", "active", "_objectSpread", "TabWithNumber", "_ref", "title", "getNumber", "refetchEvent", "_useState", "useState", "_useState2", "_slicedToArray", "loading", "setLoading", "_useState3", "_useState4", "number", "setNumber", "useEffect", "unmount", "Promise", "resolve", "n", "isNil", "undefined", "_jsxs", "_Fragment", "concat", "MyExperimentPage", "_useModel", "useModel", "_useModel$initialStat", "initialState", "_useModel$initialStat2", "_useModel$initialStat3", "userInfo", "userId", "id", "<PERSON><PERSON><PERSON><PERSON>", "ExperimentListTab", "ownerId"], "sourceRoot": ""}