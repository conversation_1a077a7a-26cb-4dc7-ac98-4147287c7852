.retroReactionRowRoot .riskTag{color:#b41500;background:linear-gradient(0deg,#f7e8e5,#f7e8e5),linear-gradient(0deg,#e8b9b2,#e8b9b2);border:1px solid #e8b9b2}.retroReactionRowRoot :global .ant-pro-card .ant-pro-card-body{padding-top:0!important}.retroReactionRowRoot :global .ant-pro-checkcard-content{display:none}.retroReactionRowRoot .retroReactionRoot{width:100%;cursor:auto}.retroReactionRowRoot .retroReactionRoot.selectable,.retroReactionRowRoot .retroReactionRoot.selectable .smiles{cursor:pointer}.retroReactionRowRoot .retroReactionRoot .actionsWrapper{display:flex;flex-direction:row;padding:0 6px}.retroReactionRowRoot .retroReactionRoot .actionsWrapper .rightActionWrapper{margin-left:auto}.retroReactionRowRoot .retroReactionRoot .sourceWrapper{padding-left:4px;font-size:12px;line-height:14px}.retroReactionTabRoot :global .ant-pro-card-body{padding:8px 4px!important}.materialsPriceTable :global .ant-modal-content{padding:20px 5px 2px!important}.materialsPriceTable :global .ant-modal-content .ant-modal-header{padding-left:15px}.viwePrice{height:24px}.viwePrice .warningIcon{position:relative;top:4px;right:-6px;height:24px}.experimentPlanDetailWrapper___zoALI{position:relative;min-height:calc(100vh - 72px)}.experimentPlanDetailWrapper___zoALI .ant-page-header{background-color:#fff}.experimentPlanDetailWrapper___zoALI .experimentPlanDetail___msMrE{height:228px;margin-top:8px;margin-bottom:20px;overflow:hidden;background-color:#fff}.experimentPlanDetailWrapper___zoALI .experimentPlanDetail___msMrE .experimentTitle___wqDbW{height:36px;padding-left:24px}.experimentPlanDetailWrapper___zoALI .experimentPlanDetail___msMrE .content___UbO_r{padding:8px 24px}.experimentPlanDetailWrapper___zoALI .experimentPlanDetail___msMrE .experimentStructure___Qtlmj{width:auto;height:auto!important;max-height:136px!important;margin-top:18px;margin-right:10px;overflow:hidden;border:1px solid #d9d9d9}.experimentPlanDetailWrapper___zoALI .experimentPlanDetail___msMrE .commonTitle___DQpPB{height:22px;color:#191919;font-weight:400;font-size:16px;line-height:22px}.experimentPlanDetailWrapper___zoALI .experimentPlanDetail___msMrE .generateContent___hdk34{display:flex;align-items:center;height:24px}.experimentPlanDetailWrapper___zoALI .experimentPlanDetail___msMrE .generateContent___hdk34 .ant-radio-group{min-width:144px!important}.experimentPlanDetailWrapper___zoALI .experimentPlanDetail___msMrE .procedureText___bazJp{height:146px;margin:8px 10px 0 0;padding:9px 14px;overflow-y:scroll;color:#555;font-weight:400;font-size:14px;line-height:20px;background:#f7f7f7;border-radius:8px}.experimentPlanDetailWrapper___zoALI .foldIcon___QscBp{position:absolute;top:264px;right:calc(50% - 13px);z-index:11;width:26px;height:26px;cursor:pointer;transition:all .5s ease-in-out}.experimentPlanDetailWrapper___zoALI .foldIcon___QscBp:before{position:absolute;left:0;width:100%;height:100%;background:url(./static/fold.25401674.svg) 0 0 no-repeat;transform:rotate(-180deg);transition:all .5s ease-in-out;content:""}.experimentPlanDetailWrapper___zoALI .hide___gxrQW{top:28px;transition:all .5s ease-in-out}.experimentPlanDetailWrapper___zoALI .hide___gxrQW:before{background:url(./static/fold.25401674.svg) 0 0 no-repeat;transform:rotate(-360deg);transition:all .5s ease-in-out}.experimentPlanDetailWrapper___zoALI .hidePanel___EU0ES{height:0px;transition:all .5s ease-in-out}.experimentPlanDetailWrapper___zoALI .showPanel___YXPi_{transition:all .5s ease-in-out}.disabledType___zn1ui svg{fill:#ddd!important}

/*# sourceMappingURL=p__experimental-procedure__detail__index.a840e4b5.chunk.css.map*/