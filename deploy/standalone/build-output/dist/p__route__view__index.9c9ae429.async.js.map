{"version": 3, "file": "p__route__view__index.9c9ae429.async.js", "mappings": "ifAyCMA,GAAsB,SAAHC,GAAW,KAAAC,EAAAC,EAAAC,EAAAC,GAAAA,EAAAJ,EAAA,EAClC,IAAAK,MAAmCC,EAAAA,UAAS,SAAS,EAA7CC,GAAsBF,GAAtBE,uBACRC,MAAgCC,EAAAA,WAA+B,EAACC,EAAAF,GAAxDG,QAASC,GAAKF,IAAA,OAAG,GAAEA,EAC3BG,MAAuBC,EAAAA,iBAAgB,EAACC,GAAAC,EAAAA,EAAAH,GAAA,GAAjCI,GAAYF,GAAA,GACbG,EAAOD,GAAaE,IAAI,MAAM,GAAK,GACnCC,EAAKC,OAAOC,SAASV,EAAK,EAChCW,GAAyBC,GAAAA,EAAIC,OAAO,EAA5BC,EAAYH,GAAZG,aACRC,MAA0CC,EAAAA,UAAsB,EAACC,EAAAb,EAAAA,EAAAW,GAAA,GAA1DG,GAAaD,EAAA,GAAEE,GAAgBF,EAAA,GACtCG,MAA4CJ,EAAAA,UAAgC,EAACK,EAAAjB,EAAAA,EAAAgB,GAAA,GAAtEE,GAAcD,EAAA,GAAEE,GAAiBF,EAAA,GACxCG,MAAgCR,EAAAA,UAA2B,EAACS,EAAArB,EAAAA,EAAAoB,GAAA,GAArDE,EAAQD,EAAA,GAAEE,EAAWF,EAAA,GAC5BG,MAA8BZ,EAAAA,UAA2B,EAACa,EAAAzB,EAAAA,EAAAwB,GAAA,GAAnDE,EAAOD,EAAA,GAAEE,GAAUF,EAAA,GAC1BG,MAAoChB,EAAAA,UAAiB,EAACiB,EAAA7B,EAAAA,EAAA4B,GAAA,GAA/CE,EAAUD,EAAA,GAAEE,GAAaF,EAAA,GAChCG,MAAkCpB,EAAAA,UAAiB,EAACqB,EAAAjC,EAAAA,EAAAgC,GAAA,GAA7CE,EAASD,EAAA,GAAEE,GAAYF,EAAA,GAC9BG,MAAgCxB,EAAAA,UAAkB,EAAK,EAACyB,EAAArC,EAAAA,EAAAoC,GAAA,GAAjDE,GAAQD,EAAA,GAAEE,EAAWF,EAAA,GAC5BG,MAA0B5B,EAAAA,UAAuB,EAAC6B,EAAAzC,EAAAA,EAAAwC,GAAA,GAA3CE,EAAKD,EAAA,GAAEE,GAAQF,EAAA,GACtBG,MAAkBC,GAAAA,GAAc,EAAxBC,GAAKF,GAALE,MAEFC,MAAoBC,GAAAA,GAAkB,SAACC,EAAG,CAAF,OAAKA,EAAEF,iBAAiB,GACtEG,MAAkCtC,EAAAA,UAAwB,CAAC,CAAC,EAACuC,EAAAnD,EAAAA,EAAAkD,GAAA,GAAtDE,GAASD,EAAA,GAAEE,EAAYF,EAAA,GACxBG,KAASC,EAAAA,WAAU,EACzBC,MAAoBC,GAAAA,IAAe,EAA3BC,GAAOF,GAAPE,QACRC,MAA4C/C,EAAAA,UAA8B,CAAC,CAAC,EAACgD,EAAA5D,EAAAA,EAAA2D,GAAA,GAAtEE,GAAcD,EAAA,GAAEE,EAAiBF,EAAA,GACxCG,MACEnD,EAAAA,UAA8B,CAAC,CAAC,EAACoD,EAAAhE,EAAAA,EAAA+D,GAAA,GAD5BE,GAAmBD,EAAA,GAAEE,EAAsBF,EAAA,GAElDG,KAKIC,GAAAA,GAAkB1C,CAAO,EAJ3B2C,EAAgBF,EAAhBE,iBACAC,EAAgBH,EAAhBG,iBACAC,EAAYJ,EAAZI,aACAC,GAAoBL,EAApBK,qBAEIC,MAAYC,EAAAA,IAAapD,CAAQ,EACjCqD,GAAc,SAACC,EAAgD,CACnE,IAAMC,GAASD,GAAI,YAAJA,EAAMxE,KAAMqE,GAAUtE,IAAIyE,EAAKxE,EAAE,EAChD,GAAIyE,EAAQ,CACV,IAAIC,KAAmBC,EAAAA,IAAkBF,EAAO,CAAC,EAAGA,EAAO,CAAC,CAAC,EAC7DtF,OAAAA,GAAuBuF,CAAgB,EAChC,GAAPE,UAAUC,EAAAA,IAAQ,UAAU,EAAC,KAAAD,OAAIF,CAAgB,CACnD,CACA,SAAOG,EAAAA,IAAQ,UAAU,CAC3B,EACAC,KAAqBC,GAAAA,MAAeC,EAAAA,IAAmB9D,CAAQ,EAAGY,CAAS,EAAnEmD,GAAGH,EAAHG,IAAKC,GAAGJ,EAAHI,IAEPC,MAAoBC,EAAAA,IACxBtB,EACAxC,EACA4C,CACF,EAEMmB,GAAiB,eAAAC,EAAAC,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAC,EAAO1F,EAAY,CAAF,IAAA2F,EAAAC,EAAAC,EAAA,OAAAL,EAAAA,EAAA,EAAAM,KAAA,SAAAC,EAAE,CAAF,cAAAA,EAAAC,KAAAD,EAAAE,KAAE,CAAF,OAAAF,OAAAA,EAAAE,KAAA,KACXC,EAAAA,SAAO,kBAAAtB,OAAiC5E,CAAE,CAAE,EACvEmG,OAAO,EACPC,aAAa,CACZ,CACEC,KAAM,mBACNC,SAAU,CAAC,CAAEC,IAAK,UAAWC,OAAQ,CAAC,IAAI,CAAE,CAAC,CAC/C,CAAC,CACF,EACAzG,IAAI,EAAC,OARW,GAQX4F,EAAAI,EAAAU,KARAb,EAAID,EAAJC,KAAMC,EAAKF,EAALE,MAAK,CASfA,EAAO,CAAFE,EAAAE,KAAA,QACP3F,EAAauF,MAAM,CAAEa,QAASb,EAAMa,OAAQ,CAAC,EAACX,EAAAE,KAAA,mBACpCL,EAAM,CAAFG,EAAAE,KAAA,SACd3F,EAAauF,MAAM,CAAEa,QAAS,yBAAF9B,OAA2B5E,EAAE,aAAa,CAAC,EAAC+F,EAAAE,KAAA,wBAAAF,EAAAY,OAAA,SAEjEf,CAAI,iBAAAG,EAAAY,OAAA,SAEN,IAAI,2BAAAZ,EAAAa,KAAA,IAAAlB,CAAA,EACZ,mBAlBsBmB,EAAA,QAAAvB,EAAAwB,MAAA,KAAAC,SAAA,MAoBjBC,GAAa,eAAAC,EAAA1B,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAyB,EACpBlH,EAAU,KAAAmH,EAAAvB,EAAA,OAAAJ,EAAAA,EAAA,EAAAM,KAAA,SAAAsB,EAAA,eAAAA,EAAApB,KAAAoB,EAAAnB,KAAA,QAAAmB,OAAAA,EAAAnB,KAAA,EAEavD,MACrBwD,EAAAA,SAAyB,mBAAmB,EACzCmB,eAAe,CAACrH,CAAE,CAAC,EACnBsH,aAAa,UAAW,CAAC,IAAI,CAAC,EAC9BvH,IAAI,CACT,EAAC,OAAAoH,OAAAA,EAAAC,EAAAX,KALOb,EAAIuB,EAAJvB,KAAIwB,EAAAT,OAAA,SAMLf,GAAI,YAAJA,EAAO,CAAC,CAAC,0BAAAwB,EAAAR,KAAA,IAAAM,CAAA,EACjB,mBAVkBK,EAAA,QAAAN,EAAAH,MAAA,KAAAC,SAAA,MA8CnB,SAlCAS,EAAAA,WAAU,UAAM,CACdnC,GAAkBrF,CAAE,EAAEyH,KAAK,SAAC7B,EAAS,CACnC,GAAIA,EAAM,KAAA8B,EACRnF,GAASqD,CAAI,EACbzE,EAAYyE,EAAK+B,SAAS,EAC1BhG,IAAa+F,EAAC9B,EAAKgC,oBAAgB,MAAAF,IAAA,cAArBA,EAAuB1H,EAAE,EACvC2C,GAAkB,GAADiC,UACZC,EAAAA,IAAQ,QAAQ,CAAC,EAAAD,UAAGiD,EAAAA,IAAK,EAAI,IAAM,EAAE,EAAAjD,OAAGgB,EAAKkC,MAAQ,EAAE,CAC5D,CACF,CACF,CAAC,CACH,EAAG,CAAC9H,CAAE,CAAC,KAEPwH,EAAAA,WAAU,UAAM,CACd,GAAItG,GAAYpB,EAAM,CACpB,IAAME,KAAK+H,EAAAA,OAAgBC,GAAAA,IAAmBlI,CAAI,EAAGoB,CAAQ,GAAK,GAClEwC,EAAkB,CAAEyC,OAAQnG,CAAG,CAAC,CAClC,CACF,EAAG,CAACkB,EAAUpB,CAAI,CAAC,KAEnB0H,EAAAA,WAAU,UAAM,CACV9F,GACFsF,GAActF,CAAU,EAAE+F,KAAK,SAAC7B,EAAS,KAAAqC,EAAAC,EACvCnH,GAAkB6E,GAAI,YAAJA,EAAMuC,MAAM,EAC9B,IAAMrG,EAAY8D,GAAI,OAAAqC,EAAJrC,EAAMwC,WAAO,MAAAH,IAAA,cAAbA,EAAejI,GAC3BU,EAAgBkF,GAAI,OAAAsC,EAAJtC,EAAMwC,WAAO,MAAAF,IAAA,cAAbA,EAAeC,OACrCxH,GAAiBD,CAAa,EAC1BoB,GACFC,GAAaD,CAAS,CAE1B,CAAC,CAEL,EAAG,CAACJ,CAAU,CAAC,EAEXzB,OAAOoI,MAAMrI,CAAE,GACjBsI,EAAAA,QAAQC,KAAK,MAAM,KACZC,EAAAA,KAAAC,EAAAA,SAAA,EAAI,GAGTvG,MAEAsG,EAAAA,KAACE,GAAAA,QAAS,CACR5G,UAAWA,EACXZ,YAAUyH,GAAAA,WAAUzH,CAAQ,EAC5B0H,SAAU,UAAM,CACdzG,EAAY,EAAK,EACjBc,EAAa,CAAC,CAAC,EACfgB,EAAiB,EAAK,EACtBH,EAAuB,CAAC,CAAC,CAC3B,EACA+E,gBAAiB7I,EACjB0B,WAAYA,CAAW,CACxB,KAKHoH,EAAAA,MAACC,GAAAA,GAAa,CAACC,UAAU,kBAAiB1C,SAAA,IACxCkC,EAAAA,KAAA,OAAKQ,UAAU,YAAW1C,SACvBpF,MACCsH,EAAAA,KAACS,GAAAA,GAAW,CACVC,KAAMhI,EACNiI,cAAenG,GACfd,SAAU,GACVkH,aAAc,SAACC,EAAM,CACnB9H,GAAW8H,CAAC,EACRlF,GAAY,MAAZA,EAAcnE,IAAIiE,EAAiBE,EAAanE,GAAIqJ,CAAC,CAC3D,EACAC,YAAarF,EACbJ,oBAAqBA,GACrBJ,eAAgBA,GAChB8F,aAAa1K,EAAAyE,GAAQkG,SAAK,MAAA3K,IAAA,QAAbA,EAAe4K,kBAAoBxE,GAAMyE,OACtDC,gBACEb,EAAAA,MAACc,GAAAA,EAAK,CAACC,KAAK,SAAQvD,SAAA,EACjBpD,GAAM,OAAApE,EAANoE,EAAQ4G,gBAAY,MAAAhL,IAAA,cAApBA,EAAsBiL,SACrB,gCACF,OAAKvB,EAAAA,KAACwB,GAAAA,EAAiB,EAAE,EACxB9G,GAAM,OAAAnE,EAANmE,EAAQ4G,gBAAY,MAAA/K,IAAA,QAApBA,EAAsBgL,SACrB,8BACF,GACA,IAACE,EAAAA,IAAmBvJ,GAAeI,EAAc,GACjD,CAAC,UAAW,WAAW,EAAEiJ,UAASzH,GAAK,YAALA,EAAO6F,SAAU,EAAE,KACnDK,EAAAA,KAAC0B,GAAAA,EAAiB,CAChBC,QAAS,UAAM,CACblH,EAAa,CACXmH,SAAU,SAAC9H,EAAU,CACnBnB,EAAYmB,CAAK,EACjBH,EAAY,EAAI,EAChBc,EAAa,CAAC,CAAC,CACjB,CACF,CAAC,CACH,EACA4G,KAAK,QACLQ,KAAK,UAAS/D,YAEbzB,EAAAA,IAAQ,MAAM,CAAC,CACC,EAEnB,EACD,EACI,CACR,CACF,CACF,CACE,KAEL2D,EAAAA,KAAC8B,GAAAA,EAAc,CACbxI,UAAWA,EACXyI,SAAUrG,EACVsG,MAAOjG,GAAYJ,CAAY,EAC/BsG,QAAS,kBAAM/G,EAAkB,CAAC,CAAC,CAAC,EACpCgH,aAActG,GACduG,SAAU,kBACRzG,GAAoBgB,MAAI0F,EAAAA,IAAmB1G,CAAgB,CAAC,CAAC,EAE/DiB,kBAAmBA,GACnB0F,eACE3J,GACAiD,MACA2G,EAAAA,IAAkB5J,EAAUiD,EAAc,SAACK,EAAS,CAClDP,EAAiBO,EAAKxE,EAAE,EACxB0D,EAAkB,CAAEyC,OAAQ3B,EAAKxE,EAAG,CAAC,CACvC,CAAC,CACF,CACF,CAAC,EACW,CAEnB,EAEA,GAAerB,E,oBC1Pf,SAASK,EAA0B+L,EAAK,CACtC,GAAIA,GAAO,KAAM,MAAM,IAAI,UAAU,sBAAwBA,CAAG,CAClE,CACAC,EAAO,QAAUhM,EAA2BgM,EAAO,QAAQ,WAAa,GAAMA,EAAO,QAAQ,QAAaA,EAAO,O", "sources": ["webpack://labwise-web/./src/pages/route/view/index.tsx", "webpack://labwise-web/./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectDestructuringEmpty.js"], "sourcesContent": ["import ButtonWithLoading from '@/components/ButtonWithLoading'\nimport {\n  GetRouteEvent,\n  RouteEditor,\n  UpdateChildrenEvent\n} from '@/components/RouteEditor'\nimport { useRouteTreeStore } from '@/components/RouteEditor/Flex/Tree/store'\nimport { useBrainFetch } from '@/hooks/useBrainFetch'\nimport { useUserSetting } from '@/hooks/useUserSetting'\nimport { getReactionFromRxn } from '@/pages/reaction/util'\nimport type { ProjectCompoundStatus, ProjectType } from '@/services/brain'\nimport { ProjectCompound, ProjectRoute, service } from '@/services/brain'\nimport { getWord, isEN, isReadonlyMolecule } from '@/utils'\nimport { PageContainer } from '@ant-design/pro-components'\nimport {\n  history,\n  useAccess,\n  useModel,\n  useParams,\n  useSearchParams\n} from '@umijs/max'\nimport { App, Space } from 'antd'\nimport { cloneDeep } from 'lodash'\nimport React, { ReactNode, useEffect, useState } from 'react'\nimport ExportRouteButton from '../components/ExportRouteButton'\nimport ReactionDrawer from '../components/ReactionDrawer'\nimport EditRoute from '../edit'\nimport { useReactionDrawer } from '../hooks/useReactionDrawer'\nimport { useRxnYieldMap } from '../useRxnYieldMap'\nimport {\n  MainTreeForRoute,\n  getAllRxnsFromTree,\n  getDeepthMap,\n  getIdOfReaction,\n  getNameOfReaction,\n  getNavigateConfig,\n  getRxnFromReaction,\n  selectProcedure\n} from '../util'\nimport './index.less'\n\nconst ViewRoute: React.FC = ({}) => {\n  const { cacheCurReactionStepNo } = useModel('commend')\n  const { routeId: idStr = '' } = useParams<{ routeId: string }>()\n  const [searchParams] = useSearchParams()\n  const step = searchParams.get('step') || ''\n  const id = Number.parseInt(idStr)\n  const { notification } = App.useApp()\n  const [projectStatus, setProjectStatus] = useState<ProjectType>()\n  const [compoundStatus, setCompoundStatus] = useState<ProjectCompoundStatus>()\n  const [mainTree, setMainTree] = useState<MainTreeForRoute>()\n  const [curTree, setCurTree] = useState<MainTreeForRoute>()\n  const [compoundId, setCompoundId] = useState<number>()\n  const [projectId, setProjectId] = useState<number>()\n  const [editMode, setEditMode] = useState<boolean>(false)\n  const [route, setRoute] = useState<ProjectRoute>()\n  const { fetch } = useBrainFetch()\n\n  const setExportFileName = useRouteTreeStore((s) => s.setExportFileName)\n  const [saveEvent, setSaveEvent] = useState<GetRouteEvent>({})\n  const access = useAccess()\n  const { setting } = useUserSetting()\n  const [selectRxnEvent, setSelectRxnEvent] = useState<{ select?: string }>({})\n  const [updateChildrenEvent, setUpdateChildrenEvent] =\n    useState<UpdateChildrenEvent>({})\n  const {\n    onSelectReaction,\n    selectedReaction,\n    selectedNode,\n    selectedMainReaction\n  } = useReactionDrawer(curTree)\n  const deepthMap = getDeepthMap(mainTree)\n  const getStepName = (node?: MainTreeForRoute): string | ReactNode => {\n    const deepth = node?.id && deepthMap.get(node.id)\n    if (deepth) {\n      let reaction_step_no = getNameOfReaction(deepth[0], deepth[1])\n      cacheCurReactionStepNo(reaction_step_no)\n      return `${getWord('reaction')} ${reaction_step_no}`\n    }\n    return getWord('reaction')\n  }\n  const { map, add } = useRxnYieldMap(getAllRxnsFromTree(mainTree), projectId)\n\n  const onSelectProcedure = selectProcedure(\n    setUpdateChildrenEvent,\n    curTree,\n    selectedReaction\n  )\n\n  const fetchProjectRoute = async (id: number) => {\n    const { data, error } = await service<ProjectRoute>(`project-routes/${id}`)\n      .select()\n      .populateDeep([\n        {\n          path: 'project_compound',\n          children: [{ key: 'project', fields: ['id'] }]\n        }\n      ])\n      .get()\n    if (error) {\n      notification.error({ message: error.message })\n    } else if (!data) {\n      notification.error({ message: `Project route with id ${id} not found` })\n    } else {\n      return data as unknown as ProjectRoute\n    }\n    return null\n  }\n\n  const fetchCompound = async (\n    id: number\n  ): Promise<ProjectCompound | undefined> => {\n    const { data } = await fetch(\n      service<ProjectCompound>('project-compounds')\n        .selectManyByID([id])\n        .populateWith('project', ['id'])\n        .get()\n    )\n    return data?.[0]\n  }\n\n  useEffect(() => {\n    fetchProjectRoute(id).then((data) => {\n      if (data) {\n        setRoute(data)\n        setMainTree(data.main_tree)\n        setCompoundId(data.project_compound?.id)\n        setExportFileName(\n          `${getWord('Routes')}${isEN() ? ' ' : ''}${data.name || ''}`\n        )\n      }\n    })\n  }, [id])\n\n  useEffect(() => {\n    if (mainTree && step) {\n      const id = getIdOfReaction(getReactionFromRxn(step), mainTree) || ''\n      setSelectRxnEvent({ select: id })\n    }\n  }, [mainTree, step])\n\n  useEffect(() => {\n    if (compoundId) {\n      fetchCompound(compoundId).then((data) => {\n        setCompoundStatus(data?.status)\n        const projectId = data?.project?.id\n        const projectStatus = data?.project?.status as ProjectType\n        setProjectStatus(projectStatus)\n        if (projectId) {\n          setProjectId(projectId)\n        }\n      })\n    }\n  }, [compoundId])\n\n  if (Number.isNaN(id)) {\n    history.push('/404')\n    return <></>\n  }\n\n  if (editMode) {\n    return (\n      <EditRoute\n        projectId={projectId}\n        mainTree={cloneDeep(mainTree)}\n        onCancel={() => {\n          setEditMode(false)\n          setSaveEvent({})\n          onSelectReaction(false)\n          setUpdateChildrenEvent({})\n        }}\n        retroBackboneId={id}\n        compoundId={compoundId}\n      />\n    )\n  }\n\n  return (\n    <PageContainer className=\"route-view-root\">\n      <div className=\"container\">\n        {mainTree && (\n          <RouteEditor\n            root={mainTree}\n            getRouteEvent={saveEvent}\n            editMode={false}\n            onTreeChange={(t) => {\n              setCurTree(t)\n              if (selectedNode?.id) onSelectReaction(selectedNode.id, t)\n            }}\n            onSelectRxn={onSelectReaction}\n            updateChildrenEvent={updateChildrenEvent}\n            selectRxnEvent={selectRxnEvent}\n            rxnYieldMap={setting.retro?.route_show_yields ? map : undefined}\n            rightTopSlot={\n              <Space size=\"middle\">\n                {access?.authCodeList?.includes(\n                  'view-by-backbone.button.export'\n                ) && <ExportRouteButton />}\n                {access?.authCodeList?.includes(\n                  'view-by-backbone.button.edit'\n                ) &&\n                !isReadonlyMolecule(projectStatus, compoundStatus) &&\n                ['editing', 'confirmed'].includes(route?.status || '') ? (\n                  <ButtonWithLoading\n                    onClick={() => {\n                      setSaveEvent({\n                        getRoute: (route) => {\n                          setMainTree(route)\n                          setEditMode(true)\n                          setSaveEvent({})\n                        }\n                      })\n                    }}\n                    size=\"small\"\n                    type=\"primary\"\n                  >\n                    {getWord('edit')}\n                  </ButtonWithLoading>\n                ) : (\n                  ''\n                )}\n              </Space>\n            }\n          />\n        )}\n      </div>\n\n      <ReactionDrawer\n        projectId={projectId}\n        reaction={selectedReaction}\n        title={getStepName(selectedNode)}\n        onClose={() => setSelectRxnEvent({})}\n        mainReaction={selectedMainReaction}\n        onUpdate={() =>\n          selectedReaction && add(getRxnFromReaction(selectedReaction))\n        }\n        onSelectProcedure={onSelectProcedure}\n        navigateConfig={\n          mainTree &&\n          selectedNode &&\n          getNavigateConfig(mainTree, selectedNode, (node) => {\n            onSelectReaction(node.id)\n            setSelectRxnEvent({ select: node.id })\n          })\n        }\n      />\n    </PageContainer>\n  )\n}\n\nexport default ViewRoute\n", "function _objectDestructuringEmpty(obj) {\n  if (obj == null) throw new TypeError(\"Cannot destructure \" + obj);\n}\nmodule.exports = _objectDestructuringEmpty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": ["ViewRoute", "_ref", "_setting$retro", "_access$authCodeList", "_access$authCodeList2", "_objectDestructuringEmpty", "_useModel", "useModel", "cacheCurReactionStepNo", "_useParams", "useParams", "_useParams$routeId", "routeId", "idStr", "_useSearchParams", "useSearchParams", "_useSearchParams2", "_slicedToArray", "searchParams", "step", "get", "id", "Number", "parseInt", "_App$useApp", "App", "useApp", "notification", "_useState", "useState", "_useState2", "projectStatus", "setProjectStatus", "_useState3", "_useState4", "compoundStatus", "setCompoundStatus", "_useState5", "_useState6", "mainTree", "setMainTree", "_useState7", "_useState8", "curTree", "setCurTree", "_useState9", "_useState10", "compoundId", "setCompoundId", "_useState11", "_useState12", "projectId", "setProjectId", "_useState13", "_useState14", "editMode", "setEditMode", "_useState15", "_useState16", "route", "setRoute", "_useBrainFetch", "useBrainFetch", "fetch", "setExportFileName", "useRouteTreeStore", "s", "_useState17", "_useState18", "saveEvent", "setSaveEvent", "access", "useAccess", "_useUserSetting", "useUserSetting", "setting", "_useState19", "_useState20", "selectRxnEvent", "setSelectRxnEvent", "_useState21", "_useState22", "updateChildrenEvent", "setUpdateChildrenEvent", "_useReactionDrawer", "useReactionDrawer", "onSelectReaction", "selectedReaction", "selectedNode", "selectedMainReaction", "deepthMap", "getDeepthMap", "getStepName", "node", "deepth", "reaction_step_no", "getNameOfReaction", "concat", "getWord", "_useRxnYieldMap", "useRxnYieldMap", "getAllRxnsFromTree", "map", "add", "onSelectProcedure", "selectProcedure", "fetchProjectRoute", "_ref2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$service$select", "data", "error", "wrap", "_context", "prev", "next", "service", "select", "populateDeep", "path", "children", "key", "fields", "sent", "message", "abrupt", "stop", "_x", "apply", "arguments", "fetchCompound", "_ref3", "_callee2", "_yield$fetch", "_context2", "selectManyByID", "populateWith", "_x2", "useEffect", "then", "_data$project_compoun", "main_tree", "project_compound", "isEN", "name", "getIdOfReaction", "getReactionFromRxn", "_data$project", "_data$project2", "status", "project", "isNaN", "history", "push", "_jsx", "_Fragment", "EditRoute", "cloneDeep", "onCancel", "retroBackboneId", "_jsxs", "<PERSON><PERSON><PERSON><PERSON>", "className", "RouteEditor", "root", "getRouteEvent", "onTreeChange", "t", "onSelectRxn", "rxnYieldMap", "retro", "route_show_yields", "undefined", "rightTopSlot", "Space", "size", "authCodeList", "includes", "ExportRouteButton", "isReadonlyMolecule", "ButtonWithLoading", "onClick", "getRoute", "type", "ReactionDrawer", "reaction", "title", "onClose", "mainReaction", "onUpdate", "getRxnFromReaction", "navigateConfig", "getNavigateConfig", "obj", "module"], "sourceRoot": ""}