"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[8268],{81012:function(ue,G,t){t.d(G,{Z:function(){return J}});var z=t(97857),m=t.n(z),Y=t(48054),Z=t(67294),c=t(85893),K=(0,Z.lazy)(function(){return Promise.all([t.e(6049),t.e(6369),t.e(6891)]).then(t.bind(t,99814)).then(function(T){return{default:T.default}})});function J(T){return(0,c.jsx)(<PERSON>.<PERSON>,{fallback:(0,c.jsx)("div",{children:(0,c.jsx)(Y.Z,{active:!0})}),children:(0,c.jsx)(K,m()({},T))})}},40802:function(ue,G,t){t.d(G,{Z:function(){return ce}});var z=t(81012),m=t(32884),Y=t(38545),Z=t(4393),c=t(66309),K=t(28036),J=t(55241),T=t(93967),S=t.n(T),w=t(70831),C={reactionCard:"reactionCard____5fXG",reactionInfo:"reactionInfo___U4SpH",reactionTitle:"reactionTitle___aJZR0",no:"no___Avx3R",title:"title___FMgkA",status:"status___xYs3R",routes:"routes___zdkV6",structure:"structure___CHZWI"},a=t(85893);function ce(V){var s,o,M,e=V.reaction,v=V.isPlayground,k=V.onAction,R=V.enableToReaction,g=V.displayProject,le=function(E){var ae=[];return E.map(function(j){return ae.push(j==null?void 0:j.id)}),ae},x=(0,w.useParams)(),Q=x.id,ee=(e==null?void 0:e.commendCount)&&Number(e==null?void 0:e.commendCount)>0,re=ee?"".concat((0,m.oz)("comment"),"\uFF08").concat(e==null?void 0:e.commendCount,"\uFF09"):(0,m.oz)("comment");return(0,a.jsxs)(Z.Z,{className:C.reactionCard,children:[e!=null&&e.reaction?(0,a.jsx)(z.Z,{structure:e==null?void 0:e.reaction,className:S()(C.structure,"enablePointer"),clickEvent:R?function(){var B;w.history.push("/projects/".concat(Q||((B=e.project)===null||B===void 0?void 0:B.id),"/reaction/").concat(e==null?void 0:e.id))}:void 0}):"",(0,a.jsxs)("div",{className:C.reactionInfo,children:[(0,a.jsxs)("div",{className:S()(C.reactionTitle,"flex-justify-space-between"),children:[(0,a.jsxs)("div",{className:S()(C.no,"flex-justify-space-between"),children:[(0,a.jsxs)("span",{className:C.title,children:[(0,m.oz)("reaction-no"),e==null?void 0:e.id]}),v&&ee&&e!==null&&e!==void 0&&e.updatedAt?(0,a.jsxs)("div",{children:["\xA0\xA0",(0,m.oz)("last-comment-date"),":"," ",(0,m.H3)(e==null?void 0:e.updatedAt)]}):"",e!=null&&e.collection_subject?(0,a.jsxs)("div",{children:["\xA0\xA0",(0,m.oz)("reaction-step-ID"),": ",e==null?void 0:e.collection_subject]}):""]}),(0,a.jsxs)("div",{className:C.status,children:[v?"":(0,a.jsx)(a.Fragment,{children:e!=null&&e.progress?(0,a.jsx)(c.Z,{color:"processing",children:(0,m.oz)("proceeded")}):(0,a.jsx)(c.Z,{color:"warning",children:(0,m.oz)("to-be-proceeded")})}),v?(0,a.jsx)(K.ZP,{type:"link",onClick:k,children:(0,a.jsxs)(J.Z,{content:re,children:[(0,a.jsx)(Y.Z,{}),ee?"\uFF08".concat(e==null?void 0:e.commendCount,"\uFF09"):""]})}):""]})]}),(0,a.jsxs)("div",{className:"display-flex",children:[(0,a.jsxs)("div",{children:[(0,m.oz)("nums-of-my-reactions"),"\uFF1A",e!=null&&(s=e.effective_procedures)!==null&&s!==void 0&&s.length?(0,a.jsx)("span",{className:"enablePointer",onClick:function(){var E;return w.history.push("/projects/".concat(Q||((E=e.project)===null||E===void 0?void 0:E.id),"/reaction/").concat(e==null?void 0:e.id,"?tab=my-reaction-design"))},children:e==null||(o=e.effective_procedures)===null||o===void 0?void 0:o.length}):0]}),"\xA0\xA0\xA0",(0,a.jsxs)("div",{children:[(0,m.oz)("nums-of-my-experiments"),"\uFF1A",e!=null&&e.experiment_count?(0,a.jsx)("span",{className:"enablePointer",style:{color:"black"},onClick:function(){var E;return w.history.push("/projects/".concat(Q||((E=e.project)===null||E===void 0?void 0:E.id),"/reaction/").concat(e==null?void 0:e.id,"?tab=my-experiment"))},children:e==null?void 0:e.experiment_count}):0]})]}),v?"":(0,a.jsxs)(a.Fragment,{children:[g&&(0,a.jsxs)("div",{className:"flex-align-items-center",children:[(0,a.jsxs)("div",{children:[(0,m.oz)("menu.list.project-list"),"\uFF1A"]}),(0,a.jsx)("div",{style:{color:"black"},children:(M=e.project)===null||M===void 0?void 0:M.no})]}),(0,a.jsxs)("div",{className:"display-flex",children:[(0,m.oz)("associated-routes"),"\uFF1A",(0,a.jsx)("span",{className:C.routes,children:(0,m.qt)(e==null?void 0:e.project_routes)?le(e==null?void 0:e.project_routes).join("\u3001"):"\u65E0"})]})]})]})]})}},21138:function(ue,G,t){t.r(G),t.d(G,{default:function(){return we}});var z=t(5574),m=t.n(z),Y=t(59652),Z=t(97857),c=t.n(Z),K=t(64599),J=t.n(K),T=t(15009),S=t.n(T),w=t(99289),C=t.n(w),a=t(89257),ce=t(6588),V=t(65160),s=t(40520),o=t(87172),M=["aiGenerated","myRoutes","reaction"],e=t(32884),v=t(77598),k=t(72252),R=t(96486),g=t(67294),le=t(67642),x=t(70831),Q=t(89575),ee=t(30042),re=t(51562),B=t(71181),E=t(45098),ae=t(94860),j=t(4393),pe=t(84722),ye=t(15101),je=t(7361),n=t(85893),ve=function(l){var r=l.route,P=l.isPlayground,N=(0,x.useModel)("compound"),H=N.updatePagenate,te=N.cacheFilterInfo,L=N.curFilterInfo,I=(0,x.useModel)("commend"),oe=I.getProfileInfo,X=(0,x.useSearchParams)(),$=m()(X,2),O=$[1],A=r.main_trees.map(ye.WN).reduce(function(_,W){return W?_+1:_},0),de=r!=null&&r.group_conditions?r.group_conditions[L==null?void 0:L.group]:void 0,ge=P?["view","feedback"]:["view","collect","feedback"];return(0,n.jsx)(je.Z,{item:r,updatedAt:(r==null?void 0:r.updated_at)||(r==null?void 0:r.updatedAt),isCollected:r==null?void 0:r.collected,routeNo:P?r==null?void 0:r.collection_id:r==null?void 0:r.id,commendCount:r==null?void 0:r.content_count,stepNum:r.backbone.length-1,hasBranches:A>1,hasBranch:A>0,group:de,handleFilterSimilar:C()(S()().mark(function _(){return S()().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:te(c()(c()({},r),{},{similarId:de})),H({page:1,pageSize:10}),O({page:"1",pageSize:"10"},{replace:!0});case 3:case"end":return d.stop()}},_)})),actions:ge,onAction:function(){var _=C()(S()().mark(function W(d){return S()().wrap(function(ie){for(;;)switch(ie.prev=ie.next){case 0:d==="feedback"&&oe({_commendSuject:r,collection_class:"retro-backbone",isPlayground:P});case 1:case"end":return ie.stop()}},W)}));return function(W){return _.apply(this,arguments)}}()})},fe=ve,xe=function(l){var r=l.result,P=l.isPlayground,N=(0,B.m)(),H=N.copy;return(0,n.jsx)(j.Z,{type:"inner",title:(0,n.jsx)(fe,{route:r,isPlayground:P}),className:"ai-synthetic-route-card-root",children:(0,n.jsx)(E.Z,{node:(0,ae.m)(r.backbone),withWrapper:!0,customProps:{reactionWrapper:{className:"reaction-wrapper"},structureWrapper:{className:"structure-wrapper"}},customChildren:{structure:function(L,I){return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("div",{className:"structure-info",children:(0,pe.Q)(I)+1})})},reaction:function(L,I){return(0,n.jsx)("div",{className:"reaction-btns",children:(0,n.jsx)("div",{className:"reaction-copy-btn reaction-btn",onClick:function(){return H("".concat(I,">>").concat(L))},children:(0,n.jsx)(re.r,{})})})}}})})},me=xe,Se=t(40802);function We(q){var l=q.item,r=(0,x.useModel)("commend"),P=r.getProfileInfo;return(0,n.jsx)(Se.Z,{isPlayground:!0,enableToReaction:!1,reaction:c()(c()({},l),{},{reaction:(0,e.qt)(l==null?void 0:l.reactants)?"".concat(l==null?void 0:l.reactants.join("."),">>").concat(l==null?void 0:l.product):null,updatedAt:l==null?void 0:l.updated_at,commendCount:l==null?void 0:l.content_count}),onAction:function(){return P({_commendSuject:l,collection_class:"retro-reaction"})}})}var Ue=t(91686),$e={routesContent:"routesContent___NMCJf",generatingGif:"generatingGif___J7Jae"},Ke=function(l){var r=l.isPlayground,P=l.moleculeId,N=l.openAiGenerateModel,H=(0,x.useModel)("commend"),te=H.getCommonExpression,L=H.reload,I=H.finishedReload,oe=H.startReload,X=(0,g.useRef)(null),$=(0,x.useSearchParams)(),O=m()($,2),A=O[0],de=O[1],ge=(0,x.useParams)(),_=ge.id,W=ge.compoundId,d=(0,x.useModel)("compound"),Ce=d.loading,ie=d.totalCount,ke=d.myRoutesTotal,Re=d.routes,Pe=d.myRoutesData,qe=d.getAIListData,ze=d.cacheFilterInfo,_e=d.getMyRoutesData,et=d.getPlaygroundData,be=d.updateRouteType,F=d.pagenate,Te=d.updatePagenate,Ee=d.curFilterInfo,tt=d.retroParamsConfig,Ne=d.myRoutesLoading,i=d.curHistoryInfo,nt=d.filteredSearchHistory,he=d.targetMolecule,rt=d.getSearchHistory,Ae=d.updateBacoboneLengthRange,Fe=d.updateMainTreeStepsRange,Le=d.cacheRetroProcessUpdates,D=d.routeType,ne=D==="myRoutes",se=(0,x.useAccess)(),Oe=function(){var p=C()(S()().mark(function h(){var u,b,U,y;return S()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:if(!r){f.next=2;break}return f.abrupt("return",et({pagenate:F,routeType:D}));case 2:if(!ne){f.next=11;break}if(!Ne){f.next=5;break}return f.abrupt("return");case 5:if(se!=null&&(b=se.authCodeList)!==null&&b!==void 0&&b.includes("compound.tab.myRoutes")){f.next=7;break}return f.abrupt("return",[]);case 7:return f.next=9,_e((0,Q.Hq)(W));case 9:f.next=17;break;case 11:if(!Ce){f.next=13;break}return f.abrupt("return");case 13:if(se!=null&&(U=se.authCodeList)!==null&&U!==void 0&&U.includes("compound.tab.aiGenerated")){f.next=15;break}return f.abrupt("return",[]);case 15:return f.next=17,qe();case 17:(u=X.current)!==null&&u!==void 0&&u.parentElement&&((y=X.current)===null||y===void 0||y.parentElement.scrollTo({top:0}));case 18:case"end":return f.stop()}},h)}));return function(){return p.apply(this,arguments)}}();(0,g.useEffect)(function(){var p=A.get("tab");return M.includes(p)?be(p):be("aiGenerated"),function(){Ae([]),Fe([]),be(null)}},[]);var at=function(){var p=C()(S()().mark(function h(){var u,b;return S()().wrap(function(y){for(;;)switch(y.prev=y.next){case 0:return y.next=2,(0,o.query)("retro-backbones/length-range/".concat(i==null?void 0:i.id)).get();case 2:u=y.sent,b=u.data,Ae(b);case 5:case"end":return y.stop()}},h)}));return function(){return p.apply(this,arguments)}}(),ot=function(){var p=C()(S()().mark(function h(){var u,b;return S()().wrap(function(y){for(;;)switch(y.prev=y.next){case 0:return y.next=2,(0,o.query)("retro-backbones/main-tree-steps-range/".concat(i==null?void 0:i.id)).get();case 2:u=y.sent,b=u.data,Fe(b);case 5:case"end":return y.stop()}},h)}));return function(){return p.apply(this,arguments)}}();(0,g.useEffect)(function(){i!=null&&i.id&&(at(),ot())},[i==null?void 0:i.id]);var it=(0,x.useModel)("compound"),st=it.searchHistoryData;(0,g.useEffect)(function(){if(W){var p=(0,le.io)("".concat((0,e.SP)().apiBase),{reconnection:!0,reconnectionDelay:1e3,reconnectionDelayMax:5e3});p.on("connect",function(){p.emit("retro-process:join",W)}),p.on("retro-process:update",function(h){console.log("---updates01---",h),h[0].compound_id===P&&Le(h);var u=!1,b=!1,U=h.some(function(Ie){return st.some(function(vt){return vt.retro_id===Ie.retro_id})}),y=J()(h),Me;try{for(y.s();!(Me=y.n()).done;){var f=Me.value;f.status==="canceled"?b=!0:f.status!=="running"&&(u=!0,f.status==="completed"&&Le([{retro_id:f==null?void 0:f.retro_id,status:"completed"}]))}}catch(Ie){y.e(Ie)}finally{y.f()}console.log("---hasUpdate---",u,U,b,P),(u&&U||b)&&(rt(P),oe())})}},[W]),(0,g.useEffect)(function(){ze(ne?{pagenate:F,moleculeId:P,routeType:D}:c()({pagenate:F},Ee))},[D]),(0,g.useEffect)(function(){Oe()},[F,Ee,tt,r]),(0,v.Z)(function(){L&&(Oe(),I())},[L]),(0,g.useEffect)(function(){A.set("page",F.page.toString()),A.set("pageSize",F.pageSize.toString()),de(A,{replace:!0})},[F]),(0,v.Z)(function(){i!=null&&i.id&&Te({page:1,pageSize:F==null?void 0:F.pageSize})},[i==null?void 0:i.id]),(0,g.useEffect)(function(){D&&(A.set("tab",D),de(A,{replace:!0})),te(D==="aiGenerated"?"retro-backbone":"project-route")},[D]);var De=(i==null?void 0:i.id)||(0,e.qt)(nt),Ge=["pending","limited"].includes(i.status),Ze=i.status==="failed",Be=(i==null?void 0:i.status)==="running",lt=function(){return Ge?(0,n.jsx)(V.r,{}):Ze?(0,n.jsx)(s.r,{}):Be?(0,n.jsx)("img",{className:$e.generatingGif,src:a}):De?null:(0,n.jsx)(ce.r,{})},dt=function(){var h=ne&&!r?(0,R.isEmpty)(Pe):(0,R.isEmpty)(Re);return ne?(0,n.jsxs)("span",{children:[(0,e.oz)("no-routes-create-tip"),(0,n.jsxs)("a",{onClick:function(){return x.history.push("/projects/".concat(_,"/compound/").concat(P,"/create"))},children:["\u3010",(0,e.oz)("new-route"),"\u3011"]}),(0,e.oz)("add-routes")]}):Ge?(0,n.jsx)(n.Fragment,{children:(0,e.oz)("wait-tip")}):Ze?(0,n.jsx)(n.Fragment,{children:(0,e.oz)("search-failed")}):Be||Ce||Ne?(0,n.jsx)(n.Fragment,{children:(0,e.oz)("generating-tip")}):De||h?(0,n.jsx)(n.Fragment,{children:(0,e.oz)("no-routes-returned")}):(0,n.jsx)("span",{children:(he==null?void 0:he.status)==="finished"?(0,e.oz)("no-AI-returned"):(0,n.jsxs)(n.Fragment,{children:[(0,e.oz)("no-AI-returned-create-I"),(0,n.jsxs)("a",{onClick:N,children:["\u3010",(0,e.oz)("gnerate-routes"),"\u3011"]}),(0,e.oz)("no-AI-returned-create-II")]})})},He=(0,g.memo)(function(p){var h=p.curRoutes;return console.log("---curRoutes---",h),(0,n.jsxs)(n.Fragment,{children:[h.map(function(u){return"backbone"in u&&D==="aiGenerated"?(0,n.jsx)(me,{result:u,routeType:D,isPlayground:r},u.id):r&&D==="reaction"?(0,n.jsx)(We,{item:u},"reaction-".concat(u.id)):(0,n.jsx)(Ue.Z,{route:u,isPlayground:r,targetMolecule:he},u.id)}),ie>10?(0,n.jsx)(k.Z,{className:"pagination",total:ne?ke:ie,current:F.page,pageSize:F.pageSize,showSizeChanger:!1,onChange:function(b,U){return Te({page:b,pageSize:U})}}):""]})}),ut=(0,g.memo)(function(p){var h=p.status,u=p.id;return(0,n.jsx)("div",{children:(0,n.jsx)(ee.Z,{image:lt(),wrapperClassName:$e.routesContent,des:(0,n.jsx)(dt,{})})},"".concat(u,"-").concat(h))}),ct=function(){return ne&&!r&&!(0,R.isEmpty)(Pe)?(0,n.jsx)(He,{curRoutes:Pe}):!ne&&!(0,R.isEmpty)(Re)?(0,n.jsx)(He,{curRoutes:Re}):(0,n.jsx)(ut,{status:i==null?void 0:i.status,id:i==null?void 0:i.id})};return(0,n.jsx)("div",{ref:X,children:(0,n.jsx)(ct,{})})},Je=Ke,Ve=t(11774),Qe=t(93967),Xe=t.n(Qe),Ye={};function we(){var q=(0,x.useModel)("compound"),l=q.updateRouteType,r=q.routeType,P=q.curAiRouteNum,N=(0,x.useModel)("commend"),H=N.showLauncher,te=N.isOpen,L=N.sendMessage,I={aiGenerated:{name:(0,e.oz)("aiGenerated")},myRoutes:{name:(0,e.oz)("myRoutes")},reaction:{name:(0,e.oz)("reaction")}},oe=function($){var O;return I[$].name;switch($){case"aiGenerated":{var A=P||"";return Number.isInteger(A)?"".concat(I[$].name," (").concat(A,")"):I[$].name}case"myRoutes":return(O=targetMolecule)!==null&&O!==void 0&&O.project_routes_number?"".concat(I[$].name," (").concat(targetMolecule.project_routes_number,")"):I[$].name;default:return""}};return(0,n.jsxs)(Ve._z,{className:Xe()(Ye.playground),children:[(0,n.jsx)(Y.Z,{onMessageWasSent:L,hiddenLauncher:H,isOpen:te,commendType:r==="reaction"?"reaction":"route"}),(0,n.jsx)(j.Z,{className:"routes-list-wrapper",tabList:Object.entries(I).map(function(X){var $=m()(X,1),O=$[0];return{key:O,tab:oe(O)}}),activeTabKey:r,onTabChange:function($){return l($)},children:(0,n.jsx)(Je,{isPlayground:!0})})]})}},77598:function(ue,G,t){t.d(G,{Z:function(){return Z}});var z=t(67294),m=function(c){return function(K,J){var T=(0,z.useRef)(!1);c(function(){return function(){T.current=!1}},[]),c(function(){if(!T.current)T.current=!0;else return K()},J)}},Y=null,Z=m(z.useEffect)},96074:function(ue,G,t){t.d(G,{Z:function(){return V}});var z=t(67294),m=t(93967),Y=t.n(m),Z=t(53124),c=t(85982),K=t(14747),J=t(83559),T=t(83262);const S=s=>{const{componentCls:o,sizePaddingEdgeHorizontal:M,colorSplit:e,lineWidth:v,textPaddingInline:k,orientationMargin:R,verticalMarginInline:g}=s;return{[o]:Object.assign(Object.assign({},(0,K.Wf)(s)),{borderBlockStart:`${(0,c.unit)(v)} solid ${e}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:g,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${(0,c.unit)(v)} solid ${e}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${(0,c.unit)(s.dividerHorizontalGutterMargin)} 0`},[`&-horizontal${o}-with-text`]:{display:"flex",alignItems:"center",margin:`${(0,c.unit)(s.dividerHorizontalWithTextGutterMargin)} 0`,color:s.colorTextHeading,fontWeight:500,fontSize:s.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${e}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${(0,c.unit)(v)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${o}-with-text-left`]:{"&::before":{width:`calc(${R} * 100%)`},"&::after":{width:`calc(100% - ${R} * 100%)`}},[`&-horizontal${o}-with-text-right`]:{"&::before":{width:`calc(100% - ${R} * 100%)`},"&::after":{width:`calc(${R} * 100%)`}},[`${o}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:k},"&-dashed":{background:"none",borderColor:e,borderStyle:"dashed",borderWidth:`${(0,c.unit)(v)} 0 0`},[`&-horizontal${o}-with-text${o}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${o}-dashed`]:{borderInlineStartWidth:v,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:e,borderStyle:"dotted",borderWidth:`${(0,c.unit)(v)} 0 0`},[`&-horizontal${o}-with-text${o}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${o}-dotted`]:{borderInlineStartWidth:v,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${o}-with-text`]:{color:s.colorText,fontWeight:"normal",fontSize:s.fontSize},[`&-horizontal${o}-with-text-left${o}-no-default-orientation-margin-left`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${o}-inner-text`]:{paddingInlineStart:M}},[`&-horizontal${o}-with-text-right${o}-no-default-orientation-margin-right`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${o}-inner-text`]:{paddingInlineEnd:M}}})}},w=s=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:s.marginXS});var C=(0,J.I$)("Divider",s=>{const o=(0,T.mergeToken)(s,{dividerHorizontalWithTextGutterMargin:s.margin,dividerHorizontalGutterMargin:s.marginLG,sizePaddingEdgeHorizontal:0});return[S(o)]},w,{unitless:{orientationMargin:!0}}),a=function(s,o){var M={};for(var e in s)Object.prototype.hasOwnProperty.call(s,e)&&o.indexOf(e)<0&&(M[e]=s[e]);if(s!=null&&typeof Object.getOwnPropertySymbols=="function")for(var v=0,e=Object.getOwnPropertySymbols(s);v<e.length;v++)o.indexOf(e[v])<0&&Object.prototype.propertyIsEnumerable.call(s,e[v])&&(M[e[v]]=s[e[v]]);return M},V=s=>{const{getPrefixCls:o,direction:M,divider:e}=z.useContext(Z.E_),{prefixCls:v,type:k="horizontal",orientation:R="center",orientationMargin:g,className:le,rootClassName:x,children:Q,dashed:ee,variant:re="solid",plain:B,style:E}=s,ae=a(s,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style"]),j=o("divider",v),[pe,ye,je]=C(j),n=!!Q,ve=R==="left"&&g!=null,fe=R==="right"&&g!=null,xe=Y()(j,e==null?void 0:e.className,ye,je,`${j}-${k}`,{[`${j}-with-text`]:n,[`${j}-with-text-${R}`]:n,[`${j}-dashed`]:!!ee,[`${j}-${re}`]:re!=="solid",[`${j}-plain`]:!!B,[`${j}-rtl`]:M==="rtl",[`${j}-no-default-orientation-margin-left`]:ve,[`${j}-no-default-orientation-margin-right`]:fe},le,x),me=z.useMemo(()=>typeof g=="number"?g:/^\d+$/.test(g)?Number(g):g,[g]),Se=Object.assign(Object.assign({},ve&&{marginLeft:me}),fe&&{marginRight:me});return pe(z.createElement("div",Object.assign({className:xe,style:Object.assign(Object.assign({},e==null?void 0:e.style),E)},ae,{role:"separator"}),Q&&k!=="vertical"&&z.createElement("span",{className:`${j}-inner-text`,style:Se},Q)))}}}]);

//# sourceMappingURL=p__playground__index.9cda5442.async.js.map