"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[3470],{4393:function(Ie,A,u){u.d(A,{Z:function(){return ce}});var o=u(67294),F=u(93967),m=u.n(F),K=u(98423),B=u(53124),Z=u(98675),X=u(48054),V=u(48096),U=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,a=Object.getOwnPropertySymbols(e);i<a.length;i++)t.indexOf(a[i])<0&&Object.prototype.propertyIsEnumerable.call(e,a[i])&&(r[a[i]]=e[a[i]]);return r},G=e=>{var{prefixCls:t,className:r,hoverable:a=!0}=e,i=U(e,["prefixCls","className","hoverable"]);const{getPrefixCls:d}=o.useContext(B.E_),g=d("card",t),h=m()(`${g}-grid`,r,{[`${g}-grid-hoverable`]:a});return o.createElement("div",Object.assign({},i,{className:h}))},n=u(85982),y=u(14747),J=u(83559),Q=u(83262);const Y=e=>{const{antCls:t,componentCls:r,headerHeight:a,cardPaddingBase:i,tabsMarginBottom:d}=e;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:a,marginBottom:-1,padding:`0 ${(0,n.unit)(i)}`,color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.headerFontSize,background:e.headerBg,borderBottom:`${(0,n.unit)(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`,borderRadius:`${(0,n.unit)(e.borderRadiusLG)} ${(0,n.unit)(e.borderRadiusLG)} 0 0`},(0,y.dF)()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},y.vS),{[`
          > ${r}-typography,
          > ${r}-typography-edit-content
        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${t}-tabs-top`]:{clear:"both",marginBottom:d,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,"&-bar":{borderBottom:`${(0,n.unit)(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`}}})},q=e=>{const{cardPaddingBase:t,colorBorderSecondary:r,cardShadow:a,lineWidth:i}=e;return{width:"33.33%",padding:t,border:0,borderRadius:0,boxShadow:`
      ${(0,n.unit)(i)} 0 0 0 ${r},
      0 ${(0,n.unit)(i)} 0 0 ${r},
      ${(0,n.unit)(i)} ${(0,n.unit)(i)} 0 0 ${r},
      ${(0,n.unit)(i)} 0 0 0 ${r} inset,
      0 ${(0,n.unit)(i)} 0 0 ${r} inset;
    `,transition:`all ${e.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:a}}},_=e=>{const{componentCls:t,iconCls:r,actionsLiMargin:a,cardActionsIconSize:i,colorBorderSecondary:d,actionsBg:g}=e;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:g,borderTop:`${(0,n.unit)(e.lineWidth)} ${e.lineType} ${d}`,display:"flex",borderRadius:`0 0 ${(0,n.unit)(e.borderRadiusLG)} ${(0,n.unit)(e.borderRadiusLG)}`},(0,y.dF)()),{"& > li":{margin:a,color:e.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:e.calc(e.cardActionsIconSize).mul(2).equal(),fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimary,transition:`color ${e.motionDurationMid}`},[`a:not(${t}-btn), > ${r}`]:{display:"inline-block",width:"100%",color:e.colorTextDescription,lineHeight:(0,n.unit)(e.fontHeight),transition:`color ${e.motionDurationMid}`,"&:hover":{color:e.colorPrimary}},[`> ${r}`]:{fontSize:i,lineHeight:(0,n.unit)(e.calc(i).mul(e.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:`${(0,n.unit)(e.lineWidth)} ${e.lineType} ${d}`}}})},k=e=>Object.assign(Object.assign({margin:`${(0,n.unit)(e.calc(e.marginXXS).mul(-1).equal())} 0`,display:"flex"},(0,y.dF)()),{"&-avatar":{paddingInlineEnd:e.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:e.marginXS}},"&-title":Object.assign({color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG},y.vS),"&-description":{color:e.colorTextDescription}}),ee=e=>{const{componentCls:t,cardPaddingBase:r,colorFillAlter:a}=e;return{[`${t}-head`]:{padding:`0 ${(0,n.unit)(r)}`,background:a,"&-title":{fontSize:e.fontSize}},[`${t}-body`]:{padding:`${(0,n.unit)(e.padding)} ${(0,n.unit)(r)}`}}},te=e=>{const{componentCls:t}=e;return{overflow:"hidden",[`${t}-body`]:{userSelect:"none"}}},ae=e=>{const{componentCls:t,cardShadow:r,cardHeadPadding:a,colorBorderSecondary:i,boxShadowTertiary:d,cardPaddingBase:g,extraColor:h}=e;return{[t]:Object.assign(Object.assign({},(0,y.Wf)(e)),{position:"relative",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,[`&:not(${t}-bordered)`]:{boxShadow:d},[`${t}-head`]:Y(e),[`${t}-extra`]:{marginInlineStart:"auto",color:h,fontWeight:"normal",fontSize:e.fontSize},[`${t}-body`]:Object.assign({padding:g,borderRadius:`0 0 ${(0,n.unit)(e.borderRadiusLG)} ${(0,n.unit)(e.borderRadiusLG)}`},(0,y.dF)()),[`${t}-grid`]:q(e),[`${t}-cover`]:{"> *":{display:"block",width:"100%",borderRadius:`${(0,n.unit)(e.borderRadiusLG)} ${(0,n.unit)(e.borderRadiusLG)} 0 0`}},[`${t}-actions`]:_(e),[`${t}-meta`]:k(e)}),[`${t}-bordered`]:{border:`${(0,n.unit)(e.lineWidth)} ${e.lineType} ${i}`,[`${t}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${t}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${e.motionDurationMid}, border-color ${e.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:r}},[`${t}-contain-grid`]:{borderRadius:`${(0,n.unit)(e.borderRadiusLG)} ${(0,n.unit)(e.borderRadiusLG)} 0 0 `,[`${t}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${t}-loading) ${t}-body`]:{marginBlockStart:e.calc(e.lineWidth).mul(-1).equal(),marginInlineStart:e.calc(e.lineWidth).mul(-1).equal(),padding:0}},[`${t}-contain-tabs`]:{[`> div${t}-head`]:{minHeight:0,[`${t}-head-title, ${t}-extra`]:{paddingTop:a}}},[`${t}-type-inner`]:ee(e),[`${t}-loading`]:te(e),[`${t}-rtl`]:{direction:"rtl"}}},ie=e=>{const{componentCls:t,cardPaddingSM:r,headerHeightSM:a,headerFontSizeSM:i}=e;return{[`${t}-small`]:{[`> ${t}-head`]:{minHeight:a,padding:`0 ${(0,n.unit)(r)}`,fontSize:i,[`> ${t}-head-wrapper`]:{[`> ${t}-extra`]:{fontSize:e.fontSize}}},[`> ${t}-body`]:{padding:r}},[`${t}-small${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{paddingTop:0,display:"flex",alignItems:"center"}}}}},re=e=>({headerBg:"transparent",headerFontSize:e.fontSizeLG,headerFontSizeSM:e.fontSize,headerHeight:e.fontSizeLG*e.lineHeightLG+e.padding*2,headerHeightSM:e.fontSize*e.lineHeight+e.paddingXS*2,actionsBg:e.colorBgContainer,actionsLiMargin:`${e.paddingSM}px 0`,tabsMarginBottom:-e.padding-e.lineWidth,extraColor:e.colorText});var ne=(0,J.I$)("Card",e=>{const t=(0,Q.mergeToken)(e,{cardShadow:e.boxShadowCard,cardHeadPadding:e.padding,cardPaddingBase:e.paddingLG,cardActionsIconSize:e.fontSize,cardPaddingSM:12});return[ae(t),ie(t)]},re),H=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,a=Object.getOwnPropertySymbols(e);i<a.length;i++)t.indexOf(a[i])<0&&Object.prototype.propertyIsEnumerable.call(e,a[i])&&(r[a[i]]=e[a[i]]);return r};const oe=e=>{const{actionClasses:t,actions:r=[],actionStyle:a}=e;return o.createElement("ul",{className:t,style:a},r.map((i,d)=>{const g=`action-${d}`;return o.createElement("li",{style:{width:`${100/r.length}%`},key:g},o.createElement("span",null,i))}))};var se=o.forwardRef((e,t)=>{const{prefixCls:r,className:a,rootClassName:i,style:d,extra:g,headStyle:h={},bodyStyle:$={},title:f,loading:x,bordered:O=!0,size:j,type:z,cover:L,actions:E,tabList:S,children:T,activeTabKey:I,defaultActiveTabKey:ge,tabBarExtraContent:ue,hoverable:be,tabProps:me={},classNames:w,styles:N}=e,ye=H(e,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:he,direction:$e,card:b}=o.useContext(B.E_),ve=c=>{var s;(s=e.onTabChange)===null||s===void 0||s.call(e,c)},v=c=>{var s;return m()((s=b==null?void 0:b.classNames)===null||s===void 0?void 0:s[c],w==null?void 0:w[c])},p=c=>{var s;return Object.assign(Object.assign({},(s=b==null?void 0:b.styles)===null||s===void 0?void 0:s[c]),N==null?void 0:N[c])},pe=o.useMemo(()=>{let c=!1;return o.Children.forEach(T,s=>{(s==null?void 0:s.type)===G&&(c=!0)}),c},[T]),l=he("card",r),[fe,Se,Ce]=ne(l),xe=o.createElement(X.Z,{loading:!0,active:!0,paragraph:{rows:4},title:!1},T),R=I!==void 0,Oe=Object.assign(Object.assign({},me),{[R?"activeKey":"defaultActiveKey"]:R?I:ge,tabBarExtraContent:ue});let W;const C=(0,Z.Z)(j),je=!C||C==="default"?"large":C,D=S?o.createElement(V.Z,Object.assign({size:je},Oe,{className:`${l}-head-tabs`,onChange:ve,items:S.map(c=>{var{tab:s}=c,M=H(c,["tab"]);return Object.assign({label:s},M)})})):null;if(f||g||D){const c=m()(`${l}-head`,v("header")),s=m()(`${l}-head-title`,v("title")),M=m()(`${l}-extra`,v("extra")),Le=Object.assign(Object.assign({},h),p("header"));W=o.createElement("div",{className:c,style:Le},o.createElement("div",{className:`${l}-head-wrapper`},f&&o.createElement("div",{className:s,style:p("title")},f),g&&o.createElement("div",{className:M,style:p("extra")},g)),D)}const ze=m()(`${l}-cover`,v("cover")),Ee=L?o.createElement("div",{className:ze,style:p("cover")},L):null,Te=m()(`${l}-body`,v("body")),Be=Object.assign(Object.assign({},$),p("body")),Pe=o.createElement("div",{className:Te,style:Be},x?xe:T),we=m()(`${l}-actions`,v("actions")),Ne=E!=null&&E.length?o.createElement(oe,{actionClasses:we,actionStyle:p("actions"),actions:E}):null,Me=(0,K.Z)(ye,["onTabChange"]),Ge=m()(l,b==null?void 0:b.className,{[`${l}-loading`]:x,[`${l}-bordered`]:O,[`${l}-hoverable`]:be,[`${l}-contain-grid`]:pe,[`${l}-contain-tabs`]:S==null?void 0:S.length,[`${l}-${C}`]:C,[`${l}-type-${z}`]:!!z,[`${l}-rtl`]:$e==="rtl"},a,i,Se,Ce),He=Object.assign(Object.assign({},b==null?void 0:b.style),d);return fe(o.createElement("div",Object.assign({ref:t},Me,{className:Ge,style:He}),W,Ee,Pe,Ne))}),le=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,a=Object.getOwnPropertySymbols(e);i<a.length;i++)t.indexOf(a[i])<0&&Object.prototype.propertyIsEnumerable.call(e,a[i])&&(r[a[i]]=e[a[i]]);return r},de=e=>{const{prefixCls:t,className:r,avatar:a,title:i,description:d}=e,g=le(e,["prefixCls","className","avatar","title","description"]),{getPrefixCls:h}=o.useContext(B.E_),$=h("card",t),f=m()(`${$}-meta`,r),x=a?o.createElement("div",{className:`${$}-meta-avatar`},a):null,O=i?o.createElement("div",{className:`${$}-meta-title`},i):null,j=d?o.createElement("div",{className:`${$}-meta-description`},d):null,z=O||j?o.createElement("div",{className:`${$}-meta-detail`},O,j):null;return o.createElement("div",Object.assign({},g,{className:f}),x,z)};const P=se;P.Grid=G,P.Meta=de;var ce=P}}]);

//# sourceMappingURL=shared-P1s7QtdXqYMxFNoOg2tTf9AZ5EY_.3b0f945c.async.js.map