(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[259],{98505:function(ze,X,r){"use strict";r.r(X),r.d(X,{default:function(){return bn}});var oe=r(15009),p=r.n(oe),se=r(99289),z=r.n(se),me=r(5574),I=r.n(me),le=r(49677),ce=r.n(le),de=r(81012),be=r(97857),y=r.n(be),S=r(14522),C=r(37507),t=r(32884),m=r(70831),Ve=r(93967),Ue=r.n(Ve),Z=r(67294),De=r(33547),Ge={},n=r(85893);function ke(){var e=(0,m.useModel)("experiment"),d=e.searchExperimentCheck,f=e.experimentCheckList,g=(0,m.useSearchParams)(),u=I()(g,2),i=u[0],l=u[1],v=(0,m.useParams)(),x=v.reactionId;return(0,Z.useEffect)(function(){d({project_reaction_id:x})},[]),(0,n.jsx)("div",{className:Ue()(Ge.detectRecord),children:(0,n.jsx)(S.Z,{isAnalysisTab:!0,analysisData:f,requestEvent:function(E){d(y()(y()({},E),{},{project_reaction_id:x}));var D=i.get("tab");D&&l({tab:D},{replace:!0})}})})}var Ke=function(d,f,g,u){return{key:"detect-record",label:(0,n.jsx)(De.Z,{title:(0,t.oz)("pages.experiment.label.operation.detectRecord"),getNumber:z()(p()().mark(function i(){var l;return p()().wrap(function(x){for(;;)switch(x.prev=x.next){case 0:if(g){x.next=2;break}return x.abrupt("return");case 2:return x.next=4,(0,C.QZ)({data:{project_reaction_id:g}});case 4:if(l=x.sent,!(0,C.y6)(l).ok){x.next=7;break}return x.abrupt("return",l.data.length);case 7:case"end":return x.stop()}},i)})),refetchEvent:u}),children:f&&g?(0,n.jsx)(ke,{}):null}},Qe=r(48632),Xe=r(26704),Je=r(42525),Ie=r(96486),he=r(94102),Ee=r(10784),Le=r(99814),Fn=function(e){return e.Batch="batch",e.Drip="drip",e.Normal="normal",e}({}),On=function(e){return e.P0="P0",e.P1="P1",e.P2="P2",e}({}),wn=function(e){return e.Canceled="canceled",e.Completed="completed",e.Failed="failed",e.Limited="limited",e.Pending="pending",e.Running="running",e}({}),Mn=function(e){return e.Canceled="canceled",e.Confirmed="confirmed",e.Editing="editing",e.Finished="finished",e}({}),Bn=function(e){return e.Canceled="canceled",e.Created="created",e.Designing="designing",e.Finished="finished",e.Synthesizing="synthesizing",e}({}),Sn=function(e){return e.BuildingBlock="building_block",e.Target="target",e.TempBlock="temp_block",e}({}),Hn=function(e){return e.Cancelled="cancelled",e.Created="created",e.Finished="finished",e.Holding="holding",e.Started="started",e}({}),Wn=function(e){return e.Ffs="ffs",e.Fte="fte",e.Personal="personal",e}({}),Yn=function(e){return e.Aloha="aloha",e.Sd01="sd-01",e}({}),Vn=function(e){return e.Error="error",e.Holding="holding",e.Idle="idle",e.Working="working",e}({}),Un=function(e){return e.Lcms="LCMS",e.Nmr="NMR",e.Tlc="TLC",e}({}),Gn=function(e){return e.Canceled="canceled",e.Checking="checking",e.Finished="finished",e.Todo="todo",e}({}),kn=function(e){return e.F="F",e.M="M",e}({}),Kn=r(49633),Qn=r(63125),Ce=function(e){return e.Canceled="canceled",e.Created="created",e.Published="published",e.Validated="validated",e}({}),Xn=function(e){return e.Created="created",e.Ready="ready",e.Running="running",e.Incident="incident",e.Hold="hold",e.Failed="failed",e.Completed="completed",e.Success="success",e}({}),Jn=function(e){return e.Experiment="experiment",e.Global="global",e.Project="project",e}({}),qn=function(e){return e.I="I",e.Ii="II",e.Iii="III",e}({}),_n=function(e){return e.AVAILABLE="AVAILABLE",e.LEAVE="LEAVE",e.VACATE="VACATE",e}({}),er=function(e){return e.Canceled="canceled",e.Completed="completed",e.Running="running",e.Todo="todo",e}({}),nr=function(e){return e.P0="P0",e.P1="P1",e.P2="P2",e}({}),rr=function(e){return e.Canceled="canceled",e.Completed="completed",e.Failed="failed",e.Limited="limited",e.Pending="pending",e.Running="running",e}({}),tr=function(e){return e.Canceled="canceled",e.Confirmed="confirmed",e.Editing="editing",e.Finished="finished",e}({}),ar=function(e){return e.Canceled="canceled",e.Created="created",e.Designing="designing",e.Finished="finished",e.Synthesizing="synthesizing",e}({}),ir=function(e){return e.BuildingBlock="building_block",e.Target="target",e.TempBlock="temp_block",e}({}),or=function(e){return e.Cancelled="cancelled",e.Created="created",e.Finished="finished",e.Holding="holding",e.Started="started",e}({}),sr=function(e){return e.Ffs="ffs",e.Fte="fte",e.Personal="personal",e}({}),lr=function(e){return e.Aloha="aloha",e.Sd01="sd-01",e}({}),cr=function(e){return e.Error="error",e.Holding="holding",e.Idle="idle",e.Working="working",e}({}),dr=function(e){return e.MainReactant="main_reactant",e.Reactant="reactant",e}({}),ur=function(e){return e.Canceled="canceled",e.Created="created",e.Prepared="prepared",e.Running="running",e.Finished="finished",e}({}),vr=function(e){return e.G="g",e.Ml="ml",e}({}),ne=r(18221),Te=r(31418),qe=r(55241),ge=r(71471),Ae=r(4393),J=r(71230),G=r(15746),re=r(42075),ue=r(28036),_e=r(96074),ve=r(66309),en=r(50187),nn=function(d){var f,g,u,i=d.procedure,l=d.design,v=d.projectReactionId,x=d.onEdit,O=d.onDelete,E=d.onUpdated,D=(0,m.useAccess)(),T=i.yields,Y=i.smiles,q=Te.Z.useApp(),k=q.modal,V=(0,m.useParams)(),K=V.id,_=V.reactionId,a=function(){return x==null?void 0:x(i)},U=function(){},$=function(){var b=z()(p()().mark(function L(){return p()().wrap(function(w){for(;;)switch(w.prev=w.next){case 0:k.confirm({title:"\u786E\u8BA4\u53D6\u6D88\u8BE5\u5B9E\u9A8C\u8BBE\u8BA1\uFF1F",onOk:function(){var M=z()(p()().mark(function W(){return p()().wrap(function(B){for(;;)switch(B.prev=B.next){case 0:return B.next=2,(0,C.gO)({data:{id:l.id,status:Ce.Canceled}});case 2:E==null||E("cancel");case 3:case"end":return B.stop()}},W)}));function P(){return M.apply(this,arguments)}return P}()});case 1:case"end":return w.stop()}},L)}));return function(){return b.apply(this,arguments)}}(),R=function(L){var H=L.content,w=L.placement;return(0,n.jsx)(qe.Z,{overlayStyle:{width:"30vw"},placement:w||"top",content:H,arrow:!1,children:(0,n.jsx)(ge.Z.Link,{children:"Procedure"})})};return(0,n.jsx)(Ae.Z,{style:{margin:"4px 0"},size:"small",className:"my-reaction-design-card-root",children:(0,n.jsxs)(J.Z,{children:[(0,n.jsxs)(G.Z,{sm:24,md:11,children:[(0,n.jsxs)(J.Z,{children:[(0,n.jsx)(ge.Z.Title,{level:5,children:(0,t.oz)("reaction")}),(0,n.jsx)(re.Z,{className:"actions-wrapper",size:"small",children:(D==null||(f=D.authCodeList)===null||f===void 0?void 0:f.includes("reaction-detail.myReactionDesign.editMolecules"))&&(0,n.jsx)(ue.ZP,{type:"link",onClick:a,children:(0,t.oz)("edit-molecules")})})]}),(0,n.jsx)(J.Z,{children:(0,n.jsxs)(re.Z,{children:[(0,n.jsxs)(ge.Z.Text,{children:[(0,t.oz)("yield"),": "]}),(0,n.jsx)(ge.Z.Text,{type:"secondary",children:(0,Ie.isNil)(T)?"-":"".concat(T,"%")})]})}),(0,n.jsx)(J.Z,{children:(0,n.jsx)(Le.default,{structure:Y})}),(0,n.jsx)(J.Z,{children:(0,n.jsx)(R,{placement:"topLeft",content:i.procedure})})]}),(0,n.jsx)(G.Z,{sm:0,md:1,className:"divider-wrapper",children:(0,n.jsx)(_e.Z,{type:"vertical",className:"divider"})}),(0,n.jsxs)(G.Z,{sm:24,md:11,children:[(0,n.jsxs)(ne.vY,{title:(0,t.oz)("experiment-design"),column:1,dataSource:l,extra:(0,n.jsxs)(re.Z,{children:[D!=null&&(g=D.authCodeList)!==null&&g!==void 0&&g.includes("projects_reaction_experimentDesign_view")?(0,n.jsx)(ue.ZP,{type:"link",onClick:function(){window.open("/projects/".concat(K,"/reaction/").concat(_,"/experimental-procedure/detail/").concat((0,t.YW)(JSON.stringify(l.id)),"?type=editor"),"_blank")},children:(0,t.oz)("pages.projectTable.actionLabel.viewDetail")}):"",l.status===Ce.Published&&(0,n.jsx)(en.Z,{materialTable:i.material_table,projectId:(0,t.Hq)(l.project_no),projectReactionId:v,experiementDesignNo:l.experiment_design_no,onSuccess:function(){return E==null?void 0:E("create")}}),(D==null||(u=D.authCodeList)===null||u===void 0?void 0:u.includes("projects_reaction_experimentDesign_cancel"))&&l.status!==Ce.Canceled&&(0,n.jsx)(Ee.Z,{type:"link",onClick:$,children:(0,t.oz)("pages.experiment.label.operation.cancel")})]}),children:[(0,n.jsx)(ne.vY.Item,{label:(0,t.oz)("pages.experiment.label.experimentDesignName"),dataIndex:"name"}),(0,n.jsx)(ne.vY.Item,{label:(0,t.oz)("experiment-design-status"),dataIndex:"status",valueEnum:{canceled:{text:(0,n.jsx)(ve.Z,{color:"#979797",children:(0,t.oz)("pages.experimentDesign.statusLabel.canceled")})},created:{text:(0,n.jsx)(ve.Z,{color:"#FAAD14",children:(0,t.oz)("pages.experimentDesign.statusLabel.created")})},published:{text:(0,n.jsx)(ve.Z,{color:"#1890FF",children:(0,t.oz)("pages.experimentDesign.statusLabel.published")})},validated:{text:(0,n.jsx)(ve.Z,{color:"#FAAD14",children:(0,t.oz)("pages.experimentDesign.statusLabel.validated")})}}}),(0,n.jsx)(ne.vY.Item,{label:(0,t.oz)("creator"),dataIndex:"creator"}),(0,n.jsx)(ne.vY.Item,{label:(0,t.oz)("last-modified-time"),dataIndex:"modified_date",valueType:"dateTime"}),(0,n.jsx)(ne.vY.Item,{label:(0,t.oz)("reaction-parameters")}),(0,n.jsx)(ne.vY.Item,{label:(0,t.oz)("experiment-log")})]}),(0,n.jsx)(R,{content:l.reference_text||l.reference_text})]})]})})},Ne=nn,rn=function(d){var f=d.reaction,g=d.projectReaction,u=d.showCanceled,i=d.renderer,l=(0,Z.useState)([]),v=I()(l,2),x=v[0],O=v[1],E=(0,Z.useState)({page:1,pageSize:10}),D=I()(E,2),T=D[0],Y=D[1],q=(0,Z.useState)(!1),k=I()(q,2),V=k[0],K=k[1],_=function(){var U=z()(p()().mark(function $(R,b){var L,H,w;return p()().wrap(function(P){for(;;)switch(P.prev=P.next){case 0:return P.next=2,(0,he.UQ)(R,b);case 2:return L=P.sent,P.t0=Ie.groupBy,P.next=6,(0,he.Ep)(L);case 6:return P.t1=P.sent,H=(0,P.t0)(P.t1,"id"),P.next=10,(0,he.BB)(L);case 10:w=P.sent,O(w.map(function(W){var Q;return{design:W,procedure:((Q=H[W.rxn_no])===null||Q===void 0?void 0:Q[0])||{}}}));case 12:case"end":return P.stop()}},$)}));return function(R,b){return U.apply(this,arguments)}}();(0,Z.useEffect)(function(){!f||!g||(K(!0),_(f,g).then(function(){return K(!1)}))},[f,g]);var a=u?x:x.filter(function(U){return U.design.status!=="canceled"});return x?(0,n.jsx)(Je.Rs,{ghost:!0,loading:V,pagination:{current:T.page,pageSize:T.pageSize,simple:!0,total:a.length,onChange:function($,R){Y({page:$,pageSize:R})}},renderItem:function($){var R=$.procedure,b=$.design;return i==null?void 0:i(R,b)},grid:{column:1},dataSource:a}):(0,n.jsx)(Xe.uk,{type:"list"})},pr=null,tn=function(d,f){var g=arguments.length>2&&arguments[2]!==void 0?arguments[2]:function(i,l){return(0,n.jsx)(Ne,{procedure:i,design:l,projectReactionId:f==null?void 0:f.id})},u=arguments.length>3?arguments[3]:void 0;return{key:"my-reaction-design",label:(0,n.jsx)(De.Z,{title:(0,t.oz)("my-reaction-experimental"),getNumber:z()(p()().mark(function i(){return p()().wrap(function(v){for(;;)switch(v.prev=v.next){case 0:if(!(!d||!f)){v.next=2;break}return v.abrupt("return",void 0);case 2:return v.next=4,(0,he.Yb)(d,f,u);case 4:return v.abrupt("return",v.sent);case 5:case"end":return v.stop()}},i)}))}),children:d&&f?(0,n.jsx)(rn,{reaction:d,projectReaction:f,renderer:g,showCanceled:u}):null}},an=r(99512),on=r(15001),sn=r(72035),$e=r(56337),ln=r(89277),cn=r(21987),N=r(15394),dn=r(34528),fr=r(84898),Fe=r(7378),un=function(d,f,g,u){return[{valueType:"text",title:(0,t.oz)("experiment-ID"),dataIndex:"experiment_no",render:function(l,v){return(0,n.jsx)("a",{onClick:function(){u==null||u(),m.history.push("/projects/".concat(d,"/reaction/").concat(f,"/experimental-procedure/conclusion/").concat((0,t.YW)(JSON.stringify(v.experiment_no))).concat(["completed","failed"].includes(v.experiment_result)?"#conclusion":""))},children:v.experiment_no})}},{valueType:"select",title:(0,t.oz)("pages.experiment.label.personInCharge"),dataIndex:"experiment_owner",valueEnum:g.reduce(function(i,l){return l.value&&!(l.value in i)&&(i[l.value]={text:l.label}),i},{})},{valueType:"dateTime",title:(0,t.oz)("start-time"),dataIndex:"experiment_start_time"},{valueType:"select",title:(0,t.oz)("conclusion"),dataIndex:"experiment_result",render:function(l,v){return(0,n.jsx)(on.Z,{status:v.experiment_result,labelPrefix:"pages.experiment.statusLabel"})}},{valueType:"text",title:(0,t.oz)("yield"),dataIndex:"experiment_yield",renderText:function(l,v){return"".concat(v.experiment_yield.toFixed(0),"%")}}]},vn=function(d){var f,g=d.projectId,u=d.reactionId,i=d.procedure,l=d.isSame,v=d.onReference,x=(0,Z.useState)(!1),O=I()(x,2),E=O[0],D=O[1],T=(0,sn.H)(g),Y=T.members,q=Te.Z.useApp(),k=q.modal,V=(0,m.useAccess)(),K=i.text,_=i.rxn,a=i.similarity,U=i.experimentalProcedure,$=i.yieldString,R=i.reference,b=R.type,L=R.link,H=R.title,w=R.authors,M=R.date,P=R.no,W=R.assignees,Q=R.reference_text,B=(0,n.jsx)(N.Z,{style:{color:"#1890ff",cursor:"pointer"},onClick:function(ee){ee.stopPropagation(),D(function(te){return!te})},children:E?"less":"more"});return(0,n.jsxs)(Ae.Z,{size:"small",className:"ai-card-root",children:[(0,n.jsxs)(J.Z,{children:[(0,n.jsx)(G.Z,{span:12,children:(0,n.jsxs)(re.Z,{className:"title",size:"large",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(N.Z,{type:"secondary",children:(0,t.oz)("similarity")}),":",(0,n.jsx)(N.Z,{style:{color:"#027AFF",paddingLeft:8},children:a})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(N.Z,{type:"secondary",children:(0,t.oz)("yield")}),":",(0,n.jsx)(N.Z,{style:{color:"#027AFF",paddingLeft:8},children:$})]}),!!i.scalable&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(re.Z,{}),(0,n.jsx)(ve.Z,{color:"lime",children:(0,t.oz)("procedure.scalable")})]}),l!==void 0?(0,Fe.fn)(l):null]})}),(0,n.jsx)(G.Z,{style:{marginLeft:"auto"},children:(0,n.jsx)(re.Z,{children:(V==null||(f=V.authCodeList)===null||f===void 0?void 0:f.includes("reaction-detail.reactionLib.addToMyReaction"))&&v&&(0,n.jsx)(ue.ZP,{type:"primary",size:"small",onClick:function(){return v(i)},children:(0,t.oz)("pages.route.label.addToMyReaction")})})})]}),(0,n.jsxs)(J.Z,{gutter:{md:16},children:[(0,n.jsx)(G.Z,{md:8,children:(0,n.jsx)(Le.default,{structure:_})}),(0,n.jsxs)(G.Z,{md:16,children:[(0,n.jsx)(N.Z,{type:"secondary",strong:!0,children:"Procedure"}),(0,n.jsxs)(cn.Z,{ellipsis:E?!1:{rows:5,expandable:!0,symbol:B},copyable:{text:K},children:[K||U||"",E&&B]}),l&&(0,n.jsxs)(N.Z,{type:"secondary",children:[(0,t.oz)("source"),"\uFF1A",(0,n.jsx)(Ee.Z,{type:"link",onClick:z()(p()().mark(function pe(){var ee,te;return p()().wrap(function(ae){for(;;)switch(ae.prev=ae.next){case 0:return ae.next=2,(0,C.XK)({routeParams:"".concat(i.id)});case 2:ee=ae.sent,(0,C.y6)(ee).ok&&(te=k.info({title:(0,t.oz)("experiment-log"),width:800,content:(0,n.jsx)($e.Z,{pagination:!1,toolbar:{settings:[],search:!1},search:!1,columns:un(g,u,Y,function(){return te.destroy()}),dataSource:ee.data.experiment_results})}));case 4:case"end":return ae.stop()}},pe)})),children:(0,t.oz)("experiment-history")})]}),!l&&(0,n.jsxs)("div",{className:"reference-wrapper",children:[(0,n.jsxs)(dn.Z,{level:5,children:["Reference ",(0,Fe.Gt)(b)]}),b==="patent"&&(P||W)&&(0,n.jsxs)(N.Z,{style:{textAlign:"right",display:"block",width:"100%"},children:[P&&(0,n.jsxs)(n.Fragment,{children:["Patent No: ",(0,n.jsx)(N.Z,{copyable:!0,children:P})]}),W&&(0,n.jsxs)(N.Z,{children:[", assigned by ",W]}),(0,n.jsx)("br",{})]}),L&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(N.Z,{type:"secondary",copyable:!0,children:L?(0,n.jsx)(ln.Z,{href:L,children:H}):H}),(0,n.jsx)("br",{})]}),Q&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(N.Z,{type:"secondary",copyable:!0,children:Q}),(0,n.jsx)("br",{})]}),(0,n.jsx)(N.Z,{italic:!0,style:{textAlign:"right",display:"block",width:"100%"},children:w}),M&&(0,n.jsx)(N.Z,{style:{textAlign:"right",display:"block",width:"100%"},children:M})]})]})]})]})},pn=vn,fn=r(16003),mn=r(61487),hn=function(d){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,g=(0,m.useParams)(),u=g[d],i=u===void 0?"":u,l=Number.parseInt(i),v=Number.isNaN(l)?f:l;return v},Oe=r(87172),gn=r(11774),xn=r(72269),Ze=r(64134),jn=r(85113),Re=r(11005),yn=function(d){var f,g;ce()(d);var u=(0,m.useAccess)(),i=hn("reactionId"),l=(0,m.useSearchParams)(),v=I()(l,2),x=v[0],O=v[1],E=(0,Z.useState)(x.get("tab")),D=I()(E,2),T=D[0],Y=D[1],q=(0,mn.f)(),k=q.fetch,V=q.loading,K=(0,Z.useState)(),_=I()(K,2),a=_[0],U=_[1],$=(0,Z.useState)(),R=I()($,2),b=R[0],L=R[1],H=(0,Z.useRef)(),w=(0,m.useModel)("@@initialState"),M=w.initialState,P=(0,Z.useState)(),W=I()(P,2),Q=W[0],B=W[1],pe=(0,Z.useState)("create"),ee=I()(pe,2),te=ee[0],xe=ee[1],ae=(0,Z.useState)(!1),we=I()(ae,2),Cn=we[0],fe=we[1],Zn=(0,Z.useState)(!1),Me=I()(Zn,2),Pe=Me[0],Rn=Me[1],Pn=(0,Z.useState)({}),Be=I()(Pn,2),je=Be[0],zn=Be[1],Dn=function(){var s=z()(p()().mark(function o(c){var j,h;return p()().wrap(function(F){for(;;)switch(F.prev=F.next){case 0:return F.next=2,k((0,Oe.service)("project-reactions").selectManyByID([c]).populateWith("project_routes",["main_tree","status","name"]).populateWith("project",["id"]).get());case 2:return j=F.sent,h=j.data,F.abrupt("return",h);case 5:case"end":return F.stop()}},o)}));return function(c){return s.apply(this,arguments)}}(),ye=function(){var s=z()(p()().mark(function o(c){return p()().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:return h.next=2,Dn(c).then(function(A){A!=null&&A.length&&(U(A[0]),L((0,Re.p6)(A[0].reaction)))});case 2:case"end":return h.stop()}},o)}));return function(c){return s.apply(this,arguments)}}(),In=function(){var s=z()(p()().mark(function o(c){var j;return p()().wrap(function(A){for(;;)switch(A.prev=A.next){case 0:B((0,Re.pK)(c,"".concat((M==null||(j=M.userInfo)===null||j===void 0?void 0:j.id)||-1))),xe("reference"),fe(!0);case 3:case"end":return A.stop()}},o)}));return function(c){return s.apply(this,arguments)}}(),En=function(){var s=z()(p()().mark(function o(){var c;return p()().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:B({smiles:(a==null?void 0:a.reaction)||"",procedure:"",material_table:[],reference_type:Re.Dx,created_by:"".concat((M==null||(c=M.userInfo)===null||c===void 0?void 0:c.id)||-1)}),xe("create"),fe(!0);case 3:case"end":return h.stop()}},o)}));return function(){return s.apply(this,arguments)}}(),Ln=function(){var s=z()(p()().mark(function o(c){return p()().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:B(c),xe("edit"),fe(!0);case 3:case"end":return h.stop()}},o)}));return function(c){return s.apply(this,arguments)}}(),Tn=function(){var s=z()(p()().mark(function o(c){var j,h,A,F;return p()().wrap(function(ie){for(;;)switch(ie.prev=ie.next){case 0:if(!(!a||!c.id)){ie.next=2;break}return ie.abrupt("return");case 2:return j=new Set(a.effective_procedures),j.delete(c.id),h=new Set(a.history_procedures),h.add(c.id),ie.next=8,(0,Oe.service)("project-reactions").update(a.id,{effective_procedures:Array.from(j.values()),history_procedures:Array.from(h.values())});case 8:A=ie.sent,F=A.data,F&&ye(i);case 11:case"end":return ie.stop()}},o)}));return function(c){return s.apply(this,arguments)}}();(0,Z.useEffect)(function(){ye(i)},[i]);var Se={key:"reaction-lib",label:(0,n.jsx)(n.Fragment,{children:(0,t.oz)("reaction-lib")}),children:b?(0,n.jsx)(an.Z,{reaction:b,renderer:function(o,c){var j;return(0,n.jsx)(pn,{procedure:o,isSame:c,onReference:In,projectId:(a==null||(j=a.project)===null||j===void 0?void 0:j.id)||0,reactionId:i})},actionSlot:T==="reaction-lib"?H.current:void 0}):null},He=(0,Z.useMemo)(function(){return tn(b,a,function(s,o){return(0,n.jsx)(Ne,{procedure:s,design:o,projectReactionId:i,onEdit:Ln,onDelete:Tn,onUpdated:function(j){ye(i),zn({}),j==="create"&&Y("my-experiment")}},o.id)},Pe)},[b,a,Pe]),We=(0,Z.useMemo)(function(){var s;return(0,Qe.z)(a==null||(s=a.project)===null||s===void 0?void 0:s.id,a==null?void 0:a.id,je)},[a,je]),An=(0,Z.useMemo)(function(){var s;return Ke(a==null?void 0:a.experimentNo,a==null||(s=a.project)===null||s===void 0?void 0:s.id,a==null?void 0:a.id,je)},[a,je]),Ye=(0,Z.useMemo)(function(){return(0,fn.H)(b)},[b]),Nn=(0,Z.useMemo)(function(){var s,o=[],c=u==null?void 0:u.authCodeList;return c.includes("reaction-detail.tab.reactionLib")&&o.push(Se),c.includes("reaction-detail.tab.myReactionDesign")&&o.push(He),c.includes("projects_reaction_experiment")&&o.push(We),c.includes("reaction-detail.tab.retroReaction")&&o.push(Ye),c.includes("reaction-detail.tab.analysisRecord")&&o.push(An),T||Y((s=o[0])===null||s===void 0?void 0:s.key),o},[u==null?void 0:u.authCodeList,Se,He,We,Ye]);(0,Z.useEffect)(function(){var s=x.get("experimentNo");s?O({tab:T,experimentNo:s},{replace:!0}):O({tab:T},{replace:!0})},[T]);var $n=(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)(J.Z,{children:[(0,n.jsxs)(G.Z,{md:8,lg:6,children:[(0,t.oz)("reaction-no"),": ",i,a!=null&&a.reaction?(0,n.jsx)(de.Z,{structure:a==null?void 0:a.reaction,clickToCheckDetail:!0,dbClickToCopy:!0}):"\u65E0\u53CD\u5E94\u5F0F"]}),(0,n.jsx)(G.Z,{md:16,lg:18,children:(0,n.jsx)($e.Z,{className:"route-table-root",ghost:!0,size:"small",search:!1,pagination:{defaultPageSize:5,showSizeChanger:!1,simple:!0},columns:[{title:(0,t.oz)("pages.reaction.label.name"),dataIndex:"name",render:function(o,c){return(0,n.jsx)(ue.ZP,{type:"link",onClick:function(){window.open("/route/view/".concat(c.id),"_blank")},children:o})}},{title:(0,t.oz)("pages.reaction.label.status"),dataIndex:"status",valueEnum:{editing:{text:(0,t.oz)("pages.reaction.statusLabel.editing")},confirmed:{text:(0,t.oz)("pages.reaction.statusLabel.confirmed")},finished:{text:(0,t.oz)("component.notification.statusValue.success")},canceled:{text:(0,t.oz)("pages.reaction.statusLabel.canceled")}}},{title:(0,t.oz)("pages.reaction.label.stepName"),render:function(o,c){var j=c.main_tree;if(!b||!j)return"-";var h=(0,Ze.at)(b,j),A=(0,Ze.e5)(j),F=h&&A.get(h);return F?(0,Ze.Um)(F[0],F[1]):"-"}}],headerTitle:(0,t.oz)("routes-citing"),showHeader:!0,dataSource:a==null||(f=a.project_routes)===null||f===void 0?void 0:f.filter(function(s){return s.status==="confirmed"})})})]})});return(0,n.jsx)(gn._z,{className:"reaction-page-root",content:$n,loading:V,fixedHeader:!1,tabList:Nn,tabProps:{destroyInactiveTabPane:!0},onTabChange:function(o){return Y(o)},tabActiveKey:T,tabBarExtraContent:(0,n.jsxs)(re.Z,{children:[T==="my-reaction-design"&&(0,n.jsxs)(n.Fragment,{children:[(u==null||(g=u.authCodeList)===null||g===void 0?void 0:g.includes("reaction-detail.myReactionDesign.newMyReaction"))&&(0,n.jsx)(ue.ZP,{type:"primary",size:"middle",onClick:En,children:(0,t.oz)("new-my-reaction")}),(0,n.jsx)(xn.Z,{checkedChildren:(0,t.oz)("all-my-reactions"),unCheckedChildren:(0,t.oz)("all-my-reactions"),checked:Pe,onChange:function(o){return Rn(o)}})]}),(0,n.jsx)("div",{ref:function(o){return H.current=o||void 0}})]}),children:a&&(0,n.jsx)(jn.Z,{projectReaction:a,procedure:Q,open:Cn,mode:te,onOpenChange:function(o){fe(o),o||B(void 0)},onCreated:function(){var s=z()(p()().mark(function o(c){return p()().wrap(function(h){for(;;)switch(h.prev=h.next){case 0:return fe(!1),h.next=3,ye(c.id);case 3:te==="reference"&&Y("my-reaction-design");case 4:case"end":return h.stop()}},o)}));return function(o){return s.apply(this,arguments)}}()})})},bn=yn},49633:function(){},63125:function(){},15394:function(ze,X,r){"use strict";var oe,p=r(64836).default,se=r(75263).default;oe={value:!0},X.Z=void 0;var z=se(r(67294)),me=p(r(18475)),I=r(13594),le=p(r(28460)),ce=function(y,S){var C={};for(var t in y)Object.prototype.hasOwnProperty.call(y,t)&&S.indexOf(t)<0&&(C[t]=y[t]);if(y!=null&&typeof Object.getOwnPropertySymbols=="function")for(var m=0,t=Object.getOwnPropertySymbols(y);m<t.length;m++)S.indexOf(t[m])<0&&Object.prototype.propertyIsEnumerable.call(y,t[m])&&(C[t[m]]=y[t[m]]);return C};const de=(y,S)=>{var{ellipsis:C}=y,t=ce(y,["ellipsis"]);const m=z.useMemo(()=>C&&typeof C=="object"?(0,me.default)(C,["expandable","rows"]):C,[C]);return z.createElement(le.default,Object.assign({ref:S},t,{ellipsis:m,component:"span"}))};var be=X.Z=z.forwardRef(de)},34528:function(ze,X,r){"use strict";var oe,p=r(64836).default,se=r(75263).default;oe={value:!0},X.Z=void 0;var z=se(r(67294)),me=r(13594),I=p(r(28460)),le=function(y,S){var C={};for(var t in y)Object.prototype.hasOwnProperty.call(y,t)&&S.indexOf(t)<0&&(C[t]=y[t]);if(y!=null&&typeof Object.getOwnPropertySymbols=="function")for(var m=0,t=Object.getOwnPropertySymbols(y);m<t.length;m++)S.indexOf(t[m])<0&&Object.prototype.propertyIsEnumerable.call(y,t[m])&&(C[t[m]]=y[t[m]]);return C};const ce=[1,2,3,4,5],de=z.forwardRef((y,S)=>{const{level:C=1}=y,t=le(y,["level"]),m=ce.includes(C)?`h${C}`:"h1";return z.createElement(I.default,Object.assign({ref:S},t,{component:m}))});var be=X.Z=de}}]);

//# sourceMappingURL=p__reaction__index.0672e644.async.js.map