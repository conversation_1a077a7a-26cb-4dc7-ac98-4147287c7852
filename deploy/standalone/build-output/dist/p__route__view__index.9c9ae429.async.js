(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[7959],{78352:function(h,g,e){"use strict";e.r(g),e.d(g,{default:function(){return Ce}});var ee=e(15009),R=e.n(ee),te=e(99289),B=e.n(te),ae=e(5574),s=e.n(ae),ne=e(49677),re=e.n(ne),se=e(10784),oe=e(58568),ue=e(19659),ie=e(61487),ce=e(82869),de=e(11005),U=e(87172),p=e(32884),le=e(11774),m=e(70831),ve=e(31418),fe=e(42075),pe=e(96486),r=e(67294),Se=e(45857),he=e(72502),me=e(13330),ge=e(66687),Re=e(49993),l=e(64134),i=e(85893),je=function(Ee){var P,I,N;re()(Ee);var ye=(0,m.useModel)("commend"),xe=ye.cacheCurReactionStepNo,Me=(0,m.useParams)(),F=Me.routeId,Pe=F===void 0?"":F,Ie=(0,m.useSearchParams)(),Ne=s()(Ie,1),Te=Ne[0],T=Te.get("step")||"",j=Number.parseInt(Pe),$e=ve.Z.useApp(),L=$e.notification,De=(0,r.useState)(),Z=s()(De,2),ze=Z[0],Ae=Z[1],Be=(0,r.useState)(),Y=s()(Be,2),Ue=Y[0],Fe=Y[1],Le=(0,r.useState)(),G=s()(Le,2),c=G[0],V=G[1],Ze=(0,r.useState)(),W=s()(Ze,2),X=W[0],Ye=W[1],Ge=(0,r.useState)(),H=s()(Ge,2),C=H[0],Ve=H[1],We=(0,r.useState)(),J=s()(We,2),$=J[0],Xe=J[1],He=(0,r.useState)(!1),K=s()(He,2),Je=K[0],O=K[1],Ke=(0,r.useState)(),Q=s()(Ke,2),D=Q[0],Oe=Q[1],Qe=(0,ie.f)(),be=Qe.fetch,we=(0,ue.b)(function(t){return t.setExportFileName}),ke=(0,r.useState)({}),b=s()(ke,2),qe=b[0],z=b[1],S=(0,m.useAccess)(),_e=(0,ce.R1)(),et=_e.setting,tt=(0,r.useState)({}),w=s()(tt,2),at=w[0],A=w[1],nt=(0,r.useState)({}),k=s()(nt,2),rt=k[0],q=k[1],E=(0,ge.M)(X),y=E.onSelectReaction,x=E.selectedReaction,v=E.selectedNode,st=E.selectedMainReaction,ot=(0,l.e5)(c),ut=function(a){var n=(a==null?void 0:a.id)&&ot.get(a.id);if(n){var o=(0,l.Um)(n[0],n[1]);return xe(o),"".concat((0,p.oz)("reaction")," ").concat(o)}return(0,p.oz)("reaction")},_=(0,Re.X)((0,l.An)(c),$),it=_.map,ct=_.add,dt=(0,l.vE)(q,X,x),lt=function(){var t=B()(R()().mark(function a(n){var o,d,M;return R()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return u.next=2,(0,U.service)("project-routes/".concat(n)).select().populateDeep([{path:"project_compound",children:[{key:"project",fields:["id"]}]}]).get();case 2:if(o=u.sent,d=o.data,M=o.error,!M){u.next=9;break}L.error({message:M.message}),u.next=14;break;case 9:if(d){u.next=13;break}L.error({message:"Project route with id ".concat(n," not found")}),u.next=14;break;case 13:return u.abrupt("return",d);case 14:return u.abrupt("return",null);case 15:case"end":return u.stop()}},a)}));return function(n){return t.apply(this,arguments)}}(),vt=function(){var t=B()(R()().mark(function a(n){var o,d;return R()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:return f.next=2,be((0,U.service)("project-compounds").selectManyByID([n]).populateWith("project",["id"]).get());case 2:return o=f.sent,d=o.data,f.abrupt("return",d==null?void 0:d[0]);case 5:case"end":return f.stop()}},a)}));return function(n){return t.apply(this,arguments)}}();return(0,r.useEffect)(function(){lt(j).then(function(t){if(t){var a;Oe(t),V(t.main_tree),Ve((a=t.project_compound)===null||a===void 0?void 0:a.id),we("".concat((0,p.oz)("Routes")).concat((0,p.Ig)()?" ":"").concat(t.name||""))}})},[j]),(0,r.useEffect)(function(){if(c&&T){var t=(0,l.at)((0,de.p6)(T),c)||"";A({select:t})}},[c,T]),(0,r.useEffect)(function(){C&&vt(C).then(function(t){var a,n;Fe(t==null?void 0:t.status);var o=t==null||(a=t.project)===null||a===void 0?void 0:a.id,d=t==null||(n=t.project)===null||n===void 0?void 0:n.status;Ae(d),o&&Xe(o)})},[C]),Number.isNaN(j)?(m.history.push("/404"),(0,i.jsx)(i.Fragment,{})):Je?(0,i.jsx)(me.default,{projectId:$,mainTree:(0,pe.cloneDeep)(c),onCancel:function(){O(!1),z({}),y(!1),q({})},retroBackboneId:j,compoundId:C}):(0,i.jsxs)(le._z,{className:"route-view-root",children:[(0,i.jsx)("div",{className:"container",children:c&&(0,i.jsx)(oe.$t,{root:c,getRouteEvent:qe,editMode:!1,onTreeChange:function(a){Ye(a),v!=null&&v.id&&y(v.id,a)},onSelectRxn:y,updateChildrenEvent:rt,selectRxnEvent:at,rxnYieldMap:(P=et.retro)!==null&&P!==void 0&&P.route_show_yields?it:void 0,rightTopSlot:(0,i.jsxs)(fe.Z,{size:"middle",children:[(S==null||(I=S.authCodeList)===null||I===void 0?void 0:I.includes("view-by-backbone.button.export"))&&(0,i.jsx)(Se.Z,{}),S!=null&&(N=S.authCodeList)!==null&&N!==void 0&&N.includes("view-by-backbone.button.edit")&&!(0,p.u9)(ze,Ue)&&["editing","confirmed"].includes((D==null?void 0:D.status)||"")?(0,i.jsx)(se.Z,{onClick:function(){z({getRoute:function(n){V(n),O(!0),z({})}})},size:"small",type:"primary",children:(0,p.oz)("edit")}):""]})})}),(0,i.jsx)(he.Z,{projectId:$,reaction:x,title:ut(v),onClose:function(){return A({})},mainReaction:st,onUpdate:function(){return x&&ct((0,l.zq)(x))},onSelectProcedure:dt,navigateConfig:c&&v&&(0,l.Ah)(c,v,function(t){y(t.id),A({select:t.id})})})]})},Ce=je},49677:function(h){function g(e){if(e==null)throw new TypeError("Cannot destructure "+e)}h.exports=g,h.exports.__esModule=!0,h.exports.default=h.exports}}]);

//# sourceMappingURL=p__route__view__index.9c9ae429.async.js.map