(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[7559],{6307:function(M,P,t){"use strict";t.r(P),t.d(P,{default:function(){return Ke}});var ke=t(15009),p=t.n(ke),Me=t(99289),j=t.n(Me),Pe=t(5574),d=t.n(Pe),xe=t(49677),Ne=t.n(xe),x=t(10784),Te=t(59652),we=t(58568),Le=t(19659),De=t(61487),Q=t(82869),Z=t(87172),l=t(32884),Ze=t(11774),E=t(70831),$e=t(31418),Ae=t(71471),Fe=t(42075),X=t(96486),c=t(67294),Be=t(45857),Oe=t(72502),Ue=t(13330),Ye=t(66687),We=t(46293),Ge=t(49993),h=t(64134),i=t(85893),Ve=function(Qe){var $,A,F,B;Ne()(Qe);var Xe=(0,E.useParams)(),H=Xe.backboneId,He=H===void 0?"":H,O=$e.Z.useApp(),U=O.message,J=O.notification,q=O.modal,Je=(0,c.useState)(null),_=d()(Je,2),f=_[0],Y=_[1],qe=(0,c.useState)([]),ee=d()(qe,2),W=ee[0],_e=ee[1],et=(0,c.useState)(),te=d()(et,2),ne=te[0],ae=te[1],tt=(0,c.useState)(!1),re=d()(tt,2),G=re[0],oe=re[1],nt=(0,c.useState)("0"),se=d()(nt,2),I=se[0],at=se[1],rt=(0,c.useState)(),ue=d()(rt,2),z=ue[0],ot=ue[1],st=(0,c.useState)(),ce=d()(st,2),ie=ce[0],ut=ce[1],ct=(0,c.useState)(),le=d()(ct,2),V=le[0],it=le[1],lt=(0,c.useState)({}),de=d()(lt,2),dt=de[0],ve=de[1],vt=(0,c.useState)(),pe=d()(vt,2),N=pe[0],pt=pe[1],ft=(0,c.useState)(),fe=d()(ft,2),mt=fe[0],ht=fe[1],St=(0,c.useState)(),me=d()(St,2),gt=me[0],bt=me[1],yt=(0,Le.b)(function(o){return o.setExportFileName}),Ct=(0,De.f)(),jt=Ct.fetch,b=(0,E.useModel)("commend"),Rt=b.getProfileInfo,Et=b.getCommonExpression,he=b.showLauncher,It=b.sendMessage,Se=b.reload,zt=b.finishedReload,kt=b.cacheCurReactionStepNo,ge=b.isOpen,Mt=(0,E.useLocation)(),Pt=Mt.pathname;(0,c.useEffect)(function(){return Et("retro-backbone"),function(){ge&&he()}},[]);var xt=(0,Q.R1)(),Nt=xt.setting,Tt=(0,We.t)(N,z,f==null?void 0:f.id),wt=(0,c.useState)(W[0]),be=d()(wt,2),y=be[0],K=be[1],S=(0,E.useAccess)(),Lt=(0,c.useState)({}),ye=d()(Lt,2),Dt=ye[0],T=ye[1],Zt=(0,c.useState)({}),Ce=d()(Zt,2),$t=Ce[0],je=Ce[1],w=(0,Ye.M)(ne),L=w.onSelectReaction,D=w.selectedReaction,R=w.selectedNode,At=w.selectedMainReaction,Ft=(0,h.e5)(y),Bt=function(n){var e=(n==null?void 0:n.id)&&Ft.get(n.id);if(e){var s=(0,h.Um)(e[0],e[1]);return kt(s),"".concat((0,l.oz)("reaction")," ").concat(s)}return(0,l.oz)("reaction")},Re=(0,Ge.X)((0,h.An)(y),N),Ot=Re.map,Ut=Re.add,g=Number.parseInt(He),Ee=function(){var o=j()(p()().mark(function n(e){var s,r,v,u;return p()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,(0,Z.service)("retro-backbones/".concat(e),{customQuery:"comment=true&collectionId=".concat(e,"-").concat(Number(I)+1)}).select(["main_trees","full_trees"]).populateDeep([{path:"retro_process",fields:["id"],children:[{key:"project_compound",fields:["id"]}]}]).get();case 2:if(s=a.sent,r=s.data,v=s.error,!v){a.next=9;break}U.error(v.message),a.next=19;break;case 9:if(r){a.next=13;break}U.error("Retro backbone with id ".concat(e," not found")),a.next=19;break;case 13:if(u=r,u.main_trees.length){a.next=18;break}U.error("Retro backbone with id ".concat(e," has no main_trees")),a.next=19;break;case 18:return a.abrupt("return",u);case 19:return a.abrupt("return",null);case 20:case"end":return a.stop()}},n)}));return function(e){return o.apply(this,arguments)}}(),Yt=function(){var o=j()(p()().mark(function n(e){var s,r;return p()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return u.next=2,jt((0,Z.service)("project-compounds").selectManyByID([e]).populateWith("project",["id"]).get());case 2:return s=u.sent,r=s.data,u.abrupt("return",r==null?void 0:r[0]);case 5:case"end":return u.stop()}},n)}));return function(e){return o.apply(this,arguments)}}(),Ie=function(){var o=j()(p()().mark(function n(){var e,s,r,v,u;return p()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:if(z){a.next=2;break}return a.abrupt("return");case 2:return a.next=4,Yt(z);case 4:r=a.sent,it(r),bt(r==null?void 0:r.status),v=r==null||(e=r.project)===null||e===void 0?void 0:e.id,u=r==null||(s=r.project)===null||s===void 0?void 0:s.status,ht(u),v&&pt(v);case 11:case"end":return a.stop()}},n)}));return function(){return o.apply(this,arguments)}}(),Wt=function(){var o=j()(p()().mark(function n(){var e,s,r;return p()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return u.next=2,(0,Z.service)("project-compounds/".concat(z,"/select"),{method:"post",data:{backboneId:g,selectedMainTreeIndex:Number.parseInt(I)}}).select().get();case 2:return e=u.sent,s=e.data,u.next=6,Ie();case 6:if(r=s==null?void 0:s[0],r){u.next=9;break}return u.abrupt("return");case 9:J.success({type:"success",message:(0,l.oz)("replaced-route-success"),duration:3,description:(0,i.jsx)(Ae.Z.Link,{onClick:function(){var a,m;J.destroy(),E.history.push("/projects/".concat((a=r.project_compound)===null||a===void 0||(a=a.project)===null||a===void 0?void 0:a.id,"/compound/").concat((m=r.project_compound)===null||m===void 0?void 0:m.id,"/edit/").concat(r.id))},children:(0,l.oz)("continue-edit")})});case 10:case"end":return u.stop()}},n)}));return function(){return o.apply(this,arguments)}}(),Gt=function(){var o=j()(p()().mark(function n(){return p()().wrap(function(s){for(;;)switch(s.prev=s.next){case 0:q.confirm({title:(0,l.oz)("choose-route"),content:(0,l.oz)("choose-route-tip"),onOk:Wt});case 1:case"end":return s.stop()}},n)}));return function(){return o.apply(this,arguments)}}();(0,c.useEffect)(function(){Promise.all([Ee(g),(0,Q.Y1)()]).then(function(o){var n=d()(o,2),e=n[0],s=n[1];if(e){var r,v,u,C,a=e.main_trees.map(function(m){return(0,h.ZP)(m)});(s==null||(r=s.retro)===null||r===void 0?void 0:r.route_detail_show_policy)==="all_route"&&((v=e.full_trees)===null||v===void 0?void 0:v.length)===((u=e.main_trees)===null||u===void 0?void 0:u.length)&&(a=e.full_trees.map(function(m){return(0,h.ZP)(m)})),Y(e),ot((C=e.retro_process.project_compound)===null||C===void 0?void 0:C.id),ut(e.retro_process.id),_e(a),K(a[0])}})},[g]);var Vt=(0,h.vE)(ve,ne,D),ze=function(){Y(null),Ee(g).then(function(n){n&&Y(n)})};if((0,c.useEffect)(function(){Se&&(ze(),zt())},[Se]),(0,c.useEffect)(function(){ze(),yt("".concat((0,l.oz)("Routes")," ").concat(g,"-").concat(Number.parseInt(I)+1))},[I]),(0,c.useEffect)(function(){Ie()},[z]),Number.isNaN(g))return E.history.push("/404"),(0,i.jsx)(i.Fragment,{});if(G)return(0,i.jsx)(Ue.default,{projectId:N,retroProcessId:ie,mainTree:(0,X.cloneDeep)(y),onCancel:function(){L(!1),T({}),ve({}),oe(!1),ae((0,X.cloneDeep)(y))},retroBackboneId:g,compoundId:z});var Kt=Pt.includes("/playground");return(0,i.jsxs)(Ze._z,{className:"route-view-by-backbone-root",title:(0,l.oz)("menu.list.route.".concat(G?"edit":"view")),header:{title:(0,l.oz)("menu.list.route.".concat(G?"edit":"view"))},tabList:W.map(function(o,n){return{tab:"".concat(g,"-").concat(n+1),key:"".concat(n)}}),tabProps:{type:"card"},tabActiveKey:I,onTabChange:function(n){at(n),K(W[Number.parseInt(n)])},children:[(0,i.jsx)("div",{className:"container",children:y&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(we.$t,{root:y,getRouteEvent:Dt,onTreeChange:function(n){ae(n),R!=null&&R.id&&L(R.id,n)},editMode:!1,onSelectRxn:L,selectRxnEvent:$t,updateChildrenEvent:dt,rxnYieldMap:($=Nt.retro)!==null&&$!==void 0&&$.route_show_yields?Ot:void 0,rightTopSlot:(0,i.jsxs)(Fe.Z,{size:"middle",children:[(S==null||(A=S.authCodeList)===null||A===void 0?void 0:A.includes("view-by-backbone.button.export"))&&(0,i.jsx)(Be.Z,{}),!(0,l.u9)(mt,gt)&&!Kt&&((V==null?void 0:V.type)==="temp_block"?(0,i.jsx)(x.Z,{onClick:Gt,size:"small",type:"primary",children:(0,l.oz)("choose")}):(0,i.jsxs)(i.Fragment,{children:[(S==null||(F=S.authCodeList)===null||F===void 0?void 0:F.includes("view-by-backbone.button.comment"))&&(0,i.jsx)(x.Z,{onClick:function(){Rt({_commendSuject:{id:"".concat(g,"-").concat(Number(I)+1)},collection_class:"retro-backbone"})},size:"small",type:"primary",children:f!=null&&f.content_count&&(f==null?void 0:f.content_count)>0?"".concat((0,l.oz)("comment"),"\uFF08").concat(f==null?void 0:f.content_count,"\uFF09"):(0,l.oz)("comment")}),(S==null||(B=S.authCodeList)===null||B===void 0?void 0:B.includes("view-by-backbone.button.edit"))&&(0,i.jsx)(x.Z,{onClick:function(){return T({getRoute:function(e){e&&K(e),oe(!0),T({})}})},size:"small",type:"primary",children:(0,l.oz)("edit")}),(0,i.jsx)(x.Z,{onClick:j()(p()().mark(function o(){return p()().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",new Promise(function(s,r){q.confirm({title:(0,l.oz)("confirm-the-route"),content:(0,l.oz)("route-confirm"),onOk:function(){return T({getRoute:function(){var u=j()(p()().mark(function a(m){return p()().wrap(function(k){for(;;)switch(k.prev=k.next){case 0:if(m){k.next=2;break}return k.abrupt("return");case 2:return k.next=4,Tt((0,h.ZP)(m),"confirm");case 4:s();case 5:case"end":return k.stop()}},a)}));function C(a){return u.apply(this,arguments)}return C}()})},onCancel:function(){return r()}})}));case 1:case"end":return e.stop()}},o)})),size:"small",type:"primary",children:(0,l.oz)("pages.route.edit.label.confirm")})]}))]})}),(0,i.jsx)(Te.Z,{onMessageWasSent:It,hiddenLauncher:he,isOpen:ge})]})}),(0,i.jsx)(Oe.Z,{projectId:N,reaction:D,title:Bt(R),onClose:function(){return je({})},retroProcessId:ie,onSelectProcedure:Vt,mainReaction:At,onUpdate:function(){return D&&Ut((0,h.zq)(D))},navigateConfig:y&&R&&(0,h.Ah)(y,R,function(o){L(o.id),je({select:o.id})})})]})},Ke=Ve},49677:function(M){function P(t){if(t==null)throw new TypeError("Cannot destructure "+t)}M.exports=P,M.exports.__esModule=!0,M.exports.default=M.exports}}]);

//# sourceMappingURL=p__route__view-by-backbone__index.c56edb41.async.js.map