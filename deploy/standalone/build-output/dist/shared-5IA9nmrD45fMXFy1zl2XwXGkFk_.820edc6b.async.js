"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[274],{32222:function(be,X,e){e.d(X,{Z:function(){return E}});var q=e(15009),p=e.n(q),k=e(99289),I=e.n(k),_=e(97857),D=e.n(_),ee=e(5574),R=e.n(ee),se=e(42282),H=e(43851),re=e(69776),N=e(64134),i=e(32884),t=e(34994),T=e(5155),g=e(24739),J=e(64317),Q=e(5966),$=e(31199),te=e(98138),w=e(67294),ae={"filter-form-root":"filter-form-root___idETP","expand-btn-wrapper":"expand-btn-wrapper___mC0Md","confirm-col":"confirm-col___fQPHq","re-retro-btn":"re-retro-btn___chR6c"},s=e(85893);function E(a){var x=te.Z.useForm(),G=R()(x,1),j=G[0],B=(0,re.Z)(),O=B.reactionRoleOptions,ne=(0,w.useState)([]),f=R()(ne,2),r=f[0],Y=f[1];(0,w.useEffect)(function(){var z,y=a==null||(z=a.material_table)===null||z===void 0?void 0:z.filter(function(c){return(c==null?void 0:c.role)!=="product"});Y(y)},[a==null?void 0:a.material_table]),(0,w.useEffect)(function(){r&&j.setFieldsValue({material_table:r})},[r]);var L=location.pathname.includes("experimental-procedure/conclusion"),ce=function(y){return y.map(function(c){return D()(D()({},c),{},{disabled:(0,N.J3)(c==null?void 0:c.value)})})};return(0,s.jsx)(t.A,{submitter:a!=null&&a.enableAdd?{onSubmit:function(){var z=I()(p()().mark(function c(){var P;return p()().wrap(function(C){for(;;)switch(C.prev=C.next){case 0:return C.next=2,j.validateFields();case 2:P=C.sent,a==null||a.updateMaterial(P==null?void 0:P.material_table);case 4:case"end":return C.stop()}},c)}));function y(){return z.apply(this,arguments)}return y}(),resetButtonProps:{style:{display:"none"}}}:!1,form:j,children:(0,s.jsx)(T.u,{name:"material_table",label:(0,i.oz)("material-sheet"),deleteIconProps:a==null?void 0:a.enableAdd,creatorButtonProps:a!=null&&a.enableAdd?{creatorButtonText:(0,i.oz)("add-raw-materials")}:!1,copyIconProps:!1,actionRender:function(y,c,P){var M=j.getFieldValue("material_table")[y.name];return(0,N.J3)(M.role)?[]:P},children:function(y){var c=j.getFieldValue("material_table")[y.name],P=(0,N.J3)(c.role),M=!(a!=null&&a.enableAdd);return(0,s.jsxs)(g.UW,{children:[(0,s.jsx)(J.Z,{disabled:M||P,name:"role",label:(0,i.oz)("role"),width:130,options:ce(O.filter(function(C){return C.value!=="product"})),required:!0,rules:[{required:!0}]}),(0,s.jsx)(Q.Z,{name:"no",width:90,label:(0,i.oz)("material-ID"),disabled:M||P}),(0,s.jsx)(Q.Z,{name:"name",label:(0,i.oz)("substance-name"),width:140,disabled:M}),(0,s.jsx)(t.A.Item,{className:ae["filter-form-root"],name:"smiles",label:(0,i.oz)("structural"),required:!0,rules:[{required:!0}],children:(0,s.jsx)(se.Z,{disabled:M||P,multiple:!1})}),(0,s.jsx)($.Z,{name:"equivalent",width:185,label:(0,i.oz)("EWR"),disabled:M,required:!0,rules:[{required:!0},{pattern:/^(?!0*(\.0{1,2})?$)\d+(\.\d{1,2})?$/,message:(0,i.oz)("enter-two-decimal")}]}),L?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)($.Z,{name:"value",label:(0,i.oz)("expected-mass"),required:!0,disabled:!0,rules:[{required:!0},{pattern:/^(?!0*(\.0{1,2})?$)\d+(\.\d{1,2})?$/,message:(0,i.oz)("enter-two-decimal")}]}),(0,s.jsx)($.Z,{name:"real_value",label:(0,i.oz)("actual-mass"),required:!0,disabled:!0,rules:[{required:!0},{pattern:/^(?!0*(\.0{1,2})?$)\d+(\.\d{1,2})?$/,message:(0,i.oz)("enter-two-decimal")}]})]}):"",(0,s.jsx)(J.Z,{name:"unit",label:(0,i.oz)("unit"),width:130,disabled:L||M,options:H.Pt,required:!0,rules:[{required:!0}]})]},"group")}})})}},50187:function(be,X,e){e.d(X,{Z:function(){return Ue}});var q=e(15009),p=e.n(q),k=e(97857),I=e.n(k),_=e(99289),D=e.n(_),ee=e(5574),R=e.n(ee),se=e(42282),H=e(43851),re=e(69776),N=e(72035),i=e(37507),t=e(32884),T=e(1413),g=e(67294),J={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M251.2 387H320v68.8c0 1.8 1.8 3.2 4 3.2h48c2.2 0 4-1.4 4-3.3V387h68.8c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H376v-68.8c0-1.8-1.8-3.2-4-3.2h-48c-2.2 0-4 1.4-4 3.2V331h-68.8c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm328 0h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm0 265h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm0 104h193.6c1.8 0 3.2-1.8 3.2-4v-48c0-2.2-1.4-4-3.3-4H579.2c-1.8 0-3.2 1.8-3.2 4v48c0 2.2 1.4 4 3.2 4zm-195.7-81l61.2-74.9c4.3-5.2.7-13.1-5.9-13.1H388c-2.3 0-4.5 1-5.9 2.9l-34 41.6-34-41.6a7.85 7.85 0 00-5.9-2.9h-50.9c-6.6 0-10.2 7.9-5.9 13.1l61.2 74.9-62.7 76.8c-4.4 5.2-.8 13.1 5.8 13.1h50.8c2.3 0 4.5-1 5.9-2.9l35.5-43.5 35.5 43.5c1.5 1.8 3.7 2.9 5.9 2.9h50.8c6.6 0 10.2-7.9 5.9-13.1L383.5 675zM880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-36 732H180V180h664v664z"}}]},name:"calculator",theme:"outlined"},Q=J,$=e(84089),te=function(n,o){return g.createElement($.Z,(0,T.Z)((0,T.Z)({},n),{},{ref:o,icon:Q}))},w=g.forwardRef(te),ae=w,s=e(37476),E=e(24739),a=e(5966),x=e(64317),G=e(5155),j=e(34994),B=e(31199),O=e(97462),ne=e(45987),f=e(92755),r=e(85893),Y=["fieldProps","request","params","proFieldProps"],L=function(n,o){var h=n.fieldProps,A=n.request,d=n.params,U=n.proFieldProps,V=(0,ne.Z)(n,Y);return(0,r.jsx)(f.Z,(0,T.Z)({valueType:"segmented",fieldProps:h,ref:o,request:A,params:d,filedConfig:{customLightMode:!0},proFieldProps:U},V))},ce=g.forwardRef(L),z=ce,y=e(86190),c=e(66758),P=["fieldProps","proFieldProps"],M="dateRange",C=g.forwardRef(function(S,n){var o=S.fieldProps,h=S.proFieldProps,A=(0,ne.Z)(S,P),d=(0,g.useContext)(c.Z);return(0,r.jsx)(f.Z,(0,T.Z)({ref:n,fieldProps:(0,T.Z)({getPopupContainer:d.getPopupContainer},o),valueType:M,proFieldProps:h,filedConfig:{valueType:M,customLightMode:!0,lightFilterLabelFormatter:function(V){return(0,y.c)(V,(o==null?void 0:o.format)||"YYYY-MM-DD")}}},A))}),Ce=C,Se=e(31418),Ae=e(98138),xe=e(28036),We=e(42075),Ie=e(83062),ue=e(96486),$e=function(){var S=D()(p()().mark(function n(o){var h;return p()().wrap(function(d){for(;;)switch(d.prev=d.next){case 0:return d.next=2,(0,i.ff)({data:o});case 2:if(h=d.sent,console.log(h),!(0,i.y6)(h).ok){d.next=6;break}return d.abrupt("return",h.data);case 6:return d.abrupt("return",{});case 7:case"end":return d.stop()}},n)}));return function(o){return S.apply(this,arguments)}}(),Be=function(n){var o=(0,g.useState)({}),h=R()(o,2),A=h[0],d=h[1],U=function(){var V=D()(p()().mark(function W(K){return p()().wrap(function(F){for(;;)switch(F.prev=F.next){case 0:return F.t0=d,F.next=3,$e(K);case 3:F.t1=F.sent,(0,F.t0)(F.t1);case 5:case"end":return F.stop()}},W)}));return function(K){return V.apply(this,arguments)}}();return(0,g.useEffect)(function(){U(n)},[]),{molWeightMap:A}},Le=function(n){var o=n.projectId,h=n.projectReactionId,A=n.experiementDesignNo,d=n.open,U=n.setOpen,V=n.triggerCom,W=n.materialTable,K=n.experiment,oe=n.onSuccess,F=Se.Z.useApp(),Ve=F.message,Ke=(0,re.Z)(),He=Ke.reactionRoleOptions,Ne=Ae.Z.useForm(),we=R()(Ne,1),le=we[0],Ge=(0,g.useState)(!1),je=R()(Ge,2),ve=je[0],Pe=je[1],Me=(0,N.H)(o||0),Ee=Me.members,Ye=Me.loading,ye=!!K,pe="pages.experimentDesign.label.".concat(ye?"update":"new","Experiment"),Je=K==null?void 0:K.id,Qe=Be((W==null?void 0:W.map(function(v){return v.smiles}).filter(function(v){return!!v}))||[]),Fe=Qe.molWeightMap,Xe=function(){var l=K||{experiment_type:"test",priority:"P1",material_table:(W==null?void 0:W.filter(function(u){return u.role!=="product"}))||[]};(0,ue.update)(l,"experiment_owner",function(u){var Z=Number.parseInt(u);if(!Number.isNaN(Z))return Z}),le.setFieldsValue(l)},de=function(l){U==null||U(l),l&&Xe()},qe=function(){var v=D()(p()().mark(function l(u){var Z,b;return p()().wrap(function(m){for(;;)switch(m.prev=m.next){case 0:if(Z=I()(I()({},u),{},{id:Je,project_no:"".concat(o),project_reaction_id:h,experiment_design_no:A}),!ye){m.next=7;break}return m.next=4,(0,i.wk)({data:Z});case 4:b=m.sent,m.next=10;break;case 7:return m.next=9,(0,i.zW)({data:Z});case 9:b=m.sent;case 10:if(!(0,i.y6)(b).ok){m.next=15;break}return Ve.success((0,r.jsxs)(r.Fragment,{children:[(0,t.oz)(pe),(0,t.oz)("app.general.message.success")]})),de==null||de(!1),oe==null||oe(),m.abrupt("return",!0);case 15:return m.abrupt("return",!1);case 16:case"end":return m.stop()}},l)}));return function(u){return v.apply(this,arguments)}}(),ke=function(){var v=D()(p()().mark(function l(u){var Z,b,fe,m,Oe,he,ze,ge,Ze,De;return p()().wrap(function(ie){for(;;)switch(ie.prev=ie.next){case 0:return Z=u.weight,b=u.weight_unit,fe=u.smiles,m=le.getFieldValue("material_table"),Pe(!0),ie.next=5,(0,i.j9)({data:{material_table:m,input_smiles:fe,weight:Z,weight_unit:b}}).finally(function(){return Pe(!1)});case 5:Oe=ie.sent,he=Oe.data,ze=he===void 0?{}:he,ge=ze.material_dict,Ze=ge===void 0?{}:ge,De=m.map(function(me){var Re=Ze[me.smiles]||{},Te=Re.weight,_e=Re.weight_unit;return(0,ue.isNil)(Te)?me:I()(I()({},me),{},{weight:Te,weight_unit:_e||me.weight_unit})}),le.setFieldValue("material_table",De),le.validateFields();case 13:case"end":return ie.stop()}},l)}));return function(u){return v.apply(this,arguments)}}();return o&&h&&A?(0,r.jsxs)(s.Y,{className:"create-form-root",form:le,open:d,onOpenChange:de,title:(0,t.oz)(pe),trigger:(0,ue.isNil)(d)?V||(0,r.jsx)(xe.ZP,{type:"link",children:(0,t.oz)(pe)}):void 0,autoFocusFirstInput:!0,width:1140,onFinish:qe,modalProps:{destroyOnClose:!0,centered:!0},children:[(0,r.jsxs)(E.UW,{children:[(0,r.jsx)(a.Z,{width:"lg",name:"name",label:(0,t.oz)("pages.experiment.label.name")}),(0,r.jsx)(x.Z,{width:"lg",name:"experiment_type",label:(0,t.oz)("pages.experiment.label.type"),required:!0,rules:[{required:!0}],valueEnum:{test:(0,t.oz)("pages.experimentDesign.label.test"),scale_up:(0,t.oz)("pages.experimentDesign.label.scale_up")}})]},"1"),(0,r.jsxs)(E.UW,{children:[(0,r.jsx)(x.Z,{width:"lg",name:"priority",label:(0,t.oz)("pages.experiment.label.priority"),required:!0,rules:[{required:!0}],options:H.jY}),(0,r.jsx)(x.Z,{width:"lg",name:"experiment_owner",transform:function(l){var u=typeof l=="number"?l:l.value;return{experiment_owner:"".concat(u)}},label:(0,t.oz)("pages.experiment.label.personInCharge"),disabled:Ye,options:Ee,initialValue:Ee[0],required:!0,rules:[{required:!0}]})]},"2"),(0,r.jsx)(G.u,{name:"material_table",label:(0,t.oz)("material-sheet"),copyIconProps:!1,deleteIconProps:!1,creatorButtonProps:!1,children:(0,r.jsxs)(E.UW,{children:[(0,r.jsx)(x.Z,{disabled:!0,name:"role",label:(0,t.oz)("role"),width:"xs",options:He.filter(function(v){return v.value!=="product"}),required:!0,rules:[{required:!0}]}),(0,r.jsx)(a.Z,{disabled:!0,width:"xs",name:"no",label:(0,t.oz)("material-ID"),required:!0,rules:[{required:!0}]}),(0,r.jsx)(j.A.Item,{className:"filter-form-root",name:"smiles",label:(0,t.oz)("structural"),required:!0,rules:[{required:!0}],children:(0,r.jsx)(se.Z,{disabled:!0,multiple:!1})}),(0,r.jsx)(B.Z,{width:"xs",name:"equivalent",label:(0,t.oz)("EWR"),disabled:!0,required:!0,rules:[{required:!0}]}),(0,r.jsx)(x.Z,{width:"xs",name:"unit",label:(0,t.oz)("unit"),disabled:!0,options:H.Pt,initialValue:"eq",required:!0,rules:[{required:!0}]}),(0,r.jsx)(O.Z,{name:["smiles"],children:function(l){var u=l.smiles;return(0,r.jsx)(B.Z,{width:"xs",label:(0,t.oz)("molecular-mass"),disabled:!0,fieldProps:{value:Fe[u]&&(0,ue.round)(Fe[u],2),placeholder:"..."}})}}),(0,r.jsx)(j.A.Item,{label:(0,t.oz)("Mass"),required:!0,children:(0,r.jsxs)(We.Z.Compact,{children:[(0,r.jsx)(B.Z,{width:"xs",name:"weight",required:!0,disabled:ve,rules:[{required:!0}]}),(0,r.jsx)(x.Z,{width:"xs",name:"weight_unit",options:["mg","g"],initialValue:"mg",required:!0,disabled:ve,rules:[{required:!0}]}),(0,r.jsx)(O.Z,{name:["smiles","weight","weight_unit"],ignoreFormListField:!1,children:function(l){return(0,r.jsx)(Ie.Z,{placement:"top",title:(0,t.oz)("calculate-materials"),children:(0,r.jsx)(xe.ZP,{type:"link",disabled:ve||!(l.weight&&l.weight_unit),onClick:function(){return ke(l)},children:(0,r.jsx)(ae,{})})})}})]})})]},"group")}),(0,r.jsxs)(E.UW,{children:[(0,r.jsx)(z,{label:(0,t.oz)("start-time"),name:"start_type",initialValue:"auto",required:!0,rules:[{required:!0}],valueEnum:{auto:(0,t.oz)("pages.experimentDesign.label.auto"),asap:(0,t.oz)("pages.experimentDesign.label.asap"),custom:(0,t.oz)("pages.experimentDesign.label.custom")}}),(0,r.jsx)(O.Z,{name:["start_type"],children:function(l){var u=l.start_type;return u==="custom"?(0,r.jsx)(Ce,{label:(0,t.oz)("start-time-limit"),name:"start_time_range",transform:function(b){return{earliest_start_time:b==null?void 0:b[0],latest_start_time:b==null?void 0:b[1]}},required:!0,rules:[{required:!0}]}):null}})]},"3")]}):null},Ue=Le},72035:function(be,X,e){e.d(X,{H:function(){return N}});var q=e(15009),p=e.n(q),k=e(99289),I=e.n(k),_=e(5574),D=e.n(_),ee=e(87172),R=e(96486),se=e.n(R),H=e(67294),re=e(61487),N=function(t){var T=(0,H.useState)([]),g=D()(T,2),J=g[0],Q=g[1],$=(0,re.f)(),te=$.fetch,w=$.loading,ae=function(){var s=I()(p()().mark(function E(a){var x,G,j,B,O;return p()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:if(j=typeof a=="number"?[a]:a,j.length){f.next=3;break}return f.abrupt("return",[]);case 3:return f.next=5,te((0,ee.query)("projects").filterDeep("id","in",j).populateWith("project_members").get());case 5:return B=f.sent,O=B.data,f.abrupt("return",((x=(0,R.uniqBy)(O==null||(G=O[0])===null||G===void 0?void 0:G.project_members,"user_id"))===null||x===void 0?void 0:x.map(function(r){var Y,L;return{label:((Y=r.user_info)===null||Y===void 0?void 0:Y.username)||"",value:((L=r.user_info)===null||L===void 0?void 0:L.id)||-1}}))||[]);case 8:case"end":return f.stop()}},E)}));return function(a){return s.apply(this,arguments)}}();return(0,H.useEffect)(function(){var s=!0;return t&&ae(t).then(function(E){return s&&Q(E)}),function(){s=!1}},[t]),{members:J,loading:w}}}}]);

//# sourceMappingURL=shared-5IA9nmrD45fMXFy1zl2XwXGkFk_.820edc6b.async.js.map