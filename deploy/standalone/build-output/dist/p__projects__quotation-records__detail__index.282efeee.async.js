"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[1130],{26363:function(Oe,ae,n){n.d(ae,{Z:function(){return c}});var q=n(1413),O=n(67294),m={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M218.9 636.3l42.6 26.6c.1.1.3.2.4.3l12.7 8 .3.3a186.9 186.9 0 0094.1 25.1c44.9 0 87.2-15.7 121-43.8a256.27 256.27 0 01164.9-59.9c52.3 0 102.2 15.7 144.6 44.5l7.9 5-111.6-289V179.8h63.5c4.4 0 8-3.6 8-8V120c0-4.4-3.6-8-8-8H264.7c-4.4 0-8 3.6-8 8v51.9c0 4.4 3.6 8 8 8h63.5v173.6L218.9 636.3zm333-203.1c22 0 39.9 17.9 39.9 39.9S573.9 513 551.9 513 512 495.1 512 473.1s17.9-39.9 39.9-39.9zM878 825.1l-29.9-77.4-85.7-53.5-.1.1c-.7-.5-1.5-1-2.2-1.5l-8.1-5-.3-.3c-29-17.5-62.3-26.8-97-26.8-44.9 0-87.2 15.7-121 43.8a256.27 256.27 0 01-164.9 59.9c-53 0-103.5-16.1-146.2-45.6l-28.9-18.1L146 825.1c-2.8 7.4-4.3 15.2-4.3 23 0 35.2 28.6 63.8 63.8 63.8h612.9c7.9 0 15.7-1.5 23-4.3a63.6 63.6 0 0036.6-82.5z"}}]},name:"experiment",theme:"filled"},y=m,oe=n(84089),o=function(d,M){return O.createElement(oe.Z,(0,q.Z)((0,q.Z)({},d),{},{ref:M,icon:y}))},J=O.forwardRef(o),c=J},55287:function(Oe,ae,n){var q=n(1413),O=n(67294),m=n(5717),y=n(84089),oe=function(c,W){return O.createElement(y.Z,(0,q.Z)((0,q.Z)({},c),{},{ref:W,icon:m.Z}))},o=O.forwardRef(oe);ae.Z=o},36223:function(Oe,ae,n){n.d(ae,{Z:function(){return c}});var q=n(1413),O=n(67294),m={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M945 412H689c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h256c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM811 548H689c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h122c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8zM477.3 322.5H434c-6.2 0-11.2 5-11.2 11.2v248c0 3.6 1.7 6.9 4.6 9l148.9 108.6c5 3.6 12 2.6 15.6-2.4l25.7-35.1v-.1c3.6-5 2.5-12-2.5-15.6l-126.7-91.6V333.7c.1-6.2-5-11.2-11.1-11.2z"}},{tag:"path",attrs:{d:"M804.8 673.9H747c-5.6 0-10.9 2.9-13.9 7.7a321 321 0 01-44.5 55.7 317.17 317.17 0 01-101.3 68.3c-39.3 16.6-81 25-124 25-43.1 0-84.8-8.4-124-25-37.9-16-72-39-101.3-68.3s-52.3-63.4-68.3-101.3c-16.6-39.2-25-80.9-25-124 0-43.1 8.4-84.7 25-124 16-37.9 39-72 68.3-101.3 29.3-29.3 63.4-52.3 101.3-68.3 39.2-16.6 81-25 124-25 43.1 0 84.8 8.4 124 25 37.9 16 72 39 101.3 68.3a321 321 0 0144.5 55.7c3 4.8 8.3 7.7 13.9 7.7h57.8c6.9 0 11.3-7.2 8.2-13.3-65.2-129.7-197.4-214-345-215.7-216.1-2.7-395.6 174.2-396 390.1C71.6 727.5 246.9 903 463.2 903c149.5 0 283.9-84.6 349.8-215.8a9.18 9.18 0 00-8.2-13.3z"}}]},name:"field-time",theme:"outlined"},y=m,oe=n(84089),o=function(d,M){return O.createElement(oe.Z,(0,q.Z)((0,q.Z)({},d),{},{ref:M,icon:y}))},J=O.forwardRef(o),c=J},38545:function(Oe,ae,n){n.d(ae,{Z:function(){return c}});var q=n(1413),O=n(67294),m={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 512a48 48 0 1096 0 48 48 0 10-96 0zm200 0a48 48 0 1096 0 48 48 0 10-96 0zm-400 0a48 48 0 1096 0 48 48 0 10-96 0zm661.2-173.6c-22.6-53.7-55-101.9-96.3-143.3a444.35 444.35 0 00-143.3-96.3C630.6 75.7 572.2 64 512 64h-2c-60.6.3-119.3 12.3-174.5 35.9a445.35 445.35 0 00-142 96.5c-40.9 41.3-73 89.3-95.2 142.8-23 55.4-34.6 114.3-34.3 174.9A449.4 449.4 0 00112 714v152a46 46 0 0046 46h152.1A449.4 449.4 0 00510 960h2.1c59.9 0 118-11.6 172.7-34.3a444.48 444.48 0 00142.8-95.2c41.3-40.9 73.8-88.7 96.5-142 23.6-55.2 35.6-113.9 35.9-174.5.3-60.9-11.5-120-34.8-175.6zm-151.1 438C704 845.8 611 884 512 884h-1.7c-60.3-.3-120.2-15.3-173.1-43.5l-8.4-4.5H188V695.2l-4.5-8.4C155.3 633.9 140.3 574 140 513.7c-.4-99.7 37.7-193.3 107.6-263.8 69.8-70.5 163.1-109.5 262.8-109.9h1.7c50 0 98.5 9.7 144.2 28.9 44.6 18.7 84.6 45.6 119 80 34.3 34.3 61.3 74.4 80 119 19.4 46.2 29.1 95.2 28.9 145.8-.6 99.6-39.7 192.9-110.1 262.7z"}}]},name:"message",theme:"outlined"},y=m,oe=n(84089),o=function(d,M){return O.createElement(oe.Z,(0,q.Z)((0,q.Z)({},d),{},{ref:M,icon:y}))},J=O.forwardRef(o),c=J},75750:function(Oe,ae,n){n.d(ae,{Z:function(){return c}});var q=n(1413),O=n(67294),m={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3zM664.8 561.6l36.1 210.3L512 672.7 323.1 772l36.1-210.3-152.8-149L417.6 382 512 190.7 606.4 382l211.2 30.7-152.8 148.9z"}}]},name:"star",theme:"outlined"},y=m,oe=n(84089),o=function(d,M){return O.createElement(oe.Z,(0,q.Z)((0,q.Z)({},d),{},{ref:M,icon:y}))},J=O.forwardRef(o),c=J},88280:function(Oe,ae,n){n.d(ae,{Z:function(){return Dt}});var q=n(4942),O=n(97685),m=n(45987),y=n(74165),oe=n(15861),o=n(1413),J=n(87462),c=n(67294),W=n(42110),d=n(43445),M=function(we,Re){return c.createElement(d.Z,(0,J.Z)({},we,{ref:Re,icon:W.Z}))},P=c.forwardRef(M),U=P,Q=n(97462),ue=n(952),Ce=n(10915),fe=n(48171),e=n(53914),$e=n(22270),Ne=n(60249),Ae=n(28036),Pe=n(98138),be=n(21770),je=n(88306),at=n(8880),vt=n(56337),se=n(85893),qe=["onTableChange","maxLength","formItemProps","recordCreatorProps","rowKey","controlled","defaultValue","onChange","editableFormRef"],Ct=["record","position","creatorButtonText","newRecordType","parentKey","style"],Ee=c.createContext(void 0);function te(v){var we=v.children,Re=v.record,t=v.position,rt=v.newRecordType,Qe=v.parentKey,A=(0,c.useContext)(Ee);return c.cloneElement(we,(0,o.Z)((0,o.Z)({},we.props),{},{onClick:function(){var it=(0,oe.Z)((0,y.Z)().mark(function Ke(ht){var ft,Ye,Ge,Le;return(0,y.Z)().wrap(function(xe){for(;;)switch(xe.prev=xe.next){case 0:return xe.next=2,(ft=(Ye=we.props).onClick)===null||ft===void 0?void 0:ft.call(Ye,ht);case 2:if(Le=xe.sent,Le!==!1){xe.next=5;break}return xe.abrupt("return");case 5:A==null||(Ge=A.current)===null||Ge===void 0||Ge.addEditRecord(Re,{position:t,newRecordType:rt,parentKey:Qe});case 6:case"end":return xe.stop()}},Ke)}));function He(Ke){return it.apply(this,arguments)}return He}()}))}function I(v){var we,Re,t=(0,Ce.YB)(),rt=v.onTableChange,Qe=v.maxLength,A=v.formItemProps,it=v.recordCreatorProps,He=v.rowKey,Ke=v.controlled,ht=v.defaultValue,ft=v.onChange,Ye=v.editableFormRef,Ge=(0,m.Z)(v,qe),Le=(0,c.useRef)(void 0),Je=(0,c.useRef)(),xe=(0,c.useRef)();(0,c.useImperativeHandle)(Ge.actionRef,function(){return Je.current},[Je.current]);var Ft=(0,be.Z)(function(){return v.value||ht||[]},{value:v.value,onChange:v.onChange}),pt=(0,O.Z)(Ft,2),re=pt[0],Pt=pt[1],Ve=c.useMemo(function(){return typeof He=="function"?He:function(g,j){return g[He]||j}},[He]),gt=(0,fe.J)(function(g){if(typeof g=="number"&&!v.name){if(g>=re.length)return g;var j=re&&re[g];return Ve==null?void 0:Ve(j,g)}if((typeof g=="string"||g>=re.length)&&v.name){var x=re.findIndex(function(h,E){var _;return(Ve==null||(_=Ve(h,E))===null||_===void 0?void 0:_.toString())===(g==null?void 0:g.toString())});if(x!==-1)return x}return g});(0,c.useImperativeHandle)(Ye,function(){var g=function(h){var E,_;if(h==null)throw new Error("rowIndex is required");var w=gt(h),L=[v.name,(E=w==null?void 0:w.toString())!==null&&E!==void 0?E:""].flat(1).filter(Boolean);return(_=xe.current)===null||_===void 0?void 0:_.getFieldValue(L)},j=function(){var h,E=[v.name].flat(1).filter(Boolean);if(Array.isArray(E)&&E.length===0){var _,w=(_=xe.current)===null||_===void 0?void 0:_.getFieldsValue();return Array.isArray(w)?w:Object.keys(w).map(function(L){return w[L]})}return(h=xe.current)===null||h===void 0?void 0:h.getFieldValue(E)};return(0,o.Z)((0,o.Z)({},xe.current),{},{getRowData:g,getRowsData:j,setRowData:function(h,E){var _,w;if(h==null)throw new Error("rowIndex is required");var L=gt(h),i=[v.name,(_=L==null?void 0:L.toString())!==null&&_!==void 0?_:""].flat(1).filter(Boolean),X=Object.assign({},(0,o.Z)((0,o.Z)({},g(h)),E||{})),ee=(0,at.Z)({},i,X);return(w=xe.current)===null||w===void 0||w.setFieldsValue(ee),!0}})},[gt,v.name,xe.current]),(0,c.useEffect)(function(){v.controlled&&(re||[]).forEach(function(g,j){var x;(x=xe.current)===null||x===void 0||x.setFieldsValue((0,q.Z)({},"".concat(Ve(g,j)),g))},{})},[(0,e.ZP)(re),v.controlled]),(0,c.useEffect)(function(){if(v.name){var g;xe.current=v==null||(g=v.editable)===null||g===void 0?void 0:g.form}},[(we=v.editable)===null||we===void 0?void 0:we.form,v.name]);var Ue=it||{},wt=Ue.record,_t=Ue.position,Rt=Ue.creatorButtonText,bt=Ue.newRecordType,jt=Ue.parentKey,Zt=Ue.style,Ot=(0,m.Z)(Ue,Ct),St=_t==="top",lt=(0,c.useMemo)(function(){return typeof Qe=="number"&&Qe<=(re==null?void 0:re.length)?!1:it!==!1&&(0,se.jsx)(te,{record:(0,$e.h)(wt,re==null?void 0:re.length,re)||{},position:_t,parentKey:(0,$e.h)(jt,re==null?void 0:re.length,re),newRecordType:bt,children:(0,se.jsx)(Ae.ZP,(0,o.Z)((0,o.Z)({type:"dashed",style:(0,o.Z)({display:"block",margin:"10px 0",width:"100%"},Zt),icon:(0,se.jsx)(U,{})},Ot),{},{children:Rt||t.getMessage("editableTable.action.add","\u6DFB\u52A0\u4E00\u884C\u6570\u636E")}))})},[it,Qe,re==null?void 0:re.length]),At=(0,c.useMemo)(function(){return lt?St?{components:{header:{wrapper:function(j){var x,h=j.className,E=j.children;return(0,se.jsxs)("thead",{className:h,children:[E,(0,se.jsxs)("tr",{style:{position:"relative"},children:[(0,se.jsx)("td",{colSpan:0,style:{visibility:"hidden"},children:lt}),(0,se.jsx)("td",{style:{position:"absolute",left:0,width:"100%"},colSpan:(x=Ge.columns)===null||x===void 0?void 0:x.length,children:lt})]})]})}}}}:{tableViewRender:function(j,x){var h,E;return(0,se.jsxs)(se.Fragment,{children:[(h=(E=v.tableViewRender)===null||E===void 0?void 0:E.call(v,j,x))!==null&&h!==void 0?h:x,lt]})}}:{}},[St,lt]),r=(0,o.Z)({},v.editable),Z=(0,fe.J)(function(g,j){var x,h,E;if((x=v.editable)===null||x===void 0||(h=x.onValuesChange)===null||h===void 0||h.call(x,g,j),(E=v.onValuesChange)===null||E===void 0||E.call(v,j,g),v.controlled){var _;v==null||(_=v.onChange)===null||_===void 0||_.call(v,j)}});return(v!=null&&v.onValuesChange||(Re=v.editable)!==null&&Re!==void 0&&Re.onValuesChange||v.controlled&&v!==null&&v!==void 0&&v.onChange)&&(r.onValuesChange=Z),(0,se.jsxs)(se.Fragment,{children:[(0,se.jsx)(Ee.Provider,{value:Je,children:(0,se.jsx)(vt.Z,(0,o.Z)((0,o.Z)((0,o.Z)({search:!1,options:!1,pagination:!1,rowKey:He,revalidateOnFocus:!1},Ge),At),{},{tableLayout:"fixed",actionRef:Je,onChange:rt,editable:(0,o.Z)((0,o.Z)({},r),{},{formProps:(0,o.Z)({formRef:xe},r.formProps)}),dataSource:re,onDataSourceChange:function(j){if(Pt(j),v.name&&_t==="top"){var x,h=(0,at.Z)({},[v.name].flat(1).filter(Boolean),j);(x=xe.current)===null||x===void 0||x.setFieldsValue(h)}}}))}),v.name?(0,se.jsx)(Q.Z,{name:[v.name],children:function(j){var x,h;if(!Le.current)return Le.current=re,null;var E=(0,je.Z)(j,[v.name].flat(1)),_=E==null?void 0:E.find(function(w,L){var i;return!(0,Ne.A)(w,(i=Le.current)===null||i===void 0?void 0:i[L])});return Le.current=re,_&&(v==null||(x=v.editable)===null||x===void 0||(h=x.onValuesChange)===null||h===void 0||h.call(x,_,E)),null}}):null]})}function ze(v){var we=ue.ZP.useFormInstance();return v.name?(0,se.jsx)(Pe.Z.Item,(0,o.Z)((0,o.Z)({style:{maxWidth:"100%"}},v==null?void 0:v.formItemProps),{},{name:v.name,shouldUpdate:function(t,rt){var Qe=[v.name].flat(1);try{return JSON.stringify((0,je.Z)(t,Qe))!==JSON.stringify((0,je.Z)(rt,Qe))}catch(A){return!0}},children:(0,se.jsx)(I,(0,o.Z)((0,o.Z)({tableLayout:"fixed",scroll:{x:"max-content"}},v),{},{editable:(0,o.Z)((0,o.Z)({},v.editable),{},{form:we})}))})):(0,se.jsx)(I,(0,o.Z)({tableLayout:"fixed",scroll:{x:"max-content"}},v))}ze.RecordCreator=te;var Dt=ze},4019:function(Oe,ae,n){var q=n(67294),O=n(85893),m=function(oe){var o=oe.color,J=o===void 0?"#222":o;return(0,O.jsxs)("svg",{viewBox:"0 -3 128 6",width:"60",height:"12",transform:"rotate(180)",children:[(0,O.jsx)("defs",{children:(0,O.jsx)("marker",{id:"reaction-arrowhead",viewBox:"0 0 8 6",markerUnits:"userSpaceOnUse",markerWidth:"18",markerHeight:"12",refX:"2",refY:"2",orient:"auto",fill:J,children:(0,O.jsx)("path",{d:"m 0 0 l 7 2.25 l -7 2.25 c 0 0 0.735 -1.084 0.735 -2.28 c 0 -1.196 -0.735 -2.22 -0.735 -2.22 z"})})}),(0,O.jsx)("line",{x1:"0",y1:"0",x2:"120",y2:"0",strokeWidth:"1",stroke:J,markerEnd:"url(#reaction-arrowhead)"})]})};ae.Z=m},10784:function(Oe,ae,n){var q=n(97857),O=n.n(q),m=n(15009),y=n.n(m),oe=n(99289),o=n.n(oe),J=n(5574),c=n.n(J),W=n(13769),d=n.n(W),M=n(28036),P=n(67294),U=n(85893),Q=["onClick"],ue=function(fe){var e=fe.onClick,$e=d()(fe,Q),Ne=(0,P.useState)(!1),Ae=c()(Ne,2),Pe=Ae[0],be=Ae[1],je=function(){var at=o()(y()().mark(function vt(se){var qe;return y()().wrap(function(Ee){for(;;)switch(Ee.prev=Ee.next){case 0:return be(!0),Ee.prev=1,Ee.next=4,e==null?void 0:e(se);case 4:return qe=Ee.sent,be(!1),Ee.abrupt("return",qe);case 9:return Ee.prev=9,Ee.t0=Ee.catch(1),be(!1),Ee.abrupt("return","");case 13:case"end":return Ee.stop()}},vt,null,[[1,9]])}));return function(se){return at.apply(this,arguments)}}();return(0,U.jsx)(M.ZP,O()(O()({loading:Pe},$e),{},{onClick:je}))};ae.Z=ue},15001:function(Oe,ae,n){var q=n(97857),O=n.n(q),m=n(32884),y=n(66309),oe=n(85893),o={created:"#F5B544",editing:"#F5B544",started:"#4B9F47",holding:"#E6521F",confirmed:"#4B9F47",finished:"#1890FF",cancelled:"#979797",canceled:"#979797",running:"#2AD259",hold:"#E6521F",completed:"#1890FF",success:"#F51D2C",failed:"#9747FF",todo:"#F5B544",checking:"#4B9F47"},J=function(W){var d=W.status,M=W.colorMap,P=W.labelPrefix,U=W.label,Q=W.className,ue=O()(O()({},o),M)[d],Ce=U||(0,m.oz)("".concat(P,".").concat(d));return(0,oe.jsx)(y.Z,{className:Q,color:ue,children:Ce})};ae.Z=J},69776:function(Oe,ae,n){var q=n(96486),O=n.n(q),m=n(63686),y=function(){var o=[{value:"created",label:(0,m.oz)("molecules-status.created")},{value:"designing",label:(0,m.oz)("molecules-status.designing")},{value:"synthesizing",label:(0,m.oz)("molecules-status.synthesizing")},{value:"finished",label:(0,m.oz)("component.notification.statusValue.success")},{value:"canceled",label:(0,m.oz)("pages.projectTable.statusLabel.cancelled")}],J=[{value:"product",label:(0,m.oz)("product")},{value:"main_reactant",label:(0,m.oz)("main-reactant")},{value:"reactant",label:(0,m.oz)("reactant")},{value:"other_reagent",label:(0,m.oz)("other-reagent")}],c=[{label:(0,m.oz)("same-key-material"),value:"start_material"},{label:(0,m.oz)("algorithm-cluster"),value:"cluster"},{label:(0,m.oz)("not-grouped"),value:"ungrouped"}],W=[{label:(0,m.oz)("algorithmic-score"),value:"score"},{label:(0,m.oz)("known-reaction-proportion"),value:"known_reaction_rate"},{label:(0,m.oz)("longest-chain-l"),value:"backbone_length"},{label:(0,m.oz)("route-length"),value:"min_n_main_tree_steps"}],d={createdAt:(0,m.oz)("creation-time"),updatedAt:(0,m.oz)("last-update-time"),no:(0,m.oz)("name")},M={target:(0,m.oz)("target-molecules"),building_block:(0,m.oz)("key-intermediate"),temp_block:(0,m.oz)("show-materials")},P=(0,q.omit)(M,"temp_block"),U={onlyOneLineEditorAlertMessage:(0,m.oz)("only-one-edit"),onlyAddOneLineAlertMessage:(0,m.oz)("only-one-added")},Q={total_cost:(0,m.oz)("total"),material_cost:(0,m.oz)("material-cost"),labor_cost:(0,m.oz)("labor-cost")},ue={draft:(0,m.oz)("draft"),published:(0,m.oz)("in-use"),deleted:(0,m.oz)("deleted")},Ce={success:(0,m.oz)("pages.experiment.statusLabel.success"),fail:(0,m.oz)("pages.experiment.statusLabel.failed"),processing:(0,m.oz)("pages.experiment.statusLabel.running")},fe={limited:(0,m.oz)("component.notification.statusValue.limited"),completed:(0,m.oz)("component.notification.statusValue.success"),running:(0,m.oz)("component.notification.statusValue.running"),pending:(0,m.oz)("component.notification.statusValue.pending"),failed:(0,m.oz)("component.notification.statusValue.failed")},e={working:(0,m.oz)("working"),holding:(0,m.oz)("hold"),error:(0,m.oz)("unavailable"),idle:(0,m.oz)("idle")};return{moleculeStatusOptions:o,reactionRoleOptions:J,groupOptions:c,proportionOptions:W,typeMap:M,sortStandard:d,typeMapForSelect:P,editableConfig:U,chargeDes:Q,materialManageStauts:ue,aiGenerateStauts:fe,aiAIInferenceStauts:Ce,robotStatus:e}};ae.Z=y},85670:function(Oe,ae,n){var q=n(34804),O=n(85418),m=n(42075),y=n(85893),oe=function(J){var c=J.currentValue,W=J.avalibleValues,d=J.onSelect,M=J.valueRender,P=M===void 0?function(Q){return(0,y.jsx)(y.Fragment,{children:Q})}:M;if(!W.length)return P(c);var U=W.map(function(Q){return{label:P(Q),key:Q}});return(0,y.jsx)(O.Z,{menu:{items:U,onClick:function(ue){return d(ue.key)}},trigger:["click"],children:(0,y.jsxs)(m.Z,{children:[P(c),(0,y.jsx)(q.Z,{})]})})};ae.Z=oe},26194:function(Oe,ae,n){n.r(ae),n.d(ae,{default:function(){return At}});var q=n(9783),O=n.n(q),m=n(97857),y=n.n(m),oe=n(15009),o=n.n(oe),J=n(99289),c=n.n(J),W=n(5574),d=n.n(W),M=n(10784),P=n(68918),U=n(42689),Q=n(17322),ue=n(91686),Ce=n(37507),fe=n(87172),e=n(32884),$e=n(11774),Ne=n(34994),Ae=n(5966),Pe=n(31199),be=n(98138),je=n(74656),at=n(45360),vt=n(71471),se=n(42075),qe=n(28036),Ct=n(93967),Ee=n.n(Ct),te=n(96486),I=n(67294),ze=n(70831),Dt=n(33188),v=n(37476),we=n(71230),Re=n(15746),t=n(85893);function rt(r){var Z,g=r.form,j=r.onFinish;(0,I.useEffect)(function(){g.setFieldsValue(r==null?void 0:r.initalValues)},[r==null?void 0:r.initalValues]);var x=(0,I.useState)("g"),h=d()(x,2),E=h[0],_=h[1],w=(0,ze.useAccess)();return(0,t.jsxs)(v.Y,{layout:"horizontal",width:680,title:(0,e.oz)("other-weight-quotate"),trigger:w!=null&&(Z=w.authCodeList)!==null&&Z!==void 0&&Z.includes("quotation-records.button.otherWeightQuotate")?(0,t.jsx)(qe.ZP,{children:(0,e.oz)("other-weight-quotate")}):(0,t.jsx)(t.Fragment,{}),form:g,autoFocusFirstInput:!0,modalProps:{destroyOnClose:!0,onCancel:function(){return console.log("run")}},submitTimeout:2e3,onFinish:function(){var L=c()(o()().mark(function i(X){return o()().wrap(function(k){for(;;)switch(k.prev=k.next){case 0:return j({target_weight:X==null?void 0:X.target_weight,target_unit:E}),k.abrupt("return",!0);case 2:case"end":return k.stop()}},i)}));return function(i){return L.apply(this,arguments)}}(),children:[(0,t.jsxs)(we.Z,{children:[(0,t.jsx)(Re.Z,{span:12,children:(0,t.jsx)(Ae.Z,{name:"compound_no",label:(0,e.oz)("molecules-no"),rules:[{required:!0}],labelCol:{span:8},wrapperCol:{span:14},disabled:!0})}),(0,t.jsx)(Re.Z,{span:12,children:(0,t.jsx)(Pe.Z,{name:"target_weight",label:(0,e.oz)("weight"),labelCol:{span:6},wrapperCol:{span:18},addonAfter:(0,t.jsxs)(je.Z,{value:E,style:{width:"60px"},onChange:function(i){return _(i)},children:[(0,t.jsx)(je.Z.Option,{value:"g",children:"g"}),(0,t.jsx)(je.Z.Option,{value:"mg",children:"mg"})]}),rules:[{required:!0}],min:.1})})]}),(0,t.jsxs)(we.Z,{children:[(0,t.jsx)(Re.Z,{span:12,children:(0,t.jsx)(Pe.Z,{labelCol:{span:8},wrapperCol:{span:14},name:"purity",rules:[{required:!0}],label:"".concat((0,e.oz)("purity"),"(%)"),min:.1,max:100,disabled:!0})}),(0,t.jsx)(Re.Z,{span:12,children:(0,t.jsx)(Pe.Z,{name:"ratio",label:(0,e.oz)("coefficient"),labelCol:{span:6},wrapperCol:{span:13},rules:[{required:!0}],min:.1,max:100,disabled:!0})})]})]})}var Qe=n(54025),A={unfoldWidth:"unfoldWidth___OVBf0",foldWidth:"foldWidth___j89Q3",unfoldWidth_EN:"unfoldWidth_EN___ic7X2",foldWidth_EN:"foldWidth_EN___RCpY4",quoteInfo:"quoteInfo___ocFoY",unitSelect:"unitSelect___W8hh1",anchor:"anchor___VpYXB",anchorEN:"anchorEN___yzHrz",quoteDetail:"quoteDetail___CPuC2",content:"content___Nss6m",title:"title___zYn2a",stepTitle:"stepTitle___OKg7c",openLinkWrapper:"openLinkWrapper___hxFyb",procedureTitle:"procedureTitle___OPIr7",procedureReference:"procedureReference___oQZE9",quotationSummaryDetail:"quotationSummaryDetail___v_H_s",routeInfo:"routeInfo___njAIE",structure:"structure___UvTj7",referenceContent:"referenceContent___OC0WQ",referenceStructure:"referenceStructure___fNzn0",laborEditor:"laborEditor___OnT9W",operateButton:"operateButton___PQYhX",confirmModal:"confirmModal___M5OOP"};function it(r){var Z=r.openEvent,g=(0,U.I)(),j=g.dialogProps,x=g.confirm,h=r.diffCostData,E=r.confirmDiff,_=[{title:(0,e.oz)("steps"),dataIndex:"step_no"},{title:(0,e.Ig)()?"".concat((0,e.oz)("english-name"),"\uFF08").concat((0,e.oz)("yuan"),"\uFF09"):"".concat((0,e.oz)("chinese-name"),"\uFF08").concat((0,e.oz)("yuan"),"\uFF09"),dataIndex:(0,e.Ig)()?"name_en":"name_zh",ellipsis:!0},{title:"CAS",dataIndex:"cas_no"},{title:(0,e.oz)("min-cost"),dataIndex:"minCost"},{title:(0,e.oz)("cost"),dataIndex:"cost"}],w={dataSource:h,bordered:!0,pagination:null},L=function(){var i=c()(o()().mark(function X(){return o()().wrap(function(k){for(;;)switch(k.prev=k.next){case 0:E(),x();case 2:case"end":return k.stop()}},X)}));return function(){return i.apply(this,arguments)}}();return(0,t.jsxs)(P.Z,y()(y()({},j),{},{title:(0,e.oz)("confirm-quote"),className:A.confirmModal,width:800,openEvent:Z,onConfirm:L,children:[(0,t.jsx)("div",{className:A.title,children:(0,e.oz)("cost-conflict-quote-double-check")}),(0,t.jsx)(Qe.Z,y()(y()({},w),{},{columns:_,rowKey:"cas_no"}))]}))}var He=n(19632),Ke=n.n(He),ht=n(83335),ft=n(43851),Ye=n(69776),Ge=n(64134),Le=n(88280),Je=n(55241),xe=n(57632),Ft=n(36295),pt=n(42282),re={editContent:"editContent___Qsb6z",smi:"smi___SGQAC"};function Pt(r){var Z=r.supplierDatas,g=r.confirmSupplierDatas,j=(0,Ye.Z)(),x=j.editableConfig,h=be.Z.useForm(),E=d()(h,1),_=E[0],w=(0,I.useState)([]),L=d()(w,2),i=L[0],X=L[1],ee=(0,I.useState)(),k=d()(ee,2),H=k[0],de=k[1],u=(0,I.useState)(!1),me=d()(u,2),ce=me[0],he=me[1];(0,I.useEffect)(function(){X(Z)},[Z]);var K=function(D){D&&(0,e.qt)(i)?(he(!0),de(function(){return i.map(function(f){return f.id})})):(he(!1),de([]))},$=function(D){return D!=null&&D.quantity&&D!==null&&D!==void 0&&D.unit?"".concat(D==null?void 0:D.quantity).concat(D==null?void 0:D.unit):"-"},Y=[{title:"CAS",dataIndex:"cas_no",width:80,readonly:!0,valueType:"text"},{title:(0,e.oz)("supplier"),dataIndex:"source",width:80,readonly:!0,valueType:"text",renderText:function(D,f){var Me,De;return f!=null&&f.source_link&&!ce?(0,t.jsxs)("a",{onClick:function(){return window.open(f==null?void 0:f.source_link)},children:[D,f!=null&&(Me=f.material_lib)!==null&&Me!==void 0&&Me.version?(0,t.jsxs)(t.Fragment,{children:["\xA0",(0,t.jsxs)("span",{className:"red",children:["(",f==null||(De=f.material_lib)===null||De===void 0?void 0:De.version,")"]})]}):""]}):D}},{title:(0,e.oz)("amount"),dataIndex:"quantity",width:80,readonly:!0,renderFormItem:function(D){return $(D==null?void 0:D.entity)},render:function(D,f){return $(f)}},{title:(0,e.oz)("purity"),dataIndex:"purity",width:80,readonly:!0},{title:(0,e.oz)("spot-or-futures"),readonly:!0,dataIndex:"in_stock",width:120,initialValue:["true","false"],valueEnum:{true:{text:(0,e.oz)("spot")},false:{text:(0,e.oz)("futures")}}},{title:"".concat((0,e.oz)("price"),"/\uFFE5"),readonly:!0,dataIndex:"price",width:120,render:function(D,f){return f!=null&&f.price&&!isNaN(f==null?void 0:f.price)?f==null?void 0:f.price.toFixed(2):""}},{title:"".concat((0,e.oz)("unit-price"),"\uFFE5/g"),readonly:!0,width:130,dataIndex:"unit_price",render:function(D,f){return f!=null&&f.unit_price&&!isNaN(f==null?void 0:f.unit_price)?f==null?void 0:f.unit_price.toFixed(2):""}},{title:(0,e.oz)("quantity"),width:90,valueType:"digit",readonly:!1,dataIndex:"count",formItemProps:{rules:[{message:(0,e.oz)("cant-input-negative-number"),pattern:/^[0-9]\d*$/}]}},{title:"".concat((0,e.oz)("cost"),"/\uFFE5"),readonly:!0,width:120,dataIndex:"decs",renderText:function(D,f){var Me=f!=null&&f.count&&f!==null&&f!==void 0&&f.price?(f==null?void 0:f.count)*(f==null?void 0:f.price):"-";return(0,e.ki)(Me)}}];return(0,t.jsxs)(v.Y,{title:(0,e.oz)("supplier"),width:1180,trigger:(0,t.jsx)("a",{children:(0,e.oz)("supplier")},"supplier"),form:_,autoFocusFirstInput:!0,onOpenChange:function(D){D||X(Z)},modalProps:{destroyOnClose:!0},submitter:{resetButtonProps:{style:{display:"none"}},submitButtonProps:{style:{display:ce?"none":void 0}},searchConfig:{submitText:(0,e.oz)("pages.route.edit.label.save")}},onFinish:c()(o()().mark(function G(){return o()().wrap(function(f){for(;;)switch(f.prev=f.next){case 0:return f.next=2,g(i);case 2:return f.abrupt("return",!0);case 3:case"end":return f.stop()}},G)})),children:[(0,t.jsxs)("div",{className:re.editContent,children:[(0,t.jsx)("span",{children:(0,e.oz)("quotate-price-tip")}),(0,t.jsx)("a",{onClick:function(){return K(!ce)},style:{width:(0,e.Ig)()?"120px":"auto"},children:ce?(0,e.oz)("supplier.edit.finished"):(0,e.oz)("edit")})]}),(0,t.jsx)(Le.Z,{columns:Y,rowKey:"id",value:i,onChange:X,recordCreatorProps:!1,scroll:{y:300},editable:y()(y()({},x),{},{type:"multiple",editableKeys:H,onValuesChange:function(D,f){X(f)},onChange:de})})]})}function Ve(r){var Z=(0,Ye.Z)(),g=Z.editableConfig,j=r.quoteMoleculeId,x=r.projectReactionId,h=location.pathname.includes("quotation-records"),E=(0,I.useState)([]),_=d()(E,2),w=_[0],L=_[1],i=(0,I.useState)([]),X=d()(i,2),ee=X[0],k=X[1],H=(0,ze.useModel)("quotation"),de=H.updateQuotes,u=(0,ze.useAccess)();(0,I.useEffect)(function(){k(r==null?void 0:r.materialData)},[r==null?void 0:r.materialData]);var me=function(){var b=c()(o()().mark(function s(a,l){var z;return o()().wrap(function(T){for(;;)switch(T.prev=T.next){case 0:return T.next=2,de(j,y()(y()({},a),{},{project_reaction_id:x}));case 2:z=T.sent,z&&l&&l();case 4:case"end":return T.stop()}},s)}));return function(a,l){return b.apply(this,arguments)}}(),ce=function(){var b=c()(o()().mark(function s(a,l,z){var p;return o()().wrap(function(ne){for(;;)switch(ne.prev=ne.next){case 0:return p={project_reaction_id:x,role:a==null?void 0:a.role,cas_no:a==null?void 0:a.cas_no,detail:a==null?void 0:a.detail,smi:a==null?void 0:a.smi,name_zh:a==null?void 0:a.name_zh,name_en:a==null?void 0:a.name_en,equivalent:a==null?void 0:a.equivalent,unit:a==null?void 0:a.unit,required_quantity:a==null?void 0:a.required_quantity,set_cost:a==null?void 0:a.set_cost,material_no:a==null?void 0:a.no,gPerMol:a==null?void 0:a.gPerMol,action:l?"add":void 0},ne.next=3,me(p,function(){z&&k(z)});case 3:case"end":return ne.stop()}},s)}));return function(a,l,z){return b.apply(this,arguments)}}(),he=function(){var b=c()(o()().mark(function s(a,l,z){var p,T,ne,ve,pe,We,Ze,ot;return o()().wrap(function(Se){for(;;)switch(Se.prev=Se.next){case 0:return p=(0,te.cloneDeep)(r==null?void 0:r.materialData),Se.next=3,(0,e.Hm)(a);case 3:if(T=Se.sent,ne=function(ke){var st=T.find(function(nt){return nt[ke]&&nt[ke]!==null});return st?st[ke]:void 0},!T){Se.next=11;break}return ve=ne("gPerMol"),pe=ne("name_en"),We=ne("name_zh"),Ze=ne("cas_no"),ot=y()(y()({},l),{},{smi:a,detail:T,gPerMol:ve,role:(l==null?void 0:l.role)||"reactant",name_en:pe,cas_no:Ze,name_zh:We}),p.push(ot),Se.next=11,ce(ot,z,p);case 11:case"end":return Se.stop()}},s)}));return function(a,l,z){return b.apply(this,arguments)}}(),K=(0,I.useState)([{title:(0,e.oz)("structural"),dataIndex:"smi",readonly:!0,width:120,valueType:"text",renderFormItem:function(s,a){var l,z,p=a.isEditable,T=s==null?void 0:s.entity,ne=s==null||(l=s.entity)===null||l===void 0?void 0:l.smi;return(s==null||(z=s.entity)===null||z===void 0?void 0:z.step_no)===(0,e.oz)("total")||p||ne==="-"?ne:(0,t.jsx)("div",{className:re.smi,children:(0,t.jsx)(pt.Z,{multiple:!1,onChange:function(pe){return he(pe,T)}})})},render:function(s,a){return(0,t.jsx)("div",{className:re.smi,children:(a==null?void 0:a.step_no)===(0,e.oz)("total")?"-":(0,t.jsx)(pt.Z,{value:s==="-"?[]:s,disabled:(0,Ge.J3)(a==null?void 0:a.role),multiple:!1,onChange:function(z){return he(z,a)}})})}},{title:(0,e.oz)("role"),dataIndex:"role",valueType:"select",width:110,valueEnum:function(s){return(s==null?void 0:s.role)==="main_reactant"?{main_reactant:(0,e.oz)("main-reactant")}:{reactant:(0,e.oz)("reactant"),other_reagent:(0,e.oz)("other-reagent"),solvent:(0,e.oz)("solvent")}}},{title:(0,e.oz)("chinese-name"),dataIndex:"name_zh",width:100,valueType:"text",hideInTable:(0,e.Ig)()},{title:(0,e.oz)("english-name"),dataIndex:"name_en",width:100,valueType:"text"},{title:"CAS",dataIndex:"cas_no",width:100,valueType:"text"},{title:(0,e.oz)("molecular-mass"),dataIndex:"gPerMol",width:100,hideInTable:!h,valueType:"digit",renderText:function(s,a){return(0,e.ki)(a==null?void 0:a.gPerMol)}},{title:(0,e.oz)("EWR"),dataIndex:"equivalent",valueType:"digit",width:140,formItemProps:{rules:[{message:(0,e.oz)("pattern-positive-number"),pattern:/^(0*[1-9]\d*\.?\d*|0+\.\d*[1-9]\d*)$/}]},renderText:function(s,a){return(0,e.ki)(a==null?void 0:a.equivalent)}},{title:(0,e.oz)("measurement-method"),dataIndex:"unit",width:120,valueEnum:ft.y5},{title:"".concat((0,e.oz)("required-weight"),"/g"),dataIndex:"required_quantity",valueType:"digit",width:100,formItemProps:{rules:[{message:(0,e.oz)("pattern-positive-number"),pattern:/^(0*[1-9]\d*\.?\d*|0+\.\d*[1-9]\d*)$/}]},renderText:function(s,a){return(0,e.ki)(a==null?void 0:a.required_quantity)}},{title:function(){return(0,t.jsx)("div",{className:"flex-align-items-center",children:(0,t.jsxs)("div",{children:[(0,e.oz)("min-cost"),"/\uFFE5"]})})},dataIndex:"cost",valueType:"text",width:140,readonly:!0,renderText:function(s,a){if((a==null?void 0:a.step_no)===(0,e.oz)("total"))return"";var l=(0,e.ki)(a==null?void 0:a.cost);return!isNaN(l)&&!(0,te.isEmpty)(l)?l:(0,e.oz)("please-quote")}},{title:function(){return(0,t.jsxs)("div",{className:"flex-align-items-center",children:[(0,t.jsxs)("div",{children:[(0,e.oz)("cost"),"/\uFFE5"]}),(0,t.jsx)(Je.Z,{content:(0,e.oz)("cost-empty-tip"),children:(0,t.jsx)(ht.r,{width:18,style:{cursor:"pointer"}})})]})},dataIndex:"set_cost",valueType:"text",width:140,formItemProps:{rules:[{message:(0,e.oz)("pattern-positive-number"),pattern:/^(0*[1-9]\d*\.?\d*|0+\.\d*[1-9]\d*)$/}]},renderText:function(s,a){if((a==null?void 0:a.step_no)===(0,e.oz)("total"))return"";var l=(0,e.ki)((a==null?void 0:a.set_cost)||(a==null?void 0:a.cost));return!isNaN(l)&&!(0,te.isEmpty)(l)?l:"-"}}]),$=d()(K,2),Y=$[0],G=$[1];(0,I.useEffect)(function(){if(!(r!=null&&r.hiddeStep)){var b=(0,te.cloneDeep)(Y);b.unshift({title:(0,e.oz)("steps"),width:60,key:"step_no",dataIndex:"step_no",fixed:"left",readonly:!0,render:function(a,l){return(l==null?void 0:l.step_no)===(0,e.oz)("total")?l==null?void 0:l.step_no:(0,t.jsx)("a",{href:"#".concat(l==null?void 0:l.step_no),children:l==null?void 0:l.step_no})}}),G(b)}},[r==null?void 0:r.hiddeStep]);var D=function(){return[{title:(0,e.oz)("pages.experiment.label.operation"),valueType:"option",width:150,fixed:"right",render:function(a,l,z,p){var T;return[(0,t.jsxs)(t.Fragment,{children:[(l==null?void 0:l.step_no)===(0,e.oz)("total")?"":(0,t.jsx)("a",{onClick:function(){var ve;L([].concat(Ke()(w),[l.no])),p==null||(ve=p.startEditable)===null||ve===void 0||ve.call(p,l.no)},children:(0,e.oz)("edit")},"editable"),u!=null&&(T=u.authCodeList)!==null&&T!==void 0&&T.includes("quotation-records.button.supplier")&&(0,e.qt)(l==null?void 0:l.detail)?(0,t.jsx)(Pt,{supplierDatas:l==null?void 0:l.detail,confirmSupplierDatas:function(){var ne=c()(o()().mark(function ve(pe){return o()().wrap(function(Ze){for(;;)switch(Ze.prev=Ze.next){case 0:return Ze.next=2,de(j,{material_no:l==null?void 0:l.no,detail:pe});case 2:return Ze.abrupt("return",Ze.sent);case 3:case"end":return Ze.stop()}},ve)}));return function(ve){return ne.apply(this,arguments)}}()}):""]})]}}]},f=(0,I.useState)(!1),Me=d()(f,2),De=Me[0],V=Me[1];return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Le.Z,{rowKey:"no",maxLength:5,scroll:{x:960},loading:!1,recordCreatorProps:r!=null&&r.enableEdit&&r!==null&&r!==void 0&&r.enableAddMaterial?{position:"bottom",newRecordType:"dataSource",creatorButtonText:(0,e.oz)("add-raw-materials"),onClick:function(){return V(!0)},record:function(){return{no:"R-".concat((0,xe.Z)().slice(0,6)),smi:"-"}}}:!1,columns:r!=null&&r.enableEdit?[].concat(Ke()(Y),Ke()(D())):Y,value:ee,editable:y()(y()({},g),{},{type:"multiple",editableKeys:w,actionRender:function(s,a,l){return s!=null&&s.role&&(s==null?void 0:s.role)==="main_reactant"?[l==null?void 0:l.save,l==null?void 0:l.cancel]:[l==null?void 0:l.save,l==null?void 0:l.cancel,l==null?void 0:l.delete]},onSave:function(){var b=c()(o()().mark(function a(l,z){return o()().wrap(function(T){for(;;)switch(T.prev=T.next){case 0:if(z.role){T.next=2;break}return T.abrupt("return",!1);case 2:return T.next=4,ce(z);case 4:case"end":return T.stop()}},a)}));function s(a,l){return b.apply(this,arguments)}return s}(),onDelete:function(){var b=c()(o()().mark(function a(l,z){return o()().wrap(function(T){for(;;)switch(T.prev=T.next){case 0:return T.next=2,me({project_reaction_id:x,material_no:z==null?void 0:z.no,action:"delete"});case 2:case"end":return T.stop()}},a)}));function s(a,l){return b.apply(this,arguments)}return s}(),onChange:L})}),(0,t.jsx)(Ft.H,{open:De,init:"",onClose:function(){var b=c()(o()().mark(function s(a){return o()().wrap(function(z){for(;;)switch(z.prev=z.next){case 0:if(!a){z.next=3;break}return z.next=3,he(a,{no:"R-".concat((0,xe.Z)().slice(0,6)),smi:a},!0);case 3:V(!1);case 4:case"end":return z.stop()}},s)}));return function(s){return b.apply(this,arguments)}}()})]})}function gt(r){var Z,g=r.curQuoteMoleculeId,j=(0,ze.useModel)("quotation"),x=j.updateQuotes,h=(0,I.useState)([]),E=d()(h,2),_=E[0],w=E[1],L=function(){var ee=c()(o()().mark(function k(H){return o()().wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return u.next=2,x(g,H);case 2:return u.abrupt("return",u.sent);case 3:case"end":return u.stop()}},k)}));return function(H){return ee.apply(this,arguments)}}(),i=function(k){var H=(0,te.cloneDeep)(k);return H.materials.forEach(function(de){var u,me;de.step_no=H==null||(u=H.step_info)===null||u===void 0?void 0:u.step_no,de.step_id=H==null||(me=H.step_info)===null||me===void 0?void 0:me.step_id}),H},X=function(){var k,H=[],de=(0,te.cloneDeep)(r==null||(k=r.quotesDetail)===null||k===void 0?void 0:k.cost_detail);de.forEach(function(u){var me=i(u);H=H.concat(me==null?void 0:me.materials)}),w(H)};return(0,I.useEffect)(function(){var ee;r!=null&&(ee=r.quotesDetail)!==null&&ee!==void 0&&ee.cost_detail&&X()},[r==null||(Z=r.quotesDetail)===null||Z===void 0?void 0:Z.cost_detail]),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Q.Z,{anchorId:"materialTable",word:(0,e.oz)("material-list")}),(0,t.jsx)("div",{className:A.content,children:(0,t.jsx)(Ve,{enableEdit:!(r!=null&&r.disabled),enableAddMaterial:!1,hiddeStep:!1,materialData:_,handleSave:L,quoteMoleculeId:g})})]})}var Ue=n(99814),wt=n(69260),_t=n(31418),Rt=n(86738),bt=n(89575);function jt(r){var Z=r.route_index,g=r.routesNum,j=r.handleStepChange,x=r.disabled;return(0,t.jsxs)(se.Z,{className:Ee()({none:g<=1||x}),children:[Z===0?(0,t.jsx)("a",{className:"disabledTip",children:(0,e.oz)("previous")}):(0,t.jsx)("a",{onClick:function(){return j("prev")},children:(0,e.oz)("previous")}),Z<g-1?(0,t.jsx)("a",{onClick:function(){return j("next")},children:(0,e.oz)("next")}):(0,t.jsx)("a",{className:"disabledTip",children:(0,e.oz)("next")})]})}function Zt(r){var Z,g,j,x,h,E,_,w,L,i=r.curQuoteMoleculeId,X=r.projectCompoundId,ee=r.routeId,k=(0,ze.useParams)(),H=k.id,de=r==null?void 0:r.costDetail,u=de.step_info,me=de.materials,ce=de.labor,he=Ne.A.useForm(),K=d()(he,1),$=K[0],Y=_t.Z.useApp(),G=Y.message,D=(0,ze.useAccess)();(0,I.useEffect)(function(){var Te,ge;$.setFieldsValue({labor:ce,yields:u!=null&&(Te=u.procedure)!==null&&Te!==void 0&&Te.yields?u==null||(ge=u.procedure)===null||ge===void 0?void 0:ge.yields:50})},[ce,u==null||(Z=u.procedure)===null||Z===void 0?void 0:Z.yields]);var f=(0,ze.useModel)("quotation"),Me=f.updateQuotes,De=(0,I.useState)(!1),V=d()(De,2),b=V[0],s=V[1],a=(0,I.useState)(!1),l=d()(a,2),z=l[0],p=l[1],T=(0,I.useState)(!1),ne=d()(T,2),ve=ne[0],pe=ne[1],We=(0,I.useState)(),Ze=d()(We,2),ot=Ze[0],xt=Ze[1],Se=(0,I.useState)(!1),ut=d()(Se,2),ke=ut[0],st=ut[1],nt=(0,I.useState)(!1),yt=d()(nt,2),Fe=yt[0],Et=yt[1],$t=(0,I.useState)(),zt=d()($t,2),It=zt[0],Mt=zt[1],dt=u==null||(g=u.reaction_ids)===null||g===void 0?void 0:g.length,Tt=function(){var Te=c()(o()().mark(function ge(Be){return o()().wrap(function(_e){for(;;)switch(_e.prev=_e.next){case 0:return _e.next=2,Me(i,{reaction_from_index:Be==="prev"?(u==null?void 0:u.index)-1:(u==null?void 0:u.index)+1,project_reaction_id:u==null?void 0:u.step_id},"\u5207\u6362\u6210\u529F");case 2:return _e.abrupt("return",_e.sent);case 3:case"end":return _e.stop()}},ge)}));return function(Be){return Te.apply(this,arguments)}}(),B=function(){var Te=c()(o()().mark(function ge(){var Be;return o()().wrap(function(_e){for(;;)switch(_e.prev=_e.next){case 0:return Be=$.getFieldValue("yields"),_e.next=3,Me(i,{step_id:u==null?void 0:u.step_id,yields:(0,bt.Hq)(Be)});case 3:pe(!1);case 4:case"end":return _e.stop()}},ge)}));return function(){return Te.apply(this,arguments)}}(),Nt=function(){pe(!1),$.setFieldValue("yields",It)};return(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)("div",{className:A.content,children:[(0,t.jsx)("span",{className:A.stepTitle,id:u==null?void 0:u.step_no,children:u==null?void 0:u.step_no}),u!=null&&u.rxn?(0,t.jsx)(Ue.default,{structure:u==null?void 0:u.rxn,className:Ee()(A.structure,"enablePointer")}):"",(0,t.jsxs)(se.Z,{className:A.openLinkWrapper,children:[(D==null||(j=D.authCodeList)===null||j===void 0?void 0:j.includes("view-by-backbone.tab.myReaction.reactionDetail"))&&(0,t.jsx)(Je.Z,{content:(0,e.oz)("open-reaction-details-tip"),children:(0,t.jsx)("a",{className:A.reactionLink,onClick:function(){G.warning((0,e.oz)("modify-reaction-tip")),window.open("/projects/".concat(H,"/reaction/").concat(u==null?void 0:u.step_id),"_blank")},children:(0,e.oz)("open-reaction-details")})}),(D==null||(x=D.authCodeList)===null||x===void 0?void 0:x.includes("quotation-records.button.openRoute"))&&(0,t.jsx)(Je.Z,{content:(0,e.oz)("open-reaction-details-tip"),children:(0,t.jsx)("a",{className:A.reactionLink,onClick:function(){G.warning((0,e.oz)("modify-reaction-tip"));var ge=new URLSearchParams;ge.set("step",u==null?void 0:u.rxn),H&&X&&ee&&window.open("/projects/".concat(H,"/compound/").concat(X,"/view/").concat(ee,"?").concat(ge.toString()),"_blank")},children:(0,e.oz)("open-route-reaction")})})]}),(0,t.jsxs)("div",{className:"flex-justify-space-between",children:[(0,t.jsxs)("div",{className:A.stepTitle,style:{fontWeight:"normal"},children:[(0,e.oz)("menu.list.workspace.myReaction"),dt?"(".concat(dt,")"):""]}),dt?(0,t.jsx)(jt,{routesNum:dt,handleStepChange:Tt,route_index:u==null?void 0:u.index}):""]}),(0,t.jsxs)(we.Z,{children:[(0,t.jsxs)(Re.Z,{span:12,className:A.referenceContent,children:[u!=null&&(h=u.procedure)!==null&&h!==void 0&&h.rxn?(0,t.jsx)(Ue.default,{structure:u==null||(E=u.procedure)===null||E===void 0?void 0:E.rxn,className:Ee()(A.referenceStructure,"enablePointer")}):"",(0,t.jsxs)(Ne.A,{grid:!0,layout:"horizontal",submitter:!1,form:$,colProps:{xs:6,sm:6,md:6,lg:10,offset:1},labelCol:{span:10},wrapperCol:{span:8},children:[(0,t.jsx)(Pe.Z,{name:"yields",label:"".concat((0,e.oz)("yield"),"%"),disabled:!Fe,max:100,extra:r.disabled?"":(0,t.jsx)(Rt.Z,{title:(0,e.oz)("update-confirm-tip"),description:ot,open:ve,onConfirm:B,onCancel:Nt,overlayStyle:{width:"500px"},children:(0,t.jsx)(qe.ZP,{type:"link",loading:z,onClick:c()(o()().mark(function Te(){var ge,Be,Ie,_e;return o()().wrap(function(Xe){for(;;)switch(Xe.prev=Xe.next){case 0:if(ge=$.getFieldValue("yields"),!Fe){Xe.next=13;break}return p(!0),Xe.next=5,(0,fe.service)("quote/yields/".concat(i,"/").concat(H),{method:"POST"}).create({step_id:u==null?void 0:u.step_id,yields:(0,bt.Hq)(ge)});case 5:Be=Xe.sent,Ie=Be.data,_e=Be.error,_e!=null&&_e.message&&($.setFieldValue("yields",It),G.error(_e==null?void 0:_e.message),Et(!Fe)),Ie!=null&&Ie.message&&(pe(!0),xt(Ie==null?void 0:Ie.message)),p(!1),Xe.next=14;break;case 13:Mt(ge);case 14:Et(!Fe);case 15:case"end":return Xe.stop()}},Te)})),className:A.laborEditor,style:{right:(0,e.Ig)()?"-68px":"-62px"},children:Fe?(0,e.oz)("pages.route.edit.label.confirm"):(0,e.oz)("edit")})})}),(0,t.jsx)(Pe.Z,{name:"labor",label:(0,e.oz)("man-days"),disabled:!ke,rules:[{pattern:/^(0*[1-9]\d*\.?\d*|0+\.\d*[1-9]\d*)$/,message:(0,e.oz)("please-input-positive-number")}],extra:r.disabled?"":(0,t.jsx)(qe.ZP,{type:"link",loading:b,onClick:c()(o()().mark(function Te(){var ge;return o()().wrap(function(Ie){for(;;)switch(Ie.prev=Ie.next){case 0:if(ge=$.getFieldValue("labor"),!(!ge||ge<=0)){Ie.next=3;break}return Ie.abrupt("return",G.error((0,e.oz)("please-input-positive-number")));case 3:if(!ke){Ie.next=8;break}return s(!0),Ie.next=7,Me(i,{step_id:u==null?void 0:u.step_id,labor:Number.parseFloat(ge)});case 7:s(!1);case 8:st(!ke);case 9:case"end":return Ie.stop()}},Te)})),className:A.laborEditor,style:{right:(0,e.Ig)()?"-68px":"-62px"},children:ke?(0,e.oz)("pages.route.edit.label.confirm"):(0,e.oz)("edit")})})]})]}),(0,t.jsxs)(Re.Z,{span:12,children:[(0,t.jsx)(wt.Z,{rows:6,procedure:{text:u==null||(_=u.procedure)===null||_===void 0?void 0:_.procedure}}),(u==null||(w=u.procedure)===null||w===void 0?void 0:w.reference_type)&&(0,t.jsxs)("div",{className:A.procedureReference,children:[(0,e.oz)("source"),"\uFF1A",u==null||(L=u.procedure)===null||L===void 0?void 0:L.reference_type]})]})]}),(0,t.jsx)(Ve,{enableEdit:!r.disabled,enableAddMaterial:!0,hiddeStep:!0,projectReactionId:u==null?void 0:u.step_id,materialData:me,quoteMoleculeId:i})]})})}function Ot(r){var Z,g=r.curQuoteMoleculeId,j=(0,Ye.Z)(),x=j.editableConfig,h=(0,I.useState)([]),E=d()(h,2),_=E[0],w=E[1],L=(0,I.useState)([]),i=d()(L,2),X=i[0],ee=i[1],k=(0,ze.useModel)("quotation"),H=k.updateQuotes,de=function(){var he,K=0,$=(0,te.cloneDeep)(r==null||(he=r.quotesDetail)===null||he===void 0?void 0:he.cost_detail);(0,e.qt)($)&&$.length>1&&($.forEach(function(Y){K+=Number(Y==null?void 0:Y.labor)}),K>0&&$.push({step_id:(0,e.oz)("total"),labor:!isNaN(K)&&K?(0,te.toNumber)(K).toFixed(2):""})),ee($)};(0,I.useEffect)(function(){var ce;r!=null&&(ce=r.quotesDetail)!==null&&ce!==void 0&&ce.cost_detail&&de()},[r==null||(Z=r.quotesDetail)===null||Z===void 0?void 0:Z.cost_detail]);var u=function(){return[{title:(0,e.oz)("pages.experiment.label.operation"),valueType:"option",width:200,render:function(K,$,Y,G){return[($==null?void 0:$.step_id)===(0,e.oz)("total")?"":(0,t.jsx)("a",{onClick:function(){var f;G==null||(f=G.startEditable)===null||f===void 0||f.call(G,$==null?void 0:$.step_id)},children:(0,e.oz)("edit")},"editable")]}}]},me=[{title:(0,e.oz)("steps"),dataIndex:["step_info","step_no"],readonly:!0,render:function(he,K){var $,Y;return(K==null?void 0:K.step_id)===(0,e.oz)("total")?K==null?void 0:K.step_id:(0,t.jsx)("a",{href:"#".concat(K==null||($=K.step_info)===null||$===void 0?void 0:$.step_no),children:K==null||(Y=K.step_info)===null||Y===void 0?void 0:Y.step_no})}},{title:(0,e.oz)("estimate-work-day"),dataIndex:"labor"}];return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Q.Z,{anchorId:"laborTable",word:(0,e.oz)("work-time-detils")}),(0,t.jsx)("div",{className:A.content,children:(0,t.jsx)(Le.Z,{rowKey:"step_id",maxLength:5,scroll:{x:960},recordCreatorProps:!1,loading:!1,columns:r!=null&&r.disabled?me:[].concat(me,Ke()(u())),value:X,editable:y()(y()({},x),{},{type:"multiple",editableKeys:_,actionRender:function(he,K,$){return[$.save,$.cancel]},onSave:function(){var ce=c()(o()().mark(function K($,Y){return o()().wrap(function(D){for(;;)switch(D.prev=D.next){case 0:return D.next=2,H(g,{step_id:Y==null?void 0:Y.step_id,labor:Number(Y==null?void 0:Y.labor)});case 2:case"end":return D.stop()}},K)}));function he(K,$){return ce.apply(this,arguments)}return he}(),onChange:w})})})]})}function St(r){var Z,g,j,x=r.curQuoteMoleculeId,h=(0,Ye.Z)(),E=h.chargeDes,_=h.editableConfig,w=(0,ze.useModel)("quotation"),L=w.updateQuotes,i=(0,I.useState)([]),X=d()(i,2),ee=X[0],k=X[1],H=(0,I.useState)([]),de=d()(H,2),u=de[0],me=de[1],ce=(0,I.useState)((0,e.oz)("other-reagent")),he=d()(ce,2),K=he[0],$=he[1],Y=function(b,s,a){if(!(0,te.isEmpty)(b)){var l=(0,te.cloneDeep)(b);if(!(0,te.isEmpty)(l))if(a){var z=l.find(function(p){return(p==null?void 0:p.value)===s||(p==null?void 0:p.label)===s});console.log("---newName02---",s),$(s),z&&(z.disabled=!1)}else l.map(function(p){return p.disabled=(p==null?void 0:p.value)===s||(p==null?void 0:p.label)===s,null});me(l)}},G=function(b,s){var a=K,l=b.findIndex(function(p){return(p==null?void 0:p.name)===(0,e.oz)("other-reagent")||(p==null?void 0:p.name)==="other"})!==-1,z=b.findIndex(function(p){return(p==null?void 0:p.name)===(0,e.oz)("chiral-separation")||(p==null?void 0:p.name)==="separation"})!==-1;l&&z?(Y(s,(0,e.oz)("other-reagent")),Y(s,(0,e.oz)("chiral-separation"))):l?(a=(0,e.oz)("chiral-separation"),Y(s,(0,e.oz)("other-reagent"))):z&&(Y(s,(0,e.oz)("chiral-separation")),a=(0,e.oz)("other-reagent")),$(a)},D=function(){var V=c()(o()().mark(function b(s){var a,l,z,p,T;return o()().wrap(function(ve){for(;;)switch(ve.prev=ve.next){case 0:return ve.next=2,(0,fe.query)("config-dicts").filterDeep("category","eq","cost").get();case 2:if(a=ve.sent,l=a==null?void 0:a.data,z=a==null?void 0:a.error,p=[],!(0,te.isEmpty)(l)&&(0,te.isArray)(l)&&(T=Ke()(l),T.map(function(pe){return p.push({value:pe==null?void 0:pe.code,label:pe==null?void 0:pe.name,disabled:s.findIndex(function(We){return We.name===(pe==null?void 0:pe.code)})!==-1}),null})),!(z!=null&&z.message)){ve.next=8;break}return ve.abrupt("return",at.ZP.error(z==null?void 0:z.message));case 8:l&&me(p),!(0,te.isEmpty)(s)&&!(0,te.isEmpty)(p)&&G(s,p);case 10:case"end":return ve.stop()}},b)}));return function(s){return V.apply(this,arguments)}}();(0,I.useEffect)(function(){!(0,te.isEmpty)(ee)&&!(0,te.isEmpty)(u)&&G(ee,u)},[ee]);var f=function(b){return b.map(function(s){return y()(y()({},s),{},{id:s.name})})};(0,I.useEffect)(function(){var V,b;if((0,e.qt)(r==null||(V=r.quotesDetail)===null||V===void 0?void 0:V.cost_summary)){var s=f(r==null||(b=r.quotesDetail)===null||b===void 0?void 0:b.cost_summary);k(s),D(s)}},[r==null||(Z=r.quotesDetail)===null||Z===void 0?void 0:Z.cost_summary]);var Me=[{title:(0,e.oz)("charge-items"),dataIndex:"name",valueType:"select",fieldProps:function(b,s){var a,l=s.rowIndex;return l+1>(r==null||(a=r.quotesDetail)===null||a===void 0||(a=a.cost_summary)===null||a===void 0?void 0:a.length)?{options:u}:{options:u,disabled:!0}},render:function(b){return E[b]?E[b]:b}},{title:"RMB",dataIndex:"RMB",valueType:"digit"},{title:"USD",dataIndex:"USD",readonly:!0}],De=function(){return[{title:(0,e.oz)("pages.experiment.label.operation"),valueType:"option",width:200,render:function(s,a,l,z){return["total_cost","material_cost","labor_cost"].includes(a==null?void 0:a.name)?"-":[(0,t.jsx)("a",{onClick:function(){var T;z==null||(T=z.startEditable)===null||T===void 0||T.call(z,a.name)},children:(0,e.oz)("edit")},"editable"),(0,t.jsx)("a",{onClick:c()(o()().mark(function p(){return o()().wrap(function(ne){for(;;)switch(ne.prev=ne.next){case 0:return k(ee.filter(function(ve){return ve.name!==a.name})),Y(u,a.name,!0),ne.next=4,L(x,{other_costs:{name:a==null?void 0:a.name,RMB:Number(a==null?void 0:a.RMB),delete:!0}});case 4:case"end":return ne.stop()}},p)})),children:(0,e.oz)("del")},"delete")]}}]};return(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)("div",{className:A.content,children:[(0,t.jsx)("div",{className:A.title,children:"".concat((0,e.oz)("quotation")," Summary")}),(0,t.jsxs)("div",{className:A.quotationSummaryDetail,children:[(0,e.oz)("estimate-delivery"),":\xA0",r==null||(g=r.quotesDetail)===null||g===void 0?void 0:g.delivery_time]}),(0,t.jsxs)("div",{className:A.quotationSummaryDetail,style:{marginBottom:"10px"},children:["".concat((0,e.oz)("quotation")," summary\uFF08RMB\uFF09"),":\xA0",r==null||(j=r.quotesDetail)===null||j===void 0?void 0:j.quotation_summary]}),(0,t.jsx)("div",{className:A.title,style:{marginBottom:"18px"},children:(0,e.oz)("cost-summary")}),(0,t.jsx)(Le.Z,{rowKey:"id",maxLength:5,scroll:{x:960},loading:!1,recordCreatorProps:r!=null&&r.disabled?!1:{position:"bottom",creatorButtonText:(0,e.oz)("add-new-charge"),record:function(){return{id:(Math.random()*1e6).toFixed(0)}}},columns:r!=null&&r.disabled?Me:[].concat(Me,Ke()(De())),value:!(0,te.isEmpty)(u)&&!(0,te.isEmpty)(ee)?ee:[],editable:y()(y()({},_),{},{type:"multiple",onSave:function(){var V=c()(o()().mark(function s(a,l){return o()().wrap(function(p){for(;;)switch(p.prev=p.next){case 0:return p.next=2,L(x,{other_costs:{name:l==null?void 0:l.name,RMB:Number(l==null?void 0:l.RMB)}});case 2:case"end":return p.stop()}},s)}));function b(s,a){return V.apply(this,arguments)}return b}()})})]})})}var lt=function(Z){var g=[];if((0,te.isEmpty)(Z)||!Z)return[];for(var j=0;j<Z.length;j++)for(var x=0;x<((h=Z[j])===null||h===void 0?void 0:h.materials.length);x++){var h,E,_=(E=Z[j])===null||E===void 0?void 0:E.materials[x];if(_!=null&&_.cost_modified){var w;g.push({step_no:(w=Z[j])===null||w===void 0||(w=w.step_info)===null||w===void 0?void 0:w.step_no,name_zh:_==null?void 0:_.name_zh,name_en:_==null?void 0:_.name_en,cas_no:_==null?void 0:_.cas_no,minCost:_==null?void 0:_.cost,cost:_==null?void 0:_.set_cost})}}return g};function At(){var r,Z,g,j,x=(0,ze.useModel)("@@initialState"),h=x.initialState,E=(0,ze.useSearchParams)(),_=d()(E,1),w=_[0],L=(0,ze.useModel)("quotation"),i=L.quotesDetail,X=L.updateQuotesDetail,ee=L.createQuote,k=L.updateQuotes,H=L.updatetDiffCosts,de=L.diffCosts,u=(0,ze.useModel)("login"),me=u.getUserConfigs,ce=(0,I.useState)("g"),he=d()(ce,2),K=he[0],$=he[1],Y=(0,I.useState)(!1),G=d()(Y,2),D=G[0],f=G[1],Me=w.get("type"),De=w.get("compound_no"),V=Me==="create",b=(0,ze.useParams)(),s=b.id,a=b.quoteMoleculeId,l=(0,ze.useAccess)(),z=(0,I.useState)(a),p=d()(z,2),T=p[0],ne=p[1],ve=(0,I.useState)(),pe=d()(ve,2),We=pe[0],Ze=pe[1],ot=(0,I.useState)([]),xt=d()(ot,2),Se=xt[0],ut=xt[1],ke=(0,I.useState)(!1),st=d()(ke,2),nt=st[0],yt=st[1],Fe=!nt&&(i==null?void 0:i.status)==="confirmed",Et=be.Z.useForm(),$t=d()(Et,1),zt=$t[0],It=function(){var R=c()(o()().mark(function S(){var F,C,N;return o()().wrap(function(le){for(;;)switch(le.prev=le.next){case 0:return le.next=2,(0,fe.query)("quote/routes?project_id=".concat(s,"&compound_id=").concat(T)).get();case 2:F=le.sent,C=F.data,N=F.meta,ut(C),Ze(N==null?void 0:N.total);case 7:case"end":return le.stop()}},S)}));return function(){return R.apply(this,arguments)}}(),Mt=function(S){if(S){var F=lt(S==null?void 0:S.cost_detail);H(F)}};(0,I.useEffect)(function(){(0,te.isNil)(i)||(Mt(i),ut([i]))},[i]);var dt=function(S){ze.history.replace("/projects/".concat(s,"/quotation-records/").concat(S,"/quote-info?type=editor&compound_no=").concat(De))},Tt=function(){var R=c()(o()().mark(function S(){var F,C,N;return o()().wrap(function(le){for(;;)switch(le.prev=le.next){case 0:return le.next=2,(0,fe.query)("quotes/".concat(T)).get();case 2:C=le.sent,N=C.data,ut([N]),Ze(N==null||(F=N.quote_route)===null||F===void 0?void 0:F.length),Mt(N),X(N);case 8:case"end":return le.stop()}},S)}));return function(){return R.apply(this,arguments)}}(),B=(0,I.useRef)();(0,I.useEffect)(function(){if(B!=null&&B.current){var R;B==null||(R=B.current)===null||R===void 0||R.setFieldsValue({target_weight:i==null?void 0:i.target_weight,purity:i==null?void 0:i.purity,FTE_unit_price:i==null?void 0:i.FTE_unit_price,ratio:i==null?void 0:i.ratio}),i!=null&&i.target_unit&&$(i==null?void 0:i.target_unit)}},[i]),(0,I.useEffect)(function(){if(B!=null&&B.current&&i!==null&&i!==void 0&&i.project_compound_id){var R;B==null||(R=B.current)===null||R===void 0||R.setFieldsValue({project_compound_id:i==null?void 0:i.project_compound_id})}},[i==null?void 0:i.project_compound_id]);var Nt=(0,I.useState)(),Te=d()(Nt,2),ge=Te[0],Be=Te[1],Ie=(0,I.useState)(0),_e=d()(Ie,2),mt=_e[0],Xe=_e[1];(0,I.useEffect)(function(){if(B!=null&&B.current&&De){var R;B==null||(R=B.current)===null||R===void 0||R.setFieldsValue({compound_no:De})}},[De]);var Xt=(0,I.useState)(),Bt=d()(Xt,2),en=Bt[0],tn=Bt[1],nn=(i==null?void 0:i.status)==="confirmed",an=(["draft","editing"].includes(i==null?void 0:i.status)||!Fe)&&(l==null||(r=l.authCodeList)===null||r===void 0?void 0:r.includes("quotation-records.button.saveDraft")),rn=(["draft","editing"].includes(i==null?void 0:i.status)||!Fe)&&(l==null||(Z=l.authCodeList)===null||Z===void 0?void 0:Z.includes("quotation-records.button.confirmQuote")),Lt=function(){var R=c()(o()().mark(function S(F){var C,N,ie,le;return o()().wrap(function(ye){for(;;)switch(ye.prev=ye.next){case 0:return ye.next=2,B==null||(C=B.current)===null||C===void 0?void 0:C.validateFields();case 2:return N=ye.sent,ye.next=5,k(T,y()(y()({},N),{},{status:F,target_unit:K,project_id:s}),F==="confirmed"?(0,e.oz)("success-confirm-quotate"):(0,e.oz)("success-save-draft"));case 5:ie=ye.sent,le=ie.error,!le&&F==="confirmed"&&yt(!1);case 8:case"end":return ye.stop()}},S)}));return function(F){return R.apply(this,arguments)}}(),ln=function(){var R=c()(o()().mark(function S(){return o()().wrap(function(C){for(;;)switch(C.prev=C.next){case 0:if(!D){C.next=2;break}return C.abrupt("return");case 2:if(f(!0),V){C.next=8;break}return C.next=6,Tt();case 6:C.next=10;break;case 8:return C.next=10,It();case 10:f(!1);case 11:case"end":return C.stop()}},S)}));return function(){return R.apply(this,arguments)}}(),on=function(){var R=c()(o()().mark(function S(){var F,C,N,ie,le;return o()().wrap(function(ye){for(;;)switch(ye.prev=ye.next){case 0:return ye.next=2,me();case 2:F=ye.sent,C=F==null?void 0:F.find(function(Jt){return Jt.setting_label==="quotation"}),B!=null&&B.current&&C&&(B==null||(N=B.current)===null||N===void 0||N.setFieldsValue({FTE_unit_price:(i==null?void 0:i.FTE_unit_price)||(C==null||(ie=C.setting_value)===null||ie===void 0?void 0:ie.FTE_rate),ratio:(i==null?void 0:i.ratio)||(C==null||(le=C.setting_value)===null||le===void 0?void 0:le.ratio)}));case 5:case"end":return ye.stop()}},S)}));return function(){return R.apply(this,arguments)}}();(0,I.useEffect)(function(){Me&&(ln(),on())},[V]),(0,I.useEffect)(function(){return function(){X(null)}},[]);var un=(0,t.jsxs)(je.Z,{className:A.unitSelect,value:K,style:{width:"60px"},onChange:function(S){return $(S)},disabled:!V,children:[(0,t.jsx)(je.Z.Option,{value:"g",children:"g"}),(0,t.jsx)(je.Z.Option,{value:"mg",children:"mg"})]}),sn=function(){var R=c()(o()().mark(function S(F){var C,N,ie,le,ct,ye;return o()().wrap(function(tt){for(;;)switch(tt.prev=tt.next){case 0:if(N=F==="prev"?(i==null?void 0:i.route_index)-1:(i==null?void 0:i.route_index)+1,ie=i==null||(C=i.quote_route[N])===null||C===void 0?void 0:C.quote_id,ie){tt.next=4;break}return tt.abrupt("return");case 4:return tt.next=6,(0,fe.query)("quotes/".concat(ie)).get();case 6:if(le=tt.sent,ct=le.data,ye=le.error,!(ye!=null&&ye.message)){tt.next=13;break}return tt.abrupt("return",at.ZP.error(ye==null?void 0:ye.message));case 13:ct&&(X(ct),ne(ie),window.history.pushState(null,"","".concat(window.location.origin,"/projects/").concat(s,"/quotation-records/").concat(ie,"/quote-info?type=editor&compound_no=").concat(De)));case 14:case"end":return tt.stop()}},S)}));return function(F){return R.apply(this,arguments)}}(),dn=function(){var R=c()(o()().mark(function S(F){var C;return o()().wrap(function(ie){for(;;)switch(ie.prev=ie.next){case 0:C=F==="prev"?mt-1:mt+1,Xe(C);case 2:case"end":return ie.stop()}},S)}));return function(F){return R.apply(this,arguments)}}(),Wt=[{key:"quotationParameter",href:"#quotationParameter",title:(0,e.oz)("quote-parmas")},{key:"confirmedRoute",href:"#confirmedRoute",title:(0,e.oz)("confirmed-route")}],Qt=function(S){var F=S.stepChange,C=S.route,N=S.routeIndex;if(!C)return"";var ie=(C==null?void 0:C.project_route_id)||(C==null?void 0:C.id);return Be(ie),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex-justify-space-between",children:[(0,t.jsxs)("div",{children:[(0,e.oz)("route-id"),"\uFF1A",ie&&(0,t.jsx)(vt.Z.Link,{href:"/projects/".concat(s,"/compound/").concat(C==null?void 0:C.project_compound_id,"/view/").concat(ie),target:"_blank",children:ie})]}),(0,t.jsx)(jt,{disabled:Fe,routesNum:We,handleStepChange:F,route_index:N})]}),(0,t.jsx)(ue.Z,{route:C,hiddenTitle:!0},ie)]})},cn=function(){return(0,te.isEmpty)(Se)||!(0,te.isArray)(Se)?"":V?(0,t.jsx)(Qt,{route:Se[mt],stepChange:dn,routeIndex:mt},"routeItem-item"):Se==null?void 0:Se.map(function(S,F){return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(Qt,{route:S,stepChange:sn,routeIndex:i==null?void 0:i.route_index},"routeItem-".concat(F))})})},vn=(0,I.useState)(),Kt=d()(vn,2),Vt=Kt[0],fn=Kt[1],mn=(0,I.useState)(),Ut=d()(mn,2),hn=Ut[0],pn=Ut[1],kt=(0,U.I)(),gn=kt.dialogProps,_n=kt.confirm,qt=function(S,F){var C=S._curQuoteMoleculeId,N=S._repeatedWeight,ie=S._quotationNo;N?(_n(),fn(N),pn(ie)):(ne(C),F&&ne(C),dt(C))},xn=(0,I.useState)(!1),Ht=d()(xn,2),yn=Ht[0],Yt=Ht[1],Gt=function(){var S;B==null||(S=B.current)===null||S===void 0||S.validateFields().then(function(){var F=c()(o()().mark(function C(N){return o()().wrap(function(le){for(;;)switch(le.prev=le.next){case 0:return Yt(!0),le.next=3,ee(y()(y()({},N),{},{target_unit:K,project_id:s,route_id:ge}),function(ct){return qt(ct,!1)});case 3:Yt(!1);case 4:case"end":return le.stop()}},C)}));return function(C){return F.apply(this,arguments)}}())},Cn=function(){var R=c()(o()().mark(function S(){var F;return o()().wrap(function(N){for(;;)switch(N.prev=N.next){case 0:return N.next=2,(0,Ce.CV)({routeParams:"".concat(hn,"/").concat(s)});case 2:F=N.sent,(0,Ce.y6)(F).ok&&Gt();case 4:case"end":return N.stop()}},S)}));return function(){return R.apply(this,arguments)}}(),et=Se==null?void 0:Se[mt];return(0,t.jsxs)("div",{className:A.quoteInfo,children:[(0,t.jsx)(it,{diffCostData:de,openEvent:en,confirmDiff:c()(o()().mark(function R(){return o()().wrap(function(F){for(;;)switch(F.prev=F.next){case 0:return H([]),F.next=3,Lt("confirmed");case 3:case"end":return F.stop()}},R)}))}),(0,t.jsxs)($e._z,{className:Ee()(A.quoteDetail,O()(O()(O()(O()({},A.unfoldWidth,!(h!=null&&h.isMenuCollapsed)),A.foldWidth,h==null?void 0:h.isMenuCollapsed),A.unfoldWidth_EN,!(h!=null&&h.isMenuCollapsed)&&(0,e.Ig)()),A.foldWidth_EN,(h==null?void 0:h.isMenuCollapsed)&&(0,e.Ig)())),children:[(0,t.jsx)(Q.Z,{anchorId:"quotationParameter",word:(0,e.oz)("quote-parmas")}),(0,t.jsxs)(Ne.A,{grid:!0,layout:"horizontal",submitter:!1,formRef:B,colProps:{xs:12,sm:12,md:8,lg:6},labelCol:{span:(0,e.Ig)()?12:10},wrapperCol:{span:(0,e.Ig)()?12:14},initialValues:{project_compound_id:T},children:[(0,t.jsx)(Ae.Z,{name:"compound_no",label:(0,e.oz)("molecules-no"),labelCol:{span:((0,e.Ig)(),10)},wrapperCol:{span:((0,e.Ig)(),14)},disabled:!0}),(0,t.jsx)(Ae.Z,{name:"project_compound_id",label:(0,e.oz)("project-molecule-id"),labelCol:{span:(0,e.Ig)()?14:10},wrapperCol:{span:(0,e.Ig)()?10:14},disabled:!0}),(0,t.jsx)(Pe.Z,{name:"target_weight",disabled:!V,label:(0,e.oz)("weight"),extra:un,rules:[{required:!0}],labelCol:{span:8},wrapperCol:{span:12},min:.1}),(0,t.jsx)(Pe.Z,{name:"purity",disabled:!V,rules:[{required:!0}],label:(0,e.oz)("purity"),min:.1,max:100,labelCol:{span:(0,e.Ig)()?12:10},wrapperCol:{span:8},addonAfter:(0,t.jsx)("div",{className:A.unitSelect,style:{right:"-24px",top:"5px"},children:"%"})}),(0,t.jsx)(Pe.Z,{name:"FTE_unit_price",label:(0,e.oz)("FTE-per-day"),labelCol:{span:((0,e.Ig)(),10)},wrapperCol:{span:((0,e.Ig)(),10)},rules:[{required:!0}],disabled:Fe,extra:(0,t.jsx)("div",{className:A.unitSelect,style:{top:"5px",right:(0,e.Ig)()?"-92px":"-40px"},children:(0,e.oz)("FTE-per-day-unit")})}),(0,t.jsx)(Pe.Z,{name:"ratio",label:(0,e.oz)("coefficient"),rules:[{required:!0}],labelCol:{span:(0,e.Ig)()?14:10},wrapperCol:{span:(0,e.Ig)()?8:14},disabled:Fe,extra:Fe||V?"":(0,t.jsx)("a",{className:A.unitSelect,style:{top:"5px",right:(0,e.Ig)()?"-130px":"-60px"},onClick:c()(o()().mark(function R(){var S,F;return o()().wrap(function(N){for(;;)switch(N.prev=N.next){case 0:return N.next=2,k(T,{FTE_unit_price:B==null||(S=B.current)===null||S===void 0?void 0:S.getFieldValue("FTE_unit_price"),ratio:B==null||(F=B.current)===null||F===void 0?void 0:F.getFieldValue("ratio")});case 2:Tt();case 3:case"end":return N.stop()}},R)})),children:(0,e.oz)("calculate-quotation")}),min:.1,max:100}),(0,t.jsx)("div",{className:A.operateButton,children:!V||nt?(0,t.jsxs)(se.Z,{children:[an?(0,t.jsx)(M.Z,{onClick:function(){return Lt("editing")},children:(0,e.oz)("save-draft")}):"",rn?(0,t.jsx)(M.Z,{onClick:function(){if(!(0,te.isEmpty)(de))return tn({open:!0});Lt("confirmed")},children:(0,e.oz)("confirm-quote")}):"",nn?(0,t.jsx)(t.Fragment,{children:!nt&&(0,t.jsxs)(t.Fragment,{children:[(l==null||(g=l.authCodeList)===null||g===void 0?void 0:g.includes("quotation-records.button.editQuote"))&&(0,t.jsx)(qe.ZP,{onClick:function(){return yt(!0)},children:(0,e.oz)("edit")}),(0,t.jsx)(rt,{form:zt,initalValues:{compound_no:De,purity:i==null?void 0:i.purity,ratio:i==null?void 0:i.ratio},onFinish:function(S){ee(y()(y()({},S),{},{quote_id:T}),function(F){return qt(F,!0)})}})]})}):""]}):(0,t.jsx)(M.Z,{onClick:Gt,loading:yn,children:(0,e.oz)("pages.route.edit.label.confirm")})})]}),(0,t.jsx)(Q.Z,{anchorId:"confirmedRoute",word:!V&&(0,e.qt)(Se)?(0,e.oz)("quotate-route"):"".concat((0,e.oz)("confirmed-route")).concat(We?"\uFF08".concat(We,"\uFF09"):"")}),(0,t.jsxs)("div",{className:A.routeInfo,children:[(0,t.jsx)(cn,{}),V?"":(0,t.jsx)(St,{quotesDetail:i,disabled:Fe,curQuoteMoleculeId:T})]}),V?"":(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(gt,{quotesDetail:i,disabled:Fe,curQuoteMoleculeId:T}),(0,t.jsx)(Ot,{quotesDetail:i,disabled:Fe,curQuoteMoleculeId:T}),(0,t.jsx)(Q.Z,{anchorId:"reactionDetail",word:(0,e.oz)("reaction-details")}),!(0,te.isEmpty)(i==null?void 0:i.cost_detail)&&(0,te.isArray)(i==null?void 0:i.cost_detail)?i==null||(j=i.cost_detail)===null||j===void 0?void 0:j.map(function(R,S){return(0,t.jsx)(Zt,{disabled:Fe,costDetail:R,curQuoteMoleculeId:T,projectCompoundId:et==null?void 0:et.project_compound_id,routeId:(et==null?void 0:et.project_route_id)||(et==null?void 0:et.id)},"reaction-".concat(S))}):""]})]}),(0,t.jsxs)("div",{style:{flex:(0,e.Ig)()?"0 0 170px":"0 0 120px"},children:[(0,t.jsx)(Dt.Z,{wrapClassName:(0,e.Ig)()?A.anchorEN:A.anchor,items:V?Wt:Wt.concat([{key:"materialTable",href:"#materialTable",title:(0,e.oz)("material-list")},{key:"laborTable",href:"#laborTable",title:(0,e.oz)("work-time-detils")},{key:"reactionDetail",href:"#reactionDetail",title:(0,e.oz)("reaction-details")}])}),(0,t.jsx)(P.Z,y()(y()({},gn),{},{title:(0,e.oz)("same-quatation"),width:800,onConfirm:Cn,children:(0,t.jsx)("span",{style:{fontSize:"20px"},children:(0,e.Ig)()?"A quotation already exists for the molecule ".concat(Vt,". Would you like to requote (the previous quotation will be deleted)?"):(0,t.jsxs)(t.Fragment,{children:["\u5DF2\u7ECF\u5B58\u5728\u8FD9\u4E2A\u5206\u5B50\xA0",(0,t.jsx)("span",{style:{color:"red"},children:Vt}),"\xA0 \u7684\u62A5\u4EF7\uFF0C\u662F\u5426\u91CD\u65B0\u62A5\u4EF7\uFF08\u539F\u6765\u7684\u62A5\u4EF7\u4F1A\u88AB\u5220\u9664\uFF09?"]})})}))]})]})}},94860:function(Oe,ae,n){n.d(ae,{A:function(){return c},m:function(){return W}});var q=n(19632),O=n.n(q),m=n(5574),y=n.n(m),oe=n(15101),o=function d(M){var P=M.children,U=P===void 0?[]:P;return U.length?U.map(function(Q){return d(Q)}).reduce(function(Q,ue){return Math.max(Q,ue)})+1:1},J=function(M,P){var U=(0,oe.Zk)(M),Q=y()(U,2),ue=Q[0],Ce=Q[1];if(ue.length===0){if(Ce.length===0)return[null,[]];var fe=Ce.reduce(function(be,je){return be.value.length>je.value.length?be:je}),e=Ce.find(function(be){return be.value===P}),$e=e||fe,Ne=Ce.filter(function(be){return be!==$e});return[$e,Ne]}var Ae=ue.reduce(function(be,je){return o(be)>=o(je)?be:je}),Pe=ue.filter(function(be){var je=be.value;return je!==Ae.value});return[Ae,[].concat(O()(Pe),O()(Ce))]},c=function d(M){var P=M.value,U=M.mainMaterial,Q=M.children,ue=Q===void 0?[]:Q,Ce=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],fe=J(ue,U),e=y()(fe,1),$e=e[0],Ne=[].concat(O()(Ce),[P]);return{value:P,child:$e?d($e,Ne):void 0,rxn:"".concat(ue.map(function(Ae){return Ae.value}).join("."),">").concat(P),path:Ne}},W=function(M){return M.reduceRight(function(P,U,Q){var ue=M.slice(0,Q+1);return P.value?{value:U,child:P,path:ue}:{value:U,path:ue}},{value:"",path:[]})}},15101:function(Oe,ae,n){n.d(ae,{WN:function(){return J},Zk:function(){return oe},yn:function(){return o}});var q=n(96486),O=n.n(q),m=function c(W,d){var M=W.value,P=W.children;return P!=null&&P.length?{value:M,children:P.map(function(U){return c(U,M)}),parent:d}:{value:M,parent:d}},y=function c(W,d){var M,P=W.target,U=W.rxn,Q=W.children,ue=U==null||(M=U.split(">>")[0])===null||M===void 0?void 0:M.split(".");if(!ue)return{value:P,parent:d};var Ce=groupBy(Q,function(fe){var e=fe.target;return e});return{value:P,children:ue.map(function(fe){var e;return(e=Ce[fe])!==null&&e!==void 0&&e.length?c(Ce[fe][0],P):{value:fe,parent:P}}),parent:d,rxn:U}},oe=function(){var W=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];return W.reduce(function(d,M){var P;return(P=M.children)!==null&&P!==void 0&&P.length?d[0].push(M):d[1].push(M),d},[[],[]])},o=function c(W){var d;return(d=W.children)!==null&&d!==void 0&&d.length?W.children.reduce(function(M,P){return M+c(P)},0)+1:0},J=function c(W){var d=W.children;if(!(d!=null&&d.length))return!1;var M=d.reduce(function(P,U){var Q;return P+((Q=U.children)!==null&&Q!==void 0&&Q.length?1:0)},0);return M>=2?!0:d.reduce(function(P,U){return P||c(U)},!1)}}}]);

//# sourceMappingURL=p__projects__quotation-records__detail__index.282efeee.async.js.map