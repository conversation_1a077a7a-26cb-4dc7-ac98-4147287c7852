"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[1558],{88284:function(G,y,t){var h=t(1413),u=t(67294),A=t(32857),d=t(84089),O=function(D,l){return u.createElement(d.Z,(0,h.Z)((0,h.Z)({},D),{},{ref:l,icon:A.Z}))},s=u.forwardRef(O);y.Z=s},85175:function(G,y,t){var h=t(1413),u=t(67294),A=t(48820),d=t(84089),O=function(D,l){return u.createElement(d.Z,(0,h.Z)((0,h.Z)({},D),{},{ref:l,icon:A.Z}))},s=u.forwardRef(O);y.Z=s},47389:function(G,y,t){var h=t(1413),u=t(67294),A=t(27363),d=t(84089),O=function(D,l){return u.createElement(d.Z,(0,h.Z)((0,h.Z)({},D),{},{ref:l,icon:A.Z}))},s=u.forwardRef(O);y.Z=s},45098:function(G,y,t){t.d(y,{Z:function(){return D}});var h=t(97857),u=t.n(h),A=t(67294),d=t(4019),O=t(99814),s=t(85893),R=function l(p){var F=p.node,b=p.node,B=b.value,w=b.child,Y=b.rxn,C=p.withWrapper,i=p.customChildren,S=i===void 0?{}:i,N=p.customProps,T=N===void 0?{}:N,P=S.structure,L=S.reaction,H=T.structureWrapper,E=T.reactionWrapper,K=(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",u()(u()({},H),{},{children:[(0,s.jsx)(O.default,{structure:B,className:"synthetic-link-display-node"}),P==null?void 0:P(B,F)]})),w&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",u()(u()({title:Y},E),{},{className:"arrow-wrapper ".concat(E==null?void 0:E.className),children:[(0,s.jsx)(d.Z,{}),L==null?void 0:L(B,w.value,F)]})),(0,s.jsx)(l,{node:w,customChildren:S,customProps:T})]})]});return C?(0,s.jsx)("div",{className:"synthetic-link-display-node-root",children:K}):(0,s.jsx)(s.Fragment,{children:K})},D=R},7361:function(G,y,t){t.d(y,{Z:function(){return ht}});var h=t(19632),u=t.n(h),A=t(15009),d=t.n(A),O=t(99289),s=t.n(O),R=t(5574),D=t.n(R),l=t(67294),p=Object.defineProperty,F=Object.getOwnPropertySymbols,b=Object.prototype.hasOwnProperty,B=Object.prototype.propertyIsEnumerable,w=(o,r,n)=>r in o?p(o,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):o[r]=n,Y=(o,r)=>{for(var n in r||(r={}))b.call(r,n)&&w(o,n,r[n]);if(F)for(var n of F(r))B.call(r,n)&&w(o,n,r[n]);return o};const C=o=>l.createElement("svg",Y({className:"collected_svg__icon",viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg"},o),l.createElement("path",{d:"m894.4 354.6-216.5-31.1-96.8-194.3c-28.2-56.7-109.5-56.7-137.7 0l-96.8 194.3L130 354.6c-63 9-88.3 86.1-42.6 130.3L244 636.1l-37 213.4c-11 62.3 55 109.8 111.4 80.6l193.8-100.9 193.7 100.9c56.3 29.3 122.3-18.2 111.4-80.6l-36.9-213.4L937 484.9c45.8-44.2 20.5-121.3-42.6-130.3zM692.4 537l-49.6 48 12.3 71.4c1.7 9.5-1.9 19.1-9.3 25.2-7.4 6.1-17.5 7.9-26.6 4.5-9-3.3-15.6-11.2-17.2-20.7l-14.7-85.3c-1.5-8.7 1.4-17.6 7.8-23.8l59.8-57.8c10.7-10.3 27.7-10.1 38.1.6 10.4 10.6 10.1 27.6-.6 37.9z",fill:"#f4ea2a"}));var i="data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJtODk0LjQgMzU0LjYtMjE2LjUtMzEuMS05Ni44LTE5NC4zYy0yOC4yLTU2LjctMTA5LjUtNTYuNy0xMzcuNyAwbC05Ni44IDE5NC4zTDEzMCAzNTQuNmMtNjMgOS04OC4zIDg2LjEtNDIuNiAxMzAuM0wyNDQgNjM2LjFsLTM3IDIxMy40Yy0xMSA2Mi4zIDU1IDEwOS44IDExMS40IDgwLjZsMTkzLjgtMTAwLjkgMTkzLjcgMTAwLjljNTYuMyAyOS4zIDEyMi4zLTE4LjIgMTExLjQtODAuNmwtMzYuOS0yMTMuNEw5MzcgNDg0LjljNDUuOC00NC4yIDIwLjUtMTIxLjMtNDIuNi0xMzAuM3pNNjkyLjQgNTM3bC00OS42IDQ4IDEyLjMgNzEuNGMxLjcgOS41LTEuOSAxOS4xLTkuMyAyNS4yLTcuNCA2LjEtMTcuNSA3LjktMjYuNiA0LjUtOS0zLjMtMTUuNi0xMS4yLTE3LjItMjAuN2wtMTQuNy04NS4zYy0xLjUtOC43IDEuNC0xNy42IDcuOC0yMy44bDU5LjgtNTcuOGMxMC43LTEwLjMgMjcuNy0xMC4xIDM4LjEuNiAxMC40IDEwLjYgMTAuMSAyNy42LS42IDM3Ljl6IiBmaWxsPSIjZjRlYTJhIi8+PC9zdmc+",S=Object.defineProperty,N=Object.getOwnPropertySymbols,T=Object.prototype.hasOwnProperty,P=Object.prototype.propertyIsEnumerable,L=(o,r,n)=>r in o?S(o,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):o[r]=n,H=(o,r)=>{for(var n in r||(r={}))T.call(r,n)&&L(o,n,r[n]);if(N)for(var n of N(r))P.call(r,n)&&L(o,n,r[n]);return o};const E=o=>l.createElement("svg",H({viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o),l.createElement("path",{d:"M4.039 2.137a.58.58 0 0 1 .992-.414l4.748 4.748a.581.581 0 0 1 0 .823l-4.743 4.742a.58.58 0 1 1-.822-.822L8.548 6.88 4.214 2.546a.573.573 0 0 1-.17-.415l-.005.006Z"}));var K="data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMTQgMTQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQuMDM5IDIuMTM3YS41OC41OCAwIDAgMSAuOTkyLS40MTRsNC43NDggNC43NDhhLjU4MS41ODEgMCAwIDEgMCAuODIzbC00Ljc0MyA0Ljc0MmEuNTguNTggMCAxIDEtLjgyMi0uODIyTDguNTQ4IDYuODggNC4yMTQgMi41NDZhLjU3My41NzMgMCAwIDEtLjE3LS40MTVsLS4wMDUuMDA2WiIvPjwvc3ZnPg==",te=t(83335),J=Object.defineProperty,se=Object.getOwnPropertySymbols,Be=Object.prototype.hasOwnProperty,We=Object.prototype.propertyIsEnumerable,ge=(o,r,n)=>r in o?J(o,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):o[r]=n,Ue=(o,r)=>{for(var n in r||(r={}))Be.call(r,n)&&ge(o,n,r[n]);if(se)for(var n of se(r))We.call(r,n)&&ge(o,n,r[n]);return o};const Ge=o=>l.createElement("svg",Ue({viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o),l.createElement("path",{d:"M10 7.1a1.1 1.1 0 1 0 0-2.2 1.1 1.1 0 0 0 0 2.2ZM11 9a1 1 0 1 0-2 0v5a1 1 0 1 0 2 0V9Z"}),l.createElement("path",{d:"M10 20C4.49 20 0 15.51 0 10S4.49 0 10 0s10 4.49 10 10-4.49 10-10 10Zm0-18c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8Z"}));var Zt="data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjAgMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEwIDcuMWExLjEgMS4xIDAgMSAwIDAtMi4yIDEuMSAxLjEgMCAwIDAgMCAyLjJaTTExIDlhMSAxIDAgMSAwLTIgMHY1YTEgMSAwIDEgMCAyIDBWOVoiLz48cGF0aCBkPSJNMTAgMjBDNC40OSAyMCAwIDE1LjUxIDAgMTBTNC40OSAwIDEwIDBzMTAgNC40OSAxMCAxMC00LjQ5IDEwLTEwIDEwWm0wLTE4Yy00LjQxIDAtOCAzLjU5LTggOHMzLjU5IDggOCA4IDgtMy41OSA4LTgtMy41OS04LTgtOFoiLz48L3N2Zz4=",fe=t(15001),He=t(61487),Qe=t(85670),ne=t(87172),je=t(15101),a=t(32884),Me=t(38545),xe=t(75750),Ve=t(55287),$e=t(26363),Ye=t(27521),re=t(1413),Ke={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M926 164H94c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V196c0-17.7-14.3-32-32-32zm-40 632H134V236h752v560zm-658.9-82.3c3.1 3.1 8.2 3.1 11.3 0l172.5-172.5 114.4 114.5c3.1 3.1 8.2 3.1 11.3 0l297-297.2c3.1-3.1 3.1-8.2 0-11.3l-36.8-36.8a8.03 8.03 0 00-11.3 0L531 565 416.6 450.5a8.03 8.03 0 00-11.3 0l-214.9 215a8.03 8.03 0 000 11.3l36.7 36.9z"}}]},name:"fund",theme:"outlined"},Je=Ke,ye=t(84089),Xe=function(r,n){return l.createElement(ye.Z,(0,re.Z)((0,re.Z)({},r),{},{ref:n,icon:Je}))},ke=l.forwardRef(Xe),qe=ke,_e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M342 472h342c.4 0 .9 0 1.3-.1 4.4-.7 7.3-4.8 6.6-9.2l-40.2-248c-.6-3.9-4-6.7-7.9-6.7H382.2c-3.9 0-7.3 2.8-7.9 6.7l-40.2 248c-.1.4-.1.9-.1 1.3 0 4.4 3.6 8 8 8zm91.2-196h159.5l20.7 128h-201l20.8-128zm2.5 282.7c-.6-3.9-4-6.7-7.9-6.7H166.2c-3.9 0-7.3 2.8-7.9 6.7l-40.2 248c-.1.4-.1.9-.1 1.3 0 4.4 3.6 8 8 8h342c.4 0 .9 0 1.3-.1 4.4-.7 7.3-4.8 6.6-9.2l-40.2-248zM196.5 748l20.7-128h159.5l20.7 128H196.5zm709.4 58.7l-40.2-248c-.6-3.9-4-6.7-7.9-6.7H596.2c-3.9 0-7.3 2.8-7.9 6.7l-40.2 248c-.1.4-.1.9-.1 1.3 0 4.4 3.6 8 8 8h342c.4 0 .9 0 1.3-.1 4.3-.7 7.3-4.8 6.6-9.2zM626.5 748l20.7-128h159.5l20.7 128H626.5z"}}]},name:"gold",theme:"outlined"},et=_e,tt=function(r,n){return l.createElement(ye.Z,(0,re.Z)((0,re.Z)({},r),{},{ref:n,icon:et}))},nt=l.forwardRef(tt),rt=nt,at=t(85175),ot=t(47389),it=t(88284),st=t(36223),Q=t(55241),le=t(45360),Ae=t(66309),lt=t(96074),ct=t(28036),ut=t(93967),Ie=t.n(ut),X=t(96486),Z=t(70831),k={"card-title":"card-title___qvJJH","left-side":"left-side___KQ09D",groupItem:"groupItem___kVtWV",barnchTag:"barnchTag___qHTBV","right-side":"right-side___tXzEy"},e=t(85893),dt=(0,a.Gk)(),Oe=dt.winWidth;function q(o){var r=o.icon,n=o.title;return Oe<=1680?(0,e.jsx)(Q.Z,{content:n,children:r}):(0,e.jsxs)(e.Fragment,{children:[r," ",n]})}var vt={canceled:[],confirmed:["canceled"],editing:["canceled"],finished:[]},mt=function(r){var n=r.item,ae=r.route,ce=r.actions,Ce=r.stepNum,pt=Ce===void 0?ae?(0,je.yn)(ae):0:Ce,De=r.hasBranch,Le=De===void 0?ae?(0,je.WN)(ae):!1:De,gt=r.hasBranches,Ee=r.updateTime,oe=r.commendCount,Se=r.routeName,ue=r.onAction,Ne=r.updatedAt,I=r.routeNo,Te=r.group,V=r.routeStatus,ft=r.handleFilterSimilar,de=r.targetMolecule,jt=r.isCollected,Mt=(0,Z.useSearchParams)(),Pe=D()(Mt,2),xt=Pe[0],yt=Pe[1],At=(0,He.f)(),ze=At.fetch,W=(0,Z.useModel)("compound"),It=W.updatePagenate,Ot=W.getMyRoutesData,ie=W.routeType,ve=W.curFilterInfo,Ct=W.collectedEvent,Dt=W.curIsCollected,$=W.curHistoryInfo,z=(0,Z.useAccess)(),Lt=(0,Z.useModel)("@@initialState"),me=Lt.initialState,we=(0,Z.useParams)(),Ze=we.id,_=we.compoundId,he=($==null?void 0:$.status)==="running",Re=function(){var f=s()(d()().mark(function v(){var c;return d()().wrap(function(g){for(;;)switch(g.prev=g.next){case 0:return c=(0,a.Hq)(xt.get("pageSize")||"")||10,It({page:1,pageSize:c}),g.next=4,Ot((0,a.Hq)(_));case 4:yt({page:"1",pageSize:c.toString()},{replace:!0});case 5:case"end":return g.stop()}},v)}));return function(){return f.apply(this,arguments)}}(),Et=function(){var f=s()(d()().mark(function v(){var c,M;return d()().wrap(function(j){for(;;)switch(j.prev=j.next){case 0:if(!(!_||!I)){j.next=2;break}return j.abrupt("return");case 2:return j.next=4,ze((0,ne.service)("project-compounds").update(_,{default_route:I}));case 4:c=j.sent,M=c.data,M&&(le.ZP.success((0,a.oz)("default-route-set")),Re());case 7:case"end":return j.stop()}},v)}));return function(){return f.apply(this,arguments)}}(),St=function(){var f=s()(d()().mark(function v(){var c,M,g,j,m,U;return d()().wrap(function(x){for(;;)switch(x.prev=x.next){case 0:if(!he){x.next=2;break}return x.abrupt("return");case 2:if(g=Dt(n),!g){x.next=9;break}return x.next=6,(0,ne.service)("collected-retro-backbones").deleteOne(n==null||(c=n.collected_retro_backbones)===null||c===void 0||(c=c[0])===null||c===void 0?void 0:c.id);case 6:x.t0=x.sent,x.next=12;break;case 9:return x.next=11,(0,ne.service)("collected-retro-backbones").create({user_id:String(me==null||(M=me.userInfo)===null||M===void 0?void 0:M.id),retro_backbone:I});case 11:x.t0=x.sent;case 12:j=x.t0,m=j.data,m&&(U=(0,X.cloneDeep)(n==null?void 0:n.collected_retro_backbones),g?U=[]:U.unshift({id:m==null?void 0:m.id,user_id:m==null?void 0:m.user_id}),Ct(I,U),le.ZP.success((0,a.oz)("operate-success")));case 15:case"end":return x.stop()}},v)}));return function(){return f.apply(this,arguments)}}(),Nt=function(){var f=s()(d()().mark(function v(c){var M,g;return d()().wrap(function(m){for(;;)switch(m.prev=m.next){case 0:if(I){m.next=2;break}return m.abrupt("return");case 2:return m.next=4,ze((0,ne.service)("project-routes").update(I,{status:c}));case 4:M=m.sent,g=M.data,g&&(le.ZP.success((0,a.oz)("success-update-status")),Re());case 7:case"end":return m.stop()}},v)}));return function(c){return f.apply(this,arguments)}}(),Tt=(0,Z.useLocation)(),Pt=Tt.pathname,ee=Pt.includes("/playground"),Fe=(0,a.zx)("viewdRoute")?JSON.parse((0,a.zx)("viewdRoute")):[],zt=function(){var v=[].concat(u()(Fe),[I]);(0,a.mW)("viewdRoute",JSON.stringify(v)),ie==="aiGenerated"?Z.history.push(ee?"/playground/commend/view-by-backbone/".concat(I):"/projects/".concat(Ze,"/compound/").concat(_,"/view-by-backbone/").concat(I)):Z.history.push("/projects/".concat(Ze,"/compound/").concat(_,"/").concat(V==="editing"?"edit":"view","/").concat(I))},pe=oe&&Number(oe)>0,wt=function(v){var c,M,g,j,m=v.action,U=pe?"".concat((0,a.oz)("comment"),"\uFF08").concat(oe,"\uFF09"):(0,a.oz)("comment");switch(m){case"feedback":return z!=null&&(c=z.authCodeList)!==null&&c!==void 0&&c.includes("compound.button.feedback")?Oe<=1680?(0,e.jsxs)(Q.Z,{content:U,children:[(0,e.jsx)(Me.Z,{}),pe?"\uFF08".concat(oe,"\uFF09"):""]}):(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(Me.Z,{})," ",U]}):null;case"collect":return z!=null&&(M=z.authCodeList)!==null&&M!==void 0&&M.includes("compound.button.collect")?(0,e.jsx)("div",{onClick:St,children:jt?(0,e.jsx)(q,{icon:(0,e.jsx)(C,{width:15,style:{position:"relative",top:"2px"}}),title:(0,a.oz)("unfavorite")}):he?(0,e.jsx)(Q.Z,{content:(0,a.oz)("temporary-route-tip"),children:(0,e.jsx)(xe.Z,{style:{color:"rgba(85, 85, 85, 1)"}})}):(0,e.jsx)(q,{icon:(0,e.jsx)(xe.Z,{}),title:(0,a.oz)("favorite")})}):null;case"view":return z!=null&&(g=z.authCodeList)!==null&&g!==void 0&&g.includes("compound.button.view")?(0,e.jsx)("div",{className:Ie()({complete:Fe.includes(I)}),onClick:zt,children:(0,e.jsx)(q,{icon:(0,e.jsx)(Ve.Z,{}),title:(0,a.oz)("pages.projectTable.actionLabel.viewDetail")})}):null;case"setAsDefault":return(0,e.jsx)("div",{onClick:Et,children:ie==="myRoutes"&&I===(de==null||(j=de.default_route)===null||j===void 0?void 0:j.id)?(0,e.jsx)(q,{icon:(0,e.jsx)($e.Z,{}),title:(0,a.oz)("default-route")}):(0,e.jsx)(q,{icon:(0,e.jsx)(Ye.Z,{}),title:(0,a.oz)("set-default-route")})});case"experimentDesign":return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(qe,{}),(0,a.oz)("experiment-design")]});case"materialDosage":return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(rt,{}),(0,a.oz)("material-demand")]});case"copyRoute":return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(at.Z,{}),(0,a.oz)("copy-route")]});case"edit":return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(ot.Z,{}),(0,a.oz)("edit")]});case"apply":return(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(it.Z,{}),(0,a.oz)("apply")]});default:return null}},be=V?vt[V]:[];return(0,e.jsxs)("div",{className:k["card-title"],children:[(0,e.jsxs)("div",{className:k["left-side"],children:[!(0,X.isNil)(Te)&&(ve==null?void 0:ve.group)!=="ungrouped"&&(0,e.jsx)("div",{className:Ie()("enablePointer flex-justify-space-between flex-align-items-center",k.groupItem),onClick:ft,children:(0,e.jsxs)(Q.Z,{content:(0,a.oz)("filter-routes-group"),children:[(0,a.oz)("new-group")," ",Te+1,(0,e.jsx)(E,{width:14,fill:"#336CC9"})]})}),(0,e.jsxs)("div",{children:["\xA0\xA0",(0,a.oz)("route-id"),": ",I]}),ie!=="aiGenerated"&&Se?(0,e.jsxs)(e.Fragment,{children:["\xA0\xA0",(0,e.jsx)(Ae.Z,{color:"#2db7f5",children:Se})]}):"",!ee&&n&&!(0,X.isNil)(n.min_n_main_tree_steps)?(0,e.jsxs)(e.Fragment,{children:["\xA0\u2022\xA0",(0,e.jsx)(Q.Z,{content:(0,a.oz)("multi-steps-tip"),children:(0,e.jsx)(te.r,{width:18,style:{cursor:"pointer"}})}),(0,e.jsxs)("div",{children:[(0,a.oz)("route-length"),":",n.min_n_main_tree_steps]}),"\xA0\xA0"]}):"",(0,e.jsxs)("div",{children:[(0,a.oz)("longest-chain-l"),"\xA0",pt]}),"\xA0\xA0",(0,e.jsxs)(Ae.Z,{className:k.barnchTag,children:[Le?(0,a.oz)("has"):(0,a.oz)("No"),(0,a.Ig)()?" ":"",Le&&gt?(0,a.oz)("multiple"):"",(0,a.Ig)()?" ":"",(0,a.oz)("branched-chain")]}),Ee&&(0,e.jsxs)("div",{children:["\xA0\u2022\xA0",(0,e.jsx)(st.Z,{}),(0,a.oz)("modified-time"),": ",(0,a.S9)(Ee)]}),!ee&&n&&!(0,X.isNil)(n.score)?(0,e.jsxs)(e.Fragment,{children:["\xA0\u2022\xA0",(0,e.jsxs)("div",{className:"flex-align-items-center",children:[(0,a.oz)("algorithmic-score"),"\xA0",Math.round(n.score*100)/100,($==null?void 0:$.status)==="completed"&&(0,e.jsxs)(e.Fragment,{children:["\xA0",(0,e.jsx)(Q.Z,{content:(0,e.jsxs)("div",{children:[(0,e.jsxs)("p",{children:[(0,a.oz)("route-novelty-score"),"\uFF1A",(0,a.T2)((n==null?void 0:n.novelty_score)*100),"/100"]}),(0,e.jsxs)("p",{children:[(0,a.oz)("route-price-score"),"\uFF1A",(0,a.T2)((n==null?void 0:n.price_score)*100),"/100"]}),(0,e.jsxs)("p",{children:[(0,a.oz)("route-safety-score"),"\uFF1A",(0,a.T2)((n==null?void 0:n.safety_score)*100),"/100"]}),(0,e.jsx)(lt.Z,{style:{margin:"8px 0"}}),(0,e.jsxs)("p",{children:[(0,a.oz)("route-base-score"),"\uFF1A",(0,a.T2)(n==null?void 0:n.originScore),"/100"]})]},"algorithmic-tip"),children:(0,e.jsx)(Ge,{className:"enablePointer",fill:"#929292",width:14})})]})]})]}):"",!ee&&n&&!(0,X.isNil)(n.known_reaction_rate)?(0,e.jsxs)(e.Fragment,{children:["\xA0\xA0",(0,e.jsxs)("div",{children:[(0,a.oz)("known-reaction-proportion"),"\xA0",n.known_reaction_rate,"%"]})]}):"",ee&&pe&&Ne?(0,e.jsxs)(e.Fragment,{children:["\xA0\u2022\xA0",(0,e.jsxs)("div",{children:[(0,a.oz)("last-comment-date"),": ",(0,a.H3)(Ne)]})]}):""]}),(0,e.jsxs)("div",{className:k["right-side"],children:[ie==="myRoutes"&&V&&(0,e.jsx)(e.Fragment,{children:be.length?(0,e.jsx)(Qe.Z,{currentValue:V,avalibleValues:be,onSelect:Nt,valueRender:function(v){return(0,e.jsx)(fe.Z,{labelPrefix:"pages.route.statusLabel",status:v})}}):(0,e.jsx)(fe.Z,{labelPrefix:"pages.route.statusLabel",status:V})}),ce==null?void 0:ce.map(function(f){return(0,e.jsx)(ct.ZP,{type:"link",onClick:function(){ue==null||ue(f)},style:{cursor:f==="collect"&&he?"not-allowed":"pointer"},children:(0,e.jsx)(wt,{action:f})},f)})]})]})},ht=mt},91686:function(G,y,t){t.d(y,{Z:function(){return w}});var h=t(51562),u=t(71181),A=t(45098),d=t(94860),O=t(70831),s=t(4393),R=t(67294),D=t(84722),l=t(7361),p=t(85893),F=function(C){var i=C.route,S=C.targetMolecule,N=C.isPlayground,T=(0,O.useModel)("commend"),P=T.getProfileInfo,L=N||i.status!=="confirmed"?["view","feedback"]:["view","setAsDefault","feedback"];return(0,p.jsx)(l.Z,{routeName:i==null?void 0:i.name,updatedAt:(i==null?void 0:i.updated_at)||(i==null?void 0:i.updatedAt),updateTime:(i==null?void 0:i.updatedAt)||(i==null?void 0:i.createdAt),routeNo:i.id,route:i.main_tree,actions:L,targetMolecule:S,commendCount:i==null?void 0:i.content_count,onAction:function(E){E==="feedback"&&P({_commendSuject:i,collection_class:"project-route"})},routeStatus:i.status})},b=F,B=function(C){var i=C.route,S=C.hiddenTitle,N=C.isPlayground,T=C.targetMolecule,P=(0,O.useParams)(),L=P.id,H=(0,u.m)(),E=H.copy;return(0,p.jsx)(s.Z,{type:"inner",title:!S&&L?(0,p.jsx)(b,{route:i,isPlayground:N,targetMolecule:T}):null,className:"synthetic-route-card-root",children:i.main_tree?(0,p.jsx)(A.Z,{node:(0,d.A)(i.main_tree),withWrapper:!0,customProps:{reactionWrapper:{className:"reaction-wrapper"},structureWrapper:{className:"structure-wrapper"}},customChildren:{structure:function(te,J){return(0,p.jsx)("div",{className:"structure-info",children:(0,D.Q)(J)+1})},reaction:function(te,J){return(0,p.jsx)("div",{className:"reaction-btns",children:(0,p.jsx)("div",{className:"reaction-copy-btn reaction-btn",onClick:function(){return E("".concat(J,">>").concat(te))},children:(0,p.jsx)(h.r,{})})})}}}):""})},w=B},84722:function(G,y,t){t.d(y,{Q:function(){return h}});var h=function u(A){return A.child?u(A.child)+1:0}}}]);

//# sourceMappingURL=shared-VqxfXAGlh0atJoLJMmPncga4F9U_.6ef1929c.async.js.map