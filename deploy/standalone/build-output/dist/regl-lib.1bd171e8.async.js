(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[1487],{81550:function(Wi){(function(De,ye){Wi.exports=ye()})(this,function(){"use strict";var De=function(e){return e instanceof Uint8Array||e instanceof Uint16Array||e instanceof Uint32Array||e instanceof Int8Array||e instanceof Int16Array||e instanceof Int32Array||e instanceof Float32Array||e instanceof Float64Array||e instanceof Uint8ClampedArray},ye=function(e,t){for(var u=Object.keys(t),A=0;A<u.length;++A)e[u[A]]=t[u[A]];return e},or=`
`;function Yi(e){return typeof atob!="undefined"?atob(e):"base64:"+e}function Je(e){var t=new Error("(regl) "+e);throw console.error(t),t}function Ee(e,t){e||Je(t)}function ur(e){return e?": "+e:""}function Qi(e,t,u){e in t||Je("unknown parameter ("+e+")"+ur(u)+". possible values: "+Object.keys(t).join())}function qi(e,t){De(e)||Je("invalid parameter type"+ur(t)+". must be a typed array")}function Bn(e,t){switch(t){case"number":return typeof e=="number";case"object":return typeof e=="object";case"string":return typeof e=="string";case"boolean":return typeof e=="boolean";case"function":return typeof e=="function";case"undefined":return typeof e=="undefined";case"symbol":return typeof e=="symbol"}}function Ki(e,t,u){Bn(e,t)||Je("invalid parameter type"+ur(u)+". expected "+t+", got "+typeof e)}function Zi(e,t){e>=0&&(e|0)===e||Je("invalid parameter type, ("+e+")"+ur(t)+". must be a nonnegative integer")}function Bt(e,t,u){t.indexOf(e)<0&&Je("invalid value"+ur(u)+". must be one of: "+t)}var In=["gl","canvas","container","attributes","pixelRatio","extensions","optionalExtensions","profile","onDone"];function Ji(e){Object.keys(e).forEach(function(t){In.indexOf(t)<0&&Je('invalid regl constructor argument "'+t+'". must be one of '+In)})}function Tr(e,t){for(e=e+"";e.length<t;)e=" "+e;return e}function It(){this.name="unknown",this.lines=[],this.index={},this.hasErrors=!1}function kn(e,t){this.number=e,this.line=t,this.errors=[]}function Pn(e,t,u){this.file=e,this.line=t,this.message=u}function sr(){var e=new Error,t=(e.stack||e).toString(),u=/compileProcedure.*\n\s*at.*\((.*)\)/.exec(t);if(u)return u[1];var A=/compileProcedure.*\n\s*at\s+(.*)(\n|$)/.exec(t);return A?A[1]:"unknown"}function Mn(){var e=new Error,t=(e.stack||e).toString(),u=/at REGLCommand.*\n\s+at.*\((.*)\)/.exec(t);if(u)return u[1];var A=/at REGLCommand.*\n\s+at\s+(.*)\n/.exec(t);return A?A[1]:"unknown"}function kt(e,t){var u=e.split(`
`),A=1,w=0,E={unknown:new It,0:new It};E.unknown.name=E[0].name=t||sr(),E.unknown.lines.push(new kn(0,""));for(var x=0;x<u.length;++x){var D=u[x],B=/^\s*#\s*(\w+)\s+(.+)\s*$/.exec(D);if(B)switch(B[1]){case"line":var M=/(\d+)(\s+\d+)?/.exec(B[2]);M&&(A=M[1]|0,M[2]&&(w=M[2]|0,w in E||(E[w]=new It)));break;case"define":var I=/SHADER_NAME(_B64)?\s+(.*)$/.exec(B[2]);I&&(E[w].name=I[1]?Yi(I[2]):I[2]);break}E[w].lines.push(new kn(A++,D))}return Object.keys(E).forEach(function(V){var X=E[V];X.lines.forEach(function(N){X.index[N.number]=N})}),E}function ef(e){var t=[];return e.split(`
`).forEach(function(u){if(!(u.length<5)){var A=/^ERROR:\s+(\d+):(\d+):\s*(.*)$/.exec(u);A?t.push(new Pn(A[1]|0,A[2]|0,A[3].trim())):u.length>0&&t.push(new Pn("unknown",0,u))}}),t}function rf(e,t){t.forEach(function(u){var A=e[u.file];if(A){var w=A.index[u.line];if(w){w.errors.push(u),A.hasErrors=!0;return}}e.unknown.hasErrors=!0,e.unknown.lines[0].errors.push(u)})}function tf(e,t,u,A,w){if(!e.getShaderParameter(t,e.COMPILE_STATUS)){var E=e.getShaderInfoLog(t),x=A===e.FRAGMENT_SHADER?"fragment":"vertex";Vn(u,"string",x+" shader source must be a string",w);var D=kt(u,w),B=ef(E);rf(D,B),Object.keys(D).forEach(function(M){var I=D[M];if(!I.hasErrors)return;var V=[""],X=[""];function N(z,p){V.push(z),X.push(p||"")}N("file number "+M+": "+I.name+`
`,"color:red;text-decoration:underline;font-weight:bold"),I.lines.forEach(function(z){if(z.errors.length>0){N(Tr(z.number,4)+"|  ","background-color:yellow; font-weight:bold"),N(z.line+or,"color:red; background-color:yellow; font-weight:bold");var p=0;z.errors.forEach(function(S){var P=S.message,W=/^\s*'(.*)'\s*:\s*(.*)$/.exec(P);if(W){var G=W[1];switch(P=W[2],G){case"assign":G="=";break}p=Math.max(z.line.indexOf(G,p),0)}else p=0;N(Tr("| ",6)),N(Tr("^^^",p+3)+or,"font-weight:bold"),N(Tr("| ",6)),N(P+or,"font-weight:bold")}),N(Tr("| ",6)+or)}else N(Tr(z.number,4)+"|  "),N(z.line+or,"color:red")}),typeof document!="undefined"&&!window.chrome?(X[0]=V.join("%c"),console.log.apply(console,X)):console.log(V.join(""))}),Ee.raise("Error compiling "+x+" shader, "+D[0].name)}}function nf(e,t,u,A,w){if(!e.getProgramParameter(t,e.LINK_STATUS)){var E=e.getProgramInfoLog(t),x=kt(u,w),D=kt(A,w),B='Error linking program with vertex shader, "'+D[0].name+'", and fragment shader "'+x[0].name+'"';typeof document!="undefined"?console.log("%c"+B+or+"%c"+E,"color:red;text-decoration:underline;font-weight:bold","color:red"):console.log(B+or+E),Ee.raise(B)}}function Un(e){e._commandRef=sr()}function af(e,t,u,A){Un(e);function w(B){return B?A.id(B):0}e._fragId=w(e.static.frag),e._vertId=w(e.static.vert);function E(B,M){Object.keys(M).forEach(function(I){B[A.id(I)]=!0})}var x=e._uniformSet={};E(x,t.static),E(x,t.dynamic);var D=e._attributeSet={};E(D,u.static),E(D,u.dynamic),e._hasCount="count"in e.static||"count"in e.dynamic||"elements"in e.static||"elements"in e.dynamic}function nt(e,t){var u=Mn();Je(e+" in command "+(t||sr())+(u==="unknown"?"":" called from "+u))}function ff(e,t,u){e||nt(t,u||sr())}function of(e,t,u,A){e in t||nt("unknown parameter ("+e+")"+ur(u)+". possible values: "+Object.keys(t).join(),A||sr())}function Vn(e,t,u,A){Bn(e,t)||nt("invalid parameter type"+ur(u)+". expected "+t+", got "+typeof e,A||sr())}function uf(e){e()}function sf(e,t,u){e.texture?Bt(e.texture._texture.internalformat,t,"unsupported texture format for attachment"):Bt(e.renderbuffer._renderbuffer.format,u,"unsupported renderbuffer format for attachment")}var at=33071,zn=9728,cf=9984,lf=9985,df=9986,mf=9987,vf=5120,hf=5121,pf=5122,_f=5123,bf=5124,yf=5125,jn=5126,Hn=32819,Xn=32820,$n=33635,Wn=34042,Ef=36193,ze={};ze[vf]=ze[hf]=1,ze[pf]=ze[_f]=ze[Ef]=ze[$n]=ze[Hn]=ze[Xn]=2,ze[bf]=ze[yf]=ze[jn]=ze[Wn]=4;function Yn(e,t){return e===Xn||e===Hn||e===$n?2:e===Wn?4:ze[e]*t}function it(e){return!(e&e-1)&&!!e}function Af(e,t,u){var A,w=t.width,E=t.height,x=t.channels;Ee(w>0&&w<=u.maxTextureSize&&E>0&&E<=u.maxTextureSize,"invalid texture shape"),(e.wrapS!==at||e.wrapT!==at)&&Ee(it(w)&&it(E),"incompatible wrap mode for texture, both width and height must be power of 2"),t.mipmask===1?w!==1&&E!==1&&Ee(e.minFilter!==cf&&e.minFilter!==df&&e.minFilter!==lf&&e.minFilter!==mf,"min filter requires mipmap"):(Ee(it(w)&&it(E),"texture must be a square power of 2 to support mipmapping"),Ee(t.mipmask===(w<<1)-1,"missing or incomplete mipmap data")),t.type===jn&&(u.extensions.indexOf("oes_texture_float_linear")<0&&Ee(e.minFilter===zn&&e.magFilter===zn,"filter not supported, must enable oes_texture_float_linear"),Ee(!e.genMipmaps,"mipmap generation not supported with float textures"));var D=t.images;for(A=0;A<16;++A)if(D[A]){var B=w>>A,M=E>>A;Ee(t.mipmask&1<<A,"missing mipmap data");var I=D[A];if(Ee(I.width===B&&I.height===M,"invalid shape for mip images"),Ee(I.format===t.format&&I.internalformat===t.internalformat&&I.type===t.type,"incompatible type for mip image"),!I.compressed)if(I.data){var V=Math.ceil(Yn(I.type,x)*B/I.unpackAlignment)*I.unpackAlignment;Ee(I.data.byteLength===V*M,"invalid data for image, buffer size is inconsistent with image format")}else I.element||I.copy}else e.genMipmaps||Ee((t.mipmask&1<<A)===0,"extra mipmap data");t.compressed&&Ee(!e.genMipmaps,"mipmap generation for compressed images not supported")}function Tf(e,t,u,A){var w=e.width,E=e.height,x=e.channels;Ee(w>0&&w<=A.maxTextureSize&&E>0&&E<=A.maxTextureSize,"invalid texture shape"),Ee(w===E,"cube map must be square"),Ee(t.wrapS===at&&t.wrapT===at,"wrap mode not supported by cube map");for(var D=0;D<u.length;++D){var B=u[D];Ee(B.width===w&&B.height===E,"inconsistent cube map face shape"),t.genMipmaps&&(Ee(!B.compressed,"can not generate mipmap for compressed textures"),Ee(B.mipmask===1,"can not specify mipmaps and generate mipmaps"));for(var M=B.images,I=0;I<16;++I){var V=M[I];if(V){var X=w>>I,N=E>>I;Ee(B.mipmask&1<<I,"missing mipmap data"),Ee(V.width===X&&V.height===N,"invalid shape for mip images"),Ee(V.format===e.format&&V.internalformat===e.internalformat&&V.type===e.type,"incompatible type for mip image"),V.compressed||(V.data?Ee(V.data.byteLength===X*N*Math.max(Yn(V.type,x),V.unpackAlignment),"invalid data for image, buffer size is inconsistent with image format"):V.element||V.copy)}}}}var f=ye(Ee,{optional:uf,raise:Je,commandRaise:nt,command:ff,parameter:Qi,commandParameter:of,constructor:Ji,type:Ki,commandType:Vn,isTypedArray:qi,nni:Zi,oneOf:Bt,shaderError:tf,linkError:nf,callSite:Mn,saveCommandRef:Un,saveDrawInfo:af,framebufferFormat:sf,guessCommand:sr,texture2D:Af,textureCube:Tf}),xf=0,Sf=0,gf=5,Lf=6;function cr(e,t){this.id=xf++,this.type=e,this.data=t}function Qn(e){return e.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}function Mr(e){if(e.length===0)return[];var t=e.charAt(0),u=e.charAt(e.length-1);if(e.length>1&&t===u&&(t==='"'||t==="'"))return['"'+Qn(e.substr(1,e.length-2))+'"'];var A=/\[(false|true|null|\d+|'[^']*'|"[^"]*")\]/.exec(e);if(A)return Mr(e.substr(0,A.index)).concat(Mr(A[1])).concat(Mr(e.substr(A.index+A[0].length)));var w=e.split(".");if(w.length===1)return['"'+Qn(e)+'"'];for(var E=[],x=0;x<w.length;++x)E=E.concat(Mr(w[x]));return E}function qn(e){return"["+Mr(e).join("][")+"]"}function wf(e,t){return new cr(e,qn(t+""))}function Rf(e){return typeof e=="function"&&!e._reglType||e instanceof cr}function Kn(e,t){if(typeof e=="function")return new cr(Sf,e);if(typeof e=="number"||typeof e=="boolean")return new cr(gf,e);if(Array.isArray(e))return new cr(Lf,e.map((u,A)=>Kn(u,t+"["+A+"]")));if(e instanceof cr)return e;f(!1,"invalid option type in uniform "+t)}var je={DynamicVariable:cr,define:wf,isDynamic:Rf,unbox:Kn,accessor:qn},Pt={next:typeof requestAnimationFrame=="function"?function(e){return requestAnimationFrame(e)}:function(e){return setTimeout(e,16)},cancel:typeof cancelAnimationFrame=="function"?function(e){return cancelAnimationFrame(e)}:clearTimeout},Zn=typeof performance!="undefined"&&performance.now?function(){return performance.now()}:function(){return+new Date};function Of(){var e={"":0},t=[""];return{id:function(u){var A=e[u];return A||(A=e[u]=t.length,t.push(u),A)},str:function(u){return t[u]}}}function Gf(e,t,u){var A=document.createElement("canvas");ye(A.style,{border:0,margin:0,padding:0,top:0,left:0}),e.appendChild(A),e===document.body&&(A.style.position="absolute",ye(e.style,{margin:0,padding:0}));function w(){var D=window.innerWidth,B=window.innerHeight;if(e!==document.body){var M=e.getBoundingClientRect();D=M.right-M.left,B=M.bottom-M.top}A.width=u*D,A.height=u*B,ye(A.style,{width:D+"px",height:B+"px"})}var E;e!==document.body&&typeof ResizeObserver=="function"?(E=new ResizeObserver(function(){setTimeout(w)}),E.observe(e)):window.addEventListener("resize",w,!1);function x(){E?E.disconnect():window.removeEventListener("resize",w),e.removeChild(A)}return w(),{canvas:A,onDestroy:x}}function Cf(e,t){function u(A){try{return e.getContext(A,t)}catch(w){return null}}return u("webgl")||u("experimental-webgl")||u("webgl-experimental")}function Ff(e){return typeof e.nodeName=="string"&&typeof e.appendChild=="function"&&typeof e.getBoundingClientRect=="function"}function Df(e){return typeof e.drawArrays=="function"||typeof e.drawElements=="function"}function Jn(e){return typeof e=="string"?e.split():(f(Array.isArray(e),"invalid extension array"),e)}function ea(e){return typeof e=="string"?(f(typeof document!="undefined","not supported outside of DOM"),document.querySelector(e)):e}function Nf(e){var t=e||{},u,A,w,E,x={},D=[],B=[],M=typeof window=="undefined"?1:window.devicePixelRatio,I=!1,V=function(z){z&&f.raise(z)},X=function(){};if(typeof t=="string"?(f(typeof document!="undefined","selector queries only supported in DOM enviroments"),u=document.querySelector(t),f(u,"invalid query string for element")):typeof t=="object"?Ff(t)?u=t:Df(t)?(E=t,w=E.canvas):(f.constructor(t),"gl"in t?E=t.gl:"canvas"in t?w=ea(t.canvas):"container"in t&&(A=ea(t.container)),"attributes"in t&&(x=t.attributes,f.type(x,"object","invalid context attributes")),"extensions"in t&&(D=Jn(t.extensions)),"optionalExtensions"in t&&(B=Jn(t.optionalExtensions)),"onDone"in t&&(f.type(t.onDone,"function","invalid or missing onDone callback"),V=t.onDone),"profile"in t&&(I=!!t.profile),"pixelRatio"in t&&(M=+t.pixelRatio,f(M>0,"invalid pixel ratio"))):f.raise("invalid arguments to regl"),u&&(u.nodeName.toLowerCase()==="canvas"?w=u:A=u),!E){if(!w){f(typeof document!="undefined","must manually specify webgl context outside of DOM environments");var N=Gf(A||document.body,V,M);if(!N)return null;w=N.canvas,X=N.onDestroy}x.premultipliedAlpha===void 0&&(x.premultipliedAlpha=!0),E=Cf(w,x)}return E?{gl:E,canvas:w,container:A,extensions:D,optionalExtensions:B,pixelRatio:M,profile:I,onDone:V,onDestroy:X}:(X(),V("webgl not supported, try upgrading your browser or graphics drivers http://get.webgl.org"),null)}function Bf(e,t){var u={};function A(x){f.type(x,"string","extension name must be string");var D=x.toLowerCase(),B;try{B=u[D]=e.getExtension(D)}catch(M){}return!!B}for(var w=0;w<t.extensions.length;++w){var E=t.extensions[w];if(!A(E))return t.onDestroy(),t.onDone('"'+E+'" extension is not supported by the current WebGL context, try upgrading your system or a different browser'),null}return t.optionalExtensions.forEach(A),{extensions:u,restore:function(){Object.keys(u).forEach(function(x){if(u[x]&&!A(x))throw new Error("(regl): error restoring extension "+x)})}}}function Ue(e,t){for(var u=Array(e),A=0;A<e;++A)u[A]=t(A);return u}var If=5120,kf=5121,Pf=5122,Mf=5123,Uf=5124,Vf=5125,zf=5126;function jf(e){for(var t=16;t<=1<<28;t*=16)if(e<=t)return t;return 0}function ra(e){var t,u;return t=(e>65535)<<4,e>>>=t,u=(e>255)<<3,e>>>=u,t|=u,u=(e>15)<<2,e>>>=u,t|=u,u=(e>3)<<1,e>>>=u,t|=u,t|e>>1}function ta(){var e=Ue(8,function(){return[]});function t(E){var x=jf(E),D=e[ra(x)>>2];return D.length>0?D.pop():new ArrayBuffer(x)}function u(E){e[ra(E.byteLength)>>2].push(E)}function A(E,x){var D=null;switch(E){case If:D=new Int8Array(t(x),0,x);break;case kf:D=new Uint8Array(t(x),0,x);break;case Pf:D=new Int16Array(t(2*x),0,x);break;case Mf:D=new Uint16Array(t(2*x),0,x);break;case Uf:D=new Int32Array(t(4*x),0,x);break;case Vf:D=new Uint32Array(t(4*x),0,x);break;case zf:D=new Float32Array(t(4*x),0,x);break;default:return null}return D.length!==x?D.subarray(0,x):D}function w(E){u(E.buffer)}return{alloc:t,free:u,allocType:A,freeType:w}}var ge=ta();ge.zero=ta();var Hf=3408,Xf=3410,$f=3411,Wf=3412,Yf=3413,Qf=3414,qf=3415,Kf=33901,Zf=33902,Jf=3379,eo=3386,ro=34921,to=36347,no=36348,ao=35661,io=35660,fo=34930,oo=36349,uo=34076,so=34024,co=7936,lo=7937,mo=7938,vo=35724,ho=34047,po=36063,_o=34852,ft=3553,na=34067,bo=34069,yo=33984,Ur=6408,Mt=5126,aa=5121,Ut=36160,Eo=36053,Ao=36064,To=16384,xo=function(e,t){var u=1;t.ext_texture_filter_anisotropic&&(u=e.getParameter(ho));var A=1,w=1;t.webgl_draw_buffers&&(A=e.getParameter(_o),w=e.getParameter(po));var E=!!t.oes_texture_float;if(E){var x=e.createTexture();e.bindTexture(ft,x),e.texImage2D(ft,0,Ur,1,1,0,Ur,Mt,null);var D=e.createFramebuffer();if(e.bindFramebuffer(Ut,D),e.framebufferTexture2D(Ut,Ao,ft,x,0),e.bindTexture(ft,null),e.checkFramebufferStatus(Ut)!==Eo)E=!1;else{e.viewport(0,0,1,1),e.clearColor(1,0,0,1),e.clear(To);var B=ge.allocType(Mt,4);e.readPixels(0,0,1,1,Ur,Mt,B),e.getError()?E=!1:(e.deleteFramebuffer(D),e.deleteTexture(x),E=B[0]===1),ge.freeType(B)}}var M=typeof navigator!="undefined"&&(/MSIE/.test(navigator.userAgent)||/Trident\//.test(navigator.appVersion)||/Edge/.test(navigator.userAgent)),I=!0;if(!M){var V=e.createTexture(),X=ge.allocType(aa,36);e.activeTexture(yo),e.bindTexture(na,V),e.texImage2D(bo,0,Ur,3,3,0,Ur,aa,X),ge.freeType(X),e.bindTexture(na,null),e.deleteTexture(V),I=!e.getError()}return{colorBits:[e.getParameter(Xf),e.getParameter($f),e.getParameter(Wf),e.getParameter(Yf)],depthBits:e.getParameter(Qf),stencilBits:e.getParameter(qf),subpixelBits:e.getParameter(Hf),extensions:Object.keys(t).filter(function(N){return!!t[N]}),maxAnisotropic:u,maxDrawbuffers:A,maxColorAttachments:w,pointSizeDims:e.getParameter(Kf),lineWidthDims:e.getParameter(Zf),maxViewportDims:e.getParameter(eo),maxCombinedTextureUnits:e.getParameter(ao),maxCubeMapSize:e.getParameter(uo),maxRenderbufferSize:e.getParameter(so),maxTextureUnits:e.getParameter(fo),maxTextureSize:e.getParameter(Jf),maxAttributes:e.getParameter(ro),maxVertexUniforms:e.getParameter(to),maxVertexTextureUnits:e.getParameter(io),maxVaryingVectors:e.getParameter(no),maxFragmentUniforms:e.getParameter(oo),glsl:e.getParameter(vo),renderer:e.getParameter(lo),vendor:e.getParameter(co),version:e.getParameter(mo),readFloat:E,npotTextureCube:I}};function We(e){return!!e&&typeof e=="object"&&Array.isArray(e.shape)&&Array.isArray(e.stride)&&typeof e.offset=="number"&&e.shape.length===e.stride.length&&(Array.isArray(e.data)||De(e.data))}var He=function(e){return Object.keys(e).map(function(t){return e[t]})},ot={shape:wo,flatten:Lo};function So(e,t,u){for(var A=0;A<t;++A)u[A]=e[A]}function go(e,t,u,A){for(var w=0,E=0;E<t;++E)for(var x=e[E],D=0;D<u;++D)A[w++]=x[D]}function ia(e,t,u,A,w,E){for(var x=E,D=0;D<t;++D)for(var B=e[D],M=0;M<u;++M)for(var I=B[M],V=0;V<A;++V)w[x++]=I[V]}function fa(e,t,u,A,w){for(var E=1,x=u+1;x<t.length;++x)E*=t[x];var D=t[u];if(t.length-u===4){var B=t[u+1],M=t[u+2],I=t[u+3];for(x=0;x<D;++x)ia(e[x],B,M,I,A,w),w+=E}else for(x=0;x<D;++x)fa(e[x],t,u+1,A,w),w+=E}function Lo(e,t,u,A){var w=1;if(t.length)for(var E=0;E<t.length;++E)w*=t[E];else w=0;var x=A||ge.allocType(u,w);switch(t.length){case 0:break;case 1:So(e,t[0],x);break;case 2:go(e,t[0],t[1],x);break;case 3:ia(e,t[0],t[1],t[2],x,0);break;default:fa(e,t,0,x,0)}return x}function wo(e){for(var t=[],u=e;u.length;u=u[0])t.push(u.length);return t}var Vt={"[object Int8Array]":5120,"[object Int16Array]":5122,"[object Int32Array]":5124,"[object Uint8Array]":5121,"[object Uint8ClampedArray]":5121,"[object Uint16Array]":5123,"[object Uint32Array]":5125,"[object Float32Array]":5126,"[object Float64Array]":5121,"[object ArrayBuffer]":5121},Ro=5120,Oo=5122,Go=5124,Co=5121,Fo=5123,Do=5125,No=5126,Bo=5126,lr={int8:Ro,int16:Oo,int32:Go,uint8:Co,uint16:Fo,uint32:Do,float:No,float32:Bo},Io=35048,ko=35040,ut={dynamic:Io,stream:ko,static:35044},zt=ot.flatten,oa=ot.shape,ua=35044,Po=35040,jt=5121,Ht=5126,tr=[];tr[5120]=1,tr[5122]=2,tr[5124]=4,tr[5121]=1,tr[5123]=2,tr[5125]=4,tr[5126]=4;function st(e){return Vt[Object.prototype.toString.call(e)]|0}function sa(e,t){for(var u=0;u<t.length;++u)e[u]=t[u]}function ca(e,t,u,A,w,E,x){for(var D=0,B=0;B<u;++B)for(var M=0;M<A;++M)e[D++]=t[w*B+E*M+x]}function Mo(e,t,u,A){var w=0,E={};function x(p){this.id=w++,this.buffer=e.createBuffer(),this.type=p,this.usage=ua,this.byteLength=0,this.dimension=1,this.dtype=jt,this.persistentData=null,u.profile&&(this.stats={size:0})}x.prototype.bind=function(){e.bindBuffer(this.type,this.buffer)},x.prototype.destroy=function(){X(this)};var D=[];function B(p,S){var P=D.pop();return P||(P=new x(p)),P.bind(),V(P,S,Po,0,1,!1),P}function M(p){D.push(p)}function I(p,S,P){p.byteLength=S.byteLength,e.bufferData(p.type,S,P)}function V(p,S,P,W,G,Y){var F;if(p.usage=P,Array.isArray(S)){if(p.dtype=W||Ht,S.length>0){var j;if(Array.isArray(S[0])){F=oa(S);for(var C=1,H=1;H<F.length;++H)C*=F[H];p.dimension=C,j=zt(S,F,p.dtype),I(p,j,P),Y?p.persistentData=j:ge.freeType(j)}else if(typeof S[0]=="number"){p.dimension=G;var J=ge.allocType(p.dtype,S.length);sa(J,S),I(p,J,P),Y?p.persistentData=J:ge.freeType(J)}else De(S[0])?(p.dimension=S[0].length,p.dtype=W||st(S[0])||Ht,j=zt(S,[S.length,S[0].length],p.dtype),I(p,j,P),Y?p.persistentData=j:ge.freeType(j)):f.raise("invalid buffer data")}}else if(De(S))p.dtype=W||st(S),p.dimension=G,I(p,S,P),Y&&(p.persistentData=new Uint8Array(new Uint8Array(S.buffer)));else if(We(S)){F=S.shape;var ne=S.stride,$=S.offset,U=0,O=0,te=0,fe=0;F.length===1?(U=F[0],O=1,te=ne[0],fe=0):F.length===2?(U=F[0],O=F[1],te=ne[0],fe=ne[1]):f.raise("invalid shape"),p.dtype=W||st(S.data)||Ht,p.dimension=O;var q=ge.allocType(p.dtype,U*O);ca(q,S.data,U,O,te,fe,$),I(p,q,P),Y?p.persistentData=q:ge.freeType(q)}else S instanceof ArrayBuffer?(p.dtype=jt,p.dimension=G,I(p,S,P),Y&&(p.persistentData=new Uint8Array(new Uint8Array(S)))):f.raise("invalid buffer data")}function X(p){t.bufferCount--,A(p);var S=p.buffer;f(S,"buffer must not be deleted already"),e.deleteBuffer(S),p.buffer=null,delete E[p.id]}function N(p,S,P,W){t.bufferCount++;var G=new x(S);E[G.id]=G;function Y(C){var H=ua,J=null,ne=0,$=0,U=1;return Array.isArray(C)||De(C)||We(C)||C instanceof ArrayBuffer?J=C:typeof C=="number"?ne=C|0:C&&(f.type(C,"object","buffer arguments must be an object, a number or an array"),"data"in C&&(f(J===null||Array.isArray(J)||De(J)||We(J),"invalid data for buffer"),J=C.data),"usage"in C&&(f.parameter(C.usage,ut,"invalid buffer usage"),H=ut[C.usage]),"type"in C&&(f.parameter(C.type,lr,"invalid buffer type"),$=lr[C.type]),"dimension"in C&&(f.type(C.dimension,"number","invalid dimension"),U=C.dimension|0),"length"in C&&(f.nni(ne,"buffer length must be a nonnegative integer"),ne=C.length|0)),G.bind(),J?V(G,J,H,$,U,W):(ne&&e.bufferData(G.type,ne,H),G.dtype=$||jt,G.usage=H,G.dimension=U,G.byteLength=ne),u.profile&&(G.stats.size=G.byteLength*tr[G.dtype]),Y}function F(C,H){f(H+C.byteLength<=G.byteLength,"invalid buffer subdata call, buffer is too small.  Can't write data of size "+C.byteLength+" starting from offset "+H+" to a buffer of size "+G.byteLength),e.bufferSubData(G.type,H,C)}function j(C,H){var J=(H||0)|0,ne;if(G.bind(),De(C)||C instanceof ArrayBuffer)F(C,J);else if(Array.isArray(C)){if(C.length>0)if(typeof C[0]=="number"){var $=ge.allocType(G.dtype,C.length);sa($,C),F($,J),ge.freeType($)}else if(Array.isArray(C[0])||De(C[0])){ne=oa(C);var U=zt(C,ne,G.dtype);F(U,J),ge.freeType(U)}else f.raise("invalid buffer data")}else if(We(C)){ne=C.shape;var O=C.stride,te=0,fe=0,q=0,ce=0;ne.length===1?(te=ne[0],fe=1,q=O[0],ce=0):ne.length===2?(te=ne[0],fe=ne[1],q=O[0],ce=O[1]):f.raise("invalid shape");var ae=Array.isArray(C.data)?G.dtype:st(C.data),se=ge.allocType(ae,te*fe);ca(se,C.data,te,fe,q,ce,C.offset),F(se,J),ge.freeType(se)}else f.raise("invalid data for buffer subdata");return Y}return P||Y(p),Y._reglType="buffer",Y._buffer=G,Y.subdata=j,u.profile&&(Y.stats=G.stats),Y.destroy=function(){X(G)},Y}function z(){He(E).forEach(function(p){p.buffer=e.createBuffer(),e.bindBuffer(p.type,p.buffer),e.bufferData(p.type,p.persistentData||p.byteLength,p.usage)})}return u.profile&&(t.getTotalBufferSize=function(){var p=0;return Object.keys(E).forEach(function(S){p+=E[S].stats.size}),p}),{create:N,createStream:B,destroyStream:M,clear:function(){He(E).forEach(X),D.forEach(X)},getBuffer:function(p){return p&&p._buffer instanceof x?p._buffer:null},restore:z,_initBuffer:V}}var Uo=0,Vo=0,zo=1,jo=1,Ho=4,Xo=4,xr={points:Uo,point:Vo,lines:zo,line:jo,triangles:Ho,triangle:Xo,"line loop":2,"line strip":3,"triangle strip":5,"triangle fan":6},$o=0,Wo=1,Vr=4,Yo=5120,Sr=5121,la=5122,gr=5123,da=5124,dr=5125,Xt=34963,Qo=35040,qo=35044;function Ko(e,t,u,A){var w={},E=0,x={uint8:Sr,uint16:gr};t.oes_element_index_uint&&(x.uint32=dr);function D(z){this.id=E++,w[this.id]=this,this.buffer=z,this.primType=Vr,this.vertCount=0,this.type=0}D.prototype.bind=function(){this.buffer.bind()};var B=[];function M(z){var p=B.pop();return p||(p=new D(u.create(null,Xt,!0,!1)._buffer)),V(p,z,Qo,-1,-1,0,0),p}function I(z){B.push(z)}function V(z,p,S,P,W,G,Y){z.buffer.bind();var F;if(p){var j=Y;!Y&&(!De(p)||We(p)&&!De(p.data))&&(j=t.oes_element_index_uint?dr:gr),u._initBuffer(z.buffer,p,S,j,3)}else e.bufferData(Xt,G,S),z.buffer.dtype=F||Sr,z.buffer.usage=S,z.buffer.dimension=3,z.buffer.byteLength=G;if(F=Y,!Y){switch(z.buffer.dtype){case Sr:case Yo:F=Sr;break;case gr:case la:F=gr;break;case dr:case da:F=dr;break;default:f.raise("unsupported type for element array")}z.buffer.dtype=F}z.type=F,f(F!==dr||!!t.oes_element_index_uint,"32 bit element buffers not supported, enable oes_element_index_uint first");var C=W;C<0&&(C=z.buffer.byteLength,F===gr?C>>=1:F===dr&&(C>>=2)),z.vertCount=C;var H=P;if(P<0){H=Vr;var J=z.buffer.dimension;J===1&&(H=$o),J===2&&(H=Wo),J===3&&(H=Vr)}z.primType=H}function X(z){A.elementsCount--,f(z.buffer!==null,"must not double destroy elements"),delete w[z.id],z.buffer.destroy(),z.buffer=null}function N(z,p){var S=u.create(null,Xt,!0),P=new D(S._buffer);A.elementsCount++;function W(G){if(!G)S(),P.primType=Vr,P.vertCount=0,P.type=Sr;else if(typeof G=="number")S(G),P.primType=Vr,P.vertCount=G|0,P.type=Sr;else{var Y=null,F=qo,j=-1,C=-1,H=0,J=0;Array.isArray(G)||De(G)||We(G)?Y=G:(f.type(G,"object","invalid arguments for elements"),"data"in G&&(Y=G.data,f(Array.isArray(Y)||De(Y)||We(Y),"invalid data for element buffer")),"usage"in G&&(f.parameter(G.usage,ut,"invalid element buffer usage"),F=ut[G.usage]),"primitive"in G&&(f.parameter(G.primitive,xr,"invalid element buffer primitive"),j=xr[G.primitive]),"count"in G&&(f(typeof G.count=="number"&&G.count>=0,"invalid vertex count for elements"),C=G.count|0),"type"in G&&(f.parameter(G.type,x,"invalid buffer type"),J=x[G.type]),"length"in G?H=G.length|0:(H=C,J===gr||J===la?H*=2:(J===dr||J===da)&&(H*=4))),V(P,Y,F,j,C,H,J)}return W}return W(z),W._reglType="elements",W._elements=P,W.subdata=function(G,Y){return S.subdata(G,Y),W},W.destroy=function(){X(P)},W}return{create:N,createStream:M,destroyStream:I,getElements:function(z){return typeof z=="function"&&z._elements instanceof D?z._elements:null},clear:function(){He(w).forEach(X)}}}var ma=new Float32Array(1),Zo=new Uint32Array(ma.buffer),Jo=5123;function va(e){for(var t=ge.allocType(Jo,e.length),u=0;u<e.length;++u)if(isNaN(e[u]))t[u]=65535;else if(e[u]===1/0)t[u]=31744;else if(e[u]===-1/0)t[u]=64512;else{ma[0]=e[u];var A=Zo[0],w=A>>>31<<15,E=(A<<1>>>24)-127,x=A>>13&1023;if(E<-24)t[u]=w;else if(E<-14){var D=-14-E;t[u]=w+(x+1024>>D)}else E>15?t[u]=w+31744:t[u]=w+(E+15<<10)+x}return t}function Te(e){return Array.isArray(e)||De(e)}var ha=function(e){return!(e&e-1)&&!!e},eu=34467,qe=3553,$t=34067,ct=34069,mr=6408,Wt=6406,lt=6407,zr=6409,dt=6410,pa=32854,Yt=32855,_a=36194,ru=32819,tu=32820,nu=33635,au=34042,Qt=6402,mt=34041,qt=35904,Kt=35906,Lr=36193,Zt=33776,Jt=33777,en=33778,rn=33779,ba=35986,ya=35987,Ea=34798,Aa=35840,Ta=35841,xa=35842,Sa=35843,ga=36196,wr=5121,tn=5123,nn=5125,jr=5126,iu=10242,fu=10243,ou=10497,an=33071,uu=33648,su=10240,cu=10241,fn=9728,lu=9729,on=9984,La=9985,wa=9986,un=9987,du=33170,vt=4352,mu=4353,vu=4354,hu=34046,pu=3317,_u=37440,bu=37441,yu=37443,Ra=37444,Hr=33984,Eu=[on,wa,La,un],ht=[0,zr,dt,lt,mr],Xe={};Xe[zr]=Xe[Wt]=Xe[Qt]=1,Xe[mt]=Xe[dt]=2,Xe[lt]=Xe[qt]=3,Xe[mr]=Xe[Kt]=4;function Rr(e){return"[object "+e+"]"}var Oa=Rr("HTMLCanvasElement"),Ga=Rr("OffscreenCanvas"),Ca=Rr("CanvasRenderingContext2D"),Fa=Rr("ImageBitmap"),Da=Rr("HTMLImageElement"),Na=Rr("HTMLVideoElement"),Au=Object.keys(Vt).concat([Oa,Ga,Ca,Fa,Da,Na]),Or=[];Or[wr]=1,Or[jr]=4,Or[Lr]=2,Or[tn]=2,Or[nn]=4;var Be=[];Be[pa]=2,Be[Yt]=2,Be[_a]=2,Be[mt]=4,Be[Zt]=.5,Be[Jt]=.5,Be[en]=1,Be[rn]=1,Be[ba]=.5,Be[ya]=1,Be[Ea]=1,Be[Aa]=.5,Be[Ta]=.25,Be[xa]=.5,Be[Sa]=.25,Be[ga]=.5;function Ba(e){return Array.isArray(e)&&(e.length===0||typeof e[0]=="number")}function Ia(e){if(!Array.isArray(e))return!1;var t=e.length;return!(t===0||!Te(e[0]))}function vr(e){return Object.prototype.toString.call(e)}function ka(e){return vr(e)===Oa}function Pa(e){return vr(e)===Ga}function Tu(e){return vr(e)===Ca}function xu(e){return vr(e)===Fa}function Su(e){return vr(e)===Da}function gu(e){return vr(e)===Na}function sn(e){if(!e)return!1;var t=vr(e);return Au.indexOf(t)>=0?!0:Ba(e)||Ia(e)||We(e)}function Ma(e){return Vt[Object.prototype.toString.call(e)]|0}function Lu(e,t){var u=t.length;switch(e.type){case wr:case tn:case nn:case jr:var A=ge.allocType(e.type,u);A.set(t),e.data=A;break;case Lr:e.data=va(t);break;default:f.raise("unsupported texture type, must specify a typed array")}}function Ua(e,t){return ge.allocType(e.type===Lr?jr:e.type,t)}function Va(e,t){e.type===Lr?(e.data=va(t),ge.freeType(t)):e.data=t}function wu(e,t,u,A,w,E){for(var x=e.width,D=e.height,B=e.channels,M=x*D*B,I=Ua(e,M),V=0,X=0;X<D;++X)for(var N=0;N<x;++N)for(var z=0;z<B;++z)I[V++]=t[u*N+A*X+w*z+E];Va(e,I)}function pt(e,t,u,A,w,E){var x;if(typeof Be[e]!="undefined"?x=Be[e]:x=Xe[e]*Or[t],E&&(x*=6),w){for(var D=0,B=u;B>=1;)D+=x*B*B,B/=2;return D}else return x*u*A}function Ru(e,t,u,A,w,E,x){var D={"don't care":vt,"dont care":vt,nice:vu,fast:mu},B={repeat:ou,clamp:an,mirror:uu},M={nearest:fn,linear:lu},I=ye({mipmap:un,"nearest mipmap nearest":on,"linear mipmap nearest":La,"nearest mipmap linear":wa,"linear mipmap linear":un},M),V={none:0,browser:Ra},X={uint8:wr,rgba4:ru,rgb565:nu,"rgb5 a1":tu},N={alpha:Wt,luminance:zr,"luminance alpha":dt,rgb:lt,rgba:mr,rgba4:pa,"rgb5 a1":Yt,rgb565:_a},z={};t.ext_srgb&&(N.srgb=qt,N.srgba=Kt),t.oes_texture_float&&(X.float32=X.float=jr),t.oes_texture_half_float&&(X.float16=X["half float"]=Lr),t.webgl_depth_texture&&(ye(N,{depth:Qt,"depth stencil":mt}),ye(X,{uint16:tn,uint32:nn,"depth stencil":au})),t.webgl_compressed_texture_s3tc&&ye(z,{"rgb s3tc dxt1":Zt,"rgba s3tc dxt1":Jt,"rgba s3tc dxt3":en,"rgba s3tc dxt5":rn}),t.webgl_compressed_texture_atc&&ye(z,{"rgb atc":ba,"rgba atc explicit alpha":ya,"rgba atc interpolated alpha":Ea}),t.webgl_compressed_texture_pvrtc&&ye(z,{"rgb pvrtc 4bppv1":Aa,"rgb pvrtc 2bppv1":Ta,"rgba pvrtc 4bppv1":xa,"rgba pvrtc 2bppv1":Sa}),t.webgl_compressed_texture_etc1&&(z["rgb etc1"]=ga);var p=Array.prototype.slice.call(e.getParameter(eu));Object.keys(z).forEach(function(o){var T=z[o];p.indexOf(T)>=0&&(N[o]=T)});var S=Object.keys(N);u.textureFormats=S;var P=[];Object.keys(N).forEach(function(o){var T=N[o];P[T]=o});var W=[];Object.keys(X).forEach(function(o){var T=X[o];W[T]=o});var G=[];Object.keys(M).forEach(function(o){var T=M[o];G[T]=o});var Y=[];Object.keys(I).forEach(function(o){var T=I[o];Y[T]=o});var F=[];Object.keys(B).forEach(function(o){var T=B[o];F[T]=o});var j=S.reduce(function(o,T){var y=N[T];return y===zr||y===Wt||y===zr||y===dt||y===Qt||y===mt||t.ext_srgb&&(y===qt||y===Kt)?o[y]=y:y===Yt||T.indexOf("rgba")>=0?o[y]=mr:o[y]=lt,o},{});function C(){this.internalformat=mr,this.format=mr,this.type=wr,this.compressed=!1,this.premultiplyAlpha=!1,this.flipY=!1,this.unpackAlignment=1,this.colorSpace=Ra,this.width=0,this.height=0,this.channels=0}function H(o,T){o.internalformat=T.internalformat,o.format=T.format,o.type=T.type,o.compressed=T.compressed,o.premultiplyAlpha=T.premultiplyAlpha,o.flipY=T.flipY,o.unpackAlignment=T.unpackAlignment,o.colorSpace=T.colorSpace,o.width=T.width,o.height=T.height,o.channels=T.channels}function J(o,T){if(!(typeof T!="object"||!T)){if("premultiplyAlpha"in T&&(f.type(T.premultiplyAlpha,"boolean","invalid premultiplyAlpha"),o.premultiplyAlpha=T.premultiplyAlpha),"flipY"in T&&(f.type(T.flipY,"boolean","invalid texture flip"),o.flipY=T.flipY),"alignment"in T&&(f.oneOf(T.alignment,[1,2,4,8],"invalid texture unpack alignment"),o.unpackAlignment=T.alignment),"colorSpace"in T&&(f.parameter(T.colorSpace,V,"invalid colorSpace"),o.colorSpace=V[T.colorSpace]),"type"in T){var y=T.type;f(t.oes_texture_float||!(y==="float"||y==="float32"),"you must enable the OES_texture_float extension in order to use floating point textures."),f(t.oes_texture_half_float||!(y==="half float"||y==="float16"),"you must enable the OES_texture_half_float extension in order to use 16-bit floating point textures."),f(t.webgl_depth_texture||!(y==="uint16"||y==="uint32"||y==="depth stencil"),"you must enable the WEBGL_depth_texture extension in order to use depth/stencil textures."),f.parameter(y,X,"invalid texture type"),o.type=X[y]}var Q=o.width,le=o.height,a=o.channels,r=!1;"shape"in T?(f(Array.isArray(T.shape)&&T.shape.length>=2,"shape must be an array"),Q=T.shape[0],le=T.shape[1],T.shape.length===3&&(a=T.shape[2],f(a>0&&a<=4,"invalid number of channels"),r=!0),f(Q>=0&&Q<=u.maxTextureSize,"invalid width"),f(le>=0&&le<=u.maxTextureSize,"invalid height")):("radius"in T&&(Q=le=T.radius,f(Q>=0&&Q<=u.maxTextureSize,"invalid radius")),"width"in T&&(Q=T.width,f(Q>=0&&Q<=u.maxTextureSize,"invalid width")),"height"in T&&(le=T.height,f(le>=0&&le<=u.maxTextureSize,"invalid height")),"channels"in T&&(a=T.channels,f(a>0&&a<=4,"invalid number of channels"),r=!0)),o.width=Q|0,o.height=le|0,o.channels=a|0;var c=!1;if("format"in T){var h=T.format;f(t.webgl_depth_texture||!(h==="depth"||h==="depth stencil"),"you must enable the WEBGL_depth_texture extension in order to use depth/stencil textures."),f.parameter(h,N,"invalid texture format");var _=o.internalformat=N[h];o.format=j[_],h in X&&("type"in T||(o.type=X[h])),h in z&&(o.compressed=!0),c=!0}!r&&c?o.channels=Xe[o.format]:r&&!c?o.channels!==ht[o.format]&&(o.format=o.internalformat=ht[o.channels]):c&&r&&f(o.channels===Xe[o.format],"number of channels inconsistent with specified format")}}function ne(o){e.pixelStorei(_u,o.flipY),e.pixelStorei(bu,o.premultiplyAlpha),e.pixelStorei(yu,o.colorSpace),e.pixelStorei(pu,o.unpackAlignment)}function $(){C.call(this),this.xOffset=0,this.yOffset=0,this.data=null,this.needsFree=!1,this.element=null,this.needsCopy=!1}function U(o,T){var y=null;if(sn(T)?y=T:T&&(f.type(T,"object","invalid pixel data type"),J(o,T),"x"in T&&(o.xOffset=T.x|0),"y"in T&&(o.yOffset=T.y|0),sn(T.data)&&(y=T.data)),f(!o.compressed||y instanceof Uint8Array,"compressed texture data must be stored in a uint8array"),T.copy){f(!y,"can not specify copy and data field for the same texture");var Q=w.viewportWidth,le=w.viewportHeight;o.width=o.width||Q-o.xOffset,o.height=o.height||le-o.yOffset,o.needsCopy=!0,f(o.xOffset>=0&&o.xOffset<Q&&o.yOffset>=0&&o.yOffset<le&&o.width>0&&o.width<=Q&&o.height>0&&o.height<=le,"copy texture read out of bounds")}else if(!y)o.width=o.width||1,o.height=o.height||1,o.channels=o.channels||4;else if(De(y))o.channels=o.channels||4,o.data=y,!("type"in T)&&o.type===wr&&(o.type=Ma(y));else if(Ba(y))o.channels=o.channels||4,Lu(o,y),o.alignment=1,o.needsFree=!0;else if(We(y)){var a=y.data;!Array.isArray(a)&&o.type===wr&&(o.type=Ma(a));var r=y.shape,c=y.stride,h,_,d,l,m,i;r.length===3?(d=r[2],i=c[2]):(f(r.length===2,"invalid ndarray pixel data, must be 2 or 3D"),d=1,i=1),h=r[0],_=r[1],l=c[0],m=c[1],o.alignment=1,o.width=h,o.height=_,o.channels=d,o.format=o.internalformat=ht[d],o.needsFree=!0,wu(o,a,l,m,i,y.offset)}else if(ka(y)||Pa(y)||Tu(y))ka(y)||Pa(y)?o.element=y:o.element=y.canvas,o.width=o.element.width,o.height=o.element.height,o.channels=4;else if(xu(y))o.element=y,o.width=y.width,o.height=y.height,o.channels=4;else if(Su(y))o.element=y,o.width=y.naturalWidth,o.height=y.naturalHeight,o.channels=4;else if(gu(y))o.element=y,o.width=y.videoWidth,o.height=y.videoHeight,o.channels=4;else if(Ia(y)){var s=o.width||y[0].length,n=o.height||y.length,v=o.channels;Te(y[0][0])?v=v||y[0][0].length:v=v||1;for(var b=ot.shape(y),L=1,R=0;R<b.length;++R)L*=b[R];var g=Ua(o,L);ot.flatten(y,b,"",g),Va(o,g),o.alignment=1,o.width=s,o.height=n,o.channels=v,o.format=o.internalformat=ht[v],o.needsFree=!0}o.type===jr?f(u.extensions.indexOf("oes_texture_float")>=0,"oes_texture_float extension not enabled"):o.type===Lr&&f(u.extensions.indexOf("oes_texture_half_float")>=0,"oes_texture_half_float extension not enabled")}function O(o,T,y){var Q=o.element,le=o.data,a=o.internalformat,r=o.format,c=o.type,h=o.width,_=o.height;ne(o),Q?e.texImage2D(T,y,r,r,c,Q):o.compressed?e.compressedTexImage2D(T,y,a,h,_,0,le):o.needsCopy?(A(),e.copyTexImage2D(T,y,r,o.xOffset,o.yOffset,h,_,0)):e.texImage2D(T,y,r,h,_,0,r,c,le||null)}function te(o,T,y,Q,le){var a=o.element,r=o.data,c=o.internalformat,h=o.format,_=o.type,d=o.width,l=o.height;ne(o),a?e.texSubImage2D(T,le,y,Q,h,_,a):o.compressed?e.compressedTexSubImage2D(T,le,y,Q,c,d,l,r):o.needsCopy?(A(),e.copyTexSubImage2D(T,le,y,Q,o.xOffset,o.yOffset,d,l)):e.texSubImage2D(T,le,y,Q,d,l,h,_,r)}var fe=[];function q(){return fe.pop()||new $}function ce(o){o.needsFree&&ge.freeType(o.data),$.call(o),fe.push(o)}function ae(){C.call(this),this.genMipmaps=!1,this.mipmapHint=vt,this.mipmask=0,this.images=Array(16)}function se(o,T,y){var Q=o.images[0]=q();o.mipmask=1,Q.width=o.width=T,Q.height=o.height=y,Q.channels=o.channels=4}function he(o,T){var y=null;if(sn(T))y=o.images[0]=q(),H(y,o),U(y,T),o.mipmask=1;else if(J(o,T),Array.isArray(T.mipmap))for(var Q=T.mipmap,le=0;le<Q.length;++le)y=o.images[le]=q(),H(y,o),y.width>>=le,y.height>>=le,U(y,Q[le]),o.mipmask|=1<<le;else y=o.images[0]=q(),H(y,o),U(y,T),o.mipmask=1;H(o,o.images[0]),o.compressed&&(o.internalformat===Zt||o.internalformat===Jt||o.internalformat===en||o.internalformat===rn)&&f(o.width%4===0&&o.height%4===0,"for compressed texture formats, mipmap level 0 must have width and height that are a multiple of 4")}function we(o,T){for(var y=o.images,Q=0;Q<y.length;++Q){if(!y[Q])return;O(y[Q],T,Q)}}var Fe=[];function me(){var o=Fe.pop()||new ae;C.call(o),o.mipmask=0;for(var T=0;T<16;++T)o.images[T]=null;return o}function Oe(o){for(var T=o.images,y=0;y<T.length;++y)T[y]&&ce(T[y]),T[y]=null;Fe.push(o)}function be(){this.minFilter=fn,this.magFilter=fn,this.wrapS=an,this.wrapT=an,this.anisotropic=1,this.genMipmaps=!1,this.mipmapHint=vt}function Re(o,T){if("min"in T){var y=T.min;f.parameter(y,I),o.minFilter=I[y],Eu.indexOf(o.minFilter)>=0&&!("faces"in T)&&(o.genMipmaps=!0)}if("mag"in T){var Q=T.mag;f.parameter(Q,M),o.magFilter=M[Q]}var le=o.wrapS,a=o.wrapT;if("wrap"in T){var r=T.wrap;typeof r=="string"?(f.parameter(r,B),le=a=B[r]):Array.isArray(r)&&(f.parameter(r[0],B),f.parameter(r[1],B),le=B[r[0]],a=B[r[1]])}else{if("wrapS"in T){var c=T.wrapS;f.parameter(c,B),le=B[c]}if("wrapT"in T){var h=T.wrapT;f.parameter(h,B),a=B[h]}}if(o.wrapS=le,o.wrapT=a,"anisotropic"in T){var _=T.anisotropic;f(typeof _=="number"&&_>=1&&_<=u.maxAnisotropic,"aniso samples must be between 1 and "),o.anisotropic=T.anisotropic}if("mipmap"in T){var d=!1;switch(typeof T.mipmap){case"string":f.parameter(T.mipmap,D,"invalid mipmap hint"),o.mipmapHint=D[T.mipmap],o.genMipmaps=!0,d=!0;break;case"boolean":d=o.genMipmaps=T.mipmap;break;case"object":f(Array.isArray(T.mipmap),"invalid mipmap type"),o.genMipmaps=!1,d=!0;break;default:f.raise("invalid mipmap type")}d&&!("min"in T)&&(o.minFilter=on)}}function Ge(o,T){e.texParameteri(T,cu,o.minFilter),e.texParameteri(T,su,o.magFilter),e.texParameteri(T,iu,o.wrapS),e.texParameteri(T,fu,o.wrapT),t.ext_texture_filter_anisotropic&&e.texParameteri(T,hu,o.anisotropic),o.genMipmaps&&(e.hint(du,o.mipmapHint),e.generateMipmap(T))}var Ce=0,Ne={},Ie=u.maxTextureUnits,xe=Array(Ie).map(function(){return null});function oe(o){C.call(this),this.mipmask=0,this.internalformat=mr,this.id=Ce++,this.refCount=1,this.target=o,this.texture=e.createTexture(),this.unit=-1,this.bindCount=0,this.texInfo=new be,x.profile&&(this.stats={size:0})}function ke(o){e.activeTexture(Hr),e.bindTexture(o.target,o.texture)}function _e(){var o=xe[0];o?e.bindTexture(o.target,o.texture):e.bindTexture(qe,null)}function ee(o){var T=o.texture;f(T,"must not double destroy texture");var y=o.unit,Q=o.target;y>=0&&(e.activeTexture(Hr+y),e.bindTexture(Q,null),xe[y]=null),e.deleteTexture(T),o.texture=null,o.params=null,o.pixels=null,o.refCount=0,delete Ne[o.id],E.textureCount--}ye(oe.prototype,{bind:function(){var o=this;o.bindCount+=1;var T=o.unit;if(T<0){for(var y=0;y<Ie;++y){var Q=xe[y];if(Q){if(Q.bindCount>0)continue;Q.unit=-1}xe[y]=o,T=y;break}T>=Ie&&f.raise("insufficient number of texture units"),x.profile&&E.maxTextureUnits<T+1&&(E.maxTextureUnits=T+1),o.unit=T,e.activeTexture(Hr+T),e.bindTexture(o.target,o.texture)}return T},unbind:function(){this.bindCount-=1},decRef:function(){--this.refCount<=0&&ee(this)}});function de(o,T){var y=new oe(qe);Ne[y.id]=y,E.textureCount++;function Q(r,c){var h=y.texInfo;be.call(h);var _=me();return typeof r=="number"?typeof c=="number"?se(_,r|0,c|0):se(_,r|0,r|0):r?(f.type(r,"object","invalid arguments to regl.texture"),Re(h,r),he(_,r)):se(_,1,1),h.genMipmaps&&(_.mipmask=(_.width<<1)-1),y.mipmask=_.mipmask,H(y,_),f.texture2D(h,_,u),y.internalformat=_.internalformat,Q.width=_.width,Q.height=_.height,ke(y),we(_,qe),Ge(h,qe),_e(),Oe(_),x.profile&&(y.stats.size=pt(y.internalformat,y.type,_.width,_.height,h.genMipmaps,!1)),Q.format=P[y.internalformat],Q.type=W[y.type],Q.mag=G[h.magFilter],Q.min=Y[h.minFilter],Q.wrapS=F[h.wrapS],Q.wrapT=F[h.wrapT],Q}function le(r,c,h,_){f(!!r,"must specify image data");var d=c|0,l=h|0,m=_|0,i=q();return H(i,y),i.width=0,i.height=0,U(i,r),i.width=i.width||(y.width>>m)-d,i.height=i.height||(y.height>>m)-l,f(y.type===i.type&&y.format===i.format&&y.internalformat===i.internalformat,"incompatible format for texture.subimage"),f(d>=0&&l>=0&&d+i.width<=y.width&&l+i.height<=y.height,"texture.subimage write out of bounds"),f(y.mipmask&1<<m,"missing mipmap data"),f(i.data||i.element||i.needsCopy,"missing image data"),ke(y),te(i,qe,d,l,m),_e(),ce(i),Q}function a(r,c){var h=r|0,_=c|0||h;if(h===y.width&&_===y.height)return Q;Q.width=y.width=h,Q.height=y.height=_,ke(y);for(var d=0;y.mipmask>>d;++d){var l=h>>d,m=_>>d;if(!l||!m)break;e.texImage2D(qe,d,y.format,l,m,0,y.format,y.type,null)}return _e(),x.profile&&(y.stats.size=pt(y.internalformat,y.type,h,_,!1,!1)),Q}return Q(o,T),Q.subimage=le,Q.resize=a,Q._reglType="texture2d",Q._texture=y,x.profile&&(Q.stats=y.stats),Q.destroy=function(){y.decRef()},Q}function ve(o,T,y,Q,le,a){var r=new oe($t);Ne[r.id]=r,E.cubeCount++;var c=new Array(6);function h(l,m,i,s,n,v){var b,L=r.texInfo;for(be.call(L),b=0;b<6;++b)c[b]=me();if(typeof l=="number"||!l){var R=l|0||1;for(b=0;b<6;++b)se(c[b],R,R)}else if(typeof l=="object")if(m)he(c[0],l),he(c[1],m),he(c[2],i),he(c[3],s),he(c[4],n),he(c[5],v);else if(Re(L,l),J(r,l),"faces"in l){var g=l.faces;for(f(Array.isArray(g)&&g.length===6,"cube faces must be a length 6 array"),b=0;b<6;++b)f(typeof g[b]=="object"&&!!g[b],"invalid input for cube map face"),H(c[b],r),he(c[b],g[b])}else for(b=0;b<6;++b)he(c[b],l);else f.raise("invalid arguments to cube map");for(H(r,c[0]),u.npotTextureCube||f(ha(r.width)&&ha(r.height),"your browser does not support non power or two texture dimensions"),L.genMipmaps?r.mipmask=(c[0].width<<1)-1:r.mipmask=c[0].mipmask,f.textureCube(r,L,c,u),r.internalformat=c[0].internalformat,h.width=c[0].width,h.height=c[0].height,ke(r),b=0;b<6;++b)we(c[b],ct+b);for(Ge(L,$t),_e(),x.profile&&(r.stats.size=pt(r.internalformat,r.type,h.width,h.height,L.genMipmaps,!0)),h.format=P[r.internalformat],h.type=W[r.type],h.mag=G[L.magFilter],h.min=Y[L.minFilter],h.wrapS=F[L.wrapS],h.wrapT=F[L.wrapT],b=0;b<6;++b)Oe(c[b]);return h}function _(l,m,i,s,n){f(!!m,"must specify image data"),f(typeof l=="number"&&l===(l|0)&&l>=0&&l<6,"invalid face");var v=i|0,b=s|0,L=n|0,R=q();return H(R,r),R.width=0,R.height=0,U(R,m),R.width=R.width||(r.width>>L)-v,R.height=R.height||(r.height>>L)-b,f(r.type===R.type&&r.format===R.format&&r.internalformat===R.internalformat,"incompatible format for texture.subimage"),f(v>=0&&b>=0&&v+R.width<=r.width&&b+R.height<=r.height,"texture.subimage write out of bounds"),f(r.mipmask&1<<L,"missing mipmap data"),f(R.data||R.element||R.needsCopy,"missing image data"),ke(r),te(R,ct+l,v,b,L),_e(),ce(R),h}function d(l){var m=l|0;if(m!==r.width){h.width=r.width=m,h.height=r.height=m,ke(r);for(var i=0;i<6;++i)for(var s=0;r.mipmask>>s;++s)e.texImage2D(ct+i,s,r.format,m>>s,m>>s,0,r.format,r.type,null);return _e(),x.profile&&(r.stats.size=pt(r.internalformat,r.type,h.width,h.height,!1,!0)),h}}return h(o,T,y,Q,le,a),h.subimage=_,h.resize=d,h._reglType="textureCube",h._texture=r,x.profile&&(h.stats=r.stats),h.destroy=function(){r.decRef()},h}function Se(){for(var o=0;o<Ie;++o)e.activeTexture(Hr+o),e.bindTexture(qe,null),xe[o]=null;He(Ne).forEach(ee),E.cubeCount=0,E.textureCount=0}x.profile&&(E.getTotalTextureSize=function(){var o=0;return Object.keys(Ne).forEach(function(T){o+=Ne[T].stats.size}),o});function Ze(){for(var o=0;o<Ie;++o){var T=xe[o];T&&(T.bindCount=0,T.unit=-1,xe[o]=null)}He(Ne).forEach(function(y){y.texture=e.createTexture(),e.bindTexture(y.target,y.texture);for(var Q=0;Q<32;++Q)if(y.mipmask&1<<Q)if(y.target===qe)e.texImage2D(qe,Q,y.internalformat,y.width>>Q,y.height>>Q,0,y.internalformat,y.type,null);else for(var le=0;le<6;++le)e.texImage2D(ct+le,Q,y.internalformat,y.width>>Q,y.height>>Q,0,y.internalformat,y.type,null);Ge(y.texInfo,y.target)})}function Ar(){for(var o=0;o<Ie;++o){var T=xe[o];T&&(T.bindCount=0,T.unit=-1,xe[o]=null),e.activeTexture(Hr+o),e.bindTexture(qe,null),e.bindTexture($t,null)}}return{create2D:de,createCube:ve,clear:Se,getTexture:function(o){return null},restore:Ze,refresh:Ar}}var nr=36161,_t=32854,za=32855,ja=36194,Ha=33189,Xa=36168,$a=34041,Wa=35907,Ya=34836,Qa=34842,qa=34843,Ye=[];Ye[_t]=2,Ye[za]=2,Ye[ja]=2,Ye[Ha]=2,Ye[Xa]=1,Ye[$a]=4,Ye[Wa]=4,Ye[Ya]=16,Ye[Qa]=8,Ye[qa]=6;function Ka(e,t,u){return Ye[e]*t*u}var Ou=function(e,t,u,A,w){var E={rgba4:_t,rgb565:ja,"rgb5 a1":za,depth:Ha,stencil:Xa,"depth stencil":$a};t.ext_srgb&&(E.srgba=Wa),t.ext_color_buffer_half_float&&(E.rgba16f=Qa,E.rgb16f=qa),t.webgl_color_buffer_float&&(E.rgba32f=Ya);var x=[];Object.keys(E).forEach(function(N){var z=E[N];x[z]=N});var D=0,B={};function M(N){this.id=D++,this.refCount=1,this.renderbuffer=N,this.format=_t,this.width=0,this.height=0,w.profile&&(this.stats={size:0})}M.prototype.decRef=function(){--this.refCount<=0&&I(this)};function I(N){var z=N.renderbuffer;f(z,"must not double destroy renderbuffer"),e.bindRenderbuffer(nr,null),e.deleteRenderbuffer(z),N.renderbuffer=null,N.refCount=0,delete B[N.id],A.renderbufferCount--}function V(N,z){var p=new M(e.createRenderbuffer());B[p.id]=p,A.renderbufferCount++;function S(W,G){var Y=0,F=0,j=_t;if(typeof W=="object"&&W){var C=W;if("shape"in C){var H=C.shape;f(Array.isArray(H)&&H.length>=2,"invalid renderbuffer shape"),Y=H[0]|0,F=H[1]|0}else"radius"in C&&(Y=F=C.radius|0),"width"in C&&(Y=C.width|0),"height"in C&&(F=C.height|0);"format"in C&&(f.parameter(C.format,E,"invalid renderbuffer format"),j=E[C.format])}else typeof W=="number"?(Y=W|0,typeof G=="number"?F=G|0:F=Y):W?f.raise("invalid arguments to renderbuffer constructor"):Y=F=1;if(f(Y>0&&F>0&&Y<=u.maxRenderbufferSize&&F<=u.maxRenderbufferSize,"invalid renderbuffer size"),!(Y===p.width&&F===p.height&&j===p.format))return S.width=p.width=Y,S.height=p.height=F,p.format=j,e.bindRenderbuffer(nr,p.renderbuffer),e.renderbufferStorage(nr,j,Y,F),f(e.getError()===0,"invalid render buffer format"),w.profile&&(p.stats.size=Ka(p.format,p.width,p.height)),S.format=x[p.format],S}function P(W,G){var Y=W|0,F=G|0||Y;return Y===p.width&&F===p.height||(f(Y>0&&F>0&&Y<=u.maxRenderbufferSize&&F<=u.maxRenderbufferSize,"invalid renderbuffer size"),S.width=p.width=Y,S.height=p.height=F,e.bindRenderbuffer(nr,p.renderbuffer),e.renderbufferStorage(nr,p.format,Y,F),f(e.getError()===0,"invalid render buffer format"),w.profile&&(p.stats.size=Ka(p.format,p.width,p.height))),S}return S(N,z),S.resize=P,S._reglType="renderbuffer",S._renderbuffer=p,w.profile&&(S.stats=p.stats),S.destroy=function(){p.decRef()},S}w.profile&&(A.getTotalRenderbufferSize=function(){var N=0;return Object.keys(B).forEach(function(z){N+=B[z].stats.size}),N});function X(){He(B).forEach(function(N){N.renderbuffer=e.createRenderbuffer(),e.bindRenderbuffer(nr,N.renderbuffer),e.renderbufferStorage(nr,N.format,N.width,N.height)}),e.bindRenderbuffer(nr,null)}return{create:V,clear:function(){He(B).forEach(I)},restore:X}},er=36160,cn=36161,hr=3553,bt=34069,Za=36064,Ja=36096,ei=36128,ri=33306,ti=36053,Gu=36054,Cu=36055,Fu=36057,Du=36061,Nu=36193,Bu=5121,Iu=5126,ni=6407,ai=6408,ku=6402,Pu=[ni,ai],ln=[];ln[ai]=4,ln[ni]=3;var yt=[];yt[Bu]=1,yt[Iu]=4,yt[Nu]=2;var Mu=32854,Uu=32855,Vu=36194,zu=33189,ju=36168,ii=34041,Hu=35907,Xu=34836,$u=34842,Wu=34843,Yu=[Mu,Uu,Vu,Hu,$u,Wu,Xu],Gr={};Gr[ti]="complete",Gr[Gu]="incomplete attachment",Gr[Fu]="incomplete dimensions",Gr[Cu]="incomplete, missing attachment",Gr[Du]="unsupported";function Qu(e,t,u,A,w,E){var x={cur:null,next:null,dirty:!1,setFBO:null},D=["rgba"],B=["rgba4","rgb565","rgb5 a1"];t.ext_srgb&&B.push("srgba"),t.ext_color_buffer_half_float&&B.push("rgba16f","rgb16f"),t.webgl_color_buffer_float&&B.push("rgba32f");var M=["uint8"];t.oes_texture_half_float&&M.push("half float","float16"),t.oes_texture_float&&M.push("float","float32");function I($,U,O){this.target=$,this.texture=U,this.renderbuffer=O;var te=0,fe=0;U?(te=U.width,fe=U.height):O&&(te=O.width,fe=O.height),this.width=te,this.height=fe}function V($){$&&($.texture&&$.texture._texture.decRef(),$.renderbuffer&&$.renderbuffer._renderbuffer.decRef())}function X($,U,O){if($)if($.texture){var te=$.texture._texture,fe=Math.max(1,te.width),q=Math.max(1,te.height);f(fe===U&&q===O,"inconsistent width/height for supplied texture"),te.refCount+=1}else{var ce=$.renderbuffer._renderbuffer;f(ce.width===U&&ce.height===O,"inconsistent width/height for renderbuffer"),ce.refCount+=1}}function N($,U){U&&(U.texture?e.framebufferTexture2D(er,$,U.target,U.texture._texture.texture,0):e.framebufferRenderbuffer(er,$,cn,U.renderbuffer._renderbuffer.renderbuffer))}function z($){var U=hr,O=null,te=null,fe=$;typeof $=="object"&&(fe=$.data,"target"in $&&(U=$.target|0)),f.type(fe,"function","invalid attachment data");var q=fe._reglType;return q==="texture2d"?(O=fe,f(U===hr)):q==="textureCube"?(O=fe,f(U>=bt&&U<bt+6,"invalid cube map target")):q==="renderbuffer"?(te=fe,U=cn):f.raise("invalid regl object for attachment"),new I(U,O,te)}function p($,U,O,te,fe){if(O){var q=A.create2D({width:$,height:U,format:te,type:fe});return q._texture.refCount=0,new I(hr,q,null)}else{var ce=w.create({width:$,height:U,format:te});return ce._renderbuffer.refCount=0,new I(cn,null,ce)}}function S($){return $&&($.texture||$.renderbuffer)}function P($,U,O){$&&($.texture?$.texture.resize(U,O):$.renderbuffer&&$.renderbuffer.resize(U,O),$.width=U,$.height=O)}var W=0,G={};function Y(){this.id=W++,G[this.id]=this,this.framebuffer=e.createFramebuffer(),this.width=0,this.height=0,this.colorAttachments=[],this.depthAttachment=null,this.stencilAttachment=null,this.depthStencilAttachment=null}function F($){$.colorAttachments.forEach(V),V($.depthAttachment),V($.stencilAttachment),V($.depthStencilAttachment)}function j($){var U=$.framebuffer;f(U,"must not double destroy framebuffer"),e.deleteFramebuffer(U),$.framebuffer=null,E.framebufferCount--,delete G[$.id]}function C($){var U;e.bindFramebuffer(er,$.framebuffer);var O=$.colorAttachments;for(U=0;U<O.length;++U)N(Za+U,O[U]);for(U=O.length;U<u.maxColorAttachments;++U)e.framebufferTexture2D(er,Za+U,hr,null,0);e.framebufferTexture2D(er,ri,hr,null,0),e.framebufferTexture2D(er,Ja,hr,null,0),e.framebufferTexture2D(er,ei,hr,null,0),N(Ja,$.depthAttachment),N(ei,$.stencilAttachment),N(ri,$.depthStencilAttachment);var te=e.checkFramebufferStatus(er);!e.isContextLost()&&te!==ti&&f.raise("framebuffer configuration not supported, status = "+Gr[te]),e.bindFramebuffer(er,x.next?x.next.framebuffer:null),x.cur=x.next,e.getError()}function H($,U){var O=new Y;E.framebufferCount++;function te(q,ce){var ae;f(x.next!==O,"can not update framebuffer which is currently in use");var se=0,he=0,we=!0,Fe=!0,me=null,Oe=!0,be="rgba",Re="uint8",Ge=1,Ce=null,Ne=null,Ie=null,xe=!1;if(typeof q=="number")se=q|0,he=ce|0||se;else if(!q)se=he=1;else{f.type(q,"object","invalid arguments for framebuffer");var oe=q;if("shape"in oe){var ke=oe.shape;f(Array.isArray(ke)&&ke.length>=2,"invalid shape for framebuffer"),se=ke[0],he=ke[1]}else"radius"in oe&&(se=he=oe.radius),"width"in oe&&(se=oe.width),"height"in oe&&(he=oe.height);("color"in oe||"colors"in oe)&&(me=oe.color||oe.colors,Array.isArray(me)&&f(me.length===1||t.webgl_draw_buffers,"multiple render targets not supported")),me||("colorCount"in oe&&(Ge=oe.colorCount|0,f(Ge>0,"invalid color buffer count")),"colorTexture"in oe&&(Oe=!!oe.colorTexture,be="rgba4"),"colorType"in oe&&(Re=oe.colorType,Oe?(f(t.oes_texture_float||!(Re==="float"||Re==="float32"),"you must enable OES_texture_float in order to use floating point framebuffer objects"),f(t.oes_texture_half_float||!(Re==="half float"||Re==="float16"),"you must enable OES_texture_half_float in order to use 16-bit floating point framebuffer objects")):Re==="half float"||Re==="float16"?(f(t.ext_color_buffer_half_float,"you must enable EXT_color_buffer_half_float to use 16-bit render buffers"),be="rgba16f"):(Re==="float"||Re==="float32")&&(f(t.webgl_color_buffer_float,"you must enable WEBGL_color_buffer_float in order to use 32-bit floating point renderbuffers"),be="rgba32f"),f.oneOf(Re,M,"invalid color type")),"colorFormat"in oe&&(be=oe.colorFormat,D.indexOf(be)>=0?Oe=!0:B.indexOf(be)>=0?Oe=!1:Oe?f.oneOf(oe.colorFormat,D,"invalid color format for texture"):f.oneOf(oe.colorFormat,B,"invalid color format for renderbuffer"))),("depthTexture"in oe||"depthStencilTexture"in oe)&&(xe=!!(oe.depthTexture||oe.depthStencilTexture),f(!xe||t.webgl_depth_texture,"webgl_depth_texture extension not supported")),"depth"in oe&&(typeof oe.depth=="boolean"?we=oe.depth:(Ce=oe.depth,Fe=!1)),"stencil"in oe&&(typeof oe.stencil=="boolean"?Fe=oe.stencil:(Ne=oe.stencil,we=!1)),"depthStencil"in oe&&(typeof oe.depthStencil=="boolean"?we=Fe=oe.depthStencil:(Ie=oe.depthStencil,we=!1,Fe=!1))}var _e=null,ee=null,de=null,ve=null;if(Array.isArray(me))_e=me.map(z);else if(me)_e=[z(me)];else for(_e=new Array(Ge),ae=0;ae<Ge;++ae)_e[ae]=p(se,he,Oe,be,Re);f(t.webgl_draw_buffers||_e.length<=1,"you must enable the WEBGL_draw_buffers extension in order to use multiple color buffers."),f(_e.length<=u.maxColorAttachments,"too many color attachments, not supported"),se=se||_e[0].width,he=he||_e[0].height,Ce?ee=z(Ce):we&&!Fe&&(ee=p(se,he,xe,"depth","uint32")),Ne?de=z(Ne):Fe&&!we&&(de=p(se,he,!1,"stencil","uint8")),Ie?ve=z(Ie):!Ce&&!Ne&&Fe&&we&&(ve=p(se,he,xe,"depth stencil","depth stencil")),f(!!Ce+!!Ne+!!Ie<=1,"invalid framebuffer configuration, can specify exactly one depth/stencil attachment");var Se=null;for(ae=0;ae<_e.length;++ae)if(X(_e[ae],se,he),f(!_e[ae]||_e[ae].texture&&Pu.indexOf(_e[ae].texture._texture.format)>=0||_e[ae].renderbuffer&&Yu.indexOf(_e[ae].renderbuffer._renderbuffer.format)>=0,"framebuffer color attachment "+ae+" is invalid"),_e[ae]&&_e[ae].texture){var Ze=ln[_e[ae].texture._texture.format]*yt[_e[ae].texture._texture.type];Se===null?Se=Ze:f(Se===Ze,"all color attachments much have the same number of bits per pixel.")}return X(ee,se,he),f(!ee||ee.texture&&ee.texture._texture.format===ku||ee.renderbuffer&&ee.renderbuffer._renderbuffer.format===zu,"invalid depth attachment for framebuffer object"),X(de,se,he),f(!de||de.renderbuffer&&de.renderbuffer._renderbuffer.format===ju,"invalid stencil attachment for framebuffer object"),X(ve,se,he),f(!ve||ve.texture&&ve.texture._texture.format===ii||ve.renderbuffer&&ve.renderbuffer._renderbuffer.format===ii,"invalid depth-stencil attachment for framebuffer object"),F(O),O.width=se,O.height=he,O.colorAttachments=_e,O.depthAttachment=ee,O.stencilAttachment=de,O.depthStencilAttachment=ve,te.color=_e.map(S),te.depth=S(ee),te.stencil=S(de),te.depthStencil=S(ve),te.width=O.width,te.height=O.height,C(O),te}function fe(q,ce){f(x.next!==O,"can not resize a framebuffer which is currently in use");var ae=Math.max(q|0,1),se=Math.max(ce|0||ae,1);if(ae===O.width&&se===O.height)return te;for(var he=O.colorAttachments,we=0;we<he.length;++we)P(he[we],ae,se);return P(O.depthAttachment,ae,se),P(O.stencilAttachment,ae,se),P(O.depthStencilAttachment,ae,se),O.width=te.width=ae,O.height=te.height=se,C(O),te}return te($,U),ye(te,{resize:fe,_reglType:"framebuffer",_framebuffer:O,destroy:function(){j(O),F(O)},use:function(q){x.setFBO({framebuffer:te},q)}})}function J($){var U=Array(6);function O(fe){var q;f(U.indexOf(x.next)<0,"can not update framebuffer which is currently in use");var ce={color:null},ae=0,se=null,he="rgba",we="uint8",Fe=1;if(typeof fe=="number")ae=fe|0;else if(!fe)ae=1;else{f.type(fe,"object","invalid arguments for framebuffer");var me=fe;if("shape"in me){var Oe=me.shape;f(Array.isArray(Oe)&&Oe.length>=2,"invalid shape for framebuffer"),f(Oe[0]===Oe[1],"cube framebuffer must be square"),ae=Oe[0]}else"radius"in me&&(ae=me.radius|0),"width"in me?(ae=me.width|0,"height"in me&&f(me.height===ae,"must be square")):"height"in me&&(ae=me.height|0);("color"in me||"colors"in me)&&(se=me.color||me.colors,Array.isArray(se)&&f(se.length===1||t.webgl_draw_buffers,"multiple render targets not supported")),se||("colorCount"in me&&(Fe=me.colorCount|0,f(Fe>0,"invalid color buffer count")),"colorType"in me&&(f.oneOf(me.colorType,M,"invalid color type"),we=me.colorType),"colorFormat"in me&&(he=me.colorFormat,f.oneOf(me.colorFormat,D,"invalid color format for texture"))),"depth"in me&&(ce.depth=me.depth),"stencil"in me&&(ce.stencil=me.stencil),"depthStencil"in me&&(ce.depthStencil=me.depthStencil)}var be;if(se)if(Array.isArray(se))for(be=[],q=0;q<se.length;++q)be[q]=se[q];else be=[se];else{be=Array(Fe);var Re={radius:ae,format:he,type:we};for(q=0;q<Fe;++q)be[q]=A.createCube(Re)}for(ce.color=Array(be.length),q=0;q<be.length;++q){var Ge=be[q];f(typeof Ge=="function"&&Ge._reglType==="textureCube","invalid cube map"),ae=ae||Ge.width,f(Ge.width===ae&&Ge.height===ae,"invalid cube map shape"),ce.color[q]={target:bt,data:be[q]}}for(q=0;q<6;++q){for(var Ce=0;Ce<be.length;++Ce)ce.color[Ce].target=bt+q;q>0&&(ce.depth=U[0].depth,ce.stencil=U[0].stencil,ce.depthStencil=U[0].depthStencil),U[q]?U[q](ce):U[q]=H(ce)}return ye(O,{width:ae,height:ae,color:be})}function te(fe){var q,ce=fe|0;if(f(ce>0&&ce<=u.maxCubeMapSize,"invalid radius for cube fbo"),ce===O.width)return O;var ae=O.color;for(q=0;q<ae.length;++q)ae[q].resize(ce);for(q=0;q<6;++q)U[q].resize(ce);return O.width=O.height=ce,O}return O($),ye(O,{faces:U,resize:te,_reglType:"framebufferCube",destroy:function(){U.forEach(function(fe){fe.destroy()})}})}function ne(){x.cur=null,x.next=null,x.dirty=!0,He(G).forEach(function($){$.framebuffer=e.createFramebuffer(),C($)})}return ye(x,{getFramebuffer:function($){if(typeof $=="function"&&$._reglType==="framebuffer"){var U=$._framebuffer;if(U instanceof Y)return U}return null},create:H,createCube:J,clear:function(){He(G).forEach(j)},restore:ne})}var qu=5126,fi=34962;function dn(){this.state=0,this.x=0,this.y=0,this.z=0,this.w=0,this.buffer=null,this.size=0,this.normalized=!1,this.type=qu,this.offset=0,this.stride=0,this.divisor=0}function Ku(e,t,u,A,w){for(var E=u.maxAttributes,x=new Array(E),D=0;D<E;++D)x[D]=new dn;var B=0,M={},I={Record:dn,scope:{},state:x,currentVAO:null,targetVAO:null,restore:X()?G:function(){},createVAO:Y,getVAO:z,destroyBuffer:V,setVAO:X()?p:S,clear:X()?P:function(){}};function V(F){for(var j=0;j<x.length;++j){var C=x[j];C.buffer===F&&(e.disableVertexAttribArray(j),C.buffer=null)}}function X(){return t.oes_vertex_array_object}function N(){return t.angle_instanced_arrays}function z(F){return typeof F=="function"&&F._vao?F._vao:null}function p(F){if(F!==I.currentVAO){var j=X();F?j.bindVertexArrayOES(F.vao):j.bindVertexArrayOES(null),I.currentVAO=F}}function S(F){if(F!==I.currentVAO){if(F)F.bindAttrs();else for(var j=N(),C=0;C<x.length;++C){var H=x[C];H.buffer?(e.enableVertexAttribArray(C),e.vertexAttribPointer(C,H.size,H.type,H.normalized,H.stride,H.offfset),j&&H.divisor&&j.vertexAttribDivisorANGLE(C,H.divisor)):(e.disableVertexAttribArray(C),e.vertexAttrib4f(C,H.x,H.y,H.z,H.w))}I.currentVAO=F}}function P(){He(M).forEach(function(F){F.destroy()})}function W(){this.id=++B,this.attributes=[];var F=X();F?this.vao=F.createVertexArrayOES():this.vao=null,M[this.id]=this,this.buffers=[]}W.prototype.bindAttrs=function(){for(var F=N(),j=this.attributes,C=0;C<j.length;++C){var H=j[C];H.buffer?(e.enableVertexAttribArray(C),e.bindBuffer(fi,H.buffer.buffer),e.vertexAttribPointer(C,H.size,H.type,H.normalized,H.stride,H.offset),F&&H.divisor&&F.vertexAttribDivisorANGLE(C,H.divisor)):(e.disableVertexAttribArray(C),e.vertexAttrib4f(C,H.x,H.y,H.z,H.w))}for(var J=j.length;J<E;++J)e.disableVertexAttribArray(J)},W.prototype.refresh=function(){var F=X();F&&(F.bindVertexArrayOES(this.vao),this.bindAttrs(),I.currentVAO=this)},W.prototype.destroy=function(){if(this.vao){var F=X();this===I.currentVAO&&(I.currentVAO=null,F.bindVertexArrayOES(null)),F.deleteVertexArrayOES(this.vao),this.vao=null}M[this.id]&&(delete M[this.id],A.vaoCount-=1)};function G(){var F=X();F&&He(M).forEach(function(j){j.refresh()})}function Y(F){var j=new W;A.vaoCount+=1;function C(H){f(Array.isArray(H),"arguments to vertex array constructor must be an array"),f(H.length<E,"too many attributes"),f(H.length>0,"must specify at least one attribute");var J={},ne=j.attributes;ne.length=H.length;for(var $=0;$<H.length;++$){var U=H[$],O=ne[$]=new dn,te=U.data||U;if(Array.isArray(te)||De(te)||We(te)){var fe;j.buffers[$]&&(fe=j.buffers[$],De(te)&&fe._buffer.byteLength>=te.byteLength?fe.subdata(te):(fe.destroy(),j.buffers[$]=null)),j.buffers[$]||(fe=j.buffers[$]=w.create(U,fi,!1,!0)),O.buffer=w.getBuffer(fe),O.size=O.buffer.dimension|0,O.normalized=!1,O.type=O.buffer.dtype,O.offset=0,O.stride=0,O.divisor=0,O.state=1,J[$]=1}else w.getBuffer(U)?(O.buffer=w.getBuffer(U),O.size=O.buffer.dimension|0,O.normalized=!1,O.type=O.buffer.dtype,O.offset=0,O.stride=0,O.divisor=0,O.state=1):w.getBuffer(U.buffer)?(O.buffer=w.getBuffer(U.buffer),O.size=(+U.size||O.buffer.dimension)|0,O.normalized=!!U.normalized||!1,"type"in U?(f.parameter(U.type,lr,"invalid buffer type"),O.type=lr[U.type]):O.type=O.buffer.dtype,O.offset=(U.offset||0)|0,O.stride=(U.stride||0)|0,O.divisor=(U.divisor||0)|0,O.state=1,f(O.size>=1&&O.size<=4,"size must be between 1 and 4"),f(O.offset>=0,"invalid offset"),f(O.stride>=0&&O.stride<=255,"stride must be between 0 and 255"),f(O.divisor>=0,"divisor must be positive"),f(!O.divisor||!!t.angle_instanced_arrays,"ANGLE_instanced_arrays must be enabled to use divisor")):"x"in U?(f($>0,"first attribute must not be a constant"),O.x=+U.x||0,O.y=+U.y||0,O.z=+U.z||0,O.w=+U.w||0,O.state=2):f(!1,"invalid attribute spec for location "+$)}for(var q=0;q<j.buffers.length;++q)!J[q]&&j.buffers[q]&&(j.buffers[q].destroy(),j.buffers[q]=null);return j.refresh(),C}return C.destroy=function(){for(var H=0;H<j.buffers.length;++H)j.buffers[H]&&j.buffers[H].destroy();j.buffers.length=0,j.destroy()},C._vao=j,C._reglType="vao",C(F)}return I}var oi=35632,Zu=35633,Ju=35718,es=35721;function rs(e,t,u,A){var w={},E={};function x(p,S,P,W){this.name=p,this.id=S,this.location=P,this.info=W}function D(p,S){for(var P=0;P<p.length;++P)if(p[P].id===S.id){p[P].location=S.location;return}p.push(S)}function B(p,S,P){var W=p===oi?w:E,G=W[S];if(!G){var Y=t.str(S);G=e.createShader(p),e.shaderSource(G,Y),e.compileShader(G),f.shaderError(e,G,Y,p,P),W[S]=G}return G}var M={},I=[],V=0;function X(p,S){this.id=V++,this.fragId=p,this.vertId=S,this.program=null,this.uniforms=[],this.attributes=[],this.refCount=1,A.profile&&(this.stats={uniformsCount:0,attributesCount:0})}function N(p,S,P){var W,G,Y=B(oi,p.fragId),F=B(Zu,p.vertId),j=p.program=e.createProgram();if(e.attachShader(j,Y),e.attachShader(j,F),P)for(W=0;W<P.length;++W){var C=P[W];e.bindAttribLocation(j,C[0],C[1])}e.linkProgram(j),f.linkError(e,j,t.str(p.fragId),t.str(p.vertId),S);var H=e.getProgramParameter(j,Ju);A.profile&&(p.stats.uniformsCount=H);var J=p.uniforms;for(W=0;W<H;++W)if(G=e.getActiveUniform(j,W),G)if(G.size>1)for(var ne=0;ne<G.size;++ne){var $=G.name.replace("[0]","["+ne+"]");D(J,new x($,t.id($),e.getUniformLocation(j,$),G))}else D(J,new x(G.name,t.id(G.name),e.getUniformLocation(j,G.name),G));var U=e.getProgramParameter(j,es);A.profile&&(p.stats.attributesCount=U);var O=p.attributes;for(W=0;W<U;++W)G=e.getActiveAttrib(j,W),G&&D(O,new x(G.name,t.id(G.name),e.getAttribLocation(j,G.name),G))}A.profile&&(u.getMaxUniformsCount=function(){var p=0;return I.forEach(function(S){S.stats.uniformsCount>p&&(p=S.stats.uniformsCount)}),p},u.getMaxAttributesCount=function(){var p=0;return I.forEach(function(S){S.stats.attributesCount>p&&(p=S.stats.attributesCount)}),p});function z(){w={},E={};for(var p=0;p<I.length;++p)N(I[p],null,I[p].attributes.map(function(S){return[S.location,S.name]}))}return{clear:function(){var p=e.deleteShader.bind(e);He(w).forEach(p),w={},He(E).forEach(p),E={},I.forEach(function(S){e.deleteProgram(S.program)}),I.length=0,M={},u.shaderCount=0},program:function(p,S,P,W){f.command(p>=0,"missing vertex shader",P),f.command(S>=0,"missing fragment shader",P);var G=M[S];G||(G=M[S]={});var Y=G[p];if(Y&&(Y.refCount++,!W))return Y;var F=new X(S,p);return u.shaderCount++,N(F,P,W),Y||(G[p]=F),I.push(F),ye(F,{destroy:function(){if(F.refCount--,F.refCount<=0){e.deleteProgram(F.program);var j=I.indexOf(F);I.splice(j,1),u.shaderCount--}G[F.vertId].refCount<=0&&(e.deleteShader(E[F.vertId]),delete E[F.vertId],delete M[F.fragId][F.vertId]),Object.keys(M[F.fragId]).length||(e.deleteShader(w[F.fragId]),delete w[F.fragId],delete M[F.fragId])}})},restore:z,shader:B,frag:-1,vert:-1}}var ts=6408,Xr=5121,ns=3333,Et=5126;function as(e,t,u,A,w,E,x){function D(I){var V;t.next===null?(f(w.preserveDrawingBuffer,'you must create a webgl context with "preserveDrawingBuffer":true in order to read pixels from the drawing buffer'),V=Xr):(f(t.next.colorAttachments[0].texture!==null,"You cannot read from a renderbuffer"),V=t.next.colorAttachments[0].texture._texture.type,E.oes_texture_float?(f(V===Xr||V===Et,"Reading from a framebuffer is only allowed for the types 'uint8' and 'float'"),V===Et&&f(x.readFloat,"Reading 'float' values is not permitted in your browser. For a fallback, please see: https://www.npmjs.com/package/glsl-read-float")):f(V===Xr,"Reading from a framebuffer is only allowed for the type 'uint8'"));var X=0,N=0,z=A.framebufferWidth,p=A.framebufferHeight,S=null;De(I)?S=I:I&&(f.type(I,"object","invalid arguments to regl.read()"),X=I.x|0,N=I.y|0,f(X>=0&&X<A.framebufferWidth,"invalid x offset for regl.read"),f(N>=0&&N<A.framebufferHeight,"invalid y offset for regl.read"),z=(I.width||A.framebufferWidth-X)|0,p=(I.height||A.framebufferHeight-N)|0,S=I.data||null),S&&(V===Xr?f(S instanceof Uint8Array,"buffer must be 'Uint8Array' when reading from a framebuffer of type 'uint8'"):V===Et&&f(S instanceof Float32Array,"buffer must be 'Float32Array' when reading from a framebuffer of type 'float'")),f(z>0&&z+X<=A.framebufferWidth,"invalid width for read pixels"),f(p>0&&p+N<=A.framebufferHeight,"invalid height for read pixels"),u();var P=z*p*4;return S||(V===Xr?S=new Uint8Array(P):V===Et&&(S=S||new Float32Array(P))),f.isTypedArray(S,"data buffer for regl.read() must be a typedarray"),f(S.byteLength>=P,"data buffer for regl.read() too small"),e.pixelStorei(ns,4),e.readPixels(X,N,z,p,ts,V,S),S}function B(I){var V;return t.setFBO({framebuffer:I.framebuffer},function(){V=D(I)}),V}function M(I){return!I||!("framebuffer"in I)?D(I):B(I)}return M}function Cr(e){return Array.prototype.slice.call(e)}function Fr(e){return Cr(e).join("")}function is(){var e=0,t=[],u=[];function A(V){for(var X=0;X<u.length;++X)if(u[X]===V)return t[X];var N="g"+e++;return t.push(N),u.push(V),N}function w(){var V=[];function X(){V.push.apply(V,Cr(arguments))}var N=[];function z(){var p="v"+e++;return N.push(p),arguments.length>0&&(V.push(p,"="),V.push.apply(V,Cr(arguments)),V.push(";")),p}return ye(X,{def:z,toString:function(){return Fr([N.length>0?"var "+N.join(",")+";":"",Fr(V)])}})}function E(){var V=w(),X=w(),N=V.toString,z=X.toString;function p(S,P){X(S,P,"=",V.def(S,P),";")}return ye(function(){V.apply(V,Cr(arguments))},{def:V.def,entry:V,exit:X,save:p,set:function(S,P,W){p(S,P),V(S,P,"=",W,";")},toString:function(){return N()+z()}})}function x(){var V=Fr(arguments),X=E(),N=E(),z=X.toString,p=N.toString;return ye(X,{then:function(){return X.apply(X,Cr(arguments)),this},else:function(){return N.apply(N,Cr(arguments)),this},toString:function(){var S=p();return S&&(S="else{"+S+"}"),Fr(["if(",V,"){",z(),"}",S])}})}var D=w(),B={};function M(V,X){var N=[];function z(){var G="a"+N.length;return N.push(G),G}X=X||0;for(var p=0;p<X;++p)z();var S=E(),P=S.toString,W=B[V]=ye(S,{arg:z,toString:function(){return Fr(["function(",N.join(),"){",P(),"}"])}});return W}function I(){var V=['"use strict";',D,"return {"];Object.keys(B).forEach(function(z){V.push('"',z,'":',B[z].toString(),",")}),V.push("}");var X=Fr(V).replace(/;/g,`;
`).replace(/}/g,`}
`).replace(/{/g,`{
`),N=Function.apply(null,t.concat(X));return N.apply(null,u)}return{global:D,link:A,block:w,proc:M,scope:E,cond:x,compile:I}}var Dr="xyzw".split(""),ui=5121,Nr=1,mn=2,vn=0,hn=1,pn=2,_n=3,At=4,si=5,ci=6,li="dither",di="blend.enable",mi="blend.color",bn="blend.equation",yn="blend.func",vi="depth.enable",hi="depth.func",pi="depth.range",_i="depth.mask",En="colorMask",bi="cull.enable",yi="cull.face",An="frontFace",Tn="lineWidth",Ei="polygonOffset.enable",xn="polygonOffset.offset",Ai="sample.alpha",Ti="sample.enable",Sn="sample.coverage",xi="stencil.enable",Si="stencil.mask",gn="stencil.func",Ln="stencil.opFront",$r="stencil.opBack",gi="scissor.enable",Tt="scissor.box",rr="viewport",Wr="profile",pr="framebuffer",Yr="vert",Qr="frag",_r="elements",br="primitive",yr="count",xt="offset",St="instances",qr="vao",wn="Width",Rn="Height",Br=pr+wn,Ir=pr+Rn,fs=rr+wn,os=rr+Rn,Li="drawingBuffer",wi=Li+wn,Ri=Li+Rn,us=[yn,bn,gn,Ln,$r,Sn,rr,Tt,xn],kr=34962,ss=34963,cs=35632,ls=35633,Oi=3553,ds=34067,ms=2884,vs=3042,hs=3024,ps=2960,_s=2929,bs=3089,ys=32823,Es=32926,As=32928,On=5126,gt=35664,Lt=35665,wt=35666,Gn=5124,Rt=35667,Ot=35668,Gt=35669,Cn=35670,Ct=35671,Ft=35672,Dt=35673,Kr=35674,Zr=35675,Jr=35676,et=35678,rt=35680,Gi=4,tt=1028,Er=1029,Ci=2304,Fn=2305,Ts=32775,xs=32776,Ss=519,ar=7680,Fi=0,Di=1,Ni=32774,gs=513,Bi=36160,Ls=36064,Ke={0:0,1:1,zero:0,one:1,"src color":768,"one minus src color":769,"src alpha":770,"one minus src alpha":771,"dst color":774,"one minus dst color":775,"dst alpha":772,"one minus dst alpha":773,"constant color":32769,"one minus constant color":32770,"constant alpha":32771,"one minus constant alpha":32772,"src alpha saturate":776},Ii=["constant color, constant alpha","one minus constant color, constant alpha","constant color, one minus constant alpha","one minus constant color, one minus constant alpha","constant alpha, constant color","constant alpha, one minus constant color","one minus constant alpha, constant color","one minus constant alpha, one minus constant color"],Pr={never:512,less:513,"<":513,equal:514,"=":514,"==":514,"===":514,lequal:515,"<=":515,greater:516,">":516,notequal:517,"!=":517,"!==":517,gequal:518,">=":518,always:519},ir={0:0,zero:0,keep:7680,replace:7681,increment:7682,decrement:7683,"increment wrap":34055,"decrement wrap":34056,invert:5386},ki={frag:cs,vert:ls},Dn={cw:Ci,ccw:Fn};function Nt(e){return Array.isArray(e)||De(e)||We(e)}function Pi(e){return e.sort(function(t,u){return t===rr?-1:u===rr?1:t<u?-1:1})}function Pe(e,t,u,A){this.thisDep=e,this.contextDep=t,this.propDep=u,this.append=A}function fr(e){return e&&!(e.thisDep||e.contextDep||e.propDep)}function Le(e){return new Pe(!1,!1,!1,e)}function Ve(e,t){var u=e.type;if(u===vn){var A=e.data.length;return new Pe(!0,A>=1,A>=2,t)}else if(u===At){var w=e.data;return new Pe(w.thisDep,w.contextDep,w.propDep,t)}else{if(u===si)return new Pe(!1,!1,!1,t);if(u===ci){for(var E=!1,x=!1,D=!1,B=0;B<e.data.length;++B){var M=e.data[B];if(M.type===hn)D=!0;else if(M.type===pn)x=!0;else if(M.type===_n)E=!0;else if(M.type===vn){E=!0;var I=M.data;I>=1&&(x=!0),I>=2&&(D=!0)}else M.type===At&&(E=E||M.data.thisDep,x=x||M.data.contextDep,D=D||M.data.propDep)}return new Pe(E,x,D,t)}else return new Pe(u===_n,u===pn,u===hn,t)}}var Mi=new Pe(!1,!1,!1,function(){});function ws(e,t,u,A,w,E,x,D,B,M,I,V,X,N,z){var p=M.Record,S={add:32774,subtract:32778,"reverse subtract":32779};u.ext_blend_minmax&&(S.min=Ts,S.max=xs);var P=u.angle_instanced_arrays,W=u.webgl_draw_buffers,G={dirty:!0,profile:z.profile},Y={},F=[],j={},C={};function H(a){return a.replace(".","_")}function J(a,r,c){var h=H(a);F.push(a),Y[h]=G[h]=!!c,j[h]=r}function ne(a,r,c){var h=H(a);F.push(a),Array.isArray(c)?(G[h]=c.slice(),Y[h]=c.slice()):G[h]=Y[h]=c,C[h]=r}J(li,hs),J(di,vs),ne(mi,"blendColor",[0,0,0,0]),ne(bn,"blendEquationSeparate",[Ni,Ni]),ne(yn,"blendFuncSeparate",[Di,Fi,Di,Fi]),J(vi,_s,!0),ne(hi,"depthFunc",gs),ne(pi,"depthRange",[0,1]),ne(_i,"depthMask",!0),ne(En,En,[!0,!0,!0,!0]),J(bi,ms),ne(yi,"cullFace",Er),ne(An,An,Fn),ne(Tn,Tn,1),J(Ei,ys),ne(xn,"polygonOffset",[0,0]),J(Ai,Es),J(Ti,As),ne(Sn,"sampleCoverage",[1,!1]),J(xi,ps),ne(Si,"stencilMask",-1),ne(gn,"stencilFunc",[Ss,0,-1]),ne(Ln,"stencilOpSeparate",[tt,ar,ar,ar]),ne($r,"stencilOpSeparate",[Er,ar,ar,ar]),J(gi,bs),ne(Tt,"scissor",[0,0,e.drawingBufferWidth,e.drawingBufferHeight]),ne(rr,rr,[0,0,e.drawingBufferWidth,e.drawingBufferHeight]);var $={gl:e,context:X,strings:t,next:Y,current:G,draw:V,elements:E,buffer:w,shader:I,attributes:M.state,vao:M,uniforms:B,framebuffer:D,extensions:u,timer:N,isBufferArgs:Nt},U={primTypes:xr,compareFuncs:Pr,blendFuncs:Ke,blendEquations:S,stencilOps:ir,glTypes:lr,orientationType:Dn};f.optional(function(){$.isArrayLike=Te}),W&&(U.backBuffer=[Er],U.drawBuffer=Ue(A.maxDrawbuffers,function(a){return a===0?[0]:Ue(a,function(r){return Ls+r})}));var O=0;function te(){var a=is(),r=a.link,c=a.global;a.id=O++,a.batchId="0";var h=r($),_=a.shared={props:"a0"};Object.keys($).forEach(function(s){_[s]=c.def(h,".",s)}),f.optional(function(){a.CHECK=r(f),a.commandStr=f.guessCommand(),a.command=r(a.commandStr),a.assert=function(s,n,v){s("if(!(",n,"))",this.CHECK,".commandRaise(",r(v),",",this.command,");")},U.invalidBlendCombinations=Ii});var d=a.next={},l=a.current={};Object.keys(C).forEach(function(s){Array.isArray(G[s])&&(d[s]=c.def(_.next,".",s),l[s]=c.def(_.current,".",s))});var m=a.constants={};Object.keys(U).forEach(function(s){m[s]=c.def(JSON.stringify(U[s]))}),a.invoke=function(s,n){switch(n.type){case vn:var v=["this",_.context,_.props,a.batchId];return s.def(r(n.data),".call(",v.slice(0,Math.max(n.data.length+1,4)),")");case hn:return s.def(_.props,n.data);case pn:return s.def(_.context,n.data);case _n:return s.def("this",n.data);case At:return n.data.append(a,s),n.data.ref;case si:return n.data.toString();case ci:return n.data.map(function(b){return a.invoke(s,b)})}},a.attribCache={};var i={};return a.scopeAttrib=function(s){var n=t.id(s);if(n in i)return i[n];var v=M.scope[n];v||(v=M.scope[n]=new p);var b=i[n]=r(v);return b},a}function fe(a){var r=a.static,c=a.dynamic,h;if(Wr in r){var _=!!r[Wr];h=Le(function(l,m){return _}),h.enable=_}else if(Wr in c){var d=c[Wr];h=Ve(d,function(l,m){return l.invoke(m,d)})}return h}function q(a,r){var c=a.static,h=a.dynamic;if(pr in c){var _=c[pr];return _?(_=D.getFramebuffer(_),f.command(_,"invalid framebuffer object"),Le(function(l,m){var i=l.link(_),s=l.shared;m.set(s.framebuffer,".next",i);var n=s.context;return m.set(n,"."+Br,i+".width"),m.set(n,"."+Ir,i+".height"),i})):Le(function(l,m){var i=l.shared;m.set(i.framebuffer,".next","null");var s=i.context;return m.set(s,"."+Br,s+"."+wi),m.set(s,"."+Ir,s+"."+Ri),"null"})}else if(pr in h){var d=h[pr];return Ve(d,function(l,m){var i=l.invoke(m,d),s=l.shared,n=s.framebuffer,v=m.def(n,".getFramebuffer(",i,")");f.optional(function(){l.assert(m,"!"+i+"||"+v,"invalid framebuffer object")}),m.set(n,".next",v);var b=s.context;return m.set(b,"."+Br,v+"?"+v+".width:"+b+"."+wi),m.set(b,"."+Ir,v+"?"+v+".height:"+b+"."+Ri),v})}else return null}function ce(a,r,c){var h=a.static,_=a.dynamic;function d(i){if(i in h){var s=h[i];f.commandType(s,"object","invalid "+i,c.commandStr);var n=!0,v=s.x|0,b=s.y|0,L,R;return"width"in s?(L=s.width|0,f.command(L>=0,"invalid "+i,c.commandStr)):n=!1,"height"in s?(R=s.height|0,f.command(R>=0,"invalid "+i,c.commandStr)):n=!1,new Pe(!n&&r&&r.thisDep,!n&&r&&r.contextDep,!n&&r&&r.propDep,function(Z,ue){var K=Z.shared.context,re=L;"width"in s||(re=ue.def(K,".",Br,"-",v));var ie=R;return"height"in s||(ie=ue.def(K,".",Ir,"-",b)),[v,b,re,ie]})}else if(i in _){var g=_[i],k=Ve(g,function(Z,ue){var K=Z.invoke(ue,g);f.optional(function(){Z.assert(ue,K+"&&typeof "+K+'==="object"',"invalid "+i)});var re=Z.shared.context,ie=ue.def(K,".x|0"),pe=ue.def(K,".y|0"),Ae=ue.def('"width" in ',K,"?",K,".width|0:","(",re,".",Br,"-",ie,")"),Me=ue.def('"height" in ',K,"?",K,".height|0:","(",re,".",Ir,"-",pe,")");return f.optional(function(){Z.assert(ue,Ae+">=0&&"+Me+">=0","invalid "+i)}),[ie,pe,Ae,Me]});return r&&(k.thisDep=k.thisDep||r.thisDep,k.contextDep=k.contextDep||r.contextDep,k.propDep=k.propDep||r.propDep),k}else return r?new Pe(r.thisDep,r.contextDep,r.propDep,function(Z,ue){var K=Z.shared.context;return[0,0,ue.def(K,".",Br),ue.def(K,".",Ir)]}):null}var l=d(rr);if(l){var m=l;l=new Pe(l.thisDep,l.contextDep,l.propDep,function(i,s){var n=m.append(i,s),v=i.shared.context;return s.set(v,"."+fs,n[2]),s.set(v,"."+os,n[3]),n})}return{viewport:l,scissor_box:d(Tt)}}function ae(a,r){var c=a.static,h=typeof c[Qr]=="string"&&typeof c[Yr]=="string";if(h){if(Object.keys(r.dynamic).length>0)return null;var _=r.static,d=Object.keys(_);if(d.length>0&&typeof _[d[0]]=="number"){for(var l=[],m=0;m<d.length;++m)f(typeof _[d[m]]=="number","must specify all vertex attribute locations when using vaos"),l.push([_[d[m]]|0,d[m]]);return l}}return null}function se(a,r,c){var h=a.static,_=a.dynamic;function d(n){if(n in h){var v=t.id(h[n]);f.optional(function(){I.shader(ki[n],v,f.guessCommand())});var b=Le(function(){return v});return b.id=v,b}else if(n in _){var L=_[n];return Ve(L,function(R,g){var k=R.invoke(g,L),Z=g.def(R.shared.strings,".id(",k,")");return f.optional(function(){g(R.shared.shader,".shader(",ki[n],",",Z,",",R.command,");")}),Z})}return null}var l=d(Qr),m=d(Yr),i=null,s;return fr(l)&&fr(m)?(i=I.program(m.id,l.id,null,c),s=Le(function(n,v){return n.link(i)})):s=new Pe(l&&l.thisDep||m&&m.thisDep,l&&l.contextDep||m&&m.contextDep,l&&l.propDep||m&&m.propDep,function(n,v){var b=n.shared.shader,L;l?L=l.append(n,v):L=v.def(b,".",Qr);var R;m?R=m.append(n,v):R=v.def(b,".",Yr);var g=b+".program("+R+","+L;return f.optional(function(){g+=","+n.command}),v.def(g+")")}),{frag:l,vert:m,progVar:s,program:i}}function he(a,r){var c=a.static,h=a.dynamic;function _(){if(_r in c){var n=c[_r];Nt(n)?n=E.getElements(E.create(n,!0)):n&&(n=E.getElements(n),f.command(n,"invalid elements",r.commandStr));var v=Le(function(L,R){if(n){var g=L.link(n);return L.ELEMENTS=g,g}return L.ELEMENTS=null,null});return v.value=n,v}else if(_r in h){var b=h[_r];return Ve(b,function(L,R){var g=L.shared,k=g.isBufferArgs,Z=g.elements,ue=L.invoke(R,b),K=R.def("null"),re=R.def(k,"(",ue,")"),ie=L.cond(re).then(K,"=",Z,".createStream(",ue,");").else(K,"=",Z,".getElements(",ue,");");return f.optional(function(){L.assert(ie.else,"!"+ue+"||"+K,"invalid elements")}),R.entry(ie),R.exit(L.cond(re).then(Z,".destroyStream(",K,");")),L.ELEMENTS=K,K})}return null}var d=_();function l(){if(br in c){var n=c[br];return f.commandParameter(n,xr,"invalid primitve",r.commandStr),Le(function(b,L){return xr[n]})}else if(br in h){var v=h[br];return Ve(v,function(b,L){var R=b.constants.primTypes,g=b.invoke(L,v);return f.optional(function(){b.assert(L,g+" in "+R,"invalid primitive, must be one of "+Object.keys(xr))}),L.def(R,"[",g,"]")})}else if(d)return fr(d)?d.value?Le(function(b,L){return L.def(b.ELEMENTS,".primType")}):Le(function(){return Gi}):new Pe(d.thisDep,d.contextDep,d.propDep,function(b,L){var R=b.ELEMENTS;return L.def(R,"?",R,".primType:",Gi)});return null}function m(n,v){if(n in c){var b=c[n]|0;return f.command(!v||b>=0,"invalid "+n,r.commandStr),Le(function(R,g){return v&&(R.OFFSET=b),b})}else if(n in h){var L=h[n];return Ve(L,function(R,g){var k=R.invoke(g,L);return v&&(R.OFFSET=k,f.optional(function(){R.assert(g,k+">=0","invalid "+n)})),k})}else if(v&&d)return Le(function(R,g){return R.OFFSET="0",0});return null}var i=m(xt,!0);function s(){if(yr in c){var n=c[yr]|0;return f.command(typeof n=="number"&&n>=0,"invalid vertex count",r.commandStr),Le(function(){return n})}else if(yr in h){var v=h[yr];return Ve(v,function(R,g){var k=R.invoke(g,v);return f.optional(function(){R.assert(g,"typeof "+k+'==="number"&&'+k+">=0&&"+k+"===("+k+"|0)","invalid vertex count")}),k})}else if(d)if(fr(d)){if(d)return i?new Pe(i.thisDep,i.contextDep,i.propDep,function(R,g){var k=g.def(R.ELEMENTS,".vertCount-",R.OFFSET);return f.optional(function(){R.assert(g,k+">=0","invalid vertex offset/element buffer too small")}),k}):Le(function(R,g){return g.def(R.ELEMENTS,".vertCount")});var b=Le(function(){return-1});return f.optional(function(){b.MISSING=!0}),b}else{var L=new Pe(d.thisDep||i.thisDep,d.contextDep||i.contextDep,d.propDep||i.propDep,function(R,g){var k=R.ELEMENTS;return R.OFFSET?g.def(k,"?",k,".vertCount-",R.OFFSET,":-1"):g.def(k,"?",k,".vertCount:-1")});return f.optional(function(){L.DYNAMIC=!0}),L}return null}return{elements:d,primitive:l(),count:s(),instances:m(St,!1),offset:i}}function we(a,r){var c=a.static,h=a.dynamic,_={};return F.forEach(function(d){var l=H(d);function m(i,s){if(d in c){var n=i(c[d]);_[l]=Le(function(){return n})}else if(d in h){var v=h[d];_[l]=Ve(v,function(b,L){return s(b,L,b.invoke(L,v))})}}switch(d){case bi:case di:case li:case xi:case vi:case gi:case Ei:case Ai:case Ti:case _i:return m(function(i){return f.commandType(i,"boolean",d,r.commandStr),i},function(i,s,n){return f.optional(function(){i.assert(s,"typeof "+n+'==="boolean"',"invalid flag "+d,i.commandStr)}),n});case hi:return m(function(i){return f.commandParameter(i,Pr,"invalid "+d,r.commandStr),Pr[i]},function(i,s,n){var v=i.constants.compareFuncs;return f.optional(function(){i.assert(s,n+" in "+v,"invalid "+d+", must be one of "+Object.keys(Pr))}),s.def(v,"[",n,"]")});case pi:return m(function(i){return f.command(Te(i)&&i.length===2&&typeof i[0]=="number"&&typeof i[1]=="number"&&i[0]<=i[1],"depth range is 2d array",r.commandStr),i},function(i,s,n){f.optional(function(){i.assert(s,i.shared.isArrayLike+"("+n+")&&"+n+".length===2&&typeof "+n+'[0]==="number"&&typeof '+n+'[1]==="number"&&'+n+"[0]<="+n+"[1]","depth range must be a 2d array")});var v=s.def("+",n,"[0]"),b=s.def("+",n,"[1]");return[v,b]});case yn:return m(function(i){f.commandType(i,"object","blend.func",r.commandStr);var s="srcRGB"in i?i.srcRGB:i.src,n="srcAlpha"in i?i.srcAlpha:i.src,v="dstRGB"in i?i.dstRGB:i.dst,b="dstAlpha"in i?i.dstAlpha:i.dst;return f.commandParameter(s,Ke,l+".srcRGB",r.commandStr),f.commandParameter(n,Ke,l+".srcAlpha",r.commandStr),f.commandParameter(v,Ke,l+".dstRGB",r.commandStr),f.commandParameter(b,Ke,l+".dstAlpha",r.commandStr),f.command(Ii.indexOf(s+", "+v)===-1,"unallowed blending combination (srcRGB, dstRGB) = ("+s+", "+v+")",r.commandStr),[Ke[s],Ke[v],Ke[n],Ke[b]]},function(i,s,n){var v=i.constants.blendFuncs;f.optional(function(){i.assert(s,n+"&&typeof "+n+'==="object"',"invalid blend func, must be an object")});function b(K,re){var ie=s.def('"',K,re,'" in ',n,"?",n,".",K,re,":",n,".",K);return f.optional(function(){i.assert(s,ie+" in "+v,"invalid "+d+"."+K+re+", must be one of "+Object.keys(Ke))}),ie}var L=b("src","RGB"),R=b("dst","RGB");f.optional(function(){var K=i.constants.invalidBlendCombinations;i.assert(s,K+".indexOf("+L+'+", "+'+R+") === -1 ","unallowed blending combination for (srcRGB, dstRGB)")});var g=s.def(v,"[",L,"]"),k=s.def(v,"[",b("src","Alpha"),"]"),Z=s.def(v,"[",R,"]"),ue=s.def(v,"[",b("dst","Alpha"),"]");return[g,Z,k,ue]});case bn:return m(function(i){if(typeof i=="string")return f.commandParameter(i,S,"invalid "+d,r.commandStr),[S[i],S[i]];if(typeof i=="object")return f.commandParameter(i.rgb,S,d+".rgb",r.commandStr),f.commandParameter(i.alpha,S,d+".alpha",r.commandStr),[S[i.rgb],S[i.alpha]];f.commandRaise("invalid blend.equation",r.commandStr)},function(i,s,n){var v=i.constants.blendEquations,b=s.def(),L=s.def(),R=i.cond("typeof ",n,'==="string"');return f.optional(function(){function g(k,Z,ue){i.assert(k,ue+" in "+v,"invalid "+Z+", must be one of "+Object.keys(S))}g(R.then,d,n),i.assert(R.else,n+"&&typeof "+n+'==="object"',"invalid "+d),g(R.else,d+".rgb",n+".rgb"),g(R.else,d+".alpha",n+".alpha")}),R.then(b,"=",L,"=",v,"[",n,"];"),R.else(b,"=",v,"[",n,".rgb];",L,"=",v,"[",n,".alpha];"),s(R),[b,L]});case mi:return m(function(i){return f.command(Te(i)&&i.length===4,"blend.color must be a 4d array",r.commandStr),Ue(4,function(s){return+i[s]})},function(i,s,n){return f.optional(function(){i.assert(s,i.shared.isArrayLike+"("+n+")&&"+n+".length===4","blend.color must be a 4d array")}),Ue(4,function(v){return s.def("+",n,"[",v,"]")})});case Si:return m(function(i){return f.commandType(i,"number",l,r.commandStr),i|0},function(i,s,n){return f.optional(function(){i.assert(s,"typeof "+n+'==="number"',"invalid stencil.mask")}),s.def(n,"|0")});case gn:return m(function(i){f.commandType(i,"object",l,r.commandStr);var s=i.cmp||"keep",n=i.ref||0,v="mask"in i?i.mask:-1;return f.commandParameter(s,Pr,d+".cmp",r.commandStr),f.commandType(n,"number",d+".ref",r.commandStr),f.commandType(v,"number",d+".mask",r.commandStr),[Pr[s],n,v]},function(i,s,n){var v=i.constants.compareFuncs;f.optional(function(){function g(){i.assert(s,Array.prototype.join.call(arguments,""),"invalid stencil.func")}g(n+"&&typeof ",n,'==="object"'),g('!("cmp" in ',n,")||(",n,".cmp in ",v,")")});var b=s.def('"cmp" in ',n,"?",v,"[",n,".cmp]",":",ar),L=s.def(n,".ref|0"),R=s.def('"mask" in ',n,"?",n,".mask|0:-1");return[b,L,R]});case Ln:case $r:return m(function(i){f.commandType(i,"object",l,r.commandStr);var s=i.fail||"keep",n=i.zfail||"keep",v=i.zpass||"keep";return f.commandParameter(s,ir,d+".fail",r.commandStr),f.commandParameter(n,ir,d+".zfail",r.commandStr),f.commandParameter(v,ir,d+".zpass",r.commandStr),[d===$r?Er:tt,ir[s],ir[n],ir[v]]},function(i,s,n){var v=i.constants.stencilOps;f.optional(function(){i.assert(s,n+"&&typeof "+n+'==="object"',"invalid "+d)});function b(L){return f.optional(function(){i.assert(s,'!("'+L+'" in '+n+")||("+n+"."+L+" in "+v+")","invalid "+d+"."+L+", must be one of "+Object.keys(ir))}),s.def('"',L,'" in ',n,"?",v,"[",n,".",L,"]:",ar)}return[d===$r?Er:tt,b("fail"),b("zfail"),b("zpass")]});case xn:return m(function(i){f.commandType(i,"object",l,r.commandStr);var s=i.factor|0,n=i.units|0;return f.commandType(s,"number",l+".factor",r.commandStr),f.commandType(n,"number",l+".units",r.commandStr),[s,n]},function(i,s,n){f.optional(function(){i.assert(s,n+"&&typeof "+n+'==="object"',"invalid "+d)});var v=s.def(n,".factor|0"),b=s.def(n,".units|0");return[v,b]});case yi:return m(function(i){var s=0;return i==="front"?s=tt:i==="back"&&(s=Er),f.command(!!s,l,r.commandStr),s},function(i,s,n){return f.optional(function(){i.assert(s,n+'==="front"||'+n+'==="back"',"invalid cull.face")}),s.def(n,'==="front"?',tt,":",Er)});case Tn:return m(function(i){return f.command(typeof i=="number"&&i>=A.lineWidthDims[0]&&i<=A.lineWidthDims[1],"invalid line width, must be a positive number between "+A.lineWidthDims[0]+" and "+A.lineWidthDims[1],r.commandStr),i},function(i,s,n){return f.optional(function(){i.assert(s,"typeof "+n+'==="number"&&'+n+">="+A.lineWidthDims[0]+"&&"+n+"<="+A.lineWidthDims[1],"invalid line width")}),n});case An:return m(function(i){return f.commandParameter(i,Dn,l,r.commandStr),Dn[i]},function(i,s,n){return f.optional(function(){i.assert(s,n+'==="cw"||'+n+'==="ccw"',"invalid frontFace, must be one of cw,ccw")}),s.def(n+'==="cw"?'+Ci+":"+Fn)});case En:return m(function(i){return f.command(Te(i)&&i.length===4,"color.mask must be length 4 array",r.commandStr),i.map(function(s){return!!s})},function(i,s,n){return f.optional(function(){i.assert(s,i.shared.isArrayLike+"("+n+")&&"+n+".length===4","invalid color.mask")}),Ue(4,function(v){return"!!"+n+"["+v+"]"})});case Sn:return m(function(i){f.command(typeof i=="object"&&i,l,r.commandStr);var s="value"in i?i.value:1,n=!!i.invert;return f.command(typeof s=="number"&&s>=0&&s<=1,"sample.coverage.value must be a number between 0 and 1",r.commandStr),[s,n]},function(i,s,n){f.optional(function(){i.assert(s,n+"&&typeof "+n+'==="object"',"invalid sample.coverage")});var v=s.def('"value" in ',n,"?+",n,".value:1"),b=s.def("!!",n,".invert");return[v,b]})}}),_}function Fe(a,r){var c=a.static,h=a.dynamic,_={};return Object.keys(c).forEach(function(d){var l=c[d],m;if(typeof l=="number"||typeof l=="boolean")m=Le(function(){return l});else if(typeof l=="function"){var i=l._reglType;i==="texture2d"||i==="textureCube"?m=Le(function(s){return s.link(l)}):i==="framebuffer"||i==="framebufferCube"?(f.command(l.color.length>0,'missing color attachment for framebuffer sent to uniform "'+d+'"',r.commandStr),m=Le(function(s){return s.link(l.color[0])})):f.commandRaise('invalid data for uniform "'+d+'"',r.commandStr)}else Te(l)?m=Le(function(s){var n=s.global.def("[",Ue(l.length,function(v){return f.command(typeof l[v]=="number"||typeof l[v]=="boolean","invalid uniform "+d,s.commandStr),l[v]}),"]");return n}):f.commandRaise('invalid or missing data for uniform "'+d+'"',r.commandStr);m.value=l,_[d]=m}),Object.keys(h).forEach(function(d){var l=h[d];_[d]=Ve(l,function(m,i){return m.invoke(i,l)})}),_}function me(a,r){var c=a.static,h=a.dynamic,_={};return Object.keys(c).forEach(function(d){var l=c[d],m=t.id(d),i=new p;if(Nt(l))i.state=Nr,i.buffer=w.getBuffer(w.create(l,kr,!1,!0)),i.type=0;else{var s=w.getBuffer(l);if(s)i.state=Nr,i.buffer=s,i.type=0;else if(f.command(typeof l=="object"&&l,"invalid data for attribute "+d,r.commandStr),"constant"in l){var n=l.constant;i.buffer="null",i.state=mn,typeof n=="number"?i.x=n:(f.command(Te(n)&&n.length>0&&n.length<=4,"invalid constant for attribute "+d,r.commandStr),Dr.forEach(function(Z,ue){ue<n.length&&(i[Z]=n[ue])}))}else{Nt(l.buffer)?s=w.getBuffer(w.create(l.buffer,kr,!1,!0)):s=w.getBuffer(l.buffer),f.command(!!s,'missing buffer for attribute "'+d+'"',r.commandStr);var v=l.offset|0;f.command(v>=0,'invalid offset for attribute "'+d+'"',r.commandStr);var b=l.stride|0;f.command(b>=0&&b<256,'invalid stride for attribute "'+d+'", must be integer betweeen [0, 255]',r.commandStr);var L=l.size|0;f.command(!("size"in l)||L>0&&L<=4,'invalid size for attribute "'+d+'", must be 1,2,3,4',r.commandStr);var R=!!l.normalized,g=0;"type"in l&&(f.commandParameter(l.type,lr,"invalid type for attribute "+d,r.commandStr),g=lr[l.type]);var k=l.divisor|0;"divisor"in l&&(f.command(k===0||P,'cannot specify divisor for attribute "'+d+'", instancing not supported',r.commandStr),f.command(k>=0,'invalid divisor for attribute "'+d+'"',r.commandStr)),f.optional(function(){var Z=r.commandStr,ue=["buffer","offset","divisor","normalized","type","size","stride"];Object.keys(l).forEach(function(K){f.command(ue.indexOf(K)>=0,'unknown parameter "'+K+'" for attribute pointer "'+d+'" (valid parameters are '+ue+")",Z)})}),i.buffer=s,i.state=Nr,i.size=L,i.normalized=R,i.type=g||s.dtype,i.offset=v,i.stride=b,i.divisor=k}}_[d]=Le(function(Z,ue){var K=Z.attribCache;if(m in K)return K[m];var re={isStream:!1};return Object.keys(i).forEach(function(ie){re[ie]=i[ie]}),i.buffer&&(re.buffer=Z.link(i.buffer),re.type=re.type||re.buffer+".dtype"),K[m]=re,re})}),Object.keys(h).forEach(function(d){var l=h[d];function m(i,s){var n=i.invoke(s,l),v=i.shared,b=i.constants,L=v.isBufferArgs,R=v.buffer;f.optional(function(){i.assert(s,n+"&&(typeof "+n+'==="object"||typeof '+n+'==="function")&&('+L+"("+n+")||"+R+".getBuffer("+n+")||"+R+".getBuffer("+n+".buffer)||"+L+"("+n+'.buffer)||("constant" in '+n+"&&(typeof "+n+'.constant==="number"||'+v.isArrayLike+"("+n+".constant))))",'invalid dynamic attribute "'+d+'"')});var g={isStream:s.def(!1)},k=new p;k.state=Nr,Object.keys(k).forEach(function(re){g[re]=s.def(""+k[re])});var Z=g.buffer,ue=g.type;s("if(",L,"(",n,")){",g.isStream,"=true;",Z,"=",R,".createStream(",kr,",",n,");",ue,"=",Z,".dtype;","}else{",Z,"=",R,".getBuffer(",n,");","if(",Z,"){",ue,"=",Z,".dtype;",'}else if("constant" in ',n,"){",g.state,"=",mn,";","if(typeof "+n+'.constant === "number"){',g[Dr[0]],"=",n,".constant;",Dr.slice(1).map(function(re){return g[re]}).join("="),"=0;","}else{",Dr.map(function(re,ie){return g[re]+"="+n+".constant.length>"+ie+"?"+n+".constant["+ie+"]:0;"}).join(""),"}}else{","if(",L,"(",n,".buffer)){",Z,"=",R,".createStream(",kr,",",n,".buffer);","}else{",Z,"=",R,".getBuffer(",n,".buffer);","}",ue,'="type" in ',n,"?",b.glTypes,"[",n,".type]:",Z,".dtype;",g.normalized,"=!!",n,".normalized;");function K(re){s(g[re],"=",n,".",re,"|0;")}return K("size"),K("offset"),K("stride"),K("divisor"),s("}}"),s.exit("if(",g.isStream,"){",R,".destroyStream(",Z,");","}"),g}_[d]=Ve(l,m)}),_}function Oe(a,r){var c=a.static,h=a.dynamic;if(qr in c){var _=c[qr];return _!==null&&M.getVAO(_)===null&&(_=M.createVAO(_)),Le(function(l){return l.link(M.getVAO(_))})}else if(qr in h){var d=h[qr];return Ve(d,function(l,m){var i=l.invoke(m,d);return m.def(l.shared.vao+".getVAO("+i+")")})}return null}function be(a){var r=a.static,c=a.dynamic,h={};return Object.keys(r).forEach(function(_){var d=r[_];h[_]=Le(function(l,m){return typeof d=="number"||typeof d=="boolean"?""+d:l.link(d)})}),Object.keys(c).forEach(function(_){var d=c[_];h[_]=Ve(d,function(l,m){return l.invoke(m,d)})}),h}function Re(a,r,c,h,_){var d=a.static,l=a.dynamic;f.optional(function(){var K=[pr,Yr,Qr,_r,br,xt,yr,St,Wr,qr].concat(F);function re(ie){Object.keys(ie).forEach(function(pe){f.command(K.indexOf(pe)>=0,'unknown parameter "'+pe+'"',_.commandStr)})}re(d),re(l)});var m=ae(a,r),i=q(a,_),s=ce(a,i,_),n=he(a,_),v=we(a,_),b=se(a,_,m);function L(K){var re=s[K];re&&(v[K]=re)}L(rr),L(H(Tt));var R=Object.keys(v).length>0,g={framebuffer:i,draw:n,shader:b,state:v,dirty:R,scopeVAO:null,drawVAO:null,useVAO:!1,attributes:{}};if(g.profile=fe(a,_),g.uniforms=Fe(c,_),g.drawVAO=g.scopeVAO=Oe(a,_),!g.drawVAO&&b.program&&!m&&u.angle_instanced_arrays){var k=!0,Z=b.program.attributes.map(function(K){var re=r.static[K];return k=k&&!!re,re});if(k&&Z.length>0){var ue=M.getVAO(M.createVAO(Z));g.drawVAO=new Pe(null,null,null,function(K,re){return K.link(ue)}),g.useVAO=!0}}return m?g.useVAO=!0:g.attributes=me(r,_),g.context=be(h,_),g}function Ge(a,r,c){var h=a.shared,_=h.context,d=a.scope();Object.keys(c).forEach(function(l){r.save(_,"."+l);var m=c[l],i=m.append(a,r);Array.isArray(i)?d(_,".",l,"=[",i.join(),"];"):d(_,".",l,"=",i,";")}),r(d)}function Ce(a,r,c,h){var _=a.shared,d=_.gl,l=_.framebuffer,m;W&&(m=r.def(_.extensions,".webgl_draw_buffers"));var i=a.constants,s=i.drawBuffer,n=i.backBuffer,v;c?v=c.append(a,r):v=r.def(l,".next"),h||r("if(",v,"!==",l,".cur){"),r("if(",v,"){",d,".bindFramebuffer(",Bi,",",v,".framebuffer);"),W&&r(m,".drawBuffersWEBGL(",s,"[",v,".colorAttachments.length]);"),r("}else{",d,".bindFramebuffer(",Bi,",null);"),W&&r(m,".drawBuffersWEBGL(",n,");"),r("}",l,".cur=",v,";"),h||r("}")}function Ne(a,r,c){var h=a.shared,_=h.gl,d=a.current,l=a.next,m=h.current,i=h.next,s=a.cond(m,".dirty");F.forEach(function(n){var v=H(n);if(!(v in c.state)){var b,L;if(v in l){b=l[v],L=d[v];var R=Ue(G[v].length,function(k){return s.def(b,"[",k,"]")});s(a.cond(R.map(function(k,Z){return k+"!=="+L+"["+Z+"]"}).join("||")).then(_,".",C[v],"(",R,");",R.map(function(k,Z){return L+"["+Z+"]="+k}).join(";"),";"))}else{b=s.def(i,".",v);var g=a.cond(b,"!==",m,".",v);s(g),v in j?g(a.cond(b).then(_,".enable(",j[v],");").else(_,".disable(",j[v],");"),m,".",v,"=",b,";"):g(_,".",C[v],"(",b,");",m,".",v,"=",b,";")}}}),Object.keys(c.state).length===0&&s(m,".dirty=false;"),r(s)}function Ie(a,r,c,h){var _=a.shared,d=a.current,l=_.current,m=_.gl;Pi(Object.keys(c)).forEach(function(i){var s=c[i];if(!(h&&!h(s))){var n=s.append(a,r);if(j[i]){var v=j[i];fr(s)?n?r(m,".enable(",v,");"):r(m,".disable(",v,");"):r(a.cond(n).then(m,".enable(",v,");").else(m,".disable(",v,");")),r(l,".",i,"=",n,";")}else if(Te(n)){var b=d[i];r(m,".",C[i],"(",n,");",n.map(function(L,R){return b+"["+R+"]="+L}).join(";"),";")}else r(m,".",C[i],"(",n,");",l,".",i,"=",n,";")}})}function xe(a,r){P&&(a.instancing=r.def(a.shared.extensions,".angle_instanced_arrays"))}function oe(a,r,c,h,_){var d=a.shared,l=a.stats,m=d.current,i=d.timer,s=c.profile;function n(){return typeof performance=="undefined"?"Date.now()":"performance.now()"}var v,b;function L(K){v=r.def(),K(v,"=",n(),";"),typeof _=="string"?K(l,".count+=",_,";"):K(l,".count++;"),N&&(h?(b=r.def(),K(b,"=",i,".getNumPendingQueries();")):K(i,".beginQuery(",l,");"))}function R(K){K(l,".cpuTime+=",n(),"-",v,";"),N&&(h?K(i,".pushScopeStats(",b,",",i,".getNumPendingQueries(),",l,");"):K(i,".endQuery();"))}function g(K){var re=r.def(m,".profile");r(m,".profile=",K,";"),r.exit(m,".profile=",re,";")}var k;if(s){if(fr(s)){s.enable?(L(r),R(r.exit),g("true")):g("false");return}k=s.append(a,r),g(k)}else k=r.def(m,".profile");var Z=a.block();L(Z),r("if(",k,"){",Z,"}");var ue=a.block();R(ue),r.exit("if(",k,"){",ue,"}")}function ke(a,r,c,h,_){var d=a.shared;function l(i){switch(i){case gt:case Rt:case Ct:return 2;case Lt:case Ot:case Ft:return 3;case wt:case Gt:case Dt:return 4;default:return 1}}function m(i,s,n){var v=d.gl,b=r.def(i,".location"),L=r.def(d.attributes,"[",b,"]"),R=n.state,g=n.buffer,k=[n.x,n.y,n.z,n.w],Z=["buffer","normalized","offset","stride"];function ue(){r("if(!",L,".buffer){",v,".enableVertexAttribArray(",b,");}");var re=n.type,ie;if(n.size?ie=r.def(n.size,"||",s):ie=s,r("if(",L,".type!==",re,"||",L,".size!==",ie,"||",Z.map(function(Ae){return L+"."+Ae+"!=="+n[Ae]}).join("||"),"){",v,".bindBuffer(",kr,",",g,".buffer);",v,".vertexAttribPointer(",[b,ie,re,n.normalized,n.stride,n.offset],");",L,".type=",re,";",L,".size=",ie,";",Z.map(function(Ae){return L+"."+Ae+"="+n[Ae]+";"}).join(""),"}"),P){var pe=n.divisor;r("if(",L,".divisor!==",pe,"){",a.instancing,".vertexAttribDivisorANGLE(",[b,pe],");",L,".divisor=",pe,";}")}}function K(){r("if(",L,".buffer){",v,".disableVertexAttribArray(",b,");",L,".buffer=null;","}if(",Dr.map(function(re,ie){return L+"."+re+"!=="+k[ie]}).join("||"),"){",v,".vertexAttrib4f(",b,",",k,");",Dr.map(function(re,ie){return L+"."+re+"="+k[ie]+";"}).join(""),"}")}R===Nr?ue():R===mn?K():(r("if(",R,"===",Nr,"){"),ue(),r("}else{"),K(),r("}"))}h.forEach(function(i){var s=i.name,n=c.attributes[s],v;if(n){if(!_(n))return;v=n.append(a,r)}else{if(!_(Mi))return;var b=a.scopeAttrib(s);f.optional(function(){a.assert(r,b+".state","missing attribute "+s)}),v={},Object.keys(new p).forEach(function(L){v[L]=r.def(b,".",L)})}m(a.link(i),l(i.info.type),v)})}function _e(a,r,c,h,_){for(var d=a.shared,l=d.gl,m,i=0;i<h.length;++i){var s=h[i],n=s.name,v=s.info.type,b=c.uniforms[n],L=a.link(s),R=L+".location",g;if(b){if(!_(b))continue;if(fr(b)){var k=b.value;if(f.command(k!==null&&typeof k!="undefined",'missing uniform "'+n+'"',a.commandStr),v===et||v===rt){f.command(typeof k=="function"&&(v===et&&(k._reglType==="texture2d"||k._reglType==="framebuffer")||v===rt&&(k._reglType==="textureCube"||k._reglType==="framebufferCube")),"invalid texture for uniform "+n,a.commandStr);var Z=a.link(k._texture||k.color[0]._texture);r(l,".uniform1i(",R,",",Z+".bind());"),r.exit(Z,".unbind();")}else if(v===Kr||v===Zr||v===Jr){f.optional(function(){f.command(Te(k),"invalid matrix for uniform "+n,a.commandStr),f.command(v===Kr&&k.length===4||v===Zr&&k.length===9||v===Jr&&k.length===16,"invalid length for matrix uniform "+n,a.commandStr)});var ue=a.global.def("new Float32Array(["+Array.prototype.slice.call(k)+"])"),K=2;v===Zr?K=3:v===Jr&&(K=4),r(l,".uniformMatrix",K,"fv(",R,",false,",ue,");")}else{switch(v){case On:f.commandType(k,"number","uniform "+n,a.commandStr),m="1f";break;case gt:f.command(Te(k)&&k.length===2,"uniform "+n,a.commandStr),m="2f";break;case Lt:f.command(Te(k)&&k.length===3,"uniform "+n,a.commandStr),m="3f";break;case wt:f.command(Te(k)&&k.length===4,"uniform "+n,a.commandStr),m="4f";break;case Cn:f.commandType(k,"boolean","uniform "+n,a.commandStr),m="1i";break;case Gn:f.commandType(k,"number","uniform "+n,a.commandStr),m="1i";break;case Ct:f.command(Te(k)&&k.length===2,"uniform "+n,a.commandStr),m="2i";break;case Rt:f.command(Te(k)&&k.length===2,"uniform "+n,a.commandStr),m="2i";break;case Ft:f.command(Te(k)&&k.length===3,"uniform "+n,a.commandStr),m="3i";break;case Ot:f.command(Te(k)&&k.length===3,"uniform "+n,a.commandStr),m="3i";break;case Dt:f.command(Te(k)&&k.length===4,"uniform "+n,a.commandStr),m="4i";break;case Gt:f.command(Te(k)&&k.length===4,"uniform "+n,a.commandStr),m="4i";break}r(l,".uniform",m,"(",R,",",Te(k)?Array.prototype.slice.call(k):k,");")}continue}else g=b.append(a,r)}else{if(!_(Mi))continue;g=r.def(d.uniforms,"[",t.id(n),"]")}v===et?(f(!Array.isArray(g),"must specify a scalar prop for textures"),r("if(",g,"&&",g,'._reglType==="framebuffer"){',g,"=",g,".color[0];","}")):v===rt&&(f(!Array.isArray(g),"must specify a scalar prop for cube maps"),r("if(",g,"&&",g,'._reglType==="framebufferCube"){',g,"=",g,".color[0];","}")),f.optional(function(){function Me(Qe,$i){a.assert(r,Qe,'bad data or missing for uniform "'+n+'".  '+$i)}function Nn(Qe){f(!Array.isArray(g),"must not specify an array type for uniform"),Me("typeof "+g+'==="'+Qe+'"',"invalid type, expected "+Qe)}function $e(Qe,$i){Array.isArray(g)?f(g.length===Qe,"must have length "+Qe):Me(d.isArrayLike+"("+g+")&&"+g+".length==="+Qe,"invalid vector, should have length "+Qe,a.commandStr)}function Xi(Qe){f(!Array.isArray(g),"must not specify a value type"),Me("typeof "+g+'==="function"&&'+g+'._reglType==="texture'+(Qe===Oi?"2d":"Cube")+'"',"invalid texture type",a.commandStr)}switch(v){case Gn:Nn("number");break;case Rt:$e(2,"number");break;case Ot:$e(3,"number");break;case Gt:$e(4,"number");break;case On:Nn("number");break;case gt:$e(2,"number");break;case Lt:$e(3,"number");break;case wt:$e(4,"number");break;case Cn:Nn("boolean");break;case Ct:$e(2,"boolean");break;case Ft:$e(3,"boolean");break;case Dt:$e(4,"boolean");break;case Kr:$e(4,"number");break;case Zr:$e(9,"number");break;case Jr:$e(16,"number");break;case et:Xi(Oi);break;case rt:Xi(ds);break}});var re=1;switch(v){case et:case rt:var ie=r.def(g,"._texture");r(l,".uniform1i(",R,",",ie,".bind());"),r.exit(ie,".unbind();");continue;case Gn:case Cn:m="1i";break;case Rt:case Ct:m="2i",re=2;break;case Ot:case Ft:m="3i",re=3;break;case Gt:case Dt:m="4i",re=4;break;case On:m="1f";break;case gt:m="2f",re=2;break;case Lt:m="3f",re=3;break;case wt:m="4f",re=4;break;case Kr:m="Matrix2fv";break;case Zr:m="Matrix3fv";break;case Jr:m="Matrix4fv";break}if(r(l,".uniform",m,"(",R,","),m.charAt(0)==="M"){var pe=Math.pow(v-Kr+2,2),Ae=a.global.def("new Float32Array(",pe,")");Array.isArray(g)?r("false,(",Ue(pe,function(Me){return Ae+"["+Me+"]="+g[Me]}),",",Ae,")"):r("false,(Array.isArray(",g,")||",g," instanceof Float32Array)?",g,":(",Ue(pe,function(Me){return Ae+"["+Me+"]="+g+"["+Me+"]"}),",",Ae,")")}else re>1?r(Ue(re,function(Me){return Array.isArray(g)?g[Me]:g+"["+Me+"]"})):(f(!Array.isArray(g),"uniform value must not be an array"),r(g));r(");")}}function ee(a,r,c,h){var _=a.shared,d=_.gl,l=_.draw,m=h.draw;function i(){var ie=m.elements,pe,Ae=r;return ie?((ie.contextDep&&h.contextDynamic||ie.propDep)&&(Ae=c),pe=ie.append(a,Ae)):pe=Ae.def(l,".",_r),pe&&Ae("if("+pe+")"+d+".bindBuffer("+ss+","+pe+".buffer.buffer);"),pe}function s(){var ie=m.count,pe,Ae=r;return ie?((ie.contextDep&&h.contextDynamic||ie.propDep)&&(Ae=c),pe=ie.append(a,Ae),f.optional(function(){ie.MISSING&&a.assert(r,"false","missing vertex count"),ie.DYNAMIC&&a.assert(Ae,pe+">=0","missing vertex count")})):(pe=Ae.def(l,".",yr),f.optional(function(){a.assert(Ae,pe+">=0","missing vertex count")})),pe}var n=i();function v(ie){var pe=m[ie];return pe?pe.contextDep&&h.contextDynamic||pe.propDep?pe.append(a,c):pe.append(a,r):r.def(l,".",ie)}var b=v(br),L=v(xt),R=s();if(typeof R=="number"){if(R===0)return}else c("if(",R,"){"),c.exit("}");var g,k;P&&(g=v(St),k=a.instancing);var Z=n+".type",ue=m.elements&&fr(m.elements);function K(){function ie(){c(k,".drawElementsInstancedANGLE(",[b,R,Z,L+"<<(("+Z+"-"+ui+")>>1)",g],");")}function pe(){c(k,".drawArraysInstancedANGLE(",[b,L,R,g],");")}n?ue?ie():(c("if(",n,"){"),ie(),c("}else{"),pe(),c("}")):pe()}function re(){function ie(){c(d+".drawElements("+[b,R,Z,L+"<<(("+Z+"-"+ui+")>>1)"]+");")}function pe(){c(d+".drawArrays("+[b,L,R]+");")}n?ue?ie():(c("if(",n,"){"),ie(),c("}else{"),pe(),c("}")):pe()}P&&(typeof g!="number"||g>=0)?typeof g=="string"?(c("if(",g,">0){"),K(),c("}else if(",g,"<0){"),re(),c("}")):K():re()}function de(a,r,c,h,_){var d=te(),l=d.proc("body",_);return f.optional(function(){d.commandStr=r.commandStr,d.command=d.link(r.commandStr)}),P&&(d.instancing=l.def(d.shared.extensions,".angle_instanced_arrays")),a(d,l,c,h),d.compile().body}function ve(a,r,c,h){xe(a,r),c.useVAO?c.drawVAO?r(a.shared.vao,".setVAO(",c.drawVAO.append(a,r),");"):r(a.shared.vao,".setVAO(",a.shared.vao,".targetVAO);"):(r(a.shared.vao,".setVAO(null);"),ke(a,r,c,h.attributes,function(){return!0})),_e(a,r,c,h.uniforms,function(){return!0}),ee(a,r,r,c)}function Se(a,r){var c=a.proc("draw",1);xe(a,c),Ge(a,c,r.context),Ce(a,c,r.framebuffer),Ne(a,c,r),Ie(a,c,r.state),oe(a,c,r,!1,!0);var h=r.shader.progVar.append(a,c);if(c(a.shared.gl,".useProgram(",h,".program);"),r.shader.program)ve(a,c,r,r.shader.program);else{c(a.shared.vao,".setVAO(null);");var _=a.global.def("{}"),d=c.def(h,".id"),l=c.def(_,"[",d,"]");c(a.cond(l).then(l,".call(this,a0);").else(l,"=",_,"[",d,"]=",a.link(function(m){return de(ve,a,r,m,1)}),"(",h,");",l,".call(this,a0);"))}Object.keys(r.state).length>0&&c(a.shared.current,".dirty=true;")}function Ze(a,r,c,h){a.batchId="a1",xe(a,r);function _(){return!0}ke(a,r,c,h.attributes,_),_e(a,r,c,h.uniforms,_),ee(a,r,r,c)}function Ar(a,r,c,h){xe(a,r);var _=c.contextDep,d=r.def(),l="a0",m="a1",i=r.def();a.shared.props=i,a.batchId=d;var s=a.scope(),n=a.scope();r(s.entry,"for(",d,"=0;",d,"<",m,";++",d,"){",i,"=",l,"[",d,"];",n,"}",s.exit);function v(Z){return Z.contextDep&&_||Z.propDep}function b(Z){return!v(Z)}if(c.needsContext&&Ge(a,n,c.context),c.needsFramebuffer&&Ce(a,n,c.framebuffer),Ie(a,n,c.state,v),c.profile&&v(c.profile)&&oe(a,n,c,!1,!0),h)c.useVAO?c.drawVAO?v(c.drawVAO)?n(a.shared.vao,".setVAO(",c.drawVAO.append(a,n),");"):s(a.shared.vao,".setVAO(",c.drawVAO.append(a,s),");"):s(a.shared.vao,".setVAO(",a.shared.vao,".targetVAO);"):(s(a.shared.vao,".setVAO(null);"),ke(a,s,c,h.attributes,b),ke(a,n,c,h.attributes,v)),_e(a,s,c,h.uniforms,b),_e(a,n,c,h.uniforms,v),ee(a,s,n,c);else{var L=a.global.def("{}"),R=c.shader.progVar.append(a,n),g=n.def(R,".id"),k=n.def(L,"[",g,"]");n(a.shared.gl,".useProgram(",R,".program);","if(!",k,"){",k,"=",L,"[",g,"]=",a.link(function(Z){return de(Ze,a,c,Z,2)}),"(",R,");}",k,".call(this,a0[",d,"],",d,");")}}function o(a,r){var c=a.proc("batch",2);a.batchId="0",xe(a,c);var h=!1,_=!0;Object.keys(r.context).forEach(function(L){h=h||r.context[L].propDep}),h||(Ge(a,c,r.context),_=!1);var d=r.framebuffer,l=!1;d?(d.propDep?h=l=!0:d.contextDep&&h&&(l=!0),l||Ce(a,c,d)):Ce(a,c,null),r.state.viewport&&r.state.viewport.propDep&&(h=!0);function m(L){return L.contextDep&&h||L.propDep}Ne(a,c,r),Ie(a,c,r.state,function(L){return!m(L)}),(!r.profile||!m(r.profile))&&oe(a,c,r,!1,"a1"),r.contextDep=h,r.needsContext=_,r.needsFramebuffer=l;var i=r.shader.progVar;if(i.contextDep&&h||i.propDep)Ar(a,c,r,null);else{var s=i.append(a,c);if(c(a.shared.gl,".useProgram(",s,".program);"),r.shader.program)Ar(a,c,r,r.shader.program);else{c(a.shared.vao,".setVAO(null);");var n=a.global.def("{}"),v=c.def(s,".id"),b=c.def(n,"[",v,"]");c(a.cond(b).then(b,".call(this,a0,a1);").else(b,"=",n,"[",v,"]=",a.link(function(L){return de(Ar,a,r,L,2)}),"(",s,");",b,".call(this,a0,a1);"))}}Object.keys(r.state).length>0&&c(a.shared.current,".dirty=true;")}function T(a,r){var c=a.proc("scope",3);a.batchId="a2";var h=a.shared,_=h.current;Ge(a,c,r.context),r.framebuffer&&r.framebuffer.append(a,c),Pi(Object.keys(r.state)).forEach(function(l){var m=r.state[l],i=m.append(a,c);Te(i)?i.forEach(function(s,n){c.set(a.next[l],"["+n+"]",s)}):c.set(h.next,"."+l,i)}),oe(a,c,r,!0,!0),[_r,xt,yr,St,br].forEach(function(l){var m=r.draw[l];m&&c.set(h.draw,"."+l,""+m.append(a,c))}),Object.keys(r.uniforms).forEach(function(l){var m=r.uniforms[l].append(a,c);Array.isArray(m)&&(m="["+m.join()+"]"),c.set(h.uniforms,"["+t.id(l)+"]",m)}),Object.keys(r.attributes).forEach(function(l){var m=r.attributes[l].append(a,c),i=a.scopeAttrib(l);Object.keys(new p).forEach(function(s){c.set(i,"."+s,m[s])})}),r.scopeVAO&&c.set(h.vao,".targetVAO",r.scopeVAO.append(a,c));function d(l){var m=r.shader[l];m&&c.set(h.shader,"."+l,m.append(a,c))}d(Yr),d(Qr),Object.keys(r.state).length>0&&(c(_,".dirty=true;"),c.exit(_,".dirty=true;")),c("a1(",a.shared.context,",a0,",a.batchId,");")}function y(a){if(!(typeof a!="object"||Te(a))){for(var r=Object.keys(a),c=0;c<r.length;++c)if(je.isDynamic(a[r[c]]))return!0;return!1}}function Q(a,r,c){var h=r.static[c];if(!h||!y(h))return;var _=a.global,d=Object.keys(h),l=!1,m=!1,i=!1,s=a.global.def("{}");d.forEach(function(v){var b=h[v];if(je.isDynamic(b)){typeof b=="function"&&(b=h[v]=je.unbox(b));var L=Ve(b,null);l=l||L.thisDep,i=i||L.propDep,m=m||L.contextDep}else{switch(_(s,".",v,"="),typeof b){case"number":_(b);break;case"string":_('"',b,'"');break;case"object":Array.isArray(b)&&_("[",b.join(),"]");break;default:_(a.link(b));break}_(";")}});function n(v,b){d.forEach(function(L){var R=h[L];if(je.isDynamic(R)){var g=v.invoke(b,R);b(s,".",L,"=",g,";")}})}r.dynamic[c]=new je.DynamicVariable(At,{thisDep:l,contextDep:m,propDep:i,ref:s,append:n}),delete r.static[c]}function le(a,r,c,h,_){var d=te();d.stats=d.link(_),Object.keys(r.static).forEach(function(m){Q(d,r,m)}),us.forEach(function(m){Q(d,a,m)});var l=Re(a,r,c,h,d);return Se(d,l),T(d,l),o(d,l),ye(d.compile(),{destroy:function(){l.shader.program.destroy()}})}return{next:Y,current:G,procs:function(){var a=te(),r=a.proc("poll"),c=a.proc("refresh"),h=a.block();r(h),c(h);var _=a.shared,d=_.gl,l=_.next,m=_.current;h(m,".dirty=false;"),Ce(a,r),Ce(a,c,null,!0);var i;P&&(i=a.link(P)),u.oes_vertex_array_object&&c(a.link(u.oes_vertex_array_object),".bindVertexArrayOES(null);");for(var s=0;s<A.maxAttributes;++s){var n=c.def(_.attributes,"[",s,"]"),v=a.cond(n,".buffer");v.then(d,".enableVertexAttribArray(",s,");",d,".bindBuffer(",kr,",",n,".buffer.buffer);",d,".vertexAttribPointer(",s,",",n,".size,",n,".type,",n,".normalized,",n,".stride,",n,".offset);").else(d,".disableVertexAttribArray(",s,");",d,".vertexAttrib4f(",s,",",n,".x,",n,".y,",n,".z,",n,".w);",n,".buffer=null;"),c(v),P&&c(i,".vertexAttribDivisorANGLE(",s,",",n,".divisor);")}return c(a.shared.vao,".currentVAO=null;",a.shared.vao,".setVAO(",a.shared.vao,".targetVAO);"),Object.keys(j).forEach(function(b){var L=j[b],R=h.def(l,".",b),g=a.block();g("if(",R,"){",d,".enable(",L,")}else{",d,".disable(",L,")}",m,".",b,"=",R,";"),c(g),r("if(",R,"!==",m,".",b,"){",g,"}")}),Object.keys(C).forEach(function(b){var L=C[b],R=G[b],g,k,Z=a.block();if(Z(d,".",L,"("),Te(R)){var ue=R.length;g=a.global.def(l,".",b),k=a.global.def(m,".",b),Z(Ue(ue,function(K){return g+"["+K+"]"}),");",Ue(ue,function(K){return k+"["+K+"]="+g+"["+K+"];"}).join("")),r("if(",Ue(ue,function(K){return g+"["+K+"]!=="+k+"["+K+"]"}).join("||"),"){",Z,"}")}else g=h.def(l,".",b),k=h.def(m,".",b),Z(g,");",m,".",b,"=",g,";"),r("if(",g,"!==",k,"){",Z,"}");c(Z)}),a.compile()}(),compile:le}}function Rs(){return{vaoCount:0,bufferCount:0,elementsCount:0,framebufferCount:0,shaderCount:0,textureCount:0,cubeCount:0,renderbufferCount:0,maxTextureUnits:0}}var Os=34918,Gs=34919,Ui=35007,Cs=function(e,t){if(!t.ext_disjoint_timer_query)return null;var u=[];function A(){return u.pop()||t.ext_disjoint_timer_query.createQueryEXT()}function w(P){u.push(P)}var E=[];function x(P){var W=A();t.ext_disjoint_timer_query.beginQueryEXT(Ui,W),E.push(W),N(E.length-1,E.length,P)}function D(){t.ext_disjoint_timer_query.endQueryEXT(Ui)}function B(){this.startQueryIndex=-1,this.endQueryIndex=-1,this.sum=0,this.stats=null}var M=[];function I(){return M.pop()||new B}function V(P){M.push(P)}var X=[];function N(P,W,G){var Y=I();Y.startQueryIndex=P,Y.endQueryIndex=W,Y.sum=0,Y.stats=G,X.push(Y)}var z=[],p=[];function S(){var P,W,G=E.length;if(G!==0){p.length=Math.max(p.length,G+1),z.length=Math.max(z.length,G+1),z[0]=0,p[0]=0;var Y=0;for(P=0,W=0;W<E.length;++W){var F=E[W];t.ext_disjoint_timer_query.getQueryObjectEXT(F,Gs)?(Y+=t.ext_disjoint_timer_query.getQueryObjectEXT(F,Os),w(F)):E[P++]=F,z[W+1]=Y,p[W+1]=P}for(E.length=P,P=0,W=0;W<X.length;++W){var j=X[W],C=j.startQueryIndex,H=j.endQueryIndex;j.sum+=z[H]-z[C];var J=p[C],ne=p[H];ne===J?(j.stats.gpuTime+=j.sum/1e6,V(j)):(j.startQueryIndex=J,j.endQueryIndex=ne,X[P++]=j)}X.length=P}}return{beginQuery:x,endQuery:D,pushScopeStats:N,update:S,getNumPendingQueries:function(){return E.length},clear:function(){u.push.apply(u,E);for(var P=0;P<u.length;P++)t.ext_disjoint_timer_query.deleteQueryEXT(u[P]);E.length=0,u.length=0},restore:function(){E.length=0,u.length=0}}},Fs=16384,Ds=256,Ns=1024,Bs=34962,Vi="webglcontextlost",zi="webglcontextrestored",ji=1,Is=2,ks=3;function Hi(e,t){for(var u=0;u<e.length;++u)if(e[u]===t)return u;return-1}function Ps(e){var t=Nf(e);if(!t)return null;var u=t.gl,A=u.getContextAttributes(),w=u.isContextLost(),E=Bf(u,t);if(!E)return null;var x=Of(),D=Rs(),B=E.extensions,M=Cs(u,B),I=Zn(),V=u.drawingBufferWidth,X=u.drawingBufferHeight,N={tick:0,time:0,viewportWidth:V,viewportHeight:X,framebufferWidth:V,framebufferHeight:X,drawingBufferWidth:V,drawingBufferHeight:X,pixelRatio:t.pixelRatio},z={},p={elements:null,primitive:4,count:-1,offset:0,instances:-1},S=xo(u,B),P=Mo(u,D,t,G),W=Ku(u,B,S,D,P);function G(ee){return W.destroyBuffer(ee)}var Y=Ko(u,B,P,D),F=rs(u,x,D,t),j=Ru(u,B,S,function(){J.procs.poll()},N,D,t),C=Ou(u,B,S,D,t),H=Qu(u,B,S,j,C,D),J=ws(u,x,B,S,P,Y,j,H,z,W,F,p,N,M,t),ne=as(u,H,J.procs.poll,N,A,B,S),$=J.next,U=u.canvas,O=[],te=[],fe=[],q=[t.onDestroy],ce=null;function ae(){if(O.length===0){M&&M.update(),ce=null;return}ce=Pt.next(ae),Ie();for(var ee=O.length-1;ee>=0;--ee){var de=O[ee];de&&de(N,null,0)}u.flush(),M&&M.update()}function se(){!ce&&O.length>0&&(ce=Pt.next(ae))}function he(){ce&&(Pt.cancel(ae),ce=null)}function we(ee){ee.preventDefault(),w=!0,he(),te.forEach(function(de){de()})}function Fe(ee){u.getError(),w=!1,E.restore(),F.restore(),P.restore(),j.restore(),C.restore(),H.restore(),W.restore(),M&&M.restore(),J.procs.refresh(),se(),fe.forEach(function(de){de()})}U&&(U.addEventListener(Vi,we,!1),U.addEventListener(zi,Fe,!1));function me(){O.length=0,he(),U&&(U.removeEventListener(Vi,we),U.removeEventListener(zi,Fe)),F.clear(),H.clear(),C.clear(),j.clear(),Y.clear(),P.clear(),W.clear(),M&&M.clear(),q.forEach(function(ee){ee()})}function Oe(ee){f(!!ee,"invalid args to regl({...})"),f.type(ee,"object","invalid args to regl({...})");function de(_){var d=ye({},_);delete d.uniforms,delete d.attributes,delete d.context,delete d.vao,"stencil"in d&&d.stencil.op&&(d.stencil.opBack=d.stencil.opFront=d.stencil.op,delete d.stencil.op);function l(m){if(m in d){var i=d[m];delete d[m],Object.keys(i).forEach(function(s){d[m+"."+s]=i[s]})}}return l("blend"),l("depth"),l("cull"),l("stencil"),l("polygonOffset"),l("scissor"),l("sample"),"vao"in _&&(d.vao=_.vao),d}function ve(_,d){var l={},m={};return Object.keys(_).forEach(function(i){var s=_[i];if(je.isDynamic(s)){m[i]=je.unbox(s,i);return}else if(d&&Array.isArray(s)){for(var n=0;n<s.length;++n)if(je.isDynamic(s[n])){m[i]=je.unbox(s,i);return}}l[i]=s}),{dynamic:m,static:l}}var Se=ve(ee.context||{},!0),Ze=ve(ee.uniforms||{},!0),Ar=ve(ee.attributes||{},!1),o=ve(de(ee),!1),T={gpuTime:0,cpuTime:0,count:0},y=J.compile(o,Ar,Ze,Se,T),Q=y.draw,le=y.batch,a=y.scope,r=[];function c(_){for(;r.length<_;)r.push(null);return r}function h(_,d){var l;if(w&&f.raise("context lost"),typeof _=="function")return a.call(this,null,_,0);if(typeof d=="function")if(typeof _=="number")for(l=0;l<_;++l)a.call(this,null,d,l);else if(Array.isArray(_))for(l=0;l<_.length;++l)a.call(this,_[l],d,l);else return a.call(this,_,d,0);else if(typeof _=="number"){if(_>0)return le.call(this,c(_|0),_|0)}else if(Array.isArray(_)){if(_.length)return le.call(this,_,_.length)}else return Q.call(this,_)}return ye(h,{stats:T,destroy:function(){y.destroy()}})}var be=H.setFBO=Oe({framebuffer:je.define.call(null,ji,"framebuffer")});function Re(ee,de){var ve=0;J.procs.poll();var Se=de.color;Se&&(u.clearColor(+Se[0]||0,+Se[1]||0,+Se[2]||0,+Se[3]||0),ve|=Fs),"depth"in de&&(u.clearDepth(+de.depth),ve|=Ds),"stencil"in de&&(u.clearStencil(de.stencil|0),ve|=Ns),f(!!ve,"called regl.clear with no buffer specified"),u.clear(ve)}function Ge(ee){if(f(typeof ee=="object"&&ee,"regl.clear() takes an object as input"),"framebuffer"in ee)if(ee.framebuffer&&ee.framebuffer_reglType==="framebufferCube")for(var de=0;de<6;++de)be(ye({framebuffer:ee.framebuffer.faces[de]},ee),Re);else be(ee,Re);else Re(null,ee)}function Ce(ee){f.type(ee,"function","regl.frame() callback must be a function"),O.push(ee);function de(){var ve=Hi(O,ee);f(ve>=0,"cannot cancel a frame twice");function Se(){var Ze=Hi(O,Se);O[Ze]=O[O.length-1],O.length-=1,O.length<=0&&he()}O[ve]=Se}return se(),{cancel:de}}function Ne(){var ee=$.viewport,de=$.scissor_box;ee[0]=ee[1]=de[0]=de[1]=0,N.viewportWidth=N.framebufferWidth=N.drawingBufferWidth=ee[2]=de[2]=u.drawingBufferWidth,N.viewportHeight=N.framebufferHeight=N.drawingBufferHeight=ee[3]=de[3]=u.drawingBufferHeight}function Ie(){N.tick+=1,N.time=oe(),Ne(),J.procs.poll()}function xe(){j.refresh(),Ne(),J.procs.refresh(),M&&M.update()}function oe(){return(Zn()-I)/1e3}xe();function ke(ee,de){f.type(de,"function","listener callback must be a function");var ve;switch(ee){case"frame":return Ce(de);case"lost":ve=te;break;case"restore":ve=fe;break;case"destroy":ve=q;break;default:f.raise("invalid event, must be one of frame,lost,restore,destroy")}return ve.push(de),{cancel:function(){for(var Se=0;Se<ve.length;++Se)if(ve[Se]===de){ve[Se]=ve[ve.length-1],ve.pop();return}}}}var _e=ye(Oe,{clear:Ge,prop:je.define.bind(null,ji),context:je.define.bind(null,Is),this:je.define.bind(null,ks),draw:Oe({}),buffer:function(ee){return P.create(ee,Bs,!1,!1)},elements:function(ee){return Y.create(ee,!1)},texture:j.create2D,cube:j.createCube,renderbuffer:C.create,framebuffer:H.create,framebufferCube:H.createCube,vao:W.createVAO,attributes:A,frame:Ce,on:ke,limits:S,hasExtension:function(ee){return S.extensions.indexOf(ee.toLowerCase())>=0},read:ne,destroy:me,_gl:u,_refresh:xe,poll:function(){Ie(),M&&M.update()},now:oe,stats:D});return t.onDone(null,_e),_e}return Ps})}}]);

//# sourceMappingURL=regl-lib.1bd171e8.async.js.map