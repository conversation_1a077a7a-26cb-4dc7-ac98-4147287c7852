!(function(){var pr=Math.pow;(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[6369],{27521:function(ae,X,D){"use strict";D.d(X,{Z:function(){return d}});var m=D(1413),c=D(67294),y={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 472a40 40 0 1080 0 40 40 0 10-80 0zm367 352.9L696.3 352V178H768v-68H256v68h71.7v174L145 824.9c-2.8 7.4-4.3 15.2-4.3 23.1 0 35.3 28.7 64 64 64h614.6c7.9 0 15.7-1.5 23.1-4.3 33-12.7 49.4-49.8 36.6-82.8zM395.7 364.7V180h232.6v184.7L719.2 600c-20.7-5.3-42.1-8-63.9-8-61.2 0-119.2 21.5-165.3 60a188.78 188.78 0 01-121.3 43.9c-32.7 0-64.1-8.3-91.8-23.7l118.8-307.5zM210.5 844l41.7-107.8c35.7 18.1 75.4 27.8 116.6 27.8 61.2 0 119.2-21.5 165.3-60 33.9-28.2 76.3-43.9 121.3-43.9 35 0 68.4 9.5 97.6 27.1L813.5 844h-603z"}}]},name:"experiment",theme:"outlined"},C=y,E=D(84089),l=function(o,n){return c.createElement(E.Z,(0,m.Z)((0,m.Z)({},o),{},{ref:n,icon:C}))},x=c.forwardRef(l),d=x},68918:function(ae,X,D){"use strict";var m=D(97857),c=D.n(m),y=D(15009),C=D.n(y),E=D(99289),l=D.n(E),x=D(5574),d=D.n(x),t=D(13769),o=D.n(t),n=D(85576),i=D(67294),u=D(85893),p=["disabled","onConfirm","onCancel","afterClose","children","title","openEvent"],b=function(s){var r=s.disabled,h=s.onConfirm,g=s.onCancel,w=s.afterClose,I=s.children,A=s.title,B=s.openEvent,O=o()(s,p),T=(0,i.useState)(!1),M=d()(T,2),H=M[0],U=M[1],$=(0,i.useState)(!1),Y=d()($,2),q=Y[0],Z=Y[1];(0,i.useEffect)(function(){return U((B==null?void 0:B.open)||!1)},[B]);var ne=(0,i.useCallback)(function(){var pe=l()(C()().mark(function me(we){var Ie;return C()().wrap(function(Ee){for(;;)switch(Ee.prev=Ee.next){case 0:return Ie=we?h:g,Z(!0),Ee.prev=2,Ee.next=5,Ie==null?void 0:Ie();case 5:Z(!1),U(!1),w==null||w(),Ee.next=14;break;case 10:throw Ee.prev=10,Ee.t0=Ee.catch(2),Z(!1),Ee.t0;case 14:case"end":return Ee.stop()}},me,null,[[2,10]])}));return function(me){return pe.apply(this,arguments)}}(),[w,g,h]);return(0,u.jsx)(n.Z,c()(c()({},O),{},{title:A,open:H,confirmLoading:q,onCancel:r?void 0:function(){return ne(!1)},onOk:r?void 0:function(){return ne(!0)},children:I}))};X.Z=b},42689:function(ae,X,D){"use strict";D.d(X,{I:function(){return o}});var m=D(97857),c=D.n(m),y=D(15009),C=D.n(y),E=D(99289),l=D.n(E),x=D(5574),d=D.n(x),t=D(67294),o=function(i){var u=(0,t.useState)({}),p=d()(u,2),b=p[0],v=p[1],s=function(){var r=l()(C()().mark(function h(){var g;return C()().wrap(function(I){for(;;)switch(I.prev=I.next){case 0:return g=new Promise(function(A,B){v({openEvent:{open:!0},onConfirm:function(){var O=l()(C()().mark(function M(){return C()().wrap(function(U){for(;;)switch(U.prev=U.next){case 0:return U.abrupt("return",A(""));case 1:case"end":return U.stop()}},M)}));function T(){return O.apply(this,arguments)}return T}(),onCancel:function(){var O=l()(C()().mark(function M(){return C()().wrap(function(U){for(;;)switch(U.prev=U.next){case 0:return U.abrupt("return",B());case 1:case"end":return U.stop()}},M)}));function T(){return O.apply(this,arguments)}return T}()})}),I.abrupt("return",g);case 2:case"end":return I.stop()}},h)}));return function(){return r.apply(this,arguments)}}();return{dialogProps:c()(c()({},i),b),confirm:s}}},99814:function(ae,X,D){"use strict";D.r(X),D.d(X,{RdKitMoleculeStructure:function(){return yt},RemoteMoleculeStructure:function(){return lt},SmilesDrawerMoleculeStructure:function(){return Lt.ZP},default:function(){return mt}});var m=D(97857),c=D.n(m),y=D(51562),C=D(80014),E=D(68918),l=D(42689),x=D(71181),d=D(27521),t=D(48054),o=D(67294),n=D(19632),i=D.n(n),u=D(15009),p=D.n(u),b=D(99289),v=D.n(b),s=D(5574),r=D.n(s),h=D(11005),g=D(11499),w=D(9669),I=D.n(w),A=Object.defineProperty,B=Object.getOwnPropertySymbols,O=Object.prototype.hasOwnProperty,T=Object.prototype.propertyIsEnumerable,M=(ve,ee,le)=>ee in ve?A(ve,ee,{enumerable:!0,configurable:!0,writable:!0,value:le}):ve[ee]=le,H=(ve,ee)=>{for(var le in ee||(ee={}))O.call(ee,le)&&M(ve,le,ee[le]);if(B)for(var le of B(ee))T.call(ee,le)&&M(ve,le,ee[le]);return ve};const U=ve=>o.createElement("svg",H({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},ve),o.createElement("path",{d:"M20.48 12.5h-17c-.28 0-.5-.22-.5-.5s.22-.5.5-.5h17c.28 0 .5.22.5.5s-.22.5-.5.5Z"}),o.createElement("path",{d:"M12 21c-.28 0-.5-.22-.5-.5v-17c0-.28.22-.5.5-.5s.5.22.5.5v17c0 .28-.22.5-.5.5Z"}));var $="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTIwLjQ4IDEyLjVoLTE3Yy0uMjggMC0uNS0uMjItLjUtLjVzLjIyLS41LjUtLjVoMTdjLjI4IDAgLjUuMjIuNS41cy0uMjIuNS0uNS41WiIvPjxwYXRoIGQ9Ik0xMiAyMWMtLjI4IDAtLjUtLjIyLS41LS41di0xN2MwLS4yOC4yMi0uNS41LS41cy41LjIyLjUuNXYxN2MwIC4yOC0uMjIuNS0uNS41WiIvPjwvc3ZnPg==",Y=Object.defineProperty,q=Object.getOwnPropertySymbols,Z=Object.prototype.hasOwnProperty,ne=Object.prototype.propertyIsEnumerable,pe=(ve,ee,le)=>ee in ve?Y(ve,ee,{enumerable:!0,configurable:!0,writable:!0,value:le}):ve[ee]=le,me=(ve,ee)=>{for(var le in ee||(ee={}))Z.call(ee,le)&&pe(ve,le,ee[le]);if(q)for(var le of q(ee))ne.call(ee,le)&&pe(ve,le,ee[le]);return ve};const we=ve=>o.createElement("svg",me({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},ve),o.createElement("path",{d:"M18.7 15.33a.47.47 0 0 1-.35-.15c-.2-.2-.2-.51 0-.71l1.98-1.98H2.47c-.28 0-.5-.22-.5-.5s.22-.5.5-.5h17.86l-1.98-1.98c-.2-.2-.2-.51 0-.71s.51-.2.71 0l2.83 2.83c.09.09.15.22.15.35s-.05.26-.15.35l-2.83 2.83c-.1.1-.23.15-.35.15Z"}));var Ie="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZD0iTTE4LjcgMTUuMzNhLjQ3LjQ3IDAgMCAxLS4zNS0uMTVjLS4yLS4yLS4yLS41MSAwLS43MWwxLjk4LTEuOThIMi40N2MtLjI4IDAtLjUtLjIyLS41LS41cy4yMi0uNS41LS41aDE3Ljg2bC0xLjk4LTEuOThjLS4yLS4yLS4yLS41MSAwLS43MXMuNTEtLjIuNzEgMGwyLjgzIDIuODNjLjA5LjA5LjE1LjIyLjE1LjM1cy0uMDUuMjYtLjE1LjM1bC0yLjgzIDIuODNjLS4xLjEtLjIzLjE1LS4zNS4xNVoiLz48L3N2Zz4=",Le=D(32884),Ee=D(35607),_e=D(93967),Re=D.n(_e),Ae=D(87172),De=D(73445),ye=D(782),Ht=function(){var ve=v()(p()().mark(function ee(){var le,de,ke,oe;return p()().wrap(function(Oe){for(;;)switch(Oe.prev=Oe.next){case 0:return Oe.next=2,(0,Ae.query)("material-display-by-names").paginate(1,1e4).get();case 2:return le=Oe.sent,de=le.data,ke=de!=null&&de.length?de:[],oe=ke.reduce(function(Ve,Ze){return Ve[Ze.inchi_smiles]=Ze.display_name,Ve},{}),Oe.abrupt("return",oe);case 7:case"end":return Oe.stop()}},ee)}));return function(){return ve.apply(this,arguments)}}(),kt={},Ft=(0,De.Ue)((0,ye.XR)((0,ye.$e)(c()({},kt),function(ve,ee){return{getMap:function(){var le=v()(p()().mark(function ke(){var oe,Ye,Oe,Ve;return p()().wrap(function(et){for(;;)switch(et.prev=et.next){case 0:if(oe=ee().inchiSmilesToName,oe!==void 0){et.next=9;break}if(Ye=ee().fetching,Ye){et.next=8;break}return Oe=function(){var Fe=v()(p()().mark(function tt(){var Je;return p()().wrap(function(ze){for(;;)switch(ze.prev=ze.next){case 0:return ze.next=2,Ht();case 2:return Je=ze.sent,ve(function(rt){return c()(c()({},rt),{},{inchiSmilesToName:Je,fetching:void 0})}),ze.abrupt("return",Je);case 5:case"end":return ze.stop()}},tt)}));return function(){return Fe.apply(this,arguments)}}(),Ve=Oe(),ve(function(Fe){return c()(c()({},Fe),{},{fetching:Ve})}),et.abrupt("return",Ve);case 8:return et.abrupt("return",Ye);case 9:return et.abrupt("return",oe);case 10:case"end":return et.stop()}},ke)}));function de(){return le.apply(this,arguments)}return de}()}}))),xe=D(85893),Pt=function(ee){var le=arguments.length>1&&arguments[1]!==void 0?arguments[1]:16,de=le*ee.length*.7,ke=le*2,oe=`
    <svg xmlns="http://www.w3.org/2000/svg" width="`.concat(de,'" height="').concat(ke,`">
      <text x="50%" y="`).concat(le,'" font-family="Arial" font-size="').concat(le,`px" fill="black" text-anchor="middle" dominant-baseline="middle">
        `).concat(ee,`
      </text>
    </svg>`),Ye=encodeURIComponent(oe).replace(/'/g,"%27").replace(/"/g,"%22");return"data:image/svg+xml;charset=utf-8,"+Ye},ue=function(ee){var le=ee.srcs,de=ee.inchiSmiles,ke=ee.height,oe=ee.width,Ye=ee.maxHeight,Oe=ee.maxWidth,Ve=ee.fitContentHeight,Ze=Ve===void 0?le.length>1:Ve,et=Ft(function(ze){return[ze.getMap,ze.inchiSmilesToName]}),Fe=r()(et,2),tt=Fe[0],Je=Fe[1];if((0,o.useEffect)(function(){tt()}),le.length===1){var ut=Je==null?void 0:Je[de[0]];return(0,xe.jsx)("div",{className:Re()("images-wrapper",{"fit-content-height":Ze}),children:(0,xe.jsx)(g.Z,{src:ut!=null&&ut.length?Pt(ut):le[0],placeholder:!0,preview:!1,wrapperClassName:"img",height:ke,width:oe,style:{maxHeight:Ye,maxWidth:Oe},fallback:"data:image/png;base64,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"})})}return(0,xe.jsx)("div",{className:Re()("images-wrapper",{"fit-content-height":Ze}),children:le.map(function(ze,rt){var it=Je==null?void 0:Je[de[rt]];return(0,xe.jsxs)(xe.Fragment,{children:[(0,xe.jsx)(g.Z,{src:it?Pt(it):ze,placeholder:!0,preview:!1,wrapperClassName:"img"},"".concat(ze,"-").concat(rt)),rt===le.length-2?(0,xe.jsx)("div",{className:"icon",children:(0,xe.jsx)(we,{})},rt):rt<le.length-2?(0,xe.jsx)("div",{className:"icon",children:(0,xe.jsx)(U,{})},rt):null]})})})},Xe=ue,qe=function(){var ve=v()(p()().mark(function ee(le){var de;return p()().wrap(function(oe){for(;;)switch(oe.prev=oe.next){case 0:return oe.next=2,I().get("".concat((0,Le.SP)().apiBase,"/api/smiles/to-image/svg"),{params:{smiles:le}});case 2:if(oe.t3=de=oe.sent,oe.t2=oe.t3===null,oe.t2){oe.next=6;break}oe.t2=de===void 0;case 6:if(oe.t1=oe.t2,oe.t1){oe.next=9;break}oe.t1=(de=de.data)===null||de===void 0;case 9:if(!oe.t1){oe.next=13;break}oe.t4=void 0,oe.next=14;break;case 13:oe.t4=de.filename;case 14:if(oe.t0=oe.t4,oe.t0){oe.next=17;break}oe.t0="";case 17:return oe.abrupt("return",oe.t0);case 18:case"end":return oe.stop()}},ee)}));return function(le){return ve.apply(this,arguments)}}(),Ke=function(ee){if(ee.includes(">")){var le=(0,h.p6)(ee),de=le.reactants,ke=le.product;return[].concat(i()(de),[ke])}return[ee]},At=function(){var ve=v()(p()().mark(function ee(le){var de,ke,oe,Ye,Oe,Ve,Ze;return p()().wrap(function(Fe){for(;;)switch(Fe.prev=Fe.next){case 0:return de=Ke(le),ke=Promise.all(de.map(function(){var tt=v()(p()().mark(function Je(ut){var ze;return p()().wrap(function(it){for(;;)switch(it.prev=it.next){case 0:return it.next=2,qe(ut).catch(function(){return""});case 2:if(ze=it.sent,!ze){it.next=5;break}return it.abrupt("return","".concat((0,Le.SP)().apiBase,"/api/smiles/image-file/").concat(ze));case 5:return it.abrupt("return","");case 6:case"end":return it.stop()}},Je)}));return function(Je){return tt.apply(this,arguments)}}())),oe=(0,Ee.d)(de),Fe.next=5,Promise.all([ke,oe]);case 5:return Ye=Fe.sent,Oe=r()(Ye,2),Ve=Oe[0],Ze=Oe[1],Fe.abrupt("return",{srcs:Ve,inchiSmiles:Ze});case 10:case"end":return Fe.stop()}},ee)}));return function(le){return ve.apply(this,arguments)}}(),We=function(ee){var le=(0,o.useState)([]),de=r()(le,2),ke=de[0],oe=de[1],Ye=(0,o.useState)([]),Oe=r()(Ye,2),Ve=Oe[0],Ze=Oe[1],et=(0,o.useState)(!1),Fe=r()(et,2),tt=Fe[0],Je=Fe[1],ut=(0,o.useState)(!1),ze=r()(ut,2),rt=ze[0],it=ze[1],Nt=(0,o.useRef)(0);return(0,o.useEffect)(function(){if(ee){Je(!0),Nt.current=Nt.current+1;var St=Nt.current;At(ee).then(function(Dt){var vt=Dt.srcs,Et=Dt.inchiSmiles;Nt.current===St&&(oe(vt),it(vt.every(function(Rt){return!Rt})),Ze(Et))}).finally(function(){return Je(!1)})}},[ee]),{srcs:ke,inchiSmiles:Ve,loading:tt,invalid:rt}},ft=function ve(ee){var le=ee.structure,de=le===void 0?"":le,ke=ee.clickEvent,oe=ee.copyBtn,Ye=oe===void 0?!0:oe,Oe=ee.expandBtn,Ve=Oe===void 0?!0:Oe,Ze=ee.fitContentHeight,et=ee.className,Fe=ee.height,tt=ee.width,Je=ee.maxHeight,ut=ee.maxWidth,ze=(0,o.useRef)(null),rt=(0,l.I)(),it=rt.dialogProps,Nt=rt.confirm,St=(0,x.m)(),Dt=St.copy,vt=We(de),Et=vt.srcs,Rt=vt.inchiSmiles,R=vt.loading,$e=vt.invalid;return(0,xe.jsxs)(xe.Fragment,{children:[(0,xe.jsxs)("div",{title:de,className:"molecule-structure-root ".concat(et),onClick:ke,ref:ze,children:[(0,xe.jsxs)("div",{onClick:function(nt){return nt.stopPropagation()},className:"buttons-wrapper",children:[Ve&&!$e&&(0,xe.jsx)("div",{onClick:function(){return Nt()},className:"expand-button-wrapper button-wrapper",children:(0,xe.jsx)(C.r,{})}),Ye&&(0,xe.jsx)("div",{onClick:function(){return Dt(de||"")},className:"copy-button-wrapper button-wrapper",children:(0,xe.jsx)(y.r,{})})]}),$e&&(0,xe.jsx)("span",{title:"Cannot render structure: ".concat(de),children:"Render Error."}),R||!Et.length?(0,xe.jsx)(t.Z.Node,{active:!0,rootClassName:"skeleton-wrapper",children:(0,xe.jsx)(d.Z,{style:{fontSize:40,color:"#bfbfbf"}})}):(0,xe.jsx)(Xe,{srcs:Et,inchiSmiles:Rt,width:tt,height:Fe,maxHeight:Je,maxWidth:ut,fitContentHeight:Ze})]}),(0,xe.jsx)("div",{onClick:function(nt){return nt.stopPropagation()},children:(0,xe.jsx)(E.Z,c()(c()({},it),{},{footer:null,cancelButtonProps:{hidden:!0},width:de!=null&&de.includes(">")?"80%":800,centered:!0,children:(0,xe.jsx)(ve,{structure:de,copyBtn:!1,expandBtn:!1})}))})]})},lt=ft,It=D(96486),xt=D.n(It),pt=function(ee){var le=ee.structure,de=ee.className,ke=ee.width,oe=ke===void 0?250:ke,Ye=ee.height,Oe=Ye===void 0?200:Ye,Ve=ee.subStructure,Ze=ee.extraDetails,et=(0,o.useState)(),Fe=r()(et,2),tt=Fe[0],Je=Fe[1],ut=(0,o.useState)(!1),ze=r()(ut,2),rt=ze[0],it=ze[1],Nt=(0,o.useState)(!1),St=r()(Nt,2),Dt=St[0],vt=St[1],Et=(0,o.useState)(""),Rt=r()(Et,2),R=Rt[0],$e=Rt[1],Ct=(0,o.useState)(c()({width:oe,height:Oe,bondLineWidth:1,addStereoAnnotation:!0},Ze)),nt=r()(Ct,2),jt=nt[0],ge=nt[1],Ut=(0,o.useCallback)(function(){if(tt){var st=tt.get_mol(le||"invalid"),bt=tt.get_qmol(Ve||"invalid");if(st!=null&&st.is_valid()){var Ot=st.get_svg_with_highlights(hr(st,bt));$e(Ot),vt(!1)}else vt(!0);st==null||st.delete(),bt==null||bt.delete(),console.debug("draw")}},[tt]);(0,o.useEffect)(function(){(0,x.F)().then(function(st){Je(st);try{Ut()}catch(bt){console.log(bt)}}).catch(function(st){console.error(st),it(!0)})},[Ut]);var hr=(0,o.useCallback)(function(st,bt){if(st!=null&&st.is_valid()&&(bt!=null&&bt.is_valid())){var Ot=JSON.parse(st.get_substruct_matches(bt)),be=xt().isEmpty(Ot)?Ot:Ot.reduce(function(ot,sr){var Xt=sr.atoms,zt=sr.bonds;return{atoms:[].concat(i()(ot.atoms),i()(Xt)),bonds:[].concat(i()(ot.bonds),i()(zt))}},{bonds:[],atoms:[]});return JSON.stringify(c()(c()({},jt),be))}return JSON.stringify(jt)},[jt]);return tt?rt?(0,xe.jsx)(xe.Fragment,{children:"Error loading renderer"}):Dt?(0,xe.jsx)("span",{title:"Cannot render structure: ".concat(le),children:"Render Error."}):(0,xe.jsx)("div",{title:le,className:de,style:{width:oe,height:Oe},dangerouslySetInnerHTML:{__html:R}}):(0,xe.jsx)(xe.Fragment,{children:"Loading renderer..."})},yt=pt,Lt=D(17706),mt=lt},17706:function(ae,X,D){"use strict";D.d(X,{ZP:function(){return h},i:function(){return v}});var m=D(97857),c=D.n(m),y=D(5574),C=D.n(y),E=D(51562),l=D(80014),x=D(67294),d=D(44261),t=D.n(d),o=D(68918),n=D(42689),i=D(71181),u=D(85893),p={scale:0,bondThickness:1,shortBondLength:.8,bondSpacing:5,atomVisualization:"default",isomeric:!0,debug:!1,terminalCarbons:!1,explicitHydrogens:!0,overlapSensitivity:.42,overlapResolutionIterations:1,compactDrawing:!1,fontFamily:"Arial, Helvetica, sans-serif",fontSizeLarge:11,fontSizeSmall:3,padding:2,experimentalSSSR:!0,kkThreshold:.5,kkInnerThreshold:.5,kkMaxIteration:1e4,kkMaxInnerIteration:1,kkMaxEnergy:1e9,weights:{colormap:null,additionalPadding:10,sigma:10,interval:.1,opacity:1},themes:{dark:{C:"#fff",O:"#e74c3c",N:"#3498db",F:"#27ae60",CL:"#16a085",BR:"#d35400",I:"#8e44ad",P:"#d35400",S:"#f1c40f",B:"#e67e22",SI:"#e67e22",H:"#aaa",BACKGROUND:"#141414"},light:{C:"#222",O:"#e74c3c",N:"#3498db",F:"#27ae60",CL:"#16a085",BR:"#d35400",I:"#8e44ad",P:"#d35400",S:"#f1c40f",B:"#e67e22",SI:"#e67e22",H:"#666",BACKGROUND:"#fff"},oldschool:{C:"#000",O:"#000",N:"#000",F:"#000",CL:"#000",BR:"#000",I:"#000",P:"#000",S:"#000",B:"#000",SI:"#000",H:"#000",BACKGROUND:"#fff"},solarized:{C:"#586e75",O:"#dc322f",N:"#268bd2",F:"#859900",CL:"#16a085",BR:"#cb4b16",I:"#6c71c4",P:"#d33682",S:"#b58900",B:"#2aa198",SI:"#2aa198",H:"#657b83",BACKGROUND:"#fff"},"solarized-dark":{C:"#93a1a1",O:"#dc322f",N:"#268bd2",F:"#859900",CL:"#16a085",BR:"#cb4b16",I:"#6c71c4",P:"#d33682",S:"#b58900",B:"#2aa198",SI:"#2aa198",H:"#839496",BACKGROUND:"#fff"},matrix:{C:"#678c61",O:"#2fc079",N:"#4f7e7e",F:"#90d762",CL:"#82d967",BR:"#23755a",I:"#409931",P:"#c1ff8a",S:"#faff00",B:"#50b45a",SI:"#409931",H:"#426644",BACKGROUND:"#fff"},github:{C:"#24292f",O:"#cf222e",N:"#0969da",F:"#2da44e",CL:"#6fdd8b",BR:"#bc4c00",I:"#8250df",P:"#bf3989",S:"#d4a72c",B:"#fb8f44",SI:"#bc4c00",H:"#57606a",BACKGROUND:"#fff"},carbon:{C:"#161616",O:"#da1e28",N:"#0f62fe",F:"#198038",CL:"#007d79",BR:"#fa4d56",I:"#8a3ffc",P:"#ff832b",S:"#f1c21b",B:"#8a3800",SI:"#e67e22",H:"#525252",BACKGROUND:"#fff"},cyberpunk:{C:"#ea00d9",O:"#ff3131",N:"#0abdc6",F:"#00ff9f",CL:"#00fe00",BR:"#fe9f20",I:"#ff00ff",P:"#fe7f00",S:"#fcee0c",B:"#ff00ff",SI:"#ffffff",H:"#913cb1",BACKGROUND:"#fff"},gruvbox:{C:"#665c54",O:"#cc241d",N:"#458588",F:"#98971a",CL:"#79740e",BR:"#d65d0e",I:"#b16286",P:"#af3a03",S:"#d79921",B:"#689d6a",SI:"#427b58",H:"#7c6f64",BACKGROUND:"#fbf1c7"},"gruvbox-dark":{C:"#ebdbb2",O:"#cc241d",N:"#458588",F:"#98971a",CL:"#b8bb26",BR:"#d65d0e",I:"#b16286",P:"#fe8019",S:"#d79921",B:"#8ec07c",SI:"#83a598",H:"#bdae93",BACKGROUND:"#282828"},custom:{C:"#222",O:"#e74c3c",N:"#3498db",F:"#27ae60",CL:"#16a085",BR:"#d35400",I:"#8e44ad",P:"#d35400",S:"#f1c40f",B:"#e67e22",SI:"#e67e22",H:"#666",BACKGROUND:"#fff"}}},b={fontSize:9,fontFamily:"Arial, Helvetica, sans-serif",spacing:10,plus:{size:9,thickness:1},arrow:{length:120,headSize:6,thickness:1,margin:3}},v=new(t()).SmiDrawer(p,b),s=function(w){return w.includes("~")&&console.warn("smiles including unsupported char ~: ".concat(w)),w.includes(" ")&&console.warn("smiles including unsupported char ' ': ".concat(w)),w.replace(/~/g,"").split(" ")[0]},r=function g(w){var I=w.structure,A=I===void 0?"":I,B=w.clickEvent,O=w.copyBtn,T=O===void 0?!0:O,M=w.expandBtn,H=M===void 0?!0:M,U=w.className,$=(0,x.useState)(!1),Y=C()($,2),q=Y[0],Z=Y[1],ne=(0,x.useRef)(null),pe=(0,x.useRef)(null),me=(0,n.I)(),we=me.dialogProps,Ie=me.confirm,Le=(0,i.m)(),Ee=Le.copy;return(0,x.useEffect)(function(){if(pe.current){var _e=A!=null&&A.includes(">")?t().parseReaction:t().parse,Re=s(A);_e(Re,function(){Z(!1),ne.current&&v.draw(Re,ne.current)},function(Ae){console.error(Ae),Z(!0)})}},[A,pe]),(0,u.jsxs)(u.Fragment,{children:[(0,u.jsxs)("div",{title:A,className:"molecule-structure-root ".concat(U),onClick:B,ref:pe,children:[(0,u.jsxs)("div",{onClick:function(Re){return Re.stopPropagation()},className:"buttons-wrapper",children:[H&&!q&&(0,u.jsx)("div",{onClick:function(){return Ie()},className:"expand-button-wrapper button-wrapper",children:(0,u.jsx)(l.r,{})}),T&&(0,u.jsx)("div",{onClick:function(){return Ee(A||"")},className:"copy-button-wrapper button-wrapper",children:(0,u.jsx)(E.r,{})})]}),q&&(0,u.jsx)("span",{title:"Cannot render structure: ".concat(A),children:"Render Error."}),(0,u.jsx)("svg",{ref:ne,display:q?"none":"block",className:"svg"})]}),(0,u.jsx)("div",{onClick:function(Re){return Re.stopPropagation()},children:(0,u.jsx)(o.Z,c()(c()({},we),{},{footer:null,cancelButtonProps:{hidden:!0},width:A!=null&&A.includes(">")?"80%":800,centered:!0,children:(0,u.jsx)(g,{structure:A,copyBtn:!1,expandBtn:!1})}))})]})},h=r},51562:function(ae,X,D){"use strict";D.d(X,{r:function(){return d}});var m=D(67294),c=Object.defineProperty,y=Object.getOwnPropertySymbols,C=Object.prototype.hasOwnProperty,E=Object.prototype.propertyIsEnumerable,l=(o,n,i)=>n in o?c(o,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):o[n]=i,x=(o,n)=>{for(var i in n||(n={}))C.call(n,i)&&l(o,i,n[i]);if(y)for(var i of y(n))E.call(n,i)&&l(o,i,n[i]);return o};const d=o=>m.createElement("svg",x({viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o),m.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6 14h8V6H6v8Zm-4-4V2h8v3H5.5a.5.5 0 0 0-.5.5V10H2Zm12.5-5H11V1.5a.5.5 0 0 0-.5-.5h-9a.5.5 0 0 0-.5.5v9a.5.5 0 0 0 .5.5H5v3.5a.5.5 0 0 0 .5.5h9a.5.5 0 0 0 .5-.5v-9a.5.5 0 0 0-.5-.5Z",fill:"#191919"}));var t="data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMTYgMTYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik02IDE0aDhWNkg2djhabS00LTRWMmg4djNINS41YS41LjUgMCAwIDAtLjUuNVYxMEgyWm0xMi41LTVIMTFWMS41YS41LjUgMCAwIDAtLjUtLjVoLTlhLjUuNSAwIDAgMC0uNS41djlhLjUuNSAwIDAgMCAuNS41SDV2My41YS41LjUgMCAwIDAgLjUuNWg5YS41LjUgMCAwIDAgLjUtLjV2LTlhLjUuNSAwIDAgMC0uNS0uNVoiIGZpbGw9IiMxOTE5MTkiLz48L3N2Zz4="},80014:function(ae,X,D){"use strict";D.d(X,{r:function(){return d}});var m=D(67294),c=Object.defineProperty,y=Object.getOwnPropertySymbols,C=Object.prototype.hasOwnProperty,E=Object.prototype.propertyIsEnumerable,l=(o,n,i)=>n in o?c(o,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):o[n]=i,x=(o,n)=>{for(var i in n||(n={}))C.call(n,i)&&l(o,i,n[i]);if(y)for(var i of y(n))E.call(n,i)&&l(o,i,n[i]);return o};const d=o=>m.createElement("svg",x({viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o),m.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M6.5 12A5.506 5.506 0 0 1 1 6.5C1 3.467 3.468 1 6.5 1S12 3.467 12 6.5 9.532 12 6.5 12Zm7.354 1.146-2.423-2.422A6.473 6.473 0 0 0 13 6.5C13 2.916 10.084 0 6.5 0A6.508 6.508 0 0 0 0 6.5C0 10.084 2.916 13 6.5 13a6.473 6.473 0 0 0 4.224-1.569l2.422 2.423a.502.502 0 0 0 .708 0 .502.502 0 0 0 0-.708Z",fill:"#333"}),m.createElement("path",{d:"M4 6.5h5M6.5 4v5",stroke:"#333",strokeLinecap:"round"}));var t="data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMTQgMTQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik02LjUgMTJBNS41MDYgNS41MDYgMCAwIDEgMSA2LjVDMSAzLjQ2NyAzLjQ2OCAxIDYuNSAxUzEyIDMuNDY3IDEyIDYuNSA5LjUzMiAxMiA2LjUgMTJabTcuMzU0IDEuMTQ2LTIuNDIzLTIuNDIyQTYuNDczIDYuNDczIDAgMCAwIDEzIDYuNUMxMyAyLjkxNiAxMC4wODQgMCA2LjUgMEE2LjUwOCA2LjUwOCAwIDAgMCAwIDYuNUMwIDEwLjA4NCAyLjkxNiAxMyA2LjUgMTNhNi40NzMgNi40NzMgMCAwIDAgNC4yMjQtMS41NjlsMi40MjIgMi40MjNhLjUwMi41MDIgMCAwIDAgLjcwOCAwIC41MDIuNTAyIDAgMCAwIDAtLjcwOFoiIGZpbGw9IiMzMzMiLz48cGF0aCBkPSJNNCA2LjVoNU02LjUgNHY1IiBzdHJva2U9IiMzMzMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPjwvc3ZnPg=="},44261:function(ae,X,D){const m=D(14881),c=D(50019),y=D(50305),C=D(1479),E=D(79350),l=D(62064),x=D(15749);var d=!!(typeof window!="undefined"&&window.document&&window.document.createElement),t={Version:"1.0.0"};t.Drawer=m,t.Parser=c,t.SvgDrawer=C,t.ReactionDrawer=E,t.ReactionParser=y,t.GaussDrawer=x,t.clean=function(o){return o.replace(/[^A-Za-z0-9@\.\+\-\?!\(\)\[\]\{\}/\\=#\$:\*]/g,"")},t.apply=function(o,n="canvas[data-smiles]",i="light",u=null){let p=new m(o),b=document.querySelectorAll(n);for(var v=0;v<b.length;v++){let s=b[v];t.parse(s.getAttribute("data-smiles"),function(r){p.draw(r,s,i,!1)},function(r){u&&u(r)})}},t.parse=function(o,n,i){try{n&&n(c.parse(o))}catch(u){i&&i(u)}},t.parseReaction=function(o,n,i){try{n&&n(y.parse(o))}catch(u){i&&i(u)}},d&&(window.SmilesDrawer=t,window.SmiDrawer=l),t.SmiDrawer=l,Array.prototype.fill||Object.defineProperty(Array.prototype,"fill",{value:function(o){if(this==null)throw new TypeError("this is null or not defined");for(var n=Object(this),i=n.length>>>0,u=arguments[1],p=u>>0,b=p<0?Math.max(i+p,0):Math.min(p,i),v=arguments[2],s=v===void 0?i:v>>0,r=s<0?Math.max(i+s,0):Math.min(s,i);b<r;)n[b]=o,b++;return n}}),ae.exports=t},16348:function(ae){class X{static clone(m){let c=Array.isArray(m)?Array():{};for(let y in m){let C=m[y];typeof C.clone=="function"?c[y]=C.clone():c[y]=typeof C=="object"?X.clone(C):C}return c}static equals(m,c){if(m.length!==c.length)return!1;let y=m.slice().sort(),C=c.slice().sort();for(var E=0;E<y.length;E++)if(y[E]!==C[E])return!1;return!0}static print(m){if(m.length==0)return"";let c="(";for(let y=0;y<m.length;y++)c+=m[y].id?m[y].id+", ":m[y]+", ";return c=c.substring(0,c.length-2),c+")"}static each(m,c){for(let y=0;y<m.length;y++)c(m[y])}static get(m,c,y){for(let C=0;C<m.length;C++)if(m[C][c]==y)return m[C]}static contains(m,c){if(!c.property&&!c.func){for(let y=0;y<m.length;y++)if(m[y]==c.value)return!0}else if(c.func){for(let y=0;y<m.length;y++)if(c.func(m[y]))return!0}else for(let y=0;y<m.length;y++)if(m[y][c.property]==c.value)return!0;return!1}static intersection(m,c){let y=new Array;for(let C=0;C<m.length;C++)for(let E=0;E<c.length;E++)m[C]===c[E]&&y.push(m[C]);return y}static unique(m){let c={};return m.filter(function(y){return c[y]!==void 0?!1:c[y]=!0})}static count(m,c){let y=0;for(let C=0;C<m.length;C++)m[C]===c&&y++;return y}static toggle(m,c){let y=Array(),C=!1;for(let E=0;E<m.length;E++)m[E]!==c?y.push(m[E]):C=!0;return C||y.push(c),y}static remove(m,c){let y=Array();for(let C=0;C<m.length;C++)m[C]!==c&&y.push(m[C]);return y}static removeUnique(m,c){let y=m.indexOf(c);return y>-1&&m.splice(y,1),m}static removeAll(m,c){return m.filter(function(y){return c.indexOf(y)===-1})}static merge(m,c){let y=new Array(m.length+c.length);for(let C=0;C<m.length;C++)y[C]=m[C];for(let C=0;C<c.length;C++)y[m.length+C]=c[C];return y}static containsAll(m,c){let y=0;for(let C=0;C<m.length;C++)for(let E=0;E<c.length;E++)m[C]===c[E]&&y++;return y===c.length}static sortByAtomicNumberDesc(m){let c=m.map(function(y,C){return{index:C,value:y.atomicNumber.split(".").map(Number)}});return c.sort(function(y,C){let E=Math.min(C.value.length,y.value.length),l=0;for(;l<E&&C.value[l]===y.value[l];)l++;return l===E?C.value.length-y.value.length:C.value[l]-y.value[l]}),c.map(function(y){return m[y.index]})}static deepCopy(m){let c=Array();for(let y=0;y<m.length;y++){let C=m[y];C instanceof Array?c[y]=X.deepCopy(C):c[y]=C}return c}}ae.exports=X},62427:function(ae,X,D){const m=D(16348),c=D(16843),y=D(96421);class C{constructor(l,x="-"){this.idx=null,this.element=l.length===1?l.toUpperCase():l,this.drawExplicit=!1,this.ringbonds=Array(),this.rings=Array(),this.bondType=x,this.branchBond=null,this.isBridge=!1,this.isBridgeNode=!1,this.originalRings=Array(),this.bridgedRing=null,this.anchoredRings=Array(),this.bracket=null,this.plane=0,this.attachedPseudoElements={},this.hasAttachedPseudoElements=!1,this.isDrawn=!0,this.isConnectedToRing=!1,this.neighbouringElements=Array(),this.isPartOfAromaticRing=l!==this.element,this.bondCount=0,this.chirality="",this.isStereoCenter=!1,this.priority=0,this.mainChain=!1,this.hydrogenDirection="down",this.subtreeDepth=1,this.hasHydrogen=!1,this.class=void 0}addNeighbouringElement(l){this.neighbouringElements.push(l)}attachPseudoElement(l,x,d=0,t=0){d===null&&(d=0),t===null&&(t=0);let o=d+l+t;this.attachedPseudoElements[o]?this.attachedPseudoElements[o].count+=1:this.attachedPseudoElements[o]={element:l,count:1,hydrogenCount:d,previousElement:x,charge:t},this.hasAttachedPseudoElements=!0}getAttachedPseudoElements(){let l={},x=this;return Object.keys(this.attachedPseudoElements).sort().forEach(function(d){l[d]=x.attachedPseudoElements[d]}),l}getAttachedPseudoElementsCount(){return Object.keys(this.attachedPseudoElements).length}isHeteroAtom(){return this.element!=="C"&&this.element!=="H"}addAnchoredRing(l){m.contains(this.anchoredRings,{value:l})||this.anchoredRings.push(l)}getRingbondCount(){return this.ringbonds.length}backupRings(){this.originalRings=Array(this.rings.length);for(let l=0;l<this.rings.length;l++)this.originalRings[l]=this.rings[l]}restoreRings(){this.rings=Array(this.originalRings.length);for(let l=0;l<this.originalRings.length;l++)this.rings[l]=this.originalRings[l]}haveCommonRingbond(l,x){for(let d=0;d<l.ringbonds.length;d++)for(let t=0;t<x.ringbonds.length;t++)if(l.ringbonds[d].id==x.ringbonds[t].id)return!0;return!1}neighbouringElementsEqual(l){if(l.length!==this.neighbouringElements.length)return!1;l.sort(),this.neighbouringElements.sort();for(var x=0;x<this.neighbouringElements.length;x++)if(l[x]!==this.neighbouringElements[x])return!1;return!0}getAtomicNumber(){return C.atomicNumbers[this.element]}getMaxBonds(){return C.maxBonds[this.element]}static get maxBonds(){return{H:1,C:4,N:3,O:2,P:3,S:2,B:3,F:1,I:1,Cl:1,Br:1}}static get atomicNumbers(){return{H:1,He:2,Li:3,Be:4,B:5,b:5,C:6,c:6,N:7,n:7,O:8,o:8,F:9,Ne:10,Na:11,Mg:12,Al:13,Si:14,P:15,p:15,S:16,s:16,Cl:17,Ar:18,K:19,Ca:20,Sc:21,Ti:22,V:23,Cr:24,Mn:25,Fe:26,Co:27,Ni:28,Cu:29,Zn:30,Ga:31,Ge:32,As:33,Se:34,Br:35,Kr:36,Rb:37,Sr:38,Y:39,Zr:40,Nb:41,Mo:42,Tc:43,Ru:44,Rh:45,Pd:46,Ag:47,Cd:48,In:49,Sn:50,Sb:51,Te:52,I:53,Xe:54,Cs:55,Ba:56,La:57,Ce:58,Pr:59,Nd:60,Pm:61,Sm:62,Eu:63,Gd:64,Tb:65,Dy:66,Ho:67,Er:68,Tm:69,Yb:70,Lu:71,Hf:72,Ta:73,W:74,Re:75,Os:76,Ir:77,Pt:78,Au:79,Hg:80,Tl:81,Pb:82,Bi:83,Po:84,At:85,Rn:86,Fr:87,Ra:88,Ac:89,Th:90,Pa:91,U:92,Np:93,Pu:94,Am:95,Cm:96,Bk:97,Cf:98,Es:99,Fm:100,Md:101,No:102,Lr:103,Rf:104,Db:105,Sg:106,Bh:107,Hs:108,Mt:109,Ds:110,Rg:111,Cn:112,Uut:113,Uuq:114,Uup:115,Uuh:116,Uus:117,Uuo:118}}static get mass(){return{H:1,He:2,Li:3,Be:4,B:5,b:5,C:6,c:6,N:7,n:7,O:8,o:8,F:9,Ne:10,Na:11,Mg:12,Al:13,Si:14,P:15,p:15,S:16,s:16,Cl:17,Ar:18,K:19,Ca:20,Sc:21,Ti:22,V:23,Cr:24,Mn:25,Fe:26,Co:27,Ni:28,Cu:29,Zn:30,Ga:31,Ge:32,As:33,Se:34,Br:35,Kr:36,Rb:37,Sr:38,Y:39,Zr:40,Nb:41,Mo:42,Tc:43,Ru:44,Rh:45,Pd:46,Ag:47,Cd:48,In:49,Sn:50,Sb:51,Te:52,I:53,Xe:54,Cs:55,Ba:56,La:57,Ce:58,Pr:59,Nd:60,Pm:61,Sm:62,Eu:63,Gd:64,Tb:65,Dy:66,Ho:67,Er:68,Tm:69,Yb:70,Lu:71,Hf:72,Ta:73,W:74,Re:75,Os:76,Ir:77,Pt:78,Au:79,Hg:80,Tl:81,Pb:82,Bi:83,Po:84,At:85,Rn:86,Fr:87,Ra:88,Ac:89,Th:90,Pa:91,U:92,Np:93,Pu:94,Am:95,Cm:96,Bk:97,Cf:98,Es:99,Fm:100,Md:101,No:102,Lr:103,Rf:104,Db:105,Sg:106,Bh:107,Hs:108,Mt:109,Ds:110,Rg:111,Cn:112,Uut:113,Uuq:114,Uup:115,Uuh:116,Uus:117,Uuo:118}}}ae.exports=C},68841:function(ae,X,D){const m=D(75474),c=D(2614),y=D(30929),C=D(16843),E=D(96421),{getChargeText:l}=D(20537);class x{constructor(t,o,n){typeof t=="string"||t instanceof String?this.canvas=document.getElementById(t):this.canvas=t,this.ctx=this.canvas.getContext("2d"),this.themeManager=o,this.opts=n,this.drawingWidth=0,this.drawingHeight=0,this.offsetX=0,this.offsetY=0,this.fontLarge=this.opts.fontSizeLarge+"pt Helvetica, Arial, sans-serif",this.fontSmall=this.opts.fontSizeSmall+"pt Helvetica, Arial, sans-serif",this.updateSize(this.opts.width,this.opts.height),this.ctx.font=this.fontLarge,this.hydrogenWidth=this.ctx.measureText("H").width,this.halfHydrogenWidth=this.hydrogenWidth/2,this.halfBondThickness=this.opts.bondThickness/2}updateSize(t,o){this.devicePixelRatio=window.devicePixelRatio||1,this.backingStoreRatio=this.ctx.webkitBackingStorePixelRatio||this.ctx.mozBackingStorePixelRatio||this.ctx.msBackingStorePixelRatio||this.ctx.oBackingStorePixelRatio||this.ctx.backingStorePixelRatio||1,this.ratio=this.devicePixelRatio/this.backingStoreRatio,this.ratio!==1?(this.canvas.width=t*this.ratio,this.canvas.height=o*this.ratio,this.canvas.style.width=t+"px",this.canvas.style.height=o+"px",this.ctx.setTransform(this.ratio,0,0,this.ratio,0,0)):(this.canvas.width=t*this.ratio,this.canvas.height=o*this.ratio)}setTheme(t){this.colors=t}scale(t){let o=-Number.MAX_VALUE,n=-Number.MAX_VALUE,i=Number.MAX_VALUE,u=Number.MAX_VALUE;for(var p=0;p<t.length;p++){if(!t[p].value.isDrawn)continue;let h=t[p].position;o<h.x&&(o=h.x),n<h.y&&(n=h.y),i>h.x&&(i=h.x),u>h.y&&(u=h.y)}var b=this.opts.padding;o+=b,n+=b,i-=b,u-=b,this.drawingWidth=o-i,this.drawingHeight=n-u;var v=this.canvas.offsetWidth/this.drawingWidth,s=this.canvas.offsetHeight/this.drawingHeight,r=v<s?v:s;this.ctx.scale(r,r),this.offsetX=-i,this.offsetY=-u,v<s?this.offsetY+=this.canvas.offsetHeight/(2*r)-this.drawingHeight/2:this.offsetX+=this.canvas.offsetWidth/(2*r)-this.drawingWidth/2}reset(){this.ctx.setTransform(1,0,0,1,0,0)}getColor(t){return t=t.toUpperCase(),t in this.colors?this.colors[t]:this.colors.C}drawCircle(t,o,n,i,u=!0,p=!1,b=""){let v=this.ctx,s=this.offsetX,r=this.offsetY;v.save(),v.lineWidth=1.5,v.beginPath(),v.arc(t+s,o+r,n,0,m.twoPI,!0),v.closePath(),p?(u?(v.fillStyle="#f00",v.fill()):(v.strokeStyle="#f00",v.stroke()),this.drawDebugText(t,o,b)):u?(v.fillStyle=i,v.fill()):(v.strokeStyle=i,v.stroke()),v.restore()}drawLine(t,o=!1,n=1){let i=this.ctx,u=this.offsetX,p=this.offsetY,b=t.clone().shorten(4),v=b.getLeftVector().clone(),s=b.getRightVector().clone();v.x+=u,v.y+=p,s.x+=u,s.y+=p,o||(i.save(),i.globalCompositeOperation="destination-out",i.beginPath(),i.moveTo(v.x,v.y),i.lineTo(s.x,s.y),i.lineCap="round",i.lineWidth=this.opts.bondThickness****,i.strokeStyle=this.themeManager.getColor("BACKGROUND"),i.stroke(),i.globalCompositeOperation="source-over",i.restore()),v=t.getLeftVector().clone(),s=t.getRightVector().clone(),v.x+=u,v.y+=p,s.x+=u,s.y+=p,i.save(),i.beginPath(),i.moveTo(v.x,v.y),i.lineTo(s.x,s.y),i.lineCap="round",i.lineWidth=this.opts.bondThickness;let r=this.ctx.createLinearGradient(v.x,v.y,s.x,s.y);r.addColorStop(.4,this.themeManager.getColor(t.getLeftElement())||this.themeManager.getColor("C")),r.addColorStop(.6,this.themeManager.getColor(t.getRightElement())||this.themeManager.getColor("C")),o&&(i.setLineDash([1,1.5]),i.lineWidth=this.opts.bondThickness/1.5),n<1&&(i.globalAlpha=n),i.strokeStyle=r,i.stroke(),i.restore()}drawWedge(t,o=1){if(isNaN(t.from.x)||isNaN(t.from.y)||isNaN(t.to.x)||isNaN(t.to.y))return;let n=this.ctx,i=this.offsetX,u=this.offsetY,p=t.clone().shorten(5),b=p.getLeftVector().clone(),v=p.getRightVector().clone();b.x+=i,b.y+=u,v.x+=i,v.y+=u,b=t.getLeftVector().clone(),v=t.getRightVector().clone(),b.x+=i,b.y+=u,v.x+=i,v.y+=u,n.save();let s=c.normals(b,v);s[0].normalize(),s[1].normalize();let r=t.getRightChiral(),h=b,g=v;r&&(h=v,g=b);let w=c.add(h,c.multiplyScalar(s[0],this.halfBondThickness)),I=c.add(g,c.multiplyScalar(s[0],1.5+this.halfBondThickness)),A=c.add(g,c.multiplyScalar(s[1],1.5+this.halfBondThickness)),B=c.add(h,c.multiplyScalar(s[1],this.halfBondThickness));n.beginPath(),n.moveTo(w.x,w.y),n.lineTo(I.x,I.y),n.lineTo(A.x,A.y),n.lineTo(B.x,B.y);let O=this.ctx.createRadialGradient(v.x,v.y,this.opts.bondLength,v.x,v.y,0);O.addColorStop(.4,this.themeManager.getColor(t.getLeftElement())||this.themeManager.getColor("C")),O.addColorStop(.6,this.themeManager.getColor(t.getRightElement())||this.themeManager.getColor("C")),n.fillStyle=O,n.fill(),n.restore()}drawDashedWedge(t){if(isNaN(t.from.x)||isNaN(t.from.y)||isNaN(t.to.x)||isNaN(t.to.y))return;let o=this.ctx,n=this.offsetX,i=this.offsetY,u=t.getLeftVector().clone(),p=t.getRightVector().clone();u.x+=n,u.y+=i,p.x+=n,p.y+=i,o.save();let b=c.normals(u,p);b[0].normalize(),b[1].normalize();let v=t.getRightChiral(),s,r,h,g,w=t.clone();v?(s=p,r=u,w.shortenRight(1),h=w.getRightVector().clone(),g=w.getLeftVector().clone()):(s=u,r=p,w.shortenLeft(1),h=w.getLeftVector().clone(),g=w.getRightVector().clone()),h.x+=n,h.y+=i,g.x+=n,g.y+=i;let I=c.subtract(r,s).normalize();o.strokeStyle=this.themeManager.getColor("C"),o.lineCap="round",o.lineWidth=this.opts.bondThickness,o.beginPath();let A=t.getLength(),B=1.25/(A/(this.opts.bondThickness*3)),O=!1;for(var T=0;T<1;T+=B){let M=c.multiplyScalar(I,T*A),H=c.add(s,M),U=1.5*T,$=c.multiplyScalar(b[0],U);!O&&T>.5&&(o.stroke(),o.beginPath(),o.strokeStyle=this.themeManager.getColor(t.getRightElement())||this.themeManager.getColor("C"),O=!0),H.subtract($),o.moveTo(H.x,H.y),H.add(c.multiplyScalar($,2)),o.lineTo(H.x,H.y)}o.stroke(),o.restore()}drawDebugText(t,o,n){let i=this.ctx;i.save(),i.font="5px Droid Sans, sans-serif",i.textAlign="start",i.textBaseline="top",i.fillStyle="#ff0000",i.fillText(n,t+this.offsetX,o+this.offsetY),i.restore()}drawBall(t,o,n){let i=this.ctx;i.save(),i.beginPath(),i.arc(t+this.offsetX,o+this.offsetY,this.opts.bondLength/4.5,0,m.twoPI,!1),i.fillStyle=this.themeManager.getColor(n),i.fill(),i.restore()}drawPoint(t,o,n){let i=this.ctx,u=this.offsetX,p=this.offsetY;i.save(),i.globalCompositeOperation="destination-out",i.beginPath(),i.arc(t+u,o+p,1.5,0,m.twoPI,!0),i.closePath(),i.fill(),i.globalCompositeOperation="source-over",i.beginPath(),i.arc(t+this.offsetX,o+this.offsetY,.75,0,m.twoPI,!1),i.fillStyle=this.themeManager.getColor(n),i.fill(),i.restore()}drawText(t,o,n,i,u,p,b,v,s,r={}){let h=this.ctx,g=this.offsetX,w=this.offsetY;h.save(),h.textAlign="start",h.textBaseline="alphabetic";let I=!1,A="",B=0;b&&(A=l(b),h.font=this.fontSmall,B=h.measureText(A).width);let O="0",T=0;v>0&&(O=v.toString(),h.font=this.fontSmall,T=h.measureText(O).width),b===1&&n==="N"&&r.hasOwnProperty("0O")&&r.hasOwnProperty("0O-1")&&(r={"0O":{element:"O",count:2,hydrogenCount:0,previousElement:"C",charge:""}},b=0),h.font=this.fontLarge,h.fillStyle=this.themeManager.getColor("BACKGROUND");let M=h.measureText(n);M.totalWidth=M.width+B,M.height=parseInt(this.fontLarge,10);let H=M.width>this.opts.fontSizeLarge?M.width:this.opts.fontSizeLarge;H/=1.5,h.globalCompositeOperation="destination-out",h.beginPath(),h.arc(t+g,o+w,H,0,m.twoPI,!0),h.closePath(),h.fill(),h.globalCompositeOperation="source-over";let U=-M.width/2,$=-M.width/2;h.fillStyle=this.themeManager.getColor(n),h.fillText(n,t+g+U,o+this.opts.halfFontSizeLarge+w),U+=M.width,b&&(h.font=this.fontSmall,h.fillText(A,t+g+U,o-this.opts.fifthFontSizeSmall+w),U+=B),v>0&&(h.font=this.fontSmall,h.fillText(O,t+g+$-T,o-this.opts.fifthFontSizeSmall+w),$-=T),h.font=this.fontLarge;let Y=0,q=0;if(i===1){let Z=t+g,ne=o+w+this.opts.halfFontSizeLarge;Y=this.hydrogenWidth,$-=Y,u==="left"?Z+=$:u==="right"||u==="up"&&p||u==="down"&&p?Z+=U:u==="up"&&!p?(ne-=this.opts.fontSizeLarge+this.opts.quarterFontSizeLarge,Z-=this.halfHydrogenWidth):u==="down"&&!p&&(ne+=this.opts.fontSizeLarge+this.opts.quarterFontSizeLarge,Z-=this.halfHydrogenWidth),h.fillText("H",Z,ne),U+=Y}else if(i>1){let Z=t+g,ne=o+w+this.opts.halfFontSizeLarge;Y=this.hydrogenWidth,h.font=this.fontSmall,q=h.measureText(i).width,$-=Y+q,u==="left"?Z+=$:u==="right"||u==="up"&&p||u==="down"&&p?Z+=U:u==="up"&&!p?(ne-=this.opts.fontSizeLarge+this.opts.quarterFontSizeLarge,Z-=this.halfHydrogenWidth):u==="down"&&!p&&(ne+=this.opts.fontSizeLarge+this.opts.quarterFontSizeLarge,Z-=this.halfHydrogenWidth),h.font=this.fontLarge,h.fillText("H",Z,ne),h.font=this.fontSmall,h.fillText(i,Z+this.halfHydrogenWidth+q,ne+this.opts.fifthFontSizeSmall),U+=Y+this.halfHydrogenWidth+q}if(I){h.restore();return}for(let Z in r){if(!r.hasOwnProperty(Z))continue;let ne=0,pe=0,me=r[Z].element,we=r[Z].count,Ie=r[Z].hydrogenCount,Le=r[Z].charge;h.font=this.fontLarge,we>1&&Ie>0&&(ne=h.measureText("(").width,pe=h.measureText(")").width);let Ee=h.measureText(me).width,_e=0,Re="",Ae=0;Y=0,Ie>0&&(Y=this.hydrogenWidth),h.font=this.fontSmall,we>1&&(_e=h.measureText(we).width),Le!==0&&(Re=l(Le),Ae=h.measureText(Re).width),q=0,Ie>1&&(q=h.measureText(Ie).width),h.font=this.fontLarge;let De=t+g,ye=o+w+this.opts.halfFontSizeLarge;h.fillStyle=this.themeManager.getColor(me),we>0&&($-=_e),we>1&&Ie>0&&(u==="left"?($-=pe,h.fillText(")",De+$,ye)):(h.fillText("(",De+U,ye),U+=ne)),u==="left"?($-=Ee,h.fillText(me,De+$,ye)):(h.fillText(me,De+U,ye),U+=Ee),Ie>0&&(u==="left"?($-=Y+q,h.fillText("H",De+$,ye),Ie>1&&(h.font=this.fontSmall,h.fillText(Ie,De+$+Y,ye+this.opts.fifthFontSizeSmall))):(h.fillText("H",De+U,ye),U+=Y,Ie>1&&(h.font=this.fontSmall,h.fillText(Ie,De+U,ye+this.opts.fifthFontSizeSmall),U+=q))),h.font=this.fontLarge,we>1&&Ie>0&&(u==="left"?($-=ne,h.fillText("(",De+$,ye)):(h.fillText(")",De+U,ye),U+=pe)),h.font=this.fontSmall,we>1&&(u==="left"?h.fillText(we,De+$+ne+pe+Y+q+Ee,ye+this.opts.fifthFontSizeSmall):(h.fillText(we,De+U,ye+this.opts.fifthFontSizeSmall),U+=_e)),Le!==0&&(u==="left"?h.fillText(Re,De+$+ne+pe+Y+q+Ee,o-this.opts.fifthFontSizeSmall+w):(h.fillText(Re,De+U,o-this.opts.fifthFontSizeSmall+w),U+=Ae))}h.restore()}getChargeText(t){return t===1?"+":t===2?"2+":t===-1?"-":t===-2?"2-":""}drawDebugPoint(t,o,n="",i="#f00"){this.drawCircle(t,o,2,i,!0,!0,n)}drawAromaticityRing(t){let o=this.ctx,n=m.apothemFromSideLength(this.opts.bondLength,t.getSize());o.save(),o.strokeStyle=this.themeManager.getColor("C"),o.lineWidth=this.opts.bondThickness,o.beginPath(),o.arc(t.center.x+this.offsetX,t.center.y+this.offsetY,n-this.opts.bondSpacing,0,Math.PI*2,!0),o.closePath(),o.stroke(),o.restore()}clear(){this.ctx.clearRect(0,0,this.canvas.offsetWidth,this.canvas.offsetHeight)}}ae.exports=x},14881:function(ae,X,D){const m=D(1479);class c{constructor(C){this.svgDrawer=new m(C)}draw(C,E,l="light",x=!1,d=[]){let t=null;typeof E=="string"||E instanceof String?t=document.getElementById(E):t=E;let o=document.createElementNS("http://www.w3.org/2000/svg","svg");o.setAttribute("xmlns","http://www.w3.org/2000/svg"),o.setAttributeNS(null,"viewBox","0 0 "+this.svgDrawer.opts.width+" "+this.svgDrawer.opts.height),o.setAttributeNS(null,"width",this.svgDrawer.opts.width+""),o.setAttributeNS(null,"height",this.svgDrawer.opts.height+""),this.svgDrawer.draw(C,o,l,x,d),this.svgDrawer.svgWrapper.toCanvas(t,this.svgDrawer.opts.width,this.svgDrawer.opts.height)}getTotalOverlapScore(){return this.svgDrawer.getTotalOverlapScore()}getMolecularFormula(){this.svgDrawer.getMolecularFormula()}}ae.exports=c},46237:function(ae,X,D){const m=D(75474),c=D(16348),y=D(2614),C=D(30929),E=D(16843),l=D(66826),x=D(62427),d=D(96421),t=D(3333),o=D(68841),n=D(84707),i=D(72473),u=D(16654),p=D(38207);class b{constructor(s){this.graph=null,this.doubleBondConfigCount=0,this.doubleBondConfig=null,this.ringIdCounter=0,this.ringConnectionIdCounter=0,this.canvasWrapper=null,this.totalOverlapScore=0,this.defaultOptions={width:500,height:500,scale:0,bondThickness:1,bondLength:30,shortBondLength:.8,bondSpacing:.17*30,atomVisualization:"default",isomeric:!0,debug:!1,terminalCarbons:!1,explicitHydrogens:!0,overlapSensitivity:.42,overlapResolutionIterations:1,compactDrawing:!0,fontFamily:"Arial, Helvetica, sans-serif",fontSizeLarge:11,fontSizeSmall:3,padding:10,experimentalSSSR:!1,kkThreshold:.1,kkInnerThreshold:.1,kkMaxIteration:2e4,kkMaxInnerIteration:50,kkMaxEnergy:1e9,weights:{colormap:null,additionalPadding:20,sigma:10,interval:0,opacity:1},themes:{dark:{C:"#fff",O:"#e74c3c",N:"#3498db",F:"#27ae60",CL:"#16a085",BR:"#d35400",I:"#8e44ad",P:"#d35400",S:"#f1c40f",B:"#e67e22",SI:"#e67e22",H:"#aaa",BACKGROUND:"#141414"},light:{C:"#222",O:"#e74c3c",N:"#3498db",F:"#27ae60",CL:"#16a085",BR:"#d35400",I:"#8e44ad",P:"#d35400",S:"#f1c40f",B:"#e67e22",SI:"#e67e22",H:"#666",BACKGROUND:"#fff"},oldschool:{C:"#000",O:"#000",N:"#000",F:"#000",CL:"#000",BR:"#000",I:"#000",P:"#000",S:"#000",B:"#000",SI:"#000",H:"#000",BACKGROUND:"#fff"},solarized:{C:"#586e75",O:"#dc322f",N:"#268bd2",F:"#859900",CL:"#16a085",BR:"#cb4b16",I:"#6c71c4",P:"#d33682",S:"#b58900",B:"#2aa198",SI:"#2aa198",H:"#657b83",BACKGROUND:"#fff"},"solarized-dark":{C:"#93a1a1",O:"#dc322f",N:"#268bd2",F:"#859900",CL:"#16a085",BR:"#cb4b16",I:"#6c71c4",P:"#d33682",S:"#b58900",B:"#2aa198",SI:"#2aa198",H:"#839496",BACKGROUND:"#fff"},matrix:{C:"#678c61",O:"#2fc079",N:"#4f7e7e",F:"#90d762",CL:"#82d967",BR:"#23755a",I:"#409931",P:"#c1ff8a",S:"#faff00",B:"#50b45a",SI:"#409931",H:"#426644",BACKGROUND:"#fff"},github:{C:"#24292f",O:"#cf222e",N:"#0969da",F:"#2da44e",CL:"#6fdd8b",BR:"#bc4c00",I:"#8250df",P:"#bf3989",S:"#d4a72c",B:"#fb8f44",SI:"#bc4c00",H:"#57606a",BACKGROUND:"#fff"},carbon:{C:"#161616",O:"#da1e28",N:"#0f62fe",F:"#198038",CL:"#007d79",BR:"#fa4d56",I:"#8a3ffc",P:"#ff832b",S:"#f1c21b",B:"#8a3800",SI:"#e67e22",H:"#525252",BACKGROUND:"#fff"},cyberpunk:{C:"#ea00d9",O:"#ff3131",N:"#0abdc6",F:"#00ff9f",CL:"#00fe00",BR:"#fe9f20",I:"#ff00ff",P:"#fe7f00",S:"#fcee0c",B:"#ff00ff",SI:"#ffffff",H:"#913cb1",BACKGROUND:"#fff"},gruvbox:{C:"#665c54",O:"#cc241d",N:"#458588",F:"#98971a",CL:"#79740e",BR:"#d65d0e",I:"#b16286",P:"#af3a03",S:"#d79921",B:"#689d6a",SI:"#427b58",H:"#7c6f64",BACKGROUND:"#fbf1c7"},"gruvbox-dark":{C:"#ebdbb2",O:"#cc241d",N:"#458588",F:"#98971a",CL:"#b8bb26",BR:"#d65d0e",I:"#b16286",P:"#fe8019",S:"#d79921",B:"#8ec07c",SI:"#83a598",H:"#bdae93",BACKGROUND:"#282828"},custom:{C:"#222",O:"#e74c3c",N:"#3498db",F:"#27ae60",CL:"#16a085",BR:"#d35400",I:"#8e44ad",P:"#d35400",S:"#f1c40f",B:"#e67e22",SI:"#e67e22",H:"#666",BACKGROUND:"#fff"}}},this.opts=p.extend(!0,this.defaultOptions,s),this.opts.halfBondSpacing=this.opts.bondSpacing/2,this.opts.bondLengthSq=this.opts.bondLength*this.opts.bondLength,this.opts.halfFontSizeLarge=this.opts.fontSizeLarge/2,this.opts.quarterFontSizeLarge=this.opts.fontSizeLarge/4,this.opts.fifthFontSizeSmall=this.opts.fontSizeSmall/5,this.theme=this.opts.themes.dark}draw(s,r,h="light",g=!1){this.initDraw(s,h,g),this.infoOnly||(this.themeManager=new u(this.opts.themes,h),this.canvasWrapper=new o(r,this.themeManager,this.opts)),g||(this.processGraph(),this.canvasWrapper.scale(this.graph.vertices),this.drawEdges(this.opts.debug),this.drawVertices(this.opts.debug),this.canvasWrapper.reset(),this.opts.debug&&(console.log(this.graph),console.log(this.rings),console.log(this.ringConnections)))}edgeRingCount(s){let r=this.graph.edges[s],h=this.graph.vertices[r.sourceId],g=this.graph.vertices[r.targetId];return Math.min(h.value.rings.length,g.value.rings.length)}getBridgedRings(){let s=Array();for(var r=0;r<this.rings.length;r++)this.rings[r].isBridged&&s.push(this.rings[r]);return s}getFusedRings(){let s=Array();for(var r=0;r<this.rings.length;r++)this.rings[r].isFused&&s.push(this.rings[r]);return s}getSpiros(){let s=Array();for(var r=0;r<this.rings.length;r++)this.rings[r].isSpiro&&s.push(this.rings[r]);return s}printRingInfo(){let s="";for(var r=0;r<this.rings.length;r++){const h=this.rings[r];s+=h.id+";",s+=h.members.length+";",s+=h.neighbours.length+";",s+=h.isSpiro?"true;":"false;",s+=h.isFused?"true;":"false;",s+=h.isBridged?"true;":"false;",s+=h.rings.length+";",s+=`
`}return s}rotateDrawing(){let s=0,r=0,h=0;for(var g=0;g<this.graph.vertices.length;g++){let A=this.graph.vertices[g];if(A.value.isDrawn)for(var w=g+1;w<this.graph.vertices.length;w++){let B=this.graph.vertices[w];if(!B.value.isDrawn)continue;let O=A.position.distanceSq(B.position);O>h&&(h=O,s=g,r=w)}}let I=-y.subtract(this.graph.vertices[s].position,this.graph.vertices[r].position).angle();if(!isNaN(I)){let A=I%.523599;A<.2617995?I=I-A:I+=.523599-A;for(var g=0;g<this.graph.vertices.length;g++)g!==r&&this.graph.vertices[g].position.rotateAround(I,this.graph.vertices[r].position);for(var g=0;g<this.rings.length;g++)this.rings[g].center.rotateAround(I,this.graph.vertices[r].position)}}getTotalOverlapScore(){return this.totalOverlapScore}getRingCount(){return this.rings.length}hasBridgedRing(){return this.bridgedRing}getHeavyAtomCount(){let s=0;for(var r=0;r<this.graph.vertices.length;r++)this.graph.vertices[r].value.element!=="H"&&s++;return s}getMolecularFormula(s=null){let r="",h=new Map,g=s===null?this.graph:new n(s,this.opts.isomeric);for(var w=0;w<g.vertices.length;w++){let A=g.vertices[w].value;if(h.has(A.element)?h.set(A.element,h.get(A.element)+1):h.set(A.element,1),A.bracket&&!A.bracket.chirality&&(h.has("H")?h.set("H",h.get("H")+A.bracket.hcount):h.set("H",A.bracket.hcount)),!A.bracket){let B=x.maxBonds[A.element]-A.bondCount;A.isPartOfAromaticRing&&B--,h.has("H")?h.set("H",h.get("H")+B):h.set("H",B)}}if(h.has("C")){let A=h.get("C");r+="C"+(A>1?A:""),h.delete("C")}if(h.has("H")){let A=h.get("H");r+="H"+(A>1?A:""),h.delete("H")}return Object.keys(x.atomicNumbers).sort().map(A=>{if(h.has(A)){let B=h.get(A);r+=A+(B>1?B:"")}}),r}getRingbondType(s,r){if(s.value.getRingbondCount()<1||r.value.getRingbondCount()<1)return null;for(var h=0;h<s.value.ringbonds.length;h++)for(var g=0;g<r.value.ringbonds.length;g++)if(s.value.ringbonds[h].id===r.value.ringbonds[g].id)return s.value.ringbonds[h].bondType==="-"?r.value.ringbonds[g].bond:s.value.ringbonds[h].bond;return null}initDraw(s,r,h,g){this.data=s,this.infoOnly=h,this.ringIdCounter=0,this.ringConnectionIdCounter=0,this.graph=new n(s,this.opts.isomeric),this.rings=Array(),this.ringConnections=Array(),this.originalRings=Array(),this.originalRingConnections=Array(),this.bridgedRing=!1,this.doubleBondConfigCount=null,this.doubleBondConfig=null,this.highlight_atoms=g,this.initRings(),this.initHydrogens()}processGraph(){this.position(),this.restoreRingInformation(),this.resolvePrimaryOverlaps();let s=this.getOverlapScore();this.totalOverlapScore=this.getOverlapScore().total;for(var r=0;r<this.opts.overlapResolutionIterations;r++)for(var h=0;h<this.graph.edges.length;h++){let g=this.graph.edges[h];if(this.isEdgeRotatable(g)){let w=this.graph.getTreeDepth(g.sourceId,g.targetId),I=this.graph.getTreeDepth(g.targetId,g.sourceId),A=g.targetId,B=g.sourceId;if(w>I&&(A=g.sourceId,B=g.targetId),this.getSubtreeOverlapScore(B,A,s.vertexScores).value>this.opts.overlapSensitivity){let T=this.graph.vertices[A],M=this.graph.vertices[B],H=M.getNeighbours(A);if(H.length===1){let U=this.graph.vertices[H[0]],$=U.position.getRotateAwayFromAngle(T.position,M.position,m.toRad(120));this.rotateSubtree(U.id,M.id,$,M.position);let Y=this.getOverlapScore().total;Y>this.totalOverlapScore?this.rotateSubtree(U.id,M.id,-$,M.position):this.totalOverlapScore=Y}else if(H.length===2){if(M.value.rings.length!==0&&T.value.rings.length!==0)continue;let U=this.graph.vertices[H[0]],$=this.graph.vertices[H[1]];if(U.value.rings.length===1&&$.value.rings.length===1){if(U.value.rings[0]!==$.value.rings[0])continue}else{if(U.value.rings.length!==0||$.value.rings.length!==0)continue;{let Y=U.position.getRotateAwayFromAngle(T.position,M.position,m.toRad(120)),q=$.position.getRotateAwayFromAngle(T.position,M.position,m.toRad(120));this.rotateSubtree(U.id,M.id,Y,M.position),this.rotateSubtree($.id,M.id,q,M.position);let Z=this.getOverlapScore().total;Z>this.totalOverlapScore?(this.rotateSubtree(U.id,M.id,-Y,M.position),this.rotateSubtree($.id,M.id,-q,M.position)):this.totalOverlapScore=Z}}}s=this.getOverlapScore()}}}this.resolveSecondaryOverlaps(s.scores),this.opts.isomeric&&this.annotateStereochemistry(),this.opts.compactDrawing&&this.opts.atomVisualization==="default"&&this.initPseudoElements(),this.rotateDrawing()}initRings(){let s=new Map;for(var r=this.graph.vertices.length-1;r>=0;r--){let w=this.graph.vertices[r];if(w.value.ringbonds.length!==0)for(var h=0;h<w.value.ringbonds.length;h++){let I=w.value.ringbonds[h].id,A=w.value.ringbonds[h].bond;if(!s.has(I))s.set(I,[w.id,A]);else{let B=w.id,O=s.get(I)[0],T=s.get(I)[1],M=new l(B,O,1);M.setBondType(T||A||"-");let H=this.graph.addEdge(M),U=this.graph.vertices[O];w.addRingbondChild(O,h),w.value.addNeighbouringElement(U.value.element),U.addRingbondChild(B,h),U.value.addNeighbouringElement(w.value.element),w.edges.push(H),U.edges.push(H),s.delete(I)}}}let g=i.getRings(this.graph,this.opts.experimentalSSSR);if(g!==null){for(var r=0;r<g.length;r++){let I=[...g[r]],A=this.addRing(new d(I));for(var h=0;h<I.length;h++)this.graph.vertices[I[h]].value.rings.push(A)}for(var r=0;r<this.rings.length-1;r++)for(var h=r+1;h<this.rings.length;h++){let A=this.rings[r],B=this.rings[h],O=new t(A,B);O.vertices.size>0&&this.addRingConnection(O)}for(var r=0;r<this.rings.length;r++){let I=this.rings[r];I.neighbours=t.getNeighbours(this.ringConnections,I.id)}for(var r=0;r<this.rings.length;r++){let I=this.rings[r];this.graph.vertices[I.members[0]].value.addAnchoredRing(I.id)}for(this.backupRingInformation();this.rings.length>0;){let w=-1;for(var r=0;r<this.rings.length;r++){let O=this.rings[r];this.isPartOfBridgedRing(O.id)&&!O.isBridged&&(w=O.id)}if(w===-1)break;let I=this.getRing(w),A=this.getBridgedRingRings(I.id);this.bridgedRing=!0,this.createBridgedRing(A,I.members[0]);for(var r=0;r<A.length;r++)this.removeRing(A[r])}}}initHydrogens(){if(!this.opts.explicitHydrogens)for(var s=0;s<this.graph.vertices.length;s++){let r=this.graph.vertices[s];if(r.value.element!=="H")continue;let h=this.graph.vertices[r.neighbours[0]];h.value.hasHydrogen=!0,(!h.value.isStereoCenter||h.value.rings.length<2&&!h.value.bridgedRing||h.value.bridgedRing&&h.value.originalRings.length<2)&&(r.value.isDrawn=!1)}}getBridgedRingRings(s){let r=Array(),h=this,g=function(w){let I=h.getRing(w);r.push(w);for(var A=0;A<I.neighbours.length;A++){let B=I.neighbours[A];r.indexOf(B)===-1&&B!==w&&t.isBridge(h.ringConnections,h.graph.vertices,w,B)&&g(B)}};return g(s),c.unique(r)}isPartOfBridgedRing(s){for(var r=0;r<this.ringConnections.length;r++)if(this.ringConnections[r].containsRing(s)&&this.ringConnections[r].isBridge(this.graph.vertices))return!0;return!1}createBridgedRing(s,r){let h=new Set,g=new Set,w=new Set;for(var I=0;I<s.length;I++){let H=this.getRing(s[I]);H.isPartOfBridged=!0;for(var A=0;A<H.members.length;A++)g.add(H.members[A]);for(var A=0;A<H.neighbours.length;A++){let $=H.neighbours[A];s.indexOf($)===-1&&w.add(H.neighbours[A])}}let B=new Set;for(let H of g){let U=this.graph.vertices[H],$=c.intersection(s,U.value.rings);U.value.rings.length===1||$.length===1?h.add(U.id):B.add(U.id)}let O=Array(),T=Array();for(let H of B){let U=this.graph.vertices[H],$=!1;for(let Y=0;Y<U.edges.length;Y++)this.edgeRingCount(U.edges[Y])===1&&($=!0);$?(U.value.isBridgeNode=!0,h.add(U.id)):(U.value.isBridge=!0,h.add(U.id))}let M=new d([...h]);this.addRing(M),M.isBridged=!0,M.neighbours=[...w];for(var I=0;I<s.length;I++)M.rings.push(this.getRing(s[I]).clone());for(var I=0;I<M.members.length;I++)this.graph.vertices[M.members[I]].value.bridgedRing=M.id;for(var I=0;I<T.length;I++){let U=this.graph.vertices[T[I]];U.value.rings=Array()}for(let H of h){let U=this.graph.vertices[H];U.value.rings=c.removeAll(U.value.rings,s),U.value.rings.push(M.id)}for(var I=0;I<s.length;I++)for(var A=I+1;A<s.length;A++)this.removeRingConnectionsBetween(s[I],s[A]);for(let H of w){let U=this.getRingConnections(H,s);for(var A=0;A<U.length;A++)this.getRingConnection(U[A]).updateOther(M.id,H);this.getRing(H).neighbours.push(M.id)}return M}areVerticesInSameRing(s,r){for(var h=0;h<s.value.rings.length;h++)for(var g=0;g<r.value.rings.length;g++)if(s.value.rings[h]===r.value.rings[g])return!0;return!1}getCommonRings(s,r){let h=Array();for(var g=0;g<s.value.rings.length;g++)for(var w=0;w<r.value.rings.length;w++)s.value.rings[g]==r.value.rings[w]&&h.push(s.value.rings[g]);return h}getLargestOrAromaticCommonRing(s,r){let h=this.getCommonRings(s,r),g=0,w=null;for(var I=0;I<h.length;I++){let A=this.getRing(h[I]),B=A.getSize();if(A.isBenzeneLike(this.graph.vertices))return A;B>g&&(g=B,w=A)}return w}getVerticesAt(s,r,h){let g=Array();for(var w=0;w<this.graph.vertices.length;w++){let I=this.graph.vertices[w];if(I.id===h||!I.positioned)continue;s.distanceSq(I.position)<=r*r&&g.push(I.id)}return g}getClosestVertex(s){let r=99999,h=null;for(var g=0;g<this.graph.vertices.length;g++){let w=this.graph.vertices[g];if(w.id===s.id)continue;let I=s.position.distanceSq(w.position);I<r&&(r=I,h=w)}return h}addRing(s){return s.id=this.ringIdCounter++,this.rings.push(s),s.id}removeRing(s){this.rings=this.rings.filter(function(h){return h.id!==s}),this.ringConnections=this.ringConnections.filter(function(h){return h.firstRingId!==s&&h.secondRingId!==s});for(var r=0;r<this.rings.length;r++){let h=this.rings[r];h.neighbours=h.neighbours.filter(function(g){return g!==s})}}getRing(s){for(var r=0;r<this.rings.length;r++)if(this.rings[r].id==s)return this.rings[r]}addRingConnection(s){return s.id=this.ringConnectionIdCounter++,this.ringConnections.push(s),s.id}removeRingConnection(s){this.ringConnections=this.ringConnections.filter(function(r){return r.id!==s})}removeRingConnectionsBetween(s,r){let h=Array();for(var g=0;g<this.ringConnections.length;g++){let w=this.ringConnections[g];(w.firstRingId===s&&w.secondRingId===r||w.firstRingId===r&&w.secondRingId===s)&&h.push(w.id)}for(var g=0;g<h.length;g++)this.removeRingConnection(h[g])}getRingConnection(s){for(var r=0;r<this.ringConnections.length;r++)if(this.ringConnections[r].id==s)return this.ringConnections[r]}getRingConnections(s,r){let h=Array();for(var g=0;g<this.ringConnections.length;g++){let I=this.ringConnections[g];for(var w=0;w<r.length;w++){let A=r[w];(I.firstRingId===s&&I.secondRingId===A||I.firstRingId===A&&I.secondRingId===s)&&h.push(I.id)}}return h}getOverlapScore(){let s=0,r=new Float32Array(this.graph.vertices.length);for(var h=0;h<this.graph.vertices.length;h++)r[h]=0;for(var h=0;h<this.graph.vertices.length;h++)for(var g=this.graph.vertices.length;--g>h;){let A=this.graph.vertices[h],B=this.graph.vertices[g];if(!A.value.isDrawn||!B.value.isDrawn)continue;let O=y.subtract(A.position,B.position).lengthSq();if(O<this.opts.bondLengthSq){let T=(this.opts.bondLength-Math.sqrt(O))/this.opts.bondLength;s+=T,r[h]+=T,r[g]+=T}}let w=Array();for(var h=0;h<this.graph.vertices.length;h++)w.push({id:h,score:r[h]});return w.sort(function(I,A){return A.score-I.score}),{total:s,scores:w,vertexScores:r}}chooseSide(s,r,h){let g=s.getNeighbours(r.id),w=r.getNeighbours(s.id),I=g.length,A=w.length,B=c.merge(g,w),O=[0,0];for(var T=0;T<B.length;T++)this.graph.vertices[B[T]].position.sameSideAs(s.position,r.position,h[0])?O[0]++:O[1]++;let M=[0,0];for(var T=0;T<this.graph.vertices.length;T++)this.graph.vertices[T].position.sameSideAs(s.position,r.position,h[0])?M[0]++:M[1]++;return{totalSideCount:M,totalPosition:M[0]>M[1]?0:1,sideCount:O,position:O[0]>O[1]?0:1,anCount:I,bnCount:A}}setRingCenter(s){let r=s.getSize(),h=new y(0,0);for(var g=0;g<r;g++)h.add(this.graph.vertices[s.members[g]].position);s.center=h.divide(r)}getSubringCenter(s,r){let h=r.value.originalRings,g=s.center,w=Number.MAX_VALUE;for(var I=0;I<h.length;I++)for(var A=0;A<s.rings.length;A++)h[I]===s.rings[A].id&&s.rings[A].getSize()<w&&(g=s.rings[A].center,w=s.rings[A].getSize());return g}drawEdges(s){let r=this,h=Array(this.graph.edges.length);if(h.fill(!1),this.graph.traverseBF(0,function(w){let I=r.graph.getEdges(w.id);for(var A=0;A<I.length;A++){let B=I[A];h[B]||(h[B]=!0,r.drawEdge(B,s))}}),!this.bridgedRing)for(var g=0;g<this.rings.length;g++){let w=this.rings[g];this.isRingAromatic(w)&&this.canvasWrapper.drawAromaticityRing(w)}}drawEdge(s,r){let h=this,g=this.graph.edges[s],w=this.graph.vertices[g.sourceId],I=this.graph.vertices[g.targetId],A=w.value.element,B=I.value.element;if((!w.value.isDrawn||!I.value.isDrawn)&&this.opts.atomVisualization==="default")return;let O=w.position,T=I.position,M=this.getEdgeNormals(g),H=c.clone(M);if(H[0].multiplyScalar(10).add(O),H[1].multiplyScalar(10).add(O),g.bondType==="="||this.getRingbondType(w,I)==="="||g.isPartOfAromaticRing&&this.bridgedRing){let U=this.areVerticesInSameRing(w,I),$=this.chooseSide(w,I,H);if(U){let q=this.getLargestOrAromaticCommonRing(w,I).center;M[0].multiplyScalar(h.opts.bondSpacing),M[1].multiplyScalar(h.opts.bondSpacing);let Z=null;q.sameSideAs(w.position,I.position,y.add(O,M[0]))?Z=new C(y.add(O,M[0]),y.add(T,M[0]),A,B):Z=new C(y.add(O,M[1]),y.add(T,M[1]),A,B),Z.shorten(this.opts.bondLength-this.opts.shortBondLength*this.opts.bondLength),g.isPartOfAromaticRing?this.canvasWrapper.drawLine(Z,!0):this.canvasWrapper.drawLine(Z),this.canvasWrapper.drawLine(new C(O,T,A,B))}else if(g.center||w.isTerminal()&&I.isTerminal()){M[0].multiplyScalar(h.opts.halfBondSpacing),M[1].multiplyScalar(h.opts.halfBondSpacing);let Y=new C(y.add(O,M[0]),y.add(T,M[0]),A,B),q=new C(y.add(O,M[1]),y.add(T,M[1]),A,B);this.canvasWrapper.drawLine(Y),this.canvasWrapper.drawLine(q)}else if($.anCount==0&&$.bnCount>1||$.bnCount==0&&$.anCount>1){M[0].multiplyScalar(h.opts.halfBondSpacing),M[1].multiplyScalar(h.opts.halfBondSpacing);let Y=new C(y.add(O,M[0]),y.add(T,M[0]),A,B),q=new C(y.add(O,M[1]),y.add(T,M[1]),A,B);this.canvasWrapper.drawLine(Y),this.canvasWrapper.drawLine(q)}else if($.sideCount[0]>$.sideCount[1]){M[0].multiplyScalar(h.opts.bondSpacing),M[1].multiplyScalar(h.opts.bondSpacing);let Y=new C(y.add(O,M[0]),y.add(T,M[0]),A,B);Y.shorten(this.opts.bondLength-this.opts.shortBondLength*this.opts.bondLength),this.canvasWrapper.drawLine(Y),this.canvasWrapper.drawLine(new C(O,T,A,B))}else if($.sideCount[0]<$.sideCount[1]){M[0].multiplyScalar(h.opts.bondSpacing),M[1].multiplyScalar(h.opts.bondSpacing);let Y=new C(y.add(O,M[1]),y.add(T,M[1]),A,B);Y.shorten(this.opts.bondLength-this.opts.shortBondLength*this.opts.bondLength),this.canvasWrapper.drawLine(Y),this.canvasWrapper.drawLine(new C(O,T,A,B))}else if($.totalSideCount[0]>$.totalSideCount[1]){M[0].multiplyScalar(h.opts.bondSpacing),M[1].multiplyScalar(h.opts.bondSpacing);let Y=new C(y.add(O,M[0]),y.add(T,M[0]),A,B);Y.shorten(this.opts.bondLength-this.opts.shortBondLength*this.opts.bondLength),this.canvasWrapper.drawLine(Y),this.canvasWrapper.drawLine(new C(O,T,A,B))}else if($.totalSideCount[0]<=$.totalSideCount[1]){M[0].multiplyScalar(h.opts.bondSpacing),M[1].multiplyScalar(h.opts.bondSpacing);let Y=new C(y.add(O,M[1]),y.add(T,M[1]),A,B);Y.shorten(this.opts.bondLength-this.opts.shortBondLength*this.opts.bondLength),this.canvasWrapper.drawLine(Y),this.canvasWrapper.drawLine(new C(O,T,A,B))}}else if(g.bondType==="#"){M[0].multiplyScalar(h.opts.bondSpacing/1.5),M[1].multiplyScalar(h.opts.bondSpacing/1.5);let U=new C(y.add(O,M[0]),y.add(T,M[0]),A,B),$=new C(y.add(O,M[1]),y.add(T,M[1]),A,B);this.canvasWrapper.drawLine(U),this.canvasWrapper.drawLine($),this.canvasWrapper.drawLine(new C(O,T,A,B))}else if(g.bondType!=="."){let U=w.value.isStereoCenter,$=I.value.isStereoCenter;g.wedge==="up"?this.canvasWrapper.drawWedge(new C(O,T,A,B,U,$)):g.wedge==="down"?this.canvasWrapper.drawDashedWedge(new C(O,T,A,B,U,$)):this.canvasWrapper.drawLine(new C(O,T,A,B,U,$))}if(r){let U=y.midpoint(O,T);this.canvasWrapper.drawDebugText(U.x,U.y,"e: "+s)}}drawVertices(s){for(var r=this.graph.vertices.length,r=0;r<this.graph.vertices.length;r++){let g=this.graph.vertices[r],w=g.value,I=0,A=0,B=g.value.bondCount,O=w.element,T=x.maxBonds[O]-B,M=g.getTextDirection(this.graph.vertices),H=this.opts.terminalCarbons||O!=="C"||w.hasAttachedPseudoElements?g.isTerminal():!1,U=w.element==="C";if(w.element==="N"&&w.isPartOfAromaticRing&&(T=0),w.bracket&&(T=w.bracket.hcount,I=w.bracket.charge,A=w.bracket.isotope),this.opts.atomVisualization==="allballs")this.canvasWrapper.drawBall(g.position.x,g.position.y,O);else if(w.isDrawn&&(!U||w.drawExplicit||H||w.hasAttachedPseudoElements)||this.graph.vertices.length===1)this.opts.atomVisualization==="default"?this.canvasWrapper.drawText(g.position.x,g.position.y,O,T,M,H,I,A,this.graph.vertices.length,w.getAttachedPseudoElements()):this.opts.atomVisualization==="balls"&&this.canvasWrapper.drawBall(g.position.x,g.position.y,O);else if(g.getNeighbourCount()===2&&g.forcePositioned==!0){let $=this.graph.vertices[g.neighbours[0]].position,Y=this.graph.vertices[g.neighbours[1]].position,q=y.threePointangle(g.position,$,Y);Math.abs(Math.PI-q)<.1&&this.canvasWrapper.drawPoint(g.position.x,g.position.y,O)}if(s){let $="v: "+g.id+" "+c.print(w.ringbonds);this.canvasWrapper.drawDebugText(g.position.x,g.position.y,$)}}if(this.opts.debug)for(var r=0;r<this.rings.length;r++){let g=this.rings[r].center;this.canvasWrapper.drawDebugPoint(g.x,g.y,"r: "+this.rings[r].id)}}position(){let s=null;for(var r=0;r<this.graph.vertices.length;r++)if(this.graph.vertices[r].value.bridgedRing!==null){s=this.graph.vertices[r];break}for(var r=0;r<this.rings.length;r++)this.rings[r].isBridged&&(s=this.graph.vertices[this.rings[r].members[0]]);this.rings.length>0&&s===null&&(s=this.graph.vertices[this.rings[0].members[0]]),s===null&&(s=this.graph.vertices[0]),this.createNextBond(s,null,0)}backupRingInformation(){this.originalRings=Array(),this.originalRingConnections=Array();for(var s=0;s<this.rings.length;s++)this.originalRings.push(this.rings[s]);for(var s=0;s<this.ringConnections.length;s++)this.originalRingConnections.push(this.ringConnections[s]);for(var s=0;s<this.graph.vertices.length;s++)this.graph.vertices[s].value.backupRings()}restoreRingInformation(){let s=this.getBridgedRings();this.rings=Array(),this.ringConnections=Array();for(var r=0;r<s.length;r++){let g=s[r];for(var h=0;h<g.rings.length;h++){let w=g.rings[h];this.originalRings[w.id].center=w.center}}for(var r=0;r<this.originalRings.length;r++)this.rings.push(this.originalRings[r]);for(var r=0;r<this.originalRingConnections.length;r++)this.ringConnections.push(this.originalRingConnections[r]);for(var r=0;r<this.graph.vertices.length;r++)this.graph.vertices[r].value.restoreRings()}createRing(s,r=null,h=null,g=null){if(s.positioned)return;r=r||new y(0,0);let w=s.getOrderedNeighbours(this.ringConnections),I=h?y.subtract(h.position,r).angle():0,A=m.polyCircumradius(this.opts.bondLength,s.getSize()),B=m.centralAngle(s.getSize());s.centralAngle=B;let O=I,T=this,M=h?h.id:null;if(s.members.indexOf(M)===-1&&(h&&(h.positioned=!1),M=s.members[0]),s.isBridged){this.graph.kkLayout(s.members.slice(),r,h.id,s,this.opts.bondLength,this.opts.kkThreshold,this.opts.kkInnerThreshold,this.opts.kkMaxIteration,this.opts.kkMaxInnerIteration,this.opts.kkMaxEnergy),s.positioned=!0,this.setRingCenter(s),r=s.center;for(var H=0;H<s.rings.length;H++)this.setRingCenter(s.rings[H])}else s.eachMember(this.graph.vertices,function($){let Y=T.graph.vertices[$];Y.positioned||Y.setPosition(r.x+Math.cos(O)*A,r.y+Math.sin(O)*A),O+=B,(!s.isBridged||s.rings.length<3)&&(Y.angle=O,Y.positioned=!0)},M,g?g.id:null);s.positioned=!0,s.center=r;for(var H=0;H<w.length;H++){let Y=this.getRing(w[H].neighbour);if(Y.positioned)continue;let q=t.getVertices(this.ringConnections,s.id,Y.id);if(q.length===2){s.isFused=!0,Y.isFused=!0;let Z=this.graph.vertices[q[0]],ne=this.graph.vertices[q[1]],pe=y.midpoint(Z.position,ne.position),me=y.normals(Z.position,ne.position);me[0].normalize(),me[1].normalize();let we=m.polyCircumradius(this.opts.bondLength,Y.getSize()),Ie=m.apothem(we,Y.getSize());me[0].multiplyScalar(Ie).add(pe),me[1].multiplyScalar(Ie).add(pe);let Le=me[0];y.subtract(r,me[1]).lengthSq()>y.subtract(r,me[0]).lengthSq()&&(Le=me[1]);let Ee=y.subtract(Z.position,Le),_e=y.subtract(ne.position,Le);Ee.clockwise(_e)===-1?Y.positioned||this.createRing(Y,Le,Z,ne):Y.positioned||this.createRing(Y,Le,ne,Z)}else if(q.length===1){s.isSpiro=!0,Y.isSpiro=!0;let Z=this.graph.vertices[q[0]],ne=y.subtract(r,Z.position);ne.invert(),ne.normalize();let pe=m.polyCircumradius(this.opts.bondLength,Y.getSize());ne.multiplyScalar(pe),ne.add(Z.position),Y.positioned||this.createRing(Y,ne,Z)}}for(var H=0;H<s.members.length;H++){let Y=this.graph.vertices[s.members[H]],q=Y.neighbours;for(var U=0;U<q.length;U++){let Z=this.graph.vertices[q[U]];Z.positioned||(Z.value.isConnectedToRing=!0,this.createNextBond(Z,Y,0))}}}rotateSubtree(s,r,h,g){let w=this;this.graph.traverseTree(s,r,function(I){I.position.rotateAround(h,g);for(var A=0;A<I.value.anchoredRings.length;A++){let B=w.rings[I.value.anchoredRings[A]];B&&B.center.rotateAround(h,g)}})}getSubtreeOverlapScore(s,r,h){let g=this,w=0,I=new y(0,0),A=0;return this.graph.traverseTree(s,r,function(B){if(!B.value.isDrawn)return;let O=h[B.id];O>g.opts.overlapSensitivity&&(w+=O,A++);let T=g.graph.vertices[B.id].position.clone();T.multiplyScalar(O),I.add(T)}),I.divide(w),{value:w/A,center:I}}getCurrentCenterOfMass(){let s=new y(0,0),r=0;for(var h=0;h<this.graph.vertices.length;h++){let g=this.graph.vertices[h];g.positioned&&(s.add(g.position),r++)}return s.divide(r)}getCurrentCenterOfMassInNeigbourhood(s,r=this.opts.bondLength*2){let h=new y(0,0),g=0,w=r*r;for(var I=0;I<this.graph.vertices.length;I++){let A=this.graph.vertices[I];A.positioned&&s.distanceSq(A.position)<w&&(h.add(A.position),g++)}return h.divide(g)}resolvePrimaryOverlaps(){let s=Array(),r=Array(this.graph.vertices.length);for(var h=0;h<this.rings.length;h++){let I=this.rings[h];for(var g=0;g<I.members.length;g++){let A=this.graph.vertices[I.members[g]];if(r[A.id])continue;r[A.id]=!0;let B=this.getNonRingNeighbours(A.id);if(B.length>1){let O=Array();for(var w=0;w<A.value.rings.length;w++)O.push(A.value.rings[w]);s.push({common:A,rings:O,vertices:B})}else if(B.length===1&&A.value.rings.length===2){let O=Array();for(var w=0;w<A.value.rings.length;w++)O.push(A.value.rings[w]);s.push({common:A,rings:O,vertices:B})}}}for(var h=0;h<s.length;h++){let A=s[h];if(A.vertices.length===2){let B=A.vertices[0],O=A.vertices[1];if(!B.value.isDrawn||!O.value.isDrawn)continue;let T=(2*Math.PI-this.getRing(A.rings[0]).getAngle())/6;this.rotateSubtree(B.id,A.common.id,T,A.common.position),this.rotateSubtree(O.id,A.common.id,-T,A.common.position);let M=this.getOverlapScore(),H=this.getSubtreeOverlapScore(B.id,A.common.id,M.vertexScores),U=this.getSubtreeOverlapScore(O.id,A.common.id,M.vertexScores),$=H.value+U.value;this.rotateSubtree(B.id,A.common.id,-2*T,A.common.position),this.rotateSubtree(O.id,A.common.id,2*T,A.common.position),M=this.getOverlapScore(),H=this.getSubtreeOverlapScore(B.id,A.common.id,M.vertexScores),U=this.getSubtreeOverlapScore(O.id,A.common.id,M.vertexScores),H.value+U.value>$&&(this.rotateSubtree(B.id,A.common.id,2*T,A.common.position),this.rotateSubtree(O.id,A.common.id,-2*T,A.common.position))}else A.vertices.length===1&&A.rings.length}}resolveSecondaryOverlaps(s){for(var r=0;r<s.length;r++)if(s[r].score>this.opts.overlapSensitivity){let h=this.graph.vertices[s[r].id];if(h.isTerminal()){let g=this.getClosestVertex(h);if(g){let w=null;g.isTerminal()?w=g.id===0?this.graph.vertices[1].position:g.previousPosition:w=g.id===0?this.graph.vertices[1].position:g.position;let I=h.id===0?this.graph.vertices[1].position:h.previousPosition;h.position.rotateAwayFrom(w,I,m.toRad(20))}}}}getLastVertexWithAngle(s){let r=0,h=null;for(;!r&&s;)h=this.graph.vertices[s],r=h.angle,s=h.parentVertexId;return h}createNextBond(s,r=null,h=0,g=!1,w=!1){if(s.positioned&&!w)return;let I=!1;if(r){let B=this.graph.getEdge(s.id,r.id);(B.bondType==="/"||B.bondType==="\\")&&++this.doubleBondConfigCount%2===1&&this.doubleBondConfig===null&&(this.doubleBondConfig=B.bondType,I=!0,r.parentVertexId===null&&s.value.branchBond&&(this.doubleBondConfig==="/"?this.doubleBondConfig="\\":this.doubleBondConfig==="\\"&&(this.doubleBondConfig="/")))}if(!w)if(r)if(r.value.rings.length>0){let B=r.neighbours,O=null,T=new y(0,0);if(r.value.bridgedRing===null&&r.value.rings.length>1)for(var A=0;A<B.length;A++){let M=this.graph.vertices[B[A]];if(c.containsAll(M.value.rings,r.value.rings)){O=M;break}}if(O===null){for(var A=0;A<B.length;A++){let H=this.graph.vertices[B[A]];H.positioned&&this.areVerticesInSameRing(H,r)&&T.add(y.subtract(H.position,r.position))}T.invert().normalize().multiplyScalar(this.opts.bondLength).add(r.position)}else T=O.position.clone().rotateAround(Math.PI,r.position);s.previousPosition=r.position,s.setPositionFromVector(T),s.positioned=!0}else{let B=new y(this.opts.bondLength,0);B.rotate(h),B.add(r.position),s.setPositionFromVector(B),s.previousPosition=r.position,s.positioned=!0}else{let B=new y(this.opts.bondLength,0);B.rotate(m.toRad(-60)),s.previousPosition=B,s.setPosition(this.opts.bondLength,0),s.angle=m.toRad(-60),s.value.bridgedRing===null&&(s.positioned=!0)}if(s.value.bridgedRing!==null){let B=this.getRing(s.value.bridgedRing);if(!B.positioned){let O=y.subtract(s.previousPosition,s.position);O.invert(),O.normalize();let T=m.polyCircumradius(this.opts.bondLength,B.members.length);O.multiplyScalar(T),O.add(s.position),this.createRing(B,O,s)}}else if(s.value.rings.length>0){let B=this.getRing(s.value.rings[0]);if(!B.positioned){let O=y.subtract(s.previousPosition,s.position);O.invert(),O.normalize();let T=m.polyCircumradius(this.opts.bondLength,B.getSize());O.multiplyScalar(T),O.add(s.position),this.createRing(B,O,s)}}else{let B=s.value.isStereoCenter,O=s.getNeighbours(),T=Array();for(var A=0;A<O.length;A++)this.graph.vertices[O[A]].value.isDrawn&&T.push(O[A]);r&&(T=c.remove(T,r.id));let M=s.getAngle();if(T.length===1){let H=this.graph.vertices[T[0]];if(s.value.bondType==="#"||r&&r.value.bondType==="#"||s.value.bondType==="="&&r&&r.value.rings.length===0&&r.value.bondType==="="&&s.value.branchBond!=="-"){if(s.value.drawExplicit=!1,r){let $=this.graph.getEdge(s.id,r.id);$.center=!0}let U=this.graph.getEdge(s.id,H.id);U.center=!0,(s.value.bondType==="#"||r&&r.value.bondType==="#")&&(H.angle=0),H.drawExplicit=!0,this.createNextBond(H,s,M+H.angle)}else if(r&&r.value.rings.length>0){let U=m.toRad(60),$=-U,Y=new y(this.opts.bondLength,0),q=new y(this.opts.bondLength,0);Y.rotate(U).add(s.position),q.rotate($).add(s.position);let Z=this.getCurrentCenterOfMass(),ne=Y.distanceSq(Z),pe=q.distanceSq(Z);H.angle=ne<pe?$:U,this.createNextBond(H,s,M+H.angle)}else{let U=s.angle;if(r&&r.neighbours.length>3?U>0?U=Math.min(1.0472,U):U<0?U=Math.max(-1.0472,U):U=1.0472:U||(U=this.getLastVertexWithAngle(s.id).angle,U||(U=1.0472)),r&&!I){let $=this.graph.getEdge(s.id,H.id).bondType;$==="/"?(this.doubleBondConfig==="/"||this.doubleBondConfig==="\\"&&(U=-U),this.doubleBondConfig=null):$==="\\"&&(this.doubleBondConfig==="/"?U=-U:this.doubleBondConfig,this.doubleBondConfig=null)}g?H.angle=U:H.angle=-U,this.createNextBond(H,s,M+H.angle)}}else if(T.length===2){let H=s.angle;H||(H=1.0472);let U=this.graph.getTreeDepth(T[0],s.id),$=this.graph.getTreeDepth(T[1],s.id),Y=this.graph.vertices[T[0]],q=this.graph.vertices[T[1]];Y.value.subtreeDepth=U,q.value.subtreeDepth=$;let Z=this.graph.getTreeDepth(r?r.id:null,s.id);r&&(r.value.subtreeDepth=Z);let ne=0,pe=1;q.value.element==="C"&&Y.value.element!=="C"&&$>1&&U<5?(ne=1,pe=0):q.value.element!=="C"&&Y.value.element==="C"&&U>1&&$<5?(ne=0,pe=1):$>U&&(ne=1,pe=0);let me=this.graph.vertices[T[ne]],we=this.graph.vertices[T[pe]],Ie=this.graph.getEdge(s.id,me.id),Le=this.graph.getEdge(s.id,we.id),Ee=!1;Z<U&&Z<$&&(Ee=!0),we.angle=H,me.angle=-H,this.doubleBondConfig==="\\"?we.value.branchBond==="\\"&&(we.angle=-H,me.angle=H):this.doubleBondConfig==="/"&&we.value.branchBond==="/"&&(we.angle=-H,me.angle=H),this.createNextBond(we,s,M+we.angle,Ee),this.createNextBond(me,s,M+me.angle,Ee)}else if(T.length===3){let H=this.graph.getTreeDepth(T[0],s.id),U=this.graph.getTreeDepth(T[1],s.id),$=this.graph.getTreeDepth(T[2],s.id),Y=this.graph.vertices[T[0]],q=this.graph.vertices[T[1]],Z=this.graph.vertices[T[2]];Y.value.subtreeDepth=H,q.value.subtreeDepth=U,Z.value.subtreeDepth=$,U>H&&U>$?(Y=this.graph.vertices[T[1]],q=this.graph.vertices[T[0]],Z=this.graph.vertices[T[2]]):$>H&&$>U&&(Y=this.graph.vertices[T[2]],q=this.graph.vertices[T[0]],Z=this.graph.vertices[T[1]]),r&&r.value.rings.length<1&&Y.value.rings.length<1&&q.value.rings.length<1&&Z.value.rings.length<1&&this.graph.getTreeDepth(q.id,s.id)===1&&this.graph.getTreeDepth(Z.id,s.id)===1&&this.graph.getTreeDepth(Y.id,s.id)>1?(Y.angle=-s.angle,s.angle>=0?(q.angle=m.toRad(30),Z.angle=m.toRad(90)):(q.angle=-m.toRad(30),Z.angle=-m.toRad(90)),this.createNextBond(Y,s,M+Y.angle),this.createNextBond(q,s,M+q.angle),this.createNextBond(Z,s,M+Z.angle)):(Y.angle=0,q.angle=m.toRad(90),Z.angle=-m.toRad(90),this.createNextBond(Y,s,M+Y.angle),this.createNextBond(q,s,M+q.angle),this.createNextBond(Z,s,M+Z.angle))}else if(T.length===4){let H=this.graph.getTreeDepth(T[0],s.id),U=this.graph.getTreeDepth(T[1],s.id),$=this.graph.getTreeDepth(T[2],s.id),Y=this.graph.getTreeDepth(T[3],s.id),q=this.graph.vertices[T[0]],Z=this.graph.vertices[T[1]],ne=this.graph.vertices[T[2]],pe=this.graph.vertices[T[3]];q.value.subtreeDepth=H,Z.value.subtreeDepth=U,ne.value.subtreeDepth=$,pe.value.subtreeDepth=Y,U>H&&U>$&&U>Y?(q=this.graph.vertices[T[1]],Z=this.graph.vertices[T[0]],ne=this.graph.vertices[T[2]],pe=this.graph.vertices[T[3]]):$>H&&$>U&&$>Y?(q=this.graph.vertices[T[2]],Z=this.graph.vertices[T[0]],ne=this.graph.vertices[T[1]],pe=this.graph.vertices[T[3]]):Y>H&&Y>U&&Y>$&&(q=this.graph.vertices[T[3]],Z=this.graph.vertices[T[0]],ne=this.graph.vertices[T[1]],pe=this.graph.vertices[T[2]]),q.angle=-m.toRad(36),Z.angle=m.toRad(36),ne.angle=-m.toRad(108),pe.angle=m.toRad(108),this.createNextBond(q,s,M+q.angle),this.createNextBond(Z,s,M+Z.angle),this.createNextBond(ne,s,M+ne.angle),this.createNextBond(pe,s,M+pe.angle)}}}getCommonRingbondNeighbour(s){let r=s.neighbours;for(var h=0;h<r.length;h++){let g=this.graph.vertices[r[h]];if(c.containsAll(g.value.rings,s.value.rings))return g}return null}isPointInRing(s){for(var r=0;r<this.rings.length;r++){let h=this.rings[r];if(!h.positioned)continue;let g=m.polyCircumradius(this.opts.bondLength,h.getSize()),w=g*g;if(s.distanceSq(h.center)<w)return!0}return!1}isEdgeInRing(s){let r=this.graph.vertices[s.sourceId],h=this.graph.vertices[s.targetId];return this.areVerticesInSameRing(r,h)}isEdgeRotatable(s){let r=this.graph.vertices[s.sourceId],h=this.graph.vertices[s.targetId];return!(s.bondType!=="-"||r.isTerminal()||h.isTerminal()||r.value.rings.length>0&&h.value.rings.length>0&&this.areVerticesInSameRing(r,h))}isRingAromatic(s){for(var r=0;r<s.members.length;r++)if(!this.graph.vertices[s.members[r]].value.isPartOfAromaticRing)return!1;return!0}getEdgeNormals(s){let r=this.graph.vertices[s.sourceId].position,h=this.graph.vertices[s.targetId].position;return y.units(r,h)}getNonRingNeighbours(s){let r=Array(),h=this.graph.vertices[s],g=h.neighbours;for(var w=0;w<g.length;w++){let I=this.graph.vertices[g[w]];c.intersection(h.value.rings,I.value.rings).length===0&&I.value.isBridge==!1&&r.push(I)}return r}annotateStereochemistry(){let s=10;for(var r=0;r<this.graph.vertices.length;r++){let I=this.graph.vertices[r];if(!I.value.isStereoCenter)continue;let A=I.getNeighbours(),B=A.length,O=Array(B);for(var h=0;h<B;h++){let Re=new Uint8Array(this.graph.vertices.length),Ae=Array(Array());Re[I.id]=1,this.visitStereochemistry(A[h],I.id,Re,Ae,s,0);for(var g=0;g<Ae.length;g++)Ae[g].sort(function(De,ye){return ye-De});O[h]=[h,Ae]}let T=0,M=0;for(var h=0;h<O.length;h++){O[h][1].length>T&&(T=O[h][1].length);for(var g=0;g<O[h][1].length;g++)O[h][1][g].length>M&&(M=O[h][1][g].length)}for(var h=0;h<O.length;h++){let Ae=T-O[h][1].length;for(var g=0;g<Ae;g++)O[h][1].push([]);O[h][1].push([A[h]]);for(var g=0;g<O[h][1].length;g++){let ye=M-O[h][1][g].length;for(var w=0;w<ye;w++)O[h][1][g].push(0)}}O.sort(function(Re,Ae){for(var De=0;De<Re[1].length;De++)for(var ye=0;ye<Re[1][De].length;ye++){if(Re[1][De][ye]>Ae[1][De][ye])return-1;if(Re[1][De][ye]<Ae[1][De][ye])return 1}return 0});let H=new Uint8Array(B);for(var h=0;h<B;h++)H[h]=O[h][0],I.value.priority=h;let U=this.graph.vertices[A[H[0]]].position,$=this.graph.vertices[A[H[1]]].position,Y=this.graph.vertices[A[H[2]]].position,q=U.relativeClockwise($,I.position),Z=U.relativeClockwise(Y,I.position),ne=q===-1,pe=I.value.bracket.chirality==="@"?-1:1,me=m.parityOfPermutation(H)*pe===1?"R":"S",we="down",Ie="up";(ne&&me!=="R"||!ne&&me!=="S")&&(I.value.hydrogenDirection="up",we="up",Ie="down"),I.value.hasHydrogen&&(this.graph.getEdge(I.id,A[H[H.length-1]]).wedge=we);let Le=new Array(A.length-1),Ee=I.value.rings.length>1&&I.value.hasHydrogen,_e=I.value.hasHydrogen?1:0;for(var h=0;h<H.length-_e;h++){Le[h]=new Uint32Array(2);let Ae=this.graph.vertices[A[H[h]]];Le[h][0]+=Ae.value.isStereoCenter?0:1e5,Le[h][0]+=this.areVerticesInSameRing(Ae,I)?0:1e4,Le[h][0]+=Ae.value.isHeteroAtom()?1e3:0,Le[h][0]-=Ae.value.subtreeDepth===0?1e3:0,Le[h][0]+=1e3-Ae.value.subtreeDepth,Le[h][1]=A[H[h]]}if(Le.sort(function(Re,Ae){return Re[0]>Ae[0]?-1:Re[0]<Ae[0]?1:0}),!Ee){let Re=Le[0][1];if(I.value.hasHydrogen)this.graph.getEdge(I.id,Re).wedge=Ie;else{let Ae=Ie;for(var h=H.length-1;h>=0&&(Ae===we?Ae=Ie:Ae=we,A[H[h]]!==Re);h--);this.graph.getEdge(I.id,Re).wedge=Ae}}I.value.chirality=me}}visitStereochemistry(s,r,h,g,w,I,A=0){h[s]=1;let B=this.graph.vertices[s],O=B.value.getAtomicNumber();g.length<=I&&g.push(Array());for(var T=0;T<this.graph.getEdge(s,r).weight;T++)g[I].push(A*1e3+O);let M=this.graph.vertices[s].neighbours;for(var T=0;T<M.length;T++)h[M[T]]!==1&&I<w-1&&this.visitStereochemistry(M[T],s,h.slice(),g,w,I+1,O);if(I<w-1){let H=0;for(var T=0;T<M.length;T++)H+=this.graph.getEdge(s,M[T]).weight;for(var T=0;T<B.value.getMaxBonds()-H;T++)g.length<=I+1&&g.push(Array()),g[I+1].push(O*1e3+1)}}initPseudoElements(){for(var s=0;s<this.graph.vertices.length;s++){const h=this.graph.vertices[s],g=h.neighbours;let w=Array(g.length);for(var r=0;r<g.length;r++)w[r]=this.graph.vertices[g[r]];if(h.getNeighbourCount()<3||h.value.rings.length>0||h.value.element==="P"||h.value.element==="C"&&w.length===3&&w[0].value.element==="N"&&w[1].value.element==="N"&&w[2].value.element==="N")continue;let I=0,A=0;for(var r=0;r<w.length;r++){let T=w[r],M=T.value.element,H=T.getNeighbourCount();M!=="C"&&M!=="H"&&H===1&&I++,H>1&&A++}if(A>1||I<2)continue;let B=null;for(var r=0;r<w.length;r++){let T=w[r];T.getNeighbourCount()>1&&(B=T)}for(var r=0;r<w.length;r++){let T=w[r];if(T.getNeighbourCount()>1)continue;T.value.isDrawn=!1;let M=x.maxBonds[T.value.element]-T.value.bondCount,H="";T.value.bracket&&(M=T.value.bracket.hcount,H=T.value.bracket.charge||0),h.value.attachPseudoElement(T.value.element,B?B.value.element:null,M,H)}}for(var s=0;s<this.graph.vertices.length;s++){const g=this.graph.vertices[s],w=g.value,I=w.element;if(I==="C"||I==="H"||!w.isDrawn)continue;const A=g.neighbours;let B=Array(A.length);for(var r=0;r<A.length;r++)B[r]=this.graph.vertices[A[r]];for(var r=0;r<B.length;r++){let T=B[r].value;if(!T.hasAttachedPseudoElements||T.getAttachedPseudoElementsCount()!==2)continue;const M=T.getAttachedPseudoElements();M.hasOwnProperty("0O")&&M.hasOwnProperty("3C")&&(T.isDrawn=!1,g.value.attachPseudoElement("Ac","",0))}}}}ae.exports=b},66826:function(ae){class X{constructor(m,c,y=1){this.id=null,this.sourceId=m,this.targetId=c,this.weight=y,this.bondType="-",this.isPartOfAromaticRing=!1,this.center=!1,this.wedge=""}setBondType(m){this.bondType=m,this.weight=X.bonds[m]}static get bonds(){return{"-":1,"/":1,"\\":1,"=":2,"#":3,$:4}}}ae.exports=X},25956:function(ae){const X={C2H4O2:"acetic acid",C3H6O:"acetone",C2H3N:"acetonitrile",C6H6:"benzene",CCl4:"carbon tetrachloride",C6H5Cl:"chlorobenzene",CHCl3:"chloroform",C6H12:"cyclohexane",C2H4Cl2:"1,2-dichloroethane",C4H10O3:"diethylene glycol",C6H14O3:"diglyme",C4H10O2:"DME",C3H7NO:"DMF",C2H6OS:"DMSO",C2H6O:"ethanol",C2H6O2:"ethylene glycol",C3H8O3:"glycerin",C7H16:"heptane",C6H18N3OP:"HMPA",C6H18N3P:"HMPT",C6H14:"hexane",CH4O:"methanol",C5H12O:"MTBE",CH2Cl2:"methylene chloride",CH5H9NO:"NMP",CH3NO2:"nitromethane",C5H12:"pentane",C5H5N:"pyridine",C7H8:"toluene",C6H15N:"triethyl amine",H2O:"water"};ae.exports=X},15749:function(ae,X,D){const m=D(2614),c=D(28132),y=D(24869);class C{constructor(l,x,d,t,o=.3,n=0,i=null,u=1,p=!1){this.points=l,this.weights=x,this.width=d,this.height=t,this.sigma=o,this.interval=n,this.opacity=u,this.normalized=p,i===null&&(i=["#c51b7d","#de77ae","#f1b6da","#fde0ef","#ffffff","#e6f5d0","#b8e186","#7fbc41","#4d9221"]),this.colormap=i,this.canvas=document.createElement("canvas"),this.context=this.canvas.getContext("2d"),this.canvas.width=this.width,this.canvas.height=this.height}setFromArray(l,x){this.points=[],l.forEach(d=>{this.points.push(new m(d[0],d[1]))}),this.weights=[],x.forEach(d=>{this.weights.push(d)})}draw(){let l=[];for(let o=0;o<this.width;o++){let n=[];for(let i=0;i<this.height;i++)n.push(0);l.push(n)}let x=1/(2*pr(this.sigma,2));for(let o=0;o<this.points.length;o++){let n=this.points[o],i=this.weights[o];for(let u=0;u<this.width;u++)for(let p=0;p<this.height;p++){let b=(pr(u-n.x,2)+pr(p-n.y,2))*x,v=i*Math.exp(-b);l[u][p]+=v}}let d=1;if(!this.normalized){let o=-Number.MAX_SAFE_INTEGER,n=Number.MAX_SAFE_INTEGER;for(let i=0;i<this.width;i++)for(let u=0;u<this.height;u++)l[i][u]<n&&(n=l[i][u]),l[i][u]>o&&(o=l[i][u]);d=Math.max(Math.abs(n),Math.abs(o))}const t=y.scale(this.colormap).domain([-1,1]);for(let o=0;o<this.width;o++)for(let n=0;n<this.height;n++){this.normalized||(l[o][n]=l[o][n]/d),this.interval!==0&&(l[o][n]=Math.round(l[o][n]/this.interval)*this.interval);let[i,u,p]=t(l[o][n]).rgb();this.setPixel(new m(o,n),i,u,p)}}getImage(l){let x=new Image;x.onload=()=>{this.context.imageSmoothingEnabled=!1,this.context.drawImage(x,0,0,this.width,this.height),l&&l(x)},x.onerror=function(d){console.log(d)},x.src=this.canvas.toDataURL()}getSVG(){return c(this.context.getImageData(0,0,this.width,this.height))}setPixel(l,x,d,t){this.context.fillStyle="rgba("+x+","+d+","+t+","+this.opacity+")",this.context.fillRect(l.x,l.y,1,1)}}ae.exports=C},84707:function(ae,X,D){const m=D(75474),c=D(2614),y=D(16843),C=D(66826),E=D(96421),l=D(62427);class x{constructor(t,o=!1){this.vertices=Array(),this.edges=Array(),this.atomIdxToVertexId=Array(),this.vertexIdsToEdgeId={},this.isomeric=o,this._atomIdx=0,this._time=0,this._init(t)}_init(t,o=0,n=null,i=!1){const u=t.atom.element?t.atom.element:t.atom;let p=new l(u,t.bond);(u!=="H"||!t.hasNext&&n===null)&&(p.idx=this._atomIdx,this._atomIdx++),p.branchBond=t.branchBond,p.ringbonds=t.ringbonds,p.bracket=t.atom.element?t.atom:null,p.class=t.atom.class;let b=new y(p),v=this.vertices[n];if(this.addVertex(b),p.idx!==null&&this.atomIdxToVertexId.push(b.id),n!==null){b.setParentVertexId(n),b.value.addNeighbouringElement(v.value.element),v.addChild(b.id),v.value.addNeighbouringElement(p.element),v.spanningTreeChildren.push(b.id);let g=new C(n,b.id,1),w=null;i?(g.setBondType(b.value.branchBond||"-"),w=b.id,g.setBondType(b.value.branchBond||"-"),w=b.id):(g.setBondType(v.value.bondType||"-"),w=v.id);let I=this.addEdge(g)}let s=t.ringbondCount+1;p.bracket&&(s+=p.bracket.hcount);let r=0;if(p.bracket&&p.bracket.chirality){p.isStereoCenter=!0,r=p.bracket.hcount;for(var h=0;h<r;h++)this._init({atom:"H",isBracket:"false",branches:Array(),branchCount:0,ringbonds:Array(),ringbondCount:!1,next:null,hasNext:!1,bond:"-"},h,b.id,!0)}for(var h=0;h<t.branchCount;h++)this._init(t.branches[h],h+s,b.id,!0);t.hasNext&&this._init(t.next,t.branchCount+s,b.id)}clear(){this.vertices=Array(),this.edges=Array(),this.vertexIdsToEdgeId={}}addVertex(t){return t.id=this.vertices.length,this.vertices.push(t),t.id}addEdge(t){let o=this.vertices[t.sourceId],n=this.vertices[t.targetId];return t.id=this.edges.length,this.edges.push(t),this.vertexIdsToEdgeId[t.sourceId+"_"+t.targetId]=t.id,this.vertexIdsToEdgeId[t.targetId+"_"+t.sourceId]=t.id,t.isPartOfAromaticRing=o.value.isPartOfAromaticRing&&n.value.isPartOfAromaticRing,o.value.bondCount+=t.weight,n.value.bondCount+=t.weight,o.edges.push(t.id),n.edges.push(t.id),t.id}getEdge(t,o){let n=this.vertexIdsToEdgeId[t+"_"+o];return n===void 0?null:this.edges[n]}getEdges(t){let o=Array(),n=this.vertices[t];for(var i=0;i<n.neighbours.length;i++)o.push(this.vertexIdsToEdgeId[t+"_"+n.neighbours[i]]);return o}hasEdge(t,o){return this.vertexIdsToEdgeId[t+"_"+o]!==void 0}getVertexList(){let t=[this.vertices.length];for(var o=0;o<this.vertices.length;o++)t[o]=this.vertices[o].id;return t}getEdgeList(){let t=Array(this.edges.length);for(var o=0;o<this.edges.length;o++)t[o]=[this.edges[o].sourceId,this.edges[o].targetId];return t}getAdjacencyMatrix(){let t=this.vertices.length,o=Array(t);for(var n=0;n<t;n++)o[n]=new Array(t),o[n].fill(0);for(var n=0;n<this.edges.length;n++){let u=this.edges[n];o[u.sourceId][u.targetId]=1,o[u.targetId][u.sourceId]=1}return o}getComponentsAdjacencyMatrix(){let t=this.vertices.length,o=Array(t),n=this.getBridges();for(var i=0;i<t;i++)o[i]=new Array(t),o[i].fill(0);for(var i=0;i<this.edges.length;i++){let p=this.edges[i];o[p.sourceId][p.targetId]=1,o[p.targetId][p.sourceId]=1}for(var i=0;i<n.length;i++)o[n[i][0]][n[i][1]]=0,o[n[i][1]][n[i][0]]=0;return o}getSubgraphAdjacencyMatrix(t){let o=t.length,n=Array(o);for(var i=0;i<o;i++){n[i]=new Array(o),n[i].fill(0);for(var u=0;u<o;u++)i!==u&&this.hasEdge(t[i],t[u])&&(n[i][u]=1)}return n}getDistanceMatrix(){let t=this.vertices.length,o=this.getAdjacencyMatrix(),n=Array(t);for(var i=0;i<t;i++)n[i]=Array(t),n[i].fill(1/0);for(var i=0;i<t;i++)for(var u=0;u<t;u++)o[i][u]===1&&(n[i][u]=1);for(var p=0;p<t;p++)for(var i=0;i<t;i++)for(var u=0;u<t;u++)n[i][u]>n[i][p]+n[p][u]&&(n[i][u]=n[i][p]+n[p][u]);return n}getSubgraphDistanceMatrix(t){let o=t.length,n=this.getSubgraphAdjacencyMatrix(t),i=Array(o);for(var u=0;u<o;u++)i[u]=Array(o),i[u].fill(1/0);for(var u=0;u<o;u++)for(var p=0;p<o;p++)n[u][p]===1&&(i[u][p]=1);for(var b=0;b<o;b++)for(var u=0;u<o;u++)for(var p=0;p<o;p++)i[u][p]>i[u][b]+i[b][p]&&(i[u][p]=i[u][b]+i[b][p]);return i}getAdjacencyList(){let t=this.vertices.length,o=Array(t);for(var n=0;n<t;n++){o[n]=[];for(var i=0;i<t;i++)n!==i&&this.hasEdge(this.vertices[n].id,this.vertices[i].id)&&o[n].push(i)}return o}getSubgraphAdjacencyList(t){let o=t.length,n=Array(o);for(var i=0;i<o;i++){n[i]=Array();for(var u=0;u<o;u++)i!==u&&this.hasEdge(t[i],t[u])&&n[i].push(u)}return n}getBridges(){let t=this.vertices.length,o=new Array(t),n=new Array(t),i=new Array(t),u=new Array(t),p=this.getAdjacencyList(),b=Array();o.fill(!1),u.fill(null),this._time=0;for(var v=0;v<t;v++)o[v]||this._bridgeDfs(v,o,n,i,u,p,b);return b}traverseBF(t,o){let n=this.vertices.length,i=new Array(n);i.fill(!1);for(var u=[t];u.length>0;){let b=u.shift(),v=this.vertices[b];o(v);for(var p=0;p<v.neighbours.length;p++){let s=v.neighbours[p];i[s]||(i[s]=!0,u.push(s))}}}getTreeDepth(t,o){if(t===null||o===null)return 0;let n=this.vertices[t].getSpanningTreeNeighbours(o),i=0;for(var u=0;u<n.length;u++){let p=n[u],b=this.getTreeDepth(p,t);b>i&&(i=b)}return i+1}traverseTree(t,o,n,i=999999,u=!1,p=1,b=null){if(b===null&&(b=new Uint8Array(this.vertices.length)),p>i+1||b[t]===1)return;b[t]=1;let v=this.vertices[t],s=v.getNeighbours(o);(!u||p>1)&&n(v);for(var r=0;r<s.length;r++)this.traverseTree(s[r],t,n,i,u,p+1,b)}kkLayout(t,o,n,i,u,p=.1,b=.1,v=2e3,s=50,r=1e9){let h=u;for(var g=t.length;g--;)var w=this.vertices[t[g]].neighbours.length;let I=this.getSubgraphDistanceMatrix(t),A=t.length,B=m.polyCircumradius(500,A),O=m.centralAngle(A),T=0,M=new Float32Array(A),H=new Float32Array(A),U=Array(A);for(g=A;g--;){let ue=this.vertices[t[g]];ue.positioned?(M[g]=ue.position.x,H[g]=ue.position.y):(M[g]=o.x+Math.cos(T)*B,H[g]=o.y+Math.sin(T)*B),U[g]=ue.positioned,T+=O}let $=Array(A);for(g=A;g--;){$[g]=new Array(A);for(var w=A;w--;)$[g][w]=u*I[g][w]}let Y=Array(A);for(g=A;g--;){Y[g]=Array(A);for(var w=A;w--;)Y[g][w]=h*Math.pow(I[g][w],-2)}let q=Array(A),Z=new Float32Array(A),ne=new Float32Array(A);for(g=A;g--;)q[g]=Array(A);g=A;let pe,me,we,Ie,Le,Ee,_e;for(;g--;){pe=M[g],me=H[g],we=0,Ie=0;let ue=A;for(;ue--;)g!==ue&&(Le=M[ue],Ee=H[ue],_e=1/Math.sqrt((pe-Le)*(pe-Le)+(me-Ee)*(me-Ee)),q[g][ue]=[Y[g][ue]*(pe-Le-$[g][ue]*(pe-Le)*_e),Y[g][ue]*(me-Ee-$[g][ue]*(me-Ee)*_e)],q[ue][g]=q[g][ue],we+=q[g][ue][0],Ie+=q[g][ue][1]);Z[g]=we,ne[g]=Ie}let Re=function(ue){return[Z[ue]*Z[ue]+ne[ue]*ne[ue],Z[ue],ne[ue]]},Ae=function(){let ue=0,Xe=0,qe=0,Ke=0;for(g=A;g--;){let[At,We,ft]=Re(g);At>ue&&U[g]===!1&&(ue=At,Xe=g,qe=We,Ke=ft)}return[Xe,ue,qe,Ke]},De=function(ue,Xe,qe){let Ke=0,At=0,We=0,ft=M[ue],lt=H[ue],It=$[ue],xt=Y[ue];for(g=A;g--;){if(g===ue)continue;let ke=M[g],oe=H[g],Ye=It[g],Oe=xt[g],Ve=(ft-ke)*(ft-ke),Ze=1/Math.pow(Ve+(lt-oe)*(lt-oe),1.5);Ke+=Oe*(1-Ye*(lt-oe)*(lt-oe)*Ze),At+=Oe*(1-Ye*Ve*Ze),We+=Oe*(Ye*(ft-ke)*(lt-oe)*Ze)}Ke===0&&(Ke=.1),At===0&&(At=.1),We===0&&(We=.1);let pt=Xe/Ke+qe/We;pt/=We/Ke-At/We;let yt=-(We*pt+Xe)/Ke;M[ue]+=yt,H[ue]+=pt;let Lt=q[ue];Xe=0,qe=0,ft=M[ue],lt=H[ue];let mt,ve,ee,le,de;for(g=A;g--;)ue!==g&&(mt=M[g],ve=H[g],ee=Lt[g][0],le=Lt[g][1],de=1/Math.sqrt((ft-mt)*(ft-mt)+(lt-ve)*(lt-ve)),yt=xt[g]*(ft-mt-It[g]*(ft-mt)*de),pt=xt[g]*(lt-ve-It[g]*(lt-ve)*de),Lt[g]=[yt,pt],Xe+=yt,qe+=pt,Z[g]+=yt-ee,ne[g]+=pt-le);Z[ue]=Xe,ne[ue]=qe},ye=0,Ht=0,kt=0,Ft=0,xe=0,Pt=0;for(;r>p&&v>xe;)for(xe++,[ye,r,Ht,kt]=Ae(),Ft=r,Pt=0;Ft>b&&s>Pt;)Pt++,De(ye,Ht,kt),[Ft,Ht,kt]=Re(ye);for(g=A;g--;){let ue=t[g],Xe=this.vertices[ue];Xe.position.x=M[g],Xe.position.y=H[g],Xe.positioned=!0,Xe.forcePositioned=!0}}_bridgeDfs(t,o,n,i,u,p,b){o[t]=!0,n[t]=i[t]=++this._time;for(var v=0;v<p[t].length;v++){let s=p[t][v];o[s]?s!==u[t]&&(i[t]=Math.min(i[t],n[s])):(u[s]=t,this._bridgeDfs(s,o,n,i,u,p,b),i[t]=Math.min(i[t],i[s]),i[s]>n[t]&&b.push([t,s]))}}static getConnectedComponents(t){let o=t.length,n=new Array(o),i=new Array,u=0;n.fill(!1);for(var p=0;p<o;p++)if(!n[p]){let b=Array();n[p]=!0,b.push(p),u++,x._ccGetDfs(p,n,t,b),b.length>1&&i.push(b)}return i}static getConnectedComponentCount(t){let o=t.length,n=new Array(o),i=0;n.fill(!1);for(var u=0;u<o;u++)n[u]||(n[u]=!0,i++,x._ccCountDfs(u,n,t));return i}static _ccCountDfs(t,o,n){for(var i=0;i<n[t].length;i++)!n[t][i]||o[i]||t===i||(o[i]=!0,x._ccCountDfs(i,o,n))}static _ccGetDfs(t,o,n,i){for(var u=0;u<n[t].length;u++)!n[t][u]||o[u]||t===u||(o[u]=!0,i.push(u),x._ccGetDfs(u,o,n,i))}}ae.exports=x},30929:function(ae,X,D){const m=D(2614);class c{constructor(C=new m(0,0),E=new m(0,0),l=null,x=null,d=!1,t=!1){this.from=C,this.to=E,this.elementFrom=l,this.elementTo=x,this.chiralFrom=d,this.chiralTo=t}clone(){return new c(this.from.clone(),this.to.clone(),this.elementFrom,this.elementTo)}getLength(){return Math.sqrt(Math.pow(this.to.x-this.from.x,2)+Math.pow(this.to.y-this.from.y,2))}getAngle(){return m.subtract(this.getRightVector(),this.getLeftVector()).angle()}getRightVector(){return this.from.x<this.to.x?this.to:this.from}getLeftVector(){return this.from.x<this.to.x?this.from:this.to}getRightElement(){return this.from.x<this.to.x?this.elementTo:this.elementFrom}getLeftElement(){return this.from.x<this.to.x?this.elementFrom:this.elementTo}getRightChiral(){return this.from.x<this.to.x?this.chiralTo:this.chiralFrom}getLeftChiral(){return this.from.x<this.to.x?this.chiralFrom:this.chiralTo}setRightVector(C,E){return this.from.x<this.to.x?(this.to.x=C,this.to.y=E):(this.from.x=C,this.from.y=E),this}setLeftVector(C,E){return this.from.x<this.to.x?(this.from.x=C,this.from.y=E):(this.to.x=C,this.to.y=E),this}rotateToXAxis(){let C=this.getLeftVector();return this.setRightVector(C.x+this.getLength(),C.y),this}rotate(C){let E=this.getLeftVector(),l=this.getRightVector(),x=Math.sin(C),d=Math.cos(C),t=d*(l.x-E.x)-x*(l.y-E.y)+E.x,o=x*(l.x-E.x)-d*(l.y-E.y)+E.y;return this.setRightVector(t,o),this}shortenFrom(C){let E=m.subtract(this.to,this.from);return E.normalize(),E.multiplyScalar(C),this.from.add(E),this}shortenTo(C){let E=m.subtract(this.from,this.to);return E.normalize(),E.multiplyScalar(C),this.to.add(E),this}shortenRight(C){return this.from.x<this.to.x?this.shortenTo(C):this.shortenFrom(C),this}shortenLeft(C){return this.from.x<this.to.x?this.shortenFrom(C):this.shortenTo(C),this}shorten(C){let E=m.subtract(this.from,this.to);return E.normalize(),E.multiplyScalar(C/2),this.to.add(E),this.from.subtract(E),this}}ae.exports=c},75474:function(ae){class X{static round(m,c){return c=c||1,+(Math.round(m+"e"+c)+"e-"+c)}static meanAngle(m){let c=0,y=0;for(var C=0;C<m.length;C++)c+=Math.sin(m[C]),y+=Math.cos(m[C]);return Math.atan2(c/m.length,y/m.length)}static innerAngle(m){return X.toRad((m-2)*180/m)}static polyCircumradius(m,c){return m/(2*Math.sin(Math.PI/c))}static apothem(m,c){return m*Math.cos(Math.PI/c)}static apothemFromSideLength(m,c){let y=X.polyCircumradius(m,c);return X.apothem(y,c)}static centralAngle(m){return X.toRad(360/m)}static toDeg(m){return m*X.degFactor}static toRad(m){return m*X.radFactor}static parityOfPermutation(m){let c=new Uint8Array(m.length),y=0,C=function(l,x=0){return c[l]===1?x:(x++,c[l]=1,C(m[l],x))};for(var E=0;E<m.length;E++){if(c[E]===1)continue;let l=C(E);y+=1-l%2}return y%2?-1:1}static get radFactor(){return Math.PI/180}static get degFactor(){return 180/Math.PI}static get twoPI(){return 2*Math.PI}}ae.exports=X},38207:function(ae){class X{static extend(){let m=this,c={},y=!1,C=0,E=arguments.length;Object.prototype.toString.call(arguments[0])==="[object Boolean]"&&(y=arguments[0],C++);let l=function(x){for(var d in x)Object.prototype.hasOwnProperty.call(x,d)&&(y&&Object.prototype.toString.call(x[d])==="[object Object]"?c[d]=m.extend(!0,c[d],x[d]):c[d]=x[d])};for(;C<E;C++){let x=arguments[C];l(x)}return c}}ae.exports=X},50019:function(ae){ae.exports=function(){"use strict";function X(c,y){function C(){this.constructor=c}C.prototype=y.prototype,c.prototype=new C}function D(c,y,C,E){this.message=c,this.expected=y,this.found=C,this.location=E,this.name="SyntaxError",typeof Error.captureStackTrace=="function"&&Error.captureStackTrace(this,D)}X(D,Error),D.buildMessage=function(c,y){var C={literal:function(n){return'"'+l(n.text)+'"'},class:function(n){var i="",u;for(u=0;u<n.parts.length;u++)i+=n.parts[u]instanceof Array?x(n.parts[u][0])+"-"+x(n.parts[u][1]):x(n.parts[u]);return"["+(n.inverted?"^":"")+i+"]"},any:function(n){return"any character"},end:function(n){return"end of input"},other:function(n){return n.description}};function E(n){return n.charCodeAt(0).toString(16).toUpperCase()}function l(n){return n.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,function(i){return"\\x0"+E(i)}).replace(/[\x10-\x1F\x7F-\x9F]/g,function(i){return"\\x"+E(i)})}function x(n){return n.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,function(i){return"\\x0"+E(i)}).replace(/[\x10-\x1F\x7F-\x9F]/g,function(i){return"\\x"+E(i)})}function d(n){return C[n.type](n)}function t(n){var i=new Array(n.length),u,p;for(u=0;u<n.length;u++)i[u]=d(n[u]);if(i.sort(),i.length>0){for(u=1,p=1;u<i.length;u++)i[u-1]!==i[u]&&(i[p]=i[u],p++);i.length=p}switch(i.length){case 1:return i[0];case 2:return i[0]+" or "+i[1];default:return i.slice(0,-1).join(", ")+", or "+i[i.length-1]}}function o(n){return n?'"'+l(n)+'"':"end of input"}return"Expected "+t(c)+" but "+o(y)+" found."};function m(c,y){y=y!==void 0?y:{};var C=c.split("(").length-1,E=c.split(")").length-1;if(C!==E)throw Jt("The number of opening parentheses does not match the number of closing parentheses.",0);var l={},x={chain:_t},d=_t,t=function(k){for(var N=[],G=[],F=0;F<k[1].length;F++)N.push(k[1][F]);for(var F=0;F<k[2].length;F++){var K=k[2][F][0]?k[2][F][0]:"-";G.push({bond:K,id:k[2][F][1]})}for(var F=0;F<k[3].length;F++)N.push(k[3][F]);for(var F=0;F<k[6].length;F++)N.push(k[6][F]);return{atom:k[0],isBracket:!!k[0].element,branches:N,branchCount:N.length,ringbonds:G,ringbondCount:G.length,bond:k[4]?k[4]:"-",next:k[5],hasNext:!!k[5]}},o="(",n=be("(",!1),i=")",u=be(")",!1),p=function(k){var N=k[1]?k[1]:"-";return k[2].branchBond=N,k[2]},b=function(k){return k},v=/^[\-=#$:\/\\.]/,s=ot(["-","=","#","$",":","/","\\","."],!1,!1),r=function(k){return k},h="[",g=be("[",!1),w="se",I=be("se",!1),A="as",B=be("as",!1),O="]",T=be("]",!1),M=function(k){return{isotope:k[1],element:k[2],chirality:k[3],hcount:k[4],charge:k[5],class:k[6]}},H="B",U=be("B",!1),$="r",Y=be("r",!1),q="C",Z=be("C",!1),ne="l",pe=be("l",!1),me=/^[NOPSFI]/,we=ot(["N","O","P","S","F","I"],!1,!1),Ie=function(k){return k.length>1?k.join(""):k},Le=/^[bcnops]/,Ee=ot(["b","c","n","o","p","s"],!1,!1),_e="*",Re=be("*",!1),Ae=function(k){return k},De=/^[A-Z]/,ye=ot([["A","Z"]],!1,!1),Ht=/^[a-z]/,kt=ot([["a","z"]],!1,!1),Ft=function(k){return k.join("")},xe="%",Pt=be("%",!1),ue=/^[1-9]/,Xe=ot([["1","9"]],!1,!1),qe=/^[0-9]/,Ke=ot([["0","9"]],!1,!1),At=function(k){return k.length==1?Number(k):Number(k.join("").replace("%",""))},We="@",ft=be("@",!1),lt="TH",It=be("TH",!1),xt=/^[12]/,pt=ot(["1","2"],!1,!1),yt="AL",Lt=be("AL",!1),mt="SP",ve=be("SP",!1),ee=/^[1-3]/,le=ot([["1","3"]],!1,!1),de="TB",ke=be("TB",!1),oe="OH",Ye=be("OH",!1),Oe=function(k){return k[1]?k[1]=="@"?"@@":k[1].join("").replace(",",""):"@"},Ve=function(k){return k},Ze="+",et=be("+",!1),Fe=function(k){return k[1]?k[1]!="+"?Number(k[1].join("")):2:1},tt="-",Je=be("-",!1),ut=function(k){return k[1]?k[1]!="-"?-Number(k[1].join("")):-2:-1},ze="H",rt=be("H",!1),it=function(k){return k[1]?Number(k[1]):1},Nt=":",St=be(":",!1),Dt=/^[0]/,vt=ot(["0"],!1,!1),Et=function(k){return Number(k[1][0]+k[1][1].join(""))},Rt=function(k){return Number(k.join(""))},R=0,$e=0,Ct=[{line:1,column:1}],nt=0,jt=[],ge=0,Ut;if("startRule"in y){if(!(y.startRule in x))throw new Error(`Can't start parsing from rule "`+y.startRule+'".');d=x[y.startRule]}function hr(){return c.substring($e,R)}function st(){return Kt($e,R)}function bt(k,N){throw N=N!==void 0?N:Kt($e,R),gr([zt(k)],c.substring($e,R),N)}function Ot(k,N){throw N=N!==void 0?N:Kt($e,R),Jt(k,N)}function be(k,N){return{type:"literal",text:k,ignoreCase:N}}function ot(k,N,G){return{type:"class",parts:k,inverted:N,ignoreCase:G}}function sr(){return{type:"any"}}function Xt(){return{type:"end"}}function zt(k){return{type:"other",description:k}}function ur(k){var N=Ct[k],G;if(N)return N;for(G=k-1;!Ct[G];)G--;for(N=Ct[G],N={line:N.line,column:N.column};G<k;)c.charCodeAt(G)===10?(N.line++,N.column=1):N.column++,G++;return Ct[k]=N,N}function Kt(k,N){var G=ur(k),F=ur(N);return{start:{offset:k,line:G.line,column:G.column},end:{offset:N,line:F.line,column:F.column}}}function he(k){R<nt||(R>nt&&(nt=R,jt=[]),jt.push(k))}function Jt(k,N){return new D(k,null,null,N)}function gr(k,N,G){return new D(D.buildMessage(k,N),k,N,G)}function _t(){var k,N,G,F,K,te,fe,ht,Mt,Bt;if(k=R,N=R,G=vr(),G!==l){for(F=[],K=Qt();K!==l;)F.push(K),K=Qt();if(F!==l){for(K=[],te=R,fe=qt(),fe===l&&(fe=null),fe!==l?(ht=lr(),ht!==l?(fe=[fe,ht],te=fe):(R=te,te=l)):(R=te,te=l);te!==l;)K.push(te),te=R,fe=qt(),fe===l&&(fe=null),fe!==l?(ht=lr(),ht!==l?(fe=[fe,ht],te=fe):(R=te,te=l)):(R=te,te=l);if(K!==l){for(te=[],fe=Qt();fe!==l;)te.push(fe),fe=Qt();if(te!==l)if(fe=qt(),fe===l&&(fe=null),fe!==l)if(ht=_t(),ht===l&&(ht=null),ht!==l){for(Mt=[],Bt=Qt();Bt!==l;)Mt.push(Bt),Bt=Qt();Mt!==l?(G=[G,F,K,te,fe,ht,Mt],N=G):(R=N,N=l)}else R=N,N=l;else R=N,N=l;else R=N,N=l}else R=N,N=l}else R=N,N=l}else R=N,N=l;return N!==l&&($e=k,N=t(N)),k=N,k}function Qt(){var k,N,G,F,K,te;return k=R,N=R,c.charCodeAt(R)===40?(G=o,R++):(G=l,ge===0&&he(n)),G!==l?(F=qt(),F===l&&(F=null),F!==l?(K=_t(),K!==l?(c.charCodeAt(R)===41?(te=i,R++):(te=l,ge===0&&he(u)),te!==l?(G=[G,F,K,te],N=G):(R=N,N=l)):(R=N,N=l)):(R=N,N=l)):(R=N,N=l),N!==l&&($e=k,N=p(N)),k=N,k}function vr(){var k,N;return k=R,N=br(),N===l&&(N=er(),N===l&&(N=mr(),N===l&&(N=tr()))),N!==l&&($e=k,N=b(N)),k=N,k}function qt(){var k,N;if(k=R,v.test(c.charAt(R))){if(N=c.charAt(R),N===c.charAt(R+1)&&(N=l,ge===0))throw Jt("The parser encountered a bond repetition.",R+1);R++}else N=l,ge===0&&he(s);return N!==l&&($e=k,N=r(N)),k=N,k}function mr(){var k,N,G,F,K,te,fe,ht,Mt,Bt;return k=R,N=R,c.charCodeAt(R)===91?(G=h,R++):(G=l,ge===0&&he(g)),G!==l?(F=Cr(),F===l&&(F=null),F!==l?(c.substr(R,2)===w?(K=w,R+=2):(K=l,ge===0&&he(I)),K===l&&(c.substr(R,2)===A?(K=A,R+=2):(K=l,ge===0&&he(B)),K===l&&(K=er(),K===l&&(K=ar(),K===l&&(K=tr())))),K!==l?(te=fr(),te===l&&(te=null),te!==l?(fe=Sr(),fe===l&&(fe=null),fe!==l?(ht=wr(),ht===l&&(ht=null),ht!==l?(Mt=rr(),Mt===l&&(Mt=null),Mt!==l?(c.charCodeAt(R)===93?(Bt=O,R++):(Bt=l,ge===0&&he(T)),Bt!==l?(G=[G,F,K,te,fe,ht,Mt,Bt],N=G):(R=N,N=l)):(R=N,N=l)):(R=N,N=l)):(R=N,N=l)):(R=N,N=l)):(R=N,N=l)):(R=N,N=l)):(R=N,N=l),N!==l&&($e=k,N=M(N)),k=N,k}function br(){var k,N,G,F;return k=R,N=R,c.charCodeAt(R)===66?(G=H,R++):(G=l,ge===0&&he(U)),G!==l?(c.charCodeAt(R)===114?(F=$,R++):(F=l,ge===0&&he(Y)),F===l&&(F=null),F!==l?(G=[G,F],N=G):(R=N,N=l)):(R=N,N=l),N===l&&(N=R,c.charCodeAt(R)===67?(G=q,R++):(G=l,ge===0&&he(Z)),G!==l?(c.charCodeAt(R)===108?(F=ne,R++):(F=l,ge===0&&he(pe)),F===l&&(F=null),F!==l?(G=[G,F],N=G):(R=N,N=l)):(R=N,N=l),N===l&&(me.test(c.charAt(R))?(N=c.charAt(R),R++):(N=l,ge===0&&he(we)))),N!==l&&($e=k,N=Ie(N)),k=N,k}function er(){var k,N;return k=R,Le.test(c.charAt(R))?(N=c.charAt(R),R++):(N=l,ge===0&&he(Ee)),N!==l&&($e=k,N=b(N)),k=N,k}function tr(){var k,N;return k=R,c.charCodeAt(R)===42?(N=_e,R++):(N=l,ge===0&&he(Re)),N!==l&&($e=k,N=Ae(N)),k=N,k}function ar(){var k,N,G,F;return k=R,N=R,De.test(c.charAt(R))?(G=c.charAt(R),R++):(G=l,ge===0&&he(ye)),G!==l?(Ht.test(c.charAt(R))?(F=c.charAt(R),R++):(F=l,ge===0&&he(kt)),F===l&&(F=null),F!==l?(G=[G,F],N=G):(R=N,N=l)):(R=N,N=l),N!==l&&($e=k,N=Ft(N)),k=N,k}function lr(){var k,N,G,F,K;return k=R,N=R,c.charCodeAt(R)===37?(G=xe,R++):(G=l,ge===0&&he(Pt)),G!==l?(ue.test(c.charAt(R))?(F=c.charAt(R),R++):(F=l,ge===0&&he(Xe)),F!==l?(qe.test(c.charAt(R))?(K=c.charAt(R),R++):(K=l,ge===0&&he(Ke)),K!==l?(G=[G,F,K],N=G):(R=N,N=l)):(R=N,N=l)):(R=N,N=l),N===l&&(qe.test(c.charAt(R))?(N=c.charAt(R),R++):(N=l,ge===0&&he(Ke))),N!==l&&($e=k,N=At(N)),k=N,k}function fr(){var k,N,G,F,K,te,fe;return k=R,N=R,c.charCodeAt(R)===64?(G=We,R++):(G=l,ge===0&&he(ft)),G!==l?(c.charCodeAt(R)===64?(F=We,R++):(F=l,ge===0&&he(ft)),F===l&&(F=R,c.substr(R,2)===lt?(K=lt,R+=2):(K=l,ge===0&&he(It)),K!==l?(xt.test(c.charAt(R))?(te=c.charAt(R),R++):(te=l,ge===0&&he(pt)),te!==l?(K=[K,te],F=K):(R=F,F=l)):(R=F,F=l),F===l&&(F=R,c.substr(R,2)===yt?(K=yt,R+=2):(K=l,ge===0&&he(Lt)),K!==l?(xt.test(c.charAt(R))?(te=c.charAt(R),R++):(te=l,ge===0&&he(pt)),te!==l?(K=[K,te],F=K):(R=F,F=l)):(R=F,F=l),F===l&&(F=R,c.substr(R,2)===mt?(K=mt,R+=2):(K=l,ge===0&&he(ve)),K!==l?(ee.test(c.charAt(R))?(te=c.charAt(R),R++):(te=l,ge===0&&he(le)),te!==l?(K=[K,te],F=K):(R=F,F=l)):(R=F,F=l),F===l&&(F=R,c.substr(R,2)===de?(K=de,R+=2):(K=l,ge===0&&he(ke)),K!==l?(ue.test(c.charAt(R))?(te=c.charAt(R),R++):(te=l,ge===0&&he(Xe)),te!==l?(qe.test(c.charAt(R))?(fe=c.charAt(R),R++):(fe=l,ge===0&&he(Ke)),fe===l&&(fe=null),fe!==l?(K=[K,te,fe],F=K):(R=F,F=l)):(R=F,F=l)):(R=F,F=l),F===l&&(F=R,c.substr(R,2)===oe?(K=oe,R+=2):(K=l,ge===0&&he(Ye)),K!==l?(ue.test(c.charAt(R))?(te=c.charAt(R),R++):(te=l,ge===0&&he(Xe)),te!==l?(qe.test(c.charAt(R))?(fe=c.charAt(R),R++):(fe=l,ge===0&&he(Ke)),fe===l&&(fe=null),fe!==l?(K=[K,te,fe],F=K):(R=F,F=l)):(R=F,F=l)):(R=F,F=l)))))),F===l&&(F=null),F!==l?(G=[G,F],N=G):(R=N,N=l)):(R=N,N=l),N!==l&&($e=k,N=Oe(N)),k=N,k}function wr(){var k,N;return k=R,N=Ar(),N===l&&(N=yr()),N!==l&&($e=k,N=Ve(N)),k=N,k}function Ar(){var k,N,G,F,K,te;return k=R,N=R,c.charCodeAt(R)===43?(G=Ze,R++):(G=l,ge===0&&he(et)),G!==l?(c.charCodeAt(R)===43?(F=Ze,R++):(F=l,ge===0&&he(et)),F===l&&(F=R,ue.test(c.charAt(R))?(K=c.charAt(R),R++):(K=l,ge===0&&he(Xe)),K!==l?(qe.test(c.charAt(R))?(te=c.charAt(R),R++):(te=l,ge===0&&he(Ke)),te===l&&(te=null),te!==l?(K=[K,te],F=K):(R=F,F=l)):(R=F,F=l)),F===l&&(F=null),F!==l?(G=[G,F],N=G):(R=N,N=l)):(R=N,N=l),N!==l&&($e=k,N=Fe(N)),k=N,k}function yr(){var k,N,G,F,K,te;return k=R,N=R,c.charCodeAt(R)===45?(G=tt,R++):(G=l,ge===0&&he(Je)),G!==l?(c.charCodeAt(R)===45?(F=tt,R++):(F=l,ge===0&&he(Je)),F===l&&(F=R,ue.test(c.charAt(R))?(K=c.charAt(R),R++):(K=l,ge===0&&he(Xe)),K!==l?(qe.test(c.charAt(R))?(te=c.charAt(R),R++):(te=l,ge===0&&he(Ke)),te===l&&(te=null),te!==l?(K=[K,te],F=K):(R=F,F=l)):(R=F,F=l)),F===l&&(F=null),F!==l?(G=[G,F],N=G):(R=N,N=l)):(R=N,N=l),N!==l&&($e=k,N=ut(N)),k=N,k}function Sr(){var k,N,G,F;return k=R,N=R,c.charCodeAt(R)===72?(G=ze,R++):(G=l,ge===0&&he(rt)),G!==l?(qe.test(c.charAt(R))?(F=c.charAt(R),R++):(F=l,ge===0&&he(Ke)),F===l&&(F=null),F!==l?(G=[G,F],N=G):(R=N,N=l)):(R=N,N=l),N!==l&&($e=k,N=it(N)),k=N,k}function rr(){var k,N,G,F,K,te,fe;if(k=R,N=R,c.charCodeAt(R)===58?(G=Nt,R++):(G=l,ge===0&&he(St)),G!==l){if(F=R,ue.test(c.charAt(R))?(K=c.charAt(R),R++):(K=l,ge===0&&he(Xe)),K!==l){for(te=[],qe.test(c.charAt(R))?(fe=c.charAt(R),R++):(fe=l,ge===0&&he(Ke));fe!==l;)te.push(fe),qe.test(c.charAt(R))?(fe=c.charAt(R),R++):(fe=l,ge===0&&he(Ke));te!==l?(K=[K,te],F=K):(R=F,F=l)}else R=F,F=l;F===l&&(Dt.test(c.charAt(R))?(F=c.charAt(R),R++):(F=l,ge===0&&he(vt))),F!==l?(G=[G,F],N=G):(R=N,N=l)}else R=N,N=l;return N!==l&&($e=k,N=Et(N)),k=N,k}function Cr(){var k,N,G,F,K;return k=R,N=R,ue.test(c.charAt(R))?(G=c.charAt(R),R++):(G=l,ge===0&&he(Xe)),G!==l?(qe.test(c.charAt(R))?(F=c.charAt(R),R++):(F=l,ge===0&&he(Ke)),F===l&&(F=null),F!==l?(qe.test(c.charAt(R))?(K=c.charAt(R),R++):(K=l,ge===0&&he(Ke)),K===l&&(K=null),K!==l?(G=[G,F,K],N=G):(R=N,N=l)):(R=N,N=l)):(R=N,N=l),N!==l&&($e=k,N=Rt(N)),k=N,k}if(Ut=d(),Ut!==l&&R===c.length)return Ut;throw Ut!==l&&R<c.length&&he(Xt()),gr(jt,nt<c.length?c.charAt(nt):null,nt<c.length?Kt(nt,nt+1):Kt(nt,nt))}return{SyntaxError:D,parse:m}}()},28132:function(ae){function X(D){"use strict";function m(i,u){var p=i.length,b=p===0||p>0&&p-1 in i,v=0;if(b)for(;v<p&&u.call(i[v],v,i[v])!==!1;v++);else for(v in i)if(u.call(i[v],v,i[v])===!1)break}function c(i){var u=parseInt(i).toString(16);return u.length==1?"0"+u:u}function y(i,u,p,b){return b=parseInt(b),b===void 0||b===255?"#"+c(i)+c(u)+c(p):b===0?!1:"rgba("+i+","+u+","+p+","+b/255+")"}function C(i,u,p){return"M"+i+" "+u+"h"+p}function E(i,u){return'<path stroke="'+i+'" d="'+u+`" />
`}function l(i){var u="";return m(i,function(p,b){var v=p;if(p=y.apply(null,p.split(",")),p!==!1){var s=[],r,h=1;m(b,function(){r&&this[1]===r[1]&&this[0]===r[0]+h?h++:(r&&(s.push(C(r[0],r[1],h)),h=1),r=this)}),s.push(C(r[0],r[1],h)),u+=E(p,s.join(""))}}),u}var x=function(i){for(var u={},p=i.data,b=p.length,v=i.width,s=i.height,r=0,h=0,g=0,w;g<b;g+=4)p[g+3]>0&&(w=p[g]+","+p[g+1]+","+p[g+2]+","+p[g+3],u[w]=u[w]||[],r=g/4%v,h=Math.floor(g/4/v),u[w].push([r,h]));return u};let d=x(D),t=l(d),o='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -0.5 '+D.width+" "+D.height+'" shape-rendering="crispEdges"><g shape-rendering="crispEdges">'+t+"</g></svg>";var n=document.createElement("div");return n.innerHTML=o,n.firstChild}ae.exports=X},28388:function(ae,X,D){const m=D(50019);class c{constructor(C){this.reactantsSmiles=[],this.reagentsSmiles=[],this.productsSmiles=[],this.reactantsWeights=[],this.reagentsWeights=[],this.productsWeights=[],this.reactants=[],this.reagents=[],this.products=[];let E=C.split(">");if(E.length!==3)throw new Error("Invalid reaction SMILES. Did you add fewer than or more than two '>'?");E[0]!==""&&(this.reactantsSmiles=E[0].split(".")),E[1]!==""&&(this.reagentsSmiles=E[1].split(".")),E[2]!==""&&(this.productsSmiles=E[2].split("."));for(var l=0;l<this.reactantsSmiles.length;l++)this.reactants.push(m.parse(this.reactantsSmiles[l]));for(var l=0;l<this.reagentsSmiles.length;l++)this.reagents.push(m.parse(this.reagentsSmiles[l]));for(var l=0;l<this.productsSmiles.length;l++)this.products.push(m.parse(this.productsSmiles[l]))}}ae.exports=c},79350:function(ae,X,D){const m=D(1479),c=D(33433),y=D(38207),C=D(16654),E=D(25956);class l{constructor(d,t){this.defaultOptions={scale:t.scale>0?t.scale:1,fontSize:t.fontSizeLarge*.8,fontFamily:"Arial, Helvetica, sans-serif",spacing:10,plus:{size:9,thickness:1},arrow:{length:t.bondLength*4,headSize:6,thickness:1,margin:3},weights:{normalize:!1}},this.opts=y.extend(!0,this.defaultOptions,d),this.drawer=new m(t),this.molOpts=this.drawer.opts}draw(d,t,o="light",n=null,i="{reagents}",u="",p=!1){if(this.themeManager=new C(this.molOpts.themes,o),this.opts.weights.normalize){let B=-Number.MAX_SAFE_INTEGER,O=Number.MAX_SAFE_INTEGER;if(n.hasOwnProperty("reactants"))for(let M=0;M<n.reactants.length;M++)for(let H=0;H<n.reactants[M].length;H++)n.reactants[M][H]<O&&(O=n.reactants[M][H]),n.reactants[M][H]>B&&(B=n.reactants[M][H]);if(n.hasOwnProperty("reagents"))for(let M=0;M<n.reagents.length;M++)for(let H=0;H<n.reagents[M].length;H++)n.reagents[M][H]<O&&(O=n.reagents[M][H]),n.reagents[M][H]>B&&(B=n.reagents[M][H]);if(n.hasOwnProperty("products"))for(let M=0;M<n.products.length;M++)for(let H=0;H<n.products[M].length;H++)n.products[M][H]<O&&(O=n.products[M][H]),n.products[M][H]>B&&(B=n.products[M][H]);let T=Math.max(Math.abs(O),Math.abs(B));if(T===0&&(T=1),n.hasOwnProperty("reactants"))for(let M=0;M<n.reactants.length;M++)for(let H=0;H<n.reactants[M].length;H++)n.reactants[M][H]/=T;if(n.hasOwnProperty("reagents"))for(let M=0;M<n.reagents.length;M++)for(let H=0;H<n.reagents[M].length;H++)n.reagents[M][H]/=T;if(n.hasOwnProperty("products"))for(let M=0;M<n.products.length;M++)for(let H=0;H<n.products[M].length;H++)n.products[M][H]/=T}let b=null;for(t===null||t==="svg"?(b=document.createElementNS("http://www.w3.org/2000/svg","svg"),b.setAttribute("xmlns","http://www.w3.org/2000/svg"),b.setAttributeNS(null,"width","500"),b.setAttributeNS(null,"height","500")):typeof t=="string"||t instanceof String?b=document.getElementById(t):b=t;b.firstChild;)b.removeChild(b.firstChild);let v=[],s=0;for(var r=0;r<d.reactants.length;r++){r>0&&v.push({width:this.opts.plus.size*this.opts.scale,height:this.opts.plus.size*this.opts.scale,svg:this.getPlus()});let B=null;n&&n.hasOwnProperty("reactants")&&n.reactants.length>r&&(B=n.reactants[r]);let O=document.createElementNS("http://www.w3.org/2000/svg","svg");this.drawer.draw(d.reactants[r],O,o,B,p,[],this.opts.weights.normalize);let T={width:O.viewBox.baseVal.width*this.opts.scale,height:O.viewBox.baseVal.height*this.opts.scale,svg:O};v.push(T),T.height>s&&(s=T.height)}v.push({width:this.opts.arrow.length*this.opts.scale,height:this.opts.arrow.headSize*2*this.opts.scale,svg:this.getArrow()});let h="";for(var r=0;r<d.reagents.length;r++){r>0&&(h+=", ");let O=this.drawer.getMolecularFormula(d.reagents[r]);O in E&&(O=E[O]),h+=c.replaceNumbersWithSubscript(O)}i=i.replace("{reagents}",h);const g=c.writeText(i,this.themeManager,this.opts.fontSize*this.opts.scale,this.opts.fontFamily,this.opts.arrow.length*this.opts.scale);let w=(this.opts.arrow.length*this.opts.scale-g.width)/2;v.push({svg:g.svg,height:g.height,width:this.opts.arrow.length*this.opts.scale,offsetX:-(this.opts.arrow.length*this.opts.scale+this.opts.spacing)+w,offsetY:-(g.height/2)-this.opts.arrow.margin,position:"relative"});const I=c.writeText(u,this.themeManager,this.opts.fontSize*this.opts.scale,this.opts.fontFamily,this.opts.arrow.length*this.opts.scale);w=(this.opts.arrow.length*this.opts.scale-I.width)/2,v.push({svg:I.svg,height:I.height,width:this.opts.arrow.length*this.opts.scale,offsetX:-(this.opts.arrow.length*this.opts.scale+this.opts.spacing)+w,offsetY:I.height/2+this.opts.arrow.margin,position:"relative"});for(var r=0;r<d.products.length;r++){r>0&&v.push({width:this.opts.plus.size*this.opts.scale,height:this.opts.plus.size*this.opts.scale,svg:this.getPlus()});let O=null;n&&n.hasOwnProperty("products")&&n.products.length>r&&(O=n.products[r]);let T=document.createElementNS("http://www.w3.org/2000/svg","svg");this.drawer.draw(d.products[r],T,o,O,p,[],this.opts.weights.normalize);let M={width:T.viewBox.baseVal.width*this.opts.scale,height:T.viewBox.baseVal.height*this.opts.scale,svg:T};v.push(M),M.height>s&&(s=M.height)}let A=0;return v.forEach(B=>{var M,H;let O=(M=B.offsetX)!=null?M:0,T=(H=B.offsetY)!=null?H:0;B.svg.setAttributeNS(null,"x",Math.round(A+O)),B.svg.setAttributeNS(null,"y",Math.round((s-B.height)/2+T)),B.svg.setAttributeNS(null,"width",Math.round(B.width)),B.svg.setAttributeNS(null,"height",Math.round(B.height)),b.appendChild(B.svg),B.position!=="relative"&&(A+=Math.round(B.width+this.opts.spacing+O))}),b.setAttributeNS(null,"viewBox",`0 0 ${A} ${s}`),b.style.width=A+"px",b.style.height=s+"px",b}getPlus(){let d=this.opts.plus.size,t=this.opts.plus.thickness,o=document.createElementNS("http://www.w3.org/2000/svg","svg"),n=document.createElementNS("http://www.w3.org/2000/svg","rect"),i=document.createElementNS("http://www.w3.org/2000/svg","rect");return o.setAttributeNS(null,"id","plus"),n.setAttributeNS(null,"x",0),n.setAttributeNS(null,"y",d/2-t/2),n.setAttributeNS(null,"width",d),n.setAttributeNS(null,"height",t),n.setAttributeNS(null,"fill",this.themeManager.getColor("C")),i.setAttributeNS(null,"x",d/2-t/2),i.setAttributeNS(null,"y",0),i.setAttributeNS(null,"width",t),i.setAttributeNS(null,"height",d),i.setAttributeNS(null,"fill",this.themeManager.getColor("C")),o.appendChild(n),o.appendChild(i),o.setAttributeNS(null,"viewBox",`0 0 ${d} ${d}`),o}getArrowhead(){let d=this.opts.arrow.headSize,t=document.createElementNS("http://www.w3.org/2000/svg","marker"),o=document.createElementNS("http://www.w3.org/2000/svg","polygon");return t.setAttributeNS(null,"id","arrowhead"),t.setAttributeNS(null,"viewBox",`0 0 ${d} ${d}`),t.setAttributeNS(null,"markerUnits","userSpaceOnUse"),t.setAttributeNS(null,"markerWidth",d),t.setAttributeNS(null,"markerHeight",d),t.setAttributeNS(null,"refX",0),t.setAttributeNS(null,"refY",d/2),t.setAttributeNS(null,"orient","auto"),t.setAttributeNS(null,"fill",this.themeManager.getColor("C")),o.setAttributeNS(null,"points",`0 0, ${d} ${d/2}, 0 ${d}`),t.appendChild(o),t}getCDArrowhead(){let d=this.opts.arrow.headSize,t=d*(7/4.5),o=document.createElementNS("http://www.w3.org/2000/svg","marker"),n=document.createElementNS("http://www.w3.org/2000/svg","path");return o.setAttributeNS(null,"id","arrowhead"),o.setAttributeNS(null,"viewBox",`0 0 ${t} ${d}`),o.setAttributeNS(null,"markerUnits","userSpaceOnUse"),o.setAttributeNS(null,"markerWidth",t*2),o.setAttributeNS(null,"markerHeight",d*2),o.setAttributeNS(null,"refX",2.2),o.setAttributeNS(null,"refY",2.2),o.setAttributeNS(null,"orient","auto"),o.setAttributeNS(null,"fill",this.themeManager.getColor("C")),n.setAttributeNS(null,"style","fill-rule:nonzero;"),n.setAttributeNS(null,"d","m 0 0 l 7 2.25 l -7 2.25 c 0 0 0.735 -1.084 0.735 -2.28 c 0 -1.196 -0.735 -2.22 -0.735 -2.22 z"),o.appendChild(n),o}getArrow(){let d=this.opts.arrow.headSize,t=this.opts.arrow.length,o=document.createElementNS("http://www.w3.org/2000/svg","svg"),n=document.createElementNS("http://www.w3.org/2000/svg","defs"),i=document.createElementNS("http://www.w3.org/2000/svg","line");return n.appendChild(this.getCDArrowhead()),o.appendChild(n),o.setAttributeNS(null,"id","arrow"),i.setAttributeNS(null,"x1",0),i.setAttributeNS(null,"y1",-this.opts.arrow.thickness/2),i.setAttributeNS(null,"x2",t),i.setAttributeNS(null,"y2",-this.opts.arrow.thickness/2),i.setAttributeNS(null,"stroke-width",this.opts.arrow.thickness),i.setAttributeNS(null,"stroke",this.themeManager.getColor("C")),i.setAttributeNS(null,"marker-end","url(#arrowhead)"),o.appendChild(i),o.setAttributeNS(null,"viewBox",`0 ${-d/2} ${t+d*(7/4.5)} ${d}`),o}}ae.exports=l},50305:function(ae,X,D){const m=D(28388);class c{static parse(C){return new m(C)}}ae.exports=c},96421:function(ae,X,D){const m=D(16348),c=D(2614),y=D(16843),C=D(3333);class E{constructor(x){this.id=null,this.members=x,this.edges=[],this.insiders=[],this.neighbours=[],this.positioned=!1,this.center=new c(0,0),this.rings=[],this.isBridged=!1,this.isPartOfBridged=!1,this.isSpiro=!1,this.isFused=!1,this.centralAngle=0,this.canFlip=!0}clone(){let x=new E(this.members);return x.id=this.id,x.insiders=m.clone(this.insiders),x.neighbours=m.clone(this.neighbours),x.positioned=this.positioned,x.center=this.center.clone(),x.rings=m.clone(this.rings),x.isBridged=this.isBridged,x.isPartOfBridged=this.isPartOfBridged,x.isSpiro=this.isSpiro,x.isFused=this.isFused,x.centralAngle=this.centralAngle,x.canFlip=this.canFlip,x}getSize(){return this.members.length}getPolygon(x){let d=[];for(let t=0;t<this.members.length;t++)d.push(x[this.members[t]].position);return d}getAngle(){return Math.PI-this.centralAngle}eachMember(x,d,t,o){t=t||t===0?t:this.members[0];let n=t,i=0;for(;n!=null&&i<100;){let u=n;d(u),n=x[n].getNextInRing(x,this.id,o),o=u,n==t&&(n=null),i++}}getOrderedNeighbours(x){let d=Array(this.neighbours.length);for(let t=0;t<this.neighbours.length;t++){let o=C.getVertices(x,this.id,this.neighbours[t]);d[t]={n:o.length,neighbour:this.neighbours[t]}}return d.sort(function(t,o){return o.n-t.n}),d}isBenzeneLike(x){let d=this.getDoubleBondCount(x),t=this.members.length;return d===3&&t===6||d===2&&t===5}getDoubleBondCount(x){let d=0;for(let t=0;t<this.members.length;t++){let o=x[this.members[t]].value;(o.bondType==="="||o.branchBond==="=")&&d++}return d}contains(x){for(let d=0;d<this.members.length;d++)if(this.members[d]==x)return!0;return!1}}ae.exports=E},3333:function(ae,X,D){const m=D(16843),c=D(96421);class y{constructor(E,l){this.id=null,this.firstRingId=E.id,this.secondRingId=l.id,this.vertices=new Set;for(var x=0;x<E.members.length;x++){let d=E.members[x];for(let t=0;t<l.members.length;t++){let o=l.members[t];d===o&&this.addVertex(d)}}}addVertex(E){this.vertices.add(E)}updateOther(E,l){this.firstRingId===l?this.secondRingId=E:this.firstRingId=E}containsRing(E){return this.firstRingId===E||this.secondRingId===E}isBridge(E){if(this.vertices.size>2)return!0;for(let l of this.vertices)if(E[l].value.rings.length>2)return!0;return!1}static isBridge(E,l,x,d){let t=null;for(let o=0;o<E.length;o++)if(t=E[o],t.firstRingId===x&&t.secondRingId===d||t.firstRingId===d&&t.secondRingId===x)return t.isBridge(l);return!1}static getNeighbours(E,l){let x=[];for(let d=0;d<E.length;d++){let t=E[d];t.firstRingId===l?x.push(t.secondRingId):t.secondRingId===l&&x.push(t.firstRingId)}return x}static getVertices(E,l,x){for(let d=0;d<E.length;d++){let t=E[d];if(t.firstRingId===l&&t.secondRingId===x||t.firstRingId===x&&t.secondRingId===l)return[...t.vertices]}}}ae.exports=y},72473:function(ae,X,D){const m=D(84707);class c{static getRings(C,E=!1){let l=C.getComponentsAdjacencyMatrix();if(l.length===0)return null;let x=m.getConnectedComponents(l),d=Array();for(var t=0;t<x.length;t++){let i=x[t],u=C.getSubgraphAdjacencyMatrix([...i]),p=new Uint16Array(u.length),b=new Uint16Array(u.length);for(var o=0;o<u.length;o++){b[o]=0,p[o]=0;for(var n=0;n<u[o].length;n++)p[o]+=u[o][n]}let v=0;for(var o=0;o<u.length;o++)for(var n=o+1;n<u.length;n++)v+=u[o][n];let s=v-u.length+1,r=!0;for(var o=0;o<p.length;o++)p[o]!==3&&(r=!1);if(r&&(s=2+v-u.length),s===1){d.push([...i]);continue}E&&(s=999);let{d:h,pe:g,pe_prime:w}=c.getPathIncludedDistanceMatrices(u),I=c.getRingCandidates(h,g,w),A=c.getSSSR(I,h,u,g,w,p,b,s);for(var o=0;o<A.length;o++){let O=Array(A[o].size),T=0;for(let M of A[o])O[T++]=i[M];d.push(O)}}return d}static matrixToString(C){let E="";for(var l=0;l<C.length;l++){for(var x=0;x<C[l].length;x++)E+=C[l][x]+" ";E+=`
`}return E}static getPathIncludedDistanceMatrices(C){let E=C.length,l=Array(E),x=Array(E),d=Array(E);for(var t=0,o=0,n=0,i=E;i--;){l[i]=Array(E),x[i]=Array(E),d[i]=Array(E);for(var p=E;p--;)l[i][p]=i===p||C[i][p]===1?C[i][p]:Number.POSITIVE_INFINITY,l[i][p]===1?x[i][p]=[[[i,p]]]:x[i][p]=Array(),d[i][p]=Array()}for(var u=E,p;u--;)for(i=E;i--;)for(p=E;p--;){const b=l[i][p],v=l[i][u]+l[u][p];if(b>v){var t,o,n;if(b===v+1)for(d[i][p]=[x[i][p].length],t=x[i][p].length;t--;)for(d[i][p][t]=[x[i][p][t].length],o=x[i][p][t].length;o--;)for(d[i][p][t][o]=[x[i][p][t][o].length],n=x[i][p][t][o].length;n--;)d[i][p][t][o][n]=[x[i][p][t][o][0],x[i][p][t][o][1]];else d[i][p]=Array();for(l[i][p]=v,x[i][p]=[[]],t=x[i][u][0].length;t--;)x[i][p][0].push(x[i][u][0][t]);for(t=x[u][p][0].length;t--;)x[i][p][0].push(x[u][p][0][t])}else if(b===v){if(x[i][u].length&&x[u][p].length){var t;if(x[i][p].length){let r=Array();for(t=x[i][u][0].length;t--;)r.push(x[i][u][0][t]);for(t=x[u][p][0].length;t--;)r.push(x[u][p][0][t]);x[i][p].push(r)}else{let r=Array();for(t=x[i][u][0].length;t--;)r.push(x[i][u][0][t]);for(t=x[u][p][0].length;t--;)r.push(x[u][p][0][t]);x[i][p][0]=r}}}else if(b===v-1){var t;if(d[i][p].length){let r=Array();for(t=x[i][u][0].length;t--;)r.push(x[i][u][0][t]);for(t=x[u][p][0].length;t--;)r.push(x[u][p][0][t]);d[i][p].push(r)}else{let r=Array();for(t=x[i][u][0].length;t--;)r.push(x[i][u][0][t]);for(t=x[u][p][0].length;t--;)r.push(x[u][p][0][t]);d[i][p][0]=r}}}return{d:l,pe:x,pe_prime:d}}static getRingCandidates(C,E,l){let x=C.length,d=Array(),t=0;for(let o=0;o<x;o++)for(let n=0;n<x;n++)C[o][n]===0||E[o][n].length===1&&l[o][n]===0||(l[o][n].length!==0?t=2*(C[o][n]+.5):t=2*C[o][n],t!==1/0&&d.push([t,E[o][n],l[o][n]]));return d.sort(function(o,n){return o[0]-n[0]}),d}static getSSSR(C,E,l,x,d,t,o,n){let i=Array(),u=Array();for(let b=0;b<C.length;b++)if(C[b][0]%2!==0)for(let v=0;v<C[b][2].length;v++){let s=C[b][1][0].concat(C[b][2][v]);for(var p=0;p<s.length;p++)s[p][0].constructor===Array&&(s[p]=s[p][0]);let r=c.bondsToAtoms(s);if(c.getBondCount(r,l)===r.size&&!c.pathSetsContain(i,r,s,u,t,o)&&(i.push(r),u=u.concat(s)),i.length>n)return i}else for(let v=0;v<C[b][1].length-1;v++){let s=C[b][1][v].concat(C[b][1][v+1]);for(var p=0;p<s.length;p++)s[p][0].constructor===Array&&(s[p]=s[p][0]);let r=c.bondsToAtoms(s);if(c.getBondCount(r,l)===r.size&&!c.pathSetsContain(i,r,s,u,t,o)&&(i.push(r),u=u.concat(s)),i.length>n)return i}return i}static getEdgeCount(C){let E=0,l=C.length;for(var x=l-1;x--;)for(var d=l;d--;)C[x][d]===1&&E++;return E}static getEdgeList(C){let E=C.length,l=Array();for(var x=E-1;x--;)for(var d=E;d--;)C[x][d]===1&&l.push([x,d]);return l}static bondsToAtoms(C){let E=new Set;for(var l=C.length;l--;)E.add(C[l][0]),E.add(C[l][1]);return E}static getBondCount(C,E){let l=0;for(let x of C)for(let d of C)x!==d&&(l+=E[x][d]);return l/2}static pathSetsContain(C,E,l,x,d,t){for(var o=C.length;o--;){if(c.isSupersetOf(E,C[o]))return!0;if(C[o].size===E.size&&c.areSetsEqual(C[o],E))return!0}let n=0,i=!1;for(o=l.length;o--;)for(var u=x.length;u--;)(l[o][0]===x[u][0]&&l[o][1]===x[u][1]||l[o][1]===x[u][0]&&l[o][0]===x[u][1])&&n++,n===l.length&&(i=!0);let p=!1;if(i){for(let b of E)if(t[b]<d[b]){p=!0;break}}if(i&&!p)return!0;for(let b of E)t[b]++;return!1}static areSetsEqual(C,E){if(C.size!==E.size)return!1;for(let l of C)if(!E.has(l))return!1;return!0}static isSupersetOf(C,E){for(var l of E)if(!C.has(l))return!1;return!0}}ae.exports=c},62064:function(ae,X,D){const m=D(14881),c=D(50019),y=D(50305),C=D(1479),E=D(79350),l=D(33433),x=D(38207);class d{constructor(o={},n={}){this.drawer=new C(o),this.reactionDrawer=new E(n,JSON.parse(JSON.stringify(this.drawer.opts)))}static apply(o={},n={},i="data-smiles",u="light",p=null,b=null){new d(o,n).apply(i,u,p,b)}apply(o="data-smiles",n="light",i=null,u=null){document.querySelectorAll(`[${o}]`).forEach(b=>{let v=b.getAttribute(o);if(v===null)throw Error("No SMILES provided.");let s=n,r=null;if(b.hasAttribute("data-smiles-theme")&&(s=b.getAttribute("data-smiles-theme")),b.hasAttribute("data-smiles-weights")&&(r=b.getAttribute("data-smiles-weights").split(",").map(parseFloat)),(b.hasAttribute("data-smiles-reactant-weights")||b.hasAttribute("data-smiles-reagent-weights")||b.hasAttribute("data-smiles-product-weights"))&&(r={reactants:[],reagents:[],products:[]},b.hasAttribute("data-smiles-reactant-weights")&&(r.reactants=b.getAttribute("data-smiles-reactant-weights").split(";").map(h=>h.split(",").map(parseFloat))),b.hasAttribute("data-smiles-reagent-weights")&&(r.reagents=b.getAttribute("data-smiles-reagent-weights").split(";").map(h=>h.split(",").map(parseFloat))),b.hasAttribute("data-smiles-product-weights")&&(r.products=b.getAttribute("data-smiles-product-weights").split(";").map(h=>h.split(",").map(parseFloat)))),b.hasAttribute("data-smiles-options")||b.hasAttribute("data-smiles-reaction-options")){let h={};b.hasAttribute("data-smiles-options")&&(h=JSON.parse(b.getAttribute("data-smiles-options").replaceAll("'",'"')));let g={};b.hasAttribute("data-smiles-reaction-options")&&(g=JSON.parse(b.getAttribute("data-smiles-reaction-options").replaceAll("'",'"'))),new d(h,g).draw(v,b,s,i,u,r)}else this.draw(v,b,s,i,u,r)})}draw(o,n,i="light",u=null,p=null,b=null){let v=[];[o,...v]=o.split(" ");let s=v.join(" "),r={};if(s.includes("__")){let g=s.substring(s.indexOf("__")+2,s.lastIndexOf("__"));r=JSON.parse(g.replaceAll("'",'"'))}let h={textAboveArrow:"{reagents}",textBelowArrow:""};if(r=x.extend(!0,h,r),o.includes(">"))try{this.drawReaction(o,n,i,r,b,u)}catch(g){p?p(g):console.error(g)}else try{this.drawMolecule(o,n,i,b,u)}catch(g){p?p(g):console.error(g)}}drawMolecule(o,n,i,u,p){let b=c.parse(o);if(n===null||n==="svg"){let v=this.drawer.draw(b,null,i,u),s=this.getDimensions(v);v.setAttributeNS(null,"width",""+s.w),v.setAttributeNS(null,"height",""+s.h),p&&p(v)}else if(n==="canvas"){let v=this.svgToCanvas(this.drawer.draw(b,null,i,u));p&&p(v)}else if(n==="img"){let v=this.svgToImg(this.drawer.draw(b,null,i,u));p&&p(v)}else n instanceof HTMLImageElement?(this.svgToImg(this.drawer.draw(b,null,i,u),n),p&&p(n)):n instanceof SVGElement?(this.drawer.draw(b,n,i,u),p&&p(n)):document.querySelectorAll(n).forEach(s=>{let r=s.nodeName.toLowerCase();r==="svg"?(this.drawer.draw(b,s,i,u),p&&p(s)):r==="canvas"?(this.svgToCanvas(this.drawer.draw(b,null,i,u),s),p&&p(s)):r==="img"&&(this.svgToImg(this.drawer.draw(b,null,i,u),s),p&&p(s))})}drawReaction(o,n,i,u,p,b){let v=y.parse(o);if(n===null||n==="svg"){let s=this.reactionDrawer.draw(v,null,i),r=this.getDimensions(s);s.setAttributeNS(null,"width",""+r.w),s.setAttributeNS(null,"height",""+r.h),b&&b(s)}else if(n==="canvas"){let s=this.svgToCanvas(this.reactionDrawer.draw(v,null,i,p,u.textAboveArrow,u.textBelowArrow));b&&b(s)}else if(n==="img"){let s=this.svgToImg(this.reactionDrawer.draw(v,null,i,p,u.textAboveArrow,u.textBelowArrow));b&&b(s)}else n instanceof HTMLImageElement?(this.svgToImg(this.reactionDrawer.draw(v,null,i,p,u.textAboveArrow,u.textBelowArrow),n),b&&b(n)):n instanceof SVGElement?(this.reactionDrawer.draw(v,n,i,p,u.textAboveArrow,u.textBelowArrow),b&&b(n)):document.querySelectorAll(n).forEach(r=>{let h=r.nodeName.toLowerCase();h==="svg"?(this.reactionDrawer.draw(v,r,i,p,u.textAboveArrow,u.textBelowArrow),this.reactionDrawer.opts.scale<=0&&(r.style.width=null,r.style.height=null),b&&b(r)):h==="canvas"?(this.svgToCanvas(this.reactionDrawer.draw(v,null,i,p,u.textAboveArrow,u.textBelowArrow),r),b&&b(r)):h==="img"&&(this.svgToImg(this.reactionDrawer.draw(v,null,i,p,u.textAboveArrow,u.textBelowArrow),r),b&&b(r))})}svgToCanvas(o,n=null){n===null&&(n=document.createElement("canvas"));let i=this.getDimensions(n,o);return l.svgToCanvas(o,n,i.w,i.h),n}svgToImg(o,n=null){n===null&&(n=document.createElement("img"));let i=this.getDimensions(n,o);return l.svgToImg(o,n,i.w,i.h),n}getDimensions(o,n=null){let i=this.drawer.opts.width,u=this.drawer.opts.height;return this.drawer.opts.scale<=0?(i===null&&(i=o.width),u===null&&(u=o.height),o.style.width!==""&&(i=parseInt(o.style.width)),o.style.height!==""&&(u=parseInt(o.style.height))):n&&(i=parseFloat(n.style.width),u=parseFloat(n.style.height)),{w:i,h:u}}}ae.exports=d},1479:function(ae,X,D){const m=D(16348),c=D(62427),y=D(46237),C=D(84707),E=D(30929),l=D(33433),x=D(16654),d=D(2614),t=D(15749);class o{constructor(i,u=!0){this.preprocessor=new y(i),this.opts=this.preprocessor.opts,this.clear=u,this.svgWrapper=null}draw(i,u,p="light",b=null,v=!1,s=[],r=!1){u===null||u==="svg"?(u=document.createElementNS("http://www.w3.org/2000/svg","svg"),u.setAttribute("xmlns","http://www.w3.org/2000/svg"),u.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),u.setAttributeNS(null,"width",this.opts.width),u.setAttributeNS(null,"height",this.opts.height)):u instanceof String&&(u=document.getElementById(u));let h={padding:this.opts.padding,compactDrawing:this.opts.compactDrawing};b!==null&&(this.opts.padding+=this.opts.weights.additionalPadding,this.opts.compactDrawing=!1);let g=this.preprocessor;return g.initDraw(i,p,v,s),v||(this.themeManager=new x(this.opts.themes,p),(this.svgWrapper===null||this.clear)&&(this.svgWrapper=new l(this.themeManager,u,this.opts,this.clear))),g.processGraph(),this.svgWrapper.determineDimensions(g.graph.vertices),this.drawAtomHighlights(g.opts.debug),this.drawEdges(g.opts.debug),this.drawVertices(g.opts.debug),b!==null&&this.drawWeights(b,r),g.opts.debug&&(console.log(g.graph),console.log(g.rings),console.log(g.ringConnections)),this.svgWrapper.constructSvg(),b!==null&&(this.opts.padding=h.padding,this.opts.compactDrawing=h.padding),u}drawCanvas(i,u,p="light",b=!1){let v=null;typeof u=="string"||u instanceof String?v=document.getElementById(u):v=u;let s=document.createElementNS("http://www.w3.org/2000/svg","svg");return s.setAttribute("xmlns","http://www.w3.org/2000/svg"),s.setAttributeNS(null,"viewBox","0 0 500 500"),s.setAttributeNS(null,"width","500"),s.setAttributeNS(null,"height","500"),s.setAttributeNS(null,"style","visibility: hidden: position: absolute; left: -1000px"),document.body.appendChild(s),this.svgDrawer.draw(i,s,p,b),this.svgDrawer.svgWrapper.toCanvas(v,this.svgDrawer.opts.width,this.svgDrawer.opts.height),document.body.removeChild(s),u}drawAromaticityRing(i){this.svgWrapper.drawRing(i.center.x,i.center.y,i.getSize())}drawEdges(i){let u=this.preprocessor,p=u.graph,b=u.rings,v=Array(this.preprocessor.graph.edges.length);if(v.fill(!1),p.traverseBF(0,r=>{let h=p.getEdges(r.id);for(var g=0;g<h.length;g++){let w=h[g];v[w]||(v[w]=!0,this.drawEdge(w,i))}}),!this.bridgedRing)for(var s=0;s<b.length;s++){let r=b[s];u.isRingAromatic(r)&&this.drawAromaticityRing(r)}}drawEdge(i,u){let p=this.preprocessor,b=p.opts,v=this.svgWrapper,s=p.graph.edges[i],r=p.graph.vertices[s.sourceId],h=p.graph.vertices[s.targetId],g=r.value.element,w=h.value.element;if((!r.value.isDrawn||!h.value.isDrawn)&&p.opts.atomVisualization==="default")return;let I=r.position,A=h.position,B=p.getEdgeNormals(s),O=m.clone(B);if(O[0].multiplyScalar(10).add(I),O[1].multiplyScalar(10).add(I),s.bondType==="="||p.getRingbondType(r,h)==="="||s.isPartOfAromaticRing&&p.bridgedRing){let T=p.areVerticesInSameRing(r,h),M=p.chooseSide(r,h,O);if(T){let U=p.getLargestOrAromaticCommonRing(r,h).center;B[0].multiplyScalar(b.bondSpacing),B[1].multiplyScalar(b.bondSpacing);let $=null;U.sameSideAs(r.position,h.position,d.add(I,B[0]))?$=new E(d.add(I,B[0]),d.add(A,B[0]),g,w):$=new E(d.add(I,B[1]),d.add(A,B[1]),g,w),$.shorten(b.bondLength-b.shortBondLength*b.bondLength),s.isPartOfAromaticRing?v.drawLine($,!0):v.drawLine($),v.drawLine(new E(I,A,g,w))}else if(s.center||r.isTerminal()&&h.isTerminal()||M.anCount==0&&M.bnCount>1||M.bnCount==0&&M.anCount>1){this.multiplyNormals(B,b.halfBondSpacing);let H=new E(d.add(I,B[0]),d.add(A,B[0]),g,w),U=new E(d.add(I,B[1]),d.add(A,B[1]),g,w);v.drawLine(H),v.drawLine(U)}else if(M.sideCount[0]>M.sideCount[1]||M.totalSideCount[0]>M.totalSideCount[1]){this.multiplyNormals(B,b.bondSpacing);let H=new E(d.add(I,B[0]),d.add(A,B[0]),g,w);H.shorten(b.bondLength-b.shortBondLength*b.bondLength),v.drawLine(H),v.drawLine(new E(I,A,g,w))}else if(M.sideCount[0]<M.sideCount[1]||M.totalSideCount[0]<=M.totalSideCount[1]){this.multiplyNormals(B,b.bondSpacing);let H=new E(d.add(I,B[1]),d.add(A,B[1]),g,w);H.shorten(b.bondLength-b.shortBondLength*b.bondLength),v.drawLine(H),v.drawLine(new E(I,A,g,w))}}else if(s.bondType==="#"){B[0].multiplyScalar(b.bondSpacing/1.5),B[1].multiplyScalar(b.bondSpacing/1.5);let T=new E(d.add(I,B[0]),d.add(A,B[0]),g,w),M=new E(d.add(I,B[1]),d.add(A,B[1]),g,w);v.drawLine(T),v.drawLine(M),v.drawLine(new E(I,A,g,w))}else if(s.bondType!=="."){let T=r.value.isStereoCenter,M=h.value.isStereoCenter;s.wedge==="up"?v.drawWedge(new E(I,A,g,w,T,M)):s.wedge==="down"?v.drawDashedWedge(new E(I,A,g,w,T,M)):v.drawLine(new E(I,A,g,w,T,M))}if(u){let T=d.midpoint(I,A);v.drawDebugText(T.x,T.y,"e: "+i)}}drawAtomHighlights(i){let u=this.preprocessor,p=u.opts,b=u.graph,v=u.rings,s=this.svgWrapper;for(var r=0;r<b.vertices.length;r++){let g=b.vertices[r],w=g.value;for(var h=0;h<u.highlight_atoms.length;h++){let I=u.highlight_atoms[h];w.class===I[0]&&s.drawAtomHighlight(g.position.x,g.position.y,I[1])}}}drawVertices(i){let u=this.preprocessor,p=u.opts,b=u.graph,v=u.rings,s=this.svgWrapper;for(var r=b.vertices.length,r=0;r<b.vertices.length;r++){let g=b.vertices[r],w=g.value,I=0,A=0,B=g.value.bondCount,O=w.element,T=c.maxBonds[O]-B,M=g.getTextDirection(b.vertices,w.hasAttachedPseudoElements),H=p.terminalCarbons||O!=="C"||w.hasAttachedPseudoElements?g.isTerminal():!1,U=w.element==="C";if(b.vertices.length<3&&(U=!1),w.element==="N"&&w.isPartOfAromaticRing&&(T=0),w.bracket&&(T=w.bracket.hcount,I=w.bracket.charge,A=w.bracket.isotope),p.atomVisualization==="allballs")s.drawBall(g.position.x,g.position.y,O);else if(w.isDrawn&&(!U||w.drawExplicit||H||w.hasAttachedPseudoElements)||b.vertices.length===1)if(p.atomVisualization==="default"){let $=w.getAttachedPseudoElements();w.hasAttachedPseudoElements&&b.vertices.length===Object.keys($).length+1&&(M="right"),s.drawText(g.position.x,g.position.y,O,T,M,H,I,A,b.vertices.length,$)}else p.atomVisualization==="balls"&&s.drawBall(g.position.x,g.position.y,O);else if(g.getNeighbourCount()===2&&g.forcePositioned==!0){let $=b.vertices[g.neighbours[0]].position,Y=b.vertices[g.neighbours[1]].position,q=d.threePointangle(g.position,$,Y);Math.abs(Math.PI-q)<.1&&s.drawPoint(g.position.x,g.position.y,O)}if(i){let $="v: "+g.id+" "+m.print(w.ringbonds);s.drawDebugText(g.position.x,g.position.y,$)}}if(p.debug)for(var r=0;r<v.length;r++){let g=v[r].center;s.drawDebugPoint(g.x,g.y,"r: "+v[r].id)}}drawWeights(i,u){if(i.every(v=>v===0))return;if(i.length!==this.preprocessor.graph.atomIdxToVertexId.length)throw new Error("The number of weights supplied must be equal to the number of (heavy) atoms in the molecule.");let p=[];for(const v of this.preprocessor.graph.atomIdxToVertexId){let s=this.preprocessor.graph.vertices[v];p.push(new d(s.position.x-this.svgWrapper.minX,s.position.y-this.svgWrapper.minY))}let b=new t(p,i,this.svgWrapper.drawingWidth,this.svgWrapper.drawingHeight,this.opts.weights.sigma,this.opts.weights.interval,this.opts.weights.colormap,this.opts.weights.opacity,u);b.draw(),this.svgWrapper.addLayer(b.getSVG())}getTotalOverlapScore(){return this.preprocessor.getTotalOverlapScore()}getMolecularFormula(i=null){return this.preprocessor.getMolecularFormula(i)}multiplyNormals(i,u){i[0].multiplyScalar(u),i[1].multiplyScalar(u)}}ae.exports=o},33433:function(ae,X,D){const{getChargeText:m}=D(20537),c=D(30929),y=D(2614),C=D(75474);function E(x){for(var d="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",o=t.length,n=0;n<x;n++)d+=t.charAt(Math.floor(Math.random()*o));return d}class l{constructor(d,t,o,n=!0){if(typeof t=="string"||t instanceof String?this.svg=document.getElementById(t):this.svg=t,this.container=null,this.opts=o,this.uid=E(5),this.gradientId=0,this.backgroundItems=[],this.paths=[],this.vertices=[],this.gradients=[],this.highlights=[],this.drawingWidth=0,this.drawingHeight=0,this.halfBondThickness=this.opts.bondThickness/2,this.themeManager=d,this.maskElements=[],this.maxX=-Number.MAX_VALUE,this.maxY=-Number.MAX_VALUE,this.minX=Number.MAX_VALUE,this.minY=Number.MAX_VALUE,n)for(;this.svg.firstChild;)this.svg.removeChild(this.svg.firstChild);this.style=document.createElementNS("http://www.w3.org/2000/svg","style"),this.style.appendChild(document.createTextNode(`
                .element {
                    font: ${this.opts.fontSizeLarge}pt ${this.opts.fontFamily};
                }
                .sub {
                    font: ${this.opts.fontSizeSmall}pt ${this.opts.fontFamily};
                }
            `)),this.svg?this.svg.appendChild(this.style):(this.container=document.createElementNS("http://www.w3.org/2000/svg","g"),container.appendChild(this.style))}constructSvg(){let d=document.createElementNS("http://www.w3.org/2000/svg","defs"),t=document.createElementNS("http://www.w3.org/2000/svg","mask"),o=document.createElementNS("http://www.w3.org/2000/svg","g"),n=document.createElementNS("http://www.w3.org/2000/svg","g"),i=document.createElementNS("http://www.w3.org/2000/svg","g"),u=document.createElementNS("http://www.w3.org/2000/svg","g"),p=this.paths,b=document.createElementNS("http://www.w3.org/2000/svg","rect");b.setAttributeNS(null,"x",this.minX),b.setAttributeNS(null,"y",this.minY),b.setAttributeNS(null,"width",this.maxX-this.minX),b.setAttributeNS(null,"height",this.maxY-this.minY),b.setAttributeNS(null,"fill","white"),t.appendChild(b),t.setAttributeNS(null,"id",this.uid+"-text-mask");for(let v of p)i.appendChild(v);for(let v of this.backgroundItems)o.appendChild(v);for(let v of this.highlights)n.appendChild(v);for(let v of this.vertices)u.appendChild(v);for(let v of this.maskElements)t.appendChild(v);for(let v of this.gradients)d.appendChild(v);if(i.setAttributeNS(null,"mask","url(#"+this.uid+"-text-mask)"),this.updateViewbox(this.opts.scale),o.setAttributeNS(null,"style",`transform: translateX(${this.minX}px) translateY(${this.minY}px)`),this.svg)this.svg.appendChild(d),this.svg.appendChild(t),this.svg.appendChild(o),this.svg.appendChild(n),this.svg.appendChild(i),this.svg.appendChild(u);else return this.container.appendChild(d),this.container.appendChild(t),this.container.appendChild(o),this.container.appendChild(i),this.container.appendChild(u),this.container}addLayer(d){this.backgroundItems.push(d.firstChild)}createGradient(d){let t=document.createElementNS("http://www.w3.org/2000/svg","linearGradient"),o=this.uid+`-line-${this.gradientId++}`,n=d.getLeftVector(),i=d.getRightVector(),u=n.x,p=n.y,b=i.x,v=i.y;t.setAttributeNS(null,"id",o),t.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),t.setAttributeNS(null,"x1",u),t.setAttributeNS(null,"y1",p),t.setAttributeNS(null,"x2",b),t.setAttributeNS(null,"y2",v);let s=document.createElementNS("http://www.w3.org/2000/svg","stop");s.setAttributeNS(null,"stop-color",this.themeManager.getColor(d.getLeftElement())||this.themeManager.getColor("C")),s.setAttributeNS(null,"offset","20%");let r=document.createElementNS("http://www.w3.org/2000/svg","stop");return r.setAttributeNS(null,"stop-color",this.themeManager.getColor(d.getRightElement()||this.themeManager.getColor("C"))),r.setAttributeNS(null,"offset","100%"),t.appendChild(s),t.appendChild(r),this.gradients.push(t),o}createSubSuperScripts(d,t){let o=document.createElementNS("http://www.w3.org/2000/svg","tspan");return o.setAttributeNS(null,"baseline-shift",t),o.appendChild(document.createTextNode(d)),o.setAttributeNS(null,"class","sub"),o}static createUnicodeCharge(d){return d===1?"\u207A":d===-1?"\u207B":d>1?l.createUnicodeSuperscript(d)+"\u207A":d<-1?l.createUnicodeSuperscript(d)+"\u207B":""}determineDimensions(d){for(var t=0;t<d.length;t++){if(!d[t].value.isDrawn)continue;let n=d[t].position;this.maxX<n.x&&(this.maxX=n.x),this.maxY<n.y&&(this.maxY=n.y),this.minX>n.x&&(this.minX=n.x),this.minY>n.y&&(this.minY=n.y)}let o=this.opts.padding;this.maxX+=o,this.maxY+=o,this.minX-=o,this.minY-=o,this.drawingWidth=this.maxX-this.minX,this.drawingHeight=this.maxY-this.minY}updateViewbox(d){let t=this.minX,o=this.minY,n=this.maxX-this.minX,i=this.maxY-this.minY;if(d<=0)if(n>i){let u=n-i;i=n,o-=u/2}else{let u=i-n;n=i,t-=u/2}else this.svg&&(this.svg.style.width=d*n+"px",this.svg.style.height=d*i+"px");this.svg.setAttributeNS(null,"viewBox",`${t} ${o} ${n} ${i}`)}drawBall(d,t,o){let n=this.opts.bondLength/4.5;d-n<this.minX&&(this.minX=d-n),d+n>this.maxX&&(this.maxX=d+n),t-n<this.minY&&(this.minY=t-n),t+n>this.maxY&&(this.maxY=t+n);let i=document.createElementNS("http://www.w3.org/2000/svg","circle");i.setAttributeNS(null,"cx",d),i.setAttributeNS(null,"cy",t),i.setAttributeNS(null,"r",n),i.setAttributeNS(null,"fill",this.themeManager.getColor(o)),this.vertices.push(i)}drawWedge(d){let t=d.getLeftVector().clone(),o=d.getRightVector().clone(),n=y.normals(t,o);n[0].normalize(),n[1].normalize();let i=d.getRightChiral(),u=t,p=o;i&&(u=o,p=t);let b=y.add(u,y.multiplyScalar(n[0],this.halfBondThickness)),v=y.add(p,y.multiplyScalar(n[0],3+this.opts.fontSizeLarge/4)),s=y.add(p,y.multiplyScalar(n[1],3+this.opts.fontSizeLarge/4)),r=y.add(u,y.multiplyScalar(n[1],this.halfBondThickness)),h=document.createElementNS("http://www.w3.org/2000/svg","polygon"),g=this.createGradient(d,t.x,t.y,o.x,o.y);h.setAttributeNS(null,"points",`${b.x},${b.y} ${v.x},${v.y} ${s.x},${s.y} ${r.x},${r.y}`),h.setAttributeNS(null,"fill",`url('#${g}')`),this.paths.push(h)}drawAtomHighlight(d,t,o="#03fc9d"){let n=document.createElementNS("http://www.w3.org/2000/svg","circle");n.setAttributeNS(null,"cx",d),n.setAttributeNS(null,"cy",t),n.setAttributeNS(null,"r",this.opts.bondLength/3),n.setAttributeNS(null,"fill",o),this.highlights.push(n)}drawDashedWedge(d){if(isNaN(d.from.x)||isNaN(d.from.y)||isNaN(d.to.x)||isNaN(d.to.y))return;let t=d.getLeftVector().clone(),o=d.getRightVector().clone(),n=y.normals(t,o);n[0].normalize(),n[1].normalize();let i=d.getRightChiral(),u,p;i?(u=o,p=t):(u=t,p=o);let b=y.subtract(p,u).normalize(),v=d.getLength(),s=1.25/(v/(this.opts.bondLength/10)),r=!1,h=this.createGradient(d);for(let g=0;g<1;g+=s){let w=y.multiplyScalar(b,g*v),I=y.add(u,w),A=this.opts.fontSizeLarge/2*g,B=y.multiplyScalar(n[0],A);I.subtract(B);let O=I.clone();O.add(y.multiplyScalar(B,2)),this.drawLine(new c(I,O),null,h)}}drawDebugPoint(d,t,o="",n="#f00"){let i=document.createElementNS("http://www.w3.org/2000/svg","circle");i.setAttributeNS(null,"cx",d),i.setAttributeNS(null,"cy",t),i.setAttributeNS(null,"r","2"),i.setAttributeNS(null,"fill","#f00"),this.vertices.push(i),this.drawDebugText(d,t,o)}drawDebugText(d,t,o){let n=document.createElementNS("http://www.w3.org/2000/svg","text");n.setAttributeNS(null,"x",d),n.setAttributeNS(null,"y",t),n.setAttributeNS(null,"class","debug"),n.setAttributeNS(null,"fill","#ff0000"),n.setAttributeNS(null,"style",`
                font: 5px Droid Sans, sans-serif;
            `),n.appendChild(document.createTextNode(o)),this.vertices.push(n)}drawRing(d,t,o){let n=document.createElementNS("http://www.w3.org/2000/svg","circle"),i=C.apothemFromSideLength(this.opts.bondLength,o);n.setAttributeNS(null,"cx",d),n.setAttributeNS(null,"cy",t),n.setAttributeNS(null,"r",i-this.opts.bondSpacing),n.setAttributeNS(null,"stroke",this.themeManager.getColor("C")),n.setAttributeNS(null,"stroke-width",this.opts.bondThickness),n.setAttributeNS(null,"fill","none"),this.paths.push(n)}drawLine(d,t=!1,o=null,n="round"){let i=this.opts,u=[["stroke-width",this.opts.bondThickness],["stroke-linecap",n],["stroke-dasharray",t?"5, 5":"none"]],p=d.getLeftVector(),b=d.getRightVector(),v=p.x,s=p.y,r=b.x,h=b.y,g=u.map(I=>I.join(":")).join(";"),w=document.createElementNS("http://www.w3.org/2000/svg","line");w.setAttributeNS(null,"x1",v),w.setAttributeNS(null,"y1",s),w.setAttributeNS(null,"x2",r),w.setAttributeNS(null,"y2",h),w.setAttributeNS(null,"style",g),this.paths.push(w),o==null&&(o=this.createGradient(d,v,s,r,h)),w.setAttributeNS(null,"stroke",`url('#${o}')`)}drawPoint(d,t,o){let n=.75;d-n<this.minX&&(this.minX=d-n),d+n>this.maxX&&(this.maxX=d+n),t-n<this.minY&&(this.minY=t-n),t+n>this.maxY&&(this.maxY=t+n);let i=document.createElementNS("http://www.w3.org/2000/svg","circle");i.setAttributeNS(null,"cx",d),i.setAttributeNS(null,"cy",t),i.setAttributeNS(null,"r","1.5"),i.setAttributeNS(null,"fill","black"),this.maskElements.push(i);let u=document.createElementNS("http://www.w3.org/2000/svg","circle");u.setAttributeNS(null,"cx",d),u.setAttributeNS(null,"cy",t),u.setAttributeNS(null,"r",n),u.setAttributeNS(null,"fill",this.themeManager.getColor(o)),this.vertices.push(u)}drawText(d,t,o,n,i,u,p,b,v,s={}){let r=[],h=o;p!==0&&p!==null&&(h+=l.createUnicodeCharge(p)),b!==0&&b!==null&&(h=l.createUnicodeSuperscript(b)+h),r.push([h,o]),n===1?r.push(["H","H"]):n>1&&r.push(["H"+l.createUnicodeSubscript(n),"H"]),p===1&&o==="N"&&s.hasOwnProperty("0O")&&s.hasOwnProperty("0O-1")&&(s={"0O":{element:"O",count:2,hydrogenCount:0,previousElement:"C",charge:""}},p=0);for(let g in s){if(!s.hasOwnProperty(g))continue;let w=s[g],I=w.element;w.count>1&&(I+=l.createUnicodeSubscript(w.count)),w.charge!==""&&(I+=l.createUnicodeCharge(p)),r.push([I,w.element]),w.hydrogenCount===1?r.push(["H","H"]):w.hydrogenCount>1&&r.push(["H"+l.createUnicodeSubscript(w.hydrogenCount),"H"])}this.write(r,i,d,t,v===1)}write(d,t,o,n,i){let u=l.measureText(d[0][1],this.opts.fontSizeLarge,this.opts.fontFamily);t==="left"&&d[0][0]!==d[0][1]&&(u.width*=2),i?(o+u.width*d.length>this.maxX&&(this.maxX=o+u.width*d.length),o-u.width/2<this.minX&&(this.minX=o-u.width/2),n-u.height<this.minY&&(this.minY=n-u.height),n+u.height>this.maxY&&(this.maxY=n+u.height)):(t!=="right"?(o+u.width*d.length>this.maxX&&(this.maxX=o+u.width*d.length),o-u.width*d.length<this.minX&&(this.minX=o-u.width*d.length)):t!=="left"&&(o+u.width*d.length>this.maxX&&(this.maxX=o+u.width*d.length),o-u.width/2<this.minX&&(this.minX=o-u.width/2)),n-u.height<this.minY&&(this.minY=n-u.height),n+u.height>this.maxY&&(this.maxY=n+u.height),t==="down"&&n+.8*u.height*d.length>this.maxY&&(this.maxY=n+.8*u.height*d.length),t==="up"&&n-.8*u.height*d.length<this.minY&&(this.minY=n-.8*u.height*d.length));let p=o,b=n,v=document.createElementNS("http://www.w3.org/2000/svg","text");v.setAttributeNS(null,"class","element");let s=document.createElementNS("http://www.w3.org/2000/svg","g");v.setAttributeNS(null,"fill","#ffffff"),t==="left"&&(d=d.reverse()),(t==="right"||t==="down"||t==="up")&&(o-=u.width/2),t==="left"&&(o+=u.width/2),d.forEach((g,w)=>{const I=g[0],A=g[1];let B=document.createElementNS("http://www.w3.org/2000/svg","tspan");B.setAttributeNS(null,"fill",this.themeManager.getColor(A)),B.textContent=I,(t==="up"||t==="down")&&(B.setAttributeNS(null,"x","0px"),t==="up"?B.setAttributeNS(null,"y",`-${.9*w}em`):B.setAttributeNS(null,"y",`${.9*w}em`)),v.appendChild(B)}),v.setAttributeNS(null,"data-direction",t),t==="left"||t==="right"?(v.setAttributeNS(null,"dominant-baseline","alphabetic"),v.setAttributeNS(null,"y","0.36em")):v.setAttributeNS(null,"dominant-baseline","central"),t==="left"&&v.setAttributeNS(null,"text-anchor","end"),s.appendChild(v),s.setAttributeNS(null,"style",`transform: translateX(${o}px) translateY(${n}px)`);let r=this.opts.fontSizeLarge*.75;d[0][1].length>1&&(r=this.opts.fontSizeLarge*1.1);let h=document.createElementNS("http://www.w3.org/2000/svg","circle");h.setAttributeNS(null,"cx",p),h.setAttributeNS(null,"cy",b),h.setAttributeNS(null,"r",r),h.setAttributeNS(null,"fill","black"),this.maskElements.push(h),this.vertices.push(s)}toCanvas(d,t,o){(typeof d=="string"||d instanceof String)&&(d=document.getElementById(d));let n=new Image;n.onload=function(){d.width=t,d.height=o,d.getContext("2d").drawImage(n,0,0,t,o)},n.src="data:image/svg+xml;charset-utf-8,"+encodeURIComponent(this.svg.outerHTML)}static createUnicodeSubscript(d){let t="";return d.toString().split("").forEach(o=>{t+=["\u2080","\u2081","\u2082","\u2083","\u2084","\u2085","\u2086","\u2087","\u2088","\u2089"][parseInt(o)]}),t}static createUnicodeSuperscript(d){let t="";return d.toString().split("").forEach(o=>{parseInt(o)&&(t+=["\u2070","\xB9","\xB2","\xB3","\u2074","\u2075","\u2076","\u2077","\u2078","\u2079"][parseInt(o)])}),t}static replaceNumbersWithSubscript(d){let t={0:"\u2080",1:"\u2081",2:"\u2082",3:"\u2083",4:"\u2084",5:"\u2085",6:"\u2086",7:"\u2087",8:"\u2088",9:"\u2089"};for(const[o,n]of Object.entries(t))d=d.replaceAll(o,n);return d}static measureText(d,t,o,n=.9){const u=document.createElement("canvas").getContext("2d");u.font=`${t}pt ${o}`;let p=u.measureText(d),b=Math.abs(p.actualBoundingBoxLeft)+Math.abs(p.actualBoundingBoxRight);return{width:p.width>b?p.width:b,height:(Math.abs(p.actualBoundingBoxAscent)+Math.abs(p.actualBoundingBoxAscent))*n}}static svgToCanvas(d,t,o,n,i=null){d.setAttributeNS(null,"width",o),d.setAttributeNS(null,"height",n);let u=new Image;return u.onload=function(){t.width=o,t.height=n;let p=t.getContext("2d");p.imageSmoothingEnabled=!1,p.drawImage(u,0,0,o,n),i&&i(t)},u.onerror=function(p){console.log(p)},u.src="data:image/svg+xml;charset-utf-8,"+encodeURIComponent(d.outerHTML),t}static svgToImg(d,t,o,n){let i=document.createElement("canvas");this.svgToCanvas(d,i,o,n,u=>{t.src=i.toDataURL("image/png")})}static writeText(d,t,o,n,i=Number.MAX_SAFE_INTEGER){let u=document.createElementNS("http://www.w3.org/2000/svg","svg"),p=document.createElementNS("http://www.w3.org/2000/svg","style");p.appendChild(document.createTextNode(`
        .text {
            font: ${o}pt ${n};
            dominant-baseline: ideographic;
        }
    `)),u.appendChild(p);let b=document.createElementNS("http://www.w3.org/2000/svg","text");b.setAttributeNS(null,"class","text");let v=0,s=0,r=[];return d.split(`
`).forEach(h=>{let g=l.measureText(h,o,n,1);if(g.width>=i){let w=0,I=0,A=h.split(" "),B=0;for(let O=0;O<A.length;O++){let T=l.measureText(A[O],o,n,1);w+T.width>i&&(r.push({text:A.slice(B,O).join(" "),width:w,height:I}),w=0,I=0,B=O),T.height>I&&(I=T.height),w+=T.width}B<A.length&&r.push({text:A.slice(B,A.length).join(" "),width:w,height:I})}else r.push({text:h,width:g.width,height:g.height})}),r.forEach((h,g)=>{s+=h.height;let w=document.createElementNS("http://www.w3.org/2000/svg","tspan");w.setAttributeNS(null,"fill",t.getColor("C")),w.textContent=h.text,w.setAttributeNS(null,"x","0px"),w.setAttributeNS(null,"y",`${s}px`),b.appendChild(w),h.width>v&&(v=h.width)}),u.appendChild(b),{svg:u,width:v,height:s}}}ae.exports=l},16654:function(ae){class X{constructor(m,c){this.colors=m,this.theme=this.colors[c]}getColor(m){return m&&(m=m.toUpperCase(),m in this.theme)?this.theme[m]:this.theme.C}setTheme(m){this.colors.hasOwnProperty(m)&&(this.theme=this.colors[m])}}ae.exports=X},20537:function(ae){function X(D){return D===1?"+":D===2?"2+":D===-1?"-":D===-2?"2-":""}ae.exports={getChargeText:X}},2614:function(ae){class X{constructor(m,c){arguments.length==0?(this.x=0,this.y=0):arguments.length==1?(this.x=m.x,this.y=m.y):(this.x=m,this.y=c)}clone(){return new X(this.x,this.y)}toString(){return"("+this.x+","+this.y+")"}add(m){return this.x+=m.x,this.y+=m.y,this}subtract(m){return this.x-=m.x,this.y-=m.y,this}divide(m){return this.x/=m,this.y/=m,this}multiply(m){return this.x*=m.x,this.y*=m.y,this}multiplyScalar(m){return this.x*=m,this.y*=m,this}invert(){return this.x=-this.x,this.y=-this.y,this}angle(){return Math.atan2(this.y,this.x)}distance(m){return Math.sqrt((m.x-this.x)*(m.x-this.x)+(m.y-this.y)*(m.y-this.y))}distanceSq(m){return(m.x-this.x)*(m.x-this.x)+(m.y-this.y)*(m.y-this.y)}clockwise(m){let c=this.y*m.x,y=this.x*m.y;return c>y?-1:c===y?0:1}relativeClockwise(m,c){let y=(this.y-m.y)*(c.x-m.x),C=(this.x-m.x)*(c.y-m.y);return y>C?-1:y===C?0:1}rotate(m){let c=new X(0,0),y=Math.cos(m),C=Math.sin(m);return c.x=this.x*y-this.y*C,c.y=this.x*C+this.y*y,this.x=c.x,this.y=c.y,this}rotateAround(m,c){let y=Math.sin(m),C=Math.cos(m);this.x-=c.x,this.y-=c.y;let E=this.x*C-this.y*y,l=this.x*y+this.y*C;return this.x=E+c.x,this.y=l+c.y,this}rotateTo(m,c,y=0){this.x+=.001,this.y-=.001;let C=X.subtract(this,c),E=X.subtract(m,c),l=X.angle(E,C);return this.rotateAround(l+y,c),this}rotateAwayFrom(m,c,y){this.rotateAround(y,c);let C=this.distanceSq(m);this.rotateAround(-2*y,c),this.distanceSq(m)<C&&this.rotateAround(2*y,c)}getRotateAwayFromAngle(m,c,y){let C=this.clone();C.rotateAround(y,c);let E=C.distanceSq(m);return C.rotateAround(-2*y,c),C.distanceSq(m)<E?y:-y}getRotateTowardsAngle(m,c,y){let C=this.clone();C.rotateAround(y,c);let E=C.distanceSq(m);return C.rotateAround(-2*y,c),C.distanceSq(m)>E?y:-y}getRotateToAngle(m,c){let y=X.subtract(this,c),C=X.subtract(m,c),E=X.angle(C,y);return Number.isNaN(E)?0:E}isInPolygon(m){let c=!1;for(let y=0,C=m.length-1;y<m.length;C=y++)m[y].y>this.y!=m[C].y>this.y&&this.x<(m[C].x-m[y].x)*(this.y-m[y].y)/(m[C].y-m[y].y)+m[y].x&&(c=!c);return c}length(){return Math.sqrt(this.x*this.x+this.y*this.y)}lengthSq(){return this.x*this.x+this.y*this.y}normalize(){return this.divide(this.length()),this}normalized(){return X.divideScalar(this,this.length())}whichSide(m,c){return(this.x-m.x)*(c.y-m.y)-(this.y-m.y)*(c.x-m.x)}sameSideAs(m,c,y){let C=this.whichSide(m,c),E=y.whichSide(m,c);return C<0&&E<0||C==0&&E==0||C>0&&E>0}static add(m,c){return new X(m.x+c.x,m.y+c.y)}static subtract(m,c){return new X(m.x-c.x,m.y-c.y)}static multiply(m,c){return new X(m.x*c.x,m.y*c.y)}static multiplyScalar(m,c){return new X(m.x,m.y).multiplyScalar(c)}static midpoint(m,c){return new X((m.x+c.x)/2,(m.y+c.y)/2)}static normals(m,c){let y=X.subtract(c,m);return[new X(-y.y,y.x),new X(y.y,-y.x)]}static units(m,c){let y=X.subtract(c,m);return[new X(-y.y,y.x).normalize(),new X(y.y,-y.x).normalize()]}static divide(m,c){return new X(m.x/c.x,m.y/c.y)}static divideScalar(m,c){return new X(m.x/c,m.y/c)}static dot(m,c){return m.x*c.x+m.y*c.y}static angle(m,c){let y=X.dot(m,c);return Math.acos(y/(m.length()*c.length()))}static threePointangle(m,c,y){let C=X.subtract(c,m),E=X.subtract(y,c),l=m.distance(c),x=c.distance(y);return Math.acos(X.dot(C,E)/(l*x))}static scalarProjection(m,c){let y=c.normalized();return X.dot(m,y)}static averageDirection(m){let c=new X(0,0);for(var y=0;y<m.length;y++){let C=m[y];c.add(C)}return c.normalize()}}ae.exports=X},16843:function(ae,X,D){const m=D(75474),c=D(16348),y=D(2614),C=D(62427);class E{constructor(x,d=0,t=0){this.id=null,this.value=x,this.position=new y(d||0,t||0),this.previousPosition=new y(0,0),this.parentVertexId=null,this.children=Array(),this.spanningTreeChildren=Array(),this.edges=Array(),this.positioned=!1,this.angle=null,this.dir=1,this.neighbourCount=0,this.neighbours=Array(),this.neighbouringElements=Array(),this.forcePositioned=!1}setPosition(x,d){this.position.x=x,this.position.y=d}setPositionFromVector(x){this.position.x=x.x,this.position.y=x.y}addChild(x){this.children.push(x),this.neighbours.push(x),this.neighbourCount++}addRingbondChild(x,d){if(this.children.push(x),this.value.bracket){let t=1;this.id===0&&this.value.bracket.hcount===0&&(t=0),this.value.bracket.hcount===1&&d===0&&(t=2),this.value.bracket.hcount===1&&d===1&&(this.neighbours.length<3?t=2:t=3),this.value.bracket.hcount===null&&d===0&&(t=1),this.value.bracket.hcount===null&&d===1&&(this.neighbours.length<3?t=1:t=2),this.neighbours.splice(t,0,x)}else this.neighbours.push(x);this.neighbourCount++}setParentVertexId(x){this.neighbourCount++,this.parentVertexId=x,this.neighbours.push(x)}isTerminal(){return this.value.hasAttachedPseudoElements?!0:this.parentVertexId===null&&this.children.length<2||this.children.length===0}clone(){let x=new E(this.value,this.position.x,this.position.y);return x.id=this.id,x.previousPosition=new y(this.previousPosition.x,this.previousPosition.y),x.parentVertexId=this.parentVertexId,x.children=c.clone(this.children),x.spanningTreeChildren=c.clone(this.spanningTreeChildren),x.edges=c.clone(this.edges),x.positioned=this.positioned,x.angle=this.angle,x.forcePositioned=this.forcePositioned,x}equals(x){return this.id===x.id}getAngle(x=null,d=!1){let t=null;return x?t=y.subtract(this.position,x):t=y.subtract(this.position,this.previousPosition),d?m.toDeg(t.angle()):t.angle()}getTextDirection(x,d=!1){let t=this.getDrawnNeighbours(x),o=Array();if(x.length===1)return"right";for(let i=0;i<t.length;i++)o.push(this.getAngle(x[t[i]].position));let n=m.meanAngle(o);if(this.isTerminal()||d)Math.round(n*100)/100===1.57&&(n=n-.2),n=Math.round(Math.round(n/Math.PI)*Math.PI);else{let i=Math.PI/2;n=Math.round(Math.round(n/i)*i)}return n===2?"down":n===-2?"up":n===0||n===-0?"right":n===3||n===-3?"left":"down"}getNeighbours(x=null){if(x===null)return this.neighbours.slice();let d=Array();for(let t=0;t<this.neighbours.length;t++)this.neighbours[t]!==x&&d.push(this.neighbours[t]);return d}getDrawnNeighbours(x){let d=Array();for(let t=0;t<this.neighbours.length;t++)x[this.neighbours[t]].value.isDrawn&&d.push(this.neighbours[t]);return d}getNeighbourCount(){return this.neighbourCount}getSpanningTreeNeighbours(x=null){let d=Array();for(let t=0;t<this.spanningTreeChildren.length;t++)(x===void 0||x!=this.spanningTreeChildren[t])&&d.push(this.spanningTreeChildren[t]);return this.parentVertexId!=null&&(x===void 0||x!=this.parentVertexId)&&d.push(this.parentVertexId),d}getNextInRing(x,d,t){let o=this.getNeighbours();for(let n=0;n<o.length;n++)if(c.contains(x[o[n]].value.rings,{value:d})&&o[n]!=t)return o[n];return null}}ae.exports=E},24869:function(ae){(function(X,D){ae.exports=D()})(this,function(){"use strict";function X(e,a,f){return a===void 0&&(a=0),f===void 0&&(f=1),t(o(a,e),f)}function D(e){e._clipped=!1,e._unclipped=e.slice(0);for(var a=0;a<=3;a++)a<3?((e[a]<0||e[a]>255)&&(e._clipped=!0),e[a]=X(e[a],0,255)):a===3&&(e[a]=X(e[a],0,1));return e}for(var m={},c=0,y=["Boolean","Number","String","Function","Array","Date","RegExp","Undefined","Null"];c<y.length;c+=1){var C=y[c];m["[object "+C+"]"]=C.toLowerCase()}function E(e){return m[Object.prototype.toString.call(e)]||"object"}function l(e,a){return a===void 0&&(a=null),e.length>=3?Array.prototype.slice.call(e):E(e[0])=="object"&&a?a.split("").filter(function(f){return e[0][f]!==void 0}).map(function(f){return e[0][f]}):e[0]}function x(e){if(e.length<2)return null;var a=e.length-1;return E(e[a])=="string"?e[a].toLowerCase():null}var d=Math.PI,t=Math.min,o=Math.max,n=d*2,i=d/3,u=d/180,p=180/d,b={format:{},autodetect:[]},v=function(){for(var a=[],f=arguments.length;f--;)a[f]=arguments[f];var S=this;if(E(a[0])==="object"&&a[0].constructor&&a[0].constructor===this.constructor)return a[0];var P=x(a),z=!1;if(!P){z=!0,b.sorted||(b.autodetect=b.autodetect.sort(function(V,re){return re.p-V.p}),b.sorted=!0);for(var L=0,j=b.autodetect;L<j.length;L+=1){var W=j[L];if(P=W.test.apply(W,a),P)break}}if(b.format[P]){var Q=b.format[P].apply(null,z?a:a.slice(0,-1));S._rgb=D(Q)}else throw new Error("unknown format: "+a);S._rgb.length===3&&S._rgb.push(1)};v.prototype.toString=function(){return E(this.hex)=="function"?this.hex():"["+this._rgb.join(",")+"]"};var s="2.6.0",r=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(r.Color,[null].concat(e)))};r.Color=v,r.version=s;var h=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];e=l(e,"cmyk");var f=e[0],S=e[1],P=e[2],z=e[3],L=e.length>4?e[4]:1;return z===1?[0,0,0,L]:[f>=1?0:255*(1-f)*(1-z),S>=1?0:255*(1-S)*(1-z),P>=1?0:255*(1-P)*(1-z),L]},g=Math.max,w=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"rgb"),S=f[0],P=f[1],z=f[2];S=S/255,P=P/255,z=z/255;var L=1-g(S,g(P,z)),j=L<1?1/(1-L):0,W=(1-S-L)*j,Q=(1-P-L)*j,V=(1-z-L)*j;return[W,Q,V,L]};v.prototype.cmyk=function(){return w(this._rgb)},r.cmyk=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["cmyk"])))},b.format.cmyk=h,b.autodetect.push({p:2,test:function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];if(e=l(e,"cmyk"),E(e)==="array"&&e.length===4)return"cmyk"}});var I=function(e){return Math.round(e*100)/100},A=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"hsla"),S=x(e)||"lsa";return f[0]=I(f[0]||0),f[1]=I(f[1]*100)+"%",f[2]=I(f[2]*100)+"%",S==="hsla"||f.length>3&&f[3]<1?(f[3]=f.length>3?f[3]:1,S="hsla"):f.length=3,S+"("+f.join(",")+")"},B=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];e=l(e,"rgba");var f=e[0],S=e[1],P=e[2];f/=255,S/=255,P/=255;var z=t(f,S,P),L=o(f,S,P),j=(L+z)/2,W,Q;return L===z?(W=0,Q=Number.NaN):W=j<.5?(L-z)/(L+z):(L-z)/(2-L-z),f==L?Q=(S-P)/(L-z):S==L?Q=2+(P-f)/(L-z):P==L&&(Q=4+(f-S)/(L-z)),Q*=60,Q<0&&(Q+=360),e.length>3&&e[3]!==void 0?[Q,W,j,e[3]]:[Q,W,j]},O=Math.round,T=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"rgba"),S=x(e)||"rgb";return S.substr(0,3)=="hsl"?A(B(f),S):(f[0]=O(f[0]),f[1]=O(f[1]),f[2]=O(f[2]),(S==="rgba"||f.length>3&&f[3]<1)&&(f[3]=f.length>3?f[3]:1,S="rgba"),S+"("+f.slice(0,S==="rgb"?3:4).join(",")+")")},M=Math.round,H=function(){for(var e,a=[],f=arguments.length;f--;)a[f]=arguments[f];a=l(a,"hsl");var S=a[0],P=a[1],z=a[2],L,j,W;if(P===0)L=j=W=z*255;else{var Q=[0,0,0],V=[0,0,0],re=z<.5?z*(1+P):z+P-z*P,J=2*z-re,se=S/360;Q[0]=se+1/3,Q[1]=se,Q[2]=se-1/3;for(var ie=0;ie<3;ie++)Q[ie]<0&&(Q[ie]+=1),Q[ie]>1&&(Q[ie]-=1),6*Q[ie]<1?V[ie]=J+(re-J)*6*Q[ie]:2*Q[ie]<1?V[ie]=re:3*Q[ie]<2?V[ie]=J+(re-J)*(2/3-Q[ie])*6:V[ie]=J;e=[M(V[0]*255),M(V[1]*255),M(V[2]*255)],L=e[0],j=e[1],W=e[2]}return a.length>3?[L,j,W,a[3]]:[L,j,W,1]},U=/^rgb\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*\)$/,$=/^rgba\(\s*(-?\d+),\s*(-?\d+)\s*,\s*(-?\d+)\s*,\s*([01]|[01]?\.\d+)\)$/,Y=/^rgb\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/,q=/^rgba\(\s*(-?\d+(?:\.\d+)?)%,\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/,Z=/^hsl\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*\)$/,ne=/^hsla\(\s*(-?\d+(?:\.\d+)?),\s*(-?\d+(?:\.\d+)?)%\s*,\s*(-?\d+(?:\.\d+)?)%\s*,\s*([01]|[01]?\.\d+)\)$/,pe=Math.round,me=function(e){e=e.toLowerCase().trim();var a;if(b.format.named)try{return b.format.named(e)}catch(ie){}if(a=e.match(U)){for(var f=a.slice(1,4),S=0;S<3;S++)f[S]=+f[S];return f[3]=1,f}if(a=e.match($)){for(var P=a.slice(1,5),z=0;z<4;z++)P[z]=+P[z];return P}if(a=e.match(Y)){for(var L=a.slice(1,4),j=0;j<3;j++)L[j]=pe(L[j]*2.55);return L[3]=1,L}if(a=e.match(q)){for(var W=a.slice(1,5),Q=0;Q<3;Q++)W[Q]=pe(W[Q]*2.55);return W[3]=+W[3],W}if(a=e.match(Z)){var V=a.slice(1,4);V[1]*=.01,V[2]*=.01;var re=H(V);return re[3]=1,re}if(a=e.match(ne)){var J=a.slice(1,4);J[1]*=.01,J[2]*=.01;var se=H(J);return se[3]=+a[4],se}};me.test=function(e){return U.test(e)||$.test(e)||Y.test(e)||q.test(e)||Z.test(e)||ne.test(e)},v.prototype.css=function(e){return T(this._rgb,e)},r.css=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["css"])))},b.format.css=me,b.autodetect.push({p:5,test:function(e){for(var a=[],f=arguments.length-1;f-- >0;)a[f]=arguments[f+1];if(!a.length&&E(e)==="string"&&me.test(e))return"css"}}),b.format.gl=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"rgba");return f[0]*=255,f[1]*=255,f[2]*=255,f},r.gl=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["gl"])))},v.prototype.gl=function(){var e=this._rgb;return[e[0]/255,e[1]/255,e[2]/255,e[3]]};var we=Math.floor,Ie=function(){for(var e,a,f,S,P,z,L=[],j=arguments.length;j--;)L[j]=arguments[j];L=l(L,"hcg");var W=L[0],Q=L[1],V=L[2],re,J,se;V=V*255;var ie=Q*255;if(Q===0)re=J=se=V;else{W===360&&(W=0),W>360&&(W-=360),W<0&&(W+=360),W/=60;var Se=we(W),Me=W-Se,Te=V*(1-Q),He=Te+ie*(1-Me),ct=Te+ie*Me,gt=Te+ie;switch(Se){case 0:e=[gt,ct,Te],re=e[0],J=e[1],se=e[2];break;case 1:a=[He,gt,Te],re=a[0],J=a[1],se=a[2];break;case 2:f=[Te,gt,ct],re=f[0],J=f[1],se=f[2];break;case 3:S=[Te,He,gt],re=S[0],J=S[1],se=S[2];break;case 4:P=[ct,Te,gt],re=P[0],J=P[1],se=P[2];break;case 5:z=[gt,Te,He],re=z[0],J=z[1],se=z[2];break}}return[re,J,se,L.length>3?L[3]:1]},Le=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"rgb"),S=f[0],P=f[1],z=f[2],L=t(S,P,z),j=o(S,P,z),W=j-L,Q=W*100/255,V=L/(255-W)*100,re;return W===0?re=Number.NaN:(S===j&&(re=(P-z)/W),P===j&&(re=2+(z-S)/W),z===j&&(re=4+(S-P)/W),re*=60,re<0&&(re+=360)),[re,Q,V]};v.prototype.hcg=function(){return Le(this._rgb)},r.hcg=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["hcg"])))},b.format.hcg=Ie,b.autodetect.push({p:1,test:function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];if(e=l(e,"hcg"),E(e)==="array"&&e.length===3)return"hcg"}});var Ee=/^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,_e=/^#?([A-Fa-f0-9]{8}|[A-Fa-f0-9]{4})$/,Re=function(e){if(e.match(Ee)){(e.length===4||e.length===7)&&(e=e.substr(1)),e.length===3&&(e=e.split(""),e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]);var a=parseInt(e,16),f=a>>16,S=a>>8&255,P=a&255;return[f,S,P,1]}if(e.match(_e)){(e.length===5||e.length===9)&&(e=e.substr(1)),e.length===4&&(e=e.split(""),e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]+e[3]+e[3]);var z=parseInt(e,16),L=z>>24&255,j=z>>16&255,W=z>>8&255,Q=Math.round((z&255)/255*100)/100;return[L,j,W,Q]}throw new Error("unknown hex color: "+e)},Ae=Math.round,De=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"rgba"),S=f[0],P=f[1],z=f[2],L=f[3],j=x(e)||"auto";L===void 0&&(L=1),j==="auto"&&(j=L<1?"rgba":"rgb"),S=Ae(S),P=Ae(P),z=Ae(z);var W=S<<16|P<<8|z,Q="000000"+W.toString(16);Q=Q.substr(Q.length-6);var V="0"+Ae(L*255).toString(16);switch(V=V.substr(V.length-2),j.toLowerCase()){case"rgba":return"#"+Q+V;case"argb":return"#"+V+Q;default:return"#"+Q}};v.prototype.hex=function(e){return De(this._rgb,e)},r.hex=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["hex"])))},b.format.hex=Re,b.autodetect.push({p:4,test:function(e){for(var a=[],f=arguments.length-1;f-- >0;)a[f]=arguments[f+1];if(!a.length&&E(e)==="string"&&[3,4,5,6,7,8,9].indexOf(e.length)>=0)return"hex"}});var ye=Math.cos,Ht=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];e=l(e,"hsi");var f=e[0],S=e[1],P=e[2],z,L,j;return isNaN(f)&&(f=0),isNaN(S)&&(S=0),f>360&&(f-=360),f<0&&(f+=360),f/=360,f<1/3?(j=(1-S)/3,z=(1+S*ye(n*f)/ye(i-n*f))/3,L=1-(j+z)):f<2/3?(f-=1/3,z=(1-S)/3,L=(1+S*ye(n*f)/ye(i-n*f))/3,j=1-(z+L)):(f-=2/3,L=(1-S)/3,j=(1+S*ye(n*f)/ye(i-n*f))/3,z=1-(L+j)),z=X(P*z*3),L=X(P*L*3),j=X(P*j*3),[z*255,L*255,j*255,e.length>3?e[3]:1]},kt=Math.min,Ft=Math.sqrt,xe=Math.acos,Pt=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"rgb"),S=f[0],P=f[1],z=f[2];S/=255,P/=255,z/=255;var L,j=kt(S,P,z),W=(S+P+z)/3,Q=W>0?1-j/W:0;return Q===0?L=NaN:(L=(S-P+(S-z))/2,L/=Ft((S-P)*(S-P)+(S-z)*(P-z)),L=xe(L),z>P&&(L=n-L),L/=n),[L*360,Q,W]};v.prototype.hsi=function(){return Pt(this._rgb)},r.hsi=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["hsi"])))},b.format.hsi=Ht,b.autodetect.push({p:2,test:function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];if(e=l(e,"hsi"),E(e)==="array"&&e.length===3)return"hsi"}}),v.prototype.hsl=function(){return B(this._rgb)},r.hsl=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["hsl"])))},b.format.hsl=H,b.autodetect.push({p:2,test:function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];if(e=l(e,"hsl"),E(e)==="array"&&e.length===3)return"hsl"}});var ue=Math.floor,Xe=function(){for(var e,a,f,S,P,z,L=[],j=arguments.length;j--;)L[j]=arguments[j];L=l(L,"hsv");var W=L[0],Q=L[1],V=L[2],re,J,se;if(V*=255,Q===0)re=J=se=V;else{W===360&&(W=0),W>360&&(W-=360),W<0&&(W+=360),W/=60;var ie=ue(W),Se=W-ie,Me=V*(1-Q),Te=V*(1-Q*Se),He=V*(1-Q*(1-Se));switch(ie){case 0:e=[V,He,Me],re=e[0],J=e[1],se=e[2];break;case 1:a=[Te,V,Me],re=a[0],J=a[1],se=a[2];break;case 2:f=[Me,V,He],re=f[0],J=f[1],se=f[2];break;case 3:S=[Me,Te,V],re=S[0],J=S[1],se=S[2];break;case 4:P=[He,Me,V],re=P[0],J=P[1],se=P[2];break;case 5:z=[V,Me,Te],re=z[0],J=z[1],se=z[2];break}}return[re,J,se,L.length>3?L[3]:1]},qe=Math.min,Ke=Math.max,At=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];e=l(e,"rgb");var f=e[0],S=e[1],P=e[2],z=qe(f,S,P),L=Ke(f,S,P),j=L-z,W,Q,V;return V=L/255,L===0?(W=Number.NaN,Q=0):(Q=j/L,f===L&&(W=(S-P)/j),S===L&&(W=2+(P-f)/j),P===L&&(W=4+(f-S)/j),W*=60,W<0&&(W+=360)),[W,Q,V]};v.prototype.hsv=function(){return At(this._rgb)},r.hsv=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["hsv"])))},b.format.hsv=Xe,b.autodetect.push({p:2,test:function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];if(e=l(e,"hsv"),E(e)==="array"&&e.length===3)return"hsv"}});var We={Kn:18,Xn:.95047,Yn:1,Zn:1.08883,t0:.137931034,t1:.206896552,t2:.12841855,t3:.008856452},ft=Math.pow,lt=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];e=l(e,"lab");var f=e[0],S=e[1],P=e[2],z,L,j,W,Q,V;return L=(f+16)/116,z=isNaN(S)?L:L+S/500,j=isNaN(P)?L:L-P/200,L=We.Yn*xt(L),z=We.Xn*xt(z),j=We.Zn*xt(j),W=It(3.2404542*z-1.5371385*L-.4985314*j),Q=It(-.969266*z+1.8760108*L+.041556*j),V=It(.0556434*z-.2040259*L+1.0572252*j),[W,Q,V,e.length>3?e[3]:1]},It=function(e){return 255*(e<=.00304?12.92*e:1.055*ft(e,1/2.4)-.055)},xt=function(e){return e>We.t1?e*e*e:We.t2*(e-We.t0)},pt=Math.pow,yt=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"rgb"),S=f[0],P=f[1],z=f[2],L=ve(S,P,z),j=L[0],W=L[1],Q=L[2],V=116*W-16;return[V<0?0:V,500*(j-W),200*(W-Q)]},Lt=function(e){return(e/=255)<=.04045?e/12.92:pt((e+.055)/1.055,2.4)},mt=function(e){return e>We.t3?pt(e,1/3):e/We.t2+We.t0},ve=function(e,a,f){e=Lt(e),a=Lt(a),f=Lt(f);var S=mt((.4124564*e+.3575761*a+.1804375*f)/We.Xn),P=mt((.2126729*e+.7151522*a+.072175*f)/We.Yn),z=mt((.0193339*e+.119192*a+.9503041*f)/We.Zn);return[S,P,z]};v.prototype.lab=function(){return yt(this._rgb)},r.lab=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["lab"])))},b.format.lab=lt,b.autodetect.push({p:2,test:function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];if(e=l(e,"lab"),E(e)==="array"&&e.length===3)return"lab"}});var ee=Math.sin,le=Math.cos,de=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"lch"),S=f[0],P=f[1],z=f[2];return isNaN(z)&&(z=0),z=z*u,[S,le(z)*P,ee(z)*P]},ke=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];e=l(e,"lch");var f=e[0],S=e[1],P=e[2],z=de(f,S,P),L=z[0],j=z[1],W=z[2],Q=lt(L,j,W),V=Q[0],re=Q[1],J=Q[2];return[V,re,J,e.length>3?e[3]:1]},oe=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"hcl").reverse();return ke.apply(void 0,f)},Ye=Math.sqrt,Oe=Math.atan2,Ve=Math.round,Ze=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"lab"),S=f[0],P=f[1],z=f[2],L=Ye(P*P+z*z),j=(Oe(z,P)*p+360)%360;return Ve(L*1e4)===0&&(j=Number.NaN),[S,L,j]},et=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"rgb"),S=f[0],P=f[1],z=f[2],L=yt(S,P,z),j=L[0],W=L[1],Q=L[2];return Ze(j,W,Q)};v.prototype.lch=function(){return et(this._rgb)},v.prototype.hcl=function(){return et(this._rgb).reverse()},r.lch=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["lch"])))},r.hcl=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["hcl"])))},b.format.lch=ke,b.format.hcl=oe,["lch","hcl"].forEach(function(e){return b.autodetect.push({p:2,test:function(){for(var a=[],f=arguments.length;f--;)a[f]=arguments[f];if(a=l(a,e),E(a)==="array"&&a.length===3)return e}})});var Fe={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",laserlemon:"#ffff54",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrod:"#fafad2",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",maroon2:"#7f0000",maroon3:"#b03060",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",purple2:"#7f007f",purple3:"#a020f0",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};v.prototype.name=function(){for(var e=De(this._rgb,"rgb"),a=0,f=Object.keys(Fe);a<f.length;a+=1){var S=f[a];if(Fe[S]===e)return S.toLowerCase()}return e},b.format.named=function(e){if(e=e.toLowerCase(),Fe[e])return Re(Fe[e]);throw new Error("unknown color name: "+e)},b.autodetect.push({p:5,test:function(e){for(var a=[],f=arguments.length-1;f-- >0;)a[f]=arguments[f+1];if(!a.length&&E(e)==="string"&&Fe[e.toLowerCase()])return"named"}});var tt=function(e){if(E(e)=="number"&&e>=0&&e<=16777215){var a=e>>16,f=e>>8&255,S=e&255;return[a,f,S,1]}throw new Error("unknown num color: "+e)},Je=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"rgb"),S=f[0],P=f[1],z=f[2];return(S<<16)+(P<<8)+z};v.prototype.num=function(){return Je(this._rgb)},r.num=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["num"])))},b.format.num=tt,b.autodetect.push({p:5,test:function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];if(e.length===1&&E(e[0])==="number"&&e[0]>=0&&e[0]<=16777215)return"num"}});var ut=Math.round;v.prototype.rgb=function(e){return e===void 0&&(e=!0),e===!1?this._rgb.slice(0,3):this._rgb.slice(0,3).map(ut)},v.prototype.rgba=function(e){return e===void 0&&(e=!0),this._rgb.slice(0,4).map(function(a,f){return f<3?e===!1?a:ut(a):a})},r.rgb=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["rgb"])))},b.format.rgb=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"rgba");return f[3]===void 0&&(f[3]=1),f},b.autodetect.push({p:3,test:function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];if(e=l(e,"rgba"),E(e)==="array"&&(e.length===3||e.length===4&&E(e[3])=="number"&&e[3]>=0&&e[3]<=1))return"rgb"}});var ze=Math.log,rt=function(e){var a=e/100,f,S,P;return a<66?(f=255,S=a<6?0:-155.25485562709179-.44596950469579133*(S=a-2)+104.49216199393888*ze(S),P=a<20?0:-254.76935184120902+.8274096064007395*(P=a-10)+115.67994401066147*ze(P)):(f=351.97690566805693+.114206453784165*(f=a-55)-40.25366309332127*ze(f),S=325.4494125711974+.07943456536662342*(S=a-50)-28.0852963507957*ze(S),P=255),[f,S,P,1]},it=Math.round,Nt=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];for(var f=l(e,"rgb"),S=f[0],P=f[2],z=1e3,L=4e4,j=.4,W;L-z>j;){W=(L+z)*.5;var Q=rt(W);Q[2]/Q[0]>=P/S?L=W:z=W}return it(W)};v.prototype.temp=v.prototype.kelvin=v.prototype.temperature=function(){return Nt(this._rgb)},r.temp=r.kelvin=r.temperature=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["temp"])))},b.format.temp=b.format.kelvin=b.format.temperature=rt;var St=Math.pow,Dt=Math.sign,vt=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];e=l(e,"lab");var f=e[0],S=e[1],P=e[2],z=St(f+.3963377774*S+.2158037573*P,3),L=St(f-.1055613458*S-.0638541728*P,3),j=St(f-.0894841775*S-1.291485548*P,3);return[255*Et(4.0767416621*z-3.3077115913*L+.2309699292*j),255*Et(-1.2684380046*z+2.6097574011*L-.3413193965*j),255*Et(-.0041960863*z-.7034186147*L+1.707614701*j),e.length>3?e[3]:1]};function Et(e){var a=Math.abs(e);return a>.0031308?(Dt(e)||1)*(1.055*St(a,1/2.4)-.055):e*12.92}var Rt=Math.cbrt,R=Math.pow,$e=Math.sign,Ct=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"rgb"),S=f[0],P=f[1],z=f[2],L=[nt(S/255),nt(P/255),nt(z/255)],j=L[0],W=L[1],Q=L[2],V=Rt(.4122214708*j+.5363325363*W+.0514459929*Q),re=Rt(.2119034982*j+.6806995451*W+.1073969566*Q),J=Rt(.0883024619*j+.2817188376*W+.6299787005*Q);return[.2104542553*V+.793617785*re-.0040720468*J,1.9779984951*V-2.428592205*re+.4505937099*J,.0259040371*V+.7827717662*re-.808675766*J]};function nt(e){var a=Math.abs(e);return a<.04045?e/12.92:($e(e)||1)*R((a+.055)/1.055,2.4)}v.prototype.oklab=function(){return Ct(this._rgb)},r.oklab=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["oklab"])))},b.format.oklab=vt,b.autodetect.push({p:3,test:function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];if(e=l(e,"oklab"),E(e)==="array"&&e.length===3)return"oklab"}});var jt=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];e=l(e,"lch");var f=e[0],S=e[1],P=e[2],z=de(f,S,P),L=z[0],j=z[1],W=z[2],Q=vt(L,j,W),V=Q[0],re=Q[1],J=Q[2];return[V,re,J,e.length>3?e[3]:1]},ge=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];var f=l(e,"rgb"),S=f[0],P=f[1],z=f[2],L=Ct(S,P,z),j=L[0],W=L[1],Q=L[2];return Ze(j,W,Q)};v.prototype.oklch=function(){return ge(this._rgb)},r.oklch=function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];return new(Function.prototype.bind.apply(v,[null].concat(e,["oklch"])))},b.format.oklch=jt,b.autodetect.push({p:3,test:function(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];if(e=l(e,"oklch"),E(e)==="array"&&e.length===3)return"oklch"}}),v.prototype.alpha=function(e,a){return a===void 0&&(a=!1),e!==void 0&&E(e)==="number"?a?(this._rgb[3]=e,this):new v([this._rgb[0],this._rgb[1],this._rgb[2],e],"rgb"):this._rgb[3]},v.prototype.clipped=function(){return this._rgb._clipped||!1},v.prototype.darken=function(e){e===void 0&&(e=1);var a=this,f=a.lab();return f[0]-=We.Kn*e,new v(f,"lab").alpha(a.alpha(),!0)},v.prototype.brighten=function(e){return e===void 0&&(e=1),this.darken(-e)},v.prototype.darker=v.prototype.darken,v.prototype.brighter=v.prototype.brighten,v.prototype.get=function(e){var a=e.split("."),f=a[0],S=a[1],P=this[f]();if(S){var z=f.indexOf(S)-(f.substr(0,2)==="ok"?2:0);if(z>-1)return P[z];throw new Error("unknown channel "+S+" in mode "+f)}else return P};var Ut=Math.pow,hr=1e-7,st=20;v.prototype.luminance=function(e,a){if(a===void 0&&(a="rgb"),e!==void 0&&E(e)==="number"){if(e===0)return new v([0,0,0,this._rgb[3]],"rgb");if(e===1)return new v([255,255,255,this._rgb[3]],"rgb");var f=this.luminance(),S=st,P=function(L,j){var W=L.interpolate(j,.5,a),Q=W.luminance();return Math.abs(e-Q)<hr||!S--?W:Q>e?P(L,W):P(W,j)},z=(f>e?P(new v([0,0,0]),this):P(this,new v([255,255,255]))).rgb();return new v(z.concat([this._rgb[3]]))}return bt.apply(void 0,this._rgb.slice(0,3))};var bt=function(e,a,f){return e=Ot(e),a=Ot(a),f=Ot(f),.2126*e+.7152*a+.0722*f},Ot=function(e){return e/=255,e<=.03928?e/12.92:Ut((e+.055)/1.055,2.4)},be={};function ot(e,a,f){f===void 0&&(f=.5);for(var S=[],P=arguments.length-3;P-- >0;)S[P]=arguments[P+3];var z=S[0]||"lrgb";if(!be[z]&&!S.length&&(z=Object.keys(be)[0]),!be[z])throw new Error("interpolation mode "+z+" is not defined");return E(e)!=="object"&&(e=new v(e)),E(a)!=="object"&&(a=new v(a)),be[z](e,a,f).alpha(e.alpha()+f*(a.alpha()-e.alpha()))}v.prototype.mix=v.prototype.interpolate=function(e,a){a===void 0&&(a=.5);for(var f=[],S=arguments.length-2;S-- >0;)f[S]=arguments[S+2];return ot.apply(void 0,[this,e,a].concat(f))},v.prototype.premultiply=function(e){e===void 0&&(e=!1);var a=this._rgb,f=a[3];return e?(this._rgb=[a[0]*f,a[1]*f,a[2]*f,f],this):new v([a[0]*f,a[1]*f,a[2]*f,f],"rgb")},v.prototype.saturate=function(e){e===void 0&&(e=1);var a=this,f=a.lch();return f[1]+=We.Kn*e,f[1]<0&&(f[1]=0),new v(f,"lch").alpha(a.alpha(),!0)},v.prototype.desaturate=function(e){return e===void 0&&(e=1),this.saturate(-e)},v.prototype.set=function(e,a,f){f===void 0&&(f=!1);var S=e.split("."),P=S[0],z=S[1],L=this[P]();if(z){var j=P.indexOf(z)-(P.substr(0,2)==="ok"?2:0);if(j>-1){if(E(a)=="string")switch(a.charAt(0)){case"+":L[j]+=+a;break;case"-":L[j]+=+a;break;case"*":L[j]*=+a.substr(1);break;case"/":L[j]/=+a.substr(1);break;default:L[j]=+a}else if(E(a)==="number")L[j]=a;else throw new Error("unsupported value for Color.set");var W=new v(L,P);return f?(this._rgb=W._rgb,this):W}throw new Error("unknown channel "+z+" in mode "+P)}else return L},v.prototype.tint=function(e){e===void 0&&(e=.5);for(var a=[],f=arguments.length-1;f-- >0;)a[f]=arguments[f+1];return ot.apply(void 0,[this,"white",e].concat(a))},v.prototype.shade=function(e){e===void 0&&(e=.5);for(var a=[],f=arguments.length-1;f-- >0;)a[f]=arguments[f+1];return ot.apply(void 0,[this,"black",e].concat(a))};var sr=function(e,a,f){var S=e._rgb,P=a._rgb;return new v(S[0]+f*(P[0]-S[0]),S[1]+f*(P[1]-S[1]),S[2]+f*(P[2]-S[2]),"rgb")};be.rgb=sr;var Xt=Math.sqrt,zt=Math.pow,ur=function(e,a,f){var S=e._rgb,P=S[0],z=S[1],L=S[2],j=a._rgb,W=j[0],Q=j[1],V=j[2];return new v(Xt(zt(P,2)*(1-f)+zt(W,2)*f),Xt(zt(z,2)*(1-f)+zt(Q,2)*f),Xt(zt(L,2)*(1-f)+zt(V,2)*f),"rgb")};be.lrgb=ur;var Kt=function(e,a,f){var S=e.lab(),P=a.lab();return new v(S[0]+f*(P[0]-S[0]),S[1]+f*(P[1]-S[1]),S[2]+f*(P[2]-S[2]),"lab")};be.lab=Kt;function he(e,a,f,S){var P,z,L,j;S==="hsl"?(L=e.hsl(),j=a.hsl()):S==="hsv"?(L=e.hsv(),j=a.hsv()):S==="hcg"?(L=e.hcg(),j=a.hcg()):S==="hsi"?(L=e.hsi(),j=a.hsi()):S==="lch"||S==="hcl"?(S="hcl",L=e.hcl(),j=a.hcl()):S==="oklch"&&(L=e.oklch().reverse(),j=a.oklch().reverse());var W,Q,V,re,J,se;(S.substr(0,1)==="h"||S==="oklch")&&(P=L,W=P[0],V=P[1],J=P[2],z=j,Q=z[0],re=z[1],se=z[2]);var ie,Se,Me,Te;return!isNaN(W)&&!isNaN(Q)?(Q>W&&Q-W>180?Te=Q-(W+360):Q<W&&W-Q>180?Te=Q+360-W:Te=Q-W,Se=W+f*Te):isNaN(W)?isNaN(Q)?Se=Number.NaN:(Se=Q,(J==1||J==0)&&S!="hsv"&&(ie=re)):(Se=W,(se==1||se==0)&&S!="hsv"&&(ie=V)),ie===void 0&&(ie=V+f*(re-V)),Me=J+f*(se-J),S==="oklch"?new v([Me,ie,Se],S):new v([Se,ie,Me],S)}var Jt=function(e,a,f){return he(e,a,f,"lch")};be.lch=Jt,be.hcl=Jt;var gr=function(e,a,f){var S=e.num(),P=a.num();return new v(S+f*(P-S),"num")};be.num=gr;var _t=function(e,a,f){return he(e,a,f,"hcg")};be.hcg=_t;var Qt=function(e,a,f){return he(e,a,f,"hsi")};be.hsi=Qt;var vr=function(e,a,f){return he(e,a,f,"hsl")};be.hsl=vr;var qt=function(e,a,f){return he(e,a,f,"hsv")};be.hsv=qt;var mr=function(e,a,f){var S=e.oklab(),P=a.oklab();return new v(S[0]+f*(P[0]-S[0]),S[1]+f*(P[1]-S[1]),S[2]+f*(P[2]-S[2]),"oklab")};be.oklab=mr;var br=function(e,a,f){return he(e,a,f,"oklch")};be.oklch=br;var er=Math.pow,tr=Math.sqrt,ar=Math.PI,lr=Math.cos,fr=Math.sin,wr=Math.atan2;function Ar(e,a,f){a===void 0&&(a="lrgb"),f===void 0&&(f=null);var S=e.length;f||(f=Array.from(new Array(S)).map(function(){return 1}));var P=S/f.reduce(function(Se,Me){return Se+Me});if(f.forEach(function(Se,Me){f[Me]*=P}),e=e.map(function(Se){return new v(Se)}),a==="lrgb")return yr(e,f);for(var z=e.shift(),L=z.get(a),j=[],W=0,Q=0,V=0;V<L.length;V++)if(L[V]=(L[V]||0)*f[0],j.push(isNaN(L[V])?0:f[0]),a.charAt(V)==="h"&&!isNaN(L[V])){var re=L[V]/180*ar;W+=lr(re)*f[0],Q+=fr(re)*f[0]}var J=z.alpha()*f[0];e.forEach(function(Se,Me){var Te=Se.get(a);J+=Se.alpha()*f[Me+1];for(var He=0;He<L.length;He++)if(!isNaN(Te[He]))if(j[He]+=f[Me+1],a.charAt(He)==="h"){var ct=Te[He]/180*ar;W+=lr(ct)*f[Me+1],Q+=fr(ct)*f[Me+1]}else L[He]+=Te[He]*f[Me+1]});for(var se=0;se<L.length;se++)if(a.charAt(se)==="h"){for(var ie=wr(Q/j[se],W/j[se])/ar*180;ie<0;)ie+=360;for(;ie>=360;)ie-=360;L[se]=ie}else L[se]=L[se]/j[se];return J/=S,new v(L,a).alpha(J>.99999?1:J,!0)}var yr=function(e,a){for(var f=e.length,S=[0,0,0,0],P=0;P<e.length;P++){var z=e[P],L=a[P]/f,j=z._rgb;S[0]+=er(j[0],2)*L,S[1]+=er(j[1],2)*L,S[2]+=er(j[2],2)*L,S[3]+=j[3]*L}return S[0]=tr(S[0]),S[1]=tr(S[1]),S[2]=tr(S[2]),S[3]>.9999999&&(S[3]=1),new v(D(S))},Sr=Math.pow;function rr(e){var a="rgb",f=r("#ccc"),S=0,P=[0,1],z=[],L=[0,0],j=!1,W=[],Q=!1,V=0,re=1,J=!1,se={},ie=!0,Se=1,Me=function(_){if(_=_||["#fff","#000"],_&&E(_)==="string"&&r.brewer&&r.brewer[_.toLowerCase()]&&(_=r.brewer[_.toLowerCase()]),E(_)==="array"){_.length===1&&(_=[_[0],_[0]]),_=_.slice(0);for(var ce=0;ce<_.length;ce++)_[ce]=r(_[ce]);z.length=0;for(var Ne=0;Ne<_.length;Ne++)z.push(Ne/(_.length-1))}return wt(),W=_},Te=function(_){if(j!=null){for(var ce=j.length-1,Ne=0;Ne<ce&&_>=j[Ne];)Ne++;return Ne-1}return 0},He=function(_){return _},ct=function(_){return _},gt=function(_,ce){var Ne,Ce;if(ce==null&&(ce=!1),isNaN(_)||_===null)return f;if(ce)Ce=_;else if(j&&j.length>2){var dt=Te(_);Ce=dt/(j.length-2)}else re!==V?Ce=(_-V)/(re-V):Ce=1;Ce=ct(Ce),ce||(Ce=He(Ce)),Se!==1&&(Ce=Sr(Ce,Se)),Ce=L[0]+Ce*(1-L[0]-L[1]),Ce=X(Ce,0,1);var Ge=Math.floor(Ce*1e4);if(ie&&se[Ge])Ne=se[Ge];else{if(E(W)==="array")for(var Pe=0;Pe<z.length;Pe++){var je=z[Pe];if(Ce<=je){Ne=W[Pe];break}if(Ce>=je&&Pe===z.length-1){Ne=W[Pe];break}if(Ce>je&&Ce<z[Pe+1]){Ce=(Ce-je)/(z[Pe+1]-je),Ne=r.interpolate(W[Pe],W[Pe+1],Ce,a);break}}else E(W)==="function"&&(Ne=W(Ce));ie&&(se[Ge]=Ne)}return Ne},wt=function(){return se={}};Me(e);var Be=function(_){var ce=r(gt(_));return Q&&ce[Q]?ce[Q]():ce};return Be.classes=function(_){if(_!=null){if(E(_)==="array")j=_,P=[_[0],_[_.length-1]];else{var ce=r.analyze(P);_===0?j=[ce.min,ce.max]:j=r.limits(ce,"e",_)}return Be}return j},Be.domain=function(_){if(!arguments.length)return P;V=_[0],re=_[_.length-1],z=[];var ce=W.length;if(_.length===ce&&V!==re)for(var Ne=0,Ce=Array.from(_);Ne<Ce.length;Ne+=1){var dt=Ce[Ne];z.push((dt-V)/(re-V))}else{for(var Ge=0;Ge<ce;Ge++)z.push(Ge/(ce-1));if(_.length>2){var Pe=_.map(function(Ue,Qe){return Qe/(_.length-1)}),je=_.map(function(Ue){return(Ue-V)/(re-V)});je.every(function(Ue,Qe){return Pe[Qe]===Ue})||(ct=function(Ue){if(Ue<=0||Ue>=1)return Ue;for(var Qe=0;Ue>=je[Qe+1];)Qe++;var Tt=(Ue-je[Qe])/(je[Qe+1]-je[Qe]),Yt=Pe[Qe]+Tt*(Pe[Qe+1]-Pe[Qe]);return Yt})}}return P=[V,re],Be},Be.mode=function(_){return arguments.length?(a=_,wt(),Be):a},Be.range=function(_,ce){return Me(_),Be},Be.out=function(_){return Q=_,Be},Be.spread=function(_){return arguments.length?(S=_,Be):S},Be.correctLightness=function(_){return _==null&&(_=!0),J=_,wt(),J?He=function(ce){for(var Ne=gt(0,!0).lab()[0],Ce=gt(1,!0).lab()[0],dt=Ne>Ce,Ge=gt(ce,!0).lab()[0],Pe=Ne+(Ce-Ne)*ce,je=Ge-Pe,Ue=0,Qe=1,Tt=20;Math.abs(je)>.01&&Tt-- >0;)(function(){return dt&&(je*=-1),je<0?(Ue=ce,ce+=(Qe-ce)*.5):(Qe=ce,ce+=(Ue-ce)*.5),Ge=gt(ce,!0).lab()[0],je=Ge-Pe})();return ce}:He=function(ce){return ce},Be},Be.padding=function(_){return _!=null?(E(_)==="number"&&(_=[_,_]),L=_,Be):L},Be.colors=function(_,ce){arguments.length<2&&(ce="hex");var Ne=[];if(arguments.length===0)Ne=W.slice(0);else if(_===1)Ne=[Be(.5)];else if(_>1){var Ce=P[0],dt=P[1]-Ce;Ne=Cr(0,_).map(function(Qe){return Be(Ce+Qe/(_-1)*dt)})}else{e=[];var Ge=[];if(j&&j.length>2)for(var Pe=1,je=j.length,Ue=1<=je;Ue?Pe<je:Pe>je;Ue?Pe++:Pe--)Ge.push((j[Pe-1]+j[Pe])*.5);else Ge=P;Ne=Ge.map(function(Qe){return Be(Qe)})}return r[ce]&&(Ne=Ne.map(function(Qe){return Qe[ce]()})),Ne},Be.cache=function(_){return _!=null?(ie=_,Be):ie},Be.gamma=function(_){return _!=null?(Se=_,Be):Se},Be.nodata=function(_){return _!=null?(f=r(_),Be):f},Be}function Cr(e,a,f){for(var S=[],P=e<a,z=a,L=e;P?L<z:L>z;P?L++:L--)S.push(L);return S}var k=function(e){for(var a=[1,1],f=1;f<e;f++){for(var S=[1],P=1;P<=a.length;P++)S[P]=(a[P]||0)+a[P-1];a=S}return a},N=function(e){var a,f,S,P,z,L,j;if(e=e.map(function(J){return new v(J)}),e.length===2)a=e.map(function(J){return J.lab()}),z=a[0],L=a[1],P=function(J){var se=[0,1,2].map(function(ie){return z[ie]+J*(L[ie]-z[ie])});return new v(se,"lab")};else if(e.length===3)f=e.map(function(J){return J.lab()}),z=f[0],L=f[1],j=f[2],P=function(J){var se=[0,1,2].map(function(ie){return(1-J)*(1-J)*z[ie]+2*(1-J)*J*L[ie]+J*J*j[ie]});return new v(se,"lab")};else if(e.length===4){var W;S=e.map(function(J){return J.lab()}),z=S[0],L=S[1],j=S[2],W=S[3],P=function(J){var se=[0,1,2].map(function(ie){return(1-J)*(1-J)*(1-J)*z[ie]+3*(1-J)*(1-J)*J*L[ie]+3*(1-J)*J*J*j[ie]+J*J*J*W[ie]});return new v(se,"lab")}}else if(e.length>=5){var Q,V,re;Q=e.map(function(J){return J.lab()}),re=e.length-1,V=k(re),P=function(J){var se=1-J,ie=[0,1,2].map(function(Se){return Q.reduce(function(Me,Te,He){return Me+V[He]*Math.pow(se,re-He)*Math.pow(J,He)*Te[Se]},0)});return new v(ie,"lab")}}else throw new RangeError("No point in running bezier with only one color.");return P};function G(e){var a=N(e);return a.scale=function(){return rr(a)},a}var F=function(e,a,f){if(!F[f])throw new Error("unknown blend mode "+f);return F[f](e,a)},K=function(e){return function(a,f){var S=r(f).rgb(),P=r(a).rgb();return r.rgb(e(S,P))}},te=function(e){return function(a,f){var S=[];return S[0]=e(a[0],f[0]),S[1]=e(a[1],f[1]),S[2]=e(a[2],f[2]),S}},fe=function(e){return e},ht=function(e,a){return e*a/255},Mt=function(e,a){return e>a?a:e},Bt=function(e,a){return e>a?e:a},zr=function(e,a){return 255*(1-(1-e/255)*(1-a/255))},Hr=function(e,a){return a<128?2*e*a/255:255*(1-2*(1-e/255)*(1-a/255))},kr=function(e,a){return 255*(1-(1-a/255)/(e/255))},Fr=function(e,a){return e===255?255:(e=255*(a/255)/(1-e/255),e>255?255:e)};F.normal=K(te(fe)),F.multiply=K(te(ht)),F.screen=K(te(zr)),F.overlay=K(te(Hr)),F.darken=K(te(Mt)),F.lighten=K(te(Bt)),F.dodge=K(te(Fr)),F.burn=K(te(kr));var jr=Math.pow,Ur=Math.sin,Wr=Math.cos;function $r(e,a,f,S,P){e===void 0&&(e=300),a===void 0&&(a=-1.5),f===void 0&&(f=1),S===void 0&&(S=1),P===void 0&&(P=[0,1]);var z=0,L;E(P)==="array"?L=P[1]-P[0]:(L=0,P=[P,P]);var j=function(W){var Q=n*((e+120)/360+a*W),V=jr(P[0]+L*W,S),re=z!==0?f[0]+W*z:f,J=re*V*(1-V)/2,se=Wr(Q),ie=Ur(Q),Se=V+J*(-.14861*se+1.78277*ie),Me=V+J*(-.29227*se-.90649*ie),Te=V+J*(1.97294*se);return r(D([Se*255,Me*255,Te*255,1]))};return j.start=function(W){return W==null?e:(e=W,j)},j.rotations=function(W){return W==null?a:(a=W,j)},j.gamma=function(W){return W==null?S:(S=W,j)},j.hue=function(W){return W==null?f:(f=W,E(f)==="array"?(z=f[1]-f[0],z===0&&(f=f[1])):z=0,j)},j.lightness=function(W){return W==null?P:(E(W)==="array"?(P=W,L=W[1]-W[0]):(P=[W,W],L=0),j)},j.scale=function(){return r.scale(j)},j.hue(f),j}var Qr="0123456789abcdef",Yr=Math.floor,Gr=Math.random;function Xr(){for(var e="#",a=0;a<6;a++)e+=Qr.charAt(Yr(Gr()*16));return new v(e,"hex")}var Rr=Math.log,Kr=Math.pow,Vr=Math.floor,Zr=Math.abs;function Mr(e,a){a===void 0&&(a=null);var f={min:Number.MAX_VALUE,max:Number.MAX_VALUE*-1,sum:0,values:[],count:0};return E(e)==="object"&&(e=Object.values(e)),e.forEach(function(S){a&&E(S)==="object"&&(S=S[a]),S!=null&&!isNaN(S)&&(f.values.push(S),f.sum+=S,S<f.min&&(f.min=S),S>f.max&&(f.max=S),f.count+=1)}),f.domain=[f.min,f.max],f.limits=function(S,P){return Ir(f,S,P)},f}function Ir(e,a,f){a===void 0&&(a="equal"),f===void 0&&(f=7),E(e)=="array"&&(e=Mr(e));var S=e.min,P=e.max,z=e.values.sort(function(Nr,Er){return Nr-Er});if(f===1)return[S,P];var L=[];if(a.substr(0,1)==="c"&&(L.push(S),L.push(P)),a.substr(0,1)==="e"){L.push(S);for(var j=1;j<f;j++)L.push(S+j/f*(P-S));L.push(P)}else if(a.substr(0,1)==="l"){if(S<=0)throw new Error("Logarithmic scales are only possible for values > 0");var W=Math.LOG10E*Rr(S),Q=Math.LOG10E*Rr(P);L.push(S);for(var V=1;V<f;V++)L.push(Kr(10,W+V/f*(Q-W)));L.push(P)}else if(a.substr(0,1)==="q"){L.push(S);for(var re=1;re<f;re++){var J=(z.length-1)*re/f,se=Vr(J);if(se===J)L.push(z[se]);else{var ie=J-se;L.push(z[se]*(1-ie)+z[se+1]*ie)}}L.push(P)}else if(a.substr(0,1)==="k"){var Se,Me=z.length,Te=new Array(Me),He=new Array(f),ct=!0,gt=0,wt=null;wt=[],wt.push(S);for(var Be=1;Be<f;Be++)wt.push(S+Be/f*(P-S));for(wt.push(P);ct;){for(var _=0;_<f;_++)He[_]=0;for(var ce=0;ce<Me;ce++)for(var Ne=z[ce],Ce=Number.MAX_VALUE,dt=void 0,Ge=0;Ge<f;Ge++){var Pe=Zr(wt[Ge]-Ne);Pe<Ce&&(Ce=Pe,dt=Ge),He[dt]++,Te[ce]=dt}for(var je=new Array(f),Ue=0;Ue<f;Ue++)je[Ue]=null;for(var Qe=0;Qe<Me;Qe++)Se=Te[Qe],je[Se]===null?je[Se]=z[Qe]:je[Se]+=z[Qe];for(var Tt=0;Tt<f;Tt++)je[Tt]*=1/He[Tt];ct=!1;for(var Yt=0;Yt<f;Yt++)if(je[Yt]!==wt[Yt]){ct=!0;break}wt=je,gt++,gt>200&&(ct=!1)}for(var Gt={},ir=0;ir<f;ir++)Gt[ir]=[];for(var nr=0;nr<Me;nr++)Se=Te[nr],Gt[Se].push(z[nr]);for(var $t=[],Vt=0;Vt<f;Vt++)$t.push(Gt[Vt][0]),$t.push(Gt[Vt][Gt[Vt].length-1]);$t=$t.sort(function(Nr,Er){return Nr-Er}),L.push($t[0]);for(var or=1;or<$t.length;or+=2){var Zt=$t[or];!isNaN(Zt)&&L.indexOf(Zt)===-1&&L.push(Zt)}}return L}function Jr(e,a){e=new v(e),a=new v(a);var f=e.luminance(),S=a.luminance();return f>S?(f+.05)/(S+.05):(S+.05)/(f+.05)}var Wt=Math.sqrt,at=Math.pow,_r=Math.min,qr=Math.max,Lr=Math.atan2,Br=Math.abs,cr=Math.cos,Tr=Math.sin,ei=Math.exp,Pr=Math.PI;function ti(e,a,f,S,P){f===void 0&&(f=1),S===void 0&&(S=1),P===void 0&&(P=1);var z=function(Zt){return 360*Zt/(2*Pr)},L=function(Zt){return 2*Pr*Zt/360};e=new v(e),a=new v(a);var j=Array.from(e.lab()),W=j[0],Q=j[1],V=j[2],re=Array.from(a.lab()),J=re[0],se=re[1],ie=re[2],Se=(W+J)/2,Me=Wt(at(Q,2)+at(V,2)),Te=Wt(at(se,2)+at(ie,2)),He=(Me+Te)/2,ct=.5*(1-Wt(at(He,7)/(at(He,7)+at(25,7)))),gt=Q*(1+ct),wt=se*(1+ct),Be=Wt(at(gt,2)+at(V,2)),_=Wt(at(wt,2)+at(ie,2)),ce=(Be+_)/2,Ne=z(Lr(V,gt)),Ce=z(Lr(ie,wt)),dt=Ne>=0?Ne:Ne+360,Ge=Ce>=0?Ce:Ce+360,Pe=Br(dt-Ge)>180?(dt+Ge+360)/2:(dt+Ge)/2,je=1-.17*cr(L(Pe-30))+.24*cr(L(2*Pe))+.32*cr(L(3*Pe+6))-.2*cr(L(4*Pe-63)),Ue=Ge-dt;Ue=Br(Ue)<=180?Ue:Ge<=dt?Ue+360:Ue-360,Ue=2*Wt(Be*_)*Tr(L(Ue)/2);var Qe=J-W,Tt=_-Be,Yt=1+.015*at(Se-50,2)/Wt(20+at(Se-50,2)),Gt=1+.045*ce,ir=1+.015*ce*je,nr=30*ei(-at((Pe-275)/25,2)),$t=2*Wt(at(ce,7)/(at(ce,7)+at(25,7))),Vt=-$t*Tr(2*L(nr)),or=Wt(at(Qe/(f*Yt),2)+at(Tt/(S*Gt),2)+at(Ue/(P*ir),2)+Vt*(Tt/(S*Gt))*(Ue/(P*ir)));return qr(0,_r(100,or))}function ri(e,a,f){f===void 0&&(f="lab"),e=new v(e),a=new v(a);var S=e.get(f),P=a.get(f),z=0;for(var L in S){var j=(S[L]||0)-(P[L]||0);z+=j*j}return Math.sqrt(z)}function ii(){for(var e=[],a=arguments.length;a--;)e[a]=arguments[a];try{return new(Function.prototype.bind.apply(v,[null].concat(e))),!0}catch(f){return!1}}for(var ni={cool:function(){return rr([r.hsl(180,1,.9),r.hsl(250,.7,.4)])},hot:function(){return rr(["#000","#f00","#ff0","#fff"]).mode("rgb")}},dr={OrRd:["#fff7ec","#fee8c8","#fdd49e","#fdbb84","#fc8d59","#ef6548","#d7301f","#b30000","#7f0000"],PuBu:["#fff7fb","#ece7f2","#d0d1e6","#a6bddb","#74a9cf","#3690c0","#0570b0","#045a8d","#023858"],BuPu:["#f7fcfd","#e0ecf4","#bfd3e6","#9ebcda","#8c96c6","#8c6bb1","#88419d","#810f7c","#4d004b"],Oranges:["#fff5eb","#fee6ce","#fdd0a2","#fdae6b","#fd8d3c","#f16913","#d94801","#a63603","#7f2704"],BuGn:["#f7fcfd","#e5f5f9","#ccece6","#99d8c9","#66c2a4","#41ae76","#238b45","#006d2c","#00441b"],YlOrBr:["#ffffe5","#fff7bc","#fee391","#fec44f","#fe9929","#ec7014","#cc4c02","#993404","#662506"],YlGn:["#ffffe5","#f7fcb9","#d9f0a3","#addd8e","#78c679","#41ab5d","#238443","#006837","#004529"],Reds:["#fff5f0","#fee0d2","#fcbba1","#fc9272","#fb6a4a","#ef3b2c","#cb181d","#a50f15","#67000d"],RdPu:["#fff7f3","#fde0dd","#fcc5c0","#fa9fb5","#f768a1","#dd3497","#ae017e","#7a0177","#49006a"],Greens:["#f7fcf5","#e5f5e0","#c7e9c0","#a1d99b","#74c476","#41ab5d","#238b45","#006d2c","#00441b"],YlGnBu:["#ffffd9","#edf8b1","#c7e9b4","#7fcdbb","#41b6c4","#1d91c0","#225ea8","#253494","#081d58"],Purples:["#fcfbfd","#efedf5","#dadaeb","#bcbddc","#9e9ac8","#807dba","#6a51a3","#54278f","#3f007d"],GnBu:["#f7fcf0","#e0f3db","#ccebc5","#a8ddb5","#7bccc4","#4eb3d3","#2b8cbe","#0868ac","#084081"],Greys:["#ffffff","#f0f0f0","#d9d9d9","#bdbdbd","#969696","#737373","#525252","#252525","#000000"],YlOrRd:["#ffffcc","#ffeda0","#fed976","#feb24c","#fd8d3c","#fc4e2a","#e31a1c","#bd0026","#800026"],PuRd:["#f7f4f9","#e7e1ef","#d4b9da","#c994c7","#df65b0","#e7298a","#ce1256","#980043","#67001f"],Blues:["#f7fbff","#deebf7","#c6dbef","#9ecae1","#6baed6","#4292c6","#2171b5","#08519c","#08306b"],PuBuGn:["#fff7fb","#ece2f0","#d0d1e6","#a6bddb","#67a9cf","#3690c0","#02818a","#016c59","#014636"],Viridis:["#440154","#482777","#3f4a8a","#31678e","#26838f","#1f9d8a","#6cce5a","#b6de2b","#fee825"],Spectral:["#9e0142","#d53e4f","#f46d43","#fdae61","#fee08b","#ffffbf","#e6f598","#abdda4","#66c2a5","#3288bd","#5e4fa2"],RdYlGn:["#a50026","#d73027","#f46d43","#fdae61","#fee08b","#ffffbf","#d9ef8b","#a6d96a","#66bd63","#1a9850","#006837"],RdBu:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#f7f7f7","#d1e5f0","#92c5de","#4393c3","#2166ac","#053061"],PiYG:["#8e0152","#c51b7d","#de77ae","#f1b6da","#fde0ef","#f7f7f7","#e6f5d0","#b8e186","#7fbc41","#4d9221","#276419"],PRGn:["#40004b","#762a83","#9970ab","#c2a5cf","#e7d4e8","#f7f7f7","#d9f0d3","#a6dba0","#5aae61","#1b7837","#00441b"],RdYlBu:["#a50026","#d73027","#f46d43","#fdae61","#fee090","#ffffbf","#e0f3f8","#abd9e9","#74add1","#4575b4","#313695"],BrBG:["#543005","#8c510a","#bf812d","#dfc27d","#f6e8c3","#f5f5f5","#c7eae5","#80cdc1","#35978f","#01665e","#003c30"],RdGy:["#67001f","#b2182b","#d6604d","#f4a582","#fddbc7","#ffffff","#e0e0e0","#bababa","#878787","#4d4d4d","#1a1a1a"],PuOr:["#7f3b08","#b35806","#e08214","#fdb863","#fee0b6","#f7f7f7","#d8daeb","#b2abd2","#8073ac","#542788","#2d004b"],Set2:["#66c2a5","#fc8d62","#8da0cb","#e78ac3","#a6d854","#ffd92f","#e5c494","#b3b3b3"],Accent:["#7fc97f","#beaed4","#fdc086","#ffff99","#386cb0","#f0027f","#bf5b17","#666666"],Set1:["#e41a1c","#377eb8","#4daf4a","#984ea3","#ff7f00","#ffff33","#a65628","#f781bf","#999999"],Set3:["#8dd3c7","#ffffb3","#bebada","#fb8072","#80b1d3","#fdb462","#b3de69","#fccde5","#d9d9d9","#bc80bd","#ccebc5","#ffed6f"],Dark2:["#1b9e77","#d95f02","#7570b3","#e7298a","#66a61e","#e6ab02","#a6761d","#666666"],Paired:["#a6cee3","#1f78b4","#b2df8a","#33a02c","#fb9a99","#e31a1c","#fdbf6f","#ff7f00","#cab2d6","#6a3d9a","#ffff99","#b15928"],Pastel2:["#b3e2cd","#fdcdac","#cbd5e8","#f4cae4","#e6f5c9","#fff2ae","#f1e2cc","#cccccc"],Pastel1:["#fbb4ae","#b3cde3","#ccebc5","#decbe4","#fed9a6","#ffffcc","#e5d8bd","#fddaec","#f2f2f2"]},xr=0,Dr=Object.keys(dr);xr<Dr.length;xr+=1){var Or=Dr[xr];dr[Or.toLowerCase()]=dr[Or]}return Object.assign(r,{average:Ar,bezier:G,blend:F,cubehelix:$r,mix:ot,interpolate:ot,random:Xr,scale:rr,analyze:Mr,contrast:Jr,deltaE:ti,distance:ri,limits:Ir,valid:ii,scales:ni,input:b,colors:Fe,brewer:dr}),r})}}]);
}());
//# sourceMappingURL=shared-qqojjJXPklnsBFge22-izTeHC-A_.f2fe6196.async.js.map