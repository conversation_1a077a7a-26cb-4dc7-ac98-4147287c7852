"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[7734],{19669:function(je,F,e){var L=e(1413),h=e(67294),y=e(95985),i=e(84089),o=function(p,t){return h.createElement(i.Z,(0,L.Z)((0,L.Z)({},p),{},{ref:t,icon:y.Z}))},M=h.forwardRef(o);F.Z=M},79090:function(je,F,e){var L=e(1413),h=e(67294),y=e(15294),i=e(84089),o=function(p,t){return h.createElement(i.Z,(0,L.Z)((0,L.Z)({},p),{},{ref:t,icon:y.Z}))},M=h.forwardRef(o);F.Z=M},52688:function(je,F,e){var L=e(1413),h=e(45987),y=e(67294),i=e(92755),o=e(85893),M=["fieldProps","unCheckedChildren","checkedChildren","proFieldProps"],R=y.forwardRef(function(p,t){var m=p.fieldProps,U=p.unCheckedChildren,_=p.checkedChildren,u=p.proFieldProps,b=(0,h.Z)(p,M);return(0,o.jsx)(i.Z,(0,L.Z)({valueType:"switch",fieldProps:(0,L.Z)({unCheckedChildren:U,checkedChildren:_},m),ref:t,valuePropName:"checked",proFieldProps:u,filedConfig:{valuePropName:"checked",ignoreWidth:!0,customLightMode:!0}},b))});F.Z=R},26704:function(je,F,e){e.d(F,{uk:function(){return O},ZP:function(){return ve}});var L=e(1413),h=e(45987),y=e(67294),i=e(48054),o=e(4393),M=e(25378),R=e(96074),p=e(42075),t=e(85893),m=function(l){var s=l.padding;return(0,t.jsx)("div",{style:{padding:s||"0 24px"},children:(0,t.jsx)(R.Z,{style:{margin:0}})})},U={xs:2,sm:2,md:4,lg:4,xl:6,xxl:6},_=function(l){var s=l.size,c=l.active,A=(0,y.useMemo)(function(){return{lg:!0,md:!0,sm:!1,xl:!1,xs:!1,xxl:!1}},[]),X=(0,M.Z)()||A,te=Object.keys(X).filter(function(x){return X[x]===!0})[0]||"md",W=s===void 0?U[te]||6:s,D=function(j){return j===0?0:W>2?42:16};return(0,t.jsx)(o.Z,{bordered:!1,style:{marginBlockEnd:16},children:(0,t.jsx)("div",{style:{width:"100%",justifyContent:"space-between",display:"flex"},children:new Array(W).fill(null).map(function(x,j){return(0,t.jsxs)("div",{style:{borderInlineStart:W>2&&j===1?"1px solid rgba(0,0,0,0.06)":void 0,paddingInlineStart:D(j),flex:1,marginInlineEnd:j===0?16:0},children:[(0,t.jsx)(i.Z,{active:c,paragraph:!1,title:{width:100,style:{marginBlockStart:0}}}),(0,t.jsx)(i.Z.Button,{active:c,style:{height:48}})]},j)})})})},u=function(l){var s=l.active;return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o.Z,{bordered:!1,style:{borderRadius:0},styles:{body:{padding:24}},children:(0,t.jsxs)("div",{style:{width:"100%",display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,t.jsx)("div",{style:{maxWidth:"100%",flex:1},children:(0,t.jsx)(i.Z,{active:s,title:{width:100,style:{marginBlockStart:0}},paragraph:{rows:1,style:{margin:0}}})}),(0,t.jsx)(i.Z.Button,{active:s,size:"small",style:{width:165,marginBlockStart:12}})]})}),(0,t.jsx)(m,{})]})},b=function(l){var s=l.size,c=l.active,A=c===void 0?!0:c,X=l.actionButton;return(0,t.jsxs)(o.Z,{bordered:!1,styles:{body:{padding:0}},children:[new Array(s).fill(null).map(function(te,W){return(0,t.jsx)(u,{active:!!A},W)}),X!==!1&&(0,t.jsx)(o.Z,{bordered:!1,style:{borderStartEndRadius:0,borderTopLeftRadius:0},styles:{body:{display:"flex",alignItems:"center",justifyContent:"center"}},children:(0,t.jsx)(i.Z.Button,{style:{width:102},active:A,size:"small"})})]})},f=function(l){var s=l.active;return(0,t.jsxs)("div",{style:{marginBlockEnd:16},children:[(0,t.jsx)(i.Z,{paragraph:!1,title:{width:185}}),(0,t.jsx)(i.Z.Button,{active:s,size:"small"})]})},S=function(l){var s=l.active;return(0,t.jsx)(o.Z,{bordered:!1,style:{borderBottomRightRadius:0,borderBottomLeftRadius:0},styles:{body:{paddingBlockEnd:8}},children:(0,t.jsxs)(p.Z,{style:{width:"100%",justifyContent:"space-between"},children:[(0,t.jsx)(i.Z.Button,{active:s,style:{width:200},size:"small"}),(0,t.jsxs)(p.Z,{children:[(0,t.jsx)(i.Z.Button,{active:s,size:"small",style:{width:120}}),(0,t.jsx)(i.Z.Button,{active:s,size:"small",style:{width:80}})]})]})})},H=function(l){var s=l.active,c=s===void 0?!0:s,A=l.statistic,X=l.actionButton,te=l.toolbar,W=l.pageHeader,D=l.list,x=D===void 0?5:D;return(0,t.jsxs)("div",{style:{width:"100%"},children:[W!==!1&&(0,t.jsx)(f,{active:c}),A!==!1&&(0,t.jsx)(_,{size:A,active:c}),(te!==!1||x!==!1)&&(0,t.jsxs)(o.Z,{bordered:!1,styles:{body:{padding:0}},children:[te!==!1&&(0,t.jsx)(S,{active:c}),x!==!1&&(0,t.jsx)(b,{size:x,active:c,actionButton:X})]})]})},r=H,Z={xs:1,sm:2,md:3,lg:3,xl:3,xxl:4},ee=function(l){var s=l.active;return(0,t.jsxs)("div",{style:{marginBlockStart:32},children:[(0,t.jsx)(i.Z.Button,{active:s,size:"small",style:{width:100,marginBlockEnd:16}}),(0,t.jsxs)("div",{style:{width:"100%",justifyContent:"space-between",display:"flex"},children:[(0,t.jsxs)("div",{style:{flex:1,marginInlineEnd:24,maxWidth:300},children:[(0,t.jsx)(i.Z,{active:s,paragraph:!1,title:{style:{marginBlockStart:0}}}),(0,t.jsx)(i.Z,{active:s,paragraph:!1,title:{style:{marginBlockStart:8}}}),(0,t.jsx)(i.Z,{active:s,paragraph:!1,title:{style:{marginBlockStart:8}}})]}),(0,t.jsx)("div",{style:{flex:1,alignItems:"center",justifyContent:"center"},children:(0,t.jsxs)("div",{style:{maxWidth:300,margin:"auto"},children:[(0,t.jsx)(i.Z,{active:s,paragraph:!1,title:{style:{marginBlockStart:0}}}),(0,t.jsx)(i.Z,{active:s,paragraph:!1,title:{style:{marginBlockStart:8}}})]})})]})]})},Y=function(l){var s=l.size,c=l.active,A=(0,y.useMemo)(function(){return{lg:!0,md:!0,sm:!1,xl:!1,xs:!1,xxl:!1}},[]),X=(0,M.Z)()||A,te=Object.keys(X).filter(function(D){return X[D]===!0})[0]||"md",W=s===void 0?Z[te]||3:s;return(0,t.jsx)("div",{style:{width:"100%",justifyContent:"space-between",display:"flex"},children:new Array(W).fill(null).map(function(D,x){return(0,t.jsxs)("div",{style:{flex:1,paddingInlineStart:x===0?0:24,paddingInlineEnd:x===W-1?0:24},children:[(0,t.jsx)(i.Z,{active:c,paragraph:!1,title:{style:{marginBlockStart:0}}}),(0,t.jsx)(i.Z,{active:c,paragraph:!1,title:{style:{marginBlockStart:8}}}),(0,t.jsx)(i.Z,{active:c,paragraph:!1,title:{style:{marginBlockStart:8}}})]},x)})})},w=function(l){var s=l.active,c=l.header,A=c===void 0?!1:c,X=(0,y.useMemo)(function(){return{lg:!0,md:!0,sm:!1,xl:!1,xs:!1,xxl:!1}},[]),te=(0,M.Z)()||X,W=Object.keys(te).filter(function(x){return te[x]===!0})[0]||"md",D=Z[W]||3;return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{style:{display:"flex",background:A?"rgba(0,0,0,0.02)":"none",padding:"24px 8px"},children:[new Array(D).fill(null).map(function(x,j){return(0,t.jsx)("div",{style:{flex:1,paddingInlineStart:A&&j===0?0:20,paddingInlineEnd:32},children:(0,t.jsx)(i.Z,{active:s,paragraph:!1,title:{style:{margin:0,height:24,width:A?"75px":"100%"}}})},j)}),(0,t.jsx)("div",{style:{flex:3,paddingInlineStart:32},children:(0,t.jsx)(i.Z,{active:s,paragraph:!1,title:{style:{margin:0,height:24,width:A?"75px":"100%"}}})})]}),(0,t.jsx)(m,{padding:"0px 0px"})]})},C=function(l){var s=l.active,c=l.size,A=c===void 0?4:c;return(0,t.jsxs)(o.Z,{bordered:!1,children:[(0,t.jsx)(i.Z.Button,{active:s,size:"small",style:{width:100,marginBlockEnd:16}}),(0,t.jsx)(w,{header:!0,active:s}),new Array(A).fill(null).map(function(X,te){return(0,t.jsx)(w,{active:s},te)}),(0,t.jsx)("div",{style:{display:"flex",justifyContent:"flex-end",paddingBlockStart:16},children:(0,t.jsx)(i.Z,{active:s,paragraph:!1,title:{style:{margin:0,height:32,float:"right",maxWidth:"630px"}}})})]})},N=function(l){var s=l.active;return(0,t.jsxs)(o.Z,{bordered:!1,style:{borderStartEndRadius:0,borderTopLeftRadius:0},children:[(0,t.jsx)(i.Z.Button,{active:s,size:"small",style:{width:100,marginBlockEnd:16}}),(0,t.jsx)(Y,{active:s}),(0,t.jsx)(ee,{active:s})]})},V=function(l){var s=l.active,c=s===void 0?!0:s,A=l.pageHeader,X=l.list;return(0,t.jsxs)("div",{style:{width:"100%"},children:[A!==!1&&(0,t.jsx)(f,{active:c}),(0,t.jsx)(N,{active:c}),X!==!1&&(0,t.jsx)(m,{}),X!==!1&&(0,t.jsx)(C,{active:c,size:X})]})},I=V,g=function(l){var s=l.active,c=s===void 0?!0:s,A=l.pageHeader;return(0,t.jsxs)("div",{style:{width:"100%"},children:[A!==!1&&(0,t.jsx)(f,{active:c}),(0,t.jsx)(o.Z,{children:(0,t.jsxs)("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",flexDirection:"column",padding:128},children:[(0,t.jsx)(i.Z.Avatar,{size:64,style:{marginBlockEnd:32}}),(0,t.jsx)(i.Z.Button,{active:c,style:{width:214,marginBlockEnd:8}}),(0,t.jsx)(i.Z.Button,{active:c,style:{width:328},size:"small"}),(0,t.jsxs)(p.Z,{style:{marginBlockStart:24},children:[(0,t.jsx)(i.Z.Button,{active:c,style:{width:116}}),(0,t.jsx)(i.Z.Button,{active:c,style:{width:116}})]})]})})]})},de=g,ne=["type"],O=function(l){var s=l.type,c=s===void 0?"list":s,A=(0,h.Z)(l,ne);return c==="result"?(0,t.jsx)(de,(0,L.Z)({},A)):c==="descriptions"?(0,t.jsx)(I,(0,L.Z)({},A)):(0,t.jsx)(r,(0,L.Z)({},A))},ve=O},10784:function(je,F,e){var L=e(97857),h=e.n(L),y=e(15009),i=e.n(y),o=e(99289),M=e.n(o),R=e(5574),p=e.n(R),t=e(13769),m=e.n(t),U=e(28036),_=e(67294),u=e(85893),b=["onClick"],f=function(H){var r=H.onClick,Z=m()(H,b),ee=(0,_.useState)(!1),Y=p()(ee,2),w=Y[0],C=Y[1],N=function(){var V=M()(i()().mark(function I(g){var de;return i()().wrap(function(O){for(;;)switch(O.prev=O.next){case 0:return C(!0),O.prev=1,O.next=4,r==null?void 0:r(g);case 4:return de=O.sent,C(!1),O.abrupt("return",de);case 9:return O.prev=9,O.t0=O.catch(1),C(!1),O.abrupt("return","");case 13:case"end":return O.stop()}},I,null,[[1,9]])}));return function(g){return V.apply(this,arguments)}}();return(0,u.jsx)(U.ZP,h()(h()({loading:w},Z),{},{onClick:N}))};F.Z=f},81012:function(je,F,e){e.d(F,{Z:function(){return R}});var L=e(97857),h=e.n(L),y=e(48054),i=e(67294),o=e(85893),M=(0,i.lazy)(function(){return Promise.all([e.e(6049),e.e(6369),e.e(6891)]).then(e.bind(e,99814)).then(function(p){return{default:p.default}})});function R(p){return(0,o.jsx)(i.Suspense,{fallback:(0,o.jsx)("div",{children:(0,o.jsx)(y.Z,{active:!0})}),children:(0,o.jsx)(M,h()({},p))})}},98083:function(je,F,e){var L=e(5574),h=e.n(L),y=e(99814),i=e(32884),o=e(51042),M=e(70831),R=e(4393),p=e(42075),t=e(66309),m=e(28036),U=e(89277),_=e(21987),u=e(15394),b=e(34528),f=e(67294),S=e(84898),H=e(7378),r=e(85893),Z=function(Y){var w,C=Y.procedure,N=Y.name,V=Y.isSame,I=Y.onReference,g=(0,M.useAccess)(),de=(0,f.useState)(!1),ne=h()(de,2),O=ne[0],ve=ne[1],k=C.text,l=C.reference,s=C.rxn,c=C.similarity,A=C.experimentalProcedure,X=C.yieldString,te=l.type,W=l.link,D=l.title,x=l.authors,j=l.date,re=l.no,Me=l.assignees,ie=l.reference_text,K=(0,r.jsx)(u.Z,{style:{color:"#1890ff",cursor:"pointer"},onClick:function(P){P.stopPropagation(),ve(function(G){return!G})},children:O?"less":"more"});return(0,r.jsxs)(R.Z,{size:"small",className:"ai-card-root",children:[(0,r.jsx)("div",{className:"title",children:(0,r.jsxs)("div",{className:"info-wrapper",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(u.Z,{type:"secondary",children:(0,i.oz)("similarity")}),":",(0,r.jsx)(u.Z,{style:{color:"#027AFF",paddingLeft:8},children:c})]}),(0,r.jsx)(p.Z,{}),(0,r.jsxs)("div",{children:[(0,r.jsx)(u.Z,{type:"secondary",children:(0,i.oz)("yield")}),":",(0,r.jsx)(u.Z,{style:{color:"#027AFF",paddingLeft:8},children:X})]}),(0,r.jsxs)("div",{className:"buttons-wrapper",children:[!!C.scalable&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.Z,{}),(0,r.jsx)(t.Z,{color:"lime",children:(0,i.oz)("procedure.scalable")})]}),V!==void 0?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.Z,{}),(0,H.fn)(V,N)]}):null,(g==null||(w=g.authCodeList)===null||w===void 0?void 0:w.includes("view-by-backbone.tab.recommend.addToMyReaction"))&&I&&(0,r.jsx)(m.ZP,{icon:(0,r.jsx)(o.Z,{}),size:"small",onClick:function(){return I(C)},children:(0,i.oz)("pages.route.label.addToMyReaction")})]})]})}),(0,r.jsx)(p.Z,{}),(0,r.jsx)("div",{children:(0,r.jsx)(y.default,{structure:s})}),(0,r.jsxs)("div",{className:"procedure-wrapper",children:[(0,r.jsx)(b.Z,{level:5,children:"Procedure"}),(0,r.jsxs)(_.Z,{ellipsis:O?!1:{rows:5,expandable:!0,symbol:K},copyable:{text:k},children:[k||A||"",O&&K]})]}),(0,r.jsxs)("div",{className:"reference-wrapper",children:[(0,r.jsxs)(b.Z,{level:5,children:["Reference ",(0,H.Gt)(te)]}),te==="patent"&&(re||Me)&&(0,r.jsxs)(u.Z,{style:{textAlign:"right",display:"block",width:"100%"},children:[re&&(0,r.jsxs)(r.Fragment,{children:["Patent No: ",(0,r.jsx)(u.Z,{copyable:!0,children:re})]}),Me&&(0,r.jsxs)(u.Z,{children:[", assigned by ",Me]}),(0,r.jsx)("br",{})]}),W&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.Z,{type:"secondary",copyable:!0,children:W?(0,r.jsx)(U.Z,{href:W,children:D}):D}),(0,r.jsx)("br",{})]}),ie&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.Z,{type:"secondary",copyable:!0,children:ie}),(0,r.jsx)("br",{})]}),(0,r.jsx)(u.Z,{italic:!0,style:{textAlign:"right",display:"block",width:"100%"},children:x}),j&&(0,r.jsx)(u.Z,{style:{textAlign:"right",display:"block",width:"100%"},children:j})]})]})};F.Z=Z},25660:function(je,F,e){e.d(F,{Z:function(){return C}});var L=e(19632),h=e.n(L),y=e(32884),i=e(1413),o=e(45987),M=e(86190),R=e(67294),p=e(66758),t=e(92755),m=e(85893),U=["fieldProps","proFieldProps"],_="dateYearRange",u=R.forwardRef(function(N,V){var I=N.fieldProps,g=N.proFieldProps,de=(0,o.Z)(N,U),ne=(0,R.useContext)(p.Z);return(0,m.jsx)(t.Z,(0,i.Z)({ref:V,fieldProps:(0,i.Z)({getPopupContainer:ne.getPopupContainer},I),valueType:_,proFieldProps:g,filedConfig:{valueType:_,customLightMode:!0,lightFilterLabelFormatter:function(ve){return(0,M.c)(ve,(I==null?void 0:I.format)||"YYYY")}}},de))}),b=u,f=["fieldProps","proFieldProps","min","max","step","marks","vertical","range"],S=R.forwardRef(function(N,V){var I=N.fieldProps,g=N.proFieldProps,de=N.min,ne=N.max,O=N.step,ve=N.marks,k=N.vertical,l=N.range,s=(0,o.Z)(N,f);return(0,m.jsx)(t.Z,(0,i.Z)({valueType:"slider",fieldProps:(0,i.Z)((0,i.Z)({},I),{},{min:de,max:ne,step:O,marks:ve,vertical:k,range:l,style:I==null?void 0:I.style}),ref:V,proFieldProps:g,filedConfig:{ignoreWidth:!0}},s))}),H=S,r=e(52688),Z={fieldsWrapper:"fieldsWrapper___YYoyM",slider:"slider___JccVt"},ee=50.5,Y=10,w=function(){return(0,m.jsxs)("div",{className:Z.fieldsWrapper,children:[(0,m.jsx)(b,{name:"yearRange",label:(0,y.oz)("procedure-filter.year-range"),fieldProps:{allowEmpty:[!0,!0],placeholder:[(0,y.oz)("procedure-filter.year-range-empty"),(0,y.oz)("procedure-filter.year-range-empty")]}}),(0,m.jsx)(H,{name:"impactFactor",range:!0,max:ee,min:0,marks:Object.fromEntries(h()(Array(Math.floor(ee/Y))).map(function(V,I){return[I*Y,"".concat(I*Y)]})),label:(0,y.oz)("procedure-filter.impact-factor"),fieldProps:{range:!0,className:Z.slider}}),(0,m.jsx)(r.Z,{name:"validProcedure",label:(0,y.oz)("procedure-filter.has-procedure-only")}),(0,m.jsx)(r.Z,{name:"scalable",label:(0,y.oz)("procedure-filter.scalable")}),(0,m.jsx)(r.Z,{name:"same",label:(0,y.oz)("same-reaction")}),(0,m.jsx)(r.Z,{name:"similar",label:(0,y.oz)("reference")})]})},C=w},14211:function(je,F,e){e.d(F,{Iv:function(){return u},ik:function(){return U}});var L=e(15009),h=e.n(L),y=e(99289),i=e.n(y),o=e(5574),M=e.n(o),R=e(98138),p=e(27484),t=e.n(p),m=e(67294),U=function(f){var S,H,r=f.yearRange,Z=f.impactFactor,ee=f.validProcedure,Y=f.scalable,w=f.same,C=f.similar;return{year_min:r==null||(S=r[0])===null||S===void 0?void 0:S.year(),year_max:r==null||(H=r[1])===null||H===void 0?void 0:H.year(),impact_factor_min:Z==null?void 0:Z[0],impact_factor_max:Z==null?void 0:Z[1],has_valid_procedure:ee,need_scalable:Y,same:w,similar:C}},_=function(f){var S=f.year_min,H=f.year_max,r=f.impact_factor_min,Z=f.impact_factor_max,ee=f.has_valid_procedure,Y=f.need_scalable,w=f.same,C=f.similar;return{yearRange:[S,H].map(function(N){return N===void 0?void 0:t()("".concat(N,"-01-01"))}),impactFactor:[r,Z],validProcedure:ee,scalable:Y,same:w,similar:C}},u=function(f){var S=R.Z.useForm(),H=M()(S,1),r=H[0],Z=(0,m.useState)(),ee=M()(Z,2),Y=ee[0],w=ee[1];(0,m.useEffect)(function(){var N=_(f||{});r.setFieldsValue(N),w(N)},[r,f]);var C=function(V){return i()(h()().mark(function I(){var g;return h()().wrap(function(ne){for(;;)switch(ne.prev=ne.next){case 0:return g=r.getFieldsValue(),V==null||V(U(g)),w(g),ne.abrupt("return",!0);case 4:case"end":return ne.stop()}},I)}))};return{form:r,handleFinish:C,reset:function(){return r.setFieldsValue(Y||{})},touched:r.isFieldsTouched()}}},99512:function(je,F,e){e.d(F,{Z:function(){return ie}});var L=e(97857),h=e.n(L),y=e(5574),i=e.n(y),o=e(67294),M=Object.defineProperty,R=Object.getOwnPropertySymbols,p=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,m=(K,a,P)=>a in K?M(K,a,{enumerable:!0,configurable:!0,writable:!0,value:P}):K[a]=P,U=(K,a)=>{for(var P in a||(a={}))p.call(a,P)&&m(K,P,a[P]);if(R)for(var P of R(a))t.call(a,P)&&m(K,P,a[P]);return K};const _=K=>o.createElement("svg",U({width:160,height:160,fill:"none",xmlns:"http://www.w3.org/2000/svg"},K),o.createElement("path",{d:"M113.5 19.833c-13.083 5.284-49.467 19.517-51.533 14.934-5.317-11.784 8.716-8.967 14.233-5.1.7.5 2.383 1.95 2.25 3.45-.383 4.816-2.567 6.583-31.583 17.25",stroke:"#333",strokeWidth:.5,strokeLinecap:"round",strokeLinejoin:"round",strokeDasharray:"2 2"}),o.createElement("path",{d:"M32.2 65.333c-3.117 6.217-10.667 9.784-10.667 9.784s.884-6.217.45-10.667c-.3-2.967 2.584-7.117 5.784-7.117 3.2 0 5.866 5.15 4.45 8H32.2Z",fill:"#6691D6"}),o.createElement("path",{d:"M25.083 68c.717-6 3.4-12.583 9.5-15.017.434-.183.917.034 1.1.467a.843.843 0 0 1-.466 1.083c-5.8 1.667-8.934 7.85-10.134 13.467Z",fill:"#CCDAF1"}),o.createElement("path",{d:"M113.667 95.533c0 17.617-14.284 31.9-31.9 31.9-17.617 0-31.9-14.283-31.9-31.9 0-17.616 14.283-31.9 31.9-31.9 3.95 0 7.75.717 11.233 2.034a31.825 31.825 0 0 1 12.8 8.883 31.801 31.801 0 0 1 7.867 20.983Z",fill:"#fff",stroke:"#000",strokeWidth:.5,strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"m109.967 68.433-4.184 6.117a31.824 31.824 0 0 0-12.8-8.883l4.184-5.684v-.016c2.266.133 8.766 1.183 12.7 8.333.033.05.05.083.066.133h.034Z",fill:"#fff",stroke:"#000",strokeWidth:.5,strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"m110.033 68.333-.066.1",stroke:"#000",strokeWidth:.5,strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"m116.883 61.533-3.55 5.2c-3.916-9.266-12.583-10.35-16.416-10.35l3.466-5.65a3.27 3.27 0 0 1 2.617-1.55c9.367-.45 13.017 5.95 14.267 9.184.4 1.05.266 2.233-.367 3.166h-.017Z",fill:"#fff",stroke:"#000",strokeWidth:.5,strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"m113.333 66.733-.133.184a3.282 3.282 0 0 1-3.167 1.4c-.05 0-.1 0-.15-.017-3.933-7.15-10.433-8.2-12.7-8.333h-.05c-1.033-.95-1.083-2.1-.3-3.467l.067-.1c3.85 0 12.5 1.083 16.417 10.35l.016-.017Z",fill:"#E0DEDE",stroke:"#000",strokeWidth:.5,strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"m97.183 59.983-.033-.033",stroke:"#000",strokeWidth:.5,strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M73.537 70.034c1.622-.575 2.8-1.427 2.632-1.904-.17-.477-1.622-.399-3.244.175-1.623.575-2.801 1.427-2.632 1.904.168.478 1.62.4 3.243-.175ZM55.35 83.667c.683-1.984 3.017-7.467 8.633-10.917.267-.167.584-.267.884-.283.5-.034 1.216.033 1.533.65.25.483.083 1.083-.35 1.416-1.117.867-4 3.5-7.7 9.95a1.674 1.674 0 0 1-.55.584c-.367.25-.983.533-1.583.366-.734-.216-1.117-1.05-.85-1.783l-.017.017Z",fill:"#9ACDF7"}),o.createElement("path",{d:"M108.717 52.683s4.15 1.15 5.983 6.684M108.017 68.35l-2.3 3",stroke:"#000",strokeWidth:.5,strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M96.617 87.133a.233.233 0 1 0 0-.466.233.233 0 0 0 0 .466ZM101 81.833a.233.233 0 1 0 0-.466.233.233 0 0 0 0 .466ZM106.517 84.133a.233.233 0 1 0 0-.466.234.234 0 1 0 0 .466ZM103.633 89.433a.117.117 0 1 0 0-.233.117.117 0 0 0 0 .233Z",fill:"#fff"}),o.createElement("path",{d:"M78.767 98.067c0 3.366-2.734 6.1-6.1 6.1a6.103 6.103 0 0 1-6.1-6.1c0-3.367 2.75-6 6.116-6 3.367 0 6.084 2.616 6.084 6Z",fill:"#000"}),o.createElement("path",{d:"M75.35 101.95a1.267 1.267 0 1 0 0-2.533 1.267 1.267 0 0 0 0 2.533Z",fill:"#fff"}),o.createElement("path",{d:"M85.45 98.067c0 3.366 2.733 6.1 6.1 6.1 3.367 0 6.1-2.734 6.1-6.1 0-3.367-2.8-6-6.167-6-3.366 0-6.05 2.616-6.05 6h.017Z",fill:"#000"}),o.createElement("path",{d:"M88.9 101.95a1.267 1.267 0 1 0 0-2.533 1.267 1.267 0 0 0 0 2.533ZM74.8 98.6a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5ZM89.3 98.6a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z",fill:"#fff"}),o.createElement("path",{d:"M81 111.167c.35-.317.8-.517 1.317-.517.533 0 1.016.217 1.366.567M87.817 87.8s4.516 3.8 7.766 1.933M75.6 87.8s-4.517 3.8-7.767 1.933",stroke:"#000",strokeWidth:.5,strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M80.833 144.167c6.444 0 11.667-1.493 11.667-3.334s-5.223-3.333-11.667-3.333c-6.443 0-11.666 1.492-11.666 3.333 0 1.841 5.223 3.334 11.666 3.334Z",fill:"#DBDBDB"}),o.createElement("path",{d:"M30.733 64.75c-3.116 6.217-10.666 9.783-10.666 9.783s.883-6.216.45-10.666c-.3-2.967 2.583-7.117 5.783-7.117s5.867 5.15 4.45 8h-.017Z",stroke:"#333",strokeWidth:.5,strokeLinecap:"round",strokeLinejoin:"round"}),o.createElement("path",{d:"M23.633 67.417c.717-6 3.4-12.584 9.5-15.017.434-.183.917.033 1.1.467a.844.844 0 0 1-.466 1.083c-5.8 1.667-8.934 7.85-10.134 13.467Z",fill:"#333"}));var u="data:image/svg+xml;base64,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",b=e(30042),f=e(82869),S=e(32884),H=e(42525),r=e(28459),Z=e(73935),ee=e(98083),Y=e(19669),w=e(37476),C=e(28036),N=e(25660),V=e(14211),I=e(85893),g=function(a){var P=a.onFinished,G=a.initialFilter,d=(0,V.Iv)(G),oe=d.form,J=d.handleFinish,pe=d.reset;return(0,I.jsx)(w.Y,{title:(0,S.oz)("procedure-filter.title"),width:400,size:"small",trigger:(0,I.jsx)(C.ZP,{size:"small",icon:(0,I.jsx)(Y.Z,{}),type:"text"}),form:oe,onFinish:J(P),modalProps:{onCancel:pe},children:(0,I.jsx)(N.Z,{})})},de=g,ne=e(15009),O=e.n(ne),ve=e(19632),k=e.n(ve),l=e(99289),s=e.n(l),c=e(11005),A=e(64134),X=e(87172),te=e(77837),W=e(7378),D=30,x=function(){var K=s()(O()().mark(function a(P,G,d){var oe,J,pe,Le,ge,$,T;return O()().wrap(function(v){for(;;)switch(v.prev=v.next){case 0:if(P.reactants.length){v.next=2;break}return v.abrupt("return",[]);case 2:if(v.t0=G,v.t0){v.next=16;break}return v.next=6,(0,c.sq)(P);case 6:if(v.t2=oe=v.sent,v.t1=v.t2===null,v.t1){v.next=10;break}v.t1=oe===void 0;case 10:if(!v.t1){v.next=14;break}v.t3=void 0,v.next=15;break;case 14:v.t3=oe.map(function(ce){return(0,A.zq)(ce)});case 15:v.t0=v.t3;case 16:return J=v.t0,pe=(0,A.zq)(P),v.next=20,(0,X.service)("procedure/rxn-match",{method:"post",normalizeData:!1,data:h()({simple_rxn:pe,rxns:J!=null&&J.length?J:[pe],similar_topk:D},d)}).select().get();case 20:if(Le=v.sent,!Le){v.next=24;break}return ge=Le,$=ge.similar_procedures,T=ge.same_procedures,v.abrupt("return",[].concat(k()(T.map(function(ce){return(0,W.C_)(ce,!0)})),k()($.map(function(ce){return(0,W.C_)(ce,!1)}))));case 24:throw new Error("Network response was not ok");case 25:case"end":return v.stop()}},a)}));return function(P,G,d){return K.apply(this,arguments)}}(),j=function(a,P,G){var d=(0,te.a)({queryKey:["procedure/rxn-match",a,P,G],queryFn:function(){return a?x(a,P,G):[]},enabled:!!a}),oe=d.data,J=d.error,pe=d.isLoading,Le=d.refetch,ge=oe==null?void 0:oe.filter(function($){return($.isSame?G==null?void 0:G.same:G==null?void 0:G.similar)!==!1});return{procedures:ge,total:ge==null?void 0:ge.length,error:J,isLoading:pe,refetch:Le}},re=function(a){return function(P,G){var d;return(0,I.jsx)(ee.Z,{procedure:P,isSame:G,name:a==null||(d=a.find(function(oe){return oe.rxn===P.query}))===null||d===void 0?void 0:d.name})}},Me=function(a){var P=a.reaction,G=a.actionSlot,d=a.rxns,oe=a.renderer,J=oe===void 0?re(d):oe,pe=(0,f.Li)(),Le=pe.setting,ge=Le===void 0?{}:Le,$=ge.procedure,T=$===void 0?{}:$,B=(0,o.useState)(),v=i()(B,2),ce=v[0],he=v[1],ae=h()(h()({},T),ce),Q=j(P,d==null?void 0:d.map(function(ze){return ze.rxn}),ae),n=Q.isLoading,q=Q.procedures,ue=(0,o.useState)({page:1,pageSize:10}),Te=i()(ue,2),We=Te[0],Re=Te[1];return(0,I.jsxs)(r.ZP,{renderEmpty:function(){return(0,I.jsx)(b.Z,{image:(0,I.jsx)(_,{}),des:(0,S.oz)("noticeIcon.empty")})},children:[(0,I.jsx)(H.Rs,{ghost:!0,loading:n,pagination:q!=null&&q.length?{current:We.page,pageSize:We.pageSize,simple:!0,total:Math.min((q==null?void 0:q.length)||0,D),onChange:function(Ce,ke){return Re({page:Ce,pageSize:ke})}}:!1,renderItem:function(Ce){return J(Ce,Ce.isSame)},grid:{column:1},dataSource:q}),G&&Z.createPortal((0,I.jsx)(de,{onFinished:he,initialFilter:ae}),G)]})},ie=Me},7378:function(je,F,e){e.d(F,{C_:function(){return o},Gt:function(){return M},fn:function(){return R},lO:function(){return i}});var L=e(32884),h=e(66309),y=e(85893),i=function(t){switch(t){case"JOURNAL":return"journal";case"Patent":return"patent";default:return t}},o=function(t){var m=t.id,U=t.experimental_procedure,_=U===void 0?"":U,u=t.query,b=u===void 0?"":u,f=t.procedure,S=f===void 0?"":f,H=t.similarity,r=H===void 0?0:H,Z=t.rxn,ee=Z===void 0?"":Z,Y=t.rxn_yields,w=Y===void 0?"":Y,C=t.yields,N=t.transformation,V=N===void 0?"":N,I=t.reference_type,g=I===void 0?"JOURNAL":I,de=t.title,ne=de===void 0?"":de,O=t.authors,ve=O===void 0?"":O,k=t.date,l=k===void 0?"":k,s=t.assignees,c=s===void 0?"":s,A=t.reference_text,X=A===void 0?"":A,te=t.patent_id,W=te===void 0?"":te,D=t.is_scalable,x=D===void 0?!1:D,j=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return{id:m,rxn:ee,isSame:j,text:S||"",experimentalProcedure:_||"",transformation:V||"",query:b||"",reference:{type:i(g),title:ne||"",authors:ve||"",date:l||"",no:W||"",reference_text:X||"",assignees:c||""},yieldString:w||"",yieldNumber:C||void 0,similarity:r||0,scalable:x||!1}},M=function(t){switch(t){case"journal":return(0,y.jsx)(h.Z,{color:"green",children:"Journal"});case"patent":return(0,y.jsx)(h.Z,{color:"blue",children:"Patent"});default:return(0,y.jsx)(h.Z,{color:"orange",children:"Custom"})}},R=function(t,m){switch(t){case!0:return(0,y.jsx)(h.Z,{color:"green",children:m?(0,L.Ig)()?"The Same Reaction as ".concat(m):"\u4E0E".concat(m,"\u662F").concat((0,L.oz)("same-reaction")):(0,L.oz)("same-reaction")});case!1:return(0,y.jsx)(h.Z,{color:"blue",children:m?(0,L.Ig)()?"Reference for ".concat(m):"\u4E0E".concat(m,"\u662F").concat((0,L.oz)("reference")):(0,L.oz)("reference")});default:return(0,y.jsx)(y.Fragment,{})}}},16003:function(je,F,e){e.d(F,{Z:function(){return Le},H:function(){return ge}});var L=e(19632),h=e.n(L),y=e(9783),i=e.n(y),o=e(15009),M=e.n(o),R=e(97857),p=e.n(R),t=e(99289),m=e.n(t),U=e(5574),_=e.n(U),u=e(67294),b=Object.defineProperty,f=Object.getOwnPropertySymbols,S=Object.prototype.hasOwnProperty,H=Object.prototype.propertyIsEnumerable,r=($,T,B)=>T in $?b($,T,{enumerable:!0,configurable:!0,writable:!0,value:B}):$[T]=B,Z=($,T)=>{for(var B in T||(T={}))S.call(T,B)&&r($,B,T[B]);if(f)for(var B of f(T))H.call(T,B)&&r($,B,T[B]);return $};const ee=$=>u.createElement("svg",Z({fill:"none",xmlns:"http://www.w3.org/2000/svg"},$),u.createElement("g",{clipPath:"url(#warning_svg__a)"},u.createElement("path",{d:"M7.978 5v4.068",stroke:"#FAAD14",strokeLinecap:"round",strokeLinejoin:"round"}),u.createElement("path",{d:"M8 11.527a.5.5 0 1 0 0-1 .5.5 0 0 0 0 1Z",fill:"#FAAD14"}),u.createElement("circle",{cx:8,cy:8,r:6.5,stroke:"#FAAD14"})),u.createElement("defs",null,u.createElement("clipPath",{id:"warning_svg__a"},u.createElement("path",{fill:"#fff",d:"M0 0h16v16H0z"}))));var Y="data:image/svg+xml;base64,PHN2ZyBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxnIGNsaXAtcGF0aD0idXJsKCNhKSI+PHBhdGggZD0iTTcuOTc4IDV2NC4wNjgiIHN0cm9rZT0iI0ZBQUQxNCIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+PHBhdGggZD0iTTggMTEuNTI3YS41LjUgMCAxIDAgMC0xIC41LjUgMCAwIDAgMCAxWiIgZmlsbD0iI0ZBQUQxNCIvPjxjaXJjbGUgY3g9IjgiIGN5PSI4IiByPSI2LjUiIHN0cm9rZT0iI0ZBQUQxNCIvPjwvZz48ZGVmcz48Y2xpcFBhdGggaWQ9ImEiPjxwYXRoIGZpbGw9IiNmZmYiIGQ9Ik0wIDBoMTZ2MTZIMHoiLz48L2NsaXBQYXRoPjwvZGVmcz48L3N2Zz4=",w=e(59652),C=e(99814),N=e(61487),V=e(64134),I=e(87172),g=e(32884),de=e(27068),ne=e(26704),O=e(42525),ve=e(85576),k=e(71471),l=e(42075),s=e(66309),c=e(55241),A=e(45360),X=e(93967),te=e.n(X),W=e(96486),D=e(70831),x=e(10784),j=e(64599),re=e.n(j),Me=e(81012),ie=e(92921),K=e(21987),a=e(85893);function P($){var T=(0,D.useModel)("compound"),B=T.materialCodeOptions,v=function(Q){if(!B)return"";var n="";return Object.keys(B).map(function(q){var ue=B[q];return ue&&ue.includes(Q)&&(n=q),null}),(0,g.oz)(n)},ce=function(Q,n){var q=[],ue=re()(Q),Te;try{for(ue.s();!(Te=ue.n()).done;){var We=Te.value,Re=v(We);Re&&q.push(Re)}}catch(ze){ue.e(ze)}finally{ue.f()}return(0,W.isEmpty)(q)?"-":n?(0,a.jsx)("a",{href:n,children:q.join("\u3001")}):q.join("\u3001")},he=[{title:(0,g.oz)("structural"),dataIndex:"canonical_smiles",render:function(Q){return Q?(0,a.jsx)(Me.Z,{structure:Q,className:"smilesItem"}):""}},{title:"CAS",dataIndex:"cas_no",render:function(Q){return Q&&Q!=="-"?(0,a.jsx)(K.Z,{ellipsis:!1,copyable:!0,style:{marginTop:"15px"},children:Q}):"-"}},{title:(0,g.oz)("unit-price"),dataIndex:"min_unit_price",render:function(Q,n){return Q?(0,g.Ig)()?"From \uFFE5".concat(Q.toFixed(2),"/").concat(n!=null&&n.unified_unit.includes("g")?"g":"ml"):"\uFFE5".concat(Q.toFixed(2),"/").concat(n!=null&&n.unified_unit.includes("g")?"g":"ml","\u8D77"):"-"}},{title:(0,g.oz)("amount"),dataIndex:"unit_range",render:function(Q,n){return(0,g.qt)(n==null?void 0:n.unit_range)&&n!==null&&n!==void 0&&n.unit_range[0]&&n!==null&&n!==void 0&&n.unit_range[1]?"".concat(n==null?void 0:n.unit_range[0]).concat(n==null?void 0:n.unified_unit,"-").concat(n==null?void 0:n.unit_range[1]).concat(n==null?void 0:n.unified_unit):"-"}},{title:(0,g.oz)("label"),dataIndex:"codes",width:110,render:function(Q,n){return(0,W.isEmpty)(n==null?void 0:n.codes)?"-":ce(n==null?void 0:n.codes,n==null?void 0:n.pubchem_safety_link)}},{title:(0,g.oz)("supplier"),dataIndex:"source",render:function(Q,n){return n!=null&&n.source_link?(0,a.jsx)("a",{onClick:function(){return window.open(n==null?void 0:n.source_link)},children:Q}):Q}},{title:(0,g.oz)("version"),dataIndex:"material_lib",render:function(Q,n){var q;return n==null||(q=n.material_lib)===null||q===void 0?void 0:q.version}}];return(0,a.jsx)(ie.Z,{columns:he,dataSource:$==null?void 0:$.materialsPrice,pagination:!1})}var G=e(33547),d={retroReactionRowRoot:"retroReactionRowRoot___jKhNK",riskTag:"riskTag___iqJK6",retroReactionRoot:"retroReactionRoot___UV23y",selectable:"selectable___NPtwN",smiles:"smiles___InELl",actionsWrapper:"actionsWrapper___G4nFE",rightActionWrapper:"rightActionWrapper___wZJji",sourceWrapper:"sourceWrapper___IiGJg",retroReactionTabRoot:"retroReactionTabRoot___iBFo5",materialsPriceTable:"materialsPriceTable___fmkpq",viwePrice:"viwePrice___ZS0HL",warningIcon:"warningIcon___PrHbA"},oe=function(T){return"".concat((0,g.oz)("reaction")).concat(T+1)},J=function(T,B){var v;return((v=T.retro_processes)===null||v===void 0?void 0:v.findIndex(function(ce){return ce.id===B}))!==-1},pe=function(T){var B,v=T.reaction,ce=T.projectId,he=T.onSelectProcedure,ae=T.onSelect,Q=T.selectAllWhenEmpty,n=T.retroProcessId,q=T.fullReactionConfig,ue=(0,D.useModel)("commend"),Te=ue.getProfileInfo,We=ue.getCommonExpression,Re=ue.showLauncher,ze=ue.sendMessage,Ce=ue.reload,ke=ue.finishedReload,$e=ue.reactionStepNo,ye=ue.isOpen,xe=(0,D.useModel)("compound"),fe=xe.getMaterialCodeOptions,le=(0,D.useAccess)(),me=(0,u.useState)([]),De=_()(me,2),Ie=De[0],Oe=De[1],Ne=(0,N.f)(),be=Ne.fetch,we=Ne.loading,se=(0,u.useState)(!1),Ee=_()(se,2),Ke=Ee[0],Qe=Ee[1],He=(0,u.useState)([]),Ve=_()(He,2),z=Ve[0],lt=Ve[1],rt=function(){var _e=m()(M()().mark(function Pe(){var E,Se,Ze;return M()().wrap(function(Ae){for(;;)switch(Ae.prev=Ae.next){case 0:if(v!=null&&(E=v.reactants)!==null&&E!==void 0&&E.length){Ae.next=2;break}return Ae.abrupt("return");case 2:return Ae.next=4,be((0,I.queryWithDefaultOrder)("retro-reactions/list?comment=true",{method:"post",data:{filters:p()(p()({},v),{},{projectId:ce})}}).populateWith("retro_processes",["id"]).sortBy([{field:"updatedAt",order:"desc"}]).paginate(1,1e3).get());case 4:Se=Ae.sent,Ze=Se.data,Ze&&(Oe(Ze),ae==null||ae(Ze.map(function(Je,Be){return{rxn:Je,name:oe(Be)}})));case 7:case"end":return Ae.stop()}},Pe)}));return function(){return _e.apply(this,arguments)}}(),st=q||{},at=st.fullReaction;return(0,de.KW)(function(){rt(),We("retro-reaction")},[v]),(0,u.useEffect)(function(){fe()},[]),(0,u.useEffect)(function(){Ce&&(rt(),ke())},[Ce]),Ie?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ve.Z,{title:(0,g.oz)("material-sheet"),open:Ke,className:d.materialsPriceTable,onCancel:function(){return Qe(!1)},footer:!1,centered:!0,width:920,children:(0,a.jsx)(P,{materialsPrice:z})}),(0,a.jsx)(O.Rs,{ghost:!0,loading:we||!(v!=null&&(B=v.reactants)!==null&&B!==void 0&&B.length),className:d.retroReactionTabRoot,pagination:!1,rowClassName:d.retroReactionRowRoot,rowSelection:ae?{onChange:function(Pe){var E=Pe.map(function(Se){return{rxn:Ie[Se],name:oe(Se)}});Pe.length===0&&Q&&(E=Ie.map(function(Se,Ze){return{rxn:Se,name:oe(Ze)}})),ae(E)}}:void 0,metas:{content:{render:function(Pe,E,Se){var Ze,Ge,Ae,Je=!!at&&(0,V.zq)(E,!0)===(0,V.zq)(at,!0);return(0,a.jsxs)("div",{className:te()(d==null?void 0:d.retroReactionRoot,i()({},d.selectable,!!ae)),children:[n&&(0,a.jsx)(k.Z.Text,{type:"secondary",className:d==null?void 0:d.sourceWrapper,children:J(E,n)?(0,g.oz)("from-this-search"):(0,g.oz)("from-other-search")}),(0,a.jsxs)("div",{className:d==null?void 0:d.actionsWrapper,children:[(0,a.jsxs)(l.Z,{className:d==null?void 0:d.leftActionWrapper,children:[(0,a.jsxs)("div",{children:[(0,g.oz)("reaction"),Se+1]}),typeof(E==null?void 0:E.reliability_score)=="number"?(0,a.jsxs)("span",{children:[(0,g.oz)("reaction-reliability"),"\uFF1A",(0,W.round)(E.reliability_score,2)]}):"",(E==null?void 0:E.is_dangerous)&&(0,a.jsx)(s.Z,{color:"error",className:d.riskTag,children:(0,g.oz)("danger-reaction")}),E.is_known_reaction&&(0,a.jsx)(s.Z,{color:"purple",children:(0,g.oz)("reported")}),E.is_selective_risk&&(0,a.jsx)(s.Z,{color:"error",children:(0,g.oz)("regioselectivity")})]}),(0,a.jsxs)(l.Z,{className:d==null?void 0:d.rightActionWrapper,onClick:function(Fe){return Fe.stopPropagation()},children:[le!=null&&(Ze=le.authCodeList)!==null&&Ze!==void 0&&Ze.includes("view-by-backbone.tab.recommend.comment")?(0,a.jsx)(x.Z,{className:d==null?void 0:d.commendButton,type:"link",onClick:function(){return Te({_commendSuject:{id:E==null?void 0:E.id},collection_class:"retro-reaction",reaction_step_no:$e})},size:"small",children:E!=null&&E.content_count&&(E==null?void 0:E.content_count)>0?"".concat((0,g.oz)("comment"),"\uFF08").concat(E==null?void 0:E.content_count,"\uFF09"):(0,g.oz)("comment")}):"",le!=null&&(Ge=le.authCodeList)!==null&&Ge!==void 0&&Ge.includes("view-by-backbone.tab.recommend.add-to-route")&&he?(0,a.jsx)(x.Z,{size:"small",type:"link",disabled:Je,className:d==null?void 0:d.addToRouteButton,onClick:function(){return he((0,V.zq)(E))},children:(0,g.oz)(Je?"added-to-route":"add-to-route")}):null,(0,a.jsxs)("div",{className:te()(d.viwePrice,"flex-align-items-center"),children:[(E==null?void 0:E.material_warning)&&(0,a.jsx)(c.Z,{content:(0,g.oz)("materials-not-available"),children:(0,a.jsx)(ee,{width:18,className:d.warningIcon})}),le!=null&&(Ae=le.authCodeList)!==null&&Ae!==void 0&&Ae.includes("view-by-backbone.tab.recommend.view-materials")?(0,a.jsx)(x.Z,{onClick:m()(M()().mark(function Be(){var Fe,Xe,Ue,qe,nt,it,et;return M()().wrap(function(Ye){for(;;)switch(Ye.prev=Ye.next){case 0:return Ye.next=2,(0,I.query)("material-items/search",{method:"POST",data:{data:{reactants:E==null?void 0:E.reactants}}}).populateWith("material_lib",["name","version","description"]).populateWith("material_tags",["name"]).get();case 2:if(Fe=Ye.sent,Xe=Fe.data,Ue=Fe.error,!(Ue!=null&&Ue.message)){Ye.next=7;break}return Ye.abrupt("return",A.ZP.error(Ue==null?void 0:Ue.message));case 7:qe=Xe,nt=qe.missed,it=qe.found,et=[].concat(h()(it),h()(nt.map(function(tt){return{canonical_smiles:tt}}))),et.sort(function(tt,ot){return tt.canonical_smiles.localeCompare(ot.canonical_smiles)}),lt(et),(0,W.isEmpty)(Xe)||Qe(!0);case 12:case"end":return Ye.stop()}},Be)})),size:"small",type:"link",children:(0,g.oz)("view-materials")}):""]})]})]}),(0,a.jsx)("div",{className:d==null?void 0:d.compoundWrapper,children:(0,a.jsx)(C.default,{className:d==null?void 0:d.smiles,structure:(0,V.zq)(E)})}),(0,a.jsx)("div",{onClick:function(Fe){return Fe.stopPropagation()},children:(0,a.jsx)(w.Z,{commendType:"reaction",onMessageWasSent:ze,hiddenLauncher:Re,isOpen:ye})})]})}}},grid:{column:1},dataSource:n?Ie.sort(function(_e,Pe){var E=J(_e,n),Se=J(Pe,n);return E===Se?_e.updatedAt===Pe.updatedAt?_e.id-Pe.id:(_e.updatedAt||"")>=(Pe.updatedAt||"")?-1:1:E?-1:1}):Ie})]}):(0,a.jsx)(ne.uk,{type:"list"})},Le=pe,ge=function(T,B){return{key:"retro-reaction",label:(0,a.jsx)(G.Z,{title:(0,g.oz)("generated-reaction"),getNumber:m()(M()().mark(function v(){var ce,he,ae;return M()().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(!(!T||!T.reactants.length)){n.next=2;break}return n.abrupt("return");case 2:return n.next=4,(0,I.queryWithDefaultOrder)("retro-reactions/list",{method:"post",data:{filters:T}},["id"]).paginate(1,1).get();case 4:if(ce=n.sent,he=ce.meta,ae=ce.error,!(ae||!(he!=null&&he.pagination))){n.next=9;break}return n.abrupt("return",void 0);case 9:return n.abrupt("return",he.pagination.total);case 10:case"end":return n.stop()}},v)}))}),children:T?(0,a.jsx)(pe,{reaction:T,onSelectProcedure:B}):null}}},33547:function(je,F,e){var L=e(5574),h=e.n(L),y=e(79090),i=e(96486),o=e.n(i),M=e(67294),R=e(85893),p=function(m){var U=m.title,_=m.getNumber,u=m.refetchEvent,b=(0,M.useState)(!1),f=h()(b,2),S=f[0],H=f[1],r=(0,M.useState)(),Z=h()(r,2),ee=Z[0],Y=Z[1];return(0,M.useEffect)(function(){var w=!1;if(_)return Promise.resolve(_()).then(function(C){return!w&&Y((0,i.isNil)(C)?void 0:C)}).finally(function(){return!w&&H(!1)}),function(){w=!0}},[_,u]),(0,R.jsxs)(R.Fragment,{children:[U,S?(0,R.jsx)(y.Z,{}):(0,i.isNil)(ee)?"":"(".concat(ee,")")]})};F.Z=p},85113:function(je,F,e){e.d(F,{Z:function(){return te}});var L=e(97857),h=e.n(L),y=e(5574),i=e.n(y),o=e(15009),M=e.n(o),R=e(99289),p=e.n(R),t=e(42282),m=e(43851),U=e(61487),_=e(69776),u=e(64134),b=e(37507),f=e(87172),S=e(32884),H=function(D,x){var j=Object.entries(D).reduce(function(re,Me){var ie=i()(Me,2),K=ie[0],a=ie[1];return a==="main_reactant"&&!x.includes(K)?re[K]="reactant":re[K]=a,re},{});return Object.values(j).includes("main_reactant")||(j[x[0]]="main_reactant"),j},r=e(37476),Z=e(31199),ee=e(5966),Y=e(5155),w=e(24739),C=e(64317),N=e(34994),V=e(90672),I=e(70831),g=e(31418),de=e(98138),ne=e(28036),O=e(67294),ve=e(11005),k=e(19632),l=e.n(k),s=function(){var W=p()(M()().mark(function D(x,j){var re,Me,ie;return M()().wrap(function(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,(0,f.query)("procedure_parse/from-text",{method:"post",data:{procedure:j.procedure,rxn:j.reference_smiles,patent_id:j.origin.reference.no,title:j.origin.reference.title,rxn_id:j.id||-1},normalizeData:!1}).get().catch();case 2:if(re=a.sent,!(!re||!re.entities)){a.next=6;break}return console.log("No procedureText, skip add others to material table"),a.abrupt("return",x);case 6:if(Me=re.entities.filter(function(P){return P.role==="other"}),Me.length){a.next=9;break}return a.abrupt("return",x);case 9:return ie=[],Me.forEach(function(P){var G=P.canonicalized_smiles||P.normalized_smiles;if(!G){console.warn("not smiles for ".concat(P.name,", skip add to material table"));return}var d=G.split(".").sort().join(".");ie.push({smiles:d,role:"other_reagent",no:"",equivalent:1,unit:"eq"})}),a.abrupt("return",[].concat(l()(x),ie));case 12:case"end":return a.stop()}},D)}));return function(x,j){return W.apply(this,arguments)}}(),c=e(85893),A=function(){var W=p()(M()().mark(function D(x,j){var re;return M()().wrap(function(ie){for(;;)switch(ie.prev=ie.next){case 0:return ie.next=2,(0,f.query)("project-reaction/get-or-create",{method:"post",data:{reaction:x,projectId:j},normalizeData:!1}).get();case 2:return re=ie.sent,ie.abrupt("return",re==null?void 0:re[0]);case 4:case"end":return ie.stop()}},D)}));return function(x,j){return W.apply(this,arguments)}}(),X=function(D){var x=D.projectReaction,j=D.procedure,re=D.open,Me=D.mode,ie=D.onOpenChange,K=D.onCreated,a=D.projectId,P=(0,_.Z)(),G=P.reactionRoleOptions,d=(0,O.useState)(j),oe=i()(d,2),J=oe[0],pe=oe[1],Le=g.Z.useApp(),ge=Le.notification,$=de.Z.useForm(),T=i()($,1),B=T[0],v=(0,U.f)(void 0,!1),ce=v.fetch,he=(0,U.f)(void 0,!1),ae=he.fetch,Q=(0,ve.p6)(x.reaction),n=Q.reactants,q=Q.product,ue=(0,I.useModel)("@@initialState"),Te=ue.initialState,We=(0,O.useState)(!1),Re=i()(We,2),ze=Re[0],Ce=Re[1],ke=function(){var ye=p()(M()().mark(function xe(fe){var le,me,De,Ie,Oe,Ne,be,we,se,Ee,Ke,Qe,He;return M()().wrap(function(z){for(;;)switch(z.prev=z.next){case 0:return De=(0,ve.tN)(fe,"".concat((Te==null||(le=Te.userInfo)===null||le===void 0?void 0:le.id)||-1)),z.next=3,ae((0,f.query)("procedure/create",{normalizeData:!1,method:"post",data:{procedure:De,searchable:!1}}).get());case 3:if(Ie=z.sent,Oe=Ie.data,Oe){z.next=7;break}return z.abrupt("return");case 7:if(Ne=new Set(x.effective_procedures),be=new Set(x.history_procedures),!a){z.next=15;break}return z.next=12,A(x.reaction,a);case 12:we=z.sent,Ne=new Set(we.effective_procedures),be=new Set(we.history_procedures);case 15:return Ne.add(Oe),se=j==null?void 0:j.id,se&&(Ne.delete(se),be.add(se)),z.next=20,(0,f.service)("project-reactions").update(x.id,{effective_procedures:Array.from(Ne.values()),history_procedures:Array.from(be.values())});case 20:return Ee=z.sent,Ke=Ee.data,z.next=24,(0,f.query)("procedure/material_replacement",{method:"post",data:{rxn:(0,u.Pd)(fe.material_table),reference:j==null?void 0:j.reference_smiles,procedure:fe.procedure},normalizeData:!1}).get().catch();case 24:if(z.t1=me=z.sent,z.t0=z.t1===null,z.t0){z.next=28;break}z.t0=me===void 0;case 28:if(!z.t0){z.next=32;break}z.t2=void 0,z.next=33;break;case 32:z.t2=me.ReactantMap;case 33:return Qe=z.t2,z.prev=34,z.next=37,(0,b.kv)({data:{rxn_no:Oe,rxn:De.smiles,project_no:(He=x.project)===null||He===void 0?void 0:He.id,reference_text:De.procedure,material_mapping:Qe||{},materials:De.material_table}});case 37:z.next=42;break;case 39:z.prev=39,z.t3=z.catch(34),console.error("create experiment design failed with ".concat(z.t3));case 42:Ke&&(K==null||K(Ke));case 43:case"end":return z.stop()}},xe,null,[[34,39]])}));return function(fe){return ye.apply(this,arguments)}}(),$e=function(){var ye=p()(M()().mark(function xe(fe){var le,me,De,Ie,Oe,Ne,be=arguments;return M()().wrap(function(se){for(;;)switch(se.prev=se.next){case 0:return le=be.length>1&&be[1]!==void 0?be[1]:!1,se.next=3,ce((0,f.query)("procedure/role",{normalizeData:!1,method:"post",data:{reaction_smiles:fe}}).get());case 3:if(me=se.sent,De=me.data,De){se.next=7;break}return se.abrupt("return");case 7:if(Ie=H(De.role,(0,ve.p6)(x.reaction).reactants),Ie[q]==="product"){se.next=11;break}return ge.error({message:(0,S.oz)("pages.reaction.label.warn.productDiff")}),se.abrupt("return");case 11:if(!n.some(function(Ee){return!Ie[Ee]})){se.next=14;break}return ge.error({message:(0,S.oz)("pages.reaction.label.warn.materialDiff")}),se.abrupt("return");case 14:if(Oe=(0,ve.x5)(fe,Ie,J==null?void 0:J.material_table),!(!le||!(j!=null&&j.procedure))){se.next=18;break}return pe(function(Ee){return Ee?h()(h()({},Ee),{},{smiles:fe,material_table:Oe,temp_smiles:""}):void 0}),se.abrupt("return");case 18:return se.next=20,s(Oe,j);case 20:Ne=se.sent,pe(function(Ee){return Ee?h()(h()({},Ee),{},{smiles:fe,material_table:Ne,temp_smiles:""}):void 0});case 22:case"end":return se.stop()}},xe)}));return function(fe){return ye.apply(this,arguments)}}();return(0,O.useEffect)(function(){if(J){var ye;!(J!=null&&(ye=J.material_table)!==null&&ye!==void 0&&ye.length)&&J.smiles?(Ce(!0),$e(J.smiles,!0).finally(function(){return Ce(!1)})):B.setFieldsValue(J)}},[J]),(0,O.useEffect)(function(){pe(j)},[j]),(0,c.jsxs)(r.Y,{className:"reaction-dialog-root",title:(0,S.oz)("pages.reaction.label.".concat(Me,"Reaction")),form:B,width:1e3,autoFocusFirstInput:!0,open:re,disabled:ze,onOpenChange:ie,modalProps:{destroyOnClose:!0,centered:!0},submitTimeout:2e3,onFinish:function(){var ye=p()(M()().mark(function xe(fe){return M()().wrap(function(me){for(;;)switch(me.prev=me.next){case 0:return me.next=2,ke(fe);case 2:return me.abrupt("return",!0);case 3:case"end":return me.stop()}},xe)}));return function(xe){return ye.apply(this,arguments)}}(),children:[(0,c.jsx)(Z.Z,{width:"md",name:"yields",label:(0,S.oz)("yield"),min:0,max:100,fieldProps:{precision:0,addonAfter:"%"}}),(0,c.jsx)(ee.Z,{name:"temp_smiles",label:(0,S.oz)("pages.reaction.label.createMaterialTableFromRxn"),fieldProps:{style:{width:900}},addonAfter:(0,c.jsx)(c.Fragment,{children:(0,c.jsx)(ne.ZP,{type:"link",onClick:function(){$e(B.getFieldValue("temp_smiles"))},children:(0,S.oz)("pages.route.edit.label.confirm")})})}),(0,c.jsx)(Y.u,{name:"material_table",label:(0,S.oz)("material-sheet"),creatorButtonProps:{creatorButtonText:(0,S.oz)("add-raw-materials")},copyIconProps:!1,actionRender:function(xe,fe,le){var me=B.getFieldValue("material_table")[xe.name];return me.smiles===q||n.includes(me.smiles)?[]:le},children:function(xe){var fe=B.getFieldValue("material_table")[xe.name],le=fe.smiles===q||n.includes(fe.smiles);return(0,c.jsxs)(w.UW,{children:[(0,c.jsx)(C.Z,{disabled:le,name:"role",width:150,label:(0,S.oz)("role"),options:G.filter(function(me){return me.value!=="product"}),required:!0,rules:[{required:!0}]}),(0,c.jsx)(N.A.Item,{className:"filter-form-root",name:"smiles",label:(0,S.oz)("structural"),required:!0,rules:[{required:!0}],children:(0,c.jsx)(t.Z,{disabled:le,multiple:!1})}),(0,c.jsx)(Z.Z,{name:"equivalent",label:(0,S.oz)("EWR"),required:!0,rules:[{required:!0},{pattern:/^(?!0*(\.0{1,2})?$)\d+(\.\d{1,2})?$/,message:(0,S.oz)("enter-two-decimal")}]}),(0,c.jsx)(C.Z,{name:"unit",label:(0,S.oz)("unit"),options:m.Pt,required:!0,rules:[{required:!0}]})]},"group")}}),(0,c.jsx)(V.Z,{disabled:!1,name:"procedure",label:"Procedure",required:!1,fieldProps:{autoSize:{minRows:5,maxRows:8}}})]})},te=X},84898:function(){},89277:function(je,F,e){var L,h=e(64836).default,y=e(75263).default;L={value:!0},F.Z=void 0;var i=y(e(67294)),o=e(13594),M=h(e(28460)),R=function(m,U){var _={};for(var u in m)Object.prototype.hasOwnProperty.call(m,u)&&U.indexOf(u)<0&&(_[u]=m[u]);if(m!=null&&typeof Object.getOwnPropertySymbols=="function")for(var b=0,u=Object.getOwnPropertySymbols(m);b<u.length;b++)U.indexOf(u[b])<0&&Object.prototype.propertyIsEnumerable.call(m,u[b])&&(_[u[b]]=m[u[b]]);return _};const p=i.forwardRef((m,U)=>{var{ellipsis:_,rel:u}=m,b=R(m,["ellipsis","rel"]);const f=Object.assign(Object.assign({},b),{rel:u===void 0&&b.target==="_blank"?"noopener noreferrer":u});return delete f.navigate,i.createElement(M.default,Object.assign({},f,{ref:U,ellipsis:!!_,component:"a"}))});var t=F.Z=p}}]);

//# sourceMappingURL=shared-xkO2Ze4y6Sd3lkRKyQOPXRZM8uM_.1235023a.async.js.map