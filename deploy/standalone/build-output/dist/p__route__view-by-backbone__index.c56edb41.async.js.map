{"version": 3, "file": "p__route__view-by-backbone__index.c56edb41.async.js", "mappings": "qgBA6CMA,GAAuB,SAAHC,GAAW,KAAAC,EAAAC,EAAAC,EAAAC,EAAAC,GAAAA,EAAAL,EAAA,EACnC,IAAAM,MAAmCC,EAAAA,WAAkC,EAACC,EAAAF,GAA9DG,WAAYC,GAAKF,IAAA,OAAG,GAAEA,EAC9BG,EAAyCC,GAAAA,EAAIC,OAAO,EAA5CC,EAAOH,EAAPG,QAASC,EAAYJ,EAAZI,aAAcC,EAAKL,EAALK,MAC/BC,MACEC,EAAAA,UAA+B,IAAI,EAACC,EAAAC,EAAAA,EAAAH,GAAA,GAD/BI,EAAkBF,EAAA,GAAEG,EAAqBH,EAAA,GAEhDI,MAAkCL,EAAAA,UAA6B,CAAC,CAAC,EAACM,GAAAJ,EAAAA,EAAAG,GAAA,GAA3DE,EAASD,GAAA,GAAEE,GAAYF,GAAA,GAC9BG,MAA8BT,EAAAA,UAA2B,EAACU,GAAAR,EAAAA,EAAAO,GAAA,GAAnDE,GAAOD,GAAA,GAAEE,GAAUF,GAAA,GAC1BG,MAAgCb,EAAAA,UAAkB,EAAK,EAACc,GAAAZ,EAAAA,EAAAW,GAAA,GAAjDE,EAAQD,GAAA,GAAEE,GAAWF,GAAA,GAC5BG,MAAsBjB,EAAAA,UAAiB,GAAG,EAACkB,GAAAhB,EAAAA,EAAAe,GAAA,GAApCE,EAAGD,GAAA,GAAEE,GAAMF,GAAA,GAClBG,MAAoCrB,EAAAA,UAAiB,EAACsB,GAAApB,EAAAA,EAAAmB,GAAA,GAA/CE,EAAUD,GAAA,GAAEE,GAAaF,GAAA,GAChCG,MAA4CzB,EAAAA,UAAiB,EAAC0B,GAAAxB,EAAAA,EAAAuB,GAAA,GAAvDE,GAAcD,GAAA,GAAEE,GAAiBF,GAAA,GACxCG,MAAgC7B,EAAAA,UAA0B,EAAC8B,GAAA5B,EAAAA,EAAA2B,GAAA,GAApDE,EAAQD,GAAA,GAAEE,GAAWF,GAAA,GAC5BG,MACEjC,EAAAA,UAA8B,CAAC,CAAC,EAACkC,GAAAhC,EAAAA,EAAA+B,GAAA,GAD5BE,GAAmBD,GAAA,GAAEE,GAAsBF,GAAA,GAElDG,MAAkCrC,EAAAA,UAAiB,EAACsC,GAAApC,EAAAA,EAAAmC,GAAA,GAA7CE,EAASD,GAAA,GAAEE,GAAYF,GAAA,GAC9BG,MAA0CzC,EAAAA,UAAsB,EAAC0C,GAAAxC,EAAAA,EAAAuC,GAAA,GAA1DE,GAAaD,GAAA,GAAEE,GAAgBF,GAAA,GACtCG,MAA4C7C,EAAAA,UAAgC,EAAC8C,GAAA5C,EAAAA,EAAA2C,GAAA,GAAtEE,GAAcD,GAAA,GAAEE,GAAiBF,GAAA,GAClCG,MAAoBC,GAAAA,GAAkB,SAACC,EAAG,CAAF,OAAKA,EAAEF,iBAAiB,GACtEG,MAAkBC,GAAAA,GAAc,EAAxBC,GAAKF,GAALE,MACRC,KASIC,EAAAA,UAAS,SAAS,EARpBC,GAAcF,EAAdE,eACAC,GAAmBH,EAAnBG,oBACAC,GAAYJ,EAAZI,aACAC,GAAWL,EAAXK,YACAC,GAAMN,EAANM,OACAC,GAAcP,EAAdO,eACAC,GAAsBR,EAAtBQ,uBACAC,GAAMT,EAANS,OAEFC,MAAqBC,EAAAA,aAAY,EAAzBC,GAAQF,GAARE,YACRC,EAAAA,WAAU,UAAM,CACdV,OAAAA,GAAoB,gBAAgB,EAC7B,UAAM,CACPM,IAAQL,GAAa,CAC3B,CACF,EAAG,CAAC,CAAC,EACL,IAAAU,MAAoBC,EAAAA,IAAe,EAA3BC,GAAOF,GAAPE,QACFC,MAAYC,GAAAA,GAAalC,EAAWhB,EAAYpB,GAAkB,YAAlBA,EAAoBuE,EAAE,EAE5EC,MAAgD3E,EAAAA,UAC9CO,EAAU,CAAC,CACb,EAACqE,GAAA1E,EAAAA,EAAAyE,GAAA,GAFME,EAAgBD,GAAA,GAAEE,EAAmBF,GAAA,GAGtCG,KAASC,EAAAA,WAAU,EACzBC,MAAkCjF,EAAAA,UAAwB,CAAC,CAAC,EAACkF,GAAAhF,EAAAA,EAAA+E,GAAA,GAAtDE,GAASD,GAAA,GAAEE,EAAYF,GAAA,GAC9BG,MAA4CrF,EAAAA,UAA8B,CAAC,CAAC,EAACsF,GAAApF,EAAAA,EAAAmF,GAAA,GAAtEE,GAAcD,GAAA,GAAEE,GAAiBF,GAAA,GACxCG,KAKIC,GAAAA,GAAkB/E,EAAO,EAJ3BgF,EAAgBF,EAAhBE,iBACAC,EAAgBH,EAAhBG,iBACAC,EAAYJ,EAAZI,aACAC,GAAoBL,EAApBK,qBAEIC,MAAYC,EAAAA,IAAanB,CAAgB,EACzCoB,GAAc,SAACC,EAAoC,CACvD,IAAMC,GAASD,GAAI,YAAJA,EAAMxB,KAAMqB,GAAUK,IAAIF,EAAKxB,EAAE,EAChD,GAAIyB,EAAQ,CACV,IAAIE,KAAmBC,EAAAA,IAAkBH,EAAO,CAAC,EAAGA,EAAO,CAAC,CAAC,EAC7DpC,OAAAA,GAAuBsC,CAAgB,EAChC,GAAPE,UAAUC,EAAAA,IAAQ,UAAU,EAAC,KAAAD,OAAIF,CAAgB,CACnD,CACA,SAAOG,EAAAA,IAAQ,UAAU,CAC3B,EACAC,MAAqBC,GAAAA,MACnBC,EAAAA,IAAmB9B,CAAgB,EACnCtC,CACF,EAHQqE,GAAGH,GAAHG,IAAKC,GAAGJ,GAAHI,IAIPnC,EAAKoC,OAAOC,SAASvH,EAAK,EAE1BwH,GAAmB,eAAAC,EAAAC,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAC,EAAO3C,EAAY,CAAF,IAAA4C,EAAAC,EAAAC,EAAAC,EAAA,OAAAN,EAAAA,EAAA,EAAAO,KAAA,SAAAC,EAAE,CAAF,cAAAA,EAAAC,KAAAD,EAAAE,KAAE,CAAF,OAAAF,OAAAA,EAAAE,KAAA,KACbC,EAAAA,SAAO,mBAAAvB,OAChB7B,CAAE,EACrB,CACEqD,YAAa,6BAAFxB,OAA+B7B,EAAE,KAAA6B,OAAIO,OAAO3F,CAAG,EAAI,CAAC,CACjE,CACF,EACG6G,OAAO,CAAC,aAAc,YAAY,CAAC,EACnCC,aAAa,CACZ,CACEC,KAAM,gBACNC,OAAQ,CAAC,IAAI,EACbC,SAAU,CAAC,CAAEC,IAAK,mBAAoBF,OAAQ,CAAC,IAAI,CAAE,CAAC,CACxD,CAAC,CACF,EACA/B,IAAI,EAAC,OAdW,GAcXkB,EAAAK,EAAAW,KAdAf,EAAID,EAAJC,KAAMC,EAAKF,EAALE,MAAK,CAefA,EAAO,CAAFG,EAAAE,KAAA,QACPjI,EAAQ4H,MAAMA,EAAM5H,OAAO,EAAC+H,EAAAE,KAAA,mBAClBN,EAAM,CAAFI,EAAAE,KAAA,SACdjI,EAAQ4H,MAAM,0BAADjB,OAA2B7B,EAAE,aAAY,EAACiD,EAAAE,KAAA,iBAElC,GAAfJ,EAAWF,EACZE,EAASc,WAAWC,OAAQ,CAAFb,EAAAE,KAAA,SAC7BjI,EAAQ4H,MAAM,0BAADjB,OAA2B7B,EAAE,qBAAoB,EAACiD,EAAAE,KAAA,wBAAAF,EAAAc,OAAA,SAExDhB,CAAQ,iBAAAE,EAAAc,OAAA,SAGZ,IAAI,2BAAAd,EAAAe,KAAA,IAAArB,CAAA,EACZ,mBA7BwBsB,EAAA,QAAA1B,EAAA2B,MAAA,KAAAC,SAAA,MA+BnBC,GAAa,eAAAC,EAAA7B,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAA4B,EACpBtE,EAAU,KAAAuE,EAAA1B,EAAA,OAAAJ,EAAAA,EAAA,EAAAO,KAAA,SAAAwB,EAAA,eAAAA,EAAAtB,KAAAsB,EAAArB,KAAA,QAAAqB,OAAAA,EAAArB,KAAA,EAEavE,MACrBwE,EAAAA,SAAyB,mBAAmB,EACzCqB,eAAe,CAACzE,CAAE,CAAC,EACnB0E,aAAa,UAAW,CAAC,IAAI,CAAC,EAC9BhD,IAAI,CACT,EAAC,OAAA6C,OAAAA,EAAAC,EAAAZ,KALOf,EAAI0B,EAAJ1B,KAAI2B,EAAAT,OAAA,SAMLlB,GAAI,YAAJA,EAAO,CAAC,CAAC,0BAAA2B,EAAAR,KAAA,IAAAM,CAAA,EACjB,mBAVkBK,EAAA,QAAAN,EAAAH,MAAA,KAAAC,SAAA,MAYbS,GAAU,eAAAC,EAAArC,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAoC,GAAA,KAAAC,EAAAC,EAAAnC,EAAAhF,EAAAI,EAAA,OAAAwE,EAAAA,EAAA,EAAAO,KAAA,SAAAiC,EAAA,eAAAA,EAAA/B,KAAA+B,EAAA9B,KAAA,WACZtG,EAAY,CAAFoI,EAAA9B,KAAA,eAAA8B,EAAAlB,OAAA,iBAAAkB,OAAAA,EAAA9B,KAAA,EACIiB,GAAcvH,CAAU,EAAC,OAAtCgG,EAAIoC,EAAArB,KACVtG,GAAYuF,CAAI,EAChBvE,GAAkBuE,GAAI,YAAJA,EAAMqC,MAAM,EACxBrH,EAAYgF,GAAI,OAAAkC,EAAJlC,EAAMsC,WAAO,MAAAJ,IAAA,cAAbA,EAAe/E,GAC3B/B,EAAgB4E,GAAI,OAAAmC,EAAJnC,EAAMsC,WAAO,MAAAH,IAAA,cAAbA,EAAeE,OACrChH,GAAiBD,CAAa,EAC1BJ,GACFC,GAAaD,CAAS,EACvB,yBAAAoH,EAAAjB,KAAA,IAAAc,CAAA,EACF,oBAXe,QAAAD,EAAAX,MAAA,KAAAC,SAAA,MAaViB,GAAY,eAAAC,EAAA7C,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAA4C,GAAA,KAAAC,EAAA1C,EAAA2C,EAAA,OAAA/C,EAAAA,EAAA,EAAAO,KAAA,SAAAyC,EAAA,eAAAA,EAAAvC,KAAAuC,EAAAtC,KAAA,QAAAsC,OAAAA,EAAAtC,KAAA,KACIC,EAAAA,SAAO,qBAAAvB,OACPhF,EAAU,WAC/B,CACE6I,OAAQ,OACR7C,KAAM,CAAEhI,WAAYmF,EAAI2F,sBAAuBvD,OAAOC,SAAS5F,CAAG,CAAE,CACtE,CACF,EACG6G,OAAO,EACP5B,IAAI,EAAC,OAAA6D,OAAAA,EAAAE,EAAA7B,KARAf,EAAI0C,EAAJ1C,KAAI4C,EAAAtC,KAAA,EASNyB,GAAW,EAAC,OAEK,GAAjBY,EAAQ3C,GAAI,YAAJA,EAAO,CAAC,EAEjB2C,EAAO,CAAFC,EAAAtC,KAAA,eAAAsC,EAAA1B,OAAA,iBACV5I,EAAayK,QAAQ,CACnBC,KAAM,UACN3K,WAAS4G,EAAAA,IAAQ,wBAAwB,EACzCgE,SAAU,EACVC,eACEC,EAAAA,KAACC,GAAAA,EAAWC,KAAI,CACdC,QAAS,UAAM,KAAAC,EAAAC,EACblL,EAAamL,QAAQ,EACrBC,EAAAA,QAAQC,KAAK,aAAD3E,QAAAuE,EACGZ,EAAMiB,oBAAgB,MAAAL,IAAA,SAAAA,EAAtBA,EAAwBjB,WAAO,MAAAiB,IAAA,cAA/BA,EAAiCpG,GAAE,cAAA6B,QAAAwE,EAAab,EAAMiB,oBAAgB,MAAAJ,IAAA,cAAtBA,EAAwBrG,GAAE,UAAA6B,OAAS2D,EAAMxF,EAAE,CAC1G,CACF,EAAE0D,YAED5B,EAAAA,IAAQ,eAAe,CAAC,CACV,CAErB,CAAC,EAAC,yBAAA2D,EAAAzB,KAAA,IAAAsB,CAAA,EACH,oBAhCiB,QAAAD,EAAAnB,MAAA,KAAAC,SAAA,MAkCZuC,GAAmB,eAAAC,EAAAnE,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAkE,GAAA,QAAAnE,EAAAA,EAAA,EAAAO,KAAA,SAAA6D,EAAA,eAAAA,EAAA3D,KAAA2D,EAAA1D,KAAA,QAC1B/H,EAAM0L,QAAQ,CACZC,SAAOjF,EAAAA,IAAQ,cAAc,EAC7BkF,WAASlF,EAAAA,IAAQ,kBAAkB,EACnCmF,KAAM7B,EACR,CAAC,EAAC,wBAAAyB,EAAA7C,KAAA,IAAA4C,CAAA,EACH,oBANwB,QAAAD,EAAAzC,MAAA,KAAAC,SAAA,SAQzBzE,EAAAA,WAAU,UAAM,CACdwH,QAAQC,IAAI,CAAC7E,GAAoBtC,CAAE,KAAGoH,EAAAA,IAAiB,CAAC,CAAC,EAAEC,KACzD,SAAAC,EAAqB,KAAAC,EAAA/L,EAAAA,EAAA8L,EAAA,GAAnBzE,EAAI0E,EAAA,GAAE1H,EAAO0H,EAAA,GACb,GAAI1E,EAAM,KAAA2E,EAAAC,EAAAC,EAAAC,EACJC,EAAQ/E,EAAKgB,WAAW3B,IAAI,SAAC2F,EAAG,CAAF,SAAKC,EAAAA,IAAkBD,CAAC,CAAC,IAEzDhI,GAAO,OAAA2H,EAAP3H,EAASkI,SAAK,MAAAP,IAAA,cAAdA,EAAgBQ,4BAA6B,eAC7CP,EAAA5E,EAAKoF,cAAU,MAAAR,IAAA,cAAfA,EAAiB3D,YAAM4D,EAAK7E,EAAKgB,cAAU,MAAA6D,IAAA,cAAfA,EAAiB5D,UAE7C8D,EAAQ/E,EAAKoF,WAAW/F,IAAI,SAAC2F,EAAG,CAAF,SAAKC,EAAAA,IAAkBD,CAAC,CAAC,IAEzDnM,EAAsBmH,CAAI,EAC1B/F,IAAa6K,EAAC9E,EAAKqF,cAAczB,oBAAgB,MAAAkB,IAAA,cAAnCA,EAAqC3H,EAAE,EACrD9C,GAAkB2F,EAAKqF,cAAclI,EAAE,EACvClE,GAAa8L,CAAK,EAClBxH,EAAoBwH,EAAM,CAAC,CAAC,CAC9B,CACF,CACF,CACF,EAAG,CAAC5H,CAAE,CAAC,EAEP,IAAMmI,MAAoBC,EAAAA,IACxB1K,GACAzB,GACAiF,CACF,EAEMmH,GAAmB,UAAM,CAC7B3M,EAAsB,IAAI,EAC1B4G,GAAoBtC,CAAE,EAAEqH,KAAK,SAACxE,EAAS,CACjCA,GACFnH,EAAsBmH,CAAI,CAE9B,CAAC,CACH,EAkBA,MAhBAnD,EAAAA,WAAU,UAAM,CACVP,KACFkJ,GAAiB,EACjBjJ,GAAe,EAEnB,EAAG,CAACD,EAAM,CAAC,KAEXO,EAAAA,WAAU,UAAM,CACd2I,GAAiB,EACjB9J,GAAkB,GAADsD,UAAIC,EAAAA,IAAQ,QAAQ,EAAC,KAAAD,OAAI7B,EAAE,KAAA6B,OAAIO,OAAOC,SAAS5F,CAAG,EAAI,CAAC,CAAE,CAC5E,EAAG,CAACA,CAAG,CAAC,KAERiD,EAAAA,WAAU,UAAM,CACdkF,GAAW,CACb,EAAG,CAAC/H,CAAU,CAAC,EAEXuF,OAAOkG,MAAMtI,CAAE,EACjBuG,OAAAA,EAAAA,QAAQC,KAAK,MAAM,KACZR,EAAAA,KAAAuC,EAAAA,SAAA,EAAI,EAGb,GAAIlM,EACF,SACE2J,EAAAA,KAACwC,GAAAA,QAAS,CACR3K,UAAWA,EACXZ,eAAgBA,GAChBwL,YAAUC,EAAAA,WAAUvI,CAAgB,EACpCwI,SAAU,UAAM,CACd1H,EAAiB,EAAK,EACtBP,EAAa,CAAC,CAAC,EACfhD,GAAuB,CAAC,CAAC,EACzBpB,GAAY,EAAK,EACjBJ,MAAWwM,EAAAA,WAAUvI,CAAgB,CAAC,CACxC,EACAyI,gBAAiB5I,EACjBnD,WAAYA,CAAW,CACxB,EAGL,IAAMgM,GAAwBpJ,GAASqJ,SAAS,aAAa,EAC7D,SACEC,EAAAA,MAACC,GAAAA,GAAa,CACZC,UAAU,8BACVlC,SAAOjF,EAAAA,IAAQ,mBAADD,OAAoBxF,EAAW,OAAS,MAAM,CAAE,EAC9D6M,OAAQ,CACNnC,SAAOjF,EAAAA,IAAQ,mBAADD,OAAoBxF,EAAW,OAAS,MAAM,CAAE,CAChE,EACA8M,QAAStN,EAAUqG,IAAI,SAACkH,EAAGC,EAAO,CAAF,MAAM,CACpC5M,IAAK,GAAFoF,OAAK7B,EAAE,KAAA6B,OAAIwH,EAAQ,CAAC,EACvB1F,IAAK,GAAF9B,OAAKwH,CAAK,CACf,CAAC,CAAC,EACFC,SAAU,CAAEzD,KAAM,MAAO,EACzB0D,aAAc9M,EACd+M,YAAa,SAAC7F,EAAQ,CACpBjH,GAAOiH,CAAG,EACVvD,EAAoBvE,EAAUuG,OAAOC,SAASsB,CAAG,CAAC,CAAC,CACrD,EAAED,SAAA,IAEFsC,EAAAA,KAAA,OAAKiD,UAAU,YAAWvF,SACvBvD,MACC4I,EAAAA,MAAAR,EAAAA,SAAA,CAAA7E,SAAA,IACEsC,EAAAA,KAACyD,GAAAA,GAAW,CACVC,KAAMvJ,EACNwJ,cAAelJ,GACfmJ,aAAc,SAAC/B,EAAM,CACnB3L,GAAW2L,CAAC,EACR1G,GAAY,MAAZA,EAAcnB,IAAIiB,EAAiBE,EAAanB,GAAI6H,CAAC,CAC3D,EACAxL,SAAU,GACVwN,YAAa5I,EACbJ,eAAgBA,GAChBpD,oBAAqBA,GACrBqM,aAAazP,EAAAwF,GAAQkI,SAAK,MAAA1N,IAAA,QAAbA,EAAe0P,kBAAoB7H,GAAM8H,OACtDC,gBACElB,EAAAA,MAACmB,GAAAA,EAAK,CAACC,KAAK,SAAQzG,SAAA,EACjBrD,GAAM,OAAA/F,EAAN+F,EAAQ+J,gBAAY,MAAA9P,IAAA,cAApBA,EAAsBwO,SACrB,gCACF,OAAK9C,EAAAA,KAACqE,GAAAA,EAAiB,EAAE,EACxB,IAACC,EAAAA,IAAmBrM,GAAeI,EAAc,GAChD,CAACwK,MACAxL,GAAQ,YAARA,EAAUwI,QAAS,gBAClBG,EAAAA,KAACuE,EAAAA,EAAiB,CAChBpE,QAASO,GACTyD,KAAK,QACLtE,KAAK,UAASnC,YAEb5B,EAAAA,IAAQ,QAAQ,CAAC,CACD,KAEnBiH,EAAAA,MAAAR,EAAAA,SAAA,CAAA7E,SAAA,EACGrD,GAAM,OAAA9F,EAAN8F,EAAQ+J,gBAAY,MAAA7P,IAAA,cAApBA,EAAsBuO,SACrB,iCACF,OACE9C,EAAAA,KAACuE,EAAAA,EAAiB,CAChBpE,QAAS,UAAM,CACbpH,GAAe,CACbyL,eAAgB,CACdxK,GAAI,GAAF6B,OAAK7B,EAAE,KAAA6B,OAAIO,OAAO3F,CAAG,EAAI,CAAC,CAC9B,EACAgO,iBAAkB,gBACpB,CAAC,CACH,EACAN,KAAK,QACLtE,KAAK,UAASnC,SAEbjI,GAAkB,MAAlBA,EAAoBiP,gBACrBjP,GAAkB,YAAlBA,EAAoBiP,eAAgB,EAAC,GAAA7I,UAC9BC,EAAAA,IAAQ,SAAS,EAAC,UAAAD,OACnBpG,GAAkB,YAAlBA,EAAoBiP,cAAa,aAEnC5I,EAAAA,IAAQ,SAAS,CAAC,CACL,GAEpBzB,GAAM,OAAA7F,EAAN6F,EAAQ+J,gBAAY,MAAA5P,IAAA,cAApBA,EAAsBsO,SACrB,8BACF,OACE9C,EAAAA,KAACuE,EAAAA,EAAiB,CAChBpE,QAAS,kBACPzF,EAAa,CACXiK,SAAU,SAACnF,EAAU,CACfA,GAAOpF,EAAoBoF,CAAK,EACpClJ,GAAY,EAAI,EAChBoE,EAAa,CAAC,CAAC,CACjB,CACF,CAAC,CAAC,EAEJyJ,KAAK,QACLtE,KAAK,UAASnC,YAEb5B,EAAAA,IAAQ,MAAM,CAAC,CACC,KAGrBkE,EAAAA,KAACuE,EAAAA,EAAiB,CAChBpE,QAAO3D,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAkI,GAAA,QAAAnI,EAAAA,EAAA,EAAAO,KAAA,SAAA6H,EAAA,eAAAA,EAAA3H,KAAA2H,EAAA1H,KAAA,eAAA0H,EAAA9G,OAAA,SACA,IAAImD,QAAQ,SAAC4D,EAASC,EAAW,CACtC3P,EAAM0L,QAAQ,CACZC,SAAOjF,EAAAA,IAAQ,mBAAmB,EAClCkF,WAASlF,EAAAA,IAAQ,eAAe,EAChCmF,KAAM,kBACJvG,EAAa,CACXiK,SAAU,UAAF,KAAAK,EAAAxI,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAuI,EAAOzF,EAAO,CAAF,OAAA/C,EAAAA,EAAA,EAAAO,KAAA,SAAAkI,EAAE,CAAF,cAAAA,EAAAhI,KAAAgI,EAAA/H,KAAE,CAAF,UACfqC,EAAO,CAAF0F,EAAA/H,KAAA,eAAA+H,EAAAnH,OAAA,iBAAAmH,OAAAA,EAAA/H,KAAA,EACJrD,MACJgI,EAAAA,IAAkBtC,CAAK,EACvB,SACF,EAAC,OACDsF,EAAQ,EAAC,wBAAAI,EAAAlH,KAAA,IAAAiH,CAAA,EACV,YAAAN,EAAAQ,EAAA,QAAAH,EAAA9G,MAAA,KAAAC,SAAA,SAAAwG,CAAA,GACH,CAAC,CAAC,EACJhC,SAAU,kBAAMoC,EAAO,CAAC,CAC1B,CAAC,CACH,CAAC,CAAC,0BAAAF,EAAA7G,KAAA,IAAA4G,CAAA,EACH,GACDT,KAAK,QACLtE,KAAK,UAASnC,YAEb5B,EAAAA,IAAQ,gCAAgC,CAAC,CACzB,CAAC,EACpB,EACF,EACC,CACR,CACF,KACDkE,EAAAA,KAACoF,GAAAA,EAAQ,CACPC,iBAAkBnM,GAClBoM,eAAgBrM,GAChBK,OAAQA,EAAO,CAChB,CAAC,EACF,CACH,CACE,KAEL0G,EAAAA,KAACuF,GAAAA,EAAc,CACb1N,UAAWA,EACX2N,SAAUtK,EACV6F,MAAOxF,GAAYJ,CAAY,EAC/BsK,QAAS,kBAAM3K,GAAkB,CAAC,CAAC,CAAC,EACpC7D,eAAgBA,GAChBkL,kBAAmBA,GACnBuD,aAActK,GACduK,SAAU,kBACRzK,GAAoBiB,MAAIyJ,EAAAA,IAAmB1K,CAAgB,CAAC,CAAC,EAE/D2K,eACE1L,GACAgB,MACA2K,EAAAA,IAAkB3L,EAAkBgB,EAAc,SAACK,EAAS,CAC1DP,EAAiBO,EAAKxB,EAAE,EACxBc,GAAkB,CAAEwC,OAAQ9B,EAAKxB,EAAG,CAAC,CACvC,CAAC,CACF,CACF,CAAC,EACW,CAEnB,EAEA,GAAe7F,E,oBC3bf,SAASM,EAA0BsR,EAAK,CACtC,GAAIA,GAAO,KAAM,MAAM,IAAI,UAAU,sBAAwBA,CAAG,CAClE,CACAC,EAAO,QAAUvR,EAA2BuR,EAAO,QAAQ,WAAa,GAAMA,EAAO,QAAQ,QAAaA,EAAO,O", "sources": ["webpack://labwise-web/./src/pages/route/view-by-backbone/index.tsx", "webpack://labwise-web/./node_modules/@umijs/babel-preset-umi/node_modules/@babel/runtime/helpers/objectDestructuringEmpty.js"], "sourcesContent": ["import ButtonWithLoading from '@/components/ButtonWithLoading'\nimport Launcher from '@/components/Launcher'\nimport {\n  GetRouteEvent,\n  RouteEditor,\n  UpdateChildrenEvent\n} from '@/components/RouteEditor'\nimport { useRouteTreeStore } from '@/components/RouteEditor/Flex/Tree/store'\nimport { useBrainFetch } from '@/hooks/useBrainFetch'\nimport { fetchUserSetting, useUserSetting } from '@/hooks/useUserSetting'\nimport type {\n  ProjectCompoundStatus,\n  ProjectRoute,\n  ProjectType\n} from '@/services/brain'\nimport { ProjectCompound, RetroBackbone, service } from '@/services/brain'\nimport { getWord, isReadonlyMolecule } from '@/utils'\nimport { PageContainer } from '@ant-design/pro-components'\nimport {\n  history,\n  useAccess,\n  useLocation,\n  useModel,\n  useParams\n} from '@umijs/max'\nimport { App, Space, Typography } from 'antd'\nimport { cloneDeep } from 'lodash'\nimport React, { useEffect, useState } from 'react'\nimport ExportRouteButton from '../components/ExportRouteButton'\nimport ReactionDrawer from '../components/ReactionDrawer'\nimport EditRoute from '../edit'\nimport { useReactionDrawer } from '../hooks/useReactionDrawer'\nimport { useSaveRoute } from '../saveRoute'\nimport { useRxnYieldMap } from '../useRxnYieldMap'\nimport normalizeMainTree, {\n  MainTreeForRoute,\n  getAllRxnsFromTree,\n  getDeepthMap,\n  getNameOfReaction,\n  getNavigateConfig,\n  getRxnFromReaction,\n  selectProcedure\n} from '../util'\nimport './index.less'\n\nconst ViewRoutes: React.FC = ({}) => {\n  const { backboneId: idStr = '' } = useParams<{ backboneId: string }>()\n  const { message, notification, modal } = App.useApp()\n  const [retroBackbonesData, setRetroBackbonesData] =\n    useState<RetroBackbone | null>(null)\n  const [mainTrees, setMainTrees] = useState<MainTreeForRoute[]>([])\n  const [curTree, setCurTree] = useState<MainTreeForRoute>()\n  const [editMode, setEditMode] = useState<boolean>(false)\n  const [tab, setTab] = useState<string>('0')\n  const [compoundId, setCompoundId] = useState<number>()\n  const [retroProcessId, setRetroProcessId] = useState<number>()\n  const [compound, setCompound] = useState<ProjectCompound>()\n  const [updateChildrenEvent, setUpdateChildrenEvent] =\n    useState<UpdateChildrenEvent>({})\n  const [projectId, setProjectId] = useState<number>()\n  const [projectStatus, setProjectStatus] = useState<ProjectType>()\n  const [compoundStatus, setCompoundStatus] = useState<ProjectCompoundStatus>()\n  const setExportFileName = useRouteTreeStore((s) => s.setExportFileName)\n  const { fetch } = useBrainFetch()\n  const {\n    getProfileInfo,\n    getCommonExpression,\n    showLauncher,\n    sendMessage,\n    reload,\n    finishedReload,\n    cacheCurReactionStepNo,\n    isOpen\n  } = useModel('commend')\n  const { pathname } = useLocation()\n  useEffect(() => {\n    getCommonExpression('retro-backbone')\n    return () => {\n      if (isOpen) showLauncher()\n    }\n  }, [])\n  const { setting } = useUserSetting()\n  const saveRoute = useSaveRoute(projectId, compoundId, retroBackbonesData?.id)\n\n  const [selectedMainTree, setSelectedMainTree] = useState<MainTreeForRoute>(\n    mainTrees[0]\n  )\n  const access = useAccess()\n  const [saveEvent, setSaveEvent] = useState<GetRouteEvent>({})\n  const [selectRxnEvent, setSelectRxnEvent] = useState<{ select?: string }>({})\n  const {\n    onSelectReaction,\n    selectedReaction,\n    selectedNode,\n    selectedMainReaction\n  } = useReactionDrawer(curTree)\n  const deepthMap = getDeepthMap(selectedMainTree)\n  const getStepName = (node?: MainTreeForRoute): string => {\n    const deepth = node?.id && deepthMap.get(node.id)\n    if (deepth) {\n      let reaction_step_no = getNameOfReaction(deepth[0], deepth[1])\n      cacheCurReactionStepNo(reaction_step_no)\n      return `${getWord('reaction')} ${reaction_step_no}`\n    }\n    return getWord('reaction')\n  }\n  const { map, add } = useRxnYieldMap(\n    getAllRxnsFromTree(selectedMainTree),\n    projectId\n  )\n  const id = Number.parseInt(idStr)\n\n  const fetchRetroBackbones = async (id: number) => {\n    const { data, error } = await service<RetroBackbone>(\n      `retro-backbones/${id}`,\n      {\n        customQuery: `comment=true&collectionId=${id}-${Number(tab) + 1}`\n      }\n    )\n      .select(['main_trees', 'full_trees'])\n      .populateDeep([\n        {\n          path: 'retro_process',\n          fields: ['id'],\n          children: [{ key: 'project_compound', fields: ['id'] }]\n        }\n      ])\n      .get()\n    if (error) {\n      message.error(error.message)\n    } else if (!data) {\n      message.error(`Retro backbone with id ${id} not found`)\n    } else {\n      const backbone = data as unknown as RetroBackbone\n      if (!backbone.main_trees.length) {\n        message.error(`Retro backbone with id ${id} has no main_trees`)\n      } else {\n        return backbone\n      }\n    }\n    return null\n  }\n\n  const fetchCompound = async (\n    id: number\n  ): Promise<ProjectCompound | undefined> => {\n    const { data } = await fetch(\n      service<ProjectCompound>('project-compounds')\n        .selectManyByID([id])\n        .populateWith('project', ['id'])\n        .get()\n    )\n    return data?.[0]\n  }\n\n  const updateData = async () => {\n    if (!compoundId) return\n    const data = await fetchCompound(compoundId)\n    setCompound(data)\n    setCompoundStatus(data?.status)\n    const projectId = data?.project?.id\n    const projectStatus = data?.project?.status as ProjectType\n    setProjectStatus(projectStatus)\n    if (projectId) {\n      setProjectId(projectId)\n    }\n  }\n\n  const handleSelect = async () => {\n    const { data } = await service<ProjectRoute>(\n      `project-compounds/${compoundId}/select`,\n      {\n        method: 'post',\n        data: { backboneId: id, selectedMainTreeIndex: Number.parseInt(tab) }\n      }\n    )\n      .select()\n      .get()\n    await updateData()\n\n    const route = data?.[0]\n\n    if (!route) return\n    notification.success({\n      type: 'success',\n      message: getWord('replaced-route-success'),\n      duration: 3,\n      description: (\n        <Typography.Link\n          onClick={() => {\n            notification.destroy()\n            history.push(\n              `/projects/${route.project_compound?.project?.id}/compound/${route.project_compound?.id}/edit/${route.id}`\n            )\n          }}\n        >\n          {getWord('continue-edit')}\n        </Typography.Link>\n      )\n    })\n  }\n\n  const handleConfirmSelect = async () => {\n    modal.confirm({\n      title: getWord('choose-route'),\n      content: getWord('choose-route-tip'),\n      onOk: handleSelect\n    })\n  }\n\n  useEffect(() => {\n    Promise.all([fetchRetroBackbones(id), fetchUserSetting()]).then(\n      ([data, setting]) => {\n        if (data) {\n          let trees = data.main_trees.map((t) => normalizeMainTree(t))\n          if (\n            setting?.retro?.route_detail_show_policy === 'all_route' &&\n            data.full_trees?.length === data.main_trees?.length\n          ) {\n            trees = data.full_trees.map((t) => normalizeMainTree(t))\n          }\n          setRetroBackbonesData(data)\n          setCompoundId(data.retro_process.project_compound?.id)\n          setRetroProcessId(data.retro_process.id)\n          setMainTrees(trees)\n          setSelectedMainTree(trees[0])\n        }\n      }\n    )\n  }, [id])\n\n  const onSelectProcedure = selectProcedure(\n    setUpdateChildrenEvent,\n    curTree,\n    selectedReaction\n  )\n\n  const getCommendAmount = () => {\n    setRetroBackbonesData(null)\n    fetchRetroBackbones(id).then((data) => {\n      if (data) {\n        setRetroBackbonesData(data)\n      }\n    })\n  }\n\n  useEffect(() => {\n    if (reload) {\n      getCommendAmount()\n      finishedReload()\n    }\n  }, [reload])\n\n  useEffect(() => {\n    getCommendAmount()\n    setExportFileName(`${getWord('Routes')} ${id}-${Number.parseInt(tab) + 1}`)\n  }, [tab])\n\n  useEffect(() => {\n    updateData()\n  }, [compoundId])\n\n  if (Number.isNaN(id)) {\n    history.push('/404')\n    return <></>\n  }\n\n  if (editMode) {\n    return (\n      <EditRoute\n        projectId={projectId}\n        retroProcessId={retroProcessId}\n        mainTree={cloneDeep(selectedMainTree)}\n        onCancel={() => {\n          onSelectReaction(false)\n          setSaveEvent({})\n          setUpdateChildrenEvent({})\n          setEditMode(false)\n          setCurTree(cloneDeep(selectedMainTree))\n        }}\n        retroBackboneId={id}\n        compoundId={compoundId}\n      />\n    )\n  }\n  const isPlayground: boolean = pathname.includes('/playground')\n  return (\n    <PageContainer\n      className=\"route-view-by-backbone-root\"\n      title={getWord(`menu.list.route.${editMode ? 'edit' : 'view'}`)}\n      header={{\n        title: getWord(`menu.list.route.${editMode ? 'edit' : 'view'}`)\n      }}\n      tabList={mainTrees.map((_, index) => ({\n        tab: `${id}-${index + 1}`,\n        key: `${index}`\n      }))}\n      tabProps={{ type: 'card' }}\n      tabActiveKey={tab}\n      onTabChange={(key) => {\n        setTab(key)\n        setSelectedMainTree(mainTrees[Number.parseInt(key)])\n      }}\n    >\n      <div className=\"container\">\n        {selectedMainTree && (\n          <>\n            <RouteEditor\n              root={selectedMainTree}\n              getRouteEvent={saveEvent}\n              onTreeChange={(t) => {\n                setCurTree(t)\n                if (selectedNode?.id) onSelectReaction(selectedNode.id, t)\n              }}\n              editMode={false}\n              onSelectRxn={onSelectReaction}\n              selectRxnEvent={selectRxnEvent}\n              updateChildrenEvent={updateChildrenEvent}\n              rxnYieldMap={setting.retro?.route_show_yields ? map : undefined}\n              rightTopSlot={\n                <Space size=\"middle\">\n                  {access?.authCodeList?.includes(\n                    'view-by-backbone.button.export'\n                  ) && <ExportRouteButton />}\n                  {!isReadonlyMolecule(projectStatus, compoundStatus) &&\n                    !isPlayground &&\n                    (compound?.type === 'temp_block' ? (\n                      <ButtonWithLoading\n                        onClick={handleConfirmSelect}\n                        size=\"small\"\n                        type=\"primary\"\n                      >\n                        {getWord('choose')}\n                      </ButtonWithLoading>\n                    ) : (\n                      <>\n                        {access?.authCodeList?.includes(\n                          'view-by-backbone.button.comment'\n                        ) && (\n                          <ButtonWithLoading\n                            onClick={() => {\n                              getProfileInfo({\n                                _commendSuject: {\n                                  id: `${id}-${Number(tab) + 1}`\n                                },\n                                collection_class: 'retro-backbone'\n                              })\n                            }}\n                            size=\"small\"\n                            type=\"primary\"\n                          >\n                            {retroBackbonesData?.content_count &&\n                            retroBackbonesData?.content_count > 0\n                              ? `${getWord('comment')}（${\n                                  retroBackbonesData?.content_count\n                                }）`\n                              : getWord('comment')}\n                          </ButtonWithLoading>\n                        )}\n                        {access?.authCodeList?.includes(\n                          'view-by-backbone.button.edit'\n                        ) && (\n                          <ButtonWithLoading\n                            onClick={() =>\n                              setSaveEvent({\n                                getRoute: (route) => {\n                                  if (route) setSelectedMainTree(route)\n                                  setEditMode(true)\n                                  setSaveEvent({})\n                                }\n                              })\n                            }\n                            size=\"small\"\n                            type=\"primary\"\n                          >\n                            {getWord('edit')}\n                          </ButtonWithLoading>\n                        )}\n\n                        <ButtonWithLoading\n                          onClick={async () => {\n                            return new Promise((resolve, reject) => {\n                              modal.confirm({\n                                title: getWord('confirm-the-route'),\n                                content: getWord('route-confirm'),\n                                onOk: () =>\n                                  setSaveEvent({\n                                    getRoute: async (route) => {\n                                      if (!route) return\n                                      await saveRoute(\n                                        normalizeMainTree(route),\n                                        'confirm'\n                                      )\n                                      resolve()\n                                    }\n                                  }),\n                                onCancel: () => reject()\n                              })\n                            })\n                          }}\n                          size=\"small\"\n                          type=\"primary\"\n                        >\n                          {getWord('pages.route.edit.label.confirm')}\n                        </ButtonWithLoading>\n                      </>\n                    ))}\n                </Space>\n              }\n            />\n            <Launcher\n              onMessageWasSent={sendMessage}\n              hiddenLauncher={showLauncher}\n              isOpen={isOpen}\n            />\n          </>\n        )}\n      </div>\n\n      <ReactionDrawer\n        projectId={projectId}\n        reaction={selectedReaction}\n        title={getStepName(selectedNode)}\n        onClose={() => setSelectRxnEvent({})}\n        retroProcessId={retroProcessId}\n        onSelectProcedure={onSelectProcedure}\n        mainReaction={selectedMainReaction}\n        onUpdate={() =>\n          selectedReaction && add(getRxnFromReaction(selectedReaction))\n        }\n        navigateConfig={\n          selectedMainTree &&\n          selectedNode &&\n          getNavigateConfig(selectedMainTree, selectedNode, (node) => {\n            onSelectReaction(node.id)\n            setSelectRxnEvent({ select: node.id })\n          })\n        }\n      />\n    </PageContainer>\n  )\n}\n\nexport default ViewRoutes\n", "function _objectDestructuringEmpty(obj) {\n  if (obj == null) throw new TypeError(\"Cannot destructure \" + obj);\n}\nmodule.exports = _objectDestructuringEmpty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": ["ViewRoutes", "_ref", "_setting$retro2", "_access$authCodeList", "_access$authCodeList2", "_access$authCodeList3", "_objectDestructuringEmpty", "_useParams", "useParams", "_useParams$backboneId", "backboneId", "idStr", "_App$useApp", "App", "useApp", "message", "notification", "modal", "_useState", "useState", "_useState2", "_slicedToArray", "retroBackbonesData", "setRetroBackbonesData", "_useState3", "_useState4", "mainTrees", "setMainTrees", "_useState5", "_useState6", "curTree", "setCurTree", "_useState7", "_useState8", "editMode", "setEditMode", "_useState9", "_useState10", "tab", "setTab", "_useState11", "_useState12", "compoundId", "setCompoundId", "_useState13", "_useState14", "retroProcessId", "setRetroProcessId", "_useState15", "_useState16", "compound", "setCompound", "_useState17", "_useState18", "updateChildrenEvent", "setUpdateChildrenEvent", "_useState19", "_useState20", "projectId", "setProjectId", "_useState21", "_useState22", "projectStatus", "setProjectStatus", "_useState23", "_useState24", "compoundStatus", "setCompoundStatus", "setExportFileName", "useRouteTreeStore", "s", "_useBrainFetch", "useBrainFetch", "fetch", "_useModel", "useModel", "getProfileInfo", "getCommonExpression", "showLauncher", "sendMessage", "reload", "finishedReload", "cacheCurReactionStepNo", "isOpen", "_useLocation", "useLocation", "pathname", "useEffect", "_useUserSetting", "useUserSetting", "setting", "saveRoute", "useSaveRoute", "id", "_useState25", "_useState26", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedMainTree", "access", "useAccess", "_useState27", "_useState28", "saveEvent", "setSaveEvent", "_useState29", "_useState30", "selectRxnEvent", "setSelectRxnEvent", "_useReactionDrawer", "useReactionDrawer", "onSelectReaction", "selectedReaction", "selectedNode", "selectedMainReaction", "deepthMap", "getDeepthMap", "getStepName", "node", "deepth", "get", "reaction_step_no", "getNameOfReaction", "concat", "getWord", "_useRxnYieldMap", "useRxnYieldMap", "getAllRxnsFromTree", "map", "add", "Number", "parseInt", "fetchRetroBackbones", "_ref2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_yield$service$select", "data", "error", "backbone", "wrap", "_context", "prev", "next", "service", "customQuery", "select", "populateDeep", "path", "fields", "children", "key", "sent", "main_trees", "length", "abrupt", "stop", "_x", "apply", "arguments", "fetchCompound", "_ref3", "_callee2", "_yield$fetch", "_context2", "selectManyByID", "populateWith", "_x2", "updateData", "_ref4", "_callee3", "_data$project", "_data$project2", "_context3", "status", "project", "handleSelect", "_ref5", "_callee4", "_yield$service$select2", "route", "_context4", "method", "selectedMainTreeIndex", "success", "type", "duration", "description", "_jsx", "Typography", "Link", "onClick", "_route$project_compou", "_route$project_compou2", "destroy", "history", "push", "project_compound", "handleConfirmSelect", "_ref6", "_callee5", "_context5", "confirm", "title", "content", "onOk", "Promise", "all", "fetchUserSetting", "then", "_ref7", "_ref8", "_setting$retro", "_data$full_trees", "_data$main_trees", "_data$retro_process$p", "trees", "t", "normalizeMainTree", "retro", "route_detail_show_policy", "full_trees", "retro_process", "onSelectProcedure", "selectProcedure", "getCommendAmount", "isNaN", "_Fragment", "EditRoute", "mainTree", "cloneDeep", "onCancel", "retroBackboneId", "isPlayground", "includes", "_jsxs", "<PERSON><PERSON><PERSON><PERSON>", "className", "header", "tabList", "_", "index", "tabProps", "tabActiveKey", "onTabChange", "RouteEditor", "root", "getRouteEvent", "onTreeChange", "onSelectRxn", "rxnYieldMap", "route_show_yields", "undefined", "rightTopSlot", "Space", "size", "authCodeList", "ExportRouteButton", "isReadonlyMolecule", "ButtonWithLoading", "_commendSuject", "collection_class", "content_count", "getRoute", "_callee7", "_context7", "resolve", "reject", "_getRoute", "_callee6", "_context6", "_x3", "Launcher", "onMessageWasSent", "hiddenLauncher", "ReactionDrawer", "reaction", "onClose", "mainReaction", "onUpdate", "getRxnFromReaction", "navigateConfig", "getNavigateConfig", "obj", "module"], "sourceRoot": ""}