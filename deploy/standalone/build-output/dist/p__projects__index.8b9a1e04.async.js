"use strict";(self.webpackChunklabwise_web=self.webpackChunklabwise_web||[]).push([[1255],{18429:function(ne,M,e){e.d(M,{Z:function(){return p}});var T=e(1413),n=e(67294),R={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm0 76c-205.4 0-372 166.6-372 372s166.6 372 372 372 372-166.6 372-372-166.6-372-372-372zm128.01 198.83c.03 0 .05.01.09.06l45.02 45.01a.2.2 0 01.05.09.12.12 0 010 .07c0 .02-.01.04-.05.08L557.25 512l127.87 127.86a.27.27 0 01.05.06v.02a.12.12 0 010 .07c0 .03-.01.05-.05.09l-45.02 45.02a.2.2 0 01-.09.05.12.12 0 01-.07 0c-.02 0-.04-.01-.08-.05L512 557.25 384.14 685.12c-.04.04-.06.05-.08.05a.12.12 0 01-.07 0c-.03 0-.05-.01-.09-.05l-45.02-45.02a.2.2 0 01-.05-.09.12.12 0 010-.07c0-.02.01-.04.06-.08L466.75 512 338.88 384.14a.27.27 0 01-.05-.06l-.01-.02a.12.12 0 010-.07c0-.03.01-.05.05-.09l45.02-45.02a.2.2 0 01.09-.05.12.12 0 01.07 0c.02 0 .04.01.08.06L512 466.75l127.86-127.86c.04-.05.06-.06.08-.06a.12.12 0 01.07 0z"}}]},name:"close-circle",theme:"outlined"},f=R,F=e(84089),c=function(d,b){return n.createElement(F.Z,(0,T.Z)((0,T.Z)({},d),{},{ref:b,icon:f}))},s=n.forwardRef(c),p=s},34804:function(ne,M,e){var T=e(1413),n=e(67294),R=e(66023),f=e(84089),F=function(p,m){return n.createElement(f.Z,(0,T.Z)((0,T.Z)({},p),{},{ref:m,icon:R.Z}))},c=n.forwardRef(F);M.Z=c},51042:function(ne,M,e){var T=e(1413),n=e(67294),R=e(42110),f=e(84089),F=function(p,m){return n.createElement(f.Z,(0,T.Z)((0,T.Z)({},p),{},{ref:m,icon:R.Z}))},c=n.forwardRef(F);M.Z=c},5966:function(ne,M,e){var T=e(97685),n=e(1413),R=e(45987),f=e(21770),F=e(98138),c=e(55241),s=e(97435),p=e(67294),m=e(92755),d=e(85893),b=["fieldProps","proFieldProps"],g=["fieldProps","proFieldProps"],t="text",K=function(P){var a=P.fieldProps,$=P.proFieldProps,N=(0,R.Z)(P,b);return(0,d.jsx)(m.Z,(0,n.Z)({valueType:t,fieldProps:a,filedConfig:{valueType:t},proFieldProps:$},N))},oe=function(P){var a=(0,f.Z)(P.open||!1,{value:P.open,onChange:P.onOpenChange}),$=(0,T.Z)(a,2),N=$[0],ie=$[1];return(0,d.jsx)(F.Z.Item,{shouldUpdate:!0,noStyle:!0,children:function(le){var V,ce=le.getFieldValue(P.name||[]);return(0,d.jsx)(c.Z,(0,n.Z)((0,n.Z)({getPopupContainer:function(_){return _&&_.parentNode?_.parentNode:_},onOpenChange:function(_){return ie(_)},content:(0,d.jsxs)("div",{style:{padding:"4px 0"},children:[(V=P.statusRender)===null||V===void 0?void 0:V.call(P,ce),P.strengthText?(0,d.jsx)("div",{style:{marginTop:10},children:(0,d.jsx)("span",{children:P.strengthText})}):null]}),overlayStyle:{width:240},placement:"rightTop"},P.popoverProps),{},{open:N,children:P.children}))}})},U=function(P){var a=P.fieldProps,$=P.proFieldProps,N=(0,R.Z)(P,g),ie=(0,p.useState)(!1),de=(0,T.Z)(ie,2),le=de[0],V=de[1];return a!=null&&a.statusRender&&N.name?(0,d.jsx)(oe,{name:N.name,statusRender:a==null?void 0:a.statusRender,popoverProps:a==null?void 0:a.popoverProps,strengthText:a==null?void 0:a.strengthText,open:le,onOpenChange:V,children:(0,d.jsx)("div",{children:(0,d.jsx)(m.Z,(0,n.Z)({valueType:"password",fieldProps:(0,n.Z)((0,n.Z)({},(0,s.Z)(a,["statusRender","popoverProps","strengthText"])),{},{onBlur:function(H){var _;a==null||(_=a.onBlur)===null||_===void 0||_.call(a,H),V(!1)},onClick:function(H){var _;a==null||(_=a.onClick)===null||_===void 0||_.call(a,H),V(!0)}}),proFieldProps:$,filedConfig:{valueType:t}},N))})}):(0,d.jsx)(m.Z,(0,n.Z)({valueType:"password",fieldProps:a,proFieldProps:$,filedConfig:{valueType:t}},N))},L=K;L.Password=U,L.displayName="ProFormComponent",M.Z=L},15001:function(ne,M,e){var T=e(97857),n=e.n(T),R=e(32884),f=e(66309),F=e(85893),c={created:"#F5B544",editing:"#F5B544",started:"#4B9F47",holding:"#E6521F",confirmed:"#4B9F47",finished:"#1890FF",cancelled:"#979797",canceled:"#979797",running:"#2AD259",hold:"#E6521F",completed:"#1890FF",success:"#F51D2C",failed:"#9747FF",todo:"#F5B544",checking:"#4B9F47"},s=function(m){var d=m.status,b=m.colorMap,g=m.labelPrefix,t=m.label,K=m.className,oe=n()(n()({},c),b)[d],U=t||(0,R.oz)("".concat(g,".").concat(d));return(0,F.jsx)(f.Z,{className:K,color:oe,children:U})};M.Z=s},2390:function(ne,M,e){e.d(M,{D:function(){return c}});var T=e(97857),n=e.n(T),R=e(55932),f=e(70831),F=function(){var p=(0,f.useAppData)(),m=p.clientRoutes,d=(0,f.useLocation)(),b=(0,f.matchRoutes)(m,d.pathname),g=b==null?void 0:b[b.length-1].route;return{matches:b,currentRoute:g}},c=function(p){var m=F(),d=m.currentRoute,b=(0,R.s5)(n()({prefix:d==null?void 0:d.path},p)),g=b.get,t=b.set;return[g,t]}},85670:function(ne,M,e){var T=e(34804),n=e(85418),R=e(42075),f=e(85893),F=function(s){var p=s.currentValue,m=s.avalibleValues,d=s.onSelect,b=s.valueRender,g=b===void 0?function(K){return(0,f.jsx)(f.Fragment,{children:K})}:b;if(!m.length)return g(p);var t=m.map(function(K){return{label:g(K),key:K}});return(0,f.jsx)(n.Z,{menu:{items:t,onClick:function(oe){return d(oe.key)}},trigger:["click"],children:(0,f.jsxs)(R.Z,{children:[g(p),(0,f.jsx)(T.Z,{})]})})};M.Z=F},45117:function(ne,M,e){var T=e(15009),n=e.n(T),R=e(99289),f=e.n(R),F=e(5574),c=e.n(F),s=e(32884),p=e(37476),m=e(90672),d=e(98138),b=e(71471),g=e(67294),t=e(85893),K=function(U){var L=U.status,se=U.operateTargetName,P=U.onFinished,a=U.onCancel,$=U.trigger,N=d.Z.useForm(),ie=c()(N,1),de=ie[0],le=(0,g.useState)(!1),V=c()(le,2),ce=V[0],H=V[1];(0,g.useEffect)(function(){return H(($==null?void 0:$.open)||!1)},[$]);var _=["cancelled","canceled"].includes(L),Ee=function(){switch(L){case"canceled":return(0,s.oz)("cancel-molecule");case"finished":return(0,s.oz)("complete-molecule");case"cancelled":return(0,s.oz)("cancel-projects");case"started":return(0,s.oz)("start-project");default:return(0,t.jsxs)(t.Fragment,{children:[(0,s.oz)("pages.projectTable.statusChangeLabel.".concat(L)),(0,s.Ig)()?" ":"",se]})}},Te=function(){switch(L){case"canceled":case"cancelled":return(0,s.oz)("cancel-reason");default:return(0,t.jsxs)(t.Fragment,{children:[(0,s.oz)("pages.projectTable.statusChangeLabel.".concat(L)),(0,s.Ig)()?" ":"",(0,s.oz)("reason")]})}},Y=function(){switch(L){case"finished":return(0,s.oz)("complete-molecule-tip");case"started":return(0,s.oz)("start-project-tip");case"canceled":return(0,s.oz)("cancel-reason");default:return(0,t.jsxs)(t.Fragment,{children:[(0,s.oz)("confirm"),(0,s.Ig)()?" ":"",(0,s.oz)("pages.projectTable.statusChangeLabel.".concat(L)),(0,s.Ig)()?" ":"",se,"\uFF1F"]})}};return(0,t.jsxs)(p.Y,{title:Ee(),width:400,open:ce,onOpenChange:function(X){X||a==null||a(),H(X)},form:de,autoFocusFirstInput:!0,modalProps:{destroyOnClose:!0,centered:!0},onFinish:function(){var J=f()(n()().mark(function X(pe){var r;return n()().wrap(function(ve){for(;;)switch(ve.prev=ve.next){case 0:return r=pe.status_update_note,ve.next=3,P==null?void 0:P({status:L,status_update_note:r});case 3:H(!1);case 4:case"end":return ve.stop()}},X)}));return function(X){return J.apply(this,arguments)}}(),children:[_&&(0,t.jsx)(b.Z.Text,{type:"danger",children:(0,s.oz)(L==="cancelled"?"cancel-projects-note":"cancel-molecule-tip")}),_||L==="holding"?(0,t.jsx)(m.Z,{width:"md",name:"status_update_note",rules:[{required:!0,message:(0,s.oz)("enter-reason")}],label:Te()}):Y()]})};M.Z=K},50145:function(ne,M,e){e.r(M),e.d(M,{default:function(){return _t}});var T=e(15009),n=e.n(T),R=e(19632),f=e.n(R),F=e(99289),c=e.n(F),s=e(5574),p=e.n(s),m=e(15001),d=e(30042),b=e(2390),g=e(87172),t=e(32884),K=e(11774),oe=e(56337),U=e(70831),L=e(55241),se=e(31418),P=e(28459),a=e(67294),$=e(97015),N=e(52677),ie=e.n(N),de=e(97857),le=e.n(de),V=e(51042),ce=e(18429),H=e(37476),_=e(24739),Ee=e(5966),Te=e(64317),Y=e(1413),J=e(45987),X=e(66758),pe=e(92755),r=e(85893),Ae=["proFieldProps","fieldProps"],ve="date",Ye=a.forwardRef(function(E,x){var h=E.proFieldProps,I=E.fieldProps,C=(0,J.Z)(E,Ae),y=(0,a.useContext)(X.Z);return(0,r.jsx)(pe.Z,(0,Y.Z)({ref:x,valueType:ve,fieldProps:(0,Y.Z)({getPopupContainer:y.getPopupContainer},I),proFieldProps:h,filedConfig:{valueType:ve,customLightMode:!0}},C))}),Ge=Ye,Qe=["proFieldProps","fieldProps"],We="dateMonth",we=a.forwardRef(function(E,x){var h=E.proFieldProps,I=E.fieldProps,C=(0,J.Z)(E,Qe),y=(0,a.useContext)(X.Z);return(0,r.jsx)(pe.Z,(0,Y.Z)({ref:x,valueType:We,fieldProps:(0,Y.Z)({getPopupContainer:y.getPopupContainer},I),proFieldProps:h,filedConfig:{valueType:We,customLightMode:!0}},C))}),He=we,Je=["fieldProps"],Be="dateQuarter",Xe=a.forwardRef(function(E,x){var h=E.fieldProps,I=(0,J.Z)(E,Je),C=(0,a.useContext)(X.Z);return(0,r.jsx)(pe.Z,(0,Y.Z)({ref:x,valueType:Be,fieldProps:(0,Y.Z)({getPopupContainer:C.getPopupContainer},h),filedConfig:{valueType:Be,customLightMode:!0}},I))}),ke=Xe,qe=["proFieldProps","fieldProps"],Ue="dateWeek",et=a.forwardRef(function(E,x){var h=E.proFieldProps,I=E.fieldProps,C=(0,J.Z)(E,qe),y=(0,a.useContext)(X.Z);return(0,r.jsx)(pe.Z,(0,Y.Z)({ref:x,valueType:Ue,fieldProps:(0,Y.Z)({getPopupContainer:y.getPopupContainer},I),proFieldProps:h,filedConfig:{valueType:Ue,customLightMode:!0}},C))}),tt=et,rt=["proFieldProps","fieldProps"],$e="dateYear",at=a.forwardRef(function(E,x){var h=E.proFieldProps,I=E.fieldProps,C=(0,J.Z)(E,rt),y=(0,a.useContext)(X.Z);return(0,r.jsx)(pe.Z,(0,Y.Z)({ref:x,valueType:$e,fieldProps:(0,Y.Z)({getPopupContainer:y.getPopupContainer},I),proFieldProps:h,filedConfig:{valueType:$e,customLightMode:!0}},C))}),nt=at,he=Ge;he.Week=tt,he.Month=He,he.Quarter=ke,he.Year=nt,he.displayName="ProFormComponent";var ot=he,st=e(5155),lt=e(28036),ut=e(71230),Ke=e(15746),Ne=e(98138),it=e(27484),dt=e.n(it),ct=function(){var x=(0,a.useState)(""),h=p()(x,2),I=h[0],C=h[1],y=function(){var te=c()(n()().mark(function fe(){var ue,re;return n()().wrap(function(k){for(;;)switch(k.prev=k.next){case 0:return k.next=2,(0,g.service)("projects/next-no").select().get();case 2:return ue=k.sent,re=ue.data,k.abrupt("return",re||"");case 5:case"end":return k.stop()}},fe)}));return function(){return te.apply(this,arguments)}}();return(0,a.useEffect)(function(){y().then(function(te){return C==null?void 0:C(te)})},[]),I},pt=ct,vt=e(14200),me=e(35293),mt=function(x){var h,I,C=x.onSuccess,y=x.openEvent,te=(0,U.useAccess)(),fe=se.Z.useApp(),ue=fe.message,re=fe.notification,ge=(0,a.useState)(!1),k=p()(ge,2),Oe=k[0],je=k[1],be=(0,a.useRef)(),ye=(0,vt.Z)(),Me=pt(),Re=(0,U.useModel)("@@initialState"),Se=Re.initialState,G=Se.userInfo;return(0,a.useEffect)(function(){y!=null&&y.open&&je(!0)},[y]),(0,r.jsxs)(H.Y,{className:"create-form-root",open:Oe,onOpenChange:je,title:(0,t.oz)("pages.projectTable.modelLabel.newProject"),trigger:te!=null&&(h=te.authCodeList)!==null&&h!==void 0&&h.includes("projects.button.add")?(0,r.jsxs)(lt.ZP,{type:"primary",title:(0,t.oz)("no-permission-tip"),children:[(0,r.jsx)(V.Z,{})," ",(0,t.oz)("pages.projectTable.modelLabel.newProject")]},"primary"):(0,r.jsx)(r.Fragment,{}),autoFocusFirstInput:!0,layout:"horizontal",grid:!0,labelCol:{span:6},validateMessages:{required:"${label}"+"".concat((0,t.Ig)()?" ":"").concat((0,t.oz)("is-required"))},modalProps:{destroyOnClose:!0,centered:!0},submitTimeout:2e3,onFinish:function(){var ae=c()(n()().mark(function W(A){var z,Q,S,O;return n()().wrap(function(l){for(;;)switch(l.prev=l.next){case 0:return l.next=2,(0,g.service)("projects").create(le()(le()({},A),{},{customer:(0,t.SV)()?"-":A.customer}));case 2:if(z=l.sent,Q=z.error,!Q){l.next=10;break}return S=Q.details.errors[0],O=Q.message,S.message==="This attribute must be unique"&&S.path.includes("no")&&(O=(0,t.oz)("project-ID-duplicated")),re.error({message:(0,t.oz)("project-create-failed"),description:O}),l.abrupt("return",!1);case 10:return ue.success("".concat((0,t.oz)("project")).concat(A.name||A.no).concat((0,t.oz)("created-succeed"))),C==null||C(),l.abrupt("return",!0);case 13:case"end":return l.stop()}},W)}));return function(W){return ae.apply(this,arguments)}}(),children:[(0,r.jsxs)(_.UW,{title:(0,t.oz)("pages.project.settings"),children:[(0,r.jsx)(Ee.Z,{colProps:{md:12},name:"no",label:(0,t.oz)("project-ID"),required:!0,initialValue:Me,rules:[{required:!0}]}),(0,r.jsx)(Ee.Z,{colProps:{md:12},name:"name",label:(0,t.oz)("project-name")}),(0,t.SV)()?null:(0,r.jsx)(Ee.Z,{colProps:{md:12},name:"customer",label:(0,t.oz)("pages.projectTable.label.customer"),rules:[{required:!0}]}),(0,r.jsx)(Te.Z,{colProps:{md:12},valueEnum:me.tW,name:"type",label:(0,t.oz)("pages.projectTable.label.type"),initialValue:(0,t.SV)()?"personal":void 0,required:!0,rules:[{required:!0}]}),(0,r.jsx)(ot,{colProps:{md:12},name:"delivery_date",label:(0,t.oz)("project-delivery-date"),fieldProps:{disabledDate:function(W){return W&&W<=dt()().startOf("day")}}})]}),(0,r.jsxs)(_.UW,{title:(0,t.oz)("team-allocation"),children:[(0,r.jsxs)(ut.Z,{className:"member-title",children:[(0,r.jsx)(Ke.Z,{span:12,children:(0,r.jsx)(Ne.Z.Item,{colon:!1,required:!0,label:(0,t.oz)("member"),labelCol:{span:24}})}),(0,r.jsx)(Ke.Z,{span:12,children:(0,r.jsx)(Ne.Z.Item,{colon:!1,required:!0,label:(0,t.oz)("role")})})]}),(0,r.jsx)(st.u,{className:"member-list",name:"project_members",deleteIconProps:{Icon:ce.Z},copyIconProps:!1,colProps:{span:24},min:1,creatorButtonProps:{creatorButtonText:(0,t.oz)("add-member")},actionRef:be,actionRender:function(W,A,z){return W.key===0?[]:z},actionGuard:{beforeRemoveRow:function(W){var A,z,Q=ie()(W)==="object"?W[0]:W,S=(A=be.current)===null||A===void 0||(A=A.get(Q))===null||A===void 0?void 0:A.role;if(!S)return!0;var O=(z=be.current)===null||z===void 0||(z=z.getList())===null||z===void 0?void 0:z.filter(function(Pe){return Pe.role===S});return S===me.xy&&(!(O!=null&&O.length)||O.length<=1)?(ue.error((0,r.jsxs)("span",{children:["\u9879\u76EE\u4E2D\u81F3\u5C11\u9700\u8981\u4E00\u4E2A",(0,t.oz)("pages.projectTable.label.".concat(S))]})),!1):!0}},initialValue:[{user_id:"".concat(G==null?void 0:G.id),role:(I=ye.find(function(ae){return ae.code===me.xy}))===null||I===void 0?void 0:I.id}],children:function(W,A){return(0,r.jsxs)(_.UW,{labelLayout:"inline",children:[(0,r.jsx)(Te.Z,{name:"user_id",label:(0,t.oz)("member"),colProps:{md:12},required:!0,rules:[{required:!0}],showSearch:!0,debounceTime:300,request:function(){var z=c()(n()().mark(function Q(S){var O,Pe,l;return n()().wrap(function(o){for(;;)switch(o.prev=o.next){case 0:if(O=S.keyWords,O!=null&&O.length){o.next=3;break}return o.abrupt("return",[{value:"".concat(G==null?void 0:G.id),label:G==null?void 0:G.username}]);case 3:return o.next=5,(0,g.service)("users/search?q=".concat(O)).select().get();case 5:return Pe=o.sent,l=Pe.data,o.abrupt("return",(l==null?void 0:l.map(function(u){return{value:"".concat(u.id),label:"".concat(u.username)}}))||[]);case 8:case"end":return o.stop()}},Q)}));return function(Q){return z.apply(this,arguments)}}()}),(0,r.jsx)(Te.Z,{colProps:{md:12},name:"role",label:(0,t.oz)("role"),required:!0,disabled:A<1,rules:[{required:!0}],request:c()(n()().mark(function z(){return n()().wrap(function(S){for(;;)switch(S.prev=S.next){case 0:return S.abrupt("return",ye.map(function(O){return{value:O.id,label:O.name_zh}}));case 1:case"end":return S.stop()}},z)}))})]})}})]})]})},ft=mt,Pt=e(85670),Ce=e(18221),ht=function(x){var h=x.audit;return h?(0,r.jsxs)(Ce.vY,{request:c()(n()().mark(function I(){return n()().wrap(function(y){for(;;)switch(y.prev=y.next){case 0:return y.abrupt("return",{data:h,success:!0});case 1:case"end":return y.stop()}},I)})),layout:"vertical",column:1,style:{width:250},children:[(0,r.jsx)(Ce.vY.Item,{dataIndex:"createdAt",label:(0,t.oz)("modification-time"),valueType:"dateTime"}),(0,r.jsx)(Ce.vY.Item,{dataIndex:"user_id",label:(0,t.oz)("modifier"),valueType:"text"}),(0,r.jsx)(Ce.vY.Item,{dataIndex:"note",label:(0,t.oz)("modification-reason"),valueType:"text"})]}):null},gt=ht,jt=e(45117),Et={},Tt=["cancelled","holding"],bt=function(){var x,h=(0,a.useRef)(),I=(0,a.useState)(),C=p()(I,2),y=C[0],te=C[1],fe=(0,a.useState)(null),ue=p()(fe,2),re=ue[0],ge=ue[1],k=(0,U.useModel)("@@initialState"),Oe=k.initialState,je=Oe.userInfo,be=["leaders","pm"].includes(je==null||(x=je.role)===null||x===void 0?void 0:x.name),ye=(0,b.D)(),Me=p()(ye,2),Re=Me[1],Se=(0,a.useState)(!1),G=p()(Se,2),ae=G[0],W=G[1],A=function(){var l=c()(n()().mark(function D(o,u,v){var j,Z,B,q,ee,w,xe,Ze,Fe,De,Ve;return n()().wrap(function(i){for(;;)switch(i.prev=i.next){case 0:W(!0),j=(0,g.query)("projects").paginate(o.current||1,o.pageSize||g.defaultPageSize).populateWith("project_status_audits").populateWith("project_members",["user_id","role"],!0).populateWith("project_compounds",["id","type"]).sortBy([].concat(f()(Object.entries(u||{}).map(function(ze){var _e=p()(ze,2),Le=_e[0],Ie=_e[1];return{field:Le,order:Ie==="ascend"?"asc":"desc"}})),[{field:"createdAt",order:"desc"},{field:"updatedAt",order:"desc"}])),Z=0,B=Object.keys(o);case 3:if(!(Z<B.length)){i.next=25;break}if(q=B[Z],ee=q,w=o[ee],w){i.next=9;break}return i.abrupt("continue",22);case 9:i.t0=ee,i.next=i.t0==="delivery_date"||i.t0==="start_datetime"||i.t0==="end_datetime"?12:i.t0==="customer"||i.t0==="name"||i.t0==="no"?14:i.t0==="managers"?16:i.t0==="status"||i.t0==="type"?19:21;break;case 12:return j.between(ee,w),i.abrupt("break",22);case 14:return j.contains(ee,w),i.abrupt("break",22);case 16:return j.filterDeep("project_members.user_id","contains",w),j.filterDeep("project_members.role.code","eq",me.xy),i.abrupt("break",22);case 19:return w.length&&j.filterDeep(ee,"in",w),i.abrupt("break",22);case 21:return i.abrupt("break",22);case 22:Z++,i.next=3;break;case 25:return v&&Object.entries(v).length&&Object.entries(v).forEach(function(ze){var _e=p()(ze,2),Le=_e[0],Ie=_e[1];Ie&&j.filterDeep(Le,"in",Ie)}),j.filterDeep("personal_owner.id","null","true"),i.next=29,j.get();case 29:if(xe=i.sent,Ze=xe.error,Fe=xe.data,De=xe.meta,W(!1),!Ze){i.next=36;break}throw Ze;case 36:if(Fe){i.next=38;break}throw new Error("No data returned");case 38:return Ve=Fe.map(me.Se),i.abrupt("return",{data:Ve,success:!0,total:De==null?void 0:De.pagination.total});case 40:case"end":return i.stop()}},D)}));return function(o,u,v){return l.apply(this,arguments)}}(),z=[{title:(0,t.oz)("pages.projectTable.label.no"),dataIndex:"no",sorter:!0,editable:!1,order:9},{title:(0,t.oz)("project-name"),dataIndex:"name",width:120,editable:!1,sorter:!0,order:8}].concat(f()((0,t.SV)()?[]:[{title:(0,t.oz)("pages.projectTable.label.customer"),dataIndex:"customer",editable:!1,sorter:!0,order:7}]),[{title:(0,t.oz)("pages.projectTable.label.type"),dataIndex:"type",valueType:"checkbox",editable:!1,sorter:!0,valueEnum:me.tW,hideInSearch:(0,t.SV)(),order:2},{order:1,title:(0,t.oz)("pages.projectTable.label.status"),dataIndex:"status",valueType:"checkbox",hideInForm:!0,hideInDescriptions:!0,sorter:!0,valueEnum:{created:{text:(0,t.oz)("pages.projectTable.statusLabel.created")},started:{text:(0,t.oz)("pages.projectTable.statusLabel.started")},finished:{text:(0,t.oz)("component.notification.statusValue.success")},holding:{text:(0,t.oz)("pages.projectTable.statusLabel.holding")},cancelled:{text:(0,t.oz)("pages.projectTable.statusLabel.cancelled")}},render:function(D,o){var u=o.id,v=o.status,j=o.last_audit;return(0,r.jsx)(L.Z,{placement:"right",content:v&&Tt.includes(v)?(0,r.jsx)(gt,{audit:j}):void 0,trigger:"hover",children:(0,r.jsx)("div",{style:{width:"fit-content"},children:(0,r.jsx)(Pt.Z,{currentValue:v||"created",valueRender:function(B){return(0,r.jsx)(m.Z,{labelPrefix:"pages.projectTable.statusLabel",status:B})},avalibleValues:v?me.YG[v]:[],onSelect:function(){var Z=c()(n()().mark(function B(q){return n()().wrap(function(w){for(;;)switch(w.prev=w.next){case 0:return w.abrupt("return",ge({id:u,status:q}));case 1:case"end":return w.stop()}},B)}));return function(B){return Z.apply(this,arguments)}}()})})})}},{title:(0,t.oz)("pages.projectTable.label.compoundNumber"),width:(0,t.Ig)()?140:80,valueType:"digit",dataIndex:"project_compounds",renderText:function(D,o){var u;return((u=o.project_compounds)===null||u===void 0||(u=u.filter(function(v){return v.type!=="temp_block"}))===null||u===void 0?void 0:u.length)||0},hideInSearch:!0},{title:(0,t.oz)("pages.projectTable.label.pm"),dataIndex:"managers",width:120,order:5,renderText:function(D,o){var u;return(u=o.managers)===null||u===void 0?void 0:u.map(function(v){var j;return(j=v.user_info)===null||j===void 0?void 0:j.username}).join(", ")},hideInDescriptions:!0,valueType:"select",request:function(){var l=c()(n()().mark(function o(u){var v,j,Z;return n()().wrap(function(q){for(;;)switch(q.prev=q.next){case 0:return v=u.keyWords,q.next=3,(0,g.service)("users/search?q=".concat(v)).select().get();case 3:return j=q.sent,Z=j.data,q.abrupt("return",(Z==null?void 0:Z.map(function(ee){return{value:"".concat(ee.id),label:"".concat(ee.username)}}))||[]);case 6:case"end":return q.stop()}},o)}));function D(o){return l.apply(this,arguments)}return D}(),formItemProps:{rules:[{required:!0}]},fieldProps:{showSearch:!0}},{title:(0,t.oz)("pages.experiment.label.actualStartTime"),valueType:"date",width:(0,t.Ig)()?120:100,dataIndex:"start_datetime",hideInSearch:!0,sorter:!0,hideInDescriptions:!0},{title:(0,t.oz)("pages.projectTable.label.deliveryDate"),valueType:"date",dataIndex:"delivery_date",sorter:!0,hideInSearch:!0},{title:(0,t.oz)("pages.experiment.label.actualEndTime"),valueType:"date",width:(0,t.Ig)()?120:100,dataIndex:"end_datetime",hideInSearch:!0,sorter:!0,hideInDescriptions:!0},{title:(0,t.oz)("pages.experiment.label.operation"),dataIndex:"option",valueType:"option",hideInDescriptions:!0,render:function(D,o){return[(0,r.jsx)("a",{onClick:function(){U.history.push("/projects/".concat(o==null?void 0:o.id))},children:(0,t.oz)("pages.projectTable.actionLabel.viewDetail")},"view-detail"),(0,r.jsx)($.Z,{projectId:o.id,trigger:(0,r.jsx)("a",{children:(0,t.oz)("pages.projectTable.actionLabel.config")}),modelProps:{onCancel:function(){var v;return(v=h.current)===null||v===void 0?void 0:v.reload()}}},"config")]}},{title:(0,t.oz)("pages.projectTable.label.deliveryDate"),valueType:"dateRange",dataIndex:"delivery_date",hideInTable:!0,hideInDescriptions:!0,order:6},{title:(0,t.oz)("pages.experiment.label.actualStartTime"),valueType:"dateRange",dataIndex:"start_datetime",hideInDescriptions:!0,hideInTable:!0,order:4},{title:(0,t.oz)("pages.experiment.label.actualEndTime"),valueType:"dateRange",dataIndex:"end_datetime",hideInDescriptions:!0,hideInTable:!0,order:3}]),Q=se.Z.useApp(),S=Q.message,O=(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(ft,{openEvent:y,onSuccess:function(){var D;return(D=h.current)===null||D===void 0?void 0:D.reload()}},"create-form")}),Pe=function(D){if(D==="Table")return(0,r.jsx)(d.Z,{des:(0,r.jsxs)(r.Fragment,{children:[(0,t.oz)("create-project-tip-I"),be&&!ae?(0,r.jsxs)(r.Fragment,{children:[(0,t.oz)("create-project-tip-II"),(0,r.jsx)("a",{onClick:function(){return te({open:!0})},children:(0,t.oz)("create-project-tip-target")}),(0,t.oz)("create-project-tip-III")]}):""]})})};return(0,r.jsxs)(K._z,{className:Et.projects,children:[(0,r.jsx)(P.ZP,{renderEmpty:Pe,children:(0,r.jsx)(oe.Z,{headerTitle:(0,t.oz)("pages.projectTable.title"),form:{onValuesChange:function(){var l=c()(n()().mark(function o(u,v){return n()().wrap(function(Z){for(;;)switch(Z.prev=Z.next){case 0:return Z.abrupt("return",Re(v));case 1:case"end":return Z.stop()}},o)}));function D(o,u){return l.apply(this,arguments)}return D}(),initialValues:{status:["created","started","finished","holding"]}},request:A,actionRef:h,rowKey:"id",search:{labelWidth:120},columns:z,toolBarRender:function(){return[O]},toolbar:{settings:[]},pagination:{pageSize:g.defaultPageSize}})}),re&&(0,r.jsx)(jt.Z,{operateTargetName:(0,t.oz)("menu.list.project-list"),status:re.status,trigger:{open:!!re},onCancel:function(){return ge(null)},onFinished:function(){var l=c()(n()().mark(function D(o){var u,v,j;return n()().wrap(function(B){for(;;)switch(B.prev=B.next){case 0:return B.next=2,(0,g.service)("projects").update(re.id,{status:o==null?void 0:o.status,status_update_note:o==null?void 0:o.status_update_note});case 2:v=B.sent,j=v.error,j?S.error(j.details):S.success((0,t.oz)("project-update-success")),ge(null),(u=h.current)===null||u===void 0||u.reload();case 7:case"end":return B.stop()}},D)}));return function(D){return l.apply(this,arguments)}}()})]})},_t=bt}}]);

//# sourceMappingURL=p__projects__index.8b9a1e04.async.js.map