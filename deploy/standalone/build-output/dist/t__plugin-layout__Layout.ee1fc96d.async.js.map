{"version": 3, "file": "t__plugin-layout__Layout.ee1fc96d.async.js", "mappings": "qwBAOIA,GAAoB,SAA2BC,EAAOC,EAAK,CAC7D,OAAoB,gBAAoBC,QAAU,KAAS,CAAC,EAAGF,EAAO,CACpE,IAAKC,EACL,KAAM,IACR,CAAC,CAAC,CACJ,EAGIE,GAAuB,aAAiBJ,EAAiB,EAI7D,GAAeI,G,kEChBXC,EAAwB,SAA+BC,EAAO,CAChE,SAAO,MAAgB,CAAC,EAAGA,EAAM,aAAc,CAC7C,YAAa,EACb,iBAAkB,GAClB,eAAgB,GAChB,aAAc,EACd,aAAc,EACd,cAAe,GACf,UAAW,SACX,SAAU,CACR,eAAgB,EAChB,MAAOA,EAAM,mBACb,SAAU,CACR,MAAOA,EAAM,mBACb,eAAgBA,EAAM,cACxB,EACA,qBAAsB,CACpB,gBAAiB,CACnB,EACA,UAAW,CACT,MAAOA,EAAM,YACf,CACF,EACA,cAAe,CACb,SAAU,OACV,MAAOA,EAAM,SACf,CACF,CAAC,CACH,EACO,SAAS,EAASC,EAAW,CAClC,SAAO,OAAa,kBAAmB,SAAUD,EAAO,CACtD,IAAIE,KAAe,QAAc,KAAc,CAAC,EAAGF,CAAK,EAAG,CAAC,EAAG,CAC7D,aAAc,IAAI,OAAOC,CAAS,CACpC,CAAC,EACD,MAAO,CAACF,EAAsBG,CAAY,CAAC,CAC7C,CAAC,CACH,C,eCjCIC,EAAe,SAAsBC,EAAM,CAC7C,IAAIC,EAAYD,EAAK,UACnBH,EAAYG,EAAK,UACjBE,EAAQF,EAAK,MACbG,EAAYH,EAAK,UACjBI,EAAQJ,EAAK,MACXK,KAAU,cAAW,kBAA4B,EACjDC,EAAgBD,EAAQ,aAAaR,GAAa,mBAAmB,EACrEU,GAAY,EAASD,CAAa,EACpCE,EAAUD,GAAU,QACpBE,GAASF,GAAU,OACrB,OAAKL,GAAS,MAAQA,IAAU,IAAS,MAAM,QAAQA,CAAK,GAAKA,EAAM,SAAW,KAAOC,GAAa,MAAQA,IAAc,IACnH,KAEFK,KAAsB,QAAM,MAAO,CACxC,UAAW,KAAWF,EAAeG,GAAQR,CAAS,EACtD,MAAOG,EACP,SAAU,CAACF,MAAsB,OAAK,MAAO,CAC3C,UAAW,GAAG,OAAOI,EAAe,QAAQ,EAAE,OAAOG,EAAM,EAAE,KAAK,EAClE,SAAUP,EAAM,IAAI,SAAUQ,GAAM,CAClC,SAAoB,OAAK,IAAK,CAC5B,UAAW,GAAG,OAAOJ,EAAe,aAAa,EAAE,OAAOG,EAAM,EAAE,KAAK,EACvE,MAAOC,GAAK,IACZ,OAAQA,GAAK,YAAc,SAAW,QACtC,KAAMA,GAAK,KACX,IAAK,aACL,SAAUA,GAAK,KACjB,EAAGA,GAAK,GAAG,CACb,CAAC,CACH,CAAC,EAAGP,MAA0B,OAAK,MAAO,CACxC,UAAW,GAAG,OAAOG,EAAe,aAAa,EAAE,OAAOG,EAAM,EAAE,KAAK,EACvE,SAAUN,CACZ,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,ECjCIQ,EAAS,IAAO,OAChBC,EAAgB,SAAuBZ,EAAM,CAC/C,IAAIE,EAAQF,EAAK,MACfG,EAAYH,EAAK,UACjBI,EAAQJ,EAAK,MACbC,EAAYD,EAAK,UACjBH,EAAYG,EAAK,UACnB,SAAoB,OAAKW,EAAQ,CAC/B,UAAWV,EACX,SAAO,KAAc,CACnB,QAAS,CACX,EAAGG,CAAK,EACR,YAAuB,OAAKL,EAAc,CACxC,MAAOG,EACP,UAAWL,EACX,UAAWM,IAAc,GAAQ,QAAoB,QAAM,WAAU,CACnE,SAAU,IAAc,OAAK,GAAmB,CAAC,CAAC,EAAG,IAAKA,CAAS,CACrE,CAAC,CACH,CAAC,CACH,CAAC,CACH,C,mICzBIU,GAAY,CAAC,YAAa,YAAa,WAAY,QAAS,OAAO,EAInEC,GAAc,SAAqBd,EAAM,CAC3C,IAAIe,EAAYf,EAAK,UACnBgB,EAAYhB,EAAK,UACjBiB,GAAWjB,EAAK,SAChBkB,GAAQlB,EAAK,MACbmB,GAAQnB,EAAK,MACboB,KAAQ,KAAyBpB,EAAMa,EAAS,EAClD,SAAoB,QAAK,MAAO,CAC9B,MAAO,CACL,kBAAmB,IACnB,UAAW,QACb,EACA,YAAuB,QAAK,OAAM,KAAc,CAC9C,KAAM,OACR,EAAGO,CAAK,CAAC,CACX,CAAC,CACH,C,qFCrBWC,KAA4B,iBAAc,CAAC,CAAC,C,oMCCnDR,GAAY,CAAC,OAAQ,YAAa,UAAW,QAAS,eAAgB,gBAAgB,EAGtFS,GAAsB,CACxB,aAAc,OACd,eAAgB,UAChB,WAAY,EACd,EACA,SAASC,GAAiBvB,EAAM,CAC9B,IAAIwB,EAAexB,EAAK,aACtByB,EAAiBzB,EAAK,eACxBsB,GAAoB,aAAeE,EACnCF,GAAoB,eAAiBG,MAAkB,MAAkBD,CAAY,EACrFF,GAAoB,WAAa,CAAC,CAACG,CACrC,CACA,SAASC,GAAmB,CAC1B,SAAO,KAAc,CAAC,EAAGJ,EAAmB,CAC9C,CACA,IAAIK,EAAW,SAAkBpC,EAAO,CACtC,IAAIqC,EAAOrC,EAAM,KACfU,EAAYV,EAAM,UAClBsC,EAAUtC,EAAM,QAChBa,EAAQb,EAAM,MACdiC,EAAejC,EAAM,aACrBkC,GAAiBlC,EAAM,eACvBuC,KAAY,MAAyBvC,EAAOsB,EAAS,EACnDkB,GAAS,UAAa,EACtBC,GAASV,GASb,GARIE,IACFQ,GAAS,CACP,aAAcR,EACd,eAAgBC,OAAkB,MAAkBD,CAAY,CAClE,MAEF,MAAgBO,EAAM,KACtB,SAAQ,KAAiBH,CAAI,EAAG,0CAA0C,OAAOA,CAAI,CAAC,EAClF,IAAC,KAAiBA,CAAI,EACxB,OAAO,KAET,IAAIK,GAASL,EACb,OAAIK,IAAU,OAAOA,GAAO,MAAS,aACnCA,MAAS,QAAc,KAAc,CAAC,EAAGA,EAAM,EAAG,CAAC,EAAG,CACpD,KAAMA,GAAO,KAAKD,GAAO,aAAcA,GAAO,cAAc,CAC9D,CAAC,MAEI,MAASC,GAAO,KAAM,OAAO,OAAOA,GAAO,IAAI,KAAG,QAAc,KAAc,CACnF,UAAWhC,EACX,QAAS4B,EACT,MAAOzB,EACP,YAAa6B,GAAO,KACpB,MAAO,MACP,OAAQ,MACR,KAAM,eACN,cAAe,MACjB,EAAGH,CAAS,EAAG,CAAC,EAAG,CACjB,IAAKC,EACP,CAAC,CAAC,CACJ,EACAJ,EAAS,YAAc,YACvBA,EAAS,iBAAmBD,EAC5BC,EAAS,iBAAmBJ,GAC5B,MAAeI,EC5DR,SAASO,EAAgBC,EAAc,CAC5C,IAAIC,KAAwB,MAAuBD,CAAY,EAC7DE,KAAyB,KAAeD,EAAuB,CAAC,EAChEZ,EAAea,EAAuB,CAAC,EACvCZ,EAAiBY,EAAuB,CAAC,EAC3C,OAAO,EAAU,iBAAiB,CAChC,aAAcb,EACd,eAAgBC,CAClB,CAAC,CACH,CACO,SAASa,GAAkB,CAChC,IAAIN,EAAS,EAAU,iBAAiB,EACxC,OAAKA,EAAO,WAGL,CAACA,EAAO,aAAcA,EAAO,cAAc,EAFzCA,EAAO,YAGlB,CCbA,IAAI,EAAY,CAAC,YAAa,OAAQ,OAAQ,SAAU,WAAY,UAAW,cAAc,EAU7FE,EAAgB,QAAK,OAAO,EAI5B,IAAIK,EAAoB,cAAiB,SAAUhD,EAAOC,EAAK,CAC7D,IAAIS,EAAYV,EAAM,UACpBqC,EAAOrC,EAAM,KACbiD,EAAOjD,EAAM,KACbkD,EAASlD,EAAM,OACfmD,EAAWnD,EAAM,SACjBsC,GAAUtC,EAAM,QAChB4C,EAAe5C,EAAM,aACrBuC,MAAY,MAAyBvC,EAAO,CAAS,EACnDoD,GAAoB,cAAiBC,GAAO,EAC9CC,GAAwBF,GAAkB,UAC1C9C,GAAYgD,KAA0B,OAAS,UAAYA,GAC3DC,GAAgBH,GAAkB,cAChCI,GAAc,KAAWD,GAAejD,MAAW,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOA,GAAW,GAAG,EAAE,OAAO+B,EAAK,IAAI,EAAG,CAAC,CAACA,EAAK,IAAI,EAAG,GAAG,OAAO/B,GAAW,OAAO,EAAG,CAAC,CAAC2C,GAAQZ,EAAK,OAAS,SAAS,EAAG3B,CAAS,EAC9N+C,EAAeN,EACfM,IAAiB,QAAanB,KAChCmB,EAAe,IAEjB,IAAIC,GAAWR,EAAS,CACtB,YAAa,UAAU,OAAOA,EAAQ,MAAM,EAC5C,UAAW,UAAU,OAAOA,EAAQ,MAAM,CAC5C,EAAI,OACAL,MAAwB,MAAuBD,CAAY,EAC7DE,MAAyB,KAAeD,GAAuB,CAAC,EAChEZ,GAAea,GAAuB,CAAC,EACvCZ,GAAiBY,GAAuB,CAAC,EAC3C,OAAoB,iBAAoB,UAAQ,KAAS,CACvD,KAAM,MACN,aAAcT,EAAK,IACrB,EAAGE,GAAW,CACZ,IAAKtC,EACL,SAAUwD,EACV,QAASnB,GACT,UAAWkB,EACb,CAAC,EAAgB,iBAAoB,EAAW,CAC9C,KAAMnB,EACN,aAAcJ,GACd,eAAgBC,GAChB,MAAOwB,EACT,CAAC,CAAC,CACJ,CAAC,EACDV,EAAK,YAAc,WACnBA,EAAK,gBAAkBD,EACvBC,EAAK,gBAAkBL,EACvB,MAAeK,C,oDC/DXW,KAA2B,iBAAc,CAAC,CAAC,EAC/C,IAAeA,C,uTCMf,SAASC,EAAUC,EAAO,CACxB,OAAOA,EAAM,QAAQ,QAAS,SAAUC,EAAOC,EAAG,CAChD,OAAOA,EAAE,YAAY,CACvB,CAAC,CACH,CACO,SAASC,EAAQC,EAAOC,EAAS,IACtC,OAAKD,EAAO,uBAAuB,OAAOC,CAAO,CAAC,CACpD,CACO,SAASC,EAAiBzB,EAAQ,CACvC,SAAO,KAAQA,CAAM,IAAM,UAAY,OAAOA,EAAO,MAAS,UAAY,OAAOA,EAAO,OAAU,cAAa,KAAQA,EAAO,IAAI,IAAM,UAAY,OAAOA,EAAO,MAAS,WAC7K,CACO,SAAS0B,IAAiB,CAC/B,IAAIC,EAAQ,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAC,EACjF,OAAO,OAAO,KAAKA,CAAK,EAAE,OAAO,SAAUC,EAAKC,EAAK,CACnD,IAAIC,EAAMH,EAAME,CAAG,EACnB,OAAQA,EAAK,CACX,IAAK,QACHD,EAAI,UAAYE,EAChB,OAAOF,EAAI,MACX,MACF,QACE,OAAOA,EAAIC,CAAG,EACdD,EAAIV,EAAUW,CAAG,CAAC,EAAIC,CAC1B,CACA,OAAOF,CACT,EAAG,CAAC,CAAC,CACP,CACO,SAASG,GAASC,EAAMH,EAAKI,EAAW,CAC7C,OAAKA,EAOe,iBAAoBD,EAAK,OAAK,QAAc,KAAc,CAC5E,IAAKH,CACP,EAAGH,GAAeM,EAAK,KAAK,CAAC,EAAGC,CAAS,GAAID,EAAK,UAAY,CAAC,GAAG,IAAI,SAAUE,EAAOC,EAAO,CAC5F,OAAOJ,GAASG,EAAO,GAAG,OAAOL,EAAK,GAAG,EAAE,OAAOG,EAAK,IAAK,GAAG,EAAE,OAAOG,CAAK,CAAC,CAChF,CAAC,CAAC,EAVoB,iBAAoBH,EAAK,OAAK,KAAc,CAC9D,IAAKH,CACP,EAAGH,GAAeM,EAAK,KAAK,CAAC,GAAIA,EAAK,UAAY,CAAC,GAAG,IAAI,SAAUE,EAAOC,EAAO,CAChF,OAAOJ,GAASG,EAAO,GAAG,OAAOL,EAAK,GAAG,EAAE,OAAOG,EAAK,IAAK,GAAG,EAAE,OAAOG,CAAK,CAAC,CAChF,CAAC,CAAC,CAON,CACO,SAASC,GAAkB7C,EAAc,CAE9C,SAAO,YAAcA,CAAY,EAAE,CAAC,CACtC,CACO,SAAS8C,EAAuBnC,EAAc,CACnD,OAAKA,EAGE,MAAM,QAAQA,CAAY,EAAIA,EAAe,CAACA,CAAY,EAFxD,CAAC,CAGZ,CAIO,IAAIoC,EAAe,CACxB,MAAO,MACP,OAAQ,MACR,KAAM,eACN,cAAe,OACf,UAAW,OACb,EACWC,EAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EACbC,EAAkB,SAAyBC,EAAQ,CAC5D,IAAIC,KAAc,eAAW,IAAW,EACtCC,EAAMD,EAAY,IAClB9E,EAAY8E,EAAY,UACtBE,EAAiBL,EACjB3E,IACFgF,EAAiBA,EAAe,QAAQ,WAAYhF,CAAS,MAE/D,cAAU,UAAY,CACpB,IAAIiF,EAAMJ,EAAO,QACbK,KAAa,MAAcD,CAAG,KAClC,OAAUD,EAAgB,oBAAqB,CAC7C,QAAS,GACT,IAAKD,EACL,SAAUG,CACZ,CAAC,CACH,EAAG,CAAC,CAAC,CACP,C,6BCvFA,SAASC,EAAQC,EAAK,CAAE,0BAA2B,OAAOD,EAAwB,OAAO,QAArB,YAA2C,OAAO,OAAO,UAA1B,SAAqC,SAAUC,EAAK,CAAE,OAAO,OAAOA,CAAK,EAAI,SAAUA,EAAK,CAAE,OAAOA,GAAqB,OAAO,QAArB,YAA+BA,EAAI,cAAgB,QAAUA,IAAQ,OAAO,UAAY,SAAW,OAAOA,CAAK,EAAGD,EAAQC,CAAG,CAAG,CAE/U,EAA6C,CAC3C,MAAO,EACT,EACAC,EAAQ,GAAe,EAAyB,EAA2B,EAAgB,EAA2B,EAAkB,EAAgB,OAKxJ,SAASC,EAAMC,EAAK,CAIlB,QAHIC,EAAS,CAAC,EACVC,EAAI,EAEDA,EAAIF,EAAI,QAAQ,CACrB,IAAIG,EAAOH,EAAIE,CAAC,EAEhB,GAAIC,IAAS,KAAOA,IAAS,KAAOA,IAAS,IAAK,CAChDF,EAAO,KAAK,CACV,KAAM,WACN,MAAOC,EACP,MAAOF,EAAIE,GAAG,CAChB,CAAC,EACD,QACF,CAEA,GAAIC,IAAS,KAAM,CACjBF,EAAO,KAAK,CACV,KAAM,eACN,MAAOC,IACP,MAAOF,EAAIE,GAAG,CAChB,CAAC,EACD,QACF,CAEA,GAAIC,IAAS,IAAK,CAChBF,EAAO,KAAK,CACV,KAAM,OACN,MAAOC,EACP,MAAOF,EAAIE,GAAG,CAChB,CAAC,EACD,QACF,CAEA,GAAIC,IAAS,IAAK,CAChBF,EAAO,KAAK,CACV,KAAM,QACN,MAAOC,EACP,MAAOF,EAAIE,GAAG,CAChB,CAAC,EACD,QACF,CAEA,GAAIC,IAAS,IAAK,CAIhB,QAHIC,EAAO,GACPC,EAAIH,EAAI,EAELG,EAAIL,EAAI,QAAQ,CACrB,IAAIM,EAAON,EAAI,WAAWK,CAAC,EAE3B,GACAC,GAAQ,IAAMA,GAAQ,IACtBA,GAAQ,IAAMA,GAAQ,IACtBA,GAAQ,IAAMA,GAAQ,KACtBA,IAAS,GAAI,CACXF,GAAQJ,EAAIK,GAAG,EACf,QACF,CAEA,KACF,CAEA,GAAI,CAACD,EAAM,MAAM,IAAI,UAAU,6BAA+BF,CAAC,EAC/DD,EAAO,KAAK,CACV,KAAM,OACN,MAAOC,EACP,MAAOE,CACT,CAAC,EACDF,EAAIG,EACJ,QACF,CAEA,GAAIF,IAAS,IAAK,CAChB,IAAII,EAAQ,EACRC,EAAU,GACVH,EAAIH,EAAI,EAEZ,GAAIF,EAAIK,CAAC,IAAM,IACb,MAAM,IAAI,UAAU,oCAAsCA,CAAC,EAG7D,KAAOA,EAAIL,EAAI,QAAQ,CACrB,GAAIA,EAAIK,CAAC,IAAM,KAAM,CACnBG,GAAWR,EAAIK,GAAG,EAAIL,EAAIK,GAAG,EAC7B,QACF,CAEA,GAAIL,EAAIK,CAAC,IAAM,KAGb,GAFAE,IAEIA,IAAU,EAAG,CACfF,IACA,KACF,UACSL,EAAIK,CAAC,IAAM,MACpBE,IAEIP,EAAIK,EAAI,CAAC,IAAM,KACjB,MAAM,IAAI,UAAU,uCAAyCA,CAAC,EAIlEG,GAAWR,EAAIK,GAAG,CACpB,CAEA,GAAIE,EAAO,MAAM,IAAI,UAAU,yBAA2BL,CAAC,EAC3D,GAAI,CAACM,EAAS,MAAM,IAAI,UAAU,sBAAwBN,CAAC,EAC3DD,EAAO,KAAK,CACV,KAAM,UACN,MAAOC,EACP,MAAOM,CACT,CAAC,EACDN,EAAIG,EACJ,QACF,CAEAJ,EAAO,KAAK,CACV,KAAM,OACN,MAAOC,EACP,MAAOF,EAAIE,GAAG,CAChB,CAAC,CACH,CAEA,SAAO,KAAK,CACV,KAAM,MACN,MAAOA,EACP,MAAO,EACT,CAAC,EACMD,CACT,CAMA,SAASQ,EAAMT,EAAKU,EAAS,CACvBA,IAAY,SAEdA,EAAU,CAAC,GAoCb,QAjCIT,EAASF,EAAMC,CAAG,EAClBW,EAAKD,EAAQ,SACbE,EAAWD,IAAO,OAAS,KAAOA,EAClCE,EAAiB,KAAOC,GAAaJ,EAAQ,WAAa,KAAK,EAAI,MACnEK,EAAS,CAAC,EACVrC,EAAM,EACNwB,EAAI,EACJc,EAAO,GAEPC,EAAa,SAAoBC,EAAM,CACzC,GAAIhB,EAAID,EAAO,QAAUA,EAAOC,CAAC,EAAE,OAASgB,EAAM,OAAOjB,EAAOC,GAAG,EAAE,KACvE,EAEIiB,EAAc,SAAqBD,EAAM,CAC3C,IAAIE,GAAQH,EAAWC,CAAI,EAC3B,GAAIE,KAAU,OAAW,OAAOA,GAChC,IAAIT,GAAKV,EAAOC,CAAC,EACbmB,GAAWV,GAAG,KACd3B,GAAQ2B,GAAG,MACf,MAAM,IAAI,UAAU,cAAgBU,GAAW,OAASrC,GAAQ,cAAgBkC,CAAI,CACtF,EAEII,EAAc,UAAuB,CAIvC,QAHIP,EAAS,GACTK,GAEGA,GAAQH,EAAW,MAAM,GAAKA,EAAW,cAAc,GAC5DF,GAAUK,GAGZ,OAAOL,CACT,EAEOb,EAAID,EAAO,QAAQ,CACxB,IAAIE,EAAOc,EAAW,MAAM,EACxBb,EAAOa,EAAW,MAAM,EACxBT,GAAUS,EAAW,SAAS,EAElC,GAAIb,GAAQI,GAAS,CACnB,IAAIe,EAASpB,GAAQ,GAEjBS,EAAS,QAAQW,CAAM,IAAM,KAC/BP,GAAQO,EACRA,EAAS,IAGPP,IACFD,EAAO,KAAKC,CAAI,EAChBA,EAAO,IAGTD,EAAO,KAAK,CACV,KAAMX,GAAQ1B,IACd,OAAQ6C,EACR,OAAQ,GACR,QAASf,IAAWK,EACpB,SAAUI,EAAW,UAAU,GAAK,EACtC,CAAC,EACD,QACF,CAEA,IAAIG,GAAQjB,GAAQc,EAAW,cAAc,EAE7C,GAAIG,GAAO,CACTJ,GAAQI,GACR,QACF,CAEIJ,IACFD,EAAO,KAAKC,CAAI,EAChBA,EAAO,IAGT,IAAIQ,GAAOP,EAAW,MAAM,EAE5B,GAAIO,GAAM,CACR,IAAID,EAASD,EAAY,EACrBG,GAASR,EAAW,MAAM,GAAK,GAC/BS,GAAYT,EAAW,SAAS,GAAK,GACrCU,GAASL,EAAY,EACzBH,EAAY,OAAO,EACnBJ,EAAO,KAAK,CACV,KAAMU,KAAWC,GAAYhD,IAAQ,IACrC,QAAS+C,IAAU,CAACC,GAAYb,EAAiBa,GACjD,OAAQH,EACR,OAAQI,GACR,SAAUV,EAAW,UAAU,GAAK,EACtC,CAAC,EACD,QACF,CAEAE,EAAY,KAAK,CACnB,CAEA,OAAOJ,CACT,CAEA,EAAgBN,EAKhB,SAASmB,GAAQ5B,EAAKU,EAAS,CAC7B,OAAOmB,GAAiBpB,EAAMT,EAAKU,CAAO,EAAGA,CAAO,CACtD,CAEA,EAAkBkB,GAKlB,SAASC,GAAiB5B,EAAQS,EAAS,CACrCA,IAAY,SAEdA,EAAU,CAAC,GAGb,IAAIoB,EAAUC,EAAMrB,CAAO,EACvBC,EAAKD,EAAQ,OACbsB,EAASrB,IAAO,OAAS,SAAUsB,EAAG,CACxC,OAAOA,CACT,EAAItB,EACAuB,EAAKxB,EAAQ,SACbyB,EAAWD,IAAO,OAAS,GAAOA,EAElCE,EAAUnC,EAAO,IAAI,SAAUzF,EAAO,CACxC,GAAIoF,EAAQpF,CAAK,IAAM,SACrB,OAAO,IAAI,OAAO,OAASA,EAAM,QAAU,KAAMsH,CAAO,CAE5D,CAAC,EACD,OAAO,SAAUO,EAAM,CAGrB,QAFIrB,EAAO,GAEFd,EAAI,EAAGA,EAAID,EAAO,OAAQC,IAAK,CACtC,IAAI1F,EAAQyF,EAAOC,CAAC,EAEpB,GAAI,OAAO1F,GAAU,SAAU,CAC7BwG,GAAQxG,EACR,QACF,CAEA,IAAI4G,EAAQiB,EAAOA,EAAK7H,EAAM,IAAI,EAAI,OAClC8H,EAAW9H,EAAM,WAAa,KAAOA,EAAM,WAAa,IACxD+H,EAAS/H,EAAM,WAAa,KAAOA,EAAM,WAAa,IAE1D,GAAI,MAAM,QAAQ4G,CAAK,EAAG,CACxB,GAAI,CAACmB,EACH,MAAM,IAAI,UAAU,aAAe/H,EAAM,KAAO,mCAAmC,EAGrF,GAAI4G,EAAM,SAAW,EAAG,CACtB,GAAIkB,EAAU,SACd,MAAM,IAAI,UAAU,aAAe9H,EAAM,KAAO,mBAAmB,CACrE,CAEA,QAAS6F,GAAI,EAAGA,GAAIe,EAAM,OAAQf,KAAK,CACrC,IAAImC,EAAUR,EAAOZ,EAAMf,EAAC,EAAG7F,CAAK,EAEpC,GAAI2H,GAAY,CAACC,EAAQlC,CAAC,EAAE,KAAKsC,CAAO,EACtC,MAAM,IAAI,UAAU,iBAAmBhI,EAAM,KAAO,eAAiBA,EAAM,QAAU,eAAiBgI,EAAU,GAAG,EAGrHxB,GAAQxG,EAAM,OAASgI,EAAUhI,EAAM,MACzC,CAEA,QACF,CAEA,GAAI,OAAO4G,GAAU,UAAY,OAAOA,GAAU,SAAU,CAC1D,IAAIoB,EAAUR,EAAO,OAAOZ,CAAK,EAAG5G,CAAK,EAEzC,GAAI2H,GAAY,CAACC,EAAQlC,CAAC,EAAE,KAAKsC,CAAO,EACtC,MAAM,IAAI,UAAU,aAAehI,EAAM,KAAO,eAAiBA,EAAM,QAAU,eAAiBgI,EAAU,GAAG,EAGjHxB,GAAQxG,EAAM,OAASgI,EAAUhI,EAAM,OACvC,QACF,CAEA,GAAI,GACJ,KAAIiI,GAAgBF,EAAS,WAAa,WAC1C,MAAM,IAAI,UAAU,aAAe/H,EAAM,KAAO,WAAaiI,EAAa,EAC5E,CAEA,OAAOzB,CACT,CACF,CAEA,EAA2Ba,GAK3B,SAAS5D,GAAM+B,EAAKU,EAAS,CAC3B,IAAIgC,EAAO,CAAC,EACRC,EAAKC,GAAa5C,EAAK0C,EAAMhC,CAAO,EACxC,OAAOmC,GAAiBF,EAAID,EAAMhC,CAAO,CAC3C,CAEA,EAAgBzC,GAKhB,SAAS4E,GAAiBF,EAAID,EAAMhC,EAAS,CACvCA,IAAY,SAEdA,EAAU,CAAC,GAGb,IAAIC,EAAKD,EAAQ,OACboC,EAASnC,IAAO,OAAS,SAAUsB,EAAG,CACxC,OAAOA,CACT,EAAItB,EACJ,OAAO,SAAUoC,EAAU,CACzB,IAAIC,EAAIL,EAAG,KAAKI,CAAQ,EACxB,GAAI,CAACC,EAAG,MAAO,GAmBf,QAlBIhC,EAAOgC,EAAE,CAAC,EACVhE,EAAQgE,EAAE,MACVC,EAAS,OAAO,OAAO,IAAI,EAE3BC,EAAU,SAAiBhD,EAAG,CAEhC,GAAI8C,EAAE9C,CAAC,IAAM,OAAW,MAAO,WAC/B,IAAIxB,EAAMgE,EAAKxC,EAAI,CAAC,EAEhBxB,EAAI,WAAa,KAAOA,EAAI,WAAa,IAC3CuE,EAAOvE,EAAI,IAAI,EAAIsE,EAAE9C,CAAC,EAAE,MAAMxB,EAAI,OAASA,EAAI,MAAM,EAAE,IAAI,SAAU0C,GAAO,CAC1E,OAAO0B,EAAO1B,GAAO1C,CAAG,CAC1B,CAAC,EAEDuE,EAAOvE,EAAI,IAAI,EAAIoE,EAAOE,EAAE9C,CAAC,EAAGxB,CAAG,CAEvC,EAESwB,EAAI,EAAGA,EAAI8C,EAAE,OAAQ9C,IAC5BgD,EAAQhD,CAAC,EAGX,MAAO,CACL,KAAMc,EACN,MAAOhC,EACP,OAAQiE,CACV,CACF,CACF,CAEA,EAA2BJ,GAK3B,SAAS/B,GAAad,EAAK,CACzB,OAAOA,EAAI,QAAQ,4BAA6B,MAAM,CACxD,CAMA,SAAS+B,EAAMrB,EAAS,CACtB,OAAOA,GAAWA,EAAQ,UAAY,GAAK,GAC7C,CAMA,SAASyC,EAAenC,EAAM0B,EAAM,CAClC,GAAI,CAACA,EAAM,OAAO1B,EAElB,IAAIoC,EAASpC,EAAK,OAAO,MAAM,WAAW,EAE1C,GAAIoC,EACF,QAASlD,EAAI,EAAGA,EAAIkD,EAAO,OAAQlD,IACjCwC,EAAK,KAAK,CACR,KAAMxC,EACN,OAAQ,GACR,OAAQ,GACR,SAAU,GACV,QAAS,EACX,CAAC,EAIL,OAAOc,CACT,CAMA,SAASqC,EAAcC,EAAOZ,EAAMhC,EAAS,CAC3C,IAAI6C,EAAQD,EAAM,IAAI,SAAUtC,EAAM,CACpC,OAAO4B,GAAa5B,EAAM0B,EAAMhC,CAAO,EAAE,MAC3C,CAAC,EACD,OAAO,IAAI,OAAO,MAAQ6C,EAAM,KAAK,GAAG,EAAI,IAAKxB,EAAMrB,CAAO,CAAC,CACjE,CAMA,SAAS8C,GAAexC,EAAM0B,EAAMhC,EAAS,CAC3C,OAAO+C,GAAehD,EAAMO,EAAMN,CAAO,EAAGgC,EAAMhC,CAAO,CAC3D,CAMA,SAAS+C,GAAexD,EAAQyC,EAAMhC,EAAS,CACzCA,IAAY,SAEdA,EAAU,CAAC,GAiBb,QAdIC,EAAKD,EAAQ,OACbgD,EAAS/C,IAAO,OAAS,GAAQA,EACjCuB,EAAKxB,EAAQ,MACbiD,EAAQzB,IAAO,OAAS,GAAOA,EAC/B0B,EAAKlD,EAAQ,IACbmD,EAAMD,IAAO,OAAS,GAAOA,EAC7BE,EAAKpD,EAAQ,OACbsB,EAAS8B,IAAO,OAAS,SAAU7B,GAAG,CACxC,OAAOA,EACT,EAAI6B,EACAC,EAAW,IAAMjD,GAAaJ,EAAQ,UAAY,EAAE,EAAI,MACxDsD,EAAY,IAAMlD,GAAaJ,EAAQ,WAAa,KAAK,EAAI,IAC7DuD,EAAQN,EAAQ,IAAM,GAEjBO,EAAK,EAAGC,GAAWlE,EAAQiE,EAAKC,GAAS,OAAQD,IAAM,CAC9D,IAAI1J,EAAQ2J,GAASD,CAAE,EAEvB,GAAI,OAAO1J,GAAU,SACnByJ,GAASnD,GAAakB,EAAOxH,CAAK,CAAC,MAC9B,CACL,IAAI+G,GAAST,GAAakB,EAAOxH,EAAM,MAAM,CAAC,EAC1CmH,GAASb,GAAakB,EAAOxH,EAAM,MAAM,CAAC,EAE9C,GAAIA,EAAM,QAGR,GAFIkI,GAAMA,EAAK,KAAKlI,CAAK,EAErB+G,IAAUI,GACZ,GAAInH,EAAM,WAAa,KAAOA,EAAM,WAAa,IAAK,CACpD,IAAI4J,GAAM5J,EAAM,WAAa,IAAM,IAAM,GACzCyJ,GAAS,MAAQ1C,GAAS,OAAS/G,EAAM,QAAU,OAASmH,GAASJ,GAAS,MAAQ/G,EAAM,QAAU,OAASmH,GAAS,IAAMyC,EAChI,MACEH,GAAS,MAAQ1C,GAAS,IAAM/G,EAAM,QAAU,IAAMmH,GAAS,IAAMnH,EAAM,cAG7EyJ,GAAS,IAAMzJ,EAAM,QAAU,IAAMA,EAAM,cAG7CyJ,GAAS,MAAQ1C,GAASI,GAAS,IAAMnH,EAAM,QAEnD,CACF,CAEA,GAAIqJ,EACGH,IAAQO,GAASD,EAAY,KAClCC,GAAUvD,EAAQ,SAAiB,MAAQqD,EAAW,IAAzB,QACxB,CACL,IAAIM,GAAWpE,EAAOA,EAAO,OAAS,CAAC,EACnCqE,GAAiB,OAAOD,IAAa,SAAWL,EAAU,QAAQK,GAASA,GAAS,OAAS,CAAC,CAAC,EAAI,GACvGA,KAAa,OAERX,IACHO,GAAS,MAAQD,EAAY,MAAQD,EAAW,OAG7CO,KACHL,GAAS,MAAQD,EAAY,IAAMD,EAAW,IAElD,CAEA,OAAO,IAAI,OAAOE,EAAOlC,EAAMrB,CAAO,CAAC,CACzC,CAEA,EAAyB+C,GASzB,SAASb,GAAa5B,EAAM0B,EAAMhC,EAAS,CACzC,OAAIM,aAAgB,OAAemC,EAAenC,EAAM0B,CAAI,EACxD,MAAM,QAAQ1B,CAAI,EAAUqC,EAAcrC,EAAM0B,EAAMhC,CAAO,EAC1D8C,GAAexC,EAAM0B,EAAMhC,CAAO,CAC3C,CAEAZ,EAAQ,GAAe8C,E,oCChiBvB,IAAI2B,EAA0B,MAAQ,KAAK,wBAA2B,SAAUC,EAAUC,EAAOC,EAAMC,EAAG,CACtG,GAAID,IAAS,KAAO,CAACC,EAAG,MAAM,IAAI,UAAU,+CAA+C,EAC3F,GAAI,OAAOF,GAAU,WAAaD,IAAaC,GAAS,CAACE,EAAI,CAACF,EAAM,IAAID,CAAQ,EAAG,MAAM,IAAI,UAAU,0EAA0E,EACjL,OAAOE,IAAS,IAAMC,EAAID,IAAS,IAAMC,EAAE,KAAKH,CAAQ,EAAIG,EAAIA,EAAE,MAAQF,EAAM,IAAID,CAAQ,CAChG,EACII,EAA0B,MAAQ,KAAK,wBAA2B,SAAUJ,EAAUC,EAAOrD,EAAOsD,EAAMC,EAAG,CAC7G,GAAID,IAAS,IAAK,MAAM,IAAI,UAAU,gCAAgC,EACtE,GAAIA,IAAS,KAAO,CAACC,EAAG,MAAM,IAAI,UAAU,+CAA+C,EAC3F,GAAI,OAAOF,GAAU,WAAaD,IAAaC,GAAS,CAACE,EAAI,CAACF,EAAM,IAAID,CAAQ,EAAG,MAAM,IAAI,UAAU,yEAAyE,EAChL,OAAQE,IAAS,IAAMC,EAAE,KAAKH,EAAUpD,CAAK,EAAIuD,EAAIA,EAAE,MAAQvD,EAAQqD,EAAM,IAAID,EAAUpD,CAAK,EAAIA,CACxG,EACIyD,EACJ,OAAO,eAAe/E,EAAS,aAAc,CAAE,MAAO,EAAK,CAAE,EAC7DA,EAAQ,UAAY,OACpBA,EAAQ,MAAQW,GAChBX,EAAQ,QAAU8B,EAClB9B,EAAQ,MAAQ7B,EAChB,MAAM6G,EAAoB,IACpBC,GAAc3D,GAAUA,EACxB4D,GAAW,sBACXC,GAAc,oCACdC,GAAY,oCACZC,GAAgB,CAElB,IAAK,IACL,IAAK,IAEL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,IACL,IAAK,GACT,EAIA,SAASC,EAAOpF,EAAK,CACjB,OAAOA,EAAI,QAAQ,uBAAwB,MAAM,CACrD,CAIA,SAASqF,EAAQ3E,EAAS,CACtB,OAAOA,EAAQ,UAAY,IAAM,IACrC,CAIA,SAAUX,EAAMC,EAAK,CACjB,MAAMsF,EAAQ,CAAC,GAAGtF,CAAG,EACrB,IAAIE,EAAI,EACR,SAASE,GAAO,CACZ,IAAIgB,EAAQ,GACZ,GAAI4D,GAAS,KAAKM,EAAM,EAAEpF,CAAC,CAAC,EAExB,IADAkB,GAASkE,EAAMpF,CAAC,EACT+E,GAAY,KAAKK,EAAM,EAAEpF,CAAC,CAAC,GAC9BkB,GAASkE,EAAMpF,CAAC,UAGfoF,EAAMpF,CAAC,IAAM,IAAK,CACvB,IAAIqF,EAAMrF,EACV,KAAOA,EAAIoF,EAAM,QAAQ,CACrB,GAAIA,EAAM,EAAEpF,CAAC,IAAM,IAAK,CACpBA,IACAqF,EAAM,EACN,KACJ,CACID,EAAMpF,CAAC,IAAM,KACbkB,GAASkE,EAAM,EAAEpF,CAAC,EAGlBkB,GAASkE,EAAMpF,CAAC,CAExB,CACA,GAAIqF,EACA,MAAM,IAAI,UAAU,yBAAyBA,CAAG,KAAKL,EAAS,EAAE,CAExE,CACA,GAAI,CAAC9D,EACD,MAAM,IAAI,UAAU,6BAA6BlB,CAAC,KAAKgF,EAAS,EAAE,EAEtE,OAAO9D,CACX,CACA,KAAOlB,EAAIoF,EAAM,QAAQ,CACrB,MAAMlE,EAAQkE,EAAMpF,CAAC,EACfgB,EAAOiE,GAAc/D,CAAK,EAChC,GAAIF,EACA,KAAM,CAAE,OAAM,MAAOhB,IAAK,OAAM,UAE3BkB,IAAU,KACf,KAAM,CAAE,KAAM,UAAW,MAAOlB,IAAK,MAAOoF,EAAMpF,GAAG,CAAE,UAElDkB,IAAU,IAAK,CACpB,MAAMA,GAAQhB,EAAK,EACnB,KAAM,CAAE,KAAM,QAAS,MAAOF,EAAG,QAAM,CAC3C,SACSkB,IAAU,IAAK,CACpB,MAAMA,GAAQhB,EAAK,EACnB,KAAM,CAAE,KAAM,WAAY,MAAOF,EAAG,QAAM,CAC9C,MAEI,KAAM,CAAE,KAAM,OAAQ,MAAOA,EAAG,MAAOoF,EAAMpF,GAAG,CAAE,CAE1D,CACA,MAAO,CAAE,KAAM,MAAO,MAAOA,EAAG,MAAO,EAAG,CAC9C,CACA,MAAMsF,EAAK,CACP,YAAYvF,EAAQ,CAChB,KAAK,OAASA,EACd4E,EAAW,IAAI,KAAM,MAAM,CAC/B,CACA,MAAO,CACH,GAAI,CAACN,EAAuB,KAAMM,EAAY,GAAG,EAAG,CAChD,MAAMY,EAAO,KAAK,OAAO,KAAK,EAC9Bb,EAAuB,KAAMC,EAAYY,EAAK,MAAO,GAAG,CAC5D,CACA,OAAOlB,EAAuB,KAAMM,EAAY,GAAG,CACvD,CACA,WAAW3D,EAAM,CACb,MAAM1G,EAAQ,KAAK,KAAK,EACxB,GAAIA,EAAM,OAAS0G,EAEnB,SAAuB,KAAM2D,EAAY,OAAW,GAAG,EAChDrK,EAAM,KACjB,CACA,QAAQ0G,EAAM,CACV,MAAME,EAAQ,KAAK,WAAWF,CAAI,EAClC,GAAIE,IAAU,OACV,OAAOA,EACX,KAAM,CAAE,KAAMC,EAAU,OAAM,EAAI,KAAK,KAAK,EAC5C,MAAM,IAAI,UAAU,cAAcA,CAAQ,OAAOrC,CAAK,cAAckC,CAAI,KAAKgE,EAAS,EAAE,CAC5F,CACA,MAAO,CACH,IAAInE,EAAS,GACTK,EACJ,KAAQA,EAAQ,KAAK,WAAW,MAAM,GAAK,KAAK,WAAW,SAAS,GAChEL,GAAUK,EAEd,OAAOL,CACX,CACJ,CACA8D,EAAa,IAAI,QAIjB,MAAMa,EAAU,CACZ,YAAYzF,EAAQ,CAChB,KAAK,OAASA,CAClB,CACJ,CACAH,EAAQ,UAAY4F,GAIpB,SAASjF,GAAMT,EAAKU,EAAU,CAAC,EAAG,CAC9B,KAAM,CAAE,aAAaqE,EAAW,EAAIrE,EAC9BiF,EAAK,IAAIH,GAAKzF,EAAMC,CAAG,CAAC,EAC9B,SAAS4F,EAAQC,GAAS,CACtB,MAAM5F,EAAS,CAAC,EAChB,OAAa,CACT,MAAMe,GAAO2E,EAAG,KAAK,EACjB3E,IACAf,EAAO,KAAK,CAAE,KAAM,OAAQ,MAAO6F,EAAW9E,EAAI,CAAE,CAAC,EACzD,MAAM+E,GAAQJ,EAAG,WAAW,OAAO,EACnC,GAAII,GAAO,CACP9F,EAAO,KAAK,CACR,KAAM,QACN,KAAM8F,EACV,CAAC,EACD,QACJ,CACA,MAAMC,GAAWL,EAAG,WAAW,UAAU,EACzC,GAAIK,GAAU,CACV/F,EAAO,KAAK,CACR,KAAM,WACN,KAAM+F,EACV,CAAC,EACD,QACJ,CAEA,GADaL,EAAG,WAAW,GAAG,EACpB,CACN1F,EAAO,KAAK,CACR,KAAM,QACN,OAAQ2F,EAAQ,GAAG,CACvB,CAAC,EACD,QACJ,CACA,SAAG,QAAQC,EAAO,EACX5F,CACX,CACJ,CACA,MAAMA,EAAS2F,EAAQ,KAAK,EAC5B,OAAO,IAAIF,GAAUzF,CAAM,CAC/B,CAIA,SAASgG,EAAS5D,EAAM3B,EAAS,CAC7B,KAAM,CAAE,SAAS,mBAAoB,YAAYoE,CAAkB,EAAIpE,EACjEwF,EAAKrE,EAAiBQ,EAAK,OAAQ2B,EAAWhC,CAAM,EAC1D,OAAO,SAAcK,GAAO,CAAC,EAAG,CAC5B,KAAM,CAACrB,EAAM,GAAGmF,EAAO,EAAID,EAAG7D,EAAI,EAClC,GAAI8D,GAAQ,OACR,MAAM,IAAI,UAAU,uBAAuBA,GAAQ,KAAK,IAAI,CAAC,EAAE,EAEnE,OAAOnF,CACX,CACJ,CAIA,SAASY,EAAQZ,EAAMN,EAAU,CAAC,EAAG,CACjC,OAAOuF,EAASjF,aAAgB0E,GAAY1E,EAAOP,GAAMO,EAAMN,CAAO,EAAGA,CAAO,CACpF,CACA,SAASmB,EAAiB5B,EAAQ+D,EAAWhC,EAAQ,CACjD,MAAMoE,EAAWnG,EAAO,IAAKzF,GAAU6L,EAAgB7L,EAAOwJ,EAAWhC,CAAM,CAAC,EAChF,OAAQK,GAAS,CACb,MAAMtB,EAAS,CAAC,EAAE,EAClB,UAAWuF,MAAWF,EAAU,CAC5B,KAAM,CAAChF,EAAO,GAAGmF,EAAM,EAAID,GAAQjE,CAAI,EACvCtB,EAAO,CAAC,GAAKK,EACbL,EAAO,KAAK,GAAGwF,EAAM,CACzB,CACA,OAAOxF,CACX,CACJ,CAIA,SAASsF,EAAgB7L,EAAOwJ,EAAWhC,EAAQ,CAC/C,GAAIxH,EAAM,OAAS,OACf,MAAO,IAAM,CAACA,EAAM,KAAK,EAC7B,GAAIA,EAAM,OAAS,QAAS,CACxB,MAAM0L,EAAKrE,EAAiBrH,EAAM,OAAQwJ,EAAWhC,CAAM,EAC3D,OAAQK,GAAS,CACb,KAAM,CAACjB,GAAO,GAAG+E,CAAO,EAAID,EAAG7D,CAAI,EACnC,OAAK8D,EAAQ,OAEN,CAAC,EAAE,EADC,CAAC/E,EAAK,CAErB,CACJ,CACA,MAAMoF,EAAcxE,GAAU+C,GAC9B,OAAIvK,EAAM,OAAS,YAAcwH,IAAW,GAChCK,GAAS,CACb,MAAMjB,EAAQiB,EAAK7H,EAAM,IAAI,EAC7B,GAAI4G,GAAS,KACT,MAAO,CAAC,GAAI5G,EAAM,IAAI,EAC1B,GAAI,CAAC,MAAM,QAAQ4G,CAAK,GAAKA,EAAM,SAAW,EAC1C,MAAM,IAAI,UAAU,aAAa5G,EAAM,IAAI,2BAA2B,EAE1E,MAAO,CACH4G,EACK,IAAI,CAACA,GAAOpC,IAAU,CACvB,GAAI,OAAOoC,IAAU,SACjB,MAAM,IAAI,UAAU,aAAa5G,EAAM,IAAI,IAAIwE,CAAK,kBAAkB,EAE1E,OAAOwH,EAAYpF,EAAK,CAC5B,CAAC,EACI,KAAK4C,CAAS,CACvB,CACJ,EAEI3B,GAAS,CACb,MAAMjB,EAAQiB,EAAK7H,EAAM,IAAI,EAC7B,GAAI4G,GAAS,KACT,MAAO,CAAC,GAAI5G,EAAM,IAAI,EAC1B,GAAI,OAAO4G,GAAU,SACjB,MAAM,IAAI,UAAU,aAAa5G,EAAM,IAAI,kBAAkB,EAEjE,MAAO,CAACgM,EAAYpF,CAAK,CAAC,CAC9B,CACJ,CAIA,SAASqF,EAAOpE,EAAM3B,EAAU,CAAC,EAAG,CAChC,KAAM,CAAE,SAAS,mBAAoB,YAAYoE,EAAmB,MAAM,GAAM,WAAW,EAAM,EAAIpE,EAC/FqB,GAAQsD,EAAQ3E,CAAO,EACvBgG,EAAU,CAAC,EACXhE,GAAO,CAAC,EACd,SAAW,CAAE,SAAO,IAAKL,EACrB,UAAWsE,MAAOC,EAAQ3G,GAAQ,EAAG,CAAC,CAAC,EAAG,CACtC,MAAM4G,EAASC,EAAiBH,GAAK3C,EAAWtB,EAAI,EACpDgE,EAAQ,KAAKG,CAAM,CACvB,CAEJ,IAAIrG,GAAU,OAAOkG,EAAQ,KAAK,GAAG,CAAC,IAClCK,IACAvG,IAAW,MAAM4E,EAAOpB,CAAS,CAAC,OACtCxD,IAAWqD,EAAM,IAAM,MAAMuB,EAAOpB,CAAS,CAAC,MAC9C,MAAMrB,GAAK,IAAI,OAAOnC,GAASuB,EAAK,EAC9BiF,GAAWtE,GAAK,IAAKhE,IACnBoE,IAAW,GACJiC,GACPrG,GAAI,OAAS,QACNoE,EACH1B,IAAUA,GAAM,MAAM4C,CAAS,EAAE,IAAIlB,CAAM,CACtD,EACD,OAAO,OAAO,OAAO,SAAe9E,GAAO,CACvC,MAAMgF,EAAIL,GAAG,KAAK3E,EAAK,EACvB,GAAI,CAACgF,EACD,MAAO,GACX,KAAM,CAAE,EAAGhC,EAAK,EAAIgC,EACdC,GAAS,OAAO,OAAO,IAAI,EACjC,QAAS/C,GAAI,EAAGA,GAAI8C,EAAE,OAAQ9C,KAAK,CAC/B,GAAI8C,EAAE9C,EAAC,IAAM,OACT,SACJ,MAAMxB,GAAMgE,GAAKxC,GAAI,CAAC,EAChB+G,GAAUD,GAAS9G,GAAI,CAAC,EAC9B+C,GAAOvE,GAAI,IAAI,EAAIuI,GAAQjE,EAAE9C,EAAC,CAAC,CACnC,CACA,MAAO,CAAE,QAAM,SAAO,CAC1B,EAAG,CAAE,KAAG,CAAC,CACb,CACA,SAASjC,EAAM+C,EAAMN,EAAU,CAAC,EAAG,CAE/B,MAAMwG,GADQ,MAAM,QAAQlG,CAAI,EAAIA,EAAO,CAACA,CAAI,GAC5B,IAAKA,GAASA,aAAgB0E,GAAY1E,EAAOP,GAAMO,EAAMN,CAAO,CAAC,EACzF,OAAO+F,EAAOS,EAAOxG,CAAO,CAChC,CAIA,SAAUkG,EAAQ3G,EAAQjB,EAAOmI,EAAM,CACnC,GAAInI,IAAUiB,EAAO,OACjB,OAAO,MAAMkH,EAEjB,MAAM3M,EAAQyF,EAAOjB,CAAK,EAC1B,GAAIxE,EAAM,OAAS,QAAS,CACxB,MAAM4M,EAAOD,EAAK,MAAM,EACxB,UAAWR,KAAOC,EAAQpM,EAAM,OAAQ,EAAG4M,CAAI,EAC3C,SAAOR,EAAQ3G,EAAQjB,EAAQ,EAAG2H,CAAG,EAE7C,MAEIQ,EAAK,KAAK3M,CAAK,EAEnB,SAAOoM,EAAQ3G,EAAQjB,EAAQ,EAAGmI,CAAI,EAC1C,CAIA,SAASL,EAAiB7G,EAAQ+D,EAAWtB,EAAM,CAC/C,IAAI3B,EAAS,GACTsG,EAAY,GACZC,EAAqB,GACzB,QAASpH,GAAI,EAAGA,GAAID,EAAO,OAAQC,KAAK,CACpC,MAAM1F,EAAQyF,EAAOC,EAAC,EACtB,GAAI1F,EAAM,OAAS,OAAQ,CACvBuG,GAAUqE,EAAO5K,EAAM,KAAK,EAC5B6M,EAAY7M,EAAM,MAClB8M,IAAuBA,EAAqB9M,EAAM,MAAM,SAASwJ,CAAS,GAC1E,QACJ,CACA,GAAIxJ,EAAM,OAAS,SAAWA,EAAM,OAAS,WAAY,CACrD,GAAI,CAAC8M,GAAsB,CAACD,EACxB,MAAM,IAAI,UAAU,uBAAuB7M,EAAM,IAAI,MAAM0K,EAAS,EAAE,EAEtE1K,EAAM,OAAS,QACfuG,GAAU,IAAIwG,EAAOvD,EAAWsD,EAAqB,GAAKD,CAAS,CAAC,KAGpEtG,GAAU,OAEd2B,EAAK,KAAKlI,CAAK,EACf6M,EAAY,GACZC,EAAqB,GACrB,QACJ,CACJ,CACA,OAAOvG,CACX,CACA,SAASwG,EAAOvD,EAAWqD,EAAW,CAClC,MAAMG,EAAS,CAACxD,EAAWqD,CAAS,EAAE,OAAO,OAAO,EAEpD,OADiBG,EAAO,MAAOpG,GAAUA,EAAM,SAAW,CAAC,EAEhD,KAAKgE,EAAOoC,EAAO,KAAK,EAAE,CAAC,CAAC,IAChC,SAASA,EAAO,IAAIpC,CAAM,EAAE,KAAK,GAAG,CAAC,KAChD,C,+ICzXWqC,GAAa,UAAsB,CAC5C,IAAIC,GACJ,OAAI,OAAOC,IAAY,YAAoB,MAClCD,GAAWC,MAAa,MAAQ,KAAa,SAAW,GAAW,2CAAkB,MAAQ,KAAa,OAAS,OAAS,GAAS,eAAiB,GACjK,EACIC,GAAwB,SAA+BpG,GAAMqG,EAAc,CAC7E,IAAI1N,KAAQ,KAAgBsN,GAAW,EAAG,QAAQ,EAAI,GAAK,CACzD,KAAMjG,GACN,aAAcqG,CAChB,EAAI,CACF,QAASrG,GACT,gBAAiBqG,CACnB,EACA,SAAO,KAAc1N,CAAK,CAC5B,C,iLCNI2N,EAA6B,SAAUC,EAAkB,IAC3D,MAAUD,GAAeC,CAAgB,EACzC,IAAIC,MAAS,MAAaF,EAAa,EACvC,SAASA,IAAgB,CACvB,IAAIG,MACJ,KAAgB,KAAMH,EAAa,EACnC,QAASI,EAAO,UAAU,OAAQC,EAAO,IAAI,MAAMD,CAAI,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAC/ED,EAAKC,CAAI,EAAI,UAAUA,CAAI,EAE7B,UAAQJ,GAAO,KAAK,MAAMA,GAAQ,CAAC,IAAI,EAAE,OAAOG,CAAI,CAAC,KACrD,SAAgB,KAAuBF,EAAK,EAAG,QAAS,CACtD,SAAU,GACV,UAAW,EACb,CAAC,EACMA,EACT,CACA,cAAaH,GAAe,CAAC,CAC3B,IAAK,oBACL,MAAO,SAA2BhM,EAAOuM,EAAW,CAGlD,QAAQ,IAAIvM,EAAOuM,CAAS,CAC9B,CACF,EAAG,CACD,IAAK,SACL,MAAO,UAAkB,CACvB,OAAI,KAAK,MAAM,YAEO,OAAK,MAAQ,CAC/B,OAAQ,QACR,MAAO,wBACP,MAAO,KAAK,MAAM,SACpB,CAAC,EAEI,KAAK,MAAM,QACpB,CACF,CAAC,EAAG,CAAC,CACH,IAAK,2BACL,MAAO,SAAkCvM,EAAO,CAC9C,MAAO,CACL,SAAU,GACV,UAAWA,EAAM,OACnB,CACF,CACF,CAAC,CAAC,EACKgM,EACT,EAAE,YAAe,C,wHChDV,SAASQ,GAAcpC,GAAIqC,GAAM,CACtC,IAAIC,MAAW,MAAetC,EAAE,EAC5BuC,KAAQ,UAAO,EACfC,KAAS,eAAY,UAAY,CAC/BD,EAAM,UACR,aAAaA,EAAM,OAAO,EAC1BA,EAAM,QAAU,KAEpB,EAAG,CAAC,CAAC,EACDE,KAAM,kBAA0B,QAAgC,KAAoB,EAAE,KAAK,SAASC,IAAW,CACjH,IAAIV,GACFC,GACAC,EACAS,EAAS,UACX,SAAO,KAAoB,EAAE,KAAK,SAAmBC,EAAW,CAC9D,OAAU,OAAQA,EAAU,KAAOA,EAAU,KAAM,CACjD,IAAK,GACH,IAAKZ,GAAOW,EAAO,OAAQV,GAAO,IAAI,MAAMD,EAAI,EAAGE,EAAO,EAAGA,EAAOF,GAAME,IACxED,GAAKC,CAAI,EAAIS,EAAOT,CAAI,EAE1B,GAAI,EAAEG,KAAS,GAAKA,KAAS,QAAY,CACvCO,EAAU,KAAO,EACjB,KACF,CACA,OAAOA,EAAU,OAAO,SAAUN,GAAS,MAAM,OAAQL,EAAI,CAAC,EAChE,IAAK,GACH,SAAO,EACAW,EAAU,OAAO,SAAU,IAAI,QAAQ,SAAUC,EAAS,CAC/DN,EAAM,QAAU,cAAyB,QAAgC,KAAoB,EAAE,KAAK,SAASO,GAAU,CACrH,SAAO,KAAoB,EAAE,KAAK,SAAkBC,EAAU,CAC5D,OAAU,OAAQA,EAAS,KAAOA,EAAS,KAAM,CAC/C,IAAK,GACH,SAAS,GAAKF,EACdE,EAAS,KAAO,EACTT,GAAS,MAAM,OAAQL,EAAI,EACpC,IAAK,GACH,SAAS,GAAKc,EAAS,QACnBA,EAAS,IAAIA,EAAS,EAAE,EACrBA,EAAS,OAAO,QAAQ,EACjC,IAAK,GACL,IAAK,MACH,OAAOA,EAAS,KAAK,CACzB,CACF,EAAGD,CAAO,CACZ,CAAC,CAAC,EAAGT,EAAI,CACX,CAAC,CAAC,EACJ,IAAK,GACL,IAAK,MACH,OAAOO,EAAU,KAAK,CAC1B,CACF,EAAGF,EAAQ,CACb,CAAC,CAAC,EAAG,CAACJ,GAAUE,EAAQH,EAAI,CAAC,EAC7B,sBAAU,UAAY,CACpB,OAAOG,CACT,EAAG,CAACA,CAAM,CAAC,EACJ,CACL,IAAKC,EACL,OAAQD,CACV,CACF,C,gGClEIQ,EAAiB,SAAwBC,GAAY,CACvD,IAAI/O,MAAM,UAAO,IAAI,EACrB,UAAI,QAAU+O,MACP,eAAY,UAAY,CAE7B,QADIC,GACKlB,GAAO,UAAU,OAAQmB,EAAO,IAAI,MAAMnB,EAAI,EAAGE,EAAO,EAAGA,EAAOF,GAAME,IAC/EiB,EAAKjB,CAAI,EAAI,UAAUA,CAAI,EAE7B,OAAQgB,GAAehP,GAAI,WAAa,MAAQgP,KAAiB,OAAS,OAASA,GAAa,KAAK,MAAMA,GAAc,CAAChP,EAAG,EAAE,UAAO,KAAmBiP,CAAI,CAAC,CAAC,CACjK,EAAG,CAAC,CAAC,CACP,C,qFCZIC,EAAS,OAAO3B,GAAY,aAAeA,EAAQ,UAAY,MAAQA,EAAQ,SAAS,MAAQ,KAUzF4B,EAAY,UAAqB,CAI1C,OAAO,OAAO,QAAW,aAAe,OAAO,OAAO,UAAa,aAAe,OAAO,OAAO,YAAe,aAAe,CAACD,CACjI,C,sECfO,IAAIE,EAAgB,SAAuB3J,EAAK,CACrD,IAAI4J,GAAS,CAAC,EAMd,GALA,OAAO,KAAK5J,GAAO,CAAC,CAAC,EAAE,QAAQ,SAAUnB,GAAK,CACxCmB,EAAInB,EAAG,IAAM,SACf+K,GAAO/K,EAAG,EAAImB,EAAInB,EAAG,EAEzB,CAAC,EACG,SAAO,KAAK+K,EAAM,EAAE,OAAS,GAGjC,OAAOA,EACT,C,6RCTe,SAASC,EAAcC,EAAY,CAChD,IAAIC,EAAQ,OAAO,QAAW,YAC1BC,KAAY,YAAS,UAAY,CACjC,OAAOD,EAAQ,GAAQ,OAAO,WAAWD,CAAU,EAAE,OACvD,CAAC,EACDG,KAAa,KAAeD,EAAW,CAAC,EACxCzH,EAAU0H,EAAW,CAAC,EACtBC,EAAaD,EAAW,CAAC,EAC3B,4BAAgB,UAAY,CAC1B,GAAI,GAGJ,KAAIE,EAAiB,OAAO,WAAWL,CAAU,EAC7CM,EAAW,SAAkBC,EAAG,CAClC,OAAOH,EAAWG,EAAE,OAAO,CAC7B,EACA,SAAe,YAAYD,CAAQ,EAC5B,UAAY,CACjB,OAAOD,EAAe,eAAeC,CAAQ,CAC/C,EACF,EAAG,CAACN,CAAU,CAAC,EACRvH,CACT,CCrBO,IAAI+H,EAAiB,CAC1B,GAAI,CACF,SAAU,IACV,WAAY,oBACd,EACA,GAAI,CACF,SAAU,IACV,SAAU,IACV,WAAY,2CACd,EACA,GAAI,CACF,SAAU,IACV,SAAU,IACV,WAAY,2CACd,EACA,GAAI,CACF,SAAU,IACV,SAAU,KACV,WAAY,4CACd,EACA,GAAI,CACF,SAAU,KACV,SAAU,KACV,WAAY,6CACd,EACA,IAAK,CACH,SAAU,KACV,WAAY,qBACd,CACF,EAOWC,EAAqB,UAA8B,CAC5D,IAAIC,EAAW,OAEf,GAAI,OAAO,QAAW,YACpB,OAAOA,EAET,IAAIC,EAAgB,OAAO,KAAKH,CAAc,EAAE,KAAK,SAAUzL,EAAK,CAClE,IAAI6L,EAAaJ,EAAezL,CAAG,EAAE,WACrC,MAAI,SAAO,WAAW6L,CAAU,EAAE,OAIpC,CAAC,EACD,SAAWD,EACJD,CACT,EACIG,EAAgB,UAAyB,CAC3C,IAAIC,EAAOf,EAAcS,EAAe,GAAG,UAAU,EACjDO,EAAOhB,EAAcS,EAAe,GAAG,UAAU,EACjDQ,EAAQjB,EAAcS,EAAe,IAAI,UAAU,EACnDS,EAAOlB,EAAcS,EAAe,GAAG,UAAU,EACjDU,EAAOnB,EAAcS,EAAe,GAAG,UAAU,EACjDW,EAAOpB,EAAcS,EAAe,GAAG,UAAU,EACjDN,KAAY,YAASO,EAAmB,CAAC,EAC3CN,KAAa,KAAeD,EAAW,CAAC,EACxCkB,EAAUjB,EAAW,CAAC,EACtBkB,EAAalB,EAAW,CAAC,EAC3B,sBAAU,UAAY,CAKpB,GAAIa,EAAO,CACTK,EAAW,KAAK,EAChB,MACF,CACA,GAAIJ,EAAM,CACRI,EAAW,IAAI,EACf,MACF,CACA,GAAIN,EAAM,CACRM,EAAW,IAAI,EACf,MACF,CACA,GAAIP,EAAM,CACRO,EAAW,IAAI,EACf,MACF,CACA,GAAIH,EAAM,CACRG,EAAW,IAAI,EACf,MACF,CACA,GAAIF,EAAM,CACRE,EAAW,IAAI,EACf,MACF,CACAA,EAAW,IAAI,CACjB,EAAG,CAACP,EAAMC,EAAMC,EAAOC,EAAMC,EAAMC,CAAI,CAAC,EACjCC,CACT,E,WChGO,SAASE,EAAiBC,EAAWC,EAAiB,CAC3D,IAAIC,EAAY,OAAOF,EAAU,UAAa,SAAWA,EAAU,MAAQC,KAC3E,aAAU,UAAY,IAChB5B,KAAU,GAAK6B,IACjB,SAAS,MAAQA,EAErB,EAAG,CAACF,EAAU,MAAOE,CAAS,CAAC,CACjC,C,yBCNO,SAASC,EAAgB7Q,EAAO,CACrC,MAAI8Q,QAAgB,KAAW,EAAG,OAAO,EAAI,EAAG,OAAO9Q,EACvD,IAAI+Q,EAAmB,CACrB,gBAAiB,kBACjB,WAAY,mBACZ,kBAAmB,0BACnB,cAAe,YACf,mBAAoB,iBACpB,6BAA8B,2BAC9B,sBAAuB,oBACvB,gCAAiC,8BACjC,sBAAuB,oBACvB,oBAAqB,kBACrB,yBAA0B,uBAC1B,4BAA6B,0BAC7B,wBAAyB,qBACzB,0BAA2B,uBAC3B,YAAa,SACb,iBAAkB,cAClB,eAAgB,gBAChB,kBAAmB,eACnB,oBAAqB,iBACrB,8BAA+B,2BAC/B,oBAAqB,iBACrB,qBAAsB,kBACtB,yBAA0B,sBAC5B,EACIC,KAAW,KAAc,CAAC,EAAGhR,CAAK,EACtC,cAAO,KAAK+Q,CAAgB,EAAE,QAAQ,SAAU7M,EAAK,CAC/C8M,EAAS9M,CAAG,IAAM,SAEpB8M,EAASD,EAAiB7M,CAAG,CAAC,EAAI8M,EAAS9M,CAAG,EAC9C,OAAO8M,EAAS9M,CAAG,EAEvB,CAAC,EACM8M,CACT,C,eC6BA,SAASC,EAAYC,EAAGzJ,EAAG,CACzB,OAAOA,IAAMyJ,EAAIzJ,GAAK,GAAKyJ,CAC7B,CAEA,SAASC,GAAO1J,EAAG2J,EAAGC,EAAG,CACvB,OAAO5J,EAAI2J,EAAI,CAAC3J,EAAI4J,CACtB,CAEA,SAASC,EAAS7J,EAAG2J,EAAGC,EAAG,CACzB,OAAO5J,EAAI2J,EAAI3J,EAAI4J,EAAID,EAAIC,CAC7B,CAEA,SAASE,GAAc9J,EAAG,CACxB,OAAOwJ,EAAY,EAAGxJ,CAAC,EAAIwJ,EAAY,GAAIxJ,CAAC,EAAIwJ,EAAY,GAAIxJ,CAAC,CACnE,CAEA,SAAS+J,GAAc/J,EAAG,CACxB,OAAOwJ,EAAY,EAAGxJ,CAAC,EAAIwJ,EAAY,GAAIxJ,CAAC,EAAIwJ,EAAY,GAAIxJ,CAAC,CACnE,CAEA,SAASgK,GAAchK,EAAG,CACxB,OAAOwJ,EAAY,EAAGxJ,CAAC,EAAIwJ,EAAY,GAAIxJ,CAAC,EAAIA,IAAM,CACxD,CAEA,SAASiK,GAAcjK,EAAG,CACxB,OAAOwJ,EAAY,GAAIxJ,CAAC,EAAIwJ,EAAY,GAAIxJ,CAAC,EAAIA,IAAM,EACzD,CAEA,SAASkK,GAAcC,EAAG/L,EAAG,CAC3B,OAAO+L,EAAE/L,EAAI,EAAI,GAAK6L,GAAcE,EAAE/L,EAAI,GAAK,EAAI,CAAC,EAAI+L,EAAE/L,EAAI,EAAI,EAAI,EAAI4L,GAAcG,EAAE/L,EAAI,EAAI,EAAI,CAAC,CACzG,CAIA,IAAIgM,GAAO,CAAC,WAAY,WAAY,WAAY,WAAY,UAAY,WAAY,WAAY,WAAY,WAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAY,UAAY,UAAY,UAAY,UAAY,UAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,WAAY,UAAU,EAGtwBC,EACA/L,GACAgM,GACAC,GAAoB,mBAIxB,SAASC,GAASxK,EAAG2J,EAAG,CACtB,IAAIc,GAAOzK,EAAI,QAAW2J,EAAI,OAC1Be,GAAO1K,GAAK,KAAO2J,GAAK,KAAOc,GAAO,IAC1C,OAAOC,GAAO,GAAKD,EAAM,KAC3B,CAIA,SAASE,IAAc,CACrBN,EAAQ,IAAI,MAAM,CAAC,EACnB/L,GAAQ,IAAI,MAAM,CAAC,EACnBgM,GAAS,IAAI,MAAM,EAAE,EACrBhM,GAAM,CAAC,EAAIA,GAAM,CAAC,EAAI,EACtB+L,EAAM,CAAC,EAAI,WACXA,EAAM,CAAC,EAAI,WACXA,EAAM,CAAC,EAAI,WACXA,EAAM,CAAC,EAAI,WACXA,EAAM,CAAC,EAAI,WACXA,EAAM,CAAC,EAAI,WACXA,EAAM,CAAC,EAAI,UACXA,EAAM,CAAC,EAAI,UACb,CAIA,SAASO,IAAmB,CAC1B,IAAI,EACAC,EACAC,EACAC,EACA9C,EACAvF,EACAzG,EACA+O,EACAC,EACAC,EACAf,EAAI,IAAI,MAAM,EAAE,EAGpB,EAAIE,EAAM,CAAC,EACXQ,EAAIR,EAAM,CAAC,EACXS,EAAIT,EAAM,CAAC,EACXU,EAAIV,EAAM,CAAC,EACXpC,EAAIoC,EAAM,CAAC,EACX3H,EAAI2H,EAAM,CAAC,EACXpO,EAAIoO,EAAM,CAAC,EACXW,EAAIX,EAAM,CAAC,EAGX,QAASpM,EAAI,EAAGA,EAAI,GAAIA,IACtBkM,EAAElM,CAAC,EAAIqM,IAAQrM,GAAK,GAAK,CAAC,EAAIqM,IAAQrM,GAAK,GAAK,CAAC,GAAK,EAAIqM,IAAQrM,GAAK,GAAK,CAAC,GAAK,GAAKqM,GAAOrM,GAAK,CAAC,GAAK,GAG3G,QAASG,EAAI,EAAGA,EAAI,GAAIA,IACtB6M,EAAKD,EAAIjB,GAAc9B,CAAC,EAAIyB,GAAOzB,EAAGvF,EAAGzG,CAAC,EAAImO,GAAKhM,CAAC,EAChDA,EAAI,GAAI6M,GAAMd,EAAE/L,CAAC,EAAO6M,GAAMf,GAAcC,EAAG/L,CAAC,EACpD8M,EAAKpB,GAAc,CAAC,EAAID,EAAS,EAAGgB,EAAGC,CAAC,EACxCE,EAAI/O,EACJA,EAAIyG,EACJA,EAAIuF,EACJA,EAAIuC,GAASO,EAAGE,CAAE,EAClBF,EAAID,EACJA,EAAID,EACJA,EAAI,EACJ,EAAIL,GAASS,EAAIC,CAAE,EAKrBb,EAAM,CAAC,GAAK,EACZA,EAAM,CAAC,GAAKQ,EACZR,EAAM,CAAC,GAAKS,EACZT,EAAM,CAAC,GAAKU,EACZV,EAAM,CAAC,GAAKpC,EACZoC,EAAM,CAAC,GAAK3H,EACZ2H,EAAM,CAAC,GAAKpO,EACZoO,EAAM,CAAC,GAAKW,CACd,CAIA,SAASG,GAAc/K,EAAMgL,EAAU,CACrC,IAAInN,EACAlB,EACAsO,EAAS,EAGbtO,EAAQuB,GAAM,CAAC,GAAK,EAAI,GACxB,IAAIgN,EAAYF,EAAW,GAO3B,KAJK9M,GAAM,CAAC,GAAK8M,GAAY,GAAKA,GAAY,GAAG9M,GAAM,CAAC,IACxDA,GAAM,CAAC,GAAK8M,GAAY,GAGnBnN,EAAI,EAAGA,EAAI,GAAKmN,EAAUnN,GAAK,GAAI,CACtC,QAASG,EAAIrB,EAAOqB,EAAI,GAAIA,IAC1BkM,GAAOlM,CAAC,EAAIgC,EAAK,WAAWiL,GAAQ,EAGtCT,GAAiB,EACjB7N,EAAQ,CACV,CAIA,QAASwO,EAAK,EAAGA,EAAKD,EAAWC,IAC/BjB,GAAOiB,CAAE,EAAInL,EAAK,WAAWiL,GAAQ,CAEzC,CAIA,SAASG,IAAe,CACtB,IAAIzO,EAAQuB,GAAM,CAAC,GAAK,EAAI,GAG5B,GAFAgM,GAAOvN,GAAO,EAAI,IAEdA,GAAS,GACX,QAASkB,EAAIlB,EAAOkB,EAAI,GAAIA,IAC1BqM,GAAOrM,CAAC,EAAI,MAET,CACL,QAASgE,EAAKlF,EAAOkF,EAAK,GAAIA,IAC5BqI,GAAOrI,CAAE,EAAI,EAGf2I,GAAiB,EAEjB,QAASa,EAAM,EAAGA,EAAM,GAAIA,IAC1BnB,GAAOmB,CAAG,EAAI,CAElB,CAEAnB,GAAO,EAAE,EAAIhM,GAAM,CAAC,IAAM,GAAK,IAC/BgM,GAAO,EAAE,EAAIhM,GAAM,CAAC,IAAM,GAAK,IAC/BgM,GAAO,EAAE,EAAIhM,GAAM,CAAC,IAAM,EAAI,IAC9BgM,GAAO,EAAE,EAAIhM,GAAM,CAAC,EAAI,IACxBgM,GAAO,EAAE,EAAIhM,GAAM,CAAC,IAAM,GAAK,IAC/BgM,GAAO,EAAE,EAAIhM,GAAM,CAAC,IAAM,GAAK,IAC/BgM,GAAO,EAAE,EAAIhM,GAAM,CAAC,IAAM,EAAI,IAC9BgM,GAAO,EAAE,EAAIhM,GAAM,CAAC,EAAI,IACxBsM,GAAiB,CACnB,CAIA,SAASc,IAAsB,CAI7B,QAHItN,EAAI,EACJuN,EAAS,IAAI,MAAM,EAAE,EAEhB1N,EAAI,EAAGA,EAAI,EAAGA,IACrB0N,EAAOvN,GAAG,EAAIiM,EAAMpM,CAAC,IAAM,GAAK,IAChC0N,EAAOvN,GAAG,EAAIiM,EAAMpM,CAAC,IAAM,GAAK,IAChC0N,EAAOvN,GAAG,EAAIiM,EAAMpM,CAAC,IAAM,EAAI,IAC/B0N,EAAOvN,GAAG,EAAIiM,EAAMpM,CAAC,EAAI,IAG3B,OAAO0N,CACT,CAIA,SAASC,IAAoB,CAG3B,QAFID,EAAS,IAAI,OAER1N,EAAI,EAAGA,EAAI,EAAGA,IACrB,QAASG,EAAI,GAAIA,GAAK,EAAGA,GAAK,EAC5BuN,GAAUpB,GAAkB,OAAOF,EAAMpM,CAAC,IAAMG,EAAI,EAAI,EAI5D,OAAOuN,CACT,CAKA,SAASE,GAAOzL,EAAM,CACpB,UAAY,EACZ+K,GAAc/K,EAAMA,EAAK,MAAM,EAC/BoL,GAAa,EACNI,GAAkB,CAC3B,CAEA,OAAeC,GCxSf,SAASlO,GAAQC,EAAK,CAAE,0BAA2B,OAAOD,GAAwB,OAAO,QAArB,YAA2C,OAAO,OAAO,UAA1B,SAAqC,SAAUC,EAAK,CAAE,OAAO,OAAOA,CAAK,EAAI,SAAUA,EAAK,CAAE,OAAOA,GAAqB,OAAO,QAArB,YAA+BA,EAAI,cAAgB,QAAUA,IAAQ,OAAO,UAAY,SAAW,OAAOA,CAAK,EAAGD,GAAQC,CAAG,CAAG,CAE/U,IAAIpE,GAAY,CAAC,wBAAyB,WAAY,OAAQ,WAAY,aAAc,QAAQ,EAEhG,SAASsS,GAAeC,EAAK9N,EAAG,CAAE,OAAO+N,GAAgBD,CAAG,GAAKE,GAAsBF,EAAK9N,CAAC,GAAKiO,GAA4BH,EAAK9N,CAAC,GAAKkO,GAAiB,CAAG,CAE7J,SAASA,IAAmB,CAAE,MAAM,IAAI,UAAU;AAAA,mFAA2I,CAAG,CAEhM,SAASF,GAAsBF,EAAK9N,EAAG,CAAE,IAAIgE,EAAK8J,GAAO,KAAO,KAAO,OAAO,QAAW,aAAeA,EAAI,OAAO,QAAQ,GAAKA,EAAI,YAAY,EAAG,GAAI9J,GAAM,KAAc,KAAImK,EAAO,CAAC,EAAOC,EAAK,GAAUxK,EAAK,GAAWyK,EAAIC,EAAI,GAAI,CAAE,IAAKtK,EAAKA,EAAG,KAAK8J,CAAG,EAAG,EAAEM,GAAMC,EAAKrK,EAAG,KAAK,GAAG,QAAoBmK,EAAK,KAAKE,EAAG,KAAK,EAAO,KAAKF,EAAK,SAAWnO,IAA3DoO,EAAK,GAA6B,CAAqC,OAASG,EAAK,CAAE3K,EAAK,GAAM0K,EAAKC,CAAK,QAAE,CAAU,GAAI,CAAM,CAACH,GAAMpK,EAAG,QAAa,MAAMA,EAAG,OAAU,CAAG,QAAE,CAAU,GAAIJ,EAAI,MAAM0K,CAAI,CAAE,CAAE,OAAOH,EAAM,CAEhgB,SAASJ,GAAgBD,EAAK,CAAE,GAAI,MAAM,QAAQA,CAAG,EAAG,OAAOA,CAAK,CAEpE,SAASU,GAA2BC,EAAGC,EAAgB,CAAE,IAAIjJ,EAAK,OAAO,QAAW,aAAegJ,EAAE,OAAO,QAAQ,GAAKA,EAAE,YAAY,EAAG,GAAI,CAAChJ,EAAI,CAAE,GAAI,MAAM,QAAQgJ,CAAC,IAAMhJ,EAAKwI,GAA4BQ,CAAC,IAAMC,GAAkBD,GAAK,OAAOA,EAAE,QAAW,SAAU,CAAMhJ,IAAIgJ,EAAIhJ,GAAI,IAAIzF,EAAI,EAAO2O,EAAI,UAAa,CAAC,EAAG,MAAO,CAAE,EAAGA,EAAG,EAAG,UAAa,CAAE,OAAI3O,GAAKyO,EAAE,OAAe,CAAE,KAAM,EAAK,EAAU,CAAE,KAAM,GAAO,MAAOA,EAAEzO,GAAG,CAAE,CAAG,EAAG,EAAG,SAAW4O,EAAK,CAAE,MAAMA,CAAK,EAAG,EAAGD,CAAE,CAAG,CAAE,MAAM,IAAI,UAAU;AAAA,mFAAuI,CAAG,CAAE,IAAIE,EAAmB,GAAMC,EAAS,GAAOP,EAAK,MAAO,CAAE,EAAG,UAAa,CAAE9I,EAAKA,EAAG,KAAKgJ,CAAC,CAAG,EAAG,EAAG,UAAa,CAAE,IAAIM,EAAOtJ,EAAG,KAAK,EAAG,SAAmBsJ,EAAK,KAAaA,CAAM,EAAG,EAAG,SAAWC,EAAK,CAAEF,EAAS,GAAMP,EAAMS,CAAK,EAAG,EAAG,UAAa,CAAE,GAAI,CAAM,CAACH,GAAoBpJ,EAAG,QAAU,MAAMA,EAAG,OAAO,CAAG,QAAE,CAAU,GAAIqJ,EAAQ,MAAMP,CAAK,CAAE,CAAE,CAAG,CAEv+B,SAASU,GAAgBC,EAAUC,EAAa,CAAE,GAAI,EAAED,aAAoBC,GAAgB,MAAM,IAAI,UAAU,mCAAmC,CAAK,CAExJ,SAASC,GAAkBzS,EAAQ1C,EAAO,CAAE,QAAS+F,EAAI,EAAGA,EAAI/F,EAAM,OAAQ+F,IAAK,CAAE,IAAIqP,EAAapV,EAAM+F,CAAC,EAAGqP,EAAW,WAAaA,EAAW,YAAc,GAAOA,EAAW,aAAe,GAAU,UAAWA,IAAYA,EAAW,SAAW,IAAM,OAAO,eAAe1S,EAAQ0S,EAAW,IAAKA,CAAU,CAAG,CAAE,CAE5T,SAASC,GAAaH,EAAaI,EAAYC,EAAa,CAAE,OAAID,GAAYH,GAAkBD,EAAY,UAAWI,CAAU,EAAOC,GAAaJ,GAAkBD,EAAaK,CAAW,EAAG,OAAO,eAAeL,EAAa,YAAa,CAAE,SAAU,EAAM,CAAC,EAAUA,CAAa,CAE5R,SAASM,GAAUC,EAAUC,EAAY,CAAE,GAAI,OAAOA,GAAe,YAAcA,IAAe,KAAQ,MAAM,IAAI,UAAU,oDAAoD,EAAKD,EAAS,UAAY,OAAO,OAAOC,GAAcA,EAAW,UAAW,CAAE,YAAa,CAAE,MAAOD,EAAU,SAAU,GAAM,aAAc,EAAK,CAAE,CAAC,EAAG,OAAO,eAAeA,EAAU,YAAa,CAAE,SAAU,EAAM,CAAC,EAAOC,GAAYC,GAAgBF,EAAUC,CAAU,CAAG,CAEnc,SAASE,GAAaC,EAAS,CAAE,IAAIC,EAA4BC,GAA0B,EAAG,OAAO,UAAgC,CAAE,IAAIC,EAAQC,GAAgBJ,CAAO,EAAGjP,EAAQ,GAAIkP,EAA2B,CAAE,IAAII,EAAYD,GAAgB,IAAI,EAAE,YAAarP,EAAS,QAAQ,UAAUoP,EAAO,UAAWE,CAAS,CAAG,MAAStP,EAASoP,EAAM,MAAM,KAAM,SAAS,EAAK,OAAOG,GAA2B,KAAMvP,CAAM,CAAG,CAAG,CAExa,SAASuP,GAA2BC,EAAMC,EAAM,CAAE,GAAIA,IAAS5Q,GAAQ4Q,CAAI,IAAM,UAAY,OAAOA,GAAS,YAAe,OAAOA,EAAa,GAAIA,IAAS,OAAU,MAAM,IAAI,UAAU,0DAA0D,EAAK,OAAOC,GAAuBF,CAAI,CAAG,CAE/R,SAASE,GAAuBF,EAAM,CAAE,GAAIA,IAAS,OAAU,MAAM,IAAI,eAAe,2DAA2D,EAAK,OAAOA,CAAM,CAErK,SAASG,GAAiBC,EAAO,CAAE,IAAIC,EAAS,OAAO,KAAQ,WAAa,IAAI,IAAQ,OAAW,UAAmB,SAA0BD,EAAO,CAAE,GAAIA,IAAU,MAAQ,CAACE,GAAkBF,CAAK,EAAG,OAAOA,EAAO,GAAI,OAAOA,GAAU,WAAc,MAAM,IAAI,UAAU,oDAAoD,EAAK,GAAI,OAAOC,GAAW,YAAa,CAAE,GAAIA,EAAO,IAAID,CAAK,EAAG,OAAOC,EAAO,IAAID,CAAK,EAAGC,EAAO,IAAID,EAAOG,CAAO,CAAG,CAAE,SAASA,GAAU,CAAE,OAAOC,GAAWJ,EAAO,UAAWP,GAAgB,IAAI,EAAE,WAAW,CAAG,CAAE,SAAQ,UAAY,OAAO,OAAOO,EAAM,UAAW,CAAE,YAAa,CAAE,MAAOG,EAAS,WAAY,GAAO,SAAU,GAAM,aAAc,EAAK,CAAE,CAAC,EAAUhB,GAAgBgB,EAASH,CAAK,CAAG,EAAUD,GAAiBC,CAAK,CAAG,CAEtvB,SAASI,GAAWC,EAAQ7I,EAAMwI,EAAO,CAAE,OAAIT,GAA0B,EAAKa,GAAa,QAAQ,UAAU,KAAK,EAAYA,GAAa,SAAoBC,EAAQ7I,EAAMwI,EAAO,CAAE,IAAIM,EAAI,CAAC,IAAI,EAAGA,EAAE,KAAK,MAAMA,EAAG9I,CAAI,EAAG,IAAIkH,EAAc,SAAS,KAAK,MAAM2B,EAAQC,CAAC,EAAO7B,EAAW,IAAIC,EAAe,OAAIsB,GAAOb,GAAgBV,EAAUuB,EAAM,SAAS,EAAUvB,CAAU,EAAY2B,GAAW,MAAM,KAAM,SAAS,CAAG,CAExa,SAASb,IAA4B,CAA0E,GAApE,OAAO,SAAY,aAAe,CAAC,QAAQ,WAA6B,QAAQ,UAAU,KAAM,MAAO,GAAO,GAAI,OAAO,OAAU,WAAY,MAAO,GAAM,GAAI,CAAE,eAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,QAAS,CAAC,EAAG,UAAY,CAAC,CAAC,CAAC,EAAU,EAAM,OAAShG,EAAG,CAAE,MAAO,EAAO,CAAE,CAExU,SAAS2G,GAAkB3K,EAAI,CAAE,OAAO,SAAS,SAAS,KAAKA,CAAE,EAAE,QAAQ,eAAe,IAAM,EAAI,CAEpG,SAAS4J,GAAgBnB,EAAGuC,EAAG,CAAE,UAAkB,OAAO,eAAiB,OAAO,eAAe,KAAK,EAAI,SAAyBvC,EAAGuC,EAAG,CAAE,SAAE,UAAYA,EAAUvC,CAAG,EAAUmB,GAAgBnB,EAAGuC,CAAC,CAAG,CAEvM,SAASd,GAAgBzB,EAAG,CAAE,UAAkB,OAAO,eAAiB,OAAO,eAAe,KAAK,EAAI,SAAyBA,EAAG,CAAE,OAAOA,EAAE,WAAa,OAAO,eAAeA,CAAC,CAAG,EAAUyB,GAAgBzB,CAAC,CAAG,CAEnN,SAASwC,GAAmBnD,EAAK,CAAE,OAAOoD,GAAmBpD,CAAG,GAAKqD,GAAiBrD,CAAG,GAAKG,GAA4BH,CAAG,GAAKsD,GAAmB,CAAG,CAExJ,SAASA,IAAqB,CAAE,MAAM,IAAI,UAAU;AAAA,mFAAsI,CAAG,CAE7L,SAASnD,GAA4BQ,EAAG4C,EAAQ,CAAE,GAAK5C,EAAW,IAAI,OAAOA,GAAM,SAAU,OAAO6C,GAAkB7C,EAAG4C,CAAM,EAAG,IAAI7F,EAAI,OAAO,UAAU,SAAS,KAAKiD,CAAC,EAAE,MAAM,EAAG,EAAE,EAAgE,GAAzDjD,IAAM,UAAYiD,EAAE,cAAajD,EAAIiD,EAAE,YAAY,MAAUjD,IAAM,OAASA,IAAM,MAAO,OAAO,MAAM,KAAKiD,CAAC,EAAG,GAAIjD,IAAM,aAAe,2CAA2C,KAAKA,CAAC,EAAG,OAAO8F,GAAkB7C,EAAG4C,CAAM,EAAG,CAE/Z,SAASF,GAAiBI,EAAM,CAAE,GAAI,OAAO,QAAW,aAAeA,EAAK,OAAO,QAAQ,GAAK,MAAQA,EAAK,YAAY,GAAK,KAAM,OAAO,MAAM,KAAKA,CAAI,CAAG,CAE7J,SAASL,GAAmBpD,EAAK,CAAE,GAAI,MAAM,QAAQA,CAAG,EAAG,OAAOwD,GAAkBxD,CAAG,CAAG,CAE1F,SAASwD,GAAkBxD,EAAK0D,EAAK,EAAMA,GAAO,MAAQA,EAAM1D,EAAI,UAAQ0D,EAAM1D,EAAI,QAAQ,QAAS9N,EAAI,EAAGyR,EAAO,IAAI,MAAMD,CAAG,EAAGxR,EAAIwR,EAAKxR,IAAOyR,EAAKzR,CAAC,EAAI8N,EAAI9N,CAAC,EAAK,OAAOyR,CAAM,CAEtL,SAASC,GAAyBC,EAAQC,EAAU,CAAE,GAAID,GAAU,KAAM,MAAO,CAAC,EAAG,IAAIhV,EAASkV,GAA8BF,EAAQC,CAAQ,EAAOpT,EAAKwB,EAAG,GAAI,OAAO,sBAAuB,CAAE,IAAI8R,EAAmB,OAAO,sBAAsBH,CAAM,EAAG,IAAK3R,EAAI,EAAGA,EAAI8R,EAAiB,OAAQ9R,IAAOxB,EAAMsT,EAAiB9R,CAAC,EAAO,IAAS,QAAQxB,CAAG,GAAK,IAAkB,OAAO,UAAU,qBAAqB,KAAKmT,EAAQnT,CAAG,IAAa7B,EAAO6B,CAAG,EAAImT,EAAOnT,CAAG,EAAK,CAAE,OAAO7B,CAAQ,CAE3e,SAASkV,GAA8BF,EAAQC,EAAU,CAAE,GAAID,GAAU,KAAM,MAAO,CAAC,EAAG,IAAIhV,EAAS,CAAC,EAAOoV,EAAa,OAAO,KAAKJ,CAAM,EAAOnT,EAAK,EAAG,IAAK,EAAI,EAAG,EAAIuT,EAAW,OAAQ,IAAOvT,EAAMuT,EAAW,CAAC,EAAO,IAAS,QAAQvT,CAAG,GAAK,KAAa7B,EAAO6B,CAAG,EAAImT,EAAOnT,CAAG,GAAK,OAAO7B,CAAQ,CAElT,SAASqV,GAAQC,EAAQC,EAAgB,CAAE,IAAI1P,EAAO,OAAO,KAAKyP,CAAM,EAAG,GAAI,OAAO,sBAAuB,CAAE,IAAIE,EAAU,OAAO,sBAAsBF,CAAM,EAAGC,IAAmBC,EAAUA,EAAQ,OAAO,SAAUC,EAAK,CAAE,OAAO,OAAO,yBAAyBH,EAAQG,CAAG,EAAE,UAAY,CAAC,GAAI5P,EAAK,KAAK,MAAMA,EAAM2P,CAAO,CAAG,CAAE,OAAO3P,CAAM,CAEpV,SAAS6P,GAAc1V,EAAQ,CAAE,QAASqD,EAAI,EAAGA,EAAI,UAAU,OAAQA,IAAK,CAAE,IAAI2R,EAAiB,UAAU3R,CAAC,GAAnB,KAAuB,UAAUA,CAAC,EAAI,CAAC,EAAGA,EAAI,EAAIgS,GAAQ,OAAOL,CAAM,EAAG,EAAE,EAAE,QAAQ,SAAUnT,EAAK,CAAE8T,GAAgB3V,EAAQ6B,EAAKmT,EAAOnT,CAAG,CAAC,CAAG,CAAC,EAAI,OAAO,0BAA4B,OAAO,iBAAiB7B,EAAQ,OAAO,0BAA0BgV,CAAM,CAAC,EAAIK,GAAQ,OAAOL,CAAM,CAAC,EAAE,QAAQ,SAAUnT,EAAK,CAAE,OAAO,eAAe7B,EAAQ6B,EAAK,OAAO,yBAAyBmT,EAAQnT,CAAG,CAAC,CAAG,CAAC,CAAG,CAAE,OAAO7B,CAAQ,CAEzf,SAAS2V,GAAgB3S,EAAKnB,EAAK0C,EAAO,CAAE,OAAI1C,KAAOmB,EAAO,OAAO,eAAeA,EAAKnB,EAAK,CAAE,MAAO0C,EAAO,WAAY,GAAM,aAAc,GAAM,SAAU,EAAK,CAAC,EAAYvB,EAAInB,CAAG,EAAI0C,EAAgBvB,CAAK,CAKzM,IAAI4S,GAAoB,SACxB,SAASC,GAAgCC,EAAK,CACnD,OAAOA,EAAI,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,CACvC,CACO,IAAIC,GAAQ,SAAe5R,EAAM,CACtC,GAAI,CAACA,EAAK,WAAW,MAAM,EACzB,MAAO,GAGT,GAAI,CACF,IAAI2R,EAAM,IAAI,IAAI3R,CAAI,EACtB,MAAO,CAAC,CAAC2R,CACX,OAAS7W,EAAO,CACd,MAAO,EACT,CACF,EACW+W,GAAe,SAAsBC,EAAM,CACpD,IAAI9R,EAAO8R,EAAK,KAEhB,GAAI,CAAC9R,GAAQA,IAAS,IAEpB,GAAI,CACF,MAAO,IAAI,OAAO+R,GAAO,KAAK,UAAUD,CAAI,CAAC,CAAC,CAChD,OAAShX,EAAO,CAChB,CAGF,OAAOkF,GAAO0R,GAAgC1R,CAAI,CACpD,EAOIgS,GAAoB,SAA2BF,EAAMG,EAAY,CACnE,IAAI7S,EAAO0S,EAAK,KACZI,EAASJ,EAAK,OAElB,MAAI,WAAYA,GAAQI,IAAW,IAAS,CAAC9S,EACpC,GAGF0S,EAAK,QAAU,GAAG,OAAOG,EAAY,GAAG,EAAE,OAAO7S,CAAI,CAC9D,EAUI+S,GAAY,UAAqB,CACnC,IAAInS,EAAO,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,GAC3EoS,EAAa,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,IAErF,OAAIpS,EAAK,SAAS,IAAI,EACbA,EAAK,QAAQ,KAAM,GAAG,GAG1BA,GAAQoS,GAAY,WAAW,GAAG,GAInCR,GAAM5R,CAAI,EACLA,EAGF,IAAI,OAAOoS,EAAY,GAAG,EAAE,OAAOpS,CAAI,EAAE,QAAQ,QAAS,GAAG,EAAE,QAAQ,QAAS,GAAG,CAC5F,EAGIqS,GAA+B,SAAsCpP,EAAO9J,EAAO,CACrF,IAAImZ,EAAcrP,EAAM,KACpBsP,EAAOD,IAAgB,OAAS,CAAC,EAAIA,EACrCE,EAAavP,EAAM,WACnBwP,EAAcxP,EAAM,KACpBjD,EAAOyS,IAAgB,OAAS,GAAKA,EACrCC,EAAiBzP,EAAM,UAAY,CAAC,EACpC0P,EAAaJ,EAAK,KAClBnT,EAAOuT,IAAe,OAAS1P,EAAM,KAAO0P,EAC5CC,EAAaL,EAAK,KAClB/W,EAAOoX,IAAe,OAAS3P,EAAM,KAAO2P,EAC5CC,EAAqBN,EAAK,aAC1BO,EAAeD,IAAuB,OAAS5P,EAAM,aAAe4P,EACpEE,EAAiBR,EAAK,SACtBS,EAAWD,IAAmB,OAAS9P,EAAM,SAAW8P,EAGxDE,EAAeT,GACnB,OAAO,KAAKA,CAAU,EAAE,KAAK,GAAG,IAAM,WAAa,CAACjB,GAAc,CAChE,KAAMvR,EACN,KAAMuS,CACR,EAAGC,CAAU,CAAC,EAAE,OAAOE,GAAkB,CAAC,CAAC,EAAIA,EAE3C3S,EAASwR,GAAc,CAAC,EAAGtO,CAAK,EAUpC,GARI7D,IACFW,EAAO,KAAOX,GAGZ5D,IACFuE,EAAO,KAAOvE,GAGZyX,GAAgBA,EAAa,OAAQ,CAEvC,GAAIH,EACF,cAAO/S,EAAO,SACPA,EAIT,IAAImT,EAAgBC,GAAU5B,GAAcA,GAAc,CAAC,EAAGpY,CAAK,EAAG,CAAC,EAAG,CACxE,KAAM8Z,CACR,CAAC,EAAGhQ,CAAK,EAGT,GAAI+P,EACF,OAAOE,EAGT,OAAOnT,EAAO0R,EAAiB,CACjC,CAEA,OAAO1R,CACT,EAEIqT,GAAe,SAAsBhT,EAAO,CAC9C,OAAO,MAAM,QAAQA,CAAK,GAAKA,EAAM,OAAS,CAChD,EAQA,SAAS+S,GAAUha,EAAO,CACxB,IAAIka,EAAS,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAC/E,KAAM,GACR,EACIhS,EAAOlI,EAAM,KACbma,EAAgBna,EAAM,cACtB8Y,EAAa9Y,EAAM,WACnBoa,EAAapa,EAAM,OAEvB,MAAI,CAACkI,GAAQ,CAAC,MAAM,QAAQA,CAAI,EACvB,CAAC,EAGHA,EAAK,OAAO,SAAUyQ,EAAM,CACjC,OAAKA,EACDsB,GAAatB,EAAK,QAAQ,GAC1BA,EAAK,MACLA,EAAK,YACLA,EAAK,OAAe,IAEpBA,EAAK,UACLA,EAAK,aAAqB,IAPZ,EASpB,CAAC,EAAE,OAAO,SAAUA,EAAM,CACxB,IAAI0B,EAAYC,EAEhB,MAAK,KAAS,OAAoCD,EAAa1B,EAAK,QAAU,MAAQ0B,IAAe,SAAkBA,EAAW,MAAU1B,GAAS,MAAmCA,EAAK,UAAc,KAAS,OAAoC2B,EAAc3B,EAAK,QAAU,MAAQ2B,IAAgB,SAAkBA,EAAY,SAClU,GAKL3B,EAAK,OAAS,EAKpB,CAAC,EAAE,IAAI,SAAU4B,EAAa,CAC5B,IAAI5B,EAAOP,GAAcA,GAAc,CAAC,EAAGmC,CAAW,EAAG,CAAC,EAAG,CAC3D,KAAMA,EAAY,MAAQA,EAAY,UACxC,CAAC,EAED,MAAI,CAAC5B,EAAK,UAAYA,EAAKL,EAAiB,IAC1CK,EAAK,SAAWA,EAAKL,EAAiB,EACtC,OAAOK,EAAKL,EAAiB,GAK3BK,EAAK,cAEP,OAAOA,EAAK,KAGVA,EAAK,OAAS,MAChBA,EAAK,KAAO,KAGVA,EAAK,OAAS,OAChBA,EAAK,KAAO,KAGV,CAACA,EAAK,MAAQA,EAAK,aACrBA,EAAK,KAAOA,EAAK,YAGZA,CACT,CAAC,EAAE,IAAI,UAAY,CACjB,IAAIA,EAAO,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAC7E,KAAM,GACR,EACIY,EAAiBZ,EAAK,UAAYA,EAAKL,EAAiB,GAAK,CAAC,EAC9DzR,EAAOmS,GAAUL,EAAK,KAAMuB,EAASA,EAAO,KAAO,GAAG,EACtDjU,EAAO0S,EAAK,KACZI,EAASF,GAAkBF,EAAMG,GAAc,MAAM,EAGrD0B,EAAazB,IAAW,IAASqB,IAAe,IAASD,GAAiBpB,EAASoB,EAAc,CACnG,GAAIpB,EACJ,eAAgB9S,CAClB,CAAC,EAAIA,EAEDwU,EAAwBP,EAAO,sBAC/BQ,EAAwBD,IAA0B,OAAS,CAAC,EAAIA,EAChEE,EAAWT,EAAO,SAClB7X,EAAO6X,EAAO,KACdL,EAAWK,EAAO,SAClBb,EAAaa,EAAO,WACpBU,EAASV,EAAO,OAChBW,EAAapD,GAAyByC,EAAQ5Y,EAAS,EAEvDwZ,EAA6B,IAAI,IAAI,CAAC,EAAE,OAAO9D,GAAmB0D,CAAqB,EAAG1D,GAAmB2B,EAAK,YAAc,CAAC,CAAC,CAAC,CAAC,EAEpIuB,EAAO,KACTY,EAA2B,IAAIZ,EAAO,GAAG,EAG3C,IAAIK,EAAcnC,GAAcA,GAAcA,GAAc,CAAC,EAAGyC,CAAU,EAAG,CAAC,EAAG,CAC/E,KAAM,MACR,EAAGlC,CAAI,EAAG,CAAC,EAAG,CACZ,KAAM9R,EACN,OAAQkS,EACR,IAAKJ,EAAK,KAAOD,GAAaN,GAAcA,GAAc,CAAC,EAAGO,CAAI,EAAG,CAAC,EAAG,CACvE,KAAM9R,CACR,CAAC,CAAC,EACF,sBAAuB,MAAM,KAAKiU,CAA0B,EAAE,OAAO,SAAUvW,EAAK,CAClF,OAAOA,GAAOA,IAAQ,GACxB,CAAC,CACH,CAAC,EAYD,GAVIiW,EACFD,EAAY,KAAOC,EAEnB,OAAOD,EAAY,KAGjBA,EAAY,OAAS,QACvB,OAAOA,EAAY,KAGjBN,GAAaV,CAAc,EAAG,CAChC,IAAIwB,EAAoBf,GAAU5B,GAAcA,GAAc,CAAC,EAAGpY,CAAK,EAAG,CAAC,EAAG,CAC5E,KAAMuZ,EACN,WAAYR,GAAU,EACxB,CAAC,EAAGwB,CAAW,EAEXN,GAAac,CAAiB,IAChCR,EAAY,SAAWQ,EAE3B,CAEA,OAAO7B,GAA6BqB,EAAava,CAAK,CACxD,CAAC,EAAE,KAAK,CAAC,CACX,CAMA,IAAIgb,GAAwB,SAASA,GAAwB,CAC3D,IAAIC,EAAW,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAC,EACpF,OAAOA,EAAS,OAAO,SAAUtC,EAAM,CACrC,OAAOA,IAASA,EAAK,MAAQsB,GAAatB,EAAK,QAAQ,IAAM,CAACA,EAAK,YAAc,CAACA,EAAK,QACzF,CAAC,EAAE,IAAI,SAAUA,EAAM,CACrB,IAAIuC,EAAU9C,GAAc,CAAC,EAAGO,CAAI,EAEhCY,EAAiB2B,EAAQ,UAAYvC,EAAKL,EAAiB,GAAK,CAAC,EAGrE,GAFA,OAAO4C,EAAQ5C,EAAiB,EAE5B2B,GAAaV,CAAc,GAAK,CAAC2B,EAAQ,oBAAsB3B,EAAe,KAAK,SAAU3U,EAAO,CACtG,OAAOA,GAAS,CAAC,CAACA,EAAM,IAC1B,CAAC,EAAG,CACF,IAAIuW,EAAcH,EAAsBzB,CAAc,EACtD,GAAI4B,EAAY,OAAQ,OAAO/C,GAAcA,GAAc,CAAC,EAAG8C,CAAO,EAAG,CAAC,EAAG,CAC3E,SAAUC,CACZ,CAAC,CACH,CAEA,OAAO/C,GAAc,CAAC,EAAGO,CAAI,CAC/B,CAAC,EAAE,OAAO,SAAUA,EAAM,CACxB,OAAOA,CACT,CAAC,CACH,EAMIyC,GAA4B,SAAUC,EAAM,CAC9C7F,GAAU4F,EAAcC,CAAI,EAE5B,IAAIxN,EAAS+H,GAAawF,CAAY,EAEtC,SAASA,GAAe,CACtB,UAAgB,KAAMA,CAAY,EAE3BvN,EAAO,MAAM,KAAM,SAAS,CACrC,CAEA,UAAauN,EAAc,CAAC,CAC1B,IAAK,MACL,MAAO,SAAaxS,EAAU,CAC5B,IAAI0S,EAEJ,GAAI,CAEF,IAAIC,EAAYhH,GAA2B,KAAK,QAAQ,CAAC,EACrDiH,EAEJ,GAAI,CACF,IAAKD,EAAU,EAAE,EAAG,EAAEC,EAAQD,EAAU,EAAE,GAAG,MAAO,CAClD,IAAIE,EAAc7H,GAAe4H,EAAM,MAAO,CAAC,EAC3CjX,EAAMkX,EAAY,CAAC,EACnBxU,EAAQwU,EAAY,CAAC,EAErB5U,EAAO0R,GAAgChU,CAAG,EAE9C,GAAI,CAACkU,GAAMlU,CAAG,MAAK,MAAasC,EAAM,CAAC,CAAC,EAAE,KAAK+B,CAAQ,EAAG,CACxD0S,EAAarU,EACb,KACF,CACF,CACF,OAASqN,EAAK,CACZiH,EAAU,EAAEjH,CAAG,CACjB,QAAE,CACAiH,EAAU,EAAE,CACd,CACF,OAAS5Z,EAAO,CACd2Z,EAAa,MACf,CAEA,OAAOA,CACT,CACF,CAAC,CAAC,EAEKF,CACT,EAAgB7E,GAAiB,GAAG,CAAC,EAOjCmF,GAAuB,SAA8BT,EAAU,CAEjE,IAAIU,EAAY,IAAIP,GAEhBQ,EAAkB,SAASA,EAAgB1T,EAAMgS,EAAQ,CAC3DhS,EAAK,QAAQ,SAAU2T,EAAU,CAC/B,IAAItC,EAAiBsC,EAAS,UAAYA,EAASvD,EAAiB,GAAK,CAAC,EAEtE2B,GAAaV,CAAc,GAC7BqC,EAAgBrC,EAAgBsC,CAAQ,EAI1C,IAAIhV,EAAOmS,GAAU6C,EAAS,KAAM3B,EAASA,EAAO,KAAO,GAAG,EAC9DyB,EAAU,IAAIpD,GAAgC1R,CAAI,EAAGgV,CAAQ,CAC/D,CAAC,CACH,EAEA,SAAgBZ,CAAQ,EACjBU,CACT,EAEIG,GAAgB,SAASA,GAAgB,CAC3C,IAAIb,EAAW,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAC,EACpF,OAAOA,EAAS,IAAI,SAAUtC,EAAM,CAClC,IAAIY,EAAiBZ,EAAK,UAAYA,EAAKL,EAAiB,EAE5D,GAAI2B,GAAaV,CAAc,EAAG,CAChC,IAAI4B,EAAcW,EAAcvC,CAAc,EAC9C,GAAI4B,EAAY,OAAQ,OAAO/C,GAAc,CAAC,EAAGO,CAAI,CACvD,CAEA,IAAI4B,EAAcnC,GAAc,CAAC,EAAGO,CAAI,EAExC,cAAO4B,EAAYjC,EAAiB,EACpC,OAAOiC,EAAY,SACZA,CACT,CAAC,EAAE,OAAO,SAAU5B,EAAM,CACxB,OAAOA,CACT,CAAC,CACH,EAUIoD,GAAiB,SAAwBC,EAAWjD,EAAQoB,EAAe8B,EAAc,CAC3F,IAAIC,EAAmBlC,GAAU,CAC/B,KAAMgC,EACN,cAAe7B,EACf,OAAQpB,CACV,CAAC,EACGkC,EAAWgB,EAAeH,GAAcI,CAAgB,EAAIlB,GAAsBkB,CAAgB,EAElGC,EAAaT,GAAqBQ,CAAgB,EACtD,MAAO,CACL,WAAYC,EACZ,SAAUlB,CACZ,CACF,EAEA,GAAec,GC7ef,SAAS,GAAQ/D,EAAQC,EAAgB,CAAE,IAAI1P,EAAO,OAAO,KAAKyP,CAAM,EAAG,GAAI,OAAO,sBAAuB,CAAE,IAAIE,EAAU,OAAO,sBAAsBF,CAAM,EAAGC,IAAmBC,EAAUA,EAAQ,OAAO,SAAUC,EAAK,CAAE,OAAO,OAAO,yBAAyBH,EAAQG,CAAG,EAAE,UAAY,CAAC,GAAI5P,EAAK,KAAK,MAAMA,EAAM2P,CAAO,CAAG,CAAE,OAAO3P,CAAM,CAEpV,SAAS,GAAc7F,EAAQ,CAAE,QAASqD,EAAI,EAAGA,EAAI,UAAU,OAAQA,IAAK,CAAE,IAAI2R,EAAiB,UAAU3R,CAAC,GAAnB,KAAuB,UAAUA,CAAC,EAAI,CAAC,EAAGA,EAAI,EAAI,GAAQ,OAAO2R,CAAM,EAAG,EAAE,EAAE,QAAQ,SAAUnT,EAAK,CAAE,GAAgB7B,EAAQ6B,EAAKmT,EAAOnT,CAAG,CAAC,CAAG,CAAC,EAAI,OAAO,0BAA4B,OAAO,iBAAiB7B,EAAQ,OAAO,0BAA0BgV,CAAM,CAAC,EAAI,GAAQ,OAAOA,CAAM,CAAC,EAAE,QAAQ,SAAUnT,EAAK,CAAE,OAAO,eAAe7B,EAAQ6B,EAAK,OAAO,yBAAyBmT,EAAQnT,CAAG,CAAC,CAAG,CAAC,CAAG,CAAE,OAAO7B,CAAQ,CAEzf,SAAS,GAAgBgD,EAAKnB,EAAK0C,EAAO,CAAE,OAAI1C,KAAOmB,EAAO,OAAO,eAAeA,EAAKnB,EAAK,CAAE,MAAO0C,EAAO,WAAY,GAAM,aAAc,GAAM,SAAU,EAAK,CAAC,EAAYvB,EAAInB,CAAG,EAAI0C,EAAgBvB,CAAK,CASzM,IAAI0W,GAAe,SAASA,GAAe,CAChD,IAAInB,EAAW,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAC,EAChFoB,EAAQ,CAAC,EACb,SAAS,QAAQ,SAAUC,EAAS,CAClC,IAAI3D,EAAO,GAAc,CAAC,EAAG2D,CAAO,EAEpC,GAAI,GAAC3D,GAAQ,CAACA,EAAK,KAInB,CAAI,CAACA,EAAK,UAAYA,EAAKL,EAAiB,IAC1CK,EAAK,SAAWA,EAAKL,EAAiB,EACtC,OAAOK,EAAKL,EAAiB,GAG/B,IAAIiB,EAAiBZ,EAAK,UAAY,CAAC,EACvC0D,EAAM9D,GAAgCI,EAAK,MAAQA,EAAK,KAAO,GAAG,CAAC,EAAI,GAAc,CAAC,EAAGA,CAAI,EAC7F0D,EAAM1D,EAAK,KAAOA,EAAK,MAAQ,GAAG,EAAI,GAAc,CAAC,EAAGA,CAAI,EAExDY,IACF8C,EAAQ,GAAc,GAAc,CAAC,EAAGA,CAAK,EAAGD,EAAa7C,CAAc,CAAC,GAEhF,CAAC,EACM8C,CACT,EACA,GAAeD,GClCJG,GAAiB,UAA0B,CACpD,IAAIC,EAAe,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAC,EACpF3V,EAAO,UAAU,OAAS,EAAI,UAAU,CAAC,EAAI,OAC7C4V,EAAQ,UAAU,OAAS,EAAI,UAAU,CAAC,EAAI,OAClD,OAAOD,EAAa,OAAO,SAAU7D,EAAM,CACzC,GAAIA,IAAS,KAAO9R,IAAS,IAC3B,MAAO,GAGT,GAAI8R,IAAS,KAAOA,IAAS,MAAQA,GAAQ,CAACF,GAAME,CAAI,EAAG,CACzD,IAAI+D,EAAUnE,GAAgCI,CAAI,EAElD,GAAI,CAcF,GAZI8D,MACE,MAAa,GAAG,OAAOC,CAAO,CAAC,EAAE,KAAK7V,CAAI,MAM5C,MAAa,GAAG,OAAO6V,CAAO,EAAG,CAAC,CAAC,EAAE,KAAK7V,CAAI,MAK9C,MAAa,GAAG,OAAO6V,EAAS,OAAO,CAAC,EAAE,KAAK7V,CAAI,EACrD,MAAO,EAEX,OAASlF,EAAO,CAChB,CACF,CAEA,MAAO,EACT,CAAC,EAAE,KAAK,SAAUmV,EAAGnE,EAAG,CAEtB,OAAImE,IAAMjQ,EACD,GAGL8L,IAAM9L,EACD,IAGFiQ,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,EAAE,OAASnE,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,EAAE,MAChE,CAAC,CACH,EAQWgK,GAAe,SAAsB/T,EAAUqS,EAI1D2B,EAAUH,EAAO,CACf,IAAII,EAAY,GAAY5B,CAAQ,EAChCuB,EAAe,OAAO,KAAKK,CAAS,EACpCC,EAAeP,GAAeC,EAAc5T,GAAY,IAAK6T,CAAK,EAEtE,MAAI,CAACK,GAAgBA,EAAa,OAAS,EAClC,CAAC,GAGLF,IACHE,EAAe,CAACA,EAAaA,EAAa,OAAS,CAAC,CAAC,GAGhDA,EAAa,IAAI,SAAUC,EAAa,CAC7C,IAAIlB,EAAWgB,EAAUE,CAAW,GAAK,CACvC,sBAAuB,GACvB,IAAK,EACP,EAEIC,EAAM,IAAI,IACVC,GAAepB,EAAS,uBAAyB,CAAC,GAAG,IAAI,SAAUtX,EAAK,CAC1E,OAAIyY,EAAI,IAAIzY,CAAG,EACN,MAGTyY,EAAI,IAAIzY,EAAK,EAAI,EACVsY,EAAUtY,CAAG,EACtB,CAAC,EAAE,OAAO,SAAUoU,EAAM,CACxB,OAAOA,CACT,CAAC,EAED,OAAIkD,EAAS,KACXoB,EAAY,KAAKpB,CAAQ,EAGpBoB,CACT,CAAC,EAAE,KAAK,CAAC,EACX,EACA,GAAeN,G,0GC7FXO,GAAc,SAAqBld,EAAO,CAC5C,IAAIoF,KAAc,cAAW,IAAW,EACtClE,EAASkE,EAAY,OACnBvE,EAAQb,EAAM,MAChBM,EAAYN,EAAM,UAClB2a,EAAW3a,EAAM,SACjBmd,EAAwBnd,EAAM,iBAC9Bod,EAAmBD,IAA0B,OAAS,EAAIA,EACxDE,EAAmB,KAAW,GAAG,OAAO/c,EAAW,UAAU,EAAGY,KAAQ,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOZ,EAAW,aAAa,EAAGN,EAAM,SAAS,EAAG,GAAG,OAAOM,EAAW,6BAA6B,EAAG8c,EAAmB,CAAC,CAAC,EAC7OE,EAAiBtd,EAAM,eAAiB2N,KAC5C,OAAO3N,EAAM,gBAAkB,MAAqB,OAAK,KAAO,QAAS,CACvE,UAAWqd,EACX,MAAOxc,EACP,SAAU8Z,CACZ,CAAC,KAAiB,OAAK2C,EAAgB,CACrC,YAAuB,OAAK,KAAO,QAAS,CAC1C,UAAWD,EACX,MAAOxc,EACP,SAAU8Z,CACZ,CAAC,CACH,CAAC,CACH,EC1BW4C,GAAO,UAAgB,CAChC,SAAoB,QAAM,MAAO,CAC/B,MAAO,MACP,OAAQ,MACR,QAAS,cACT,SAAU,IAAc,QAAM,OAAQ,CACpC,SAAU,IAAc,QAAM,iBAAkB,CAC9C,GAAI,cACJ,GAAI,KACJ,GAAI,aACJ,GAAI,cACJ,GAAI,mBACJ,SAAU,IAAc,OAAK,OAAQ,CACnC,UAAW,UACX,OAAQ,IACV,CAAC,KAAgB,OAAK,OAAQ,CAC5B,UAAW,UACX,OAAQ,MACV,CAAC,CAAC,CACJ,CAAC,KAAgB,QAAM,iBAAkB,CACvC,GAAI,aACJ,GAAI,KACJ,GAAI,cACJ,GAAI,cACJ,GAAI,mBACJ,SAAU,IAAc,OAAK,OAAQ,CACnC,UAAW,UACX,OAAQ,IACV,CAAC,KAAgB,OAAK,OAAQ,CAC5B,UAAW,UACX,OAAQ,aACV,CAAC,KAAgB,OAAK,OAAQ,CAC5B,UAAW,UACX,OAAQ,MACV,CAAC,CAAC,CACJ,CAAC,KAAgB,QAAM,iBAAkB,CACvC,GAAI,cACJ,GAAI,eACJ,GAAI,cACJ,GAAI,cACJ,GAAI,mBACJ,SAAU,IAAc,OAAK,OAAQ,CACnC,UAAW,UACX,OAAQ,IACV,CAAC,KAAgB,OAAK,OAAQ,CAC5B,UAAW,UACX,OAAQ,YACV,CAAC,KAAgB,OAAK,OAAQ,CAC5B,UAAW,UACX,OAAQ,MACV,CAAC,CAAC,CACJ,CAAC,KAAgB,QAAM,iBAAkB,CACvC,GAAI,cACJ,GAAI,eACJ,GAAI,cACJ,GAAI,cACJ,GAAI,mBACJ,SAAU,IAAc,OAAK,OAAQ,CACnC,UAAW,UACX,OAAQ,IACV,CAAC,KAAgB,OAAK,OAAQ,CAC5B,UAAW,UACX,OAAQ,aACV,CAAC,KAAgB,OAAK,OAAQ,CAC5B,UAAW,UACX,OAAQ,MACV,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,KAAgB,OAAK,IAAK,CACzB,OAAQ,OACR,YAAa,EACb,KAAM,OACN,SAAU,UACV,YAAuB,OAAK,IAAK,CAC/B,UAAW,oCACX,YAAuB,OAAK,IAAK,CAC/B,UAAW,kCACX,YAAuB,QAAM,IAAK,CAChC,SAAU,IAAc,QAAM,IAAK,CACjC,SAAU,UACV,SAAU,IAAc,QAAM,IAAK,CACjC,SAAU,IAAc,OAAK,OAAQ,CACnC,EAAG,g3BACH,KAAM,wBACR,CAAC,KAAgB,OAAK,OAAQ,CAC5B,EAAG,o0BACH,KAAM,wBACR,CAAC,CAAC,CACJ,CAAC,KAAgB,OAAK,OAAQ,CAC5B,EAAG,+fACH,KAAM,wBACR,CAAC,CAAC,CACJ,CAAC,KAAgB,OAAK,UAAW,CAC/B,KAAM,yBACN,GAAI,aACJ,GAAI,aACJ,GAAI,aACJ,GAAI,WACN,CAAC,CAAC,CACJ,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CACH,E,YCzGWC,GAA0B,SAASA,EAAwBvC,EAAU,CAC9E,OAAQA,GAAY,CAAC,GAAG,OAAO,SAAUwC,EAAK9E,EAAM,CAIlD,GAHIA,EAAK,KACP8E,EAAI,KAAK9E,EAAK,GAAG,EAEfA,EAAK,UAAYA,EAAK,OAAQ,CAChC,IAAI+E,EAAWD,EAAI,OAAOD,EAAwB7E,EAAK,UAAYA,EAAK,MAAM,GAAK,CAAC,CAAC,EACrF,OAAO+E,CACT,CACA,OAAOD,CACT,EAAG,CAAC,CAAC,CACP,EACIE,GAAc,CAChB,SAAU,UACV,SAAU,UACV,KAAM,UACN,QAAS,UACT,OAAQ,UACR,KAAM,UACN,MAAO,UACP,SAAU,UACV,OAAQ,SACV,EAMO,SAASC,GAAiBpZ,EAAK,CACpC,OAAOA,GAAOmZ,GAAYnZ,CAAG,EAAImZ,GAAYnZ,CAAG,EAAIA,GAAO,EAC7D,CACO,SAASqZ,GAAcC,EAAW,CACvC,OAAOA,EAAU,IAAI,SAAUnF,EAAM,CACnC,IAAIgC,EAAWhC,EAAK,UAAY,CAAC,EAC7BoF,KAAY,KAAc,CAAC,EAAGpF,CAAI,EAItC,GAHI,CAACoF,EAAU,UAAYA,EAAU,SACnCA,EAAU,SAAWA,EAAU,QAE7B,CAACA,EAAU,MAAQA,EAAU,WAC/B,OAAO,KAET,GAAIA,GAAaA,IAAc,MAAQA,IAAc,QAAUA,EAAU,SAAU,CACjF,GAAI,CAACA,EAAU,oBAAsBpD,EAAS,KAAK,SAAU/V,EAAO,CAClE,OAAOA,GAASA,EAAM,MAAQ,CAACA,EAAM,UACvC,CAAC,EACC,SAAO,QAAc,KAAc,CAAC,EAAG+T,CAAI,EAAG,CAAC,EAAG,CAChD,SAAUkF,GAAclD,CAAQ,CAClC,CAAC,EAGH,OAAOoD,EAAU,QACnB,CACA,cAAOA,EAAU,OACVA,CACT,CAAC,EAAE,OAAO,SAAUpF,EAAM,CACxB,OAAOA,CACT,CAAC,CACH,C,gBCzDIqF,GAAe,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,yQAA0Q,CAAE,CAAC,CAAE,EAAG,KAAQ,OAAQ,MAAS,UAAW,EAChd,GAAeA,G,YCKX,GAAe,SAAsBhe,EAAOC,EAAK,CACnD,OAAoB,gBAAoBC,QAAU,MAAS,CAAC,EAAGF,EAAO,CACpE,IAAKC,EACL,KAAM,EACR,CAAC,CAAC,CACJ,EAGIE,GAAuB,aAAiB,EAAY,EAIxD,GAAeA,G,YCdJ8d,GAAW,UAAoB,CACxC,SAAoB,OAAK,MAAO,CAC9B,MAAO,MACP,OAAQ,MACR,QAAS,YACT,KAAM,eACN,cAAe,OACf,YAAuB,OAAK,OAAQ,CAClC,EAAG,2IACL,CAAC,CACH,CAAC,CACH,ECZWC,GAAiB,SAASA,EAAele,EAAO,CACzD,IAAIme,EAAUne,EAAM,QAClBe,EAAgBf,EAAM,cACtBkB,EAASlB,EAAM,OACfoe,EAAYpe,EAAM,UACpB,SAAoB,OAAK,MAAO,CAC9B,UAAW,GAAG,OAAOe,EAAe,WAAW,EAAE,OAAOG,CAAM,EAAE,KAAK,EACrE,YAAuB,OAAK,KAAM,CAChC,UAAW,GAAG,OAAOH,EAAe,gBAAgB,EAAE,OAAOG,CAAM,EAAE,KAAK,EAC1E,SAAUid,GAAY,KAA6B,OAASA,EAAQ,IAAI,SAAUE,EAAKxZ,EAAO,CAC5F,IAAIyZ,EACJ,OAAID,GAAQ,OAA2BC,EAAgBD,EAAI,YAAc,MAAQC,IAAkB,QAAUA,EAAc,UACrG,QAAM,MAAO,CAC/B,UAAW,GAAG,OAAOvd,EAAe,2BAA2B,EAAE,OAAOG,CAAM,EAAE,KAAK,EACrF,SAAU,IAAc,OAAK,MAAO,CAClC,UAAW,GAAG,OAAOH,EAAe,iCAAiC,EAAE,OAAOG,CAAM,EAAE,KAAK,EAC3F,SAAUmd,EAAI,KAChB,CAAC,KAAgB,OAAKH,EAAgB,CACpC,OAAQhd,EACR,UAAWkd,EACX,QAASC,GAAQ,KAAyB,OAASA,EAAI,SACvD,cAAetd,CACjB,CAAC,CAAC,CACJ,EAAG8D,CAAK,KAEU,OAAK,KAAM,CAC7B,UAAW,GAAG,OAAO9D,EAAe,qBAAqB,EAAE,OAAOG,CAAM,EAAE,KAAK,EAC/E,QAAS,SAAiB6O,EAAG,CAC3BA,EAAE,gBAAgB,EAClBqO,GAAc,MAAgCA,EAAUC,CAAG,CAC7D,EACA,YAAuB,QAAM,IAAK,CAChC,KAAMD,EAAY,OAAYC,EAAI,IAClC,OAAQA,EAAI,OACZ,IAAK,aACL,SAAU,CAACE,GAAkBF,EAAI,IAAI,KAAgB,QAAM,MAAO,CAChE,SAAU,IAAc,OAAK,MAAO,CAClC,SAAUA,EAAI,KAChB,CAAC,EAAGA,EAAI,QAAoB,OAAK,OAAQ,CACvC,SAAUA,EAAI,IAChB,CAAC,EAAI,IAAI,CACX,CAAC,CAAC,CACJ,CAAC,CACH,EAAGxZ,CAAK,CACV,CAAC,CACH,CAAC,CACH,CAAC,CACH,EC9CW,GAAQ,SAAegC,EAAM,CAEtC,GADI,CAACA,GACD,CAACA,EAAK,WAAW,MAAM,EACzB,MAAO,GAET,GAAI,CACF,IAAI2R,EAAM,IAAI,IAAI3R,CAAI,EACtB,MAAO,CAAC,CAAC2R,CACX,OAAS7W,EAAO,CACd,MAAO,EACT,CACF,ECLW6c,GAAa,SAAoBC,EAAMC,EAAO,CACvD,GAAID,GAAQ,OAAOA,GAAS,UAAY,GAAMA,CAAI,EAChD,SAAoB,OAAK,MAAO,CAC9B,IAAKA,EACL,IAAK,MACP,CAAC,EAEH,GAAI,OAAOA,GAAS,WAClB,OAAOA,EAAK,EAEd,GAAIA,GAAQ,OAAOA,GAAS,SAC1B,SAAoB,OAAK,MAAO,CAC9B,GAAI,aACJ,SAAUA,CACZ,CAAC,EAEH,GAAI,CAACA,GAAQC,GAAS,OAAOA,GAAU,SAAU,CAC/C,IAAIC,EAASD,EAAM,UAAU,EAAG,CAAC,EACjC,SAAoB,OAAK,MAAO,CAC9B,GAAI,aACJ,SAAUC,CACZ,CAAC,CACH,CACA,OAAOF,CACT,EACWG,GAAgB,SAASA,EAAc5e,EAAO,CACvD,IAAIme,EAAUne,EAAM,QAClBe,EAAgBf,EAAM,cACtBkB,EAASlB,EAAM,OACfoe,EAAYpe,EAAM,UACpB,SAAoB,OAAK,MAAO,CAC9B,UAAW,GAAG,OAAOe,EAAe,WAAW,EAAE,OAAOG,CAAM,EAAE,KAAK,EACrE,YAAuB,OAAK,KAAM,CAChC,UAAW,GAAG,OAAOH,EAAe,gBAAgB,EAAE,OAAOG,CAAM,EAAE,KAAK,EAC1E,SAAUid,GAAY,KAA6B,OAASA,EAAQ,IAAI,SAAUE,EAAKxZ,EAAO,CAC5F,IAAIyZ,EACJ,OAAID,GAAQ,OAA2BC,EAAgBD,EAAI,YAAc,MAAQC,IAAkB,QAAUA,EAAc,UACrG,QAAM,MAAO,CAC/B,UAAW,GAAG,OAAOvd,EAAe,2BAA2B,EAAE,OAAOG,CAAM,EAAE,KAAK,EACrF,SAAU,IAAc,OAAK,MAAO,CAClC,UAAW,GAAG,OAAOH,EAAe,iCAAiC,EAAE,OAAOG,CAAM,EAAE,KAAK,EAC3F,SAAUmd,EAAI,KAChB,CAAC,KAAgB,OAAKO,EAAe,CACnC,OAAQ1d,EACR,UAAWkd,EACX,QAASC,GAAQ,KAAyB,OAASA,EAAI,SACvD,cAAetd,CACjB,CAAC,CAAC,CACJ,EAAG8D,CAAK,KAEU,OAAK,KAAM,CAC7B,UAAW,GAAG,OAAO9D,EAAe,qBAAqB,EAAE,OAAOG,CAAM,EAAE,KAAK,EAC/E,QAAS,SAAiB6O,EAAG,CAC3BA,EAAE,gBAAgB,EAClBqO,GAAc,MAAgCA,EAAUC,CAAG,CAC7D,EACA,YAAuB,QAAM,IAAK,CAChC,KAAMD,EAAY,eAAiBC,EAAI,IACvC,OAAQA,EAAI,OACZ,IAAK,aACL,SAAU,CAACG,GAAWH,EAAI,KAAMA,EAAI,KAAK,KAAgB,OAAK,MAAO,CACnE,YAAuB,OAAK,MAAO,CACjC,SAAUA,EAAI,KAChB,CAAC,CACH,CAAC,CAAC,CACJ,CAAC,CACH,EAAGxZ,CAAK,CACV,CAAC,CACH,CAAC,CACH,CAAC,CACH,E,YChFIga,GAAwC,SAA+Cxe,EAAO,CAChG,MAAO,CACL,YAAa,CACX,UAAW,qBACX,SAAU,OACV,SAAU,CACR,UAAW,cACX,SAAU,IACV,YAAa,EACb,aAAc,EACd,aAAc,EACd,cAAe,EACf,UAAW,OACX,SAAU,CACR,SAAU,WACV,QAAS,eACT,MAAO,IACP,OAAQ,GACR,cAAe,GACf,aAAc,GACd,cAAe,MACf,cAAe,OACf,WAAY,8CACZ,aAAcA,EAAM,aACpB,UAAW,CACT,aAAc,GACd,UAAW,CACT,OAAQ,kBACR,WAAY,IACZ,MAAO,sBACP,SAAU,GACV,QAAS,IACT,WAAY,IACZ,gBAAiB,CACf,UAAW,EACb,CACF,CACF,EACA,UAAW,CACT,gBAAiBA,EAAM,gBACzB,EACA,QAAS,QAAmB,MAAQ,QAAmB,OAAS,UAAS,OAAeA,CAAK,EAC7F,EAAG,CACD,QAAS,OACT,OAAQ,OACR,SAAU,GACV,eAAgB,OAChB,UAAW,CACT,MAAO,GACP,OAAQ,EACV,EACA,UAAW,CACT,kBAAmB,GACnB,MAAOA,EAAM,iBACb,SAAU,GACV,WAAY,OACZ,WAAY,SACZ,aAAc,UAChB,EACA,iBAAkB,CAChB,MAAOA,EAAM,mBACb,SAAU,GACV,WAAY,MACd,CACF,CACF,CACF,CACF,CACF,CACF,ECtEIye,GAAuC,SAA8Cze,EAAO,CAC9F,MAAO,CACL,YAAa,CACX,UAAW,qBACX,SAAU,OACV,SAAU,CACR,UAAW,aACX,SAAU,IACV,YAAa,EACb,aAAc,EACd,aAAc,EACd,cAAe,EACf,UAAW,OACX,SAAU,CACR,SAAU,WACV,QAAS,eACT,MAAO,IACP,OAAQ,IACR,YAAa,EACb,aAAc,EACd,cAAe,GACf,aAAc,GACd,cAAe,MACf,cAAe,OACf,WAAY,8CACZ,aAAcA,EAAM,aACpB,UAAW,CACT,aAAc,GACd,UAAW,CACT,OAAQ,kBACR,WAAY,IACZ,MAAO,sBACP,SAAU,GACV,QAAS,IACT,WAAY,IACZ,gBAAiB,CACf,UAAW,EACb,CACF,CACF,EACA,UAAW,CACT,gBAAiBA,EAAM,gBACzB,EACA,EAAG,CACD,QAAS,OACT,cAAe,SACf,WAAY,SACZ,OAAQ,OACR,SAAU,GACV,eAAgB,OAChB,kBAAmB,CACjB,MAAO,GACP,OAAQ,GACR,OAAQ,SACR,MAAOA,EAAM,aACb,SAAU,GACV,WAAY,OACZ,UAAW,SACX,gBAAiB,oDACjB,aAAcA,EAAM,YACtB,EACA,UAAW,CACT,MAAO,GACP,OAAQ,EACV,EACA,UAAW,CACT,iBAAkB,EAClB,kBAAmB,EACnB,MAAOA,EAAM,iBACb,SAAU,GACV,WAAY,OACZ,WAAY,SACZ,aAAc,UAChB,EACA,iBAAkB,CAChB,MAAOA,EAAM,mBACb,SAAU,GACV,WAAY,MACd,CACF,CACF,CACF,CACF,CACF,CACF,EC/EI0e,GAA6B,SAAoC1e,EAAO,CAC1E,IAAI2e,EAAeC,EAAgBC,EAAgBC,EAAgBC,EACnE,SAAO,KAAgB,CAAC,EAAG/e,EAAM,aAAc,CAC7C,SAAU,CACR,QAAS,cACT,WAAY,SACZ,eAAgB,SAChB,cAAe,EACf,aAAc,EACd,SAAU,GACV,WAAY,OACZ,OAAQ,GACR,MAAO,GACP,OAAQ,UACR,OAAQ2e,EAAgB3e,EAAM,UAAY,MAAQ2e,IAAkB,OAAS,OAASA,EAAc,qBACpG,aAAc3e,EAAM,aACpB,UAAW,CACT,OAAQ4e,EAAiB5e,EAAM,UAAY,MAAQ4e,IAAmB,OAAS,OAASA,EAAe,0BACvG,iBAAkBC,EAAiB7e,EAAM,UAAY,MAAQ6e,IAAmB,OAAS,OAASA,EAAe,uBACnH,EACA,WAAY,CACV,OAAQC,EAAiB9e,EAAM,UAAY,MAAQ8e,IAAmB,OAAS,OAASA,EAAe,0BACvG,iBAAkBC,EAAiB/e,EAAM,UAAY,MAAQ+e,IAAmB,OAAS,OAASA,EAAe,uBACnH,CACF,EACA,eAAgB,CACd,kBAAmB,OACnB,gBAAiB,MACjB,iBAAkB,EAClB,eAAgB,OAChB,WAAY,IACZ,MAAO,sBACP,SAAU,GACV,QAAS,IACT,WAAY,IACZ,gBAAiB,CACf,iBAAkB,EACpB,CACF,EACA,eAAa,KAAgB,CAAC,EAAG,GAAG,OAAO/e,EAAM,OAAQ,gBAAgB,EAAG,CAC1E,QAAS,MACX,CAAC,EACD,WAAYye,GAAqCze,CAAK,EACtD,YAAawe,GAAsCxe,CAAK,CAC1D,CAAC,CACH,EACO,SAAS,GAASC,EAAW,CAClC,SAAO,OAAa,qBAAsB,SAAUD,EAAO,CACzD,IAAIE,KAAe,QAAc,KAAc,CAAC,EAAGF,CAAK,EAAG,CAAC,EAAG,CAC7D,aAAc,IAAI,OAAOC,CAAS,CACpC,CAAC,EACD,MAAO,CAACye,GAA2Bxe,CAAY,CAAC,CAClD,CAAC,CACH,CCtCO,IAAIge,GAAoB,SAA2BE,EAAM,CAC9D,OAAI,OAAOA,GAAS,YACE,OAAK,MAAO,CAC9B,MAAO,OACP,OAAQ,GACR,IAAKA,EACL,IAAK,MACP,CAAC,EAEC,OAAOA,GAAS,WACXA,EAAK,EAEPA,CACT,EAQWY,GAAqB,SAA4Brf,EAAO,CACjE,IAAIsf,EACAnB,EAAUne,EAAM,QAClBuf,EAAgBvf,EAAM,cACtBwf,EAAmBxf,EAAM,UACzBM,EAAYkf,IAAqB,OAAS,UAAYA,EACtDpB,EAAYpe,EAAM,YAChBC,EAAM,SAAa,IAAI,EACvBwf,EAAa,SAAa,IAAI,EAC9B1e,EAAgB,GAAG,OAAOT,EAAW,cAAc,EACnDU,EAAY,GAASD,CAAa,EACpCE,EAAUD,EAAU,QACpBE,EAASF,EAAU,OACjB0O,KAAY,YAAS,EAAK,EAC5BC,KAAa,KAAeD,EAAW,CAAC,EACxCrI,EAAOsI,EAAW,CAAC,EACnB+P,EAAU/P,EAAW,CAAC,EACpBgQ,EAAiB,SAAwBtB,EAAK,CAChDD,GAAc,MAAgCA,EAAUC,EAAKoB,CAAU,CACzE,EACIG,KAAoB,WAAQ,UAAY,CAC1C,IAAIC,EAAW1B,GAAY,KAA6B,OAASA,EAAQ,KAAK,SAAUE,EAAK,CAC3F,MAAO,EAAEA,GAAQ,MAA0BA,EAAI,KACjD,CAAC,EACD,OAAIwB,KACkB,OAAKjB,GAAe,CACtC,OAAQ1d,EACR,QAASid,EACT,UAAWC,EAAYuB,EAAiB,OACxC,cAAe,GAAG,OAAO5e,EAAe,SAAS,CACnD,CAAC,KAEiB,OAAKmd,GAAgB,CACvC,OAAQhd,EACR,QAASid,EACT,UAAWC,EAAYuB,EAAiB,OACxC,cAAe,GAAG,OAAO5e,EAAe,UAAU,CACpD,CAAC,CACH,EAAG,CAACod,EAASpd,EAAeG,CAAM,CAAC,EACnC,GAAI,EAAElB,GAAU,OAA6Bsf,EAAiBtf,EAAM,WAAa,MAAQsf,IAAmB,QAAUA,EAAe,QAAS,OAAO,KACrJ,IAAIQ,EAAiBP,EAAgBA,EAAcvf,GAAU,KAA2B,OAASA,EAAM,QAAS4f,CAAiB,EAAIA,EACjIG,KAAmBtS,KAAsB,OAAW,SAAUuS,EAAY,CAC5E,OAAON,EAAQM,CAAU,CAC3B,CAAC,EACD,OAAO/e,KAAsB,QAAM,WAAW,CAC5C,SAAU,IAAc,OAAK,MAAO,CAClC,IAAKhB,EACL,QAAS,SAAiB8P,EAAG,CAC3BA,EAAE,gBAAgB,EAClBA,EAAE,eAAe,CACnB,CACF,CAAC,KAAgB,OAAK,QAAS,QAAc,KAAc,CACzD,UAAW,cACX,QAAS,CAAC,OAAO,EACjB,OAAQ,KACR,MAAO,EACT,EAAGgQ,CAAgB,EAAG,CAAC,EAAG,CACxB,iBAAkB,GAAG,OAAOhf,EAAe,WAAW,EAAE,OAAOG,CAAM,EAAE,KAAK,EAC5E,QAAS4e,EACT,kBAAmB,UAA6B,CAC9C,OAAO7f,EAAI,SAAW,SAAS,IACjC,EACA,YAAuB,OAAK,OAAQ,CAClC,IAAKwf,EACL,QAAS,SAAiB1P,EAAG,CAC3BA,EAAE,gBAAgB,CACpB,EACA,UAAW,KAAW,GAAG,OAAOhP,EAAe,OAAO,EAAGG,KAAQ,KAAgB,CAAC,EAAG,GAAG,OAAOH,EAAe,cAAc,EAAGsG,CAAI,CAAC,EACpI,YAAuB,OAAK4W,GAAU,CAAC,CAAC,CAC1C,CAAC,CACH,CAAC,CAAC,CAAC,CACL,CAAC,CAAC,CACJ,E,mCChHA,SAASgC,IAAe,CACtB,SAAoB,OAAK,MAAO,CAC9B,MAAO,MACP,OAAQ,MACR,QAAS,YACT,KAAM,eACN,cAAe,OACf,YAAuB,OAAK,OAAQ,CAClC,EAAG,2MACL,CAAC,CACH,CAAC,CACH,CCTA,IAAIC,GAAoB,SAA2B7f,EAAO,CACxD,IAAI2e,EAAeC,EAAgBC,EACnC,SAAO,KAAgB,CAAC,EAAG7e,EAAM,aAAc,CAC7C,SAAU,WACV,gBAAiB,OACjB,OAAQ,MACR,MAAO,OACP,OAAQ,OACR,SAAU,CAAC,OAAQ,MAAM,EACzB,UAAW,SACX,aAAc,OACd,eAAgB,QAChB,WAAY,iBACZ,QAAS,OACT,WAAY,SACZ,eAAgB,SAChB,OAAQ,UACR,OAAQ2e,EAAgB3e,EAAM,UAAY,MAAQ2e,IAAkB,SAAWA,EAAgBA,EAAc,SAAW,MAAQA,IAAkB,OAAS,OAASA,EAAc,yBAClL,iBAAkBC,EAAiB5e,EAAM,UAAY,MAAQ4e,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,uBAClM,UAAW,kGACX,UAAW,CACT,OAAQC,EAAiB7e,EAAM,UAAY,MAAQ6e,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,8BACxL,UAAW,oGACb,EACA,WAAY,CACV,SAAU,MACZ,EACA,UAAW,CACT,WAAY,kBACZ,UAAW,eACb,EACA,cAAe,CACb,UAAW,CACT,UAAW,gBACb,CACF,CACF,CAAC,CACH,EACO,SAAS,GAAS5e,EAAW,CAClC,SAAO,OAAa,yBAA0B,SAAUD,EAAO,CAC7D,IAAI8f,KAAiB,QAAc,KAAc,CAAC,EAAG9f,CAAK,EAAG,CAAC,EAAG,CAC/D,aAAc,IAAI,OAAOC,CAAS,CACpC,CAAC,EACD,MAAO,CAAC4f,GAAkBC,CAAc,CAAC,CAC3C,CAAC,CACH,CC7CA,IAAI,GAAY,CAAC,WAAY,WAAW,EAK7BC,GAAgB,SAAuBpgB,EAAO,CACvD,IAAIqgB,EAAWrgB,EAAM,SACnBsgB,EAAYtgB,EAAM,UAClBkP,KAAO,MAAyBlP,EAAO,EAAS,EAC9CgB,EAAY,GAAShB,EAAM,SAAS,EACtCiB,EAAUD,EAAU,QACpBE,EAASF,EAAU,OACrB,OAAIqf,GAAYC,EAAkB,KAC3Brf,KAAsB,OAAK,SAAO,QAAc,KAAc,CAAC,EAAGiO,CAAI,EAAG,CAAC,EAAG,CAClF,UAAW,KAAWlP,EAAM,UAAWkB,KAAQ,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOlB,EAAM,UAAW,YAAY,EAAGsgB,CAAS,EAAG,GAAG,OAAOtgB,EAAM,UAAW,YAAY,EAAGqgB,CAAQ,CAAC,EAC5L,YAAuB,OAAKJ,GAAc,CAAC,CAAC,CAC9C,CAAC,CAAC,CAAC,CACL,E,uEChBI,GAAY,CAAC,YAAa,YAAa,UAAW,OAAQ,SAAU,WAAY,UAAW,UAAU,EAOrGjd,GAAoB,aAAiB,SAAUhD,EAAOC,EAAK,CAC7D,IAAIS,EAAYV,EAAM,UACpBugB,EAAYvgB,EAAM,UAClBwgB,EAAUxgB,EAAM,QAChBiD,EAAOjD,EAAM,KACbkD,EAASlD,EAAM,OACfmD,EAAWnD,EAAM,SACjBsC,EAAUtC,EAAM,QAChB2a,EAAW3a,EAAM,SACjBuC,KAAY,MAAyBvC,EAAO,EAAS,EACnDygB,EAAU,SAAa,EACvBC,KAAY,OAAcD,EAASxgB,CAAG,KAC1C,OAAQ,GAAQsgB,GAAa5F,GAAW,6CAA6C,KACrF,OAAgB8F,CAAO,EACvB,IAAIrd,EAAoB,aAAiBC,IAAO,EAC9CC,EAAwBF,EAAkB,UAC1C9C,EAAYgD,IAA0B,OAAS,UAAYA,EAC3DC,EAAgBH,EAAkB,cAChCI,EAAc,KAAWD,EAAejD,KAAW,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAW,OAAO,EAAG,CAAC,CAAC2C,GAAQ,CAAC,CAACsd,CAAS,EAAG7f,CAAS,EACvIigB,EAAiB,QAAW,KAAgB,CAAC,EAAG,GAAG,OAAOrgB,EAAW,OAAO,EAAG,CAAC,CAAC2C,CAAI,CAAC,EACtFS,EAAWR,EAAS,CACtB,YAAa,UAAU,OAAOA,EAAQ,MAAM,EAC5C,UAAW,UAAU,OAAOA,EAAQ,MAAM,CAC5C,EAAI,OACA0d,KAAgB,QAAc,KAAc,CAAC,EAAG,KAAY,EAAG,CAAC,EAAG,CACrE,UAAWD,EACX,MAAOjd,EACP,QAAS8c,CACX,CAAC,EACIA,GACH,OAAOI,EAAc,QAIvB,IAAIC,EAAkB,UAA2B,CAC/C,OAAIN,EACkB,gBAAoBA,EAAWK,EAAejG,CAAQ,EAExEA,MACF,OAAQ,EAAQ6F,GAAY,WAAe,MAAM7F,CAAQ,IAAM,GAAkB,iBAAqBA,CAAQ,GAAK,WAAe,KAAKA,CAAQ,EAAE,OAAS,MAAO,0FAA+F,EAC5O,gBAAoB,SAAO,MAAS,CAAC,EAAGiG,EAAe,CACzE,QAASJ,CACX,CAAC,EAAG7F,CAAQ,GAEP,IACT,EACIlX,EAAeN,EACnB,OAAIM,IAAiB,QAAanB,IAChCmB,EAAe,IAEG,gBAAoB,UAAQ,MAAS,CACvD,KAAM,KACR,EAAGlB,EAAW,CACZ,IAAKme,EACL,SAAUjd,EACV,QAASnB,EACT,UAAWkB,CACb,CAAC,EAAGqd,EAAgB,CAAC,CACvB,CAAC,EACD7d,GAAK,YAAc,WACnB,OAAeA,GCrEX,GAAY,CAAC,OAAQ,UAAU,EAG/B8d,GAAc,IAAI,IACtB,SAASC,GAAuBC,EAAW,CACzC,MAAO,GAAQ,OAAOA,GAAc,UAAYA,EAAU,QAAU,CAACF,GAAY,IAAIE,CAAS,EAChG,CACA,SAASC,GAAwBC,EAAY,CAC3C,IAAIrc,EAAQ,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,EAC5Esc,EAAmBD,EAAWrc,CAAK,EACvC,GAAIkc,GAAuBI,CAAgB,EAAG,CAC5C,IAAIC,EAAS,SAAS,cAAc,QAAQ,EAC5CA,EAAO,aAAa,MAAOD,CAAgB,EAC3CC,EAAO,aAAa,iBAAkBD,CAAgB,EAClDD,EAAW,OAASrc,EAAQ,IAC9Buc,EAAO,OAAS,UAAY,CAC1BH,GAAwBC,EAAYrc,EAAQ,CAAC,CAC/C,EACAuc,EAAO,QAAU,UAAY,CAC3BH,GAAwBC,EAAYrc,EAAQ,CAAC,CAC/C,GAEFic,GAAY,IAAIK,CAAgB,EAChC,SAAS,KAAK,YAAYC,CAAM,CAClC,CACF,CACe,SAASC,IAAS,CAC/B,IAAI9a,EAAU,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAC,EAC/Eya,EAAYza,EAAQ,UACtB+a,EAAwB/a,EAAQ,iBAChCgb,EAAmBD,IAA0B,OAAS,CAAC,EAAIA,EAQzDN,GAAa,OAAO,UAAa,aAAe,OAAO,QAAW,aAAe,OAAO,SAAS,eAAkB,aACjH,MAAM,QAAQA,CAAS,EAEzBC,GAAwBD,EAAU,QAAQ,CAAC,EAE3CC,GAAwB,CAACD,CAAS,CAAC,GAGvC,IAAIQ,EAAwB,aAAiB,SAAUxhB,EAAOC,EAAK,CACjE,IAAI8G,EAAO/G,EAAM,KACf2a,EAAW3a,EAAM,SACjBuC,KAAY,MAAyBvC,EAAO,EAAS,EAGnDyhB,EAAU,KACd,OAAIzhB,EAAM,OACRyhB,EAAuB,gBAAoB,MAAO,CAChD,UAAW,IAAI,OAAO1a,CAAI,CAC5B,CAAC,GAEC4T,IACF8G,EAAU9G,GAEQ,gBAAoB,MAAM,MAAS,CAAC,EAAG4G,EAAkBhf,EAAW,CACtF,IAAKtC,CACP,CAAC,EAAGwhB,CAAO,CACb,CAAC,EACD,SAAS,YAAc,WAChBD,CACT,CCpEO,SAASE,GAAM7a,EAAM,CAC1B,MAAO,uCAAuC,KAAKA,CAAI,CACzD,C,4BCHI8a,GAAkB,CACpB,SAAU,QACV,OAAQ,OACR,aAAc,QACd,YAAa,GACb,YAAa,GACb,YAAa,GACb,aAAc,UACd,WAAY,EACd,ECNIC,GAA4B,SAAmCvhB,EAAOwhB,EAAM,CAC9E,IAAI7C,EAAeC,EACf6C,EAAYD,EAAK,SAAS,YAAY,GAAK7C,EAAgB3e,EAAM,UAAY,MAAQ2e,IAAkB,OAAS,OAASA,EAAc,QAAUC,EAAiB5e,EAAM,UAAY,MAAQ4e,IAAmB,OAAS,OAASA,EAAe,MACpP,SAAO,QAAc,QAAc,KAAgB,CAAC,EAAG,GAAG,OAAO5e,EAAM,YAAY,KAAG,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,KAAgB,CACpO,WAAY,cACZ,MAAOyhB,GAAc,KAA+B,OAASA,EAAU,cACvE,OAAQ,MACV,EAAG,GAAG,OAAOzhB,EAAM,aAAc,YAAY,EAAG,CAC9C,WAAY,iBACd,CAAC,EAAG,GAAG,OAAOA,EAAM,aAAc,mBAAmB,KAAG,KAAgB,CAAC,EAAG,KAAK,OAAOA,EAAM,OAAQ,WAAW,EAAG,CAClH,mBAAoB,EACtB,CAAC,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,qBAAqB,EAAG,CACnD,MAAO,OACP,OAAQ,OACR,QAAS,aACX,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,qBAAqB,EAAG,CAClD,gBAAiB,CACf,MAAO,MACT,CACF,CAAC,EAAG,GAAG,OAAOA,EAAM,aAAc,YAAY,EAAG,CAC/C,QAAS,OACT,WAAY,QACd,CAAC,EAAG,kBAAgB,QAAgB,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ;AAAA,SAAwB,EAAE,OAAOA,EAAM,OAAQ,qBAAqB,EAAE,OAAOA,EAAM,OAAQ,0BAA0B,EAAE,OAAOA,EAAM,OAAQ;AAAA,SAAwB,EAAE,OAAOA,EAAM,OAAQ,qBAAqB,EAAE,OAAOA,EAAM,OAAQ,0BAA0B,EAAE,OAAOA,EAAM,OAAQ,kBAAkB,EAAE,OAAOA,EAAM,OAAQ;AAAA,SAAiC,EAAE,OAAOA,EAAM,OAAQ,kBAAkB,EAAE,OAAOA,EAAM,OAAQ,qBAAqB,EAAG,CACxhB,cAAe,eACf,YAAa,gBACf,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,qBAAqB,EAAE,OAAOA,EAAM,OAAQ,0BAA0B,EAAE,OAAOA,EAAM,OAAQ,2BAA2B,EAAE,OAAOA,EAAM,OAAQ;AAAA,SAAiC,EAAE,OAAOA,EAAM,OAAQ,2BAA2B,EAAE,OAAOA,EAAM,OAAQ,qBAAqB,EAAG,CAC3S,gBAAiByhB,GAAc,KAA+B,OAASA,EAAU,wBACjF,aAAczhB,EAAM,cACtB,CAAC,EAAG,GAAG,OAAOA,EAAM,aAAc,QAAQ,KAAG,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,wBAAwB,EAAG,CAClH,cAAe,CACjB,CAAC,CAAC,CAAC,EAAG,kBAAgB,QAAgB,QAAgB,QAAgB,QAAgB,KAAgB,CACpG,QAAS,OACT,cAAe,MACf,WAAY,SACZ,IAAKA,EAAM,QACb,EAAG,GAAG,OAAOA,EAAM,aAAc,YAAY,EAAG,CAC9C,SAAU,OACV,aAAc,WACd,SAAU,SACV,UAAW,YACX,WAAY,QACd,CAAC,EAAG,iBAAe,QAAgB,KAAgB,CACjD,SAAU,GACV,OAAQ,EACV,EAAG,GAAG,OAAOA,EAAM,aAAc,YAAY,EAAG,CAC9C,OAAQ,OACR,MAAO,OACP,WAAY,kBACZ,WAAY,CACV,WAAY,kBACZ,OAAQ,MACV,CACF,CAAC,EAAG,GAAG,OAAOA,EAAM,aAAc,qBAAqB,EAAG,CACxD,QAAS,iBACX,CAAC,CAAC,EAAG,sBAAuB,CAC1B,cAAe,SACf,eAAgB,QAClB,CAAC,EAAG,IAAI,OAAOA,EAAM,aAAc,mBAAmB,EAAG,CACvD,IAAKA,EAAM,SACX,OAAQ,GACR,SAAU,QACZ,CAAC,EAAG,IAAI,OAAOA,EAAM,aAAc,4BAA4B,KAAG,KAAgB,CAChF,WAAY,OACZ,IAAK,CACP,EAAG,IAAI,OAAOA,EAAM,aAAc,uBAAuB,KAAG,QAAgB,KAAgB,CAC1F,QAAS,MACX,EAAG,GAAG,OAAOA,EAAM,aAAc,YAAY,EAAG,CAC9C,OAAQ,OACR,MAAO,OACP,WAAY,kBACZ,WAAY,CACV,WAAY,iBACZ,OAAQ,MACV,CACF,CAAC,EAAG,GAAG,OAAOA,EAAM,aAAc,YAAY,EAAG,CAC/C,QAAS,eACT,QAAS,oBACT,UAAW,SACX,SAAU,GACV,OAAQ,GACR,WAAY,OACZ,SAAU,SACV,aAAc,WACd,WAAY,SACZ,MAAO,OACP,OAAQ,EACR,QAAS,EACT,iBAAkB,CACpB,CAAC,CAAC,CAAC,CAAC,EAAG,aAAW,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,wBAAwB,EAAG,CACvF,SAAU,GACV,MAAOA,EAAM,eACb,WAAY,CACV,gBAAiB,CACnB,CACF,CAAC,CAAC,EAAG,kBAAmB,CACtB,MAAOA,EAAM,mBACb,SAAU,GACV,WAAY,EACd,CAAC,CAAC,EAAGwhB,EAAK,SAAS,YAAY,EAAI,CAAC,KAAI,KAAgB,CAAC,EAAG,GAAG,OAAOxhB,EAAM,OAAQ,eAAe,EAAE,OAAOA,EAAM,OAAQ,qBAAqB,KAAG,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAM,aAAc,aAAa,EAAG,CAClN,WAAY,YACd,CAAC,CAAC,CAAC,EAAG,CAAC,KAAG,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,qBAAqB,EAAG,CAC5E,gBAAiB,4BACjB,0BAA2B,YAC3B,eAAgB,WAClB,CAAC,CAAC,CACJ,EACO,SAAS,GAASC,EAAWuhB,EAAM,CACxC,SAAO,OAAa,oBAAsBA,EAAM,SAAUxhB,EAAO,CAC/D,IAAI0hB,KAAqB,QAAc,KAAc,CAAC,EAAG1hB,CAAK,EAAG,CAAC,EAAG,CACnE,aAAc,IAAI,OAAOC,CAAS,CACpC,CAAC,EACD,MAAO,CAACshB,GAA0BG,EAAoBF,GAAQ,QAAQ,CAAC,CACzE,CAAC,CACH,CChGA,IAAIG,GAAkB,SAAyBhiB,EAAO,CACpD,IAAI0P,KAAY,YAAS1P,EAAM,SAAS,EACtC2P,KAAa,KAAeD,EAAW,CAAC,EACxC4Q,EAAY3Q,EAAW,CAAC,EACxBsS,EAAetS,EAAW,CAAC,EACzBuS,KAAa,YAAS,EAAK,EAC7BC,KAAa,KAAeD,EAAY,CAAC,EACzC7a,EAAO8a,EAAW,CAAC,EACnBzC,EAAUyC,EAAW,CAAC,EAOxB,SANA,aAAU,UAAY,CACpBzC,EAAQ,EAAK,EACb,WAAW,UAAY,CACrBuC,EAAajiB,EAAM,SAAS,CAC9B,EAAG,GAAG,CACR,EAAG,CAACA,EAAM,SAAS,CAAC,EAChBA,EAAM,QACDA,EAAM,YAEK,OAAK,KAAS,CAChC,MAAOA,EAAM,MACb,KAAMsgB,GAAatgB,EAAM,UAAYqH,EAAO,GAC5C,UAAW,QACX,aAAcqY,EACd,SAAU1f,EAAM,QAClB,CAAC,CACH,EACIoiB,GAAWf,GAAqB,CAClC,UAAWM,GAAgB,WAC7B,CAAC,EAQGU,GAAU,SAAiBhgB,EAAM,CACnC,IAAIigB,EAAe,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,QACnF5hB,EAAY,UAAU,OAAS,EAAI,UAAU,CAAC,EAAI,OACtD,GAAI,OAAO2B,GAAS,UAAYA,IAAS,GAAI,CAC3C,GAAI,GAAMA,CAAI,GAAKqf,GAAMrf,CAAI,EAC3B,SAAoB,OAAK,MAAO,CAC9B,MAAO,GACP,IAAKA,EACL,IAAK,OACL,UAAW3B,CACb,EAAG2B,CAAI,EAET,GAAIA,EAAK,WAAWigB,CAAY,EAC9B,SAAoB,OAAKF,GAAU,CACjC,KAAM/f,CACR,CAAC,CAEL,CACA,OAAOA,CACT,EACIkgB,GAAqB,SAA4B7D,EAAO,CAC1D,GAAIA,GAAS,OAAOA,GAAU,SAAU,CACtC,IAAIC,EAASD,EAAM,UAAU,EAAG,CAAC,EAAE,YAAY,EAC/C,OAAOC,CACT,CACA,OAAO,IACT,EACI6D,MAAwB,MAAa,SAASA,EAASxiB,EAAO,CAChE,IAAI8N,EAAQ,QACZ,MAAgB,KAAM0U,CAAQ,KAC9B,KAAgB,KAAM,QAAS,MAAM,KACrC,KAAgB,KAAM,kBAAmB,UAAY,CACnD,IAAI1E,EAAY,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,CAAC,EACjF2E,EAAQ,UAAU,OAAS,EAAI,UAAU,CAAC,EAAI,OAC9CC,EAAe,UAAU,OAAS,EAAI,UAAU,CAAC,EAAI,OACzD,OAAO5E,EAAU,IAAI,SAAUnF,EAAM,CACnC,OAAO7K,EAAM,iBAAiB6K,EAAM8J,EAAOC,CAAY,CACzD,CAAC,EAAE,OAAO,SAAU/J,EAAM,CACxB,OAAOA,CACT,CAAC,EAAE,KAAK,CAAC,CACX,CAAC,KAED,KAAgB,KAAM,mBAAoB,SAAUA,EAAM8J,EAAOC,EAAc,CAC7E,IAAIC,EAAc7U,EAAM,MACtB8U,EAAoBD,EAAY,kBAChC5hB,EAAgB4hB,EAAY,cAC5BriB,EAAYqiB,EAAY,UACxBrC,EAAYqC,EAAY,UACxBvJ,EAAOuJ,EAAY,KACnBL,EAAeK,EAAY,aAC3BE,EAASF,EAAY,OACnBG,GAAW1J,GAAS,KAA0B,OAASA,EAAK,QAAU,SAAWyJ,IAAW,MAC5FE,EAAcjV,EAAM,MAAM,MAC1B7H,EAAO6H,EAAM,YAAY6K,CAAI,EAC7BgC,GAAYhC,GAAS,KAA0B,OAASA,EAAK,YAAcA,GAAS,KAA0B,OAASA,EAAK,QAC5HqK,EAAWF,GAAWL,IAAU,EAAI,QAAU,OAClD,GAAI,MAAM,QAAQ9H,CAAQ,GAAKA,EAAS,OAAS,EAAG,CAClD,IAAIsI,EAAcC,EAAcC,EAAcC,EAAcC,EAExDC,GAAgBb,IAAU,GAAKK,GAAWL,IAAU,EAGpDc,EAAUlB,GAAQ1J,EAAK,KAAM2J,EAAc,GAAG,OAAOvhB,EAAe,QAAQ,EAAE,QAAQkiB,EAAenV,EAAM,SAAW,MAAQmV,IAAiB,OAAS,OAASA,EAAa,MAAM,CAAC,EAIrLO,EAAclD,GAAagD,GAAgBf,GAAmBtc,CAAI,EAAI,KACtEwd,MAA4B,QAAM,MAAO,CAC3C,UAAW,KAAW,GAAG,OAAO1iB,EAAe,aAAa,GAAImiB,EAAepV,EAAM,SAAW,MAAQoV,IAAiB,OAAS,OAASA,EAAa,UAAQ,QAAgB,QAAgB,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOniB,EAAe,uBAAuB,EAAGuf,CAAS,EAAG,GAAG,OAAOvf,EAAe,8BAA8B,EAAE,OAAO2hB,CAAY,EAAGpC,CAAS,EAAG,GAAG,OAAOvf,EAAe,mBAAmB,EAAGiiB,IAAa,OAAO,EAAG,GAAG,OAAOjiB,EAAe,4BAA4B,GAAIqY,GAAS,KAA0B,OAASA,EAAK,qBAAuBkH,CAAS,CAAC,EAC/kB,SAAU,CAAC0C,IAAa,SAAW1C,EAAY,KAAOgD,IAAiBC,KAAuB,OAAK,OAAQ,CACzG,UAAW,GAAG,OAAOxiB,EAAe,aAAa,EAAE,QAAQoiB,EAAerV,EAAM,SAAW,MAAQqV,IAAiB,OAAS,OAASA,EAAa,MAAM,EAAE,KAAK,EAChK,SAAUI,CACZ,CAAC,EAAIC,KAA0B,OAAK,OAAQ,CAC1C,UAAW,KAAW,GAAG,OAAOziB,EAAe,YAAY,GAAIqiB,EAAetV,EAAM,SAAW,MAAQsV,IAAiB,OAAS,OAASA,EAAa,UAAQ,KAAgB,CAAC,EAAG,GAAG,OAAOriB,EAAe,qBAAqB,EAAGiiB,IAAa,SAAWM,KAAkBC,GAAWC,EAAY,CAAC,EACtS,SAAUvd,CACZ,CAAC,CAAC,CACJ,CAAC,EAGGyY,GAAQkE,EAAoBA,KAAkB,QAAc,KAAc,CAAC,EAAGjK,CAAI,EAAG,CAAC,EAAG,CAC3F,MAAO,EACT,CAAC,EAAG8K,GAAc3V,EAAM,KAAK,EAAI2V,GAGjC,GAAIX,GAAWL,IAAU,GAAK3U,EAAM,MAAM,WAAa,CAACsL,EAAK,wBAC3D,OAAOtL,EAAM,gBAAgB6M,EAAU8H,EAAQ,EAAGA,CAAK,EAEzD,IAAI3I,EAAehM,EAAM,gBAAgB6M,EAAU8H,EAAQ,EAAGK,GAAWL,IAAU,GAAK3U,EAAM,MAAM,UAAY2U,EAAQA,EAAQ,CAAC,EACjI,MAAO,CAAC,CACN,KAAMO,EACN,IAAKrK,EAAK,KAAOA,EAAK,KACtB,MAAO+F,GACP,QAASoE,EAAU,OAAYnK,EAAK,aACpC,SAAUmB,EACV,UAAW,QAAW,QAAgB,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAO/Y,EAAe,QAAQ,EAAGiiB,IAAa,OAAO,EAAG,GAAG,OAAOjiB,EAAe,UAAU,EAAGiiB,IAAa,OAAO,EAAG,GAAG,OAAOjiB,EAAe,mBAAmB,EAAGiiB,IAAa,SAAWM,IAAiBC,CAAO,CAAC,CACpS,EAAGT,GAAWL,IAAU,EAAI,CAC1B,KAAM,UACN,UAAWniB,EACX,UAAW,GAAG,OAAOS,EAAe,UAAU,EAC9C,KAAM4X,EAAK,KAAOA,EAAK,MAAQ,iBAC/B,MAAO,CACL,QAAS,EACT,eAAgB,EAChB,OAAQ7K,EAAM,MAAM,UAAY,MAAQ,WACxC,iBAAkBA,EAAM,MAAM,UAAY,EAAI,EAC9C,YAAaiV,GAAgB,OAAmCM,EAAsBN,EAAY,UAAY,MAAQM,IAAwB,SAAWA,EAAsBA,EAAoB,SAAW,MAAQA,IAAwB,OAAS,OAASA,EAAoB,oBACtR,CACF,EAAI,MAAS,EAAE,OAAO,OAAO,CAC/B,CACA,MAAO,CACL,UAAW,GAAG,OAAOtiB,EAAe,YAAY,EAChD,SAAU4X,EAAK,SACf,IAAKA,EAAK,KAAOA,EAAK,KACtB,QAASA,EAAK,aAEd,MAAO7K,EAAM,gBAAgB6K,EAAM8J,EAAOC,CAAY,CACxD,CACF,CAAC,KACD,KAAgB,KAAM,cAAe,SAAU/J,EAAM,CACnD,IAAI1S,EAAO0S,EAAK,KACdI,EAASJ,EAAK,OACZ+K,EAAe5V,EAAM,MACvBsL,EAAOsK,EAAa,KACpBvJ,EAAgBuJ,EAAa,cAC3BC,EAAY1d,EAOhB,OANI8S,IAAWK,GAAS,KAA0B,OAASA,EAAK,UAAY,KAC1EuK,EAAYxJ,GAAkB,KAAmC,OAASA,EAAc,CACtF,GAAIpB,EACJ,eAAgB9S,CAClB,CAAC,GAEC6H,EAAM,MAAM,eACPA,EAAM,MAAM,eAAe6K,EAAMgL,EAAW7V,EAAM,KAAK,EAEzD6V,CACT,CAAC,KAMD,KAAgB,KAAM,kBAAmB,SAAUhL,EAAM8J,EAAOC,EAAc,CAC5E,IAAIkB,EAAcC,EAAeC,EAAeC,EAC5CC,EAAWlW,EAAM,eAAe6K,EAAK,MAAQ,GAAG,EAChDsL,EAAenW,EAAM,MACvBoW,EAAwBD,EAAa,SACrCE,EAAWD,IAA0B,OAAS,CAC5C,SAAU,GACZ,EAAIA,EACJ7D,EAAW4D,EAAa,SACxBG,EAAaH,EAAa,WAC1BI,EAAiBJ,EAAa,eAC9B3B,EAAe2B,EAAa,aAG1BK,EAAgBxW,EAAM,YAAY6K,CAAI,EACtC4L,EAAezW,EAAM,MACvB/M,EAAgBwjB,EAAa,cAC7BnL,EAAOmL,EAAa,KACpBjE,EAAYiE,EAAa,UACvBzB,GAAW1J,GAAS,KAA0B,OAASA,EAAK,QAAU,QAEtEoL,GAAU/B,IAAU,GAAKK,GAAWL,IAAU,EAC9CpgB,EAAQmiB,GAAiBnC,GAAQ1J,EAAK,KAAM2J,EAAc,GAAG,OAAOvhB,EAAe,QAAQ,EAAE,QAAQ6iB,EAAe9V,EAAM,SAAW,MAAQ8V,IAAiB,OAAS,OAASA,EAAa,MAAM,CAAC,EAAlL,KAGlBJ,EAAclD,GAAakE,GAAUjC,GAAmB+B,CAAa,EAAI,KACzEG,MAA2B,QAAM,MAAO,CAC1C,UAAW,KAAW,GAAG,OAAO1jB,EAAe,aAAa,GAAI8iB,EAAgB/V,EAAM,SAAW,MAAQ+V,IAAkB,OAAS,OAASA,EAAc,UAAQ,QAAgB,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAO9iB,EAAe,uBAAuB,EAAGuf,CAAS,EAAG,GAAG,OAAOvf,EAAe,8BAA8B,EAAE,OAAO2hB,CAAY,EAAGpC,CAAS,EAAG,GAAG,OAAOvf,EAAe,4BAA4B,GAAIqY,GAAS,KAA0B,OAASA,EAAK,qBAAuBkH,CAAS,CAAC,EAC5f,SAAU,IAAc,OAAK,OAAQ,CACnC,UAAW,GAAG,OAAOvf,EAAe,aAAa,EAAE,QAAQ+iB,EAAgBhW,EAAM,SAAW,MAAQgW,IAAkB,OAAS,OAASA,EAAc,MAAM,EAAE,KAAK,EACnK,MAAO,CACL,QAASN,IAAgB,MAAQ,CAACnhB,EAAO,OAAS,EACpD,EACA,SAAUA,MAAqB,OAAK,OAAQ,CAC1C,UAAW,UACX,SAAUmhB,CACZ,CAAC,CACH,CAAC,KAAgB,OAAK,OAAQ,CAC5B,UAAW,KAAW,GAAG,OAAOziB,EAAe,YAAY,GAAIgjB,EAAgBjW,EAAM,SAAW,MAAQiW,IAAkB,OAAS,OAASA,EAAc,UAAQ,KAAgB,CAAC,EAAG,GAAG,OAAOhjB,EAAe,qBAAqB,EAAGyjB,KAAYniB,GAAQmhB,EAAY,CAAC,EACxQ,SAAUc,CACZ,CAAC,CAAC,CACJ,EAAGN,CAAQ,EACPU,GAAY,GAAMV,CAAQ,EAG9B,GAAIU,GAAW,CACb,IAAIC,EAAeC,GAAeC,EAClCJ,MAA2B,QAAM,OAAQ,CACvC,QAAS,UAAmB,CAC1B,IAAIK,GAASC,IACZD,GAAU,UAAY,MAAQA,KAAY,SAAWC,GAAeD,GAAQ,QAAU,MAAQC,KAAiB,QAAUA,GAAa,KAAKD,GAASd,EAAU,QAAQ,CACzK,EACA,UAAW,KAAW,GAAG,OAAOjjB,EAAe,aAAa,GAAI4jB,EAAgB7W,EAAM,SAAW,MAAQ6W,IAAkB,OAAS,OAASA,EAAc,UAAQ,QAAgB,QAAgB,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAO5jB,EAAe,uBAAuB,EAAGuf,CAAS,EAAG,GAAG,OAAOvf,EAAe,8BAA8B,EAAE,OAAO2hB,CAAY,EAAGpC,CAAS,EAAG,GAAG,OAAOvf,EAAe,YAAY,EAAG,EAAI,EAAG,GAAG,OAAOA,EAAe,4BAA4B,GAAIqY,GAAS,KAA0B,OAASA,EAAK,qBAAuBkH,CAAS,CAAC,EAC3jB,SAAU,IAAc,OAAK,OAAQ,CACnC,UAAW,GAAG,OAAOvf,EAAe,aAAa,EAAE,QAAQ6jB,GAAgB9W,EAAM,SAAW,MAAQ8W,KAAkB,OAAS,OAASA,GAAc,MAAM,EAAE,KAAK,EACnK,MAAO,CACL,QAASpB,IAAgB,MAAQ,CAACnhB,EAAO,OAAS,EACpD,EACA,SAAUA,MAAqB,OAAK,OAAQ,CAC1C,UAAW,UACX,SAAUmhB,CACZ,CAAC,CACH,CAAC,KAAgB,OAAK,OAAQ,CAC5B,UAAW,KAAW,GAAG,OAAOziB,EAAe,YAAY,GAAI8jB,EAAgB/W,EAAM,SAAW,MAAQ+W,IAAkB,OAAS,OAASA,EAAc,UAAQ,KAAgB,CAAC,EAAG,GAAG,OAAO9jB,EAAe,qBAAqB,EAAGyjB,KAAYniB,GAAQmhB,EAAY,CAAC,EACxQ,SAAUc,CACZ,CAAC,CAAC,CACJ,EAAGN,CAAQ,CACb,CACA,GAAIK,EAAgB,CAClB,IAAIW,MAAkB,QAAc,KAAc,CAAC,EAAGrM,CAAI,EAAG,CAAC,EAAG,CAC/D,MAAO+L,GACP,SAAUV,EACV,SAAU3D,EACV,QAAS2D,IAAaG,EAAS,SAC/B,QAAS,UAAmB,CAC1B,OAAOC,GAAcA,EAAW,EAAI,CACtC,EACA,SAAU,MACZ,CAAC,EACD,OAAO3B,IAAU,KAAiB,OAAKT,GAAiB,CACtD,UAAW1B,EACX,MAAOgE,EACP,QAAS3L,EAAK,gBACd,SAAU0L,EAAeW,GAAiBP,GAAa3W,EAAM,KAAK,CACpE,CAAC,EAAIuW,EAAeW,GAAiBP,GAAa3W,EAAM,KAAK,CAC/D,CACA,OAAO2U,IAAU,KAAiB,OAAKT,GAAiB,CACtD,UAAW1B,EACX,MAAOgE,EACP,QAAS3L,EAAK,gBACd,SAAU8L,EACZ,CAAC,EAAIA,EACP,CAAC,KACD,KAAgB,KAAM,iBAAkB,SAAU5d,EAAM,CACtD,OAAIA,GAAQA,EAAK,QAAQ,MAAM,IAAM,EAC5BA,EAEF,IAAI,OAAOA,GAAQ,EAAE,EAAE,QAAQ,OAAQ,GAAG,CACnD,CAAC,EACD,KAAK,MAAQ7G,CACf,CAAC,EAMGilB,GAAmB,SAA0BC,EAAUzkB,EAAM,CAC/D,IAAIoiB,EAASpiB,EAAK,OAChB6f,EAAY7f,EAAK,UACf0kB,EAAgB,CAAC,EACrB,OAAID,GAAY,CAAC5E,GAAa,CAAC,OAAQ,KAAK,EAAE,SAASuC,GAAU,KAAK,IACpEsC,EAAgB,CACd,SAAUD,CACZ,GAEKC,CACT,EACIC,GAAW,SAAkBplB,EAAO,CACtC,IAAI6hB,EAAO7hB,EAAM,KACfU,EAAYV,EAAM,UAClBqlB,EAAmBrlB,EAAM,iBACzBa,EAAQb,EAAM,MACdib,EAAWjb,EAAM,SACjBM,EAAYN,EAAM,UAClBoZ,EAAOpZ,EAAM,KACbslB,EAAgBtlB,EAAM,cACtBulB,EAAcvlB,EAAM,YACpBwlB,EAAoBxlB,EAAM,aAC1BylB,EAAWzlB,EAAM,SACjB0lB,EAAiB1lB,EAAM,eACvB2lB,EAAgB3lB,EAAM,SACpBoF,KAAc,cAAW,IAAW,EACtCwgB,EAAOxgB,EAAY,KACnB2d,EAAc3d,EAAY,MACxBrE,EAAgB,GAAG,OAAOT,EAAW,aAAa,EAAE,OAAOuhB,CAAI,EAE/DgE,KAAqB,UAAO,CAAC,CAAC,EAC9BC,KAAsB,KAAmB1M,GAAS,KAA0B,OAASA,EAAK,cAAc,EAC1G2M,KAAuB,KAAeD,EAAqB,CAAC,EAC5DE,EAAiBD,EAAqB,CAAC,EACvCE,EAAoBF,EAAqB,CAAC,EACxCG,MAAuB,KAAmB,UAAY,CACtD,OAAI9M,GAAS,MAA2BA,EAAK,eACpCoE,GAAwBvC,CAAQ,GAAK,CAAC,EAE3C0K,IAAkB,GACb,GAEF,CAAC,CACV,EAAG,CACD,MAAOA,IAAkB,GAAQ,OAAYA,EAC7C,SAAUN,CACZ,CAAC,EACDc,KAAuB,KAAeD,GAAsB,CAAC,EAC7DhB,EAAWiB,EAAqB,CAAC,EACjCC,GAAcD,EAAqB,CAAC,EAClCE,MAAuB,KAAmB,CAAC,EAAG,CAC9C,MAAOb,EACP,SAAUC,EAAW,SAAUld,GAAM,CAC/Bkd,GAAYld,IACdkd,EAASld,EAAI,CAEjB,EAAI,MACN,CAAC,EACD+d,KAAuB,KAAeD,GAAsB,CAAC,EAC7DE,GAAeD,EAAqB,CAAC,EACrCE,EAAkBF,EAAqB,CAAC,KAC1C,aAAU,UAAY,CAChBlN,GAAS,MAA2BA,EAAK,gBAAkBuM,IAAkB,IAG7EL,IACFc,GAAYd,CAAa,EACzBkB,EAAgBlB,CAAa,EAGjC,EAAG,CAACA,EAAc,KAAK,GAAG,CAAC,CAAC,KAC5B,aAAU,UAAY,CAEhBC,IACFnD,GAAWf,GAAqB,CAC9B,UAAWkE,CACb,CAAC,EAEL,EAAG,CAACA,CAAW,CAAC,KAChB,aAAU,UAAY,CAKpB,GAHID,EAAc,KAAK,GAAG,KAAOiB,IAAgB,CAAC,GAAG,KAAK,GAAG,GAC3DC,EAAgBlB,CAAa,EAE3B,CAACU,GAAkBL,IAAkB,IAASL,EAAc,KAAK,GAAG,KAAOJ,GAAY,CAAC,GAAG,KAAK,GAAG,EAAG,CACxG,IAAIuB,GAAUnB,GAETlM,GAAS,KAA0B,OAASA,EAAK,aAAe,KACnEqN,GAAU,MAAM,KAAK,IAAI,IAAI,CAAC,EAAE,UAAO,MAAmBnB,CAAa,KAAG,MAAmBJ,GAAY,CAAC,CAAC,CAAC,CAAC,CAAC,GAEhHkB,GAAYK,EAAO,CACrB,MAAWrN,GAAS,MAA2BA,EAAK,gBAAkB4M,EAEpEI,GAAY5I,GAAwBvC,CAAQ,CAAC,EAE7CgL,EAAkB,EAAK,CAE3B,EAEA,CAACX,EAAc,KAAK,GAAG,CAAC,CAAC,EACzB,IAAIH,MAAgB,WAAQ,UAAY,CACtC,OAAOF,GAAiBC,EAAUllB,CAAK,CACzC,EAEA,CAACklB,GAAYA,EAAS,KAAK,GAAG,EAAGllB,EAAM,OAAQA,EAAM,SAAS,CAAC,EAC3DgB,GAAY,GAASD,EAAe8gB,CAAI,EAC1C5gB,GAAUD,GAAU,QACpBE,GAASF,GAAU,OACjB0lB,MAAY,WAAQ,UAAY,CAClC,OAAO,IAAIlE,MAAS,QAAc,KAAc,CAAC,EAAGxiB,CAAK,EAAG,CAAC,EAAG,CAC9D,MAAO+iB,EACP,eAAgB2C,EAChB,cAAe3kB,EACf,OAAQG,EACV,CAAC,CAAC,CACJ,EAAG,CAAClB,EAAO+iB,EAAa2C,EAAgB3kB,EAAeG,EAAM,CAAC,EAC9D,GAAIkY,GAAS,MAA2BA,EAAK,QAC3C,SAAoB,OAAK,MAAO,CAC9B,MAAOyI,GAAS,MAA2BA,EAAK,SAAS,QAAQ,EAAI,CACnE,QAAS,EACX,EAAI,CACF,iBAAkB,EACpB,EACA,YAAuB,OAAK,KAAU,CACpC,OAAQ,GACR,MAAO,GACP,UAAW,CACT,KAAMA,GAAS,MAA2BA,EAAK,SAAS,QAAQ,EAAI,EAAI,CAC1E,CACF,CAAC,CACH,CAAC,EAMC7hB,EAAM,WAAa,IAAS,CAACA,EAAM,mBACrC6lB,EAAmB,QAAUP,GAE/B,IAAIqB,GAAc3mB,EAAM,aAAeA,EAAM,aAAaib,CAAQ,EAAIA,EACtE,OAAI0L,KAAgBA,IAAgB,KAAiC,OAASA,GAAY,QAAU,EAC3F,KAEF1lB,MAAsB,iBAAe,QAAM,QAAc,KAAc,CAAC,EAAGkkB,EAAa,EAAG,CAAC,EAAG,CACpG,qCAAsC,GACtC,IAAK,OACL,KAAMtD,EACN,aAAc,GACd,gBAAiBgE,EAAmB,QACpC,MAAOD,EAAO,OAAS,QACvB,aAAcW,GACd,SAAO,KAAc,CACnB,gBAAiB,cACjB,OAAQ,MACV,EAAG1lB,CAAK,EACR,UAAW,KAAWH,EAAWQ,GAAQH,KAAe,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAe,aAAa,EAAG8gB,IAAS,YAAY,EAAG,GAAG,OAAO9gB,EAAe,YAAY,EAAGf,EAAM,SAAS,CAAC,EACrN,MAAO0mB,GAAU,gBAAgBC,GAAa,EAAG,CAAC,EAClD,aAAc,SAAsBC,GAAW,CACxC5mB,EAAM,WACTomB,GAAYQ,EAAS,CAEzB,CACF,EAAG5mB,EAAM,SAAS,CAAC,CAAC,CACtB,EC/cO,SAAS6mB,GAAWvmB,EAAWG,EAAM,CAC1C,IAAIqmB,EAAUrmB,EAAK,QACjBsmB,EAA0BtmB,EAAK,wBACjC,SAAO,OAAa,4BAA6B,SAAUJ,EAAO,CAChE,IAAI8f,KAAiB,QAAc,KAAc,CAAC,EAAG9f,CAAK,EAAG,CAAC,EAAG,CAC/D,aAAc,IAAI,OAAOC,CAAS,EAClC,wBAAyBymB,CAC3B,CAAC,EACD,OAAKD,EACE,IAAC,KAAgB,CAAC,EAAG,MAAM,OAAOzmB,EAAM,iBAAkB,SAAS,KAAG,KAAgB,CAAC,EAAG,GAAG,OAAO8f,EAAe,YAAY,EAAG2G,GAAY,KAA6B,OAASA,EAAQ3G,CAAc,CAAC,CAAC,CAAC,EAD/L,CAAC,CAExB,CAAC,CACH,CCXA,IAAI,GAAY,CAAC,QAAS,QAAQ,EAa9B6G,GAAsC,OAAW,SAAUhnB,EAAO,CAIpE,SAAoB,OAAK,WAAW,CAClC,SAAUA,EAAM,QAClB,CAAC,CACH,CAAC,EACGinB,GAAQ,KAAO,MACjBC,GAAwB,KAAO,sBAC/BC,GAAeD,KAA0B,OAAS,CAChD,SAAUF,EACZ,EAAIE,GAQKE,GAAqB,SAA4BpnB,EAAO,CACjE,IAAIqnB,EAAY,UAAU,OAAS,GAAK,UAAU,CAAC,IAAM,OAAY,UAAU,CAAC,EAAI,mBAChF5I,EAAOze,EAAM,KACf0e,EAAQ1e,EAAM,MACd6iB,EAAS7iB,EAAM,OACbsnB,EAAiBtnB,EAAMqnB,CAAS,EACpC,GAAIC,IAAmB,GACrB,OAAO,KAET,IAAIC,EAAUhJ,GAAkBE,CAAI,EAChC+I,KAAwB,OAAK,KAAM,CACrC,SAAU9I,GAAU,KAA2BA,EAAQ,gBACzD,CAAC,EACD,OAAI4I,EAEKA,EAAeC,EAASvnB,EAAM,UAAY,KAAOwnB,EAAUxnB,CAAK,EAMrEA,EAAM,SACD,KAEL6iB,IAAW,OAASwE,IAAc,mBAA2B,GAC7DrnB,EAAM,aACY,OAAK,IAAK,CAC5B,SAAUunB,CACZ,EAAG,OAAO,KAEQ,QAAM,IAAK,CAC7B,SAAU,CAACA,EAASC,CAAQ,CAC9B,EAAG,OAAO,CACZ,EACIC,GAAY,SAAmBznB,EAAO,CACxC,IAAI0nB,EACApH,EAAYtgB,EAAM,UACpB2nB,EAAkB3nB,EAAM,gBACxB4nB,EAAc5nB,EAAM,YACpB6nB,EAAmB7nB,EAAM,iBACzB8nB,EAAc9nB,EAAM,WACpB+nB,EAAQ/nB,EAAM,MACdgoB,EAAahoB,EAAM,WACnBqgB,EAAWrgB,EAAM,SACjBioB,EAAoBjoB,EAAM,kBAC1BkoB,EAAoBloB,EAAM,WAC1BmoB,EAAaD,IAAsB,OAAS,KAAOA,EACnDrnB,EAAQb,EAAM,MACd6iB,EAAS7iB,EAAM,OACfooB,EAAwBpoB,EAAM,gBAC9BqoB,EAAkBD,IAA0B,OAAS,GAAQA,EAC7DznB,EAAQX,EAAM,MACdsoB,EAAoBtoB,EAAM,kBAC1BuoB,EAAwBvoB,EAAM,sBAC9BM,EAAYN,EAAM,UAClBwoB,EAAcxoB,EAAM,YACpByoB,EAAqBzoB,EAAM,mBAC3B0oB,GAAgB1oB,EAAM,cACtB0N,EAAe1N,EAAM,aACrB8mB,EAAU9mB,EAAM,QAChB2oB,GAAY3oB,EAAM,UAChBoF,MAAc,cAAW,IAAW,EACtClE,EAASkE,GAAY,OACnBwjB,MAAoB,WAAQ,UAAY,CAE1C,MADI,KACA/F,IAAW,MAEjB,EAAG,CAACxC,EAAUwC,CAAM,CAAC,EACjB9hB,EAAgB,GAAG,OAAOT,EAAW,QAAQ,EAG7CuoB,GAAiB,GAGjBC,GAAmBjC,GAAW,GAAG,OAAO9lB,EAAe,GAAG,EAAE,OAAOA,EAAe,UAAU,EAAG,CACjG,QAAS+lB,EACT,wBAAyB+B,EAC3B,CAAC,EACGE,GAAiB,KAAW,GAAG,OAAOhoB,CAAa,EAAGG,KAAQ,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOH,EAAe,QAAQ,EAAG6mB,CAAW,EAAG,GAAG,OAAO7mB,EAAe,YAAY,EAAG8hB,IAAW,OAAS,CAACxC,GAAYuH,CAAW,EAAG,GAAG,OAAO7mB,EAAe,YAAY,EAAGf,EAAM,SAAS,EAAG,GAAG,OAAOe,EAAe,UAAU,EAAE,OAAO8hB,CAAM,EAAGA,GAAU,CAACxC,CAAQ,EAAG,GAAG,OAAOtf,EAAe,QAAQ,EAAGgnB,IAAU,MAAM,EAAG,GAAG,OAAOhnB,EAAe,MAAM,EAAG8hB,IAAW,OAAS,CAACxC,CAAQ,EAAG,GAAG,OAAOtf,EAAe,UAAU,EAAG,CAAC,CAAC+lB,CAAO,CAAC,EACzmBkC,GAAY5B,GAAmBpnB,CAAK,EACpCipB,GAAWZ,GAAmBA,EAAgBroB,CAAK,EACnDkpB,MAAU,WAAQ,UAAY,CAChC,OAAOZ,IAAsB,OAAsB,iBAAelD,MAAU,QAAc,KAAc,CAAC,EAAGplB,CAAK,EAAG,CAAC,EAAG,CACtH,IAAK,YACL,KAAMsgB,GAAa,CAACD,EAAW,WAAa,SAC5C,iBAAkB3S,EAClB,MAAO,CACL,MAAO,MACT,EACA,UAAW,GAAG,OAAO3M,EAAe,QAAQ,EAAE,OAAOG,CAAM,EAAE,KAAK,CACpE,CAAC,CAAC,CACJ,EAAG,CAACH,EAAeG,EAAQonB,EAAmB5a,EAAc1N,CAAK,CAAC,EAC9DmpB,IAAkBxoB,GAAS,CAAC,GAAG,IAAI,SAAU+D,GAAMG,GAAO,CAC5D,MAAO,CACL,UAAW,GAAG,OAAO9D,EAAe,OAAO,EAC3C,MAAO2D,GACP,IAAKG,EACP,CACF,CAAC,EACGukB,MAAgB,WAAQ,UAAY,CACtC,OAAOd,EAAoBA,EAAkBtoB,EAAOkpB,EAAO,EAAIA,EACjE,EAAG,CAACZ,EAAmBY,GAASlpB,CAAK,CAAC,EAClCqpB,MAAY,WAAQ,UAAY,CAClC,GAAI,CAACb,EAAa,OAAO,KACzB,IAAI9J,GAAQ8J,EAAY,MACtBc,GAASd,EAAY,OACrBtZ,MAAO,MAAyBsZ,EAAa,EAAS,EACpDe,MAAmB,QAAM,MAAO,CAClC,UAAW,GAAG,OAAOxoB,EAAe,iBAAiB,EACrD,SAAU,CAACmO,IAAS,MAA2BA,GAAK,KAAOA,IAAS,MAA2BA,GAAK,QAAUA,GAAK,MAAQA,GAAK,YAAwB,OAAK,QAAQ,KAAc,CACjL,KAAM,EACR,EAAGA,EAAI,CAAC,EAAI,KAAMsZ,EAAY,OAAS,CAAClI,MAA0B,OAAK,OAAQ,CAC7E,SAAU5B,EACZ,CAAC,CAAC,CACJ,CAAC,EACD,OAAI4K,GACKA,GAAOd,EAAae,GAAKvpB,CAAK,EAEhCupB,EACT,EAAG,CAACf,EAAaznB,EAAeuf,CAAS,CAAC,EACtCkJ,MAAa,WAAQ,UAAY,CACnC,OAAKd,MACe,OAAK,KAAO,CAC9B,MAAO,SACP,KAAM,EACN,UAAWpI,EAAY,WAAa,aACpC,UAAW,KAAW,CAAC,GAAG,OAAOvf,EAAe,eAAe,EAAGuf,GAAa,GAAG,OAAOvf,EAAe,yBAAyB,EAAGG,CAAM,CAAC,EAC3I,SAAU,CAACwnB,IAAkB,KAAmC,OAASA,GAAc1oB,CAAK,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,SAAU2Y,GAAM9T,GAAO,CAChI,SAAoB,OAAK,MAAO,CAC9B,UAAW,GAAG,OAAO9D,EAAe,qBAAqB,EAAE,OAAOG,CAAM,EAAE,KAAK,EAC/E,SAAUyX,EACZ,EAAG9T,EAAK,CACV,CAAC,CACH,CAAC,EAZ0B,IAa7B,EAEA,CAAC6jB,GAAe3nB,EAAeuf,CAAS,CAAC,EACrCmJ,MAAU,WAAQ,UAAY,CAChC,SAAoB,OAAKpK,GAAoB,CAC3C,YAAarf,EAAM,UACnB,cAAeA,EAAM,cACrB,QAASA,EAAM,QACf,UAAWA,EAAM,SACnB,CAAC,CACH,EAAG,CAACA,EAAM,QAASA,EAAM,cAAeA,EAAM,SAAS,CAAC,EACpD0pB,MAAe,WAAQ,UAAY,CACrC,GAAInB,IAA0B,GAAO,OAAO,KAC5C,IAAIgB,MAAmB,OAAKnJ,GAAe,CACzC,SAAUC,EACV,UAAWsH,EACX,UAAW,GAAG,OAAO5mB,EAAe,mBAAmB,EACvD,QAAS,UAAmB,CAC1B+mB,GAAgB,MAAkCA,EAAY,CAACH,CAAe,CAChF,CACF,CAAC,EACD,OAAIY,EAA8BA,EAAsBjI,EAAWiJ,EAAG,EAC/DA,EACT,EAAG,CAAChB,EAAuBlI,EAAUsH,EAAiB5mB,EAAeuf,EAAWwH,CAAW,CAAC,EAGxF6B,MAAgB,WAAQ,UAAY,CACtC,MAAI,CAACN,IAAa,CAACG,GAAmB,QAClB,QAAM,MAAO,CAC/B,UAAW,KAAW,GAAG,OAAOzoB,EAAe,UAAU,EAAGG,EAAQof,GAAa,GAAG,OAAOvf,EAAe,oBAAoB,CAAC,EAC/H,SAAU,CAACsoB,GAAWG,EAAU,CAClC,CAAC,CACH,EAAG,CAACA,GAAYH,GAAWtoB,EAAeuf,EAAWpf,CAAM,CAAC,EAGxD0oB,MAAiC,WAAQ,UAAY,CACvD,IAAIC,GAEJ,OAAI7pB,GAAU,OAA6B6pB,GAAc7pB,EAAM,QAAU,MAAQ6pB,KAAgB,QAAUA,GAAY,uBAAyBvJ,EACvI,GAAG,OAAOvf,EAAe,sBAAsB,EAEjD,IACT,EAAG,CAACA,EAAeuf,EAAWtgB,GAAU,OAA6B0nB,EAAe1nB,EAAM,QAAU,MAAQ0nB,IAAiB,OAAS,OAASA,EAAa,qBAAqB,CAAC,EAC9KoC,GAAgBjC,IAAqBA,GAAqB,KAAsC,OAASA,EAAiB7nB,CAAK,GAC/H+pB,MAA4B,QAAM,WAAW,CAC/C,SAAU,CAACf,OAA0B,QAAM,MAAO,CAChD,UAAW,KAAW,CAAC,KAAW,GAAG,OAAOjoB,EAAe,OAAO,EAAGG,KAAQ,KAAgB,CAAC,EAAG,GAAG,OAAOH,EAAe,iBAAiB,EAAGuf,CAAS,CAAC,CAAC,CAAC,EAC1J,QAASsI,GAAoBX,EAAoB,OACjD,GAAI,OACJ,MAAOU,GACP,SAAU,CAACK,GAAWS,EAAO,CAC/B,CAAC,EAAGR,OAAyB,OAAK,MAAO,CACvC,UAAW,KAAW,CAAC,GAAG,OAAOloB,EAAe,QAAQ,EAAG,CAACioB,IAAa,GAAG,OAAOjoB,EAAe,gBAAgB,EAAGG,CAAM,CAAC,EAC5H,SAAU+nB,EACZ,CAAC,KAAgB,OAAK,MAAO,CAC3B,MAAO,CACL,KAAM,EACN,UAAW,OACX,UAAW,QACb,EACA,SAAUG,EACZ,CAAC,KAAgB,QAAMjC,GAAa,SAAU,CAC5C,MAAO,CAAC,EACR,SAAU,CAACxmB,KAAqB,OAAK,MAAO,CAC1C,UAAW,GAAG,OAAOI,EAAe,SAAS,EAAE,OAAOG,CAAM,EAAE,KAAK,EACnE,YAAuB,OAAK,KAAM,CAChC,aAAc,GACd,UAAW,GAAG,OAAOH,EAAe,aAAa,EAAE,OAAOG,CAAM,EAAE,KAAK,EACvE,aAAc,CAAC,EACf,SAAU,CAAC,EACX,MAAO6mB,EACP,KAAM,SACN,MAAOoB,EACT,CAAC,CACH,CAAC,EAAI,KAAMP,OAAkC,QAAM,WAAW,CAC5D,SAAU,CAACe,GAAe,CAACH,IAAcf,KAAkC,OAAK,MAAO,CACrF,UAAW,KAAW,GAAG,OAAO1nB,EAAe,UAAU,EAAGG,KAAQ,KAAgB,CAAC,EAAG,GAAG,OAAOH,EAAe,oBAAoB,EAAGuf,CAAS,CAAC,EAClJ,SAAUmI,GAAuB,KAAwC,OAASA,EAAmBzoB,CAAK,CAC5G,CAAC,EAAI,IAAI,CACX,CAAC,EAAG8pB,OAA8B,OAAK,MAAO,CAC5C,UAAW,KAAW,CAAC,GAAG,OAAO/oB,EAAe,SAAS,EAAGG,KAAQ,KAAgB,CAAC,EAAG,GAAG,OAAOH,EAAe,mBAAmB,EAAGuf,CAAS,CAAC,CAAC,EAClJ,SAAUwJ,EACZ,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,EACD,OAAOhB,GAAiB,WAAsB,QAAM,WAAW,CAC7D,SAAU,CAAClB,GAAe,CAACvH,GAAY,CAACuJ,OAA+C,OAAK,MAAO,CACjG,SAAO,KAAc,CACnB,MAAOtJ,EAAYuI,GAAiBb,EACpC,SAAU,SACV,KAAM,OAAO,OAAO1H,EAAYuI,GAAiBb,EAAY,IAAI,EACjE,SAAU1H,EAAYuI,GAAiBb,EACvC,SAAU1H,EAAYuI,GAAiBb,EACvC,WAAY,kBACd,EAAGnnB,CAAK,CACV,CAAC,KAAgB,QAAMomB,GAAO,CAC5B,YAAa,GACb,QAAS,KACT,UAAW3G,EACX,WAAY6H,IAAe,GAAQ,OAAYA,EAC/C,WAAY,SAAoB6B,GAAU,CACpC3J,GACJyH,GAAgB,MAAkCA,EAAYkC,EAAQ,CACxE,EACA,eAAgBnB,GAChB,MAAOhoB,EACP,MAAOknB,EACP,MAAOC,EACP,UAAW,KAAWe,GAAgB7nB,EAAQ0oB,EAA8B,EAC5E,SAAU,CAACA,MAA8C,OAAK,MAAO,CACnE,UAAW,GAAG,OAAO7oB,EAAe,uBAAuB,EAAE,OAAOG,CAAM,EAAE,KAAK,EACjF,MAAO,CACL,OAAQ,OACR,MAAO,OACP,QAAS0oB,GAAiC,EAAI,CAChD,EACA,SAAUG,EACZ,CAAC,EAAIA,GAAcL,EAAY,CACjC,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,E,uBC/RIO,GAAuB,SAA8B5pB,EAAO,CAC9D,IAAI2e,EAAeC,EAAgBC,EAAgBC,EAAgBC,EACnE,SAAO,KAAgB,CAAC,EAAG/e,EAAM,aAAc,CAC7C,mBAAoB,CAClB,QAAS,OACT,OAAQ,OACR,WAAY,SACZ,SAAU,CACR,QAAS,cACT,WAAY,SACZ,eAAgB,SAChB,aAAc,EACd,cAAe,EACf,OAAQ2e,EAAgB3e,EAAM,UAAY,MAAQ2e,IAAkB,SAAWA,EAAgBA,EAAc,UAAY,MAAQA,IAAkB,OAAS,OAASA,EAAc,0BACnL,SAAU,OACV,OAAQ,UACR,aAAc3e,EAAM,aACpB,MAAO,CACL,cAAe,EACf,aAAc,EACd,aAAcA,EAAM,aACpB,UAAW,CACT,iBAAkB4e,EAAiB5e,EAAM,UAAY,MAAQ4e,IAAmB,SAAWA,EAAiBA,EAAe,UAAY,MAAQA,IAAmB,OAAS,OAASA,EAAe,4BACrM,CACF,CACF,EACA,WAAY,CACV,QAAS,cACT,WAAY,SACZ,eAAgB,SAChB,mBAAoB5e,EAAM,QAC1B,iBAAkBA,EAAM,QACxB,OAAQ,UACR,OAAQ6e,EAAiB7e,EAAM,UAAY,MAAQ6e,IAAmB,SAAWA,EAAiBA,EAAe,UAAY,MAAQA,IAAmB,OAAS,OAASA,EAAe,0BACzL,QAAS,CACP,OAAQ,OACR,OAAQC,EAAiB9e,EAAM,UAAY,MAAQ8e,IAAmB,SAAWA,EAAiBA,EAAe,UAAY,MAAQA,IAAmB,OAAS,OAASA,EAAe,0BACzL,cAAe,EACf,aAAc,EACd,OAAQ,UACR,QAAS,OACT,WAAY,SACZ,WAAY,OACZ,aAAc9e,EAAM,aACpB,UAAW,CACT,iBAAkB+e,EAAiB/e,EAAM,UAAY,MAAQ+e,IAAmB,SAAWA,EAAiBA,EAAe,UAAY,MAAQA,IAAmB,OAAS,OAASA,EAAe,4BACrM,CACF,CACF,CACF,CACF,CAAC,CACH,EACO,SAAS,GAAS9e,EAAW,CAClC,SAAO,OAAa,wBAAyB,SAAUD,EAAO,CAC5D,IAAI6pB,KAAW,QAAc,KAAc,CAAC,EAAG7pB,CAAK,EAAG,CAAC,EAAG,CACzD,aAAc,IAAI,OAAOC,CAAS,CACpC,CAAC,EACD,MAAO,CAAC2pB,GAAqBC,CAAQ,CAAC,CACxC,CAAC,CACH,CCxDA,IAAI,GAAY,CAAC,qBAAsB,cAAe,gBAAiB,qBAAqB,EAC1FC,GAAa,CAAC,QAAS,QAAQ,EAetBC,GAAiB,SAAwB3pB,EAAM,CACxD,IAAIgoB,EAAqBhoB,EAAK,mBAC5B+nB,EAAc/nB,EAAK,YACnBioB,EAAgBjoB,EAAK,cACrB4pB,EAAsB5pB,EAAK,oBAC3BT,KAAQ,MAAyBS,EAAM,EAAS,EAC9C2E,KAAc,cAAW,mBAA4B,EACvDklB,EAAellB,EAAY,aACzB9E,EAAY,GAAG,OAAOgqB,EAAa,EAAG,oBAAoB,EAC1DtpB,EAAY,GAASV,CAAS,EAChCW,EAAUD,EAAU,QACpBE,EAASF,EAAU,OACjB0O,KAAY,YAAS,MAAM,EAC7BC,KAAa,KAAeD,EAAW,CAAC,EACxC6a,EAAY5a,EAAW,CAAC,EACxB6a,EAAe7a,EAAW,CAAC,EACzB0Z,KAAY,WAAQ,UAAY,CAClC,GAAI,CAACb,EAAa,OAAO,KACzB,IAAI9J,EAAQ8J,EAAY,MACtBc,EAASd,EAAY,OACrBtZ,KAAO,MAAyBsZ,EAAa2B,EAAU,EACrDM,GAAU,CAACvb,GAAS,MAA2BA,EAAK,KAAOA,GAAS,MAA2BA,EAAK,QAAUA,EAAK,MAAQA,EAAK,YAAwB,iBAAe,QAAQ,QAAc,KAAc,CAAC,EAAGA,CAAI,EAAG,CAAC,EAAG,CAC5N,KAAM,GACN,IAAK,QACP,CAAC,CAAC,EAAI,KAAMwP,KAAqB,OAAK,OAAQ,CAC5C,MAAO,CACL,kBAAmB,CACrB,EACA,SAAUA,CACZ,EAAG,MAAM,EAAI,MAAS,EACtB,OAAI4K,EACKA,EAAOd,KAA0B,OAAK,MAAO,CAClD,SAAUiC,EACZ,CAAC,EAAGzqB,CAAK,KAES,OAAK,MAAO,CAC9B,SAAUyqB,EACZ,CAAC,CACH,EAAG,CAACjC,CAAW,CAAC,EACZkC,EAAqBhC,GAAiBW,EAAY,SAAUsB,EAAY,CAC1E,IAAIC,EAAOlC,IAAkBA,GAAkB,KAAmC,OAASA,EAAciC,CAAU,GACnH,MAAI,CAACC,GAAQ,CAACvB,EAAkB,KAC3B,MAAM,QAAQuB,CAAI,EAOhB3pB,KAAsB,QAAM,MAAO,CACxC,UAAW,GAAG,OAAOX,EAAW,kBAAkB,EAAE,OAAOY,CAAM,EAAE,KAAK,EACxE,SAAU,CAAC0pB,EAAK,OAAO,OAAO,EAAE,IAAI,SAAUrB,EAAK1kB,GAAO,CACxD,IAAIgmB,EAAY,GAEhB,GAAkB,iBAAqBtB,CAAG,EAAG,CAC3C,IAAIuB,EACJD,EAAY,CAAC,EAAEtB,GAAQ,OAA2BuB,EAAavB,EAAI,SAAW,MAAQuB,IAAe,QAAUA,EAAW,aAAa,EACzI,CACA,SAAoB,OAAK,MAAO,CAC9B,UAAW,KAAW,GAAG,OAAOxqB,EAAW,uBAAuB,EAAE,OAAOY,CAAM,KAAG,KAAgB,CAAC,EAAG,GAAG,OAAOZ,EAAW,uBAAuB,EAAG,CAACuqB,CAAS,CAAC,EAClK,SAAUtB,CACZ,EAAG1kB,EAAK,CACV,CAAC,EAAGwkB,MAA0B,OAAK,OAAQ,CACzC,UAAW,GAAG,OAAO/oB,EAAW,yBAAyB,EAAE,OAAOY,CAAM,EAAE,KAAK,EAC/E,SAAUmoB,CACZ,CAAC,CAAC,CACJ,CAAC,CAAC,EAxB+BpoB,KAAsB,QAAM,MAAO,CAClE,UAAW,GAAG,OAAOX,EAAW,kBAAkB,EAAE,OAAOY,CAAM,EAAE,KAAK,EACxE,SAAU,CAAC0pB,EAAMvB,MAA0B,OAAK,OAAQ,CACtD,UAAW,GAAG,OAAO/oB,EAAW,yBAAyB,EAAE,OAAOY,CAAM,EAAE,KAAK,EAC/E,SAAUmoB,CACZ,CAAC,CAAC,CACJ,CAAC,CAAC,CAmBJ,EAAI,OAEA0B,KAAyB5c,MAA4B,UAAY,CACnE,IAAI6c,KAAQ,SAAgC,MAAoB,EAAE,KAAK,SAASnc,EAAQoc,EAAO,CAC7F,SAAO,MAAoB,EAAE,KAAK,SAAkBnc,EAAU,CAC5D,OAAU,OAAQA,EAAS,KAAOA,EAAS,KAAM,CAC/C,IAAK,GACH0b,EAAaS,CAAK,EACpB,IAAK,GACL,IAAK,MACH,OAAOnc,EAAS,KAAK,CACzB,CACF,EAAGD,CAAO,CACZ,CAAC,CAAC,EACF,OAAO,SAAUqc,EAAI,CACnB,OAAOF,EAAM,MAAM,KAAM,SAAS,CACpC,CACF,EAAE,EAAG,GAAG,EACJG,EAAgBT,GAAsBjC,EAC1C,SAAoB,OAAK,MAAO,CAC9B,UAAW,GAAG,OAAOnoB,EAAW,iBAAiB,EAAE,OAAOY,CAAM,EAAE,KAAK,EACvE,MAAO,CACL,SAAUqpB,EACV,OAAQ,MACV,EACA,YAAuB,OAAK,MAAO,CACjC,MAAO,CACL,OAAQ,MACV,EACA,YAAuB,OAAK,WAAgB,CAC1C,SAAU,SAAkBa,EAAO,CACjC,IAAIH,EAAQG,EAAM,MAClBL,EAAuB,IAAIE,CAAK,CAClC,EACA,SAAUE,KAA6B,OAAK,MAAO,CACjD,MAAO,CACL,QAAS,OACT,WAAY,SACZ,OAAQ,OACR,eAAgB,UAClB,EACA,SAAUA,KAAc,QAAc,KAAc,CAAC,EAAGnrB,CAAK,EAAG,CAAC,EAAG,CAGlE,iBAAkBuqB,CACpB,CAAC,CAAC,CACJ,CAAC,EAAI,IACP,CAAC,CACH,CAAC,CACH,CAAC,CACH,ECxII,GAAuB,SAA8BlqB,EAAO,CAC9D,IAAI2e,EAAeC,EACnB,SAAO,KAAgB,CAAC,EAAG5e,EAAM,aAAc,CAC7C,SAAU,WACV,MAAO,OACP,OAAQ,OACR,gBAAiB,cACjB,WAAY,CACV,MAAO,SACT,EACA,SAAU,CACR,QAAS,OACT,OAAQ,OACR,mBAAoB,OACpB,YAAU,KAAgB,CACxB,QAAS,OACT,WAAY,QACd,EAAG,GAAG,OAAOA,EAAM,iBAAkB,mBAAmB,EAAG,CACzD,gBAAiB,GACjB,kBAAmB,EACrB,CAAC,CACH,EACA,SAAU,CACR,SAAU,KACV,OAAQ,QACV,EACA,SAAU,CACR,SAAU,WACV,QAAS,OACT,OAAQ,OACR,WAAY,SACZ,SAAU,SACV,kBAAmB,CACjB,QAAS,OACT,WAAY,SACZ,UAAW,OACX,SAAU,MACZ,EACA,wBAAyB,CACvB,QAAS,eACT,OAAQ,OACR,cAAe,QACjB,EACA,uBAAwB,CACtB,QAAS,eACT,YAAa,EACb,aAAc,EACd,WAAY,OACZ,kBAAmB,EACnB,WAAY,MACZ,SAAU,OACV,OAAQ2e,EAAgB3e,EAAM,UAAY,MAAQ2e,IAAkB,SAAWA,EAAgBA,EAAc,UAAY,MAAQA,IAAkB,OAAS,OAASA,EAAc,iBACnL,cAAe,KACjB,CACF,EACA,SAAU,CACR,SAAU,EACV,QAAS,OACT,WAAY,SACZ,cAAe,EACf,aAAc,EACd,WAAY,GAAG,OAAO,KAAK,OAAOC,EAAiB5e,EAAM,UAAY,MAAQ4e,IAAmB,SAAWA,EAAiBA,EAAe,UAAY,MAAQA,IAAmB,OAAS,OAASA,EAAe,qBAAuB,IAAM,GAAI,EAAE,EAAG,IAAI,CAC/P,CACF,CAAC,CACH,EACO,SAAS,GAAS3e,EAAW,CAClC,SAAO,OAAa,wBAAyB,SAAUD,EAAO,CAC5D,IAAIgrB,KAAoB,QAAc,KAAc,CAAC,EAAGhrB,CAAK,EAAG,CAAC,EAAG,CAClE,aAAc,IAAI,OAAOC,CAAS,CACpC,CAAC,EACD,MAAO,CAAC,GAAqB+qB,CAAiB,CAAC,CACjD,CAAC,CACH,CC7DA,IAAIC,GAAe,SAAsBtrB,EAAO,CAC9C,IAAIurB,EAAiBC,EAAiBC,EAAiBC,EAAiBC,EAAiBC,EAAiBC,EACtG5rB,KAAM,UAAO,IAAI,EACjBgoB,EAAoBjoB,EAAM,kBAC5B8rB,EAAe9rB,EAAM,aACrByoB,EAAqBzoB,EAAM,mBAC3B+rB,EAAiB/rB,EAAM,UACvBa,EAAQb,EAAM,MACdqqB,EAAsBrqB,EAAM,oBAC5B6iB,EAAS7iB,EAAM,OACf0oB,EAAgB1oB,EAAM,cACpBoF,KAAc,cAAW,mBAA4B,EACvDklB,EAAellB,EAAY,aACzB4mB,KAAe,cAAW,IAAW,EACvCpG,EAAOoG,EAAa,KAClB1rB,EAAY,GAAG,OAAON,EAAM,WAAasqB,EAAa,KAAK,EAAG,iBAAiB,EAC/EtpB,EAAY,GAASV,CAAS,EAChCW,GAAUD,EAAU,QACpBE,EAASF,EAAU,OACjBqmB,EAAY,OACZrnB,EAAM,mBAAqB,OAC7BqnB,EAAY,oBACHxE,IAAW,OAASA,IAAW,SACxCwE,EAAY,qBAEd,IAAI2B,GAAY5B,MAAmB,QAAc,KAAc,CAAC,EAAGpnB,CAAK,EAAG,CAAC,EAAG,CAC7E,UAAW,EACb,CAAC,EAAGqnB,CAAS,EACT4E,MAAe,cAAW,IAAW,EACvC5rB,EAAQ4rB,GAAa,MACnBC,MAAa,WAAQ,UAAY,CACnC,IAAIlN,EAAeC,GAAgBC,GAAgBC,GAAgBC,GAAgB+M,GAAgBC,GAAgBC,GAAgBC,GAAgBC,GAAiBC,GAAiBC,GAAiBC,GAClMC,MAA0B,OAAK,MACjC,CACA,MAAO,CACL,UAAQ,MAAe,EACvB,WAAY,CACV,OAAQ,CACN,SAAU,cACV,OAAQ,aACV,EACA,QAAM,KAAc,CAAC,EAAGzb,EAAgB,CACtC,cAAe8N,EAAgB3e,EAAM,UAAY,MAAQ2e,IAAkB,SAAWA,EAAgBA,EAAc,UAAY,MAAQA,IAAkB,OAAS,OAASA,EAAc,gBAAkB,cAC5M,iBAAkBC,GAAiB5e,EAAM,UAAY,MAAQ4e,KAAmB,SAAWA,GAAiBA,GAAe,UAAY,MAAQA,KAAmB,OAAS,OAASA,GAAe,gBAAkB,cACrN,WAAY5e,EAAM,aAClB,sBAAuB6e,GAAiB7e,EAAM,UAAY,MAAQ6e,KAAmB,SAAWA,GAAiBA,GAAe,UAAY,MAAQA,KAAmB,OAAS,OAASA,GAAe,2BAA6B7e,GAAU,KAA2B,OAASA,EAAM,kBACzR,cAAe8e,GAAiB9e,EAAM,UAAY,MAAQ8e,KAAmB,SAAWA,GAAiBA,GAAe,UAAY,MAAQA,KAAmB,OAAS,OAASA,GAAe,wBAA0B9e,GAAU,KAA2B,OAASA,EAAM,kBAC9Q,gCAAiC+e,GAAiB/e,EAAM,UAAY,MAAQ+e,KAAmB,SAAWA,GAAiBA,GAAe,UAAY,MAAQA,KAAmB,OAAS,OAASA,GAAe,2BAA6B/e,GAAU,KAA2B,OAASA,EAAM,kBACnS,oBAAqB,EACrB,qBAAsB,EACtB,yBAA0B,EAC1B,gBAAiB8rB,GAAiB9rB,EAAM,UAAY,MAAQ8rB,KAAmB,SAAWA,GAAiBA,GAAe,UAAY,MAAQA,KAAmB,OAAS,OAASA,GAAe,iBAAmB9rB,GAAU,KAA2B,OAASA,EAAM,oBACzQ,+BAAgC+rB,GAAiB/rB,EAAM,UAAY,MAAQ+rB,KAAmB,SAAWA,GAAiBA,GAAe,UAAY,MAAQA,KAAmB,OAAS,OAASA,GAAe,uBAAyB/rB,GAAU,KAA2B,OAASA,EAAM,WAC9R,kCAAmCgsB,GAAiBhsB,EAAM,UAAY,MAAQgsB,KAAmB,SAAWA,GAAiBA,GAAe,UAAY,MAAQA,KAAmB,OAAS,OAASA,GAAe,yBAA2BhsB,GAAU,KAA2B,OAASA,EAAM,eACnS,2BAA4B,EAC5B,qBAAsBisB,GAAiBjsB,EAAM,UAAY,MAAQisB,KAAmB,SAAWA,GAAiBA,GAAe,UAAY,MAAQA,KAAmB,OAAS,OAASA,GAAe,sBAAwB,sBAC/N,wBAAyBC,GAAkBlsB,EAAM,UAAY,MAAQksB,KAAoB,SAAWA,GAAkBA,GAAgB,UAAY,MAAQA,KAAoB,OAAS,OAASA,GAAgB,uBAAyB,sBACzO,wBAAyBC,GAAkBnsB,EAAM,UAAY,MAAQmsB,KAAoB,SAAWA,GAAkBA,GAAgB,UAAY,MAAQA,KAAoB,OAAS,OAASA,GAAgB,wBAA0B,mBAC1O,QAASnsB,GAAU,KAA2B,OAASA,EAAM,gBAC7D,cAAeA,GAAU,KAA2B,OAASA,EAAM,gBACnE,kBAAmB,cACnB,YAAaA,GAAU,KAA2B,OAASA,EAAM,eACnE,CAAC,CAAC,CACJ,EACA,MAAO,CACL,kBAAmBosB,GAAkBpsB,EAAM,UAAY,MAAQosB,KAAoB,SAAWA,GAAkBA,GAAgB,UAAY,MAAQA,KAAoB,OAAS,OAASA,GAAgB,gBAAkB,aAC9N,CACF,EACA,YAAuB,OAAKrH,MAAU,QAAc,QAAc,KAAc,CAC9E,MAAOQ,EAAO,OAAS,OACzB,EAAG5lB,CAAK,EAAG,CAAC,EAAG,CACb,UAAW,GAAG,OAAOM,EAAW,aAAa,EAAE,OAAOY,CAAM,EAAE,KAAK,CACrE,EAAGlB,EAAM,SAAS,EAAG,CAAC,EAAG,CACvB,SAAO,KAAc,CACnB,MAAO,MACT,GAAI0sB,GAAmB1sB,EAAM,aAAe,MAAQ0sB,KAAqB,OAAS,OAASA,GAAiB,KAAK,EACjH,UAAW,GACX,eAAgB,SAChB,KAAM,YACR,CAAC,CAAC,CACJ,CAAC,EACD,OAAIrC,EACKA,EAAoBrqB,EAAO2sB,EAAU,EAEvCA,EACT,EAAG,EAAEpB,EAAkBlrB,EAAM,UAAY,MAAQkrB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,eAAgBC,EAAkBnrB,EAAM,UAAY,MAAQmrB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,yBAA0BC,EAAkBprB,EAAM,UAAY,MAAQorB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,sBAAuBC,EAAkBrrB,EAAM,UAAY,MAAQqrB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,eAAgBC,EAAkBtrB,EAAM,UAAY,MAAQsrB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,qBAAsBC,EAAkBvrB,EAAM,UAAY,MAAQurB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,uBAAwBC,EAAkBxrB,EAAM,UAAY,MAAQwrB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,oBAAqBxrB,EAAM,aAAcA,GAAU,KAA2B,OAASA,EAAM,iBAAkBA,GAAU,KAA2B,OAASA,EAAM,mBAAoBA,GAAU,KAA2B,OAASA,EAAM,UAAWA,GAAU,KAA2B,OAASA,EAAM,cAAeA,EAAM,gBAAiBulB,EAAM5lB,EAAOM,EAAWY,EAAQmpB,CAAmB,CAAC,EACzwD,OAAOppB,MAAsB,OAAK,MAAO,CACvC,UAAW,KAAWX,EAAWY,EAAQ6qB,KAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOzrB,EAAW,QAAQ,EAAG,EAAI,CAAC,EAClH,MAAOO,EACP,YAAuB,QAAM,MAAO,CAClC,IAAKZ,EACL,UAAW,KAAW,GAAG,OAAOK,EAAW,OAAO,EAAGY,KAAQ,KAAgB,CAAC,EAAG,GAAG,OAAOZ,EAAW,OAAO,EAAGwrB,IAAiB,SAAWjJ,IAAW,KAAK,CAAC,EAC7J,SAAU,CAACmG,OAA0B,QAAM,MAAO,CAChD,UAAW,KAAW,GAAG,OAAO1oB,EAAW,aAAa,EAAE,OAAOY,CAAM,CAAC,EACxE,QAAS+mB,EACT,SAAU,IAAc,OAAK5I,MAAoB,KAAc,CAAC,EAAGrf,CAAK,CAAC,KAAgB,OAAK,MAAO,CACnG,UAAW,GAAG,OAAOM,EAAW,QAAQ,EAAE,OAAOY,CAAM,EAAE,KAAK,EAC9D,GAAI,OACJ,SAAU8nB,EACZ,EAAG,MAAM,CAAC,CACZ,CAAC,KAAgB,OAAK,MAAO,CAC3B,MAAO,CACL,KAAM,CACR,EACA,UAAW,GAAG,OAAO1oB,EAAW,QAAQ,EAAE,OAAOY,CAAM,EAAE,KAAK,EAC9D,SAAUgrB,EACZ,CAAC,GAAIzD,GAAsBC,GAAiB1oB,EAAM,iBAA6B,OAAKoqB,MAAgB,QAAc,KAAc,CAC9H,mBAAoB3B,CACtB,EAAGzoB,CAAK,EAAG,CAAC,EAAG,CACb,UAAWM,CACb,CAAC,CAAC,CAAC,CACL,CAAC,CACH,CAAC,CAAC,CACJ,EC5HIssB,GAAuB,SAA8BvsB,EAAO,CAC9D,IAAI2e,EAAeC,EAAgBC,EACnC,SAAO,KAAgB,CAAC,EAAG7e,EAAM,gBAAc,QAAgB,QAAgB,QAAgB,KAAgB,CAC7G,SAAU,WACV,WAAY,cACZ,QAAS,OACT,WAAY,SACZ,YAAa,EACb,aAAc,GACd,SAAU2e,EAAgB3e,EAAM,UAAY,MAAQ2e,IAAkB,SAAWA,EAAgBA,EAAc,UAAY,MAAQA,IAAkB,OAAS,OAASA,EAAc,qBAAuB,GAC5M,UAAW,aACX,MAAO,CACL,OAAQ,MACV,CACF,EAAG,GAAG,OAAO3e,EAAM,iBAAkB,mBAAmB,EAAG,CACzD,gBAAiB,EACnB,CAAC,EAAG,qBAAsB,CACxB,UAAW,OACX,OAAQ4e,EAAiB5e,EAAM,UAAY,MAAQ4e,IAAmB,SAAWA,EAAiBA,EAAe,UAAY,MAAQA,IAAmB,OAAS,OAASA,EAAe,iBACzL,SAAU,OACV,gBAAiB,MACnB,CAAC,EAAG,SAAU,CACZ,SAAU,WACV,gBAAiB,OACjB,EAAG,CACD,QAAS,OACT,WAAY,SACZ,OAAQ,OACR,UAAW,OACX,SAAU,MACZ,EACA,IAAK,CACH,OAAQ,MACV,EACA,GAAI,CACF,OAAQ,OACR,YAAa,EACb,aAAc,EACd,kBAAmB,EACnB,WAAY,MACZ,QAASC,EAAiB7e,EAAM,UAAY,MAAQ6e,IAAmB,SAAWA,EAAiBA,EAAe,UAAY,MAAQA,IAAmB,OAAS,OAASA,EAAe,mBAAqB7e,EAAM,iBACrN,SAAU,OACV,WAAY,MACd,EACA,QAAS,CACP,QAAS,OACT,WAAY,QACd,CACF,CAAC,EAAG,gBAAiB,CACnB,SAAU,OACV,gBAAiB,CACnB,CAAC,CAAC,CACJ,EACO,SAAS,GAASC,EAAW,CAClC,SAAO,OAAa,wBAAyB,SAAUD,EAAO,CAC5D,IAAIwsB,KAAoB,QAAc,KAAc,CAAC,EAAGxsB,CAAK,EAAG,CAAC,EAAG,CAClE,aAAc,IAAI,OAAOC,CAAS,CACpC,CAAC,EACD,MAAO,CAACssB,GAAqBC,CAAiB,CAAC,CACjD,CAAC,CACH,CChDA,IAAI,GAAa,SAAoBC,EAAkBvF,EAAS,CAC9D,OAAIuF,IAAqB,GAChB,KAELA,EACKA,EAAiBvF,EAAS,IAAI,EAEhCA,CACT,EACIwF,GAAe,SAAsB/sB,EAAO,CAC9C,IAAIqgB,EAAWrgB,EAAM,SACnBye,EAAOze,EAAM,KACbsgB,EAAYtgB,EAAM,UAClBokB,EAAapkB,EAAM,WACnByoB,EAAqBzoB,EAAM,mBAC3B8sB,EAAmB9sB,EAAM,iBACzBioB,EAAoBjoB,EAAM,kBAC1BgtB,EAAgBhtB,EAAM,UACtBa,EAAQb,EAAM,MACd6iB,EAAS7iB,EAAM,OACf2a,EAAW3a,EAAM,SACjBitB,EAAajtB,EAAM,WACnBib,EAAWjb,EAAM,SACjBM,EAAYN,EAAM,UAChBoF,KAAc,cAAW,mBAA4B,EACvDklB,EAAellB,EAAY,aAC3B8nB,EAAY9nB,EAAY,UACtBrE,EAAgB,GAAG,OAAOT,GAAagqB,EAAa,KAAK,EAAG,gBAAgB,EAC5EtpB,EAAY,GAASD,CAAa,EACpCE,EAAUD,EAAU,QACpBE,EAASF,EAAU,OACjBN,EAAY,KAAWssB,EAAejsB,EAAeG,CAAM,EAC/D,GAAI2hB,IAAW,OAAS,CAACxC,GAAY4M,EAAY,CAC/C,IAAIE,IAAsBlS,GAAY,CAAC,GAAG,IAAI,SAAUtC,GAAM,CAC5D,SAAO,QAAc,KAAc,CAAC,EAAGA,EAAI,EAAG,CAAC,EAAG,CAChD,SAAU,OACV,OAAQ,MACV,CAAC,CACH,CAAC,EACGyU,EAAgBvP,GAAcsP,EAAkB,EACpD,SAAoB,OAAK7B,MAAc,QAAc,KAAc,CACjE,KAAM,YACR,EAAGtrB,CAAK,EAAG,CAAC,EAAG,CACb,WAAY,GACZ,SAAUotB,CACZ,CAAC,CAAC,CACJ,CACA,IAAIC,EAAiB,KAAW,GAAG,OAAOtsB,EAAe,OAAO,EAAGG,KAAQ,QAAgB,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOH,EAAe,WAAW,EAAGmsB,IAAc,KAAK,EAAG,GAAG,OAAOnsB,EAAe,WAAW,EAAG8hB,IAAW,KAAK,EAAG,GAAG,OAAO9hB,EAAe,cAAc,EAAGsf,CAAQ,CAAC,EACrSkH,MAAuB,OAAK,OAAQ,CACtC,UAAW8F,EACX,YAAuB,OAAK,IAAK,CAC/B,SAAU9O,GAAkBE,CAAI,CAClC,CAAC,CACH,EAAG,MAAM,EACT,OAAOxd,KAAsB,QAAM,MAAO,CACxC,UAAWP,EACX,SAAO,KAAc,CAAC,EAAGG,CAAK,EAC9B,SAAU,CAACwf,MAAyB,OAAK,OAAQ,CAC/C,UAAW,GAAG,OAAOtf,EAAe,oBAAoB,EAAE,OAAOG,CAAM,EAAE,KAAK,EAC9E,QAAS,UAAmB,CAC1BkjB,GAAe,MAAiCA,EAAW,CAAC9D,CAAS,CACvE,EACA,YAAuB,OAAK,GAAc,CAAC,CAAC,CAC9C,CAAC,EAAGD,GAAY,GAAWyM,EAAkBvF,EAAO,EAAG1E,IAAW,OAAS,CAACxC,MAAyB,QAAM,WAAW,CACpH,SAAU,IAAc,OAAKhB,MAAoB,KAAc,CAAC,EAAGrf,CAAK,CAAC,KAAgB,OAAK,MAAO,CACnG,UAAWqtB,EACX,QAASpF,EACT,SAAUb,MAAmB,QAAc,KAAc,CAAC,EAAGpnB,CAAK,EAAG,CAAC,EAAG,CACvE,UAAW,EACb,CAAC,EAAG,mBAAmB,CACzB,CAAC,CAAC,CACJ,CAAC,KAAgB,OAAK,MAAO,CAC3B,MAAO,CACL,KAAM,CACR,EACA,SAAU2a,CACZ,CAAC,GAAI8N,GAAsBzoB,EAAM,eAAiBA,EAAM,iBAA6B,OAAKoqB,MAAgB,KAAc,CACtH,mBAAoB3B,CACtB,EAAGzoB,CAAK,CAAC,CAAC,CACZ,CAAC,CAAC,CACJ,EC5FIstB,GAA0B,SAAiCjtB,EAAO,CACpE,IAAI2e,EAAeC,EAAgBC,EAAgBC,EACnD,SAAO,KAAgB,CAAC,EAAG,GAAG,OAAO9e,EAAM,iBAAkB,SAAS,KAAG,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,gBAAgB,EAAE,OAAOA,EAAM,YAAY,EAAG,CACjK,SAAU2e,EAAgB3e,EAAM,UAAY,MAAQ2e,IAAkB,SAAWA,EAAgBA,EAAc,UAAY,MAAQA,IAAkB,OAAS,OAASA,EAAc,qBAAuB,GAC5M,WAAY,GAAG,SAASC,EAAiB5e,EAAM,UAAY,MAAQ4e,IAAmB,SAAWA,EAAiBA,EAAe,UAAY,MAAQA,IAAmB,OAAS,OAASA,EAAe,qBAAuB,GAAI,IAAI,EAExO,OAAQ,GACR,MAAO,OACP,aAAc,EACd,cAAe,EACf,eAAgB,aAAa,OAAO5e,EAAM,UAAU,EACpD,kBAAmB6e,EAAiB7e,EAAM,UAAY,MAAQ6e,IAAmB,SAAWA,EAAiBA,EAAe,UAAY,MAAQA,IAAmB,OAAS,OAASA,EAAe,gBAAkB,2BACtN,qBAAsB,YACtB,eAAgB,YAChB,WAAY,6DACZ,iBAAkB,CAChB,SAAU,QACV,gBAAiB,EACjB,MAAO,OACP,OAAQ,IACR,eAAgB,CAClB,EACA,wBAAyB,CACvB,kBAAmBC,EAAiB9e,EAAM,UAAY,MAAQ8e,IAAmB,SAAWA,EAAiBA,EAAe,UAAY,MAAQA,IAAmB,OAAS,OAASA,EAAe,sBAAwB,0BAC9N,EACA,mBAAoB,CAClB,QAAS,OACT,WAAY,SACZ,SAAU,KACV,OAAQ,UACR,WAAY,CACV,aAAc,EACd,cAAe,EACf,UAAW,CACT,MAAO9e,EAAM,SACf,CACF,CACF,EACA,oBAAqB,CACnB,UAAW,gCACb,EACA,iCAAkC,CAChC,WAAY,iDACd,CACF,CAAC,CAAC,CACJ,EACO,SAAS,GAASC,EAAW,CAClC,SAAO,OAAa,kBAAmB,SAAUD,EAAO,CACtD,IAAIktB,KAAuB,QAAc,KAAc,CAAC,EAAGltB,CAAK,EAAG,CAAC,EAAG,CACrE,aAAc,IAAI,OAAOC,CAAS,CACpC,CAAC,EACD,MAAO,CAACgtB,GAAwBC,CAAoB,CAAC,CACvD,CAAC,CACH,CCrDO,SAAS,GAAWjtB,EAAWG,EAAM,CAC1C,IAAIqmB,EAAUrmB,EAAK,QACjBsmB,EAA0BtmB,EAAK,wBACjC,SAAO,OAAa,yBAA0B,SAAUJ,EAAO,CAC7D,IAAImtB,KAAe,QAAc,KAAc,CAAC,EAAGntB,CAAK,EAAG,CAAC,EAAG,CAC7D,aAAc,IAAI,OAAOC,CAAS,EAClC,wBAAyBymB,CAC3B,CAAC,EACD,OAAKD,EACE,IAAC,KAAgB,CAAC,EAAG,MAAM,OAAOzmB,EAAM,iBAAkB,SAAS,KAAG,KAAgB,CAAC,EAAG,GAAG,OAAOmtB,EAAa,YAAY,EAAG1G,GAAY,KAA6B,OAASA,EAAQ0G,CAAY,CAAC,CAAC,CAAC,EAD3L,CAAC,CAExB,CAAC,CACH,CCCA,IAAIC,GAAS,KAAO,OAChBC,GAAgB,SAAuB1tB,EAAO,CAChD,IAAIif,EAAgBC,EAAgBC,EAChCkB,EAAWrgB,EAAM,SACnB2tB,EAAc3tB,EAAM,YACpB+rB,EAAiB/rB,EAAM,UACvBa,EAAQb,EAAM,MACdsgB,EAAYtgB,EAAM,UAClBM,EAAYN,EAAM,UAClBokB,EAAapkB,EAAM,WACnB6iB,EAAS7iB,EAAM,OACf4tB,EAAe5tB,EAAM,aACrBqqB,EAAsBrqB,EAAM,oBAC1BoF,KAAc,cAAW,IAAW,EACtC/E,EAAQ+E,EAAY,MAClBtE,KAAU,cAAW,mBAA4B,EACjD4O,KAAY,YAAS,EAAK,EAC5BC,KAAa,KAAeD,EAAW,CAAC,EACxCme,EAAsBle,EAAW,CAAC,EAClCme,EAAyBne,EAAW,CAAC,EACnCoe,EAAkBJ,GAAe9K,IAAW,MAC5CmL,KAAgB,eAAY,UAAY,CAC1C,IAAIC,EAAQpL,IAAW,MACnBuK,GAAgBvP,GAAc7d,EAAM,UAAY,CAAC,CAAC,EAClD2sB,MAA0B,OAAKI,MAAc,QAAc,KAAc,CAC3E,WAAY3I,CACd,EAAGpkB,CAAK,EAAG,CAAC,EAAG,CACb,SAAUotB,GACV,SAAU/C,GAAuBA,EAAoBrqB,EAAO,IAAI,CAClE,CAAC,CAAC,EASF,OARIiuB,GAAS,CAAC5N,IACZsM,MAA0B,OAAKrB,MAAc,QAAc,KAAc,CACvE,KAAM,aACN,WAAYlH,CACd,EAAGpkB,CAAK,EAAG,CAAC,EAAG,CACb,SAAUotB,EACZ,CAAC,CAAC,GAEAQ,GAAgB,OAAOA,GAAiB,WACnCA,EAAa5tB,EAAO2sB,EAAU,EAEhCA,EACT,EAAG,CAACtC,EAAqBuD,EAAcvN,EAAUwC,EAAQuB,EAAYpkB,CAAK,CAAC,KAC3E,aAAU,UAAY,CACpB,IAAIkuB,EACA3E,IAAOzoB,GAAY,OAA+BotB,EAAwBptB,EAAQ,sBAAwB,MAAQotB,IAA0B,OAAS,OAASA,EAAsB,KAAKptB,CAAO,IAAM,SAAS,KAC/MqtB,GAAkB,UAA2B,CAC/C,IAAInP,GACAoP,GAAY7E,GAAI,UACpB,OAAI6E,MAAepP,GAAgB3e,EAAM,UAAY,MAAQ2e,KAAkB,SAAWA,GAAgBA,GAAc,UAAY,MAAQA,KAAkB,OAAS,OAASA,GAAc,qBAAuB,KAAO,CAAC6O,GAC3NC,EAAuB,EAAI,EACpB,KAELD,GACFC,EAAuB,EAAK,EAEvB,GACT,EACA,GAAKC,GACD,OAAO,QAAW,YACtB,UAAI,iBAAiB,SAAUI,GAAiB,CAC9C,QAAS,EACX,CAAC,EACM,UAAY,CACjB5E,GAAI,oBAAoB,SAAU4E,EAAe,CACnD,CACF,EAAG,EAAElP,EAAiB5e,EAAM,UAAY,MAAQ4e,IAAmB,SAAWA,EAAiBA,EAAe,UAAY,MAAQA,IAAmB,OAAS,OAASA,EAAe,mBAAoB8O,EAAiBF,CAAmB,CAAC,EAC/O,IAAII,GAAQpL,IAAW,MACnB9hB,EAAgB,GAAG,OAAOT,EAAW,gBAAgB,EACrDU,EAAY,GAASD,CAAa,EACpCE,GAAUD,EAAU,QACpBE,GAASF,EAAU,OACjB8lB,EAAU,GAAW,GAAG,OAAO/lB,EAAe,GAAG,EAAE,OAAOA,EAAe,UAAU,EAAG,CACxF,wBAAyB,GACzB,QAASf,EAAM,OACjB,CAAC,EACGU,GAAY,KAAWqrB,EAAgB7qB,GAAQH,KAAe,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAe,eAAe,EAAGgtB,CAAe,EAAG,GAAG,OAAOhtB,EAAe,sBAAsB,EAAG8sB,CAAmB,EAAG,GAAG,OAAO9sB,EAAe,MAAM,EAAG8hB,IAAW,KAAK,EAAG,GAAG,OAAO9hB,EAAe,sBAAsB,EAAG,CAACuf,CAAS,EAAG,GAAG,OAAOvf,EAAe,WAAW,EAAGktB,EAAK,EAAG,GAAG,OAAOltB,EAAe,SAAS,EAAG,EAAI,EAAG,GAAG,OAAOA,EAAe,UAAU,EAAG,CAAC,CAACf,EAAM,OAAO,CAAC,EAChkB,OAAI6iB,IAAW,QAAU,CAACxC,EAAiB,KACpCyG,EAAQ,QAAQ7lB,MAAsB,OAAK,WAAW,CAC3D,YAAuB,QAAM,MAE3B,CACA,MAAO,CACL,UAAQ,MAAe,EACvB,WAAY,CACV,OAAQ,CACN,SAAU,cACV,OAAQ,aACV,CACF,CACF,EACA,SAAU,CAAC8sB,MAAgC,OAAKN,GAAQ,CACtD,SAAO,KAAc,CACnB,SAAUvO,EAAiB7e,EAAM,UAAY,MAAQ6e,IAAmB,SAAWA,EAAiBA,EAAe,UAAY,MAAQA,IAAmB,OAAS,OAASA,EAAe,qBAAuB,GAClN,WAAY,GAAG,SAASC,EAAiB9e,EAAM,UAAY,MAAQ8e,IAAmB,SAAWA,EAAiBA,EAAe,UAAY,MAAQA,IAAmB,OAAS,OAASA,EAAe,qBAAuB,GAAI,IAAI,EACxO,gBAAiB,cACjB,OAAQ,EACV,EAAGte,CAAK,CACV,CAAC,KAAgB,OAAK4sB,GAAQ,CAC5B,UAAW/sB,GACX,MAAOG,EACP,SAAUmtB,EAAc,CAC1B,CAAC,CAAC,CACJ,CAAC,CACH,CAAC,CAAC,CAAC,CACL,E,oCCpHWK,GAAqB,IAAI,aAAU,wBAAyB,CACrE,KAAM,CACJ,QAAS,OACT,QAAS,EACT,SAAU,QACZ,EACA,MAAO,CACL,SAAU,QACZ,EACA,OAAQ,CACN,QAAS,QACT,QAAS,CACX,CACF,CAAC,EACG,GAAoB,SAA2BhuB,EAAO,CACxD,IAAI2e,EAAeC,EAAgBC,EAAgBC,EAAgBC,EAAgB+M,EAAgBC,EAAgBC,EAAgBC,EAAgBC,EAAiBC,EAAiBC,EACrL,SAAO,KAAgB,CAAC,EAAG,GAAG,OAAOpsB,EAAM,iBAAkB,SAAS,KAAG,QAAgB,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,eAAe,EAAE,OAAOA,EAAM,YAAY,EAAG,CAChM,aAAc2e,EAAgB3e,EAAM,UAAY,MAAQ2e,IAAkB,SAAWA,EAAgBA,EAAc,SAAW,MAAQA,IAAkB,OAAS,OAASA,EAAc,sBAAwB,aAClN,CAAC,EAAG3e,EAAM,gBAAc,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,KAAgB,CACtK,SAAU,WACV,UAAW,aACX,SAAU,CACR,SAAU,WACV,OAAQ,GACR,UAAW,MACb,CACF,EAAG,KAAK,OAAOA,EAAM,OAAQ,wBAAwB,EAAG,CACtD,SAAU,WACV,QAAS,OACT,cAAe,SACf,OAAQ,OACR,eAAgB4e,EAAiB5e,EAAM,UAAY,MAAQ4e,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,wBAChM,cAAeC,EAAiB7e,EAAM,UAAY,MAAQ6e,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,uBAC/L,gBAAiB,aAAa,OAAO7e,EAAM,UAAU,EACrD,gBAAiB,EACnB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,OAAO,KAAG,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,wBAAwB,EAAG,CAC3H,SAAUA,EAAM,WAChB,cAAe,CACjB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,iBAAiB,EAAE,OAAOA,EAAM,OAAQ,4BAA4B,EAAG,CACjG,OAAQ8e,EAAiB9e,EAAM,UAAY,MAAQ8e,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,sBAC1L,CAAC,CAAC,EAAG,SAAU,CACb,SAAU,WACV,QAAS,OACT,WAAY,SACZ,eAAgB,gBAChB,cAAe,GACf,aAAc,GACd,OAAQC,EAAiB/e,EAAM,UAAY,MAAQ+e,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,cACxL,OAAQ,UACR,eAAgB,aAAa,QAAQ+M,EAAiB9rB,EAAM,UAAY,MAAQ8rB,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,oBAAoB,EACzO,MAAO,CACL,QAAS,OACT,WAAY,SACZ,eAAgB,SAChB,UAAW,GACX,SAAU,GACV,QAAS,CACP,QAAS,eACT,OAAQ,GACR,cAAe,QACjB,EACA,OAAQ,CACN,QAAS,eACT,OAAQ,GACR,YAAa,EACb,gBAAiB,EACjB,kBAAmB,EACnB,OAAQC,EAAiB/rB,EAAM,UAAY,MAAQ+rB,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,mBACxL,cAAeiC,GACf,kBAAmB,MACnB,wBAAyB,OACzB,WAAY,IACZ,SAAU,GACV,WAAY,OACZ,cAAe,QACjB,CACF,EACA,iBAAe,KAAgB,CAC7B,cAAe,iBACf,OAAQ,EACR,QAAS,EACX,EAAG,GAAG,OAAOhuB,EAAM,iBAAkB,mBAAmB,EAAG,CACzD,eAAgB,EAChB,SAAU,GACV,WAAY,mDACd,CAAC,CACH,CAAC,EAAG,YAAa,CACf,QAAS,OACT,WAAY,SACZ,eAAgB,gBAChB,YAAa,EACb,aAAc,EACd,OAAQgsB,EAAiBhsB,EAAM,UAAY,MAAQgsB,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,cACxL,cAAe,CACb,cAAe,iBACf,aAAc,EACd,cAAe,EACf,SAAU,GACV,WAAY,4BACd,EACA,SAAU,CACR,OAAQC,EAAiBjsB,EAAM,UAAY,MAAQisB,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,uBACxL,cAAe,CACb,eAAgB,EAChB,cAAe,MACjB,EACA,SAAU,CACR,cAAe,EACf,aAAc,EACd,WAAY,OACZ,SAAU,GACV,OAAQ,UACR,aAAcjsB,EAAM,aACpB,UAAW,CACT,WAAYA,EAAM,gBACpB,CACF,CACF,EACA,WAAY,CACV,SAAU,GACV,cAAe,EACf,aAAc,EACd,QAAS,OACT,WAAY,SACZ,IAAKA,EAAM,SACX,aAAcA,EAAM,aACpB,MAAO,CACL,OAAQ,SACV,EACA,UAAW,CACT,WAAYA,EAAM,gBACpB,CACF,CACF,CAAC,EAAG,wBAAyB,CAC3B,iBAAkB,IAAI,OAAOA,EAAM,wBAA0B,GAAI,IAAI,EACrE,SAAU,UACZ,CAAC,EAAG,UAAW,CACb,eAAgB,GAChB,YAAa,EACb,aAAc,GACd,YAAa,CACX,iBAAkB,EACpB,CACF,CAAC,EAAG,UAAW,CACb,MAAO,OACP,GAAI,CACF,OAAQ,MACV,CACF,CAAC,EAAG,cAAe,CACjB,OAAQ,OACR,UAAW,OACX,WAAY,aACd,CAAC,EAAG,WAAY,CACd,OAAQksB,EAAkBlsB,EAAM,UAAY,MAAQksB,IAAoB,SAAWA,EAAkBA,EAAgB,SAAW,MAAQA,IAAoB,OAAS,OAASA,EAAgB,uBAC9L,gBAAiB,GACjB,SAAUlsB,EAAM,SAChB,cAAeguB,GACf,kBAAmB,MACnB,wBAAyB,MAC3B,CAAC,CAAC,EAAG,GAAG,OAAOhuB,EAAM,YAAY,EAAE,OAAOA,EAAM,aAAc,QAAQ,EAAG,CACvE,SAAU,QACV,gBAAiB,EACjB,iBAAkB,EAClB,OAAQ,MACR,OAAQ,OACR,QAAS,CACP,OAAQ,eAAe,SAASmsB,EAAkBnsB,EAAM,UAAY,MAAQmsB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,qBAAuB,GAAI,KAAK,EACvP,gBAAiB,GAAG,SAASC,EAAkBpsB,EAAM,UAAY,MAAQosB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,qBAAuB,GAAI,IAAI,CACrP,CACF,CAAC,CAAC,CACJ,EACO,SAAS,GAASnsB,EAAW0qB,EAAO,CACzC,IAAIjE,EAA0BiE,EAAM,wBACpC,SAAO,OAAa,qBAAsB,SAAU3qB,EAAO,CACzD,IAAI8f,KAAiB,QAAc,KAAc,CAAC,EAAG9f,CAAK,EAAG,CAAC,EAAG,CAC/D,aAAc,IAAI,OAAOC,CAAS,EAClC,wBAAyBymB,CAC3B,CAAC,EACD,MAAO,CAAC,GAAkB5G,CAAc,CAAC,CAC3C,CAAC,CACH,CC9KA,IAAImO,GAAmB,SAA0BtuB,EAAO,CACtD,IAAIgf,EACAqB,EAAWrgB,EAAM,SACnBgoB,EAAahoB,EAAM,WACnBsgB,EAAYtgB,EAAM,UAClBokB,EAAapkB,EAAM,WACnBa,EAAQb,EAAM,MACdU,EAAYV,EAAM,UAClBuuB,EAAOvuB,EAAM,KACbM,EAAYN,EAAM,UAClBwuB,EAAexuB,EAAM,aACnBoF,KAAc,cAAW,IAAW,EACtC/E,EAAQ+E,EAAY,SACtB,aAAU,UAAY,CAChBib,IAAa,KACf+D,GAAe,MAAiCA,EAAW,EAAI,EAGnE,EAAG,CAAC/D,CAAQ,CAAC,EACb,IAAIoO,KAAY,MAAKzuB,EAAO,CAAC,YAAa,OAAO,CAAC,EAC9CoD,EAAoB,aAAiB,mBAA4B,EACnE8pB,EAAY9pB,EAAkB,UAC5BpC,EAAY,GAAS,GAAG,OAAOV,EAAW,QAAQ,EAAG,CACrD,wBAAyB,EAC3B,CAAC,EACDW,EAAUD,EAAU,QACpBE,EAASF,EAAU,OACjB+nB,EAAiB,KAAW,GAAG,OAAOzoB,EAAW,QAAQ,EAAGI,EAAWQ,CAAM,EACjF,GAAIqtB,EACF,OAAO,KAET,IAAIG,KAAkBjhB,KAAsB,CAAC6S,EAAW,UAAY,CAClE,OAAO8D,GAAe,KAAgC,OAASA,EAAW,EAAI,CAChF,CAAC,EACD,OAAOnjB,EAAQof,KAAwB,OAAK,QAAQ,QAAc,KAAc,CAC9E,UAAW6M,IAAc,MAAQ,QAAU,OAC3C,UAAW,KAAW,GAAG,OAAO5sB,EAAW,eAAe,EAAGI,CAAS,CACxE,EAAGguB,CAAe,EAAG,CAAC,EAAG,CACvB,SAAO,KAAc,CACnB,QAAS,EACT,OAAQ,OACV,EAAG7tB,CAAK,EACR,QAAS,UAAmB,CAC1BujB,GAAe,MAAiCA,EAAW,EAAI,CACjE,EACA,aAAc,GACd,SAAU,GACV,aAAcoK,GAAgB,GAC9B,MAAOxG,EACP,OAAQ,CACN,KAAM,CACJ,OAAQ,QACR,QAAS,EACT,QAAS,OACT,cAAe,MACf,iBAAkBhJ,EAAgB3e,EAAM,UAAY,MAAQ2e,IAAkB,SAAWA,EAAgBA,EAAc,SAAW,MAAQA,IAAkB,OAAS,OAASA,EAAc,mBAC9L,CACF,EACA,YAAuB,OAAKyI,MAAW,QAAc,KAAc,CAAC,EAAGgH,CAAS,EAAG,CAAC,EAAG,CACrF,SAAU,GACV,UAAW1F,EACX,UAAW1I,EAAW,GAAQC,EAC9B,WAAY,GACZ,gBAAiBA,CACnB,CAAC,CAAC,CACJ,CAAC,CAAC,KAAiB,OAAKmH,MAAW,QAAc,KAAc,CAC7D,UAAWsB,EACX,gBAAiBzI,CACnB,EAAGmO,CAAS,EAAG,CAAC,EAAG,CACjB,MAAO5tB,CACT,CAAC,CAAC,CAAC,CACL,E,wBC/EW8tB,GAAkB,SAAyB/lB,EAAUuT,EAAYyS,EAAe,CAGzF,GAAIA,EAAe,CACjB,IAAIlS,KAAU,MAAmBkS,EAAc,KAAK,CAAC,EAAE,KAAK,SAAUrqB,EAAK,CACzE,GAAI,CACF,OAAIA,EAAI,WAAW,MAAM,EAChB,MAEF,UAAMA,CAAG,EAAEqE,CAAQ,CAC5B,OAASjH,EAAO,CACd,eAAQ,IAAI,MAAO4C,EAAK5C,CAAK,EACtB,EACT,CACF,CAAC,EACD,GAAI+a,EACF,OAAOkS,EAAc,IAAIlS,CAAO,CAEpC,CAIA,GAAIP,EAAY,CACd,IAAI0S,EAAW,OAAO,KAAK1S,CAAU,EAAE,KAAK,SAAU5X,EAAK,CACzD,GAAI,CACF,OAAIA,GAAQ,MAA0BA,EAAI,WAAW,MAAM,EAClD,MAEF,UAAMA,CAAG,EAAEqE,CAAQ,CAC5B,OAASjH,EAAO,CACd,eAAQ,IAAI,MAAO4C,EAAK5C,CAAK,EACtB,EACT,CACF,CAAC,EACD,GAAIktB,EACF,OAAO1S,EAAW0S,CAAQ,CAE9B,CACA,MAAO,CACL,KAAM,EACR,CACF,EAOWC,GAAmB,SAA0B9uB,EAAO+uB,EAAa,CAC1E,IAAIC,EAAkBhvB,EAAM,SAC1B4I,EAAWomB,IAAoB,OAAS,IAAMA,EAC9C7S,EAAanc,EAAM,WACnB4uB,EAAgB5uB,EAAM,cACtBma,EAAgBna,EAAM,cACtB0e,EAAQ1e,EAAM,MACd6pB,EAAc7pB,EAAM,KACpBoZ,EAAOyQ,IAAgB,OAAS,CAC9B,OAAQ,EACV,EAAIA,EACFoF,EAAYF,EAAc,GAAKrQ,GAAS,GACxCwQ,EAAiBP,GAAgB/lB,EAAUuT,EAAYyS,CAAa,EACxE,GAAI,CAACM,EACH,MAAO,CACL,MAAOD,EACP,GAAI,GACJ,SAAUA,CACZ,EAEF,IAAIE,EAAWD,EAAe,KAO9B,OANI9V,EAAK,SAAW,IAAS8V,EAAe,QAAU/U,IACpDgV,EAAWhV,EAAc,CACvB,GAAI+U,EAAe,QAAU,GAC7B,eAAgBA,EAAe,IACjC,CAAC,GAEEC,EAODJ,GAAe,CAACrQ,EACX,CACL,MAAOyQ,EACP,GAAID,EAAe,QAAU,GAC7B,SAAUC,CACZ,EAEK,CACL,MAAO,GAAG,OAAOA,EAAU,KAAK,EAAE,OAAOzQ,CAAK,EAC9C,GAAIwQ,EAAe,QAAU,GAC7B,SAAUC,CACZ,EAjBS,CACL,MAAOF,EACP,GAAIC,EAAe,QAAU,GAC7B,SAAUD,CACZ,CAcJ,EACWG,GAAe,SAAsBpvB,EAAO+uB,EAAa,CAClE,OAAOD,GAAiB9uB,EAAO+uB,CAAW,EAAE,KAC9C,ECnGA,GAAe,CACb,wBAAyB,qBACzB,6BAA8B,kBAC9B,8BAA+B,mBAC/B,iCAAkC,oBAClC,4BAA6B,gBAC7B,kCAAmC,QACnC,kCAAmC,QACnC,yBAA0B,cAC1B,8BAA+B,WAC/B,iCAAkC,UAClC,gCAAiC,gBACjC,8BAA+B,OAC/B,+BAAgC,cAChC,kCAAmC,sBACnC,kCAAmC,gBACnC,kCAAmC,YACnC,gCAAiC,gBACjC,4BAA6B,gBAC7B,gCAAiC,UACjC,kCAAmC,WACnC,6BAA8B,kBAC9B,+BAAgC,oBAChC,sCAAuC,SACvC,oCAAqC,OACrC,sCAAuC,SACvC,0CAA2C,cAC3C,uBAAwB,mBACxB,sBAAuB,kBACvB,sBAAuB,kBACvB,yBAA0B,cAC1B,0BAA2B,eAC3B,2BAA4B,gBAC5B,gCAAiC,4BACjC,yBAA0B,+BAC1B,8BAA+B,sCAC/B,4BAA6B,iBAC7B,uBAAwB,YACxB,mBAAoB,eACpB,sBAAuB,gBACvB,uBAAwB,4EACxB,8BAA+B,6EACjC,ECxCA,MAAe,KAAc,CAAC,EAAGM,EAAa,ECF9C,GAAe,CACb,wBAAyB,wBACzB,6BAA8B,aAC9B,8BAA+B,cAC/B,4BAA6B,qBAC7B,kCAAmC,QACnC,kCAAmC,SACnC,yBAA0B,kBAC1B,8BAA+B,gBAC/B,iCAAkC,UAClC,gCAAiC,qBACjC,8BAA+B,QAC/B,+BAAgC,eAChC,kCAAmC,qBACnC,kCAAmC,sBACnC,kCAAmC,WACnC,gCAAiC,eACjC,6BAA8B,6BAC9B,uBAAwB,gBACxB,sBAAuB,kBACvB,sBAAuB,aACvB,yBAA0B,cAC1B,0BAA2B,gBAC3B,2BAA4B,sBAC5B,gCAAiC,oCACjC,yBAA0B,0CAC1B,8BAA+B,4DAC/B,4BAA6B,qBAC7B,uBAAwB,iBACxB,mBAAoB,qBACpB,sBAAuB,iBACvB,uBAAwB,uFACxB,8BAA+B,kHACjC,EC/BA,MAAe,KAAc,CAAC,EAAG,EAAa,ECF9C,GAAe,CACb,wBAAyB,kCACzB,6BAA8B,4BAC9B,8BAA+B,kCAC/B,4BAA6B,kCAC7B,kCAAmC,eACnC,kCAAmC,eACnC,yBAA0B,4BAC1B,8BAA+B,WAC/B,iCAAkC,UAClC,gCAAiC,gBACjC,8BAA+B,OAC/B,+BAAgC,cAChC,kCAAmC,qBACnC,kCAAmC,gBACnC,kCAAmC,YACnC,gCAAiC,gBACjC,6BAA8B,8CAC9B,+BAAgC,kCAChC,sCAAuC,eACvC,oCAAqC,eACrC,sCAAuC,qBACvC,0CAA2C,4BAC3C,uBAAwB,+CACxB,sBAAuB,yCACvB,sBAAuB,kCACvB,yBAA0B,4BAC1B,0BAA2B,4BAC3B,2BAA4B,wCAC5B,gCAAiC,0GACjC,yBAA0B,4DAC1B,8BAA+B,0GAC/B,4BAA6B,4BAC7B,uBAAwB,kCACxB,mBAAoB,kCACpB,sBAAuB,mCACvB,uBAAwB,oIACxB,8BAA+B,yLACjC,ECpCA,MAAe,KAAc,CAAC,EAAG,EAAa,ECF9C,GAAe,CACb,wBAAyB,uCACzB,6BAA8B,uCAC9B,8BAA+B,uCAC/B,iCAAkC,qDAClC,4BAA6B,uCAC7B,kCAAmC,eACnC,kCAAmC,eACnC,yBAA0B,qBAC1B,8BAA+B,eAC/B,iCAAkC,eAClC,gCAAiC,eACjC,8BAA+B,eAC/B,+BAAgC,qBAChC,kCAAmC,6CACnC,kCAAmC,eACnC,kCAAmC,qBACnC,gCAAiC,eACjC,6BAA8B,2BAC9B,4BAA6B,uCAC7B,gCAAiC,2BACjC,kCAAmC,2BACnC,+BAAgC,2BAChC,sCAAuC,eACvC,oCAAqC,eACrC,sCAAuC,eACvC,0CAA2C,qBAC3C,uBAAwB,uCACxB,sBAAuB,uCACvB,sBAAuB,uCACvB,yBAA0B,uCAC1B,0BAA2B,sBAC3B,2BAA4B,uCAC5B,gCAAiC,+DACjC,yBAA0B,wCAC1B,8BAA+B,+CAC/B,4BAA6B,2BAC7B,uBAAwB,2BACxB,mBAAoB,2BACpB,sBAAuB,uCACvB,uBAAwB,+GACxB,8BAA+B,oNACjC,ECxCA,MAAe,KAAc,CAAC,EAAG,EAAa,ECF9C,GAAe,CACb,wBAAyB,uCACzB,6BAA8B,uCAC9B,iCAAkC,qDAClC,8BAA+B,uCAC/B,4BAA6B,uCAC7B,kCAAmC,eACnC,kCAAmC,eACnC,yBAA0B,qBAC1B,8BAA+B,eAC/B,iCAAkC,eAClC,gCAAiC,eACjC,8BAA+B,eAC/B,+BAAgC,qBAChC,kCAAmC,6CACnC,kCAAmC,qBACnC,kCAAmC,qBACnC,gCAAiC,eACjC,6BAA8B,2BAC9B,uBAAwB,uCACxB,sBAAuB,uCACvB,sBAAuB,uCACvB,yBAA0B,uCAC1B,0BAA2B,sBAC3B,2BAA4B,uCAC5B,gCAAiC,+DACjC,yBAA0B,wCAC1B,8BAA+B,+CAC/B,4BAA6B,2BAC7B,uBAAwB,2BACxB,mBAAoB,2BACpB,sBAAuB,uCACvB,uBAAwB,+GACxB,8BAA+B,oNACjC,EChCA,MAAe,KAAc,CAAC,EAAG,EAAa,ECI1CC,GAAU,CACZ,QAAS,GACT,QAAS,GACT,QAAS,GACT,QAAS,GACT,QAAS,EACX,EACWC,GAAc,UAAuB,CAE9C,GAAI,IAACngB,KAAU,EAAG,MAAO,QACzB,IAAIogB,EAAO,OAAO,aAAa,QAAQ,YAAY,EACnD,OAAOA,GAAQ,OAAO,UAAY,UAAU,QAC9C,EACWC,GAAgB,UAAyB,CAClD,IAAIC,EAAUH,GAAY,EAC1B,OAAOD,GAAQI,CAAO,GAAKJ,GAAQ,OAAO,CAC5C,E,wBClBIhiB,GAAa,UAAsB,CACrC,IAAIC,EACJ,OAAI,OAAOC,IAAY,YAAoBmiB,OAClCpiB,EAAWC,MAAa,MAAQ,KAAa,SAAW,GAAW,2CAAkB,MAAQ,KAAa,OAAS,OAAS,GAAS,eAAiBmiB,IACjK,EASIC,GAAkB,SAAyBvvB,EAAO,CACpD,IAAIwvB,EAAa7Q,EAAeC,EAAgBC,EAAgBC,EAAgBC,EAAgB+M,EAAgBC,EAAgBC,EAAgBC,EAAgBC,EAAiBC,EAAiBC,EAAiBlB,EAAiBC,EAAiBC,EAAiBC,EAAiBC,EAAiBC,EAAiBC,EAAiBiE,EAAWC,EAAiBC,GAAiBC,EAAiBC,EAAiBC,GAAiBC,GAAiBC,EAAiBC,GAAiBC,EAAiBC,GAAiBC,GAC/f,OAAKZ,EAAcviB,GAAW,KAAO,MAAQuiB,IAAgB,QAAUA,EAAY,WAAW,GAAG,EACxF,CAAC,KAEH,QAAgB,QAAgB,KAAgB,CAAC,EAAGxvB,EAAM,gBAAc,QAAgB,KAAgB,CAC7G,MAAO,OACP,OAAQ,MACV,EAAG,GAAG,OAAOA,EAAM,iBAAkB,YAAY,GAAIyvB,EAAY,CAC/D,OAAQ9Q,EAAgB3e,EAAM,UAAY,MAAQ2e,IAAkB,SAAWA,EAAgBA,EAAc,SAAW,MAAQA,IAAkB,OAAS,OAASA,EAAc,aACpL,KAAG,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,QAAgB,KAAgB8Q,EAAW,GAAG,OAAOzvB,EAAM,OAAQ,WAAW,EAAG,CAClN,gBAAiB,wBACjB,OAAQ4e,EAAiB5e,EAAM,UAAY,MAAQ4e,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,aAC1L,CAAC,EAAG,KAAK,OAAO5e,EAAM,OAAQ,SAAS,EAAG,CACxC,gBAAiB,cACjB,MAAO,MACT,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,6BAA6B,EAAE,OAAOA,EAAM,OAAQ,qBAAqB,EAAG,CACtG,MAAO,SACT,CAAC,EAAG,IAAI,OAAOA,EAAM,OAAQ,OAAO,KAAG,QAAgB,KAAgB,CACrE,OAAQ6e,EAAiB7e,EAAM,UAAY,MAAQ6e,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,aAC1L,EAAG,GAAG,OAAO7e,EAAM,OAAQ,YAAY,EAAG,CACxC,IAAK,CACH,WAAY,iBACd,CACF,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,cAAc,EAAG,CAC3C,MAAO,SACT,CAAC,CAAC,EAAG,IAAI,OAAOA,EAAM,OAAQ,cAAc,KAAG,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,wBAAwB,EAAE,OAAOA,EAAM,OAAQ,4BAA4B,EAAG,CACvK,QAAS,MACX,CAAC,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,YAAY,EAAE,OAAOA,EAAM,OAAQ,cAAc,EAAG,CAC/E,gBAAiB,uBACnB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ;AAAA,SAA+B,EAAE,OAAOA,EAAM,OAAQ,4BAA4B,EAAG,CAC/G,gBAAiB,uBACnB,CAAC,EAAG,IAAI,OAAOA,EAAM,OAAQ,aAAa,KAAG,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ;AAAA,aAAkC,EAAE,OAAOA,EAAM,OAAQ;AAAA,aAAkC,EAAE,OAAOA,EAAM,OAAQ;AAAA,aAAsC,EAAE,OAAOA,EAAM,OAAQ,2BAA2B,KAAG,KAAgB,CACpT,OAAQ8e,EAAiB9e,EAAM,UAAY,MAAQ8e,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,oBACxL,aAAc9e,EAAM,YACtB,EAAG,GAAG,OAAOA,EAAM,OAAQ,qBAAqB,EAAG,CACjD,OAAQ+e,EAAiB/e,EAAM,UAAY,MAAQ+e,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,mBAC1L,CAAC,CAAC,CAAC,EAAG,IAAI,OAAO/e,EAAM,OAAQ,YAAY,EAAE,OAAOA,EAAM,OAAQ,mBAAmB,KAAG,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,qBAAqB,EAAG,CAC1K,iBAAkB8rB,EAAiB9rB,EAAM,UAAY,MAAQ8rB,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,wBAClM,aAAc9rB,EAAM,YACtB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ;AAAA,aAAkC,EAAE,OAAOA,EAAM,OAAQ;AAAA,aAAkC,EAAE,OAAOA,EAAM,OAAQ,2BAA2B,KAAG,KAAgB,CAC1L,OAAQ+rB,EAAiB/rB,EAAM,UAAY,MAAQ+rB,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,oBACxL,aAAc/rB,EAAM,aACpB,gBAAiB,GAAG,QAAQgsB,EAAiBhsB,EAAM,UAAY,MAAQgsB,IAAmB,SAAWA,EAAiBA,EAAe,UAAY,MAAQA,IAAmB,OAAS,OAASA,EAAe,qBAAsB,aAAa,CAClP,EAAG,GAAG,OAAOhsB,EAAM,OAAQ,qBAAqB,EAAG,CACjD,OAAQisB,EAAiBjsB,EAAM,UAAY,MAAQisB,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,mBAC1L,CAAC,CAAC,CAAC,EAAG,GAAG,OAAOjsB,EAAM,OAAQ,qBAAqB,EAAG,CACpD,OAAQksB,EAAkBlsB,EAAM,UAAY,MAAQksB,IAAoB,SAAWA,EAAkBA,EAAgB,SAAW,MAAQA,IAAoB,OAAS,OAASA,EAAgB,qBAChM,CAAC,KAAG,QAAgB,QAAgB,QAAgB,QAAgB,KAAgBuD,EAAW,GAAG,OAAOzvB,EAAM,OAAQ,wBAAwB,EAAG,CAChJ,OAAQmsB,EAAkBnsB,EAAM,UAAY,MAAQmsB,IAAoB,SAAWA,EAAkBA,EAAgB,SAAW,MAAQA,IAAoB,OAAS,OAASA,EAAgB,qBAChM,CAAC,EAAG,IAAI,OAAOnsB,EAAM,OAAQ,YAAY,EAAE,OAAOA,EAAM,OAAQ,gBAAgB,EAAE,OAAOA,EAAM,OAAQ,oBAAoB,EAAG,CAC5H,OAAQosB,EAAkBpsB,EAAM,UAAY,MAAQosB,IAAoB,SAAWA,EAAkBA,EAAgB,SAAW,MAAQA,IAAoB,OAAS,OAASA,EAAgB,qBAChM,CAAC,EAAG,IAAI,OAAOpsB,EAAM,OAAQ,gBAAgB,KAAG,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,wBAAwB,EAAG,CACrH,aAAcA,EAAM,aACpB,OAAQkrB,EAAkBlrB,EAAM,UAAY,MAAQkrB,IAAoB,SAAWA,EAAkBA,EAAgB,SAAW,MAAQA,IAAoB,OAAS,OAASA,EAAgB,qBAChM,CAAC,CAAC,EAAG,GAAG,OAAOlrB,EAAM,OAAQ,wBAAwB,EAAE,OAAOA,EAAM,OAAQ,wBAAwB,EAAE,OAAOA,EAAM,OAAQ,qBAAqB,EAAG,CACjJ,OAAQmrB,EAAkBnrB,EAAM,UAAY,MAAQmrB,IAAoB,SAAWA,EAAkBA,EAAgB,SAAW,MAAQA,IAAoB,OAAS,OAASA,EAAgB,mBAChM,CAAC,EAAG,IAAI,OAAOnrB,EAAM,OAAQ,kBAAkB,KAAG,QAAgB,QAAgB,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ;AAAA,WAA+B,EAAE,OAAOA,EAAM,OAAQ;AAAA,WAAkC,EAAE,OAAOA,EAAM,OAAQ;AAAA,WAAgC,EAAE,OAAOA,EAAM,OAAQ,sBAAsB,EAAG,CAC3U,aAAc,EACd,WAAY,OACZ,OAAQorB,EAAkBprB,EAAM,UAAY,MAAQorB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,oBAC/L,gBAAiB,GAAG,QAAQC,EAAkBrrB,EAAM,UAAY,MAAQqrB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,qBAAsB,aAAa,CACxP,CAAC,EAAG,GAAG,OAAOrrB,EAAM,OAAQ;AAAA,WAA8B,EAAE,OAAOA,EAAM,OAAQ;AAAA,WAAiC,EAAE,OAAOA,EAAM,OAAQ;AAAA,WAAkC,EAAE,OAAOA,EAAM,OAAQ,wBAAwB,KAAG,KAAgB,CAC3O,iBAAkBsrB,EAAkBtrB,EAAM,UAAY,MAAQsrB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,wBACzM,aAActrB,EAAM,aACpB,WAAY,OACZ,MAAO,GAAG,QAAQurB,EAAkBvrB,EAAM,UAAY,MAAQurB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,sBAAuB,aAAa,CAC/O,EAAG,GAAG,OAAOvrB,EAAM,OAAQ,qBAAqB,EAAG,CACjD,MAAO,GAAG,QAAQwrB,EAAkBxrB,EAAM,UAAY,MAAQwrB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,sBAAuB,aAAa,CAC/O,CAAC,CAAC,EAAG,KAAK,OAAOxrB,EAAM,OAAQ,gBAAgB,EAAE,OAAOA,EAAM,OAAQ,eAAe,EAAG,CACtF,cAAe,GACf,aAAc,CAChB,CAAC,EAAG,KAAK,OAAOA,EAAM,OAAQ,uBAAuB,EAAE,OAAOA,EAAM,OAAQ,sBAAsB,EAAG,CACnG,QAAS,MACX,CAAC,CAAC,EAAE,EAAG,GAAG,OAAOA,EAAM,iBAAkB,2BAA2B,KAAG,QAAgB,KAAgB,CAAC,EAAG,IAAI,OAAOA,EAAM,OAAQ,OAAO,KAAG,KAAgB,CAC5J,OAAQ0vB,EAAkB1vB,EAAM,UAAY,MAAQ0vB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,aACjM,EAAG,GAAG,OAAO1vB,EAAM,OAAQ,cAAc,EAAG,CAC1C,MAAO,SACT,CAAC,CAAC,EAAG,IAAI,OAAOA,EAAM,OAAQ,aAAa,KAAG,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ;AAAA,aAAkC,EAAE,OAAOA,EAAM,OAAQ;AAAA,aAAkC,EAAE,OAAOA,EAAM,OAAQ;AAAA,aAAsC,EAAE,OAAOA,EAAM,OAAQ,2BAA2B,KAAG,KAAgB,CACrU,OAAQ2vB,GAAkB3vB,EAAM,UAAY,MAAQ2vB,KAAoB,SAAWA,GAAkBA,GAAgB,UAAY,MAAQA,KAAoB,OAAS,OAASA,GAAgB,oBAC/L,aAAc3vB,EAAM,aACpB,WAAY,OACZ,iBAAkB4vB,EAAkB5vB,EAAM,UAAY,MAAQ4vB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,uBAC3M,EAAG,GAAG,OAAO5vB,EAAM,OAAQ,qBAAqB,EAAG,CACjD,OAAQ6vB,EAAkB7vB,EAAM,UAAY,MAAQ6vB,IAAoB,SAAWA,EAAkBA,EAAgB,UAAY,MAAQA,IAAoB,OAAS,OAASA,EAAgB,mBACjM,CAAC,CAAC,EAAG,GAAG,OAAO7vB,EAAM,OAAQ,qBAAqB,EAAG,CACnD,OAAQ8vB,GAAkB9vB,EAAM,UAAY,MAAQ8vB,KAAoB,SAAWA,GAAkBA,GAAgB,UAAY,MAAQA,KAAoB,OAAS,OAASA,GAAgB,sBAC/L,aAAc9vB,EAAM,aACpB,iBAAkB+vB,GAAkB/vB,EAAM,UAAY,MAAQ+vB,KAAoB,SAAWA,GAAkBA,GAAgB,UAAY,MAAQA,KAAoB,OAAS,OAASA,GAAgB,uBAC3M,CAAC,CAAC,CAAC,CAAC,EAAG,GAAG,OAAO/vB,EAAM,OAAQ,WAAW,EAAE,OAAOA,EAAM,OAAQ,cAAc,EAAG,CAChF,gBAAiB,uBACnB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,qBAAqB,KAAG,QAAgB,QAAgB,QAAgB,KAAgB,CAClH,gBAAiB,4BACjB,0BAA2B,YAC3B,eAAgB,WAClB,EAAG,GAAG,OAAOA,EAAM,OAAQ,OAAO,KAAG,KAAgB,CACnD,WAAY,yBACZ,gBAAiB,wBACnB,EAAG,GAAG,OAAOA,EAAM,OAAQ;AAAA,SAA+B,EAAE,OAAOA,EAAM,OAAQ,4BAA4B,EAAG,CAC9G,gBAAiB,uBACnB,CAAC,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,qBAAqB,EAAG,CACnD,OAAQgwB,EAAkBhwB,EAAM,UAAY,MAAQgwB,IAAoB,SAAWA,EAAkBA,EAAgB,SAAW,MAAQA,IAAoB,OAAS,OAASA,EAAgB,qBAChM,CAAC,EAAG,GAAG,OAAOhwB,EAAM,OAAQ,wBAAwB,EAAG,CACrD,OAAQiwB,GAAkBjwB,EAAM,UAAY,MAAQiwB,KAAoB,SAAWA,GAAkBA,GAAgB,SAAW,MAAQA,KAAoB,OAAS,OAASA,GAAgB,qBAChM,CAAC,EAAG,GAAG,OAAOjwB,EAAM,OAAQ,YAAY,EAAE,OAAOA,EAAM,OAAQ,mBAAmB,KAAG,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOA,EAAM,OAAQ,qBAAqB,EAAG,CACvK,gBAAiB,sBACjB,aAAcA,EAAM,aACpB,OAAQkwB,EAAkBlwB,EAAM,UAAY,MAAQkwB,IAAoB,SAAWA,EAAkBA,EAAgB,SAAW,MAAQA,IAAoB,OAAS,OAASA,EAAgB,qBAChM,CAAC,EAAG,GAAG,OAAOlwB,EAAM,OAAQ;AAAA,WAAgC,EAAE,OAAOA,EAAM,OAAQ;AAAA,WAAgC,EAAE,OAAOA,EAAM,OAAQ,2BAA2B,KAAG,KAAgB,CACtL,OAAQmwB,GAAkBnwB,EAAM,UAAY,MAAQmwB,KAAoB,SAAWA,GAAkBA,GAAgB,SAAW,MAAQA,KAAoB,OAAS,OAASA,GAAgB,oBAC9L,aAAcnwB,EAAM,YACtB,EAAG,GAAG,OAAOA,EAAM,OAAQ,qBAAqB,EAAG,CACjD,OAAQowB,GAAkBpwB,EAAM,UAAY,MAAQowB,KAAoB,SAAWA,GAAkBA,GAAgB,SAAW,MAAQA,KAAoB,OAAS,OAASA,GAAgB,mBAChM,CAAC,CAAC,CAAC,CAAC,CACN,EACIC,GAAoB,SAA2BrwB,EAAO,CACxD,IAAIswB,EAAiBC,EAAiBC,EAAiBC,EACvD,SAAO,QAAgB,KAAgB,CAAC,EAAG,GAAG,OAAOzwB,EAAM,OAAQ,SAAS,EAAG,CAC7E,gBAAiB,wBACnB,CAAC,EAAGA,EAAM,gBAAc,QAAgB,QAAgB,QAAgB,KAAgB,CAAC,EAAG,KAAK,OAAOA,EAAM,OAAQ,SAAS,EAAG,CAChI,QAAS,OACT,gBAAiB,cACjB,MAAO,MACT,CAAC,EAAG,GAAG,OAAOA,EAAM,aAAc,UAAU,EAAG,CAC7C,QAAS,OACT,cAAe,SACf,MAAO,OACP,kBAAmBswB,EAAkBtwB,EAAM,UAAY,MAAQswB,IAAoB,SAAWA,EAAkBA,EAAgB,iBAAmB,MAAQA,IAAoB,OAAS,OAASA,EAAgB,uBAAyB,cAC1O,SAAU,WACV,cAAeC,EAAkBvwB,EAAM,UAAY,MAAQuwB,IAAoB,SAAWA,EAAkBA,EAAgB,iBAAmB,MAAQA,IAAoB,OAAS,OAASA,EAAgB,iCAC7M,eAAgBC,EAAkBxwB,EAAM,UAAY,MAAQwwB,IAAoB,SAAWA,EAAkBA,EAAgB,iBAAmB,MAAQA,IAAoB,OAAS,OAASA,EAAgB,kCAC9M,uBAAwB,CACtB,QAAS,CACX,CACF,CAAC,EAAG,GAAG,OAAOxwB,EAAM,aAAc,YAAY,EAAG,CAC/C,MAAO,OACP,QAAS,OACT,cAAe,SACf,SAAU,EACV,UAAW,EACX,gBAAiB,aACnB,CAAC,EAAG,GAAG,OAAOA,EAAM,aAAc,UAAU,EAAG,CAC7C,cAAe,OACf,SAAU,QACV,SAAU,SACV,gBAAiB,EACjB,iBAAkB,EAClB,OAAQ,EACR,OAAQ,OACR,MAAO,OACP,YAAaywB,EAAkBzwB,EAAM,UAAY,MAAQywB,IAAoB,OAAS,OAASA,EAAgB,QACjH,CAAC,CAAC,CACJ,EACO,SAAS,GAASxwB,EAAW,CAClC,SAAO,OAAa,YAAa,SAAUD,EAAO,CAChD,IAAI0wB,KAAiB,QAAc,KAAc,CAAC,EAAG1wB,CAAK,EAAG,CAAC,EAAG,CAC/D,aAAc,IAAI,OAAOC,CAAS,CACpC,CAAC,EACD,MAAO,CAACowB,GAAkBK,CAAc,EAAGnB,GAAgBmB,CAAc,CAAC,CAC5E,CAAC,CACH,CChLO,SAASC,GAAUxY,EAAK,CAC7B,GAAI,CAACA,GAAOA,IAAQ,IAClB,MAAO,CAAC,GAAG,EAEb,IAAIyY,EAAUzY,EAAI,MAAM,GAAG,EAAE,OAAO,SAAUzS,EAAG,CAC/C,OAAOA,CACT,CAAC,EACD,OAAOkrB,EAAQ,IAAI,SAAUC,EAASrsB,EAAO,CAC3C,MAAO,IAAI,OAAOosB,EAAQ,MAAM,EAAGpsB,EAAQ,CAAC,EAAE,KAAK,GAAG,CAAC,CACzD,CAAC,CACH,C,gBCLW,GAAa,UAAsB,CAC5C,IAAI0I,EACJ,OAAI,OAAO,IAAY,YAAoBoiB,OAClCpiB,EAAW,MAAa,MAAQ,KAAa,SAAW,GAAW,2CAAkB,MAAQ,KAAa,OAAS,OAAS,GAAS,eAAiBoiB,IACjK,EAGIwB,GAAoB,SAA2BrnB,EAAOsnB,EAAGxW,EAAQ,CACnE,IAAIna,EAAOqJ,EACTunB,EAAiB5wB,EAAK,eACtBie,EAAQje,EAAK,MACboG,EAAOpG,EAAK,KACV6wB,EAAO1W,EAAO,UAAU,SAAU7U,EAAG,CACvC,OAEEA,EAAE,WAAa+D,EAAM,IAEzB,CAAC,IAAM8Q,EAAO,OAAS,EACvB,OAAO0W,KAAoB,OAAK,OAAQ,CACtC,SAAU5S,GAAS2S,CACrB,CAAC,KAAiB,OAAK,OAAQ,CAC7B,QAASxqB,EAAO,UAAY,CAC1B,OAAO,SAAS,KAAOA,CACzB,EAAI,OACJ,SAAU6X,GAAS2S,CACrB,CAAC,CACH,EACIE,GAAkB,SAAyB5Y,EAAM3Y,EAAO,CAC1D,IAAIma,EAAgBna,EAAM,cACxBoZ,EAAOpZ,EAAM,KACf,OAAI2Y,EAAK,QAAUwB,IAAkBf,GAAS,KAA0B,OAASA,EAAK,UAAY,GACzFe,EAAc,CACnB,GAAIxB,EAAK,OACT,eAAgBA,EAAK,IACvB,CAAC,EAEIA,EAAK,IACd,EACW6Y,GAAgB,SAAuB5C,EAAepW,EAAK,CACpE,IAAIiZ,EAAiB7C,EAAc,IAAIpW,CAAG,EAC1C,GAAI,CAACiZ,EAAgB,CAGnB,IAAIlpB,EAAO,MAAM,KAAKqmB,EAAc,KAAK,CAAC,GAAK,CAAC,EAC5C8C,EAAanpB,EAAK,KAAK,SAAU1B,EAAM,CACzC,GAAI,CACF,OAAIA,GAAS,MAA2BA,EAAK,WAAW,MAAM,EAAU,MACjE,UAAMA,EAAK,QAAQ,IAAK,EAAE,CAAC,EAAE2R,CAAG,CACzC,OAAS7W,EAAO,CACd,eAAQ,IAAI,OAAQkF,EAAMlF,CAAK,EACxB,EACT,CACF,CAEA,EACI+vB,IAAYD,EAAiB7C,EAAc,IAAI8C,CAAU,EAC/D,CACA,OAAOD,GAAkB,CACvB,KAAM,EACR,CACF,EACWE,GAAyB,SAAgC3xB,EAAO,CACzE,IAAImkB,EAAWnkB,EAAM,SACnB4uB,EAAgB5uB,EAAM,cACxB,MAAO,CACL,SAAUmkB,EACV,cAAeyK,CACjB,CACF,EACIgD,GAAyB,SAAgCC,EAAgBjD,EAAe5uB,EAAO,CAEjG,IAAI8xB,EAAed,GAAUa,GAAmB,KAAoC,OAASA,EAAe,QAAQ,EAEhHE,EAAuBD,EAAa,IAAI,SAAUtZ,EAAK,CACzD,IAAIwZ,EAAoBR,GAAc5C,EAAepW,CAAG,EACpDvS,EAAOsrB,GAAgBS,EAAmBhyB,CAAK,EAC/CiyB,EAAmBD,EAAkB,iBACzC,OAAO/rB,GAAQ,CAACgsB,EAAmB,CACjC,SAAUzZ,EACV,eAAgBvS,EAChB,MAAOA,EACP,UAAW+rB,EAAkB,SAC/B,EAAI,CACF,SAAU,GACV,eAAgB,GAChB,MAAO,EACT,CACF,CAAC,EAAE,OAAO,SAAUrZ,EAAM,CACxB,OAAOA,GAAQA,EAAK,QACtB,CAAC,EACD,OAAOoZ,CACT,EAEWG,GAAqB,SAA4BlyB,EAAO,CACjE,IAAImyB,EAAwBR,GAAuB3xB,CAAK,EACtDmkB,EAAWgO,EAAsB,SACjCvD,EAAgBuD,EAAsB,cAIxC,OAAIhO,GAAYA,EAAS,UAAYyK,EAC5BgD,GAAuBzN,EAAUyK,EAAe5uB,CAAK,EAEvD,CAAC,CACV,EAGWoyB,GAAqB,SAA4BpyB,EAAOqyB,EACjE,CAEA,IAAIC,EAAmBtyB,EAAM,iBAC3BuyB,EAAkBvyB,EAAM,WAEtBgrB,EAAQqH,EAAW,iBAAmB,CAAC,EACzCG,EAAkBxH,EAAM,UACxByH,EAAYD,IAAoB,OAAS,EAAIA,EAE3CE,EAAcR,GAAmBlyB,CAAK,EAEtC2yB,EAAa,SAAoBha,EAAM,CAEzC,QADI2O,EAAiBiL,GAAmBpB,GAC/BpjB,EAAO,UAAU,OAAQmB,EAAO,IAAI,MAAMnB,EAAO,EAAIA,EAAO,EAAI,CAAC,EAAGE,EAAO,EAAGA,EAAOF,EAAME,IAClGiB,EAAKjB,EAAO,CAAC,EAAI,UAAUA,CAAI,EAEjC,OAAOqZ,GAAmB,KAAoC,OAASA,EAAe,MAAM,OAAQ,IAAC,QAAc,KAAc,CAAC,EAAG3O,CAAI,EAAG,CAAC,EAAG,CAG9I,KAAMA,EAAK,UAAYA,EAAK,IAC9B,CAAC,CAAC,EAAE,OAAOzJ,CAAI,CAAC,CAClB,EACInC,EAAQ2lB,EAEZ,OAAIJ,IACFvlB,EAAQulB,EAAiBvlB,GAAS,CAAC,CAAC,GAAK,SAGvCA,GAASA,EAAM,OAAS0lB,GAAaH,IAAqB,MAC5DvlB,EAAQ,WAGHoE,KAAgB,GAAW,EAAG,OAAO,EAAI,GAAK,CACnD,MAAOpE,EACP,WAAY4lB,CACd,EAAI,CACF,OAAQ5lB,EACR,WAAY4lB,CACd,CACF,ECtJA,SAASC,GAAYC,EAAU,CAC7B,SAAO,MAAmBA,CAAQ,EAAE,OAAO,SAAUntB,EAAKjF,EAAM,CAC9D,IAAIuqB,KAAQ,KAAevqB,EAAM,CAAC,EAChC8D,EAAMymB,EAAM,CAAC,EACbxmB,EAAMwmB,EAAM,CAAC,EAEf,SAAIzmB,CAAG,EAAIC,EACJkB,CACT,EAAG,CAAC,CAAC,CACP,CACA,IAAIotB,GAAc,SAASA,EAAYlY,EAAQxB,EAAMe,EAAe4Y,EAAgB,CAClF,IAAIC,EAAkB,GAAepY,GAASxB,GAAS,KAA0B,OAASA,EAAK,SAAW,GAAOe,EAAe,EAAI,EAClIc,EAAW+X,EAAgB,SAC3B7W,EAAa6W,EAAgB,WAC/B,OAAKD,EAOED,EAAYC,EAAe9X,CAAQ,EAAG7B,EAAMe,EAAe,MAAS,EANlE,CACL,WAAYyY,GAAYzW,CAAU,EAClC,cAAeA,EACf,SAAUlB,CACZ,CAGJ,E,wBCrBIgY,GAA4B,SAAmCC,EAAa,CAC9E,IAAIxjB,KAAY,YAAS,CAAC,CAAC,EACzBC,KAAa,KAAeD,EAAW,CAAC,EACxCyjB,EAAyBxjB,EAAW,CAAC,EACrCyjB,EAA4BzjB,EAAW,CAAC,EAC1C,sBAAU,UAAY,CACpByjB,KAA0B/jB,MAAc,CAEtC,UAAQ,MAAQ6jB,EAAY,MAAM,IAAM,SAAWA,EAAY,OAAS,OACxE,SAAUA,EAAY,SACtB,WAAYA,EAAY,WACxB,aAAcA,EAAY,aAC1B,iBAAkBA,EAAY,iBAC9B,aAAcA,EAAY,aAC1B,YAAaA,EAAY,WAC3B,CAAC,CAAC,CACJ,EAAG,CAACA,EAAY,OAAQA,EAAY,SAAUA,EAAY,WAAYA,EAAY,aAAcA,EAAY,iBAAkBA,EAAY,aAAcA,EAAY,WAAW,CAAC,EACzKC,CACT,EChBI,GAAY,CAAC,KAAM,gBAAgB,EACrC,GAAa,CAAC,cAAe,WAAY,QAAQ,EA6B/CE,GAAc,EACdzF,GAAe,SAAsB5tB,EAAOslB,EAAe,CAC7D,IAAIgO,EACJ,OAAItzB,EAAM,eAAiB,IAASA,EAAM,KACjC,QAEW,OAAK,MAAQ,QAAc,KAAc,CAC3D,cAAeslB,CACjB,EAAGtlB,CAAK,EAAG,CAAC,EAAG,CACb,SAAUszB,EAAiBtzB,EAAM,WAAa,MAAQszB,IAAmB,OAAS,OAASA,EAAe,MAC5G,CAAC,CAAC,CACJ,EACIC,GAAe,SAAsBvzB,EAAO,CAC9C,OAAIA,EAAM,eAAiB,IAASA,EAAM,KACjC,KAELA,EAAM,aACDA,EAAM,gBAAa,KAAc,CAAC,EAAGA,CAAK,KAAgB,OAAKoB,KAAQ,CAAC,CAAC,CAAC,EAE5E,IACT,EACIoyB,GAAkB,SAAyBxzB,EAAOslB,EAAe,CACnE,IAAImO,EACA5Q,EAAS7iB,EAAM,OACjBqgB,EAAWrgB,EAAM,SACjBumB,EAAevmB,EAAM,aACrBklB,EAAWllB,EAAM,SACjBitB,EAAajtB,EAAM,WACnB0zB,EAA6B1zB,EAAM,2BACnC2zB,EAAa3zB,EAAM,WACrB,GAAIA,EAAM,aAAe,IAASA,EAAM,KACtC,OAAO,KAET,IAAIib,EAAWjb,EAAM,SAGrB,GAAIitB,IAAe/H,IAAa,IAASrC,IAAW,QAAU,CAACxC,EAAU,CACvE,IAAI5f,EAAO8lB,GAAgBjB,EACzB0F,KAAQ,KAAevqB,EAAM,CAAC,EAC9B8D,EAAMymB,EAAM,CAAC,EACf,GAAIzmB,EAAK,CACP,IAAIqvB,EACJ3Y,IAAa2Y,EAAkB5zB,EAAM,YAAc,MAAQ4zB,IAAoB,SAAWA,EAAkBA,EAAgB,KAAK,SAAUjb,EAAM,CAC/I,OAAOA,EAAK,MAAQpU,CACtB,CAAC,KAAO,MAAQqvB,IAAoB,OAAS,OAASA,EAAgB,WAAa,CAAC,CACtF,MACE3Y,EAAW,CAAC,CAEhB,CAEA,IAAImS,EAAgBvP,GAAc5C,GAAY,CAAC,CAAC,EAChD,GAAImS,IAAkBA,GAAkB,KAAmC,OAASA,EAAc,QAAU,IAAMH,GAAcyG,GAC9H,OAAO,KAET,GAAI7Q,IAAW,OAAS,CAACxC,EAAU,CACjC,IAAIwT,EACJ,SAAoB,OAAK,MAAW,QAAc,KAAc,CAC9D,cAAevO,CACjB,EAAGtlB,CAAK,EAAG,CAAC,EAAG,CACb,KAAM,GACN,SAAU6zB,EAAkB7zB,EAAM,WAAa,MAAQ6zB,IAAoB,OAAS,OAASA,EAAgB,KAC/G,CAAC,CAAC,CACJ,CACA,IAAIlH,KAA0B,OAAK,MAAW,QAAc,KAAc,CACxE,cAAerH,CACjB,EAAGtlB,CAAK,EAAG,CAAC,EAAG,CAEb,SAAUotB,EACV,SAAUqG,EAAkBzzB,EAAM,WAAa,MAAQyzB,IAAoB,OAAS,OAASA,EAAgB,KAC/G,CAAC,CAAC,EACF,OAAIE,EACKA,EAAW3zB,EAAO2sB,CAAU,EAE9BA,CACT,EACImH,GAAyB,SAAgCC,EAAW/zB,EAAO,CAC7E,IAAIg0B,EAAkBh0B,EAAM,gBACxBi0B,EAAgBnF,GAAiBiF,CAAS,EAC9C,GAAIC,IAAoB,GACtB,MAAO,CACL,MAAOh0B,EAAM,OAAS,GACtB,GAAI,GACJ,SAAU,EACZ,EAEF,GAAIg0B,EAAiB,CACnB,IAAItV,EAAQsV,EAAgBD,EAAWE,EAAc,MAAOA,CAAa,EACzE,GAAI,OAAOvV,GAAU,SACnB,OAAOoQ,MAAiB,QAAc,KAAc,CAAC,EAAGmF,CAAa,EAAG,CAAC,EAAG,CAC1E,MAAOvV,CACT,CAAC,CAAC,KAEJ1a,OAAQ,OAAO0a,GAAU,SAAU,6DAA6D,CAClG,CACA,OAAOuV,CACT,EACIC,GAAwB,SAA+BC,EAAgB7T,EAAW0H,EAAY,CAChG,OAAImM,EACK7T,EAAY,GAAK0H,EAEnB,CACT,EAOIoM,GAAgB,SAAuBp0B,EAAO,CAChD,IAAIwf,EAAkB6U,EAAoBrV,EAAeC,EAAgBC,EAAgBC,EAAgBC,EAAgB+M,EAAgBC,EAAgBC,EAAgBC,EAAgBC,EAAiBC,EAAiBC,EACvNrB,EAAQprB,GAAS,CAAC,EACpB2a,EAAWyQ,EAAM,SACjBkJ,EAAkBlJ,EAAM,WACxBmJ,EAAiBnJ,EAAM,SACvBjH,EAAWoQ,IAAmB,OAAS,CACrC,SAAU,GACZ,EAAIA,EACJC,EAAepJ,EAAM,aACrBthB,EAAQshB,EAAM,MACdqJ,EAAmBrJ,EAAM,iBACzBvqB,GAAQuqB,EAAM,MACdsJ,EAAkBtJ,EAAM,WACxBhS,EAAOgS,EAAM,KACbuJ,GAAgBvJ,EAAM,cACtBwJ,GAAwBxJ,EAAM,iBAC9B2H,EAAiB3H,EAAM,eACvByJ,GAAYzJ,EAAM,UAClB0J,EAAkB1J,EAAM,gBACxB2J,GAAqB3J,EAAM,cAC3B4J,GAAU5J,EAAM,QACdpD,MAAa,WAAQ,UAAY,CACnC,OAAI0M,IACA10B,EAAM,SAAW,MAAc,IAC5B,IACT,EAAG,CAACA,EAAM,OAAQ00B,CAAe,CAAC,EAC9B5zB,MAAU,cAAW,mBAA4B,EACjDR,IAAakf,EAAmBxf,EAAM,aAAe,MAAQwf,IAAqB,OAASA,EAAmB1e,GAAQ,aAAa,KAAK,EACxIglB,MAAsB,KAAmB,GAAO,CAChD,MAAO1M,GAAS,KAA0B,OAASA,EAAK,QACxD,SAAUA,GAAS,KAA0B,OAASA,EAAK,eAC7D,CAAC,EACD2M,MAAuB,KAAeD,GAAqB,CAAC,EAC5DmP,GAAclP,GAAqB,CAAC,EACpCmP,GAAiBnP,GAAqB,CAAC,EAGrCrW,MAAY,YAAS,UAAY,CACjC,WAAe,EACR,cAAc,OAAO2jB,EAAW,CACzC,CAAC,EACD1jB,MAAa,KAAeD,GAAW,CAAC,EACxCylB,GAAYxlB,GAAW,CAAC,EAOtBwK,MAAgB,eAAY,SAAUib,GAAO,CAC/C,IAAIC,GAAKD,GAAM,GACbE,GAAiBF,GAAM,eACvBzK,MAAa,MAAyByK,GAAO,EAAS,EACxD,GAAIL,GACF,OAAOA,MAAmB,KAAc,CACtC,GAAIM,GACJ,eAAgBC,EAClB,EAAG3K,EAAU,CAAC,EAEhB,IAAI2E,GAAUG,GAAc,EAC5B,OAAOH,GAAQ+F,EAAE,EAAI/F,GAAQ+F,EAAE,EAAIC,EACrC,EAAG,CAACP,EAAkB,CAAC,EACnBQ,MAAU,OAAO,CAACJ,GAAW/b,GAAS,KAA0B,OAASA,EAAK,MAAM,EAAgB,UAAY,CAChH,IAAIoc,MAAQ,SAAgC,MAAoB,EAAE,KAAK,SAAS3mB,GAAQ4mB,GAAO,CAC7F,IAAIC,GACAC,GAAO7sB,GAAQ8sB,GACnB,SAAO,MAAoB,EAAE,KAAK,SAAkB9mB,GAAU,CAC5D,OAAU,OAAQA,GAAS,KAAOA,GAAS,KAAM,CAC/C,IAAK,GACH,aAAQ,KAAe2mB,GAAO,CAAC,EAAG3sB,GAAS6sB,GAAM,CAAC,EAClDT,GAAe,EAAI,EACnBpmB,GAAS,KAAO,EACTsK,GAAS,OAA4Bsc,GAAgBtc,EAAK,WAAa,MAAQsc,KAAkB,OAAS,OAASA,GAAc,KAAKtc,EAAMtQ,IAAU,CAAC,GAAIgB,GAAU,KAA2B,OAASA,EAAM,YAAcA,GAAU,KAA2B,OAASA,EAAM,SAAW,CAAC,CAAC,EACvS,IAAK,GACH,UAAgBgF,GAAS,KACzBomB,GAAe,EAAK,EACbpmB,GAAS,OAAO,SAAU8mB,EAAa,EAChD,IAAK,GACL,IAAK,MACH,OAAO9mB,GAAS,KAAK,CACzB,CACF,EAAGD,EAAO,CACZ,CAAC,CAAC,EACF,OAAO,SAAUqc,GAAI,CACnB,OAAOsK,GAAM,MAAM,KAAM,SAAS,CACpC,CACF,EAAE,EAAG,CACH,kBAAmB,GACnB,mBAAoB,GACpB,sBAAuB,EACzB,CAAC,EACDttB,GAAOqtB,GAAQ,KACfM,GAASN,GAAQ,OACjB/zB,GAAY+zB,GAAQ,aACtB,aAAU,UAAY,CACpBL,GAAe1zB,EAAS,CAE1B,EAAG,CAACA,EAAS,CAAC,EACd,IAAIs0B,MAAgB,OAAa,EAC/BC,GAAQD,GAAc,SACxB,aAAU,UAAY,CACpB,OAAO,UAAY,CACbC,cAAiB,KAAKA,GAAM,OAAOZ,EAAS,CAClD,CAEF,EAAG,CAAC,CAAC,EACL,IAAIa,MAAe,WAAQ,UAAY,CACrC,OAAOlD,GAAY5qB,KAAS4B,GAAU,KAA2B,OAASA,EAAM,YAAcA,GAAU,KAA2B,OAASA,EAAM,SAAW,CAAC,EAAGsP,EAAMe,GAAe4Y,CAAc,CACtM,EAAG,CAAC5Y,GAAef,EAAM2Z,EAAgB7qB,GAAM4B,GAAU,KAA2B,OAASA,EAAM,SAAUA,GAAU,KAA2B,OAASA,EAAM,MAAM,CAAC,EACpKmsB,GAAQD,IAAgB,CAAC,EAC3B7Z,GAAa8Z,GAAM,WACnBrH,GAAgBqH,GAAM,cACtBC,GAAiBD,GAAM,SACvBhb,GAAWib,KAAmB,OAAS,CAAC,EAAIA,GAC1CrB,IAAazb,IAAS,MAAQA,IAAS,QAAUA,EAAK,UACxDyb,GAAU,QAAU,CAClB,OAAQ,UAAkB,CACxBgB,GAAO,CACT,CACF,GAEF,IAAIM,MAAa,WAAQ,UAAY,CACnC,OAAO,GAAahS,EAAS,UAAY,IAAKlJ,IAAY,CAAC,EAAG,EAAI,CACpE,EAAG,CAACkJ,EAAS,SAAUlJ,EAAQ,CAAC,EAC5BqK,MAAgB,WAAQ,UAAY,CACtC,OAAO,MAAM,KAAK,IAAI,IAAI6Q,GAAW,IAAI,SAAUxd,GAAM,CACvD,OAAOA,GAAK,KAAOA,GAAK,MAAQ,EAClC,CAAC,CAAC,CAAC,CACL,EAAG,CAACwd,EAAU,CAAC,EAGXjD,GAAciD,GAAWA,GAAW,OAAS,CAAC,GAAK,CAAC,EACpDhD,GAAyBF,GAA0BC,EAAW,EAC9DkD,MAAwB,QAAc,KAAc,CAAC,EAAGp2B,CAAK,EAAGmzB,EAAsB,EACxFvL,GAAcwO,GAAsB,YACpCC,GAAWD,GAAsB,SACjCE,GAAcF,GAAsB,OACpClnB,MAAO,MAAyBknB,GAAuB,EAAU,EAC/DG,GAAUlmB,EAAc,EACxBgQ,MAAW,WAAQ,UAAY,CACjC,OAAQkW,KAAY,MAAQA,KAAY,OAAS,CAACv2B,EAAM,aAC1D,EAAG,CAACu2B,GAASv2B,EAAM,aAAa,CAAC,EAK7Bm0B,GAAiBmC,KAAgB,OAAS,CAACjW,GAC3CmW,MAAkBC,KAAe,UAAY,CAC7C,OAAIhC,IAAqB,OAAkBA,EAEvC,OACA8B,KAAY,KAElB,EAAG,CACD,MAAOv2B,EAAM,UACb,SAAUs0B,CACZ,CAAC,EACDoC,MAAmB,KAAeF,GAAiB,CAAC,EACpDlW,GAAYoW,GAAiB,CAAC,EAC9BtS,GAAasS,GAAiB,CAAC,EAG7BC,MAAe,SAAK,QAAc,QAAc,KAAc,CAChE,UAAWr2B,EACb,EAAGN,CAAK,EAAG,CAAC,EAAG,CACb,WAAYgoB,EACd,EAAGmL,EAAsB,EAAG,CAAC,EAAG,CAC9B,cAAehZ,GACf,WAAYgC,GACZ,QAAM,QAAc,KAAc,CAAC,EAAG/C,CAAI,EAAG,CAAC,EAAG,CAC/C,KAAMub,KAAkBvb,GAAS,KAA0B,OAASA,EAAK,MACzE,QAAS6b,EACX,CAAC,EACD,OAAQqB,EACV,CAAC,EAAG,CAAC,YAAa,QAAS,kBAAkB,CAAC,EAG1CrC,GAAgBH,MAAuB,QAAc,KAAc,CACrE,SAAU3P,EAAS,QACrB,EAAGwS,EAAY,EAAG,CAAC,EAAG,CACpB,cAAe/H,EACjB,CAAC,EAAG5uB,CAAK,EAGL42B,GAAkBxE,MAAmB,QAAc,KAAc,CAAC,EAAGuE,EAAY,EAAG,CAAC,EAAG,CAC1F,iBAAkB32B,EAAM,iBACxB,cAAe4uB,EACjB,CAAC,EAAG5uB,CAAK,EAGL62B,GAAerD,MAAgB,QAAc,KAAc,CAAC,EAAGmD,EAAY,EAAG,CAAC,EAAG,CACpF,SAAU1b,GACV,WAAYmJ,GACZ,SAAU/D,GACV,UAAWC,EACb,CAAC,EAAGgF,EAAa,EAGb0D,GAAY4E,MAAa,QAAc,KAAc,CAAC,EAAG+I,EAAY,EAAG,CAAC,EAAG,CAC9E,SAAU,KACV,aAAc,CAAC,CAACE,GAChB,SAAU5b,GACV,SAAUoF,GACV,UAAWC,GACX,WAAY8D,EACd,CAAC,EAAGkB,EAAa,EAGbwR,GAAYvD,MAAa,KAAc,CACzC,SAAUlT,GACV,UAAWC,EACb,EAAGqW,EAAY,CAAC,EACZvxB,MAAc,cAAWtD,IAAY,EACvCi1B,GAA0B3xB,GAAY,iBAGpC4xB,GAAmBpC,KAA0B,OAAYA,GAAwBmC,GACjFE,GAAqB,GAAG,OAAO32B,GAAW,SAAS,EACnDU,GAAY,GAASi2B,EAAkB,EACzCh2B,GAAUD,GAAU,QACpBE,GAASF,GAAU,OAGjBN,GAAY,KAAWV,EAAM,UAAWkB,GAAQ,iBAAkB+1B,MAAoB,QAAgB,QAAgB,QAAgB,QAAgB,KAAgB,CAAC,EAAG,UAAU,OAAOV,EAAO,EAAGA,EAAO,EAAG,GAAG,OAAOU,GAAoB,WAAW,EAAGX,KAAgB,KAAK,EAAG,GAAG,OAAOW,GAAoB,cAAc,EAAGD,EAAgB,EAAG,GAAG,OAAOC,GAAoB,eAAe,EAAGrP,EAAW,EAAG,GAAG,OAAOqP,GAAoB,GAAG,EAAE,OAAOX,EAAW,EAAGA,EAAW,CAAC,EAGvdY,GAAiBhD,GAAsB,CAAC,CAACC,GAAgB7T,GAAW0H,EAAU,EAG9EmP,GAAiB,CACnB,SAAU,UACZ,GAGIH,IAAoBxC,GAAgBA,EAAa,aACnD2C,GAAe,UAAY,MAI7B,aAAU,UAAY,CACpB,IAAIC,IACHA,GAAsBp3B,EAAM,gBAAkB,MAAQo3B,KAAwB,QAAUA,GAAoB,KAAKp3B,EAAOA,EAAM,QAAQ,CAEzI,EAAG,CAACmkB,EAAS,UAAWkQ,EAAqBlQ,EAAS,YAAc,MAAQkQ,IAAuB,OAAS,OAASA,EAAmB,MAAM,CAAC,EAC/I,IAAInS,MAAa,YAAS,EAAK,EAC7BC,MAAa,KAAeD,GAAY,CAAC,EACzCmV,GAAmBlV,GAAW,CAAC,EAC/BmV,GAAsBnV,GAAW,CAAC,EAIhCoV,MAAa,YAAS,CAAC,EACzBC,MAAa,KAAeD,GAAY,CAAC,EACzCna,GAAmBoa,GAAW,CAAC,EAC/BC,GAAsBD,GAAW,CAAC,EACpC1mB,EAAiBmjB,GAAej0B,EAAM,OAAS,EAAK,EACpD,IAAIgsB,MAAe,cAAW,IAAW,EACvC3rB,GAAQ2rB,GAAa,MACnB0L,MAAiB,WAAQ,UAAY,CACvC,OAAI5C,GAAmBA,EAAgB,OAAS,EACvCA,GAAoB,KAAqC,OAASA,EAAgB,IAAI,SAAUnc,GAAM9T,GAAO,CAClH,SAAoB,OAAK,MAAO,CAC9B,IAAK8T,GAAK,IACV,SAAO,KAAc,CACnB,SAAU,UACZ,EAAGA,EAAI,CACT,EAAG9T,EAAK,CACV,CAAC,EAEI,IACT,EAAG,CAACiwB,CAAe,CAAC,EACpB,OAAO7zB,MAAsB,OAAKa,KAAa,SAAU,CACvD,SAAO,QAAc,KAAc,CAAC,EAAG60B,EAAY,EAAG,CAAC,EAAG,CACxD,WAAYC,GACZ,SAAU3b,GACV,SAAUoF,GACV,UAAWC,GACX,iBAAkBlD,GAClB,oBAAqBqa,GACrB,iBAAkB,GAClB,MAAOxD,GAAc,SACrB,aAAc,CAAC,CAAC4C,GAChB,UAAW,CAAC,CAAC7N,GACb,WAAYkO,GACZ,UAAW,CAAC,CAACJ,GACb,iBAAkBO,GAClB,oBAAqBC,GACrB,cAAerD,GACf,WAAYkC,GACZ,cAAe7Q,GACf,YAAa4N,EACf,CAAC,EACD,SAAUlzB,EAAM,QAAoB,OAAK,WAAW,CAClD,SAAU2a,CACZ,CAAC,KAAiB,QAAM,MAAO,CAC7B,UAAWja,GACX,SAAU,CAACg3B,KAAmB1Y,EAAgB3e,GAAM,UAAY,MAAQ2e,IAAkB,QAAUA,EAAc,YAAwB,OAAK,MAAO,CACpJ,UAAW,KAAW,GAAG,OAAOiY,GAAoB,UAAU,EAAG/1B,EAAM,EACvE,SAAUw2B,EACZ,CAAC,EAAI,QAAmB,QAAM,KAAQ,CACpC,SAAO,KAAc,CACnB,UAAW,OAEX,cAAeb,GAAe,MAAQ,MACxC,EAAGh2B,EAAK,EACR,SAAU,IAAc,OAAK,MAE3B,CACA,MAAO,CACL,UAAQ,MAAe,EACvB,MAAO,CACL,kBAAmBoe,EAAiB5e,GAAM,UAAY,MAAQ4e,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,cAAgB5e,IAAU,KAA2B,OAASA,GAAM,gBACzQ,EACA,WAAY,CACV,KAAM6Q,EAAgB,CACpB,cAAegO,EAAiB7e,GAAM,UAAY,MAAQ6e,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,sBAAwB,cACvN,iBAAkBC,EAAiB9e,GAAM,UAAY,MAAQ8e,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,sBAAwB,cAC1N,WAAY9e,GAAM,aAClB,sBAAuB+e,EAAiB/e,GAAM,UAAY,MAAQ+e,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,2BAA6B/e,IAAU,KAA2B,OAASA,GAAM,kBACxR,mBAAoB8rB,EAAiB9rB,GAAM,UAAY,MAAQ8rB,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,wBAA0B9rB,IAAU,KAA2B,OAASA,GAAM,kBAClR,oBAAqB+rB,EAAiB/rB,GAAM,UAAY,MAAQ+rB,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,yBAA2B/rB,IAAU,KAA2B,OAASA,GAAM,mBACpR,gCAAiCgsB,EAAiBhsB,GAAM,UAAY,MAAQgsB,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,2BAA6BhsB,IAAU,KAA2B,OAASA,GAAM,kBAClS,oBAAqB,EACrB,qBAAsB,EACtB,yBAA0B,EAC1B,gBAAiBisB,EAAiBjsB,GAAM,UAAY,MAAQisB,IAAmB,SAAWA,EAAiBA,EAAe,SAAW,MAAQA,IAAmB,OAAS,OAASA,EAAe,iBAAmBjsB,IAAU,KAA2B,OAASA,GAAM,oBACxQ,qBAAsBksB,EAAkBlsB,GAAM,UAAY,MAAQksB,IAAoB,SAAWA,EAAkBA,EAAgB,SAAW,MAAQA,IAAoB,OAAS,OAASA,EAAgB,yBAA2B,sBAEvO,wBAAyBC,EAAkBnsB,GAAM,UAAY,MAAQmsB,IAAoB,SAAWA,EAAkBA,EAAgB,SAAW,MAAQA,IAAoB,OAAS,OAASA,EAAgB,wBAA0B,mBACzO,QAASnsB,IAAU,KAA2B,OAASA,GAAM,gBAC7D,cAAeA,IAAU,KAA2B,OAASA,GAAM,gBACnE,kBAAmB,cACnB,YAAaA,IAAU,KAA2B,OAASA,GAAM,eACnE,CAAC,CACH,CACF,EACA,SAAUw2B,EACZ,CAAC,KAAgB,QAAM,MAAO,CAC5B,MAAOM,GACP,UAAW,GAAG,OAAOF,GAAoB,aAAa,EAAE,OAAO/1B,EAAM,EAAE,KAAK,EAC5E,SAAU,CAAC8nB,MAAwB,OAAK9L,MAAa,QAAc,KAAc,CAC/E,iBAAkBE,GAClB,iBAAkB4Z,EACpB,EAAG9nB,EAAI,EAAG,CAAC,EAAG,CACZ,UAAW,CAAC,CAAC8Z,GACb,UAAWiO,GACX,MAAOzC,EACP,SAAUQ,MAAuB,OAAKzzB,KAAa,CAAC,CAAC,EAAIoZ,CAC3D,CAAC,CAAC,EAAGmc,GAAWO,OAAiC,OAAK,MAAO,CAC3D,UAAW,GAAG,OAAOJ,GAAoB,aAAa,EACtD,MAAO,CACL,OAAQ,GACR,kBAAmBxK,EAAkBpsB,GAAM,UAAY,MAAQosB,IAAoB,SAAWA,EAAkBA,EAAgB,iBAAmB,MAAQA,IAAoB,OAAS,OAASA,EAAgB,gCACnN,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,CAAC,CACJ,CAAC,CACH,CAAC,CAAC,CACJ,EACIkL,GAAY,SAAmB33B,EAAO,CACxC,IAAI43B,EAAe53B,EAAM,aACrB63B,EAAY73B,EAAM,WAAa,OAAY,CAC7C,KAAMA,EAAM,WAAa,UAC3B,EAAI,CAAC,EACL,SAAoB,OAAK,MAAgB,CACvC,MAAO43B,EAAe,CACpB,MAAO,CACL,aAAcA,CAChB,CACF,EAAI,OACJ,YAAuB,OAAK,QAAmB,QAAc,KAAc,CAAC,EAAGC,CAAS,EAAG,CAAC,EAAG,CAC7F,MAAO73B,EAAM,MACb,UAAWA,EAAM,UACjB,YAAuB,OAAKo0B,MAAe,QAAc,KAAc,CACrE,QAAmB,OAAK7W,GAAM,CAAC,CAAC,CAClC,EAAGoE,EAAe,EAAG,CAAC,EAAG,CACvB,YAAUvS,KAAU,EAAI,OAAO,SAAW,MAC5C,EAAGpP,CAAK,CAAC,CACX,CAAC,CAAC,CACJ,CAAC,CACH,ECzgBM83B,GAAqB,UAAM,CAC/B,SACEC,eACEC,MAAM,6BACN/M,MAAM,KACNgN,OAAO,KACPzX,QAAQ,cAAa7F,aAErBod,6BACEA,0BACE1C,GAAG,mBACH6C,GAAG,UACHC,GAAG,WACHC,GAAG,KACHC,GAAG,UAAS1d,aAEZ2d,eAAMC,OAAO,KAAKC,UAAU,SAAS,CAAO,KAC5CF,eAAMC,OAAO,OAAOC,UAAU,SAAS,CAAO,CAAC,EACjC,KAChBT,0BACE1C,GAAG,mBACH6C,GAAG,UACHC,GAAG,UACHC,GAAG,KACHC,GAAG,WAAU1d,aAEb2d,eAAMC,OAAO,KAAKC,UAAU,SAAS,CAAO,KAC5CF,eAAMC,OAAO,SAASC,UAAU,SAAS,CAAO,KAChDF,eAAMC,OAAO,OAAOC,UAAU,SAAS,CAAO,CAAC,EACjC,KAChBT,0BACE1C,GAAG,mBACH6C,GAAG,UACHC,GAAG,UACHC,GAAG,WACHC,GAAG,WAAU1d,aAEb2d,eAAMC,OAAO,KAAKC,UAAU,SAAS,CAAO,KAC5CF,eAAMC,OAAO,UAAUC,UAAU,SAAS,CAAO,KACjDF,eAAMC,OAAO,OAAOC,UAAU,SAAS,CAAO,CAAC,EACjC,KAChBT,0BACE1C,GAAG,mBACH6C,GAAG,UACHC,GAAG,SACHC,GAAG,WACHC,GAAG,WAAU1d,aAEb2d,eAAMC,OAAO,KAAKC,UAAU,SAAS,CAAO,KAC5CF,eAAMC,OAAO,UAAUC,UAAU,SAAS,CAAO,KACjDF,eAAMC,OAAO,OAAOC,UAAU,SAAS,CAAO,CAAC,EACjC,CAAC,EACb,KACNF,YAAGG,KAAK,OAAOC,SAAS,UAAUC,OAAO,OAAOC,YAAY,IAAGje,YAC7D2d,YAAGO,UAAU,qBAAoBle,YAC/B2d,YAAGO,UAAU,mBAAkBle,YAC7Bod,0BACEA,aAAGW,SAAS,UAAS/d,aACnBod,0BACEO,eACEG,KAAK,yBACL5lB,EAAE,2bAA2b,CACxb,KACPylB,eACEG,KAAK,yBACL5lB,EAAE,oZAAoZ,CACjZ,CAAC,EACP,KACHylB,eACEG,KAAK,yBACL5lB,EAAE,+QAA+Q,CAC5Q,CAAC,EACP,KACHylB,kBACEQ,GAAG,UACHC,GAAG,UACHN,KAAK,yBACLO,GAAG,OACHC,GAAG,QAAQ,CACH,CAAC,EACV,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACD,CAET,EAEA,GAAenB,G,wBCtFToB,GAOD,SAACl5B,EAAO,CAAF,cAER,CAACA,EAAM8J,QAAU9J,EAAMm5B,SAAWn5B,EAAMo5B,aAExCC,IAAMvvB,SAAK,wBAAXuvB,EAAaC,gBAAiBt5B,EAAMu5B,cAAgBv5B,EAAMw5B,gBAEzD,CAACx5B,EAAM8J,SAAK2vB,EAAIz5B,EAAM8J,SAAK,wBAAX2vB,EAAaH,mBAC7BhB,OAACoB,MAAM,CACLC,OAAQ35B,EAAM8J,MAAQ,MAAQ,MAC9B4U,MAAO1e,EAAM8J,MAAQ,MAAQ,MAC7B8vB,SAAU55B,EAAM8J,MAAQ,qEAAgB,2EACxC+vB,SACEvB,OAACwB,MAAM,CAAC/yB,KAAK,UAAUzE,QAAS,kBAAMy3B,UAAQC,KAAK,GAAG,CAAC,EAACrf,SAAC,0BAEzD,CAAQ,CACT,CACF,GAGH3a,EAAM2a,QAAQ,EAGhB,GAAeue,G,gDC5BR,SAASe,GAAuBC,EAKnC,eACF,GAAIA,EAAKC,cAAcC,YACrB,OAAOF,EAAKC,cAAcC,YACxBF,EAAKG,aACLH,EAAKI,gBACLJ,EAAKC,aACP,EAGF,IAAMI,IAAaC,IAAKH,gBAAY,wBAAjBG,EAAmBC,WAAMC,EAAIR,EAAKG,gBAAY,wBAAjBK,EAAmBz0B,OAAQi0B,EAAKC,cAAcQ,OACxFC,IAAmBC,IAAKR,gBAAY,wBAAjBQ,EAAmBJ,UAAW,GACjDK,EAAgBF,EAAmB,2DAA6D,yBAChGH,EACJF,KACExC,gBAAMr3B,UAAU,2BAA0Bia,UACtCigB,EAWI,QATFtC,OAACyC,KAAM,CACLC,KAAK,QACLt6B,UAAU,2BACVu6B,MACEC,IAAKb,gBAAY,wBAAjBa,EAAmBT,SACnB,iFAEFU,IAAI,QAAQ,CACb,KAEL7C,eAAM53B,UAAWo6B,EAAcngB,YAAEuf,EAAKG,gBAAY,wBAAjBe,EAAmBn1B,IAAI,CAAO,CAAC,EAC5D,EACJ,KAGN,GAAIi0B,EAAKlF,QACP,SACEsD,cAAK53B,UAAU,0BAAyBia,YACtC2d,OAAC+C,KAAI,CAACL,KAAK,QAAQn6B,MAAQ,CAAEy6B,WAAY,EAAGC,YAAa,CAAE,CAAG,CAAE,CAAC,CAC9D,EAMT,IAAMC,EAAW,CACf96B,UAAW,yBACX6lB,aAAc,CAAC,EACfxZ,MAAO,CACL,CACExI,IAAK,SACLk3B,SACE1D,iCACEO,OAACoD,KAAc,EAAE,EAAC,0BAEpB,EAAE,EAEJp5B,QAAS,UAAM,SACb43B,GAAI,SAAJA,EAAMC,iBAAa,qBAAnBwB,EAAqBhB,UAAM,kBAA3BiB,SAA8B1B,EAAKG,YAAY,CACjD,CACF,CAAC,CAEL,EAEIwB,EACJ,OAAIlM,KAAQmM,WAAW,IAAI,GAAKnM,KAAQmM,WAAW,OAAO,EACxDD,EAAgB,CAAEziB,KAAMoiB,CAAS,EACxB7L,KAAQmM,WAAW,IAAI,EAChCD,EAAgB,CACdE,WACEzD,OAAC0D,KAAI,UACFR,EAASzuB,MAAMiQ,IAAI,SAACrE,EAAM,CAAF,SACvB2f,OAAC0D,KAAKC,KAAI,CAAgB35B,QAASqW,EAAKrW,QAAQqY,SAC7ChC,EAAK8iB,KAAK,EADG9iB,EAAKpU,GAEV,CAAC,CACb,CAAC,CACE,CAEV,EAEAs3B,EAAgB,CAAEE,WAASzD,OAAC0D,KAAI5jB,QAAKojB,CAAQ,CAAG,CAAE,KAMlDzD,eAAKr3B,UAAU,kCAAiCia,UAC7Cuf,EAAKC,cAAcQ,UAClBrC,OAAC4D,KAAQ9jB,aAAKyjB,CAAa,MAAEM,iBAAiB,8BAA6BxhB,SACxE8f,CAAM,CAAC,CACA,EAEVA,KAEFnC,OAAC8D,MAAU,EAAE,CAAC,EACX,CAET,C,4BCpFMC,GAAe,SAAfA,EAAgBzhB,EAAkB0hB,EAAyC,CAC/E,GAAI1hB,EAAO2hB,SAAW,EACpB,MAAO,CAAC,EAGV,IAAIC,EAAY,CAAC,EAACjhB,OACEX,CAAM,QAA1B,2BAA4B,KAAjB9Q,EAAK0R,QACRihB,EAAQrkB,QAAOtO,CAAK,EACtBwyB,EAASxyB,CAAK,EACZ4yB,MAAMC,QAAQF,EAAS7hB,MAAM,GAC/B4hB,EAAUxC,KAAI4C,MAAdJ,EAASxlB,KAASqlB,EAAaI,EAAS7hB,OAAQ0hB,CAAQ,CAAC,CAAC,GAGxDI,MAAMC,QAAQF,EAAS9hB,QAAQ,IACjC8hB,EAAS9hB,SAAW0hB,EAAaI,EAAS9hB,SAAU2hB,CAAQ,EAC5DG,EAAS7hB,OAAS6hB,EAAS9hB,UAE7B6hB,EAAUxC,KAAKyC,CAAQ,EAE3B,CAAC,+BAED,OAAOD,CACT,EAGMK,GAAY,SAAZA,EAAajiB,EAAqB,CACtC,OAAIA,EAAO2hB,SAAW,EACb,CAAC,EAEH3hB,EAAOoC,IAAI,WAAS,CAEzB,IAAMyf,EAAQrkB,QAAOtO,CAAK,EAC1B,OAAIA,EAAMgzB,aACRL,EAAS51B,KAAOiD,EAAMgzB,YAGpBJ,MAAMC,QAAQ7yB,EAAM8Q,MAAM,IAC5B6hB,EAAS7hB,OAASiiB,EAAU/yB,EAAM8Q,MAAM,GAGtC8hB,MAAMC,QAAQ7yB,EAAM6Q,QAAQ,IAC9B8hB,EAAS9hB,SAAWkiB,EAAU/yB,EAAM6Q,QAAQ,GAGvC8hB,CACT,CAAC,CACH,EAEA,GAAe,SAACz8B,EAAe,CAC7B,IAAMmkB,KAAW4Y,eAAY,EACvBC,KAAWC,eAAY,EAC7BC,KAAwCC,cAAW,EAA3CC,EAAYF,EAAZE,aAAcC,EAAaH,EAAbG,cAChBC,EAAeC,SAAYA,MAAS,gBAAgB,GAAM,CAC9DlD,aAAcmD,OACdxI,QAAS,GACTsF,gBAAiB,IACnB,EACQD,EAA2CiD,EAA3CjD,aAAcrF,EAA6BsI,EAA7BtI,QAASsF,EAAoBgD,EAApBhD,gBACzBmD,EAAa,CACnB,OAAU,GACV,SAAY,QACZ,aAAgB,UAChB,OAAU,OACV,aAAgB,QAChB,YAAe,GACf,YAAe,GACf,IAAO,GACP,KAAQ,YACR,WAAc,GACd,cAAiB,MACjB,UAAa,GACb,MAAS,UACT,WAAc,IACd,YAAe,GACf,MAAS,CAAC,CACZ,EACAC,KAA0BC,OAAQ,EAA1BxjB,EAAaujB,EAAbvjB,cACAggB,EAAgBkD,EAAcO,aAAa,CAC/Cr5B,IAAK,SACLwC,KAAM,SACN82B,aAAYzlB,QACPklB,CAAW,CAElB,CAAC,EAIKd,EAAYH,GAAae,EAAaU,OAAO,WAAK,QAAIh0B,EAAMurB,KAAO,uBAAuB,GAAG,SAACvrB,EAAU,CAC5G,MAAQ,CAAC,CAACA,EAAMi0B,UAAYj0B,EAAMurB,KAAO,yBAA4B,CAAC,CAACvrB,EAAMk0B,SAC/E,CAAC,EACDC,KAAgBC,OAAsBrB,GAAUL,CAAS,CAAC,EAAC2B,WAApDr0B,EAAKq0B,KAENC,KAAeC,WAAQ,+BAAMC,eAAYx0B,EAAM6Q,SAAUwJ,EAASvb,QAAQ,KAAC,qBAA9C21B,EAAgDC,OAAG,qBAAnDC,QAAsD,KAAC,wBAAvDA,EAAyD30B,KAAK,EAAE,CAACqa,EAASvb,QAAQ,CAAC,EAEtH,SACE0vB,OAACX,GAASvf,WACRtO,MAAOA,EACPqa,SAAUA,EACVzF,MAAO+e,EAAW/e,OAAS,cAC3B2X,SAAS,OACTrO,WAAY,IACZC,kBAAmB,SAAClY,EAAM,CACxBA,EAAE2uB,gBAAgB,EAClB3uB,EAAE4uB,eAAe,EACjB3B,EAAS,GAAG,CACd,EACA7iB,cAAesjB,EAAWtjB,eAAiBA,EAC3Cf,KAAM,CAAEL,OAAQ0kB,EAAW1kB,MAAO,EAClC0F,KAAMlB,GACN8G,eAAgB,SAACua,EAAejS,EAAe,CAC7C,OAAIiS,EAAcnmB,OAASmmB,EAAcjkB,SAChCgS,EAELiS,EAAc/3B,MAAQsd,EAASvb,WAAag2B,EAAc/3B,QAG1DyxB,OAACuG,OAAI,CAACC,GAAIF,EAAc/3B,KAAKk4B,QAAQ,KAAM,EAAE,EAAGr8B,OAAQk8B,EAAcl8B,OAAOiY,SAC1EgS,CAAU,CACP,EAGHA,CACT,EACAgG,WAAY,SAAC7oB,EAAOsnB,EAAGxW,EAAW,CAChC,IAAQyW,EAAgCvnB,EAAhCunB,eAAgB3S,GAAgB5U,EAAhB4U,MAAO7X,EAASiD,EAATjD,KACzB40B,EAAQ/c,IAAS2S,EACjBC,GAAO1W,EAAOA,EAAO2hB,OAAS,CAAC,EACrC,OAAIjL,KACEA,GAAKzqB,OAASA,GAAQyqB,GAAK0N,WAAan4B,MACnCyxB,wBAAOmD,CAAK,CAAO,KAGvBnD,OAACuG,OAAI,CAACC,GAAIj4B,EAAK8T,SAAE8gB,CAAK,CAAO,CACtC,EACAwD,qBAAoB,GACpBrX,YAAW,GACX+F,YAAW,IACPwM,CAAa,MACjB1R,mBACE0R,EAAc1R,qBAAuB,IACpC,SAACyW,EAAgB,CAChB,IAAM3V,EAAM0Q,GAAsB,CAChCE,gBACAnF,UACAqF,eACAC,iBACF,CAAC,EACD,OAAIH,EAAc1R,mBACT0R,EAAc1R,mBAAmByW,EAAa3V,EAAK,CAExDkU,aACAtD,gBACAnF,UACAqF,eACAC,iBACF,CAAC,EAEI/Q,CACT,EACD5O,YAED2d,OAACY,GAAS,CACRpvB,MAAOs0B,EACPjF,QAASgB,GAAa,YAAbA,EAAehB,QACxBC,SAAUe,GAAa,YAAbA,EAAef,SACzBG,aAAcY,GAAa,YAAbA,EAAeZ,aAC7BC,aAAcW,GAAa,YAAbA,EAAeX,aAAa7e,SAEzCwf,EAAcgF,eACXhF,EAAcgF,kBAAe7G,OAAC8G,SAAM,EAAE,EAAGp/B,CAAK,KAC9Cs4B,OAAC8G,SAAM,EAAE,CAAC,CAEL,CAAC,EACH,CAEf,C", "sources": ["webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@ant-design/icons/es/icons/CopyrightOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/GlobalFooter/style.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/GlobalFooter/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/Footer.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/PageLoading/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/context/RouteContext.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@ant-design/icons/es/components/IconBase.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@ant-design/icons/es/components/twoTonePrimaryColor.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@ant-design/icons/es/components/AntdIcon.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@ant-design/icons/es/components/Context.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@ant-design/icons/es/utils.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@umijs/route-utils/es/path-to-regexp.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/path-to-regexp/dist/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-utils/es/compareVersions/openVisibleCompatible.js", "webpack://labwise-web/./node_modules/@ant-design/pro-utils/es/components/ErrorBoundary/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-utils/es/hooks/useDebounceFn/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-utils/es/hooks/useRefFunction/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-utils/es/isBrowser/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-utils/es/omitUndefined/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-utils/es/useMediaQuery/query.js", "webpack://labwise-web/./node_modules/@ant-design/pro-utils/es/useMediaQuery/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-utils/es/hooks/useDocumentTitle/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-utils/es/compareVersions/coverToNewToken.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@umijs/route-utils/es/sha265.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@umijs/route-utils/es/transformRoute/transformRoute.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@umijs/route-utils/es/getFlatMenus/getFlatMenus.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@umijs/route-utils/es/getMatchMenu/getMatchMenu.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/WrapContent.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/assert/Logo.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/utils/utils.js", "webpack://labwise-web/./node_modules/@ant-design/icons-svg/es/asn/MenuOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@ant-design/icons/es/icons/MenuOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/AppsLogo.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/DefaultContent.js", "webpack://labwise-web/./node_modules/@ant-design/pro-utils/es/isUrl/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/SimpleContent.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/style/default.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/style/simple.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/style/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/AppsLogoComponents/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/SiderMenu/Arrow.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/CollapsedIcon/style.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/CollapsedIcon/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@ant-design/icons/es/components/Icon.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/node_modules/@ant-design/icons/es/components/IconFont.js", "webpack://labwise-web/./node_modules/@ant-design/pro-utils/es/isImg/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/defaultSettings.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/SiderMenu/style/menu.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/SiderMenu/BaseMenu.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/SiderMenu/style/stylish.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/SiderMenu/SiderMenu.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/GlobalHeader/rightContentStyle.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/GlobalHeader/ActionsContent.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/TopNavHeader/style.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/TopNavHeader/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/GlobalHeader/style.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/GlobalHeader/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/Header/style/header.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/Header/style/stylish.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/Header/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/SiderMenu/style/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/components/SiderMenu/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/getPageTitle.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/locales/en-US/settingDrawer.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/locales/en-US.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/locales/it-IT/settingDrawer.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/locales/it-IT.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/locales/ko-KR/settingDrawer.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/locales/ko-KR.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/locales/zh-CN/settingDrawer.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/locales/zh-CN.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/locales/zh-TW/settingDrawer.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/locales/zh-TW.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/locales/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/style/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/utils/pathTools.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/utils/getBreadcrumbProps.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/utils/getMenuData.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/utils/useCurrentMenuLayoutProps.js", "webpack://labwise-web/./node_modules/@ant-design/pro-layout/es/ProLayout.js", "webpack://labwise-web/./src/.umi-production/plugin-layout/Logo.tsx", "webpack://labwise-web/./src/.umi-production/plugin-layout/Exception.tsx", "webpack://labwise-web/./src/.umi-production/plugin-layout/rightRender.tsx", "webpack://labwise-web/./src/.umi-production/plugin-layout/Layout.tsx"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport CopyrightOutlinedSvg from \"@ant-design/icons-svg/es/asn/CopyrightOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar CopyrightOutlined = function CopyrightOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: CopyrightOutlinedSvg\n  }));\n};\n\n/**![copyright](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnptNS42LTUzMi43YzUzIDAgODkgMzMuOCA5MyA4My40LjMgNC4yIDMuOCA3LjQgOCA3LjRoNTYuN2MyLjYgMCA0LjctMi4xIDQuNy00LjcgMC04Ni43LTY4LjQtMTQ3LjQtMTYyLjctMTQ3LjRDNDA3LjQgMjkwIDM0NCAzNjQuMiAzNDQgNDg2Ljh2NTIuM0MzNDQgNjYwLjggNDA3LjQgNzM0IDUxNy4zIDczNGM5NCAwIDE2Mi43LTU4LjggMTYyLjctMTQxLjQgMC0yLjYtMi4xLTQuNy00LjctNC43aC01Ni44Yy00LjIgMC03LjYgMy4yLTggNy4zLTQuMiA0Ni4xLTQwLjEgNzcuOC05MyA3Ny44LTY1LjMgMC0xMDIuMS00Ny45LTEwMi4xLTEzMy42di01Mi42Yy4xLTg3IDM3LTEzNS41IDEwMi4yLTEzNS41eiIgLz48L3N2Zz4=) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(CopyrightOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CopyrightOutlined';\n}\nexport default RefIcon;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nvar genFooterToolBarStyle = function genFooterToolBarStyle(token) {\n  return _defineProperty({}, token.componentCls, {\n    marginBlock: 0,\n    marginBlockStart: 48,\n    marginBlockEnd: 24,\n    marginInline: 0,\n    paddingBlock: 0,\n    paddingInline: 16,\n    textAlign: 'center',\n    '&-list': {\n      marginBlockEnd: 8,\n      color: token.colorTextSecondary,\n      '&-link': {\n        color: token.colorTextSecondary,\n        textDecoration: token.linkDecoration\n      },\n      '*:not(:last-child)': {\n        marginInlineEnd: 8\n      },\n      '&:hover': {\n        color: token.colorPrimary\n      }\n    },\n    '&-copyright': {\n      fontSize: '14px',\n      color: token.colorText\n    }\n  });\n};\nexport function useStyle(prefixCls) {\n  return useAntdStyle('ProLayoutFooter', function (token) {\n    var proCardToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genFooterToolBarStyle(proCardToken)];\n  });\n}", "import { Config<PERSON>rovider } from 'antd';\nimport classNames from 'classnames';\nimport React, { useContext } from 'react';\nimport { useStyle } from \"./style\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nvar GlobalFooter = function GlobalFooter(_ref) {\n  var className = _ref.className,\n    prefixCls = _ref.prefixCls,\n    links = _ref.links,\n    copyright = _ref.copyright,\n    style = _ref.style;\n  var context = useContext(ConfigProvider.ConfigContext);\n  var baseClassName = context.getPrefixCls(prefixCls || 'pro-global-footer');\n  var _useStyle = useStyle(baseClassName),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  if ((links == null || links === false || Array.isArray(links) && links.length === 0) && (copyright == null || copyright === false)) {\n    return null;\n  }\n  return wrapSSR( /*#__PURE__*/_jsxs(\"div\", {\n    className: classNames(baseClassName, hashId, className),\n    style: style,\n    children: [links && /*#__PURE__*/_jsx(\"div\", {\n      className: \"\".concat(baseClassName, \"-list \").concat(hashId).trim(),\n      children: links.map(function (link) {\n        return /*#__PURE__*/_jsx(\"a\", {\n          className: \"\".concat(baseClassName, \"-list-link \").concat(hashId).trim(),\n          title: link.key,\n          target: link.blankTarget ? '_blank' : '_self',\n          href: link.href,\n          rel: \"noreferrer\",\n          children: link.title\n        }, link.key);\n      })\n    }), copyright && /*#__PURE__*/_jsx(\"div\", {\n      className: \"\".concat(baseClassName, \"-copyright \").concat(hashId).trim(),\n      children: copyright\n    })]\n  }));\n};\nexport { GlobalFooter };", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { CopyrightOutlined } from '@ant-design/icons';\nimport { Layout } from 'antd';\nimport React, { Fragment } from 'react';\nimport { GlobalFooter } from \"./GlobalFooter\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Footer = Layout.Footer;\nvar DefaultFooter = function DefaultFooter(_ref) {\n  var links = _ref.links,\n    copyright = _ref.copyright,\n    style = _ref.style,\n    className = _ref.className,\n    prefixCls = _ref.prefixCls;\n  return /*#__PURE__*/_jsx(Footer, {\n    className: className,\n    style: _objectSpread({\n      padding: 0\n    }, style),\n    children: /*#__PURE__*/_jsx(GlobalFooter, {\n      links: links,\n      prefixCls: prefixCls,\n      copyright: copyright === false ? null : /*#__PURE__*/_jsxs(Fragment, {\n        children: [/*#__PURE__*/_jsx(CopyrightOutlined, {}), \" \", copyright]\n      })\n    })\n  });\n};\nexport { DefaultFooter };", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"isLoading\", \"pastDelay\", \"timedOut\", \"error\", \"retry\"];\nimport { Spin } from 'antd';\nimport React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar PageLoading = function PageLoading(_ref) {\n  var isLoading = _ref.isLoading,\n    pastDelay = _ref.pastDelay,\n    timedOut = _ref.timedOut,\n    error = _ref.error,\n    retry = _ref.retry,\n    reset = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/_jsx(\"div\", {\n    style: {\n      paddingBlockStart: 100,\n      textAlign: 'center'\n    },\n    children: /*#__PURE__*/_jsx(Spin, _objectSpread({\n      size: \"large\"\n    }, reset))\n  });\n};\nexport { PageLoading };", "import { createContext } from 'react';\nexport var RouteContext = /*#__PURE__*/createContext({});", "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar _excluded = [\"icon\", \"className\", \"onClick\", \"style\", \"primaryColor\", \"secondaryColor\"];\nimport * as React from 'react';\nimport { generate, getSecondaryColor, isIconDefinition, warning, useInsertStyles } from \"../utils\";\nvar twoToneColorPalette = {\n  primaryColor: '#333',\n  secondaryColor: '#E6E6E6',\n  calculated: false\n};\nfunction setTwoToneColors(_ref) {\n  var primaryColor = _ref.primaryColor,\n    secondaryColor = _ref.secondaryColor;\n  twoToneColorPalette.primaryColor = primaryColor;\n  twoToneColorPalette.secondaryColor = secondaryColor || getSecondaryColor(primaryColor);\n  twoToneColorPalette.calculated = !!secondaryColor;\n}\nfunction getTwoToneColors() {\n  return _objectSpread({}, twoToneColorPalette);\n}\nvar IconBase = function IconBase(props) {\n  var icon = props.icon,\n    className = props.className,\n    onClick = props.onClick,\n    style = props.style,\n    primaryColor = props.primaryColor,\n    secondaryColor = props.secondaryColor,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var svgRef = React.useRef();\n  var colors = twoToneColorPalette;\n  if (primaryColor) {\n    colors = {\n      primaryColor: primaryColor,\n      secondaryColor: secondaryColor || getSecondaryColor(primaryColor)\n    };\n  }\n  useInsertStyles(svgRef);\n  warning(isIconDefinition(icon), \"icon should be icon definiton, but got \".concat(icon));\n  if (!isIconDefinition(icon)) {\n    return null;\n  }\n  var target = icon;\n  if (target && typeof target.icon === 'function') {\n    target = _objectSpread(_objectSpread({}, target), {}, {\n      icon: target.icon(colors.primaryColor, colors.secondaryColor)\n    });\n  }\n  return generate(target.icon, \"svg-\".concat(target.name), _objectSpread(_objectSpread({\n    className: className,\n    onClick: onClick,\n    style: style,\n    'data-icon': target.name,\n    width: '1em',\n    height: '1em',\n    fill: 'currentColor',\n    'aria-hidden': 'true'\n  }, restProps), {}, {\n    ref: svgRef\n  }));\n};\nIconBase.displayName = 'IconReact';\nIconBase.getTwoToneColors = getTwoToneColors;\nIconBase.setTwoToneColors = setTwoToneColors;\nexport default IconBase;", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport ReactIcon from \"./IconBase\";\nimport { normalizeTwoToneColors } from \"../utils\";\nexport function setTwoToneColor(twoToneColor) {\n  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),\n    _normalizeTwoToneColo2 = _slicedToArray(_normalizeTwoToneColo, 2),\n    primaryColor = _normalizeTwoToneColo2[0],\n    secondaryColor = _normalizeTwoToneColo2[1];\n  return ReactIcon.setTwoToneColors({\n    primaryColor: primaryColor,\n    secondaryColor: secondaryColor\n  });\n}\nexport function getTwoToneColor() {\n  var colors = ReactIcon.getTwoToneColors();\n  if (!colors.calculated) {\n    return colors.primaryColor;\n  }\n  return [colors.primaryColor, colors.secondaryColor];\n}", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"icon\", \"spin\", \"rotate\", \"tabIndex\", \"onClick\", \"twoToneColor\"];\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { blue } from '@ant-design/colors';\nimport Context from \"./Context\";\nimport ReactIcon from \"./IconBase\";\nimport { getTwoToneColor, setTwoToneColor } from \"./twoTonePrimaryColor\";\nimport { normalizeTwoToneColors } from \"../utils\";\n// Initial setting\n// should move it to antd main repo?\nsetTwoToneColor(blue.primary);\n\n// https://github.com/DefinitelyTyped/DefinitelyTyped/issues/34757#issuecomment-488848720\n\nvar Icon = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var className = props.className,\n    icon = props.icon,\n    spin = props.spin,\n    rotate = props.rotate,\n    tabIndex = props.tabIndex,\n    onClick = props.onClick,\n    twoToneColor = props.twoToneColor,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(Context),\n    _React$useContext$pre = _React$useContext.prefixCls,\n    prefixCls = _React$useContext$pre === void 0 ? 'anticon' : _React$useContext$pre,\n    rootClassName = _React$useContext.rootClassName;\n  var classString = classNames(rootClassName, prefixCls, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-\").concat(icon.name), !!icon.name), \"\".concat(prefixCls, \"-spin\"), !!spin || icon.name === 'loading'), className);\n  var iconTabIndex = tabIndex;\n  if (iconTabIndex === undefined && onClick) {\n    iconTabIndex = -1;\n  }\n  var svgStyle = rotate ? {\n    msTransform: \"rotate(\".concat(rotate, \"deg)\"),\n    transform: \"rotate(\".concat(rotate, \"deg)\")\n  } : undefined;\n  var _normalizeTwoToneColo = normalizeTwoToneColors(twoToneColor),\n    _normalizeTwoToneColo2 = _slicedToArray(_normalizeTwoToneColo, 2),\n    primaryColor = _normalizeTwoToneColo2[0],\n    secondaryColor = _normalizeTwoToneColo2[1];\n  return /*#__PURE__*/React.createElement(\"span\", _extends({\n    role: \"img\",\n    \"aria-label\": icon.name\n  }, restProps, {\n    ref: ref,\n    tabIndex: iconTabIndex,\n    onClick: onClick,\n    className: classString\n  }), /*#__PURE__*/React.createElement(ReactIcon, {\n    icon: icon,\n    primaryColor: primaryColor,\n    secondaryColor: secondaryColor,\n    style: svgStyle\n  }));\n});\nIcon.displayName = 'AntdIcon';\nIcon.getTwoToneColor = getTwoToneColor;\nIcon.setTwoToneColor = setTwoToneColor;\nexport default Icon;", "import { createContext } from 'react';\nvar IconContext = /*#__PURE__*/createContext({});\nexport default IconContext;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { generate as generateColor } from '@ant-design/colors';\nimport { updateCSS } from \"rc-util/es/Dom/dynamicCSS\";\nimport { getShadowRoot } from \"rc-util/es/Dom/shadow\";\nimport warn from \"rc-util/es/warning\";\nimport React, { useContext, useEffect } from 'react';\nimport IconContext from \"./components/Context\";\nfunction camelCase(input) {\n  return input.replace(/-(.)/g, function (match, g) {\n    return g.toUpperCase();\n  });\n}\nexport function warning(valid, message) {\n  warn(valid, \"[@ant-design/icons] \".concat(message));\n}\nexport function isIconDefinition(target) {\n  return _typeof(target) === 'object' && typeof target.name === 'string' && typeof target.theme === 'string' && (_typeof(target.icon) === 'object' || typeof target.icon === 'function');\n}\nexport function normalizeAttrs() {\n  var attrs = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  return Object.keys(attrs).reduce(function (acc, key) {\n    var val = attrs[key];\n    switch (key) {\n      case 'class':\n        acc.className = val;\n        delete acc.class;\n        break;\n      default:\n        delete acc[key];\n        acc[camelCase(key)] = val;\n    }\n    return acc;\n  }, {});\n}\nexport function generate(node, key, rootProps) {\n  if (!rootProps) {\n    return /*#__PURE__*/React.createElement(node.tag, _objectSpread({\n      key: key\n    }, normalizeAttrs(node.attrs)), (node.children || []).map(function (child, index) {\n      return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n    }));\n  }\n  return /*#__PURE__*/React.createElement(node.tag, _objectSpread(_objectSpread({\n    key: key\n  }, normalizeAttrs(node.attrs)), rootProps), (node.children || []).map(function (child, index) {\n    return generate(child, \"\".concat(key, \"-\").concat(node.tag, \"-\").concat(index));\n  }));\n}\nexport function getSecondaryColor(primaryColor) {\n  // choose the second color\n  return generateColor(primaryColor)[0];\n}\nexport function normalizeTwoToneColors(twoToneColor) {\n  if (!twoToneColor) {\n    return [];\n  }\n  return Array.isArray(twoToneColor) ? twoToneColor : [twoToneColor];\n}\n\n// These props make sure that the SVG behaviours like general text.\n// Reference: https://blog.prototypr.io/align-svg-icons-to-text-and-say-goodbye-to-font-icons-d44b3d7b26b4\nexport var svgBaseProps = {\n  width: '1em',\n  height: '1em',\n  fill: 'currentColor',\n  'aria-hidden': 'true',\n  focusable: 'false'\n};\nexport var iconStyles = \"\\n.anticon {\\n  display: inline-flex;\\n  align-items: center;\\n  color: inherit;\\n  font-style: normal;\\n  line-height: 0;\\n  text-align: center;\\n  text-transform: none;\\n  vertical-align: -0.125em;\\n  text-rendering: optimizeLegibility;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n.anticon > * {\\n  line-height: 1;\\n}\\n\\n.anticon svg {\\n  display: inline-block;\\n}\\n\\n.anticon::before {\\n  display: none;\\n}\\n\\n.anticon .anticon-icon {\\n  display: block;\\n}\\n\\n.anticon[tabindex] {\\n  cursor: pointer;\\n}\\n\\n.anticon-spin::before,\\n.anticon-spin {\\n  display: inline-block;\\n  -webkit-animation: loadingCircle 1s infinite linear;\\n  animation: loadingCircle 1s infinite linear;\\n}\\n\\n@-webkit-keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n@keyframes loadingCircle {\\n  100% {\\n    -webkit-transform: rotate(360deg);\\n    transform: rotate(360deg);\\n  }\\n}\\n\";\nexport var useInsertStyles = function useInsertStyles(eleRef) {\n  var _useContext = useContext(IconContext),\n    csp = _useContext.csp,\n    prefixCls = _useContext.prefixCls;\n  var mergedStyleStr = iconStyles;\n  if (prefixCls) {\n    mergedStyleStr = mergedStyleStr.replace(/anticon/g, prefixCls);\n  }\n  useEffect(function () {\n    var ele = eleRef.current;\n    var shadowRoot = getShadowRoot(ele);\n    updateCSS(mergedStyleStr, '@ant-design-icons', {\n      prepend: true,\n      csp: csp,\n      attachTo: shadowRoot\n    });\n  }, []);\n};", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nObject.defineProperty(exports, '__esModule', {\n  value: true\n});\nexports.pathToRegexp = exports.tokensToRegexp = exports.regexpToFunction = exports.match = exports.tokensToFunction = exports.compile = exports.parse = void 0;\n/**\n * Tokenize input string.\n */\n\nfunction lexer(str) {\n  var tokens = [];\n  var i = 0;\n\n  while (i < str.length) {\n    var char = str[i];\n\n    if (char === '*' || char === '+' || char === '?') {\n      tokens.push({\n        type: 'MODIFIER',\n        index: i,\n        value: str[i++]\n      });\n      continue;\n    }\n\n    if (char === '\\\\') {\n      tokens.push({\n        type: 'ESCAPED_CHAR',\n        index: i++,\n        value: str[i++]\n      });\n      continue;\n    }\n\n    if (char === '{') {\n      tokens.push({\n        type: 'OPEN',\n        index: i,\n        value: str[i++]\n      });\n      continue;\n    }\n\n    if (char === '}') {\n      tokens.push({\n        type: 'CLOSE',\n        index: i,\n        value: str[i++]\n      });\n      continue;\n    }\n\n    if (char === ':') {\n      var name = '';\n      var j = i + 1;\n\n      while (j < str.length) {\n        var code = str.charCodeAt(j);\n\n        if ( // `0-9`\n        code >= 48 && code <= 57 || // `A-Z`\n        code >= 65 && code <= 90 || // `a-z`\n        code >= 97 && code <= 122 || // `_`\n        code === 95) {\n          name += str[j++];\n          continue;\n        }\n\n        break;\n      }\n\n      if (!name) throw new TypeError('Missing parameter name at ' + i);\n      tokens.push({\n        type: 'NAME',\n        index: i,\n        value: name\n      });\n      i = j;\n      continue;\n    }\n\n    if (char === '(') {\n      var count = 1;\n      var pattern = '';\n      var j = i + 1;\n\n      if (str[j] === '?') {\n        throw new TypeError('Pattern cannot start with \"?\" at ' + j);\n      }\n\n      while (j < str.length) {\n        if (str[j] === '\\\\') {\n          pattern += str[j++] + str[j++];\n          continue;\n        }\n\n        if (str[j] === ')') {\n          count--;\n\n          if (count === 0) {\n            j++;\n            break;\n          }\n        } else if (str[j] === '(') {\n          count++;\n\n          if (str[j + 1] !== '?') {\n            throw new TypeError('Capturing groups are not allowed at ' + j);\n          }\n        }\n\n        pattern += str[j++];\n      }\n\n      if (count) throw new TypeError('Unbalanced pattern at ' + i);\n      if (!pattern) throw new TypeError('Missing pattern at ' + i);\n      tokens.push({\n        type: 'PATTERN',\n        index: i,\n        value: pattern\n      });\n      i = j;\n      continue;\n    }\n\n    tokens.push({\n      type: 'CHAR',\n      index: i,\n      value: str[i++]\n    });\n  }\n\n  tokens.push({\n    type: 'END',\n    index: i,\n    value: ''\n  });\n  return tokens;\n}\n/**\n * Parse a string for the raw tokens.\n */\n\n\nfunction parse(str, options) {\n  if (options === void 0) {\n    // eslint-disable-next-line no-param-reassign\n    options = {};\n  }\n\n  var tokens = lexer(str);\n  var _a = options.prefixes,\n      prefixes = _a === void 0 ? './' : _a;\n  var defaultPattern = '[^' + escapeString(options.delimiter || '/#?') + ']+?';\n  var result = [];\n  var key = 0;\n  var i = 0;\n  var path = '';\n\n  var tryConsume = function tryConsume(type) {\n    if (i < tokens.length && tokens[i].type === type) return tokens[i++].value;\n  };\n\n  var mustConsume = function mustConsume(type) {\n    var value = tryConsume(type);\n    if (value !== undefined) return value;\n    var _a = tokens[i],\n        nextType = _a.type,\n        index = _a.index;\n    throw new TypeError('Unexpected ' + nextType + ' at ' + index + ', expected ' + type);\n  };\n\n  var consumeText = function consumeText() {\n    var result = '';\n    var value; // tslint:disable-next-line\n\n    while (value = tryConsume('CHAR') || tryConsume('ESCAPED_CHAR')) {\n      result += value;\n    }\n\n    return result;\n  };\n\n  while (i < tokens.length) {\n    var char = tryConsume('CHAR');\n    var name = tryConsume('NAME');\n    var pattern = tryConsume('PATTERN');\n\n    if (name || pattern) {\n      var prefix = char || '';\n\n      if (prefixes.indexOf(prefix) === -1) {\n        path += prefix;\n        prefix = '';\n      }\n\n      if (path) {\n        result.push(path);\n        path = '';\n      }\n\n      result.push({\n        name: name || key++,\n        prefix: prefix,\n        suffix: '',\n        pattern: pattern || defaultPattern,\n        modifier: tryConsume('MODIFIER') || ''\n      });\n      continue;\n    }\n\n    var value = char || tryConsume('ESCAPED_CHAR');\n\n    if (value) {\n      path += value;\n      continue;\n    }\n\n    if (path) {\n      result.push(path);\n      path = '';\n    }\n\n    var open = tryConsume('OPEN');\n\n    if (open) {\n      var prefix = consumeText();\n      var name_1 = tryConsume('NAME') || '';\n      var pattern_1 = tryConsume('PATTERN') || '';\n      var suffix = consumeText();\n      mustConsume('CLOSE');\n      result.push({\n        name: name_1 || (pattern_1 ? key++ : ''),\n        pattern: name_1 && !pattern_1 ? defaultPattern : pattern_1,\n        prefix: prefix,\n        suffix: suffix,\n        modifier: tryConsume('MODIFIER') || ''\n      });\n      continue;\n    }\n\n    mustConsume('END');\n  }\n\n  return result;\n}\n\nexports.parse = parse;\n/**\n * Compile a string to a template function for the path.\n */\n\nfunction compile(str, options) {\n  return tokensToFunction(parse(str, options), options);\n}\n\nexports.compile = compile;\n/**\n * Expose a method for transforming tokens into the path function.\n */\n\nfunction tokensToFunction(tokens, options) {\n  if (options === void 0) {\n    // eslint-disable-next-line no-param-reassign\n    options = {};\n  }\n\n  var reFlags = flags(options);\n  var _a = options.encode,\n      encode = _a === void 0 ? function (x) {\n    return x;\n  } : _a,\n      _b = options.validate,\n      validate = _b === void 0 ? true : _b; // Compile all the tokens into regexps.\n\n  var matches = tokens.map(function (token) {\n    if (_typeof(token) === 'object') {\n      return new RegExp('^(?:' + token.pattern + ')$', reFlags);\n    }\n  });\n  return function (data) {\n    var path = '';\n\n    for (var i = 0; i < tokens.length; i++) {\n      var token = tokens[i];\n\n      if (typeof token === 'string') {\n        path += token;\n        continue;\n      }\n\n      var value = data ? data[token.name] : undefined;\n      var optional = token.modifier === '?' || token.modifier === '*';\n      var repeat = token.modifier === '*' || token.modifier === '+';\n\n      if (Array.isArray(value)) {\n        if (!repeat) {\n          throw new TypeError('Expected \"' + token.name + '\" to not repeat, but got an array');\n        }\n\n        if (value.length === 0) {\n          if (optional) continue;\n          throw new TypeError('Expected \"' + token.name + '\" to not be empty');\n        }\n\n        for (var j = 0; j < value.length; j++) {\n          var segment = encode(value[j], token);\n\n          if (validate && !matches[i].test(segment)) {\n            throw new TypeError('Expected all \"' + token.name + '\" to match \"' + token.pattern + '\", but got \"' + segment + '\"');\n          }\n\n          path += token.prefix + segment + token.suffix;\n        }\n\n        continue;\n      }\n\n      if (typeof value === 'string' || typeof value === 'number') {\n        var segment = encode(String(value), token);\n\n        if (validate && !matches[i].test(segment)) {\n          throw new TypeError('Expected \"' + token.name + '\" to match \"' + token.pattern + '\", but got \"' + segment + '\"');\n        }\n\n        path += token.prefix + segment + token.suffix;\n        continue;\n      }\n\n      if (optional) continue;\n      var typeOfMessage = repeat ? 'an array' : 'a string';\n      throw new TypeError('Expected \"' + token.name + '\" to be ' + typeOfMessage);\n    }\n\n    return path;\n  };\n}\n\nexports.tokensToFunction = tokensToFunction;\n/**\n * Create path match function from `path-to-regexp` spec.\n */\n\nfunction match(str, options) {\n  var keys = [];\n  var re = pathToRegexp(str, keys, options);\n  return regexpToFunction(re, keys, options);\n}\n\nexports.match = match;\n/**\n * Create a path match function from `path-to-regexp` output.\n */\n\nfunction regexpToFunction(re, keys, options) {\n  if (options === void 0) {\n    // eslint-disable-next-line no-param-reassign\n    options = {};\n  }\n\n  var _a = options.decode,\n      decode = _a === void 0 ? function (x) {\n    return x;\n  } : _a;\n  return function (pathname) {\n    var m = re.exec(pathname);\n    if (!m) return false;\n    var path = m[0],\n        index = m.index;\n    var params = Object.create(null);\n\n    var _loop_1 = function _loop_1(i) {\n      // tslint:disable-next-line\n      if (m[i] === undefined) return 'continue';\n      var key = keys[i - 1];\n\n      if (key.modifier === '*' || key.modifier === '+') {\n        params[key.name] = m[i].split(key.prefix + key.suffix).map(function (value) {\n          return decode(value, key);\n        });\n      } else {\n        params[key.name] = decode(m[i], key);\n      }\n    };\n\n    for (var i = 1; i < m.length; i++) {\n      _loop_1(i);\n    }\n\n    return {\n      path: path,\n      index: index,\n      params: params\n    };\n  };\n}\n\nexports.regexpToFunction = regexpToFunction;\n/**\n * Escape a regular expression string.\n */\n\nfunction escapeString(str) {\n  return str.replace(/([.+*?=^!:${}()[\\]|/\\\\])/g, '\\\\$1');\n}\n/**\n * Get the flags for a regexp from the options.\n */\n\n\nfunction flags(options) {\n  return options && options.sensitive ? '' : 'i';\n}\n/**\n * Pull out keys from a regexp.\n */\n\n\nfunction regexpToRegexp(path, keys) {\n  if (!keys) return path; // Use a negative lookahead to match only capturing groups.\n\n  var groups = path.source.match(/\\((?!\\?)/g);\n\n  if (groups) {\n    for (var i = 0; i < groups.length; i++) {\n      keys.push({\n        name: i,\n        prefix: '',\n        suffix: '',\n        modifier: '',\n        pattern: ''\n      });\n    }\n  }\n\n  return path;\n}\n/**\n * Transform an array into a regexp.\n */\n\n\nfunction arrayToRegexp(paths, keys, options) {\n  var parts = paths.map(function (path) {\n    return pathToRegexp(path, keys, options).source;\n  });\n  return new RegExp('(?:' + parts.join('|') + ')', flags(options));\n}\n/**\n * Create a path regexp from string input.\n */\n\n\nfunction stringToRegexp(path, keys, options) {\n  return tokensToRegexp(parse(path, options), keys, options);\n}\n/**\n * Expose a function for taking tokens and returning a RegExp.\n */\n\n\nfunction tokensToRegexp(tokens, keys, options) {\n  if (options === void 0) {\n    // eslint-disable-next-line no-param-reassign\n    options = {};\n  }\n\n  var _a = options.strict,\n      strict = _a === void 0 ? false : _a,\n      _b = options.start,\n      start = _b === void 0 ? true : _b,\n      _c = options.end,\n      end = _c === void 0 ? true : _c,\n      _d = options.encode,\n      encode = _d === void 0 ? function (x) {\n    return x;\n  } : _d;\n  var endsWith = '[' + escapeString(options.endsWith || '') + ']|$';\n  var delimiter = '[' + escapeString(options.delimiter || '/#?') + ']';\n  var route = start ? '^' : ''; // Iterate over the tokens and create our regexp string.\n\n  for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n    var token = tokens_1[_i];\n\n    if (typeof token === 'string') {\n      route += escapeString(encode(token));\n    } else {\n      var prefix = escapeString(encode(token.prefix));\n      var suffix = escapeString(encode(token.suffix));\n\n      if (token.pattern) {\n        if (keys) keys.push(token);\n\n        if (prefix || suffix) {\n          if (token.modifier === '+' || token.modifier === '*') {\n            var mod = token.modifier === '*' ? '?' : '';\n            route += '(?:' + prefix + '((?:' + token.pattern + ')(?:' + suffix + prefix + '(?:' + token.pattern + '))*)' + suffix + ')' + mod;\n          } else {\n            route += '(?:' + prefix + '(' + token.pattern + ')' + suffix + ')' + token.modifier;\n          }\n        } else {\n          route += '(' + token.pattern + ')' + token.modifier;\n        }\n      } else {\n        route += '(?:' + prefix + suffix + ')' + token.modifier;\n      }\n    }\n  }\n\n  if (end) {\n    if (!strict) route += delimiter + '?';\n    route += !options.endsWith ? '$' : '(?=' + endsWith + ')';\n  } else {\n    var endToken = tokens[tokens.length - 1];\n    var isEndDelimited = typeof endToken === 'string' ? delimiter.indexOf(endToken[endToken.length - 1]) > -1 : // tslint:disable-next-line\n    endToken === undefined;\n\n    if (!strict) {\n      route += '(?:' + delimiter + '(?=' + endsWith + '))?';\n    }\n\n    if (!isEndDelimited) {\n      route += '(?=' + delimiter + '|' + endsWith + ')';\n    }\n  }\n\n  return new RegExp(route, flags(options));\n}\n\nexports.tokensToRegexp = tokensToRegexp;\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n */\n\nfunction pathToRegexp(path, keys, options) {\n  if (path instanceof RegExp) return regexpToRegexp(path, keys);\n  if (Array.isArray(path)) return arrayToRegexp(path, keys, options);\n  return stringToRegexp(path, keys, options);\n}\n\nexports.pathToRegexp = pathToRegexp;", "\"use strict\";\nvar __classPrivateFieldGet = (this && this.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar __classPrivateFieldSet = (this && this.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar _Iter_peek;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.TokenData = void 0;\nexports.parse = parse;\nexports.compile = compile;\nexports.match = match;\nconst DEFAULT_DELIMITER = \"/\";\nconst NOOP_VALUE = (value) => value;\nconst ID_START = /^[$_\\p{ID_Start}]$/u;\nconst ID_CONTINUE = /^[$\\u200c\\u200d\\p{ID_Continue}]$/u;\nconst DEBUG_URL = \"https://git.new/pathToRegexpError\";\nconst SIMPLE_TOKENS = {\n    // Groups.\n    \"{\": \"{\",\n    \"}\": \"}\",\n    // Reserved.\n    \"(\": \"(\",\n    \")\": \")\",\n    \"[\": \"[\",\n    \"]\": \"]\",\n    \"+\": \"+\",\n    \"?\": \"?\",\n    \"!\": \"!\",\n};\n/**\n * Escape a regular expression string.\n */\nfunction escape(str) {\n    return str.replace(/[.+*?^${}()[\\]|/\\\\]/g, \"\\\\$&\");\n}\n/**\n * Get the flags for a regexp from the options.\n */\nfunction toFlags(options) {\n    return options.sensitive ? \"s\" : \"is\";\n}\n/**\n * Tokenize input string.\n */\nfunction* lexer(str) {\n    const chars = [...str];\n    let i = 0;\n    function name() {\n        let value = \"\";\n        if (ID_START.test(chars[++i])) {\n            value += chars[i];\n            while (ID_CONTINUE.test(chars[++i])) {\n                value += chars[i];\n            }\n        }\n        else if (chars[i] === '\"') {\n            let pos = i;\n            while (i < chars.length) {\n                if (chars[++i] === '\"') {\n                    i++;\n                    pos = 0;\n                    break;\n                }\n                if (chars[i] === \"\\\\\") {\n                    value += chars[++i];\n                }\n                else {\n                    value += chars[i];\n                }\n            }\n            if (pos) {\n                throw new TypeError(`Unterminated quote at ${pos}: ${DEBUG_URL}`);\n            }\n        }\n        if (!value) {\n            throw new TypeError(`Missing parameter name at ${i}: ${DEBUG_URL}`);\n        }\n        return value;\n    }\n    while (i < chars.length) {\n        const value = chars[i];\n        const type = SIMPLE_TOKENS[value];\n        if (type) {\n            yield { type, index: i++, value };\n        }\n        else if (value === \"\\\\\") {\n            yield { type: \"ESCAPED\", index: i++, value: chars[i++] };\n        }\n        else if (value === \":\") {\n            const value = name();\n            yield { type: \"PARAM\", index: i, value };\n        }\n        else if (value === \"*\") {\n            const value = name();\n            yield { type: \"WILDCARD\", index: i, value };\n        }\n        else {\n            yield { type: \"CHAR\", index: i, value: chars[i++] };\n        }\n    }\n    return { type: \"END\", index: i, value: \"\" };\n}\nclass Iter {\n    constructor(tokens) {\n        this.tokens = tokens;\n        _Iter_peek.set(this, void 0);\n    }\n    peek() {\n        if (!__classPrivateFieldGet(this, _Iter_peek, \"f\")) {\n            const next = this.tokens.next();\n            __classPrivateFieldSet(this, _Iter_peek, next.value, \"f\");\n        }\n        return __classPrivateFieldGet(this, _Iter_peek, \"f\");\n    }\n    tryConsume(type) {\n        const token = this.peek();\n        if (token.type !== type)\n            return;\n        __classPrivateFieldSet(this, _Iter_peek, undefined, \"f\"); // Reset after consumed.\n        return token.value;\n    }\n    consume(type) {\n        const value = this.tryConsume(type);\n        if (value !== undefined)\n            return value;\n        const { type: nextType, index } = this.peek();\n        throw new TypeError(`Unexpected ${nextType} at ${index}, expected ${type}: ${DEBUG_URL}`);\n    }\n    text() {\n        let result = \"\";\n        let value;\n        while ((value = this.tryConsume(\"CHAR\") || this.tryConsume(\"ESCAPED\"))) {\n            result += value;\n        }\n        return result;\n    }\n}\n_Iter_peek = new WeakMap();\n/**\n * Tokenized path instance.\n */\nclass TokenData {\n    constructor(tokens) {\n        this.tokens = tokens;\n    }\n}\nexports.TokenData = TokenData;\n/**\n * Parse a string for the raw tokens.\n */\nfunction parse(str, options = {}) {\n    const { encodePath = NOOP_VALUE } = options;\n    const it = new Iter(lexer(str));\n    function consume(endType) {\n        const tokens = [];\n        while (true) {\n            const path = it.text();\n            if (path)\n                tokens.push({ type: \"text\", value: encodePath(path) });\n            const param = it.tryConsume(\"PARAM\");\n            if (param) {\n                tokens.push({\n                    type: \"param\",\n                    name: param,\n                });\n                continue;\n            }\n            const wildcard = it.tryConsume(\"WILDCARD\");\n            if (wildcard) {\n                tokens.push({\n                    type: \"wildcard\",\n                    name: wildcard,\n                });\n                continue;\n            }\n            const open = it.tryConsume(\"{\");\n            if (open) {\n                tokens.push({\n                    type: \"group\",\n                    tokens: consume(\"}\"),\n                });\n                continue;\n            }\n            it.consume(endType);\n            return tokens;\n        }\n    }\n    const tokens = consume(\"END\");\n    return new TokenData(tokens);\n}\n/**\n * Transform tokens into a path building function.\n */\nfunction $compile(data, options) {\n    const { encode = encodeURIComponent, delimiter = DEFAULT_DELIMITER } = options;\n    const fn = tokensToFunction(data.tokens, delimiter, encode);\n    return function path(data = {}) {\n        const [path, ...missing] = fn(data);\n        if (missing.length) {\n            throw new TypeError(`Missing parameters: ${missing.join(\", \")}`);\n        }\n        return path;\n    };\n}\n/**\n * Compile a string to a template function for the path.\n */\nfunction compile(path, options = {}) {\n    return $compile(path instanceof TokenData ? path : parse(path, options), options);\n}\nfunction tokensToFunction(tokens, delimiter, encode) {\n    const encoders = tokens.map((token) => tokenToFunction(token, delimiter, encode));\n    return (data) => {\n        const result = [\"\"];\n        for (const encoder of encoders) {\n            const [value, ...extras] = encoder(data);\n            result[0] += value;\n            result.push(...extras);\n        }\n        return result;\n    };\n}\n/**\n * Convert a single token into a path building function.\n */\nfunction tokenToFunction(token, delimiter, encode) {\n    if (token.type === \"text\")\n        return () => [token.value];\n    if (token.type === \"group\") {\n        const fn = tokensToFunction(token.tokens, delimiter, encode);\n        return (data) => {\n            const [value, ...missing] = fn(data);\n            if (!missing.length)\n                return [value];\n            return [\"\"];\n        };\n    }\n    const encodeValue = encode || NOOP_VALUE;\n    if (token.type === \"wildcard\" && encode !== false) {\n        return (data) => {\n            const value = data[token.name];\n            if (value == null)\n                return [\"\", token.name];\n            if (!Array.isArray(value) || value.length === 0) {\n                throw new TypeError(`Expected \"${token.name}\" to be a non-empty array`);\n            }\n            return [\n                value\n                    .map((value, index) => {\n                    if (typeof value !== \"string\") {\n                        throw new TypeError(`Expected \"${token.name}/${index}\" to be a string`);\n                    }\n                    return encodeValue(value);\n                })\n                    .join(delimiter),\n            ];\n        };\n    }\n    return (data) => {\n        const value = data[token.name];\n        if (value == null)\n            return [\"\", token.name];\n        if (typeof value !== \"string\") {\n            throw new TypeError(`Expected \"${token.name}\" to be a string`);\n        }\n        return [encodeValue(value)];\n    };\n}\n/**\n * Create path match function from `path-to-regexp` spec.\n */\nfunction $match(data, options = {}) {\n    const { decode = decodeURIComponent, delimiter = DEFAULT_DELIMITER, end = true, trailing = true, } = options;\n    const flags = toFlags(options);\n    const sources = [];\n    const keys = [];\n    for (const { tokens } of data) {\n        for (const seq of flatten(tokens, 0, [])) {\n            const regexp = sequenceToRegExp(seq, delimiter, keys);\n            sources.push(regexp);\n        }\n    }\n    let pattern = `^(?:${sources.join(\"|\")})`;\n    if (trailing)\n        pattern += `(?:${escape(delimiter)}$)?`;\n    pattern += end ? \"$\" : `(?=${escape(delimiter)}|$)`;\n    const re = new RegExp(pattern, flags);\n    const decoders = keys.map((key) => {\n        if (decode === false)\n            return NOOP_VALUE;\n        if (key.type === \"param\")\n            return decode;\n        return (value) => value.split(delimiter).map(decode);\n    });\n    return Object.assign(function match(input) {\n        const m = re.exec(input);\n        if (!m)\n            return false;\n        const { 0: path } = m;\n        const params = Object.create(null);\n        for (let i = 1; i < m.length; i++) {\n            if (m[i] === undefined)\n                continue;\n            const key = keys[i - 1];\n            const decoder = decoders[i - 1];\n            params[key.name] = decoder(m[i]);\n        }\n        return { path, params };\n    }, { re });\n}\nfunction match(path, options = {}) {\n    const paths = Array.isArray(path) ? path : [path];\n    const items = paths.map((path) => path instanceof TokenData ? path : parse(path, options));\n    return $match(items, options);\n}\n/**\n * Generate a flat list of sequence tokens from the given tokens.\n */\nfunction* flatten(tokens, index, init) {\n    if (index === tokens.length) {\n        return yield init;\n    }\n    const token = tokens[index];\n    if (token.type === \"group\") {\n        const fork = init.slice();\n        for (const seq of flatten(token.tokens, 0, fork)) {\n            yield* flatten(tokens, index + 1, seq);\n        }\n    }\n    else {\n        init.push(token);\n    }\n    yield* flatten(tokens, index + 1, init);\n}\n/**\n * Transform a flat sequence of tokens into a regular expression.\n */\nfunction sequenceToRegExp(tokens, delimiter, keys) {\n    let result = \"\";\n    let backtrack = \"\";\n    let isSafeSegmentParam = true;\n    for (let i = 0; i < tokens.length; i++) {\n        const token = tokens[i];\n        if (token.type === \"text\") {\n            result += escape(token.value);\n            backtrack = token.value;\n            isSafeSegmentParam || (isSafeSegmentParam = token.value.includes(delimiter));\n            continue;\n        }\n        if (token.type === \"param\" || token.type === \"wildcard\") {\n            if (!isSafeSegmentParam && !backtrack) {\n                throw new TypeError(`Missing text after \"${token.name}\": ${DEBUG_URL}`);\n            }\n            if (token.type === \"param\") {\n                result += `(${negate(delimiter, isSafeSegmentParam ? \"\" : backtrack)}+)`;\n            }\n            else {\n                result += `(.+)`;\n            }\n            keys.push(token);\n            backtrack = \"\";\n            isSafeSegmentParam = false;\n            continue;\n        }\n    }\n    return result;\n}\nfunction negate(delimiter, backtrack) {\n    const values = [delimiter, backtrack].filter(Boolean);\n    const isSimple = values.every((value) => value.length === 1);\n    if (isSimple)\n        return `[^${escape(values.join(\"\"))}]`;\n    return `(?:(?!${values.map(escape).join(\"|\")}).)`;\n}\n//# sourceMappingURL=index.js.map", "import { version } from 'antd';\nimport { omitUndefined } from \"../omitUndefined\";\nimport { compareVersions } from \"./index\";\nexport var getVersion = function getVersion() {\n  var _process;\n  if (typeof process === 'undefined') return version;\n  return ((_process = process) === null || _process === void 0 || (_process = _process.env) === null || _process === void 0 ? void 0 : _process.ANTD_VERSION) || version;\n};\nvar openVisibleCompatible = function openVisibleCompatible(open, onOpenChange) {\n  var props = compareVersions(getVersion(), '4.23.0') > -1 ? {\n    open: open,\n    onOpenChange: onOpenChange\n  } : {\n    visible: open,\n    onVisibleChange: onOpenChange\n  };\n  return omitUndefined(props);\n};\nexport { openVisibleCompatible };", "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Result } from 'antd';\nimport React from 'react';\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n  _inherits(ErrorBoundary, _React$Component);\n  var _super = _createSuper(ErrorBoundary);\n  function ErrorBoundary() {\n    var _this;\n    _classCallCheck(this, ErrorBoundary);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      hasError: false,\n      errorInfo: ''\n    });\n    return _this;\n  }\n  _createClass(ErrorBoundary, [{\n    key: \"componentDidCatch\",\n    value: function componentDidCatch(error, errorInfo) {\n      // You can also log the error to an error reporting service\n      // eslint-disable-next-line no-console\n      console.log(error, errorInfo);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      if (this.state.hasError) {\n        // You can render any custom fallback UI\n        return /*#__PURE__*/_jsx(Result, {\n          status: \"error\",\n          title: \"Something went wrong.\",\n          extra: this.state.errorInfo\n        });\n      }\n      return this.props.children;\n    }\n  }], [{\n    key: \"getDerivedStateFromError\",\n    value: function getDerivedStateFromError(error) {\n      return {\n        hasError: true,\n        errorInfo: error.message\n      };\n    }\n  }]);\n  return ErrorBoundary;\n}(React.Component);\nexport { ErrorBoundary };", "import _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport { useCallback, useEffect, useRef } from 'react';\nimport { useRefFunction } from \"../useRefFunction\";\n/**\n * 一个去抖的 hook，传入一个 function，返回一个去抖后的 function\n * @param  {(...args:T) => Promise<any>} fn\n * @param  {number} wait?\n */\nexport function useDebounceFn(fn, wait) {\n  var callback = useRefFunction(fn);\n  var timer = useRef();\n  var cancel = useCallback(function () {\n    if (timer.current) {\n      clearTimeout(timer.current);\n      timer.current = null;\n    }\n  }, []);\n  var run = useCallback( /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n    var _len,\n      args,\n      _key,\n      _args2 = arguments;\n    return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n      while (1) switch (_context2.prev = _context2.next) {\n        case 0:\n          for (_len = _args2.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n            args[_key] = _args2[_key];\n          }\n          if (!(wait === 0 || wait === undefined)) {\n            _context2.next = 3;\n            break;\n          }\n          return _context2.abrupt(\"return\", callback.apply(void 0, args));\n        case 3:\n          cancel();\n          return _context2.abrupt(\"return\", new Promise(function (resolve) {\n            timer.current = setTimeout( /*#__PURE__*/_asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n              return _regeneratorRuntime().wrap(function _callee$(_context) {\n                while (1) switch (_context.prev = _context.next) {\n                  case 0:\n                    _context.t0 = resolve;\n                    _context.next = 3;\n                    return callback.apply(void 0, args);\n                  case 3:\n                    _context.t1 = _context.sent;\n                    (0, _context.t0)(_context.t1);\n                    return _context.abrupt(\"return\");\n                  case 6:\n                  case \"end\":\n                    return _context.stop();\n                }\n              }, _callee);\n            })), wait);\n          }));\n        case 5:\n        case \"end\":\n          return _context2.stop();\n      }\n    }, _callee2);\n  })), [callback, cancel, wait]);\n  useEffect(function () {\n    return cancel;\n  }, [cancel]);\n  return {\n    run: run,\n    cancel: cancel\n  };\n}", "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { useCallback, useRef } from 'react';\nvar useRefFunction = function useRefFunction(reFunction) {\n  var ref = useRef(null);\n  ref.current = reFunction;\n  return useCallback(function () {\n    var _ref$current;\n    for (var _len = arguments.length, rest = new Array(_len), _key = 0; _key < _len; _key++) {\n      rest[_key] = arguments[_key];\n    }\n    return (_ref$current = ref.current) === null || _ref$current === void 0 ? void 0 : _ref$current.call.apply(_ref$current, [ref].concat(_toConsumableArray(rest)));\n  }, []);\n};\nexport { useRefFunction };", "var isNode = typeof process !== 'undefined' && process.versions != null && process.versions.node != null;\n\n/**\n * 用于判断当前是否在浏览器环境中。\n * 首先会判断当前是否处于测试环境中（通过 process.env.NODE_ENV === 'TEST' 判断），\n * 如果是，则返回 true。否则，会进一步判断是否存在 window 对象、document 对象以及 matchMedia 方法\n * 同时通过 !isNode 判断当前不是在服务器（Node.js）环境下执行，\n * 如果都符合，则返回 true 表示当前处于浏览器环境中。\n * @returns  boolean\n */\nexport var isBrowser = function isBrowser() {\n  if (typeof process !== 'undefined' && process.env.NODE_ENV === 'TEST') {\n    return true;\n  }\n  return typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.matchMedia !== 'undefined' && !isNode;\n};", "export var omitUndefined = function omitUndefined(obj) {\n  var newObj = {};\n  Object.keys(obj || {}).forEach(function (key) {\n    if (obj[key] !== undefined) {\n      newObj[key] = obj[key];\n    }\n  });\n  if (Object.keys(newObj).length < 1) {\n    return undefined;\n  }\n  return newObj;\n};", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useLayoutEffect, useState } from 'react';\nexport default function useMediaQuery(mediaQuery) {\n  var isSsr = typeof window === 'undefined';\n  var _useState = useState(function () {\n      return isSsr ? false : window.matchMedia(mediaQuery).matches;\n    }),\n    _useState2 = _slicedToArray(_useState, 2),\n    matches = _useState2[0],\n    setMatches = _useState2[1];\n  useLayoutEffect(function () {\n    if (isSsr) {\n      return;\n    }\n    var mediaQueryList = window.matchMedia(mediaQuery);\n    var listener = function listener(e) {\n      return setMatches(e.matches);\n    };\n    mediaQueryList.addListener(listener);\n    return function () {\n      return mediaQueryList.removeListener(listener);\n    };\n  }, [mediaQuery]);\n  return matches;\n}", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useEffect, useState } from 'react';\nimport useMediaQuery from \"./query\";\nexport var MediaQueryEnum = {\n  xs: {\n    maxWidth: 575,\n    matchMedia: '(max-width: 575px)'\n  },\n  sm: {\n    minWidth: 576,\n    maxWidth: 767,\n    matchMedia: '(min-width: 576px) and (max-width: 767px)'\n  },\n  md: {\n    minWidth: 768,\n    maxWidth: 991,\n    matchMedia: '(min-width: 768px) and (max-width: 991px)'\n  },\n  lg: {\n    minWidth: 992,\n    maxWidth: 1199,\n    matchMedia: '(min-width: 992px) and (max-width: 1199px)'\n  },\n  xl: {\n    minWidth: 1200,\n    maxWidth: 1599,\n    matchMedia: '(min-width: 1200px) and (max-width: 1599px)'\n  },\n  xxl: {\n    minWidth: 1600,\n    matchMedia: '(min-width: 1600px)'\n  }\n};\n/**\n * loop query screen className\n * Array.find will throw a error\n * `Rendered more hooks than during the previous render.`\n * So should use Array.forEach\n */\nexport var getScreenClassName = function getScreenClassName() {\n  var queryKey = undefined;\n  // support ssr\n  if (typeof window === 'undefined') {\n    return queryKey;\n  }\n  var mediaQueryKey = Object.keys(MediaQueryEnum).find(function (key) {\n    var matchMedia = MediaQueryEnum[key].matchMedia;\n    if (window.matchMedia(matchMedia).matches) {\n      return true;\n    }\n    return false;\n  });\n  queryKey = mediaQueryKey;\n  return queryKey;\n};\nvar useBreakpoint = function useBreakpoint() {\n  var isMd = useMediaQuery(MediaQueryEnum.md.matchMedia);\n  var isLg = useMediaQuery(MediaQueryEnum.lg.matchMedia);\n  var isXxl = useMediaQuery(MediaQueryEnum.xxl.matchMedia);\n  var isXl = useMediaQuery(MediaQueryEnum.xl.matchMedia);\n  var isSm = useMediaQuery(MediaQueryEnum.sm.matchMedia);\n  var isXs = useMediaQuery(MediaQueryEnum.xs.matchMedia);\n  var _useState = useState(getScreenClassName()),\n    _useState2 = _slicedToArray(_useState, 2),\n    colSpan = _useState2[0],\n    setColSpan = _useState2[1];\n  useEffect(function () {\n    if (process.env.NODE_ENV === 'TEST') {\n      setColSpan(process.env.USE_MEDIA || 'md');\n      return;\n    }\n    if (isXxl) {\n      setColSpan('xxl');\n      return;\n    }\n    if (isXl) {\n      setColSpan('xl');\n      return;\n    }\n    if (isLg) {\n      setColSpan('lg');\n      return;\n    }\n    if (isMd) {\n      setColSpan('md');\n      return;\n    }\n    if (isSm) {\n      setColSpan('sm');\n      return;\n    }\n    if (isXs) {\n      setColSpan('xs');\n      return;\n    }\n    setColSpan('md');\n  }, [isMd, isLg, isXxl, isXl, isSm, isXs]);\n  return colSpan;\n};\nexport { useBreakpoint };", "import { useEffect } from 'react';\nimport { isBrowser } from \"../../isBrowser\";\nexport function useDocumentTitle(titleInfo, appDefaultTitle) {\n  var titleText = typeof titleInfo.pageName === 'string' ? titleInfo.title : appDefaultTitle;\n  useEffect(function () {\n    if (isBrowser() && titleText) {\n      document.title = titleText;\n    }\n  }, [titleInfo.title, titleText]);\n}", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { compareVersions } from \"./index\";\nimport { getVersion } from \"./openVisibleCompatible\";\nexport function coverToNewToken(token) {\n  if (compareVersions(getVersion(), '5.6.0') < 0) return token;\n  var deprecatedTokens = {\n    colorGroupTitle: 'groupTitleColor',\n    radiusItem: 'itemBorderRadius',\n    radiusSubMenuItem: 'subMenuItemBorderRadius',\n    colorItemText: 'itemColor',\n    colorItemTextHover: 'itemHoverColor',\n    colorItemTextHoverHorizontal: 'horizontalItemHoverColor',\n    colorItemTextSelected: 'itemSelectedColor',\n    colorItemTextSelectedHorizontal: 'horizontalItemSelectedColor',\n    colorItemTextDisabled: 'itemDisabledColor',\n    colorDangerItemText: 'dangerItemColor',\n    colorDangerItemTextHover: 'dangerItemHoverColor',\n    colorDangerItemTextSelected: 'dangerItemSelectedColor',\n    colorDangerItemBgActive: 'dangerItemActiveBg',\n    colorDangerItemBgSelected: 'dangerItemSelectedBg',\n    colorItemBg: 'itemBg',\n    colorItemBgHover: 'itemHoverBg',\n    colorSubItemBg: 'subMenuItemBg',\n    colorItemBgActive: 'itemActiveBg',\n    colorItemBgSelected: 'itemSelectedBg',\n    colorItemBgSelectedHorizontal: 'horizontalItemSelectedBg',\n    colorActiveBarWidth: 'activeBarWidth',\n    colorActiveBarHeight: 'activeBarHeight',\n    colorActiveBarBorderSize: 'activeBarBorderWidth'\n  };\n  var newToken = _objectSpread({}, token);\n  Object.keys(deprecatedTokens).forEach(function (key) {\n    if (newToken[key] !== undefined) {\n      // @ts-ignore\n      newToken[deprecatedTokens[key]] = newToken[key];\n      delete newToken[key];\n    }\n  });\n  return newToken;\n}", "/* eslint-disable no-redeclare */\n\n/* eslint-disable no-multi-assign */\n\n/* eslint-disable no-param-reassign */\n\n/* eslint-disable no-return-assign */\n\n/* eslint-disable no-new-wrappers */\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n\n/* eslint-disable no-var */\n\n/* eslint-disable no-plusplus */\n\n/* eslint-disable prefer-destructuring */\n\n/* eslint-disable @typescript-eslint/naming-convention */\n\n/* eslint-disable block-scoped-var */\n\n/* eslint-disable vars-on-top */\n\n/* eslint-disable no-bitwise */\n\n/* eslint-disable no-cond-assign */\n\n/*\n * A JavaScript implementation of the SHA256 hash function.\n *\n * FILE:\tsha256.js\n * VERSION:\t0.8\n * AUTHOR:\t<PERSON> <<EMAIL>>\n *\n * NOTE: This version is not tested thoroughly!\n *\n * Copyright (c) 2003, <PERSON>\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions\n * are met:\n * 1. Redistributions of source code must retain the above copyright\n *    notice, this list of conditions and the following disclaimer.\n * 2. Redistributions in binary form must reproduce the above copyright\n *    notice, this list of conditions and the following disclaimer in the\n *    documentation and/or other materials provided with the distribution.\n * 3. Neither the name of the copyright holder nor the names of contributors\n *    may be used to endorse or promote products derived from this software\n *    without specific prior written permission.\n *\n * ======================================================================\n *\n * THIS SOFTWARE IS PROVIDED BY THE AUTHORS ''AS IS'' AND ANY EXPRESS\n * OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHORS OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR\n * BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,\n * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE\n * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,\n * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n */\n\n/* SHA256 logical functions */\nfunction rotateRight(n, x) {\n  return x >>> n | x << 32 - n;\n}\n\nfunction choice(x, y, z) {\n  return x & y ^ ~x & z;\n}\n\nfunction majority(x, y, z) {\n  return x & y ^ x & z ^ y & z;\n}\n\nfunction sha256_Sigma0(x) {\n  return rotateRight(2, x) ^ rotateRight(13, x) ^ rotateRight(22, x);\n}\n\nfunction sha256_Sigma1(x) {\n  return rotateRight(6, x) ^ rotateRight(11, x) ^ rotateRight(25, x);\n}\n\nfunction sha256_sigma0(x) {\n  return rotateRight(7, x) ^ rotateRight(18, x) ^ x >>> 3;\n}\n\nfunction sha256_sigma1(x) {\n  return rotateRight(17, x) ^ rotateRight(19, x) ^ x >>> 10;\n}\n\nfunction sha256_expand(W, j) {\n  return W[j & 0x0f] += sha256_sigma1(W[j + 14 & 0x0f]) + W[j + 9 & 0x0f] + sha256_sigma0(W[j + 1 & 0x0f]);\n}\n/* Hash constant words K: */\n\n\nvar K256 = [0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5, 0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5, 0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3, 0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174, 0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc, 0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da, 0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7, 0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967, 0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13, 0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85, 0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3, 0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070, 0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5, 0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3, 0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208, 0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2];\n/* global arrays */\n\nvar ihash;\nvar count;\nvar buffer;\nvar sha256_hex_digits = '0123456789abcdef';\n/* Add 32-bit integers with 16-bit operations (bug in some JS-interpreters:\noverflow) */\n\nfunction safe_add(x, y) {\n  var lsw = (x & 0xffff) + (y & 0xffff);\n  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n  return msw << 16 | lsw & 0xffff;\n}\n/* Initialise the SHA256 computation */\n\n\nfunction sha256_init() {\n  ihash = new Array(8);\n  count = new Array(2);\n  buffer = new Array(64);\n  count[0] = count[1] = 0;\n  ihash[0] = 0x6a09e667;\n  ihash[1] = 0xbb67ae85;\n  ihash[2] = 0x3c6ef372;\n  ihash[3] = 0xa54ff53a;\n  ihash[4] = 0x510e527f;\n  ihash[5] = 0x9b05688c;\n  ihash[6] = 0x1f83d9ab;\n  ihash[7] = 0x5be0cd19;\n}\n/* Transform a 512-bit message block */\n\n\nfunction sha256_transform() {\n  var a;\n  var b;\n  var c;\n  var d;\n  var e;\n  var f;\n  var g;\n  var h;\n  var T1;\n  var T2;\n  var W = new Array(16);\n  /* Initialize registers with the previous intermediate value */\n\n  a = ihash[0];\n  b = ihash[1];\n  c = ihash[2];\n  d = ihash[3];\n  e = ihash[4];\n  f = ihash[5];\n  g = ihash[6];\n  h = ihash[7];\n  /* make 32-bit words */\n\n  for (var i = 0; i < 16; i++) {\n    W[i] = buffer[(i << 2) + 3] | buffer[(i << 2) + 2] << 8 | buffer[(i << 2) + 1] << 16 | buffer[i << 2] << 24;\n  }\n\n  for (var j = 0; j < 64; j++) {\n    T1 = h + sha256_Sigma1(e) + choice(e, f, g) + K256[j];\n    if (j < 16) T1 += W[j];else T1 += sha256_expand(W, j);\n    T2 = sha256_Sigma0(a) + majority(a, b, c);\n    h = g;\n    g = f;\n    f = e;\n    e = safe_add(d, T1);\n    d = c;\n    c = b;\n    b = a;\n    a = safe_add(T1, T2);\n  }\n  /* Compute the current intermediate hash value */\n\n\n  ihash[0] += a;\n  ihash[1] += b;\n  ihash[2] += c;\n  ihash[3] += d;\n  ihash[4] += e;\n  ihash[5] += f;\n  ihash[6] += g;\n  ihash[7] += h;\n}\n/* Read the next chunk of data and update the SHA256 computation */\n\n\nfunction sha256_update(data, inputLen) {\n  var i;\n  var index;\n  var curpos = 0;\n  /* Compute number of bytes mod 64 */\n\n  index = count[0] >> 3 & 0x3f;\n  var remainder = inputLen & 0x3f;\n  /* Update number of bits */\n\n  if ((count[0] += inputLen << 3) < inputLen << 3) count[1]++;\n  count[1] += inputLen >> 29;\n  /* Transform as many times as possible */\n\n  for (i = 0; i + 63 < inputLen; i += 64) {\n    for (var j = index; j < 64; j++) {\n      buffer[j] = data.charCodeAt(curpos++);\n    }\n\n    sha256_transform();\n    index = 0;\n  }\n  /* Buffer remaining input */\n\n\n  for (var _j = 0; _j < remainder; _j++) {\n    buffer[_j] = data.charCodeAt(curpos++);\n  }\n}\n/* Finish the computation by operations such as padding */\n\n\nfunction sha256_final() {\n  var index = count[0] >> 3 & 0x3f;\n  buffer[index++] = 0x80;\n\n  if (index <= 56) {\n    for (var i = index; i < 56; i++) {\n      buffer[i] = 0;\n    }\n  } else {\n    for (var _i = index; _i < 64; _i++) {\n      buffer[_i] = 0;\n    }\n\n    sha256_transform();\n\n    for (var _i2 = 0; _i2 < 56; _i2++) {\n      buffer[_i2] = 0;\n    }\n  }\n\n  buffer[56] = count[1] >>> 24 & 0xff;\n  buffer[57] = count[1] >>> 16 & 0xff;\n  buffer[58] = count[1] >>> 8 & 0xff;\n  buffer[59] = count[1] & 0xff;\n  buffer[60] = count[0] >>> 24 & 0xff;\n  buffer[61] = count[0] >>> 16 & 0xff;\n  buffer[62] = count[0] >>> 8 & 0xff;\n  buffer[63] = count[0] & 0xff;\n  sha256_transform();\n}\n/* Split the internal hash values into an array of bytes */\n\n\nfunction sha256_encode_bytes() {\n  var j = 0;\n  var output = new Array(32);\n\n  for (var i = 0; i < 8; i++) {\n    output[j++] = ihash[i] >>> 24 & 0xff;\n    output[j++] = ihash[i] >>> 16 & 0xff;\n    output[j++] = ihash[i] >>> 8 & 0xff;\n    output[j++] = ihash[i] & 0xff;\n  }\n\n  return output;\n}\n/* Get the internal hash as a hex string */\n\n\nfunction sha256_encode_hex() {\n  var output = new String();\n\n  for (var i = 0; i < 8; i++) {\n    for (var j = 28; j >= 0; j -= 4) {\n      output += sha256_hex_digits.charAt(ihash[i] >>> j & 0x0f);\n    }\n  }\n\n  return output;\n}\n/* Main function: returns a hex string representing the SHA256 value of the\ngiven data */\n\n\nfunction digest(data) {\n  sha256_init();\n  sha256_update(data, data.length);\n  sha256_final();\n  return sha256_encode_hex();\n}\n\nexport default digest;", "function _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nvar _excluded = [\"pro_layout_parentKeys\", \"children\", \"icon\", \"flatMenu\", \"indexRoute\", \"routes\"];\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e2) { throw _e2; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e3) { didErr = true; err = _e3; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); Object.defineProperty(subClass, \"prototype\", { writable: false }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } else if (call !== void 0) { throw new TypeError(\"Derived constructors may only return object or undefined\"); } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _wrapNativeSuper(Class) { var _cache = typeof Map === \"function\" ? new Map() : undefined; _wrapNativeSuper = function _wrapNativeSuper(Class) { if (Class === null || !_isNativeFunction(Class)) return Class; if (typeof Class !== \"function\") { throw new TypeError(\"Super expression must either be null or a function\"); } if (typeof _cache !== \"undefined\") { if (_cache.has(Class)) return _cache.get(Class); _cache.set(Class, Wrapper); } function Wrapper() { return _construct(Class, arguments, _getPrototypeOf(this).constructor); } Wrapper.prototype = Object.create(Class.prototype, { constructor: { value: Wrapper, enumerable: false, writable: true, configurable: true } }); return _setPrototypeOf(Wrapper, Class); }; return _wrapNativeSuper(Class); }\n\nfunction _construct(Parent, args, Class) { if (_isNativeReflectConstruct()) { _construct = Reflect.construct.bind(); } else { _construct = function _construct(Parent, args, Class) { var a = [null]; a.push.apply(a, args); var Constructor = Function.bind.apply(Parent, a); var instance = new Constructor(); if (Class) _setPrototypeOf(instance, Class.prototype); return instance; }; } return _construct.apply(null, arguments); }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _isNativeFunction(fn) { return Function.toString.call(fn).indexOf(\"[native code]\") !== -1; }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n//@ts-ignore\nimport { pathToRegexp } from '../path-to-regexp';\nimport sha265 from '../sha265';\nexport var childrenPropsName = 'routes';\nexport function stripQueryStringAndHashFromPath(url) {\n  return url.split('?')[0].split('#')[0];\n}\nexport var isUrl = function isUrl(path) {\n  if (!path.startsWith('http')) {\n    return false;\n  }\n\n  try {\n    var url = new URL(path);\n    return !!url;\n  } catch (error) {\n    return false;\n  }\n};\nexport var getKeyByPath = function getKeyByPath(item) {\n  var path = item.path;\n\n  if (!path || path === '/') {\n    // 如果还是没有，用对象的hash 生成一个\n    try {\n      return \"/\".concat(sha265(JSON.stringify(item)));\n    } catch (error) {// dom some thing\n    }\n  }\n\n  return path ? stripQueryStringAndHashFromPath(path) : path;\n};\n/**\n * 获取locale，增加了一个功能，如果 locale = false，将不使用国际化\n * @param item\n * @param parentName\n */\n\nvar getItemLocaleName = function getItemLocaleName(item, parentName) {\n  var name = item.name,\n      locale = item.locale; // 如果配置了 locale 并且 locale 为 false或 \"\"\n\n  if ('locale' in item && locale === false || !name) {\n    return false;\n  }\n\n  return item.locale || \"\".concat(parentName, \".\").concat(name);\n};\n/**\n * 如果不是 / 开头的和父节点做一下合并\n * 如果是 / 开头的不作任何处理\n * 如果是 url 也直接返回\n * @param path\n * @param parentPath\n */\n\n\nvar mergePath = function mergePath() {\n  var path = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n  var parentPath = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '/';\n\n  if (path.endsWith('/*')) {\n    return path.replace('/*', '/');\n  }\n\n  if ((path || parentPath).startsWith('/')) {\n    return path;\n  }\n\n  if (isUrl(path)) {\n    return path;\n  }\n\n  return \"/\".concat(parentPath, \"/\").concat(path).replace(/\\/\\//g, '/').replace(/\\/\\//g, '/');\n}; // bigfish 的兼容准话\n\n\nvar bigfishCompatibleConversions = function bigfishCompatibleConversions(route, props) {\n  var _route$menu = route.menu,\n      menu = _route$menu === void 0 ? {} : _route$menu,\n      indexRoute = route.indexRoute,\n      _route$path = route.path,\n      path = _route$path === void 0 ? '' : _route$path;\n  var routerChildren = route.children || [];\n  var _menu$name = menu.name,\n      name = _menu$name === void 0 ? route.name : _menu$name,\n      _menu$icon = menu.icon,\n      icon = _menu$icon === void 0 ? route.icon : _menu$icon,\n      _menu$hideChildren = menu.hideChildren,\n      hideChildren = _menu$hideChildren === void 0 ? route.hideChildren : _menu$hideChildren,\n      _menu$flatMenu = menu.flatMenu,\n      flatMenu = _menu$flatMenu === void 0 ? route.flatMenu : _menu$flatMenu; // 兼容平铺式写法\n  // 拼接 childrenRoutes, 处理存在 indexRoute 时的逻辑\n\n  var childrenList = indexRoute && // 如果只有 redirect,不用处理的\n  Object.keys(indexRoute).join(',') !== 'redirect' ? [_objectSpread({\n    path: path,\n    menu: menu\n  }, indexRoute)].concat(routerChildren || []) : routerChildren; // 拼接返回的 menu 数据\n\n  var result = _objectSpread({}, route);\n\n  if (name) {\n    result.name = name;\n  }\n\n  if (icon) {\n    result.icon = icon;\n  }\n\n  if (childrenList && childrenList.length) {\n    /** 在菜单中隐藏子项 */\n    if (hideChildren) {\n      delete result.children;\n      return result;\n    } // 需要重新进行一次\n\n\n    var finalChildren = formatter(_objectSpread(_objectSpread({}, props), {}, {\n      data: childrenList\n    }), route);\n    /** 在菜单中只隐藏此项，子项往上提，仍旧展示 */\n\n    if (flatMenu) {\n      return finalChildren;\n    }\n\n    delete result[childrenPropsName];\n  }\n\n  return result;\n};\n\nvar notNullArray = function notNullArray(value) {\n  return Array.isArray(value) && value.length > 0;\n};\n/**\n *\n * @param props\n * @param parent\n */\n\n\nfunction formatter(props) {\n  var parent = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    path: '/'\n  };\n  var data = props.data,\n      formatMessage = props.formatMessage,\n      parentName = props.parentName,\n      menuLocale = props.locale;\n\n  if (!data || !Array.isArray(data)) {\n    return [];\n  }\n\n  return data.filter(function (item) {\n    if (!item) return false;\n    if (notNullArray(item.children)) return true;\n    if (item.path) return true;\n    if (item.originPath) return true;\n    if (item.layout) return true; // 重定向\n\n    if (item.redirect) return false;\n    if (item.unaccessible) return false;\n    return false;\n  }).filter(function (item) {\n    var _item$menu, _item$menu2;\n\n    if ((item === null || item === void 0 ? void 0 : (_item$menu = item.menu) === null || _item$menu === void 0 ? void 0 : _item$menu.name) || (item === null || item === void 0 ? void 0 : item.flatMenu) || (item === null || item === void 0 ? void 0 : (_item$menu2 = item.menu) === null || _item$menu2 === void 0 ? void 0 : _item$menu2.flatMenu)) {\n      return true;\n    } // 显示指定在 menu 中隐藏该项\n    // layout 插件的功能，其实不应该存在的\n\n\n    if (item.menu === false) {\n      return false;\n    }\n\n    return true;\n  }).map(function (finallyItem) {\n    var item = _objectSpread(_objectSpread({}, finallyItem), {}, {\n      path: finallyItem.path || finallyItem.originPath\n    });\n\n    if (!item.children && item[childrenPropsName]) {\n      item.children = item[childrenPropsName];\n      delete item[childrenPropsName];\n    } // 是否没有权限查看\n    // 这样就不会显示，是一个兼容性的方式\n\n\n    if (item.unaccessible) {\n      // eslint-disable-next-line no-param-reassign\n      delete item.name;\n    }\n\n    if (item.path === '*') {\n      item.path = '.';\n    }\n\n    if (item.path === '/*') {\n      item.path = '.';\n    }\n\n    if (!item.path && item.originPath) {\n      item.path = item.originPath;\n    }\n\n    return item;\n  }).map(function () {\n    var item = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {\n      path: '/'\n    };\n    var routerChildren = item.children || item[childrenPropsName] || [];\n    var path = mergePath(item.path, parent ? parent.path : '/');\n    var name = item.name;\n    var locale = getItemLocaleName(item, parentName || 'menu'); // if enableMenuLocale use item.name,\n    // close menu international\n\n    var localeName = locale !== false && menuLocale !== false && formatMessage && locale ? formatMessage({\n      id: locale,\n      defaultMessage: name\n    }) : name;\n\n    var _parent$pro_layout_pa = parent.pro_layout_parentKeys,\n        pro_layout_parentKeys = _parent$pro_layout_pa === void 0 ? [] : _parent$pro_layout_pa,\n        children = parent.children,\n        icon = parent.icon,\n        flatMenu = parent.flatMenu,\n        indexRoute = parent.indexRoute,\n        routes = parent.routes,\n        restParent = _objectWithoutProperties(parent, _excluded);\n\n    var item_pro_layout_parentKeys = new Set([].concat(_toConsumableArray(pro_layout_parentKeys), _toConsumableArray(item.parentKeys || [])));\n\n    if (parent.key) {\n      item_pro_layout_parentKeys.add(parent.key);\n    }\n\n    var finallyItem = _objectSpread(_objectSpread(_objectSpread({}, restParent), {}, {\n      menu: undefined\n    }, item), {}, {\n      path: path,\n      locale: locale,\n      key: item.key || getKeyByPath(_objectSpread(_objectSpread({}, item), {}, {\n        path: path\n      })),\n      pro_layout_parentKeys: Array.from(item_pro_layout_parentKeys).filter(function (key) {\n        return key && key !== '/';\n      })\n    });\n\n    if (localeName) {\n      finallyItem.name = localeName;\n    } else {\n      delete finallyItem.name;\n    }\n\n    if (finallyItem.menu === undefined) {\n      delete finallyItem.menu;\n    }\n\n    if (notNullArray(routerChildren)) {\n      var formatterChildren = formatter(_objectSpread(_objectSpread({}, props), {}, {\n        data: routerChildren,\n        parentName: locale || ''\n      }), finallyItem);\n\n      if (notNullArray(formatterChildren)) {\n        finallyItem.children = formatterChildren;\n      }\n    }\n\n    return bigfishCompatibleConversions(finallyItem, props);\n  }).flat(1);\n}\n/**\n * 删除 hideInMenu 和 item.name 不存在的\n */\n\n\nvar defaultFilterMenuData = function defaultFilterMenuData() {\n  var menuData = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return menuData.filter(function (item) {\n    return item && (item.name || notNullArray(item.children)) && !item.hideInMenu && !item.redirect;\n  }).map(function (item) {\n    var newItem = _objectSpread({}, item);\n\n    var routerChildren = newItem.children || item[childrenPropsName] || [];\n    delete newItem[childrenPropsName];\n\n    if (notNullArray(routerChildren) && !newItem.hideChildrenInMenu && routerChildren.some(function (child) {\n      return child && !!child.name;\n    })) {\n      var newChildren = defaultFilterMenuData(routerChildren);\n      if (newChildren.length) return _objectSpread(_objectSpread({}, newItem), {}, {\n        children: newChildren\n      });\n    }\n\n    return _objectSpread({}, item);\n  }).filter(function (item) {\n    return item;\n  });\n};\n/**\n * support pathToRegexp get string\n */\n\n\nvar RouteListMap = /*#__PURE__*/function (_Map) {\n  _inherits(RouteListMap, _Map);\n\n  var _super = _createSuper(RouteListMap);\n\n  function RouteListMap() {\n    _classCallCheck(this, RouteListMap);\n\n    return _super.apply(this, arguments);\n  }\n\n  _createClass(RouteListMap, [{\n    key: \"get\",\n    value: function get(pathname) {\n      var routeValue;\n\n      try {\n        // eslint-disable-next-line no-restricted-syntax\n        var _iterator = _createForOfIteratorHelper(this.entries()),\n            _step;\n\n        try {\n          for (_iterator.s(); !(_step = _iterator.n()).done;) {\n            var _step$value = _slicedToArray(_step.value, 2),\n                key = _step$value[0],\n                value = _step$value[1];\n\n            var path = stripQueryStringAndHashFromPath(key);\n\n            if (!isUrl(key) && pathToRegexp(path, []).test(pathname)) {\n              routeValue = value;\n              break;\n            }\n          }\n        } catch (err) {\n          _iterator.e(err);\n        } finally {\n          _iterator.f();\n        }\n      } catch (error) {\n        routeValue = undefined;\n      }\n\n      return routeValue;\n    }\n  }]);\n\n  return RouteListMap;\n}( /*#__PURE__*/_wrapNativeSuper(Map));\n/**\n * 获取面包屑映射\n * @param MenuDataItem[] menuData 菜单配置\n */\n\n\nvar getBreadcrumbNameMap = function getBreadcrumbNameMap(menuData) {\n  // Map is used to ensure the order of keys\n  var routerMap = new RouteListMap();\n\n  var flattenMenuData = function flattenMenuData(data, parent) {\n    data.forEach(function (menuItem) {\n      var routerChildren = menuItem.children || menuItem[childrenPropsName] || [];\n\n      if (notNullArray(routerChildren)) {\n        flattenMenuData(routerChildren, menuItem);\n      } // Reduce memory usage\n\n\n      var path = mergePath(menuItem.path, parent ? parent.path : '/');\n      routerMap.set(stripQueryStringAndHashFromPath(path), menuItem);\n    });\n  };\n\n  flattenMenuData(menuData);\n  return routerMap;\n};\n\nvar clearChildren = function clearChildren() {\n  var menuData = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return menuData.map(function (item) {\n    var routerChildren = item.children || item[childrenPropsName];\n\n    if (notNullArray(routerChildren)) {\n      var newChildren = clearChildren(routerChildren);\n      if (newChildren.length) return _objectSpread({}, item);\n    }\n\n    var finallyItem = _objectSpread({}, item);\n\n    delete finallyItem[childrenPropsName];\n    delete finallyItem.children;\n    return finallyItem;\n  }).filter(function (item) {\n    return item;\n  });\n};\n/**\n * @param routeList 路由配置\n * @param locale 是否使用国际化\n * @param formatMessage 国际化的程序\n * @param ignoreFilter 是否筛选掉不展示的 menuItem 项，plugin-layout需要所有项目来计算布局样式\n * @returns { breadcrumb, menuData}\n */\n\n\nvar transformRoute = function transformRoute(routeList, locale, formatMessage, ignoreFilter) {\n  var originalMenuData = formatter({\n    data: routeList,\n    formatMessage: formatMessage,\n    locale: locale\n  });\n  var menuData = ignoreFilter ? clearChildren(originalMenuData) : defaultFilterMenuData(originalMenuData); // Map type used for internal logic\n\n  var breadcrumb = getBreadcrumbNameMap(originalMenuData);\n  return {\n    breadcrumb: breadcrumb,\n    menuData: menuData\n  };\n};\n\nexport default transformRoute;", "function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nimport { stripQueryStringAndHashFromPath, childrenPropsName } from '../transformRoute/transformRoute';\n/**\n * 获取打平的 menuData\n * 以 path 为 key\n * @param menuData\n */\n\nexport var getFlatMenus = function getFlatMenus() {\n  var menuData = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var menus = {};\n  menuData.forEach(function (mapItem) {\n    var item = _objectSpread({}, mapItem);\n\n    if (!item || !item.key) {\n      return;\n    }\n\n    if (!item.children && item[childrenPropsName]) {\n      item.children = item[childrenPropsName];\n      delete item[childrenPropsName];\n    }\n\n    var routerChildren = item.children || [];\n    menus[stripQueryStringAndHashFromPath(item.path || item.key || '/')] = _objectSpread({}, item);\n    menus[item.key || item.path || '/'] = _objectSpread({}, item);\n\n    if (routerChildren) {\n      menus = _objectSpread(_objectSpread({}, menus), getFlatMenus(routerChildren));\n    }\n  });\n  return menus;\n};\nexport default getFlatMenus;", "//@ts-ignore\nimport { pathToRegexp } from '../path-to-regexp';\nimport getFlatMenu from '../getFlatMenus/getFlatMenus';\nimport { isUrl, stripQueryStringAndHashFromPath } from '../transformRoute/transformRoute';\nexport var getMenuMatches = function getMenuMatches() {\n  var flatMenuKeys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var path = arguments.length > 1 ? arguments[1] : undefined;\n  var exact = arguments.length > 2 ? arguments[2] : undefined;\n  return flatMenuKeys.filter(function (item) {\n    if (item === '/' && path === '/') {\n      return true;\n    }\n\n    if (item !== '/' && item !== '/*' && item && !isUrl(item)) {\n      var pathKey = stripQueryStringAndHashFromPath(item);\n\n      try {\n        // exact\n        if (exact) {\n          if (pathToRegexp(\"\".concat(pathKey)).test(path)) {\n            return true;\n          }\n        } // /a\n\n\n        if (pathToRegexp(\"\".concat(pathKey), []).test(path)) {\n          return true;\n        } // /a/b/b\n\n\n        if (pathToRegexp(\"\".concat(pathKey, \"/(.*)\")).test(path)) {\n          return true;\n        }\n      } catch (error) {// console.log(error, path);\n      }\n    }\n\n    return false;\n  }).sort(function (a, b) {\n    // 如果完全匹配放到最后面\n    if (a === path) {\n      return 10;\n    }\n\n    if (b === path) {\n      return -10;\n    }\n\n    return a.substr(1).split('/').length - b.substr(1).split('/').length;\n  });\n};\n/**\n * 获取当前的选中菜单列表\n * @param pathname\n * @param menuData\n * @returns MenuDataItem[]\n */\n\nexport var getMatchMenu = function getMatchMenu(pathname, menuData,\n/**\n * 要不要展示全部的 key\n */\nfullKeys, exact) {\n  var flatMenus = getFlatMenu(menuData);\n  var flatMenuKeys = Object.keys(flatMenus);\n  var menuPathKeys = getMenuMatches(flatMenuKeys, pathname || '/', exact);\n\n  if (!menuPathKeys || menuPathKeys.length < 1) {\n    return [];\n  }\n\n  if (!fullKeys) {\n    menuPathKeys = [menuPathKeys[menuPathKeys.length - 1]];\n  }\n\n  return menuPathKeys.map(function (menuPathKey) {\n    var menuItem = flatMenus[menuPathKey] || {\n      pro_layout_parentKeys: '',\n      key: ''\n    }; // 去重\n\n    var map = new Map();\n    var parentItems = (menuItem.pro_layout_parentKeys || []).map(function (key) {\n      if (map.has(key)) {\n        return null;\n      }\n\n      map.set(key, true);\n      return flatMenus[key];\n    }).filter(function (item) {\n      return item;\n    });\n\n    if (menuItem.key) {\n      parentItems.push(menuItem);\n    }\n\n    return parentItems;\n  }).flat(1);\n};\nexport default getMatchMenu;", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { ProProvider } from '@ant-design/pro-provider';\nimport { ErrorBoundary } from '@ant-design/pro-utils';\nimport { Layout } from 'antd';\nimport classNames from 'classnames';\nimport React, { useContext } from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar WrapContent = function WrapContent(props) {\n  var _useContext = useContext(ProProvider),\n    hashId = _useContext.hashId;\n  var style = props.style,\n    prefixCls = props.prefixCls,\n    children = props.children,\n    _props$hasPageContain = props.hasPageContainer,\n    hasPageContainer = _props$hasPageContain === void 0 ? 0 : _props$hasPageContain;\n  var contentClassName = classNames(\"\".concat(prefixCls, \"-content\"), hashId, _defineProperty(_defineProperty({}, \"\".concat(prefixCls, \"-has-header\"), props.hasHeader), \"\".concat(prefixCls, \"-content-has-page-container\"), hasPageContainer > 0));\n  var ErrorComponent = props.ErrorBoundary || ErrorBoundary;\n  return props.ErrorBoundary === false ? /*#__PURE__*/_jsx(Layout.Content, {\n    className: contentClassName,\n    style: style,\n    children: children\n  }) : /*#__PURE__*/_jsx(ErrorComponent, {\n    children: /*#__PURE__*/_jsx(Layout.Content, {\n      className: contentClassName,\n      style: style,\n      children: children\n    })\n  });\n};\nexport { WrapContent };", "import { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport var Logo = function Logo() {\n  return /*#__PURE__*/_jsxs(\"svg\", {\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 200 200\",\n    children: [/*#__PURE__*/_jsxs(\"defs\", {\n      children: [/*#__PURE__*/_jsxs(\"linearGradient\", {\n        x1: \"62.1023273%\",\n        y1: \"0%\",\n        x2: \"108.19718%\",\n        y2: \"37.8635764%\",\n        id: \"linearGradient-1\",\n        children: [/*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#4285EB\",\n          offset: \"0%\"\n        }), /*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#2EC7FF\",\n          offset: \"100%\"\n        })]\n      }), /*#__PURE__*/_jsxs(\"linearGradient\", {\n        x1: \"69.644116%\",\n        y1: \"0%\",\n        x2: \"54.0428975%\",\n        y2: \"108.456714%\",\n        id: \"linearGradient-2\",\n        children: [/*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#29CDFF\",\n          offset: \"0%\"\n        }), /*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#148EFF\",\n          offset: \"37.8600687%\"\n        }), /*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#0A60FF\",\n          offset: \"100%\"\n        })]\n      }), /*#__PURE__*/_jsxs(\"linearGradient\", {\n        x1: \"69.6908165%\",\n        y1: \"-12.9743587%\",\n        x2: \"16.7228981%\",\n        y2: \"117.391248%\",\n        id: \"linearGradient-3\",\n        children: [/*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#FA816E\",\n          offset: \"0%\"\n        }), /*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#F74A5C\",\n          offset: \"41.472606%\"\n        }), /*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#F51D2C\",\n          offset: \"100%\"\n        })]\n      }), /*#__PURE__*/_jsxs(\"linearGradient\", {\n        x1: \"68.1279872%\",\n        y1: \"-35.6905737%\",\n        x2: \"30.4400914%\",\n        y2: \"114.942679%\",\n        id: \"linearGradient-4\",\n        children: [/*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#FA8E7D\",\n          offset: \"0%\"\n        }), /*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#F74A5C\",\n          offset: \"51.2635191%\"\n        }), /*#__PURE__*/_jsx(\"stop\", {\n          stopColor: \"#F51D2C\",\n          offset: \"100%\"\n        })]\n      })]\n    }), /*#__PURE__*/_jsx(\"g\", {\n      stroke: \"none\",\n      strokeWidth: 1,\n      fill: \"none\",\n      fillRule: \"evenodd\",\n      children: /*#__PURE__*/_jsx(\"g\", {\n        transform: \"translate(-20.000000, -20.000000)\",\n        children: /*#__PURE__*/_jsx(\"g\", {\n          transform: \"translate(20.000000, 20.000000)\",\n          children: /*#__PURE__*/_jsxs(\"g\", {\n            children: [/*#__PURE__*/_jsxs(\"g\", {\n              fillRule: \"nonzero\",\n              children: [/*#__PURE__*/_jsxs(\"g\", {\n                children: [/*#__PURE__*/_jsx(\"path\", {\n                  d: \"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C99.2571609,26.9692191 101.032305,26.9692191 102.20193,28.1378823 L129.985225,55.8983314 C134.193707,60.1033528 141.017005,60.1033528 145.225487,55.8983314 C149.433969,51.69331 149.433969,44.8756232 145.225487,40.6706018 L108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z\",\n                  fill: \"url(#linearGradient-1)\"\n                }), /*#__PURE__*/_jsx(\"path\", {\n                  d: \"M91.5880863,4.17652823 L4.17996544,91.5127728 C-0.519240605,96.2081146 -0.519240605,103.791885 4.17996544,108.487227 L91.5880863,195.823472 C96.2872923,200.518814 103.877304,200.518814 108.57651,195.823472 L145.225487,159.204632 C149.433969,154.999611 149.433969,148.181924 145.225487,143.976903 C141.017005,139.771881 134.193707,139.771881 129.985225,143.976903 L102.20193,171.737352 C101.032305,172.906015 99.2571609,172.906015 98.0875359,171.737352 L28.285908,101.993122 C27.1162831,100.824459 27.1162831,99.050775 28.285908,97.8821118 L98.0875359,28.1378823 C100.999864,25.6271836 105.751642,20.541824 112.729652,19.3524487 C117.915585,18.4685261 123.585219,20.4140239 129.738554,25.1889424 C125.624663,21.0784292 118.571995,14.0340304 108.58055,4.05574592 C103.862049,-0.537986846 96.2692618,-0.500797906 91.5880863,4.17652823 Z\",\n                  fill: \"url(#linearGradient-2)\"\n                })]\n              }), /*#__PURE__*/_jsx(\"path\", {\n                d: \"M153.685633,135.854579 C157.894115,140.0596 164.717412,140.0596 168.925894,135.854579 L195.959977,108.842726 C200.659183,104.147384 200.659183,96.5636133 195.960527,91.8688194 L168.690777,64.7181159 C164.472332,60.5180858 157.646868,60.5241425 153.435895,64.7316526 C149.227413,68.936674 149.227413,75.7543607 153.435895,79.9593821 L171.854035,98.3623765 C173.02366,99.5310396 173.02366,101.304724 171.854035,102.473387 L153.685633,120.626849 C149.47715,124.83187 149.47715,131.649557 153.685633,135.854579 Z\",\n                fill: \"url(#linearGradient-3)\"\n              })]\n            }), /*#__PURE__*/_jsx(\"ellipse\", {\n              fill: \"url(#linearGradient-4)\",\n              cx: \"100.519339\",\n              cy: \"100.436681\",\n              rx: \"23.6001926\",\n              ry: \"23.580786\"\n            })]\n          })\n        })\n      })\n    })]\n  });\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nexport var getOpenKeysFromMenuData = function getOpenKeysFromMenuData(menuData) {\n  return (menuData || []).reduce(function (pre, item) {\n    if (item.key) {\n      pre.push(item.key);\n    }\n    if (item.children || item.routes) {\n      var newArray = pre.concat(getOpenKeysFromMenuData(item.children || item.routes) || []);\n      return newArray;\n    }\n    return pre;\n  }, []);\n};\nvar themeConfig = {\n  techBlue: '#1677FF',\n  daybreak: '#1890ff',\n  dust: '#F5222D',\n  volcano: '#FA541C',\n  sunset: '#FAAD14',\n  cyan: '#13C2C2',\n  green: '#52C41A',\n  geekblue: '#2F54EB',\n  purple: '#722ED1'\n};\n/**\n * Daybreak-> #1890ff\n *\n * @param val\n */\nexport function genStringToTheme(val) {\n  return val && themeConfig[val] ? themeConfig[val] : val || '';\n}\nexport function clearMenuItem(menusData) {\n  return menusData.map(function (item) {\n    var children = item.children || [];\n    var finalItem = _objectSpread({}, item);\n    if (!finalItem.children && finalItem.routes) {\n      finalItem.children = finalItem.routes;\n    }\n    if (!finalItem.name || finalItem.hideInMenu) {\n      return null;\n    }\n    if (finalItem && finalItem !== null && finalItem !== void 0 && finalItem.children) {\n      if (!finalItem.hideChildrenInMenu && children.some(function (child) {\n        return child && child.name && !child.hideInMenu;\n      })) {\n        return _objectSpread(_objectSpread({}, item), {}, {\n          children: clearMenuItem(children)\n        });\n      }\n      // children 为空就直接删掉\n      delete finalItem.children;\n    }\n    delete finalItem.routes;\n    return finalItem;\n  }).filter(function (item) {\n    return item;\n  });\n}", "// This icon file is generated automatically.\nvar MenuOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M904 160H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0 624H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8zm0-312H120c-4.4 0-8 3.6-8 8v64c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-64c0-4.4-3.6-8-8-8z\" } }] }, \"name\": \"menu\", \"theme\": \"outlined\" };\nexport default MenuOutlined;\n", "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport MenuOutlinedSvg from \"@ant-design/icons-svg/es/asn/MenuOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar MenuOutlined = function MenuOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: MenuOutlinedSvg\n  }));\n};\n\n/**![menu](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTkwNCAxNjBIMTIwYy00LjQgMC04IDMuNi04IDh2NjRjMCA0LjQgMy42IDggOCA4aDc4NGM0LjQgMCA4LTMuNiA4LTh2LTY0YzAtNC40LTMuNi04LTgtOHptMCA2MjRIMTIwYy00LjQgMC04IDMuNi04IDh2NjRjMCA0LjQgMy42IDggOCA4aDc4NGM0LjQgMCA4LTMuNiA4LTh2LTY0YzAtNC40LTMuNi04LTgtOHptMC0zMTJIMTIwYy00LjQgMC04IDMuNi04IDh2NjRjMCA0LjQgMy42IDggOCA4aDc4NGM0LjQgMCA4LTMuNiA4LTh2LTY0YzAtNC40LTMuNi04LTgtOHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(MenuOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'MenuOutlined';\n}\nexport default RefIcon;", "import { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * 默认的应用列表的图标\n *\n */\nexport var AppsLogo = function AppsLogo() {\n  return /*#__PURE__*/_jsx(\"svg\", {\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 12 12\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      d: \"M0 0h3v3H0V0zm4.5 0h3v3h-3V0zM9 0h3v3H9V0zM0 4.5h3v3H0v-3zm4.503 0h3v3h-3v-3zM9 4.5h3v3H9v-3zM0 9h3v3H0V9zm4.503 0h3v3h-3V9zM9 9h3v3H9V9z\"\n    })\n  });\n};", "import React from 'react';\nimport { defaultRenderLogo } from \"./index\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport var DefaultContent = function DefaultContent(props) {\n  var appList = props.appList,\n    baseClassName = props.baseClassName,\n    hashId = props.hashId,\n    itemClick = props.itemClick;\n  return /*#__PURE__*/_jsx(\"div\", {\n    className: \"\".concat(baseClassName, \"-content \").concat(hashId).trim(),\n    children: /*#__PURE__*/_jsx(\"ul\", {\n      className: \"\".concat(baseClassName, \"-content-list \").concat(hashId).trim(),\n      children: appList === null || appList === void 0 ? void 0 : appList.map(function (app, index) {\n        var _app$children;\n        if (app !== null && app !== void 0 && (_app$children = app.children) !== null && _app$children !== void 0 && _app$children.length) {\n          return /*#__PURE__*/_jsxs(\"div\", {\n            className: \"\".concat(baseClassName, \"-content-list-item-group \").concat(hashId).trim(),\n            children: [/*#__PURE__*/_jsx(\"div\", {\n              className: \"\".concat(baseClassName, \"-content-list-item-group-title \").concat(hashId).trim(),\n              children: app.title\n            }), /*#__PURE__*/_jsx(DefaultContent, {\n              hashId: hashId,\n              itemClick: itemClick,\n              appList: app === null || app === void 0 ? void 0 : app.children,\n              baseClassName: baseClassName\n            })]\n          }, index);\n        }\n        return /*#__PURE__*/_jsx(\"li\", {\n          className: \"\".concat(baseClassName, \"-content-list-item \").concat(hashId).trim(),\n          onClick: function onClick(e) {\n            e.stopPropagation();\n            itemClick === null || itemClick === void 0 || itemClick(app);\n          },\n          children: /*#__PURE__*/_jsxs(\"a\", {\n            href: itemClick ? undefined : app.url,\n            target: app.target,\n            rel: \"noreferrer\",\n            children: [defaultRenderLogo(app.icon), /*#__PURE__*/_jsxs(\"div\", {\n              children: [/*#__PURE__*/_jsx(\"div\", {\n                children: app.title\n              }), app.desc ? /*#__PURE__*/_jsx(\"span\", {\n                children: app.desc\n              }) : null]\n            })]\n          })\n        }, index);\n      })\n    })\n  });\n};", "/**\n * 判断是不是一个 url\n * @param  {string|undefined} path\n * @returns boolean\n */\nexport var isUrl = function isUrl(path) {\n  if (!path) return false;\n  if (!path.startsWith('http')) {\n    return false;\n  }\n  try {\n    var url = new URL(path);\n    return !!url;\n  } catch (error) {\n    return false;\n  }\n};", "import { isUrl } from '@ant-design/pro-utils';\nimport React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * simple 模式渲染logo的方式\n *\n * @param logo\n * @param title\n * @returns\n */\nexport var renderLogo = function renderLogo(logo, title) {\n  if (logo && typeof logo === 'string' && isUrl(logo)) {\n    return /*#__PURE__*/_jsx(\"img\", {\n      src: logo,\n      alt: \"logo\"\n    });\n  }\n  if (typeof logo === 'function') {\n    return logo();\n  }\n  if (logo && typeof logo === 'string') {\n    return /*#__PURE__*/_jsx(\"div\", {\n      id: \"avatarLogo\",\n      children: logo\n    });\n  }\n  if (!logo && title && typeof title === 'string') {\n    var symbol = title.substring(0, 1);\n    return /*#__PURE__*/_jsx(\"div\", {\n      id: \"avatarLogo\",\n      children: symbol\n    });\n  }\n  return logo;\n};\nexport var SimpleContent = function SimpleContent(props) {\n  var appList = props.appList,\n    baseClassName = props.baseClassName,\n    hashId = props.hashId,\n    itemClick = props.itemClick;\n  return /*#__PURE__*/_jsx(\"div\", {\n    className: \"\".concat(baseClassName, \"-content \").concat(hashId).trim(),\n    children: /*#__PURE__*/_jsx(\"ul\", {\n      className: \"\".concat(baseClassName, \"-content-list \").concat(hashId).trim(),\n      children: appList === null || appList === void 0 ? void 0 : appList.map(function (app, index) {\n        var _app$children;\n        if (app !== null && app !== void 0 && (_app$children = app.children) !== null && _app$children !== void 0 && _app$children.length) {\n          return /*#__PURE__*/_jsxs(\"div\", {\n            className: \"\".concat(baseClassName, \"-content-list-item-group \").concat(hashId).trim(),\n            children: [/*#__PURE__*/_jsx(\"div\", {\n              className: \"\".concat(baseClassName, \"-content-list-item-group-title \").concat(hashId).trim(),\n              children: app.title\n            }), /*#__PURE__*/_jsx(SimpleContent, {\n              hashId: hashId,\n              itemClick: itemClick,\n              appList: app === null || app === void 0 ? void 0 : app.children,\n              baseClassName: baseClassName\n            })]\n          }, index);\n        }\n        return /*#__PURE__*/_jsx(\"li\", {\n          className: \"\".concat(baseClassName, \"-content-list-item \").concat(hashId).trim(),\n          onClick: function onClick(e) {\n            e.stopPropagation();\n            itemClick === null || itemClick === void 0 || itemClick(app);\n          },\n          children: /*#__PURE__*/_jsxs(\"a\", {\n            href: itemClick ? 'javascript:;' : app.url,\n            target: app.target,\n            rel: \"noreferrer\",\n            children: [renderLogo(app.icon, app.title), /*#__PURE__*/_jsx(\"div\", {\n              children: /*#__PURE__*/_jsx(\"div\", {\n                children: app.title\n              })\n            })]\n          })\n        }, index);\n      })\n    })\n  });\n};", "import { resetComponent } from '@ant-design/pro-utils';\nvar genAppsLogoComponentsDefaultListStyle = function genAppsLogoComponentsDefaultListStyle(token) {\n  return {\n    '&-content': {\n      maxHeight: 'calc(100vh - 48px)',\n      overflow: 'auto',\n      '&-list': {\n        boxSizing: 'content-box',\n        maxWidth: 656,\n        marginBlock: 0,\n        marginInline: 0,\n        paddingBlock: 0,\n        paddingInline: 0,\n        listStyle: 'none',\n        '&-item': {\n          position: 'relative',\n          display: 'inline-block',\n          width: 328,\n          height: 72,\n          paddingInline: 16,\n          paddingBlock: 16,\n          verticalAlign: 'top',\n          listStyleType: 'none',\n          transition: 'transform 0.2s cubic-bezier(0.333, 0, 0, 1)',\n          borderRadius: token.borderRadius,\n          '&-group': {\n            marginBottom: 16,\n            '&-title': {\n              margin: '16px 0 8px 12px',\n              fontWeight: 600,\n              color: 'rgba(0, 0, 0, 0.88)',\n              fontSize: 16,\n              opacity: 0.85,\n              lineHeight: 1.5,\n              '&:first-child': {\n                marginTop: 12\n              }\n            }\n          },\n          '&:hover': {\n            backgroundColor: token.colorBgTextHover\n          },\n          '* div': resetComponent === null || resetComponent === void 0 ? void 0 : resetComponent(token),\n          a: {\n            display: 'flex',\n            height: '100%',\n            fontSize: 12,\n            textDecoration: 'none',\n            '& > img': {\n              width: 40,\n              height: 40\n            },\n            '& > div': {\n              marginInlineStart: 14,\n              color: token.colorTextHeading,\n              fontSize: 14,\n              lineHeight: '22px',\n              whiteSpace: 'nowrap',\n              textOverflow: 'ellipsis'\n            },\n            '& > div > span': {\n              color: token.colorTextSecondary,\n              fontSize: 12,\n              lineHeight: '20px'\n            }\n          }\n        }\n      }\n    }\n  };\n};\nexport { genAppsLogoComponentsDefaultListStyle };", "var genAppsLogoComponentsSimpleListStyle = function genAppsLogoComponentsSimpleListStyle(token) {\n  return {\n    '&-content': {\n      maxHeight: 'calc(100vh - 48px)',\n      overflow: 'auto',\n      '&-list': {\n        boxSizing: 'border-box',\n        maxWidth: 376,\n        marginBlock: 0,\n        marginInline: 0,\n        paddingBlock: 0,\n        paddingInline: 0,\n        listStyle: 'none',\n        '&-item': {\n          position: 'relative',\n          display: 'inline-block',\n          width: 104,\n          height: 104,\n          marginBlock: 8,\n          marginInline: 8,\n          paddingInline: 24,\n          paddingBlock: 24,\n          verticalAlign: 'top',\n          listStyleType: 'none',\n          transition: 'transform 0.2s cubic-bezier(0.333, 0, 0, 1)',\n          borderRadius: token.borderRadius,\n          '&-group': {\n            marginBottom: 16,\n            '&-title': {\n              margin: '16px 0 8px 12px',\n              fontWeight: 600,\n              color: 'rgba(0, 0, 0, 0.88)',\n              fontSize: 16,\n              opacity: 0.85,\n              lineHeight: 1.5,\n              '&:first-child': {\n                marginTop: 12\n              }\n            }\n          },\n          '&:hover': {\n            backgroundColor: token.colorBgTextHover\n          },\n          a: {\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            height: '100%',\n            fontSize: 12,\n            textDecoration: 'none',\n            '& > #avatarLogo': {\n              width: 40,\n              height: 40,\n              margin: '0 auto',\n              color: token.colorPrimary,\n              fontSize: 22,\n              lineHeight: '40px',\n              textAlign: 'center',\n              backgroundImage: 'linear-gradient(180deg, #E8F0FB 0%, #F6F8FC 100%)',\n              borderRadius: token.borderRadius\n            },\n            '& > img': {\n              width: 40,\n              height: 40\n            },\n            '& > div': {\n              marginBlockStart: 5,\n              marginInlineStart: 0,\n              color: token.colorTextHeading,\n              fontSize: 14,\n              lineHeight: '22px',\n              whiteSpace: 'nowrap',\n              textOverflow: 'ellipsis'\n            },\n            '& > div > span': {\n              color: token.colorTextSecondary,\n              fontSize: 12,\n              lineHeight: '20px'\n            }\n          }\n        }\n      }\n    }\n  };\n};\nexport { genAppsLogoComponentsSimpleListStyle };", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nimport { genAppsLogoComponentsDefaultListStyle } from \"./default\";\nimport { genAppsLogoComponentsSimpleListStyle } from \"./simple\";\nvar genAppsLogoComponentsStyle = function genAppsLogoComponentsStyle(token) {\n  var _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5;\n  return _defineProperty({}, token.componentCls, {\n    '&-icon': {\n      display: 'inline-flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      paddingInline: 4,\n      paddingBlock: 0,\n      fontSize: 14,\n      lineHeight: '14px',\n      height: 28,\n      width: 28,\n      cursor: 'pointer',\n      color: (_token$layout = token.layout) === null || _token$layout === void 0 ? void 0 : _token$layout.colorTextAppListIcon,\n      borderRadius: token.borderRadius,\n      '&:hover': {\n        color: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorTextAppListIconHover,\n        backgroundColor: (_token$layout3 = token.layout) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorBgAppListIconHover\n      },\n      '&-active': {\n        color: (_token$layout4 = token.layout) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorTextAppListIconHover,\n        backgroundColor: (_token$layout5 = token.layout) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorBgAppListIconHover\n      }\n    },\n    '&-item-title': {\n      marginInlineStart: '16px',\n      marginInlineEnd: '8px',\n      marginBlockStart: 0,\n      marginBlockEnd: '12px',\n      fontWeight: 600,\n      color: 'rgba(0, 0, 0, 0.88)',\n      fontSize: 16,\n      opacity: 0.85,\n      lineHeight: 1.5,\n      '&:first-child': {\n        marginBlockStart: 12\n      }\n    },\n    '&-popover': _defineProperty({}, \"\".concat(token.antCls, \"-popover-arrow\"), {\n      display: 'none'\n    }),\n    '&-simple': genAppsLogoComponentsSimpleListStyle(token),\n    '&-default': genAppsLogoComponentsDefaultListStyle(token)\n  });\n};\nexport function useStyle(prefixCls) {\n  return useAntdStyle('AppsLogoComponents', function (token) {\n    var proCardToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genAppsLogoComponentsStyle(proCardToken)];\n  });\n}", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { openVisibleCompatible } from '@ant-design/pro-utils';\nimport { Popover } from 'antd';\nimport classNames from 'classnames';\nimport React, { useMemo, useState } from 'react';\nimport { AppsLogo } from \"./AppsLogo\";\nimport { DefaultContent } from \"./DefaultContent\";\nimport { SimpleContent } from \"./SimpleContent\";\nimport { useStyle } from \"./style/index\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * 默认渲染logo的方式，如果是个string，用img。否则直接返回\n *\n * @param logo\n * @returns\n */\nexport var defaultRenderLogo = function defaultRenderLogo(logo) {\n  if (typeof logo === 'string') {\n    return /*#__PURE__*/_jsx(\"img\", {\n      width: \"auto\",\n      height: 22,\n      src: logo,\n      alt: \"logo\"\n    });\n  }\n  if (typeof logo === 'function') {\n    return logo();\n  }\n  return logo;\n};\n\n/**\n * 相关品牌额icon 列表。用于展示相关的品牌\n *\n * @param props\n * @returns\n */\nexport var AppsLogoComponents = function AppsLogoComponents(props) {\n  var _props$appList;\n  var appList = props.appList,\n    appListRender = props.appListRender,\n    _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'ant-pro' : _props$prefixCls,\n    itemClick = props.onItemClick;\n  var ref = React.useRef(null);\n  var popoverRef = React.useRef(null);\n  var baseClassName = \"\".concat(prefixCls, \"-layout-apps\");\n  var _useStyle = useStyle(baseClassName),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    open = _useState2[0],\n    setOpen = _useState2[1];\n  var cloneItemClick = function cloneItemClick(app) {\n    itemClick === null || itemClick === void 0 || itemClick(app, popoverRef);\n  };\n  var defaultDomContent = useMemo(function () {\n    var isSimple = appList === null || appList === void 0 ? void 0 : appList.some(function (app) {\n      return !(app !== null && app !== void 0 && app.desc);\n    });\n    if (isSimple) {\n      return /*#__PURE__*/_jsx(SimpleContent, {\n        hashId: hashId,\n        appList: appList,\n        itemClick: itemClick ? cloneItemClick : undefined,\n        baseClassName: \"\".concat(baseClassName, \"-simple\")\n      });\n    }\n    return /*#__PURE__*/_jsx(DefaultContent, {\n      hashId: hashId,\n      appList: appList,\n      itemClick: itemClick ? cloneItemClick : undefined,\n      baseClassName: \"\".concat(baseClassName, \"-default\")\n    });\n  }, [appList, baseClassName, hashId]);\n  if (!(props !== null && props !== void 0 && (_props$appList = props.appList) !== null && _props$appList !== void 0 && _props$appList.length)) return null;\n  var popoverContent = appListRender ? appListRender(props === null || props === void 0 ? void 0 : props.appList, defaultDomContent) : defaultDomContent;\n  var popoverOpenProps = openVisibleCompatible(undefined, function (openChange) {\n    return setOpen(openChange);\n  });\n  return wrapSSR( /*#__PURE__*/_jsxs(_Fragment, {\n    children: [/*#__PURE__*/_jsx(\"div\", {\n      ref: ref,\n      onClick: function onClick(e) {\n        e.stopPropagation();\n        e.preventDefault();\n      }\n    }), /*#__PURE__*/_jsx(Popover, _objectSpread(_objectSpread({\n      placement: \"bottomRight\",\n      trigger: ['click'],\n      zIndex: 9999,\n      arrow: false\n    }, popoverOpenProps), {}, {\n      overlayClassName: \"\".concat(baseClassName, \"-popover \").concat(hashId).trim(),\n      content: popoverContent,\n      getPopupContainer: function getPopupContainer() {\n        return ref.current || document.body;\n      },\n      children: /*#__PURE__*/_jsx(\"span\", {\n        ref: popoverRef,\n        onClick: function onClick(e) {\n          e.stopPropagation();\n        },\n        className: classNames(\"\".concat(baseClassName, \"-icon\"), hashId, _defineProperty({}, \"\".concat(baseClassName, \"-icon-active\"), open)),\n        children: /*#__PURE__*/_jsx(AppsLogo, {})\n      })\n    }))]\n  }));\n};", "import { jsx as _jsx } from \"react/jsx-runtime\";\nfunction ArrowSvgIcon() {\n  return /*#__PURE__*/_jsx(\"svg\", {\n    width: \"1em\",\n    height: \"1em\",\n    viewBox: \"0 0 12 12\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    children: /*#__PURE__*/_jsx(\"path\", {\n      d: \"M6.432 7.967a.448.448 0 01-.318.133h-.228a.46.46 0 01-.318-.133L2.488 4.85a.305.305 0 010-.43l.427-.43a.293.293 0 01.42 0L6 6.687l2.665-2.699a.299.299 0 01.426 0l.42.431a.305.305 0 010 .43L6.432 7.967z\"\n    })\n  });\n}\nexport { ArrowSvgIcon };", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nvar genSiderMenuStyle = function genSiderMenuStyle(token) {\n  var _token$layout, _token$layout2, _token$layout3;\n  return _defineProperty({}, token.componentCls, {\n    position: 'absolute',\n    insetBlockStart: '18px',\n    zIndex: '101',\n    width: '24px',\n    height: '24px',\n    fontSize: ['14px', '16px'],\n    textAlign: 'center',\n    borderRadius: '40px',\n    insetInlineEnd: '-13px',\n    transition: 'transform 0.3s',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    color: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.sider) === null || _token$layout === void 0 ? void 0 : _token$layout.colorTextCollapsedButton,\n    backgroundColor: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.sider) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorBgCollapsedButton,\n    boxShadow: '0 2px 8px -2px rgba(0,0,0,0.05), 0 1px 4px -1px rgba(25,15,15,0.07), 0 0 1px 0 rgba(0,0,0,0.08)',\n    '&:hover': {\n      color: (_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.sider) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorTextCollapsedButtonHover,\n      boxShadow: '0 4px 16px -4px rgba(0,0,0,0.05), 0 2px 8px -2px rgba(25,15,15,0.07), 0 1px 2px 0 rgba(0,0,0,0.08)'\n    },\n    '.anticon': {\n      fontSize: '14px'\n    },\n    '& > svg': {\n      transition: 'transform  0.3s',\n      transform: 'rotate(90deg)'\n    },\n    '&-collapsed': {\n      '& > svg': {\n        transform: 'rotate(-90deg)'\n      }\n    }\n  });\n};\nexport function useStyle(prefixCls) {\n  return useAntdStyle('SiderMenuCollapsedIcon', function (token) {\n    var siderMenuToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genSiderMenuStyle(siderMenuToken)];\n  });\n}", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"isMobile\", \"collapsed\"];\nimport classNames from 'classnames';\nimport { ArrowSvgIcon } from \"../SiderMenu/Arrow\";\nimport { useStyle } from \"./style\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var CollapsedIcon = function CollapsedIcon(props) {\n  var isMobile = props.isMobile,\n    collapsed = props.collapsed,\n    rest = _objectWithoutProperties(props, _excluded);\n  var _useStyle = useStyle(props.className),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  if (isMobile && collapsed) return null;\n  return wrapSSR( /*#__PURE__*/_jsx(\"div\", _objectSpread(_objectSpread({}, rest), {}, {\n    className: classNames(props.className, hashId, _defineProperty(_defineProperty({}, \"\".concat(props.className, \"-collapsed\"), collapsed), \"\".concat(props.className, \"-is-mobile\"), isMobile)),\n    children: /*#__PURE__*/_jsx(ArrowSvgIcon, {})\n  })));\n};", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"className\", \"component\", \"viewBox\", \"spin\", \"rotate\", \"tabIndex\", \"onClick\", \"children\"];\n// Seems this is used for iconFont\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport Context from \"./Context\";\nimport { svgBaseProps, warning, useInsertStyles } from \"../utils\";\nvar Icon = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var className = props.className,\n    Component = props.component,\n    viewBox = props.viewBox,\n    spin = props.spin,\n    rotate = props.rotate,\n    tabIndex = props.tabIndex,\n    onClick = props.onClick,\n    children = props.children,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var iconRef = React.useRef();\n  var mergedRef = useComposeRef(iconRef, ref);\n  warning(Boolean(Component || children), 'Should have `component` prop or `children`.');\n  useInsertStyles(iconRef);\n  var _React$useContext = React.useContext(Context),\n    _React$useContext$pre = _React$useContext.prefixCls,\n    prefixCls = _React$useContext$pre === void 0 ? 'anticon' : _React$useContext$pre,\n    rootClassName = _React$useContext.rootClassName;\n  var classString = classNames(rootClassName, prefixCls, _defineProperty({}, \"\".concat(prefixCls, \"-spin\"), !!spin && !!Component), className);\n  var svgClassString = classNames(_defineProperty({}, \"\".concat(prefixCls, \"-spin\"), !!spin));\n  var svgStyle = rotate ? {\n    msTransform: \"rotate(\".concat(rotate, \"deg)\"),\n    transform: \"rotate(\".concat(rotate, \"deg)\")\n  } : undefined;\n  var innerSvgProps = _objectSpread(_objectSpread({}, svgBaseProps), {}, {\n    className: svgClassString,\n    style: svgStyle,\n    viewBox: viewBox\n  });\n  if (!viewBox) {\n    delete innerSvgProps.viewBox;\n  }\n\n  // component > children\n  var renderInnerNode = function renderInnerNode() {\n    if (Component) {\n      return /*#__PURE__*/React.createElement(Component, innerSvgProps, children);\n    }\n    if (children) {\n      warning(Boolean(viewBox) || React.Children.count(children) === 1 && /*#__PURE__*/React.isValidElement(children) && React.Children.only(children).type === 'use', 'Make sure that you provide correct `viewBox`' + ' prop (default `0 0 1024 1024`) to the icon.');\n      return /*#__PURE__*/React.createElement(\"svg\", _extends({}, innerSvgProps, {\n        viewBox: viewBox\n      }), children);\n    }\n    return null;\n  };\n  var iconTabIndex = tabIndex;\n  if (iconTabIndex === undefined && onClick) {\n    iconTabIndex = -1;\n  }\n  return /*#__PURE__*/React.createElement(\"span\", _extends({\n    role: \"img\"\n  }, restProps, {\n    ref: mergedRef,\n    tabIndex: iconTabIndex,\n    onClick: onClick,\n    className: classString\n  }), renderInnerNode());\n});\nIcon.displayName = 'AntdIcon';\nexport default Icon;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"type\", \"children\"];\nimport * as React from 'react';\nimport Icon from \"./Icon\";\nvar customCache = new Set();\nfunction isValidCustomScriptUrl(scriptUrl) {\n  return Boolean(typeof scriptUrl === 'string' && scriptUrl.length && !customCache.has(scriptUrl));\n}\nfunction createScriptUrlElements(scriptUrls) {\n  var index = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var currentScriptUrl = scriptUrls[index];\n  if (isValidCustomScriptUrl(currentScriptUrl)) {\n    var script = document.createElement('script');\n    script.setAttribute('src', currentScriptUrl);\n    script.setAttribute('data-namespace', currentScriptUrl);\n    if (scriptUrls.length > index + 1) {\n      script.onload = function () {\n        createScriptUrlElements(scriptUrls, index + 1);\n      };\n      script.onerror = function () {\n        createScriptUrlElements(scriptUrls, index + 1);\n      };\n    }\n    customCache.add(currentScriptUrl);\n    document.body.appendChild(script);\n  }\n}\nexport default function create() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var scriptUrl = options.scriptUrl,\n    _options$extraCommonP = options.extraCommonProps,\n    extraCommonProps = _options$extraCommonP === void 0 ? {} : _options$extraCommonP;\n\n  /**\n   * DOM API required.\n   * Make sure in browser environment.\n   * The Custom Icon will create a <script/>\n   * that loads SVG symbols and insert the SVG Element into the document body.\n   */\n  if (scriptUrl && typeof document !== 'undefined' && typeof window !== 'undefined' && typeof document.createElement === 'function') {\n    if (Array.isArray(scriptUrl)) {\n      // 因为iconfont资源会把svg插入before，所以前加载相同type会覆盖后加载，为了数组覆盖顺序，倒叙插入\n      createScriptUrlElements(scriptUrl.reverse());\n    } else {\n      createScriptUrlElements([scriptUrl]);\n    }\n  }\n  var Iconfont = /*#__PURE__*/React.forwardRef(function (props, ref) {\n    var type = props.type,\n      children = props.children,\n      restProps = _objectWithoutProperties(props, _excluded);\n\n    // children > type\n    var content = null;\n    if (props.type) {\n      content = /*#__PURE__*/React.createElement(\"use\", {\n        xlinkHref: \"#\".concat(type)\n      });\n    }\n    if (children) {\n      content = children;\n    }\n    return /*#__PURE__*/React.createElement(Icon, _extends({}, extraCommonProps, restProps, {\n      ref: ref\n    }), content);\n  });\n  Iconfont.displayName = 'Iconfont';\n  return Iconfont;\n}", "/** 判断是否是图片链接 */\nexport function isImg(path) {\n  return /\\w.(png|jpg|jpeg|svg|webp|gif|bmp)$/i.test(path);\n}", "var defaultSettings = {\n  navTheme: 'light',\n  layout: 'side',\n  contentWidth: 'Fluid',\n  fixedHeader: false,\n  fixSiderbar: true,\n  iconfontUrl: '',\n  colorPrimary: '#1677FF',\n  splitMenus: false\n};\nexport { defaultSettings };", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nvar genProLayoutBaseMenuStyle = function genProLayoutBaseMenuStyle(token, mode) {\n  var _token$layout, _token$layout2;\n  var menuToken = mode.includes('horizontal') ? (_token$layout = token.layout) === null || _token$layout === void 0 ? void 0 : _token$layout.header : (_token$layout2 = token.layout) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.sider;\n  return _objectSpread(_objectSpread(_defineProperty({}, \"\".concat(token.componentCls), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n    background: 'transparent',\n    color: menuToken === null || menuToken === void 0 ? void 0 : menuToken.colorTextMenu,\n    border: 'none'\n  }, \"\".concat(token.componentCls, \"-menu-item\"), {\n    transition: 'none !important'\n  }), \"\".concat(token.componentCls, \"-submenu-has-icon\"), _defineProperty({}, \"> \".concat(token.antCls, \"-menu-sub\"), {\n    paddingInlineStart: 10\n  })), \"\".concat(token.antCls, \"-menu-title-content\"), {\n    width: '100%',\n    height: '100%',\n    display: 'inline-flex'\n  }), \"\".concat(token.antCls, \"-menu-title-content\"), {\n    '&:first-child': {\n      width: '100%'\n    }\n  }), \"\".concat(token.componentCls, \"-item-icon\"), {\n    display: 'flex',\n    alignItems: 'center'\n  }), \"&&-collapsed\", _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-menu-item, \\n        \").concat(token.antCls, \"-menu-item-group > \").concat(token.antCls, \"-menu-item-group-list > \").concat(token.antCls, \"-menu-item, \\n        \").concat(token.antCls, \"-menu-item-group > \").concat(token.antCls, \"-menu-item-group-list > \").concat(token.antCls, \"-menu-submenu > \").concat(token.antCls, \"-menu-submenu-title, \\n        \").concat(token.antCls, \"-menu-submenu > \").concat(token.antCls, \"-menu-submenu-title\"), {\n    paddingInline: '0 !important',\n    marginBlock: '4px !important'\n  }), \"\".concat(token.antCls, \"-menu-item-group > \").concat(token.antCls, \"-menu-item-group-list > \").concat(token.antCls, \"-menu-submenu-selected > \").concat(token.antCls, \"-menu-submenu-title, \\n        \").concat(token.antCls, \"-menu-submenu-selected > \").concat(token.antCls, \"-menu-submenu-title\"), {\n    backgroundColor: menuToken === null || menuToken === void 0 ? void 0 : menuToken.colorBgMenuItemSelected,\n    borderRadius: token.borderRadiusLG\n  }), \"\".concat(token.componentCls, \"-group\"), _defineProperty({}, \"\".concat(token.antCls, \"-menu-item-group-title\"), {\n    paddingInline: 0\n  }))), '&-item-title', _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n    display: 'flex',\n    flexDirection: 'row',\n    alignItems: 'center',\n    gap: token.marginXS\n  }, \"\".concat(token.componentCls, \"-item-text\"), {\n    maxWidth: '100%',\n    textOverflow: 'ellipsis',\n    overflow: 'hidden',\n    wordBreak: 'break-all',\n    whiteSpace: 'nowrap'\n  }), '&-collapsed', _defineProperty(_defineProperty({\n    minWidth: 40,\n    height: 40\n  }, \"\".concat(token.componentCls, \"-item-icon\"), {\n    height: '16px',\n    width: '16px',\n    lineHeight: '16px !important',\n    '.anticon': {\n      lineHeight: '16px !important',\n      height: '16px'\n    }\n  }), \"\".concat(token.componentCls, \"-item-text-has-icon\"), {\n    display: 'none !important'\n  })), '&-collapsed-level-0', {\n    flexDirection: 'column',\n    justifyContent: 'center'\n  }), \"&\".concat(token.componentCls, \"-group-item-title\"), {\n    gap: token.marginXS,\n    height: 18,\n    overflow: 'hidden'\n  }), \"&\".concat(token.componentCls, \"-item-collapsed-show-title\"), _defineProperty({\n    lineHeight: '16px',\n    gap: 0\n  }, \"&\".concat(token.componentCls, \"-item-title-collapsed\"), _defineProperty(_defineProperty({\n    display: 'flex'\n  }, \"\".concat(token.componentCls, \"-item-icon\"), {\n    height: '16px',\n    width: '16px',\n    lineHeight: '16px !important',\n    '.anticon': {\n      lineHeight: '16px!important',\n      height: '16px'\n    }\n  }), \"\".concat(token.componentCls, \"-item-text\"), {\n    opacity: '1 !important',\n    display: 'inline !important',\n    textAlign: 'center',\n    fontSize: 12,\n    height: 12,\n    lineHeight: '12px',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    whiteSpace: 'nowrap',\n    width: '100%',\n    margin: 0,\n    padding: 0,\n    marginBlockStart: 4\n  })))), '&-group', _defineProperty({}, \"\".concat(token.antCls, \"-menu-item-group-title\"), {\n    fontSize: 12,\n    color: token.colorTextLabel,\n    '.anticon': {\n      marginInlineEnd: 8\n    }\n  })), '&-group-divider', {\n    color: token.colorTextSecondary,\n    fontSize: 12,\n    lineHeight: 20\n  })), mode.includes('horizontal') ? {} : _defineProperty({}, \"\".concat(token.antCls, \"-menu-submenu\").concat(token.antCls, \"-menu-submenu-popup\"), _defineProperty({}, \"\".concat(token.componentCls, \"-item-title\"), {\n    alignItems: 'flex-start'\n  }))), {}, _defineProperty({}, \"\".concat(token.antCls, \"-menu-submenu-popup\"), {\n    backgroundColor: 'rgba(255, 255, 255, 0.42)',\n    '-webkit-backdrop-filter': 'blur(8px)',\n    backdropFilter: 'blur(8px)'\n  }));\n};\nexport function useStyle(prefixCls, mode) {\n  return useAntdStyle('ProLayoutBaseMenu' + mode, function (token) {\n    var proLayoutMenuToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genProLayoutBaseMenuStyle(proLayoutMenuToken, mode || 'inline')];\n  });\n}", "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { createFromIconfontCN } from '@ant-design/icons';\nimport { ProProvider } from '@ant-design/pro-provider';\nimport { isImg, isUrl, useMountMergeState } from '@ant-design/pro-utils';\nimport { Menu, Skeleton, Tooltip } from 'antd';\nimport classNames from 'classnames';\nimport React, { useContext, useEffect, useMemo, useRef, useState } from 'react';\nimport { defaultSettings } from \"../../defaultSettings\";\nimport { getOpenKeysFromMenuData } from \"../../utils/utils\";\nimport { useStyle } from \"./style/menu\";\n\n// todo\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { createElement as _createElement } from \"react\";\nvar MenuItemTooltip = function MenuItemTooltip(props) {\n  var _useState = useState(props.collapsed),\n    _useState2 = _slicedToArray(_useState, 2),\n    collapsed = _useState2[0],\n    setCollapsed = _useState2[1];\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    open = _useState4[0],\n    setOpen = _useState4[1];\n  useEffect(function () {\n    setOpen(false);\n    setTimeout(function () {\n      setCollapsed(props.collapsed);\n    }, 400);\n  }, [props.collapsed]);\n  if (props.disable) {\n    return props.children;\n  }\n  return /*#__PURE__*/_jsx(Tooltip, {\n    title: props.title,\n    open: collapsed && props.collapsed ? open : false,\n    placement: \"right\",\n    onOpenChange: setOpen,\n    children: props.children\n  });\n};\nvar IconFont = createFromIconfontCN({\n  scriptUrl: defaultSettings.iconfontUrl\n});\n\n// Allow menu.js config icon as string or ReactNode\n//   icon: 'setting',\n//   icon: 'icon-geren' #For Iconfont ,\n//   icon: 'http://demo.com/icon.png',\n//   icon: '/favicon.png',\n//   icon: <Icon type=\"setting\" />,\nvar getIcon = function getIcon(icon) {\n  var iconPrefixes = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'icon-';\n  var className = arguments.length > 2 ? arguments[2] : undefined;\n  if (typeof icon === 'string' && icon !== '') {\n    if (isUrl(icon) || isImg(icon)) {\n      return /*#__PURE__*/_jsx(\"img\", {\n        width: 16,\n        src: icon,\n        alt: \"icon\",\n        className: className\n      }, icon);\n    }\n    if (icon.startsWith(iconPrefixes)) {\n      return /*#__PURE__*/_jsx(IconFont, {\n        type: icon\n      });\n    }\n  }\n  return icon;\n};\nvar getMenuTitleSymbol = function getMenuTitleSymbol(title) {\n  if (title && typeof title === 'string') {\n    var symbol = title.substring(0, 1).toUpperCase();\n    return symbol;\n  }\n  return null;\n};\nvar MenuUtil = /*#__PURE__*/_createClass(function MenuUtil(props) {\n  var _this = this;\n  _classCallCheck(this, MenuUtil);\n  _defineProperty(this, \"props\", void 0);\n  _defineProperty(this, \"getNavMenuItems\", function () {\n    var menusData = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    var level = arguments.length > 1 ? arguments[1] : undefined;\n    var noGroupLevel = arguments.length > 2 ? arguments[2] : undefined;\n    return menusData.map(function (item) {\n      return _this.getSubMenuOrItem(item, level, noGroupLevel);\n    }).filter(function (item) {\n      return item;\n    }).flat(1);\n  });\n  /** Get SubMenu or Item */\n  _defineProperty(this, \"getSubMenuOrItem\", function (item, level, noGroupLevel) {\n    var _this$props = _this.props,\n      subMenuItemRender = _this$props.subMenuItemRender,\n      baseClassName = _this$props.baseClassName,\n      prefixCls = _this$props.prefixCls,\n      collapsed = _this$props.collapsed,\n      menu = _this$props.menu,\n      iconPrefixes = _this$props.iconPrefixes,\n      layout = _this$props.layout;\n    var isGroup = (menu === null || menu === void 0 ? void 0 : menu.type) === 'group' && layout !== 'top';\n    var designToken = _this.props.token;\n    var name = _this.getIntlName(item);\n    var children = (item === null || item === void 0 ? void 0 : item.children) || (item === null || item === void 0 ? void 0 : item.routes);\n    var menuType = isGroup && level === 0 ? 'group' : undefined;\n    if (Array.isArray(children) && children.length > 0) {\n      var _this$props2, _this$props3, _this$props4, _this$props5, _designToken$layout;\n      /** Menu 第一级可以有icon，或者 isGroup 时第二级别也要有 */\n      var shouldHasIcon = level === 0 || isGroup && level === 1;\n\n      //  get defaultTitle by menuItemRender\n      var iconDom = getIcon(item.icon, iconPrefixes, \"\".concat(baseClassName, \"-icon \").concat((_this$props2 = _this.props) === null || _this$props2 === void 0 ? void 0 : _this$props2.hashId));\n      /**\n       * 如果没有icon在收起的时候用首字母代替\n       */\n      var defaultIcon = collapsed && shouldHasIcon ? getMenuTitleSymbol(name) : null;\n      var defaultTitle = /*#__PURE__*/_jsxs(\"div\", {\n        className: classNames(\"\".concat(baseClassName, \"-item-title\"), (_this$props3 = _this.props) === null || _this$props3 === void 0 ? void 0 : _this$props3.hashId, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-item-title-collapsed\"), collapsed), \"\".concat(baseClassName, \"-item-title-collapsed-level-\").concat(noGroupLevel), collapsed), \"\".concat(baseClassName, \"-group-item-title\"), menuType === 'group'), \"\".concat(baseClassName, \"-item-collapsed-show-title\"), (menu === null || menu === void 0 ? void 0 : menu.collapsedShowTitle) && collapsed)),\n        children: [menuType === 'group' && collapsed ? null : shouldHasIcon && iconDom ? /*#__PURE__*/_jsx(\"span\", {\n          className: \"\".concat(baseClassName, \"-item-icon \").concat((_this$props4 = _this.props) === null || _this$props4 === void 0 ? void 0 : _this$props4.hashId).trim(),\n          children: iconDom\n        }) : defaultIcon, /*#__PURE__*/_jsx(\"span\", {\n          className: classNames(\"\".concat(baseClassName, \"-item-text\"), (_this$props5 = _this.props) === null || _this$props5 === void 0 ? void 0 : _this$props5.hashId, _defineProperty({}, \"\".concat(baseClassName, \"-item-text-has-icon\"), menuType !== 'group' && shouldHasIcon && (iconDom || defaultIcon))),\n          children: name\n        })]\n      });\n\n      // subMenu only title render\n      var title = subMenuItemRender ? subMenuItemRender(_objectSpread(_objectSpread({}, item), {}, {\n        isUrl: false\n      }), defaultTitle, _this.props) : defaultTitle;\n\n      // 如果收起来，没有子菜单了，就不需要展示 group，所以 level 不增加\n      if (isGroup && level === 0 && _this.props.collapsed && !menu.collapsedShowGroupTitle) {\n        return _this.getNavMenuItems(children, level + 1, level);\n      }\n      var childrenList = _this.getNavMenuItems(children, level + 1, isGroup && level === 0 && _this.props.collapsed ? level : level + 1);\n      return [{\n        type: menuType,\n        key: item.key || item.path,\n        label: title,\n        onClick: isGroup ? undefined : item.onTitleClick,\n        children: childrenList,\n        className: classNames(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-group\"), menuType === 'group'), \"\".concat(baseClassName, \"-submenu\"), menuType !== 'group'), \"\".concat(baseClassName, \"-submenu-has-icon\"), menuType !== 'group' && shouldHasIcon && iconDom))\n      }, isGroup && level === 0 ? {\n        type: 'divider',\n        prefixCls: prefixCls,\n        className: \"\".concat(baseClassName, \"-divider\"),\n        key: (item.key || item.path) + '-group-divider',\n        style: {\n          padding: 0,\n          borderBlockEnd: 0,\n          margin: _this.props.collapsed ? '4px' : '6px 16px',\n          marginBlockStart: _this.props.collapsed ? 4 : 8,\n          borderColor: designToken === null || designToken === void 0 || (_designToken$layout = designToken.layout) === null || _designToken$layout === void 0 || (_designToken$layout = _designToken$layout.sider) === null || _designToken$layout === void 0 ? void 0 : _designToken$layout.colorMenuItemDivider\n        }\n      } : undefined].filter(Boolean);\n    }\n    return {\n      className: \"\".concat(baseClassName, \"-menu-item\"),\n      disabled: item.disabled,\n      key: item.key || item.path,\n      onClick: item.onTitleClick,\n      // eslint-disable-next-line react/no-is-mounted\n      label: _this.getMenuItemPath(item, level, noGroupLevel)\n    };\n  });\n  _defineProperty(this, \"getIntlName\", function (item) {\n    var name = item.name,\n      locale = item.locale;\n    var _this$props6 = _this.props,\n      menu = _this$props6.menu,\n      formatMessage = _this$props6.formatMessage;\n    var finalName = name;\n    if (locale && (menu === null || menu === void 0 ? void 0 : menu.locale) !== false) {\n      finalName = formatMessage === null || formatMessage === void 0 ? void 0 : formatMessage({\n        id: locale,\n        defaultMessage: name\n      });\n    }\n    if (_this.props.menuTextRender) {\n      return _this.props.menuTextRender(item, finalName, _this.props);\n    }\n    return finalName;\n  });\n  /**\n   * 判断是否是http链接.返回 Link 或 a Judge whether it is http link.return a or Link\n   *\n   * @memberof SiderMenu\n   */\n  _defineProperty(this, \"getMenuItemPath\", function (item, level, noGroupLevel) {\n    var _this$props9, _this$props10, _this$props11, _this$props12;\n    var itemPath = _this.conversionPath(item.path || '/');\n    var _this$props7 = _this.props,\n      _this$props7$location = _this$props7.location,\n      location = _this$props7$location === void 0 ? {\n        pathname: '/'\n      } : _this$props7$location,\n      isMobile = _this$props7.isMobile,\n      onCollapse = _this$props7.onCollapse,\n      menuItemRender = _this$props7.menuItemRender,\n      iconPrefixes = _this$props7.iconPrefixes;\n\n    // if local is true formatMessage all name。\n    var menuItemTitle = _this.getIntlName(item);\n    var _this$props8 = _this.props,\n      baseClassName = _this$props8.baseClassName,\n      menu = _this$props8.menu,\n      collapsed = _this$props8.collapsed;\n    var isGroup = (menu === null || menu === void 0 ? void 0 : menu.type) === 'group';\n    /** Menu 第一级可以有icon，或者 isGroup 时第二级别也要有 */\n    var hasIcon = level === 0 || isGroup && level === 1;\n    var icon = !hasIcon ? null : getIcon(item.icon, iconPrefixes, \"\".concat(baseClassName, \"-icon \").concat((_this$props9 = _this.props) === null || _this$props9 === void 0 ? void 0 : _this$props9.hashId));\n\n    // 如果没有 icon 在收起的时候用首字母代替\n    var defaultIcon = collapsed && hasIcon ? getMenuTitleSymbol(menuItemTitle) : null;\n    var defaultItem = /*#__PURE__*/_jsxs(\"div\", {\n      className: classNames(\"\".concat(baseClassName, \"-item-title\"), (_this$props10 = _this.props) === null || _this$props10 === void 0 ? void 0 : _this$props10.hashId, _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-item-title-collapsed\"), collapsed), \"\".concat(baseClassName, \"-item-title-collapsed-level-\").concat(noGroupLevel), collapsed), \"\".concat(baseClassName, \"-item-collapsed-show-title\"), (menu === null || menu === void 0 ? void 0 : menu.collapsedShowTitle) && collapsed)),\n      children: [/*#__PURE__*/_jsx(\"span\", {\n        className: \"\".concat(baseClassName, \"-item-icon \").concat((_this$props11 = _this.props) === null || _this$props11 === void 0 ? void 0 : _this$props11.hashId).trim(),\n        style: {\n          display: defaultIcon === null && !icon ? 'none' : ''\n        },\n        children: icon || /*#__PURE__*/_jsx(\"span\", {\n          className: \"anticon\",\n          children: defaultIcon\n        })\n      }), /*#__PURE__*/_jsx(\"span\", {\n        className: classNames(\"\".concat(baseClassName, \"-item-text\"), (_this$props12 = _this.props) === null || _this$props12 === void 0 ? void 0 : _this$props12.hashId, _defineProperty({}, \"\".concat(baseClassName, \"-item-text-has-icon\"), hasIcon && (icon || defaultIcon))),\n        children: menuItemTitle\n      })]\n    }, itemPath);\n    var isHttpUrl = isUrl(itemPath);\n\n    // Is it a http link\n    if (isHttpUrl) {\n      var _this$props13, _this$props14, _this$props15;\n      defaultItem = /*#__PURE__*/_jsxs(\"span\", {\n        onClick: function onClick() {\n          var _window, _window$open;\n          (_window = window) === null || _window === void 0 || (_window$open = _window.open) === null || _window$open === void 0 || _window$open.call(_window, itemPath, '_blank');\n        },\n        className: classNames(\"\".concat(baseClassName, \"-item-title\"), (_this$props13 = _this.props) === null || _this$props13 === void 0 ? void 0 : _this$props13.hashId, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-item-title-collapsed\"), collapsed), \"\".concat(baseClassName, \"-item-title-collapsed-level-\").concat(noGroupLevel), collapsed), \"\".concat(baseClassName, \"-item-link\"), true), \"\".concat(baseClassName, \"-item-collapsed-show-title\"), (menu === null || menu === void 0 ? void 0 : menu.collapsedShowTitle) && collapsed)),\n        children: [/*#__PURE__*/_jsx(\"span\", {\n          className: \"\".concat(baseClassName, \"-item-icon \").concat((_this$props14 = _this.props) === null || _this$props14 === void 0 ? void 0 : _this$props14.hashId).trim(),\n          style: {\n            display: defaultIcon === null && !icon ? 'none' : ''\n          },\n          children: icon || /*#__PURE__*/_jsx(\"span\", {\n            className: \"anticon\",\n            children: defaultIcon\n          })\n        }), /*#__PURE__*/_jsx(\"span\", {\n          className: classNames(\"\".concat(baseClassName, \"-item-text\"), (_this$props15 = _this.props) === null || _this$props15 === void 0 ? void 0 : _this$props15.hashId, _defineProperty({}, \"\".concat(baseClassName, \"-item-text-has-icon\"), hasIcon && (icon || defaultIcon))),\n          children: menuItemTitle\n        })]\n      }, itemPath);\n    }\n    if (menuItemRender) {\n      var renderItemProps = _objectSpread(_objectSpread({}, item), {}, {\n        isUrl: isHttpUrl,\n        itemPath: itemPath,\n        isMobile: isMobile,\n        replace: itemPath === location.pathname,\n        onClick: function onClick() {\n          return onCollapse && onCollapse(true);\n        },\n        children: undefined\n      });\n      return level === 0 ? /*#__PURE__*/_jsx(MenuItemTooltip, {\n        collapsed: collapsed,\n        title: menuItemTitle,\n        disable: item.disabledTooltip,\n        children: menuItemRender(renderItemProps, defaultItem, _this.props)\n      }) : menuItemRender(renderItemProps, defaultItem, _this.props);\n    }\n    return level === 0 ? /*#__PURE__*/_jsx(MenuItemTooltip, {\n      collapsed: collapsed,\n      title: menuItemTitle,\n      disable: item.disabledTooltip,\n      children: defaultItem\n    }) : defaultItem;\n  });\n  _defineProperty(this, \"conversionPath\", function (path) {\n    if (path && path.indexOf('http') === 0) {\n      return path;\n    }\n    return \"/\".concat(path || '').replace(/\\/+/g, '/');\n  });\n  this.props = props;\n});\n/**\n * 生成openKeys 的对象，因为设置了openKeys 就会变成受控，所以需要一个空对象\n *\n * @param BaseMenuProps\n */\nvar getOpenKeysProps = function getOpenKeysProps(openKeys, _ref) {\n  var layout = _ref.layout,\n    collapsed = _ref.collapsed;\n  var openKeysProps = {};\n  if (openKeys && !collapsed && ['side', 'mix'].includes(layout || 'mix')) {\n    openKeysProps = {\n      openKeys: openKeys\n    };\n  }\n  return openKeysProps;\n};\nvar BaseMenu = function BaseMenu(props) {\n  var mode = props.mode,\n    className = props.className,\n    handleOpenChange = props.handleOpenChange,\n    style = props.style,\n    menuData = props.menuData,\n    prefixCls = props.prefixCls,\n    menu = props.menu,\n    matchMenuKeys = props.matchMenuKeys,\n    iconfontUrl = props.iconfontUrl,\n    propsSelectedKeys = props.selectedKeys,\n    onSelect = props.onSelect,\n    menuRenderType = props.menuRenderType,\n    propsOpenKeys = props.openKeys;\n  var _useContext = useContext(ProProvider),\n    dark = _useContext.dark,\n    designToken = _useContext.token;\n  var baseClassName = \"\".concat(prefixCls, \"-base-menu-\").concat(mode);\n  // 用于减少 defaultOpenKeys 计算的组件\n  var defaultOpenKeysRef = useRef([]);\n  var _useMountMergeState = useMountMergeState(menu === null || menu === void 0 ? void 0 : menu.defaultOpenAll),\n    _useMountMergeState2 = _slicedToArray(_useMountMergeState, 2),\n    defaultOpenAll = _useMountMergeState2[0],\n    setDefaultOpenAll = _useMountMergeState2[1];\n  var _useMountMergeState3 = useMountMergeState(function () {\n      if (menu !== null && menu !== void 0 && menu.defaultOpenAll) {\n        return getOpenKeysFromMenuData(menuData) || [];\n      }\n      if (propsOpenKeys === false) {\n        return false;\n      }\n      return [];\n    }, {\n      value: propsOpenKeys === false ? undefined : propsOpenKeys,\n      onChange: handleOpenChange\n    }),\n    _useMountMergeState4 = _slicedToArray(_useMountMergeState3, 2),\n    openKeys = _useMountMergeState4[0],\n    setOpenKeys = _useMountMergeState4[1];\n  var _useMountMergeState5 = useMountMergeState([], {\n      value: propsSelectedKeys,\n      onChange: onSelect ? function (keys) {\n        if (onSelect && keys) {\n          onSelect(keys);\n        }\n      } : undefined\n    }),\n    _useMountMergeState6 = _slicedToArray(_useMountMergeState5, 2),\n    selectedKeys = _useMountMergeState6[0],\n    setSelectedKeys = _useMountMergeState6[1];\n  useEffect(function () {\n    if (menu !== null && menu !== void 0 && menu.defaultOpenAll || propsOpenKeys === false) {\n      return;\n    }\n    if (matchMenuKeys) {\n      setOpenKeys(matchMenuKeys);\n      setSelectedKeys(matchMenuKeys);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [matchMenuKeys.join('-')]);\n  useEffect(function () {\n    // reset IconFont\n    if (iconfontUrl) {\n      IconFont = createFromIconfontCN({\n        scriptUrl: iconfontUrl\n      });\n    }\n  }, [iconfontUrl]);\n  useEffect(function () {\n    // if pathname can't match, use the nearest parent's key\n    if (matchMenuKeys.join('-') !== (selectedKeys || []).join('-')) {\n      setSelectedKeys(matchMenuKeys);\n    }\n    if (!defaultOpenAll && propsOpenKeys !== false && matchMenuKeys.join('-') !== (openKeys || []).join('-')) {\n      var newKeys = matchMenuKeys;\n      // 如果不自动关闭，我需要把 openKeys 放进去\n      if ((menu === null || menu === void 0 ? void 0 : menu.autoClose) === false) {\n        newKeys = Array.from(new Set([].concat(_toConsumableArray(matchMenuKeys), _toConsumableArray(openKeys || []))));\n      }\n      setOpenKeys(newKeys);\n    } else if (menu !== null && menu !== void 0 && menu.ignoreFlatMenu && defaultOpenAll) {\n      // 忽略用户手动折叠过的菜单状态，折叠按钮切换之后也可实现默认展开所有菜单\n      setOpenKeys(getOpenKeysFromMenuData(menuData));\n    } else {\n      setDefaultOpenAll(false);\n    }\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [matchMenuKeys.join('-')]);\n  var openKeysProps = useMemo(function () {\n    return getOpenKeysProps(openKeys, props);\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [openKeys && openKeys.join(','), props.layout, props.collapsed]);\n  var _useStyle = useStyle(baseClassName, mode),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  var menuUtils = useMemo(function () {\n    return new MenuUtil(_objectSpread(_objectSpread({}, props), {}, {\n      token: designToken,\n      menuRenderType: menuRenderType,\n      baseClassName: baseClassName,\n      hashId: hashId\n    }));\n  }, [props, designToken, menuRenderType, baseClassName, hashId]);\n  if (menu !== null && menu !== void 0 && menu.loading) {\n    return /*#__PURE__*/_jsx(\"div\", {\n      style: mode !== null && mode !== void 0 && mode.includes('inline') ? {\n        padding: 24\n      } : {\n        marginBlockStart: 16\n      },\n      children: /*#__PURE__*/_jsx(Skeleton, {\n        active: true,\n        title: false,\n        paragraph: {\n          rows: mode !== null && mode !== void 0 && mode.includes('inline') ? 6 : 1\n        }\n      })\n    });\n  }\n\n  // 这次 openKeys === false 的时候的情况，这种情况下帮用户选中一次\n  // 第二此不会使用，所以用了 defaultOpenKeys\n  // 这里返回 null，是为了让 defaultOpenKeys 生效\n  if (props.openKeys === false && !props.handleOpenChange) {\n    defaultOpenKeysRef.current = matchMenuKeys;\n  }\n  var finallyData = props.postMenuData ? props.postMenuData(menuData) : menuData;\n  if (finallyData && (finallyData === null || finallyData === void 0 ? void 0 : finallyData.length) < 1) {\n    return null;\n  }\n  return wrapSSR( /*#__PURE__*/_createElement(Menu, _objectSpread(_objectSpread({}, openKeysProps), {}, {\n    _internalDisableMenuItemTitleTooltip: true,\n    key: \"Menu\",\n    mode: mode,\n    inlineIndent: 16,\n    defaultOpenKeys: defaultOpenKeysRef.current,\n    theme: dark ? 'dark' : 'light',\n    selectedKeys: selectedKeys,\n    style: _objectSpread({\n      backgroundColor: 'transparent',\n      border: 'none'\n    }, style),\n    className: classNames(className, hashId, baseClassName, _defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-horizontal\"), mode === 'horizontal'), \"\".concat(baseClassName, \"-collapsed\"), props.collapsed)),\n    items: menuUtils.getNavMenuItems(finallyData, 0, 0),\n    onOpenChange: function onOpenChange(_openKeys) {\n      if (!props.collapsed) {\n        setOpenKeys(_openKeys);\n      }\n    }\n  }, props.menuProps)));\n};\nexport { BaseMenu };", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nexport function useStylish(prefixCls, _ref) {\n  var stylish = _ref.stylish,\n    proLayoutCollapsedWidth = _ref.proLayoutCollapsedWidth;\n  return useAntdStyle('ProLayoutSiderMenuStylish', function (token) {\n    var siderMenuToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls),\n      proLayoutCollapsedWidth: proLayoutCollapsedWidth\n    });\n    if (!stylish) return [];\n    return [_defineProperty({}, \"div\".concat(token.proComponentsCls, \"-layout\"), _defineProperty({}, \"\".concat(siderMenuToken.componentCls), stylish === null || stylish === void 0 ? void 0 : stylish(siderMenuToken)))];\n  });\n}", "import _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"title\", \"render\"];\nimport { ProProvider } from '@ant-design/pro-provider';\nimport { Avatar, Layout, Menu, Space, version } from 'antd';\nimport classNames from 'classnames';\nimport React, { useContext, useMemo } from 'react';\nimport { AppsLogoComponents, defaultRenderLogo } from \"../AppsLogoComponents\";\nimport { CollapsedIcon } from \"../CollapsedIcon\";\nimport { BaseMenu } from \"./BaseMenu\";\nimport { useStylish } from \"./style/stylish\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { createElement as _createElement } from \"react\";\nvar _SafetyWarningProvider = /*#__PURE__*/React.memo(function (props) {\n  if (process.env.NODE_ENV !== 'production') {\n    console.warn(\"[pro-layout] SiderMenu required antd@^4.24.15 || antd@^5.11.2 for access the menu context, please upgrade your antd version (current \".concat(version, \").\"));\n  }\n  return /*#__PURE__*/_jsx(_Fragment, {\n    children: props.children\n  });\n});\nvar Sider = Layout.Sider,\n  _Layout$_InternalSide = Layout._InternalSiderContext,\n  SiderContext = _Layout$_InternalSide === void 0 ? {\n    Provider: _SafetyWarningProvider\n  } : _Layout$_InternalSide;\n/**\n * 渲染 title 和 logo\n *\n * @param props\n * @param renderKey\n * @returns\n */\nexport var renderLogoAndTitle = function renderLogoAndTitle(props) {\n  var renderKey = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'menuHeaderRender';\n  var logo = props.logo,\n    title = props.title,\n    layout = props.layout;\n  var renderFunction = props[renderKey];\n  if (renderFunction === false) {\n    return null;\n  }\n  var logoDom = defaultRenderLogo(logo);\n  var titleDom = /*#__PURE__*/_jsx(\"h1\", {\n    children: title !== null && title !== void 0 ? title : 'Ant Design Pro'\n  });\n  if (renderFunction) {\n    // when collapsed, no render title\n    return renderFunction(logoDom, props.collapsed ? null : titleDom, props);\n  }\n\n  /**\n   * 收起来时候直接不显示\n   */\n  if (props.isMobile) {\n    return null;\n  }\n  if (layout === 'mix' && renderKey === 'menuHeaderRender') return false;\n  if (props.collapsed) {\n    return /*#__PURE__*/_jsx(\"a\", {\n      children: logoDom\n    }, \"title\");\n  }\n  return /*#__PURE__*/_jsxs(\"a\", {\n    children: [logoDom, titleDom]\n  }, \"title\");\n};\nvar SiderMenu = function SiderMenu(props) {\n  var _props$menu2;\n  var collapsed = props.collapsed,\n    originCollapsed = props.originCollapsed,\n    fixSiderbar = props.fixSiderbar,\n    menuFooterRender = props.menuFooterRender,\n    _onCollapse = props.onCollapse,\n    theme = props.theme,\n    siderWidth = props.siderWidth,\n    isMobile = props.isMobile,\n    onMenuHeaderClick = props.onMenuHeaderClick,\n    _props$breakpoint = props.breakpoint,\n    breakpoint = _props$breakpoint === void 0 ? 'lg' : _props$breakpoint,\n    style = props.style,\n    layout = props.layout,\n    _props$menuExtraRende = props.menuExtraRender,\n    menuExtraRender = _props$menuExtraRende === void 0 ? false : _props$menuExtraRende,\n    links = props.links,\n    menuContentRender = props.menuContentRender,\n    collapsedButtonRender = props.collapsedButtonRender,\n    prefixCls = props.prefixCls,\n    avatarProps = props.avatarProps,\n    rightContentRender = props.rightContentRender,\n    actionsRender = props.actionsRender,\n    onOpenChange = props.onOpenChange,\n    stylish = props.stylish,\n    logoStyle = props.logoStyle;\n  var _useContext = useContext(ProProvider),\n    hashId = _useContext.hashId;\n  var showSiderExtraDom = useMemo(function () {\n    if (isMobile) return false;\n    if (layout === 'mix') return false;\n    return true;\n  }, [isMobile, layout]);\n  var baseClassName = \"\".concat(prefixCls, \"-sider\");\n\n  // 收起的宽度\n  var collapsedWidth = 64;\n\n  // 之所以这样写是为了提升样式优先级，不然会被sider 自带的覆盖掉\n  var stylishClassName = useStylish(\"\".concat(baseClassName, \".\").concat(baseClassName, \"-stylish\"), {\n    stylish: stylish,\n    proLayoutCollapsedWidth: collapsedWidth\n  });\n  var siderClassName = classNames(\"\".concat(baseClassName), hashId, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-fixed\"), fixSiderbar), \"\".concat(baseClassName, \"-fixed-mix\"), layout === 'mix' && !isMobile && fixSiderbar), \"\".concat(baseClassName, \"-collapsed\"), props.collapsed), \"\".concat(baseClassName, \"-layout-\").concat(layout), layout && !isMobile), \"\".concat(baseClassName, \"-light\"), theme !== 'dark'), \"\".concat(baseClassName, \"-mix\"), layout === 'mix' && !isMobile), \"\".concat(baseClassName, \"-stylish\"), !!stylish));\n  var headerDom = renderLogoAndTitle(props);\n  var extraDom = menuExtraRender && menuExtraRender(props);\n  var menuDom = useMemo(function () {\n    return menuContentRender !== false && /*#__PURE__*/_createElement(BaseMenu, _objectSpread(_objectSpread({}, props), {}, {\n      key: \"base-menu\",\n      mode: collapsed && !isMobile ? 'vertical' : 'inline',\n      handleOpenChange: onOpenChange,\n      style: {\n        width: '100%'\n      },\n      className: \"\".concat(baseClassName, \"-menu \").concat(hashId).trim()\n    }));\n  }, [baseClassName, hashId, menuContentRender, onOpenChange, props]);\n  var linksMenuItems = (links || []).map(function (node, index) {\n    return {\n      className: \"\".concat(baseClassName, \"-link\"),\n      label: node,\n      key: index\n    };\n  });\n  var menuRenderDom = useMemo(function () {\n    return menuContentRender ? menuContentRender(props, menuDom) : menuDom;\n  }, [menuContentRender, menuDom, props]);\n  var avatarDom = useMemo(function () {\n    if (!avatarProps) return null;\n    var title = avatarProps.title,\n      render = avatarProps.render,\n      rest = _objectWithoutProperties(avatarProps, _excluded);\n    var dom = /*#__PURE__*/_jsxs(\"div\", {\n      className: \"\".concat(baseClassName, \"-actions-avatar\"),\n      children: [rest !== null && rest !== void 0 && rest.src || rest !== null && rest !== void 0 && rest.srcSet || rest.icon || rest.children ? /*#__PURE__*/_jsx(Avatar, _objectSpread({\n        size: 28\n      }, rest)) : null, avatarProps.title && !collapsed && /*#__PURE__*/_jsx(\"span\", {\n        children: title\n      })]\n    });\n    if (render) {\n      return render(avatarProps, dom, props);\n    }\n    return dom;\n  }, [avatarProps, baseClassName, collapsed]);\n  var actionsDom = useMemo(function () {\n    if (!actionsRender) return null;\n    return /*#__PURE__*/_jsx(Space, {\n      align: \"center\",\n      size: 4,\n      direction: collapsed ? 'vertical' : 'horizontal',\n      className: classNames([\"\".concat(baseClassName, \"-actions-list\"), collapsed && \"\".concat(baseClassName, \"-actions-list-collapsed\"), hashId]),\n      children: [actionsRender === null || actionsRender === void 0 ? void 0 : actionsRender(props)].flat(1).map(function (item, index) {\n        return /*#__PURE__*/_jsx(\"div\", {\n          className: \"\".concat(baseClassName, \"-actions-list-item \").concat(hashId).trim(),\n          children: item\n        }, index);\n      })\n    });\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [actionsRender, baseClassName, collapsed]);\n  var appsDom = useMemo(function () {\n    return /*#__PURE__*/_jsx(AppsLogoComponents, {\n      onItemClick: props.itemClick,\n      appListRender: props.appListRender,\n      appList: props.appList,\n      prefixCls: props.prefixCls\n    });\n  }, [props.appList, props.appListRender, props.prefixCls]);\n  var collapsedDom = useMemo(function () {\n    if (collapsedButtonRender === false) return null;\n    var dom = /*#__PURE__*/_jsx(CollapsedIcon, {\n      isMobile: isMobile,\n      collapsed: originCollapsed,\n      className: \"\".concat(baseClassName, \"-collapsed-button\"),\n      onClick: function onClick() {\n        _onCollapse === null || _onCollapse === void 0 || _onCollapse(!originCollapsed);\n      }\n    });\n    if (collapsedButtonRender) return collapsedButtonRender(collapsed, dom);\n    return dom;\n  }, [collapsedButtonRender, isMobile, originCollapsed, baseClassName, collapsed, _onCollapse]);\n\n  /** 操作区域的dom */\n  var actionAreaDom = useMemo(function () {\n    if (!avatarDom && !actionsDom) return null;\n    return /*#__PURE__*/_jsxs(\"div\", {\n      className: classNames(\"\".concat(baseClassName, \"-actions\"), hashId, collapsed && \"\".concat(baseClassName, \"-actions-collapsed\")),\n      children: [avatarDom, actionsDom]\n    });\n  }, [actionsDom, avatarDom, baseClassName, collapsed, hashId]);\n\n  /* Using the useMemo hook to create a CSS class that will hide the menu when the menu is collapsed. */\n  var hideMenuWhenCollapsedClassName = useMemo(function () {\n    var _props$menu;\n    // 收起时完全隐藏菜单\n    if (props !== null && props !== void 0 && (_props$menu = props.menu) !== null && _props$menu !== void 0 && _props$menu.hideMenuWhenCollapsed && collapsed) {\n      return \"\".concat(baseClassName, \"-hide-menu-collapsed\");\n    }\n    return null;\n  }, [baseClassName, collapsed, props === null || props === void 0 || (_props$menu2 = props.menu) === null || _props$menu2 === void 0 ? void 0 : _props$menu2.hideMenuWhenCollapsed]);\n  var menuFooterDom = menuFooterRender && (menuFooterRender === null || menuFooterRender === void 0 ? void 0 : menuFooterRender(props));\n  var menuDomItems = /*#__PURE__*/_jsxs(_Fragment, {\n    children: [headerDom && /*#__PURE__*/_jsxs(\"div\", {\n      className: classNames([classNames(\"\".concat(baseClassName, \"-logo\"), hashId, _defineProperty({}, \"\".concat(baseClassName, \"-logo-collapsed\"), collapsed))]),\n      onClick: showSiderExtraDom ? onMenuHeaderClick : undefined,\n      id: \"logo\",\n      style: logoStyle,\n      children: [headerDom, appsDom]\n    }), extraDom && /*#__PURE__*/_jsx(\"div\", {\n      className: classNames([\"\".concat(baseClassName, \"-extra\"), !headerDom && \"\".concat(baseClassName, \"-extra-no-logo\"), hashId]),\n      children: extraDom\n    }), /*#__PURE__*/_jsx(\"div\", {\n      style: {\n        flex: 1,\n        overflowY: 'auto',\n        overflowX: 'hidden'\n      },\n      children: menuRenderDom\n    }), /*#__PURE__*/_jsxs(SiderContext.Provider, {\n      value: {},\n      children: [links ? /*#__PURE__*/_jsx(\"div\", {\n        className: \"\".concat(baseClassName, \"-links \").concat(hashId).trim(),\n        children: /*#__PURE__*/_jsx(Menu, {\n          inlineIndent: 16,\n          className: \"\".concat(baseClassName, \"-link-menu \").concat(hashId).trim(),\n          selectedKeys: [],\n          openKeys: [],\n          theme: theme,\n          mode: \"inline\",\n          items: linksMenuItems\n        })\n      }) : null, showSiderExtraDom && /*#__PURE__*/_jsxs(_Fragment, {\n        children: [actionAreaDom, !actionsDom && rightContentRender ? /*#__PURE__*/_jsx(\"div\", {\n          className: classNames(\"\".concat(baseClassName, \"-actions\"), hashId, _defineProperty({}, \"\".concat(baseClassName, \"-actions-collapsed\"), collapsed)),\n          children: rightContentRender === null || rightContentRender === void 0 ? void 0 : rightContentRender(props)\n        }) : null]\n      }), menuFooterDom && /*#__PURE__*/_jsx(\"div\", {\n        className: classNames([\"\".concat(baseClassName, \"-footer\"), hashId, _defineProperty({}, \"\".concat(baseClassName, \"-footer-collapsed\"), collapsed)]),\n        children: menuFooterDom\n      })]\n    })]\n  });\n  return stylishClassName.wrapSSR( /*#__PURE__*/_jsxs(_Fragment, {\n    children: [fixSiderbar && !isMobile && !hideMenuWhenCollapsedClassName && /*#__PURE__*/_jsx(\"div\", {\n      style: _objectSpread({\n        width: collapsed ? collapsedWidth : siderWidth,\n        overflow: 'hidden',\n        flex: \"0 0 \".concat(collapsed ? collapsedWidth : siderWidth, \"px\"),\n        maxWidth: collapsed ? collapsedWidth : siderWidth,\n        minWidth: collapsed ? collapsedWidth : siderWidth,\n        transition: 'all 0.2s ease 0s'\n      }, style)\n    }), /*#__PURE__*/_jsxs(Sider, {\n      collapsible: true,\n      trigger: null,\n      collapsed: collapsed,\n      breakpoint: breakpoint === false ? undefined : breakpoint,\n      onCollapse: function onCollapse(collapse) {\n        if (isMobile) return;\n        _onCollapse === null || _onCollapse === void 0 || _onCollapse(collapse);\n      },\n      collapsedWidth: collapsedWidth,\n      style: style,\n      theme: theme,\n      width: siderWidth,\n      className: classNames(siderClassName, hashId, hideMenuWhenCollapsedClassName),\n      children: [hideMenuWhenCollapsedClassName ? /*#__PURE__*/_jsx(\"div\", {\n        className: \"\".concat(baseClassName, \"-hide-when-collapsed \").concat(hashId).trim(),\n        style: {\n          height: '100%',\n          width: '100%',\n          opacity: hideMenuWhenCollapsedClassName ? 0 : 1\n        },\n        children: menuDomItems\n      }) : menuDomItems, collapsedDom]\n    })]\n  }));\n};\nexport { SiderMenu };", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-utils';\nvar genTopNavHeaderStyle = function genTopNavHeaderStyle(token) {\n  var _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5;\n  return _defineProperty({}, token.componentCls, {\n    '&-header-actions': {\n      display: 'flex',\n      height: '100%',\n      alignItems: 'center',\n      '&-item': {\n        display: 'inline-flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        paddingBlock: 0,\n        paddingInline: 2,\n        color: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.colorTextRightActionsItem,\n        fontSize: '16px',\n        cursor: 'pointer',\n        borderRadius: token.borderRadius,\n        '> *': {\n          paddingInline: 6,\n          paddingBlock: 6,\n          borderRadius: token.borderRadius,\n          '&:hover': {\n            backgroundColor: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorBgRightActionsItemHover\n          }\n        }\n      },\n      '&-avatar': {\n        display: 'inline-flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        paddingInlineStart: token.padding,\n        paddingInlineEnd: token.padding,\n        cursor: 'pointer',\n        color: (_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.header) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorTextRightActionsItem,\n        '> div': {\n          height: '44px',\n          color: (_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.header) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorTextRightActionsItem,\n          paddingInline: 8,\n          paddingBlock: 8,\n          cursor: 'pointer',\n          display: 'flex',\n          alignItems: 'center',\n          lineHeight: '44px',\n          borderRadius: token.borderRadius,\n          '&:hover': {\n            backgroundColor: (_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.header) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorBgRightActionsItemHover\n          }\n        }\n      }\n    }\n  });\n};\nexport function useStyle(prefixCls) {\n  return useAntdStyle('ProLayoutRightContent', function (token) {\n    var proToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genTopNavHeaderStyle(proToken)];\n  });\n}", "import _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"rightContentRender\", \"avatarProps\", \"actionsRender\", \"headerContentRender\"],\n  _excluded2 = [\"title\", \"render\"];\nimport { useDebounceFn } from '@ant-design/pro-utils';\nimport { Avatar, ConfigProvider } from 'antd';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport React, { useContext, useMemo, useState } from 'react';\nimport { useStyle } from \"./rightContentStyle\";\n/**\n * 抽离出来是为了防止 rightSize 经常改变导致菜单 render\n *\n * @param param0\n */\nimport { createElement as _createElement } from \"react\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport var ActionsContent = function ActionsContent(_ref) {\n  var rightContentRender = _ref.rightContentRender,\n    avatarProps = _ref.avatarProps,\n    actionsRender = _ref.actionsRender,\n    headerContentRender = _ref.headerContentRender,\n    props = _objectWithoutProperties(_ref, _excluded);\n  var _useContext = useContext(ConfigProvider.ConfigContext),\n    getPrefixCls = _useContext.getPrefixCls;\n  var prefixCls = \"\".concat(getPrefixCls(), \"-pro-global-header\");\n  var _useStyle = useStyle(prefixCls),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  var _useState = useState('auto'),\n    _useState2 = _slicedToArray(_useState, 2),\n    rightSize = _useState2[0],\n    setRightSize = _useState2[1];\n  var avatarDom = useMemo(function () {\n    if (!avatarProps) return null;\n    var title = avatarProps.title,\n      render = avatarProps.render,\n      rest = _objectWithoutProperties(avatarProps, _excluded2);\n    var domList = [rest !== null && rest !== void 0 && rest.src || rest !== null && rest !== void 0 && rest.srcSet || rest.icon || rest.children ? /*#__PURE__*/_createElement(Avatar, _objectSpread(_objectSpread({}, rest), {}, {\n      size: 28,\n      key: \"avatar\"\n    })) : null, title ? /*#__PURE__*/_jsx(\"span\", {\n      style: {\n        marginInlineStart: 8\n      },\n      children: title\n    }, \"name\") : undefined];\n    if (render) {\n      return render(avatarProps, /*#__PURE__*/_jsx(\"div\", {\n        children: domList\n      }), props);\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      children: domList\n    });\n  }, [avatarProps]);\n  var rightActionsRender = actionsRender || avatarDom ? function (restParams) {\n    var doms = actionsRender && (actionsRender === null || actionsRender === void 0 ? void 0 : actionsRender(restParams));\n    if (!doms && !avatarDom) return null;\n    if (!Array.isArray(doms)) return wrapSSR( /*#__PURE__*/_jsxs(\"div\", {\n      className: \"\".concat(prefixCls, \"-header-actions \").concat(hashId).trim(),\n      children: [doms, avatarDom && /*#__PURE__*/_jsx(\"span\", {\n        className: \"\".concat(prefixCls, \"-header-actions-avatar \").concat(hashId).trim(),\n        children: avatarDom\n      })]\n    }));\n    return wrapSSR( /*#__PURE__*/_jsxs(\"div\", {\n      className: \"\".concat(prefixCls, \"-header-actions \").concat(hashId).trim(),\n      children: [doms.filter(Boolean).map(function (dom, index) {\n        var hideHover = false;\n        // 如果配置了 hideHover 就不展示 hover 效果了\n        if ( /*#__PURE__*/React.isValidElement(dom)) {\n          var _dom$props;\n          hideHover = !!(dom !== null && dom !== void 0 && (_dom$props = dom.props) !== null && _dom$props !== void 0 && _dom$props['aria-hidden']);\n        }\n        return /*#__PURE__*/_jsx(\"div\", {\n          className: classNames(\"\".concat(prefixCls, \"-header-actions-item \").concat(hashId), _defineProperty({}, \"\".concat(prefixCls, \"-header-actions-hover\"), !hideHover)),\n          children: dom\n        }, index);\n      }), avatarDom && /*#__PURE__*/_jsx(\"span\", {\n        className: \"\".concat(prefixCls, \"-header-actions-avatar \").concat(hashId).trim(),\n        children: avatarDom\n      })]\n    }));\n  } : undefined;\n  /** 减少一下渲染的次数 */\n  var setRightSizeDebounceFn = useDebounceFn( /*#__PURE__*/function () {\n    var _ref2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(width) {\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            setRightSize(width);\n          case 1:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee);\n    }));\n    return function (_x) {\n      return _ref2.apply(this, arguments);\n    };\n  }(), 160);\n  var contentRender = rightActionsRender || rightContentRender;\n  return /*#__PURE__*/_jsx(\"div\", {\n    className: \"\".concat(prefixCls, \"-right-content \").concat(hashId).trim(),\n    style: {\n      minWidth: rightSize,\n      height: '100%'\n    },\n    children: /*#__PURE__*/_jsx(\"div\", {\n      style: {\n        height: '100%'\n      },\n      children: /*#__PURE__*/_jsx(ResizeObserver, {\n        onResize: function onResize(_ref3) {\n          var width = _ref3.width;\n          setRightSizeDebounceFn.run(width);\n        },\n        children: contentRender ? /*#__PURE__*/_jsx(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            height: '100%',\n            justifyContent: 'flex-end'\n          },\n          children: contentRender(_objectSpread(_objectSpread({}, props), {}, {\n            // 测试专用\n            //@ts-ignore\n            rightContentSize: rightSize\n          }))\n        }) : null\n      })\n    })\n  });\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nvar genTopNavHeaderStyle = function genTopNavHeaderStyle(token) {\n  var _token$layout, _token$layout2;\n  return _defineProperty({}, token.componentCls, {\n    position: 'relative',\n    width: '100%',\n    height: '100%',\n    backgroundColor: 'transparent',\n    '.anticon': {\n      color: 'inherit'\n    },\n    '&-main': {\n      display: 'flex',\n      height: '100%',\n      paddingInlineStart: '16px',\n      '&-left': _defineProperty({\n        display: 'flex',\n        alignItems: 'center'\n      }, \"\".concat(token.proComponentsCls, \"-layout-apps-icon\"), {\n        marginInlineEnd: 16,\n        marginInlineStart: -8\n      })\n    },\n    '&-wide': {\n      maxWidth: 1152,\n      margin: '0 auto'\n    },\n    '&-logo': {\n      position: 'relative',\n      display: 'flex',\n      height: '100%',\n      alignItems: 'center',\n      overflow: 'hidden',\n      '> *:first-child': {\n        display: 'flex',\n        alignItems: 'center',\n        minHeight: '22px',\n        fontSize: '22px'\n      },\n      '> *:first-child > img': {\n        display: 'inline-block',\n        height: '32px',\n        verticalAlign: 'middle'\n      },\n      '> *:first-child > h1': {\n        display: 'inline-block',\n        marginBlock: 0,\n        marginInline: 0,\n        lineHeight: '24px',\n        marginInlineStart: 6,\n        fontWeight: '600',\n        fontSize: '16px',\n        color: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.colorHeaderTitle,\n        verticalAlign: 'top'\n      }\n    },\n    '&-menu': {\n      minWidth: 0,\n      display: 'flex',\n      alignItems: 'center',\n      paddingInline: 6,\n      paddingBlock: 6,\n      lineHeight: \"\".concat(Math.max((((_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.heightLayoutHeader) || 56) - 12, 40), \"px\")\n    }\n  });\n};\nexport function useStyle(prefixCls) {\n  return useAntdStyle('ProLayoutTopNavHeader', function (token) {\n    var topNavHeaderToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genTopNavHeaderStyle(topNavHeaderToken)];\n  });\n}", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { isNeedOpenHash, ProProvider } from '@ant-design/pro-provider';\nimport { coverToNewToken } from '@ant-design/pro-utils';\nimport { ConfigProvider } from 'antd';\nimport classNames from 'classnames';\nimport React, { useContext, useMemo, useRef } from 'react';\nimport { AppsLogoComponents } from \"../AppsLogoComponents\";\nimport { ActionsContent } from \"../GlobalHeader/ActionsContent\";\nimport { BaseMenu } from \"../SiderMenu/BaseMenu\";\nimport { renderLogoAndTitle } from \"../SiderMenu/SiderMenu\";\nimport { useStyle } from \"./style\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nvar TopNavHeader = function TopNavHeader(props) {\n  var _token$layout13, _token$layout14, _token$layout15, _token$layout16, _token$layout17, _token$layout18, _token$layout19;\n  var ref = useRef(null);\n  var onMenuHeaderClick = props.onMenuHeaderClick,\n    contentWidth = props.contentWidth,\n    rightContentRender = props.rightContentRender,\n    propsClassName = props.className,\n    style = props.style,\n    headerContentRender = props.headerContentRender,\n    layout = props.layout,\n    actionsRender = props.actionsRender;\n  var _useContext = useContext(ConfigProvider.ConfigContext),\n    getPrefixCls = _useContext.getPrefixCls;\n  var _useContext2 = useContext(ProProvider),\n    dark = _useContext2.dark;\n  var prefixCls = \"\".concat(props.prefixCls || getPrefixCls('pro'), \"-top-nav-header\");\n  var _useStyle = useStyle(prefixCls),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  var renderKey = undefined;\n  if (props.menuHeaderRender !== undefined) {\n    renderKey = 'menuHeaderRender';\n  } else if (layout === 'mix' || layout === 'top') {\n    renderKey = 'headerTitleRender';\n  }\n  var headerDom = renderLogoAndTitle(_objectSpread(_objectSpread({}, props), {}, {\n    collapsed: false\n  }), renderKey);\n  var _useContext3 = useContext(ProProvider),\n    token = _useContext3.token;\n  var contentDom = useMemo(function () {\n    var _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5, _token$layout6, _token$layout7, _token$layout8, _token$layout9, _token$layout10, _token$layout11, _token$layout12, _props$menuProps;\n    var defaultDom = /*#__PURE__*/_jsx(ConfigProvider // @ts-ignore\n    , {\n      theme: {\n        hashed: isNeedOpenHash(),\n        components: {\n          Layout: {\n            headerBg: 'transparent',\n            bodyBg: 'transparent'\n          },\n          Menu: _objectSpread({}, coverToNewToken({\n            colorItemBg: ((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.colorBgHeader) || 'transparent',\n            colorSubItemBg: ((_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorBgHeader) || 'transparent',\n            radiusItem: token.borderRadius,\n            colorItemBgSelected: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.header) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorBgMenuItemSelected) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),\n            itemHoverBg: ((_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.header) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorBgMenuItemHover) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),\n            colorItemBgSelectedHorizontal: ((_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.header) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorBgMenuItemSelected) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),\n            colorActiveBarWidth: 0,\n            colorActiveBarHeight: 0,\n            colorActiveBarBorderSize: 0,\n            colorItemText: ((_token$layout6 = token.layout) === null || _token$layout6 === void 0 || (_token$layout6 = _token$layout6.header) === null || _token$layout6 === void 0 ? void 0 : _token$layout6.colorTextMenu) || (token === null || token === void 0 ? void 0 : token.colorTextSecondary),\n            colorItemTextHoverHorizontal: ((_token$layout7 = token.layout) === null || _token$layout7 === void 0 || (_token$layout7 = _token$layout7.header) === null || _token$layout7 === void 0 ? void 0 : _token$layout7.colorTextMenuActive) || (token === null || token === void 0 ? void 0 : token.colorText),\n            colorItemTextSelectedHorizontal: ((_token$layout8 = token.layout) === null || _token$layout8 === void 0 || (_token$layout8 = _token$layout8.header) === null || _token$layout8 === void 0 ? void 0 : _token$layout8.colorTextMenuSelected) || (token === null || token === void 0 ? void 0 : token.colorTextBase),\n            horizontalItemBorderRadius: 4,\n            colorItemTextHover: ((_token$layout9 = token.layout) === null || _token$layout9 === void 0 || (_token$layout9 = _token$layout9.header) === null || _token$layout9 === void 0 ? void 0 : _token$layout9.colorTextMenuActive) || 'rgba(0, 0, 0, 0.85)',\n            horizontalItemHoverBg: ((_token$layout10 = token.layout) === null || _token$layout10 === void 0 || (_token$layout10 = _token$layout10.header) === null || _token$layout10 === void 0 ? void 0 : _token$layout10.colorBgMenuItemHover) || 'rgba(0, 0, 0, 0.04)',\n            colorItemTextSelected: ((_token$layout11 = token.layout) === null || _token$layout11 === void 0 || (_token$layout11 = _token$layout11.header) === null || _token$layout11 === void 0 ? void 0 : _token$layout11.colorTextMenuSelected) || 'rgba(0, 0, 0, 1)',\n            popupBg: token === null || token === void 0 ? void 0 : token.colorBgElevated,\n            subMenuItemBg: token === null || token === void 0 ? void 0 : token.colorBgElevated,\n            darkSubMenuItemBg: 'transparent',\n            darkPopupBg: token === null || token === void 0 ? void 0 : token.colorBgElevated\n          }))\n        },\n        token: {\n          colorBgElevated: ((_token$layout12 = token.layout) === null || _token$layout12 === void 0 || (_token$layout12 = _token$layout12.header) === null || _token$layout12 === void 0 ? void 0 : _token$layout12.colorBgHeader) || 'transparent'\n        }\n      },\n      children: /*#__PURE__*/_jsx(BaseMenu, _objectSpread(_objectSpread(_objectSpread({\n        theme: dark ? 'dark' : 'light'\n      }, props), {}, {\n        className: \"\".concat(prefixCls, \"-base-menu \").concat(hashId).trim()\n      }, props.menuProps), {}, {\n        style: _objectSpread({\n          width: '100%'\n        }, (_props$menuProps = props.menuProps) === null || _props$menuProps === void 0 ? void 0 : _props$menuProps.style),\n        collapsed: false,\n        menuRenderType: \"header\",\n        mode: \"horizontal\"\n      }))\n    });\n    if (headerContentRender) {\n      return headerContentRender(props, defaultDom);\n    }\n    return defaultDom;\n  }, [(_token$layout13 = token.layout) === null || _token$layout13 === void 0 || (_token$layout13 = _token$layout13.header) === null || _token$layout13 === void 0 ? void 0 : _token$layout13.colorBgHeader, (_token$layout14 = token.layout) === null || _token$layout14 === void 0 || (_token$layout14 = _token$layout14.header) === null || _token$layout14 === void 0 ? void 0 : _token$layout14.colorBgMenuItemSelected, (_token$layout15 = token.layout) === null || _token$layout15 === void 0 || (_token$layout15 = _token$layout15.header) === null || _token$layout15 === void 0 ? void 0 : _token$layout15.colorBgMenuItemHover, (_token$layout16 = token.layout) === null || _token$layout16 === void 0 || (_token$layout16 = _token$layout16.header) === null || _token$layout16 === void 0 ? void 0 : _token$layout16.colorTextMenu, (_token$layout17 = token.layout) === null || _token$layout17 === void 0 || (_token$layout17 = _token$layout17.header) === null || _token$layout17 === void 0 ? void 0 : _token$layout17.colorTextMenuActive, (_token$layout18 = token.layout) === null || _token$layout18 === void 0 || (_token$layout18 = _token$layout18.header) === null || _token$layout18 === void 0 ? void 0 : _token$layout18.colorTextMenuSelected, (_token$layout19 = token.layout) === null || _token$layout19 === void 0 || (_token$layout19 = _token$layout19.header) === null || _token$layout19 === void 0 ? void 0 : _token$layout19.colorBgMenuElevated, token.borderRadius, token === null || token === void 0 ? void 0 : token.colorBgTextHover, token === null || token === void 0 ? void 0 : token.colorTextSecondary, token === null || token === void 0 ? void 0 : token.colorText, token === null || token === void 0 ? void 0 : token.colorTextBase, token.colorBgElevated, dark, props, prefixCls, hashId, headerContentRender]);\n  return wrapSSR( /*#__PURE__*/_jsx(\"div\", {\n    className: classNames(prefixCls, hashId, propsClassName, _defineProperty({}, \"\".concat(prefixCls, \"-light\"), true)),\n    style: style,\n    children: /*#__PURE__*/_jsxs(\"div\", {\n      ref: ref,\n      className: classNames(\"\".concat(prefixCls, \"-main\"), hashId, _defineProperty({}, \"\".concat(prefixCls, \"-wide\"), contentWidth === 'Fixed' && layout === 'top')),\n      children: [headerDom && /*#__PURE__*/_jsxs(\"div\", {\n        className: classNames(\"\".concat(prefixCls, \"-main-left \").concat(hashId)),\n        onClick: onMenuHeaderClick,\n        children: [/*#__PURE__*/_jsx(AppsLogoComponents, _objectSpread({}, props)), /*#__PURE__*/_jsx(\"div\", {\n          className: \"\".concat(prefixCls, \"-logo \").concat(hashId).trim(),\n          id: \"logo\",\n          children: headerDom\n        }, \"logo\")]\n      }), /*#__PURE__*/_jsx(\"div\", {\n        style: {\n          flex: 1\n        },\n        className: \"\".concat(prefixCls, \"-menu \").concat(hashId).trim(),\n        children: contentDom\n      }), (rightContentRender || actionsRender || props.avatarProps) && /*#__PURE__*/_jsx(ActionsContent, _objectSpread(_objectSpread({\n        rightContentRender: rightContentRender\n      }, props), {}, {\n        prefixCls: prefixCls\n      }))]\n    })\n  }));\n};\nexport { TopNavHeader };", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nvar genGlobalHeaderStyle = function genGlobalHeaderStyle(token) {\n  var _token$layout, _token$layout2, _token$layout3;\n  return _defineProperty({}, token.componentCls, _defineProperty(_defineProperty(_defineProperty(_defineProperty({\n    position: 'relative',\n    background: 'transparent',\n    display: 'flex',\n    alignItems: 'center',\n    marginBlock: 0,\n    marginInline: 16,\n    height: ((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.heightLayoutHeader) || 56,\n    boxSizing: 'border-box',\n    '> a': {\n      height: '100%'\n    }\n  }, \"\".concat(token.proComponentsCls, \"-layout-apps-icon\"), {\n    marginInlineEnd: 16\n  }), '&-collapsed-button', {\n    minHeight: '22px',\n    color: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorHeaderTitle,\n    fontSize: '18px',\n    marginInlineEnd: '16px'\n  }), '&-logo', {\n    position: 'relative',\n    marginInlineEnd: '16px',\n    a: {\n      display: 'flex',\n      alignItems: 'center',\n      height: '100%',\n      minHeight: '22px',\n      fontSize: '20px'\n    },\n    img: {\n      height: '28px'\n    },\n    h1: {\n      height: '32px',\n      marginBlock: 0,\n      marginInline: 0,\n      marginInlineStart: 8,\n      fontWeight: '600',\n      color: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.header) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorHeaderTitle) || token.colorTextHeading,\n      fontSize: '18px',\n      lineHeight: '32px'\n    },\n    '&-mix': {\n      display: 'flex',\n      alignItems: 'center'\n    }\n  }), '&-logo-mobile', {\n    minWidth: '24px',\n    marginInlineEnd: 0\n  }));\n};\nexport function useStyle(prefixCls) {\n  return useAntdStyle('ProLayoutGlobalHeader', function (token) {\n    var GlobalHeaderToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genGlobalHeaderStyle(GlobalHeaderToken)];\n  });\n}", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { MenuOutlined } from '@ant-design/icons';\nimport { ConfigProvider } from 'antd';\nimport classNames from 'classnames';\nimport React, { useContext } from 'react';\nimport { clearMenuItem } from \"../../utils/utils\";\nimport { AppsLogoComponents, defaultRenderLogo } from \"../AppsLogoComponents\";\nimport { renderLogoAndTitle } from \"../SiderMenu/SiderMenu\";\nimport { TopNavHeader } from \"../TopNavHeader\";\nimport { ActionsContent } from \"./ActionsContent\";\nimport { useStyle } from \"./style\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nvar renderLogo = function renderLogo(menuHeaderRender, logoDom) {\n  if (menuHeaderRender === false) {\n    return null;\n  }\n  if (menuHeaderRender) {\n    return menuHeaderRender(logoDom, null);\n  }\n  return logoDom;\n};\nvar GlobalHeader = function GlobalHeader(props) {\n  var isMobile = props.isMobile,\n    logo = props.logo,\n    collapsed = props.collapsed,\n    onCollapse = props.onCollapse,\n    rightContentRender = props.rightContentRender,\n    menuHeaderRender = props.menuHeaderRender,\n    onMenuHeaderClick = props.onMenuHeaderClick,\n    propClassName = props.className,\n    style = props.style,\n    layout = props.layout,\n    children = props.children,\n    splitMenus = props.splitMenus,\n    menuData = props.menuData,\n    prefixCls = props.prefixCls;\n  var _useContext = useContext(ConfigProvider.ConfigContext),\n    getPrefixCls = _useContext.getPrefixCls,\n    direction = _useContext.direction;\n  var baseClassName = \"\".concat(prefixCls || getPrefixCls('pro'), \"-global-header\");\n  var _useStyle = useStyle(baseClassName),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  var className = classNames(propClassName, baseClassName, hashId);\n  if (layout === 'mix' && !isMobile && splitMenus) {\n    var noChildrenMenuData = (menuData || []).map(function (item) {\n      return _objectSpread(_objectSpread({}, item), {}, {\n        children: undefined,\n        routes: undefined\n      });\n    });\n    var clearMenuData = clearMenuItem(noChildrenMenuData);\n    return /*#__PURE__*/_jsx(TopNavHeader, _objectSpread(_objectSpread({\n      mode: \"horizontal\"\n    }, props), {}, {\n      splitMenus: false,\n      menuData: clearMenuData\n    }));\n  }\n  var logoClassNames = classNames(\"\".concat(baseClassName, \"-logo\"), hashId, _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-logo-rtl\"), direction === 'rtl'), \"\".concat(baseClassName, \"-logo-mix\"), layout === 'mix'), \"\".concat(baseClassName, \"-logo-mobile\"), isMobile));\n  var logoDom = /*#__PURE__*/_jsx(\"span\", {\n    className: logoClassNames,\n    children: /*#__PURE__*/_jsx(\"a\", {\n      children: defaultRenderLogo(logo)\n    })\n  }, \"logo\");\n  return wrapSSR( /*#__PURE__*/_jsxs(\"div\", {\n    className: className,\n    style: _objectSpread({}, style),\n    children: [isMobile && /*#__PURE__*/_jsx(\"span\", {\n      className: \"\".concat(baseClassName, \"-collapsed-button \").concat(hashId).trim(),\n      onClick: function onClick() {\n        onCollapse === null || onCollapse === void 0 || onCollapse(!collapsed);\n      },\n      children: /*#__PURE__*/_jsx(MenuOutlined, {})\n    }), isMobile && renderLogo(menuHeaderRender, logoDom), layout === 'mix' && !isMobile && /*#__PURE__*/_jsxs(_Fragment, {\n      children: [/*#__PURE__*/_jsx(AppsLogoComponents, _objectSpread({}, props)), /*#__PURE__*/_jsx(\"div\", {\n        className: logoClassNames,\n        onClick: onMenuHeaderClick,\n        children: renderLogoAndTitle(_objectSpread(_objectSpread({}, props), {}, {\n          collapsed: false\n        }), 'headerTitleRender')\n      })]\n    }), /*#__PURE__*/_jsx(\"div\", {\n      style: {\n        flex: 1\n      },\n      children: children\n    }), (rightContentRender || props.actionsRender || props.avatarProps) && /*#__PURE__*/_jsx(ActionsContent, _objectSpread({\n      rightContentRender: rightContentRender\n    }, props))]\n  }));\n};\nexport { GlobalHeader };", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nvar genProLayoutHeaderStyle = function genProLayoutHeaderStyle(token) {\n  var _token$layout, _token$layout2, _token$layout3, _token$layout4;\n  return _defineProperty({}, \"\".concat(token.proComponentsCls, \"-layout\"), _defineProperty({}, \"\".concat(token.antCls, \"-layout-header\").concat(token.componentCls), {\n    height: ((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.heightLayoutHeader) || 56,\n    lineHeight: \"\".concat(((_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.heightLayoutHeader) || 56, \"px\"),\n    // hitu 用了这个属性，不能删除哦 @南取\n    zIndex: 19,\n    width: '100%',\n    paddingBlock: 0,\n    paddingInline: 0,\n    borderBlockEnd: \"1px solid \".concat(token.colorSplit),\n    backgroundColor: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.header) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorBgHeader) || 'rgba(255, 255, 255, 0.4)',\n    WebkitBackdropFilter: 'blur(8px)',\n    backdropFilter: 'blur(8px)',\n    transition: 'background-color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)',\n    '&-fixed-header': {\n      position: 'fixed',\n      insetBlockStart: 0,\n      width: '100%',\n      zIndex: 100,\n      insetInlineEnd: 0\n    },\n    '&-fixed-header-scroll': {\n      backgroundColor: ((_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.header) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorBgScrollHeader) || 'rgba(255, 255, 255, 0.8)'\n    },\n    '&-header-actions': {\n      display: 'flex',\n      alignItems: 'center',\n      fontSize: '16',\n      cursor: 'pointer',\n      '& &-item': {\n        paddingBlock: 0,\n        paddingInline: 8,\n        '&:hover': {\n          color: token.colorText\n        }\n      }\n    },\n    '&-header-realDark': {\n      boxShadow: '0 2px 8px 0 rgba(0, 0, 0, 65%)'\n    },\n    '&-header-actions-header-action': {\n      transition: 'width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)'\n    }\n  }));\n};\nexport function useStyle(prefixCls) {\n  return useAntdStyle('ProLayoutHeader', function (token) {\n    var ProLayoutHeaderToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genProLayoutHeaderStyle(ProLayoutHeaderToken)];\n  });\n}", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nexport function useStylish(prefixCls, _ref) {\n  var stylish = _ref.stylish,\n    proLayoutCollapsedWidth = _ref.proLayoutCollapsedWidth;\n  return useAntdStyle('ProLayoutHeaderStylish', function (token) {\n    var stylishToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls),\n      proLayoutCollapsedWidth: proLayoutCollapsedWidth\n    });\n    if (!stylish) return [];\n    return [_defineProperty({}, \"div\".concat(token.proComponentsCls, \"-layout\"), _defineProperty({}, \"\".concat(stylishToken.componentCls), stylish === null || stylish === void 0 ? void 0 : stylish(stylishToken)))];\n  });\n}", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { isNeedOpenHash, ProProvider } from '@ant-design/pro-provider';\nimport { ConfigProvider, Layout } from 'antd';\nimport classNames from 'classnames';\nimport React, { useCallback, useContext, useEffect, useState } from 'react';\nimport { clearMenuItem } from \"../../utils/utils\";\nimport { GlobalHeader } from \"../GlobalHeader\";\nimport { TopNavHeader } from \"../TopNavHeader\";\nimport { useStyle } from \"./style/header\";\nimport { useStylish } from \"./style/stylish\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nvar Header = Layout.Header;\nvar DefaultHeader = function DefaultHeader(props) {\n  var _token$layout2, _token$layout3, _token$layout4;\n  var isMobile = props.isMobile,\n    fixedHeader = props.fixedHeader,\n    propsClassName = props.className,\n    style = props.style,\n    collapsed = props.collapsed,\n    prefixCls = props.prefixCls,\n    onCollapse = props.onCollapse,\n    layout = props.layout,\n    headerRender = props.headerRender,\n    headerContentRender = props.headerContentRender;\n  var _useContext = useContext(ProProvider),\n    token = _useContext.token;\n  var context = useContext(ConfigProvider.ConfigContext);\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isFixedHeaderScroll = _useState2[0],\n    setIsFixedHeaderScroll = _useState2[1];\n  var needFixedHeader = fixedHeader || layout === 'mix';\n  var renderContent = useCallback(function () {\n    var isTop = layout === 'top';\n    var clearMenuData = clearMenuItem(props.menuData || []);\n    var defaultDom = /*#__PURE__*/_jsx(GlobalHeader, _objectSpread(_objectSpread({\n      onCollapse: onCollapse\n    }, props), {}, {\n      menuData: clearMenuData,\n      children: headerContentRender && headerContentRender(props, null)\n    }));\n    if (isTop && !isMobile) {\n      defaultDom = /*#__PURE__*/_jsx(TopNavHeader, _objectSpread(_objectSpread({\n        mode: \"horizontal\",\n        onCollapse: onCollapse\n      }, props), {}, {\n        menuData: clearMenuData\n      }));\n    }\n    if (headerRender && typeof headerRender === 'function') {\n      return headerRender(props, defaultDom);\n    }\n    return defaultDom;\n  }, [headerContentRender, headerRender, isMobile, layout, onCollapse, props]);\n  useEffect(function () {\n    var _context$getTargetCon;\n    var dom = (context === null || context === void 0 || (_context$getTargetCon = context.getTargetContainer) === null || _context$getTargetCon === void 0 ? void 0 : _context$getTargetCon.call(context)) || document.body;\n    var isFixedHeaderFn = function isFixedHeaderFn() {\n      var _token$layout;\n      var scrollTop = dom.scrollTop;\n      if (scrollTop > (((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.header) === null || _token$layout === void 0 ? void 0 : _token$layout.heightLayoutHeader) || 56) && !isFixedHeaderScroll) {\n        setIsFixedHeaderScroll(true);\n        return true;\n      }\n      if (isFixedHeaderScroll) {\n        setIsFixedHeaderScroll(false);\n      }\n      return false;\n    };\n    if (!needFixedHeader) return;\n    if (typeof window === 'undefined') return;\n    dom.addEventListener('scroll', isFixedHeaderFn, {\n      passive: true\n    });\n    return function () {\n      dom.removeEventListener('scroll', isFixedHeaderFn);\n    };\n  }, [(_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.header) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.heightLayoutHeader, needFixedHeader, isFixedHeaderScroll]);\n  var isTop = layout === 'top';\n  var baseClassName = \"\".concat(prefixCls, \"-layout-header\");\n  var _useStyle = useStyle(baseClassName),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  var stylish = useStylish(\"\".concat(baseClassName, \".\").concat(baseClassName, \"-stylish\"), {\n    proLayoutCollapsedWidth: 64,\n    stylish: props.stylish\n  });\n  var className = classNames(propsClassName, hashId, baseClassName, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(baseClassName, \"-fixed-header\"), needFixedHeader), \"\".concat(baseClassName, \"-fixed-header-scroll\"), isFixedHeaderScroll), \"\".concat(baseClassName, \"-mix\"), layout === 'mix'), \"\".concat(baseClassName, \"-fixed-header-action\"), !collapsed), \"\".concat(baseClassName, \"-top-menu\"), isTop), \"\".concat(baseClassName, \"-header\"), true), \"\".concat(baseClassName, \"-stylish\"), !!props.stylish));\n  if (layout === 'side' && !isMobile) return null;\n  return stylish.wrapSSR(wrapSSR( /*#__PURE__*/_jsx(_Fragment, {\n    children: /*#__PURE__*/_jsxs(ConfigProvider\n    // @ts-ignore\n    , {\n      theme: {\n        hashed: isNeedOpenHash(),\n        components: {\n          Layout: {\n            headerBg: 'transparent',\n            bodyBg: 'transparent'\n          }\n        }\n      },\n      children: [needFixedHeader && /*#__PURE__*/_jsx(Header, {\n        style: _objectSpread({\n          height: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.header) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.heightLayoutHeader) || 56,\n          lineHeight: \"\".concat(((_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.header) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.heightLayoutHeader) || 56, \"px\"),\n          backgroundColor: 'transparent',\n          zIndex: 19\n        }, style)\n      }), /*#__PURE__*/_jsx(Header, {\n        className: className,\n        style: style,\n        children: renderContent()\n      })]\n    })\n  })));\n};\nexport { DefaultHeader };", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { Keyframes } from '@ant-design/cssinjs';\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nexport var proLayoutTitleHide = new Keyframes('antBadgeLoadingCircle', {\n  '0%': {\n    display: 'none',\n    opacity: 0,\n    overflow: 'hidden'\n  },\n  '80%': {\n    overflow: 'hidden'\n  },\n  '100%': {\n    display: 'unset',\n    opacity: 1\n  }\n});\nvar genSiderMenuStyle = function genSiderMenuStyle(token) {\n  var _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5, _token$layout6, _token$layout7, _token$layout8, _token$layout9, _token$layout10, _token$layout11, _token$layout12;\n  return _defineProperty({}, \"\".concat(token.proComponentsCls, \"-layout\"), _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-layout-sider\").concat(token.componentCls), {\n    background: ((_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.sider) === null || _token$layout === void 0 ? void 0 : _token$layout.colorMenuBackground) || 'transparent'\n  }), token.componentCls, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({\n    position: 'relative',\n    boxSizing: 'border-box',\n    '&-menu': {\n      position: 'relative',\n      zIndex: 10,\n      minHeight: '100%'\n    }\n  }, \"& \".concat(token.antCls, \"-layout-sider-children\"), {\n    position: 'relative',\n    display: 'flex',\n    flexDirection: 'column',\n    height: '100%',\n    paddingInline: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.sider) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.paddingInlineLayoutMenu,\n    paddingBlock: (_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.sider) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.paddingBlockLayoutMenu,\n    borderInlineEnd: \"1px solid \".concat(token.colorSplit),\n    marginInlineEnd: -1\n  }), \"\".concat(token.antCls, \"-menu\"), _defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-menu-item-group-title\"), {\n    fontSize: token.fontSizeSM,\n    paddingBottom: 4\n  }), \"\".concat(token.antCls, \"-menu-item:not(\").concat(token.antCls, \"-menu-item-selected):hover\"), {\n    color: (_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.sider) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorTextMenuItemHover\n  })), '&-logo', {\n    position: 'relative',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    paddingInline: 12,\n    paddingBlock: 16,\n    color: (_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.sider) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorTextMenu,\n    cursor: 'pointer',\n    borderBlockEnd: \"1px solid \".concat((_token$layout6 = token.layout) === null || _token$layout6 === void 0 || (_token$layout6 = _token$layout6.sider) === null || _token$layout6 === void 0 ? void 0 : _token$layout6.colorMenuItemDivider),\n    '> a': {\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      minHeight: 22,\n      fontSize: 22,\n      '> img': {\n        display: 'inline-block',\n        height: 22,\n        verticalAlign: 'middle'\n      },\n      '> h1': {\n        display: 'inline-block',\n        height: 22,\n        marginBlock: 0,\n        marginInlineEnd: 0,\n        marginInlineStart: 6,\n        color: (_token$layout7 = token.layout) === null || _token$layout7 === void 0 || (_token$layout7 = _token$layout7.sider) === null || _token$layout7 === void 0 ? void 0 : _token$layout7.colorTextMenuTitle,\n        animationName: proLayoutTitleHide,\n        animationDuration: '.4s',\n        animationTimingFunction: 'ease',\n        fontWeight: 600,\n        fontSize: 16,\n        lineHeight: '22px',\n        verticalAlign: 'middle'\n      }\n    },\n    '&-collapsed': _defineProperty({\n      flexDirection: 'column-reverse',\n      margin: 0,\n      padding: 12\n    }, \"\".concat(token.proComponentsCls, \"-layout-apps-icon\"), {\n      marginBlockEnd: 8,\n      fontSize: 16,\n      transition: 'font-size 0.2s ease-in-out,color 0.2s ease-in-out'\n    })\n  }), '&-actions', {\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    marginBlock: 4,\n    marginInline: 0,\n    color: (_token$layout8 = token.layout) === null || _token$layout8 === void 0 || (_token$layout8 = _token$layout8.sider) === null || _token$layout8 === void 0 ? void 0 : _token$layout8.colorTextMenu,\n    '&-collapsed': {\n      flexDirection: 'column-reverse',\n      paddingBlock: 0,\n      paddingInline: 8,\n      fontSize: 16,\n      transition: 'font-size 0.3s ease-in-out'\n    },\n    '&-list': {\n      color: (_token$layout9 = token.layout) === null || _token$layout9 === void 0 || (_token$layout9 = _token$layout9.sider) === null || _token$layout9 === void 0 ? void 0 : _token$layout9.colorTextMenuSecondary,\n      '&-collapsed': {\n        marginBlockEnd: 8,\n        animationName: 'none'\n      },\n      '&-item': {\n        paddingInline: 6,\n        paddingBlock: 6,\n        lineHeight: '16px',\n        fontSize: 16,\n        cursor: 'pointer',\n        borderRadius: token.borderRadius,\n        '&:hover': {\n          background: token.colorBgTextHover\n        }\n      }\n    },\n    '&-avatar': {\n      fontSize: 14,\n      paddingInline: 8,\n      paddingBlock: 8,\n      display: 'flex',\n      alignItems: 'center',\n      gap: token.marginXS,\n      borderRadius: token.borderRadius,\n      '& *': {\n        cursor: 'pointer'\n      },\n      '&:hover': {\n        background: token.colorBgTextHover\n      }\n    }\n  }), '&-hide-menu-collapsed', {\n    insetInlineStart: \"-\".concat(token.proLayoutCollapsedWidth - 12, \"px\"),\n    position: 'absolute'\n  }), '&-extra', {\n    marginBlockEnd: 16,\n    marginBlock: 0,\n    marginInline: 16,\n    '&-no-logo': {\n      marginBlockStart: 16\n    }\n  }), '&-links', {\n    width: '100%',\n    ul: {\n      height: 'auto'\n    }\n  }), '&-link-menu', {\n    border: 'none',\n    boxShadow: 'none',\n    background: 'transparent'\n  }), '&-footer', {\n    color: (_token$layout10 = token.layout) === null || _token$layout10 === void 0 || (_token$layout10 = _token$layout10.sider) === null || _token$layout10 === void 0 ? void 0 : _token$layout10.colorTextMenuSecondary,\n    paddingBlockEnd: 16,\n    fontSize: token.fontSize,\n    animationName: proLayoutTitleHide,\n    animationDuration: '.4s',\n    animationTimingFunction: 'ease'\n  })), \"\".concat(token.componentCls).concat(token.componentCls, \"-fixed\"), {\n    position: 'fixed',\n    insetBlockStart: 0,\n    insetInlineStart: 0,\n    zIndex: '100',\n    height: '100%',\n    '&-mix': {\n      height: \"calc(100% - \".concat(((_token$layout11 = token.layout) === null || _token$layout11 === void 0 || (_token$layout11 = _token$layout11.header) === null || _token$layout11 === void 0 ? void 0 : _token$layout11.heightLayoutHeader) || 56, \"px)\"),\n      insetBlockStart: \"\".concat(((_token$layout12 = token.layout) === null || _token$layout12 === void 0 || (_token$layout12 = _token$layout12.header) === null || _token$layout12 === void 0 ? void 0 : _token$layout12.heightLayoutHeader) || 56, \"px\")\n    }\n  }));\n};\nexport function useStyle(prefixCls, _ref2) {\n  var proLayoutCollapsedWidth = _ref2.proLayoutCollapsedWidth;\n  return useAntdStyle('ProLayoutSiderMenu', function (token) {\n    var siderMenuToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls),\n      proLayoutCollapsedWidth: proLayoutCollapsedWidth\n    });\n    return [genSiderMenuStyle(siderMenuToken)];\n  });\n}", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { ProProvider } from '@ant-design/pro-provider';\nimport { openVisibleCompatible } from '@ant-design/pro-utils';\nimport { ConfigProvider, Drawer } from 'antd';\nimport classNames from 'classnames';\nimport Omit from 'omit.js';\nimport React, { useContext, useEffect } from 'react';\nimport { SiderMenu } from \"./SiderMenu\";\nimport { useStyle } from \"./style/index\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nvar SiderMenuWrapper = function SiderMenuWrapper(props) {\n  var _token$layout;\n  var isMobile = props.isMobile,\n    siderWidth = props.siderWidth,\n    collapsed = props.collapsed,\n    onCollapse = props.onCollapse,\n    style = props.style,\n    className = props.className,\n    hide = props.hide,\n    prefixCls = props.prefixCls,\n    getContainer = props.getContainer;\n  var _useContext = useContext(ProProvider),\n    token = _useContext.token;\n  useEffect(function () {\n    if (isMobile === true) {\n      onCollapse === null || onCollapse === void 0 || onCollapse(true);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isMobile]);\n  var omitProps = Omit(props, ['className', 'style']);\n  var _React$useContext = React.useContext(ConfigProvider.ConfigContext),\n    direction = _React$useContext.direction;\n  var _useStyle = useStyle(\"\".concat(prefixCls, \"-sider\"), {\n      proLayoutCollapsedWidth: 64\n    }),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n  var siderClassName = classNames(\"\".concat(prefixCls, \"-sider\"), className, hashId);\n  if (hide) {\n    return null;\n  }\n  var drawerOpenProps = openVisibleCompatible(!collapsed, function () {\n    return onCollapse === null || onCollapse === void 0 ? void 0 : onCollapse(true);\n  });\n  return wrapSSR(isMobile ? /*#__PURE__*/_jsx(Drawer, _objectSpread(_objectSpread({\n    placement: direction === 'rtl' ? 'right' : 'left',\n    className: classNames(\"\".concat(prefixCls, \"-drawer-sider\"), className)\n  }, drawerOpenProps), {}, {\n    style: _objectSpread({\n      padding: 0,\n      height: '100vh'\n    }, style),\n    onClose: function onClose() {\n      onCollapse === null || onCollapse === void 0 || onCollapse(true);\n    },\n    maskClosable: true,\n    closable: false,\n    getContainer: getContainer || false,\n    width: siderWidth,\n    styles: {\n      body: {\n        height: '100vh',\n        padding: 0,\n        display: 'flex',\n        flexDirection: 'row',\n        backgroundColor: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.sider) === null || _token$layout === void 0 ? void 0 : _token$layout.colorMenuBackground\n      }\n    },\n    children: /*#__PURE__*/_jsx(SiderMenu, _objectSpread(_objectSpread({}, omitProps), {}, {\n      isMobile: true,\n      className: siderClassName,\n      collapsed: isMobile ? false : collapsed,\n      splitMenus: false,\n      originCollapsed: collapsed\n    }))\n  })) : /*#__PURE__*/_jsx(SiderMenu, _objectSpread(_objectSpread({\n    className: siderClassName,\n    originCollapsed: collapsed\n  }, omitProps), {}, {\n    style: style\n  })));\n};\nexport { SiderMenuWrapper as SiderMenu };", "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { match } from 'path-to-regexp';\nexport var matchParamsPath = function matchParamsPath(pathname, breadcrumb, breadcrumbMap) {\n  // Internal logic use breadcrumbMap to ensure the order\n  // 内部逻辑使用 breadcrumbMap 来确保查询顺序\n  if (breadcrumbMap) {\n    var pathKey = _toConsumableArray(breadcrumbMap.keys()).find(function (key) {\n      try {\n        if (key.startsWith('http')) {\n          return false;\n        }\n        return match(key)(pathname);\n      } catch (error) {\n        console.log('key', key, error);\n        return false;\n      }\n    });\n    if (pathKey) {\n      return breadcrumbMap.get(pathKey);\n    }\n  }\n\n  // External uses use breadcrumb\n  // 外部用户使用 breadcrumb 参数\n  if (breadcrumb) {\n    var _pathKey = Object.keys(breadcrumb).find(function (key) {\n      try {\n        if (key !== null && key !== void 0 && key.startsWith('http')) {\n          return false;\n        }\n        return match(key)(pathname);\n      } catch (error) {\n        console.log('key', key, error);\n        return false;\n      }\n    });\n    if (_pathKey) {\n      return breadcrumb[_pathKey];\n    }\n  }\n  return {\n    path: ''\n  };\n};\n/**\n * 获取关于 pageTitle 的所有信息方便包装\n *\n * @param props\n * @param ignoreTitle\n */\nexport var getPageTitleInfo = function getPageTitleInfo(props, ignoreTitle) {\n  var _props$pathname = props.pathname,\n    pathname = _props$pathname === void 0 ? '/' : _props$pathname,\n    breadcrumb = props.breadcrumb,\n    breadcrumbMap = props.breadcrumbMap,\n    formatMessage = props.formatMessage,\n    title = props.title,\n    _props$menu = props.menu,\n    menu = _props$menu === void 0 ? {\n      locale: false\n    } : _props$menu;\n  var pageTitle = ignoreTitle ? '' : title || '';\n  var currRouterData = matchParamsPath(pathname, breadcrumb, breadcrumbMap);\n  if (!currRouterData) {\n    return {\n      title: pageTitle,\n      id: '',\n      pageName: pageTitle\n    };\n  }\n  var pageName = currRouterData.name;\n  if (menu.locale !== false && currRouterData.locale && formatMessage) {\n    pageName = formatMessage({\n      id: currRouterData.locale || '',\n      defaultMessage: currRouterData.name\n    });\n  }\n  if (!pageName) {\n    return {\n      title: pageTitle,\n      id: currRouterData.locale || '',\n      pageName: pageTitle\n    };\n  }\n  if (ignoreTitle || !title) {\n    return {\n      title: pageName,\n      id: currRouterData.locale || '',\n      pageName: pageName\n    };\n  }\n  return {\n    title: \"\".concat(pageName, \" - \").concat(title),\n    id: currRouterData.locale || '',\n    pageName: pageName\n  };\n};\nexport var getPageTitle = function getPageTitle(props, ignoreTitle) {\n  return getPageTitleInfo(props, ignoreTitle).title;\n};", "export default {\n  'app.setting.pagestyle': 'Page style setting',\n  'app.setting.pagestyle.dark': 'Dark Menu style',\n  'app.setting.pagestyle.light': 'Light Menu style',\n  'app.setting.pagestyle.realdark': 'Dark style (Beta)',\n  'app.setting.content-width': 'Content Width',\n  'app.setting.content-width.fixed': 'Fixed',\n  'app.setting.content-width.fluid': 'Fluid',\n  'app.setting.themecolor': 'Theme Color',\n  'app.setting.themecolor.dust': 'Dust Red',\n  'app.setting.themecolor.volcano': 'Volcano',\n  'app.setting.themecolor.sunset': 'Sunset Orange',\n  'app.setting.themecolor.cyan': 'Cyan',\n  'app.setting.themecolor.green': 'Polar Green',\n  'app.setting.themecolor.techBlue': 'Tech Blue (default)',\n  'app.setting.themecolor.daybreak': 'Daybreak Blue',\n  'app.setting.themecolor.geekblue': 'Geek Blue',\n  'app.setting.themecolor.purple': 'Golden Purple',\n  'app.setting.sidermenutype': 'SideMenu Type',\n  'app.setting.sidermenutype-sub': 'Classic',\n  'app.setting.sidermenutype-group': 'Grouping',\n  'app.setting.navigationmode': 'Navigation Mode',\n  'app.setting.regionalsettings': 'Regional Settings',\n  'app.setting.regionalsettings.header': 'Header',\n  'app.setting.regionalsettings.menu': 'Menu',\n  'app.setting.regionalsettings.footer': 'Footer',\n  'app.setting.regionalsettings.menuHeader': 'Menu Header',\n  'app.setting.sidemenu': 'Side Menu Layout',\n  'app.setting.topmenu': 'Top Menu Layout',\n  'app.setting.mixmenu': 'Mix Menu Layout',\n  'app.setting.splitMenus': 'Split Menus',\n  'app.setting.fixedheader': 'Fixed Header',\n  'app.setting.fixedsidebar': 'Fixed Sidebar',\n  'app.setting.fixedsidebar.hint': 'Works on Side Menu Layout',\n  'app.setting.hideheader': 'Hidden Header when scrolling',\n  'app.setting.hideheader.hint': 'Works when Hidden Header is enabled',\n  'app.setting.othersettings': 'Other Settings',\n  'app.setting.weakmode': 'Weak Mode',\n  'app.setting.copy': 'Copy Setting',\n  'app.setting.loading': 'Loading theme',\n  'app.setting.copyinfo': 'copy success，please replace defaultSettings in src/models/setting.js',\n  'app.setting.production.hint': 'Setting panel shows in development environment only, please manually modify'\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport settingDrawer from \"./en-US/settingDrawer\";\nexport default _objectSpread({}, settingDrawer);", "export default {\n  'app.setting.pagestyle': 'Impostazioni di stile',\n  'app.setting.pagestyle.dark': 'Tema scuro',\n  'app.setting.pagestyle.light': 'Tema chiaro',\n  'app.setting.content-width': 'Largezza contenuto',\n  'app.setting.content-width.fixed': 'Fissa',\n  'app.setting.content-width.fluid': 'Fluida',\n  'app.setting.themecolor': 'Colore del tema',\n  'app.setting.themecolor.dust': 'Rosso polvere',\n  'app.setting.themecolor.volcano': 'Vulcano',\n  'app.setting.themecolor.sunset': 'Arancione tramonto',\n  'app.setting.themecolor.cyan': '<PERSON><PERSON>',\n  'app.setting.themecolor.green': 'Verde polare',\n  'app.setting.themecolor.techBlue': 'Tech Blu (default)',\n  'app.setting.themecolor.daybreak': 'Blu cielo mattutino',\n  'app.setting.themecolor.geekblue': 'Blu geek',\n  'app.setting.themecolor.purple': 'Viola dorato',\n  'app.setting.navigationmode': 'Modalità di navigazione',\n  'app.setting.sidemenu': 'Menu laterale',\n  'app.setting.topmenu': 'Menu in testata',\n  'app.setting.mixmenu': 'Menu misto',\n  'app.setting.splitMenus': 'Menu divisi',\n  'app.setting.fixedheader': 'Testata fissa',\n  'app.setting.fixedsidebar': 'Menu laterale fisso',\n  'app.setting.fixedsidebar.hint': 'Solo se selezionato Menu laterale',\n  'app.setting.hideheader': 'Nascondi testata durante lo scorrimento',\n  'app.setting.hideheader.hint': 'Solo se abilitato Nascondi testata durante lo scorrimento',\n  'app.setting.othersettings': 'Altre impostazioni',\n  'app.setting.weakmode': 'Inverti colori',\n  'app.setting.copy': 'Copia impostazioni',\n  'app.setting.loading': 'Carico tema...',\n  'app.setting.copyinfo': 'Impostazioni copiate con successo! Incolla il contenuto in config/defaultSettings.js',\n  'app.setting.production.hint': 'Questo pannello è visibile solo durante lo sviluppo. Le impostazioni devono poi essere modificate manulamente'\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport settingDrawer from \"./it-IT/settingDrawer\";\nexport default _objectSpread({}, settingDrawer);", "export default {\n  'app.setting.pagestyle': '스타일 설정',\n  'app.setting.pagestyle.dark': '다크 모드',\n  'app.setting.pagestyle.light': '라이트 모드',\n  'app.setting.content-width': '컨텐츠 너비',\n  'app.setting.content-width.fixed': '고정',\n  'app.setting.content-width.fluid': '흐름',\n  'app.setting.themecolor': '테마 색상',\n  'app.setting.themecolor.dust': 'Dust Red',\n  'app.setting.themecolor.volcano': 'Volcano',\n  'app.setting.themecolor.sunset': 'Sunset Orange',\n  'app.setting.themecolor.cyan': '<PERSON>an',\n  'app.setting.themecolor.green': 'Polar Green',\n  'app.setting.themecolor.techBlue': 'Tech Blu (default)',\n  'app.setting.themecolor.daybreak': 'Daybreak Blue',\n  'app.setting.themecolor.geekblue': 'Geek Blue',\n  'app.setting.themecolor.purple': 'Golden Purple',\n  'app.setting.navigationmode': '네비게이션 모드',\n  'app.setting.regionalsettings': '영역별 설정',\n  'app.setting.regionalsettings.header': '헤더',\n  'app.setting.regionalsettings.menu': '메뉴',\n  'app.setting.regionalsettings.footer': '바닥글',\n  'app.setting.regionalsettings.menuHeader': '메뉴 헤더',\n  'app.setting.sidemenu': '메뉴 사이드 배치',\n  'app.setting.topmenu': '메뉴 상단 배치',\n  'app.setting.mixmenu': '혼합형 배치',\n  'app.setting.splitMenus': '메뉴 분리',\n  'app.setting.fixedheader': '헤더 고정',\n  'app.setting.fixedsidebar': '사이드바 고정',\n  'app.setting.fixedsidebar.hint': \"'메뉴 사이드 배치'를 선택했을 때 동작함\",\n  'app.setting.hideheader': '스크롤 중 헤더 감추기',\n  'app.setting.hideheader.hint': \"'헤더 감추기 옵션'을 선택했을 때 동작함\",\n  'app.setting.othersettings': '다른 설정',\n  'app.setting.weakmode': '고대비 모드',\n  'app.setting.copy': '설정값 복사',\n  'app.setting.loading': '테마 로딩 중',\n  'app.setting.copyinfo': '복사 성공. src/models/settings.js에 있는 defaultSettings를 교체해 주세요.',\n  'app.setting.production.hint': '설정 판넬은 개발 환경에서만 보여집니다. 직접 수동으로 변경바랍니다.'\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport settingDrawer from \"./ko-KR/settingDrawer\";\nexport default _objectSpread({}, settingDrawer);", "export default {\n  'app.setting.pagestyle': '整体风格设置',\n  'app.setting.pagestyle.dark': '暗色菜单风格',\n  'app.setting.pagestyle.light': '亮色菜单风格',\n  'app.setting.pagestyle.realdark': '暗色风格(实验功能)',\n  'app.setting.content-width': '内容区域宽度',\n  'app.setting.content-width.fixed': '定宽',\n  'app.setting.content-width.fluid': '流式',\n  'app.setting.themecolor': '主题色',\n  'app.setting.themecolor.dust': '薄暮',\n  'app.setting.themecolor.volcano': '火山',\n  'app.setting.themecolor.sunset': '日暮',\n  'app.setting.themecolor.cyan': '明青',\n  'app.setting.themecolor.green': '极光绿',\n  'app.setting.themecolor.techBlue': '科技蓝（默认）',\n  'app.setting.themecolor.daybreak': '拂晓',\n  'app.setting.themecolor.geekblue': '极客蓝',\n  'app.setting.themecolor.purple': '酱紫',\n  'app.setting.navigationmode': '导航模式',\n  'app.setting.sidermenutype': '侧边菜单类型',\n  'app.setting.sidermenutype-sub': '经典模式',\n  'app.setting.sidermenutype-group': '分组模式',\n  'app.setting.regionalsettings': '内容区域',\n  'app.setting.regionalsettings.header': '顶栏',\n  'app.setting.regionalsettings.menu': '菜单',\n  'app.setting.regionalsettings.footer': '页脚',\n  'app.setting.regionalsettings.menuHeader': '菜单头',\n  'app.setting.sidemenu': '侧边菜单布局',\n  'app.setting.topmenu': '顶部菜单布局',\n  'app.setting.mixmenu': '混合菜单布局',\n  'app.setting.splitMenus': '自动分割菜单',\n  'app.setting.fixedheader': '固定 Header',\n  'app.setting.fixedsidebar': '固定侧边菜单',\n  'app.setting.fixedsidebar.hint': '侧边菜单布局时可配置',\n  'app.setting.hideheader': '下滑时隐藏 Header',\n  'app.setting.hideheader.hint': '固定 Header 时可配置',\n  'app.setting.othersettings': '其他设置',\n  'app.setting.weakmode': '色弱模式',\n  'app.setting.copy': '拷贝设置',\n  'app.setting.loading': '正在加载主题',\n  'app.setting.copyinfo': '拷贝成功，请到 src/defaultSettings.js 中替换默认配置',\n  'app.setting.production.hint': '配置栏只在开发环境用于预览，生产环境不会展现，请拷贝后手动修改配置文件'\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport settingDrawer from \"./zh-CN/settingDrawer\";\nexport default _objectSpread({}, settingDrawer);", "export default {\n  'app.setting.pagestyle': '整體風格設置',\n  'app.setting.pagestyle.dark': '暗色菜單風格',\n  'app.setting.pagestyle.realdark': '暗色風格(实验功能)',\n  'app.setting.pagestyle.light': '亮色菜單風格',\n  'app.setting.content-width': '內容區域寬度',\n  'app.setting.content-width.fixed': '定寬',\n  'app.setting.content-width.fluid': '流式',\n  'app.setting.themecolor': '主題色',\n  'app.setting.themecolor.dust': '薄暮',\n  'app.setting.themecolor.volcano': '火山',\n  'app.setting.themecolor.sunset': '日暮',\n  'app.setting.themecolor.cyan': '明青',\n  'app.setting.themecolor.green': '極光綠',\n  'app.setting.themecolor.techBlue': '科技蓝（默認）',\n  'app.setting.themecolor.daybreak': '拂曉藍',\n  'app.setting.themecolor.geekblue': '極客藍',\n  'app.setting.themecolor.purple': '醬紫',\n  'app.setting.navigationmode': '導航模式',\n  'app.setting.sidemenu': '側邊菜單布局',\n  'app.setting.topmenu': '頂部菜單布局',\n  'app.setting.mixmenu': '混合菜單布局',\n  'app.setting.splitMenus': '自动分割菜单',\n  'app.setting.fixedheader': '固定 Header',\n  'app.setting.fixedsidebar': '固定側邊菜單',\n  'app.setting.fixedsidebar.hint': '側邊菜單布局時可配置',\n  'app.setting.hideheader': '下滑時隱藏 Header',\n  'app.setting.hideheader.hint': '固定 Header 時可配置',\n  'app.setting.othersettings': '其他設置',\n  'app.setting.weakmode': '色弱模式',\n  'app.setting.copy': '拷貝設置',\n  'app.setting.loading': '正在加載主題',\n  'app.setting.copyinfo': '拷貝成功，請到 src/defaultSettings.js 中替換默認配置',\n  'app.setting.production.hint': '配置欄只在開發環境用於預覽，生產環境不會展現，請拷貝後手動修改配置文件'\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport settingDrawer from \"./zh-TW/settingDrawer\";\nexport default _objectSpread({}, settingDrawer);", "import { isBrowser } from '@ant-design/pro-utils';\nimport enUSLocal from \"./en-US\";\nimport itITLocal from \"./it-IT\";\nimport koKRLocal from \"./ko-KR\";\nimport zhLocal from \"./zh-CN\";\nimport zhTWLocal from \"./zh-TW\";\nvar locales = {\n  'zh-CN': zhLocal,\n  'zh-TW': zhTWLocal,\n  'en-US': enUSLocal,\n  'it-IT': itITLocal,\n  'ko-KR': koKRLocal\n};\nexport var getLanguage = function getLanguage() {\n  // support ssr\n  if (!isBrowser()) return 'zh-CN';\n  var lang = window.localStorage.getItem('umi_locale');\n  return lang || window.g_locale || navigator.language;\n};\nexport var gLocaleObject = function gLocaleObject() {\n  var gLocale = getLanguage();\n  return locales[gLocale] || locales['zh-CN'];\n};", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { useStyle as useAntdStyle } from '@ant-design/pro-provider';\nimport { version } from 'antd';\nvar getVersion = function getVersion() {\n  var _process;\n  if (typeof process === 'undefined') return version;\n  return ((_process = process) === null || _process === void 0 || (_process = _process.env) === null || _process === void 0 ? void 0 : _process.ANTD_VERSION) || version;\n};\n\n/**\n * 主要区别：\n * 需要手动引入 import 'antd/dist/antd.css';\n * 需要重置 menu 的样式\n * @param token\n * @returns\n */\nvar compatibleStyle = function compatibleStyle(token) {\n  var _getVersion, _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5, _token$layout6, _token$layout7, _token$layout8, _token$layout9, _token$layout10, _token$layout11, _token$layout12, _token$layout13, _token$layout14, _token$layout15, _token$layout16, _token$layout17, _token$layout18, _token$layout19, _$concat6, _token$layout20, _token$layout21, _token$layout22, _token$layout23, _token$layout24, _token$layout25, _token$layout26, _token$layout27, _token$layout28, _token$layout29, _token$layout30;\n  if ((_getVersion = getVersion()) !== null && _getVersion !== void 0 && _getVersion.startsWith('5')) {\n    return {};\n  }\n  return _defineProperty(_defineProperty(_defineProperty({}, token.componentCls, _defineProperty(_defineProperty({\n    width: '100%',\n    height: '100%'\n  }, \"\".concat(token.proComponentsCls, \"-base-menu\"), (_$concat6 = {\n    color: (_token$layout = token.layout) === null || _token$layout === void 0 || (_token$layout = _token$layout.sider) === null || _token$layout === void 0 ? void 0 : _token$layout.colorTextMenu\n  }, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_$concat6, \"\".concat(token.antCls, \"-menu-sub\"), {\n    backgroundColor: 'transparent!important',\n    color: (_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.sider) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.colorTextMenu\n  }), \"& \".concat(token.antCls, \"-layout\"), {\n    backgroundColor: 'transparent',\n    width: '100%'\n  }), \"\".concat(token.antCls, \"-menu-submenu-expand-icon, \").concat(token.antCls, \"-menu-submenu-arrow\"), {\n    color: 'inherit'\n  }), \"&\".concat(token.antCls, \"-menu\"), _defineProperty(_defineProperty({\n    color: (_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.sider) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorTextMenu\n  }, \"\".concat(token.antCls, \"-menu-item\"), {\n    '*': {\n      transition: 'none !important'\n    }\n  }), \"\".concat(token.antCls, \"-menu-item a\"), {\n    color: 'inherit'\n  })), \"&\".concat(token.antCls, \"-menu-inline\"), _defineProperty({}, \"\".concat(token.antCls, \"-menu-selected::after,\").concat(token.antCls, \"-menu-item-selected::after\"), {\n    display: 'none'\n  })), \"\".concat(token.antCls, \"-menu-sub \").concat(token.antCls, \"-menu-inline\"), {\n    backgroundColor: 'transparent!important'\n  }), \"\".concat(token.antCls, \"-menu-item:active, \\n        \").concat(token.antCls, \"-menu-submenu-title:active\"), {\n    backgroundColor: 'transparent!important'\n  }), \"&\".concat(token.antCls, \"-menu-light\"), _defineProperty({}, \"\".concat(token.antCls, \"-menu-item:hover, \\n            \").concat(token.antCls, \"-menu-item-active,\\n            \").concat(token.antCls, \"-menu-submenu-active, \\n            \").concat(token.antCls, \"-menu-submenu-title:hover\"), _defineProperty({\n    color: (_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.sider) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorTextMenuActive,\n    borderRadius: token.borderRadius\n  }, \"\".concat(token.antCls, \"-menu-submenu-arrow\"), {\n    color: (_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.sider) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorTextMenuActive\n  }))), \"&\".concat(token.antCls, \"-menu:not(\").concat(token.antCls, \"-menu-horizontal)\"), _defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-menu-item-selected\"), {\n    backgroundColor: (_token$layout6 = token.layout) === null || _token$layout6 === void 0 || (_token$layout6 = _token$layout6.sider) === null || _token$layout6 === void 0 ? void 0 : _token$layout6.colorBgMenuItemSelected,\n    borderRadius: token.borderRadius\n  }), \"\".concat(token.antCls, \"-menu-item:hover, \\n            \").concat(token.antCls, \"-menu-item-active,\\n            \").concat(token.antCls, \"-menu-submenu-title:hover\"), _defineProperty({\n    color: (_token$layout7 = token.layout) === null || _token$layout7 === void 0 || (_token$layout7 = _token$layout7.sider) === null || _token$layout7 === void 0 ? void 0 : _token$layout7.colorTextMenuActive,\n    borderRadius: token.borderRadius,\n    backgroundColor: \"\".concat((_token$layout8 = token.layout) === null || _token$layout8 === void 0 || (_token$layout8 = _token$layout8.header) === null || _token$layout8 === void 0 ? void 0 : _token$layout8.colorBgMenuItemHover, \" !important\")\n  }, \"\".concat(token.antCls, \"-menu-submenu-arrow\"), {\n    color: (_token$layout9 = token.layout) === null || _token$layout9 === void 0 || (_token$layout9 = _token$layout9.sider) === null || _token$layout9 === void 0 ? void 0 : _token$layout9.colorTextMenuActive\n  }))), \"\".concat(token.antCls, \"-menu-item-selected\"), {\n    color: (_token$layout10 = token.layout) === null || _token$layout10 === void 0 || (_token$layout10 = _token$layout10.sider) === null || _token$layout10 === void 0 ? void 0 : _token$layout10.colorTextMenuSelected\n  }), _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_$concat6, \"\".concat(token.antCls, \"-menu-submenu-selected\"), {\n    color: (_token$layout11 = token.layout) === null || _token$layout11 === void 0 || (_token$layout11 = _token$layout11.sider) === null || _token$layout11 === void 0 ? void 0 : _token$layout11.colorTextMenuSelected\n  }), \"&\".concat(token.antCls, \"-menu:not(\").concat(token.antCls, \"-menu-inline) \").concat(token.antCls, \"-menu-submenu-open\"), {\n    color: (_token$layout12 = token.layout) === null || _token$layout12 === void 0 || (_token$layout12 = _token$layout12.sider) === null || _token$layout12 === void 0 ? void 0 : _token$layout12.colorTextMenuSelected\n  }), \"&\".concat(token.antCls, \"-menu-vertical\"), _defineProperty({}, \"\".concat(token.antCls, \"-menu-submenu-selected\"), {\n    borderRadius: token.borderRadius,\n    color: (_token$layout13 = token.layout) === null || _token$layout13 === void 0 || (_token$layout13 = _token$layout13.sider) === null || _token$layout13 === void 0 ? void 0 : _token$layout13.colorTextMenuSelected\n  })), \"\".concat(token.antCls, \"-menu-submenu:hover > \").concat(token.antCls, \"-menu-submenu-title > \").concat(token.antCls, \"-menu-submenu-arrow\"), {\n    color: (_token$layout14 = token.layout) === null || _token$layout14 === void 0 || (_token$layout14 = _token$layout14.sider) === null || _token$layout14 === void 0 ? void 0 : _token$layout14.colorTextMenuActive\n  }), \"&\".concat(token.antCls, \"-menu-horizontal\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-menu-item:hover,\\n          \").concat(token.antCls, \"-menu-submenu:hover,\\n          \").concat(token.antCls, \"-menu-item-active,\\n          \").concat(token.antCls, \"-menu-submenu-active\"), {\n    borderRadius: 4,\n    transition: 'none',\n    color: (_token$layout15 = token.layout) === null || _token$layout15 === void 0 || (_token$layout15 = _token$layout15.header) === null || _token$layout15 === void 0 ? void 0 : _token$layout15.colorTextMenuActive,\n    backgroundColor: \"\".concat((_token$layout16 = token.layout) === null || _token$layout16 === void 0 || (_token$layout16 = _token$layout16.header) === null || _token$layout16 === void 0 ? void 0 : _token$layout16.colorBgMenuItemHover, \" !important\")\n  }), \"\".concat(token.antCls, \"-menu-item-open,\\n          \").concat(token.antCls, \"-menu-submenu-open,\\n          \").concat(token.antCls, \"-menu-item-selected,\\n          \").concat(token.antCls, \"-menu-submenu-selected\"), _defineProperty({\n    backgroundColor: (_token$layout17 = token.layout) === null || _token$layout17 === void 0 || (_token$layout17 = _token$layout17.header) === null || _token$layout17 === void 0 ? void 0 : _token$layout17.colorBgMenuItemSelected,\n    borderRadius: token.borderRadius,\n    transition: 'none',\n    color: \"\".concat((_token$layout18 = token.layout) === null || _token$layout18 === void 0 || (_token$layout18 = _token$layout18.header) === null || _token$layout18 === void 0 ? void 0 : _token$layout18.colorTextMenuSelected, \" !important\")\n  }, \"\".concat(token.antCls, \"-menu-submenu-arrow\"), {\n    color: \"\".concat((_token$layout19 = token.layout) === null || _token$layout19 === void 0 || (_token$layout19 = _token$layout19.header) === null || _token$layout19 === void 0 ? void 0 : _token$layout19.colorTextMenuSelected, \" !important\")\n  })), \"> \".concat(token.antCls, \"-menu-item, > \").concat(token.antCls, \"-menu-submenu\"), {\n    paddingInline: 16,\n    marginInline: 4\n  }), \"> \".concat(token.antCls, \"-menu-item::after, > \").concat(token.antCls, \"-menu-submenu::after\"), {\n    display: 'none'\n  })))), \"\".concat(token.proComponentsCls, \"-top-nav-header-base-menu\"), _defineProperty(_defineProperty({}, \"&\".concat(token.antCls, \"-menu\"), _defineProperty({\n    color: (_token$layout20 = token.layout) === null || _token$layout20 === void 0 || (_token$layout20 = _token$layout20.header) === null || _token$layout20 === void 0 ? void 0 : _token$layout20.colorTextMenu\n  }, \"\".concat(token.antCls, \"-menu-item a\"), {\n    color: 'inherit'\n  })), \"&\".concat(token.antCls, \"-menu-light\"), _defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-menu-item:hover, \\n            \").concat(token.antCls, \"-menu-item-active,\\n            \").concat(token.antCls, \"-menu-submenu-active, \\n            \").concat(token.antCls, \"-menu-submenu-title:hover\"), _defineProperty({\n    color: (_token$layout21 = token.layout) === null || _token$layout21 === void 0 || (_token$layout21 = _token$layout21.header) === null || _token$layout21 === void 0 ? void 0 : _token$layout21.colorTextMenuActive,\n    borderRadius: token.borderRadius,\n    transition: 'none',\n    backgroundColor: (_token$layout22 = token.layout) === null || _token$layout22 === void 0 || (_token$layout22 = _token$layout22.header) === null || _token$layout22 === void 0 ? void 0 : _token$layout22.colorBgMenuItemSelected\n  }, \"\".concat(token.antCls, \"-menu-submenu-arrow\"), {\n    color: (_token$layout23 = token.layout) === null || _token$layout23 === void 0 || (_token$layout23 = _token$layout23.header) === null || _token$layout23 === void 0 ? void 0 : _token$layout23.colorTextMenuActive\n  })), \"\".concat(token.antCls, \"-menu-item-selected\"), {\n    color: (_token$layout24 = token.layout) === null || _token$layout24 === void 0 || (_token$layout24 = _token$layout24.header) === null || _token$layout24 === void 0 ? void 0 : _token$layout24.colorTextMenuSelected,\n    borderRadius: token.borderRadius,\n    backgroundColor: (_token$layout25 = token.layout) === null || _token$layout25 === void 0 || (_token$layout25 = _token$layout25.header) === null || _token$layout25 === void 0 ? void 0 : _token$layout25.colorBgMenuItemSelected\n  })))), \"\".concat(token.antCls, \"-menu-sub\").concat(token.antCls, \"-menu-inline\"), {\n    backgroundColor: 'transparent!important'\n  }), \"\".concat(token.antCls, \"-menu-submenu-popup\"), _defineProperty(_defineProperty(_defineProperty(_defineProperty({\n    backgroundColor: 'rgba(255, 255, 255, 0.42)',\n    '-webkit-backdrop-filter': 'blur(8px)',\n    backdropFilter: 'blur(8px)'\n  }, \"\".concat(token.antCls, \"-menu\"), _defineProperty({\n    background: 'transparent !important',\n    backgroundColor: 'transparent !important'\n  }, \"\".concat(token.antCls, \"-menu-item:active, \\n        \").concat(token.antCls, \"-menu-submenu-title:active\"), {\n    backgroundColor: 'transparent!important'\n  })), \"\".concat(token.antCls, \"-menu-item-selected\"), {\n    color: (_token$layout26 = token.layout) === null || _token$layout26 === void 0 || (_token$layout26 = _token$layout26.sider) === null || _token$layout26 === void 0 ? void 0 : _token$layout26.colorTextMenuSelected\n  }), \"\".concat(token.antCls, \"-menu-submenu-selected\"), {\n    color: (_token$layout27 = token.layout) === null || _token$layout27 === void 0 || (_token$layout27 = _token$layout27.sider) === null || _token$layout27 === void 0 ? void 0 : _token$layout27.colorTextMenuSelected\n  }), \"\".concat(token.antCls, \"-menu:not(\").concat(token.antCls, \"-menu-horizontal)\"), _defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-menu-item-selected\"), {\n    backgroundColor: 'rgba(0, 0, 0, 0.04)',\n    borderRadius: token.borderRadius,\n    color: (_token$layout28 = token.layout) === null || _token$layout28 === void 0 || (_token$layout28 = _token$layout28.sider) === null || _token$layout28 === void 0 ? void 0 : _token$layout28.colorTextMenuSelected\n  }), \"\".concat(token.antCls, \"-menu-item:hover, \\n          \").concat(token.antCls, \"-menu-item-active,\\n          \").concat(token.antCls, \"-menu-submenu-title:hover\"), _defineProperty({\n    color: (_token$layout29 = token.layout) === null || _token$layout29 === void 0 || (_token$layout29 = _token$layout29.sider) === null || _token$layout29 === void 0 ? void 0 : _token$layout29.colorTextMenuActive,\n    borderRadius: token.borderRadius\n  }, \"\".concat(token.antCls, \"-menu-submenu-arrow\"), {\n    color: (_token$layout30 = token.layout) === null || _token$layout30 === void 0 || (_token$layout30 = _token$layout30.sider) === null || _token$layout30 === void 0 ? void 0 : _token$layout30.colorTextMenuActive\n  }))));\n};\nvar genProLayoutStyle = function genProLayoutStyle(token) {\n  var _token$layout31, _token$layout32, _token$layout33, _token$layout34;\n  return _defineProperty(_defineProperty({}, \"\".concat(token.antCls, \"-layout\"), {\n    backgroundColor: 'transparent !important'\n  }), token.componentCls, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"& \".concat(token.antCls, \"-layout\"), {\n    display: 'flex',\n    backgroundColor: 'transparent',\n    width: '100%'\n  }), \"\".concat(token.componentCls, \"-content\"), {\n    display: 'flex',\n    flexDirection: 'column',\n    width: '100%',\n    backgroundColor: ((_token$layout31 = token.layout) === null || _token$layout31 === void 0 || (_token$layout31 = _token$layout31.pageContainer) === null || _token$layout31 === void 0 ? void 0 : _token$layout31.colorBgPageContainer) || 'transparent',\n    position: 'relative',\n    paddingBlock: (_token$layout32 = token.layout) === null || _token$layout32 === void 0 || (_token$layout32 = _token$layout32.pageContainer) === null || _token$layout32 === void 0 ? void 0 : _token$layout32.paddingBlockPageContainerContent,\n    paddingInline: (_token$layout33 = token.layout) === null || _token$layout33 === void 0 || (_token$layout33 = _token$layout33.pageContainer) === null || _token$layout33 === void 0 ? void 0 : _token$layout33.paddingInlinePageContainerContent,\n    '&-has-page-container': {\n      padding: 0\n    }\n  }), \"\".concat(token.componentCls, \"-container\"), {\n    width: '100%',\n    display: 'flex',\n    flexDirection: 'column',\n    minWidth: 0,\n    minHeight: 0,\n    backgroundColor: 'transparent'\n  }), \"\".concat(token.componentCls, \"-bg-list\"), {\n    pointerEvents: 'none',\n    position: 'fixed',\n    overflow: 'hidden',\n    insetBlockStart: 0,\n    insetInlineStart: 0,\n    zIndex: 0,\n    height: '100%',\n    width: '100%',\n    background: (_token$layout34 = token.layout) === null || _token$layout34 === void 0 ? void 0 : _token$layout34.bgLayout\n  }));\n};\nexport function useStyle(prefixCls) {\n  return useAntdStyle('ProLayout', function (token) {\n    var proLayoutToken = _objectSpread(_objectSpread({}, token), {}, {\n      componentCls: \".\".concat(prefixCls)\n    });\n    return [genProLayoutStyle(proLayoutToken), compatibleStyle(proLayoutToken)];\n  });\n}", "// /userInfo/2144/id => ['/userInfo','/userInfo/2144,'/userInfo/2144/id']\nexport function urlToList(url) {\n  if (!url || url === '/') {\n    return ['/'];\n  }\n  var urlList = url.split('/').filter(function (i) {\n    return i;\n  });\n  return urlList.map(function (urlItem, index) {\n    return \"/\".concat(urlList.slice(0, index + 1).join('/'));\n  });\n}", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { compareVersions } from '@ant-design/pro-utils';\nimport { version } from 'antd';\nimport { match } from 'path-to-regexp';\nimport { urlToList } from \"./pathTools\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport var getVersion = function getVersion() {\n  var _process;\n  if (typeof process === 'undefined') return version;\n  return ((_process = process) === null || _process === void 0 || (_process = _process.env) === null || _process === void 0 ? void 0 : _process.ANTD_VERSION) || version;\n};\n// 渲染 Breadcrumb 子节点\n// Render the Breadcrumb child node\nvar defaultItemRender = function defaultItemRender(route, _, routes) {\n  var _ref = route,\n    breadcrumbName = _ref.breadcrumbName,\n    title = _ref.title,\n    path = _ref.path;\n  var last = routes.findIndex(function (i) {\n    return (\n      // @ts-ignore\n      i.linkPath === route.path\n    );\n  }) === routes.length - 1;\n  return last ? /*#__PURE__*/_jsx(\"span\", {\n    children: title || breadcrumbName\n  }) : /*#__PURE__*/_jsx(\"span\", {\n    onClick: path ? function () {\n      return location.href = path;\n    } : undefined,\n    children: title || breadcrumbName\n  });\n};\nvar renderItemLocal = function renderItemLocal(item, props) {\n  var formatMessage = props.formatMessage,\n    menu = props.menu;\n  if (item.locale && formatMessage && (menu === null || menu === void 0 ? void 0 : menu.locale) !== false) {\n    return formatMessage({\n      id: item.locale,\n      defaultMessage: item.name\n    });\n  }\n  return item.name;\n};\nexport var getBreadcrumb = function getBreadcrumb(breadcrumbMap, url) {\n  var breadcrumbItem = breadcrumbMap.get(url);\n  if (!breadcrumbItem) {\n    // Find the first matching path in the order defined by route config\n    // 按照 route config 定义的顺序找到第一个匹配的路径\n    var keys = Array.from(breadcrumbMap.keys()) || [];\n    var targetPath = keys.find(function (path) {\n      try {\n        if (path !== null && path !== void 0 && path.startsWith('http')) return false;\n        return match(path.replace('?', ''))(url);\n      } catch (error) {\n        console.log('path', path, error);\n        return false;\n      }\n    }\n    // remove ? ,不然会重复\n    );\n    if (targetPath) breadcrumbItem = breadcrumbMap.get(targetPath);\n  }\n  return breadcrumbItem || {\n    path: ''\n  };\n};\nexport var getBreadcrumbFromProps = function getBreadcrumbFromProps(props) {\n  var location = props.location,\n    breadcrumbMap = props.breadcrumbMap;\n  return {\n    location: location,\n    breadcrumbMap: breadcrumbMap\n  };\n};\nvar conversionFromLocation = function conversionFromLocation(routerLocation, breadcrumbMap, props) {\n  // Convertor the url to an array\n  var pathSnippets = urlToList(routerLocation === null || routerLocation === void 0 ? void 0 : routerLocation.pathname);\n  // Loop data mosaic routing\n  var extraBreadcrumbItems = pathSnippets.map(function (url) {\n    var currentBreadcrumb = getBreadcrumb(breadcrumbMap, url);\n    var name = renderItemLocal(currentBreadcrumb, props);\n    var hideInBreadcrumb = currentBreadcrumb.hideInBreadcrumb;\n    return name && !hideInBreadcrumb ? {\n      linkPath: url,\n      breadcrumbName: name,\n      title: name,\n      component: currentBreadcrumb.component\n    } : {\n      linkPath: '',\n      breadcrumbName: '',\n      title: ''\n    };\n  }).filter(function (item) {\n    return item && item.linkPath;\n  });\n  return extraBreadcrumbItems;\n};\n/** 将参数转化为面包屑 Convert parameters into breadcrumbs */\nexport var genBreadcrumbProps = function genBreadcrumbProps(props) {\n  var _getBreadcrumbFromPro = getBreadcrumbFromProps(props),\n    location = _getBreadcrumbFromPro.location,\n    breadcrumbMap = _getBreadcrumbFromPro.breadcrumbMap;\n\n  // 根据 location 生成 面包屑\n  // Generate breadcrumbs based on location\n  if (location && location.pathname && breadcrumbMap) {\n    return conversionFromLocation(location, breadcrumbMap, props);\n  }\n  return [];\n};\n\n// 声明一个导出函数，接收两个参数：BreadcrumbProps和ProLayoutProps，返回一个BreadcrumbListReturn类型的对象\nexport var getBreadcrumbProps = function getBreadcrumbProps(props, layoutPros // ProLayoutProps类型的layoutPros\n) {\n  // 解构赋值获取props中的breadcrumbRender和props中的itemRender，如果props中没有itemRender则使用默认的defaultItemRender函数\n  var breadcrumbRender = props.breadcrumbRender,\n    propsItemRender = props.itemRender;\n  // 解构赋值获取layoutPros.breadcrumbProps.minLenght的值，如果没有设置，则默认为2\n  var _ref2 = layoutPros.breadcrumbProps || {},\n    _ref2$minLength = _ref2.minLength,\n    minLength = _ref2$minLength === void 0 ? 2 : _ref2$minLength;\n  // 生成面包屑的路由数组，该数组中包含菜单项和面包屑项\n  var routesArray = genBreadcrumbProps(props);\n  // 如果props中有itemRender，则使用props中的itemRender，否则使用默认函数defaultItemRender\n  var itemRender = function itemRender(item) {\n    var renderFunction = propsItemRender || defaultItemRender;\n    for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      rest[_key - 1] = arguments[_key];\n    }\n    return renderFunction === null || renderFunction === void 0 ? void 0 : renderFunction.apply(void 0, [_objectSpread(_objectSpread({}, item), {}, {\n      // 如果item.linkPath存在，则使用item.linkPath，否则使用item.path\n      // @ts-ignore\n      path: item.linkPath || item.path\n    })].concat(rest));\n  };\n  var items = routesArray;\n  // 如果面包屑渲染函数breadcrumbRender存在，则使用其渲染数组items\n  if (breadcrumbRender) {\n    items = breadcrumbRender(items || []) || undefined;\n  }\n  // 如果items（渲染后的数组）的长度小于minLength或者breadcrumbRender为false，则items为undefined\n  if (items && items.length < minLength || breadcrumbRender === false) {\n    items = undefined;\n  }\n  // 如果当前 ant design 包的版本大于等于5.3.0，则返回一个对象{items,itemRender},否则返回一个对象{routes:item,itemRender}\n  return compareVersions(getVersion(), '5.3.0') > -1 ? {\n    items: items,\n    itemRender: itemRender\n  } : {\n    routes: items,\n    itemRender: itemRender\n  };\n};", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport { transformRoute } from '@umijs/route-utils';\nfunction fromEntries(iterable) {\n  return _toConsumableArray(iterable).reduce(function (obj, _ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      key = _ref2[0],\n      val = _ref2[1];\n    // eslint-disable-next-line no-param-reassign\n    obj[key] = val;\n    return obj;\n  }, {});\n}\nvar getMenuData = function getMenuData(routes, menu, formatMessage, menuDataRender) {\n  var _transformRoute = transformRoute(routes, (menu === null || menu === void 0 ? void 0 : menu.locale) || false, formatMessage, true),\n    menuData = _transformRoute.menuData,\n    breadcrumb = _transformRoute.breadcrumb;\n  if (!menuDataRender) {\n    return {\n      breadcrumb: fromEntries(breadcrumb),\n      breadcrumbMap: breadcrumb,\n      menuData: menuData\n    };\n  }\n  return getMenuData(menuDataRender(menuData), menu, formatMessage, undefined);\n};\nexport { getMenuData };", "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { omitUndefined } from '@ant-design/pro-utils';\nimport { useEffect, useState } from 'react';\nvar useCurrentMenuLayoutProps = function useCurrentMenuLayoutProps(currentMenu) {\n  var _useState = useState({}),\n    _useState2 = _slicedToArray(_useState, 2),\n    currentMenuLayoutProps = _useState2[0],\n    setCurrentMenuLayoutProps = _useState2[1];\n  useEffect(function () {\n    setCurrentMenuLayoutProps(omitUndefined({\n      // 有时候会变成对象，是原来的方式\n      layout: _typeof(currentMenu.layout) !== 'object' ? currentMenu.layout : undefined,\n      navTheme: currentMenu.navTheme,\n      menuRender: currentMenu.menuRender,\n      footerRender: currentMenu.footerRender,\n      menuHeaderRender: currentMenu.menuHeaderRender,\n      headerRender: currentMenu.headerRender,\n      fixSiderbar: currentMenu.fixSiderbar\n    }));\n  }, [currentMenu.layout, currentMenu.navTheme, currentMenu.menuRender, currentMenu.footerRender, currentMenu.menuHeaderRender, currentMenu.headerRender, currentMenu.fixSiderbar]);\n  return currentMenuLayoutProps;\n};\nexport { useCurrentMenuLayoutProps };", "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nvar _excluded = [\"id\", \"defaultMessage\"],\n  _excluded2 = [\"fixSiderbar\", \"navTheme\", \"layout\"];\nimport { ProConfigProvider, ProProvider, isNeedOpenHash } from '@ant-design/pro-provider';\nimport { coverToNewToken, isBrowser, useBreakpoint, useDocumentTitle, useMountMergeState } from '@ant-design/pro-utils';\nimport { getMatchMenu } from '@umijs/route-utils';\nimport { ConfigProvider, Layout } from 'antd';\nimport classNames from 'classnames';\nimport Omit from 'omit.js';\nimport useMergedState from \"rc-util/es/hooks/useMergedState\";\nimport warning from \"rc-util/es/warning\";\nimport React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';\nimport useSWR, { useSWRConfig } from 'swr';\nimport { WrapContent } from \"./WrapContent\";\nimport { Logo } from \"./assert/Logo\";\nimport { DefaultFooter as Footer } from \"./components/Footer\";\nimport { DefaultHeader as Header } from \"./components/Header\";\nimport { PageLoading } from \"./components/PageLoading\";\nimport { SiderMenu } from \"./components/SiderMenu\";\nimport { RouteContext } from \"./context/RouteContext\";\nimport { defaultSettings } from \"./defaultSettings\";\nimport { getPageTitleInfo } from \"./getPageTitle\";\nimport { gLocaleObject } from \"./locales\";\nimport { useStyle } from \"./style\";\nimport { getBreadcrumbProps } from \"./utils/getBreadcrumbProps\";\nimport { getMenuData } from \"./utils/getMenuData\";\nimport { useCurrentMenuLayoutProps } from \"./utils/useCurrentMenuLayoutProps\";\nimport { clearMenuItem } from \"./utils/utils\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nvar layoutIndex = 0;\nvar headerRender = function headerRender(props, matchMenuKeys) {\n  var _props$stylish;\n  if (props.headerRender === false || props.pure) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(Header, _objectSpread(_objectSpread({\n    matchMenuKeys: matchMenuKeys\n  }, props), {}, {\n    stylish: (_props$stylish = props.stylish) === null || _props$stylish === void 0 ? void 0 : _props$stylish.header\n  }));\n};\nvar footerRender = function footerRender(props) {\n  if (props.footerRender === false || props.pure) {\n    return null;\n  }\n  if (props.footerRender) {\n    return props.footerRender(_objectSpread({}, props), /*#__PURE__*/_jsx(Footer, {}));\n  }\n  return null;\n};\nvar renderSiderMenu = function renderSiderMenu(props, matchMenuKeys) {\n  var _props$stylish3;\n  var layout = props.layout,\n    isMobile = props.isMobile,\n    selectedKeys = props.selectedKeys,\n    openKeys = props.openKeys,\n    splitMenus = props.splitMenus,\n    suppressSiderWhenMenuEmpty = props.suppressSiderWhenMenuEmpty,\n    menuRender = props.menuRender;\n  if (props.menuRender === false || props.pure) {\n    return null;\n  }\n  var menuData = props.menuData;\n\n  /** 如果是分割菜单模式，需要专门实现一下 */\n  if (splitMenus && (openKeys !== false || layout === 'mix') && !isMobile) {\n    var _ref = selectedKeys || matchMenuKeys,\n      _ref2 = _slicedToArray(_ref, 1),\n      key = _ref2[0];\n    if (key) {\n      var _props$menuData;\n      menuData = ((_props$menuData = props.menuData) === null || _props$menuData === void 0 || (_props$menuData = _props$menuData.find(function (item) {\n        return item.key === key;\n      })) === null || _props$menuData === void 0 ? void 0 : _props$menuData.children) || [];\n    } else {\n      menuData = [];\n    }\n  }\n  // 这里走了可以少一次循环\n  var clearMenuData = clearMenuItem(menuData || []);\n  if (clearMenuData && (clearMenuData === null || clearMenuData === void 0 ? void 0 : clearMenuData.length) < 1 && (splitMenus || suppressSiderWhenMenuEmpty)) {\n    return null;\n  }\n  if (layout === 'top' && !isMobile) {\n    var _props$stylish2;\n    return /*#__PURE__*/_jsx(SiderMenu, _objectSpread(_objectSpread({\n      matchMenuKeys: matchMenuKeys\n    }, props), {}, {\n      hide: true,\n      stylish: (_props$stylish2 = props.stylish) === null || _props$stylish2 === void 0 ? void 0 : _props$stylish2.sider\n    }));\n  }\n  var defaultDom = /*#__PURE__*/_jsx(SiderMenu, _objectSpread(_objectSpread({\n    matchMenuKeys: matchMenuKeys\n  }, props), {}, {\n    // 这里走了可以少一次循环\n    menuData: clearMenuData,\n    stylish: (_props$stylish3 = props.stylish) === null || _props$stylish3 === void 0 ? void 0 : _props$stylish3.sider\n  }));\n  if (menuRender) {\n    return menuRender(props, defaultDom);\n  }\n  return defaultDom;\n};\nvar defaultPageTitleRender = function defaultPageTitleRender(pageProps, props) {\n  var pageTitleRender = props.pageTitleRender;\n  var pageTitleInfo = getPageTitleInfo(pageProps);\n  if (pageTitleRender === false) {\n    return {\n      title: props.title || '',\n      id: '',\n      pageName: ''\n    };\n  }\n  if (pageTitleRender) {\n    var title = pageTitleRender(pageProps, pageTitleInfo.title, pageTitleInfo);\n    if (typeof title === 'string') {\n      return getPageTitleInfo(_objectSpread(_objectSpread({}, pageTitleInfo), {}, {\n        title: title\n      }));\n    }\n    warning(typeof title === 'string', 'pro-layout: renderPageTitle return value should be a string');\n  }\n  return pageTitleInfo;\n};\nvar getPaddingInlineStart = function getPaddingInlineStart(hasLeftPadding, collapsed, siderWidth) {\n  if (hasLeftPadding) {\n    return collapsed ? 64 : siderWidth;\n  }\n  return 0;\n};\n\n/**\n * 🌃 Powerful and easy to use beautiful layout 🏄‍ Support multiple topics and layout types\n *\n * @param props\n */\nvar BaseProLayout = function BaseProLayout(props) {\n  var _props$prefixCls, _location$pathname, _token$layout, _token$layout2, _token$layout3, _token$layout4, _token$layout5, _token$layout6, _token$layout7, _token$layout8, _token$layout9, _token$layout10, _token$layout11, _token$layout12;\n  var _ref3 = props || {},\n    children = _ref3.children,\n    propsOnCollapse = _ref3.onCollapse,\n    _ref3$location = _ref3.location,\n    location = _ref3$location === void 0 ? {\n      pathname: '/'\n    } : _ref3$location,\n    contentStyle = _ref3.contentStyle,\n    route = _ref3.route,\n    defaultCollapsed = _ref3.defaultCollapsed,\n    style = _ref3.style,\n    propsSiderWidth = _ref3.siderWidth,\n    menu = _ref3.menu,\n    siderMenuType = _ref3.siderMenuType,\n    propsIsChildrenLayout = _ref3.isChildrenLayout,\n    menuDataRender = _ref3.menuDataRender,\n    actionRef = _ref3.actionRef,\n    bgLayoutImgList = _ref3.bgLayoutImgList,\n    propsFormatMessage = _ref3.formatMessage,\n    loading = _ref3.loading;\n  var siderWidth = useMemo(function () {\n    if (propsSiderWidth) return propsSiderWidth;\n    if (props.layout === 'mix') return 215;\n    return 256;\n  }, [props.layout, propsSiderWidth]);\n  var context = useContext(ConfigProvider.ConfigContext);\n  var prefixCls = (_props$prefixCls = props.prefixCls) !== null && _props$prefixCls !== void 0 ? _props$prefixCls : context.getPrefixCls('pro');\n  var _useMountMergeState = useMountMergeState(false, {\n      value: menu === null || menu === void 0 ? void 0 : menu.loading,\n      onChange: menu === null || menu === void 0 ? void 0 : menu.onLoadingChange\n    }),\n    _useMountMergeState2 = _slicedToArray(_useMountMergeState, 2),\n    menuLoading = _useMountMergeState2[0],\n    setMenuLoading = _useMountMergeState2[1];\n\n  // give a default key for swr\n  var _useState = useState(function () {\n      layoutIndex += 1;\n      return \"pro-layout-\".concat(layoutIndex);\n    }),\n    _useState2 = _slicedToArray(_useState, 1),\n    defaultId = _useState2[0];\n\n  /**\n   * 处理国际化相关 formatMessage\n   * 如果有用户配置的以用户为主\n   * 如果没有用自己实现的\n   */\n  var formatMessage = useCallback(function (_ref4) {\n    var id = _ref4.id,\n      defaultMessage = _ref4.defaultMessage,\n      restParams = _objectWithoutProperties(_ref4, _excluded);\n    if (propsFormatMessage) {\n      return propsFormatMessage(_objectSpread({\n        id: id,\n        defaultMessage: defaultMessage\n      }, restParams));\n    }\n    var locales = gLocaleObject();\n    return locales[id] ? locales[id] : defaultMessage;\n  }, [propsFormatMessage]);\n  var _useSWR = useSWR([defaultId, menu === null || menu === void 0 ? void 0 : menu.params], /*#__PURE__*/function () {\n      var _ref6 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(_ref5) {\n        var _menu$request;\n        var _ref7, params, menuDataItems;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _ref7 = _slicedToArray(_ref5, 2), params = _ref7[1];\n              setMenuLoading(true);\n              _context.next = 4;\n              return menu === null || menu === void 0 || (_menu$request = menu.request) === null || _menu$request === void 0 ? void 0 : _menu$request.call(menu, params || {}, (route === null || route === void 0 ? void 0 : route.children) || (route === null || route === void 0 ? void 0 : route.routes) || []);\n            case 4:\n              menuDataItems = _context.sent;\n              setMenuLoading(false);\n              return _context.abrupt(\"return\", menuDataItems);\n            case 7:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function (_x) {\n        return _ref6.apply(this, arguments);\n      };\n    }(), {\n      revalidateOnFocus: false,\n      shouldRetryOnError: false,\n      revalidateOnReconnect: false\n    }),\n    data = _useSWR.data,\n    mutate = _useSWR.mutate,\n    isLoading = _useSWR.isLoading;\n  useEffect(function () {\n    setMenuLoading(isLoading);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isLoading]);\n  var _useSWRConfig = useSWRConfig(),\n    cache = _useSWRConfig.cache;\n  useEffect(function () {\n    return function () {\n      if (cache instanceof Map) cache.delete(defaultId);\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  var menuInfoData = useMemo(function () {\n    return getMenuData(data || (route === null || route === void 0 ? void 0 : route.children) || (route === null || route === void 0 ? void 0 : route.routes) || [], menu, formatMessage, menuDataRender);\n  }, [formatMessage, menu, menuDataRender, data, route === null || route === void 0 ? void 0 : route.children, route === null || route === void 0 ? void 0 : route.routes]);\n  var _ref8 = menuInfoData || {},\n    breadcrumb = _ref8.breadcrumb,\n    breadcrumbMap = _ref8.breadcrumbMap,\n    _ref8$menuData = _ref8.menuData,\n    menuData = _ref8$menuData === void 0 ? [] : _ref8$menuData;\n  if (actionRef && menu !== null && menu !== void 0 && menu.request) {\n    actionRef.current = {\n      reload: function reload() {\n        mutate();\n      }\n    };\n  }\n  var matchMenus = useMemo(function () {\n    return getMatchMenu(location.pathname || '/', menuData || [], true);\n  }, [location.pathname, menuData]);\n  var matchMenuKeys = useMemo(function () {\n    return Array.from(new Set(matchMenus.map(function (item) {\n      return item.key || item.path || '';\n    })));\n  }, [matchMenus]);\n\n  // 当前选中的menu，一般不会为空\n  var currentMenu = matchMenus[matchMenus.length - 1] || {};\n  var currentMenuLayoutProps = useCurrentMenuLayoutProps(currentMenu);\n  var _props$currentMenuLay = _objectSpread(_objectSpread({}, props), currentMenuLayoutProps),\n    fixSiderbar = _props$currentMenuLay.fixSiderbar,\n    navTheme = _props$currentMenuLay.navTheme,\n    propsLayout = _props$currentMenuLay.layout,\n    rest = _objectWithoutProperties(_props$currentMenuLay, _excluded2);\n  var colSize = useBreakpoint();\n  var isMobile = useMemo(function () {\n    return (colSize === 'sm' || colSize === 'xs') && !props.disableMobile;\n  }, [colSize, props.disableMobile]);\n\n  // If it is a fix menu, calculate padding\n  // don't need padding in phone mode\n  /* Checking if the menu is loading and if it is, it will return a skeleton loading screen. */\n  var hasLeftPadding = propsLayout !== 'top' && !isMobile;\n  var _useMergedState = useMergedState(function () {\n      if (defaultCollapsed !== undefined) return defaultCollapsed;\n      if (process.env.NODE_ENV === 'TEST') return false;\n      if (isMobile) return true;\n      if (colSize === 'md') return true;\n      return false;\n    }, {\n      value: props.collapsed,\n      onChange: propsOnCollapse\n    }),\n    _useMergedState2 = _slicedToArray(_useMergedState, 2),\n    collapsed = _useMergedState2[0],\n    onCollapse = _useMergedState2[1];\n\n  // Splicing parameters, adding menuData and formatMessage in props\n  var defaultProps = Omit(_objectSpread(_objectSpread(_objectSpread({\n    prefixCls: prefixCls\n  }, props), {}, {\n    siderWidth: siderWidth\n  }, currentMenuLayoutProps), {}, {\n    formatMessage: formatMessage,\n    breadcrumb: breadcrumb,\n    menu: _objectSpread(_objectSpread({}, menu), {}, {\n      type: siderMenuType || (menu === null || menu === void 0 ? void 0 : menu.type),\n      loading: menuLoading\n    }),\n    layout: propsLayout\n  }), ['className', 'style', 'breadcrumbRender']);\n\n  // gen page title\n  var pageTitleInfo = defaultPageTitleRender(_objectSpread(_objectSpread({\n    pathname: location.pathname\n  }, defaultProps), {}, {\n    breadcrumbMap: breadcrumbMap\n  }), props);\n\n  // gen breadcrumbProps, parameter for pageHeader\n  var breadcrumbProps = getBreadcrumbProps(_objectSpread(_objectSpread({}, defaultProps), {}, {\n    breadcrumbRender: props.breadcrumbRender,\n    breadcrumbMap: breadcrumbMap\n  }), props);\n\n  // render sider dom\n  var siderMenuDom = renderSiderMenu(_objectSpread(_objectSpread({}, defaultProps), {}, {\n    menuData: menuData,\n    onCollapse: onCollapse,\n    isMobile: isMobile,\n    collapsed: collapsed\n  }), matchMenuKeys);\n\n  // render header dom\n  var headerDom = headerRender(_objectSpread(_objectSpread({}, defaultProps), {}, {\n    children: null,\n    hasSiderMenu: !!siderMenuDom,\n    menuData: menuData,\n    isMobile: isMobile,\n    collapsed: collapsed,\n    onCollapse: onCollapse\n  }), matchMenuKeys);\n\n  // render footer dom\n  var footerDom = footerRender(_objectSpread({\n    isMobile: isMobile,\n    collapsed: collapsed\n  }, defaultProps));\n  var _useContext = useContext(RouteContext),\n    contextIsChildrenLayout = _useContext.isChildrenLayout;\n\n  // 如果 props 中定义，以 props 为准\n  var isChildrenLayout = propsIsChildrenLayout !== undefined ? propsIsChildrenLayout : contextIsChildrenLayout;\n  var proLayoutClassName = \"\".concat(prefixCls, \"-layout\");\n  var _useStyle = useStyle(proLayoutClassName),\n    wrapSSR = _useStyle.wrapSSR,\n    hashId = _useStyle.hashId;\n\n  // gen className\n  var className = classNames(props.className, hashId, 'ant-design-pro', proLayoutClassName, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"screen-\".concat(colSize), colSize), \"\".concat(proLayoutClassName, \"-top-menu\"), propsLayout === 'top'), \"\".concat(proLayoutClassName, \"-is-children\"), isChildrenLayout), \"\".concat(proLayoutClassName, \"-fix-siderbar\"), fixSiderbar), \"\".concat(proLayoutClassName, \"-\").concat(propsLayout), propsLayout));\n\n  /** 计算 slider 的宽度 */\n  var leftSiderWidth = getPaddingInlineStart(!!hasLeftPadding, collapsed, siderWidth);\n\n  // siderMenuDom 为空的时候，不需要 padding\n  var genLayoutStyle = {\n    position: 'relative'\n  };\n\n  // if is some layout children, don't need min height\n  if (isChildrenLayout || contentStyle && contentStyle.minHeight) {\n    genLayoutStyle.minHeight = 0;\n  }\n\n  /** 页面切换的时候触发 */\n  useEffect(function () {\n    var _props$onPageChange;\n    (_props$onPageChange = props.onPageChange) === null || _props$onPageChange === void 0 || _props$onPageChange.call(props, props.location);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [location.pathname, (_location$pathname = location.pathname) === null || _location$pathname === void 0 ? void 0 : _location$pathname.search]);\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    hasFooterToolbar = _useState4[0],\n    setHasFooterToolbar = _useState4[1];\n  /**\n   * 使用number是因为多标签页的时候有多个 PageContainer，只有有任意一个就应该展示这个className\n   */\n  var _useState5 = useState(0),\n    _useState6 = _slicedToArray(_useState5, 2),\n    hasPageContainer = _useState6[0],\n    setHasPageContainer = _useState6[1];\n  useDocumentTitle(pageTitleInfo, props.title || false);\n  var _useContext2 = useContext(ProProvider),\n    token = _useContext2.token;\n  var bgImgStyleList = useMemo(function () {\n    if (bgLayoutImgList && bgLayoutImgList.length > 0) {\n      return bgLayoutImgList === null || bgLayoutImgList === void 0 ? void 0 : bgLayoutImgList.map(function (item, index) {\n        return /*#__PURE__*/_jsx(\"img\", {\n          src: item.src,\n          style: _objectSpread({\n            position: 'absolute'\n          }, item)\n        }, index);\n      });\n    }\n    return null;\n  }, [bgLayoutImgList]);\n  return wrapSSR( /*#__PURE__*/_jsx(RouteContext.Provider, {\n    value: _objectSpread(_objectSpread({}, defaultProps), {}, {\n      breadcrumb: breadcrumbProps,\n      menuData: menuData,\n      isMobile: isMobile,\n      collapsed: collapsed,\n      hasPageContainer: hasPageContainer,\n      setHasPageContainer: setHasPageContainer,\n      isChildrenLayout: true,\n      title: pageTitleInfo.pageName,\n      hasSiderMenu: !!siderMenuDom,\n      hasHeader: !!headerDom,\n      siderWidth: leftSiderWidth,\n      hasFooter: !!footerDom,\n      hasFooterToolbar: hasFooterToolbar,\n      setHasFooterToolbar: setHasFooterToolbar,\n      pageTitleInfo: pageTitleInfo,\n      matchMenus: matchMenus,\n      matchMenuKeys: matchMenuKeys,\n      currentMenu: currentMenu\n    }),\n    children: props.pure ? /*#__PURE__*/_jsx(_Fragment, {\n      children: children\n    }) : /*#__PURE__*/_jsxs(\"div\", {\n      className: className,\n      children: [bgImgStyleList || (_token$layout = token.layout) !== null && _token$layout !== void 0 && _token$layout.bgLayout ? /*#__PURE__*/_jsx(\"div\", {\n        className: classNames(\"\".concat(proLayoutClassName, \"-bg-list\"), hashId),\n        children: bgImgStyleList\n      }) : null, /*#__PURE__*/_jsxs(Layout, {\n        style: _objectSpread({\n          minHeight: '100%',\n          // hack style\n          flexDirection: siderMenuDom ? 'row' : undefined\n        }, style),\n        children: [/*#__PURE__*/_jsx(ConfigProvider\n        // @ts-ignore\n        , {\n          theme: {\n            hashed: isNeedOpenHash(),\n            token: {\n              controlHeightLG: ((_token$layout2 = token.layout) === null || _token$layout2 === void 0 || (_token$layout2 = _token$layout2.sider) === null || _token$layout2 === void 0 ? void 0 : _token$layout2.menuHeight) || (token === null || token === void 0 ? void 0 : token.controlHeightLG)\n            },\n            components: {\n              Menu: coverToNewToken({\n                colorItemBg: ((_token$layout3 = token.layout) === null || _token$layout3 === void 0 || (_token$layout3 = _token$layout3.sider) === null || _token$layout3 === void 0 ? void 0 : _token$layout3.colorMenuBackground) || 'transparent',\n                colorSubItemBg: ((_token$layout4 = token.layout) === null || _token$layout4 === void 0 || (_token$layout4 = _token$layout4.sider) === null || _token$layout4 === void 0 ? void 0 : _token$layout4.colorMenuBackground) || 'transparent',\n                radiusItem: token.borderRadius,\n                colorItemBgSelected: ((_token$layout5 = token.layout) === null || _token$layout5 === void 0 || (_token$layout5 = _token$layout5.sider) === null || _token$layout5 === void 0 ? void 0 : _token$layout5.colorBgMenuItemSelected) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),\n                colorItemBgHover: ((_token$layout6 = token.layout) === null || _token$layout6 === void 0 || (_token$layout6 = _token$layout6.sider) === null || _token$layout6 === void 0 ? void 0 : _token$layout6.colorBgMenuItemHover) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),\n                colorItemBgActive: ((_token$layout7 = token.layout) === null || _token$layout7 === void 0 || (_token$layout7 = _token$layout7.sider) === null || _token$layout7 === void 0 ? void 0 : _token$layout7.colorBgMenuItemActive) || (token === null || token === void 0 ? void 0 : token.colorBgTextActive),\n                colorItemBgSelectedHorizontal: ((_token$layout8 = token.layout) === null || _token$layout8 === void 0 || (_token$layout8 = _token$layout8.sider) === null || _token$layout8 === void 0 ? void 0 : _token$layout8.colorBgMenuItemSelected) || (token === null || token === void 0 ? void 0 : token.colorBgTextHover),\n                colorActiveBarWidth: 0,\n                colorActiveBarHeight: 0,\n                colorActiveBarBorderSize: 0,\n                colorItemText: ((_token$layout9 = token.layout) === null || _token$layout9 === void 0 || (_token$layout9 = _token$layout9.sider) === null || _token$layout9 === void 0 ? void 0 : _token$layout9.colorTextMenu) || (token === null || token === void 0 ? void 0 : token.colorTextSecondary),\n                colorItemTextHover: ((_token$layout10 = token.layout) === null || _token$layout10 === void 0 || (_token$layout10 = _token$layout10.sider) === null || _token$layout10 === void 0 ? void 0 : _token$layout10.colorTextMenuItemHover) || 'rgba(0, 0, 0, 0.85)',\n                // 悬浮态\n                colorItemTextSelected: ((_token$layout11 = token.layout) === null || _token$layout11 === void 0 || (_token$layout11 = _token$layout11.sider) === null || _token$layout11 === void 0 ? void 0 : _token$layout11.colorTextMenuSelected) || 'rgba(0, 0, 0, 1)',\n                popupBg: token === null || token === void 0 ? void 0 : token.colorBgElevated,\n                subMenuItemBg: token === null || token === void 0 ? void 0 : token.colorBgElevated,\n                darkSubMenuItemBg: 'transparent',\n                darkPopupBg: token === null || token === void 0 ? void 0 : token.colorBgElevated\n              })\n            }\n          },\n          children: siderMenuDom\n        }), /*#__PURE__*/_jsxs(\"div\", {\n          style: genLayoutStyle,\n          className: \"\".concat(proLayoutClassName, \"-container \").concat(hashId).trim(),\n          children: [headerDom, /*#__PURE__*/_jsx(WrapContent, _objectSpread(_objectSpread({\n            hasPageContainer: hasPageContainer,\n            isChildrenLayout: isChildrenLayout\n          }, rest), {}, {\n            hasHeader: !!headerDom,\n            prefixCls: proLayoutClassName,\n            style: contentStyle,\n            children: loading ? /*#__PURE__*/_jsx(PageLoading, {}) : children\n          })), footerDom, hasFooterToolbar && /*#__PURE__*/_jsx(\"div\", {\n            className: \"\".concat(proLayoutClassName, \"-has-footer\"),\n            style: {\n              height: 64,\n              marginBlockStart: (_token$layout12 = token.layout) === null || _token$layout12 === void 0 || (_token$layout12 = _token$layout12.pageContainer) === null || _token$layout12 === void 0 ? void 0 : _token$layout12.paddingBlockPageContainerContent\n            }\n          })]\n        })]\n      })]\n    })\n  }));\n};\nvar ProLayout = function ProLayout(props) {\n  var colorPrimary = props.colorPrimary;\n  var darkProps = props.navTheme !== undefined ? {\n    dark: props.navTheme === 'realDark'\n  } : {};\n  return /*#__PURE__*/_jsx(ConfigProvider, {\n    theme: colorPrimary ? {\n      token: {\n        colorPrimary: colorPrimary\n      }\n    } : undefined,\n    children: /*#__PURE__*/_jsx(ProConfigProvider, _objectSpread(_objectSpread({}, darkProps), {}, {\n      token: props.token,\n      prefixCls: props.prefixCls,\n      children: /*#__PURE__*/_jsx(BaseProLayout, _objectSpread(_objectSpread({\n        logo: /*#__PURE__*/_jsx(Logo, {})\n      }, defaultSettings), {}, {\n        location: isBrowser() ? window.location : undefined\n      }, props))\n    }))\n  });\n};\nexport { ProLayout };", "// @ts-nocheck\n// This file is generated by Umi automatically\n// DO NOT CHANGE IT MANUALLY!\nimport React from 'react';\n\nconst LogoIcon: React.FC = () => {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"32\"\n      height=\"32\"\n      viewBox=\"0 0 200 200\"\n    >\n      <defs>\n        <linearGradient\n          id=\"linearGradient-1\"\n          x1=\"62.102%\"\n          x2=\"108.197%\"\n          y1=\"0%\"\n          y2=\"37.864%\"\n        >\n          <stop offset=\"0%\" stopColor=\"#4285EB\"></stop>\n          <stop offset=\"100%\" stopColor=\"#2EC7FF\"></stop>\n        </linearGradient>\n        <linearGradient\n          id=\"linearGradient-2\"\n          x1=\"69.644%\"\n          x2=\"54.043%\"\n          y1=\"0%\"\n          y2=\"108.457%\"\n        >\n          <stop offset=\"0%\" stopColor=\"#29CDFF\"></stop>\n          <stop offset=\"37.86%\" stopColor=\"#148EFF\"></stop>\n          <stop offset=\"100%\" stopColor=\"#0A60FF\"></stop>\n        </linearGradient>\n        <linearGradient\n          id=\"linearGradient-3\"\n          x1=\"69.691%\"\n          x2=\"16.723%\"\n          y1=\"-12.974%\"\n          y2=\"117.391%\"\n        >\n          <stop offset=\"0%\" stopColor=\"#FA816E\"></stop>\n          <stop offset=\"41.473%\" stopColor=\"#F74A5C\"></stop>\n          <stop offset=\"100%\" stopColor=\"#F51D2C\"></stop>\n        </linearGradient>\n        <linearGradient\n          id=\"linearGradient-4\"\n          x1=\"68.128%\"\n          x2=\"30.44%\"\n          y1=\"-35.691%\"\n          y2=\"114.943%\"\n        >\n          <stop offset=\"0%\" stopColor=\"#FA8E7D\"></stop>\n          <stop offset=\"51.264%\" stopColor=\"#F74A5C\"></stop>\n          <stop offset=\"100%\" stopColor=\"#F51D2C\"></stop>\n        </linearGradient>\n      </defs>\n      <g fill=\"none\" fillRule=\"evenodd\" stroke=\"none\" strokeWidth=\"1\">\n        <g transform=\"translate(-20 -20)\">\n          <g transform=\"translate(20 20)\">\n            <g>\n              <g fillRule=\"nonzero\">\n                <g>\n                  <path\n                    fill=\"url(#linearGradient-1)\"\n                    d=\"M91.588 4.177L4.18 91.513a11.981 11.981 0 000 16.974l87.408 87.336a12.005 12.005 0 0016.989 0l36.648-36.618c4.209-4.205 4.209-11.023 0-15.228-4.208-4.205-11.031-4.205-15.24 0l-27.783 27.76c-1.17 1.169-2.945 1.169-4.114 0l-69.802-69.744c-1.17-1.169-1.17-2.942 0-4.11l69.802-69.745c1.17-1.169 2.944-1.169 4.114 0l27.783 27.76c4.209 4.205 11.032 4.205 15.24 0 4.209-4.205 4.209-11.022 0-15.227L108.581 4.056c-4.719-4.594-12.312-4.557-16.993.12z\"\n                  ></path>\n                  <path\n                    fill=\"url(#linearGradient-2)\"\n                    d=\"M91.588 4.177L4.18 91.513a11.981 11.981 0 000 16.974l87.408 87.336a12.005 12.005 0 0016.989 0l36.648-36.618c4.209-4.205 4.209-11.023 0-15.228-4.208-4.205-11.031-4.205-15.24 0l-27.783 27.76c-1.17 1.169-2.945 1.169-4.114 0l-69.802-69.744c-1.17-1.169-1.17-2.942 0-4.11l69.802-69.745c2.912-2.51 7.664-7.596 14.642-8.786 5.186-.883 10.855 1.062 17.009 5.837L108.58 4.056c-4.719-4.594-12.312-4.557-16.993.12z\"\n                  ></path>\n                </g>\n                <path\n                  fill=\"url(#linearGradient-3)\"\n                  d=\"M153.686 135.855c4.208 4.205 11.031 4.205 15.24 0l27.034-27.012c4.7-4.696 4.7-12.28 0-16.974l-27.27-27.15c-4.218-4.2-11.043-4.195-15.254.013-4.209 4.205-4.209 11.022 0 15.227l18.418 18.403c1.17 1.169 1.17 2.943 0 4.111l-18.168 18.154c-4.209 4.205-4.209 11.023 0 15.228z\"\n                ></path>\n              </g>\n              <ellipse\n                cx=\"100.519\"\n                cy=\"100.437\"\n                fill=\"url(#linearGradient-4)\"\n                rx=\"23.6\"\n                ry=\"23.581\"\n              ></ellipse>\n            </g>\n          </g>\n        </g>\n      </g>\n    </svg>\n  );\n};\n\nexport default LogoIcon;\n", "// @ts-nocheck\n// This file is generated by <PERSON><PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\nimport React from 'react';\nimport { history, type IRoute } from '@umijs/max';\nimport { Result, Button } from 'antd';\n\nconst Exception: React.FC<{\n  children: React.ReactNode;\n  route?: IRoute;\n  notFound?: React.ReactNode;\n  noAccessible?: React.ReactNode;\n  unAccessible?: React.ReactNode;\n  noFound?: React.ReactNode;\n}> = (props) => (\n  // render custom 404\n  (!props.route && (props.noFound || props.notFound)) ||\n  // render custom 403\n  (props.route?.unaccessible && (props.unAccessible || props.noAccessible)) ||\n  // render default exception\n  ((!props.route || props.route?.unaccessible) && (\n    <Result\n      status={props.route ? '403' : '404'}\n      title={props.route ? '403' : '404'}\n      subTitle={props.route ? '抱歉，你无权访问该页面' : '抱歉，你访问的页面不存在'}\n      extra={\n        <Button type=\"primary\" onClick={() => history.push('/')}>\n          返回首页\n        </Button>\n      }\n    />\n  )) ||\n  // normal render\n  props.children\n);\n\nexport default Exception;\n", "// @ts-nocheck\n// This file is generated by Um<PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\nimport React from 'react';\nimport { Avatar, version, Dropdown, Menu, Spin } from 'antd';\nimport { LogoutOutlined } from '/app/node_modules/@ant-design/icons';\nimport { SelectLang } from '@@/plugin-locale';\n\nexport function getRightRenderContent (opts: {\n   runtimeConfig: any,\n   loading: boolean,\n   initialState: any,\n   setInitialState: any,\n }) {\n  if (opts.runtimeConfig.rightRender) {\n    return opts.runtimeConfig.rightRender(\n      opts.initialState,\n      opts.setInitialState,\n      opts.runtimeConfig,\n    );\n  }\n\n  const showAvatar = opts.initialState?.avatar || opts.initialState?.name || opts.runtimeConfig.logout;\n  const disableAvatarImg = opts.initialState?.avatar === false;\n  const nameClassName = disableAvatarImg ? 'umi-plugin-layout-name umi-plugin-layout-hide-avatar-img' : 'umi-plugin-layout-name';\n  const avatar =\n    showAvatar ? (\n      <span className=\"umi-plugin-layout-action\">\n        {!disableAvatarImg ?\n          (\n            <Avatar\n              size=\"small\"\n              className=\"umi-plugin-layout-avatar\"\n              src={\n                opts.initialState?.avatar ||\n                \"https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png\"\n              }\n              alt=\"avatar\"\n            />\n          ) : null}\n        <span className={nameClassName}>{opts.initialState?.name}</span>\n      </span>\n    ) : null;\n\n\n  if (opts.loading) {\n    return (\n      <div className=\"umi-plugin-layout-right\">\n        <Spin size=\"small\" style={ { marginLeft: 8, marginRight: 8 } } />\n      </div>\n    );\n  }\n\n  // 如果没有打开Locale，并且头像为空就取消掉这个返回的内容\n\n  const langMenu = {\n    className: \"umi-plugin-layout-menu\",\n    selectedKeys: [],\n    items: [\n      {\n        key: \"logout\",\n        label: (\n          <>\n            <LogoutOutlined />\n            退出登录\n          </>\n        ),\n        onClick: () => {\n          opts?.runtimeConfig?.logout?.(opts.initialState);\n        },\n      },\n    ],\n  };\n  // antd@5 和  4.24 之后推荐使用 menu，性能更好\n  let dropdownProps;\n  if (version.startsWith(\"5.\") || version.startsWith(\"4.24.\")) {\n    dropdownProps = { menu: langMenu };\n  } else if (version.startsWith(\"3.\")) {\n    dropdownProps = {\n      overlay: (\n        <Menu>\n          {langMenu.items.map((item) => (\n            <Menu.Item key={item.key} onClick={item.onClick}>\n              {item.label}\n            </Menu.Item>\n          ))}\n        </Menu>\n      ),\n    };\n  } else { // 需要 antd 4.20.0 以上版本\n    dropdownProps = { overlay: <Menu {...langMenu} /> };\n  }\n\n\n\n  return (\n    <div className=\"umi-plugin-layout-right anticon\">\n      {opts.runtimeConfig.logout ? (\n        <Dropdown {...dropdownProps} overlayClassName=\"umi-plugin-layout-container\">\n          {avatar}\n        </Dropdown>\n      ) : (\n        avatar\n      )}\n      <SelectLang />\n    </div>\n  );\n}\n", "// @ts-nocheck\n// This file is generated by <PERSON><PERSON> automatically\n// DO NOT CHANGE IT MANUALLY!\n/// <reference types=\"@ant-design/pro-components\" />\n/// <reference types=\"antd\" />\n\nimport {\n  Link, useLocation, useNavigate, Outlet, useAppData, matchRoutes,\n  type IRoute\n} from '@umijs/max';\nimport React, { useMemo } from 'react';\nimport {\n  ProLayout,\n} from \"/app/node_modules/@ant-design/pro-components\";\nimport './Layout.css';\nimport Logo from './Logo';\nimport Exception from './Exception';\nimport { getRightRenderContent } from './rightRender';\nimport { useModel } from '@@/plugin-model';\nimport { useAccessMarkedRoutes } from '@@/plugin-access';\nimport { useIntl } from '@@/plugin-locale';\n\n// 过滤出需要显示的路由, 这里的filterFn 指 不希望显示的层级\nconst filterRoutes = (routes: IRoute[], filterFn: (route: IRoute) => boolean) => {\n  if (routes.length === 0) {\n    return []\n  }\n\n  let newRoutes = []\n  for (const route of routes) {\n    const newRoute = {...route };\n    if (filterFn(route)) {\n      if (Array.isArray(newRoute.routes)) {\n        newRoutes.push(...filterRoutes(newRoute.routes, filterFn))\n      }\n    } else {\n      if (Array.isArray(newRoute.children)) {\n        newRoute.children = filterRoutes(newRoute.children, filterFn);\n        newRoute.routes = newRoute.children;\n      }\n      newRoutes.push(newRoute);\n    }\n  }\n\n  return newRoutes;\n}\n\n// 格式化路由 处理因 wrapper 导致的 菜单 path 不一致\nconst mapRoutes = (routes: IRoute[]) => {\n  if (routes.length === 0) {\n    return []\n  }\n  return routes.map(route => {\n    // 需要 copy 一份, 否则会污染原始数据\n    const newRoute = {...route}\n    if (route.originPath) {\n      newRoute.path = route.originPath\n    }\n\n    if (Array.isArray(route.routes)) {\n      newRoute.routes = mapRoutes(route.routes);\n    }\n\n    if (Array.isArray(route.children)) {\n      newRoute.children = mapRoutes(route.children);\n    }\n\n    return newRoute\n  })\n}\n\nexport default (props: any) => {\n  const location = useLocation();\n  const navigate = useNavigate();\n  const { clientRoutes, pluginManager } = useAppData();\n  const initialInfo = (useModel && useModel('@@initialState')) || {\n    initialState: undefined,\n    loading: false,\n    setInitialState: null,\n  };\n  const { initialState, loading, setInitialState } = initialInfo;\n  const userConfig = {\n  \"locale\": true,\n  \"navTheme\": \"light\",\n  \"colorPrimary\": \"#1677FF\",\n  \"layout\": \"side\",\n  \"contentWidth\": \"Fluid\",\n  \"fixedHeader\": false,\n  \"fixSiderbar\": true,\n  \"pwa\": true,\n  \"logo\": \"/logo.png\",\n  \"splitMenus\": false,\n  \"siderMenuType\": \"sub\",\n  \"colorWeak\": false,\n  \"title\": \"Labwise\",\n  \"siderWidth\": 256,\n  \"iconfontUrl\": \"\",\n  \"token\": {}\n};\nconst { formatMessage } = useIntl();\n  const runtimeConfig = pluginManager.applyPlugins({\n    key: 'layout',\n    type: 'modify',\n    initialValue: {\n      ...initialInfo\n    },\n  });\n\n\n  // 现在的 layout 及 wrapper 实现是通过父路由的形式实现的, 会导致路由数据多了冗余层级, proLayout 消费时, 无法正确展示菜单, 这里对冗余数据进行过滤操作\n  const newRoutes = filterRoutes(clientRoutes.filter(route => route.id === 'ant-design-pro-layout'), (route) => {\n    return (!!route.isLayout && route.id !== 'ant-design-pro-layout') || !!route.isWrapper;\n  })\n  const [route] = useAccessMarkedRoutes(mapRoutes(newRoutes));\n\n  const matchedRoute = useMemo(() => matchRoutes(route.children, location.pathname)?.pop?.()?.route, [location.pathname]);\n\n  return (\n    <ProLayout\n      route={route}\n      location={location}\n      title={userConfig.title || 'labwise-web'}\n      navTheme=\"dark\"\n      siderWidth={256}\n      onMenuHeaderClick={(e) => {\n        e.stopPropagation();\n        e.preventDefault();\n        navigate('/');\n      }}\n      formatMessage={userConfig.formatMessage || formatMessage}\n      menu={{ locale: userConfig.locale }}\n      logo={Logo}\n      menuItemRender={(menuItemProps, defaultDom) => {\n        if (menuItemProps.isUrl || menuItemProps.children) {\n          return defaultDom;\n        }\n        if (menuItemProps.path && location.pathname !== menuItemProps.path) {\n          return (\n            // handle wildcard route path, for example /slave/* from qiankun\n            <Link to={menuItemProps.path.replace('/*', '')} target={menuItemProps.target}>\n              {defaultDom}\n            </Link>\n          );\n        }\n        return defaultDom;\n      }}\n      itemRender={(route, _, routes) => {\n        const { breadcrumbName, title, path } = route;\n        const label = title || breadcrumbName\n        const last = routes[routes.length - 1]\n        if (last) {\n          if (last.path === path || last.linkPath === path) {\n            return <span>{label}</span>;\n          }\n        }\n        return <Link to={path}>{label}</Link>;\n      }}\n      disableContentMargin\n      fixSiderbar\n      fixedHeader\n      {...runtimeConfig}\n      rightContentRender={\n        runtimeConfig.rightContentRender !== false &&\n        ((layoutProps) => {\n          const dom = getRightRenderContent({\n            runtimeConfig,\n            loading,\n            initialState,\n            setInitialState,\n          });\n          if (runtimeConfig.rightContentRender) {\n            return runtimeConfig.rightContentRender(layoutProps, dom, {\n              // BREAK CHANGE userConfig > runtimeConfig\n              userConfig,\n              runtimeConfig,\n              loading,\n              initialState,\n              setInitialState,\n            });\n          }\n          return dom;\n        })\n      }\n    >\n      <Exception\n        route={matchedRoute}\n        noFound={runtimeConfig?.noFound}\n        notFound={runtimeConfig?.notFound}\n        unAccessible={runtimeConfig?.unAccessible}\n        noAccessible={runtimeConfig?.noAccessible}\n      >\n        {runtimeConfig.childrenRender\n          ? runtimeConfig.childrenRender(<Outlet />, props)\n          : <Outlet />\n        }\n      </Exception>\n    </ProLayout>\n  );\n}\n"], "names": ["CopyrightOutlined", "props", "ref", "AntdIcon", "RefIcon", "genFooterToolBarStyle", "token", "prefixCls", "proCardToken", "GlobalFooter", "_ref", "className", "links", "copyright", "style", "context", "baseClassName", "_useStyle", "wrapSSR", "hashId", "link", "Footer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_excluded", "PageLoading", "isLoading", "past<PERSON>elay", "timedOut", "error", "retry", "reset", "RouteContext", "twoToneColorPalette", "setTwoToneColors", "primaryColor", "secondaryColor", "getTwoToneColors", "IconBase", "icon", "onClick", "restProps", "svgRef", "colors", "target", "setTwoToneColor", "twoToneColor", "_normalizeTwoToneColo", "_normalizeTwoToneColo2", "getTwoToneColor", "Icon", "spin", "rotate", "tabIndex", "_React$useContext", "Context", "_React$useContext$pre", "rootClassName", "classString", "iconTabIndex", "svgStyle", "IconContext", "camelCase", "input", "match", "g", "warning", "valid", "message", "isIconDefinition", "normalizeAttrs", "attrs", "acc", "key", "val", "generate", "node", "rootProps", "child", "index", "getSecondaryColor", "normalizeTwoToneColors", "svgBaseProps", "iconStyles", "useInsertStyles", "eleRef", "_useContext", "csp", "mergedStyleStr", "ele", "shadowRoot", "_typeof", "obj", "exports", "lexer", "str", "tokens", "i", "char", "name", "j", "code", "count", "pattern", "parse", "options", "_a", "prefixes", "defaultPattern", "escapeString", "result", "path", "tryConsume", "type", "mustConsume", "value", "nextType", "consumeText", "prefix", "open", "name_1", "pattern_1", "suffix", "compile", "tokensToFunction", "reFlags", "flags", "encode", "x", "_b", "validate", "matches", "data", "optional", "repeat", "segment", "typeOfMessage", "keys", "re", "pathToRegexp", "regexpToFunction", "decode", "pathname", "m", "params", "_loop_1", "regexpToRegexp", "groups", "arrayToRegexp", "paths", "parts", "stringToRegexp", "tokensToRegexp", "strict", "start", "_c", "end", "_d", "endsWith", "delimiter", "route", "_i", "tokens_1", "mod", "endToken", "isEndDelimited", "__classPrivateFieldGet", "receiver", "state", "kind", "f", "__classPrivateFieldSet", "_Iter_peek", "DEFAULT_DELIMITER", "NOOP_VALUE", "ID_START", "ID_CONTINUE", "DEBUG_URL", "SIMPLE_TOKENS", "escape", "to<PERSON><PERSON><PERSON>", "chars", "pos", "<PERSON><PERSON>", "next", "TokenData", "it", "consume", "endType", "encodePath", "param", "wildcard", "$compile", "fn", "missing", "encoders", "tokenToFunction", "encoder", "extras", "encodeValue", "$match", "sources", "seq", "flatten", "regexp", "sequenceToRegExp", "trailing", "decoders", "decoder", "items", "init", "fork", "backtrack", "isSafeSegmentParam", "negate", "values", "getVersion", "_process", "process", "openVisibleCompatible", "onOpenChange", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "_super", "_this", "_len", "args", "_key", "errorInfo", "useDebounceFn", "wait", "callback", "timer", "cancel", "run", "_callee2", "_args2", "_context2", "resolve", "_callee", "_context", "useRefFunction", "reFunction", "_ref$current", "rest", "isNode", "<PERSON><PERSON><PERSON><PERSON>", "omitUndefined", "newObj", "useMediaQuery", "mediaQuery", "isSsr", "_useState", "_useState2", "setMatches", "mediaQueryList", "listener", "e", "MediaQueryEnum", "getScreenClassName", "query<PERSON><PERSON>", "mediaQuery<PERSON>ey", "matchMedia", "useBreakpoint", "isMd", "isLg", "isXxl", "isXl", "isSm", "isXs", "colSpan", "setColSpan", "useDocumentTitle", "titleInfo", "appDefaultTitle", "titleText", "coverToNewToken", "compareVersions", "deprecatedTokens", "newToken", "rotateRight", "n", "choice", "y", "z", "majority", "sha256_Sigma0", "sha256_Sigma1", "sha256_sigma0", "sha256_sigma1", "sha256_expand", "W", "K256", "i<PERSON>h", "buffer", "sha256_hex_digits", "safe_add", "lsw", "msw", "sha256_init", "sha256_transform", "b", "c", "d", "h", "T1", "T2", "sha256_update", "inputLen", "curpos", "remainder", "_j", "sha256_final", "_i2", "sha256_encode_bytes", "output", "sha256_encode_hex", "digest", "_slicedToArray", "arr", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "_arr", "_n", "_s", "_e", "err", "_createForOfIteratorHelper", "o", "allowArrayLike", "F", "_e2", "normalCompletion", "didErr", "step", "_e3", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "_defineProperties", "descriptor", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "_setPrototypeOf", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "Super", "_getPrototypeOf", "<PERSON><PERSON><PERSON><PERSON>", "_possibleConstructorReturn", "self", "call", "_assertThisInitialized", "_wrapNativeSuper", "Class", "_cache", "_isNativeFunction", "Wrapper", "_construct", "Parent", "a", "p", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_nonIterableSpread", "minLen", "_arrayLikeToArray", "iter", "len", "arr2", "_objectWithoutProperties", "source", "excluded", "_objectWithoutPropertiesLoose", "sourceSymbolKeys", "sourceKeys", "ownKeys", "object", "enumerableOnly", "symbols", "sym", "_objectSpread", "_defineProperty", "childrenPropsName", "stripQueryStringAndHashFromPath", "url", "isUrl", "get<PERSON>eyByPath", "item", "sha265", "getItemLocaleName", "parentName", "locale", "mergePath", "parentPath", "bigfishCompatibleConversions", "_route$menu", "menu", "indexRoute", "_route$path", "routerChildren", "_menu$name", "_menu$icon", "_menu$hideChildren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_menu$flatMenu", "flatMenu", "childrenList", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "formatter", "notNullArray", "parent", "formatMessage", "menuLocale", "_item$menu", "_item$menu2", "finallyItem", "localeName", "_parent$pro_layout_pa", "pro_layout_parentKeys", "children", "routes", "restParent", "item_pro_layout_parentKeys", "formatter<PERSON><PERSON><PERSON>n", "defaultFilterMenuData", "menuData", "newItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RouteListMap", "_Map", "routeValue", "_iterator", "_step", "_step$value", "getBreadcrumbNameMap", "routerMap", "flattenMenuData", "menuItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transformRoute", "routeList", "ignoreFilter", "originalMenuData", "breadcrumb", "getFlatMenus", "menus", "mapItem", "getMenuMatches", "flatMenuKeys", "exact", "path<PERSON><PERSON>", "getMatchMenu", "fullKeys", "flatMenus", "menuPathKeys", "menuPath<PERSON>ey", "map", "parentItems", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_props$hasPageContain", "hasPageContainer", "contentClassName", "ErrorComponent", "Logo", "getOpenKeysFromMenuData", "pre", "newArray", "themeConfig", "genStringToTheme", "clearMenuItem", "menusData", "finalItem", "MenuOutlined", "AppsLogo", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "appList", "itemClick", "app", "_app$children", "defaultRenderLogo", "renderLogo", "logo", "title", "symbol", "SimpleContent", "genAppsLogoComponentsDefaultListStyle", "genAppsLogoComponentsSimpleListStyle", "genAppsLogoComponentsStyle", "_token$layout", "_token$layout2", "_token$layout3", "_token$layout4", "_token$layout5", "AppsLogoComponents", "_props$appList", "appListRender", "_props$prefixCls", "popoverRef", "<PERSON><PERSON><PERSON>", "cloneItemClick", "defaultDomContent", "isSimple", "popoverContent", "popoverOpenProps", "openChange", "ArrowSvgIcon", "genSiderMenuStyle", "siderMenuToken", "CollapsedIcon", "isMobile", "collapsed", "Component", "viewBox", "iconRef", "mergedRef", "svgClassString", "innerSvgProps", "renderInnerNode", "customCache", "isValidCustomScriptUrl", "scriptUrl", "createScriptUrlElements", "scriptUrls", "currentScriptUrl", "script", "create", "_options$extraCommonP", "extraCommonProps", "Iconfont", "content", "isImg", "defaultSettings", "genProLayoutBaseMenuStyle", "mode", "menuToken", "proLayoutMenuToken", "MenuItemTooltip", "setCollapsed", "_useState3", "_useState4", "IconFont", "getIcon", "iconPrefixes", "getMenuTitleSymbol", "<PERSON><PERSON><PERSON><PERSON>", "level", "noGroupLevel", "_this$props", "subMenuItemRender", "layout", "isGroup", "designToken", "menuType", "_this$props2", "_this$props3", "_this$props4", "_this$props5", "_designToken$layout", "shouldHasIcon", "iconDom", "defaultIcon", "defaultTitle", "_this$props6", "finalName", "_this$props9", "_this$props10", "_this$props11", "_this$props12", "itemPath", "_this$props7", "_this$props7$location", "location", "onCollapse", "menuItemRender", "menuItemTitle", "_this$props8", "hasIcon", "defaultItem", "isHttpUrl", "_this$props13", "_this$props14", "_this$props15", "_window", "_window$open", "renderItemProps", "getOpenKeysProps", "openKeys", "openKeysProps", "BaseMenu", "handleOpenChange", "matchMenuKeys", "iconfontUrl", "propsSelectedKeys", "onSelect", "menuRenderType", "props<PERSON><PERSON><PERSON><PERSON>s", "dark", "defaultOpenKeysRef", "_useMountMergeState", "_useMountMergeState2", "defaultOpenAll", "setDefaultOpenAll", "_useMountMergeState3", "_useMountMergeState4", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_useMountMergeState5", "_useMountMergeState6", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedKeys", "newKeys", "menuUtils", "finallyData", "_openKeys", "useStylish", "stylish", "proLayoutCollapsedWidth", "_SafetyWarningProvider", "<PERSON><PERSON>", "_Layout$_InternalSide", "SiderContext", "renderLogoAndTitle", "<PERSON><PERSON><PERSON>", "renderFunction", "logoDom", "titleDom", "SiderMenu", "_props$menu2", "originCollapsed", "fixSiderbar", "menu<PERSON><PERSON><PERSON><PERSON><PERSON>", "_onCollapse", "theme", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onMenuHeaderClick", "_props$breakpoint", "breakpoint", "_props$menuExtraRende", "menuExtraRender", "menuContentRender", "collapsed<PERSON><PERSON>onRender", "avatarProps", "right<PERSON><PERSON>nt<PERSON><PERSON>", "actionsRender", "logoStyle", "showSiderExtraDom", "collapsedWidth", "stylishClassName", "siderClassName", "headerDom", "extraDom", "menuDom", "linksMenuItems", "menuRenderDom", "avatarDom", "render", "dom", "actionsDom", "appsDom", "collapsedDom", "actionAreaDom", "hideMenuWhenCollapsedClassName", "_props$menu", "menuFooterDom", "menuDomItems", "collapse", "genTopNavHeaderStyle", "proToken", "_excluded2", "ActionsContent", "headerContentRender", "getPrefixCls", "rightSize", "setRightSize", "domList", "rightActionsRender", "restParams", "doms", "hideHover", "_dom$props", "setRightSizeDebounceFn", "_ref2", "width", "_x", "contentRender", "_ref3", "topNavHeaderToken", "TopNavHeader", "_token$layout13", "_token$layout14", "_token$layout15", "_token$layout16", "_token$layout17", "_token$layout18", "_token$layout19", "contentWidth", "props<PERSON><PERSON><PERSON><PERSON>", "_useContext2", "_useContext3", "contentDom", "_token$layout6", "_token$layout7", "_token$layout8", "_token$layout9", "_token$layout10", "_token$layout11", "_token$layout12", "_props$menuProps", "defaultDom", "genGlobalHeaderStyle", "GlobalHeaderToken", "menuHeaderRender", "GlobalHeader", "propClassName", "splitMenus", "direction", "noChildrenMenuData", "clearMenuData", "logoClassNames", "genProLayoutHeaderStyle", "ProLayoutHeaderToken", "stylishToken", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fixedHeader", "headerRender", "isFixedHeaderScroll", "setIsFixedHeaderScroll", "needFixedHeader", "renderContent", "isTop", "_context$getTargetCon", "isFixedHeaderFn", "scrollTop", "proLayoutTitleHide", "SiderMenuWrapper", "hide", "getContainer", "omitProps", "drawerOpenProps", "matchParamsPath", "breadcrumbMap", "_path<PERSON>ey", "getPageTitleInfo", "ignoreTitle", "_props$pathname", "pageTitle", "currRouterData", "pageName", "getPageTitle", "<PERSON><PERSON><PERSON><PERSON>", "locales", "getLanguage", "lang", "gLocaleObject", "gLocale", "version", "compatibleStyle", "_getVersion", "_$concat6", "_token$layout20", "_token$layout21", "_token$layout22", "_token$layout23", "_token$layout24", "_token$layout25", "_token$layout26", "_token$layout27", "_token$layout28", "_token$layout29", "_token$layout30", "genProLayoutStyle", "_token$layout31", "_token$layout32", "_token$layout33", "_token$layout34", "proLayoutToken", "urlToList", "urlList", "urlItem", "defaultItemRender", "_", "breadcrumbName", "last", "renderItemLocal", "getBreadcrumb", "breadcrumbItem", "targetPath", "getBreadcrumbFromProps", "conversionFromLocation", "routerLocation", "pathSnippets", "extraBreadcrumbItems", "currentBreadcrumb", "hideInBreadcrumb", "genBreadcrumbProps", "_getBreadcrumbFromPro", "getBreadcrumbProps", "layoutPros", "breadcrumbRender", "props<PERSON><PERSON><PERSON><PERSON>", "_ref2$minLength", "<PERSON><PERSON><PERSON><PERSON>", "routesArray", "itemRender", "fromEntries", "iterable", "getMenuData", "menuDataRender", "_transformRoute", "useCurrentMenuLayoutProps", "currentMenu", "currentMenuLayoutProps", "setCurrentMenuLayoutProps", "layoutIndex", "_props$stylish", "footer<PERSON><PERSON>", "renderSiderMenu", "_props$stylish3", "suppressSiderWhenMenuEmpty", "menuRender", "_props$menuData", "_props$stylish2", "defaultPageTitleRender", "pageProps", "pageTitleRender", "pageTitleInfo", "getPaddingInlineStart", "hasLeftPadding", "BaseProLayout", "_location$pathname", "propsOnCollapse", "_ref3$location", "contentStyle", "defaultCollapsed", "props<PERSON><PERSON><PERSON><PERSON><PERSON>", "siderMenuType", "props<PERSON>s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "actionRef", "bgLayoutImgList", "propsFormatMessage", "loading", "menuLoading", "setMenuLoading", "defaultId", "_ref4", "id", "defaultMessage", "_useSWR", "_ref6", "_ref5", "_menu$request", "_ref7", "menuDataItems", "mutate", "_useSWRConfig", "cache", "menuInfoData", "_ref8", "_ref8$menuData", "matchMenus", "_props$currentMenuLay", "navTheme", "propsLayout", "colSize", "_useMergedState", "useMergedState", "_useMergedState2", "defaultProps", "breadcrumbProps", "siderMenuDom", "footerDom", "contextIsChildrenLayout", "isChildrenLayout", "proLayoutClassName", "leftSiderWidth", "genLayoutStyle", "_props$onPageChange", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setHasFooterToolbar", "_useState5", "_useState6", "setHasPageContainer", "bgImgStyleList", "ProLayout", "colorPrimary", "darkProps", "LogoIcon", "_jsxs", "xmlns", "height", "x1", "x2", "y1", "y2", "_jsx", "offset", "stopColor", "fill", "fillRule", "stroke", "strokeWidth", "transform", "cx", "cy", "rx", "ry", "Exception", "noFound", "notFound", "_props$route", "unaccessible", "unAccessible", "noAccessible", "_props$route2", "Result", "status", "subTitle", "extra", "<PERSON><PERSON>", "history", "push", "getRightRenderContent", "opts", "runtimeConfig", "rightRender", "initialState", "setInitialState", "showAvatar", "_opts$initialState", "avatar", "_opts$initialState2", "logout", "disableAvatarImg", "_opts$initialState3", "nameClassName", "Avatar", "size", "src", "_opts$initialState4", "alt", "_opts$initialState5", "Spin", "marginLeft", "marginRight", "langMenu", "label", "LogoutOutlined", "_opts$runtimeConfig", "_opts$runtimeConfig$l", "dropdownProps", "startsWith", "overlay", "<PERSON><PERSON>", "<PERSON><PERSON>", "Dropdown", "overlayClassName", "SelectLang", "filterRoutes", "filterFn", "length", "newRoutes", "newRoute", "Array", "isArray", "apply", "mapRoutes", "originPath", "useLocation", "navigate", "useNavigate", "_useAppData", "useAppData", "clientRoutes", "pluginManager", "initialInfo", "useModel", "undefined", "userConfig", "_useIntl", "useIntl", "applyPlugins", "initialValue", "filter", "isLayout", "isWrapper", "_useAccessMarkedRoute", "useAccessMarkedRoutes", "_useAccessMarkedRoute2", "matchedRoute", "useMemo", "matchRoutes", "_matchRoutes", "pop", "_matchRoutes$pop", "stopPropagation", "preventDefault", "menuItemProps", "Link", "to", "replace", "linkPath", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "layoutProps", "childrenRender", "Outlet"], "sourceRoot": ""}