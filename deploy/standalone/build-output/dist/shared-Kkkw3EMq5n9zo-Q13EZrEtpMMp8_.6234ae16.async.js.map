{"version": 3, "file": "shared-Kkkw3EMq5n9zo-Q13EZrEtpMMp8_.6234ae16.async.js", "mappings": "gKAMIA,EAAe,SAAsBC,EAAOC,EAAK,CACnD,OAAoB,gBAAoB,OAAU,QAAc,KAAc,CAAC,EAAGD,CAAK,EAAG,CAAC,EAAG,CAC5F,IAAKC,EACL,KAAM,GACR,CAAC,CAAC,CACJ,EACIC,EAAuB,aAAiBH,CAAY,EAIxD,IAAeG,C,kFCdXC,EAAY,CAAC,aAAc,MAAO,gBAAiB,KAAK,EASxDC,EAAe,SAAsBC,EAAMJ,EAAK,CAClD,IAAIK,EAAaD,EAAK,WACpBE,EAAMF,EAAK,IACXG,EAAgBH,EAAK,cACrBI,EAAMJ,EAAK,IACXK,KAAO,KAAyBL,EAAMF,CAAS,EACjD,SAAoB,OAAK,OAAc,KAAc,CACnD,UAAW,QACX,cAAY,KAAc,CACxB,IAAKI,EACL,IAAKE,CACP,EAAGH,CAAU,EACb,IAAKL,EACL,YAAa,CACX,aAAc,CACZ,MAAO,MACT,CACF,EACA,cAAeO,CACjB,EAAGE,CAAI,CAAC,CACV,EACIC,EAAsC,aAAiBP,CAAY,EACvE,IAAeO,C,wIC9BXR,EAAY,CAAC,aAAc,eAAe,EAC5CS,EAAa,CAAC,aAAc,eAAe,EAQzCC,EAAY,OAMZC,EAAc,SAAqBT,EAAM,CAC3C,IAAIC,EAAaD,EAAK,WACpBG,EAAgBH,EAAK,cACrBK,KAAO,KAAyBL,EAAMF,CAAS,EACjD,SAAoB,OAAK,OAAU,KAAc,CAC/C,UAAWU,EACX,WAAYP,EACZ,YAAa,CACX,UAAWO,CACb,EACA,cAAeL,CACjB,EAAGE,CAAI,CAAC,CACV,EACIK,EAAmB,SAA0Bf,EAAO,CACtD,IAAIgB,KAAsB,KAAmBhB,EAAM,MAAQ,GAAO,CAC9D,MAAOA,EAAM,KACb,SAAUA,EAAM,YAClB,CAAC,EACDiB,KAAuB,KAAeD,EAAqB,CAAC,EAC5DE,EAAOD,EAAqB,CAAC,EAC7BE,EAAUF,EAAqB,CAAC,EAClC,SAAoB,OAAK,IAAK,KAAM,CAClC,aAAc,GACd,QAAS,GACT,SAAU,SAAkBG,EAAM,CAChC,IAAIC,EACAC,EAAQF,EAAK,cAAcpB,EAAM,MAAQ,CAAC,CAAC,EAC/C,SAAoB,OAAK,OAAS,QAAc,KAAc,CAC5D,kBAAmB,SAA2BuB,EAAM,CAClD,OAAIA,GAAQA,EAAK,WACRA,EAAK,WAEPA,CACT,EACA,aAAc,SAAsBC,EAAG,CACrC,OAAOL,EAAQK,CAAC,CAClB,EACA,WAAsB,QAAM,MAAO,CACjC,MAAO,CACL,QAAS,OACX,EACA,SAAU,EAAEH,EAAsBrB,EAAM,gBAAkB,MAAQqB,IAAwB,OAAS,OAASA,EAAoB,KAAKrB,EAAOsB,CAAK,EAAGtB,EAAM,gBAA4B,OAAK,MAAO,CAChM,MAAO,CACL,UAAW,EACb,EACA,YAAuB,OAAK,OAAQ,CAClC,SAAUA,EAAM,YAClB,CAAC,CACH,CAAC,EAAI,IAAI,CACX,CAAC,EACD,aAAc,CACZ,MAAO,GACT,EACA,UAAW,UACb,EAAGA,EAAM,YAAY,EAAG,CAAC,EAAG,CAC1B,KAAMkB,EACN,SAAUlB,EAAM,QAClB,CAAC,CAAC,CACJ,CACF,CAAC,CACH,EACIyB,EAAW,SAAkBC,EAAO,CACtC,IAAIpB,EAAaoB,EAAM,WACrBlB,EAAgBkB,EAAM,cACtBhB,KAAO,KAAyBgB,EAAOd,CAAU,EAC/Ce,KAAY,YAAS,EAAK,EAC5BC,KAAa,KAAeD,EAAW,CAAC,EACxCT,EAAOU,EAAW,CAAC,EACnBT,EAAUS,EAAW,CAAC,EACxB,OAAItB,GAAe,MAAiCA,EAAW,cAAgBI,EAAK,QAC9D,OAAKK,EAAkB,CACzC,KAAML,EAAK,KACX,aAAcJ,GAAe,KAAgC,OAASA,EAAW,aACjF,aAAcA,GAAe,KAAgC,OAASA,EAAW,aACjF,aAAcA,GAAe,KAAgC,OAASA,EAAW,aACjF,KAAMY,EACN,aAAcC,EACd,YAAuB,OAAK,MAAO,CACjC,YAAuB,OAAK,OAAU,KAAc,CAClD,UAAW,WACX,cAAY,QAAc,KAAc,CAAC,KAAG,KAAKb,EAAY,CAAC,eAAgB,eAAgB,cAAc,CAAC,CAAC,EAAG,CAAC,EAAG,CACnH,OAAQ,SAAgBkB,EAAG,CACzB,IAAIK,EACJvB,GAAe,OAAkCuB,EAAqBvB,EAAW,UAAY,MAAQuB,IAAuB,QAAUA,EAAmB,KAAKvB,EAAYkB,CAAC,EAC3KL,EAAQ,EAAK,CACf,EACA,QAAS,SAAiBK,EAAG,CAC3B,IAAIM,EACJxB,GAAe,OAAkCwB,EAAsBxB,EAAW,WAAa,MAAQwB,IAAwB,QAAUA,EAAoB,KAAKxB,EAAYkB,CAAC,EAC/KL,EAAQ,EAAI,CACd,CACF,CAAC,EACD,cAAeX,EACf,YAAa,CACX,UAAWK,CACb,CACF,EAAGH,CAAI,CAAC,CACV,CAAC,CACH,CAAC,KAEiB,OAAK,OAAU,KAAc,CAC/C,UAAW,WACX,WAAYJ,EACZ,cAAeE,EACf,YAAa,CACX,UAAWK,CACb,CACF,EAAGH,CAAI,CAAC,CACV,EACIqB,EAAqBjB,EACzBiB,EAAmB,SAAWN,EAI9BM,EAAmB,YAAc,mBACjC,IAAeA,C,6KC9HTC,EAA4C,SAAH3B,EAMzC,KALJ4B,EAAI5B,EAAJ4B,KACAf,EAAIb,EAAJa,KAAIgB,EAAA7B,EACJ8B,MAAAA,EAAKD,IAAA,OAAG,MAAKA,EACbE,EAAO/B,EAAP+B,QAAOC,EAAAhC,EACPiC,eAAAA,EAAcD,IAAA,OAAG,GAAKA,EAEtBE,KAAwCC,EAAAA,GAAWP,CAAI,EAA/CQ,EAASF,EAATE,UAAWC,EAASH,EAATG,UAAW1C,EAAKuC,EAALvC,MAC9B2B,KAA4CgB,EAAAA,UAAS,EAAK,EAACf,EAAAgB,EAAAA,EAAAjB,EAAA,GAApDkB,EAAcjB,EAAA,GAAEkB,EAAiBlB,EAAA,GAClCmB,EAAK,eAAArB,EAAAsB,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAC,EAAOC,EAAsB,CAAF,IAAAC,EAAA,OAAAJ,EAAAA,EAAA,EAAAK,KAAA,SAAAC,EAAE,CAAF,cAAAA,EAAAC,KAAAD,EAAAE,KAAE,CAAF,OAChB,GAAvBX,EAAkB,EAAI,EACjBM,EAAY,CAAFG,EAAAE,KAAA,QACbrB,GAAO,MAAPA,EAAU,IAAI,EACdU,EAAkB,EAAK,EAACS,EAAAE,KAAA,gBAAAF,OAAAA,EAAAE,KAAA,EAEHhB,EAAU,EAAC,OAA1BY,OAAAA,EAAME,EAAAG,KAAAH,EAAAE,KAAG,GACTrB,GAAO,YAAPA,EAAUiB,GAAU,IAAI,EAAC,QAC/BP,EAAkB,EAAK,EAAC,QAE1BR,GAAkBI,EAAU,EAAE,EAAC,yBAAAa,EAAAI,KAAA,IAAAR,CAAA,EAChC,mBAXUS,EAAA,QAAAlC,EAAAmC,MAAA,KAAAC,SAAA,MAaX,SACEC,EAAAA,KAACC,EAAAA,EAAK,CACJC,SAAOC,EAAAA,IAAQ,eAAe,EAC9B/B,MAAOA,EACPjB,KAAMA,EACNiD,KAAInB,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAkB,GAAA,QAAAnB,EAAAA,EAAA,EAAAK,KAAA,SAAAe,EAAA,eAAAA,EAAAb,KAAAa,EAAAZ,KAAA,eAAAY,EAAAC,OAAA,SAAYvB,EAAM,EAAI,CAAC,0BAAAsB,EAAAV,KAAA,IAAAS,CAAA,EAAC,CAAD,EAC7BG,SAAQvB,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAsB,GAAA,QAAAvB,EAAAA,EAAA,EAAAK,KAAA,SAAAmB,EAAA,eAAAA,EAAAjB,KAAAiB,EAAAhB,KAAA,eAAAgB,EAAAH,OAAA,SAAYvB,EAAM,CAAC,0BAAA0B,EAAAd,KAAA,IAAAa,CAAA,EAAC,CAAD,EAC7B3B,eAAgBA,EAChB6B,uBAAsB,GACtBC,eAAc,GACdC,SAAQ,GAAAC,YAERd,EAAAA,KAACe,EAAAA,EAAaC,EAAAA,EAAA,GAAK/E,CAAK,CAAG,CAAC,CACvB,CAEX,EAEA,IAAegC,C,gFC1CTgD,KAASC,EAAAA,MAAK,kBAAM,yHAA4B,GAEhDC,EAAqD,SAAClF,EAAU,CACpE,SACE+D,EAAAA,KAACoB,EAAAA,SAAQ,CAACC,YAAUrB,EAAAA,KAACsB,EAAAA,EAAU,EAAE,EAAER,YACjCd,EAAAA,KAACiB,EAAMD,EAAAA,EAAA,GAAK/E,CAAK,CAAG,CAAC,CACb,CAEd,EAEA,IAAekF,C,4OCXFI,EAAkB,UAG1B,CACH,IAAA3D,KAAsCgB,EAAAA,UAA4B,CAAC,CAAC,EAACf,EAAAgB,EAAAA,EAAAjB,EAAA,GAA9D4D,EAAW3D,EAAA,GAAE4D,EAAc5D,EAAA,GAC5B6D,EAAc,SAACxD,EAAkB,CACrC,IAAMyD,EAAU,IAAIC,QAAgB,SAACC,EAASC,EAAW,CACvDL,EAAe,CACbvD,KAAMA,GAAQ,GACdf,KAAM,GACNoB,eAAgB,GAChBF,QAAS,SAACiB,EAAW,CACfA,IAAW,KAAMwC,EAAO,EACvBD,EAAQvC,CAAM,EACnBmC,EAAe,CAAEtE,KAAM,GAAOe,KAAM,EAAG,CAAC,CAC1C,CACF,CAAC,CACH,CAAC,EACD,OAAOyD,CACT,EAEA,MAAO,CAAEH,YAAAA,EAAaE,YAAAA,CAAY,CACpC,C,kOCfMK,EAA0C,SAAHzF,EAOvC,KANJiB,EAAKjB,EAALiB,MACAyE,EAAQ1F,EAAR0F,SAAQC,EAAA3F,EACR4F,SAAAA,EAAQD,IAAA,OAAG,GAAIA,EACfE,EAAS7F,EAAT6F,UACAC,EAAQ9F,EAAR8F,SAAQC,EAAA/F,EACRgG,KAAAA,EAAID,IAAA,OAAG,WAAUA,EAEjBzE,KAA4BgB,EAAAA,UAC1B,OAAOrB,GAAU,SAAW,CAACA,CAAK,EAAIA,GAAS,CAAC,CAClD,EAACM,EAAAgB,EAAAA,EAAAjB,EAAA,GAFM0B,EAAMzB,EAAA,GAAEc,EAASd,EAAA,GAGxB0E,KAAqChB,EAAAA,IAAgB,EAA7CC,EAAWe,EAAXf,YAAaE,EAAWa,EAAXb,YACfc,EAAe,SAACC,EAAwB,CAC5C9D,EAAU8D,CAAS,EACnBT,GAAQ,MAARA,EAAWE,EAAWO,EAAYA,EAAU,CAAC,CAAC,CAChD,KAEAC,EAAAA,WAAU,UAAM,CAEZ/D,EADE,OAAOpB,GAAU,SACT,CAACA,CAAK,EAENA,GAAS,CAAC,CAFH,CAIrB,EAAG,CAACA,CAAK,CAAC,EAEV,IAAMoF,KACJC,EAAAA,MAAA,OACET,UAAU,aACVU,QAAO5D,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAE,SAAAC,GAAA,KAAAqD,EAAA,OAAAvD,EAAAA,EAAA,EAAAK,KAAA,SAAAC,EAAA,eAAAA,EAAAC,KAAAD,EAAAE,KAAA,YACH0C,EAAU,CAAF5C,EAAAE,KAAA,eAAAF,EAAAe,OAAA,iBAAAf,OAAAA,EAAAE,KAAA,EACYgC,EAAY,EAAE,EAAC,OAAxB,GAATe,EAASjD,EAAAG,KACV8C,EAAW,CAAFjD,EAAAE,KAAA,eAAAF,EAAAe,OAAA,qBACVjB,EAAOwD,SAASL,CAAS,EAAG,CAAFjD,EAAAE,KAAA,SAC5BqD,OAAAA,EAAAA,EAAQC,KAAK,CACXD,WAAS5C,EAAAA,IAAQ,gBAAgB,EACjC8C,YAAa,EACf,CAAC,EAACzD,EAAAe,OAAA,kBAGJiC,EAAaN,EAAW,CAAC,EAAJgB,OAAAC,EAAAA,EAAO7D,CAAM,GAAEmD,CAAS,CAAC,EAAG,CAACA,CAAS,CAAC,EAAC,yBAAAjD,EAAAI,KAAA,IAAAR,CAAA,EAC9D,GAAC0B,SAAA,IAEFd,EAAAA,KAAChE,EAAAA,EAAY,EAAE,KACfgE,EAAAA,KAAA,OAAKoD,MAAO,CAAEC,UAAW,CAAE,EAAEvC,SAAC,KAAG,CAAK,CAAC,EACpC,EAGP,SACE8B,EAAAA,MAAAU,EAAAA,SAAA,CAAAxC,SAAA,IACEd,EAAAA,KAACuD,EAAAA,EAAM,CACLC,SAAS,eACTpB,SAAUA,EACVD,UAAS,eAAAe,OAAiBZ,IAAS,WAAa,gBAAkB,GAAE,KAAAY,OAClEf,GAAa,GAAE,KAAAe,OACbhB,EAAW,GAAK5C,GAAM,MAANA,EAAQmE,OAAS,kBAAoB,EAAE,EAC3DvB,SAAUA,EACVwB,SAAUxB,EAAWyB,OAAY,EACjCC,sBAAuB,GACvBC,WAAY,SAACC,EAAM,CAAF,SACf9D,EAAAA,KAAC+D,EAAAA,QAAiB,CAChBC,UAAWF,EAAKG,KAChBC,QAAS,GACTC,UAAW,EAAM,CAClB,CAAC,EAEJnC,SAAU,SAAAoC,EAAkB,KAAfC,EAAQD,EAARC,SACX7B,EAAa6B,EAASC,IAAI,SAAAC,EAAA,KAAGN,EAAIM,EAAJN,KAAI,OAAOA,CAAI,EAAC,CAC/C,EACAI,SAAU/E,EAAOgF,IAAI,SAACE,EAAG,CAAF,MAAM,CAAEC,IAAKD,EAAGP,KAAMO,CAAE,CAAC,CAAC,EACjDE,eAAgB,CACdC,iBAAkB,GAClBC,gBAAiB,GACjBC,eAAgB,EAClB,EAAE/D,SAED6B,CAAY,CACP,KACR3C,EAAAA,KAAC/B,EAAAA,EAAY+C,EAAAA,EAAA,GAAKQ,CAAW,CAAG,CAAC,EACjC,CAEN,EAEA,IAAeO,C,uJC7ETtD,EAAa,SACjBa,EAMG,CACH,IAAA1B,KAAsCgB,EAAAA,UAAmC,EAACf,EAAAgB,EAAAA,EAAAjB,EAAA,GAAnEkH,EAAWjH,EAAA,GAAEkH,EAAclH,EAAA,GAClCmH,KAA8BpG,EAAAA,UAAkB,EAACqG,EAAApG,EAAAA,EAAAmG,EAAA,GAA1CE,EAAOD,EAAA,GAAEE,EAAUF,EAAA,GAC1BG,EAAoBC,EAAAA,EAAIC,OAAO,EAAvBvC,EAAOqC,EAAPrC,QAEFrE,EAAS,eAAApC,EAAA2C,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAC,GAAA,KAAAmG,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA1G,EAAA2G,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAxG,UAAA,OAAAb,EAAAA,EAAA,EAAAK,KAAA,SAAAC,EAAA,eAAAA,EAAAC,KAAAD,EAAAE,KAAA,QAAO6F,OAAAA,EAAuBgB,EAAA9C,OAAA,GAAA8C,EAAA,KAAA5C,OAAA4C,EAAA,GAAG,CAAC,EAACf,EAM/CD,EAJFE,SAAAA,EAAQD,IAAA,OAAG,GAAIA,EAAAE,EAIbH,EAHFI,QAAAA,EAAOD,IAAA,OAAG,GAAKA,EAAAE,EAGbL,EAFFM,SAAAA,EAAQD,IAAA,OAAG,GAAIA,EAAAE,EAEbP,EADFQ,eAAAA,EAAcD,IAAA,OAAG,GAAKA,EAAAtG,EAAAE,KAAA,EAEIwF,GAAO,YAAPA,EAASxG,UAAU,EAAC,OAA7B,GAAbsH,EAAaxG,EAAAG,KACdkG,EAAU,CAAFrG,EAAAE,KAAA,eAAAF,EAAAe,OAAA,SACJyF,GAAiB,EAAK,iBAG3BQ,EAAAA,OAAMR,CAAa,MAAKS,EAAAA,SAAQT,CAAa,GAAC,CAAAxG,EAAAE,KAAA,SAChDqD,OAAAA,EAAQ2D,SAAMvG,EAAAA,IAAQ,iBAAiB,CAAC,EAACX,EAAAe,OAAA,SAClC,EAAK,eAEVwF,GAAkBC,EAAclD,SAAS,GAAG,GAAC,CAAAtD,EAAAE,KAAA,SAC/CqD,OAAAA,EAAQ2D,MAAM,0KAA8B,EAAClH,EAAAe,OAAA,SACtC,EAAK,UAGY,GAAtBjB,EAAS0G,EAAa,CACtBP,EAAU,CAAFjG,EAAAE,KAAA,SAAAF,OAAAA,EAAAE,KAAA,MACgBiH,EAAAA,GAAe,CAACX,CAAa,CAAC,EAAC,QAAAC,EAAAzG,EAAAG,KAAAuG,EAAArH,EAAAA,EAAAoH,EAAA,GAAlDE,EAASD,EAAA,GAChB5G,EAAS6G,EAAS,YAEhBR,EAAS,CAAFnG,EAAAE,KAAA,SAAAF,OAAAA,EAAAE,KAAA,MACuBkH,EAAAA,GAAc,CAACtH,CAAM,CAAC,EAAC,QAAA8G,EAAA5G,EAAAG,KAAA0G,EAAAxH,EAAAA,EAAAuH,EAAA,GAAhDE,EAAeD,EAAA,GACtB/G,EAASgH,EAAe,WAGrBhH,EAAOmE,OAAQ,CAAFjE,EAAAE,KAAA,SAChBqD,OAAAA,EAAQ2D,SAAMvG,EAAAA,IAAQ,sBAAsB,CAAC,EAACX,EAAAe,OAAA,SACvC,EAAK,iBAAAf,EAAAe,OAAA,SAEPjB,CAAM,2BAAAE,EAAAI,KAAA,IAAAR,CAAA,EACd,oBApCc,QAAA9C,EAAAwD,MAAA,KAAAC,SAAA,MAsCTpB,EAAY,SAACW,EAAoB,CACrCyF,EAAe,CAAE8B,OAAQvH,GAAU,EAAG,CAAC,CACzC,EAEA,MAAO,CACL4F,QAAAA,EACAxG,UAAAA,EACAC,UAAAA,EACA1C,MAAO,CACLyC,UAAAA,EACAC,UAAAA,EACAmI,gBAAiB3B,EACjBL,YAAAA,EACAiC,mBAAoBzH,CACtB,CACF,CACF,EAEA,IAAeb,C", "sources": ["webpack://labwise-web/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/pro-form/es/components/Digit/index.js", "webpack://labwise-web/./node_modules/@ant-design/pro-form/es/components/Text/index.js", "webpack://labwise-web/./src/components/MoleculeEditor/EditorDialog.tsx", "webpack://labwise-web/./src/components/MoleculeEditor/LazyKetcher.tsx", "webpack://labwise-web/./src/components/MoleculeEditor/useSmilesEditor.ts", "webpack://labwise-web/./src/components/SmilesInput/index.tsx", "webpack://labwise-web/./src/hooks/useKetcher.ts"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport PlusOutlinedSvg from \"@ant-design/icons-svg/es/asn/PlusOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar PlusOutlined = function PlusOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: PlusOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(PlusOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'PlusOutlined';\n}\nexport default RefIcon;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"fieldProps\", \"min\", \"proFieldProps\", \"max\"];\nimport React from 'react';\nimport ProFormField from \"../Field\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\n/**\n * 数组选择组件\n *\n * @param\n */\nvar ProFormDigit = function ProFormDigit(_ref, ref) {\n  var fieldProps = _ref.fieldProps,\n    min = _ref.min,\n    proFieldProps = _ref.proFieldProps,\n    max = _ref.max,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/_jsx(ProFormField, _objectSpread({\n    valueType: \"digit\",\n    fieldProps: _objectSpread({\n      min: min,\n      max: max\n    }, fieldProps),\n    ref: ref,\n    filedConfig: {\n      defaultProps: {\n        width: '100%'\n      }\n    },\n    proFieldProps: proFieldProps\n  }, rest));\n};\nvar ForwardRefProFormDigit = /*#__PURE__*/React.forwardRef(ProFormDigit);\nexport default ForwardRefProFormDigit;", "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"fieldProps\", \"proFieldProps\"],\n  _excluded2 = [\"fieldProps\", \"proFieldProps\"];\nimport { useMountMergeState } from '@ant-design/pro-utils';\nimport { Form, Popover } from 'antd';\nimport omit from 'omit.js';\nimport React, { useState } from 'react';\nimport ProField from \"../Field\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nvar valueType = 'text';\n/**\n * 文本组件\n *\n * @param\n */\nvar ProFormText = function ProFormText(_ref) {\n  var fieldProps = _ref.fieldProps,\n    proFieldProps = _ref.proFieldProps,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/_jsx(ProField, _objectSpread({\n    valueType: valueType,\n    fieldProps: fieldProps,\n    filedConfig: {\n      valueType: valueType\n    },\n    proFieldProps: proFieldProps\n  }, rest));\n};\nvar PassWordStrength = function PassWordStrength(props) {\n  var _useMountMergeState = useMountMergeState(props.open || false, {\n      value: props.open,\n      onChange: props.onOpenChange\n    }),\n    _useMountMergeState2 = _slicedToArray(_useMountMergeState, 2),\n    open = _useMountMergeState2[0],\n    setOpen = _useMountMergeState2[1];\n  return /*#__PURE__*/_jsx(Form.Item, {\n    shouldUpdate: true,\n    noStyle: true,\n    children: function children(form) {\n      var _props$statusRender;\n      var value = form.getFieldValue(props.name || []);\n      return /*#__PURE__*/_jsx(Popover, _objectSpread(_objectSpread({\n        getPopupContainer: function getPopupContainer(node) {\n          if (node && node.parentNode) {\n            return node.parentNode;\n          }\n          return node;\n        },\n        onOpenChange: function onOpenChange(e) {\n          return setOpen(e);\n        },\n        content: /*#__PURE__*/_jsxs(\"div\", {\n          style: {\n            padding: '4px 0'\n          },\n          children: [(_props$statusRender = props.statusRender) === null || _props$statusRender === void 0 ? void 0 : _props$statusRender.call(props, value), props.strengthText ? /*#__PURE__*/_jsx(\"div\", {\n            style: {\n              marginTop: 10\n            },\n            children: /*#__PURE__*/_jsx(\"span\", {\n              children: props.strengthText\n            })\n          }) : null]\n        }),\n        overlayStyle: {\n          width: 240\n        },\n        placement: \"rightTop\"\n      }, props.popoverProps), {}, {\n        open: open,\n        children: props.children\n      }));\n    }\n  });\n};\nvar Password = function Password(_ref2) {\n  var fieldProps = _ref2.fieldProps,\n    proFieldProps = _ref2.proFieldProps,\n    rest = _objectWithoutProperties(_ref2, _excluded2);\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    open = _useState2[0],\n    setOpen = _useState2[1];\n  if (fieldProps !== null && fieldProps !== void 0 && fieldProps.statusRender && rest.name) {\n    return /*#__PURE__*/_jsx(PassWordStrength, {\n      name: rest.name,\n      statusRender: fieldProps === null || fieldProps === void 0 ? void 0 : fieldProps.statusRender,\n      popoverProps: fieldProps === null || fieldProps === void 0 ? void 0 : fieldProps.popoverProps,\n      strengthText: fieldProps === null || fieldProps === void 0 ? void 0 : fieldProps.strengthText,\n      open: open,\n      onOpenChange: setOpen,\n      children: /*#__PURE__*/_jsx(\"div\", {\n        children: /*#__PURE__*/_jsx(ProField, _objectSpread({\n          valueType: \"password\",\n          fieldProps: _objectSpread(_objectSpread({}, omit(fieldProps, ['statusRender', 'popoverProps', 'strengthText'])), {}, {\n            onBlur: function onBlur(e) {\n              var _fieldProps$onBlur;\n              fieldProps === null || fieldProps === void 0 || (_fieldProps$onBlur = fieldProps.onBlur) === null || _fieldProps$onBlur === void 0 || _fieldProps$onBlur.call(fieldProps, e);\n              setOpen(false);\n            },\n            onClick: function onClick(e) {\n              var _fieldProps$onClick;\n              fieldProps === null || fieldProps === void 0 || (_fieldProps$onClick = fieldProps.onClick) === null || _fieldProps$onClick === void 0 || _fieldProps$onClick.call(fieldProps, e);\n              setOpen(true);\n            }\n          }),\n          proFieldProps: proFieldProps,\n          filedConfig: {\n            valueType: valueType\n          }\n        }, rest))\n      })\n    });\n  }\n  return /*#__PURE__*/_jsx(ProField, _objectSpread({\n    valueType: \"password\",\n    fieldProps: fieldProps,\n    proFieldProps: proFieldProps,\n    filedConfig: {\n      valueType: valueType\n    }\n  }, rest));\n};\nvar WrappedProFormText = ProFormText;\nWrappedProFormText.Password = Password;\n\n// @ts-ignore\n// eslint-disable-next-line no-param-reassign\nWrappedProFormText.displayName = 'ProFormComponent';\nexport default WrappedProFormText;", "/* eslint-disable @typescript-eslint/no-unused-expressions */\nimport useKetcher from '@/hooks/useKetcher'\nimport { getWord } from '@/utils'\nimport { Modal } from 'antd'\nimport React, { useState } from 'react'\nimport KetcherEditor from './LazyKetcher'\nimport type { EditorDialogProps } from './type'\nconst EditorDialog: React.FC<EditorDialogProps> = ({\n  init,\n  open,\n  width = '80%',\n  onClose,\n  clearWhenClose = false\n}) => {\n  const { getSmiles, setSmiles, props } = useKetcher(init)\n  const [confirmLoading, setConfirmLoading] = useState(false)\n  const close = async (withSmiles?: boolean) => {\n    setConfirmLoading(true)\n    if (!withSmiles) {\n      onClose?.(null)\n      setConfirmLoading(false)\n    } else {\n      const smiles = await getSmiles()\n      await onClose?.(smiles || null)\n      setConfirmLoading(false)\n    }\n    clearWhenClose && setSmiles('')\n  }\n\n  return (\n    <Modal\n      title={getWord('edit-molecule')}\n      width={width}\n      open={open}\n      onOk={async () => close(true)}\n      onCancel={async () => close()}\n      confirmLoading={confirmLoading}\n      focusTriggerAfterClose\n      destroyOnClose\n      centered\n    >\n      <KetcherEditor {...props} />\n    </Modal>\n  )\n}\n\nexport default EditorDialog\n", "import React, { Suspense, lazy } from 'react'\nimport LoadingTip from '../LoadingTip'\nimport { KetcherWithInputEditorProps } from './type'\n\nconst Editor = lazy(() => import('./KetcherWithInput'))\n\nconst LazyKetcher: React.FC<KetcherWithInputEditorProps> = (props) => {\n  return (\n    <Suspense fallback={<LoadingTip />}>\n      <Editor {...props} />\n    </Suspense>\n  )\n}\n\nexport default LazyKetcher\n", "import { useState } from 'react'\nimport { EditorDialogProps } from './EditorDialog'\n\nexport const useSmilesEditor = (): {\n  dialogProps: EditorDialogProps\n  inputSmiles: (smiles?: string) => Promise<string>\n} => {\n  const [dialogProps, setDialogProps] = useState<EditorDialogProps>({})\n  const inputSmiles = (init?: string) => {\n    const promise = new Promise<string>((resolve, reject) => {\n      setDialogProps({\n        init: init || '',\n        open: true,\n        clearWhenClose: true,\n        onClose: (smiles) => {\n          if (smiles === null) reject()\n          else resolve(smiles)\n          setDialogProps({ open: false, init: '' })\n        }\n      })\n    })\n    return promise\n  }\n\n  return { dialogProps, inputSmiles }\n}\n", "import { useSmilesEditor } from '@/components/MoleculeEditor'\nimport EditorDialog from '@/components/MoleculeEditor/EditorDialog'\nimport MoleculeStructure from '@/components/MoleculeStructure'\nimport { getWord } from '@/utils'\nimport message from '@/utils/message'\nimport { PlusOutlined } from '@ant-design/icons'\nimport { Upload } from 'antd'\nimport React, { useEffect, useState } from 'react'\nimport type { SmilesInputProps } from './index.d'\n\nconst SmilesInput: React.FC<SmilesInputProps> = ({\n  value,\n  onChange,\n  multiple = true,\n  className,\n  disabled,\n  type = 'molecule'\n}) => {\n  const [smiles, setSmiles] = useState<string[]>(\n    typeof value === 'string' ? [value] : value || []\n  )\n  const { dialogProps, inputSmiles } = useSmilesEditor()\n  const updateSmiles = (newSmiles: string[]) => {\n    setSmiles(newSmiles)\n    onChange?.(multiple ? newSmiles : newSmiles[0])\n  }\n\n  useEffect(() => {\n    if (typeof value === 'string') {\n      setSmiles([value])\n    } else {\n      setSmiles(value || [])\n    }\n  }, [value])\n\n  const uploadButton = (\n    <div\n      className=\"add-button\"\n      onClick={async () => {\n        if (disabled) return\n        const newSmiles = await inputSmiles('')\n        if (!newSmiles) return\n        if (smiles.includes(newSmiles)) {\n          message.warn({\n            message: getWord('molecule-exist'),\n            description: ''\n          })\n          return\n        }\n        updateSmiles(multiple ? [...smiles, newSmiles] : [newSmiles])\n      }}\n    >\n      <PlusOutlined />\n      <div style={{ marginTop: 8 }}>Add</div>\n    </div>\n  )\n\n  return (\n    <>\n      <Upload<string>\n        listType=\"picture-card\"\n        disabled={disabled}\n        className={`smiles-list ${type === 'reaction' ? 'reaction-list' : ''} ${\n          className || ''\n        } ${multiple ? '' : smiles?.length ? 'hide-upload-btn' : ''}`}\n        multiple={multiple}\n        maxCount={multiple ? undefined : 1}\n        openFileDialogOnClick={false}\n        iconRender={(file) => (\n          <MoleculeStructure\n            structure={file.name}\n            copyBtn={false}\n            expandBtn={false}\n          />\n        )}\n        onChange={({ fileList }) => {\n          updateSmiles(fileList.map(({ name }) => name))\n        }}\n        fileList={smiles.map((s) => ({ uid: s, name: s }))}\n        showUploadList={{\n          showDownloadIcon: false,\n          showPreviewIcon: false,\n          showRemoveIcon: true\n        }}\n      >\n        {uploadButton}\n      </Upload>\n      <EditorDialog {...dialogProps} />\n    </>\n  )\n}\n\nexport default SmilesInput\n", "import { KetcherWithInputEditorProps } from '@/components/MoleculeEditor/type'\nimport { getWord } from '@/utils'\nimport { inchifySmiles, kekulizeSmiles } from '@/utils/smiles'\nimport { App } from 'antd'\nimport { <PERSON><PERSON><PERSON> } from 'ketcher-core'\nimport { isEmpty, isNil } from 'lodash'\nimport { useState } from 'react'\n\nexport interface GetSmilesConfig {\n  kekulize?: boolean\n  inchify?: boolean\n  validate?: boolean\n  forbidMultiple?: boolean\n}\n\nconst useKetcher = (\n  smiles?: string\n): {\n  props: KetcherWithInputEditorProps\n  ketcher?: Ketcher\n  getSmiles: (config?: GetSmilesConfig) => Promise<string | false>\n  setSmiles: (smiles: string) => void\n} => {\n  const [updateEvent, setUpdateEvent] = useState<Record<'update', string>>()\n  const [ketcher, setKetcher] = useState<Ketcher>()\n  const { message } = App.useApp()\n\n  const getSmiles = async (config: GetSmilesConfig = {}) => {\n    const {\n      kekulize = true,\n      inchify = false,\n      validate = true,\n      forbidMultiple = false\n    } = config\n    const ketcherSmiles = await ketcher?.getSmiles()\n    if (!validate) {\n      return ketcherSmiles || false\n    }\n\n    if (isNil(ketcherSmiles) || isEmpty(ketcherSmiles)) {\n      message.error(getWord('no-molecule-tip'))\n      return false\n    }\n    if (forbidMultiple && ketcherSmiles.includes('.')) {\n      message.error('一次只能创建一个分子，如果您想创建多个分子，请分多次创建')\n      return false\n    }\n\n    let smiles = ketcherSmiles\n    if (kekulize) {\n      const [kekulized] = await kekulizeSmiles([ketcherSmiles])\n      smiles = kekulized\n    }\n    if (inchify) {\n      const [inchifiedSmiles] = await inchifySmiles([smiles])\n      smiles = inchifiedSmiles\n    }\n\n    if (!smiles.length) {\n      message.error(getWord('valid-molecule-enter'))\n      return false\n    }\n    return smiles\n  }\n\n  const setSmiles = (smiles?: string) => {\n    setUpdateEvent({ update: smiles || '' })\n  }\n\n  return {\n    ketcher,\n    getSmiles,\n    setSmiles,\n    props: {\n      getSmiles,\n      setSmiles,\n      onKetcherUpdate: setKetcher,\n      updateEvent,\n      initMoleculeSmiles: smiles\n    }\n  }\n}\n\nexport default useKetcher\n"], "names": ["PlusOutlined", "props", "ref", "RefIcon", "_excluded", "ProFormDigit", "_ref", "fieldProps", "min", "proFieldProps", "max", "rest", "ForwardRefProFormDigit", "_excluded2", "valueType", "ProFormText", "PassWordStrength", "_useMountMergeState", "_useMountMergeState2", "open", "<PERSON><PERSON><PERSON>", "form", "_props$statusRender", "value", "node", "e", "Password", "_ref2", "_useState", "_useState2", "_fieldProps$onBlur", "_fieldProps$onClick", "WrappedProFormText", "EditorDialog", "init", "_ref$width", "width", "onClose", "_ref$clearWhenClose", "clearWhenClose", "_useKetcher", "useKetcher", "getSmiles", "setSmiles", "useState", "_slicedToArray", "confirmLoading", "setConfirmLoading", "close", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "withSmiles", "smiles", "wrap", "_context", "prev", "next", "sent", "stop", "_x", "apply", "arguments", "_jsx", "Modal", "title", "getWord", "onOk", "_callee2", "_context2", "abrupt", "onCancel", "_callee3", "_context3", "focusTriggerAfterClose", "destroyOnClose", "centered", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_objectSpread", "Editor", "lazy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Suspense", "fallback", "LoadingTip", "useSmilesEditor", "dialogProps", "setDialogProps", "inputSmiles", "promise", "Promise", "resolve", "reject", "SmilesInput", "onChange", "_ref$multiple", "multiple", "className", "disabled", "_ref$type", "type", "_useSmilesEditor", "updateSmiles", "newSmiles", "useEffect", "uploadButton", "_jsxs", "onClick", "includes", "message", "warn", "description", "concat", "_toConsumableArray", "style", "marginTop", "_Fragment", "Upload", "listType", "length", "maxCount", "undefined", "openFileDialogOnClick", "iconRender", "file", "MoleculeStructure", "structure", "name", "copyBtn", "expandBtn", "_ref3", "fileList", "map", "_ref4", "s", "uid", "showUploadList", "showDownloadIcon", "showPreviewIcon", "showRemoveIcon", "updateEvent", "setUpdateEvent", "_useState3", "_useState4", "ketcher", "<PERSON><PERSON><PERSON><PERSON>", "_App$useApp", "App", "useApp", "config", "_config$kekulize", "kekulize", "_config$inchify", "inchify", "_config$validate", "validate", "_config$forbidMultipl", "forbidMultiple", "ketcherSmiles", "_yield$kekulizeSmiles", "_yield$kekulizeSmiles2", "kekulized", "_yield$inchifySmiles", "_yield$inchifySmiles2", "inchifiedSmiles", "_args", "isNil", "isEmpty", "error", "kekulizeSmiles", "inchifySmiles", "update", "onKetcherUpdate", "initMoleculeSmiles"], "sourceRoot": ""}