{"version": 3, "file": "shared-VqxfXAGlh0atJoLJMmPncga4F9U_.6ef1929c.async.js", "mappings": "gKAMIA,EAAgB,SAAuBC,EAAOC,EAAK,CACrD,OAAoB,gBAAoB,OAAU,QAAc,KAAc,CAAC,EAAGD,CAAK,EAAG,CAAC,EAAG,CAC5F,IAAKC,EACL,KAAM,GACR,CAAC,CAAC,CACJ,EACIC,EAAuB,aAAiBH,CAAa,EAIzD,IAAeG,C,uECVXC,EAAe,SAAsBH,EAAOC,EAAK,CACnD,OAAoB,gBAAoB,OAAU,QAAc,KAAc,CAAC,EAAGD,CAAK,EAAG,CAAC,EAAG,CAC5F,IAAKC,EACL,KAAM,GACR,CAAC,CAAC,CACJ,EACIC,EAAuB,aAAiBC,CAAY,EAIxD,IAAeD,C,uECVXE,EAAe,SAAsBJ,EAAOC,EAAK,CACnD,OAAoB,gBAAoB,OAAU,QAAc,KAAc,CAAC,EAAGD,CAAK,EAAG,CAAC,EAAG,CAC5F,IAAKC,EACL,KAAM,GACR,CAAC,CAAC,CACJ,EACIC,EAAuB,aAAiBE,CAAY,EAIxD,IAAeF,C,2HCOTG,EAA4D,SAA5DA,EAAyDC,EAMzD,KALJC,EAAID,EAAJC,KAAIC,EAAAF,EACJC,KAAQE,EAAKD,EAALC,MAAOC,EAAKF,EAALE,MAAOC,EAAGH,EAAHG,IACtBC,EAAWN,EAAXM,YAAWC,EAAAP,EACXQ,eAAAA,EAAcD,IAAA,OAAG,CAAC,EAACA,EAAAE,EAAAT,EACnBU,YAAAA,EAAWD,IAAA,OAAG,CAAC,EAACA,EAERE,EAAwBH,EAAxBG,UAAWC,EAAaJ,EAAbI,SACXC,EAAsCH,EAAtCG,iBAAkBC,EAAoBJ,EAApBI,gBAEpBC,KACJC,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,IACEF,EAAAA,MAAA,MAAAG,EAAAA,EAAAA,EAAAA,EAAA,GAASN,CAAgB,MAAAK,SAAA,IACvBE,EAAAA,KAACC,EAAAA,QAA6B,CAC5BV,UAAWR,EACXmB,UAAU,6BAA6B,CACxC,EACAX,GAAS,YAATA,EAAYR,EAAOF,CAAI,CAAC,EAAC,CACvB,EACJG,MACCY,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,IACEF,EAAAA,MAAA,MAAAG,EAAAA,EAAAA,EAAAA,EAAA,CACEI,MAAOlB,CAAI,EACPS,CAAe,MACnBQ,UAAS,iBAAAE,OAAmBV,GAAe,YAAfA,EAAiBQ,SAAS,EAAGJ,SAAA,IAEzDE,EAAAA,KAACK,EAAAA,EAAK,EAAE,EACPb,GAAQ,YAARA,EAAWT,EAAOC,EAAMD,MAAOF,CAAI,CAAC,EAAC,CACnC,KACLmB,EAAAA,KAACrB,EAAoB,CACnBE,KAAMG,EACNI,eAAgBA,EAChBE,YAAaA,CAAY,CAC1B,CAAC,EACF,CACH,EACD,EAEJ,OAAOJ,KACLc,EAAAA,KAAA,OAAKE,UAAU,mCAAkCJ,SAAEH,CAAO,CAAM,KAEhEK,EAAAA,KAAAH,EAAAA,SAAA,CAAAC,SAAGH,CAAO,CAAG,CAEjB,EAEA,EAAehB,C,sJCpEX2B,EAAY,OAAO,eACnBC,EAAsB,OAAO,sBAC7BC,EAAe,OAAO,UAAU,eAChCC,EAAe,OAAO,UAAU,qBAChCC,EAAkB,CAACC,EAAKC,EAAK7B,IAAU6B,KAAOD,EAAML,EAAUK,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAA7B,CAAM,CAAC,EAAI4B,EAAIC,CAAG,EAAI7B,EACtJ8B,EAAiB,CAACC,EAAGC,IAAM,CAC7B,QAASC,KAAQD,IAAMA,EAAI,CAAC,GACtBP,EAAa,KAAKO,EAAGC,CAAI,GAC3BN,EAAgBI,EAAGE,EAAMD,EAAEC,CAAI,CAAC,EACpC,GAAIT,EACF,QAASS,KAAQT,EAAoBQ,CAAC,EAChCN,EAAa,KAAKM,EAAGC,CAAI,GAC3BN,EAAgBI,EAAGE,EAAMD,EAAEC,CAAI,CAAC,EAEtC,OAAOF,CACT,EAEA,MAAMG,EAAgB3C,GAA0B,gBAAoB,MAAOuC,EAAe,CAAE,UAAW,sBAAuB,QAAS,gBAAiB,MAAO,4BAA6B,EAAGvC,CAAK,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,ueAAwe,KAAM,SAAU,CAAC,CAAC,EAElvB,MAAe,qzBCnBX,EAAY,OAAO,eACnB,EAAsB,OAAO,sBAC7B,EAAe,OAAO,UAAU,eAChC,EAAe,OAAO,UAAU,qBAChC,EAAkB,CAACqC,EAAKC,EAAK7B,IAAU6B,KAAOD,EAAM,EAAUA,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAA7B,CAAM,CAAC,EAAI4B,EAAIC,CAAG,EAAI7B,EACtJ,EAAiB,CAAC+B,EAAGC,IAAM,CAC7B,QAASC,KAAQD,IAAMA,EAAI,CAAC,GACtB,EAAa,KAAKA,EAAGC,CAAI,GAC3B,EAAgBF,EAAGE,EAAMD,EAAEC,CAAI,CAAC,EACpC,GAAI,EACF,QAASA,KAAQ,EAAoBD,CAAC,EAChC,EAAa,KAAKA,EAAGC,CAAI,GAC3B,EAAgBF,EAAGE,EAAMD,EAAEC,CAAI,CAAC,EAEtC,OAAOF,CACT,EAEA,MAAMI,EAAY5C,GAA0B,gBAAoB,MAAO,EAAe,CAAE,QAAS,YAAa,KAAM,OAAQ,MAAO,4BAA6B,EAAGA,CAAK,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,qKAAsK,CAAC,CAAC,EAEpY,MAAe,iX,YCnBX,EAAY,OAAO,eACnB,GAAsB,OAAO,sBAC7B,GAAe,OAAO,UAAU,eAChC,GAAe,OAAO,UAAU,qBAChC,GAAkB,CAACqC,EAAKC,EAAK7B,IAAU6B,KAAOD,EAAM,EAAUA,EAAKC,EAAK,CAAE,WAAY,GAAM,aAAc,GAAM,SAAU,GAAM,MAAA7B,CAAM,CAAC,EAAI4B,EAAIC,CAAG,EAAI7B,EACtJ,GAAiB,CAAC+B,EAAGC,IAAM,CAC7B,QAASC,KAAQD,IAAMA,EAAI,CAAC,GACtB,GAAa,KAAKA,EAAGC,CAAI,GAC3B,GAAgBF,EAAGE,EAAMD,EAAEC,CAAI,CAAC,EACpC,GAAI,GACF,QAASA,KAAQ,GAAoBD,CAAC,EAChC,GAAa,KAAKA,EAAGC,CAAI,GAC3B,GAAgBF,EAAGE,EAAMD,EAAEC,CAAI,CAAC,EAEtC,OAAOF,CACT,EAEA,MAAMK,GAAU7C,GAA0B,gBAAoB,MAAO,GAAe,CAAE,QAAS,YAAa,KAAM,OAAQ,MAAO,4BAA6B,EAAGA,CAAK,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,wFAAyF,CAAC,EAAmB,gBAAoB,OAAQ,CAAE,EAAG,gIAAiI,CAAC,CAAC,EAE3e,OAAe,ic,8IClBX8C,GAAe,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,iXAAkX,CAAE,CAAC,CAAE,EAAG,KAAQ,OAAQ,MAAS,UAAW,EACxjB,GAAeA,G,YCIX,GAAe,SAAsB9C,EAAOC,EAAK,CACnD,OAAoB,gBAAoB8C,GAAA,KAAU,SAAc,MAAc,CAAC,EAAG/C,CAAK,EAAG,CAAC,EAAG,CAC5F,IAAKC,EACL,KAAM,EACR,CAAC,CAAC,CACJ,EACIC,GAAuB,aAAiB,EAAY,EAIxD,GAAeA,GCfX8C,GAAe,CAAE,KAAQ,CAAE,IAAO,MAAO,MAAS,CAAE,QAAW,gBAAiB,UAAa,OAAQ,EAAG,SAAY,CAAC,CAAE,IAAO,OAAQ,MAAS,CAAE,EAAK,0lBAA2lB,CAAE,CAAC,CAAE,EAAG,KAAQ,OAAQ,MAAS,UAAW,EACjyB,GAAeA,GCIX,GAAe,SAAsBhD,EAAOC,EAAK,CACnD,OAAoB,gBAAoB8C,GAAA,KAAU,SAAc,MAAc,CAAC,EAAG/C,CAAK,EAAG,CAAC,EAAG,CAC5F,IAAKC,EACL,KAAM,EACR,CAAC,CAAC,CACJ,EACI,GAAuB,aAAiB,EAAY,EAIxD,GAAe,G,wJCff,EAAe,CAAC,aAAa,qBAAqB,YAAY,oBAAoB,UAAY,oBAAoB,UAAY,oBAAoB,aAAa,oBAAoB,E,WCyDnLgD,MAAqBC,EAAAA,IAAiB,EAA9BC,GAAQF,GAARE,SACR,SAASC,EAAW9C,EAAyD,KAAtD+C,EAAI/C,EAAJ+C,KAAMxB,EAAKvB,EAALuB,MAC3B,OAAOsB,IAAY,QACjBzB,EAAAA,KAAC4B,EAAAA,EAAO,CAACC,QAAS1B,EAAML,SAAE6B,CAAI,CAAU,KAExC/B,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,CACG6B,EAAK,IAAExB,CAAK,EACb,CAEN,CAEA,IAAM2B,GAGF,CACFC,SAAU,CAAC,EACXC,UAAW,CAAC,UAAU,EACtBC,QAAS,CAAC,UAAU,EACpBC,SAAU,CAAC,CACb,EAEMC,GAAgC,SAAHC,EAkB7B,KAjBJC,EAAID,EAAJC,KACAC,GAAKF,EAALE,MACAC,GAAOH,EAAPG,QAAOC,GAAAJ,EACPK,QAAAA,GAAOD,KAAA,OAAGF,MAAQI,GAAAA,IAAiBJ,EAAK,EAAI,EAACE,GAAAG,GAAAP,EAC7CQ,UAAAA,GAASD,KAAA,OAAGL,MAAQO,GAAAA,IAAaP,EAAK,EAAI,GAAKK,GAC/CG,GAAWV,EAAXU,YACAC,GAAUX,EAAVW,WACAC,GAAYZ,EAAZY,aACAC,GAASb,EAATa,UACAC,GAAQd,EAARc,SACAC,GAASf,EAATe,UACAC,EAAOhB,EAAPgB,QACAC,GAAKjB,EAALiB,MACAC,EAAWlB,EAAXkB,YACAC,GAAmBnB,EAAnBmB,oBACAC,GAAcpB,EAAdoB,eACAC,GAAWrB,EAAXqB,YAEAC,MAAwCC,EAAAA,iBAAgB,EAACC,GAAAC,EAAAA,EAAAH,GAAA,GAAlDI,GAAYF,GAAA,GAAEG,GAAeH,GAAA,GACpCI,MAAkBC,GAAAA,GAAc,EAAxBC,GAAKF,GAALE,MACRC,KAQIC,EAAAA,UAAS,UAAU,EAPrBC,GAAcF,EAAdE,eACAC,GAAeH,EAAfG,gBACAC,GAASJ,EAATI,UACAC,GAAaL,EAAbK,cACAC,GAAcN,EAAdM,eACAC,GAAcP,EAAdO,eACAC,EAAcR,EAAdQ,eAEIC,KAASC,EAAAA,WAAU,EACzBC,MAAyBV,EAAAA,UAAS,gBAAgB,EAA1CW,GAAYD,GAAZC,aACRC,MAAsCC,EAAAA,WAGnC,EAHSC,GAASF,GAAbG,GAAeC,EAAUJ,GAAVI,WAKjBC,IAAmBV,GAAc,YAAdA,EAAgBW,UAAW,UAE9CC,GAAM,eAAAC,EAAAC,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAC,GAAA,KAAAC,EAAA,OAAAH,EAAAA,EAAA,EAAAI,KAAA,SAAAC,EAAA,eAAAA,EAAAC,KAAAD,EAAAE,KAAA,QACTJ,OAAAA,KAAiBK,EAAAA,IAAMpC,GAAaqC,IAAI,UAAU,GAAK,EAAE,GAAK,GAClE9B,GAAe,CACb+B,KAAM,EACNC,SAAUR,CACZ,CAAgB,EAACE,EAAAE,KAAA,EACX3B,MAAgB4B,EAAAA,IAAMd,CAAU,CAAW,EAAC,OAClDrB,GACE,CAAEqC,KAAM,IAAKC,SAAUR,EAAeS,SAAS,CAAE,EACjD,CAAEC,QAAS,EAAK,CAClB,EAAC,wBAAAR,EAAAS,KAAA,IAAAZ,CAAA,EACF,oBAXW,QAAAJ,EAAAiB,MAAA,KAAAC,SAAA,MAaNC,GAAkB,eAAAC,EAAAnB,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAkB,GAAA,KAAAC,EAAAC,EAAA,OAAArB,EAAAA,EAAA,EAAAI,KAAA,SAAAkB,EAAA,eAAAA,EAAAhB,KAAAgB,EAAAf,KAAA,aACrB,CAACb,GAAc,CAAChC,GAAO,CAAA4D,EAAAf,KAAA,eAAAe,EAAAC,OAAA,iBAAAD,OAAAA,EAAAf,KAAA,EACJ/B,MACrBgD,GAAAA,SAAyB,mBAAmB,EAAEC,OAAO/B,EAAY,CAC/DgC,cAAehE,CACjB,CAAC,CACH,EAAC,OAAA0D,EAAAE,EAAAK,KAJON,EAAID,EAAJC,KAKJA,IACFO,GAAAA,GAAQC,WAAQC,EAAAA,IAAQ,mBAAmB,CAAC,EAC5CjC,GAAO,GACR,wBAAAyB,EAAAR,KAAA,IAAAK,CAAA,EACF,oBAXuB,QAAAD,EAAAH,MAAA,KAAAC,SAAA,MAalBe,GAAe,eAAAC,EAAAjC,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAgC,GAAA,KAAAC,EAAAC,EAAApE,EAAAqE,EAAAf,EAAAgB,EAAA,OAAArC,EAAAA,EAAA,EAAAI,KAAA,SAAAkC,EAAA,eAAAA,EAAAhC,KAAAgC,EAAA/B,KAAA,YAClBZ,GAAkB,CAAF2C,EAAA/B,KAAA,eAAA+B,EAAAf,OAAA,iBACoB,GAAlCxD,EAAciB,GAAerC,CAAI,EAAC,CACvBoB,EAAW,CAAAuE,EAAA/B,KAAA,QAAA+B,OAAAA,EAAA/B,KAAA,KAClBiB,GAAAA,SAAQ,2BAA2B,EAAEe,UACzC5F,GAAI,OAAAuF,EAAJvF,EAAM6F,6BAAyB,MAAAN,IAAA,SAAAA,EAA/BA,EAAkC,CAAC,KAAC,MAAAA,IAAA,cAApCA,EAAsCzC,EACxC,EAAC,OAAA6C,EAAAG,GAAAH,EAAAX,KAAAW,EAAA/B,KAAA,gBAAA+B,OAAAA,EAAA/B,KAAA,MACKiB,GAAAA,SAAQ,2BAA2B,EAAEkB,OAAO,CAChDC,QAASC,OAAOvD,IAAY,OAAA8C,EAAZ9C,GAAcwD,YAAQ,MAAAV,IAAA,cAAtBA,EAAwB1C,EAAE,EAC1CqD,eAAgBpF,CAClB,CAAC,EAAC,QAAA4E,EAAAG,GAAAH,EAAAX,KAAA,QAAAS,EAAAE,EAAAG,GAPEpB,EAAIe,EAAJf,KAQJA,IACEgB,KAAwDU,EAAAA,WAC1DpG,GAAI,YAAJA,EAAM6F,yBACR,EACIzE,EACFsE,EAA6B,CAAC,EAE9BA,EAA2BW,QAAQ,CACjCvD,GAAI4B,GAAI,YAAJA,EAAM5B,GACVkD,QAAStB,GAAI,YAAJA,EAAMsB,OACjB,CAAC,EAEH5D,GAAerB,EAAS2E,CAA0B,EAClDT,GAAAA,GAAQC,WAAQC,EAAAA,IAAQ,iBAAiB,CAAC,GAC3C,yBAAAQ,EAAAxB,KAAA,IAAAmB,CAAA,EACF,oBA1BoB,QAAAD,EAAAjB,MAAA,KAAAC,SAAA,MA4BfiC,GAAkB,eAAAC,EAAAnD,EAAAA,EAAAC,EAAAA,EAAA,EAAAC,KAAG,SAAAkD,EAAOvD,EAA4B,CAAF,IAAAwD,EAAA/B,EAAA,OAAArB,EAAAA,EAAA,EAAAI,KAAA,SAAAiD,EAAE,CAAF,cAAAA,EAAA/C,KAAA+C,EAAA9C,KAAE,CAAF,UACrD7C,EAAS,CAAF2F,EAAA9C,KAAA,eAAA8C,EAAA9B,OAAA,iBAAA8B,OAAAA,EAAA9C,KAAA,EACW/B,MACrBgD,GAAAA,SAAsB,gBAAgB,EAAEC,OAAO/D,EAAS,CAAEkC,OAAAA,CAAO,CAAC,CACpE,EAAC,OAAAwD,EAAAC,EAAA1B,KAFON,EAAI+B,EAAJ/B,KAGJA,IACFO,GAAAA,GAAQC,WAAQC,EAAAA,IAAQ,uBAAuB,CAAC,EAChDjC,GAAO,GACR,wBAAAwD,EAAAvC,KAAA,IAAAqC,CAAA,EACF,mBATuBG,EAAA,QAAAJ,EAAAnC,MAAA,KAAAC,SAAA,MAWxBuC,MAAqBC,EAAAA,aAAY,EAAzBC,GAAQF,GAARE,SACFC,GAAwBD,GAASE,SAAS,aAAa,EACvDC,MAAiBC,EAAAA,IAAc,YAAY,EAC7CC,KAAKC,SAAMF,EAAAA,IAAc,YAAY,CAAC,EACtC,CAAC,EACCG,GAAa,UAAM,CACvB,IAAIC,EAA2B,CAAC,EAAJvJ,OAAAwJ,EAAAA,EAAON,EAAc,GAAElG,CAAO,CAAC,KAC3DyG,EAAAA,IAAc,aAAcL,KAAKM,UAAUH,CAAc,CAAC,EACtDpF,KAAc,cAChBwF,EAAAA,QAAQC,KACNZ,GAAY,wCAAAhJ,OACgCgD,CAAO,eAAAhD,OAClC8E,GAAS,cAAA9E,OAAagF,EAAU,sBAAAhF,OAAqBgD,CAAO,CAC/E,EAEA2G,EAAAA,QAAQC,KAAK,aAAD5J,OACG8E,GAAS,cAAA9E,OAAagF,EAAU,KAAAhF,OAC3CkD,IAAgB,UAAY,OAAS,OAAM,KAAAlD,OACzCgD,CAAO,CACb,CAEJ,EAEM6G,GAAkBjH,IAAgBkH,OAAOlH,EAAY,EAAI,EACzDmH,GAAY,SAAHC,EAA2C,KAAAC,EAAAC,EAAAC,EAAAC,EAArCC,EAAML,EAANK,OACbC,EAAcT,GAAe,GAAA7J,UAC5BoH,EAAAA,IAAQ,SAAS,EAAC,UAAApH,OAAI4C,GAAY,aACrCwE,EAAAA,IAAQ,SAAS,EACrB,OAAQiD,EAAQ,CACd,IAAK,WACH,OAAK7F,GAAM,OAAAyF,EAANzF,EAAQ+F,gBAAY,MAAAN,IAAA,QAApBA,EAAsBhB,SAAS,0BAA0B,EAEvD5H,IAAY,QACjB7B,EAAAA,MAACgC,EAAAA,EAAO,CAACC,QAAS6I,EAAI5K,SAAA,IACpBE,EAAAA,KAAC4K,GAAAA,EAAe,EAAE,EACjBX,GAAkB,SAAH7J,OAAO4C,GAAY,UAAM,EAAE,EACpC,KAETpD,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,IACEE,EAAAA,KAAC4K,GAAAA,EAAe,EAAE,EAAC,IAAEF,CAAG,EACxB,EATK,KAWX,IAAK,UACH,OAAK9F,GAAM,OAAA0F,EAAN1F,EAAQ+F,gBAAY,MAAAL,IAAA,QAApBA,EAAsBjB,SAAS,yBAAyB,KAG3DrJ,EAAAA,KAAA,OAAK6K,QAASpD,GAAgB3H,SAC3B2D,MACCzD,EAAAA,KAAC0B,EAAW,CACVC,QACE3B,EAAAA,KAAC8K,EAAa,CACZC,MAAO,GACPC,MAAO,CAAEC,SAAU,WAAYC,IAAK,KAAM,CAAE,CAC7C,EAEH/K,SAAOqH,EAAAA,IAAQ,YAAY,CAAE,CAC9B,EACCnC,MACFrF,EAAAA,KAAC4B,EAAAA,EAAO,CAACC,WAAS2F,EAAAA,IAAQ,qBAAqB,EAAE1H,YAC/CE,EAAAA,KAACmL,GAAAA,EAAY,CACXH,MAAO,CACLI,MAAO,qBACT,CAAE,CACH,CAAC,CACK,KAETpL,EAAAA,KAAC0B,EAAW,CACVC,QAAM3B,EAAAA,KAACmL,GAAAA,EAAY,EAAE,EACrBhL,SAAOqH,EAAAA,IAAQ,UAAU,CAAE,CAC5B,CACF,CACE,EA3BE,KA6BX,IAAK,OACH,OAAK5C,GAAM,OAAA2F,EAAN3F,EAAQ+F,gBAAY,MAAAJ,IAAA,QAApBA,EAAsBlB,SAAS,sBAAsB,KAExDrJ,EAAAA,KAAA,OACEE,UAAWmL,GAAAA,EAAG,CACZC,SAAUhC,GAAeD,SAASjG,CAAO,CAC3C,CAAC,EACDyH,QAASnB,GAAW5J,YAEpBE,EAAAA,KAAC0B,EAAW,CACVC,QAAM3B,EAAAA,KAACuL,GAAAA,EAAW,EAAE,EACpBpL,SAAOqH,EAAAA,IAAQ,2CAA2C,CAAE,CAC7D,CAAC,CACC,EAZ6D,KActE,IAAK,eACH,SACExH,EAAAA,KAAA,OAAK6K,QAASlE,GAAmB7G,SAC9ByE,KAAc,YACfnB,KAAYI,IAAc,OAAAgH,EAAdhH,GAAgB4D,iBAAa,MAAAoD,IAAA,cAA7BA,EAA+BrF,OACzCnF,EAAAA,KAAC0B,EAAW,CACVC,QAAM3B,EAAAA,KAACwL,GAAAA,EAAgB,EAAE,EACzBrL,SAAOqH,EAAAA,IAAQ,eAAe,CAAE,CACjC,KAEDxH,EAAAA,KAAC0B,EAAW,CACVC,QAAM3B,EAAAA,KAACyL,GAAAA,EAAkB,EAAE,EAC3BtL,SAAOqH,EAAAA,IAAQ,mBAAmB,CAAE,CACrC,CACF,CACE,EAET,IAAK,mBACH,SACE5H,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,IACEE,EAAAA,KAACoB,GAAY,EAAE,KACdoG,EAAAA,IAAQ,mBAAmB,CAAC,EAC7B,EAEN,IAAK,iBACH,SACE5H,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,IACEE,EAAAA,KAACsB,GAAY,EAAE,KACdkG,EAAAA,IAAQ,iBAAiB,CAAC,EAC3B,EAEN,IAAK,YACH,SACE5H,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,IACEE,EAAAA,KAACvB,GAAAA,EAAY,EAAE,KACd+I,EAAAA,IAAQ,YAAY,CAAC,EACtB,EAEN,IAAK,OACH,SACE5H,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,IACEE,EAAAA,KAACtB,GAAAA,EAAY,EAAE,KACd8I,EAAAA,IAAQ,MAAM,CAAC,EAChB,EAEN,IAAK,QACH,SACE5H,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,IACEE,EAAAA,KAAC3B,GAAAA,EAAa,EAAE,KACfmJ,EAAAA,IAAQ,OAAO,CAAC,EACjB,EAEN,QACE,OAAO,IACX,CACF,EAEMkE,GAAkBpI,EACpBxB,GAAyBwB,CAAW,EACpC,CAAC,EAEL,SACE1D,EAAAA,MAAA,OAAKM,UAAWyL,EAAO,YAAY,EAAE7L,SAAA,IACnCF,EAAAA,MAAA,OAAKM,UAAWyL,EAAO,WAAW,EAAE7L,SAAA,CACjC,IAAC8L,EAAAA,OAAMvI,EAAK,IAAKmB,IAAa,YAAbA,GAAenB,SAAU,gBACzCrD,EAAAA,KAAA,OACEE,UAAWmL,GAAAA,EACT,mEACAM,EAAOE,SACT,EACAhB,QAAStH,GAAoBzD,YAE7BF,EAAAA,MAACgC,EAAAA,EAAO,CAACC,WAAS2F,EAAAA,IAAQ,qBAAqB,EAAE1H,SAAA,IAC9C0H,EAAAA,IAAQ,WAAW,EAAE,IAAEnE,GAAQ,KAChCrD,EAAAA,KAAC8L,EAAS,CAACf,MAAO,GAAIgB,KAAK,SAAS,CAAE,CAAC,EAChC,CAAC,CACP,KAEPnM,EAAAA,MAAA,OAAAE,SAAA,CAAK,cAEF0H,EAAAA,IAAQ,UAAU,EAAE,KAAGpE,CAAO,EAC5B,EACJmB,KAAc,eAAiBtB,MAC9BrD,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,CAAE,cAEAE,EAAAA,KAACgM,GAAAA,EAAG,CAACZ,MAAM,UAAStL,SAAEmD,EAAS,CAAM,CAAC,EACtC,EAEF,GAED,CAACmG,IAAgB/G,GAAQ,IAACuJ,EAAAA,OAAMvJ,EAAK4J,qBAAqB,KACzDrM,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,CAAE,oBAEAE,EAAAA,KAAC4B,EAAAA,EAAO,CAACC,WAAS2F,EAAAA,IAAQ,iBAAiB,EAAE1H,YAC3CE,EAAAA,KAACkM,GAAAA,EAAQ,CAACnB,MAAO,GAAIC,MAAO,CAAEmB,OAAQ,SAAU,CAAE,CAAE,CAAC,CAC9C,KACTvM,EAAAA,MAAA,OAAAE,SAAA,IACG0H,EAAAA,IAAQ,cAAc,EAAE,IAAEnF,EAAK4J,qBAAqB,EAClD,EAAC,UAER,EAAE,EAEF,MAEFrM,EAAAA,MAAA,OAAAE,SAAA,IACG0H,EAAAA,IAAQ,iBAAiB,EAAE,OAE3B/E,EAAO,EACL,EAAC,cAEN7C,EAAAA,MAACoM,GAAAA,EAAG,CAAC9L,UAAWyL,EAAOS,UAAUtM,SAAA,CAC9B8C,MAAY4E,EAAAA,IAAQ,KAAK,KAAIA,EAAAA,IAAQ,IAAI,KACzC6E,EAAAA,IAAK,EAAI,IAAM,GACfzJ,IAAaE,MAAc0E,EAAAA,IAAQ,UAAU,EAAI,MACjD6E,EAAAA,IAAK,EAAI,IAAM,MACf7E,EAAAA,IAAQ,gBAAgB,CAAC,EACvB,EACJzE,OACCnD,EAAAA,MAAA,OAAAE,SAAA,CAAK,oBAEHE,EAAAA,KAACsM,GAAAA,EAAiB,EAAE,KACnB9E,EAAAA,IAAQ,eAAe,EAAE,QAAG+E,EAAAA,IAAcxJ,EAAU,CAAC,EACnD,EAEN,CAACqG,IAAgB/G,GAAQ,IAACuJ,EAAAA,OAAMvJ,EAAKmK,KAAK,KACzC5M,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,CAAE,oBAEAF,EAAAA,MAAA,OAAKM,UAAW,0BAA0BJ,SAAA,IACvC0H,EAAAA,IAAQ,mBAAmB,EAAE,OAE7BiF,KAAKC,MAAMrK,EAAKmK,MAAQ,GAAG,EAAI,KAC/B7H,GAAc,YAAdA,EAAgBW,UAAW,gBAC1B1F,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,CAAE,UAEAE,EAAAA,KAAC4B,EAAAA,EAAO,CACNC,WACEjC,EAAAA,MAAA,OAAAE,SAAA,IACEF,EAAAA,MAAA,KAAAE,SAAA,IACG0H,EAAAA,IAAQ,qBAAqB,EAAE,YAC/BmF,EAAAA,KAAwBtK,GAAI,YAAJA,EAAMuK,eAAgB,GAAG,EAAE,MAEtD,EAAG,KACHhN,EAAAA,MAAA,KAAAE,SAAA,IACG0H,EAAAA,IAAQ,mBAAmB,EAAE,YAC7BmF,EAAAA,KAAwBtK,GAAI,YAAJA,EAAMwK,aAAc,GAAG,EAAE,MAEpD,EAAG,KACHjN,EAAAA,MAAA,KAAAE,SAAA,IACG0H,EAAAA,IAAQ,oBAAoB,EAAE,YAC9BmF,EAAAA,KAAwBtK,GAAI,YAAJA,EAAMyK,cAAe,GAAG,EAAE,MAErD,EAAG,KACH9M,EAAAA,KAAC+M,GAAAA,EAAO,CAAC/B,MAAO,CAAEgC,OAAQ,OAAQ,CAAE,CAAE,KACtCpN,EAAAA,MAAA,KAAAE,SAAA,IACG0H,EAAAA,IAAQ,kBAAkB,EAAE,YAC5BmF,EAAAA,IAAwBtK,GAAI,YAAJA,EAAM4K,WAAW,EAAE,MAE9C,EAAG,CAAC,GArBG,iBAsBJ,EACNnN,YAEDE,EAAAA,KAACkN,GAAO,CACNhN,UAAU,gBACV6L,KAAK,UACLhB,MAAO,EAAG,CACX,CAAC,CACK,CAAC,EACV,CACH,EACE,CAAC,EACN,EAEF,GAED,CAAC3B,IAAgB/G,GAAQ,IAACuJ,EAAAA,OAAMvJ,EAAK8K,mBAAmB,KACvDvN,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,CAAE,cAEAF,EAAAA,MAAA,OAAAE,SAAA,IACG0H,EAAAA,IAAQ,2BAA2B,EAAE,OAErCnF,EAAK8K,oBAAoB,GAC5B,EAAK,CAAC,EACN,EAEF,GAED/D,IAAgBa,IAAmB9G,MAClCvD,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,CAAE,oBAEAF,EAAAA,MAAA,OAAAE,SAAA,IACG0H,EAAAA,IAAQ,mBAAmB,EAAE,QAAG4F,EAAAA,IAAgBjK,EAAS,CAAC,EACxD,CAAC,EACN,EAEF,EACD,EACE,KACLvD,EAAAA,MAAA,OAAKM,UAAWyL,EAAO,YAAY,EAAE7L,SAAA,CAClCyE,KAAc,YAAcjB,MAC3BtD,EAAAA,KAAAH,EAAAA,SAAA,CAAAC,SACG4L,GAAgB2B,UACfrN,EAAAA,KAACsN,GAAAA,EAAY,CACXC,aAAcjK,EACdkK,eAAgB9B,GAChB+B,SAAU9E,GACV+E,YAAa,SAACC,EAAG,CAAF,SACb3N,EAAAA,KAAC4N,GAAAA,EAAY,CACXC,YAAY,0BACZvI,OAAQqI,CAAE,CACX,CAAC,CACF,CACH,KAED3N,EAAAA,KAAC4N,GAAAA,EAAY,CACXC,YAAY,0BACZvI,OAAQhC,CAAY,CACrB,CACF,CACD,EAEHf,IAAO,YAAPA,GAASuL,IAAI,SAACrD,EAAoB,CAAF,SAC/BzK,EAAAA,KAAC+N,GAAAA,GAAM,CACLC,KAAK,OAELnD,QAAS,UAAM,CACb3H,IAAQ,MAARA,GAAWuH,CAAM,CACnB,EACAO,MAAO,CACLmB,OACE1B,IAAW,WAAapF,GACpB,cACA,SACR,EAAEvF,YAEFE,EAAAA,KAACmK,GAAS,CAACM,OAAQA,CAAO,CAAE,CAAC,EAXxBA,CAYC,CAAC,CACV,CAAC,EACC,CAAC,EACH,CAET,EAEA,GAAetI,E,wKCpfT8L,EAAsD,SAAHrP,EAInD,KAHJ0D,EAAK1D,EAAL0D,MACAkB,EAAc5E,EAAd4E,eACA4F,EAAYxK,EAAZwK,aAEAjF,KAA2BC,EAAAA,UAAS,SAAS,EAArC8J,EAAc/J,EAAd+J,eAEF3L,EACJ6G,GAAgB9G,EAAMgD,SAAW,YAC7B,CAAC,OAAQ,UAAU,EACnB,CAAC,OAAQ,eAAgB,UAAU,EACzC,SACEtF,EAAAA,KAACmC,EAAAA,EAAS,CACRc,UAAWX,GAAK,YAALA,EAAO6L,KAClBhL,WAAYb,GAAK,YAALA,EAAO8L,cAAc9L,GAAK,YAALA,EAAOa,WACxCJ,YAAaT,GAAK,YAALA,EAAOa,aAAab,GAAK,YAALA,EAAO+L,WACxCjL,QAASd,EAAM6C,GACf7C,MAAOA,EAAMgM,UACb/L,QAASA,EACTiB,eAAgBA,EAChBR,aAAcV,GAAK,YAALA,EAAOiM,cACrBrL,SAAU,SAACsL,EAAM,CACXA,IAAM,YACRN,EAAe,CACbO,eAAgBnM,EAChBoM,iBAAkB,eACpB,CAAC,CAEL,EACApL,YAAahB,EAAMgD,MAAO,CAC3B,CAEL,EAEA,EAAe2I,EClCTU,EAAwD,SAAH/P,EAKrD,KAJJ0D,EAAK1D,EAAL0D,MACAsM,EAAWhQ,EAAXgQ,YACAxF,EAAYxK,EAAZwK,aACA5F,EAAc5E,EAAd4E,eAEAwB,KAA2BC,EAAAA,WAA0B,EAAzC4J,EAAU7J,EAAdG,GACR2J,KAAiBC,EAAAA,GAAmB,EAA5BC,EAAIF,EAAJE,KAER,SACEhP,EAAAA,KAACiP,EAAAA,EAAI,CACHjB,KAAK,QACL7N,MACE,CAACyO,GAAeC,KACd7O,EAAAA,KAACiO,EAAiB,CAChB3L,MAAOA,EACP8G,aAAcA,EACd5F,eAAgBA,CAAe,CAChC,EACC,KAENtD,UAAU,4BAA2BJ,SAEpCwC,EAAMgM,aACLtO,EAAAA,KAACrB,EAAAA,EAAoB,CACnBE,QAAMqQ,EAAAA,GAAoB5M,EAAMgM,SAAS,EACzCpP,YAAW,GACXI,YAAa,CACXI,gBAAiB,CAAEQ,UAAW,kBAAmB,EACjDT,iBAAkB,CAAES,UAAW,mBAAoB,CACrD,EACAd,eAAgB,CACdG,UAAW,SAAC4P,GAAGtQ,EAAM,CAAF,SACjBmB,EAAAA,KAAA,OAAKE,UAAU,iBAAgBJ,YAAEsP,EAAAA,GAAUvQ,CAAI,EAAI,CAAC,CAAM,CAAC,EAE7DW,SAAU,SAAC6P,GAAQC,EAAU,CAAF,SACzBtP,EAAAA,KAAA,OAAKE,UAAU,gBAAeJ,YAC5BE,EAAAA,KAAA,OACEE,UAAU,iCACV2K,QAAS,kBAAMmE,EAAK,GAAD5O,OAAIkP,EAAQ,MAAAlP,OAAKiP,EAAM,CAAE,CAAC,EAACvP,YAE9CE,EAAAA,KAACuP,EAAAA,EAAgB,EAAE,CAAC,CACjB,CAAC,CACH,CAAC,CAEV,CAAE,CACH,EAED,EACD,CACG,CAEV,EACA,EAAeZ,C,wDChER,IAAMS,EAAY,SAAZA,EAAavQ,EAAgC,CACxD,OAAKA,EAAKG,MACHoQ,EAAUvQ,EAAKG,KAAK,EAAI,EADP,CAE1B,C", "sources": ["webpack://labwise-web/./node_modules/@ant-design/icons/es/icons/CheckOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/icons/es/icons/CopyOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/icons/es/icons/EditOutlined.js", "webpack://labwise-web/./src/components/SyntheticLinkDisplay/index.tsx", "webpack://labwise-web/./src/assets/svgs/collected.svg", "webpack://labwise-web/./src/assets/svgs/group.svg", "webpack://labwise-web/./src/assets/svgs/route-operation/tip.svg", "webpack://labwise-web/./node_modules/@ant-design/icons-svg/es/asn/FundOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/icons/es/icons/FundOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/icons-svg/es/asn/GoldOutlined.js", "webpack://labwise-web/./node_modules/@ant-design/icons/es/icons/GoldOutlined.js", "webpack://labwise-web/./src/components/SyntheticRoutes/CardTitle/index.less?c3eb", "webpack://labwise-web/./src/components/SyntheticRoutes/CardTitle/index.tsx", "webpack://labwise-web/./src/components/SyntheticRoutes/SyntheticRouteCard/ProjectRouteTitle.tsx", "webpack://labwise-web/./src/components/SyntheticRoutes/SyntheticRouteCard/index.tsx", "webpack://labwise-web/./src/components/SyntheticRoutes/util.ts"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport CheckOutlinedSvg from \"@ant-design/icons-svg/es/asn/CheckOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar CheckOutlined = function CheckOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: CheckOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(CheckOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CheckOutlined';\n}\nexport default RefIcon;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport CopyOutlinedSvg from \"@ant-design/icons-svg/es/asn/CopyOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar CopyOutlined = function CopyOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: CopyOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(CopyOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'CopyOutlined';\n}\nexport default RefIcon;", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport EditOutlinedSvg from \"@ant-design/icons-svg/es/asn/EditOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar EditOutlined = function EditOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: EditOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(EditOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'EditOutlined';\n}\nexport default RefIcon;", "import { SyntheticLink } from '@/types/SyntheticRoute/SyntheticLink'\nimport React, { ReactElement } from 'react'\nimport Arrow from '../Arrow'\nimport SmilesDrawerMoleculeStructure from '../MoleculeStructure'\nimport './index.less'\n\nexport type SyntheticLinkDisplayProps = {\n  node: SyntheticLink\n  withWrapper?: boolean\n  customChildren?: {\n    structure?: (smiles: string, node: SyntheticLink) => ReactElement\n    reaction?: (\n      target: string,\n      reactant: string,\n      node: SyntheticLink\n    ) => ReactElement\n  }\n  customProps?: {\n    structureWrapper?: React.HTMLAttributes<HTMLDivElement>\n    reactionWrapper?: React.HTMLAttributes<HTMLDivElement>\n  }\n}\n\nconst SyntheticLinkDisplay: React.FC<SyntheticLinkDisplayProps> = ({\n  node,\n  node: { value, child, rxn },\n  withWrapper,\n  customChildren = {},\n  customProps = {}\n}) => {\n  const { structure, reaction } = customChildren\n  const { structureWrapper, reactionWrapper } = customProps\n\n  const context = (\n    <>\n      <div {...structureWrapper}>\n        <SmilesDrawerMoleculeStructure\n          structure={value}\n          className=\"synthetic-link-display-node\"\n        />\n        {structure?.(value, node)}\n      </div>\n      {child && (\n        <>\n          <div\n            title={rxn}\n            {...reactionWrapper}\n            className={`arrow-wrapper ${reactionWrapper?.className}`}\n          >\n            <Arrow />\n            {reaction?.(value, child.value, node)}\n          </div>\n          <SyntheticLinkDisplay\n            node={child}\n            customChildren={customChildren}\n            customProps={customProps}\n          />\n        </>\n      )}\n    </>\n  )\n  return withWrapper ? (\n    <div className=\"synthetic-link-display-node-root\">{context}</div>\n  ) : (\n    <>{context}</>\n  )\n}\n\nexport default SyntheticLinkDisplay\n", "var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nimport * as React from \"react\";\nconst SvgCollected = (props) => /* @__PURE__ */ React.createElement(\"svg\", __spreadValues({ className: \"collected_svg__icon\", viewBox: \"0 0 1024 1024\", xmlns: \"http://www.w3.org/2000/svg\" }, props), /* @__PURE__ */ React.createElement(\"path\", { d: \"m894.4 354.6-216.5-31.1-96.8-194.3c-28.2-56.7-109.5-56.7-137.7 0l-96.8 194.3L130 354.6c-63 9-88.3 86.1-42.6 130.3L244 636.1l-37 213.4c-11 62.3 55 109.8 111.4 80.6l193.8-100.9 193.7 100.9c56.3 29.3 122.3-18.2 111.4-80.6l-36.9-213.4L937 484.9c45.8-44.2 20.5-121.3-42.6-130.3zM692.4 537l-49.6 48 12.3 71.4c1.7 9.5-1.9 19.1-9.3 25.2-7.4 6.1-17.5 7.9-26.6 4.5-9-3.3-15.6-11.2-17.2-20.7l-14.7-85.3c-1.5-8.7 1.4-17.6 7.8-23.8l59.8-57.8c10.7-10.3 27.7-10.1 38.1.6 10.4 10.6 10.1 27.6-.6 37.9z\", fill: \"#f4ea2a\" }));\nexport { SvgCollected as ReactComponent };\nexport default \"data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJtODk0LjQgMzU0LjYtMjE2LjUtMzEuMS05Ni44LTE5NC4zYy0yOC4yLTU2LjctMTA5LjUtNTYuNy0xMzcuNyAwbC05Ni44IDE5NC4zTDEzMCAzNTQuNmMtNjMgOS04OC4zIDg2LjEtNDIuNiAxMzAuM0wyNDQgNjM2LjFsLTM3IDIxMy40Yy0xMSA2Mi4zIDU1IDEwOS44IDExMS40IDgwLjZsMTkzLjgtMTAwLjkgMTkzLjcgMTAwLjljNTYuMyAyOS4zIDEyMi4zLTE4LjIgMTExLjQtODAuNmwtMzYuOS0yMTMuNEw5MzcgNDg0LjljNDUuOC00NC4yIDIwLjUtMTIxLjMtNDIuNi0xMzAuM3pNNjkyLjQgNTM3bC00OS42IDQ4IDEyLjMgNzEuNGMxLjcgOS41LTEuOSAxOS4xLTkuMyAyNS4yLTcuNCA2LjEtMTcuNSA3LjktMjYuNiA0LjUtOS0zLjMtMTUuNi0xMS4yLTE3LjItMjAuN2wtMTQuNy04NS4zYy0xLjUtOC43IDEuNC0xNy42IDcuOC0yMy44bDU5LjgtNTcuOGMxMC43LTEwLjMgMjcuNy0xMC4xIDM4LjEuNiAxMC40IDEwLjYgMTAuMSAyNy42LS42IDM3Ljl6IiBmaWxsPSIjZjRlYTJhIi8+PC9zdmc+\";\n", "var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nimport * as React from \"react\";\nconst SvgGroup = (props) => /* @__PURE__ */ React.createElement(\"svg\", __spreadValues({ viewBox: \"0 0 14 14\", fill: \"none\", xmlns: \"http://www.w3.org/2000/svg\" }, props), /* @__PURE__ */ React.createElement(\"path\", { d: \"M4.039 2.137a.58.58 0 0 1 .992-.414l4.748 4.748a.581.581 0 0 1 0 .823l-4.743 4.742a.58.58 0 1 1-.822-.822L8.548 6.88 4.214 2.546a.573.573 0 0 1-.17-.415l-.005.006Z\" }));\nexport { SvgGroup as ReactComponent };\nexport default \"data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMTQgMTQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQuMDM5IDIuMTM3YS41OC41OCAwIDAgMSAuOTkyLS40MTRsNC43NDggNC43NDhhLjU4MS41ODEgMCAwIDEgMCAuODIzbC00Ljc0MyA0Ljc0MmEuNTguNTggMCAxIDEtLjgyMi0uODIyTDguNTQ4IDYuODggNC4yMTQgMi41NDZhLjU3My41NzMgMCAwIDEtLjE3LS40MTVsLS4wMDUuMDA2WiIvPjwvc3ZnPg==\";\n", "var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nimport * as React from \"react\";\nconst SvgTip = (props) => /* @__PURE__ */ React.createElement(\"svg\", __spreadValues({ viewBox: \"0 0 20 20\", fill: \"none\", xmlns: \"http://www.w3.org/2000/svg\" }, props), /* @__PURE__ */ React.createElement(\"path\", { d: \"M10 7.1a1.1 1.1 0 1 0 0-2.2 1.1 1.1 0 0 0 0 2.2ZM11 9a1 1 0 1 0-2 0v5a1 1 0 1 0 2 0V9Z\" }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M10 20C4.49 20 0 15.51 0 10S4.49 0 10 0s10 4.49 10 10-4.49 10-10 10Zm0-18c-4.41 0-8 3.59-8 8s3.59 8 8 8 8-3.59 8-8-3.59-8-8-8Z\" }));\nexport { SvgTip as ReactComponent };\nexport default \"data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjAgMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEwIDcuMWExLjEgMS4xIDAgMSAwIDAtMi4yIDEuMSAxLjEgMCAwIDAgMCAyLjJaTTExIDlhMSAxIDAgMSAwLTIgMHY1YTEgMSAwIDEgMCAyIDBWOVoiLz48cGF0aCBkPSJNMTAgMjBDNC40OSAyMCAwIDE1LjUxIDAgMTBTNC40OSAwIDEwIDBzMTAgNC40OSAxMCAxMC00LjQ5IDEwLTEwIDEwWm0wLTE4Yy00LjQxIDAtOCAzLjU5LTggOHMzLjU5IDggOCA4IDgtMy41OSA4LTgtMy41OS04LTgtOFoiLz48L3N2Zz4=\";\n", "// This icon file is generated automatically.\nvar FundOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M926 164H94c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V196c0-17.7-14.3-32-32-32zm-40 632H134V236h752v560zm-658.9-82.3c3.1 3.1 8.2 3.1 11.3 0l172.5-172.5 114.4 114.5c3.1 3.1 8.2 3.1 11.3 0l297-297.2c3.1-3.1 3.1-8.2 0-11.3l-36.8-36.8a8.03 8.03 0 00-11.3 0L531 565 416.6 450.5a8.03 8.03 0 00-11.3 0l-214.9 215a8.03 8.03 0 000 11.3l36.7 36.9z\" } }] }, \"name\": \"fund\", \"theme\": \"outlined\" };\nexport default FundOutlined;\n", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport FundOutlinedSvg from \"@ant-design/icons-svg/es/asn/FundOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar FundOutlined = function FundOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: FundOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(FundOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FundOutlined';\n}\nexport default RefIcon;", "// This icon file is generated automatically.\nvar GoldOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M342 472h342c.4 0 .9 0 1.3-.1 4.4-.7 7.3-4.8 6.6-9.2l-40.2-248c-.6-3.9-4-6.7-7.9-6.7H382.2c-3.9 0-7.3 2.8-7.9 6.7l-40.2 248c-.1.4-.1.9-.1 1.3 0 4.4 3.6 8 8 8zm91.2-196h159.5l20.7 128h-201l20.8-128zm2.5 282.7c-.6-3.9-4-6.7-7.9-6.7H166.2c-3.9 0-7.3 2.8-7.9 6.7l-40.2 248c-.1.4-.1.9-.1 1.3 0 4.4 3.6 8 8 8h342c.4 0 .9 0 1.3-.1 4.4-.7 7.3-4.8 6.6-9.2l-40.2-248zM196.5 748l20.7-128h159.5l20.7 128H196.5zm709.4 58.7l-40.2-248c-.6-3.9-4-6.7-7.9-6.7H596.2c-3.9 0-7.3 2.8-7.9 6.7l-40.2 248c-.1.4-.1.9-.1 1.3 0 4.4 3.6 8 8 8h342c.4 0 .9 0 1.3-.1 4.3-.7 7.3-4.8 6.6-9.2zM626.5 748l20.7-128h159.5l20.7 128H626.5z\" } }] }, \"name\": \"gold\", \"theme\": \"outlined\" };\nexport default GoldOutlined;\n", "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\nimport * as React from 'react';\nimport GoldOutlinedSvg from \"@ant-design/icons-svg/es/asn/GoldOutlined\";\nimport AntdIcon from '../components/AntdIcon';\nvar GoldOutlined = function GoldOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _objectSpread(_objectSpread({}, props), {}, {\n    ref: ref,\n    icon: GoldOutlinedSvg\n  }));\n};\nvar RefIcon = /*#__PURE__*/React.forwardRef(GoldOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'GoldOutlined';\n}\nexport default RefIcon;", "// extracted by mini-css-extract-plugin\nexport default {\"card-title\":\"card-title___qvJJH\",\"left-side\":\"left-side___KQ09D\",\"groupItem\":\"groupItem___kVtWV\",\"barnchTag\":\"barnchTag___qHTBV\",\"right-side\":\"right-side___tXzEy\"};", "import { ReactComponent as CollectedIcon } from '@/assets/svgs/collected.svg'\nimport { ReactComponent as GroupIcon } from '@/assets/svgs/group.svg'\nimport { ReactComponent as HelpIcon } from '@/assets/svgs/help.svg'\nimport { ReactComponent as TipIcon } from '@/assets/svgs/route-operation/tip.svg'\nimport StatusRender from '@/components/StatusRender'\nimport { useBrainFetch } from '@/hooks/useBrainFetch'\nimport EnumSwitcher from '@/pages/projects/components/EnumSwitcher'\nimport {\n  ProjectCompound,\n  ProjectRoute,\n  ProjectRouteStatus,\n  service\n} from '@/services/brain'\nimport { CollectedRetroBackbones } from '@/services/brain/types/retro-backbones'\nimport {\n  calHasBranch,\n  calSyntheticStep\n} from '@/types/SyntheticRoute/SyntheticTree'\nimport { IPagination } from '@/types/common'\nimport {\n  detectDeviceInfo,\n  formatYMDHMTime,\n  formatYTSTime,\n  getLocalValue,\n  getWord,\n  isEN,\n  roundToOneDecimalPlaces,\n  setLocalValue,\n  toInt\n} from '@/utils'\nimport {\n  CheckOutlined,\n  CopyOutlined,\n  EditOutlined,\n  ExperimentFilled,\n  ExperimentOutlined,\n  EyeOutlined,\n  FieldTimeOutlined,\n  FundOutlined,\n  GoldOutlined,\n  MessageOutlined,\n  StarOutlined\n} from '@ant-design/icons'\nimport { Button, Divider, Popover, Tag, message } from 'antd'\nimport cs from 'classnames'\nimport { cloneDeep, isNil } from 'lodash'\nimport { FC, ReactElement } from 'react'\nimport {\n  history,\n  useAccess,\n  useLocation,\n  useModel,\n  useParams,\n  useSearchParams\n} from 'umi'\nimport type { CardAction, CardTitleProps } from './index.d'\nimport styles from './index.less'\n\nconst { winWidth } = detectDeviceInfo()\nfunction OperateIcon({ icon, title }: { icon: ReactElement; title: string }) {\n  return winWidth <= 1680 ? (\n    <Popover content={title}>{icon}</Popover>\n  ) : (\n    <>\n      {icon} {title}\n    </>\n  )\n}\n\nconst projectStatusTransferMap: Record<\n  ProjectRouteStatus,\n  ProjectRouteStatus[]\n> = {\n  canceled: [],\n  confirmed: ['canceled'],\n  editing: ['canceled'],\n  finished: []\n}\n\nconst CardTitle: FC<CardTitleProps> = ({\n  item,\n  route,\n  actions,\n  stepNum = route ? calSyntheticStep(route) : 0,\n  hasBranch = route ? calHasBranch(route) : false,\n  hasBranches,\n  updateTime,\n  commendCount,\n  routeName,\n  onAction,\n  updatedAt,\n  routeNo,\n  group,\n  routeStatus,\n  handleFilterSimilar,\n  targetMolecule,\n  isCollected\n}) => {\n  const [searchParams, setSearchParams] = useSearchParams()\n  const { fetch } = useBrainFetch()\n  const {\n    updatePagenate,\n    getMyRoutesData,\n    routeType,\n    curFilterInfo,\n    collectedEvent,\n    curIsCollected,\n    curHistoryInfo\n  } = useModel('compound')\n  const access = useAccess()\n  const { initialState } = useModel('@@initialState')\n  const { id: projectId, compoundId } = useParams<{\n    id: string\n    compoundId: string\n  }>()\n\n  const isTemporaryRoute = curHistoryInfo?.status === 'running'\n\n  const reload = async () => {\n    let customPageSize = toInt(searchParams.get('pageSize') || '') || 10\n    updatePagenate({\n      page: 1,\n      pageSize: customPageSize\n    } as IPagination)\n    await getMyRoutesData(toInt(compoundId) as number)\n    setSearchParams(\n      { page: '1', pageSize: customPageSize.toString() },\n      { replace: true }\n    )\n  }\n\n  const handleSetAsDefault = async () => {\n    if (!compoundId || !routeNo) return\n    const { data } = await fetch(\n      service<ProjectCompound>('project-compounds').update(compoundId, {\n        default_route: routeNo as unknown as ProjectRoute\n      })\n    )\n    if (data) {\n      message.success(getWord('default-route-set'))\n      reload()\n    }\n  }\n\n  const handleCollected = async () => {\n    if (isTemporaryRoute) return\n    const isCollected = curIsCollected(item)\n    const { data } = isCollected\n      ? await service('collected-retro-backbones').deleteOne(\n          item?.collected_retro_backbones?.[0]?.id\n        )\n      : await service('collected-retro-backbones').create({\n          user_id: String(initialState?.userInfo?.id),\n          retro_backbone: routeNo\n        })\n    if (data as CollectedRetroBackbones) {\n      let newCollectedRetroBackbones: CollectedRetroBackbones[] = cloneDeep(\n        item?.collected_retro_backbones\n      )\n      if (isCollected) {\n        newCollectedRetroBackbones = []\n      } else {\n        newCollectedRetroBackbones.unshift({\n          id: data?.id,\n          user_id: data?.user_id\n        })\n      }\n      collectedEvent(routeNo, newCollectedRetroBackbones)\n      message.success(getWord('operate-success'))\n    }\n  }\n\n  const handleChangeStatus = async (status: ProjectRouteStatus) => {\n    if (!routeNo) return\n    const { data } = await fetch(\n      service<ProjectRoute>('project-routes').update(routeNo, { status })\n    )\n    if (data) {\n      message.success(getWord('success-update-status'))\n      reload()\n    }\n  }\n\n  const { pathname } = useLocation()\n  const isPlayground: boolean = pathname.includes('/playground')\n  const preViewdRoutes = getLocalValue('viewdRoute')\n    ? JSON.parse(getLocalValue('viewdRoute'))\n    : []\n  const handleView = () => {\n    let newViewdRoutes: string[] = [...preViewdRoutes, routeNo]\n    setLocalValue('viewdRoute', JSON.stringify(newViewdRoutes))\n    if (routeType === 'aiGenerated') {\n      history.push(\n        isPlayground\n          ? `/playground/commend/view-by-backbone/${routeNo}`\n          : `/projects/${projectId}/compound/${compoundId}/view-by-backbone/${routeNo}`\n      )\n    } else {\n      history.push(\n        `/projects/${projectId}/compound/${compoundId}/${\n          routeStatus === 'editing' ? 'edit' : 'view'\n        }/${routeNo}`\n      )\n    }\n  }\n\n  const hasCommendCount = commendCount && Number(commendCount) > 0\n  const ActionCom = ({ action }: { action: CardAction }) => {\n    const des: string = hasCommendCount\n      ? `${getWord('comment')}（${commendCount}）`\n      : getWord('comment')\n    switch (action) {\n      case 'feedback':\n        if (!access?.authCodeList?.includes('compound.button.feedback'))\n          return null\n        return winWidth <= 1680 ? (\n          <Popover content={des}>\n            <MessageOutlined />\n            {hasCommendCount ? `（${commendCount}）` : ''}\n          </Popover>\n        ) : (\n          <>\n            <MessageOutlined /> {des}\n          </>\n        )\n      case 'collect':\n        if (!access?.authCodeList?.includes('compound.button.collect'))\n          return null\n        return (\n          <div onClick={handleCollected}>\n            {isCollected ? (\n              <OperateIcon\n                icon={\n                  <CollectedIcon\n                    width={15}\n                    style={{ position: 'relative', top: '2px' }}\n                  />\n                }\n                title={getWord('unfavorite')}\n              />\n            ) : isTemporaryRoute ? (\n              <Popover content={getWord('temporary-route-tip')}>\n                <StarOutlined\n                  style={{\n                    color: 'rgba(85, 85, 85, 1)'\n                  }}\n                />\n              </Popover>\n            ) : (\n              <OperateIcon\n                icon={<StarOutlined />}\n                title={getWord('favorite')}\n              />\n            )}\n          </div>\n        )\n      case 'view':\n        if (!access?.authCodeList?.includes('compound.button.view')) return null\n        return (\n          <div\n            className={cs({\n              complete: preViewdRoutes.includes(routeNo)\n            })}\n            onClick={handleView}\n          >\n            <OperateIcon\n              icon={<EyeOutlined />}\n              title={getWord('pages.projectTable.actionLabel.viewDetail')}\n            />\n          </div>\n        )\n      case 'setAsDefault':\n        return (\n          <div onClick={handleSetAsDefault}>\n            {routeType === 'myRoutes' &&\n            routeNo === targetMolecule?.default_route?.id ? (\n              <OperateIcon\n                icon={<ExperimentFilled />}\n                title={getWord('default-route')}\n              />\n            ) : (\n              <OperateIcon\n                icon={<ExperimentOutlined />}\n                title={getWord('set-default-route')}\n              />\n            )}\n          </div>\n        )\n      case 'experimentDesign':\n        return (\n          <>\n            <FundOutlined />\n            {getWord('experiment-design')}\n          </>\n        )\n      case 'materialDosage':\n        return (\n          <>\n            <GoldOutlined />\n            {getWord('material-demand')}\n          </>\n        )\n      case 'copyRoute':\n        return (\n          <>\n            <CopyOutlined />\n            {getWord('copy-route')}\n          </>\n        )\n      case 'edit':\n        return (\n          <>\n            <EditOutlined />\n            {getWord('edit')}\n          </>\n        )\n      case 'apply':\n        return (\n          <>\n            <CheckOutlined />\n            {getWord('apply')}\n          </>\n        )\n      default:\n        return null\n    }\n  }\n\n  const avaliableStatus = routeStatus\n    ? projectStatusTransferMap[routeStatus]\n    : []\n\n  return (\n    <div className={styles['card-title']}>\n      <div className={styles['left-side']}>\n        {!isNil(group) && curFilterInfo?.group !== 'ungrouped' && (\n          <div\n            className={cs(\n              'enablePointer flex-justify-space-between flex-align-items-center',\n              styles.groupItem\n            )}\n            onClick={handleFilterSimilar}\n          >\n            <Popover content={getWord('filter-routes-group')}>\n              {getWord('new-group')} {group + 1}\n              <GroupIcon width={14} fill=\"#336CC9\" />\n            </Popover>\n          </div>\n        )}\n        <div>\n          &nbsp;&nbsp;\n          {getWord('route-id')}: {routeNo}\n        </div>\n        {routeType !== 'aiGenerated' && routeName ? (\n          <>\n            &nbsp;&nbsp;\n            <Tag color=\"#2db7f5\">{routeName}</Tag>\n          </>\n        ) : (\n          ''\n        )}\n        {!isPlayground && item && !isNil(item.min_n_main_tree_steps) ? (\n          <>\n            &nbsp;&bull;&nbsp;\n            <Popover content={getWord('multi-steps-tip')}>\n              <HelpIcon width={18} style={{ cursor: 'pointer' }} />\n            </Popover>\n            <div>\n              {getWord('route-length')}:{item.min_n_main_tree_steps}\n            </div>\n            &nbsp;&nbsp;\n          </>\n        ) : (\n          ''\n        )}\n        <div>\n          {getWord('longest-chain-l')}\n          &nbsp;\n          {stepNum}\n        </div>\n        &nbsp;&nbsp;\n        <Tag className={styles.barnchTag}>\n          {hasBranch ? getWord('has') : getWord('No')}\n          {isEN() ? ' ' : ''}\n          {hasBranch && hasBranches ? getWord('multiple') : ''}\n          {isEN() ? ' ' : ''}\n          {getWord('branched-chain')}\n        </Tag>\n        {updateTime && (\n          <div>\n            &nbsp;&bull;&nbsp;\n            <FieldTimeOutlined />\n            {getWord('modified-time')}: {formatYTSTime(updateTime)}\n          </div>\n        )}\n        {!isPlayground && item && !isNil(item.score) ? (\n          <>\n            &nbsp;&bull;&nbsp;\n            <div className={'flex-align-items-center'}>\n              {getWord('algorithmic-score')}\n              &nbsp;\n              {Math.round(item.score * 100) / 100}\n              {curHistoryInfo?.status === 'completed' && (\n                <>\n                  &nbsp;\n                  <Popover\n                    content={\n                      <div key=\"algorithmic-tip\">\n                        <p>\n                          {getWord('route-novelty-score')}：\n                          {roundToOneDecimalPlaces(item?.novelty_score * 100)}\n                          /100\n                        </p>\n                        <p>\n                          {getWord('route-price-score')}：\n                          {roundToOneDecimalPlaces(item?.price_score * 100)}\n                          /100\n                        </p>\n                        <p>\n                          {getWord('route-safety-score')}：\n                          {roundToOneDecimalPlaces(item?.safety_score * 100)}\n                          /100\n                        </p>\n                        <Divider style={{ margin: '8px 0' }} />\n                        <p>\n                          {getWord('route-base-score')}：\n                          {roundToOneDecimalPlaces(item?.originScore)}\n                          /100\n                        </p>\n                      </div>\n                    }\n                  >\n                    <TipIcon\n                      className=\"enablePointer\"\n                      fill=\"#929292\"\n                      width={14}\n                    />\n                  </Popover>\n                </>\n              )}\n            </div>\n          </>\n        ) : (\n          ''\n        )}\n        {!isPlayground && item && !isNil(item.known_reaction_rate) ? (\n          <>\n            &nbsp;&nbsp;\n            <div>\n              {getWord('known-reaction-proportion')}\n              &nbsp;\n              {item.known_reaction_rate}%\n            </div>\n          </>\n        ) : (\n          ''\n        )}\n        {isPlayground && hasCommendCount && updatedAt ? (\n          <>\n            &nbsp;&bull;&nbsp;\n            <div>\n              {getWord('last-comment-date')}: {formatYMDHMTime(updatedAt)}\n            </div>\n          </>\n        ) : (\n          ''\n        )}\n      </div>\n      <div className={styles['right-side']}>\n        {routeType === 'myRoutes' && routeStatus && (\n          <>\n            {avaliableStatus.length ? (\n              <EnumSwitcher\n                currentValue={routeStatus}\n                avalibleValues={avaliableStatus}\n                onSelect={handleChangeStatus}\n                valueRender={(s) => (\n                  <StatusRender\n                    labelPrefix=\"pages.route.statusLabel\"\n                    status={s}\n                  />\n                )}\n              />\n            ) : (\n              <StatusRender\n                labelPrefix=\"pages.route.statusLabel\"\n                status={routeStatus}\n              />\n            )}\n          </>\n        )}\n        {actions?.map((action: CardAction) => (\n          <Button\n            type=\"link\"\n            key={action}\n            onClick={() => {\n              onAction?.(action)\n            }}\n            style={{\n              cursor:\n                action === 'collect' && isTemporaryRoute\n                  ? 'not-allowed'\n                  : 'pointer'\n            }}\n          >\n            <ActionCom action={action} />\n          </Button>\n        ))}\n      </div>\n    </div>\n  )\n}\n\nexport default CardTitle\n", "import { ProjectCompound, ProjectRoute } from '@/services/brain'\nimport { useModel } from '@umijs/max'\nimport { Dayjs } from 'dayjs'\nimport React from 'react'\nimport CardTitle from '../CardTitle'\nimport type { CardAction } from '../CardTitle/index.d'\n\nexport interface ProjectRouteTitleProps {\n  route: ProjectRoute\n  isPlayground?: boolean\n  targetMolecule?: ProjectCompound\n}\n\nconst ProjectRouteTitle: React.FC<ProjectRouteTitleProps> = ({\n  route,\n  targetMolecule,\n  isPlayground\n}) => {\n  const { getProfileInfo } = useModel('commend')\n\n  const actions: CardAction[] =\n    isPlayground || route.status !== 'confirmed'\n      ? ['view', 'feedback']\n      : ['view', 'setAsDefault', 'feedback']\n  return (\n    <CardTitle\n      routeName={route?.name}\n      updatedAt={(route?.updated_at || route?.updatedAt) as Dayjs | undefined}\n      updateTime={(route?.updatedAt || route?.createdAt) as Dayjs | undefined}\n      routeNo={route.id}\n      route={route.main_tree}\n      actions={actions}\n      targetMolecule={targetMolecule}\n      commendCount={route?.content_count as number | undefined}\n      onAction={(t) => {\n        if (t === 'feedback') {\n          getProfileInfo({\n            _commendSuject: route,\n            collection_class: 'project-route'\n          })\n        }\n      }}\n      routeStatus={route.status}\n    />\n  )\n}\n\nexport default ProjectRouteTitle\n", "/* 我的路线 */\nimport { ReactComponent as CopyMaterialIcon } from '@/assets/svgs/route-operation/copy-material.svg'\nimport { useCopyToClipboard } from '@/components/MoleculeStructure/util'\nimport SyntheticLinkDisplay from '@/components/SyntheticLinkDisplay'\nimport { syntheticTreeToLink } from '@/types/SyntheticRoute/SyntheticLink'\nimport { useParams } from '@umijs/max'\nimport { Card } from 'antd'\nimport React from 'react'\nimport { calDeepth } from '../util'\nimport ProjectRouteTitle from './ProjectRouteTitle'\nimport type { SyntheticRouteCardProps } from './index.d'\nimport './index.less'\n\nconst SyntheticRouteCard: React.FC<SyntheticRouteCardProps> = ({\n  route,\n  hiddenTitle,\n  isPlayground,\n  targetMolecule\n}) => {\n  const { id: moleculeId } = useParams<{ id: string }>()\n  const { copy } = useCopyToClipboard()\n\n  return (\n    <Card\n      type=\"inner\"\n      title={\n        !hiddenTitle && moleculeId ? (\n          <ProjectRouteTitle\n            route={route}\n            isPlayground={isPlayground}\n            targetMolecule={targetMolecule}\n          />\n        ) : null\n      }\n      className=\"synthetic-route-card-root\"\n    >\n      {route.main_tree ? (\n        <SyntheticLinkDisplay\n          node={syntheticTreeToLink(route.main_tree)}\n          withWrapper\n          customProps={{\n            reactionWrapper: { className: 'reaction-wrapper' },\n            structureWrapper: { className: 'structure-wrapper' }\n          }}\n          customChildren={{\n            structure: (_, node) => (\n              <div className=\"structure-info\">{calDeepth(node) + 1}</div>\n            ),\n            reaction: (target, reactant) => (\n              <div className=\"reaction-btns\">\n                <div\n                  className=\"reaction-copy-btn reaction-btn\"\n                  onClick={() => copy(`${reactant}>>${target}`)}\n                >\n                  <CopyMaterialIcon />\n                </div>\n              </div>\n            )\n          }}\n        />\n      ) : (\n        ''\n      )}\n    </Card>\n  )\n}\nexport default SyntheticRouteCard\n", "import { SyntheticLink } from '@/types/SyntheticRoute/SyntheticLink'\n\nexport const calDeepth = (node: SyntheticLink): number => {\n  if (!node.child) return 0\n  return calDeepth(node.child) + 1\n}\n"], "names": ["CheckOutlined", "props", "ref", "RefIcon", "CopyOutlined", "EditOutlined", "SyntheticLinkDisplay", "_ref", "node", "_ref$node", "value", "child", "rxn", "with<PERSON><PERSON><PERSON>", "_ref$customChildren", "customChildren", "_ref$customProps", "customProps", "structure", "reaction", "structureWrapper", "reactionWrapper", "context", "_jsxs", "_Fragment", "children", "_objectSpread", "_jsx", "SmilesDrawerMoleculeStructure", "className", "title", "concat", "Arrow", "__defProp", "__getOwnPropSymbols", "__hasOwnProp", "__propIsEnum", "__defNormalProp", "obj", "key", "__spreadValues", "a", "b", "prop", "SvgCollected", "SvgGroup", "SvgTip", "FundOutlined", "AntdIcon", "GoldOutlined", "_detectDeviceInfo", "detectDeviceInfo", "winWidth", "OperateIcon", "icon", "Popover", "content", "projectStatusTransferMap", "canceled", "confirmed", "editing", "finished", "CardTitle", "_ref2", "item", "route", "actions", "_ref2$stepNum", "<PERSON><PERSON><PERSON>", "calSyntheticStep", "_ref2$hasBranch", "hasBranch", "calHasBranch", "hasBranches", "updateTime", "commendCount", "routeName", "onAction", "updatedAt", "routeNo", "group", "routeStatus", "handleFilterSimilar", "targetMolecule", "isCollected", "_useSearchParams", "useSearchParams", "_useSearchParams2", "_slicedToArray", "searchParams", "setSearchParams", "_useBrainFetch", "useBrainFetch", "fetch", "_useModel", "useModel", "updatePagenate", "getMyRoutesData", "routeType", "curFilterInfo", "collectedEvent", "curIsCollected", "curHistoryInfo", "access", "useAccess", "_useModel2", "initialState", "_useParams", "useParams", "projectId", "id", "compoundId", "isTemporaryRoute", "status", "reload", "_ref3", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "customPageSize", "wrap", "_context", "prev", "next", "toInt", "get", "page", "pageSize", "toString", "replace", "stop", "apply", "arguments", "handleSetAsDefault", "_ref4", "_callee2", "_yield$fetch", "data", "_context2", "abrupt", "service", "update", "default_route", "sent", "message", "success", "getWord", "handleCollected", "_ref5", "_callee3", "_item$collected_retro", "_initialState$userInf", "_ref6", "newCollectedRetroBackbones", "_context3", "deleteOne", "collected_retro_backbones", "t0", "create", "user_id", "String", "userInfo", "retro_backbone", "cloneDeep", "unshift", "handleChangeStatus", "_ref7", "_callee4", "_yield$fetch2", "_context4", "_x", "_useLocation", "useLocation", "pathname", "isPlayground", "includes", "preViewdRoutes", "getLocalValue", "JSON", "parse", "handleView", "newViewdRoutes", "_toConsumableArray", "setLocalValue", "stringify", "history", "push", "hasCommendCount", "Number", "ActionCom", "_ref8", "_access$authCodeList", "_access$authCodeList2", "_access$authCodeList3", "_targetMolecule$defau", "action", "des", "authCodeList", "MessageOutlined", "onClick", "CollectedIcon", "width", "style", "position", "top", "StarOutlined", "color", "cs", "complete", "EyeOutlined", "ExperimentFilled", "ExperimentOutlined", "avaliableStatus", "styles", "isNil", "groupItem", "GroupIcon", "fill", "Tag", "min_n_main_tree_steps", "HelpIcon", "cursor", "barnchTag", "isEN", "FieldTimeOutlined", "formatYTSTime", "score", "Math", "round", "roundToOneDecimalPlaces", "novelty_score", "price_score", "safety_score", "Divider", "margin", "originScore", "TipIcon", "known_reaction_rate", "formatYMDHMTime", "length", "EnumSwitcher", "currentValue", "avalibleValues", "onSelect", "valueRender", "s", "StatusRender", "labelPrefix", "map", "<PERSON><PERSON>", "type", "ProjectRouteTitle", "getProfileInfo", "name", "updated_at", "createdAt", "main_tree", "content_count", "t", "_commendSuject", "collection_class", "SyntheticRouteCard", "hiddenTitle", "moleculeId", "_useCopyToClipboard", "useCopyToClipboard", "copy", "Card", "syntheticTreeToLink", "_", "cal<PERSON><PERSON><PERSON>", "target", "reactant", "CopyMaterialIcon"], "sourceRoot": ""}