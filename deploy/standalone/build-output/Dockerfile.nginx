FROM --platform=linux/amd64 nginx:1.25.3 AS nginx_base

# Install file command and additional tools for verification
RUN apt-get update && \
    apt-get install -y file && \
    rm -rf /var/lib/apt/lists/*

# Verify architecture and nginx binary
RUN echo "Architecture verification:" && \
    echo "System architecture: $(uname -m)" && \
    echo "Nginx binary info:" && \
    file /usr/sbin/nginx && \
    echo "Library dependencies:" && \
    ldd /usr/sbin/nginx && \
    echo "Nginx version:" && \
    /usr/sbin/nginx -v

FROM scratch AS export
COPY --from=nginx_base /usr/sbin/nginx /nginx/
COPY --from=nginx_base /usr/lib/nginx/ /nginx/modules/
COPY --from=nginx_base /etc/nginx/ /nginx/conf/
COPY --from=nginx_base /var/log/nginx/ /nginx/logs/
COPY --from=nginx_base /var/cache/nginx/ /nginx/cache/
COPY --from=nginx_base /usr/share/nginx/ /nginx/html/
# Create a script to copy all required libraries dynamically
RUN mkdir -p /nginx/lib && \
    echo "#!/bin/bash" > /copy-libs.sh && \
    echo "# Copy all required libraries for nginx" >> /copy-libs.sh && \
    echo "ldd /usr/sbin/nginx | grep -o '/[^ ]*' | while read lib; do" >> /copy-libs.sh && \
    echo "  if [ -f \"$lib\" ]; then" >> /copy-libs.sh && \
    echo "    echo \"Copying library: $lib\"" >> /copy-libs.sh && \
    echo "    cp \"$lib\" /nginx/lib/" >> /copy-libs.sh && \
    echo "  fi" >> /copy-libs.sh && \
    echo "done" >> /copy-libs.sh && \
    chmod +x /copy-libs.sh && \
    /copy-libs.sh

# Also copy some essential libraries manually to ensure they're present
COPY --from=nginx_base /lib/x86_64-linux-gnu/ld-linux-x86-64.so.2 /nginx/lib/
COPY --from=nginx_base /lib/x86_64-linux-gnu/libc.so.6 /nginx/lib/
