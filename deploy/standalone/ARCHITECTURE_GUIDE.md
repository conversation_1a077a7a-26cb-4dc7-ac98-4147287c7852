# Architecture Compatibility Guide

This guide explains how the Labwise Web standalone deployment handles cross-platform builds and architecture compatibility.

## Problem Statement

The original issue was an architecture mismatch error:
```
cannot execute binary file: Exec format error
```

This occurred when building on Apple Silicon Mac (ARM64) but deploying on Rocky Linux 8.10 x86_64.

## Solution Overview

The build system now:
1. **Always targets linux/amd64** regardless of build host architecture
2. **Validates architecture** during both build and deployment
3. **Provides clear error messages** for architecture mismatches
4. **Supports cross-platform builds** from any Docker-capable system

## Build Process Changes

### Platform Targeting

All Docker builds now explicitly target `linux/amd64`:

```dockerfile
FROM --platform=linux/amd64 node:18-buster-slim AS builder
FROM --platform=linux/amd64 nginx:1.25.3-alpine AS nginx_base
```

### Build Commands

The build script uses Docker buildx for cross-platform support:

```bash
# Frontend build
docker buildx build \
    --platform linux/amd64 \
    -f Dockerfile.build \
    --target export \
    --output build-output \
    .

# Nginx extraction
docker buildx build \
    --platform linux/amd64 \
    -f Dockerfile.nginx \
    --target export \
    --output build-output \
    .
```

### Architecture Verification

The build process now verifies the extracted binaries:

```bash
# Check nginx binary architecture
file build-output/nginx/nginx
# Expected: ELF 64-bit LSB executable, x86-64
```

## Deployment Validation

### System Architecture Check

The deployment script validates the target system:

```bash
system_arch=$(uname -m)
if [ "$system_arch" != "x86_64" ]; then
    echo "Architecture mismatch detected!"
    echo "Package built for: x86_64"
    echo "Current system: $system_arch"
    exit 1
fi
```

### Binary Compatibility Test

Before starting the service, the script tests the nginx binary:

```bash
if ! ./nginx/nginx -v &> /dev/null; then
    echo "Nginx binary compatibility test failed!"
    echo "This indicates an architecture mismatch"
    exit 1
fi
```

## Supported Configurations

### Build Environments ✅

| Platform | Architecture | Status | Notes |
|----------|-------------|---------|-------|
| macOS Apple Silicon | arm64 | ✅ | Cross-platform build |
| macOS Intel | x86_64 | ✅ | Native build |
| Ubuntu x86_64 | x86_64 | ✅ | Native build |
| Windows WSL2 | x86_64 | ✅ | Native build |

### Target Environments ✅

| Platform | Architecture | Status | Notes |
|----------|-------------|---------|-------|
| Rocky Linux 8.10 | x86_64 | ✅ | Primary target |
| CentOS 8 | x86_64 | ✅ | Compatible |
| RHEL 8 | x86_64 | ✅ | Compatible |
| Ubuntu 20.04+ | x86_64 | ✅ | Compatible |

### Unsupported Configurations ❌

| Platform | Architecture | Status | Reason |
|----------|-------------|---------|---------|
| Any system | arm64/aarch64 | ❌ | Package built for x86_64 |
| Any system | i386/32-bit | ❌ | Package built for x86_64 |
| Windows | Any | ❌ | Linux-specific binaries |
| macOS | Any | ❌ | Linux-specific binaries |

## Troubleshooting

### Build Issues

**Docker buildx not available:**
```bash
# Install buildx
docker buildx install

# Create builder
docker buildx create --use --name multiarch
```

**Platform not supported:**
```bash
# Check available platforms
docker buildx ls

# Inspect builder
docker buildx inspect --bootstrap
```

### Deployment Issues

**Architecture mismatch:**
```bash
# Check system architecture
uname -m

# Verify package architecture
file nginx/nginx

# Rebuild if necessary
./build-standalone.sh product
```

**Binary won't execute:**
```bash
# Check dependencies
ldd nginx/nginx

# Check file permissions
ls -la nginx/nginx
chmod +x nginx/nginx
```

## Best Practices

1. **Always use the updated build script** - it handles platform targeting automatically
2. **Verify target system architecture** before deployment
3. **Test deployment** on a representative system before production
4. **Keep Docker updated** for best cross-platform support
5. **Use buildx** for consistent cross-platform builds

## Migration from Old Build Process

If you have packages built with the old process:

1. **Rebuild all packages** using the updated build script
2. **Verify architecture** of existing deployments
3. **Update deployment procedures** to include architecture validation
4. **Test thoroughly** on target systems

The updated build process ensures compatibility and prevents the "Exec format error" issue.
