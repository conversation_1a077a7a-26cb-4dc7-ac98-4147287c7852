# Labwise Web Standalone Deployment

This package provides a Docker-free deployment solution for the Labwise Web frontend application. It uses containerized builds for consistency but produces a self-contained deployment package that runs without Docker on the target system.

## Overview

The standalone deployment solution consists of:

- **Build Phase**: Uses Docker to create a portable deployment package (works on macOS/Ubuntu)
- **Deployment Phase**: Runs without Docker on the target system (Rocky Linux 8.10)
- **Self-contained**: Includes nginx binary, static files, and all dependencies
- **No system modifications**: Runs as a user process without requiring admin privileges
- **Cross-platform builds**: Always targets linux/amd64 regardless of build host architecture

## Architecture Compatibility

**Build Environment (any of these):**
- macOS ARM64 (Apple Silicon)
- macOS Intel (x86_64)
- Ubuntu x86_64
- Any Docker-capable system

**Target Environment (must be):**
- Rocky Linux 8.10 x86_64
- CentOS 8 x86_64
- RHEL 8 x86_64
- Other compatible x86_64 Linux distributions

⚠️ **Important**: The build process always creates x86_64 binaries regardless of the build host architecture. This ensures compatibility with Rocky Linux 8.10 systems.

## Quick Start

### 1. Build the Deployment Package

On your development machine (macOS or Ubuntu):

```bash
# Navigate to the standalone directory
cd deploy/standalone

# Build for production environment
./build-standalone.sh product

# Or build for other environments
./build-standalone.sh develop
./build-standalone.sh test
```

### 2. Transfer to Target System

```bash
# Copy the generated package to your Rocky Linux 8.10 server
scp deploy-package/labwise-web-standalone-*.tar.gz user@your-server:/path/to/deployment/
```

### 3. Deploy on Target System

```bash
# Extract the package
tar -xzf labwise-web-standalone-*.tar.gz
cd labwise-web-standalone-*

# Deploy the service
./deploy.sh

# Or deploy on a specific port
./deploy.sh --port 9000
```

## Build Process

### Prerequisites

- Docker installed and running
- Node.js 16+ (for development)
- Yarn package manager

### Build Script Usage

```bash
./build-standalone.sh [environment]
```

**Environments:**
- `develop` - Development environment (default)
- `test` - Test environment
- `product` - Production environment
- `demo` - Demo environment

**Environment Variables:**
- `UMI_ENV` - Override environment detection

### Testing the Build Process

Before running a full build, you can test the build process:

```bash
# Test Docker prerequisites and build process
./test-build.sh

# If tests pass, run the full build
./build-standalone.sh product
```

The test script verifies:
- Docker prerequisites and buildx support
- Platform targeting (linux/amd64)
- Nginx extraction with file command
- Architecture verification methods

### Build Output

The build process creates:
- `build-output/` - Temporary build artifacts
- `deploy-package/` - Final deployment packages

Each package contains:
- `nginx/` - Nginx binary and dependencies
- `html/` - Frontend static assets
- `conf/` - Configuration files
- `logs/` - Log directory
- `cache/` - Cache directory
- `run/` - Runtime files (PID, sockets)
- Deployment scripts (`deploy.sh`, `start.sh`, `stop.sh`, `status.sh`)

## Deployment Process

### System Requirements

**Target System (Rocky Linux 8.10):**
- No Docker required
- No root/sudo access required
- Basic system utilities (ps, netstat/ss, tar)
- Available port (default: 8080)

### Deployment Script Usage

```bash
./deploy.sh [OPTIONS]

Options:
  -p, --port PORT     Port to run the service on (default: 8080)
  -u, --user USER     User to run nginx as (default: current user)
  -h, --help          Show help message

Examples:
  ./deploy.sh                  # Deploy with default settings
  ./deploy.sh -p 9000          # Deploy on port 9000
  ./deploy.sh --port 8080      # Deploy on port 8080
```

### Service Management

After deployment, use these commands to manage the service:

```bash
# Start the service
./start.sh

# Stop the service
./stop.sh

# Check service status
./status.sh

# Restart the service
./stop.sh && ./start.sh
```

### Service Information

- **Default Port**: 8080 (configurable)
- **Process Type**: User process (no system service)
- **Configuration**: `run/nginx.conf`
- **Logs**: `logs/access.log`, `logs/error.log`
- **PID File**: `run/nginx.pid`

## Configuration

### Backend Services

The default configuration includes placeholder endpoints for backend services. You need to update the nginx configuration to point to your actual backend services:

1. Edit `conf/nginx.conf` after deployment
2. Update the proxy_pass directives for:
   - `/api/` - Main API backend
   - `/api/smiles/to-image` - SMILES image service
   - `/api/smiles/image-file` - SMILES file service  
   - `/api/smiles/inchi` - InChI service
   - WebSocket endpoints

Example configuration update:

```nginx
location /api/ {
  proxy_pass http://your-backend-server:1337/api/;
  proxy_set_header Host $host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  proxy_http_version 1.1;
  proxy_set_header Upgrade $http_upgrade;
  proxy_set_header Connection "Upgrade";
}
```

3. Restart the service: `./stop.sh && ./start.sh`

### Port Configuration

If you need to change the port after deployment:

1. Stop the service: `./stop.sh`
2. Run deploy again with new port: `./deploy.sh -p NEW_PORT`

### SSL/TLS Configuration

To enable HTTPS:

1. Obtain SSL certificates
2. Copy certificates to the deployment directory
3. Update `conf/nginx.conf` to add SSL configuration
4. Restart the service

## Monitoring and Troubleshooting

### Service Status

```bash
# Check detailed service status
./status.sh

# Check if service is running
ps aux | grep nginx

# Check port usage
netstat -tuln | grep :8080
# or
ss -tuln | grep :8080
```

### Log Files

```bash
# View access logs
tail -f logs/access.log

# View error logs  
tail -f logs/error.log

# Clear logs
> logs/access.log && > logs/error.log
```

### Common Issues

**Architecture mismatch (Exec format error):**
- **Error**: `cannot execute binary file: Exec format error`
- **Cause**: Trying to run ARM64 binary on x86_64 system (or vice versa)
- **Solution**:
  1. Verify target system: `uname -m` (should show `x86_64`)
  2. Rebuild package ensuring Docker platform targeting: `./build-standalone.sh product`
  3. Check build logs for platform confirmation
- **Prevention**: Always build with the updated build script that forces linux/amd64

**Port already in use:**
- The deployment script will automatically find an available port
- Or specify a different port: `./deploy.sh -p 9001`

**Permission denied:**
- Ensure the deployment directory is writable
- Check that the nginx binary is executable: `chmod +x nginx/nginx`

**Service won't start:**
- Check error logs: `cat logs/error.log`
- Verify configuration: `./nginx/nginx -t -c run/nginx.conf`
- Check available ports: `netstat -tuln`

**Backend API errors:**
- Update nginx configuration with correct backend URLs
- Ensure backend services are running and accessible
- Check firewall settings

**Docker build issues on Apple Silicon:**
- Ensure Docker Desktop is updated to latest version
- Enable "Use Rosetta for x86/amd64 emulation" in Docker Desktop settings
- Verify buildx is available: `docker buildx version`

**Build fails with "file: not found" error:**
- **Fixed**: The build script now automatically installs the `file` package
- **Cause**: Alpine Linux doesn't include `file` command by default
- **Solution**: Build script now includes `RUN apt-get install -y file` in Dockerfile
- **Test**: Run `./test-build.sh` to verify the fix works

**Deployment fails with "Nginx binary compatibility test failed":**
- **Fixed**: Switched from Alpine (musl) to Debian-based (glibc) nginx
- **Cause**: musl vs glibc incompatibility between Alpine and Rocky Linux
- **Solution**: Build now uses `nginx:1.25.3` (Debian/glibc) instead of `nginx:1.25.3-alpine`
- **Library handling**: Automatic detection and copying of all required glibc libraries
- **Test**: Run `./test-build.sh` to verify glibc compatibility

### Health Check

The service provides a health check endpoint:

```bash
# Check service health
curl http://localhost:8080/health

# Expected response:
{"status": "healthy", "service": "labwise-web", "timestamp": "2024-01-01T12:00:00Z"}
```

## Security Considerations

- Service runs as non-root user
- Security headers are configured by default
- Static file serving with appropriate caching
- No system-level modifications required
- Logs are written to local directory only

## Backup and Recovery

### Backup

```bash
# Backup the entire deployment
tar -czf labwise-web-backup-$(date +%Y%m%d).tar.gz .

# Backup configuration only
cp -r conf/ conf-backup-$(date +%Y%m%d)/
```

### Recovery

```bash
# Stop service
./stop.sh

# Restore from backup
tar -xzf labwise-web-backup-YYYYMMDD.tar.gz

# Start service
./start.sh
```

## Updating

To update to a new version:

1. Build new deployment package
2. Stop current service: `./stop.sh`
3. Backup current deployment
4. Extract new package
5. Copy any custom configuration from backup
6. Deploy new version: `./deploy.sh`

## Support

For issues and questions:

1. Check the troubleshooting section above
2. Review log files for error details
3. Verify system requirements are met
4. Ensure backend services are properly configured

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Development   │    │   Build Process  │    │  Target System  │
│   Environment   │    │   (Docker)       │    │  (Rocky Linux)  │
├─────────────────┤    ├──────────────────┤    ├─────────────────┤
│ macOS/Ubuntu    │───▶│ Frontend Build   │───▶│ Nginx Process   │
│ Docker          │    │ Nginx Extract    │    │ Static Files    │
│ Node.js/Yarn    │    │ Package Creation │    │ No Docker       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

This architecture ensures:
- Consistent builds across development environments
- No Docker dependency on production systems
- Self-contained deployment packages
- Easy deployment in restricted environments
